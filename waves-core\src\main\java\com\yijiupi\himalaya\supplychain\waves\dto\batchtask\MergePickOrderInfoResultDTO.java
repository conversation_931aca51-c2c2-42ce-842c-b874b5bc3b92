package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/25
 */
public class MergePickOrderInfoResultDTO implements Serializable {
    /**
     * 是否合并拣货
     */
    private boolean isMergePick = Boolean.FALSE;
    /**
     * 查询的订单id
     */
    private Long orderId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 合并拣货的订单
     */
    private List<MergePickOrderInfoRelatedOrderResultDTO> otherOrderList;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 是否合并拣货
     *
     * @return isMergePick 是否合并拣货
     */
    public boolean isIsMergePick() {
        return this.isMergePick;
    }

    /**
     * 设置 是否合并拣货
     *
     * @param isMergePick 是否合并拣货
     */
    public void setIsMergePick(boolean isMergePick) {
        this.isMergePick = isMergePick;
    }

    /**
     * 获取 查询的订单id
     *
     * @return orderId 查询的订单id
     */
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 查询的订单id
     *
     * @param orderId 查询的订单id
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 订单号
     *
     * @return orderNo 订单号
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置 订单号
     *
     * @param orderNo 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取 合并拣货的订单
     *
     * @return otherOrderList 合并拣货的订单
     */
    public List<MergePickOrderInfoRelatedOrderResultDTO> getOtherOrderList() {
        return this.otherOrderList;
    }

    /**
     * 设置 合并拣货的订单
     *
     * @param otherOrderList 合并拣货的订单
     */
    public void setOtherOrderList(List<MergePickOrderInfoRelatedOrderResultDTO> otherOrderList) {
        this.otherOrderList = otherOrderList;
    }
}
