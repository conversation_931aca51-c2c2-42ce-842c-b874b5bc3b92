package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-09-04 16:42
 **/
public class RestockRequestParam implements Serializable {

    /**
     * 仓库 id
     */
    private Integer warehouseId;

    /**
     * 产品明细
     */
    private List<RequestItems> items;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<RequestItems> getItems() {
        return items;
    }

    public void setItems(List<RequestItems> items) {
        this.items = items;
    }

    public static class RequestItems implements Serializable {

        /**
         * skuId
         */
        private Long skuId;
        /**
         * 货位名称
         */
        private String locationName;

        public Long getSkuId() {
            return skuId;
        }

        public void setSkuId(Long skuId) {
            this.skuId = skuId;
        }

        public String getLocationName() {
            return locationName;
        }

        public void setLocationName(String locationName) {
            this.locationName = locationName;
        }
    }

}
