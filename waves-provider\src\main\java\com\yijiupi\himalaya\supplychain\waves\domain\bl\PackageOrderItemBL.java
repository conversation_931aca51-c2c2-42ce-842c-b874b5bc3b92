package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.orderdocument.OrderDocumentDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.orderdocument.OrderPickDocumentDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IOrderLocationPalletService;
import com.yijiupi.himalaya.supplychain.waves.advice.MarkMethodInvokeFlag;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchFinishedBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchFinishedLackOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.tms.TmsApiManager;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.PackageOrderItemConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.PackageOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.mq.PackageSyncMQ;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.*;
import com.yijiupi.himalaya.supplychain.waves.dto.secondsort.SecondSortQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderSearchSO;
import com.yijiupi.himalaya.supplychain.waves.search.PackageOrderItemSO;
import com.yijiupi.himalaya.supplychain.waves.util.ConstantUtil;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;
import com.yijiupi.himalaya.supplychain.waves.utils.StreamUtils;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;
import com.yijiupi.supplychain.serviceutils.constant.redis.RedisConstant;

/**
 * 产品包装
 *
 * <AUTHOR>
 * @since 2018/7/12 18:19
 */
@Service
@SuppressWarnings("NonAsciiCharacters")
public class PackageOrderItemBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(PackageOrderItemBL.class);

    @Autowired
    private PackageOrderItemMapper packageOrderItemMapper;

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;

    @Autowired
    private PackageSyncMQ packageSyncMQ;

    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;

    @Autowired
    private BatchOrderBL batchOrderBL;

    @Autowired
    private OutStockOrderBL outStockOrderBL;

    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    @Autowired
    private BatchFinishedBL batchFinishedBL;

    @Resource
    private OrderCenterBL orderCenterBL;

    @Resource
    private TmsApiManager tmsApiManager;

    @Resource
    private BatchFinishedLackOrderBL batchFinishedLackOrderBL;
    @Resource
    private PackageOrderItemConvertor packageOrderItemConvertor;

    @Reference
    private IOrderLocationPalletService iOrderLocationPalletService;

    private static final NumberFormat nf = NumberFormat.getInstance();

    static {
        nf.setMinimumIntegerDigits(5);
        nf.setGroupingUsed(false);
    }

    /**
     * 根据出库单号查询包装信息详情
     */
    public PageList<PackageOrderItemDTO> listPackageOrderItem(PackageOrderItemSO packageOrderItemSO) {
        AssertUtils.notNull(packageOrderItemSO, "参数不能为空");
        AssertUtils.notNull(packageOrderItemSO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(packageOrderItemSO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(packageOrderItemSO.getRefOrderNo(), "单号不能为空");
        OutStockOrderPO outStockOrderPO = outStockOrderMapper.selectByRefOrderNo(packageOrderItemSO.getRefOrderNo(),
                packageOrderItemSO.getOrgId(), packageOrderItemSO.getWarehouseId());
        if (null == outStockOrderPO) {
            throw new BusinessException("该订单不存在");
        }
        if (OutStockOrderStateEnum.已拣货.getType() != (byte) outStockOrderPO.getState().intValue()) {
            throw new BusinessValidateException("该订单不是已拣货状态");
        }
        PageResult<PackageOrderItemDTO> packageOrderItemDTOPageResult = packageOrderItemMapper.listPackageOrderItem(
                packageOrderItemSO, packageOrderItemSO.getCurrentPage(), packageOrderItemSO.getPageSize());
        PageList<PackageOrderItemDTO> pageList = packageOrderItemDTOPageResult.toPageList();
        LOGGER.info("包装详情列表：{}", JSON.toJSONString(pageList));
        return pageList;
    }

    /**
     * 根据单号查询所有包装信息
     */
    public List<PackageOrderItemDTO> listPackageOrderItemByOrderNo(String refOrderNo, Integer cityId,
                                                                   Integer warehouseId) {
        AssertUtils.notNull(refOrderNo, "订单号不能为空");
        AssertUtils.notNull(cityId, "城市id不能为空");
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        return packageOrderItemMapper.listPackageOrderItemByOrderNo(refOrderNo, cityId, warehouseId);
    }

    /**
     * 新增包装详情
     */
    public void savePackage(PackageOrderItemDTO packageOrderItemDTO) {
        AssertUtils.notNull(packageOrderItemDTO, "包装参数不能为空");
        AssertUtils.notNull(packageOrderItemDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(packageOrderItemDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(packageOrderItemDTO.getRefOrderId(), "出库单Id不能为空");
        AssertUtils.notNull(packageOrderItemDTO.getRefOrderNo(), "出库单编号不能为空");
        AssertUtils.notNull(packageOrderItemDTO.getRefOrderItemId(), "出库单项id不能为空");
        AssertUtils.notNull(packageOrderItemDTO.getBoxCode(), "箱码编号不能为空");
        AssertUtils.notNull(packageOrderItemDTO.getSkuId(), "产品SKUID不能为空");

        PackageOrderItemPO packageOrderItemPO = new PackageOrderItemPO();
        BeanUtils.copyProperties(packageOrderItemDTO, packageOrderItemPO);
        packageOrderItemPO.setId(Long.valueOf(UuidUtil.generatorId()));
        packageOrderItemPO.setBoxCodeNo(generateBoxCodeNo(packageOrderItemDTO));
        LOGGER.info("新增包装详情参数：{}", JSON.toJSONString(packageOrderItemPO));
        packageOrderItemMapper.insert(packageOrderItemPO);
    }

    /**
     * 批量新增包装详情
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @MarkMethodInvokeFlag(key = RedisConstant.SUP_F + "UserOnlineCache",
            warehouseId = "#packageOrderItemDTOList[0].warehouseId", userId = "#packageOrderItemDTOList[0].operatorId")
    public void savePackageBatch(List<PackageOrderItemDTO> packageOrderItemDTOList, Boolean isPDA) {
        savePackageBatch(packageOrderItemDTOList, isPDA, true);
    }

    public void savePackageBatch(List<PackageOrderItemDTO> items, Boolean isPDA, boolean deleteByOrder) {
        AssertUtils.notEmpty(items, "包装参数不能为空");
        LOGGER.info("打包信息日志：{}; isPDA : {}", JSON.toJSONString(items), isPDA);
        // 已拣货状态订单才可包装
        List<String> refOrderNoList = items.stream().map(PackageOrderItemDTO::getRefOrderNo)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<Long> refOrderIdList = items.stream().map(PackageOrderItemDTO::getRefOrderId).filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refOrderNoList) && CollectionUtils.isEmpty(refOrderIdList)) {
            throw new DataValidateException("订单编号不能为空");
        }
        List<OutStockOrderPO> lstOrderPO = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(refOrderNoList)) {
            lstOrderPO = outStockOrderMapper.listOutStockOrderByOrderNo(refOrderNoList, items.get(0).getOrgId(),
                    items.get(0).getWarehouseId());
        } else if (CollectionUtils.isNotEmpty(refOrderIdList)) {
            lstOrderPO = outStockOrderMapper.listOutStockOrderByOrderId(refOrderIdList);
        }
        List<OutStockOrderPO> finalLstOrderPO = lstOrderPO;

        if (CollectionUtils.isNotEmpty(refOrderNoList)) {
            refOrderNoList.forEach(orderNo -> {
                Optional<OutStockOrderPO> stockOrderPO =
                        finalLstOrderPO.stream().filter(p -> p.getReforderno().equals(orderNo)).findAny();
                if (!stockOrderPO.isPresent()) {
                    throw new BusinessException("该订单(" + orderNo + ")不存在");
                }
                fillBatchTaskItemOrderIdAndItemId(items, stockOrderPO.get());
            });
        } else if (CollectionUtils.isNotEmpty(refOrderIdList)) {
            refOrderIdList.forEach(orderId -> {
                Optional<OutStockOrderPO> stockOrderPO =
                        finalLstOrderPO.stream().filter(p -> p.getId().equals(orderId)).findAny();
                if (!stockOrderPO.isPresent()) {
                    throw new BusinessException("该订单(" + orderId + ")不存在");
                }
                fillBatchTaskItemOrderIdAndItemId(items, stockOrderPO.get());
            });
        }
        updatePackageOrderItems(items, isPDA, deleteByOrder);
    }

    private final List<Byte> lstOnProcessingTaskState = Arrays.asList(OutStockOrderStateEnum.已拣货.getType(),
            OutStockOrderStateEnum.拣货中.getType(), OutStockOrderStateEnum.待拣货.getType());

    private void fillBatchTaskItemOrderIdAndItemId(List<PackageOrderItemDTO> items, OutStockOrderPO outStockOrderPO) {
        if (!lstOnProcessingTaskState.contains(outStockOrderPO.getState().byteValue())) {
            throw new BusinessValidateException("该订单(" + outStockOrderPO.getReforderno() + ")不是待拣货、拣货中或者已拣货状态");
        }
        String businessId = outStockOrderPO.getBusinessId();
        if (OrderConstant.ALLOT_TYPE_DEFAULT.equals(outStockOrderPO.getAllotType())) {
            businessId = outStockOrderPO.getId().toString();
        }

        String finalBusinessId = businessId;
        Map<Long, OutStockOrderItemPO> outStockOrderItemMap = outStockOrderPO.getItems().stream()
                .collect(Collectors.toMap(OutStockOrderItemPO::getId, Function.identity(), (key1, key2) -> key2));
        // 设置订单序号
        items.forEach(p -> {
            // 如果不是当前循环的订单，跳过不处理
            if (Objects.equals(p.getRefOrderId(), outStockOrderPO.getId())
                    || Objects.equals(p.getRefOrderNo(), outStockOrderPO.getReforderno())) {
                p.setRefOrderId(outStockOrderPO.getId());
                p.setRefOrderNo(outStockOrderPO.getReforderno());
                p.setOrderSequence(outStockOrderPO.getOrderSequence());
                p.setBusinessId(finalBusinessId);
                // oms订单id
                p.setOrderBusinessId(outStockOrderPO.getBusinessId());

                OutStockOrderItemPO outStockOrderItem = outStockOrderItemMap.get(p.getRefOrderItemId());
                // 兼容 RefOrderItemId 为空的情况
                if (outStockOrderItem == null) {
                    outStockOrderItem = outStockOrderItemMap.values().stream()
                            .filter(item -> Objects.equals(item.getSkuid(), p.getSkuId())).findAny().orElse(null);
                }
                if (outStockOrderItem != null) {
                    if (OrderConstant.ALLOT_TYPE_DEFAULT.equals(outStockOrderPO.getAllotType())) {
                        p.setBusinessItemId(p.getRefOrderItemId().toString());
                    } else {
                        p.setBusinessItemId(outStockOrderItem.getBusinessItemId());
                    }
                    p.setOrderItemBusinessId(outStockOrderItem.getBusinessItemId());
                }
            }
        });
    }

    /**
     * 处理先装箱，然后全缺，需要清空箱号的情况
     */
    public void handleAllPackageRemove(List<OrderItemTaskInfoPO> orderItemTaskInfoPOS,
                                       List<BatchTaskItemDTO> batchTaskItemDTOList) {
        if (CollectionUtils.isEmpty(batchTaskItemDTOList)) {
            return;
        }

        Map<String, BatchTaskItemDTO> batchTaskItemMap =
                batchTaskItemDTOList.stream().collect(Collectors.toMap(BatchTaskItemDTO::getId, v -> v));

        List<Long> refOrderItemIdList = orderItemTaskInfoPOS.stream()
                .filter(item -> Objects.nonNull(batchTaskItemMap.get(item.getBatchTaskItemId()))
                        && item.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0)
                .map(OrderItemTaskInfoPO::getRefOrderItemId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(refOrderItemIdList)) {
            return;
        }
        List<PackageOrderItemDTO> oldPackageOrderItems = packageOrderItemMapper
                .listPackageItemsByItemIds(refOrderItemIdList, batchTaskItemDTOList.get(0).getOrgId());
        if (CollectionUtils.isEmpty(oldPackageOrderItems)) {
            return;
        }
        LOGGER.info("更新成0的包装箱关联id：{}", oldPackageOrderItems.stream().map(PackageOrderItemDTO::getRefOrderItemId)
                .distinct().collect(Collectors.toList()));
        oldPackageOrderItems.stream().collect(Collectors.groupingBy(PackageOrderItemDTO::getOrgId,
                Collectors.mapping(PackageOrderItemDTO::getId, Collectors.toList()))).forEach((orgId, idList) -> {
            for (Long aLong : idList) {
                PackageOrderItemDTO updatePackageOrderItemDTO = new PackageOrderItemDTO();
                updatePackageOrderItemDTO.setId(aLong);
                updatePackageOrderItemDTO.setUnitTotalCount(BigDecimal.ZERO);
                updatePackageOrderItemDTO.setPackageCount(BigDecimal.ZERO);
                updatePackageOrderItemDTO.setUnitCount(BigDecimal.ZERO);
                packageOrderItemMapper.updateBoxCodeById(updatePackageOrderItemDTO);
            }
        });
    }

    /**
     * 箱码完整编号生成规则<br/>
     * 按一定规则生成箱码
     */
    @SuppressWarnings("NonAsciiCharacters")
    public String generateBoxCodeNo(PackageOrderItemDTO item) {
        String orderSequence = getOrderSequence(item.getOrderSequence());
        String boxCode = item.getBoxCode();
        Long refOrderItemId = item.getRefOrderItemId();
        Byte packageType = item.getPackageType();
        String passageCode = item.getPassageCode();
        String boxCodeNo = "";
        if (orderSequence != null && StringUtils.isNotEmpty(boxCode) && refOrderItemId != null) {
            if (StringUtils.isNotEmpty(passageCode)) {
                boxCode = String.format("%s%s", passageCode, boxCode);
            }
            OutStockOrderItemPO outStockOrderItemPO = outStockOrderItemMapper.findById(refOrderItemId);
            if (outStockOrderItemPO != null && StringUtils.isNotEmpty(outStockOrderItemPO.getBatchtaskno())) {
                String batchTaskNO = outStockOrderItemPO.getBatchtaskno();
                if (packageType != null && packageType == PackageTypeEnum.托盘.getType()) {
                    batchTaskNO = batchTaskNO.substring(2);
                    boxCodeNo = String.format("ZT%s-%s", batchTaskNO, boxCode);
                } else {
                    batchTaskNO = batchTaskNO.substring(batchTaskNO.length() - 2);
                    boxCodeNo = String.format("BZ%s-%s", orderSequence, boxCode);
                }
            }
        }
        return boxCodeNo;
    }

    private String getOrderSequence(Integer orderSequence) {
        if (Objects.isNull(orderSequence)) {
            return null;
        }

        String orderSequenceStr = orderSequence.toString();
        if (orderSequenceStr.length() == 5) {
            return orderSequenceStr;
        }

        String formatted = nf.format(orderSequence);

        return formatted;
    }

    /**
     * 根据订单号获取打印子箱条码信息
     */
    public List<PackageCodePrintDTO> listPackageCodePrint(String refOrderNo, Integer cityId, Integer warehouseId) {
        AssertUtils.notNull(refOrderNo, "订单号不能为空");
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        // 获取出库单详情
        OutStockOrderPO outStockOrderPO = outStockOrderMapper.selectByRefOrderNo(refOrderNo, cityId, warehouseId);
        if (null == outStockOrderPO) {
            throw new BusinessException("找不到对应的出库单");
        }
        // 根据单号查询所有包装信息
        List<PackageOrderItemDTO> packageOrderItemDTOList =
                packageOrderItemMapper.listPackageOrderItemByOrderNo(refOrderNo, cityId, warehouseId);
        if (CollectionUtils.isEmpty(packageOrderItemDTOList)) {
            throw new BusinessException("当前订单没有生成子箱条码");
        }
        List<PackageCodePrintDTO> packageCodePrintDTOList = new ArrayList<>();
        // 按箱号分组
        Map<String, List<PackageOrderItemDTO>> packageCodeMap =
                packageOrderItemDTOList.stream().collect(Collectors.groupingBy(PackageOrderItemDTO::getBoxCodeNo));
        packageCodeMap.forEach((boxCodeNo, list) -> {
            PackageCodePrintDTO packageCodePrintDTO = new PackageCodePrintDTO();
            packageCodePrintDTO.setRefOrderNo(refOrderNo);
            packageCodePrintDTO.setBoxCode(list.get(0).getBoxCode());
            packageCodePrintDTO.setBoxCodeNo(boxCodeNo);
            packageCodePrintDTO.setShopName(outStockOrderPO.getShopname());
            packageCodePrintDTO.setRouteName(outStockOrderPO.getRouteName());
            packageCodePrintDTO.setRouteSequence(outStockOrderPO.getRouteSequence());
            packageCodePrintDTO
                    .setSkuCount(list.stream().collect(Collectors.groupingBy(PackageOrderItemDTO::getSkuId)).size());
            packageCodePrintDTO.setUnitTotalCount(
                    list.stream().map(PackageOrderItemDTO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add));

            packageCodePrintDTO.setUserName(outStockOrderPO.getUsername());
            packageCodePrintDTO.setMobileNo(outStockOrderPO.getMobileno());
            packageCodePrintDTO.setProvince(outStockOrderPO.getProvince());
            packageCodePrintDTO.setCity(outStockOrderPO.getCity());
            packageCodePrintDTO.setCounty(outStockOrderPO.getCounty());
            packageCodePrintDTO.setStreet(outStockOrderPO.getStreet());
            packageCodePrintDTO.setDetailAddress(outStockOrderPO.getDetailaddress());
            packageCodePrintDTO.setOrderSequence(outStockOrderPO.getOrderSequence());
            packageCodePrintDTOList.add(packageCodePrintDTO);
        });
        LOGGER.info("需要打印的子箱条码信息：{}", JSON.toJSONString(packageCodePrintDTOList));
        return packageCodePrintDTOList;
    }

    /**
     * 根据播种任务编号获取打印子箱条码信息
     */
    public List<PackageCodePrintDTO> listPackageCodePrintBySowTaskNo(Integer orgId, String sowTaskNo) {
        AssertUtils.notNull(orgId, "城市id不能为空");
        AssertUtils.notNull(sowTaskNo, "播种任务编号不能为空");
        List<PackageCodePrintDTO> resultPrintDTOList = new ArrayList<>();

        // 根据播种任务编号查询所有包装信息
        List<PackageCodePrintDTO> packageCodePrintDTOList =
                packageOrderItemMapper.listPackageCodePrintBySowTaskNo(orgId, sowTaskNo);
        if (CollectionUtils.isEmpty(packageCodePrintDTOList)) {
            return resultPrintDTOList;
        }

        // 按订单分组
        Map<String, List<PackageCodePrintDTO>> orderNoMap =
                packageCodePrintDTOList.stream().collect(Collectors.groupingBy(PackageCodePrintDTO::getRefOrderNo));
        resultPrintDTOList = getPackageCodePrints(orderNoMap);
        return resultPrintDTOList;
    }

    /**
     * 根据订单编号获取打印子箱条码信息（播种任务）
     */
    public List<PackageCodePrintDTO> listPackageCodePrintByOrderNos(Integer orgId, Integer warehouseId,
                                                                    List<String> orderNos) {
        AssertUtils.notNull(orgId, "城市id不能为空");
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        AssertUtils.notEmpty(orderNos, "订单编号不能为空");
        List<PackageCodePrintDTO> resultPrintDTOList = new ArrayList<>();

        // 根据订单编号查询所有包装信息
        List<PackageCodePrintDTO> packageCodePrintDTOS =
                packageOrderItemMapper.listPackageCodePrintByOrderNos(orgId, warehouseId, orderNos);
        if (CollectionUtils.isEmpty(packageCodePrintDTOS)) {
            throw new BusinessValidateException("当前订单没有生成子箱条码");
        }
        Map<String, List<PackageCodePrintDTO>> sowTaskNoMap =
                packageCodePrintDTOS.stream().filter(print -> print.getSowTaskNo() != null)
                        .collect(Collectors.groupingBy(PackageCodePrintDTO::getSowTaskNo));
        if (sowTaskNoMap == null || sowTaskNoMap.size() == 0) {
            throw new BusinessException("当前订单没有关联播种任务");
        }

        sowTaskNoMap.forEach((sowTaskNo, PackageCodePrintDTOs) -> {
            // 按订单分组
            Map<String, List<PackageCodePrintDTO>> orderNoMap =
                    PackageCodePrintDTOs.stream().collect(Collectors.groupingBy(PackageCodePrintDTO::getRefOrderNo));
            resultPrintDTOList.addAll(getPackageCodePrints(orderNoMap));
        });

        return resultPrintDTOList;
    }

    private List<PackageCodePrintDTO> getPackageCodePrints(Map<String, List<PackageCodePrintDTO>> packageCodePrintMap) {
        List<PackageCodePrintDTO> resultPrintDTOList = new ArrayList<>();

        // 内配中转单需要查询二级仓出库位
        List<OutStockOrderLocationDTO> outStockOrderLocationDTOS = new ArrayList<>();
        packageCodePrintMap.values().stream().flatMap(Collection::stream)
                .filter(print -> ConstantUtil.InternalDeliveryJudge(print.getAllotType()))
                .collect(Collectors.groupingBy(PackageCodePrintDTO::getBusinessId)).forEach((businessId, print) -> {
                    OutStockOrderLocationDTO outStockOrderLocationDTO = new OutStockOrderLocationDTO();
                    outStockOrderLocationDTO.setWarehouseId(print.get(0).getWarehouseId());
                    outStockOrderLocationDTO.setOmsOrderId(
                            StreamUtils.isNum(businessId) ? Long.valueOf(businessId) : print.get(0).getOrderId());
                    outStockOrderLocationDTO.setOrderNo(print.get(0).getRefOrderNo());

                    outStockOrderLocationDTOS.add(outStockOrderLocationDTO);
                });

        Map<String, OutStockOrderLocationDTO> outStockOrderLocationMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(outStockOrderLocationDTOS)) {
            List<OutStockOrderLocationDTO> outStockOrderLocations =
                    outStockOrderBL.findOutStockOrderLocation(outStockOrderLocationDTOS);
            if (CollectionUtils.isNotEmpty(outStockOrderLocations)) {
                outStockOrderLocationMap = outStockOrderLocations.stream().collect(
                        Collectors.toMap(OutStockOrderLocationDTO::getOrderNo, Function.identity(), (key1, key2) -> key2));
            }
        }

        Map<String, OutStockOrderLocationDTO> finalOutStockOrderLocationMap = outStockOrderLocationMap;
        packageCodePrintMap.forEach((refOrderNo, packageCodePrints) -> {

            // 内配单设置二级仓出库位
            OutStockOrderLocationDTO outStockOrderLocationDTO = finalOutStockOrderLocationMap.get(refOrderNo);
            if (outStockOrderLocationDTO != null) {
                packageCodePrints.forEach(print -> {
                    print.setToLocationId(outStockOrderLocationDTO.getToLocationId());
                    print.setToLocationName(outStockOrderLocationDTO.getToLocationName());
                    print.setToCity(outStockOrderLocationDTO.getToCity());
                    print.setToWarehouseName(outStockOrderLocationDTO.getToWarehouseName());
                });
            }

            // 按箱号分组
            Map<String, List<PackageCodePrintDTO>> boxCodeNoMap =
                    packageCodePrints.stream().collect(Collectors.groupingBy(PackageCodePrintDTO::getBoxCodeNo));
            boxCodeNoMap.forEach((boxCodeNo, boxCodeNoList) -> {
                PackageCodePrintDTO packageCodePrintDTO = new PackageCodePrintDTO();
                PackageCodePrintDTO boxPackageCode = boxCodeNoList.get(0);
                BeanUtils.copyProperties(boxPackageCode, packageCodePrintDTO);
                packageCodePrintDTO.setRefOrderNo(refOrderNo);
                packageCodePrintDTO.setBoxCodeNo(boxCodeNo);
                if (boxPackageCode.getSkuId() != null) {
                    packageCodePrintDTO.setSkuCount(
                            boxCodeNoList.stream().collect(Collectors.groupingBy(PackageCodePrintDTO::getSkuId)).size());
                }
                packageCodePrintDTO.setUnitTotalCount(boxCodeNoList.stream().map(PackageCodePrintDTO::getUnitTotalCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));

                resultPrintDTOList.add(packageCodePrintDTO);
            });
        });
        return resultPrintDTOList;
    }

    /**
     * 批量新增包装详情(知花知果)
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void savePackageBatchByOrderId(List<PackageOrderItemDTO> packageOrderItemDTOList, Integer orgId,
                                          Boolean pda) {
        AssertUtils.notEmpty(packageOrderItemDTOList, "包装参数不能为空");
        // 已拣货状态订单才可包装
        List<Long> refOrderIdList = packageOrderItemDTOList.stream().map(PackageOrderItemDTO::getRefOrderId).distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refOrderIdList)) {
            throw new BusinessException("订单id不能为空");
        }

        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.listOutStockOrderByOrderId(refOrderIdList);

        Map<Long, List<OutStockOrderPO>> outStockOrderMap =
                outStockOrderPOList.stream().collect(Collectors.groupingBy(OutStockOrderPO::getId));

        refOrderIdList.forEach(id -> {
            if (CollectionUtils.isEmpty(outStockOrderMap.get(id))) {
                throw new BusinessException("该订单(" + id + ")不存在");
            }

            OutStockOrderPO outStockOrderPO = outStockOrderMap.get(id).get(0);
            if (OutStockOrderStateEnum.已出库.getType() == (byte) outStockOrderPO.getState().intValue()
                    || OutStockOrderStateEnum.已取消.getType() == (byte) outStockOrderPO.getState().intValue()
                    || OutStockOrderStateEnum.已作废.getType() == (byte) outStockOrderPO.getState().intValue()
                    || OutStockOrderStateEnum.待审核.getType() == (byte) outStockOrderPO.getState().intValue()) {
                throw new BusinessValidateException("该订单:" + outStockOrderPO.getReforderno()
                        + OutStockOrderStateEnum.getEnum((byte) outStockOrderPO.getState().intValue()) + ",不能装箱");
            }

            String businessId = outStockOrderPO.getBusinessId();
            if (OrderConstant.ALLOT_TYPE_DEFAULT.equals(outStockOrderPO.getAllotType())) {
                businessId = outStockOrderPO.getId().toString();
            }

            Map<Long, OutStockOrderItemPO> outStockOrderItemMap = outStockOrderPO.getItems().stream()
                    .collect(Collectors.toMap(OutStockOrderItemPO::getId, Function.identity(), (key1, key2) -> key2));
            String finalBusinessId = businessId;
            // 设置订单序号
            packageOrderItemDTOList.forEach(p -> {
                if (p.getRefOrderId().equals(id)) {
                    p.setRefOrderNo(outStockOrderPO.getReforderno());
                    p.setOrderSequence(outStockOrderPO.getOrderSequence());
                    p.setBusinessId(finalBusinessId);
                    // oms订单id
                    p.setOrderBusinessId(outStockOrderPO.getBusinessId());
                }

                OutStockOrderItemPO outStockOrderItem = outStockOrderItemMap.get(p.getRefOrderItemId());
                if (outStockOrderItem != null) {
                    if (OrderConstant.ALLOT_TYPE_DEFAULT.equals(outStockOrderPO.getAllotType())) {
                        p.setBusinessItemId(outStockOrderItem.getId().toString());
                    } else {
                        p.setBusinessItemId(outStockOrderItem.getBusinessItemId());
                    }
                    p.setOrderItemBusinessId(outStockOrderItem.getBusinessItemId());
                }

            });
        });

        updatePackageOrderItems(packageOrderItemDTOList, pda);
    }

    /**
     * 校验包装数据,返回未完成的订单数据
     */
    public void checkPackageItems(List<PackageOrderItemDTO> packageOrderItemList, Integer orgId) {
        Map<Long, List<PackageOrderItemDTO>> packageOrderItemMap =
                packageOrderItemList.stream().collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderItemId));
        List<Long> itemIds = new ArrayList<>(packageOrderItemMap.keySet());
        List<PackageOrderItemDTO> overPackageOrderItems =
                packageOrderItemMapper.listPackageItemsByItemIds(itemIds, orgId);
        List<OutStockOrderItemPO> outStockOrderItemPOS = outStockOrderItemMapper.listByIds(itemIds);

        Map<Long, List<PackageOrderItemDTO>> overPackageOrderItemMap = new HashMap<>();
        Map<Long, List<OutStockOrderItemPO>> outStockOrderItemMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(overPackageOrderItems))
            overPackageOrderItemMap =
                    overPackageOrderItems.stream().collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderItemId));
        if (CollectionUtils.isNotEmpty(outStockOrderItemPOS))
            outStockOrderItemMap =
                    outStockOrderItemPOS.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getId));

        for (Map.Entry<Long, List<PackageOrderItemDTO>> itemMap : packageOrderItemMap.entrySet()) {
            Long itemId = itemMap.getKey();
            List<PackageOrderItemDTO> items = itemMap.getValue();

            BigDecimal overUnitTotalCount =
                    items.stream().map(PackageOrderItemDTO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);

            List<PackageOrderItemDTO> packageOrderItemDTOS = overPackageOrderItemMap.get(itemId);
            if (CollectionUtils.isNotEmpty(packageOrderItemDTOS)) {
                overUnitTotalCount = overUnitTotalCount.add(packageOrderItemDTOS.stream()
                        .map(PackageOrderItemDTO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }

            List<OutStockOrderItemPO> outStockOrderItem = outStockOrderItemMap.get(itemId);
            if (CollectionUtils.isNotEmpty(outStockOrderItem)) {
                BigDecimal unitTotalCount = outStockOrderItem.stream().map(OutStockOrderItemPO::getUnittotalcount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal surplusCount = unitTotalCount.subtract(overUnitTotalCount);
                if (surplusCount.compareTo(BigDecimal.ZERO) < 0) {
                    throw new BusinessValidateException("订单:" + packageOrderItemDTOS.get(0).getRefOrderNo() + "中的商品"
                            + items.get(0).getProductName() + "的包装数量超过该订单项中实际数量");
                }
            }
        }

        packageOrderItemList.addAll(overPackageOrderItems);
    }

    private void updatePackageOrderItems(List<PackageOrderItemDTO> packageOrderItemDTOList, Boolean pda) {
        updatePackageOrderItems(packageOrderItemDTOList, pda, true);
    }

    /**
     * 包装数据更新
     */
    private void updatePackageOrderItems(List<PackageOrderItemDTO> items, Boolean pda, boolean deleteByOrder) {
        LOGGER.info("是否按订单维度删除装箱数据: {}", deleteByOrder);
        // 1、删除原包装信息（PDA操作时不进行删除）
        Set<String> removePackageOrderNoList = new HashSet<>();
        if (!pda && deleteByOrder) {
            // 客户端按订单维度删除
            List<Long> refOrderIdList =
                    items.stream().map(PackageOrderItemDTO::getRefOrderId).distinct().collect(Collectors.toList());
            List<String> refOrderNoList =
                    items.stream().map(PackageOrderItemDTO::getRefOrderNo).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(refOrderIdList)) {
                LOGGER.info("订单 id 列表: {}", JSON.toJSONString(refOrderIdList));
                // 通过orderId查询
                List<PackageOrderItemDTO> oldPackageOrderItems =
                        packageOrderItemMapper.listPackageItemsByOrderIds(refOrderIdList, items.get(0).getOrgId());
                if (CollectionUtils.isNotEmpty(oldPackageOrderItems)) {
                    oldPackageOrderItems.stream()
                            .collect(Collectors.groupingBy(PackageOrderItemDTO::getOrgId,
                                    Collectors.mapping(PackageOrderItemDTO::getId, Collectors.toList())))
                            .forEach((orgId, idList) -> packageOrderItemMapper.batchDelete(idList, orgId));
                }
            }
            if (CollectionUtils.isNotEmpty(refOrderNoList)) {
                removePackageOrderNoList.addAll(refOrderNoList);
            }
        } else {
            // PDA按订单项维度删除
            List<Long> refOrderItemIdList =
                    items.stream().map(PackageOrderItemDTO::getRefOrderItemId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(refOrderItemIdList)) {
                // 通过itemId查询
                List<PackageOrderItemDTO> oldPackageOrderItems =
                        packageOrderItemMapper.listPackageItemsByItemIds(refOrderItemIdList, items.get(0).getOrgId());
                if (CollectionUtils.isNotEmpty(oldPackageOrderItems)) {
                    oldPackageOrderItems.stream()
                            .collect(Collectors.groupingBy(PackageOrderItemDTO::getOrgId,
                                    Collectors.mapping(PackageOrderItemDTO::getId, Collectors.toList())))
                            .forEach((orgId, idList) -> packageOrderItemMapper.batchDelete(idList, orgId));
                }
            }
        }

        // 2、新增包装信息
        List<PackageOrderItemPO> processedItems = items.stream().map(packageItem -> {
            PackageOrderItemPO item = new PackageOrderItemPO();
            BeanUtils.copyProperties(packageItem, item);
            item.setId(Long.valueOf(UuidUtil.generatorId()));
            // 箱号为 0 时, 表示删除箱号
            if (Objects.equals(packageItem.getBoxCode(), "0")) {
                item.setBoxCode("");
                item.setBoxCodeNo("");
            } else if (StringUtils.isEmpty(item.getBoxCodeNo())) {
                item.setBoxCodeNo(generateBoxCodeNo(packageItem));
            }
            if (item.getCreateTime() == null) {
                item.setCreateTime(new Date());
            }
            return item;
        }).collect(Collectors.toList());
        if (processedItems.isEmpty()) {
            return;
        }

        LOGGER.info("批量新增包装详情参数: {}", JSON.toJSONString(processedItems));

        // 2025-02-10 增加过滤条件，避免误删
        Set<String> deletedPackage =
                processedItems.stream().filter(it -> StringUtils.isEmpty(it.getBoxCode()) && it.getRefOrderItemId() > 0)
                        .map(PackageOrderItemPO::getRefOrderNo).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(deletedPackage)) {
            // 非pda过来的数据（播种打包），会先删除之前的打包信息，所以这里要通知tms删除之后再新增
            removePackageOrderNoList.addAll(deletedPackage);
        }
        tmsApiManager.removePackage(removePackageOrderNoList, processedItems.get(0).getWarehouseId());
        // 过滤掉箱号为空, id 为空的包装信息
        List<PackageOrderItemPO> filteredPackages = processedItems.stream()
                .filter(p -> StringUtils.isNotEmpty(p.getBoxCode()) && p.getId() != null).collect(Collectors.toList());
        if (filteredPackages.isEmpty()) {
            return;
        }
        packageOrderItemMapper.insertBatch(filteredPackages);
        // 发送消息到 OMS 同步分包信息
        packageSyncMQ.send(filteredPackages);
    }

    /**
     * 根据订单项id查询包装信息
     */
    public List<PackageOrderItemDTO> listPackageOrderItemByOrderItemId(List<Long> refOrderItemIds, Integer orgId) {
        return packageOrderItemMapper.listPackageItemsByItemIds(refOrderItemIds, orgId);
    }

    /**
     * 根据箱号完整编码查询内配单号
     */
    public List<String> listOrderNoByBoxCodeNo(String boxCodeNo) {
        AssertUtils.notNull(boxCodeNo, "箱号完整编码不能为空");
        return packageOrderItemMapper.listOrderNoByBoxCodeNo(boxCodeNo);
    }

    public PackageOrderDTO getPackageOrderByBarCode(Integer orgId, Integer warehouseId, String barCode) {
        // 根据条码判断是箱码还是订单号查找包装箱信息
        String orderNo = null;
        String boxCodeNo = null;
        if (barCode.startsWith("BZ")) {
            boxCodeNo = barCode;
        } else {
            orderNo = barCode;
        }
        List<PackageOrderItemDTO> packageOrderItemDTOS =
                packageOrderItemMapper.listPackageOrderItemByBarCode(orgId, warehouseId, boxCodeNo, orderNo);

        if (CollectionUtils.isEmpty(packageOrderItemDTOS) && boxCodeNo != null) {
            throw new BusinessValidateException("找不到对应装箱数据");
        }

        // 通过订单项查找订单信息和出库位
        // 目前一个箱子只装一个订单
        if (orderNo == null) {
            orderNo = packageOrderItemDTOS.get(0).getRefOrderNo();
        }
        OutStockOrderPO outStockOrderPO = outStockOrderMapper.selectByRefOrderNo(orderNo, orgId, warehouseId);

        if (outStockOrderPO == null) {
            throw new BusinessValidateException("找不到对应订单");
        }

        List<OutStockOrderItemPrintPO> outStockOrderItemPrintPOS =
                outStockOrderItemMapper.listOutStockOrderPrintByOrderId(orgId, outStockOrderPO.getId());
        if (CollectionUtils.isEmpty(outStockOrderItemPrintPOS)) {
            throw new BusinessValidateException("找不到对应集货任务");
        }

        List<OutStockOrderItemPrintPO> noFinishedOrders = outStockOrderItemPrintPOS.stream()
                .filter(item -> item.getTaskState() != TaskStateEnum.已完成.getType()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noFinishedOrders)) {
            throw new BusinessValidateException("请先完成该订单关联的拣货任务");
        }
        OutStockOrderItemPrintPO order = outStockOrderItemPrintPOS.get(0);
        if (order.getState() == SowTaskStateEnum.待播种.getType() || order.getState() == SowTaskStateEnum.播种中.getType()) {
            throw new BusinessValidateException("找不到对应集货任务");
        }
        PackageOrderDTO packageOrderDTO = new PackageOrderDTO();
        packageOrderDTO.setOutStockOrderId(outStockOrderPO.getId());
        packageOrderDTO.setRefOrderNo(outStockOrderPO.getReforderno());
        packageOrderDTO.setDetailAddress(outStockOrderPO.getDetailaddress());
        packageOrderDTO.setRouteName(outStockOrderPO.getRouteName());
        packageOrderDTO.setToLocationId(order.getToLocationId());
        packageOrderDTO.setToLocationName(order.getToLocationName());

        BigDecimal packagedCount = BigDecimal.ZERO;
        BigDecimal packagedUnitCount = BigDecimal.ZERO;
        int count = 0;
        if (CollectionUtils.isNotEmpty(packageOrderItemDTOS)) {
            List<Long> outStockOrderItemIds = outStockOrderItemPrintPOS.stream()
                    .map(OutStockOrderItemPrintPO::getOutStockOrderItemId).collect(Collectors.toList());
            packageOrderItemDTOS = packageOrderItemDTOS.stream()
                    .filter(item -> outStockOrderItemIds.contains(item.getRefOrderItemId())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(packageOrderItemDTOS)) {
                count = (int) packageOrderItemDTOS.stream().map(PackageOrderItemDTO::getBoxCodeNo).distinct().count();
                packagedCount = packageOrderItemDTOS.stream().map(PackageOrderItemDTO::getPackageCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                packagedUnitCount = packageOrderItemDTOS.stream().map(PackageOrderItemDTO::getUnitCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
        }

        BigDecimal packageCount = outStockOrderItemPrintPOS.stream().map(OutStockOrderItemPrintPO::getPackageCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal unitCount = outStockOrderItemPrintPOS.stream().map(OutStockOrderItemPrintPO::getUnitCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        packageOrderDTO.setPackingCount(count);
        packageOrderDTO.setPackageCount(packageCount.subtract(packagedCount));
        packageOrderDTO.setUnitCount(unitCount.subtract(packagedUnitCount));

        packageOrderDTO.setState(order.getState());
        return packageOrderDTO;
    }

    public List<PackageOrderItemDTO> listByOrderNos(Integer orgId, Integer warehouseId, List<String> refOrderNos) {
        return packageOrderItemMapper.listByOrderNos(orgId, warehouseId, refOrderNos);
    }

    public PageList<PackageOrderItemDTO> listPackageOrderItemAll(PackageOrderItemSO packageOrderItemSO) {
        AssertUtils.notNull(packageOrderItemSO, "参数不能为空");
        AssertUtils.notNull(packageOrderItemSO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(packageOrderItemSO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(packageOrderItemSO.getRefOrderNo(), "单号不能为空");
        OutStockOrderPO outStockOrderPO = outStockOrderMapper.selectByRefOrderNo(packageOrderItemSO.getRefOrderNo(),
                packageOrderItemSO.getOrgId(), packageOrderItemSO.getWarehouseId());
        if (null == outStockOrderPO) {
            throw new BusinessException("该订单不存在");
        }
        PageResult<PackageOrderItemDTO> packageOrderItemDTOPageResult = packageOrderItemMapper.listPackageOrderItem(
                packageOrderItemSO, packageOrderItemSO.getCurrentPage(), packageOrderItemSO.getPageSize());
        PageList<PackageOrderItemDTO> pageList = packageOrderItemDTOPageResult.toPageList();
        return pageList;
    }

    /**
     * 根据单号查询已播种的包装信息
     */
    public Map<String, List<PackageOrderItemDTO>> listSowPackageOrderItems(List<String> orderNos, Integer orgId,
                                                                           Integer warehouseId) {
        Map<String, List<PackageOrderItemDTO>> packageOrderMap = new HashMap<>();
        List<PackageOrderItemDTO> oldPackageOrderItemDTOS =
                packageOrderItemMapper.listSowPackageOrderItems(orgId, warehouseId, orderNos);
        if (CollectionUtils.isNotEmpty(oldPackageOrderItemDTOS)) {
            List<PackageOrderItemDTO> packageOrderItemDTOS = new ArrayList<>();
            oldPackageOrderItemDTOS.stream().forEach(it -> {
                if (it.getPackageType() != null && it.getPackageType().equals(PackageTypeEnum.二次分拣.getType())
                        && StringUtils.isNotEmpty(it.getBoxCode())) {
                    String[] split = it.getBoxCode().split(",");
                    for (String s : split) {
                        PackageOrderItemDTO newPackageOrderItemDTO = new PackageOrderItemDTO();
                        BeanUtils.copyProperties(it, newPackageOrderItemDTO);
                        newPackageOrderItemDTO.setBoxCode(s);
                        newPackageOrderItemDTO.setBoxCodeNo(s);
                        packageOrderItemDTOS.add(newPackageOrderItemDTO);
                    }
                } else {
                    packageOrderItemDTOS.add(it);
                }
            });
            packageOrderMap =
                    packageOrderItemDTOS.stream().collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderNo));
            packageOrderMap.forEach((orderNo, items) -> {
                items.sort(
                        Comparator.comparing(PackageOrderItemDTO::getOperatingTime, Comparator.nullsLast(Date::compareTo))
                                .reversed());
                for (int i = 0; i < items.size(); i++) {
                    items.get(i).setOrderSequence(i);
                }
            });
        }
        return packageOrderMap;
    }

    /**
     * 整拖复核列表查询
     */
    public PageList<PalletOrderDTO> pageListPalletOrders(PalletOrderQueryDTO palletOrderQueryDTO) {
        LOGGER.info("整拖复核列表查询 入参：{}", JSON.toJSONString(palletOrderQueryDTO));
        if (palletOrderQueryDTO.getStartTime() == null && palletOrderQueryDTO.getEndTime() == null) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.add(Calendar.MONTH, -1);
            palletOrderQueryDTO.setStartTime(cal.getTime());
        }
        PageResult<PalletOrderDTO> result = packageOrderItemMapper.pageListPalletOrders(palletOrderQueryDTO,
                palletOrderQueryDTO.getPageNum(), palletOrderQueryDTO.getPageSize());
        PageList<PalletOrderDTO> pageList = result.toPageList();
        // 查询拣货信息
        if (CollectionUtils.isNotEmpty(result.getResult())) {
            List<String> boxCodeNos = result.stream().map(PalletOrderDTO::getBoxCodeNo).collect(Collectors.toList());
            List<PalletOrderDTO> palletOrderDTOS = packageOrderItemMapper.findPackageBatchTaskItem(boxCodeNos,
                    palletOrderQueryDTO.getOrgId(), palletOrderQueryDTO.getWarehouseId());
            LOGGER.info("整拖复核列表查询 palletOrderDTOS结果：{}", JSON.toJSONString(palletOrderDTOS));
            if (CollectionUtils.isNotEmpty(palletOrderDTOS)) {
                // 兼容旧数据
                List<Long> packageItemIds =
                        palletOrderDTOS.stream().filter(palletOrder -> palletOrder.getLocationId() == null)
                                .map(PalletOrderDTO::getId).collect(Collectors.toList());
                Map<String, PalletOrderDTO> palletOrderMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(packageItemIds)) {
                    List<PalletOrderDTO> palletOrders =
                            packageOrderItemMapper.findPackageBatchTaskItemByIds(packageItemIds);
                    if (CollectionUtils.isNotEmpty(palletOrders)) {
                        palletOrderMap = palletOrders.stream().collect(Collectors.toMap(PalletOrderDTO::getBoxCodeNo,
                                Function.identity(), (key1, key2) -> key2 != null ? key2 : key1));
                    }
                }
                Map<String, PalletOrderDTO> finalPalletOrderMap = palletOrderMap;

                // 获取取货司机
                Map<Long, String> orderIdMap = new HashMap<>();
                List<Long> outStockorderIds =
                        palletOrderDTOS.stream().filter(palletOrder -> palletOrder.getRefOrderId() != null)
                                .map(PalletOrderDTO::getRefOrderId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outStockorderIds)) {
                    OutStockOrderSearchSO searchSO = new OutStockOrderSearchSO();
                    searchSO.setOrgId(palletOrderQueryDTO.getOrgId());
                    searchSO.setWareHouseId(palletOrderQueryDTO.getWarehouseId());
                    searchSO.setOrderIds(outStockorderIds);
                    LOGGER.info("整拖复核列表查询 searchSO入参：{}", JSON.toJSONString(searchSO));
                    List<OutStockOrderPO> outStockOrderPOS =
                            outStockOrderMapper.queryPickUpUserNameByCondition(searchSO);
                    LOGGER.info("整拖复核列表查询 outStockOrderPOS结果：{}", JSON.toJSONString(outStockOrderPOS));
                    if (CollectionUtils.isNotEmpty(outStockOrderPOS)) {
                        orderIdMap =
                                outStockOrderPOS.stream().filter(p -> StringUtils.isNotEmpty(p.getPickUpUserName()))
                                        .collect(Collectors.toMap(p -> p.getId(), p -> p.getPickUpUserName(), (v1, v2) -> v1));
                    }
                }

                Map<Long, String> finalOrderIdMap = orderIdMap;

                List<PalletOrderDTO> palletOrderList = new ArrayList<>();
                palletOrderDTOS.stream().collect(Collectors.groupingBy(PalletOrderDTO::getBoxCodeNo))
                        .forEach((boxCodeNo, list) -> {
                            PalletOrderDTO palletOrderDTO = new PalletOrderDTO();
                            palletOrderDTO.setBoxCodeNo(boxCodeNo);
                            palletOrderDTO.setLocationId(list.get(0).getLocationId());
                            palletOrderDTO.setLocationName(list.get(0).getLocationName());
                            palletOrderDTO.setSorter(list.get(0).getSorter());

                            // 兼容旧数据
                            PalletOrderDTO oldPalletOrder = finalPalletOrderMap.get(boxCodeNo);
                            if (oldPalletOrder != null) {
                                palletOrderDTO.setLocationId(oldPalletOrder.getLocationId());
                                palletOrderDTO.setLocationName(oldPalletOrder.getLocationName());
                                palletOrderDTO.setSorter(oldPalletOrder.getSorter());
                            }

                            palletOrderDTO.setBatchNo(list.get(0).getBatchNo());
                            palletOrderDTO.setDriverName(finalOrderIdMap.get(list.get(0).getRefOrderId()));

                            BigDecimal packageCount = list.stream().filter(StreamUtils.distinctByKey(PalletOrderDTO::getId))
                                    .map(PalletOrderDTO::getPackageCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal unitCount = list.stream().filter(StreamUtils.distinctByKey(PalletOrderDTO::getId))
                                    .map(PalletOrderDTO::getUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            long skuCount = list.stream().map(PalletOrderDTO::getSkuId).distinct().count();
                            palletOrderDTO.setPackageCount(packageCount);
                            palletOrderDTO.setUnitCount(unitCount);
                            palletOrderDTO.setSkuCount((int) skuCount);
                            Date createTime = list.stream().filter(item -> item.getCreateTime() != null)
                                    .map(PalletOrderDTO::getCreateTime).max(Comparator.comparing(Date::getTime)).get();
                            palletOrderDTO.setCreateTime(createTime);
                            Optional<Date> operatingTime = list.stream().filter(item -> item.getOperatingTime() != null)
                                    .map(PalletOrderDTO::getOperatingTime).max(Comparator.comparing(Date::getTime));
                            palletOrderDTO.setOperatingTime(operatingTime.orElse(createTime));

                            palletOrderList.add(palletOrderDTO);
                        });
                palletOrderList.sort(Comparator.comparing(PalletOrderDTO::getOperatingTime).reversed());
                LOGGER.info("整拖复核列表查询 palletOrderList：{}", JSON.toJSONString(palletOrderList));
                pageList.setDataList(palletOrderList);
            }
        }
        return pageList;
    }

    /**
     * 整拖复核明细查询
     */
    public List<PalletOrderItemDTO> listPalletOrderItems(PalletOrderQueryDTO palletOrderQueryDTO) {
        List<PalletOrderItemDTO> palletOrderItemDTOS = new ArrayList<>();
        List<PalletOrderItemDTO> palletOrderItems = packageOrderItemMapper.listPalletOrderItems(palletOrderQueryDTO);
        if (CollectionUtils.isNotEmpty(palletOrderItems)) {

            // 兼容旧数据
            List<Long> orderItemIds = palletOrderItems.stream().map(PalletOrderItemDTO::getRefOrderItemId).distinct()
                    .collect(Collectors.toList());
            List<OrderItemTaskInfoPO> orderItemTaskInfoPOS = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(orderItemIds)) {
                orderItemTaskInfoPOS = orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderItemIds(orderItemIds);
            }
            List<OrderItemTaskInfoPO> finalOrderItemTaskInfoPOS = orderItemTaskInfoPOS;

            palletOrderItems.stream().collect(Collectors.groupingBy(PalletOrderItemDTO::getRefOrderItemId))
                    .forEach((orderItemId, items) -> {
                        PalletOrderItemDTO palletOrderItemDTO = new PalletOrderItemDTO();
                        BeanUtils.copyProperties(items.get(0), palletOrderItemDTO);

                        List<OrderItemTaskInfoPO> itemTaskInfoPOS = finalOrderItemTaskInfoPOS.stream()
                                .filter(item -> orderItemId.equals(item.getRefOrderItemId())).collect(Collectors.toList());
                        // 兼容旧数据
                        if (CollectionUtils.isNotEmpty(itemTaskInfoPOS)) {
                            BigDecimal unitTotalCount = itemTaskInfoPOS.stream().map(OrderItemTaskInfoPO::getOverSortCount)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal[] divideAndRemainder =
                                    unitTotalCount.divideAndRemainder(items.get(0).getSpecQuantity());
                            if (palletOrderItemDTO.getOutStockOrderId() == null) {
                                palletOrderItemDTO.setOrderPackageCount(BigDecimal.ZERO);
                                palletOrderItemDTO.setOrderUnitCount(BigDecimal.ZERO);
                                palletOrderItemDTO.setOrderUnitTotalCount(BigDecimal.ZERO);
                            } else {
                                palletOrderItemDTO.setOrderPackageCount(divideAndRemainder[0]);
                                palletOrderItemDTO.setOrderUnitCount(divideAndRemainder[1]);
                                palletOrderItemDTO.setOrderUnitTotalCount(unitTotalCount);
                            }
                            palletOrderItemDTO.setPickingPackageCount(divideAndRemainder[0]);
                            palletOrderItemDTO.setPickingUnitCount(divideAndRemainder[1]);
                            palletOrderItemDTO.setPickingUnitTotalCount(unitTotalCount);
                        }

                        palletOrderItemDTOS.add(palletOrderItemDTO);
                    });
        }
        return palletOrderItemDTOS;
    }

    /**
     * 整拖复核
     */
    @Transactional(rollbackFor = Exception.class)
    public void packageOrderItemReview(List<PackageOrderItemDTO> packageOrderItemDTOS) {
        if (CollectionUtils.isNotEmpty(packageOrderItemDTOS)) {
            // 查找对应波次，修改波次状态
            List<Long> packageOrderItemIds =
                    packageOrderItemDTOS.stream().map(PackageOrderItemDTO::getId).collect(Collectors.toList());
            List<String> batchNos = packageOrderItemMapper.findBatchNoByIds(packageOrderItemIds);
            packageOrderItemMapper.batchReview(packageOrderItemDTOS);
            batchNos.forEach(batchNo -> {
                // batchOrderBL.updateBatchStateByBatchNo(batchNo, packageOrderItemDTOS.get(0).getOperatorName(),
                // packageOrderItemDTOS.get(0).getOperatorId());
                batchFinishedBL.completeWave(batchNo, packageOrderItemDTOS.get(0).getOperatorId(),
                        packageOrderItemDTOS.get(0).getOrgId());
            });
        }
    }

    /**
     * 通过波次id查询包装箱信息
     */
    public List<PackageOrderItemDTO> findPackageOrderItemsByBatchId(String batchId, Integer orgId, Byte packageType,
                                                                    List<Byte> states) {
        return packageOrderItemMapper.findPackageOrderItemsByBatchId(batchId, orgId, packageType, states);
    }

    /**
     * 拣货装箱
     */
    @Deprecated
    public void pickingAndPacking(Integer warehouseId, Integer cityId, Integer userId, String userName,
                                  List<OrderItemTaskInfoPO> orderItemTaskInfoPOS, BatchTaskItemDTO batchTaskItemDTO,
                                  List<PackageCodeSortDTO> packageCodeSortDTOS, List<PackageOrderItemDTO> packageOrderItemDTOS) {
        if (CollectionUtils.isEmpty(packageCodeSortDTOS)) {
            return;
        }
        // LOGGER.info("拣货装箱参数orderItemTaskInfoPOS:{},batchTaskItemDTO:{},packageCodeSortDTOS:{}",
        // JSON.toJSONString(orderItemTaskInfoPOS), JSON.toJSONString(batchTaskItemDTO),
        // JSON.toJSON(packageCodeSortDTOS));
        for (PackageCodeSortDTO boxCodeDTO : packageCodeSortDTOS) {
            orderItemTaskInfoPOS.stream()
                    .filter(item -> item.getBatchTaskItemId().equals(batchTaskItemDTO.getId())
                            && item.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0)
                    .collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId))
                    .forEach((orderItemId, items) -> {
                        PackageOrderItemDTO packageOrderItemDTO = new PackageOrderItemDTO();
                        packageOrderItemDTO.setPassageCode(boxCodeDTO.getPassageCode());
                        packageOrderItemDTO.setOrgId(cityId);
                        packageOrderItemDTO.setWarehouseId(warehouseId);
                        packageOrderItemDTO.setRefOrderId(items.get(0).getRefOrderId());
                        packageOrderItemDTO.setRefOrderItemId(orderItemId);
                        packageOrderItemDTO.setBoxCode(boxCodeDTO.getBoxCode());
                        packageOrderItemDTO.setProductName(batchTaskItemDTO.getProductName());
                        packageOrderItemDTO.setSkuId(batchTaskItemDTO.getSkuId());
                        packageOrderItemDTO.setSpecName(batchTaskItemDTO.getSpecName());
                        packageOrderItemDTO.setSpecQuantity(batchTaskItemDTO.getSpecQuantity());
                        packageOrderItemDTO.setPackageName(batchTaskItemDTO.getPackageName());
                        if (boxCodeDTO.getPackageCount().compareTo(BigDecimal.ZERO) == 0
                                && boxCodeDTO.getUnitCount().compareTo(BigDecimal.ZERO) == 0) {
                            packageOrderItemDTO.setUnitTotalCount(BigDecimal.ZERO);
                            packageOrderItemDTO.setPackageCount(BigDecimal.ZERO);
                            packageOrderItemDTO.setUnitCount(BigDecimal.ZERO);
                        } else {
                            BigDecimal unitTotalCount = items.stream().map(OrderItemTaskInfoPO::getOverSortCount)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            packageOrderItemDTO.setUnitTotalCount(unitTotalCount);
                            BigDecimal[] divideAndRemainder =
                                    unitTotalCount.divideAndRemainder(packageOrderItemDTO.getSpecQuantity());
                            packageOrderItemDTO.setPackageCount(divideAndRemainder[0]);
                            packageOrderItemDTO.setUnitCount(divideAndRemainder[1]);
                        }
                        packageOrderItemDTO.setUnitName(batchTaskItemDTO.getUnitName());
                        packageOrderItemDTO.setRemark(null);
                        packageOrderItemDTO.setCreateUser(String.valueOf(userId));
                        packageOrderItemDTO.setPackageType(boxCodeDTO.getPackageType());
                        packageOrderItemDTO.setOperatorId(userId);
                        packageOrderItemDTO.setOperatorName(userName);
                        if (boxCodeDTO.getPackageType() != null
                                && boxCodeDTO.getPackageType() == PackageTypeEnum.托盘.getType()) {
                            packageOrderItemDTO.setReviewState(PackageReviewStateEnum.待复核.getType());
                        } else {
                            packageOrderItemDTO.setReviewState(PackageReviewStateEnum.已复核.getType());
                        }

                        Optional<PackageOrderItemDTO> first =
                                packageOrderItemDTOS.stream().filter(item -> item.getRefOrderItemId().equals(orderItemId)
                                        && item.getBoxCode().equals(boxCodeDTO.getBoxCode())).findFirst();
                        if (first.isPresent()) {
                            PackageOrderItemDTO packageOrderItem = first.get();
                            packageOrderItem.setPackageCount(
                                    packageOrderItem.getPackageCount().add(packageOrderItemDTO.getPackageCount()));
                            packageOrderItem
                                    .setUnitCount(packageOrderItem.getUnitCount().add(packageOrderItemDTO.getUnitCount()));
                            packageOrderItem.setUnitTotalCount(
                                    packageOrderItem.getUnitTotalCount().add(packageOrderItemDTO.getUnitTotalCount()));
                        } else {
                            packageOrderItemDTOS.add(packageOrderItemDTO);
                        }
                        packageOrderItemDTO.setBoxCodeNo(boxCodeDTO.getBoxCodeNo());
                    });
        }
        // LOGGER.info("拣货装箱数据:{}", JSON.toJSONString(packageOrderItemDTOS));
    }

    /**
     * 针对pda提交的装箱按订单互斥
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @MarkMethodInvokeFlag(key = RedisConstant.SUP_F + "UserOnlineCache", warehouseId = "#packageItems[0].warehouseId",
            userId = "#packageItems[0].operatorId")
    @DistributeLock(conditions = "#refOrderNo", key = "batchSyncOMSOrder", lockType = DistributeLock.LockType.MUTEXLOCK)
    public void savePackageBatchByPDA(String refOrderNo, List<PackageOrderItemDTO> packageItems, boolean isPDA) {
        savePackageBatch(packageItems, isPDA);
        batchFinishedLackOrderBL.handleNotEqualPackageItem(packageItems);
    }

    /**
     * 根据订单编号删除包装信息
     */
    public void removePackageByRefOrderNos(List<String> refOrderNos, Integer orgId, Integer warehouseId) {
        removePackageByRefOrderNos(refOrderNos, orgId, warehouseId, null);
    }

    public void removePackageByRefOrderNos(List<String> refOrderNos, Integer orgId, Integer warehouseId,
                                           Integer userId) {
        packageOrderItemMapper.removePackageByRefOrderNos(refOrderNos, orgId, warehouseId);
        // 发送消息到OMS同步分包信息
        packageSyncMQ.sendRemove(refOrderNos, orgId, warehouseId);
        // 通知 tms
        tmsApiManager.removePackage(refOrderNos, warehouseId, userId);
    }

    /**
     * 创建播种托盘号详情
     */
    public List<PackageOrderItemDTO> createPackageorderItem(List<PackageOrderItemPO> pos) {
        packageOrderItemMapper.insertBatch(pos);
        return PackageOrderItemConvertor.convertPo2Dto(pos);
    }

    public List<PackageOrderItemDTO> findPackageOrderItem(SecondSortQueryDTO queryDTO) {
        List<PackageOrderItemPO> pos = packageOrderItemMapper.listSowBoxcodeItems(queryDTO);
        return PackageOrderItemConvertor.convertPo2Dto(pos);
    }

    public List<PackageOrderItemPO> listSowBoxcodeItems(SecondSortQueryDTO queryDTO) {
        List<PackageOrderItemPO> pos = packageOrderItemMapper.listSowBoxcodeItems(queryDTO);
        if (CollectionUtils.isNotEmpty(pos)) {
            pos.stream().filter(it -> it.getUnitTotalCount() != null).forEach(it -> {
                BigDecimal[] counts = it.getUnitTotalCount().divideAndRemainder(it.getSpecQuantity());
                it.setPackageCount(counts[0]);
                it.setUnitCount(counts[1]);
            });
        }
        return pos;
    }

    public List<PackageOrderItemDTO> listPackageOrderItemByOrderIdList(PackageOrderItemSO packageOrderItemSO) {
        verifyGetBoxParam(packageOrderItemSO);
        List<PackageOrderItemPO> packageOrderItemPOS =
                packageOrderItemMapper.listPackageOrderItemByOrderIdList(packageOrderItemSO);
        List<PackageOrderItemDTO> packageOrderItemList = PackageOrderItemConvertor.convertPo2Dto(packageOrderItemPOS);
        return packageOrderItemList;
    }

    /**
     * 获取批次订单中最大箱 归总箱总数
     */
    public List<PackageOrderItemDTO> getBoxMaxCountByOrderIdList(PackageOrderItemSO query) {
        verifyGetBoxParam(query);
        List<PackageOrderItemPO> result = packageOrderItemMapper.getBoxMaxCountByOrderIdList(query);
        return PackageOrderItemConvertor.convertPo2Dto(result);
    }

    private void verifyGetBoxParam(PackageOrderItemSO packageOrderItemSO) {
        AssertUtils.notNull(packageOrderItemSO, "参数不能为空");
        AssertUtils.notNull(packageOrderItemSO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(packageOrderItemSO.getOrderIdList(), "单号不能为空");
    }

    /**
     * 获取订单打包信息, 给客户端 订单详情页面使用<br/>
     * 其中的 refOrderItemId 将会被替换成 businessItemId
     *
     * @param packageOrderItemSO 查询条件
     */
    public List<PackageOrderItemDTO> queryPackageOrderItem(PackageOrderItemSO packageOrderItemSO) {
        AssertUtils.notNull(packageOrderItemSO, "查询条件不能为空");
        Integer orgId = packageOrderItemSO.getOrgId();
        Integer warehouseId = packageOrderItemSO.getWarehouseId();
        String refOrderNo = packageOrderItemSO.getRefOrderNo();
        AssertUtils.notNull(orgId, "查询条件不能为空");
        AssertUtils.notNull(warehouseId, "查询条件不能为空");
        AssertUtils.notNull(refOrderNo, "查询条件不能为空");
        List<String> refOrderNos = Collections.singletonList(refOrderNo);
        OutStockOrderPO outStockOrder = outStockOrderMapper.selectByRefOrderNo(refOrderNo, orgId, warehouseId);
        if (outStockOrder == null) {
            return Collections.emptyList();
        }
        List<String> businessIds = Collections.singletonList(outStockOrder.getBusinessId());
        List<OrderDocumentDTO> centerOrder = orderCenterBL.findOrderCenterByPage(businessIds);
        if (centerOrder.isEmpty()) {
            return Collections.emptyList();
        }
        boolean isAllot = OrderConstant.ALLOT_TYPE_ALLOCATION.equals(outStockOrder.getAllotType());
        if (isAllot) {
            OrderPickDocumentDTO orderPick = centerOrder.get(0).getOrderPick();
            orgId = orderPick.getOrgId().intValue();
            warehouseId = orderPick.getWarehouseId().intValue();
        }
        List<PackageOrderItemDTO> packageOrderItems =
                packageOrderItemMapper.listByOrderNos(orgId, warehouseId, refOrderNos);
        if (packageOrderItems.isEmpty()) {
            return packageOrderItems;
        }
        Set<Long> outStockOrderItemIds =
                packageOrderItems.stream().map(PackageOrderItemDTO::getRefOrderItemId).collect(Collectors.toSet());
        Map<Long, String> itemMap = outStockOrderItemMapper.findByItemIds(outStockOrderItemIds, null).stream()
                .collect(Collectors.toMap(OutStockOrderItemDTO::getId, OutStockOrderItemDTO::getBusinessItemId));
        for (PackageOrderItemDTO item : packageOrderItems) {
            item.setRefOrderItemId(Long.valueOf(itemMap.getOrDefault(item.getRefOrderItemId(), "-1")));
        }
        return packageOrderItems;
    }

    /**
     * 扫描订单箱号查询包装箱数
     *
     * @param dto
     * @return
     */
    public List<ScanOrderPackageInfoResultDTO> scanOrderPackageInfo(ScanOrderPackageInfoQueryDTO dto) {
        List<Integer> orderStateList = new ArrayList<>();
        orderStateList.add((int) OutStockOrderStateEnum.已拣货.getType());
        orderStateList.add((int) OutStockOrderStateEnum.拣货中.getType());

        List<PackageOrderItemPO> packageOrderItemPOList = packageOrderItemMapper
                .findPackageInfoByBoxCodeNo(dto.getOrgId(), dto.getWarehouseId(), dto.getBoxCodeNo(), orderStateList);
        if (CollectionUtils.isEmpty(packageOrderItemPOList)) {
            return Collections.emptyList();
        }

        List<Long> refOrderItemIds = packageOrderItemPOList.stream().map(PackageOrderItemPO::getRefOrderItemId)
                .distinct().collect(Collectors.toList());

        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
                orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderItemIds(refOrderItemIds);

        PackageOrderItemPO packageOrderItemPO = packageOrderItemPOList.stream().findFirst().get();
        List<String> orderNoList = packageOrderItemPOList.stream().map(PackageOrderItemPO::getRefOrderNo).distinct()
                .collect(Collectors.toList());
        List<Long> orderIdList = packageOrderItemPOList.stream().map(PackageOrderItemPO::getRefOrderId).distinct()
                .collect(Collectors.toList());

        List<PackageOrderItemDTO> totalPackageItemList =
                packageOrderItemMapper.listPackageItemsByOrderIds(orderIdList, dto.getOrgId());

        Map<Long, String> orderNoIdMap = packageOrderItemPOList.stream().collect(
                Collectors.toMap(PackageOrderItemPO::getRefOrderId, PackageOrderItemPO::getRefOrderNo, (v1, v2) -> v1));

        List<String> batchTaskIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderItemTaskInfoPOList)) {
            batchTaskIds.addAll(orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getBatchTaskId).distinct()
                    .collect(Collectors.toList()));
        }

        List<OutStockOrderItemPO> orderItemPOList = outStockOrderItemMapper.listByIds(refOrderItemIds);

        final PackageOrderItemConvertor.OutStockLocationBO locationBO =
                packageOrderItemConvertor.getOutStockLocation(batchTaskIds, orderItemPOList, packageOrderItemPO.getOrgId());

        List<Long> orderIds = orderItemTaskInfoMapper.listRefOrderIdByBatchTaskIds(batchTaskIds);

        List<OutStockOrderPO> orderList = outStockOrderMapper.selectByIds(orderIds);

        validateMergePickOrderHaveBindPallet(orderList, packageOrderItemPO);

        OutStockOrderPO orderPO = orderList.stream()
                .filter(m -> m.getOrderSequence().equals(packageOrderItemPO.getOrderSequence())).findAny().get();

        ScanOrderPackageInfoResultDTO scanOrderPackageInfoResultDTO = new ScanOrderPackageInfoResultDTO();
        scanOrderPackageInfoResultDTO.setOrderSequence(packageOrderItemPO.getOrderSequence());
        scanOrderPackageInfoResultDTO.setBoxCodeNo(packageOrderItemPO.getBoxCodeNo());
        scanOrderPackageInfoResultDTO.setWarehouseId(packageOrderItemPO.getWarehouseId());
        scanOrderPackageInfoResultDTO.setTotalBoxCount(getPackageCount(orderList, packageOrderItemPO));
        scanOrderPackageInfoResultDTO.setOrderIdList(orderIdList);
        scanOrderPackageInfoResultDTO.setRefOrderNoList(orderNoList);
        scanOrderPackageInfoResultDTO.setBatchTaskIds(batchTaskIds);
        scanOrderPackageInfoResultDTO.setLocationId(locationBO.locationId);
        scanOrderPackageInfoResultDTO.setLocationName(locationBO.locationName);
        scanOrderPackageInfoResultDTO.setPalletCount(packageOrderItemConvertor.getPalletNo(locationBO.locationId));
        scanOrderPackageInfoResultDTO.setShopName(orderPO.getShopname());

        List<ScanOrderPackageInfoBatchTaskResultDTO> orderBatchTaskInfoList =
                orderItemTaskInfoPOList.stream().map(orderItemTaskInfoPO -> {
                    ScanOrderPackageInfoBatchTaskResultDTO batchTaskResultDTO =
                            new ScanOrderPackageInfoBatchTaskResultDTO();
                    batchTaskResultDTO.setOrderId(orderItemTaskInfoPO.getRefOrderId());
                    batchTaskResultDTO.setBatchTaskId(orderItemTaskInfoPO.getBatchTaskId());
                    batchTaskResultDTO.setOrderNo(orderNoIdMap.get(orderItemTaskInfoPO.getRefOrderId()));
                    batchTaskResultDTO.setLocationId(locationBO.locationId);
                    batchTaskResultDTO.setLocationName(locationBO.locationName);
                    return batchTaskResultDTO;
                }).collect(Collectors.toMap(task -> task.getOrderId() + "-" + task.getBatchTaskId(), task -> task,
                        (existing, replacement) -> existing)).values().stream().collect(Collectors.toList());

        scanOrderPackageInfoResultDTO.setOrderBatchTaskInfoList(orderBatchTaskInfoList);

        return Collections.singletonList(scanOrderPackageInfoResultDTO);
    }

    private int getPackageCount(List<OutStockOrderPO> orderList, PackageOrderItemPO packageOrderItemPO) {
        List<Long> totalOrderIds =
                orderList.stream().filter(m -> m.getOrderSequence().equals(packageOrderItemPO.getOrderSequence()))
                        .map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());

        List<PackageOrderItemDTO> totalPackageItemList =
                packageOrderItemMapper.listPackageItemsByOrderIds(totalOrderIds, packageOrderItemPO.getOrgId());

        long packageCount = totalPackageItemList.stream().map(PackageOrderItemDTO::getBoxCodeNo).distinct().count();

        return (int) packageCount;
    }

    private void validateMergePickOrderHaveBindPallet(List<OutStockOrderPO> orderList,
                                                      PackageOrderItemPO packageOrderItemPO) {
        List<Long> totalOrderIds =
                orderList.stream().filter(m -> m.getOrderSequence().equals(packageOrderItemPO.getOrderSequence()))
                        .map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());

        if (totalOrderIds.size() < 2) {
            return;
        }
        // 订单数量>=2，说明是合并拣货
        OrderLocationPalletQueryDTO queryDTO = new OrderLocationPalletQueryDTO();
        queryDTO.setWarehouseId(packageOrderItemPO.getWarehouseId());
        queryDTO.setOrderIdList(totalOrderIds);
        List<OrderLocationPalletDTO> orderLocationPalletDTOS =
                iOrderLocationPalletService.findPalletByCondition(queryDTO);

        if (CollectionUtils.isEmpty(orderLocationPalletDTOS)) {
            return;
        }

        String refOrderNos = orderLocationPalletDTOS.stream().map(OrderLocationPalletDTO::getOrderNo).distinct()
                .collect(Collectors.joining(","));

        String palletNos = orderLocationPalletDTOS.stream().map(OrderLocationPalletDTO::getPalletNo).distinct()
                .collect(Collectors.joining(","));

        throw new BusinessValidateException("订单" + refOrderNos + "已绑定托盘号" + palletNos + "请勿重复绑定！");
    }

    /**
     * 根据订单号或装箱码查找订单号
     *
     * @return
     */
    public List<String> listOrderNoByCode(String code, Integer orgId, Integer warehouseId) {
        LOGGER.info("根据订单号或装箱码查找订单号，入参：{}", code);
        Set<String> firstOrderNoSet = new HashSet<>();
        // 1、判断是否是装箱码
        if (code.startsWith("BZ")) {
            List<String> orderNoByNPList = listOrderNoByBoxCodeNo(code);
            if (org.springframework.util.CollectionUtils.isEmpty(orderNoByNPList)) {
                LOGGER.info("根据装箱码找不到订单号");
                return null;
            }
            // 找到订单号
            firstOrderNoSet.addAll(orderNoByNPList);
        } else {
            firstOrderNoSet.addAll(getCodeList(code, orgId, warehouseId));
        }

        return new ArrayList<>(firstOrderNoSet);
    }

    private List<String> getCodeList(String code, Integer orgId, Integer warehouseId) {
        OutStockOrderPO outStockOrderPO = outStockOrderMapper.getByOrderNo(orgId, warehouseId, code);
        if (Objects.isNull(outStockOrderPO)) {
            return Collections.singletonList(code);
        }
        OutStockOrderSearchSO so = new OutStockOrderSearchSO();
        so.setOrgId(orgId);
        so.setWareHouseId(warehouseId);
        so.setOrderSequence(outStockOrderPO.getOrderSequence());
        so.setOrderStates(Arrays.asList(OutStockOrderStateEnum.待拣货.getType()));

        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findByCondition(so);
        Set<String> refOrderNoList = outStockOrderPOList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toSet());

        refOrderNoList.add(code);

        return new ArrayList<>(refOrderNoList);
    }

}
