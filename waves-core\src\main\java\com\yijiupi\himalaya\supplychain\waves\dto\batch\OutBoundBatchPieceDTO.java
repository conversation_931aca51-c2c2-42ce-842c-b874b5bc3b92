package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023-12-12 11:59
 **/
public class OutBoundBatchPieceDTO implements Serializable {

    /**
     * 出库批次名称
     */
    private String boundBatchNo;

    /**
     * 重新计算后的大件数量
     */
    private BigDecimal packageCount;

    /**
     * 重新计算后的小件数量
     */
    private BigDecimal unitCount;

    private OutBoundBatchPieceDTO(String boundBatchNo, BigDecimal packageCount, BigDecimal unitCount) {
        this.boundBatchNo = boundBatchNo;
        this.packageCount = packageCount;
        this.unitCount = unitCount;
    }

    public static OutBoundBatchPieceDTO of(String boundBatchNo, BigDecimal packageCount, BigDecimal unitCount) {
        return new OutBoundBatchPieceDTO(boundBatchNo, packageCount, unitCount);
    }

    public String getBoundBatchNo() {
        return boundBatchNo;
    }

    public OutBoundBatchPieceDTO setBoundBatchNo(String boundBatchNo) {
        this.boundBatchNo = boundBatchNo;
        return this;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public OutBoundBatchPieceDTO setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
        return this;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public OutBoundBatchPieceDTO setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
        return this;
    }

}
