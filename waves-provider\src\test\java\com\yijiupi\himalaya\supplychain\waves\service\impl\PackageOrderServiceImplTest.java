package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.search.PackageOrderItemSO;

/**
 * <AUTHOR>
 * @date 2018/7/13 10:25
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class PackageOrderServiceImplTest {

    @Autowired
    private PackageOrderServiceImpl packageOrderService;

    @Test
    public void listPackageCodePrint() {
        // packageOrderService.listPackageCodePrint("100816200001");
    }

    @Test
    public void removePackage() {
        // packageOrderService.removePackage(Long.valueOf("20180713"));
    }

    @Test
    public void listPackageOrderItem() {
        PackageOrderItemSO packageOrderItemSO = new PackageOrderItemSO();
        packageOrderItemSO.setOrgId(123);
        packageOrderItemSO.setWarehouseId(9999);
        packageOrderItemSO.setRefOrderNo("20180713");
        packageOrderItemSO.setCurrentPage(1);
        packageOrderItemSO.setPageSize(20);
        System.out.println(JSON.toJSONString(packageOrderItemSO));
        packageOrderService.listPackageOrderItem(packageOrderItemSO);
    }

    @Test
    public void savePackage() {
        PackageOrderItemDTO packageOrderItemDTO = new PackageOrderItemDTO();
        packageOrderItemDTO.setOrgId(123);
        packageOrderItemDTO.setWarehouseId(9999);
        packageOrderItemDTO.setRefOrderNo("20180713");
        packageOrderItemDTO.setRefOrderId(Long.valueOf("123456"));
        packageOrderItemDTO.setBoxCode("1");
        packageOrderItemDTO.setProductName("三只松鼠");
        packageOrderItemDTO.setSkuId(Long.valueOf("100"));
        packageOrderItemDTO.setSpecName("225g/袋");
        // packageOrderItemDTO.setSpecQuantity(10);
        packageOrderItemDTO.setPackageName("包");
        packageOrderItemDTO.setPackageCount(new BigDecimal(1));
        packageOrderItemDTO.setUnitName("袋");
        packageOrderItemDTO.setUnitCount(new BigDecimal(5));
        packageOrderItemDTO.setUnitTotalCount(new BigDecimal(15));
        packageOrderItemDTO.setRemark("备注啊");
        packageOrderItemDTO.setCreateUser("余攀");
        packageOrderService.savePackage(packageOrderItemDTO);
    }

    @Test
    public void savePackageBatch() {
        List<PackageOrderItemDTO> packageOrderItemDTOList = new ArrayList<>();
        PackageOrderItemDTO packageOrderItemDTO = new PackageOrderItemDTO();
        packageOrderItemDTO.setOrgId(123);
        packageOrderItemDTO.setWarehouseId(9999);
        packageOrderItemDTO.setRefOrderNo("20180713");
        packageOrderItemDTO.setRefOrderId(Long.valueOf("123456"));
        packageOrderItemDTO.setRefOrderItemId(Long.valueOf("110"));
        packageOrderItemDTO.setBoxCode("2");
        packageOrderItemDTO.setProductName("三只熊猫");
        packageOrderItemDTO.setSkuId(Long.valueOf("101"));
        packageOrderItemDTO.setSpecName("250ml/瓶");
        // packageOrderItemDTO.setSpecQuantity(5);
        packageOrderItemDTO.setPackageName("箱");
        packageOrderItemDTO.setPackageCount(new BigDecimal(3));
        packageOrderItemDTO.setUnitName("瓶");
        packageOrderItemDTO.setUnitCount(new BigDecimal(5));
        packageOrderItemDTO.setUnitTotalCount(new BigDecimal(20));
        packageOrderItemDTO.setRemark("备注123");
        packageOrderItemDTO.setCreateUser("1001");
        packageOrderItemDTOList.add(packageOrderItemDTO);

        PackageOrderItemDTO packageOrderItemDTO2 = new PackageOrderItemDTO();
        packageOrderItemDTO2.setOrgId(123);
        packageOrderItemDTO2.setWarehouseId(9999);
        packageOrderItemDTO2.setRefOrderNo("20180713");
        packageOrderItemDTO2.setRefOrderId(Long.valueOf("123456"));
        packageOrderItemDTO2.setRefOrderItemId(Long.valueOf("111"));
        packageOrderItemDTO2.setBoxCode("3");
        packageOrderItemDTO2.setProductName("雀巢咖啡");
        packageOrderItemDTO2.setSkuId(Long.valueOf("102"));
        packageOrderItemDTO2.setSpecName("220g/袋");
        // packageOrderItemDTO2.setSpecQuantity(20);
        packageOrderItemDTO2.setPackageName("包");
        packageOrderItemDTO2.setPackageCount(new BigDecimal(1));
        packageOrderItemDTO2.setUnitName("袋");
        packageOrderItemDTO2.setUnitCount(new BigDecimal(5));
        packageOrderItemDTO2.setUnitTotalCount(new BigDecimal(25));
        packageOrderItemDTO2.setRemark("备注456");
        packageOrderItemDTO2.setCreateUser("1001");
        packageOrderItemDTOList.add(packageOrderItemDTO2);

        packageOrderService.savePackageBatch(packageOrderItemDTOList);
    }
}
