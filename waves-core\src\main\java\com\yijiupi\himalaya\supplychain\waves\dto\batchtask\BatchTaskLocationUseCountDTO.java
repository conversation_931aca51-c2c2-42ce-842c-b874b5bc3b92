package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 拣货任务占用的出库位库存数量
 *
 * <AUTHOR>
 * @date 2020-03-19 15:41
 */
public class BatchTaskLocationUseCountDTO implements Serializable {
    private static final long serialVersionUID = -3281337849215097815L;

    /**
     * 产品skuId
     */
    private Long productSkuId;

    /**
     * 货位id
     */
    private Long locationId;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 批次时间
     */
    private Date batchTime;

    /**
     * 占用的库存数量
     */
    private BigDecimal useCount;

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public BigDecimal getUseCount() {
        return useCount;
    }

    public void setUseCount(BigDecimal useCount) {
        this.useCount = useCount;
    }
}
