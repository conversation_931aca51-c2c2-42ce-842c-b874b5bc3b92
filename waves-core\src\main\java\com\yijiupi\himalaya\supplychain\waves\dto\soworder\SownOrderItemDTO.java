package com.yijiupi.himalaya.supplychain.waves.dto.soworder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单播种信息
 */
public class SownOrderItemDTO implements Serializable {

    /**
     * 出库单id
     */
    private Long outStockOrderId;

    /**
     * 订单编号
     */
    private String refOrderNo;

    /**
     * 已播种Sku数量
     */
    private Integer sownSkuCount;

    /**
     * 已播种大件数量
     */
    private BigDecimal sownPackageAmount;

    /**
     * 已播种小件数量
     */
    private BigDecimal sownUnitAmount;

    /**
     * 播种订单序号
     */
    private Integer sowOrderSequence;

    public Long getOutStockOrderId() {
        return outStockOrderId;
    }

    public void setOutStockOrderId(Long outStockOrderId) {
        this.outStockOrderId = outStockOrderId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Integer getSownSkuCount() {
        return sownSkuCount;
    }

    public void setSownSkuCount(Integer sownSkuCount) {
        this.sownSkuCount = sownSkuCount;
    }

    public BigDecimal getSownPackageAmount() {
        return sownPackageAmount;
    }

    public void setSownPackageAmount(BigDecimal sownPackageAmount) {
        this.sownPackageAmount = sownPackageAmount;
    }

    public BigDecimal getSownUnitAmount() {
        return sownUnitAmount;
    }

    public void setSownUnitAmount(BigDecimal sownUnitAmount) {
        this.sownUnitAmount = sownUnitAmount;
    }

    public Integer getSowOrderSequence() {
        return sowOrderSequence;
    }

    public void setSowOrderSequence(Integer sowOrderSequence) {
        this.sowOrderSequence = sowOrderSequence;
    }
}
