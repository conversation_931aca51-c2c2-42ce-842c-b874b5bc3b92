package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/7/2
 */
public class PickLocationModToBatchTaskDTO implements Serializable {
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 货位信息
     */
    private List<PickLocationModToBatchTaskItemDTO> locationInfoList;
    /**
     * 操作人
     */
    private Integer optUserId;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 货位信息
     *
     * @return locationInfoList 货位信息
     */
    public List<PickLocationModToBatchTaskItemDTO> getLocationInfoList() {
        return this.locationInfoList;
    }

    /**
     * 设置 货位信息
     *
     * @param locationInfoList 货位信息
     */
    public void setLocationInfoList(List<PickLocationModToBatchTaskItemDTO> locationInfoList) {
        this.locationInfoList = locationInfoList;
    }

    /**
     * 获取 操作人
     *
     * @return optUserId 操作人
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人
     *
     * @param optUserId 操作人
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }
}
