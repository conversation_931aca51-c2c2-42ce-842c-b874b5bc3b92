package com.yijiupi.himalaya.supplychain.waves.batch;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.WaitSplitOrderQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.*;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderLocationInfoToTmsDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderUpdateLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.CompleteReceiveTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.UpdateBatchTaskLocaitonDTO;

import java.util.List;

/**
 * 波次任务操作
 *
 * <AUTHOR> 2018/4/4
 */
public interface IBatchTaskManageService {
    /**
     * 拣货任务确认完成
     *
     * <AUTHOR>
     */
    BatchTaskCompleteResultDTO batchTaskComplete(BatchTaskConfirmDTO batchTaskConfirmDTO);

    /**
     * 根据波次任务编号查找默认片区
     *
     * @param batchTaskNoS
     * <AUTHOR>
     */
    List<String> findAreaListByTaskNo(List<String> batchTaskNoS);

    /**
     * 拣货任务确认合并
     *
     * @param batchTaskMergeDTO
     */
    void batchTaskConfirmMerge(BatchTaskMergeDTO batchTaskMergeDTO);

    /**
     * 拣货任务确认拆分
     *
     * @param batchTaskSplitDTO
     */
    void batchTaskConfirmSplit(BatchTaskSplitDTO batchTaskSplitDTO);

    /**
     * 拣货任务拆分(PDA)
     */
    void batchTaskConfirmSplitByPDA(BatchTaskSplitByPdaDTO batchTaskSplitByPdaDTO);

    /**
     * 根据波次任务编号查询待拆分订单(分页)
     *
     * @param batchTaskNo
     * @param currentPage
     * @param pageSize
     * @return
     */
    PageList<BatchTaskSplitOrderDTO> listNotSplitOrderByBatchTaskNo(String batchTaskNo, Integer currentPage,
        Integer pageSize);

    /**
     * 根据波次任务编号查询待拆分订单(分页)
     *
     * @param waitSplitOrderQueryDTO
     * @return
     */
    PageList<BatchTaskSplitOrderDTO> listWaitSplitOrderByBatchTaskNo(WaitSplitOrderQueryDTO waitSplitOrderQueryDTO);

    /**
     * 获取波次默认备货区
     */
    LoactionDTO findDefaultLocationByBatchNo(Integer orgId, String batchNo);

    /**
     * 获取拣货任务默认备货区
     */
    LoactionDTO findDefaultLocationByBatchTaskNo(Integer orgId, String batchTaskNo);

    /**
     * 修改拣货任务的周转区
     */
    void updateBatchTaskLocation(UpdateBatchTaskLocaitonDTO updateBatchTaskLocaitonDTO);

    /**
     * 待打印数据保存
     */
    void saveOrderPrintInfo(OrderPrintInfoDTO orderPrintInfoDTO);

    /**
     * 集货任务完成
     */
    void completeReceiveTask(CompleteReceiveTaskDTO completeReceiveTaskDTO);

    /**
     * 拣货任务修改出库位
     */
    void updateBatchTaskLocationByCk(BatchTaskUpdateLocationDTO batchTaskUpdateLocationDTO);

    /**
     * 订单修改出库位
     */
    void updateOrderLocationByCk(OutStockOrderUpdateLocationDTO outStockOrderUpdateLocationDTO);

    /**
     * 修改拣货任务明细（op后台工具）
     */
    void updateBatchTaskItem(BatchTaskItemDTO batchTaskItemDTO);

    /**
     * 拣货任务移库（op后台工具）
     */
    void updateBatchTaskPickCount(String batchTaskNo);

    /**
     * 对PDA客户端波次任务修改请求进行校验
     */
    BatchTaskCompleteResultDTO checkOutStockByOutStockConfig(List<BatchTaskItemCompleteDTO> batchTaskItemUpdateDTOList,
        Integer warehouseId);

    /**
     * 重置拣货任务明细的拣货人
     *
     * @param dto
     */
    void wcsAssignSorter(WCSAssignItemSorterDTO dto);

    /**
     * 完成拣货任务明细，明细全部完成后自动完成拣货任务
     *
     * @param dto
     */
    void completeRobotBatchTask(CompleteRobotBatchTaskDTO dto);

    /**
     * 2.5 和 2.5+ 货位变更->未开始拣货的拣货任务的货位也变更
     *
     * @param dto
     */
    void pickLocationModToBatchTask(PickLocationModToBatchTaskDTO dto);

    void updateBatchTask(BatchTaskDTO updateBatchTask);

    /**
     * 拣货任务完成接口
     *
     * @param batchTaskCompleteDTO
     */
    void completeBatchTask(BatchTaskCompleteDTO batchTaskCompleteDTO);

    /**
     * 拍灯领取拣货任务
     *
     * @param dto
     */
    void getDigitalBatchTask(DigitalGetBatchTaskDTO dto);

    /**
     * 清理电子标签拣货任务
     *
     * @param dto
     */
    void cleanUpDigitalTask(CleanUpDigitalTaskDTO dto);

    /**
     * 同步拣货任务到电子标签
     *
     * @param batchTaskDTO
     */
    void syncBatchTaskToRfid(BatchTaskDTO batchTaskDTO);

    /**
     * 拣货任务拣货方式调整
     *
     * @param batchTaskDTO
     */
    void updateBatchTaskKindOfPicking(BatchTaskDTO batchTaskDTO);

    /**
     * 更新拣货模式
     *
     * @param dto
     */
    void changeKindOfPickingToPickPattern(CleanUpDigitalTaskDTO dto);

    /**
     * 同步托盘信息给tms
     *
     * @param dto
     */
    void sendOrderLocationInfoToTms(OrderLocationInfoToTmsDTO dto);

    /**
     * SCM-22614 按客户拣货，相同地址合并拣货 1、开启了【仓库按订单实时分拣】，未开启【是否开启接力分拣模式】时，休百订单或酒饮订单分拣完成，都要选择绑定托盘位，同时同客户其他订单同步出库位和托盘位 补充： 1）前提条件是
     * 开启了 isNeedPalletForStock 按订单拣货装托 2）已完成的订单，同步到当前分拣的出库位和托盘位 不允许修改 3）开启了【仓库按订单实时分拣】未开启【是否开启接力分拣模式】，未开启
     * isNeedPalletForStock 按订单拣货装托，实时同步出库位信息
     *
     * @param dto
     * @return
     */
    UserSamePalletInfoResultDTO findSameUserPalletInfo(UserSamePalletInfoQueryDTO dto);
}
