package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.yijiupi.himalaya.supplychain.waves.utils.CustomJsonDateDeserializer;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 波次列表查询dto
 * 
 * <AUTHOR> 2018/3/15
 */
public class BatchQueryDTO implements Serializable {
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 波次编号
     */
    private String batchNo;
    /**
     * 订单号
     */
    private String refOrderNo;
    /**
     * 波次名称
     */
    private String batchName;
    /**
     * 波次状态
     */
    private Byte state;

    /**
     * 波次状态
     */
    private List<Byte> stateList;
    /**
     * 创建起始时间
     */
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date startTime;
    /**
     * 创建结束时间
     */
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date endTime;
    /**
     * 当前页
     */
    private Integer currentPage = 1;
    /**
     * @Fields 每页的数量
     */
    private Integer pageSize = 100;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;

    public List<Byte> getStateList() {
        return stateList;
    }

    public void setStateList(List<Byte> stateList) {
        this.stateList = stateList;
    }

    /**
     * 获取 波次编号
     *
     * @return batchNo 波次编号
     */
    public String getBatchNo() {
        return this.batchNo;
    }

    /**
     * 设置 波次编号
     *
     * @param batchNo 波次编号
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * 获取 订单号
     *
     * @return refOrderNo 订单号
     */
    public String getRefOrderNo() {
        return this.refOrderNo;
    }

    /**
     * 设置 订单号
     *
     * @param refOrderNo 订单号
     */
    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    /**
     * 获取 波次名称
     *
     * @return batchName 波次名称
     */
    public String getBatchName() {
        return this.batchName;
    }

    /**
     * 设置 波次名称
     *
     * @param batchName 波次名称
     */
    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    /**
     * 获取 波次状态
     *
     * @return state 波次状态
     */
    public Byte getState() {
        return this.state;
    }

    /**
     * 设置 波次状态
     *
     * @param state 波次状态
     */
    public void setState(Byte state) {
        this.state = state;
    }

    /**
     * 获取 创建起始时间
     *
     * @return startTime 创建起始时间
     */
    public Date getStartTime() {
        return this.startTime;
    }

    /**
     * 设置 创建起始时间
     *
     * @param startTime 创建起始时间
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * 获取 创建结束时间
     *
     * @return endTime 创建结束时间
     */
    public Date getEndTime() {
        return this.endTime;
    }

    /**
     * 设置 创建结束时间
     *
     * @param endTime 创建结束时间
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * 获取 当前页
     *
     * @return currentPage 当前页
     */
    public Integer getCurrentPage() {
        return this.currentPage;
    }

    /**
     * 设置 当前页
     *
     * @param currentPage 当前页
     */
    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    /**
     * 获取 @Fields 每页的数量
     *
     * @return pageSize @Fields 每页的数量
     */
    public Integer getPageSize() {
        return this.pageSize;
    }

    /**
     * 设置 @Fields 每页的数量
     *
     * @param pageSize @Fields 每页的数量
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 获取 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }
}
