package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * 拣货任务修改出库位
 *
 * <AUTHOR>
 * @date 2019-07-25 11:42
 */
public class BatchTaskUpdateLocationDTO implements Serializable {

    private static final long serialVersionUID = 6377773432068286587L;

    /**
     * 拣货任务ID集合
     */
    private List<String> batchTaskIdList;

    /**
     * 出库位id
     */
    private Long locationId;

    /**
     * 出库位名称
     */
    private String locationName;

    /**
     * 操作人
     */
    private String operateUser;
    /**
     * 托盘信息列表
     */
    private List<String> palletNoList;

    public List<String> getBatchTaskIdList() {
        return batchTaskIdList;
    }

    public void setBatchTaskIdList(List<String> batchTaskIdList) {
        this.batchTaskIdList = batchTaskIdList;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    /**
     * 获取 托盘信息列表
     *
     * @return palletNoList 托盘信息列表
     */
    public List<String> getPalletNoList() {
        return this.palletNoList;
    }

    /**
     * 设置 托盘信息列表
     *
     * @param palletNoList 托盘信息列表
     */
    public void setPalletNoList(List<String> palletNoList) {
        this.palletNoList = palletNoList;
    }
}
