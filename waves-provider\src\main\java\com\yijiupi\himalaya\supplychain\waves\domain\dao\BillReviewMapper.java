package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.supplychain.waves.domain.po.BillReviewPO;

public interface BillReviewMapper {

    int insert(BillReviewPO record);

    int insertSelective(BillReviewPO record);

    BillReviewPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BillReviewPO record);

    int updateByPrimaryKey(BillReviewPO record);

    BillReviewPO getByBusinessNo(@Param("businessNo") String businessNo,
        @Param("relatedBusinessNo") String relatedBusinessNo, @Param("orgId") Integer orgId);

    void insertOrUpdateBatch(@Param("list") List<BillReviewPO> billReviewPOS);
}