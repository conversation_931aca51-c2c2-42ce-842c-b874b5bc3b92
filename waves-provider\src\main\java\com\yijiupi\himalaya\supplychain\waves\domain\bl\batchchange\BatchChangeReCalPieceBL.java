package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.yijiupi.himalaya.common.dto.GetItemProductSizeDTO;
import com.yijiupi.himalaya.common.util.GetItemProductSize;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.PackageOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.InBoundBatchPieceParam;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.OutBoundBatchPieceDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.PageHelperUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-12-11 11:49
 **/
@Service
public class BatchChangeReCalPieceBL {

    @Resource
    private OutStockOrderMapper outStockOrderMapper;
    @Resource
    private BatchMapper batchMapper;
    @Resource
    private PackageOrderItemMapper packageOrderItemMapper;

    private static final Logger logger = LoggerFactory.getLogger(BatchChangeReCalPieceBL.class);

    /**
     * 需要重算的波次状态
     */
    private static final Set<Byte> RE_CAL_OUT_BATCH_STATUE = Sets.newHashSet(
            BatchStateEnum.PICKINGEND.getType().byteValue(), BatchStateEnum.ALREADYOUTOFSTORE.getType().byteValue()
    );

    /**
     * 重算出库单大小件信息
     *
     * @param outBoundNos 出库批次号
     * @param orgId       城市 id
     * @return 计算结果 key 为出库批次号, value 为 大小件信息的 map
     */
    public Map<String, OutBoundBatchPieceDTO> reCalPieceInfo(Set<String> outBoundNos, Integer orgId) {
        logger.info("重算出库批次大小件: {}", outBoundNos);
        boolean showLog = outBoundNos.size() == 1;
        Map<String, List<OutStockOrderPO>> orderBatchMap = PageHelperUtils.splitPageQuery(outBoundNos, 1, it -> outStockOrderMapper.findByOutBoundNoList(it, orgId))
                .stream().collect(Collectors.groupingBy(OutStockOrderPO::getBoundNo));
        Set<Long> outStockOrderIds = orderBatchMap.values().stream().flatMap(Collection::stream)
                .map(OutStockOrderPO::getId).collect(Collectors.toSet());
        List<OutStockOrderPO> orders = orderBatchMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        Set<String> batchNos = orders.stream().map(OutStockOrderPO::getBatchno).filter(StringUtils::hasText).collect(Collectors.toSet());
        if (batchNos.isEmpty()) {
            logger.info("没有生成波次了的订单, 跳过处理");
            return Collections.emptyMap();
        }
        Set<String> pickedBatch = batchMapper.listByBatchNos(batchNos).stream()
                .filter(it -> RE_CAL_OUT_BATCH_STATUE.contains(it.getState()))
                .map(BatchPO::getBatchNo)
                .collect(Collectors.toSet());
        boolean hasNotPickedBatch = orders.stream().noneMatch(it -> pickedBatch.contains(it.getBatchno()));
        if (hasNotPickedBatch) {
            logger.info("没有拣货完成的波次, 跳过处理");
            return Collections.emptyMap();
        }
        Map<Long, List<PackageOrderItemDTO>> packageBoxMap = listBoxesByOrderIds(outStockOrderIds, orgId).stream()
                .collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderId));
        return orderBatchMap.entrySet().stream()
                .map(it -> convertToOutBoundBatchPiece(it, packageBoxMap, showLog))
                .collect(Collectors.toMap(OutBoundBatchPieceDTO::getBoundBatchNo, Function.identity()));
    }

    /**
     * 重算入库批次大小件信息
     *
     * @param param 计算参数
     * @return 计算结果 key 为入库批次号, value 为 大小件信息的 map
     */
    public Map<String, OutBoundBatchPieceDTO> reCalPieceInfo(List<InBoundBatchPieceParam> param) {
        logger.info("重算入库批次大小件: {}", JSON.toJSONString(param));
        boolean showLog = param.size() == 1;
        Set<Long> outStockOrderIds = param.stream().map(InBoundBatchPieceParam::getOutStockOrderInfo)
                .flatMap(Collection::stream)
                .map(InBoundBatchPieceParam.InBoundBatchPieceOrderInfo::getOrderId)
                .collect(Collectors.toSet());
        if (outStockOrderIds.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<Long, OutStockOrderPO> orderMap = outStockOrderMapper.findByOrderIds(outStockOrderIds).stream()
                .collect(Collectors.toMap(OutStockOrderPO::getId, Function.identity()));
        // 入库批次 -> 出库单 map
        Map<String, List<OutStockOrderPO>> orderBatchMap = orderMap.entrySet().stream().map(it -> Pair.of(
                param.stream().filter(item -> item.containsOrder(it.getValue().getId()))
                        .findFirst().map(InBoundBatchPieceParam::getBoundNo).orElse("null"), it
        )).collect(Collectors.groupingBy(Pair::getFirst, Collectors.mapping(it -> it.getSecond().getValue(), Collectors.toList())));
        // 按城市 id 分组再循环查询装箱信息
        Map<Long, List<PackageOrderItemDTO>> packageBoxMap = param.stream().map(InBoundBatchPieceParam::getOutStockOrderInfo)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(InBoundBatchPieceParam.InBoundBatchPieceOrderInfo::getOrgId,
                        Collectors.mapping(InBoundBatchPieceParam.InBoundBatchPieceOrderInfo::getOrderId, Collectors.toSet())
                )).entrySet().stream().map(it -> listBoxesByOrderIds(it.getValue(), it.getKey()))
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderId));
        return orderBatchMap.entrySet().stream()
                .map(it -> convertToOutBoundBatchPiece(it, packageBoxMap, showLog))
                .collect(Collectors.toMap(OutBoundBatchPieceDTO::getBoundBatchNo, Function.identity()));
    }

    private OutBoundBatchPieceDTO convertToOutBoundBatchPiece(
            Map.Entry<String, List<OutStockOrderPO>> entry, Map<Long, List<PackageOrderItemDTO>> packageBoxMap,
            boolean showLog
    ) {
        List<OutStockOrderPO> orders = entry.getValue();
        Set<String> boxCodeNos = new HashSet<>();
        List<OutStockOrderItemPO> allUnPackagedItems = new ArrayList<>();
        List<PackageOrderItemDTO> allPackageItems = new ArrayList<>();
        for (OutStockOrderPO order : orders) {
            Long orderId = order.getId();
            Integer addressId = order.getAddressId();
            List<OutStockOrderItemPO> outStockOrderItems = order.getItems();
            // 打包了的订单项, 只有一个是真正的包, 其它的都是空包, 计算大小件时需要去掉空包
            List<PackageOrderItemDTO> packageItems = new ArrayList<>(packageBoxMap.getOrDefault(orderId, Collections.emptyList()));
            Set<Long> packageItemSet = packageItems.stream().map(PackageOrderItemDTO::getRefOrderItemId)
                    .collect(Collectors.toSet());
            // 一个箱号算一大件, 合并同地址的订单的箱号
            List<String> itemBoxCodeNos = packageItems.stream().map(PackageOrderItemDTO::getBoxCodeNo)
                    .map(it -> (addressId != null && addressId > 0) ? addressId + "_" + it : it)
                    .collect(Collectors.toList());
            boxCodeNos.addAll(itemBoxCodeNos);
            // 未装箱 的 item 对大小件求和
            List<OutStockOrderItemPO> unPackagedItems = outStockOrderItems.stream()
                    .filter(item -> !packageItemSet.contains(item.getId())).collect(Collectors.toList());
            allPackageItems.addAll(packageItems);
            allUnPackagedItems.addAll(unPackagedItems);
        }
        BigDecimal[] calculateSize = calculateSize(allUnPackagedItems, allPackageItems);
        if (showLog) {
            logger.info("计算出库批次大小件, 订单数量: {}, 总包装箱数量: {}, 打包订单项个数: {}, 未打包订单项个数: {}, 打包后算作大件的数量: {}",
                    orders.size(), packageBoxMap.size(), allPackageItems.size(), allUnPackagedItems.size(), boxCodeNos.size()
            );
        }
        return OutBoundBatchPieceDTO.of(entry.getKey(), calculateSize[0].add(new BigDecimal(boxCodeNos.size())), calculateSize[1]);
    }

    private static BigDecimal[] calculateSize(List<OutStockOrderItemPO> items, List<PackageOrderItemDTO> boxes) {
        Map<Long, List<PackageOrderItemDTO>> boxMap = boxes.stream()
                .collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderItemId));
        List<GetItemProductSizeDTO> list = items.stream().map(it -> {
            List<PackageOrderItemDTO> itemBox = boxMap.get(it.getId());
            BigDecimal packagedUnitCount = BigDecimal.ZERO;
            if (itemBox != null) {
                packagedUnitCount = itemBox.stream().map(PackageOrderItemDTO::getUnitTotalCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            GetItemProductSizeDTO dto = new GetItemProductSizeDTO();
            dto.setProductSkuId(it.getSkuid());
            // 打包的小件不参与大小件计算
            dto.setMinUnitTotalCount(it.getUnittotalcount().subtract(packagedUnitCount));
            dto.setSpecQuantity(it.getSpecquantity());
            return dto;
        }).collect(Collectors.toList());
        return GetItemProductSize.getDeliverySizeByMin(list);
    }

    /**
     * 分页查询打包信息
     *
     * @param ids 出库单 id
     * @param orgId            城市 id
     * @return 分页查询后合并了的结果集
     */
    private List<PackageOrderItemDTO> listBoxesByOrderIds(Set<Long> ids, Integer orgId) {
        return PageHelperUtils.splitPageQuery(ids, 500, it -> packageOrderItemMapper.listBoxesByOrderIds(it, orgId));
    }

}
