package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/27
 */
public class OrderQueryDTO implements Serializable {

    private static final long serialVersionUID = -6496795333306278263L;
    private String code;

    private Integer orgId;

    private Integer warehouseId;

    private List<Byte> stateList;

    /**
     * 搜索的订单类型
     */
    private List<Integer> orderTypes;

    /**
     * 订单来源
     */
    private List<Byte> orderSourceTypeList;

    public List<Integer> getOrderTypes() {
        return orderTypes;
    }

    public void setOrderTypes(List<Integer> orderTypes) {
        this.orderTypes = orderTypes;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<Byte> getStateList() {
        return stateList;
    }

    public void setStateList(List<Byte> stateList) {
        this.stateList = stateList;
    }

    public List<Byte> getOrderSourceTypeList() {
        return orderSourceTypeList;
    }

    public void setOrderSourceTypeList(List<Byte> orderSourceTypeList) {
        this.orderSourceTypeList = orderSourceTypeList;
    }
}
