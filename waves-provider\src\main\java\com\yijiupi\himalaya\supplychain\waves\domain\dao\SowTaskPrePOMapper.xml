<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskPrePOMapper">
  <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPrePO">
    <!--@mbg.generated-->
    <!--@Table sowtaskpre-->
    <id column="Id" jdbcType="BIGINT" property="id" />
    <result column="OrgId" jdbcType="INTEGER" property="orgId" />
    <result column="WarehouseId" jdbcType="INTEGER" property="warehouseId" />
    <result column="BatchId" jdbcType="VARCHAR" property="batchId" />
    <result column="BatchNo" jdbcType="VARCHAR" property="batchNo" />
    <result column="SowTaskPreNo" jdbcType="VARCHAR" property="sowTaskPreNo" />
    <result column="Version" jdbcType="INTEGER" property="version" />
    <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime" />
    <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    Id, OrgId, WarehouseId, BatchId, BatchNo, SowTaskPreNo, Version, CreateTime, LastUpdateTime
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from sowtaskpre
    where Id = #{id,jdbcType=BIGINT}
  </select>

  <select id="findSowTaskPreByBatchId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sowtaskpre
    where orgId = #{orgId,jdbcType=INTEGER}
        and BatchId = #{batchId,jdbcType=VARCHAR}
        and version = #{version,jdbcType=INTEGER}
  </select>

  <select id="findSowTaskPreByBatchNos" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sowtaskpre
    where orgId = #{orgId,jdbcType=INTEGER}
    and version = #{version,jdbcType=INTEGER}
    and BatchNo in
    <foreach item="item" index="index" collection="batchNos" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from sowtaskpre
    where Id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPrePO">
    <!--@mbg.generated-->
    insert into sowtaskpre
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="orgId != null">
        OrgId,
      </if>
      <if test="warehouseId != null">
        WarehouseId,
      </if>
      <if test="batchId != null and batchId != ''">
        BatchId,
      </if>
      <if test="batchNo != null and batchNo != ''">
        BatchNo,
      </if>
      <if test="sowTaskPreNo != null and sowTaskPreNo != ''">
        SowTaskPreNo,
      </if>
      <if test="version != null">
        Version,
      </if>
      <if test="createTime != null">
        CreateTime,
      </if>
      <if test="lastUpdateTime != null">
        LastUpdateTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="warehouseId != null">
        #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="batchId != null and batchId != ''">
        #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null and batchNo != ''">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="sowTaskPreNo != null and sowTaskPreNo != ''">
        #{sowTaskPreNo,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPrePO">
    <!--@mbg.generated-->
    update sowtaskpre
    <set>
      <if test="orgId != null">
          OrgId = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="warehouseId != null">
        WarehouseId = #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="batchId != null and batchId != ''">
        BatchId = #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null and batchNo != ''">
        BatchNo = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="sowTaskPreNo != null and sowTaskPreNo != ''">
        SowTaskPreNo = #{sowTaskPreNo,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        Version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateTime != null">
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>