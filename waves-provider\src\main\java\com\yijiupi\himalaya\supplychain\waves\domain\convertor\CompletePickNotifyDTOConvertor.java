package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IOrderLocationPalletService;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter.CompletePickNotifyDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderLocationInfoToTmsDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderUpdateLocationDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/25
 */
@Component
public class CompletePickNotifyDTOConvertor {

    @Reference
    private IOrderLocationPalletService iOrderLocationPalletService;

    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;

    public List<CompletePickNotifyDTO> convert(OutStockOrderPO outStockOrderPO,
        OutStockOrderUpdateLocationDTO orderUpdateLocationDTO) {
        String locationName = orderUpdateLocationDTO.getLocationName()
            .concat(getPalletNo(outStockOrderPO.getWarehouseId(), orderUpdateLocationDTO.getOrderNo()));

        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(Collections.singletonList(outStockOrderPO.getId()));

        List<String> batchTaskIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getBatchTaskId).distinct()
            .collect(Collectors.toList());

        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findBatchTaskByIds(batchTaskIds);
        List<String> userNameList = batchTaskPOList.stream().map(BatchTaskPO::getCompleteUser).filter(Objects::nonNull)
            .collect(Collectors.toList());

        CompletePickNotifyDTO completePickNotifyDTO = new CompletePickNotifyDTO();
        completePickNotifyDTO.setOrderId(Long.valueOf(outStockOrderPO.getBusinessId()));
        completePickNotifyDTO.setPickUserNameList(userNameList);
        completePickNotifyDTO.setDefaultLocationId(orderUpdateLocationDTO.getLocationId());
        completePickNotifyDTO.setDefaultLocationName(locationName);
        completePickNotifyDTO.setWarehouseId(outStockOrderPO.getWarehouseId());
        Optional<String> userOptional = userNameList.stream().findAny();
        completePickNotifyDTO.setStevedoreUserName(userOptional.orElse(""));

        List<Date> completeTimeList = batchTaskPOList.stream().map(BatchTaskPO::getCompleteTime)
            .filter(Objects::nonNull).sorted(Comparator.reverseOrder()).collect(Collectors.toList());

        Date completeTime = completeTimeList.stream().findFirst().get();
        completePickNotifyDTO.setCompletePickTime(completeTime);

        completePickNotifyDTO.setOptUserId(orderUpdateLocationDTO.getOptUserId().toString());

        return Collections.singletonList(completePickNotifyDTO);
    }

    public List<CompletePickNotifyDTO> convert(OrderLocationInfoToTmsDTO dto, OutStockOrderPO outStockOrderPO) {
        OrderLocationPalletQueryDTO queryDTO = new OrderLocationPalletQueryDTO();
        queryDTO.setWarehouseId(outStockOrderPO.getWarehouseId());
        queryDTO.setOrderNo(outStockOrderPO.getReforderno());
        List<OrderLocationPalletDTO> palletList = iOrderLocationPalletService.findPalletByCondition(queryDTO);

        if (CollectionUtils.isEmpty(palletList)) {
            return null;
        }
        OrderLocationPalletDTO orderLocationPalletDTO = palletList.stream().findFirst().get();

        String locationName = orderLocationPalletDTO.getLocationName().concat(
            "#" + palletList.stream().map(OrderLocationPalletDTO::getPalletNo).collect(Collectors.joining("#")));

        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(Collections.singletonList(outStockOrderPO.getId()));

        List<String> batchTaskIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getBatchTaskId).distinct()
            .collect(Collectors.toList());

        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findBatchTaskByIds(batchTaskIds);
        List<String> userNameList = batchTaskPOList.stream().map(BatchTaskPO::getCompleteUser).filter(Objects::nonNull)
            .collect(Collectors.toList());

        CompletePickNotifyDTO completePickNotifyDTO = new CompletePickNotifyDTO();
        completePickNotifyDTO.setOrderId(Long.valueOf(outStockOrderPO.getBusinessId()));
        completePickNotifyDTO.setPickUserNameList(userNameList);
        completePickNotifyDTO.setDefaultLocationId(orderLocationPalletDTO.getLocationId());
        completePickNotifyDTO.setDefaultLocationName(locationName);
        completePickNotifyDTO.setWarehouseId(outStockOrderPO.getWarehouseId());
        Optional<String> userOptional = userNameList.stream().findAny();
        completePickNotifyDTO.setStevedoreUserName(userOptional.orElse(""));

        completePickNotifyDTO.setOptUserId(dto.getOptUserId().toString());
        List<Date> completeTimeList = batchTaskPOList.stream().map(BatchTaskPO::getCompleteTime)
            .filter(Objects::nonNull).sorted(Comparator.reverseOrder()).collect(Collectors.toList());

        Date completeTime = new Date();
        if (CollectionUtils.isNotEmpty(completeTimeList)) {
            completeTime = completeTimeList.stream().findFirst().get();
        }
        completePickNotifyDTO.setCompletePickTime(completeTime);

        return Collections.singletonList(completePickNotifyDTO);
    }

    private String getPalletNo(Integer warehouseId, String orderNo) {
        OrderLocationPalletQueryDTO queryDTO = new OrderLocationPalletQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setOrderNo(orderNo);
        List<OrderLocationPalletDTO> palletList = iOrderLocationPalletService.findPalletByCondition(queryDTO);
        if (CollectionUtils.isEmpty(palletList)) {
            return "";
        }

        return "#" + palletList.stream().map(OrderLocationPalletDTO::getPalletNo).collect(Collectors.joining("#"));
    }

}
