package com.yijiupi;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskQueryService;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemPackageReviewDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemPickCompleteDetailQueryDTO;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

public class IBatchTaskQueryServiceTest extends AbstractBaseTest {

    @Reference
    private IBatchTaskQueryService iBatchTaskQueryService;

    @Test
    public void findPackageReviewBatchItemTest() {
        BatchTaskItemPickCompleteDetailQueryDTO queryDTO = new BatchTaskItemPickCompleteDetailQueryDTO();
        queryDTO.setBatchTaskId("2022122700212");
        List<BatchTaskItemPackageReviewDetailDTO> detailDTOS =
            iBatchTaskQueryService.findPackageReviewBatchItem(queryDTO);

        Assert.assertNotNull(detailDTOS);
    }
}
