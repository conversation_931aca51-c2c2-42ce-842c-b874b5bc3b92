package com.yijiupi.himalaya.supplychain.waves.domain.bl.perform;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskQueryPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskQueryPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.HistoryTaskPerformanceCalDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/25
 */
@Service
public class HistoryTaskPerformanceCalculateBL {

    @Autowired
    private SowTaskMapper sowTaskMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private TaskPerformanceCalculateBL taskPerformanceCalculateBL;

    private static final Logger LOGGER = LoggerFactory.getLogger(HistoryTaskPerformanceCalculateBL.class);

    /**
     * 计算历史任务绩效
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void calculateHistoryTaskPerformance(HistoryTaskPerformanceCalDTO dto) {
        LOGGER.info("【计算历史绩效】 入参：{}", JSON.toJSONString(dto));
        handleBatchTaskPerformance(dto);
        handleSowTaskPerformance(dto);
    }

    private void handleSowTaskPerformance(HistoryTaskPerformanceCalDTO dto) {
        SowTaskQueryPO queryDTO = new SowTaskQueryPO();
        queryDTO.setWarehouseIds(dto.getWarehouseIds());
        queryDTO.setStartTime(dto.getTimeS());
        queryDTO.setEndTime(dto.getTimeE());
        queryDTO.setStates(Collections.singletonList(SowTaskStateEnum.已播种.getType()));
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(50);
        processSowTaskPagedResults(queryDTO);
    }

    private void processSowTaskPagedResults(SowTaskQueryPO queryDTO) {
        int i = 0;
        PageResult<SowTaskPO> pageResult = sowTaskMapper.findSowTaskByCon(queryDTO);
        while (queryDTO.getPageNum() <= pageResult.getPager().getTotalPage()) {
            if (CollectionUtils.isEmpty(pageResult.getResult())) {
                break;
            }
            if (i > pageResult.getPager().getTotalPage()) {
                LOGGER.warn("同步入库批次信息 , 当前页面值 : {} ; 总页面值 : {} ; i 值 : {} ;", pageResult.getPager().getCurrentPage(),
                    pageResult.getPager().getTotalPage(), i);
                break;
            }
            calSowTaskPerformance(pageResult.getResult());
            queryDTO.setPageNum(queryDTO.getPageNum() + 1);
            pageResult = sowTaskMapper.findSowTaskByCon(queryDTO);
            i++;
        }
    }

    private void calSowTaskPerformance(List<SowTaskPO> sowTaskPOList) {
        if (CollectionUtils.isEmpty(sowTaskPOList)) {
            return;
        }
        sowTaskPOList.stream().filter(m -> Objects.isNull(m.getShiftOfPerformance())).forEach(sowTaskPO -> {
            String shiftOfPerformance = DateUtils.getDateFormat(sowTaskPO.getCompleteTime());
            SowTaskPO updateSowTaskPO = new SowTaskPO();
            updateSowTaskPO.setId(sowTaskPO.getId());
            updateSowTaskPO.setShiftOfPerformance(shiftOfPerformance);
            sowTaskMapper.updateByPrimaryKeySelective(updateSowTaskPO);
        });

    }

    private void handleBatchTaskPerformance(HistoryTaskPerformanceCalDTO dto) {
        BatchTaskQueryPO batchTaskQueryDTO = new BatchTaskQueryPO();
        batchTaskQueryDTO.setWarehouseIds(dto.getWarehouseIds());
        batchTaskQueryDTO.setStartTime(dto.getTimeS());
        batchTaskQueryDTO.setEndTime(dto.getTimeE());
        batchTaskQueryDTO.setTaskStateList(Collections.singletonList(TaskStateEnum.已完成.getType()));
        processBatchTaskPagedResults(batchTaskQueryDTO);
    }

    private void processBatchTaskPagedResults(BatchTaskQueryPO queryDTO) {
        int i = 0;
        Integer pageNum = 1;
        Integer pageSize = 50;
        PageResult<BatchTaskPO> pageResult = batchTaskMapper.findBatchTaskListByCon(queryDTO, pageNum, pageSize);
        while (queryDTO.getCurrentPage() <= pageResult.getPager().getTotalPage()) {
            if (CollectionUtils.isEmpty(pageResult.getResult())) {
                break;
            }
            if (i > pageResult.getPager().getTotalPage()) {
                LOGGER.warn("同步入库批次信息 , 当前页面值 : {} ; 总页面值 : {} ; i 值 : {} ;", pageResult.getPager().getCurrentPage(),
                    pageResult.getPager().getTotalPage(), i);
                break;
            }
            calBatchTaskPerformance(pageResult.getResult());
            queryDTO.setCurrentPage(queryDTO.getCurrentPage() + 1);
            pageNum++;
            pageResult = batchTaskMapper.findBatchTaskListByCon(queryDTO, pageNum, pageSize);
            i++;
        }
    }

    private void calBatchTaskPerformance(List<BatchTaskPO> batchTaskPOList) {
        if (CollectionUtils.isEmpty(batchTaskPOList)) {
            return;
        }
        batchTaskPOList.stream().filter(m -> Objects.isNull(m.getShiftOfPerformance())).forEach(batchTaskPO -> {
            String shiftOfPerformance = DateUtils.getDateFormat(batchTaskPO.getCompleteTime());
            BatchTaskPO updateBatchTaskPO = new BatchTaskPO();
            updateBatchTaskPO.setId(batchTaskPO.getId());
            updateBatchTaskPO.setShiftOfPerformance(shiftOfPerformance);
            batchTaskMapper.updateByPrimaryKeySelective(updateBatchTaskPO);
        });

    }

}
