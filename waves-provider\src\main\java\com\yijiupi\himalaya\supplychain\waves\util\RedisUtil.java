package com.yijiupi.himalaya.supplychain.waves.util;

import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

@Component
public class RedisUtil<T> {
    private static final StringRedisSerializer STRING_SERIALIZER = new StringRedisSerializer();
    private static final JdkSerializationRedisSerializer VALUE_SERIALIZER = new JdkSerializationRedisSerializer();
    @Autowired
    private RedisTemplate<String, T> redisTemplate;

    @PostConstruct
    public void init() {
        redisTemplate.setKeySerializer(STRING_SERIALIZER);
        redisTemplate.setHashKeySerializer(STRING_SERIALIZER);
        redisTemplate.setHashValueSerializer(VALUE_SERIALIZER);
    }

    public Boolean hasKey(String key, String field) {
        return redisTemplate.opsForHash().hasKey(key, field);
    }

    public void setHash(String key, String field, T value, long timeout, TimeUnit unit) {
        redisTemplate.opsForHash().put(key, field, value);
        redisTemplate.opsForHash().getOperations().expire(key, timeout, unit);
    }

    public T getHash(String key, String field) {
        if (hasKey(key, field)) {
            return (T)redisTemplate.opsForHash().get(key, field);
        }
        return null;
    }

    public void delHash(String key, String[] field) {
        redisTemplate.opsForHash().delete(key, field);
    }

    public boolean setNX(String key, T value, long timeout, TimeUnit unit) {
        Boolean absent = redisTemplate.opsForValue().setIfAbsent(key, value);
        if (absent) {
            redisTemplate.opsForHash().getOperations().expire(key, timeout, unit);
        }
        return absent;
    }

    public T getAndSet(String key, T value, long timeout, TimeUnit unit) {
        boolean absent = setNX(key, value, timeout, unit);
        if (absent) {
            return value;
        } else {
            return redisTemplate.opsForValue().get(key);
        }
    }

    public T get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    public void set(String key, T value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public void set(String key, T value, long timeout, TimeUnit unit) {
        redisTemplate.boundValueOps(key).set(value, timeout, unit);
    }

    public void put(String key, T value, long timeout, TimeUnit unit) {
        redisTemplate.boundValueOps(key).set(value, timeout, unit);
    }

    public void batchDel(List<String> keyList) {
        redisTemplate.delete(keyList);
    }

    public Long getExpire(String key) {
        return redisTemplate.getExpire(key);
    }
}
