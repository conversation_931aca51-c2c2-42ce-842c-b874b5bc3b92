package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.slitbyorder;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation.RecommendOutLocationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderRequestBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchAttrSettingWayConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/13
 */
@Component
public class SplitBatchTaskNormalByOrderBL extends SplitBatchTaskByOrderBL {

    @Autowired
    private RecommendOutLocationBL recommendOutLocationBL;

    @Override
    List<OutStockOrderPO> supportOrderList(SplitBatchTaskByOrderRequestBO bo) {
        List<OutStockOrderPO> normalOrderList =
            bo.getCreateDTO().getOrders().stream().filter(m -> !abnormalSourceTypes.contains(m.getOrderSourceType()))
                .filter(m -> !SplitWaveOrderUtil.orderIsPromotion(m)).collect(Collectors.toList());

        return normalOrderList;
    }

    @Override
    protected List<SplitBatchTaskByOrderResultBO> doSplitBatchTaskByOrder(SplitBatchTaskByOrderRequestBO bo,
        List<OutStockOrderPO> normalOrderList) {
        WaveCreateDTO createDTO = bo.getCreateDTO();
        WavesStrategyBO wavesStrategyDTO = createDTO.getWavesStrategyDTO();

        if (CollectionUtils.isEmpty(normalOrderList)) {
            return Collections.emptyList();
        }

        // 如果不是按用户，则生成一个大波次
        if (!BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_USER
            .equals(createDTO.getWavesStrategyDTO().getBatchAttrSettingWay())) {

            try {
                List<SplitBatchTaskByOrderResultBO> boList =
                    splitBatchTaskByOrderDefault(createDTO, bo.getBatchPO(), normalOrderList);
                return boList;
            } catch (Exception e) {
                LOG.error("出库位设置报错", e);
                return splitByOrderFeature(normalOrderList);
            }
        }

        // 否则，按用户拆分波次
        return splitByOrderFeatureAndAddress(normalOrderList);
    }

    // 根据推荐出库位拆分拣货任务
    public List<SplitBatchTaskByOrderResultBO> splitBatchTaskByOrderDefault(WaveCreateDTO createDTO, BatchPO batchPO,
        List<OutStockOrderPO> outStockOrderPOList) {
        if (BooleanUtils.isFalse(needHandleOutStockLocation(createDTO, batchPO))) {
            return splitByOrderFeature(outStockOrderPOList);
        }

        boolean allHaveAddress = outStockOrderPOList.stream().anyMatch(m -> Objects.isNull(m.getAddressId()));
        if (BooleanUtils.isTrue(allHaveAddress)) {
            return splitByOrderFeature(outStockOrderPOList);
        }

        WavesStrategyBO wavesStrategyBO = createDTO.getWavesStrategyDTO();

        // 如果没处理出库位，先取默认出库位，再按默认出库位分组
        List<LocationRuleDTO> locRuleList =
            recommendOutLocationBL.getRecommendOutLocation(batchPO, outStockOrderPOList, wavesStrategyBO);

        // 如果没有推荐出库位，直接分组返回
        if (CollectionUtils.isEmpty(locRuleList)) {
            return splitByOrderFeature(outStockOrderPOList);
        }

        locRuleList = locRuleList.stream().filter(m -> Objects.nonNull(m.getLocationId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locRuleList)) {
            return splitByOrderFeature(outStockOrderPOList);
        }

        Map<Integer, LocationRuleDTO> addressLocationMap =
            locRuleList.stream().collect(Collectors.toMap(LocationRuleDTO::getAddressId, v -> v, (v1, v2) -> v1));

        Map<Long, LocationRuleDTO> locationRuleDTOMap =
            locRuleList.stream().collect(Collectors.toMap(LocationRuleDTO::getLocationId, v -> v, (v1, v2) -> v1));

        // 没有出库位的，分一组
        List<OutStockOrderPO> notHaveLocationOrderList = outStockOrderPOList.stream()
            .filter(m -> BooleanUtils.isFalse(haveLocation(addressLocationMap, m.getAddressId())))
            .collect(Collectors.toList());

        // 有出库位的，分一组
        List<OutStockOrderPO> haveLocationOrderList = outStockOrderPOList.stream()
            .filter(m -> BooleanUtils.isTrue(haveLocation(addressLocationMap, m.getAddressId())))
            .collect(Collectors.toList());

        // 有出库位的，按出库位分组
        Map<String, List<OutStockOrderPO>> locationOrderGroupMap =
            haveLocationOrderList.stream().collect(Collectors.groupingBy(order -> {
                LocationRuleDTO locationRuleDTO = addressLocationMap.get(order.getAddressId());
                if (Objects.nonNull(locationRuleDTO)) {
                    return locationRuleDTO.getLocationId().toString().concat(DEFAULT_GROUP);
                }
                return "Default_No_Location";
            }));

        List<SplitBatchTaskByOrderResultBO> totalResultBoList = new ArrayList<>();
        for (Map.Entry<String, List<OutStockOrderPO>> entry : locationOrderGroupMap.entrySet()) {
            totalResultBoList.addAll(splitByOrderFeature(entry.getValue()));
        }

        if (CollectionUtils.isEmpty(notHaveLocationOrderList)) {
            return totalResultBoList;
        }

        List<SplitBatchTaskByOrderResultBO> notHaveLocationOrderResultBOList =
            splitByOrderFeature(notHaveLocationOrderList);

        totalResultBoList.addAll(notHaveLocationOrderResultBOList);

        return totalResultBoList;
    }

    private boolean haveLocation(Map<Integer, LocationRuleDTO> locationRuleDTOMap, Integer addressId) {
        LocationRuleDTO locationRuleDTO = locationRuleDTOMap.get(addressId);
        if (Objects.isNull(locationRuleDTO)) {
            return Boolean.FALSE;
        }
        Long locationId = locationRuleDTO.getLocationId();
        if (Objects.isNull(locationId)) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    public List<SplitBatchTaskByOrderResultBO> splitByOrderFeature(List<OutStockOrderPO> waveOrders) {
        List<Long> outStockOrderIds =
            waveOrders.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());

        final Map<Long, List<Byte>> featureMap = orderFeatureBL.getOrderFeatureMap(outStockOrderIds);

        Map<String, List<OutStockOrderPO>> orderGroupMap =
            waveOrders.stream().collect(Collectors.groupingBy(order -> groupOrderByFeature(order, featureMap)));

        return SplitBatchTaskByOrderResultBO.getNormalTaskList(orderGroupMap);
    }

    private String groupOrderByFeature(OutStockOrderPO order, Map<Long, List<Byte>> featureMap) {
        List<Byte> featureList = featureMap.get(order.getId());
        String defaultKey = MessageFormat.format(pattern, DEFAULT_GROUP, DEFAULT_GROUP);
        boolean isPromotion =
            order.getItems().stream().anyMatch(m -> ConditionStateEnum.是.getType().equals(m.getIsAdvent()));
        Byte promotionCon = isPromotion ? ConditionStateEnum.是.getType() : ConditionStateEnum.否.getType();
        if (CollectionUtils.isEmpty(featureList)) {
            return defaultKey;
        }
        if (featureList.stream().anyMatch(OrderFeatureConstant.FEATURE_TYPE_DRINKING::equals)) {
            return MessageFormat.format(pattern, OrderFeatureConstant.FEATURE_TYPE_DRINKING.toString(), promotionCon);
        }
        if (featureList.stream().anyMatch(OrderFeatureConstant.FEATURE_TYPE_REST::equals)) {
            return MessageFormat.format(pattern, OrderFeatureConstant.FEATURE_TYPE_REST.toString(), promotionCon);
        }
        return defaultKey;
    }

    /**
     * @param createDTO
     * @param batchPO
     * @return
     * @see com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderProcessBL#setBatchTaskToLocation
     */
    private boolean needHandleOutStockLocation(WaveCreateDTO createDTO, BatchPO batchPO) {
        if (Objects.nonNull(createDTO.getSowNo())) {
            return Boolean.FALSE;
        } else if (Objects.nonNull(createDTO.getToLocationId())) {
            return Boolean.FALSE;
        } else if (createDTO.getAllocationFlag()) {
            return Boolean.FALSE;
        } else if (Objects.equals(createDTO.getBatchTaskType(), BatchTaskTypeEnum.按自提点.getType())) {
            return Boolean.FALSE;
        } else if (Objects.nonNull(batchPO.getOrderSelection())
            && WavesStrategyConstants.ORDERSELECTION_NO != batchPO.getOrderSelection()) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }
}
