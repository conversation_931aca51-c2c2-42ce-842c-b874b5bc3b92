package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.assertj.core.api.Assertions;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationLargePickCargoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationOpenLocationGroupBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationRecommendBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchBaseBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CreateBatchLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.CreateBatchByManualOperationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.lock.LockCreateBatchBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderItemDetailAllotDTO;

/**
 * <AUTHOR>
 * @title: CreateBatchLocationRobotCargoBLTest
 * @description:
 * @date 2022-12-02 10:45
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class CreateBatchLocationLargePickCargoBLTest {

    @Autowired
    private BatchOrderProcessBL batchOrderProcessBL;
    @Autowired
    private CreateBatchLocationLargePickCargoBL cargoBL;
    @Autowired
    private CreateBatchLocationOpenLocationGroupBL createBatchLocationOpenLocationGroupBL;
    @Autowired
    private CreateBatchLocationRecommendBL createBatchLocationRecommendBL;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Autowired
    private CreateBatchByManualOperationBL createBatchByManualOperationBL;

    @Test
    public void openLocationGroupTest() {
        List<Long> orderIds = Arrays.asList(998000221124149798L, 998000221124149797L, 998000221123169781L,
            998000221123109734L, 998000221123109721L);
        List<OutStockOrderPO> outStockOrderList = outStockOrderMapper
            .findByOrderIdWithoutCon(orderIds.stream().map(String::valueOf).collect(Collectors.toList()));

        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(9981);

        CreateBatchLocationBO bo = new CreateBatchLocationBO();
        bo.setWarehouseId(9981);
        bo.setWarehouseConfigDTO(warehouseConfigDTO);

        // List<OutStockOrderPO> orderPOList = createBatchLocationOpenLocationGroupBL.setLocation(outStockOrderList,
        // bo);
        //
        // Assertions.assertThat(orderPOList).isNotNull();
    }

    @Test
    public void recommendLocationTest() {
        List<Long> orderIds = Arrays.asList(998000221124149798L, 998000221124149797L, 998000221123169781L,
            998000221123109734L, 998000221123109721L);
        List<OutStockOrderPO> outStockOrderList = outStockOrderMapper
            .findByOrderIdWithoutCon(orderIds.stream().map(String::valueOf).collect(Collectors.toList()));

        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(9981);

        CreateBatchLocationBO bo = new CreateBatchLocationBO();
        bo.setWarehouseId(9981);
        bo.setWarehouseConfigDTO(warehouseConfigDTO);

        // List<OutStockOrderPO> orderPOList = createBatchLocationRecommendBL.setLocation(outStockOrderList, bo);
        //
        // Assertions.assertThat(orderPOList).isNotNull();
    }

    @Test
    public void locationTest() {
        // 998000221124159800L
        List<Long> orderIds = Arrays.asList(998000221124149798L, 998000221124149797L, 998000221123169781L,
            998000221123109734L, 998000221123109721L);
        List<OutStockOrderPO> outStockOrderList = outStockOrderMapper
            .findByOrderIdWithoutCon(orderIds.stream().map(String::valueOf).collect(Collectors.toList()));

        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(9981);

        CreateBatchLocationBO bo = new CreateBatchLocationBO();
        bo.setWarehouseId(9981);
        bo.setWarehouseConfigDTO(warehouseConfigDTO);

        // List<OutStockOrderPO> orderList = cargoBL.setLocation(outStockOrderList, bo);
        //
        // List<OutStockOrderItemPO> totalItemList = orderList.stream().flatMap(m ->
        // m.getItems().stream()).collect(Collectors.toList());
        //
        // List<OutStockOrderItemPO> passageItemLis = new ArrayList<>();
        //
        // List<OutStockOrderItemPO> coloItemList =
        // LocationSplitHelperResultBOConvertor.copyItemList(totalItemList.stream().filter(m ->
        // m.getSkuid().equals(5038459890122859460L)).collect(Collectors.toList()));
        // List<OutStockOrderItemPO> hahaItemList =
        // LocationSplitHelperResultBOConvertor.copyItemList(totalItemList.stream().filter(m ->
        // m.getSkuid().equals(5013425378064305107L)).collect(Collectors.toList()));
        // List<OutStockOrderItemPO> teaItemList =
        // LocationSplitHelperResultBOConvertor.copyItemList(totalItemList.stream().filter(m ->
        // m.getSkuid().equals(4983004920933682706L)).collect(Collectors.toList()));
        //
        //
        // List<OutStockOrderItemPO> lstAdded = new ArrayList<>();
        //
        // passageItemLis.add(totalItemList.get(0));
        // passageItemLis.add(totalItemList.get(2));
        //
        // WavesStrategyDTO wavesStrategyDTO = new WavesStrategyDTO();
        // wavesStrategyDTO.setPassPickType(PassagePickTypeEnum.不开启.getType());
        // List<OutStockOrderItemPO> itemList = MergeSplitItemConvertor.mergerItemList(passageItemLis, totalItemList,
        // lstAdded, warehouseConfigDTO, wavesStrategyDTO);
        //
        // Assertions.assertThat(itemList).isNotNull();
    }

    @Test
    public void createBatchTest() {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setCityId(998);
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setPickingType((byte)2);
        batchCreateDTO.setPickingGroupStrategy((byte)2);
        batchCreateDTO.setPassPickType((byte)1);
        batchCreateDTO.setGroupType(1);
        batchCreateDTO.setOperateUser("临时外包-张裔譞阿里");
        batchCreateDTO.setOrderIdList(Arrays.asList("5337000840099376523", "5337000895875231109"));

        CreateBatchBaseBO createBatchBaseBO = new CreateBatchBaseBO();
        createBatchBaseBO.setBatchCreateDTO(batchCreateDTO);
        createBatchByManualOperationBL.createBatch(createBatchBaseBO);

        Assert.assertNotNull(batchCreateDTO);
    }

    @Test
    public void getOrderItemDetailAllotMapTest() {
        List<Long> orderItemIds = Arrays.asList(5337000896232711139L, 5337000840557519853L);
        Integer warehouseId = 9981;
        Map<Long, List<OrderItemDetailAllotDTO>> map =
            batchOrderTaskBL.getOrderItemDetailAllotMap(orderItemIds, warehouseId,false);
        Assertions.assertThat(map).isNotNull();
    }

    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    @Test
    public void handleLiquidNotPackAllotInfoTest() {
        List<Long> orderItemIds = Arrays.asList(5335597517972134400L, 5335597518014077447L);
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOS =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(orderItemIds);
        Map<Long, List<OrderItemDetailAllotDTO>> map =
            batchOrderTaskBL.handleLiquidNotPackAllotInfo(orderItemTaskInfoPOS, orderItemIds);

        Assertions.assertThat(map).isNotNull();
    }

    @Autowired
    private BatchOrderBL batchOrderBL;

    @Test
    public void sortOrderItemTaskInfoDetailTest() {
        List<Long> orderItemIds = Arrays.asList(5335597517972134400L, 5335597518014077447L);
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOS =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(orderItemIds);
        List<OrderItemTaskInfoDetailPO> taskInfoDetailPOList =
            orderItemTaskInfoPOS.stream().flatMap(m -> m.getDetailList().stream()).collect(Collectors.toList());

        List<OrderItemTaskInfoDetailPO> list =
            batchOrderBL.sortOrderItemTaskInfoDetail(taskInfoDetailPOList, 999, false);

        Assertions.assertThat(list).isNotNull();
    }

    @Test
    public void findBatchTaskSortListTest() {
        BatchTaskSortQueryDTO batchTaskSortQueryDTO = new BatchTaskSortQueryDTO();
        // batchTaskSortQueryDTO.setBatchTaskType((byte)1);
        batchTaskSortQueryDTO.setWarehouseId(9981);
        batchTaskSortQueryDTO.setCityId(998);
        batchTaskSortQueryDTO.setUserId(68364588);
        batchTaskSortQueryDTO.setSorterType((byte)2);
        batchTaskSortQueryDTO.setType(1);
        batchTaskSortQueryDTO.setPageSize(200);

        PageList<BatchTaskSortDTO> pageList = batchOrderTaskBL.findBatchTaskSortList(batchTaskSortQueryDTO);

        Assertions.assertThat(pageList).isNotNull();
    }

    @Autowired
    private LockCreateBatchBL lockCreateBatchBL;

    @Test
    public void createBatchSowControlTest() {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setCityId(998);
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setPickingType((byte)2);
        batchCreateDTO.setPickingGroupStrategy((byte)2);
        batchCreateDTO.setPassPickType((byte)1);
        batchCreateDTO.setGroupType(1);
        batchCreateDTO.setOperateUser("临时外包-张裔譞阿里");
        batchCreateDTO.setOrderIdList(Arrays.asList("5335642697364400974"));

        CreateBatchBaseBO createBatchBaseBO = new CreateBatchBaseBO();
        createBatchBaseBO.setBatchCreateDTO(batchCreateDTO);

        lockCreateBatchBL.releaseLock(Collections.singletonList(5335642697364400974L),
            createBatchBaseBO.getWarehouseId());

        createBatchByManualOperationBL.createBatch(createBatchBaseBO);

        Assert.assertNotNull(batchCreateDTO);
    }

}
