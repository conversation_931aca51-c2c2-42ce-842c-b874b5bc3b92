package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 拣货任务提交标识
 *
 * <AUTHOR>
 * @date 2019/06/17 17:30
 */
public enum SubmitFlagEnum {
    /**
     * 初次提交
     */
    初次提交((byte)0),
    /**
     * 修改提交
     */
    修改提交((byte)1);

    /**
     * type
     */
    private Byte type;

    SubmitFlagEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

}
