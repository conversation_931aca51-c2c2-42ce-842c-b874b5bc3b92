package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/10/12
 */
public class OutStockOrderItemDetailRecoverErpDTO implements Serializable {

    /**
     * 是否修复erp数据
     */
    private Boolean recoverErpOrNot = Boolean.FALSE;
    /**
     * 是否发送整车
     */
    private Boolean sendAllBatch = Boolean.FALSE;
    /**
     * 仅仅发送变更的订单
     */
    private Boolean sendChangeOrder = Boolean.TRUE;

    /**
     * 获取 是否修复erp数据
     *
     * @return recoverErpOrNot 是否修复erp数据
     */
    public boolean isRecoverErpOrNot() {
        return this.recoverErpOrNot;
    }

    /**
     * 设置 是否修复erp数据
     *
     * @param recoverErpOrNot 是否修复erp数据
     */
    public void setRecoverErpOrNot(Boolean recoverErpOrNot) {
        this.recoverErpOrNot = recoverErpOrNot;
    }

    /**
     * 获取 是否发送整车
     *
     * @return sendAllBatch 是否发送整车
     */
    public Boolean isSendAllBatch() {
        return this.sendAllBatch;
    }

    /**
     * 设置 是否发送整车
     *
     * @param sendAllBatch 是否发送整车
     */
    public void setSendAllBatch(Boolean sendAllBatch) {
        this.sendAllBatch = sendAllBatch;
    }

    /**
     * 获取 仅仅发送变更的订单
     *
     * @return sendChangeOrder 仅仅发送变更的订单
     */
    public Boolean getSendChangeOrder() {
        return this.sendChangeOrder;
    }

    /**
     * 设置 仅仅发送变更的订单
     *
     * @param sendChangeOrder 仅仅发送变更的订单
     */
    public void setSendChangeOrder(Boolean sendChangeOrder) {
        this.sendChangeOrder = sendChangeOrder;
    }
}
