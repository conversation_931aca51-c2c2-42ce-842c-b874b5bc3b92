package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.util.List;

import com.yijiupi.himalaya.base.search.PageCondition;

public class AddressSowTaskQueryDTO extends PageCondition {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 播种任务明细id
     */
    private List<Long> sowTaskItemIds;

    /**
     * 自提点位id
     */
    private List<Long> addressLocationIds;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 播种状态
     */
    private List<Byte> states;

    /**
     * 播种人id
     */
    private Integer operatorId;

    /**
     * 查询模式(0.列表 1.明细)
     */
    private Byte queryCondition;

    /**
     * skuIds
     */
    private List<Long> skuIds;

    /**
     * 已完成的自提点位id
     */
    private List<String> overLocationIds;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<Long> getSowTaskItemIds() {
        return sowTaskItemIds;
    }

    public void setSowTaskItemIds(List<Long> sowTaskItemIds) {
        this.sowTaskItemIds = sowTaskItemIds;
    }

    public List<Long> getAddressLocationIds() {
        return addressLocationIds;
    }

    public void setAddressLocationIds(List<Long> addressLocationIds) {
        this.addressLocationIds = addressLocationIds;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Byte getQueryCondition() {
        return queryCondition;
    }

    public void setQueryCondition(Byte queryCondition) {
        this.queryCondition = queryCondition;
    }

    public List<Byte> getStates() {
        return states;
    }

    public void setStates(List<Byte> states) {
        this.states = states;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public List<Long> getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(List<Long> skuIds) {
        this.skuIds = skuIds;
    }

    public List<String> getOverLocationIds() {
        return overLocationIds;
    }

    public void setOverLocationIds(List<String> overLocationIds) {
        this.overLocationIds = overLocationIds;
    }
}
