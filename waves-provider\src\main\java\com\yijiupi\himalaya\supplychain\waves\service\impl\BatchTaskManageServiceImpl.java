package com.yijiupi.himalaya.supplychain.waves.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderInfoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.WaitSplitOrderQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.*;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderLocationInfoToTmsDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderUpdateLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.CompleteReceiveTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.UpdateBatchTaskLocaitonDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 波次任务操作
 *
 * <AUTHOR> 2018/4/4
 */
@Service(timeout = 120000)
public class BatchTaskManageServiceImpl implements IBatchTaskManageService {

    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;
    @Autowired
    private BatchOrderInfoBL batchOrderInfoBL;
    @Autowired
    private BatchTaskFinishBL batchTaskFinishBL;
    @Autowired
    private BatchTaskLocationModBL batchTaskLocationModBL;
    @Autowired
    private BatchTaskCompleteBL batchTaskCompleteBL;
    @Autowired
    private BatchTaskQueryBL batchTaskQueryBL;
    @Autowired
    private BatchTaskCompleteApplicationBL batchTaskCompleteApplicationBL;

    protected static final Logger LOGGER = LoggerFactory.getLogger(BatchTaskManageServiceImpl.class);

    /**
     * 拣货任务确认完成
     *
     * <AUTHOR>
     */
    @Override
    public BatchTaskCompleteResultDTO batchTaskComplete(BatchTaskConfirmDTO batchTaskConfirmDTO) {
        AssertUtils.notEmpty(batchTaskConfirmDTO.getBatchTaskNo(), "拣货任务号不能为空");
        AssertUtils.notNull(batchTaskConfirmDTO.getWarehouseId(), "仓库Id不能为空");
        // AssertUtils.notNull(locationId, "货位Id不能为空");
        // AssertUtils.notNull(locationName, "货位名称不能为空");
        BatchTaskDTO batchTaskDTO = batchTaskQueryBL.getBatchTaskByNo(batchTaskConfirmDTO.getBatchTaskNo().get(0));
        try {
            return batchTaskCompleteApplicationBL.batchTaskComplete(batchTaskConfirmDTO, batchTaskDTO.getBatchNo());
            // return batchOrderTaskBL.batchTaskComplete(batchTaskConfirmDTO);
        } catch (BusinessValidateException e) {
            if (e.getMessage().contains("已经加锁")) {
                throw new BusinessValidateException("拣货任务正在处理中，请稍后再试！");
            }
            throw e;
        }
    }

    /**
     * 根据波次任务编号查找默认片区
     *
     * @param batchTaskNoS
     */
    @Override
    public List<String> findAreaListByTaskNo(List<String> batchTaskNoS) {
        AssertUtils.notEmpty(batchTaskNoS, "拣货任务号不能为空");
        return batchOrderTaskBL.findAreaListByTaskNo(batchTaskNoS);
    }

    /**
     * 拣货任务确认合并
     *
     * @param batchTaskMergeDTO
     */
    @Override
    public void batchTaskConfirmMerge(BatchTaskMergeDTO batchTaskMergeDTO) {
        batchOrderTaskBL.batchTaskConfirmMerge(batchTaskMergeDTO);
    }

    /**
     * 拣货任务确认拆分
     *
     * @param batchTaskSplitDTO
     */
    @Override
    public void batchTaskConfirmSplit(BatchTaskSplitDTO batchTaskSplitDTO) {
        batchOrderTaskBL.batchTaskConfirmSplit(batchTaskSplitDTO);
    }

    /**
     * 拣货任务拆分(PDA)
     */
    @Override
    public void batchTaskConfirmSplitByPDA(BatchTaskSplitByPdaDTO batchTaskSplitByPdaDTO) {
        batchOrderTaskBL.batchTaskConfirmSplitByPDA(batchTaskSplitByPdaDTO);
    }

    /**
     * 根据波次任务编号查询待拆分订单（按订单接货）
     *
     * @param batchTaskNo
     * @param currentPage
     * @param pageSize
     * @return
     */
    @Override
    public PageList<BatchTaskSplitOrderDTO> listNotSplitOrderByBatchTaskNo(String batchTaskNo, Integer currentPage,
        Integer pageSize) {
        return batchOrderTaskBL.listNotSplitOrderByBatchTaskNo(batchTaskNo, currentPage, pageSize);
    }

    /**
     * 根据波次任务编号查询待拆分订单(分页)
     *
     * @param waitSplitOrderQueryDTO
     * @return
     */
    @Override
    public PageList<BatchTaskSplitOrderDTO>
        listWaitSplitOrderByBatchTaskNo(WaitSplitOrderQueryDTO waitSplitOrderQueryDTO) {
        return batchOrderTaskBL.listNotSplitOrderByBatchTaskNo(waitSplitOrderQueryDTO.getBatchTaskNo(),
            waitSplitOrderQueryDTO.getCurrentPage(), waitSplitOrderQueryDTO.getPageSize());
    }

    /**
     * 获取波次默认备货区
     */
    @Override
    public LoactionDTO findDefaultLocationByBatchNo(Integer orgId, String batchNo) {
        return batchOrderTaskBL.findDefaultLocationByBatchNo(orgId, batchNo);
    }

    /**
     * 获取拣货任务默认备货区
     */
    @Override
    public LoactionDTO findDefaultLocationByBatchTaskNo(Integer orgId, String batchTaskNo) {
        return batchOrderTaskBL.findDefaultLocationByBatchTaskNo(orgId, batchTaskNo);
    }

    /**
     * 修改拣货任务的周转区
     */
    @Override
    public void updateBatchTaskLocation(UpdateBatchTaskLocaitonDTO updateBatchTaskLocaitonDTO) {
        batchOrderTaskBL.updateBatchTaskLocation(updateBatchTaskLocaitonDTO);
    }

    /**
     * 待打印数据保存
     *
     * @param orderPrintInfoDTO
     */
    @Override
    public void saveOrderPrintInfo(OrderPrintInfoDTO orderPrintInfoDTO) {
        AssertUtils.notNull(orderPrintInfoDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(orderPrintInfoDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(orderPrintInfoDTO.getPrinter(), "打印机名称不能为空");
        AssertUtils.notEmpty(orderPrintInfoDTO.getOrderNos(), "订单号不能为空");

        // redis版本
        batchOrderInfoBL.saveOrderPrintInfo(orderPrintInfoDTO);
        // MQTT版本
        // batchOrderInfoBL.pushPrintingOrderMsg(orderPrintInfoDTO);
    }

    /**
     * 集货任务完成
     *
     * @param completeReceiveTaskDTO
     */
    @Override
    public void completeReceiveTask(CompleteReceiveTaskDTO completeReceiveTaskDTO) {
        AssertUtils.notNull(completeReceiveTaskDTO.getOutStockOrderId(), "订单id不能为空");
        batchOrderTaskBL.completeReceiveTask(completeReceiveTaskDTO);
    }

    /**
     * 拣货任务修改出库位
     */
    @Override
    public void updateBatchTaskLocationByCk(BatchTaskUpdateLocationDTO batchTaskUpdateLocationDTO) {
        batchOrderTaskBL.updateBatchTaskLocationByCk(batchTaskUpdateLocationDTO);
    }

    /**
     * 订单修改出库位
     */
    @Override
    public void updateOrderLocationByCk(OutStockOrderUpdateLocationDTO outStockOrderUpdateLocationDTO) {
        batchOrderTaskBL.updateOrderLocationByCk(outStockOrderUpdateLocationDTO);
    }

    /**
     * 修改拣货任务明细（op后台工具）
     */
    @Override
    public void updateBatchTaskItem(BatchTaskItemDTO batchTaskItemDTO) {
        batchOrderTaskBL.updateBatchTaskItem(batchTaskItemDTO);
    }

    /**
     * 拣货任务移库（op后台工具）
     */
    @Override
    public void updateBatchTaskPickCount(String batchTaskNo) {
        batchOrderTaskBL.updateBatchTaskPickCount(batchTaskNo);
    }

    @Override
    public BatchTaskCompleteResultDTO
        checkOutStockByOutStockConfig(List<BatchTaskItemCompleteDTO> batchTaskItemUpdateDTOList, Integer warehouseId) {
        return batchOrderTaskBL.checkOutStockByOutStockConfig(batchTaskItemUpdateDTOList, warehouseId);
    }

    /**
     * 重置拣货任务明细的拣货人
     *
     * @param dto
     */
    @Override
    public void wcsAssignSorter(WCSAssignItemSorterDTO dto) {
        if (CollectionUtils.isEmpty(dto.getBatchTaskItemIds()) && StringUtils.isEmpty(dto.getBatchTaskItemId())) {
            throw new BusinessValidateException("拣货任务明细不能为空！");
        }

        batchOrderTaskBL.wcsAssignSorter(dto);
    }

    /**
     * 完成拣货任务明细，明细全部完成后自动完成拣货任务
     *
     * @param dto
     */
    @Override
    public void completeRobotBatchTask(CompleteRobotBatchTaskDTO dto) {
        AssertUtils.notNull(dto.getBatchTaskId(), "拣货任务信息不能为空！");
        AssertUtils.notEmpty(dto.getComleteBatchTaskItemIdList(), "拣货任务明细信息不能为空！");

        batchTaskFinishBL.completeRobotBatchTask(dto);
    }

    /**
     * 2.5 和 2.5+ 货位变更->未开始拣货的拣货任务的货位也变更
     *
     * @param dto
     */
    @Override
    public void pickLocationModToBatchTask(PickLocationModToBatchTaskDTO dto) {
        try {
            batchTaskLocationModBL.batchTaskLocationMod(dto);
        } catch (Exception e) {
            LOGGER.warn("更新拣货任务项货位信息失败：" + JSON.toJSONString(dto), e);
        }
    }

    @Override
    public void updateBatchTask(BatchTaskDTO updateBatchTask) {
        batchTaskLocationModBL.updateBatchTask(updateBatchTask);
    }

    /**
     * 拣货任务完成接口
     *
     * @param batchTaskCompleteDTO
     */
    @Override
    public void completeBatchTask(BatchTaskCompleteDTO batchTaskCompleteDTO) {
        AssertUtils.notNull(batchTaskCompleteDTO.getBatchTaskId(), "拣货任务信息不能为空！");
        AssertUtils.notNull(batchTaskCompleteDTO.getOptUserId(), "操作人信息不能为空！");
        BatchTaskDTO batchTaskDTO = batchTaskQueryBL.getBatchTaskById(batchTaskCompleteDTO.getBatchTaskId());
        try {
            batchTaskCompleteDTO.setWarehouseId(batchTaskDTO.getWarehouseId());
            batchTaskCompleteApplicationBL.completeBatchTask(batchTaskCompleteDTO, batchTaskDTO.getBatchNo());
            // batchTaskCompleteBL.completeBatchTask(batchTaskCompleteDTO);
        } catch (BusinessValidateException e) {
            if (e.getMessage().contains("已经加锁")) {
                throw new BusinessValidateException("拣货任务正在处理中，请稍后再试！");
            }
            throw e;
        }
    }

    /**
     * 拍灯领取拣货任务
     *
     * @param dto
     */
    @Override
    public void getDigitalBatchTask(DigitalGetBatchTaskDTO dto) {
        AssertUtils.notNull(dto.getOptUserId(), "操作人不能为空！");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");
        AssertUtils.hasText(dto.getBatchTaskId(), "拣货任务信息不能为空！");

        batchOrderTaskBL.getDigitalBatchTask(dto);
    }

    /**
     * 清理电子标签拣货任务
     *
     * @param dto
     */
    @Override
    public void cleanUpDigitalTask(CleanUpDigitalTaskDTO dto) {
        AssertUtils.notNull(dto.getOptUserId(), "操作人不能为空！");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");
        AssertUtils.notEmpty(dto.getBatchTaskIds(), "拣货任务信息不能为空！");

        batchOrderTaskBL.cleanUpDigitalTask(dto);
    }

    /**
     * 同步拣货任务到电子标签
     *
     * @param dto
     */
    @Override
    public void syncBatchTaskToRfid(BatchTaskDTO dto) {
        AssertUtils.notNull(dto.getSorter(), "操作人不能为空！");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");
        AssertUtils.notEmpty(dto.getBatchTaskIds(), "拣货任务id列表不能为空！");

        batchOrderTaskBL.syncBatchTaskToRfid(dto);
    }

    /**
     * 拣货任务拣货方式调整
     *
     * @param dto
     */
    @Override
    public void updateBatchTaskKindOfPicking(BatchTaskDTO dto) {
        AssertUtils.notNull(dto.getSorter(), "操作人不能为空！");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");
        AssertUtils.notEmpty(dto.getBatchTaskIds(), "拣货任务id列表不能为空！");
        if (Objects.isNull(dto.getPickPattern()) && Objects.isNull(dto.getKindOfPicking())) {
            throw new BusinessValidateException("拣货方式不能为空！");
        }

        batchOrderTaskBL.updateBatchTaskKindOfPicking(dto);
    }

    /**
     * 更新拣货模式
     *
     * @param dto
     */
    @Override
    public void changeKindOfPickingToPickPattern(CleanUpDigitalTaskDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");
        AssertUtils.notEmpty(dto.getBatchTaskIds(), "拣货任务信息不能为空！");

        batchOrderTaskBL.changeKindOfPickingToPickPattern(dto);
    }

    /**
     * 同步托盘信息给tms
     *
     * @param dto
     */
    @Override
    public void sendOrderLocationInfoToTms(OrderLocationInfoToTmsDTO dto) {
        batchOrderTaskBL.sendOrderLocationInfoToTms(dto);
    }

    /**
     * SCM-22614 按客户拣货，相同地址合并拣货 1、开启了【仓库按订单实时分拣】，未开启【是否开启接力分拣模式】时，休百订单或酒饮订单分拣完成，都要选择绑定托盘位，同时同客户其他订单同步出库位和托盘位 补充： 1）前提条件是
     * 开启了 isNeedPalletForStock 按订单拣货装托 2）已完成的订单，同步到当前分拣的出库位和托盘位 不允许修改 3）开启了【仓库按订单实时分拣】未开启【是否开启接力分拣模式】，未开启
     * isNeedPalletForStock 按订单拣货装托，实时同步出库位信息
     *
     * @param dto
     * @return
     */
    @Override
    public UserSamePalletInfoResultDTO findSameUserPalletInfo(UserSamePalletInfoQueryDTO dto) {
        AssertUtils.hasText(dto.getOrderNo(), "订单信息不能为空！");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");

        return batchOrderTaskBL.findSameUserPalletInfo(dto);
    }
}
