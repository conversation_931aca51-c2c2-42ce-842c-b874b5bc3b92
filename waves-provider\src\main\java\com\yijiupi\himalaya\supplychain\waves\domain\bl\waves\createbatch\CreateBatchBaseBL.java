package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.supplychain.baseutil.NullUtils;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.CarDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.ICarService;
import com.yijiupi.himalaya.supplychain.outstock.service.IWMSOrderExceptionService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.waves.advice.MarkMethodInvokeFlag;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.*;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.pickup.IdleOrderPickerBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchBaseBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.lock.LockCreateBatchBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.split.SplitBatchTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchCreateDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchTitleConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.OutStockOrderConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.WavesStrategyBOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByRefOrderNoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.DeliveryTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.secondsort.SecondPickLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.secondsort.SecondPickLocationSyncDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.PickingTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderSearchSO;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;
import com.yijiupi.supplychain.serviceutils.constant.redis.RedisConstant;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/10/16
 */
public abstract class CreateBatchBaseBL {

    @Reference
    protected ICarService iCarService;
    @Reference
    private IWMSOrderExceptionService iwmsOrderExceptionService;
    @Reference
    protected IWarehouseQueryService warehouseQueryService;

    @Autowired
    protected WavesStrategyBOConvertor wavesStrategyBOConvertor;
    @Autowired
    protected OutStockOrderBL outStockOrderBL;
    @Autowired
    protected BatchOrderProcessBL batchOrderProcessBL;
    @Autowired
    protected SecondSortBL secondSortBl;
    @Autowired
    protected BatchOrderProcessTransferBL batchOrderProcessTransferBL;
    @Autowired
    private LockCreateBatchBL lockCreateBatchBL;
    @Autowired
    protected BatchTitleConvertor batchTitleConvertor;
    @Autowired
    protected OutStockOrderMapper outStockOrderMapper;
    @Autowired
    protected OutStockOrderItemMapper outStockOrderItemMapper;
    @Resource
    private OrderCenterBL orderCenterBL;
    @Autowired
    protected CreateBatchValidateBL createBatchValidateBL;
    @Autowired
    protected BatchCreateDTOConvertor batchCreateDTOConvertor;
    @Autowired
    private OrderFeatureBL orderFeatureBL;
    @Autowired
    private SplitBatchTaskBL splitBatchTaskBL;
    @Resource
    private IdleOrderPickerBL idleOrderPickerBL;

    @Autowired
    private GlobalCache globalCache;

    protected static final Integer MAX_SIZE = 180;
    private static final List<Integer> OUTBOUNDTYPE_LIST = Arrays.asList(OutBoundTypeEnum.ALLOT_ORDER.getCode(),
            OutBoundTypeEnum.SALE_ORDER.getCode(), OutBoundTypeEnum.SELF_PICKUP_SALE_ORDER.getCode(),
            OutBoundTypeEnum.INTERNAL_DELIVERY_ORDER.getCode(), OutBoundTypeEnum.OFFLINE_OUT_ORDER.getCode());
    protected static final Logger LOG = LoggerFactory.getLogger(CreateBatchBaseBL.class);

    /**
     * 生成波次 2025-02-12 增加仓库维度并发锁，同一个仓只能有一个正在进行中的任务，其他等待
     *
     * @param createBatchBaseBO
     */
    @MarkMethodInvokeFlag(key = RedisConstant.SUP_F + "UserOnlineCache", warehouseId = "#createBatchBaseBO.warehouseId",
            userId = "#createBatchBaseBO.optUserId")
    @DistributeLock(conditions = "#createBatchBaseBO.warehouseId", sleepMills = 600000, expireMills = 600000,
            key = RedisConstant.SUP_F + "createBatchLimitByWarehouse", lockType = DistributeLock.LockType.WAITLOCK)
    public void createBatch(CreateBatchBaseBO createBatchBaseBO) {
        long start = System.currentTimeMillis();
        validateParam(createBatchBaseBO);
        doPreCreateBatch(createBatchBaseBO);
        List<OutStockOrderPO> outStockOrderPOList = filterOutStockOrder(getOutStockOrder(createBatchBaseBO));
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            LOG.info("过滤后没有需要创建波次的订单，入参{}", JSON.toJSONString(createBatchBaseBO));
            return;
        }
        validateDetailInfo(outStockOrderPOList);
        validateBusiness(createBatchBaseBO, outStockOrderPOList);
        List<Long> lock = lockCreateBatchBL.lock(outStockOrderPOList, createBatchBaseBO.getWarehouseId());
        try {
            doCreateBatch(createBatchBaseBO, outStockOrderPOList);
        } catch (BusinessValidateException e) {
            if (e.getMessage().contains("已经加锁")) {
                throw new BusinessValidateException("选中订单已被处理，请刷新界面后，重新选择订单创建波次！");
            }
            throw e;
        } catch (Exception e) {
            LOG.warn("创建波次执行失败：{}原因:", JSON.toJSONString(createBatchBaseBO), e);
            throw e;
        } finally {
            lockCreateBatchBL.releaseLock(lock, createBatchBaseBO.getWarehouseId());
            LOG.info("创建波次执行完毕：{}", JSON.toJSONString(createBatchBaseBO));
        }
        // 波次创建完毕后通知中台
        orderCenterBL.createWaveNotifyByOrder(outStockOrderPOList, createBatchBaseBO);
        // 波次创建完毕后自动指派空闲分拣工
        idleOrderPickerBL.assignIdleOrderPicker(outStockOrderPOList, createBatchBaseBO.getWarehouseId());
        long end = System.currentTimeMillis();
        log(start, end, createBatchBaseBO);
    }

    private void log(long start, long end, CreateBatchBaseBO createBatchBaseBO) {
        long duration = (end - start) / (1000);
        if (duration >= 60) {
            LOG.info("创建波次执行完毕：{} ; 执行时间大于 {} 分钟 : 为 ：{}", JSON.toJSONString(createBatchBaseBO), duration / 60,
                    duration);
        }
    }

    private void validateDetailInfo(List<OutStockOrderPO> outStockOrderPOList) {
        List<OutStockOrderPO> needValidateOrderList =
                outStockOrderPOList.stream().filter(m -> Objects.nonNull(m.getOutBoundType()))
                        .filter(m -> OUTBOUNDTYPE_LIST.contains(m.getOutBoundType().intValue())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needValidateOrderList)) {
            return;
        }
        // 只校验数量不为0 的
        List<OutStockOrderItemPO> needValidateOrderItemList =
                needValidateOrderList.stream().flatMap(m -> m.getItems().stream())
                        .filter(m -> m.getUnittotalcount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        List<Long> notHaveDetailList = new ArrayList<>();
        List<Long> detailNotEqualList = new ArrayList<>();
        for (OutStockOrderItemPO outStockOrderItemPO : needValidateOrderItemList) {
            if (CollectionUtils.isEmpty(outStockOrderItemPO.getItemDetails())) {
                notHaveDetailList.add(outStockOrderItemPO.getOutstockorderId());
                continue;
            }

            BigDecimal detailUnitTotalCount = outStockOrderItemPO.getItemDetails().stream()
                    .map(OutStockOrderItemDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (detailUnitTotalCount.compareTo(outStockOrderItemPO.getUnittotalcount()) != 0) {
                detailNotEqualList.add(outStockOrderItemPO.getOutstockorderId());
            }
        }

        if (CollectionUtils.isEmpty(notHaveDetailList) && CollectionUtils.isEmpty(detailNotEqualList)) {
            return;
        }
        String errorMsg = "";
        if (!CollectionUtils.isEmpty(notHaveDetailList)) {
            errorMsg =
                    errorMsg.concat(String.format("出库单货主信息不存在, 出库单信息为: %s ; ", JSON.toJSONString(notHaveDetailList)));
        }
        if (!CollectionUtils.isEmpty(detailNotEqualList)) {
            errorMsg =
                    errorMsg.concat(String.format("出库单货主信息与订单信息不匹配, 出库单信息为: %s ;", JSON.toJSONString(detailNotEqualList)));
        }

        LOG.warn(errorMsg);
        // throw new BusinessValidateException(errorMsg);
    }

    protected abstract void doPreCreateBatch(CreateBatchBaseBO createBatchBaseBO);

    protected abstract void validateParam(CreateBatchBaseBO createBatchBaseBO);

    protected abstract void validateBusiness(CreateBatchBaseBO createBatchBaseBO,
                                             List<OutStockOrderPO> outStockOrderPOList);

    protected abstract void doCreateBatch(CreateBatchBaseBO createBatchBaseBO,
                                          List<OutStockOrderPO> outStockOrderPOList);

    protected abstract List<OutStockOrderPO> getOutStockOrder(CreateBatchBaseBO createBatchBaseBO);

    protected void updateLocation(BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO,
                                  List<OutStockOrderPO> outStockOrderPOList) {
        // 判断尾货是否为空
        List<OutStockOrderItemPO> collect = outStockOrderPOList.stream()
                .flatMap(o -> o.getItems().stream().filter(oItem -> ObjectUtils.isEmpty(oItem.getLocationId())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            // 设置过出库位 不需要再设置
            LOG.warn("已经设置过出库位 不需要设置");
            return;
        }
        // 出库位为空 修改出库位
        List<DeliveryTaskDTO> deliveryTaskList = batchCreateByRefOrderNoDTO.getDeliveryTaskList();
        if (!CollectionUtils.isEmpty(deliveryTaskList)) {
            // 车次集合不为空 根据车次id 查询默认出库位
            for (DeliveryTaskDTO deliveryTaskDTO : deliveryTaskList) {
                if (ObjectUtils.isEmpty(deliveryTaskDTO.getDeliveryCarId())) {
                    LOG.warn("车次id为空");
                    continue;
                }
                // 修改出库位信息
                List<String> orderNoList = deliveryTaskDTO.getOrderNoList();
                if (CollectionUtils.isEmpty(orderNoList)) {
                    continue;
                }

                List<OutStockOrderPO> outStockOrderPOS = outStockOrderPOList.stream()
                        .filter(c -> orderNoList.contains(c.getReforderno())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(outStockOrderPOS)) {
                    continue;
                }
                CarDTO carInfo = iCarService.findOne(deliveryTaskDTO.getDeliveryCarId());
                if (ObjectUtils.isEmpty(carInfo)) {
                    LOG.warn("未查询到默认出库位");
                    continue;
                }
                // 设置出库位
                Long defaultDeliveryLocation_id = carInfo.getDefaultDeliveryLocation_Id();
                if (ObjectUtils.isEmpty(defaultDeliveryLocation_id)) {
                    LOG.warn("未查询到默认出库位");
                    continue;
                }
                for (OutStockOrderPO outStockOrderPO : outStockOrderPOS) {
                    List<OutStockOrderItemPO> items = outStockOrderPO.getItems();
                    for (OutStockOrderItemPO item : items) {
                        item.setLocationId(defaultDeliveryLocation_id);
                        item.setLocationName(carInfo.getDefaultDeliveryLocation());
                    }
                }
                outStockOrderItemMapper.updateOutStockOrderItemLocationByIds(outStockOrderPOList.get(0).getOrgId(),
                        defaultDeliveryLocation_id, carInfo.getDefaultDeliveryLocation(),
                        outStockOrderPOS.stream().flatMap(o -> o.getItems().stream().map(OutStockOrderItemPO::getId))
                                .collect(Collectors.toList()));
            }
        }
    }

    /**
     * 通过创建波次请求，创建出库批次
     */
    protected void handleByDeliveryTask(BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO,
                                        List<OutStockOrderPO> outOrderList, ProcessBatchDTO processBatchDTO, WavesStrategyBO wavesStrategyDTO) {
        batchCreateDTOConvertor.handleDeliveryTask(batchCreateByRefOrderNoDTO, wavesStrategyDTO, processBatchDTO);

        List<DeliveryTaskDTO> deliveryTaskList = batchCreateByRefOrderNoDTO.getDeliveryTaskList();
        if (CollectionUtils.isEmpty(deliveryTaskList)) {
            return;
        }
        handleOpenOrderCenter(deliveryTaskList, outOrderList, processBatchDTO, wavesStrategyDTO);
        // handleNotOpenOrderCenter(deliveryTaskList, outOrderList, processBatchDTO, wavesStrategyDTO);
    }

    private void handleOpenOrderCenter(List<DeliveryTaskDTO> deliveryTaskList, List<OutStockOrderPO> outOrderList,
                                       ProcessBatchDTO processBatchDTO, WavesStrategyDTO wavesStrategyDTO) {
        if (CollectionUtils.isEmpty(deliveryTaskList)) {
            return;
        }

        Integer orgId = outOrderList.get(0).getOrgId();
        Integer warehouseId = outOrderList.get(0).getWarehouseId();

        List<SecondPickLocationDTO> batchLocationList = new ArrayList<>();
        for (DeliveryTaskDTO deliveryTask : deliveryTaskList) {
            if (CollectionUtils.isEmpty(deliveryTask.getOrderNoList())) {
                LOG.info("[多出库批次创建波次]无出库单号，出库批次：{}，出库单号：{}", deliveryTask.getDeliveryTaskNo(),
                        deliveryTask.getOrderNoList());
                continue;
            }
            List<OutStockOrderPO> curOrderList =
                    outOrderList.stream().filter(ord -> deliveryTask.getOrderNoList().contains(ord.getReforderno()))
                            .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(curOrderList)) {
                LOG.info("[多出库批次创建波次]无出库单，出库批次：{}，出库单号：{}", deliveryTask.getDeliveryTaskNo(),
                        deliveryTask.getOrderNoList());
                continue;
            }
            curOrderList.forEach(m -> {
                m.setPickUpUserName(NullUtils.getDefaultStr(deliveryTask.getDeliveryUserName()) + "-"
                        + NullUtils.getDefaultStr(deliveryTask.getDeliveryCarName()));
            });

            // 未设置出库位时
            if (curOrderList.stream().flatMap(ord -> ord.getItems().stream())
                    .anyMatch(item -> item.getLocationId() == null)) {
                CarDTO carInfo = iCarService.findOne(deliveryTask.getDeliveryCarId());
                LOG.info("[多出库批次创建波次]出库位为空，车辆信息：{}，车辆ID：{}", JSON.toJSONString(carInfo),
                        deliveryTask.getDeliveryCarId());
                if (carInfo != null) {
                    SecondPickLocationDTO secondPickLocationDTO = new SecondPickLocationDTO();
                    secondPickLocationDTO.setLocationId(carInfo.getDefaultDeliveryLocation_Id());
                    secondPickLocationDTO.setLocationName(carInfo.getDefaultDeliveryLocation());
                    secondPickLocationDTO.setRefOrderNoList(
                            curOrderList.stream().map(OutStockOrderPO::getReforderno).collect(Collectors.toList()));
                    batchLocationList.add(secondPickLocationDTO);
                }
            }
        }

        if (!CollectionUtils.isEmpty(batchLocationList)) {
            SecondPickLocationSyncDTO locationSyncDTO = new SecondPickLocationSyncDTO();
            locationSyncDTO.setOrgId(orgId);
            locationSyncDTO.setWarehouseId(warehouseId);
            locationSyncDTO.setLocationList(batchLocationList);
            LOG.info("[多出库批次创建波次]无初始出库位置，出库位再同步：{}", JSON.toJSONString(locationSyncDTO));
            secondSortBl.secondPickLocationSync(locationSyncDTO);
        }

    }

    protected List<OutStockOrderPO> filterOutStockOrder(List<OutStockOrderPO> outStockOrderPOList) {
        return createBatchValidateBL.filterOutStockOrder(outStockOrderPOList);
    }

    // 开启分仓的仓库，生成波次的时候，按仓拆分波次
    public void processCreateBatch(List<OutStockOrderPO> outStockOrderPOList, WavesStrategyBO wavesStrategyBO,
                                   ProcessBatchDTO processBatchDTO) {
//        if (BooleanUtils.isFalse(wavesStrategyBO.getOpenWarehouseSeparateAttributeConfig())) {
//            batchOrderProcessTransferBL.processCreateBatch(outStockOrderPOList, wavesStrategyBO, processBatchDTO);
//            return;
//        }

        Map<Byte, List<OutStockOrderPO>> orderGroupMap = splitBatchTaskBL.splitOrderByFeature(outStockOrderPOList);

        orderGroupMap.forEach((k, v) -> {
            WavesStrategyBO copyBO = new WavesStrategyBO();
            BeanUtils.copyProperties(wavesStrategyBO, copyBO);
            copyBO.setFeatureType(k);
            wavesStrategyBOConvertor.resetPackageOrderPickAlone(copyBO);
            batchOrderProcessTransferBL.processCreateBatch(v, copyBO, processBatchDTO);
        });

    }

    /**
     * 按客户创建波次检查
     *
     * @param createBatchBaseBO
     */
    public List<OutStockOrderDTO> checkCreateBatchByCustomer(CreateBatchBaseBO createBatchBaseBO) {
        // 检查配置挪到最前面，减少不必要的查询
        Boolean isPickByCustomer = globalCache.getPickByCustomerFromCache(createBatchBaseBO.getWarehouseId());
        if (!isPickByCustomer) {
            return Collections.emptyList();
        }

        validateParam(createBatchBaseBO);
        doPreCreateBatch(createBatchBaseBO);

        List<OutStockOrderPO> outStockOrderPOList = filterOutStockOrder(getOutStockOrder(createBatchBaseBO));

        // 2025-02-08 SCM-20405 合并拣货仅对销售订单生效，排除调拨单，分单时不要出现拦截界面。
        outStockOrderPOList.removeIf(p -> p.getAddressId() == null || p.getAddressId() <= 0);

        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            LOG.info("过滤后没有需要创建波次的订单，入参{}", JSON.toJSONString(createBatchBaseBO));
            return Collections.emptyList();
        }

        return validateIsPickByCustomer(createBatchBaseBO, outStockOrderPOList);
    }

    /**
     * 开启按客户拣货配置，需要检查提示adressId+订单特征需一起创建波次
     *
     * @param createBatchBaseBO
     */
    private List<OutStockOrderDTO> validateIsPickByCustomer(CreateBatchBaseBO createBatchBaseBO,
                                                            List<OutStockOrderPO> outStockOrderPOList) {
        LOG.info("按客户拣货相同地址和特征订单检查 入参：{}", JSON.toJSONString(outStockOrderPOList));
        // 按单拣货检查是否开启按客户拣货配置，相同adressId+订单特征需一起创建波次
        if (!Objects.equals(createBatchBaseBO.getBatchCreateDTO().getPickingType(), PickingTypeEnum.订单拣货.getType())) {
            return Collections.emptyList();
        }

        // 获取当前出库单的订单特征
        List<Long> orderIds = outStockOrderPOList.stream().filter(p -> p.getId() != null && p.getAddressId() != null)
                .map(p -> p.getId()).distinct().collect(Collectors.toList());
        List<Integer> addressIds =
                outStockOrderPOList.stream().filter(p -> p.getId() != null && p.getAddressId() != null)
                        .map(p -> p.getAddressId()).distinct().collect(Collectors.toList());
        OutStockOrderSearchSO searchSO = new OutStockOrderSearchSO();
        searchSO.setWareHouseId(createBatchBaseBO.getWarehouseId());
        searchSO.setOrderIds(orderIds);
        searchSO.setPackageAttribute(
                Arrays.asList(OrderFeatureConstant.FEATURE_TYPE_DRINKING, OrderFeatureConstant.FEATURE_TYPE_REST));
        List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.findByCondition(searchSO);
        LOG.info("按客户拣货相同地址和特征订单检查 酒饮及休食出库单查询结果：{}", JSON.toJSONString(outStockOrderPOS));
        Map<String,
                List<Long>> batchOrderIdMap = outStockOrderPOS.stream()
                .filter(p -> p != null && p.getPackageAttribute() != null && p.getAddressId() != null)
                .collect(Collectors.groupingBy(p -> String.format("%s-%s", p.getAddressId(), p.getPackageAttribute()),
                        Collectors.mapping(OutStockOrderPO::getId, Collectors.toList())));
        if (batchOrderIdMap == null || batchOrderIdMap.size() <= 0) {
            return Collections.emptyList();
        }

        List<Byte> packageAttributeList = outStockOrderPOList.stream().filter(p -> p.getPackageAttribute() != null)
                .map(p -> p.getPackageAttribute()).distinct().collect(Collectors.toList());
        OutStockOrderSearchSO so = new OutStockOrderSearchSO();
        so.setWareHouseId(createBatchBaseBO.getWarehouseId());
        // so.setOrderIds(orderIds);
        so.setAddressIdList(addressIds);
        so.setPackageAttribute(packageAttributeList);
        so.setOrderStates(Arrays.asList(OutStockOrderStateEnum.待调度.getType()));
        List<OutStockOrderPO> sameAddressAndFeatureOrders = outStockOrderMapper.findByCondition(so).stream()
                .filter(p -> p != null && !orderIds.contains(p.getId())).collect(Collectors.toList());
        LOG.info("按客户拣货相同地址和特征订单检查 其它出库单查询结果：{}", JSON.toJSONString(sameAddressAndFeatureOrders));
        Map<String,
                List<OutStockOrderPO>> otherOrderMap = sameAddressAndFeatureOrders.stream()
                .filter(p -> p != null && p.getPackageAttribute() != null && p.getAddressId() != null)
                .collect(Collectors.groupingBy(p -> String.format("%s-%s", p.getAddressId(), p.getPackageAttribute())));
        if (otherOrderMap == null || otherOrderMap.size() <= 0) {
            return Collections.emptyList();
        }

        List<OutStockOrderDTO> needCreateBatchOtherOrders = new ArrayList<>();
        batchOrderIdMap.forEach((orderKey, ids) -> {
            List<OutStockOrderPO> otherOrderList = otherOrderMap.get(orderKey);
            if (CollectionUtils.isEmpty(otherOrderList)) {
                return;
            }
            needCreateBatchOtherOrders
                    .addAll(otherOrderList.stream().map(OutStockOrderConverter::toDTO).collect(Collectors.toList()));
        });

        LOG.info("按客户拣货相同地址和特征订单检查结果：{}", JSON.toJSONString(needCreateBatchOtherOrders));
        return needCreateBatchOtherOrders;
    }
}
