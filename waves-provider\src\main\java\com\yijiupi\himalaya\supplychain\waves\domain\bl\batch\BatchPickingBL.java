package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch;

import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/21
 */
@Service
public class BatchPickingBL {

    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private GlobalCache globalCache;

    @Transactional(rollbackFor = Exception.class)
    public void beginPicking(String batchTaskId, Integer optUserId) {
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);
        if (Objects.isNull(batchTaskPO)) {
            return;
        }

        BatchPO batchPO =
            batchMapper.selectBatchByBatchNo(Integer.valueOf(batchTaskPO.getOrgId()), batchTaskPO.getBatchNo());

        if (Objects.isNull(batchPO)) {
            return;
        }

        if (BatchStateEnum.PENDINGPICKING.getType().byteValue() != batchPO.getState()) {
            return;
        }

        BatchPO updateBatchPO = new BatchPO();
        updateBatchPO.setId(batchPO.getId());
        updateBatchPO.setState(BatchStateEnum.PICKING.getType().byteValue());
        updateBatchPO.setLastUpdateUser(globalCache.getUserName(optUserId));

        batchMapper.updateBatchInfo(updateBatchPO);
    }

}
