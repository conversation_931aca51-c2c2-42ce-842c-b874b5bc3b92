package com.yijiupi.himalaya.supplychain.waves.service.impl;

/**
 * <AUTHOR> 2018/3/16
 */
// @RunWith(SpringRunner.class)
// @SpringBootTest(classes = WavesApp.class)
// public class BatchTaskQueryServiceImplTest {
//
// private static Gson gson = new Gson();
//
// @Autowired
// private BatchTaskQueryServiceImpl waveTaskQueryService;
// @Autowired
// private TestService testService;
// @Autowired
// private RedisTemplate<String, String> redisTemplate;
//
// // instant.clusterGetNodeForSlot(instant.clusterGetSlotForKey("111111".getBytes()));
// @Test
// public void slotTest() {
// redisTemplate.boundValueOps("10000000").set("123123123123123123");
// byte[] keys = "10000000".getBytes();
// RedisClusterConnection instant = redisTemplate.getConnectionFactory().getClusterConnection();
//
// int ss = JedisClusterCRC16.getCRC16("10000000");
// String luaScript1 = "return redis.call('info key', KEYS[1])";
// RedisScript<String> redisScript1 = new DefaultRedisScript<>(luaScript1, String.class);
// Object tttt1 = redisTemplate.execute(new RedisCallback<Object>() {
// @Override
// public Object doInRedis(RedisConnection redisConnection) throws DataAccessException {
// return ((JedisCluster)redisConnection.getNativeConnection()).eval(redisScript1.getScriptAsString(),
// Collections.singletonList("10000000"), Collections.singletonList("11111"));
// }
// });
//
// // String luaScript = "redis.call('CLUSTER KEYSLOT', KEYS[1])";
// String luaScript = "return redis.call('CLUSTER KEYSLOT', KEYS[1])";
// RedisScript<String> redisScript = new DefaultRedisScript<>(luaScript, String.class);
// Object tttt = redisTemplate.execute(new RedisCallback<Object>() {
// @Override
// public Object doInRedis(RedisConnection redisConnection) throws DataAccessException {
// return ((JedisCluster)redisConnection.getNativeConnection()).eval(redisScript.getScriptAsString(),
// Collections.singletonList("100000"), Collections.singletonList("111"));
// }
// });
//
// int result = redisTemplate.getConnectionFactory().getClusterConnection().clusterGetSlotForKey(keys);
// List<byte[]> resultList =
// redisTemplate.getConnectionFactory().getClusterConnection().clusterGetKeysInSlot(result, 10);
// List<String> resultStrList = resultList.stream().map(String::valueOf).collect(Collectors.toList());
// Assertions.assertThat(resultStrList).isNotNull();
// // redisTemplate.execute(new RedisCallback<Object>() {
// // @Override
// // public Object doInRedis(RedisConnection connection) throws DataAccessException {
// // Object nativeConnection = connection.getNativeConnection();
// // if (nativeConnection instanceof JedisCluster) {
// // byte[] result = ((JedisCluster)connection).eval("CLUSTER".getBytes(), "SLOTS".getBytes());
// // // 解析返回的字节数组，转换为对象列表
// // return (List<Object>)connection.getConvert().convertResults(new ByteArrayDeserializer(), result);
// // } else if (nativeConnection instanceof Jedis) {
// // String[] tmpParams = new String[params.length + 1];
// // tmpParams[0] = key;
// // for (int i = 0; i < tmpParams.length; i++) {
// // tmpParams[i + 1] = params[i];
// // }
// // return (Long)((Jedis)nativeConnection).eval(redisLockScript.getScriptAsString(), DEFAULT_KEY_NUMS,
// // tmpParams);
// // }
// // return -1L;
// // }
// // });
// }
// // String luaScript = "return redis.call('CLUSTER', 'NODES')";
//
// @Test
// public void test() throws Exception {
// CountDownLatch latch = new CountDownLatch(1);
//
// Thread[] threads = new Thread[10];
//
// for (int i = 0; i < threads.length; i++) {
// threads[i] = new Thread() {
// @Override
// public void run() {
// try {
// latch.await();
// } catch (Exception e) {
// System.out.println(e.getMessage());
// }
// // waveTaskQueryService.updateBatchTaskItem(null, "123", null, null, null, null, null, null, null);
// List<String> orderNoList = new ArrayList<>();
// orderNoList.add("111111");
// orderNoList.add("222222");
// orderNoList.add("333333");
// orderNoList.add("444444");
// orderNoList.add("555555");
// // testService.test(orderNoList);
// }
// };
// }
// for (int i = 0; i < threads.length; i++) {
// threads[i].start();
// }
//
// latch.countDown();
//
// CountDownLatch latch2 = new CountDownLatch(1);
// latch2.await();
//
// }
//
// /**
// * 查找波次任务列表
// *
// * @throws Exception
// */
// @Test
// public void findBatchOrderTaskList() throws Exception {
// BatchTaskQueryDTO batchTaskQueryDTO = new BatchTaskQueryDTO();
// // batchTaskQueryDTO.setBatchTaskNo("11");
// // batchTaskQueryDTO.setSorterId(1);
// // batchTaskQueryDTO.setStartTime(new Date());
// batchTaskQueryDTO.setBatchNo("BC9991418042400004");
// batchTaskQueryDTO.setCurrentPage(1);
// batchTaskQueryDTO.setPageSize(100);
// batchTaskQueryDTO.setWarehouseId(9991);
// PageList<BatchTaskDTO> batchOrderTaskList = waveTaskQueryService.findBatchTaskList(batchTaskQueryDTO);
// }
//
// /**
// * 根据不同拣货策略查找波次任务列表
// */
// @Test
// public void findBatchTaskSortList() {
// BatchTaskSortQueryDTO batchTaskSortQueryDTO = new BatchTaskSortQueryDTO();
// batchTaskSortQueryDTO.setType(3);
// batchTaskSortQueryDTO.setUserId(284);
// batchTaskSortQueryDTO.setCityId(999);
// batchTaskSortQueryDTO.setTaskState(Arrays.asList(TaskStateEnum.已完成.getType()));
// PageList<BatchTaskSortDTO> batchTaskSortList1 =
// waveTaskQueryService.findBatchTaskSortList(batchTaskSortQueryDTO);
// System.out.println(JSON.toJSONString(batchTaskSortList1));
// }
//
// /**
// * 查找 根据订单拣货的波次任务详情
// */
// @Test
// public void findBatchTaskSortItemByOrder() {
// System.out.println(gson.toJson(waveTaskQueryService.findBatchTaskSortItemByOrder("2018052400311", 1, 100)));
// }
//
// /**
// * 查找 根据产品拣货的波次任务详情
// */
// @Test
// public void findBatchTaskSortItemByProduct() {
// System.out.println(gson.toJson(waveTaskQueryService.findBatchTaskSortItemByProduct("1", 1, 100)));
// }
//
// @Test
// public void beginBatchTask() {
// waveTaskQueryService.beginBatchTask("123", null, 1);
// }
//
// @Test
// public void updateBatchTaskItem() {
// List<BatchTaskItemUpdateDTO> batchTaskItemUpdateDTOList = new ArrayList<>();
// BatchTaskItemUpdateDTO batchTaskItemUpdateDTO = new BatchTaskItemUpdateDTO();
// batchTaskItemUpdateDTO.setId("1");
// batchTaskItemUpdateDTO.setOverSortPackageCount(new BigDecimal(2));
// batchTaskItemUpdateDTO.setOverSortUnitCount(new BigDecimal(0));
// batchTaskItemUpdateDTO.setLackUnitCount(new BigDecimal(0));
// batchTaskItemUpdateDTOList.add(batchTaskItemUpdateDTO);
// // waveTaskQueryService.updateBatchTaskItem(batchTaskItemUpdateDTOList, "1", "aa");
// }
//
// @Test
// public void findBatchTaskItemList() throws Exception {
// BatchTaskItemQueryDTO batchTaskItemQueryDTO = new BatchTaskItemQueryDTO();
// batchTaskItemQueryDTO.setCityId(997);
// batchTaskItemQueryDTO.setPageSize(20);
// batchTaskItemQueryDTO.setCurrentPage(1);
// PageList<BatchTaskItemDTO> batchTaskItemList =
// waveTaskQueryService.findBatchTaskItemList(batchTaskItemQueryDTO);
// }
//
// @Test
// public void recordPrint() throws Exception {
// waveTaskQueryService.recordPrint(Collections.singletonList("TS1804041003298"), "ls");
// }
//
// @Test
// public void findBatchTaskItemMap() throws Exception {
// Map<String, List<BatchTaskItemDTO>> batchTaskItemMap =
// waveTaskQueryService.findBatchTaskItemMap(Collections.singletonList("TS1804041003298"));
// }
// }