package com.yijiupi.himalaya.supplychain.waves.util;

import java.util.Objects;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;

public class ConstantUtil {

    /**
     * 内配中转判断
     */
    public static boolean InternalDeliveryJudge(Byte allotType) {
        return Objects.equals(OrderConstant.ALLOT_TYPE_DELIVERY, allotType)
            || Objects.equals(OrderConstant.ALLOT_TYPE_ALLOCATION, allotType);
    }

    /**
     * 字符串超长截取
     * 
     * @return
     */
    public static String getMaxLen(String str, Integer len) {
        if (StringUtils.isNotEmpty(str) && len != null && len > 0 && str.length() > len) {
            str = str.substring(0, len);
        }
        return str;
    }

    /**
     * 字符串按指定符号截取并保证长度
     *
     * @return
     */
    public static String splitStringBySymbol(String inputString, String symbol, int maxLength) {
        String[] words = inputString.split(symbol);
        StringBuilder current = new StringBuilder();
        for (String word : words) {
            if (current.length() + word.length() + 1 > maxLength) {
                break;
            }

            if (current.length() > 0) {
                current.append(symbol).append(word);
            } else {
                current.append(word);
            }
        }

        return current != null && current.length() > 0 ? current.toString() : "";
    }
}
