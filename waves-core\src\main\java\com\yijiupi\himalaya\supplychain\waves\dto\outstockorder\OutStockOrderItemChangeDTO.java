package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单变更明细
 *
 * <AUTHOR>
 * @date 2020-06-03 17:43
 */
public class OutStockOrderItemChangeDTO implements Serializable {

    private static final long serialVersionUID = -4719889829677033845L;

    /**
     * 订单项id
     */
    private Long id;

    /**
     * 产品skuId
     */
    private Long skuId;

    /**
     * 变更小单位总数量（变少是负数，变多则为正数）
     */
    private BigDecimal changeCount;

    /**
     * 播种任务ID
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 集货位id
     */
    private Long sowLocationId;

    /**
     * 集货位名称
     */
    private String sowLocationName;

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Long getSowLocationId() {
        return sowLocationId;
    }

    public void setSowLocationId(Long sowLocationId) {
        this.sowLocationId = sowLocationId;
    }

    public String getSowLocationName() {
        return sowLocationName;
    }

    public void setSowLocationName(String sowLocationName) {
        this.sowLocationName = sowLocationName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getChangeCount() {
        return changeCount;
    }

    public void setChangeCount(BigDecimal changeCount) {
        this.changeCount = changeCount;
    }
}
