package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskProductPO;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;

/**
 * <AUTHOR>
 * @Title: himalaya-supplychain-microservice-waves
 * @Package com.yijiupi.himalaya.supplychain.waves.domain.convertor
 * @Description:
 * @date 2018/3/26 14:14
 */
public class GatherTaskConvertor {

    public static GatherTaskPO convertToGatherTaskPO(BatchTaskPO batchTaskPO, Long userId) {
        GatherTaskPO po = new GatherTaskPO();
        po.setBatchTaskId(batchTaskPO.getId());
        po.setBatchtaskNo(batchTaskPO.getBatchTaskNo());
        po.setOrgId(Integer.valueOf(batchTaskPO.getOrgId()));
        po.setTaskName(batchTaskPO.getBatchTaskName());
        po.setWarehouseId(batchTaskPO.getWarehouseId());
        po.setCreateUser(userId);
        po.setLastUpdateUser(userId);
        po.setId(UuidUtil.getUUidInt());
        return po;
    }

    public static List<GatherTaskProductPO> convertToGatherTaskProductPOList(List<BatchTaskItemPO> batchTaskItemPOList,
        Long gatherTaskId, Long userId) {
        List<GatherTaskProductPO> productPOList = batchTaskItemPOList.stream().map(input -> {
            GatherTaskProductPO po = new GatherTaskProductPO();
            po.setId(UuidUtil.getUUidInt());
            po.setBatchTaskItemId(Long.valueOf(input.getId()));
            po.setGatherTaskId(gatherTaskId);
            po.setOrgId(input.getOrgId());
            po.setProductSkuId(input.getSkuId());
            po.setProductName(input.getProductName());
            po.setSpecificationId(input.getProductSpecificationId());
            po.setProductSpecName(input.getSpecName());
            po.setSpecQuantity(input.getSpecQuantity());
            po.setTotalCount(input.getUnitTotalCount());
            po.setTakeCount(BigDecimal.ZERO);
            po.setCreateUser(userId);
            po.setLastUpdateUser(userId);
            return po;
        }).collect(Collectors.toList());

        return productPOList;
    }

    /**
     * Boolean转化成对应的Byte
     * 
     * @param bool
     * @return
     */
    public static Boolean convertToBoolean(Byte b) {
        if (null == b) {
            return null;
        }
        return b == 1 ? true : false;
    }
}
