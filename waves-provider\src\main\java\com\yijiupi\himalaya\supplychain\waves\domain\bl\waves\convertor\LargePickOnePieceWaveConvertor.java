package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor;

import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.util.OrderRebuildUtil;
import com.yijiupi.himalaya.supplychain.waves.util.RobotPickConstants;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: LargePickOnePieceWaveConvertor
 * @description:
 * @date 2023-02-08 17:42
 */
public class LargePickOnePieceWaveConvertor {

    /**
     *
     * @param orders 原始订单
     * @param lstOrderItems 所有的订单项
     * @param lstCreateDTO 拣货任务列表
     * @return
     */
    public static List<OutStockOrderPO> createLargePickWave(List<OutStockOrderPO> orders, List<OutStockOrderItemPO> lstOrderItems, List<WaveCreateDTO> lstCreateDTO,
                                                   WavesStrategyDTO wavesStrategyDTO, WarehouseConfigDTO warehouseConfigDTO) {
        if (BooleanUtils.isFalse(RobotPickConstants.isWarehouseOpenLargePick(wavesStrategyDTO, warehouseConfigDTO))) {
            return Collections.emptyList();
        }

        List<OutStockOrderItemPO> lstTmpItems = new ArrayList<>();
        lstCreateDTO.forEach(p -> {
            p.getOrders().forEach(order -> {
                lstTmpItems.addAll(order.getItems());
            });
        });

        List<OutStockOrderItemPO> lstOtherItems = lstOrderItems.stream()
                // 整件拆零的情况下把数量条件也加上
                .filter(p -> lstTmpItems.stream().noneMatch(q -> q.getId().equals(p.getId()) && Objects.equals(q.getLocationId(), p.getLocationId()) && Objects.equals(q.getUnittotalcount(), p.getUnittotalcount())))
                .filter(p -> p.getPackagecount().compareTo(BigDecimal.ZERO) > 0)
                .filter(p -> p.getUnitcount().compareTo(BigDecimal.ZERO) == 0)
                .filter(p -> Objects.nonNull(p.getSubCategory()) && LocationEnum.存储位.getType().byteValue() == p.getSubCategory())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(lstOtherItems)) {
            return Collections.emptyList();
        }

        return OrderRebuildUtil.getOrdersByPassageItems(orders, lstOtherItems);
    }


}
