package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;

/**
 * 生成波次选择产品
 *
 * <AUTHOR>
 * @date 2019/4/16 18:09
 */
public class BatchCreateChooseProductDTO implements Serializable {

    private static final long serialVersionUID = -604525850314425396L;

    /**
     * 产品skuId
     */
    private Long productSkuId;

    /**
     * 产品规格id
     */
    private Long productSpecId;

    /**
     * onwerId
     */
    private Long ownerId;

    /**
     * 是否拣货 0:否 1:是
     */
    private Byte pick;

    /**
     * 是否播种 0:否 1:是
     */
    private Byte sow;

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Byte getPick() {
        return pick;
    }

    public void setPick(Byte pick) {
        this.pick = pick;
    }

    public Byte getSow() {
        return sow;
    }

    public void setSow(Byte sow) {
        this.sow = sow;
    }
}
