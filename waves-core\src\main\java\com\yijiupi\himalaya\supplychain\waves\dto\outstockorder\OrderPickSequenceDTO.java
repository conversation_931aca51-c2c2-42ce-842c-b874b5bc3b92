package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/5/16
 */
public class OrderPickSequenceDTO implements Serializable {
    private static final long serialVersionUID = 2156264080352736972L;
    /**
     * 出库单Id
     */
    private Long orderId;
    /**
     * 出库单项ID
     */
    private Long orderItemId;
    /**
     * 分拣顺序
     */
    private Integer pickSequence;

    public Integer getPickSequence() {
        return pickSequence;
    }

    public void setPickSequence(Integer pickSequence) {
        this.pickSequence = pickSequence;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }
}
