package com.yijiupi.himalaya.supplychain.waves.batch;

import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderItemDetailAllotDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderSaveDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderUpdateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.*;
import com.yijiupi.himalaya.supplychain.waves.dto.trace.OrderTraceDTO;

public interface ISowManageService {

    /**
     * 保存播种任务订单关联信息
     */
    @Deprecated
    void saveSowOrder(SowOrderSaveDTO sowOrderSaveDTO);

    /**
     * 播种任务领取 根据时间排序筛选出第一个拣货任务完成的播种任务
     */
    SowTaskDTO receiveSowTaskByClient(SowTaskReceiveDTO taskReceiveDTO);

    /**
     * 修改播种任务信息(通过播种任务id)
     */
    void updateSowTask(SowTaskDTO sowTaskDTO);

    /**
     * 为播种任务设置集货区
     */
    Long setSowLocation(String batchTaskId);

    /**
     * 通过SCOP提交播种任务
     */
    void saveSowOrderBySCOP(SowOrderSaveDTO sowOrderSaveDTO);

    /**
     * 开始播种任务(知花知果)
     */
    @Deprecated
    void startSowing(String sowTaskNo, Integer orgId, String sower);

    /**
     * 指定播种人
     */
    @Deprecated
    void assignmentSower(UpdateSowerDTO updateSowerDTO);

    /**
     * 批量删除播种任务
     */
    void delSowTask(List<String> sowTaskNos);

    /**
     * 修改播种任务集货位
     */
    void updateSowLocation(SowTaskDTO sowTaskDTO);

    /**
     * 根据订单取消播种任务
     * 
     * @param sowTaskCancelDTOS
     */
    void cancelSowTaskByOrder(List<SowTaskCancelDTO> sowTaskCancelDTOS);

    /**
     * 播种任务完成
     */
    void completeSowTaskItems(SowOrderSaveDTO sowOrderSaveDTO);

    /**
     * 修改播种任务中的订单出库位
     */
    void updateSowOrderLocation(SowOrderUpdateDTO sowOrderUpdateDTO);

    /**
     * 播种任务领取
     */
    void receiveSowTask(SowTaskReceiveDTO sowTaskReceiveDTO);

    /**
     * 自提点播种任务完成
     */
    List<Long> completeAddressSowTaskItems(SowOrderSaveDTO sowOrderSaveDTO);

    List<SowTaskDTO> listSowTaskByBatchNos(List<String> batchNos);

    /**
     * 播种任务缺货
     */
    void sowTaskLack(SowTaskProductLackReviewDTO lackReviewDTO);

    /**
     * 播种任务完成PDA
     */
    void completeSowTaskItemsPDA(SowOrderSaveDTO sowOrderSaveDTO);

    /**
     * 完成产品聚合的播种任务项
     */
    void completeSowTaskByProduct(ProductSowTaskDTO completeDTO);

    /**
     * 查询播种任务是否有播种复核
     * 
     * @param traceDTOList
     * @return
     */
    List<CheckSowReviewDTO> checkSowReview(List<OrderTraceDTO> traceDTOList);

    /**
     * 根据播种任务id 查询播种明细
     * 
     * @param orgId
     * @param taskIds
     * @return
     */
    List<SowTaskItemDTO> listSowTaskItemBySowTaskIds(Integer orgId, List<Long> taskIds);

    /**
     * 领取播种任务
     * 
     * @param sowTaskReceiveDTO
     * @return
     */
    SowTaskDTO getSowTask(SowTaskReceiveDTO sowTaskReceiveDTO);

    /**
     * 修改播种任务中的订单出库位
     */
    void updateSowOrderLocationBySowTaskItemIds(SowOrderUpdateDTO sowOrderUpdateDTO);

    /**
     * 查询播种任务线路司机
     * 
     * @param sowTaskDTO
     * @return
     */
    SowTaskRouteAndDriverDTO getRouteAndDriver(SowTaskDTO sowTaskDTO);

    /**
     * 修复sowOrder上的SowOrderSequence
     * 
     * @param sowTaskDTO
     */
    void fixSowOrderSequence(FixSowOrderSequenceDTO dto);

    void updateSowTaskState(SowOrderSaveDTO sowOrderSaveDTO);

    /**
     * 
     * @param orderItemIds
     * @param warehouseId
     * @return
     */
    Map<Long, List<OrderItemDetailAllotDTO>> getOrderItemDetailAllotMap(List<Long> orderItemIds, Integer warehouseId);

}
