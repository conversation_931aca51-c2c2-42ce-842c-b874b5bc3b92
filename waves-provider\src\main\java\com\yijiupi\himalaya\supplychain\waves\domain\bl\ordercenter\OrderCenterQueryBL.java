package com.yijiupi.himalaya.supplychain.waves.domain.bl.ordercenter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.gson.reflect.TypeToken;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.OrderApiResult;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.OrderCommonDetailDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.secowner.OrderWithItemOwnersDTO;
import com.yijiupi.himalaya.supplychain.waves.util.ServiceAbilityClientUtil;

/**
 * <AUTHOR>
 * @since 2023-11-02 15:42
 **/
@Service
public class OrderCenterQueryBL {

    @Resource
    private ServiceAbilityClientUtil serviceAbilityClientUtil;
    @Value("${ordercenter.sdk.timeout}")
    private Integer timeout;
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderCenterQueryBL.class);
    private static final int TIMEOUT = 30000;
    private static final String GET_BY_ORDER_ID = "/aggregatequery/OrderCommonQueryService/getOrderById";
    private static final String FIND_BY_STATUS_LIST = " /aggregatequery/OrderStatusConfigService/findByStatusList";
    private static final String FIND_SEC_OWNER_INFO = "/aggregatequery/OrderQueryService/getOrderWithItemOwners";

    /**
     * 根据订单 id 查询 oms 订单
     */
    public OrderCommonDetailDTO getByOrderId(Long orderId) {
        LOGGER.info("根据订单 id 查询 oms 订单, 入参: {}", orderId);
        if (null == orderId) {
            return null;
        }
        OrderApiResult<OrderCommonDetailDTO> orderDetailResult = serviceAbilityClientUtil.getInstance().invoke(
            GET_BY_ORDER_ID, TIMEOUT, new TypeToken<OrderApiResult<OrderCommonDetailDTO>>() {}.getType(), "", orderId);
        if (orderDetailResult.getCode() != 200) {
            LOGGER.warn("调取订单中台分页查询接口报错{}", orderDetailResult.getMsg());
        }
        LOGGER.info("根据订单 id 查询 oms 订单, 查询结果: {}", JSON.toJSONString(orderDetailResult.getData()));
        return orderDetailResult.getData();
    }

    /**
     * 根据订单状态 code 查询订单状态含义
     *
     * @param statusList 订单状态 code
     * @return 订单状态含义
     */
    // public OrderStatusConfig findByStatusList(List<Integer> statusList) {
    // LOGGER.info("根据订单状态 code 查询订单状态含义入参: {}", statusList);
    // if (CollectionUtils.isEmpty(statusList)) {
    // return null;
    // }
    // Map<String, List<Integer>> param = new HashMap<>();
    // param.put("statusValueList", statusList);
    // OrderApiResult<OrderStatusConfig> orderDetailResult = serviceAbilityClientUtil.getInstance().invoke(
    // FIND_BY_STATUS_LIST, TIMEOUT, new TypeToken<OrderApiResult<OrderStatusConfig>>() {}.getType(), "", param);
    // if (orderDetailResult.getCode() != 200) {
    // LOGGER.warn("调取订单中台查询接口报错: {}", orderDetailResult.getMsg());
    // }
    // OrderStatusConfig result = orderDetailResult.getData();
    // LOGGER.info("根据订单状态 id 查询订单状态含义结果: {}", JSON.toJSONString(result));
    // return result;
    // }

    /**
     * 根据订单 id 查询 oms 订单
     */
    public List<OrderWithItemOwnersDTO> getSecOwnerByOrderIds(List<Long> orderIds) {
        LOGGER.info("根据订单 id 查询 oms 订单, 入参: {}", JSON.toJSONString(orderIds));
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        try {
            OrderApiResult<List<OrderWithItemOwnersDTO>> orderDetailResult =
                serviceAbilityClientUtil.getInstance().invoke(FIND_SEC_OWNER_INFO, TIMEOUT,
                    new TypeToken<OrderApiResult<List<OrderWithItemOwnersDTO>>>() {}.getType(), orderIds);
            if (orderDetailResult.getCode() != 200) {
                LOGGER.warn("调取订单中台分页查询接口报错{}", orderDetailResult.getMsg());
            }
            LOGGER.info("根据订单 id 查询 oms 订单, 查询结果: {}", JSON.toJSONString(orderDetailResult.getData()));
            return orderDetailResult.getData();
        } catch (Exception e) {
            LOGGER.warn("根据订单 id 查询 oms 订单失败, 入参: " + JSON.toJSONString(orderIds), e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据订单ids批量查询中台订单
     */
    public List<OrderCommonDetailDTO> getByOrderIds(List<Long> orderIds) {
        LOGGER.info("根据订单ids批量查询中台订单-getByOrderIds，入参:{}", orderIds);
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        List<OrderCommonDetailDTO> orderCommonDetailDTOList = new ArrayList<>();
        try {
            Lists.partition(orderIds, 20).forEach(list -> {
                OrderApiResult<List<OrderCommonDetailDTO>> orderDetailResult = serviceAbilityClientUtil.getInstance()
                        .invoke("/aggregatequery/OrderCommonQueryService/findOrderByIdList", timeout,
                                new TypeToken<OrderApiResult<List<OrderCommonDetailDTO>>>() {}.getType(), list);
                if (orderDetailResult.getCode() != 200) {
                    LOGGER.warn("调取订单中台分页查询接口报错{}", orderDetailResult.getMsg());
                }
                LOGGER.info("根据订单ids批量查询中台订单-getByOrderIds，查询结果:{}", JSON.toJSONString(orderDetailResult.getData()));
                if (orderDetailResult.getData() != null) {
                    orderCommonDetailDTOList.addAll(orderDetailResult.getData());
                }
            });
        } catch (Exception ex) {
            LOGGER.error("[根据订单ids批量查询中台订单]异常，订单ID：{}", orderIds, ex);
        }
        return orderCommonDetailDTOList;
    }

}
