package com.yijiupi.himalaya.supplychain.waves.domain.bl.ordercenter;

import static com.yijiupi.himalaya.supplychain.ordercenter.consant.SyncTraceTypeConstants.BATCH_DELETE;
import static com.yijiupi.himalaya.supplychain.ordercenter.consant.SyncTraceTypeConstants.BATCH_DELETE_CONFIRM;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.ordercenter.consant.SyncTraceMapKeyConstants;
import com.yijiupi.himalaya.supplychain.ordercenter.consant.SyncTraceTypeConstants;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.SyncTraceDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.service.OrderCenterService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter.CompletePickNotifyDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;

/**
 * <AUTHOR>
 * @since 2023-10-30 09:53
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class OrderCenterNotifyBL {

    @Reference
    private OrderCenterService orderCenterService;
    @Resource
    private OutStockOrderMapper outStockOrderMapper;
    @Resource
    private GlobalCache globalCache;
    @Resource
    private BatchTaskItemMapper batchTaskItemMapper;
    @Resource
    private BatchTaskMapper batchTaskMapper;

    private static final Logger logger = LoggerFactory.getLogger(OrderCenterNotifyBL.class);

    /**
     * 同步删除波次 trace
     *
     * @param batchNos 波次变化
     * @param optUserId 操作人
     * @param warehouseId 仓库 id
     * @param orgId 城市 id
     */
    @Async("waveTaskExecutor")
    public void syncBatchDeleteTrace(List<String> batchNos, Integer optUserId, Integer warehouseId, Integer orgId) {
        List<OutStockOrderPO> outstockOrderList = outStockOrderMapper.findByBatchNos(batchNos, orgId);
        Map<String, List<OutStockOrderPO>> batchOrderMap =
            outstockOrderList.stream().collect(Collectors.groupingBy(OutStockOrderPO::getBatchno));
        logger.info("同步删除波次 trace, batchNos: {}, optUserId: {}, warehouseId: {}, orgId: {}, orderNos: {}", batchNos,
            optUserId, warehouseId, orgId, batchOrderMap);
        Map<String, Set<String>> batchTaskIdMap = batchTaskMapper.findTaskByBatchNo(batchNos).stream().collect(
            Collectors.groupingBy(BatchTaskPO::getBatchNo, Collectors.mapping(BatchTaskPO::getId, Collectors.toSet())));
        Set<String> taskIds = batchTaskIdMap.values().stream().flatMap(Collection::stream).filter(Objects::nonNull)
            .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(taskIds)) {
            logger.info("拣货任务信息不存在！");
            return;
        }
        // 波次里有一个拣货任务项已完成就走新 trace
        Map<String,
            List<BatchTaskItemPO>> taskMap = batchTaskItemMapper.findBatchTaskItemDTOListByTaskIds(taskIds).stream()
                .peek(it -> fillBatchNo(it, batchTaskIdMap))
                .collect(Collectors.groupingBy(BatchTaskItemPO::getBatchNo));
        for (Map.Entry<String, List<BatchTaskItemPO>> entry : taskMap.entrySet()) {
            String batchNo = entry.getKey();
            List<Long> orderIds = batchOrderMap.getOrDefault(batchNo, Collections.emptyList()).stream()
                .map(OutStockOrderPO::getBusinessId).filter(NumberUtils::isDigits).map(Long::valueOf)
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderIds)) {
                logger.warn("波次: {} 没有关联的订单", batchNo);
                continue;
            }
            if (entry.getValue().stream().anyMatch(it -> TaskStateEnum.已完成.valueEquals(it.getTaskState()))) {
                syncTrace(BATCH_DELETE_CONFIRM, globalCache.getUserName(optUserId), batchNo, orderIds);
            } else {
                syncTrace(BATCH_DELETE, globalCache.getUserName(optUserId), batchNo, orderIds);
            }
        }
    }

    /**
     * 出库单状态变更 trace 同步
     *
     * @see SyncTraceTypeConstants#OUT_STOCK_ORDER_STATE_CHANGE
     */
    @Async("waveTaskExecutor")
    public void syncOutStockOrderStateChangeTrace(List<Long> outStockOrderIds, Byte state) {
        if (CollectionUtils.isEmpty(outStockOrderIds)) {
            logger.info("同步 trace 出库单 id 为空");
            return;
        }
        List<Long> orderIds = outStockOrderMapper.listOutStockOrderByOrderId(outStockOrderIds).stream()
            .map(OutStockOrderPO::getBusinessId).filter(StringUtils::isNumeric).distinct().map(Long::valueOf)
            .collect(Collectors.toList());
        String stateName = OutStockOrderStateEnum.getEnum(state).map(Enum::name).orElse(String.valueOf(state));
        syncTrace(SyncTraceTypeConstants.OUT_STOCK_ORDER_STATE_CHANGE, null, stateName, orderIds);
    }

    /**
     * 同步取消包装 trace
     *
     * @param refOrderNos 订单号
     * @param warehouseId 仓库 id
     * @param opUserId 操作人 id
     */
    @Async("waveTaskExecutor")
    public void syncRemovePackageTrace(Collection<String> refOrderNos, Integer warehouseId, Integer opUserId) {
        List<Long> orderIds =
            outStockOrderMapper.findIdByOrderNos(refOrderNos, warehouseId).stream().map(OutStockOrderPO::getBusinessId)
                .filter(NumberUtils::isDigits).map(Long::valueOf).collect(Collectors.toList());
        if (orderIds.isEmpty()) {
            logger.warn("订单 id 为空: {}", JSON.toJSONString(refOrderNos));
            return;
        }
        syncTrace(SyncTraceTypeConstants.PACKAGE_CANCEL, globalCache.getUserName(opUserId), null, orderIds);
    }

    @Async("waveTaskExecutor")
    public void completePickNotifyTrace(List<CompletePickNotifyDTO> pickNotifyList) {
        if (CollectionUtils.isEmpty(pickNotifyList)) {
            return;
        }
        // 不同订单，属于不同拣货任务，拣货员不一致
        for (CompletePickNotifyDTO pickNotifyDTO : pickNotifyList) {
            String userName = pickNotifyDTO.getStevedoreUserName();
            if (CollectionUtils.isEmpty(pickNotifyDTO.getPickUserNameList())) {
                logger.warn("拣货员名称为空！{}", JSON.toJSONString(pickNotifyDTO));
            } else {
                userName = String.join(",", pickNotifyDTO.getPickUserNameList());
            }
            List<Long> orderIds = Collections.singletonList(pickNotifyDTO.getOrderId());
            String warehouseId = String.valueOf(pickNotifyDTO.getWarehouseId());
            String warehouseName = getWarehouseName(pickNotifyDTO.getWarehouseId());
            syncTrace(SyncTraceTypeConstants.COMPLETE_ORDER_PICK_V2, userName, null, orderIds, warehouseId,
                warehouseName);
        }
    }

    // 清理出库单上波次信息
    @Async("waveTaskExecutor")
    public void clearOutStockOrderBatchInfoTrace(OutStockOrderPO outStockOrderPO) {
        if (Objects.isNull(outStockOrderPO)) {
            return;
        }
        Integer optUserId = 1;
        SyncTraceDTO syncTraceDTO = new SyncTraceDTO();
        syncTraceDTO.setTraceType(SyncTraceTypeConstants.CLEAR_BATCH_INTO_STOCK_ORDER);
        Map<String, String> traceParamMap = new HashMap<>(16);
        traceParamMap.put(SyncTraceMapKeyConstants.OP_USER_ID, globalCache.getAdminTrueName(optUserId));
        traceParamMap.put(SyncTraceMapKeyConstants.ORDER_NO, outStockOrderPO.getBusinessNo());
        traceParamMap.put(SyncTraceMapKeyConstants.BATCH_NO, outStockOrderPO.getBatchno());

        syncTraceDTO.setTraceParamMap(traceParamMap);
        orderCenterService.syncTrace(syncTraceDTO,
            Collections.singletonList(Long.valueOf(outStockOrderPO.getBusinessId())));
    }

    private void syncTrace(int type, @Nullable String userName, @Nullable String orderNo, @Nonnull List<Long> ids) {
        syncTrace(type, userName, orderNo, ids, null);
    }

    /**
     * 通用 trace 同步方法
     *
     * @param type trace 类型
     * @param userName 操作人 {@link SyncTraceMapKeyConstants#OP_USER_ID}, 可为 null
     * @param orderNo 订单号 {@link SyncTraceMapKeyConstants#ORDER_NO}, 可为 null
     * @param warehouseName 仓库名称 {@link SyncTraceMapKeyConstants#WAREHOUSE_NAME}, 可以为 null
     * @param ids 订单 id, 不可为 null
     */
    private void syncTrace(int type, @Nullable String userName, @Nullable String orderNo, @Nonnull List<Long> ids,
        @Nullable String warehouseName) {
        Map<String, String> traceParamMap = new HashMap<>();
        Optional.ofNullable(userName).ifPresent(it -> traceParamMap.put(SyncTraceMapKeyConstants.OP_USER_ID, it));
        Optional.ofNullable(orderNo).ifPresent(it -> traceParamMap.put(SyncTraceMapKeyConstants.ORDER_NO, it));
        Optional.ofNullable(warehouseName)
            .ifPresent(it -> traceParamMap.put(SyncTraceMapKeyConstants.WAREHOUSE_NAME, it));
        SyncTraceDTO syncTraceDTO = newTrace(type, traceParamMap);
        orderCenterService.syncTrace(syncTraceDTO, ids);
    }

    /**
     * 根据 traceType 和 traceParamMap 构造一个新的 syncTraceDTO
     *
     * @param traceType trace 类型
     * @param traceParamMap trace 参数
     * @return 新的 syncTraceDTO
     */
    @Nonnull
    private SyncTraceDTO newTrace(int traceType, Map<String, String> traceParamMap) {
        SyncTraceDTO syncTraceDTO = new SyncTraceDTO();
        syncTraceDTO.setTraceType(traceType);
        syncTraceDTO.setTraceParamMap(traceParamMap);
        return syncTraceDTO;
    }

    private String getWarehouseName(Integer warehouseId) {
        return Optional.ofNullable(globalCache.getWarehouse(warehouseId)).map(Warehouse::getName).orElse(null);
    }

    private void fillBatchNo(BatchTaskItemPO it, Map<String, Set<String>> batchTaskIdMap) {
        for (Map.Entry<String, Set<String>> entry : batchTaskIdMap.entrySet()) {
            if (entry.getValue().contains(it.getBatchTaskId())) {
                it.setBatchNo(entry.getKey());
            }
        }
    }

    /**
     * 通用 trace 同步方法
     *
     * @param type trace 类型
     * @param userName 操作人 {@link SyncTraceMapKeyConstants#OP_USER_ID}, 可为 null
     * @param orderNo 订单号 {@link SyncTraceMapKeyConstants#ORDER_NO}, 可为 null
     * @param warehouseId 仓库名称 {@link SyncTraceMapKeyConstants#WAREHOUSE_ID}
     * @param warehouseName 仓库名称 {@link SyncTraceMapKeyConstants#WAREHOUSE_NAME}, 可以为 null
     * @param ids 订单 id, 不可为 null
     */
    private void syncTrace(int type, @Nullable String userName, @Nullable String orderNo, @Nonnull List<Long> ids,
        @Nullable String warehouseId, @Nullable String warehouseName) {
        Map<String, String> traceParamMap = new HashMap<>();
        Optional.ofNullable(userName).ifPresent(it -> traceParamMap.put(SyncTraceMapKeyConstants.OP_USER_ID, it));
        Optional.ofNullable(orderNo).ifPresent(it -> traceParamMap.put(SyncTraceMapKeyConstants.ORDER_NO, it));
        Optional.ofNullable(warehouseId).ifPresent(it -> traceParamMap.put(SyncTraceMapKeyConstants.WAREHOUSE_ID, it));
        Optional.ofNullable(warehouseName)
            .ifPresent(it -> traceParamMap.put(SyncTraceMapKeyConstants.WAREHOUSE_NAME, it));
        SyncTraceDTO syncTraceDTO = newTrace(type, traceParamMap);
        orderCenterService.syncTrace(syncTraceDTO, ids);
    }
}
