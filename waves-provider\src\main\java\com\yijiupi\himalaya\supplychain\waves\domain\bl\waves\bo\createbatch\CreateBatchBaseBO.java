package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch;

import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByRefOrderNoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;

import java.io.Serializable;
import java.util.Optional;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/10/16
 */
public class CreateBatchBaseBO implements Serializable {
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 手动创建波次参数
     */
    private BatchCreateDTO batchCreateDTO;
    /**
     * 按订单创建拣货任务参数
     */
    private BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO;
    /**
     * 操作人
     */
    private Integer optUserId;

    public CreateBatchBaseBO() {}

    public CreateBatchBaseBO(Integer warehouseId, BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO) {
        this.warehouseId = warehouseId;
        this.optUserId = batchCreateByRefOrderNoDTO.getOperateUserId();
        this.batchCreateByRefOrderNoDTO = batchCreateByRefOrderNoDTO;
    }

    public CreateBatchBaseBO(Integer warehouseId, BatchCreateDTO batchCreateDTO) {
        this.warehouseId = warehouseId;
        this.optUserId = batchCreateDTO.getOperateUserId();
        this.batchCreateDTO = batchCreateDTO;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 手动创建波次参数
     *
     * @return batchCreateDTO 手动创建波次参数
     */
    public BatchCreateDTO getBatchCreateDTO() {
        return this.batchCreateDTO;
    }

    /**
     * 设置 手动创建波次参数
     *
     * @param batchCreateDTO 手动创建波次参数
     */
    public void setBatchCreateDTO(BatchCreateDTO batchCreateDTO) {
        this.batchCreateDTO = batchCreateDTO;
    }

    /**
     * 获取 按订单创建拣货任务参数
     *
     * @return batchCreateByRefOrderNoDTO 按订单创建拣货任务参数
     */
    public BatchCreateByRefOrderNoDTO getBatchCreateByRefOrderNoDTO() {
        return this.batchCreateByRefOrderNoDTO;
    }

    /**
     * 设置 按订单创建拣货任务参数
     *
     * @param batchCreateByRefOrderNoDTO 按订单创建拣货任务参数
     */
    public void setBatchCreateByRefOrderNoDTO(BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO) {
        this.batchCreateByRefOrderNoDTO = batchCreateByRefOrderNoDTO;
    }

    public String getOperateUserId() {
        if (batchCreateDTO != null) {
            return String.valueOf(batchCreateDTO.getOperateUserId());
        }
        if (batchCreateByRefOrderNoDTO != null) {
            return String.valueOf(batchCreateByRefOrderNoDTO.getOperateUserId());
        }
        return null;
    }

    public String getOperateUserName() {
        if (batchCreateDTO != null) {
            return batchCreateDTO.getOperateUser();
        }
        if (batchCreateByRefOrderNoDTO != null) {
            return batchCreateByRefOrderNoDTO.getOperateUser();
        }
        return null;
    }

    /**
     * 获取 操作人
     *
     * @return optUserId 操作人
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人
     *
     * @param optUserId 操作人
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }
}
