package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationLargePickCargoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.LocationSplitHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.util.LargePickCargoUtil;
import com.yijiupi.himalaya.supplychain.waves.util.RobotPickConstants;

/**
 * <AUTHOR>
 * @title: MergeSplitItemConvertor
 * @description:
 * @date 2023-02-10 09:16
 */
public class MergeSplitItemConvertor {

    private static final Logger LOG = LoggerFactory.getLogger(MergeSplitItemConvertor.class);

    // 开启了货位库存、 整件拆零 的 把 存储位 的 订单项合并 到 通道订单项上去， 并把根据skuId聚合后数量为大件的，订单项id 一致的 多个订单项合并成一个
    // 包装规格是5，
    // 第一种情况： 一个买了2小件，一个买了3小件; 这种情况之前 把 3小件的 拆成 2 + 1； 1小件分的零拣位，2小件分的存储位，需要把之前拆分的订单项合并
    // 第二种情况： 一个买了3小件，一个买了3小件；这种情况之前 把 3小件拆成 2 + 1；1小件分的零拣位，2小件分的存储位； 不需要把之前拆分的订单项合并
    // 第三种情况： 一个买了5小件，一个买了1小件；没有拆分，不需要把之前拆分的订单项合并
    // 上述 处理完成后 ，把skuId 相同的聚合起来， 统一生成播种任务，但 大件生成一个拣货任务，小件生成一个拣货任务
    // ====================================
    // 新需求之后上面作废
    // 这里的订单项都是拆过之后的订单项（即订单项有1大件1小件，已经拆成了两个），订单项全是大数量，不用进通道，到后面单独生成一个拣货任务；
    // 如果全是小单位数量，需要处理，1、是小件数且location为存储位的，需要将item添加到订单里；2、

    /**
     * 合并那些为了找到通道，生成播种 而拆分出来的订单项
     *
     * @param passageItemList 属于该通道的订单项
     * @param totalItemList 所有订单项
     * @param lstAdded 已经生成拣货任务的订单项
     * @param warehouseConfigDTO 仓库配置
     * @param wavesStrategyDTO 波次策略
     * @return
     */
    @Deprecated
    public static List<OutStockOrderItemPO> mergerItemList(List<OutStockOrderItemPO> passageItemList,
        List<OutStockOrderItemPO> totalItemList, List<OutStockOrderItemPO> lstAdded,
        WarehouseConfigDTO warehouseConfigDTO, WavesStrategyBO wavesStrategyDTO) {
        LOG.info("合并totalItemList前：{}", JSON.toJSONString(totalItemList));
        // 如果没有开启配置的，不处理
        if (BooleanUtils.isFalse(RobotPickConstants.isWarehouseOpenLargePick(wavesStrategyDTO, warehouseConfigDTO))) {

            return passageItemList;
        }

        // 规格是6 4小件 没拆；3小件 拆成 2小件 和 1小件
        Map<Long, Long> skuIdMap = passageItemList.stream()
            .collect(Collectors.toMap(OutStockOrderItemPO::getSkuid, OutStockOrderItemPO::getSkuid, (v1, v2) -> v1));

        Map<Long, List<OutStockOrderItemPO>> skuGroupMap =
            totalItemList.stream().filter(m -> m.getPackagecount().compareTo(BigDecimal.ZERO) == 0)
                .filter(m -> m.getUnitcount().compareTo(BigDecimal.ZERO) > 0)
                .filter(m -> Objects.nonNull(skuIdMap.get(m.getSkuid())))
                .collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid));

        if (CollectionUtils.isEmpty(skuGroupMap)) {
            return passageItemList;
        }

        List<LocationSplitHelperBO> splitHelperList =
            LocationSplitHelperBOConvertor.convert(skuGroupMap, wavesStrategyDTO, warehouseConfigDTO);

        // 按skuId聚合后，过滤大件数不为0的订单项，如果没有，直接返回
        List<LocationSplitHelperBO> packageHelperList = splitHelperList.stream()
            .filter(bo -> bo.getPackageCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(packageHelperList)) {
            return passageItemList;
        }

        // List<OutStockOrderItemPO> groupItemList = passageItemList.stream().map(m ->
        // skuGroupMap.get(m.getSkuid())).filter(com.alibaba.dubbo.common.utils.CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());

        // 不用过滤了，直接用新的，因为按订单项拆，有可能有的订单项跟跟原来id一致，数量一致（货位id不一致）。但这样没有单独标识，后面没法单独拆拣货任务（暂时按subCategory为存储位的进行拆分）
        // 这里有两种情况，一种合并成一个订单项、一种添加到passageItem中

        // 四种情况：
        // 小件聚合为整大件，且大于货位库存上限的，拆出一件走通道
        // 小件聚合为整大件，且小于货位库存上限的，全走零拣位，
        // 小件聚合为大件+小件，且大于货位库存上限的，大件走存储位，小件走零拣位
        // 小件聚合为大件+小件，且小于货位库存上限的，全走零拣位
        Map<String,
            List<OutStockOrderItemPO>> itemIdGroupMap = splitHelperList.stream()
                .filter(LargePickCargoUtil::needMergeUnit).flatMap(m -> m.getItemList().stream())
                .collect(Collectors.groupingBy(item -> item.getId() + " " + item.getItemDetailId()));

        if (CollectionUtils.isEmpty(itemIdGroupMap)) {
            return passageItemList;
        }

        for (Map.Entry<String, List<OutStockOrderItemPO>> entry : itemIdGroupMap.entrySet()) {
            if (BooleanUtils.isFalse(LargePickCargoUtil.filterMerge(entry.getValue()))) {
                continue;
            }
            // 这里有极端情况； 规格为5， 两个订单一个3，一个2； 数量为3的第一步会拆分成1（零） 和 2（存），如果这里detail原始有三项，那么就会拆成两个订单项
            // 如果 一个订单项有多个货主 进行 拆分，零拣位 一定是一项；存储位有可能有多项，所以先找零拣位，然后根据零拣位的 itemId 去匹配 存储位
            OutStockOrderItemPO otherItem = entry.getValue().stream()
                .filter(m -> Objects.nonNull(m.getSubCategory())
                    && CreateBatchLocationLargePickCargoBL.smallLocationList.contains(m.getSubCategory().intValue()))
                .findFirst().get();
            OutStockOrderItemPO storeItem = entry.getValue().stream()
                .filter(m -> Objects.nonNull(m.getSubCategory())
                    && LocationEnum.存储位.getType().byteValue() == m.getSubCategory()
                    && m.getItemDetailId().equals(otherItem.getItemDetailId()))
                .findFirst().get();

            // passageItemList移除零拣位的项，添加合并后的项
            // totalItemList移除零拣位的项 和 存储位的项 ，添加 合并后的项目

            OutStockOrderItemPO newItem = LocationSplitHelperResultBOConvertor.mergeOrderItem(storeItem, otherItem);
            // 移除的时候要数量相等
            totalItemList.removeIf(
                m -> m.getId().equals(storeItem.getId()) && storeItem.getLocationId().equals(m.getLocationId())
                    && m.getItemDetailId().equals(storeItem.getItemDetailId())
                    && m.getUnittotalcount().equals(storeItem.getUnittotalcount()));
            totalItemList.removeIf(
                m -> m.getId().equals(otherItem.getId()) && otherItem.getLocationId().equals(m.getLocationId())
                    && m.getItemDetailId().equals(otherItem.getItemDetailId()));
            totalItemList.add(newItem);

            passageItemList.removeIf(
                m -> m.getId().equals(otherItem.getId()) && otherItem.getLocationId().equals(m.getLocationId())
                    && m.getItemDetailId().equals(otherItem.getItemDetailId()));
            passageItemList.add(newItem);

            lstAdded.removeIf(
                m -> m.getId().equals(otherItem.getId()) && otherItem.getLocationId().equals(m.getLocationId())
                    && m.getItemDetailId().equals(otherItem.getItemDetailId()));
            lstAdded.add(newItem);

        }

        LOG.info("合并totalItemList后：{}", JSON.toJSONString(totalItemList));
        Map<Long, List<OutStockOrderItemPO>> newSkuGroupMap =
            totalItemList.stream().filter(m -> Objects.nonNull(skuIdMap.get(m.getSkuid())))
                .filter(m -> m.getPackagecount().compareTo(BigDecimal.ZERO) == 0)
                .filter(m -> m.getUnitcount().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid));
        return newSkuGroupMap.values().stream().filter(com.alibaba.dubbo.common.utils.CollectionUtils::isNotEmpty)
            .flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     *
     * 1、全是大件的，由于要进通道，所以前面拆了一个小件出来，这里要进行合并 2、小件聚合成大件，超出货位补货上限的，也可能会对订单项进行拆分。规格6，补货上限6，数量4小件 + 3小件，这时候就会进行拆分，这时候是不需要进行合并
     *
     * 所以，这里进行合并的时候，判断订单项按聚合id聚合后，是否是整件，只有是整件，才合并
     *
     * 如果开了机器人，上述描述成立 如果没开机器人，就要按skuId去聚合，然后直接合并
     *
     * @param passageItemList 通道内的订单项
     * @param totalItemList 全部订单项
     * @param lstAdded 已添加到通道的订单项
     * @param warehouseConfigDTO
     * @param wavesStrategyDTO
     * @return
     */
    // public static List<OutStockOrderItemPO> mergerItemListByUuid(List<OutStockOrderItemPO> passageItemList,
    // List<OutStockOrderItemPO> totalItemList, List<OutStockOrderItemPO> lstAdded,
    // WarehouseConfigDTO warehouseConfigDTO, WavesStrategyDTO wavesStrategyDTO, PassageDTO passageDTO) {
    // LOG.info("合并totalItemList前：{}", JSON.toJSONString(totalItemList));
    // // 如果没有开启配置的，不处理
    // if (BooleanUtils.isFalse(RobotPickConstants.isWarehouseOpenLargePick(wavesStrategyDTO, warehouseConfigDTO))) {
    //
    // return passageItemList;
    // }
    //
    // List<OutStockOrderItemPO> needMergeItemList =
    // passageItemList.stream().filter(m -> Objects.nonNull(m.getUuid())).collect(Collectors.toList());
    // if (CollectionUtils.isEmpty(needMergeItemList)) {
    // return getNeedPassageItemList(passageItemList, totalItemList);
    // }
    //
    // Map<String, String> uuidMap = needMergeItemList.stream()
    // .collect(Collectors.toMap(OutStockOrderItemPO::getUuid, OutStockOrderItemPO::getUuid, (v1, v2) -> v1));
    // // TODO 优化代码
    // //
    // if (!RobotPickConstants.isPassageRobotPickOpen(wavesStrategyDTO, passageDTO)) {
    // return mergeNormalOrderItem(passageItemList, totalItemList, lstAdded, uuidMap);
    // }
    //
    // return mergeRobotOrderItem(passageItemList, totalItemList, lstAdded, uuidMap);
    // }

    /**
     * 合并普通的订单项
     *
     * @param passageItemList
     * @param totalItemList
     * @param lstAdded
     * @param uuidMap
     * @return
     */
    // private static List<OutStockOrderItemPO> mergeNormalOrderItem(List<OutStockOrderItemPO> passageItemList,
    // List<OutStockOrderItemPO> totalItemList, List<OutStockOrderItemPO> lstAdded, Map<String, String> uuidMap) {
    // // 按skuId 聚合 然后 恢复
    // Map<Long, List<OutStockOrderItemPO>> skuItemMap =
    // totalItemList.stream().filter(m -> Objects.nonNull(uuidMap.get(m.getUuid())))
    // .collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid));
    // for (Map.Entry<Long, List<OutStockOrderItemPO>> entry : skuItemMap.entrySet()) {
    // if (BooleanUtils.isFalse(skuNeedMerge(entry.getValue()))) {
    // continue;
    // }
    //
    // Map<String, List<OutStockOrderItemPO>> uuidItemMap =
    // entry.getValue().stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getUuid));
    // // 这里有两种情况：第一种，正常拆成存储位和零拣位的；第二种，没有存储位的，所以合并的时候要分开处理；或者把第二种情况，直接前拆分的时候就合并处理了
    // for (Map.Entry<String, List<OutStockOrderItemPO>> uuidEntry : uuidItemMap.entrySet()) {
    // if (uuidEntry.getValue().size() == 1) {
    // continue;
    // }
    // OutStockOrderItemPO otherItem = getOtherItem(uuidEntry.getValue());
    // OutStockOrderItemPO storeItem = getStoreItem(uuidEntry.getValue(), otherItem);
    //
    // OutStockOrderItemPO newItem = LocationSplitHelperResultBOConvertor.mergeOrderItem(storeItem, otherItem);
    // // 移除的时候要数量相等
    // totalItemList.removeIf(
    // m -> m.getId().equals(storeItem.getId()) && storeItem.getLocationId().equals(m.getLocationId())
    // && m.getItemDetailId().equals(storeItem.getItemDetailId())
    // && storeItem.getUuid().equals(m.getUuid()));
    // totalItemList.removeIf(
    // m -> m.getId().equals(otherItem.getId()) && otherItem.getLocationId().equals(m.getLocationId())
    // && m.getItemDetailId().equals(otherItem.getItemDetailId())
    // && otherItem.getUuid().equals(m.getUuid()));
    // totalItemList.add(newItem);
    //
    // passageItemList.removeIf(
    // m -> m.getId().equals(otherItem.getId()) && otherItem.getLocationId().equals(m.getLocationId())
    // && m.getItemDetailId().equals(otherItem.getItemDetailId())
    // && otherItem.getUuid().equals(m.getUuid()));
    // passageItemList.add(newItem);
    //
    // lstAdded.removeIf(
    // m -> m.getId().equals(otherItem.getId()) && otherItem.getLocationId().equals(m.getLocationId())
    // && m.getItemDetailId().equals(otherItem.getItemDetailId())
    // && otherItem.getUuid().equals(m.getUuid()));
    // lstAdded.add(newItem);
    // }
    // }
    //
    // return getNeedPassageItemList(passageItemList, totalItemList);
    // }

    // private static List<OutStockOrderItemPO> mergeRobotOrderItem(List<OutStockOrderItemPO> passageItemList,
    // List<OutStockOrderItemPO> totalItemList, List<OutStockOrderItemPO> lstAdded, Map<String, String> uuidMap) {
    // Map<String, List<OutStockOrderItemPO>> filterMergeItemMap =
    // totalItemList.stream().filter(m -> Objects.nonNull(uuidMap.get(m.getUuid())))
    // .collect(Collectors.groupingBy(OutStockOrderItemPO::getUuid));
    //
    // for (Map.Entry<String, List<OutStockOrderItemPO>> entry : filterMergeItemMap.entrySet()) {
    //
    // if (BooleanUtils.isFalse(needMerge(entry.getValue()))) {
    // continue;
    // }
    //
    // OutStockOrderItemPO otherItem = getOtherItem(entry.getValue());
    // OutStockOrderItemPO storeItem = getStoreItem(entry.getValue(), otherItem);
    //
    // OutStockOrderItemPO newItem = LocationSplitHelperResultBOConvertor.mergeOrderItem(storeItem, otherItem);
    // // 移除的时候要数量相等
    // totalItemList.removeIf(m -> m.getId().equals(storeItem.getId())
    // && storeItem.getLocationId().equals(m.getLocationId())
    // && m.getItemDetailId().equals(storeItem.getItemDetailId()) && storeItem.getUuid().equals(m.getUuid()));
    // totalItemList.removeIf(m -> m.getId().equals(otherItem.getId())
    // && otherItem.getLocationId().equals(m.getLocationId())
    // && m.getItemDetailId().equals(otherItem.getItemDetailId()) && otherItem.getUuid().equals(m.getUuid()));
    // totalItemList.add(newItem);
    //
    // passageItemList.removeIf(m -> m.getId().equals(otherItem.getId())
    // && otherItem.getLocationId().equals(m.getLocationId())
    // && m.getItemDetailId().equals(otherItem.getItemDetailId()) && otherItem.getUuid().equals(m.getUuid()));
    // passageItemList.add(newItem);
    //
    // lstAdded.removeIf(m -> m.getId().equals(otherItem.getId())
    // && otherItem.getLocationId().equals(m.getLocationId())
    // && m.getItemDetailId().equals(otherItem.getItemDetailId()) && otherItem.getUuid().equals(m.getUuid()));
    // lstAdded.add(newItem);
    // }
    //
    // return getNeedPassageItemList(passageItemList, totalItemList);
    // }

    private static OutStockOrderItemPO getOtherItem(List<OutStockOrderItemPO> itemList) {
        List<OutStockOrderItemPO> otherItemList = itemList.stream()
            .filter(m -> Objects.nonNull(m.getSubCategory())
                && CreateBatchLocationLargePickCargoBL.smallLocationList.contains(m.getSubCategory().intValue()))
            .collect(Collectors.toList());

        return otherItemList.get(0);
    }

    private static OutStockOrderItemPO getStoreItem(List<OutStockOrderItemPO> itemList, OutStockOrderItemPO otherItem) {
        Optional<OutStockOrderItemPO> storeItemOptional =
            itemList.stream()
                .filter(m -> Objects.nonNull(m.getSubCategory())
                    && LocationEnum.存储位.getType().byteValue() == m.getSubCategory()
                    && m.getItemDetailId().equals(otherItem.getItemDetailId()))
                .findFirst();
        if (storeItemOptional.isPresent()) {
            return storeItemOptional.get();
        }

        List<OutStockOrderItemPO> otherItemList = itemList.stream().filter(
            m -> Objects.nonNull(m.getSubCategory()) && LocationEnum.存储位.getType().byteValue() != m.getSubCategory())
            .collect(Collectors.toList());

        return otherItemList.get(1);
    }

    private static List<OutStockOrderItemPO> getNeedPassageItemList(List<OutStockOrderItemPO> passageItemList,
        List<OutStockOrderItemPO> totalItemList) {
        Map<Long, Long> skuIdMap = passageItemList.stream()
            .collect(Collectors.toMap(OutStockOrderItemPO::getSkuid, OutStockOrderItemPO::getSkuid, (v1, v2) -> v1));

        Map<Long, List<OutStockOrderItemPO>> newSkuGroupMap =
            totalItemList.stream().filter(m -> Objects.nonNull(skuIdMap.get(m.getSkuid())))
                .collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid));
        return newSkuGroupMap.values().stream().filter(com.alibaba.dubbo.common.utils.CollectionUtils::isNotEmpty)
            .flatMap(Collection::stream).collect(Collectors.toList());

    }

    private static boolean needMerge(List<OutStockOrderItemPO> orderItemList) {
        if (CollectionUtils.isEmpty(orderItemList)) {
            return Boolean.FALSE;
        }
        if (orderItemList.size() == 1) {
            return Boolean.FALSE;
        }
        BigDecimal totalUnitCount =
            orderItemList.stream().map(OutStockOrderItemPO::getUnittotalcount).reduce(BigDecimal.ZERO, BigDecimal::add);
        OutStockOrderItemPO outStockOrderItemPO = orderItemList.get(0);
        BigDecimal[] count = totalUnitCount.divideAndRemainder(outStockOrderItemPO.getSpecquantity());
        if (BigDecimal.ZERO.compareTo(count[1]) == 0) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    private static boolean skuNeedMerge(List<OutStockOrderItemPO> orderItemList) {
        BigDecimal totalUnitCount =
            orderItemList.stream().map(OutStockOrderItemPO::getUnittotalcount).reduce(BigDecimal.ZERO, BigDecimal::add);
        OutStockOrderItemPO outStockOrderItemPO = orderItemList.get(0);
        BigDecimal[] count = totalUnitCount.divideAndRemainder(outStockOrderItemPO.getSpecquantity());
        if (BigDecimal.ZERO.compareTo(count[1]) == 0) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    /**
     * 聚合的订单，进行的复制，订单项没复制，仍然是原来的
     *
     * @param orderList
     * @return
     */
    public static List<OutStockOrderPO> copyAndMergeSameOrder(List<OutStockOrderPO> orderList) {
        Map<Long, List<OutStockOrderPO>> outStockOrderMap =
            orderList.stream().collect(Collectors.groupingBy(OutStockOrderPO::getId));
        List<OutStockOrderPO> mergeOrderList = new ArrayList<>();
        for (Map.Entry<Long, List<OutStockOrderPO>> entry : outStockOrderMap.entrySet()) {
            OutStockOrderPO oldOrder = entry.getValue().get(0);
            List<OutStockOrderItemPO> itemList =
                entry.getValue().stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList());
            OutStockOrderPO newOrder = new OutStockOrderPO();
            BeanUtils.copyProperties(oldOrder, newOrder);
            newOrder.setItems(itemList);
            newOrder.setPackageamount(
                itemList.stream().map(OutStockOrderItemPO::getPackagecount).reduce(BigDecimal.ZERO, BigDecimal::add));
            newOrder.setUnitamount(
                itemList.stream().map(OutStockOrderItemPO::getUnitcount).reduce(BigDecimal.ZERO, BigDecimal::add));
            mergeOrderList.add(newOrder);
        }

        return mergeOrderList;
    }

    public static OutStockOrderItemPO mergeDetail(List<OutStockOrderItemPO> itemList) {
        OutStockOrderItemPO newItemPO = new OutStockOrderItemPO();
        OutStockOrderItemPO itemPO = itemList.get(0);

        BeanUtils.copyProperties(itemPO, newItemPO);

        BigDecimal totalUnitCount =
            itemList.stream().map(OutStockOrderItemPO::getUnittotalcount).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal[] count = totalUnitCount.divideAndRemainder(newItemPO.getSpecquantity());
        newItemPO.setPackagecount(count[0]);
        newItemPO.setUnitcount(count[1]);
        newItemPO.setUnittotalcount(totalUnitCount);

        OutStockOrderItemDetailPO detailPO = newItemPO.getItemDetails().get(0);
        detailPO.setUnitTotalCount(totalUnitCount);

        return newItemPO;
    }

}