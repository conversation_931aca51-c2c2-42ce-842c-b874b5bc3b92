package com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch;

import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.ExistSowTaskBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.GoodsCollectionLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/25
 */
public class CreateSowTaskByPassageBO {

    private java.util.List<OutStockOrderPO> orderList;
    private WavesStrategyBO wavesStrategyDTO;
    private PassageDTO passageDTO;
    private String title;
    private String operateUser;
    private Integer cityId;
    private WarehouseConfigDTO warehouseConfigDTO;
    private BatchBO batchBO;
    private List<WaveCreateDTO> waveCreateDTOList;
    private List<GoodsCollectionLocationBO> lstLocations;
    private List<SowTaskPO> sowTaskPOList;
    private String locationName;
    private List<SowOrderPO> sowOrderPOList;
    private String driverName;
    private Boolean allocationFlag;
    private WaveCreateDTO oriCreateDTO;
    private ExistSowTaskBO existSowTaskPO;
    private boolean needRecheck;

    /**
     * /** 获取
     *
     * @return wavesStrategyDTO
     */
    public WavesStrategyBO getWavesStrategyDTO() {
        return this.wavesStrategyDTO;
    }

    /**
     * 设置
     *
     * @param wavesStrategyDTO
     */
    public void setWavesStrategyDTO(WavesStrategyBO wavesStrategyDTO) {
        this.wavesStrategyDTO = wavesStrategyDTO;
    }

    /**
     * 获取
     *
     * @return passageDTO
     */
    public PassageDTO getPassageDTO() {
        return this.passageDTO;
    }

    /**
     * 设置
     *
     * @param passageDTO
     */
    public void setPassageDTO(PassageDTO passageDTO) {
        this.passageDTO = passageDTO;
    }

    /**
     * 获取
     *
     * @return title
     */
    public String getTitle() {
        return this.title;
    }

    /**
     * 设置
     *
     * @param title
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 获取
     *
     * @return operateUser
     */
    public String getOperateUser() {
        return this.operateUser;
    }

    /**
     * 设置
     *
     * @param operateUser
     */
    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    /**
     * 获取
     *
     * @return cityId
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置
     *
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取
     *
     * @return warehouseConfigDTO
     */
    public WarehouseConfigDTO getWarehouseConfigDTO() {
        return this.warehouseConfigDTO;
    }

    /**
     * 设置
     *
     * @param warehouseConfigDTO
     */
    public void setWarehouseConfigDTO(WarehouseConfigDTO warehouseConfigDTO) {
        this.warehouseConfigDTO = warehouseConfigDTO;
    }

    /**
     * 获取
     *
     * @return batchBO
     */
    public BatchBO getBatchBO() {
        return this.batchBO;
    }

    /**
     * 设置
     *
     * @param batchBO
     */
    public void setBatchBO(BatchBO batchBO) {
        this.batchBO = batchBO;
    }

    /**
     * 获取
     *
     * @return lstLocations
     */
    public List<GoodsCollectionLocationBO> getLstLocations() {
        return this.lstLocations;
    }

    /**
     * 设置
     *
     * @param lstLocations
     */
    public void setLstLocations(List<GoodsCollectionLocationBO> lstLocations) {
        this.lstLocations = lstLocations;
    }

    /**
     * 获取
     *
     * @return locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置
     *
     * @param locationName
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取
     *
     * @return driverName
     */
    public String getDriverName() {
        return this.driverName;
    }

    /**
     * 设置
     *
     * @param driverName
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    /**
     * 获取
     *
     * @return allocationFlag
     */
    public Boolean getAllocationFlag() {
        return this.allocationFlag;
    }

    /**
     * 设置
     *
     * @param allocationFlag
     */
    public void setAllocationFlag(Boolean allocationFlag) {
        this.allocationFlag = allocationFlag;
    }

    /**
     * 获取
     *
     * @return oriCreateDTO
     */
    public WaveCreateDTO getOriCreateDTO() {
        return this.oriCreateDTO;
    }

    /**
     * 设置
     *
     * @param oriCreateDTO
     */
    public void setOriCreateDTO(WaveCreateDTO oriCreateDTO) {
        this.oriCreateDTO = oriCreateDTO;
    }

    /**
     * 获取
     *
     * @return existSowTaskPO
     */
    public ExistSowTaskBO getExistSowTaskPO() {
        return this.existSowTaskPO;
    }

    /**
     * 设置
     *
     * @param existSowTaskPO
     */
    public void setExistSowTaskPO(ExistSowTaskBO existSowTaskPO) {
        this.existSowTaskPO = existSowTaskPO;
    }

    /**
     * 获取
     *
     * @return orderList
     */
    public List<OutStockOrderPO> getOrderList() {
        return this.orderList;
    }

    /**
     * 设置
     *
     * @param orderList
     */
    public void setOrderList(List<OutStockOrderPO> orderList) {
        this.orderList = orderList;
    }

    /**
     * 获取
     *
     * @return sowTaskPOList
     */
    public List<SowTaskPO> getSowTaskPOList() {
        return this.sowTaskPOList;
    }

    /**
     * 设置
     *
     * @param sowTaskPOList
     */
    public void setSowTaskPOList(List<SowTaskPO> sowTaskPOList) {
        this.sowTaskPOList = sowTaskPOList;
    }

    /**
     * 获取
     *
     * @return sowOrderPOList
     */
    public List<SowOrderPO> getSowOrderPOList() {
        return this.sowOrderPOList;
    }

    /**
     * 设置
     *
     * @param sowOrderPOList
     */
    public void setSowOrderPOList(List<SowOrderPO> sowOrderPOList) {
        this.sowOrderPOList = sowOrderPOList;
    }

    /**
     * 获取
     *
     * @return needRecheck
     */
    public boolean isNeedRecheck() {
        return this.needRecheck;
    }

    /**
     * 设置
     *
     * @param needRecheck
     */
    public void setNeedRecheck(boolean needRecheck) {
        this.needRecheck = needRecheck;
    }

    /**
     * 获取
     *
     * @return waveCreateDTOList
     */
    public List<WaveCreateDTO> getWaveCreateDTOList() {
        return this.waveCreateDTOList;
    }

    /**
     * 设置
     *
     * @param waveCreateDTOList
     */
    public void setWaveCreateDTOList(List<WaveCreateDTO> waveCreateDTOList) {
        this.waveCreateDTOList = waveCreateDTOList;
    }

    public static CreateSowTaskByPassageBO copyBO(CreateSowTaskByPassageBO createSowTaskByPassageBO) {
        CreateSowTaskByPassageBO passageBO = new CreateSowTaskByPassageBO();
        passageBO.setWavesStrategyDTO(createSowTaskByPassageBO.getWavesStrategyDTO());
        passageBO.setPassageDTO(createSowTaskByPassageBO.getPassageDTO());
        passageBO.setTitle(createSowTaskByPassageBO.getTitle());
        passageBO.setOperateUser(createSowTaskByPassageBO.getOperateUser());
        passageBO.setCityId(createSowTaskByPassageBO.getCityId());
        passageBO.setWarehouseConfigDTO(createSowTaskByPassageBO.getWarehouseConfigDTO());
        passageBO.setBatchBO(createSowTaskByPassageBO.getBatchBO());
        passageBO.setLstLocations(createSowTaskByPassageBO.getLstLocations());
        passageBO.setLocationName(createSowTaskByPassageBO.getLocationName());
        passageBO.setDriverName(createSowTaskByPassageBO.getDriverName());
        passageBO.setAllocationFlag(createSowTaskByPassageBO.getAllocationFlag());
        passageBO.setOriCreateDTO(createSowTaskByPassageBO.getOriCreateDTO());
        passageBO.setExistSowTaskPO(createSowTaskByPassageBO.getExistSowTaskPO());
        passageBO.setOrderList(createSowTaskByPassageBO.getOrderList());
        passageBO.setSowTaskPOList(createSowTaskByPassageBO.getSowTaskPOList());
        passageBO.setSowOrderPOList(createSowTaskByPassageBO.getSowOrderPOList());
        passageBO.setNeedRecheck(createSowTaskByPassageBO.isNeedRecheck());
        passageBO.setWaveCreateDTOList(createSowTaskByPassageBO.waveCreateDTOList);

        return passageBO;
    }

    public static CreateSowTaskBO convertToCreateSowTaskBO(CreateSowTaskByPassageBO createSowTaskByPassageBO) {
        CreateSowTaskBO createSowTaskBO = CreateSowTaskBO.CreateSowTaskBOBuilder.aCreateSowTaskBO()
            .withLstSowTaskPO(createSowTaskByPassageBO.getSowTaskPOList())
            .withLstSowOrders(createSowTaskByPassageBO.getSowOrderPOList())
            .withBatchBO(createSowTaskByPassageBO.getBatchBO())
            .withOriCreateDTO(createSowTaskByPassageBO.getOriCreateDTO())
            .withCityId(createSowTaskByPassageBO.getCityId())
            .withLstLocations(createSowTaskByPassageBO.getLstLocations())
            .withLocationName(createSowTaskByPassageBO.getLocationName())
            .withDriverName(createSowTaskByPassageBO.getDriverName())
            .withPassageDTO(createSowTaskByPassageBO.getPassageDTO())
            .withAllocationFlag(createSowTaskByPassageBO.getAllocationFlag())
            .withNeedRecheck(createSowTaskByPassageBO.isNeedRecheck())
            .withOperateUser(createSowTaskByPassageBO.getOperateUser()).withTitle(createSowTaskByPassageBO.getTitle())
            .withSplitOrderList(createSowTaskByPassageBO.getOrderList())
            .withWarehouseConfigDTO(createSowTaskByPassageBO.getWarehouseConfigDTO())
            .withWavesStrategyDTO(createSowTaskByPassageBO.getWavesStrategyDTO()).build();

        return createSowTaskBO;
    }

    public static final class CreateSowTaskByPassageBOBuilder {
        private List<OutStockOrderPO> orderList;
        private WavesStrategyBO wavesStrategyDTO;
        private PassageDTO passageDTO;
        private String title;
        private String operateUser;
        private Integer cityId;
        private WarehouseConfigDTO warehouseConfigDTO;
        private BatchBO batchBO;
        private List<WaveCreateDTO> waveCreateDTOList;
        private List<GoodsCollectionLocationBO> lstLocations;
        private List<SowTaskPO> sowTaskPOList;
        private String locationName;
        private List<SowOrderPO> sowOrderPOList;
        private String driverName;
        private Boolean allocationFlag;
        private WaveCreateDTO oriCreateDTO;
        private ExistSowTaskBO existSowTaskPO;
        private boolean needRecheck;

        private CreateSowTaskByPassageBOBuilder() {}

        public static CreateSowTaskByPassageBOBuilder createSowTaskByPassageBO() {
            return new CreateSowTaskByPassageBOBuilder();
        }

        public CreateSowTaskByPassageBOBuilder withOrderList(List<OutStockOrderPO> orderList) {
            this.orderList = orderList;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withWavesStrategyDTO(WavesStrategyBO wavesStrategyDTO) {
            this.wavesStrategyDTO = wavesStrategyDTO;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withPassageDTO(PassageDTO passageDTO) {
            this.passageDTO = passageDTO;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withTitle(String title) {
            this.title = title;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withOperateUser(String operateUser) {
            this.operateUser = operateUser;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withCityId(Integer cityId) {
            this.cityId = cityId;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withWarehouseConfigDTO(WarehouseConfigDTO warehouseConfigDTO) {
            this.warehouseConfigDTO = warehouseConfigDTO;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withBatchBO(BatchBO batchBO) {
            this.batchBO = batchBO;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withWaveCreateDTOList(List<WaveCreateDTO> waveCreateDTOList) {
            this.waveCreateDTOList = waveCreateDTOList;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withLstLocations(List<GoodsCollectionLocationBO> lstLocations) {
            this.lstLocations = lstLocations;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withSowTaskPOList(List<SowTaskPO> sowTaskPOList) {
            this.sowTaskPOList = sowTaskPOList;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withLocationName(String locationName) {
            this.locationName = locationName;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withSowOrderPOList(List<SowOrderPO> sowOrderPOList) {
            this.sowOrderPOList = sowOrderPOList;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withDriverName(String driverName) {
            this.driverName = driverName;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withAllocationFlag(Boolean allocationFlag) {
            this.allocationFlag = allocationFlag;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withOriCreateDTO(WaveCreateDTO oriCreateDTO) {
            this.oriCreateDTO = oriCreateDTO;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withExistSowTaskPO(ExistSowTaskBO existSowTaskPO) {
            this.existSowTaskPO = existSowTaskPO;
            return this;
        }

        public CreateSowTaskByPassageBOBuilder withNeedRecheck(boolean needRecheck) {
            this.needRecheck = needRecheck;
            return this;
        }

        public CreateSowTaskByPassageBO build() {
            CreateSowTaskByPassageBO createSowTaskByPassageBO = new CreateSowTaskByPassageBO();
            createSowTaskByPassageBO.setOrderList(orderList);
            createSowTaskByPassageBO.setWavesStrategyDTO(wavesStrategyDTO);
            createSowTaskByPassageBO.setPassageDTO(passageDTO);
            createSowTaskByPassageBO.setTitle(title);
            createSowTaskByPassageBO.setOperateUser(operateUser);
            createSowTaskByPassageBO.setCityId(cityId);
            createSowTaskByPassageBO.setWarehouseConfigDTO(warehouseConfigDTO);
            createSowTaskByPassageBO.setBatchBO(batchBO);
            createSowTaskByPassageBO.setWaveCreateDTOList(waveCreateDTOList);
            createSowTaskByPassageBO.setLstLocations(lstLocations);
            createSowTaskByPassageBO.setSowTaskPOList(sowTaskPOList);
            createSowTaskByPassageBO.setLocationName(locationName);
            createSowTaskByPassageBO.setSowOrderPOList(sowOrderPOList);
            createSowTaskByPassageBO.setDriverName(driverName);
            createSowTaskByPassageBO.setAllocationFlag(allocationFlag);
            createSowTaskByPassageBO.setOriCreateDTO(oriCreateDTO);
            createSowTaskByPassageBO.setExistSowTaskPO(existSowTaskPO);
            createSowTaskByPassageBO.setNeedRecheck(needRecheck);
            return createSowTaskByPassageBO;
        }
    }
}
