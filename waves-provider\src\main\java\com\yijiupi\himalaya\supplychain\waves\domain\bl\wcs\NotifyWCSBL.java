package com.yijiupi.himalaya.supplychain.waves.domain.bl.wcs;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskChangeNotifyBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.AppendSowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.ReplenishmentTaskItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.LocationAreaService;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.ReplenishmentTaskItemDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.WCSTaskCreateConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.mq.PushMsgMQ;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByRefOrderNoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum;
import com.yijiupi.himalaya.supplychain.wcs.dto.task.DPSPickTaskBatchCreateDTO;
import com.yijiupi.himalaya.supplychain.wcs.service.task.IDPSTaskService;

/**
 * <AUTHOR>
 * @since 2022-10-22 15:35
 */
@Service
public class NotifyWCSBL {

    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private SowTaskMapper sowTaskMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private Gson gson;
    @Autowired
    private PushMsgMQ pushMsgMQ;
    @Resource
    private RedisBoxCacheBL redisBoxCacheBL;
    @Autowired
    private GlobalCache globalCache;
    @Autowired
    private BatchTaskChangeNotifyBL batchTaskChangeNotifyBL;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    @Reference
    private LocationAreaService locationAreaService;
    @Reference
    private IDPSTaskService idpsTaskService;
    private static final Logger LOG = LoggerFactory.getLogger(NotifyWCSBL.class);

    @Transactional(rollbackFor = Exception.class)
    public void notifyBatchCreateByOrders(BatchCreateByRefOrderNoDTO dto) {
        createWCSTaskByOrders(dto);
        notifyDigitalBatchCreateByOrders(dto);
    }

    @Transactional(rollbackFor = Exception.class)
    public void notifyBatchCreateByOrderIds(BatchCreateDTO dto) {
        createWCSTaskByOrderIds(dto);
        notifyDigitalBatchCreateByOrderIds(dto);
    }

    @Transactional(rollbackFor = Exception.class)
    public void notifyByAppendBatch(AppendSowTaskDTO appendSowTaskDTO) {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setOrderIdList(appendSowTaskDTO.getOutStockOrderIds());
        batchCreateDTO.setWarehouseId(appendSowTaskDTO.getWarehouseId());
        batchCreateDTO.setOperateUserId(appendSowTaskDTO.getOptUserId());
        batchCreateDTO.setOperateUser(globalCache.getAdminTrueName(appendSowTaskDTO.getOptUserId()));
        createWCSTaskByAppend(batchCreateDTO);
        notifyDigitalBatchByAppend(batchCreateDTO);
    }

    // 暂时只处理按产品拣货的
    @Transactional(rollbackFor = Exception.class)
    public void createWCSTaskByIds(List<String> batchTaskIds, String optUserName) {
        LOG.info("创建DPS任务，入参拣货任务id列表 : {}", gson.toJson(batchTaskIds));
        if (CollectionUtils.isEmpty(batchTaskIds)) {
            return;
        }
        List<BatchTaskPO> batchTaskList = batchTaskMapper.findTasksByBatchTaskId(batchTaskIds);

        batchTaskList =
            batchTaskList.stream().filter(m -> BatchTaskPickPatternEnum.机器人拣货.getType().equals(m.getPickPattern()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(batchTaskList)) {
            return;
        }

        BatchTaskPO batchTaskPO = batchTaskList.get(0);
        Integer warehouseId = batchTaskPO.getWarehouseId();
        Integer orgId = Integer.valueOf(batchTaskPO.getOrgId());

        List<BatchTaskItemPO> itemList = batchTaskItemMapper.findBatchTaskItemDTOListByTaskIds(batchTaskIds);

        List<Long> sowTaskIds = batchTaskList.stream().map(BatchTaskPO::getSowTaskId).collect(Collectors.toList());

        List<SowTaskPO> sowTaskList = sowTaskMapper.findSowTaskByIds(sowTaskIds);

        // List<Long> skuIds = itemList.stream().map(BatchTaskItemPO::getSkuId).distinct().collect(Collectors.toList());
        // Map<Long, VesselDetailsDTO> vesselMap = locationAreaService.listSKUBesselDetails(skuIds, warehouseId);
        List<String> locationIds = itemList.stream().map(BatchTaskItemPO::getLocationId).filter(Objects::nonNull)
            .distinct().map(String::valueOf).collect(Collectors.toList());
        List<LocationReturnDTO> areaLocationList = locationAreaService.findLocationAreaListById(locationIds);

        Map<Long, LocationReturnDTO> areaMap = new HashMap<>();

        if (!org.springframework.util.CollectionUtils.isEmpty(areaLocationList)) {
            areaMap.putAll(areaLocationList.stream().collect(Collectors.toMap(LocationReturnDTO::getId, v -> v)));
        }

        DPSPickTaskBatchCreateDTO createDTO =
            WCSTaskCreateConvertor.convert(batchTaskList, itemList, areaMap, sowTaskList, optUserName);
        idpsTaskService.batchCreatePickTask(createDTO);

        List<ReplenishmentTaskItemDTO> replenishmentTaskItemDTOS =
            ReplenishmentTaskItemDTOConvertor.convert(itemList, areaMap, warehouseId);
        LOG.info("创建机器人完成，{}; 创建补货任务：{}", JSON.toJSONString(createDTO), JSON.toJSONString(replenishmentTaskItemDTOS));
        // 发补货消息
        pushMsgMQ.pushReplenishmentAdd(ReplenishmentTaskItemDTOConvertor.convert(itemList, areaMap, warehouseId));
    }

    @Transactional(rollbackFor = Exception.class)
    public void createWCSTaskByOrders(BatchCreateByRefOrderNoDTO dto) {
        if (BooleanUtils.isFalse(openRobot(dto.getWarehouseId()))) {
            return;
        }
        try {
            List<String> refOrderNoList = WCSTaskCreateConvertor.getOrderNoList(dto);

            List<String> batchTaskIds = getBatchTaskIds(refOrderNoList, Collections.emptyList(), dto.getWarehouseId());
            if (CollectionUtils.isEmpty(batchTaskIds)) {
                return;
            }

            createWCSTaskByIds(batchTaskIds, dto.getOperateUser());
        } catch (Exception e) {
            LOG.error("创建WCS任务失败, 入参:{} ", gson.toJson(dto), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void createWCSTaskByOrderIds(BatchCreateDTO dto) {
        if (BooleanUtils.isFalse(openRobot(dto.getWarehouseId()))) {
            return;
        }
        try {
            List<String> batchTaskIds = getBatchTaskIds(Collections.emptyList(),
                dto.getOrderIdList().stream().map(Long::valueOf).collect(Collectors.toList()), dto.getWarehouseId());
            if (CollectionUtils.isEmpty(batchTaskIds)) {
                return;
            }

            createWCSTaskByIds(batchTaskIds, dto.getOperateUser());
        } catch (Exception e) {
            LOG.error("创建WCS任务失败, 入参: {}", gson.toJson(dto), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void createWCSTaskByAppend(BatchCreateDTO dto) {
        if (BooleanUtils.isFalse(openRobot(dto.getWarehouseId()))) {
            return;
        }
        try {
            List<String> batchTaskIds = getAppendBatchTaskIds(
                dto.getOrderIdList().stream().map(Long::valueOf).collect(Collectors.toList()), dto.getWarehouseId());
            if (CollectionUtils.isEmpty(batchTaskIds)) {
                return;
            }

            createWCSTaskByIds(batchTaskIds, dto.getOperateUser());
        } catch (Exception e) {
            LOG.error("创建WCS任务失败, 入参: {}", gson.toJson(dto), e);
        }
    }

    private boolean openRobot(Integer warehouseId) {
        Boolean open = globalCache.checkWarehouseIsRobotPicking(warehouseId);

        return open;
    }

    @Transactional(rollbackFor = Exception.class)
    public void notifyDigitalBatchCreateByOrders(BatchCreateByRefOrderNoDTO dto) {
        if (BooleanUtils.isFalse(globalCache.openDigitalTag(dto.getWarehouseId()))) {
            return;
        }
        try {
            List<String> refOrderNoList = WCSTaskCreateConvertor.getOrderNoList(dto);

            List<String> batchTaskIds = getBatchTaskIds(refOrderNoList, Collections.emptyList(), dto.getWarehouseId());
            if (CollectionUtils.isEmpty(batchTaskIds)) {
                return;
            }
            List<BatchTaskPO> batchTaskList = batchTaskMapper.findTasksByBatchTaskId(batchTaskIds);
            batchTaskChangeNotifyBL.notifyDigitalBatchTaskCreate(batchTaskList);
        } catch (Exception e) {
            LOG.error("通知电子标签信息失败, 入参:{} ", gson.toJson(dto), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void notifyDigitalBatchCreateByOrderIds(BatchCreateDTO dto) {
        if (BooleanUtils.isFalse(globalCache.openDigitalTag(dto.getWarehouseId()))) {
            return;
        }
        try {
            List<String> batchTaskIds = getBatchTaskIds(Collections.emptyList(),
                dto.getOrderIdList().stream().map(Long::valueOf).collect(Collectors.toList()), dto.getWarehouseId());
            if (CollectionUtils.isEmpty(batchTaskIds)) {
                return;
            }

            List<BatchTaskPO> batchTaskList = batchTaskMapper.findTasksByBatchTaskId(batchTaskIds);
            batchTaskChangeNotifyBL.notifyDigitalBatchTaskCreate(batchTaskList);
        } catch (Exception e) {
            LOG.error("通知电子标签信息失败, 入参: {}", gson.toJson(dto), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void notifyDigitalBatchByAppend(BatchCreateDTO dto) {
        if (BooleanUtils.isFalse(globalCache.openDigitalTag(dto.getWarehouseId()))) {
            return;
        }
        try {
            List<String> batchTaskIds = getAppendBatchTaskIds(
                dto.getOrderIdList().stream().map(Long::valueOf).collect(Collectors.toList()), dto.getWarehouseId());
            if (CollectionUtils.isEmpty(batchTaskIds)) {
                return;
            }

            List<BatchTaskPO> batchTaskList = batchTaskMapper.findTasksByBatchTaskId(batchTaskIds);
            batchTaskChangeNotifyBL.notifyDigitalBatchTaskCreate(batchTaskList);
        } catch (Exception e) {
            LOG.error("通知电子标签信息失败, 入参: {}", gson.toJson(dto), e);
        }
    }

    private List<String> getBatchTaskIds(List<String> refOrderNoList, List<Long> orderIds, Integer warehouseId) {
        if (CollectionUtils.isEmpty(refOrderNoList) && CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }

        List<String> batchIds = null;
        if (CollectionUtils.isNotEmpty(refOrderNoList)) {
            batchIds = outStockOrderMapper.findByBatchIdByRefNos(refOrderNoList, warehouseId);
        }

        if (CollectionUtils.isNotEmpty(orderIds)) {
            batchIds = outStockOrderMapper.findByBatchIdByIds(orderIds, warehouseId);
        }

        if (CollectionUtils.isEmpty(batchIds)) {
            return Collections.emptyList();
        }

        List<String> batchTaskIds = batchTaskMapper.findIdByBatchIds(batchIds);

        return batchTaskIds;
    }

    private List<String> getAppendBatchTaskIds(List<Long> orderIds, Integer warehouseId) {
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(orderIds);
        List<String> batchTaskIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getBatchTaskId).distinct()
            .collect(Collectors.toList());

        return batchTaskIds;
    }

    /**
     * 清除 wcs 物料箱缓存数据
     */
    @Async
    public void cleanCachedBoxInfo(SowTaskPO sowTask) {
        redisBoxCacheBL.cleanCachedBoxInfo(sowTask);
    }

}
