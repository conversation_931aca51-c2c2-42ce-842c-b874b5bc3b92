<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoDetailMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="INTEGER" property="orgId"/>
        <result column="TaskInfo_Id" jdbcType="BIGINT" property="taskInfoId"/>
        <result column="SecOwner_Id" jdbcType="BIGINT" property="secOwnerId"/>
        <result column="Owner_Id" jdbcType="BIGINT" property="ownerId"/>
        <result column="ProductSpecification_Id" jdbcType="BIGINT" property="productSpecificationId"/>
        <result column="UnitTotalCount" jdbcType="DECIMAL" property="unitTotalCount"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="OriginalUnitTotalCount" property="originalUnitTotalCount" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Org_Id, TaskInfo_Id, SecOwner_Id, Owner_Id, ProductSpecification_Id, UnitTotalCount,
        CreateTime, CreateUser, LastUpdateTime, LastUpdateUser,OriginalUnitTotalCount
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from orderitemtaskinfodetail
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from orderitemtaskinfodetail
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertBatch" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO">
        INSERT INTO orderitemtaskinfodetail (
        Id,
        Org_Id,
        TaskInfo_Id,
        SecOwner_Id,
        Owner_Id,
        ProductSpecification_Id,
        UnitTotalCount,
        CreateUser,
        OriginalUnitTotalCount
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.orgId,jdbcType=INTEGER},
            #{item.taskInfoId,jdbcType=BIGINT},
            #{item.secOwnerId,jdbcType=BIGINT},
            #{item.ownerId,jdbcType=BIGINT},
            #{item.productSpecificationId,jdbcType=BIGINT},
            #{item.unitTotalCount,jdbcType=DECIMAL},
            #{item.createUser,jdbcType=VARCHAR},
            #{item.originalUnitTotalCount,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        UPDATE orderitemtaskinfodetail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="UnitTotalCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.unitTotalCount != null ">
                        when id=#{item.id} then #{item.unitTotalCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.unitTotalCount == null ">
                        when id=#{item.id} then UnitTotalCount
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateBatchCount" parameterType="java.util.List">
        UPDATE orderitemtaskinfodetail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="UnitTotalCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.unitTotalCount != null ">
                        when id=#{item.id} then UnitTotalCount + #{item.unitTotalCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.unitTotalCount == null ">
                        when id=#{item.id} then UnitTotalCount
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO">
        insert into orderitemtaskinfodetail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="orgId != null">
                Org_Id,
            </if>
            <if test="taskInfoId != null">
                TaskInfo_Id,
            </if>
            <if test="secOwnerId != null">
                SecOwner_Id,
            </if>
            <if test="ownerId != null">
                Owner_Id,
            </if>
            <if test="productSpecificationId != null">
                ProductSpecification_Id,
            </if>
            <if test="unitTotalCount != null">
                UnitTotalCount,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="taskInfoId != null">
                #{taskInfoId,jdbcType=BIGINT},
            </if>
            <if test="secOwnerId != null">
                #{secOwnerId,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="productSpecificationId != null">
                #{productSpecificationId,jdbcType=BIGINT},
            </if>
            <if test="unitTotalCount != null">
                #{unitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO">
        update orderitemtaskinfodetail
        <set>
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="taskInfoId != null">
                TaskInfo_Id = #{taskInfoId,jdbcType=BIGINT},
            </if>
            <if test="secOwnerId != null">
                SecOwner_Id = #{secOwnerId,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                Owner_Id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="productSpecificationId != null">
                ProductSpecification_Id = #{productSpecificationId,jdbcType=BIGINT},
            </if>
            <if test="unitTotalCount != null">
                UnitTotalCount = #{unitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteByIds">
        delete from orderitemtaskinfodetail
        where Id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>

    <select id="selectIdsByTaskInfoIds" parameterType="java.lang.Long" resultType="java.lang.Long">
        select id from orderitemtaskinfodetail
        where taskInfo_id in
        <foreach collection="taskInfoIds" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

</mapper>