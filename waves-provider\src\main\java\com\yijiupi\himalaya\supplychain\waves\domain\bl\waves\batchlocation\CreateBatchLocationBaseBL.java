package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.constants.WarehouseConfigConstants;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.DefaultLocationConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.enums.DefaultTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.service.IDefaultLocationConfigService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationBusinessTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.OutStockOrderHandleLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CreateBatchLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.RobotPickConstants;
import com.yijiupi.supplychain.serviceutils.constant.DeliveryOrderConstant;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;

/**
 * <AUTHOR>
 * @title: CreateBatchLocationBaseBL
 * @description:
 * @date 2022-09-26 14:34
 */
public abstract class CreateBatchLocationBaseBL {

    @Reference
    protected WarehouseConfigService warehouseConfigService;
    @Reference
    protected IProductLocationService iProductLocationService;
    @Reference(timeout = 100000)
    protected IBatchInventoryQueryService batchInventoryQueryService;
    @Reference
    private IDefaultLocationConfigService iDefaultLocationConfigService;
    @Reference
    private ILocationService iLocationService;

    protected static final Logger LOG = LoggerFactory.getLogger(CreateBatchLocationBaseBL.class);

    public boolean support(CreateBatchLocationBO bo) {
        if (BooleanUtils.isTrue(isOpenRobotCargo(bo))) {
            return Boolean.FALSE;
        }

        return doSupport(bo);
    }

    protected abstract boolean doSupport(CreateBatchLocationBO bo);

    public OutStockOrderHandleLocationBO setLocation(List<OutStockOrderPO> outStockOrderList,
        CreateBatchLocationBO bo) {
        boolean isNeedHandleNpProductPick = needHandleNp(outStockOrderList, bo);
        if (BooleanUtils.isFalse(isNeedHandleNpProductPick)) {
            return OutStockOrderHandleLocationBO.getDefaultNormal(handleLocation(outStockOrderList, bo));
        }

        // 获取内配暂存货区
        DefaultLocationConfigDTO defaultLocationConfigDTO = getDefaultLocationConfigByWarehouseId(bo.getWarehouseId());
        if (Objects.isNull(defaultLocationConfigDTO)) {
            return OutStockOrderHandleLocationBO.getDefaultNormal(handleLocation(outStockOrderList, bo));
        }
        List<OutStockOrderPO> npOrderList =
            outStockOrderList.stream().filter(this::isNpOrder).collect(Collectors.toList());
        List<OutStockOrderPO> notNpOrderList =
            outStockOrderList.stream().filter(m -> !isNpOrder(m)).collect(Collectors.toList());
        // 处理内配单
        handleNpLocationInfo(npOrderList, defaultLocationConfigDTO);
        // 处理普通订单
        doSetLocation(notNpOrderList, bo);
        // 【促销订单】设置关联货位
        resetPromotionOrderItem(notNpOrderList, bo.getWarehouseId());

        return OutStockOrderHandleLocationBO.getDefaultBoth(notNpOrderList, npOrderList);
    }

    private List<OutStockOrderPO> handleLocation(List<OutStockOrderPO> outStockOrderList, CreateBatchLocationBO bo) {
        List<OutStockOrderPO> locationOrderList = doSetLocation(outStockOrderList, bo);
        // 【促销订单】设置关联货位
        resetPromotionOrderItem(locationOrderList, bo.getWarehouseId());

        return locationOrderList;
    }

    // 1、按产品拣货；2、开了配置；3、没开货位库存
    protected boolean needHandleNp(List<OutStockOrderPO> outStockOrderList, CreateBatchLocationBO bo) {
        WavesStrategyBO wavesStrategyBO = bo.getWavesStrategyDTO();
        if (WavesStrategyConstants.PICKINGTYPE_PRODUCT != wavesStrategyBO.getPickingType().intValue()) {
            return Boolean.FALSE;
        }

        boolean openFrontWarehouseOpenNPProductPick = bo.getOpenFrontWarehouseOpenNPProductPick();
        if (BooleanUtils.isFalse(openFrontWarehouseOpenNPProductPick)) {
            return Boolean.FALSE;
        }
        WarehouseConfigDTO warehouseConfigDTO = bo.getWarehouseConfigDTO();

        if (WarehouseConfigConstants.CARGO_STOCK_ON.equals(warehouseConfigDTO.getIsOpenCargoStock())) {
            return Boolean.FALSE;
        }

        return outStockOrderList.stream().anyMatch(this::isNpOrder);
    }

    private boolean isNpOrder(OutStockOrderPO m) {
        return OrderConstant.ALLOT_TYPE_ALLOCATION.equals(m.getAllotType())
            && OutBoundTypeEnum.SALE_ORDER.getCode().byteValue() == m.getOutBoundType();
    }

    private void handleNpLocationInfo(List<OutStockOrderPO> npOrderList,
        DefaultLocationConfigDTO defaultLocationConfigDTO) {
        npOrderList.stream().flatMap(m -> m.getItems().stream()).forEach(m -> {
            m.setLocationId(defaultLocationConfigDTO.getTemporaryLocationId());
            m.setLocationName(defaultLocationConfigDTO.getTemporaryLocationName());
        });
    }

    public abstract List<OutStockOrderPO> doSetLocation(List<OutStockOrderPO> outStockOrderList,
        CreateBatchLocationBO bo);

    protected boolean isOpenRobotCargo(CreateBatchLocationBO bo) {
        if (bo.getWavesStrategyDTO().getIsOpenSecondSort()) {
            return Boolean.FALSE;
        }
        if (BooleanUtils.isFalse(
            RobotPickConstants.isWarehouseOpenLargePick(bo.getWavesStrategyDTO(), bo.getWarehouseConfigDTO()))) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * 将订单项按详细拆分
     */
    protected void setOutStockOrderItemSplitDetail(List<OutStockOrderPO> outStockOrderPOList) {
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }
        outStockOrderPOList.forEach(order -> {
            if (order != null && !CollectionUtils.isEmpty(order.getItems())) {
                List<OutStockOrderItemPO> newItemPOList = new ArrayList<>();
                order.getItems().forEach(item -> {
                    if (item == null) {
                        return;
                    }
                    if (!CollectionUtils.isEmpty(item.getItemDetails())) {
                        // 按detail复制多个item
                        item.getItemDetails().forEach(detail -> {
                            OutStockOrderItemPO outStockOrderItemPO = new OutStockOrderItemPO();
                            BeanUtils.copyProperties(item, outStockOrderItemPO);
                            outStockOrderItemPO.setItemDetailId(detail.getId());
                            outStockOrderItemPO.setSecOwnerId(detail.getSecOwnerId());
                            outStockOrderItemPO.setUnittotalcount(detail.getUnitTotalCount());
                            BigDecimal[] pickUpCountRemainder = outStockOrderItemPO.getUnittotalcount()
                                .divideAndRemainder(outStockOrderItemPO.getSpecquantity());
                            outStockOrderItemPO.setPackagecount(pickUpCountRemainder[0]);
                            outStockOrderItemPO.setUnitcount(pickUpCountRemainder[1]);
                            OutStockOrderItemDetailPO detailPO = new OutStockOrderItemDetailPO();
                            BeanUtils.copyProperties(detail, detailPO);
                            List<OutStockOrderItemDetailPO> detailList = new ArrayList<>();
                            detailList.add(detailPO);
                            outStockOrderItemPO.setItemDetails(detailList);
                            newItemPOList.add(outStockOrderItemPO);
                        });
                    } else {
                        newItemPOList.add(item);
                    }
                });
                if (!CollectionUtils.isEmpty(newItemPOList)) {
                    order.setItems(newItemPOList);
                }
            }
        });
    }

    /**
     * 将订单项按详细拆分
     */
    protected void setOutStockOrderItemDetail(List<OutStockOrderPO> outStockOrderPOList) {
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }
        outStockOrderPOList.forEach(order -> {
            if (order != null && !CollectionUtils.isEmpty(order.getItems())) {
                List<OutStockOrderItemPO> newItemPOList = new ArrayList<>();
                order.getItems().forEach(item -> {
                    if (item == null) {
                        return;
                    }
                    if (!CollectionUtils.isEmpty(item.getItemDetails())) {
                        // 按detail复制多个item
                        item.getItemDetails().forEach(detail -> {
                            OutStockOrderItemPO outStockOrderItemPO = new OutStockOrderItemPO();
                            BeanUtils.copyProperties(item, outStockOrderItemPO);
                            outStockOrderItemPO.setItemDetailId(detail.getId());
                            outStockOrderItemPO.setSecOwnerId(detail.getSecOwnerId());
                            outStockOrderItemPO.setUnittotalcount(detail.getUnitTotalCount());
                            BigDecimal[] pickUpCountRemainder = outStockOrderItemPO.getUnittotalcount()
                                .divideAndRemainder(outStockOrderItemPO.getSpecquantity());
                            outStockOrderItemPO.setPackagecount(pickUpCountRemainder[0]);
                            outStockOrderItemPO.setUnitcount(pickUpCountRemainder[1]);
                            newItemPOList.add(outStockOrderItemPO);
                        });
                    } else {
                        newItemPOList.add(item);
                    }
                });
                if (!CollectionUtils.isEmpty(newItemPOList)) {
                    order.setItems(newItemPOList);
                }
            }
        });
    }

    /**
     * 按配置货位拣货出库的时候，如果产品配置了多个货位，优先使用零拣位和分拣位，再存储位
     *
     * @return
     */
    protected ProductLoactionItemDTO getProductLocationItemDTO(List<ProductLoactionItemDTO> productLocationItemDTOS) {
        if (CollectionUtils.isEmpty(productLocationItemDTOS)) {
            return null;
        }

        ProductLoactionItemDTO productLoactionItemDTO = null;
        // 按配置货位拣货出库的时候，如果产品配置了多个货位，优先使用零拣位和分拣位，再存储位
        if (productLocationItemDTOS.stream()
            .anyMatch(p -> p.getSubcategory() != null && LocationEnum.零拣位.getType() == p.getSubcategory().intValue())) {
            Optional<ProductLoactionItemDTO> optional = productLocationItemDTOS.stream()
                .filter(p -> p.getSubcategory() != null && LocationEnum.零拣位.getType() == p.getSubcategory().intValue())
                .findFirst();
            if (optional.isPresent()) {
                productLoactionItemDTO = optional.get();
            }
        } else if (productLocationItemDTOS.stream()
            .anyMatch(p -> p.getSubcategory() != null && LocationEnum.分拣位.getType() == p.getSubcategory().intValue())) {
            Optional<ProductLoactionItemDTO> optional = productLocationItemDTOS.stream()
                .filter(p -> p.getSubcategory() != null && LocationEnum.分拣位.getType() == p.getSubcategory().intValue())
                .findFirst();
            if (optional.isPresent()) {
                productLoactionItemDTO = optional.get();
            }
        } else if (productLocationItemDTOS.stream()
            .anyMatch(p -> p.getSubcategory() != null && LocationEnum.存储位.getType() == p.getSubcategory().intValue())) {
            Optional<ProductLoactionItemDTO> optional = productLocationItemDTOS.stream()
                .filter(p -> p.getSubcategory() != null && LocationEnum.存储位.getType() == p.getSubcategory().intValue())
                .findFirst();
            if (optional.isPresent()) {
                productLoactionItemDTO = optional.get();
            }
        }
        if (productLoactionItemDTO == null) {
            productLoactionItemDTO = productLocationItemDTOS.get(0);
        }
        return productLoactionItemDTO;
    }

    /**
     * 给没开启货位库存的产品设置最老的生产日期和批次时间
     *
     * @param outStockOrderPOList
     */
    protected void setProductDateAndBatchTime(List<OutStockOrderPO> outStockOrderPOList) {
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }
        Integer warehouseId = outStockOrderPOList.get(0).getWarehouseId();
        Set<Long> skuIds = new HashSet<>();
        outStockOrderPOList.forEach(order -> {
            if (order != null && !CollectionUtils.isEmpty(order.getItems())) {
                order.getItems().forEach(item -> {
                    if (item != null && item.getSkuid() != null) {
                        skuIds.add(item.getSkuid());
                    }
                });
            }
        });
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        // 查询货位库存
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setWarehouseId(warehouseId);
        batchInventoryQueryDTO.setSkuIds(new ArrayList<>(skuIds));
        PageList<BatchInventoryDTO> pageList =
            batchInventoryQueryService.findBatchInventoryListBatchNew(batchInventoryQueryDTO);
        if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
            return;
        }
        // 分别找到订单项skuid已分配货位的批次库存
        outStockOrderPOList.forEach(order -> {
            if (order != null && !CollectionUtils.isEmpty(order.getItems())) {
                order.getItems().forEach(item -> {
                    if (item != null && item.getSkuid() != null) {
                        List<BatchInventoryDTO> fitlerList = pageList.getDataList().stream()
                            .filter(p -> Objects.equals(p.getProductSkuId(), item.getSkuid()))
                            .collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(fitlerList)) {
                            // 存在多条时，按生产日期+批次日期先入先出排序
                            if (fitlerList.size() > 1) {
                                fitlerList = fitlerList.stream()
                                    .sorted(Comparator.nullsFirst(Comparator
                                        .comparing(BatchInventoryDTO::getProductionDate,
                                            Comparator.nullsFirst(Date::compareTo))
                                        .thenComparing(BatchInventoryDTO::getBatchTime,
                                            Comparator.nullsFirst(Date::compareTo))))
                                    .collect(Collectors.toList());
                            }
                            item.setProductionDate(fitlerList.get(0).getProductionDate());
                            item.setBatchTime(fitlerList.get(0).getBatchTime());
                        }
                    }
                });
            }
        });
    }

    /**
     * 查询产品关联货位
     *
     * @param outStockOrderList
     * @param bo
     * @return
     */
    protected Map<Long, List<ProductLoactionItemDTO>> getRecommendLocationMap(List<OutStockOrderPO> outStockOrderList,
        CreateBatchLocationBO bo) {
        // 查询产品关联货位
        List<Long> productSkuIds = new ArrayList<>();
        List<ProductLoactionItemDTO> locationBySkuId = new ArrayList<>();
        for (OutStockOrderPO order : outStockOrderList) {
            if (!CollectionUtils.isEmpty(order.getItems())) {
                productSkuIds
                    .addAll(order.getItems().stream().map(p -> p.getSkuid()).distinct().collect(Collectors.toList()));
            }
        }
        if (productSkuIds.size() > 0) {
            productSkuIds = productSkuIds.stream().distinct().collect(Collectors.toList());
            locationBySkuId = iProductLocationService.findLocationBySkuId(bo.getWarehouseId(), productSkuIds);
        }
        Map<Long, List<ProductLoactionItemDTO>> productLocationMap =
            locationBySkuId.stream().collect(Collectors.groupingBy(ProductLoactionItemDTO::getProductSkuId));

        return productLocationMap;
    }

    /**
     * 自定义优先级获取拣货位
     *
     * @return
     */
    protected ProductLoactionItemDTO getProductLoactionItemDTOByCondition(
        List<ProductLoactionItemDTO> productLoactionItemDTOS, List<Integer> firstLocationList,
        List<Integer> secondLocationList) {
        if (CollectionUtils.isEmpty(productLoactionItemDTOS)) {
            return null;
        }
        // 第一优先级
        Optional<ProductLoactionItemDTO> firstOptional =
            productLoactionItemDTOS.stream().filter(p -> p.getSubcategory() != null && firstLocationList != null
                && firstLocationList.contains(p.getSubcategory().intValue())).findFirst();
        if (firstOptional.isPresent()) {
            return firstOptional.get();
        }
        // 第二优先级
        Optional<ProductLoactionItemDTO> secondOptional =
            productLoactionItemDTOS.stream().filter(p -> p.getSubcategory() != null && secondLocationList != null
                && secondLocationList.contains(p.getSubcategory().intValue())).findFirst();
        return secondOptional.orElseGet(() -> productLoactionItemDTOS.get(0));
        // 随便取一个
    }

    protected void setOrderItemByLocation(OutStockOrderItemPO itemPO, ProductLoactionItemDTO productLoactionDTO) {
        if (productLoactionDTO != null) {
            itemPO.setLocationId(productLoactionDTO.getLocationId());
            itemPO.setLocationName(productLoactionDTO.getLocationName());
            itemPO.setSubCategory(productLoactionDTO.getSubcategory());
            itemPO.setAreaId(productLoactionDTO.getAreaId());
            itemPO.setAreaName(productLoactionDTO.getAreaName());
        }
    }

    private DefaultLocationConfigDTO getDefaultLocationConfigByWarehouseId(Integer warehouseId) {
        List<DefaultLocationConfigDTO> defaultLocationConfigDTOList =
            iDefaultLocationConfigService.findDefaultLocationConfigByWarehouseId(warehouseId);
        Optional<DefaultLocationConfigDTO> optional = defaultLocationConfigDTOList.stream()
            .filter(location -> Objects.equals(DefaultTypeEnum.内配单收货暂存位.getType(), location.getTemporaryLocationType()))
            .findFirst();

        return optional.orElse(null);
    }

    // 【促销订单】重新设置产品关联货位（TODO 内配单是否也一致）
    private void resetPromotionOrderItem(List<OutStockOrderPO> outStockOrderPOList, Integer warehouseId) {
        List<OutStockOrderItemPO> promotionItemList = outStockOrderPOList.stream()
            .filter(
                o -> !Objects.equals(o.getDeliveryMarkState(), DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue()))
            .flatMap(o -> o.getItems().stream())
            .filter(item -> ConditionStateEnum.是.getType().equals(item.getIsAdvent())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(promotionItemList)) {
            return;
        }

        List<Long> skuIds =
            promotionItemList.stream().map(OutStockOrderItemPO::getSkuid).distinct().collect(Collectors.toList());
        List<ProductLoactionItemDTO> productLocationItemDTOS =
            iProductLocationService.findLocationBySkuId(warehouseId, skuIds);
        if (CollectionUtils.isEmpty(productLocationItemDTOS)) {
            LOG.warn("促销订单未设置货位：{}", JSON.toJSONString(promotionItemList));
            return;
        }
        List<Long> locationIds = productLocationItemDTOS.stream().map(ProductLoactionItemDTO::getLocationId).distinct()
            .collect(Collectors.toList());
        List<LoactionDTO> loactionDTOList = iLocationService.findLocationByIds(locationIds);
        Map<Long, List<ProductLoactionItemDTO>> productLocationItemGroupMap =
            productLocationItemDTOS.stream().collect(Collectors.groupingBy(ProductLoactionItemDTO::getProductSkuId));

        loactionDTOList =
            loactionDTOList.stream().filter(m -> LocationBusinessTypeEnum.促销.getType().equals(m.getBusinessType()))
                .collect(Collectors.toList());
        Map<Long, LoactionDTO> loactionDTOMap =
            loactionDTOList.stream().collect(Collectors.toMap(LoactionDTO::getId, v -> v));
        for (OutStockOrderItemPO promotionItem : promotionItemList) {
            List<ProductLoactionItemDTO> productLocationItemList =
                productLocationItemGroupMap.get(promotionItem.getSkuid());
            if (CollectionUtils.isEmpty(productLocationItemList)) {
                continue;
            }

            Optional<LoactionDTO> locationOptional =
                productLocationItemList.stream().filter(m -> Objects.nonNull(loactionDTOMap.get(m.getLocationId())))
                    .map(m -> loactionDTOMap.get(m.getLocationId())).findFirst();
            if (!locationOptional.isPresent()) {
                continue;
            }

            LoactionDTO loactionDTO = locationOptional.get();
            promotionItem.setLocationId(loactionDTO.getId());
            promotionItem.setLocationName(loactionDTO.getName());
            promotionItem.setSubCategory(loactionDTO.getSubcategory());
            promotionItem.setAreaId(loactionDTO.getAreaId());
            promotionItem.setAreaName(loactionDTO.getArea());
        }

    }

}
