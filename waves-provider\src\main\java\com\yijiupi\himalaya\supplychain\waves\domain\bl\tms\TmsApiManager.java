package com.yijiupi.himalaya.supplychain.waves.domain.bl.tms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.ordercenter.OrderCenterNotifyBL;
import com.yijiupi.himalaya.supplychain.waves.domain.model.tms.DeliveryOrderInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.tms.DeliveryOrderQueryConParam;
import com.yijiupi.himalaya.supplychain.waves.domain.model.tms.PackageCancelParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2023-09-07 13:41
 **/
@Component
public class TmsApiManager {

    @SuppressWarnings("HttpUrlsUsage")
    private static final String BASE_URL = "http://in-apitms.yjp.com/tms";
    private static final String TMS_DELIVERY_ORDER_QUERY = BASE_URL + "/tmsdeliveryorder/common/IDeliveryOrderQueryService/findByCon";
    private static final String TMS_PACKAGE_CANCEL_POST = BASE_URL + "/tmscombine/packagebox/IPackageBoxSyncDeleteService/deletePackageBox";
    private static final String TMS_DELETE_PACKAGE_ITEM = BASE_URL + "/tmsdeliveryorder/common/IOrderPackageBoxItemService/deleteByOrderItemIdList";
    private static final Logger logger = LoggerFactory.getLogger(TmsApiManager.class);

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private OrderCenterNotifyBL orderCenterNotifyBL;

    /**
     * tms 通过指定条件查找运单接口, 不会返回 null, 不会向外抛出异常 (打日志), 出错返回空集合
     *
     * @param param 查询条件
     * @return 查询结果
     */
    public List<DeliveryOrderInfoDTO> findByCon(DeliveryOrderQueryConParam param) {
        return runCaching(() -> {
            String response = doPost(TMS_DELIVERY_ORDER_QUERY, param);
            return JSON.parseObject(response, new TypeReference<List<DeliveryOrderInfoDTO>>() {
            });
        }, "调用 tms 接口查询运单出错: ", Collections::emptyList);
    }

    /**
     * 通过 businessId 查找 tms 运单
     *
     * @param businessId  businessId
     * @param warehouseId 仓库 id
     * @return 查找结果, 可能为空
     */
    public Optional<DeliveryOrderInfoDTO> findByBusinessId(Long businessId, Integer warehouseId) {
        DeliveryOrderQueryConParam param = new DeliveryOrderQueryConParam();
        param.setOrderIdList(Collections.singletonList(businessId));
        param.setDeliveryCenterIds(Collections.singletonList(warehouseId));
        List<DeliveryOrderInfoDTO> result = findByCon(param);
        if (CollectionUtils.isEmpty(result)) {
            return Optional.empty();
        }
        return Optional.ofNullable(result.get(0));
    }

    /**
     * 按订单删除打包信息
     *
     * @param refOrderNos 订单号
     * @param warehouseId 仓库 id
     */
    public void removePackage(Collection<String> refOrderNos, Integer warehouseId) {
        removePackage(refOrderNos, warehouseId, null);
    }

    public void removePackage(Collection<String> refOrderNos, Integer warehouseId, Integer opUserId) {
        logger.info("取消包装通知 tms: {}, {}, {}", refOrderNos, warehouseId, opUserId);
        if (CollectionUtils.isEmpty(refOrderNos)) {
            return;
        }
        PackageCancelParam body = PackageCancelParam.of(warehouseId, refOrderNos);
        runCaching(() -> doPost(TMS_PACKAGE_CANCEL_POST, body, false),
                "取消包装通知 tms 失败", null
        );
        if (opUserId != null) {
            orderCenterNotifyBL.syncRemovePackageTrace(refOrderNos, warehouseId, opUserId);
        }
    }

    /**
     * 按订单项删除打包信息
     *
     * @param orderItemIds 订单项 id (中台 id)
     */
    public void removePackageByItem(Collection<String> orderItemIds) {
        logger.info("取消包装通知 tms, removePackageByItem: {}", orderItemIds);
        if (CollectionUtils.isEmpty(orderItemIds)) {
            return;
        }
        runCaching(() -> doPost(TMS_DELETE_PACKAGE_ITEM, orderItemIds, false),
                "取消包装通知 tms, removePackageByItem 失败", null
        );
    }

    /**
     * 运行一段代码并捕获所有异常
     *
     * @param supplier     要运行的代码
     * @param errorMessage 异常日志消息
     * @param ifError      出错时返回值
     * @param <T>          返回值类型
     */
    @Nullable
    private <T> T runCaching(Supplier<T> supplier, String errorMessage, @Nullable Supplier<T> ifError) {
        try {
            return supplier.get();
        } catch (Throwable t) {
            logger.error(errorMessage, t);
            return Optional.ofNullable(ifError).map(Supplier::get).orElse(null);
        }
    }

    private String doPost(String url, Object body) {
        return doPost(url, body, true);
    }

    /**
     * 使用 RestTemplate 向指定 url 发起一次 post 请求
     *
     * @param url  要请求的目标 url
     * @param body 请求体, 不能是 String, 否则可能出错
     * @param checkResponseNull 是否要校验响应为 null 的情况, (true 的话当 response 为 null 时将会抛出异常)
     * @return 以 String 表示的响应内容
     */
    private String doPost(String url, Object body, boolean checkResponseNull) {
        try {
            String payload = JSON.toJSONString(body);
            logger.info("调用 tms 接口: {}, 入参: {}", url, payload);
            String response = restTemplate.postForObject(url, buildEntity(payload), String.class);
            if (checkResponseNull && response == null) {
                throw new RuntimeException("tms 接口响应为 null");
            }
            logger.info("tms 接口返回值: {}", response);
            return response;
        } catch (Exception e) {
            if (e instanceof HttpServerErrorException) {
                String responseBody = ((HttpServerErrorException) e).getResponseBodyAsString();
                logger.warn("请求出错, 错误响应: {}", responseBody);
            }
            throw new RuntimeException("restTemplate 异常, url = " + url, e);
        }
    }

    /**
     * 使用 obj 作为 请求体, token 请求头 构造一个 HttpEntity
     *
     * @param obj 请求体
     * @param <T> 请求体数据类型
     * @return 请求结果, String 类型
     */
    private <T> HttpEntity<T> buildEntity(T obj) {
        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_JSON_UTF8);
        header.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        return new HttpEntity<>(obj, header);
    }

}
