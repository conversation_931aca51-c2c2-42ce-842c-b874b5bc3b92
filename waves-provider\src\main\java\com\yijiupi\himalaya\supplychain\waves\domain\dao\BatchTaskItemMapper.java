package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.BatchTaskItemBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchTaskItemByLocationInfoQueryBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemUpdatePO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskSortOrderOtherDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSplitOrderDTO;

/**
 * 波次任务详情
 */
@Mapper
public interface BatchTaskItemMapper {

    int insertList(@Param("batchTaskItemPOs") List<BatchTaskItemPO> batchTaskItemPOs);

    int update(@Param("batchTaskItemPO") BatchTaskItemPO batchTaskItemPO);

    /**
     * 查询拣货任务详情列表
     *
     * @return
     */
    PageResult<BatchTaskItemDTO> findBatchTaskItemDTOList(@Param("so") BatchTaskItemQueryDTO batchTaskItemQueryDTO,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据波次任务编号查拣货任务详情
     *
     * @param batchTaskNo
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<BatchTaskItemDTO> findBatchTaskItemDTOListByBatchTaskNo(@Param("cityId") Integer cityId,
        @Param("batchTaskNo") String batchTaskNo, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    /**
     * 根据波次任务编号查询待拆分订单(按订单分组)
     *
     * @param batchTaskNo
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<BatchTaskSplitOrderDTO> listNotSplitOrderByBatchTaskNo(@Param("batchTaskNo") String batchTaskNo,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据订单号和拣货任务编号获取所有拣货任务详情id
     *
     * @param batchTaskNo
     * @param refOrderNo
     * @return
     */
    List<String> listBatchTaskItemId(@Param("batchTaskNo") String batchTaskNo, @Param("refOrderNo") String refOrderNo);

    /**
     * 查询打印拣货单
     *
     * @param batchTaskId
     * @return
     */
    List<BatchTaskItemDTO> findBatchTaskItemDTOListByBatchTaskId(@Param("batchTaskId") String batchTaskId);

    /**
     * 根据id查找波次任务详情
     *
     * @param orderList
     * @return
     */
    List<BatchTaskItemDTO> findBatchTaskItemDTOListByOrderId(@Param("batchTaskId") String batchTaskId,
        @Param("orderList") List<String> orderList);

    /**
     * 以订单为维度，查找订单id集合
     *
     * @param batchTaskId
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<String> finBatchOrderIdListById(@Param("batchTaskId") String batchTaskId,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据id查找波次任务详情
     *
     * @param batchTaskId
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<BatchTaskItemDTO> findPageBatchTaskItemDTOListById(@Param("batchTaskId") String batchTaskId,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据id查找波次任务详情不分页
     *
     * @param batchTaskId
     * @return
     */
    List<BatchTaskItemPO> findBatchTaskItemDTOListNoPage(@Param("batchTaskId") String batchTaskId);

    /**
     * 根据拣货任务id查找波次任务详情
     */
    List<BatchTaskItemPO> findBatchTaskItemDTOListByTaskIds(@Param("list") Collection<String> batchTaskIds);

    int findBatchTaskItemCountByTaskIds(@Param("list") List<String> batchTaskIds);

    /**
     * 根据id不分页查找根据产品拣货的波次任务详情
     *
     * @param batchTaskId
     * @return
     */
    List<BatchTaskItemDTO> findBatchTaskItemDtoListById(@Param("batchTaskId") String batchTaskId);

    /**
     * 根据波次任务号查询详情
     *
     * @param batchTaskNoS
     * @return
     */
    List<BatchTaskItemDTO> findBatchTaskItemDtoListByNo(@Param("list") List<String> batchTaskNoS);

    /**
     * 根据播种任务编号查询详情
     *
     * @param sowTaskNo
     * @return
     */
    List<BatchTaskItemDTO> findBatchTaskItemDtoListBySowTaskNo(@Param("sowTaskNo") String sowTaskNo);

    /**
     * 根据波次任务号查询订单片区
     *
     * @param batchTaskNoS
     * @return
     */
    List<String> findAreaListByTaskNo(@Param("list") List<String> batchTaskNoS);

    /**
     * 查找拣货任务详情
     *
     * @return
     */
    BatchTaskItemPO selectBatchTaskItemById(String id);

    /**
     * 查找拣货任务详情
     */
    List<BatchTaskItemPO> selectBatchTaskItemByIds(@Param("ids") Collection<String> ids);

    /**
     * 修改波次任务详情
     */
    void updateBatchTaskItem(BatchTaskItemPO batchTaskItemPO);

    /**
     * 批量修改波次任务详情
     *
     * @param poList
     */
    void updateBatchTaskByItem(@Param("poList") List<BatchTaskItemUpdatePO> poList);

    /**
     * 批量修改波次任务详情(包含货位id)
     *
     * @param poList
     */
    void updateBatchTaskByItemIncludeLocation(@Param("poList") List<BatchTaskItemUpdatePO> poList,
        @Param("orgId") Integer orgId);

    /**
     * 批量修改波次任务状态
     *
     * @param map
     */
    void updateBatchTaskItemList(@Param("map") Map<String, Byte> map);

    /**
     * 修改拣货任务状态为已完成
     *
     * @param batchTaskNo
     * @param operateUser
     */
    void updateTaskState2Complete(@Param("list") List<String> batchTaskNo, @Param("operateUser") String operateUser);

    /**
     * 根据波次任务主键删除波次任务详情
     *
     * @param batchTaskId
     */
    void deleteByBatchTaskId(@Param("list") List<String> batchTaskId);

    /**
     * 根据波次任务编号删除波次任务详情
     */
    void deleteByBatchTaskNo(@Param("list") List<String> batchTaskNo);

    /**
     * 批量修改拣货任务详情关联的波次任务编号和波次任务id
     *
     * @param batchTaskItemPOs
     */
    void updateList(@Param("batchTaskItemPOs") List<BatchTaskItemPO> batchTaskItemPOs);

    /**
     * 批量修改拣货任务详情关联的波次任务编号、波次任务id和拣货状态
     *
     * @param batchTaskItemPOs
     */
    void updateListNew(@Param("batchTaskItemPOs") List<BatchTaskItemPO> batchTaskItemPOs);

    /**
     * 根据id查询拣货任务详情
     *
     * @param batchTaskId
     * @return
     */
    List<BatchTaskItemPO> listBatchTaskItemByIds(@Param("list") List<String> ids,
        @Param("batchTaskNo") String batchTaskNo);

    /**
     * 根据订单编号查询拣货任务详情
     *
     * @param orderNos
     * @param orgId
     * @return
     */
    List<BatchTaskItemPO> findBatchTaskItemByOrderNos(@Param("list") List<String> orderNos,
        @Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId);

    /**
     * 查找订单号涉及的拣货任务(已完成拣货)
     *
     * @param orderNos
     * @param orgId
     * @return
     */
    List<BatchTaskItemDTO> findUndoneItemsByOrderNos(@Param("list") List<String> orderNos,
        @Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId);

    /**
     * 根据订单id查找非当前拣货任务的其它拣货任务
     *
     * @return
     */
    List<BatchTaskSortOrderOtherDTO> listOtherBatchTaskByRefOrderId(@Param("batchTaskId") String batchTaskId,
        @Param("list") List<String> orderIds, @Param("orgId") Integer orgId);

    /**
     * 根据拣货任务id集合查找拣货任务详情列表
     */
    List<BatchTaskItemPO> listBatchTaskItemByTaskId(@Param("bathTaskIds") List<String> bathTaskIds,
        @Param("orgId") Integer orgId);

    List<BatchTaskItemPO> listBatchTaskItemByTaskIdSku(@Param("bathTaskIds") List<String> bathTaskIds,
        @Param("orgId") Integer orgId, @Param("skuId") Long skuId);

    List<BatchTaskItemPO> findBatchTaskItemsBySowTaskNos(@Param("sowTaskNos") List<String> sowTaskNos,
        @Param("orgId") Integer orgId);

    List<BatchTaskItemDTO> findBatchTaskItemDtoListBySowTaskId(@Param("sowTaskId") Long sowTaskId,
        @Param("state") Byte state);

    /**
     * 根据波次id获取拣货任务详情
     *
     * @return
     */
    List<BatchTaskItemDTO> findBatchTaskItemDtoListByBatchIds(@Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId, @Param("batchIds") List<String> batchIds);

    /**
     * 根据波次id查找拣货任务详情
     */
    List<BatchTaskItemPO> findBatchTaskItemsByBatchIds(@Param("list") Collection<String> batchIds);

    /**
     * 根据任务详情id删除波次任务详情
     */
    void deleteByBatchTaskItemId(@Param("list") List<String> batchTaskItemIds);

    /**
     * 修改波次任务详情状态
     */
    void updateStateByBatchTaskItemId(@Param("list") List<String> batchTaskItemIds, @Param("state") Byte state);

    /**
     * 批量修改拣货任务详情的大小件数量
     *
     * @return
     */
    void updateBatchItemProductCount(@Param("list") List<BatchTaskItemPO> batchTaskItemPOS);

    /**
     * 直接更新数量，不是sql里加减的方式
     *
     * @param batchTaskItemPOS
     */
    void updateBatchItemProductCountDirect(@Param("list") List<BatchTaskItemPO> batchTaskItemPOS);

    /**
     * 批量修改拣货任务详情的回退数量
     *
     * @return
     */
    void updateBatchItemRollBackCount(@Param("list") List<BatchTaskItemPO> batchTaskItemPOS);

    /**
     * 根据波次编号查找已完成的拣货任务详情
     *
     * @return
     */
    List<BatchTaskItemPO> findPickBatchTaskItemsByBatchNos(@Param("list") List<String> batchNos);

    /**
     * 查询hourLimit小时内，sku已经标记缺货的分拣任务项数
     *
     * @param skuId 产品skuId
     * @param timeLimit 截止时间
     * @return
     */
    Integer findLackCountWithinHourBySku(@Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId,
        @Param("skuId") String skuId, @Param("timeLimit") Date timeLimit);

    /**
     * 查询hourLimit小时内，仓库的标记缺货sku的数量
     *
     * @param warehouseId 仓库id
     * @param timeLimit 截止时间
     * @return
     */
    Integer findLackSkuCountWithinHour(@Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId,
        @Param("skuId") String skuId, @Param("timeLimit") Date timeLimit);

    /**
     * 更新播种信息
     */
    int updateBatchItemSowTaskId(BatchTaskItemPO batchTaskItemPO);

    /**
     * 根据订单编号查询出库货位信息
     *
     * @param orderNos
     * @param orgId
     * @return
     */
    List<BatchTaskItemPO> listToLocationName(@Param("list") List<String> orderNos, @Param("orgId") Integer orgId);

    void batchUpdateBatchTaskItem(@Param("list") List<BatchTaskItemPO> batchTaskItemPOs);

    PageResult<BatchTaskItemDTO> listBatchTaskItem(BatchTaskItemQueryDTO batchTaskItemQueryDTO);

    /**
     * 查询sku信息
     *
     * @param id
     * @param orgId
     * @return
     */
    List<String> listBatchTaskSkuId(@Param("id") Long id, @Param("orgId") Integer orgId);

    int updateBatchTaskItemSorter(BatchTaskItemPO batchTaskItemPO);

    int updateBatchTaskItemSorterBatch(@Param("poList") List<BatchTaskItemPO> poList);

    List<String> findBatchTaskItemIdByTaskIds(@Param("list") Collection<String> list);

    /**
     * 根据拣货明细id列表查询拣货任务明细
     */
    List<BatchTaskItemPO> findBatchTaskItemByIds(@Param("list") Collection<String> ids);

    List<BatchTaskItemPO> findBatchTaskItemByLocationInfo(@Param("query") BatchTaskItemByLocationInfoQueryBO query);

    List<String> findBatchTaskItemIdByLocationInfo(@Param("query") BatchTaskItemByLocationInfoQueryBO query);

    int updateByPrimaryKeySelective(BatchTaskItemPO batchTaskItemPO);

}