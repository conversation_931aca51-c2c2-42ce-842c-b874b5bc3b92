package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.util.Arrays;

import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchBaseBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.CreateBatchByManualOperationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.CreateBatchByRefOrderNoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.PickingTypeEnum;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/7
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class CreateBatchTest {

    @Autowired
    private CreateBatchByManualOperationBL createBatchByManualOperationBL;

    @Autowired
    private CreateBatchByRefOrderNoBL createBatchByRefOrderNoBL;

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;

    @Test
    public void createBatchTest() {
        String json =
            "{\"batchName\":\"手动生成波次\",\"cityId\":998,\"operateUser\":\"李响\",\"operateUserId\":67792548,\"orderIdList\":[\"5378374891623003468\"],\"passPickType\":1,\"pickingGroupStrategy\":2,\"pickingType\":2,\"warehouseId\":9981}";

        BatchCreateDTO batchCreateDTO = JSON.parseObject(json, BatchCreateDTO.class);
        CreateBatchBaseBO createBatchBaseBO = new CreateBatchBaseBO(batchCreateDTO.getWarehouseId(), batchCreateDTO);
        createBatchByManualOperationBL.createBatch(createBatchBaseBO);

    }

    @Test
    public void validateIsPickByCustomerTest() {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setOrderIdList(Arrays.asList("5400763476838197632"));
        batchCreateDTO.setPickingType(PickingTypeEnum.订单拣货.getType());
        CreateBatchBaseBO createBatchBaseBO = new CreateBatchBaseBO(batchCreateDTO.getWarehouseId(), batchCreateDTO);
        createBatchByManualOperationBL.checkCreateBatchByCustomer(createBatchBaseBO);
    }

    @Test
    public void createBatchBySortTest() {
        String json =
            "{\"orderIdList\":[\"5418950330619056677\"],\"cityId\":998,\"warehouseId\":9981,\"pickingType\":1,\"pickingGroupStrategy\":3,\"operateUser\":\"测试李\",\"passPickType\":0}";

        CreateBatchBaseBO createBatchBaseBO = new CreateBatchBaseBO();
        BatchCreateDTO batchCreateDTO = JSON.parseObject(json, BatchCreateDTO.class);
        createBatchBaseBO.setBatchCreateDTO(batchCreateDTO);
        createBatchByManualOperationBL.createBatch(createBatchBaseBO);
    }

    @Test
    public void createPromotionBatchTest() {
        String json =
            "{\"batchName\":\"手动生成波次\",\"cityId\":998,\"operateUser\":\"测试账号\",\"operateUserId\":14399,\"orderIdList\":[\"5469311737843176674\"],\"passPickType\":1,\"pickingGroupStrategy\":3,\"pickingType\":1,\"warehouseId\":9981}";

        CreateBatchBaseBO createBatchBaseBO = new CreateBatchBaseBO();
        BatchCreateDTO batchCreateDTO = JSON.parseObject(json, BatchCreateDTO.class);
        createBatchBaseBO.setBatchCreateDTO(batchCreateDTO);
        createBatchByManualOperationBL.createBatch(createBatchBaseBO);
    }

    @Test
    public void locationCreateBatchTest() {
        String json =
            "{\"orderIdList\":[\"5471492997600531685\",\"5471493214186001636\"],\"cityId\":998,\"warehouseId\":9981,\"pickingType\":1,\"pickingGroupStrategy\":3,\"operateUser\":\"测试李\",\"passPickType\":0}";

        CreateBatchBaseBO createBatchBaseBO = new CreateBatchBaseBO();
        BatchCreateDTO batchCreateDTO = JSON.parseObject(json, BatchCreateDTO.class);
        createBatchBaseBO.setBatchCreateDTO(batchCreateDTO);
        createBatchByManualOperationBL.createBatch(createBatchBaseBO);
    }

    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;

    @Test
    public void batchTaskListTest() {
        BatchTaskQueryDTO batchTaskQueryDTO = new BatchTaskQueryDTO();
        batchTaskQueryDTO.setWarehouseId(9981);
        batchTaskQueryDTO.setShiftOfPerformance("2025-07-18");
        PageList<BatchTaskDTO> pageList = batchOrderTaskBL.findBatchOrderTaskList(batchTaskQueryDTO);

        Assertions.assertThat(pageList).isNotNull();
    }

    @Autowired
    private SowQueryBL sowQueryBL;

    @Test
    public void sowTaskTest() {
        SowTaskQueryDTO sowTaskQueryDTO = new SowTaskQueryDTO();
        sowTaskQueryDTO.setWarehouseId(9981);
        sowTaskQueryDTO.setShiftOfPerformance("2025-07-11");
        PageList<SowTaskDTO> pageList = sowQueryBL.findSowTaskList(sowTaskQueryDTO);

        Assertions.assertThat(pageList).isNotNull();
    }

}
