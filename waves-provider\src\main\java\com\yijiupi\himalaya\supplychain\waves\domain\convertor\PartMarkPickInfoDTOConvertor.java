package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.PartMarkPickInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.PartMarkPickItemInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskItemStateEnum;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/7
 */
public class PartMarkPickInfoDTOConvertor {

    public static List<PartMarkPickInfoDTO> convert(List<OutStockOrderPO> outStockOrderList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, List<BatchTaskItemPO> batchTaskItemList,
        List<BatchPO> batchPOList) {
        Map<Long, List<OrderItemTaskInfoPO>> taskInfoGroupMap =
            orderItemTaskInfoPOList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId));
        Map<String, BatchPO> batchMap = batchPOList.stream().collect(Collectors.toMap(BatchPO::getBatchNo, v -> v));
        Map<String, BatchTaskItemPO> batchTaskItemMap =
            batchTaskItemList.stream().collect(Collectors.toMap(BatchTaskItemPO::getId, v -> v));

        return outStockOrderList.stream()
            .map(order -> convert(order, taskInfoGroupMap, batchTaskItemMap, batchMap.get(order.getBatchno())))
            .collect(Collectors.toList());
    }

    private static PartMarkPickInfoDTO convert(OutStockOrderPO outStockOrderPO,
        Map<Long, List<OrderItemTaskInfoPO>> taskInfoGroupMap, Map<String, BatchTaskItemPO> batchTaskItemMap,
        BatchPO batchPO) {
        if (BatchStateEnum.PICKINGEND.getType().byteValue() == batchPO.getState()) {
            return PartMarkPickInfoDTO.getDefault(Long.valueOf(outStockOrderPO.getBusinessId()));
        }
        //
        // boolean allNotPick =
        // outStockOrderPO.getItems().stream().allMatch(item -> isNotPick(batchTaskItemMap, taskInfoGroupMap, item));
        //
        // if (allNotPick) {
        // return PartMarkPickInfoDTO.getDefault(Long.valueOf(outStockOrderPO.getBusinessId()));
        // }

        List<PartMarkPickItemInfoDTO> itemInfoDTOList = outStockOrderPO.getItems().stream()
            .map(item -> convertItem(batchTaskItemMap, taskInfoGroupMap.get(item.getId()), item))
            .collect(Collectors.toList());
        PartMarkPickInfoDTO partMarkPickInfoDTO = new PartMarkPickInfoDTO();
        partMarkPickInfoDTO.setItemList(itemInfoDTOList);
        partMarkPickInfoDTO.setOrderId(Long.valueOf(outStockOrderPO.getBusinessId()));
        partMarkPickInfoDTO.setOrderNo(outStockOrderPO.getReforderno());
        partMarkPickInfoDTO.setShouldHandleCal(ConditionStateEnum.是.getType());

        return partMarkPickInfoDTO;
    }

    private static PartMarkPickItemInfoDTO convertItem(Map<String, BatchTaskItemPO> batchTaskItemMap,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, OutStockOrderItemPO outStockOrderItemPO) {
        Long businessItemId = Long.valueOf(outStockOrderItemPO.getBusinessItemId());
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return PartMarkPickItemInfoDTO.getDefault(businessItemId);
        }

        List<BatchTaskItemPO> batchTaskItemList = orderItemTaskInfoPOList.stream()
            .map(m -> batchTaskItemMap.get(m.getBatchTaskItemId())).collect(Collectors.toList());

        // boolean notPick = batchTaskItemList.stream()
        // .allMatch(batchTaskItemPO -> TaskItemStateEnum.未分拣.getType() == batchTaskItemPO.getTaskState());
        // if (notPick) {
        // return PartMarkPickItemInfoDTO.getDefault(businessItemId);
        // }
        // 这里拣货的时候就改了出库单项。所以直接取差额
        // 但赠品的时候不确定。
        // if (outStockOrderItemPO.getOriginalUnitTotalCount().compareTo(outStockOrderItemPO.getUnittotalcount()) != 0)
        // {
        // return new PartMarkPickItemInfoDTO(businessItemId,
        // outStockOrderItemPO.getOriginalUnitTotalCount().subtract(outStockOrderItemPO.getUnittotalcount()));
        // }
        BigDecimal lackCount = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getLackUnitCount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        return new PartMarkPickItemInfoDTO(businessItemId, lackCount);
    }

    private static boolean isNotPick(Map<String, BatchTaskItemPO> batchTaskItemMap,
        Map<Long, List<OrderItemTaskInfoPO>> taskInfoGroupMap, OutStockOrderItemPO outStockOrderItemPO) {
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList = taskInfoGroupMap.get(outStockOrderItemPO.getId());
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return Boolean.TRUE;
        }

        List<BatchTaskItemPO> batchTaskItemList = orderItemTaskInfoPOList.stream()
            .map(m -> batchTaskItemMap.get(m.getBatchTaskItemId())).collect(Collectors.toList());

        return batchTaskItemList.stream()
            .allMatch(batchTaskItemPO -> TaskItemStateEnum.未分拣.getType() == batchTaskItemPO.getTaskState());
    }

}
