package com.yijiupi.himalaya.supplychain.waves.util;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 分页查询工具类
 *
 * <AUTHOR>
 * @since 2023-07-26 11:20
 **/
@Deprecated
public class PageHelperUtils {

    private static final Logger logger = LoggerFactory.getLogger(PageHelperUtils.class);
    /**
     * 一次最多查询数量
     */
    public static final int LIMIT_COUNT = 5000;

    /**
     * 使用默认分页参数进行查询
     *
     * @param queryFunction 分页查询函数
     * @param <T>           结果数据类型
     * @return 查询结果
     * @see #LIMIT_COUNT
     */
    public static <T> List<T> pageQuery(Supplier<List<T>> queryFunction) {
        return pageQuery(queryFunction, LIMIT_COUNT);
    }

    /**
     * 分批查询
     *
     * @param list          查询参数
     * @param queryFunction 查询方法
     * @param <T>           参数类型
     * @param <R>           结果类型
     * @return 查询结果
     */
    public static <T, R> List<R> splitPageQuery(Collection<T> list, Function<List<T>, List<R>> queryFunction) {
        return splitPageQuery(list, LIMIT_COUNT, queryFunction);
    }

    /**
     * 分批、分页查询
     *
     * @param list          查询参数
     * @param queryFunction 查询方法
     * @param <T>           参数类型
     * @param <R>           结果类型
     * @return 查询结果
     */
    public static <T, R> List<R> splitWithPageQuery(Collection<T> list, Function<List<T>, List<R>> queryFunction) {
        return splitWithPageQuery(list, LIMIT_COUNT, queryFunction);
    }

    /**
     * 使用默认分页参数进行查询
     *
     * @param limitCount    分页参数
     * @param queryFunction 分页查询函数
     * @param <T>           结果数据类型
     * @return 查询结果
     */
    public static <T> List<T> pageQuery(Supplier<List<T>> queryFunction, int limitCount) {
        long count = PageHelper.count(queryFunction::get);
        logger.info("pageQuery, count: {}", count);
        if (count < limitCount) {
            return queryFunction.get();
        } else {
            int loop = (int) count / limitCount;
            // 若有余数, 则增加一次查询次数
            if (count % limitCount != 0) {
                loop = loop + 1;
            }
            logger.info("pageQuery, loop: {}", loop);
            return IntStream.rangeClosed(1, loop).mapToObj(page -> {
                logger.info("pageQuery, 正在进行第 {} 次分页查询, page: {}, size: {}", page, page, limitCount);
                PageHelper.startPage(page, limitCount);
                return queryFunction.get();
            }).flatMap(Collection::stream).collect(Collectors.toList());
        }
    }

    public static <T, R> List<R> splitPageQuery(Collection<T> list, int size, Function<List<T>, List<R>> queryFunction) {
        return Lists.partition(new ArrayList<>(list), size).stream().map(queryFunction)
                .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
    }

    public static <T, R> List<R> splitWithPageQuery(Collection<T> list, int size, Function<List<T>, List<R>> queryFunction) {
        return Lists.partition(new ArrayList<>(list), size).stream().map(it -> pageQuery(() -> queryFunction.apply(it), size))
                .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
    }

}
