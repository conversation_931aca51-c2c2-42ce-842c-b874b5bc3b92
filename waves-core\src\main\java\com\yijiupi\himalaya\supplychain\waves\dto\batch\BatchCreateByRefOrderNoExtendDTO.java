package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/12
 */
public class BatchCreateByRefOrderNoExtendDTO implements Serializable {
    /**
     * 合并车次任务：0 否；1 是
     */
    private Byte mergeTask;

    /**
     * 获取 合并车次任务：0 否；1 是
     *
     * @return mergeTask 合并车次任务：0 否；1 是
     */
    public Byte getMergeTask() {
        return this.mergeTask;
    }

    /**
     * 设置 合并车次任务：0 否；1 是
     *
     * @param mergeTask 合并车次任务：0 否；1 是
     */
    public void setMergeTask(Byte mergeTask) {
        this.mergeTask = mergeTask;
    }
}
