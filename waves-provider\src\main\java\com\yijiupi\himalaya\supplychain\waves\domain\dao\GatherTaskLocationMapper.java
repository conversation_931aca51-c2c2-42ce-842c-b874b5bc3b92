/*
 * @ClassName GatherTaskLocationMapper
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-10-31 10:56:55
 */
package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskLocationPO;
import com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskLocationScaleDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskProductScaleDTO;
import com.yijiupi.himalaya.supplychain.waves.search.GatherTaskSO;

public interface GatherTaskLocationMapper {
    /**
     * @Title deleteByPrimaryKey
     * @param id
     * @return int
     */
    int deleteByPrimaryKey(Long id);

    /**
     * @Title insert
     * @param record
     * @return int
     */
    int insert(GatherTaskLocationPO record);

    /**
     * @Title insertSelective
     * @param record
     * @return int
     */
    int insertSelective(GatherTaskLocationPO record);

    /**
     * 批量新增集货任务产品
     * 
     * @param pos
     * @return
     */
    int insertList(@Param("pos") List<GatherTaskLocationPO> pos);

    /**
     * @Title selectByPrimaryKey
     * @param id
     * @return GatherTaskLocation
     */
    GatherTaskLocationPO selectByPrimaryKey(Long id);

    /**
     * 查询拣货任务对应的集货出库为
     * 
     * @param batchTaskId
     * @return
     */
    List<GatherTaskLocationScaleDTO> selectLocaltion(@Param("so") GatherTaskSO so);

    /**
     * 查询出库位产品明细
     * 
     * @param LocationId
     * @param gatherTaskProductIds
     * @return
     */
    List<GatherTaskProductScaleDTO> selectLocaltionDetail(@Param("locationId") Long locationId,
        @Param("orgId") Integer orgId, @Param("gatherTaskProductIds") String[] gatherTaskProductIds);

    /**
     * @Title updateByPrimaryKeySelective
     * @param record
     * @return int
     */
    int updateByPrimaryKeySelective(GatherTaskLocationPO record);

    /**
     * 修改为已集货状态
     * 
     * @param record
     * @return
     */
    int updateGatherTaskLocationStatus(GatherTaskLocationPO record);

    /**
     * 查询是否还有没集货完成的库位
     * 
     * @param gatherTaskProductId
     * @param orgId
     * @return
     */
    int selectStatusZeroCount(@Param("gatherTaskId") Long gatherTaskId, @Param("orgId") Integer orgId);

    /**
     * @Title updateByPrimaryKey
     * @param record
     * @return int
     */
    int updateByPrimaryKey(GatherTaskLocationPO record);
}