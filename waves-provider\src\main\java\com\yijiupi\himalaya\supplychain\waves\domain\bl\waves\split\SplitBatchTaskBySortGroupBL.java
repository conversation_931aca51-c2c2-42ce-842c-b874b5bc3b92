package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.split;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupListDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupSettingDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortGroupFlagEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortType;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ISortGroupService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderFeatureBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderGroupSortResultHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.PickingTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/16
 */
@Service
public class SplitBatchTaskBySortGroupBL {

    @Reference
    private ISortGroupService iSortGroupService;

    @Autowired
    private GlobalCache globalCache;
    @Autowired
    private OrderFeatureBL orderFeatureBL;
    private static final Logger LOG = LoggerFactory.getLogger(SplitBatchTaskBySortGroupBL.class);

    /**
     * 是否酒饮按订单分区拣货
     *
     * @param wavesStrategyBO
     * @return
     */
    public boolean isDrinkPickBySortGroup(WavesStrategyBO wavesStrategyBO) {
        if (PickingTypeEnum.订单拣货.getType() != wavesStrategyBO.getPickingType()) {
            return Boolean.FALSE;
        }

        if (!OrderFeatureConstant.FEATURE_TYPE_DRINKING.equals(wavesStrategyBO.getFeatureType())) {
            return Boolean.FALSE;
        }

        if (globalCache.isDrinkPickBySortGroup(wavesStrategyBO.getWarehouseId())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 开启配置的按分区拆分拣货任务
     *
     * @param batchPO
     * @param wavesStrategyBO
     * @param waveOrders
     * @return
     */
    public List<SplitBatchTaskByOrderGroupSortResultHelperBO> splitBatchTaskBySortGroup(BatchPO batchPO,
        WavesStrategyBO wavesStrategyBO, List<OutStockOrderPO> waveOrders) {
        if (BooleanUtils.isFalse(isDrinkPickBySortGroup(wavesStrategyBO))) {
            return Collections.emptyList();
        }
        List<Long> outStockOrderIds =
            waveOrders.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());

        Map<Long, List<Byte>> orderFeatureMap = orderFeatureBL.getOrderFeatureMap(outStockOrderIds);
        List<OutStockOrderPO> drinkOrderList = waveOrders.stream().filter(m -> {
            List<Byte> orderFeatureTypeList = orderFeatureMap.get(m.getId());
            return orderFeatureTypeList.contains(OrderFeatureConstant.FEATURE_TYPE_DRINKING);
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(drinkOrderList)) {
            return Collections.emptyList();
        }

        List<OutStockOrderPO> copyOrderList = new ArrayList<>(drinkOrderList.size());
        drinkOrderList.forEach(order -> {
            OutStockOrderPO newOrder = new OutStockOrderPO();
            BeanUtils.copyProperties(order, newOrder);
            copyOrderList.add(newOrder);
        });

        List<OutStockOrderPO> finalDrinkOrderList = copyOrderList;

        // 开启了默认按货位 PickingGroupStrategyEnum.货位.getType() == wavesStrategyBO.getPickingGroupStrategy()
        List<OutStockOrderPO> noLocationOrderList = SplitWaveOrderUtil.getNoLocationOrder(finalDrinkOrderList);
        LOG.info("noLocationOrderPOS:{}", JSON.toJSONString(noLocationOrderList));

        List<OutStockOrderItemPO> totalOutStockOrderItemList = new ArrayList<>();
        finalDrinkOrderList.forEach(p -> {
            totalOutStockOrderItemList.addAll(p.getItems());
        });

        List<SplitBatchTaskByOrderGroupSortResultHelperBO> helperBOList = new ArrayList<>();

        SortGroupSO so = new SortGroupSO();
        so.setWarehouseId(batchPO.getWarehouseId());
        // 根据拣货分组策略 1货区 2货位 3类目
        // so.setGroupType(wavesStrategyBO.getPickingGroupStrategy());
        so.setGroupType(SortType.SORT_LOCATION);
        // 分区标识
        so.setFlag(SortGroupFlagEnum.分区拣货.getType());
        so.setState(ConditionStateEnum.是.getType());
        PageList<SortGroupListDTO> listGroup = iSortGroupService.listGroup(so);

        if (listGroup.getDataList().isEmpty()) {
            LOG.info(String.format("仓库%s没有找到分区！", batchPO.getWarehouseId()));
            return null;
        }

        listGroup.getDataList().forEach(group -> {
            Long groupId = group.getId();
            SortGroupDTO groupDTO = iSortGroupService.getGroup(groupId);
            List<String> lstIds = groupDTO.getGroupSettingList().stream().filter(p -> p.getSortId() != null)
                .map(SortGroupSettingDTO::getSortId).distinct().collect(Collectors.toList());
            List<OutStockOrderItemPO> matchItemList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(lstIds)) {
                // 默认就是按货位
                matchItemList = totalOutStockOrderItemList.stream()
                    .filter(p -> p.getLocationId() != null && lstIds.contains(p.getLocationId().toString()))
                    .collect(Collectors.toList());

            }
            if (!CollectionUtils.isEmpty(matchItemList)) {
                // 移除匹配到的订单项
                totalOutStockOrderItemList.removeAll(matchItemList);

                SplitBatchTaskByOrderGroupSortResultHelperBO helperBO =
                    new SplitBatchTaskByOrderGroupSortResultHelperBO();
                helperBO.setPickingType(PickingTypeEnum.订单拣货.getType());
                helperBO.setGroupDTO(groupDTO);
                helperBO.setSortGroupListDTO(group);
                List<OutStockOrderPO> outStockOrderPOList = rebuildFromItem(matchItemList, finalDrinkOrderList);
                helperBO.setOutStockOrderPOList(outStockOrderPOList);
                helperBO.setOutStockOrderItemPOList(matchItemList);
                helperBOList.add(helperBO);
            }
        });

        // 一个分区没匹配到，走老逻辑
        if (CollectionUtils.isEmpty(helperBOList)) {
            return Collections.emptyList();
        }

        if (!totalOutStockOrderItemList.isEmpty()) {
            // 剩余订单项与之前获取的无货区订单匹配
            List<Long> lstOrderItemIds = totalOutStockOrderItemList.stream()
                .map(OutStockOrderItemPO::getOutstockorderId).distinct().collect(Collectors.toList());
            // 无货区订单添加同订单id的订单项
            noLocationOrderList.forEach(order -> {
                if (lstOrderItemIds.contains(order.getId())) {
                    order.getItems().addAll(totalOutStockOrderItemList.stream()
                        .filter(item -> item.getOutstockorderId().equals(order.getId())).collect(Collectors.toList()));
                }
            });

            List<OutStockOrderItemPO> noLocationOrderItemList =
                noLocationOrderList.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList());

            totalOutStockOrderItemList.removeAll(noLocationOrderItemList);
        }

        if (!CollectionUtils.isEmpty(totalOutStockOrderItemList)) {

            SplitBatchTaskByOrderGroupSortResultHelperBO helperBO = new SplitBatchTaskByOrderGroupSortResultHelperBO();
            helperBO.setPickingType(PickingTypeEnum.订单拣货.getType());
            List<OutStockOrderPO> outStockOrderPOList =
                rebuildFromItem(totalOutStockOrderItemList, finalDrinkOrderList);
            helperBO.setOutStockOrderPOList(outStockOrderPOList);
            helperBO.setOutStockOrderItemPOList(totalOutStockOrderItemList);
            helperBOList.add(helperBO);
        }

        if (noLocationOrderList.isEmpty()) {
            return helperBOList;
        }

        // 没有匹配到分区的，单独生一个拣货任务
        SplitBatchTaskByOrderGroupSortResultHelperBO notSortHelperBO =
            new SplitBatchTaskByOrderGroupSortResultHelperBO();
        notSortHelperBO.setPickingType(PickingTypeEnum.订单拣货.getType());
        notSortHelperBO.setGroupDTO(null);
        notSortHelperBO.setSortGroupListDTO(null);
        notSortHelperBO.setOutStockOrderPOList(noLocationOrderList);

        helperBOList.add(notSortHelperBO);

        return helperBOList;
    }

    public List<OutStockOrderPO> rebuildFromItem(List<OutStockOrderItemPO> items, List<OutStockOrderPO> orders) {
        Map<Long, OutStockOrderPO> outStockOrderPOMap =
            orders.stream().collect(Collectors.toMap(OutStockOrderPO::getId, v -> v, (v1, v2) -> (v1)));

        Map<Long, List<OutStockOrderItemPO>> itemGroupMap =
            items.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getOutstockorderId));

        List<OutStockOrderPO> orderList = new ArrayList<>();
        for (Map.Entry<Long, List<OutStockOrderItemPO>> entry : itemGroupMap.entrySet()) {
            Long outStockOrderId = entry.getKey();
            OutStockOrderPO outStockOrderPO = outStockOrderPOMap.get(outStockOrderId);
            List<OutStockOrderItemPO> itemList = entry.getValue();
            OutStockOrderPO newOrderPO = new OutStockOrderPO();
            BeanUtils.copyProperties(outStockOrderPO, newOrderPO);
            newOrderPO.setItems(itemList);
            orderList.add(newOrderPO);
        }

        return orderList;
    }

}
