package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 * 完成机器人拣货任务
 * <AUTHOR>
 * @date 2023/7/28
 */
public class CompleteRobotBatchTaskDTO implements Serializable {
    /**
     * 拣货任务id
     */
    private String batchTaskId;
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 完成的拣货任务明细id
     */
    private List<String> comleteBatchTaskItemIdList;

    /**
     * 获取 拣货任务id
     *
     * @return batchTaskId 拣货任务id
     */
    public String getBatchTaskId() {
        return this.batchTaskId;
    }

    /**
     * 设置 拣货任务id
     *
     * @param batchTaskId 拣货任务id
     */
    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    /**
     * 获取 完成的拣货任务明细id
     *
     * @return comleteBatchTaskItemIdList 完成的拣货任务明细id
     */
    public List<String> getComleteBatchTaskItemIdList() {
        return this.comleteBatchTaskItemIdList;
    }

    /**
     * 设置 完成的拣货任务明细id
     *
     * @param comleteBatchTaskItemIdList 完成的拣货任务明细id
     */
    public void setComleteBatchTaskItemIdList(List<String> comleteBatchTaskItemIdList) {
        this.comleteBatchTaskItemIdList = comleteBatchTaskItemIdList;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
