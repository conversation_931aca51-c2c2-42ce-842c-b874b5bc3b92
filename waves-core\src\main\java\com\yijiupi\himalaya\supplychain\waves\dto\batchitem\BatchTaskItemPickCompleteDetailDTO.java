package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: BatchTaskItemPickCompleteDetailDTO
 * @description:
 * @date 2022-11-11 14:25
 */
public class BatchTaskItemPickCompleteDetailDTO implements Serializable {

    /**
     * 集货位id
     */
    private Long sowLocationId;

    /**
     * 集货位名称
     */
    private String sowLocationName;

    /**
     * 仓库Id
     */
    private Integer warehouseId;
    /**
     * 集货信息
     */
    private List<BatchTaskItemPickCompleteCollectDetailDTO> collectDetailList;
    /**
     * 打包信息
     */
    private List<BatchTaskItemPickCompletePackageDetailDTO> packageDetailList;

    /**
     * 获取 集货位id
     *
     * @return sowLocationId 集货位id
     */
    public Long getSowLocationId() {
        return this.sowLocationId;
    }

    /**
     * 设置 集货位id
     *
     * @param sowLocationId 集货位id
     */
    public void setSowLocationId(Long sowLocationId) {
        this.sowLocationId = sowLocationId;
    }

    /**
     * 获取 集货位名称
     *
     * @return sowLocationName 集货位名称
     */
    public String getSowLocationName() {
        return this.sowLocationName;
    }

    /**
     * 设置 集货位名称
     *
     * @param sowLocationName 集货位名称
     */
    public void setSowLocationName(String sowLocationName) {
        this.sowLocationName = sowLocationName;
    }

    /**
     * 获取 仓库Id
     *
     * @return warehouseId 仓库Id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库Id
     *
     * @param warehouseId 仓库Id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 集货信息
     *
     * @return collectDetailList 集货信息
     */
    public List<BatchTaskItemPickCompleteCollectDetailDTO> getCollectDetailList() {
        return this.collectDetailList;
    }

    /**
     * 设置 集货信息
     *
     * @param collectDetailList 集货信息
     */
    public void setCollectDetailList(List<BatchTaskItemPickCompleteCollectDetailDTO> collectDetailList) {
        this.collectDetailList = collectDetailList;
    }

    /**
     * 获取 打包信息
     *
     * @return packageDetailList 打包信息
     */
    public List<BatchTaskItemPickCompletePackageDetailDTO> getPackageDetailList() {
        return this.packageDetailList;
    }

    /**
     * 设置 打包信息
     *
     * @param packageDetailList 打包信息
     */
    public void setPackageDetailList(List<BatchTaskItemPickCompletePackageDetailDTO> packageDetailList) {
        this.packageDetailList = packageDetailList;
    }
}
