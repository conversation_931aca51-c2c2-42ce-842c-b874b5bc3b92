package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemContainerDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 波次任务拣货详情DTO
 *
 * <AUTHOR> 2018/3/17
 */
public class BatchTaskSortItemDTO implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = -5962307337878624179L;
    /**
     * 主键
     */
    private String id;
    /**
     * 城市ID，分库用（第三方订单使用一个新的自定义CityId）
     */
    private Integer orgId;
    /**
     * 波次任务编号
     */
    private String batchTaskNo;
    /**
     * 波次任务Id
     */
    private String batchTaskId;
    /**
     * 订单编号
     */
    private String refOrderNo;
    /**
     * 关联订单表id
     */
    private String refOrderId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * skuId（赠品SKUId可能为null）
     */
    private Long skuId;
    /**
     * 品牌
     */
    private String productBrand;
    /**
     * 类目
     */
    private String categoryName;
    /**
     * 包装规格
     */
    private String specName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal specQuantity;
    /**
     * 销售规格名称
     */
    private String saleSpec;
    /**
     * 销售规格系数
     */
    private BigDecimal saleSpecQuantity;
    /**
     * 大单位名称
     */
    private String packageName;
    /**
     * 大单位数量
     */
    private BigDecimal packageCount;
    /**
     * 小单位名称
     */
    private String unitName;
    /**
     * 小单位数量
     */
    private BigDecimal unitCount;
    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;
    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte taskState;
    /**
     * 缺货数量
     */
    private BigDecimal lackUnitCount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 已拣货数量
     */
    private BigDecimal overSortCount;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * 货位顺序-边拣边分
     */
    private Integer locationSequence;
    /**
     * 关联订单详情
     */
    private Long orderItemId;
    /**
     * 箱码
     */
    private List<String> packageCode;
    /**
     * 瓶码
     */
    private List<String> unitCode;

    /**
     * 箱号
     */
    private String boxCode;

    /**
     * 货位类型
     */
    private Byte locationType;

    /**
     * 货位类型名称
     */
    private String locationTypeName;

    /**
     * 多箱号
     */
    private List<String> boxCodeList;

    /**
     * 订单项id
     */
    private String refOrderItemId;

    /**
     * 控货策略id
     */
    private Long controlConfigId;

    /**
     * 控货策略名称
     */
    private String controlConfigName;

    /**
     * 拣货任务明细项容器位
     */
    private List<BatchTaskItemContainerDTO> containerDTOList;

    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）， 2:知花知果，3:易款连锁
     */
    private Byte source;

    /**
     * 关联产品skuId
     */
    private List<Long> relationProductSkuIds;

    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Byte channel;

    /**
     * 规格ID
     */
    private Long productSpecificationId;

    /**
     * 货主ID
     */
    private Long ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 二级货位ID
     */
    private Long secOwnerId;

    /**
     * 包装箱类型
     */
    private Byte packageType;

    /**
     * 批次入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date batchTime;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 是否是库龄管控产品 true：是
     */
    private Boolean inventoryAgeFlag;

    /**
     * 产品默认图片
     */
    private String defaultImageFile;

    /**
     * 产品图片
     */
    private List<String> imageFiles;

    /**
     * 产品合计金额（产品单价 * 小单位总数量）
     */
    private BigDecimal totalAmount;

    /**
     * 已播种小件总数量
     */
    private BigDecimal sownUnitTotalCount;
    /**
     * 订单项信息
     */
    private List<BatchTaskSortItemOrderItemDTO> orderItemList;
    /**
     * 是否是促销订单项关联的拣货任务项
     *
     * @see com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum
     */
    private Byte isPromotion;

    /**
     * 重构出库单类型
     */
    private Byte outBoundType;

    public BigDecimal getSownUnitTotalCount() {
        return sownUnitTotalCount;
    }

    public void setSownUnitTotalCount(BigDecimal sownUnitTotalCount) {
        this.sownUnitTotalCount = sownUnitTotalCount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public List<String> getImageFiles() {
        return imageFiles;
    }

    public void setImageFiles(List<String> imageFiles) {
        this.imageFiles = imageFiles;
    }

    public String getDefaultImageFile() {
        return defaultImageFile;
    }

    public void setDefaultImageFile(String defaultImageFile) {
        this.defaultImageFile = defaultImageFile;
    }

    public Byte getChannel() {
        return channel;
    }

    public void setChannel(Byte channel) {
        this.channel = channel;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public List<Long> getRelationProductSkuIds() {
        return relationProductSkuIds;
    }

    public void setRelationProductSkuIds(List<Long> relationProductSkuIds) {
        this.relationProductSkuIds = relationProductSkuIds;
    }

    public Boolean getInventoryAgeFlag() {
        return inventoryAgeFlag;
    }

    public void setInventoryAgeFlag(Boolean inventoryAgeFlag) {
        this.inventoryAgeFlag = inventoryAgeFlag;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Long getControlConfigId() {
        return controlConfigId;
    }

    public void setControlConfigId(Long controlConfigId) {
        this.controlConfigId = controlConfigId;
    }

    public String getControlConfigName() {
        return controlConfigName;
    }

    public void setControlConfigName(String controlConfigName) {
        this.controlConfigName = controlConfigName;
    }

    public String getRefOrderItemId() {
        return refOrderItemId;
    }

    public void setRefOrderItemId(String refOrderItemId) {
        this.refOrderItemId = refOrderItemId;
    }

    public List<String> getBoxCodeList() {
        return boxCodeList;
    }

    public void setBoxCodeList(List<String> boxCodeList) {
        this.boxCodeList = boxCodeList;
    }

    public Byte getLocationType() {
        return locationType;
    }

    public void setLocationType(Byte locationType) {
        this.locationType = locationType;
    }

    public String getLocationTypeName() {
        return locationTypeName;
    }

    public void setLocationTypeName(String locationTypeName) {
        this.locationTypeName = locationTypeName;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public String getRefOrderId() {
        return refOrderId;
    }

    public void setRefOrderId(String refOrderId) {
        this.refOrderId = refOrderId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public String getSaleSpec() {
        return saleSpec;
    }

    public void setSaleSpec(String saleSpec) {
        this.saleSpec = saleSpec;
    }

    public BigDecimal getSaleSpecQuantity() {
        return saleSpecQuantity;
    }

    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Byte getTaskState() {
        return taskState;
    }

    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    public BigDecimal getLackUnitCount() {
        return lackUnitCount;
    }

    public void setLackUnitCount(BigDecimal lackUnitCount) {
        this.lackUnitCount = lackUnitCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getOverSortCount() {
        return overSortCount;
    }

    public void setOverSortCount(BigDecimal overSortCount) {
        this.overSortCount = overSortCount;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 箱码
     */
    public List<String> getPackageCode() {
        return this.packageCode;
    }

    /**
     * 设置 箱码
     */
    public void setPackageCode(List<String> packageCode) {
        this.packageCode = packageCode;
    }

    /**
     * 获取 瓶码
     */
    public List<String> getUnitCode() {
        return this.unitCode;
    }

    /**
     * 设置 瓶码
     */
    public void setUnitCode(List<String> unitCode) {
        this.unitCode = unitCode;
    }

    public String getBoxCode() {
        return boxCode;
    }

    public void setBoxCode(String boxCode) {
        this.boxCode = boxCode;
    }

    /**
     * @return the containerDTOList
     */
    public List<BatchTaskItemContainerDTO> getContainerDTOList() {
        return containerDTOList;
    }

    /**
     * @param containerDTOList the containerDTOList to set
     */
    public void setContainerDTOList(List<BatchTaskItemContainerDTO> containerDTOList) {
        this.containerDTOList = containerDTOList;
    }

    public Byte getPackageType() {
        return packageType;
    }

    public void setPackageType(Byte packageType) {
        this.packageType = packageType;
    }

    /**
     * 获取 货位顺序-边拣边分
     *
     * @return locationSequence 货位顺序-边拣边分
     */
    public Integer getLocationSequence() {
        return this.locationSequence;
    }

    /**
     * 设置 货位顺序-边拣边分
     *
     * @param locationSequence 货位顺序-边拣边分
     */
    public void setLocationSequence(Integer locationSequence) {
        this.locationSequence = locationSequence;
    }

    /**
     * 获取 订单项信息
     *
     * @return orderItemList 订单项信息
     */
    public List<BatchTaskSortItemOrderItemDTO> getOrderItemList() {
        return this.orderItemList;
    }

    /**
     * 设置 订单项信息
     *
     * @param orderItemList 订单项信息
     */
    public void setOrderItemList(List<BatchTaskSortItemOrderItemDTO> orderItemList) {
        this.orderItemList = orderItemList;
    }

    public Byte getOutBoundType() {
        return outBoundType;
    }

    public void setOutBoundType(Byte outBoundType) {
        this.outBoundType = outBoundType;
    }

    /**
     * 获取 是否是促销订单项关联的拣货任务项           @see com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum
     *
     * @return isPromotion 是否是促销订单项关联的拣货任务项           @see com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum
     */
    public Byte getIsPromotion() {
        return this.isPromotion;
    }

    /**
     * 设置 是否是促销订单项关联的拣货任务项           @see com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum
     *
     * @param isPromotion 是否是促销订单项关联的拣货任务项           @see com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum
     */
    public void setIsPromotion(Byte isPromotion) {
        this.isPromotion = isPromotion;
    }
}