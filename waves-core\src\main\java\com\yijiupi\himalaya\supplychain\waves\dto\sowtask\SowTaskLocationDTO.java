package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-08-29 16:44
 **/
public class SowTaskLocationDTO implements Serializable {

    private Long locationId;
    private String locationName;

    public static SowTaskLocationDTO of(Long locationId, String locationName) {
        SowTaskLocationDTO locationDTO = new SowTaskLocationDTO();
        locationDTO.setLocationId(locationId);
        locationDTO.setLocationName(locationName);
        return locationDTO;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
