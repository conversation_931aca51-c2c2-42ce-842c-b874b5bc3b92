package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.OutStockOrderLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderNewPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SameUserOrderInBatchQueryPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSorterDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickedCountDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickedDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderByProductDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderWaitDeliveryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.ProductAllotStoreDTO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderByProductSO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderItemProductSO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderSearchSO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderWaitDeliverySO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * Created by 余明 on 2018-03-15.
 */
@Mapper
public interface OutStockOrderMapper {

    /**
     * 查询满足波次策略的订单
     */
    List<OutStockOrderPO> findOutStockOrderPOList(@Param("dto") OutStockOrderSearchSO dto);

    List<OutStockOrderPO> findByOrderId(@Param("OrderIds") Collection<Long> orderIds);

    List<OutStockOrderPO> findByOrderIdWithoutCon(@Param("OrderIds") List<String> orderIds);

    List<OutStockOrderPO> findByOrderIds(@Param("OrderIds") Collection<Long> orderIds);

    /**
     * 查询出库单是否生成了内配单
     */
    List<OutStockOrderPO> findCreateAllocationByOrderId(@Param("OrderIds") List<String> orderIds);

    /**
     * 查询未生成波次的订单
     */
    List<OutStockOrderPO> findNoWaveOrderByRefOrderNo(@Param("refOrderNos") List<String> refOrderNos,
        @Param("warehouseId") Integer warehouseId);

    /**
     * 查询未生成波次的订单
     */
    List<OutStockOrderPO> findOrderByRefOrderNo(@Param("refOrderNos") List<String> refOrderNos,
        @Param("warehouseId") Integer warehouseId);

    /**
     * 根据订单ID获取订单信息
     */
    List<OutStockOrderPO> listOutStockOrderByOrderId(@Param("OrderIds") List<Long> orderIds);

    /**
     * 根据订单号获取订单信息
     *
     * @return
     */
    List<OutStockOrderPO> listOutStockOrderByOrderNo(@Param("OrderNos") List<String> orderNos,
        @Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId);

    List<String> findByBatchIdByRefNos(@Param("OrderNos") List<String> orderNos,
        @Param("warehouseId") Integer warehouseId);

    List<String> findByBatchIdByIds(@Param("orderIds") List<Long> orderIds, @Param("warehouseId") Integer warehouseId);

    /**
     * 根据订单项id查询订单
     */
    List<OutStockOrderPO> findByOrderItemId(@Param("orderItemIds") List<Long> orderItemIds);

    /**
     * 获取待出库的延迟配送订单
     */
    List<OutStockOrderPO> findDelayOrderByWarehouseId(Integer warehouseId);

    // /**
    // * 查询可用的订单(分页)
    // *
    // * @param dto
    // * @param pageNum
    // * @param pageSize
    // * @return
    // */
    // PageResult<OutStockOrderPO> findOutStockOrderPOPageList(@Param("dto") OutStockOrderSearchSO dto,
    // @Param("pageNum") Integer pageNum,
    // @Param("pageSize") Integer pageSize);

    /**
     * 查询totalCount
     */
    PageResult<String> findOutStockOrderPOPageCount(@Param("dto") OutStockOrderSearchSO dto,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 查询出库单列表
     */
    PageResult<OutStockOrderPO> findOutStockOrderList(@Param("dto") OutStockOrderSearchSO dto,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    int batchUpdateOutStockOrder(@Param("list") List<OutStockOrderPO> outStockOrderPOS);

    /**
     * 根据batchNo清除相关字段
     *
     * @param batchNoList
     */
    void cleanBatch(@Param("list") List<String> batchNoList);

    List<Long> findOutStockOrderIdsByBatchId(@Param("batchId") String batchId);

    /**
     * 更新订单的状态
     *
     * @param lstOrderIds
     * @return
     */
    int updateStateByOrderIds(@Param("list") List<Long> lstOrderIds, @Param("state") Byte state);

    /**
     * 查找订单项全部生成了拣货任务的订单id
     *
     * @return
     */
    List<Long> findOrderIdByItemState(@Param("list") List<Long> lstOrderIds);

    void insertList(@Param("recordList") List<OutStockOrderNewPO> recordList);

    /**
     * 根据订单号模糊查询已拣货的订单号列表
     *
     * @param outStockOrderPO
     * @return
     */
    List<String> listRefOrderNoByLike(@Param("outStockOrderPO") OutStockOrderPO outStockOrderPO);

    /**
     * 根据订单号查询出库单详情, 这个方法的 item 字段不全
     */
    OutStockOrderPO selectByRefOrderNo(@Param("refOrderNo") String refOrderNo, @Param("cityId") Integer cityId,
        @Param("warehouseId") Integer warehouseId);

    /**
     * 根据订单号查询出库单详情
     *
     * @return
     */
    List<OutStockOrderPO> listByRefOrderNo(@Param("list") List<String> refOrderNo, @Param("cityId") Integer cityId,
        @Param("warehouseId") Integer warehouseId);

    /**
     * 根据订单id查询出库单详情
     *
     * @param refOrderId
     * @return
     */
    OutStockOrderPO selectByRefOrderId(@Param("refOrderId") Long refOrderId);

    /**
     * 根据波次编号查询关联的订单编号
     *
     * @return
     */
    List<String> listRefOrderNoByBatchNo(@Param("batchNo") String batchNo);

    /**
     * 根据波次编号查询关联的订单编号
     *
     * @return
     */
    List<Long> findOrderIdByBatchNo(@Param("batchNo") String batchNo, @Param("orgId") Integer orgId);

    /**
     * 根据订单号查询订单列表
     */
    List<OutStockOrderPO> findSimpleByIds(@Param("ids") List<Long> ids, @Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId);

    /**
     * 查询出库单待出库的产品
     */
    List<Long> listOutStockOrderProduct(OutStockOrderItemProductSO so);

    /**
     * 查找待出库订单项列表
     */
    PageResult<OutStockOrderWaitDeliveryDTO> listOutStockOrderItemWaitDelivery(OutStockOrderWaitDeliverySO so);

    /**
     * 根据波次id查询订单产品明细
     */
    PageResult<OutStockOrderByProductDTO> listOrderProductByBatchId(OutStockOrderByProductSO so);

    /**
     * 根据波次id查询产品汇总
     */
    PageResult<OutStockOrderByProductDTO> listProductGroupByBatchId(OutStockOrderByProductSO so);

    /**
     * 根据波次编号查询该波次关联的订单列表
     */
    PageResult<OutStockOrderDTO> listOrderByBatchNo(@Param("batchNo") String batchNo, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    /**
     * 获取产品分配波次的数量
     *
     * @return
     */
    List<ProductAllotStoreDTO> listProductAllotStore(@Param("cityId") Integer cityId,
        @Param("warehouseId") Integer warehouseId, @Param("list") List<Long> productSkuIds);

    List<OutStockOrderPO> findByBatchNos(@Param("batchNos") Collection<String> batchNos, @Param("orgId") Integer orgId);

    List<OutStockOrderPO> findSimpleInfoByBatchNos(@Param("batchNos") Collection<String> batchNos,
        @Param("orgId") Integer orgId);

    /**
     * 查询出库单（详情）列表
     */
    PageResult<OutStockOrderPO> findOutStockOrderItemList(@Param("dto") OutStockOrderSearchSO dto,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize,
        @Param("descending") boolean descending);

    /**
     * 查询出库单列表
     */
    PageResult<OutStockOrderPO> findOutStockOrderListNew(@Param("dto") OutStockOrderSearchSO dto,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize,
        @Param("descending") boolean descending);

    /**
     * 查询二级仓生成内配单的订单中分拣占用数量
     *
     * @return
     */
    List<PickedCountDTO> findPickedCountBySkuIdsForCreateAllocation(@Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId, @Param("skuIdList") List<Long> productSkuIds,
        @Param("orderNoList") List<String> orderNoList);

    /**
     * 查询二级仓生成内配单的订单中分拣占用详情
     *
     * @return
     */
    List<PickedDetailDTO> findPickedDetailBySkuIdsForCreateAllocation(@Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId, @Param("skuIdList") List<Long> productSkuIds,
        @Param("orderNoList") List<String> orderNoList);

    List<OutStockOrderPO> listByBatchTaskItemIds(@Param("batchTaskItemIds") List<String> batchTaskItemIds,
        @Param("orgId") Integer cityId);

    OutStockOrderPO getByOrderNo(@Param("orgId") Integer cityId, @Param("warehouseId") Integer warehouseId,
        @Param("orderNo") String orderNo);

    List<OutStockOrderPO> listSimpleBySowTaskNo(@Param("sowTaskNo") String sowTaskNo, @Param("orgId") Integer orgId);

    /**
     * 根据订单号获取订单信息
     *
     * @return
     */
    List<OutStockOrderPO> listOutStockOrderAllByOrderNo(@Param("OrderNos") List<String> orderNos,
        @Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId);

    /**
     * 根据波次id获取订单信息
     *
     * @return
     */
    List<OutStockOrderPO> listOutStockOrderByBatchIds(@Param("batchIds") List<String> batchIds,
        @Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId);

    List<OutStockOrderPO> listSimpleBySowTaskNos(@Param("sowTaskNos") List<String> sowTaskNos,
        @Param("orgId") Integer orgId);

    /**
     * 根据订单编号list、仓库Id查找指定分拣员
     *
     * @param orderNos 订单编号
     * @param warehouseId 仓库Id
     * @return 分拣员信息
     */
    List<BatchTaskSorterDTO> listSorterByOrderNos(@Param("orderNos") List<String> orderNos,
        @Param("warehouseId") Integer warehouseId);

    List<OutStockOrderPO> listOutStockOrderByBatchTaskId(@Param("batchTaskId") String batchTaskId,
        @Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId);

    /**
     * 根据订单号查询订单列表
     */
    List<OutStockOrderPO> listOrderByIds(@Param("ids") Collection<Long> ids, @Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId);

    /**
     * 查询出库单列表 根据新订单类型查询
     */
    PageResult<OutStockOrderPO> listOutStockOrder(@Param("dto") OutStockOrderSearchSO dto,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据订单项id查询订单
     */
    List<OutStockOrderPO> listByOrderItem(OutStockOrderSearchSO querySO);

    /**
     * 查询播种任务批次信息
     *
     * @param collect
     * @param cityId
     * @return
     */
    List<OutStockOrderPO> querySowBoundBySowTaskId(@Param("collect") List<Long> collect,
        @Param("cityId") Integer cityId);

    /**
     * 查询订单类型
     */
    List<Integer> queryOutBoundType(@Param("batchNos") Collection<String> batchNos, @Param("orgId") Integer orgId);

    /**
     * 更新订单的下推状态
     *
     * @param lstOrderIds
     * @return
     */
    int updatePushStateByOrderIds(@Param("list") List<Long> lstOrderIds, @Param("pushState") Byte pushState);

    /**
     * 查询订单
     */
    List<OutStockOrderPO> listAllInfo(OutStockOrderSearchSO querySO);

    int updateBatchByPOList(@Param("list") List<OutStockOrderPO> poList);

    /**
     * 根据城市 订单查询波次信息
     *
     * @param refOrderIdList
     * @param orgId
     * @return
     */
    List<OutStockOrderPO> queryBatchNoByOrder(@Param("refOrderIdList") List<String> refOrderIdList,
        @Param("orgId") Integer orgId);

    /**
     * 根据城市 播种任务明细查询线路和司机信息
     *
     * @param sowTaskItemIds
     * @param orgId
     * @return
     */
    List<OutStockOrderPO> queryRouteAndDriverByOrder(@Param("sowTaskItemIds") List<Long> sowTaskItemIds,
        @Param("orgId") Integer orgId, @Param("locationId") Long locationId);

    /**
     * 根据波次任务号查询出库单
     */
    List<OutStockOrderPO> findByBatchTaskNos(@Param("batchTaskNos") List<String> batchTaskNos,
        @Param("warehouseId") Integer warehouseId);

    /**
     * 通过出库批次号查询出库单
     *
     * @param boundNos 出库批次号
     * @param orgId 城市 id
     * @return 查询结果
     */
    List<OutStockOrderPO> findByOutBoundNoList(@Param("boundNos") Collection<String> boundNos,
        @Param("orgId") Integer orgId);

    List<Long> findIdsByBusinessIds(@Param("businessIds") List<String> businessIds,
        @Param("warehouseId") Integer warehouseId);

    /**
     * 通过 businessId 查找出库单
     *
     * @param businessIds businessId
     * @param warehouseId 仓库 id
     */
    List<OutStockOrderPO> findByBusinessIds(@Param("businessIds") Collection<String> businessIds,
        @Param("warehouseId") Integer warehouseId);

    List<OutStockOrderPO> findIdByOrderNos(@Param("refOrderNos") Collection<String> refOrderNos,
        @Param("warehouseId") Integer warehouseId);

    List<OutStockOrderPO> findIdByIds(@Param("ids") List<Long> ids, @Param("warehouseId") Integer warehouseId);

    OutStockOrderPO findRoutInfoByRefOrderNo(@Param("refOrderNos") List<String> refOrderNos,
        @Param("warehouseId") Integer warehouseId);

    List<OutStockOrderPO> findOutStockOrderByRefOrderNo(@Param("refOrderNos") List<String> refOrderNos,
        @Param("warehouseId") Integer warehouseId, @Param("boundNos") List<String> boundNos);

    List<OutStockOrderPO> selectByBusinessIdAndWarehouseId(@Param("businessIds") List<String> businessIds,
        @Param("warehouseId") Integer warehouseId);

    void clearBatchInfo(@Param("idList") List<Long> ids);

    List<OutStockOrderPO> findSimpleOrderInfoByOutBoundNoList(@Param("boundNos") Collection<String> boundNos,
        @Param("warehouseId") Integer warehouseId);

    List<OutStockOrderPO> findSimpleInfoByBusinessIds(@Param("businessIds") List<String> businessIds,
        @Param("warehouseId") Integer warehouseId);

    List<OutStockOrderPO> findSimpleInfoByIds(@Param("ids") List<Long> ids, @Param("warehouseId") Integer warehouseId);

    /**
     * 查询出库信息
     */
    List<OutStockOrderPO> queryPickUpUserNameByCondition(@Param("dto") OutStockOrderSearchSO dto);

    /**
     * 通过出库单 id 查询出库单, 只有 {@link OutStockOrderPO}
     */
    List<OutStockOrderPO> selectByIds(@Param("ids") Collection<Long> ids);

    /**
     * 查询相同用户下的其他订单特征的订单
     *
     * @param queryPO
     * @return
     */
    List<OutStockOrderPO> findSameUserOrderInBatch(SameUserOrderInBatchQueryPO queryPO);
    /**
     * 批量更新排序号
     *
     * @param lstOrderIds
     * @param orderSequence
     * @return
     */
    int updateOrderSequenceByOrderIds(@Param("orderIds") List<Long> orderIds,
        @Param("orderSequence") Integer orderSequence);

    /**
     * 按订单特征查询
     */
    List<OutStockOrderPO> findByCondition(@Param("dto") OutStockOrderSearchSO dto);

    /**
     * 查找批次下出库单id列表
     *
     * @param batchNo
     * @param orgId
     * @return
     */
    List<Long> findOrderIdsInBatch(@Param("batchNo") String batchNo, @Param("orgId") Integer orgId);

    /**
     * 根据波次查询出库单关联货位
     */
    List<OutStockOrderLocationBO> findOutStockOrderLocationByBatchNo(@Param("batchNo") String batchNo, @Param("orgId") Integer orgId);
}
