package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/29
 */
public class OutStockOrderItemDetailRecoverHandleByItemBO {

    private OutStockOrderPO outStockOrderPO;

    private List<OrderItemTaskInfoPO> orderItemTaskInfoPOList;

    private List<OutStockOrderItemPO> needHandleItemList;

    private Integer warehouseId;

    private Integer orgId;

    /**
     * 是否处理item和detail数量相等的数据
     */
    private boolean handleEqualCountItem = Boolean.FALSE;
    /**
     * 是否强制使用订单中台的数据创建detail
     */
    private boolean useOrderCenterDetail = Boolean.FALSE;
    /**
     * 是否用库存记录创建detail
     */
    private boolean useInventoryRecord;


    /**
     * 获取
     *
     * @return outStockOrderPO
     */
    public OutStockOrderPO getOutStockOrderPO() {
        return this.outStockOrderPO;
    }

    /**
     * 设置
     *
     * @param outStockOrderPO
     */
    public void setOutStockOrderPO(OutStockOrderPO outStockOrderPO) {
        this.outStockOrderPO = outStockOrderPO;
    }

    /**
     * 获取
     *
     * @return orderItemTaskInfoPOList
     */
    public List<OrderItemTaskInfoPO> getOrderItemTaskInfoPOList() {
        return this.orderItemTaskInfoPOList;
    }

    /**
     * 设置
     *
     * @param orderItemTaskInfoPOList
     */
    public void setOrderItemTaskInfoPOList(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        this.orderItemTaskInfoPOList = orderItemTaskInfoPOList;
    }

    /**
     * 获取
     *
     * @return needHandleItemList
     */
    public List<OutStockOrderItemPO> getNeedHandleItemList() {
        return this.needHandleItemList;
    }

    /**
     * 设置
     *
     * @param needHandleItemList
     */
    public void setNeedHandleItemList(List<OutStockOrderItemPO> needHandleItemList) {
        this.needHandleItemList = needHandleItemList;
    }

    /**
     * 获取
     *
     * @return warehouseId
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置
     *
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }


    /**
     * 获取 是否处理item和detail数量相等的数据
     *
     * @return handleEqualCountItem 是否处理item和detail数量相等的数据
     */
    public boolean isHandleEqualCountItem() {
        return this.handleEqualCountItem;
    }

    /**
     * 设置 是否处理item和detail数量相等的数据
     *
     * @param handleEqualCountItem 是否处理item和detail数量相等的数据
     */
    public void setHandleEqualCountItem(boolean handleEqualCountItem) {
        this.handleEqualCountItem = handleEqualCountItem;
    }

    /**
     * 获取 是否强制使用订单中台的数据创建detail
     *
     * @return useOrderCenterDetail 是否强制使用订单中台的数据创建detail
     */
    public boolean isUseOrderCenterDetail() {
        return this.useOrderCenterDetail;
    }

    /**
     * 设置 是否强制使用订单中台的数据创建detail
     *
     * @param useOrderCenterDetail 是否强制使用订单中台的数据创建detail
     */
    public void setUseOrderCenterDetail(boolean useOrderCenterDetail) {
        this.useOrderCenterDetail = useOrderCenterDetail;
    }

    /**
     * 获取 是否用库存记录创建detail
     *
     * @return useInventoryRecord 是否用库存记录创建detail
     */
    public boolean isUseInventoryRecord() {
        return this.useInventoryRecord;
    }

    /**
     * 设置 是否用库存记录创建detail
     *
     * @param useInventoryRecord 是否用库存记录创建detail
     */
    public void setUseInventoryRecord(boolean useInventoryRecord) {
        this.useInventoryRecord = useInventoryRecord;
    }

    /**
     * 获取
     *
     * @return orgId
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置
     *
     * @param orgId
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }
}
