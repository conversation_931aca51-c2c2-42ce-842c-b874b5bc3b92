package com.yijiupi.himalaya.supplychain.waves.enums;

import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;

import java.util.Objects;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/24
 */
public final class BatchAttrSettingWayConstants {

    public static final Byte BATCH_ATTR_SETTING_WAY_DEFAULT = 0;

    public static final Byte BATCH_ATTR_SETTING_WAY_ROUTE = 1;

    public static final Byte BATCH_ATTR_SETTING_WAY_AREA = 2;

    public static final Byte BATCH_ATTR_SETTING_WAY_USER = 3;

    public static final Byte BATCH_ATTR_SETTING_WAY_DRIVER = 4;
    /**
     * 地图调度，tms按地图调度时，传过来的groupType=4对应的参数
     */
    public static final Byte BATCH_ATTR_SETTING_WAY_MAP = 5;

    public static boolean isAreaBatch(Byte orderSelection, Byte batchAttrSettingWay) {
        if (WavesStrategyConstants.ORDERSELECTION_AREA == orderSelection.intValue()) {
            return Boolean.TRUE;
        }
        if (Objects.isNull(batchAttrSettingWay)) {
            return Boolean.FALSE;
        }

        if (WavesStrategyConstants.ORDERSELECTION_USER == orderSelection.intValue()
            && BATCH_ATTR_SETTING_WAY_AREA.equals(batchAttrSettingWay)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    public static boolean isRouteBatch(Byte orderSelection, Byte batchAttrSettingWay) {
        if (WavesStrategyConstants.ORDERSELECTION_WAY == orderSelection.intValue()) {
            return Boolean.TRUE;
        }

        if (Objects.isNull(batchAttrSettingWay)) {
            return Boolean.FALSE;
        }

        if (WavesStrategyConstants.ORDERSELECTION_USER == orderSelection.intValue()
            && BATCH_ATTR_SETTING_WAY_ROUTE.equals(batchAttrSettingWay)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }
}
