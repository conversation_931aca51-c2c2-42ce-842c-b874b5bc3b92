package com.yijiupi.himalaya.supplychain.waves.util;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.ordercenter.sdk.serviceability.ServiceAbilityClient;
import com.yijiupi.himalaya.supplychain.dto.VariableValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/8/16
 * @description 服务能力工具类
 */
@Component
public class ServiceAbilityClientUtil {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Reference
    private IVariableValueService iVariableValueService;

    /**
     * 租户配置信息的appKey
     */
    private final static String ORDERCENTER_SDK_APP_KEY_CONFIG = "wms-ordercenter.sdk.app-key";
    /**
     * 租户配置信息的appSecret
     */
    private final static String ORDERCENTER_SDK_APP_SECRET_CONFIG = "wms-ordercenter.sdk.app-secret";

    /**
     * redis中缓存租户配置的appKey信息
     */
    private final static String REDIS_ORDERCENTER_CONFIG_APP_KEY = "supf:wms:ordercenter:config:" + ORDERCENTER_SDK_APP_KEY_CONFIG;

    /**
     * 将服务能力通过配置的appKey对应缓存起来
     */
    private final ConcurrentHashMap<String, ServiceAbilityClient> serviceAbilityClientMap = new ConcurrentHashMap<>(4);

    /**
     * 获取调用订单中台的服务能力实例：
     * 1.从租户的数据库中获取配置信息，如果没有配置信息则直接报错，如果有配置信息继续往下
     * 2.通过租户的配置信息初始化对应的服务能力，并将appKey对应的服务能力放入map中，返回对应的服务能力
     */
    public ServiceAbilityClient getInstance(){
        // 调用服务能力的配置信息
        AppKeySecretConfig<String, String> appKeySecret;
        try {
            // 获取租户调用服务能力配置信息
            appKeySecret = Optional.ofNullable(getAppKeySecret()).orElseThrow(()->new BusinessException("没有查询到租户配置订单中台的key和secret信息"));
            if( Objects.isNull(serviceAbilityClientMap.get(appKeySecret.appKey)) ) {
                synchronized (serviceAbilityClientMap) {
                    if (Objects.isNull(serviceAbilityClientMap.get(appKeySecret.appKey))) {
                        serviceAbilityClientMap.put(appKeySecret.appKey, new ServiceAbilityClient(appKeySecret.appKey, appKeySecret.appSecret));
                    }
                }
            }
        } catch (Exception e) {
            logger.error("获取服务能力报错，原因：", e);
            throw new BusinessException("获取服务能力报错");
        }

        return serviceAbilityClientMap.get(appKeySecret.appKey);
    }

    /**
     * 获取租户配置调用订单中台的key和secret
     */
    private AppKeySecretConfig<String, String> getAppKeySecret(){
        VariableValueQueryDTO variableValueQueryDTO = new VariableValueQueryDTO();
        variableValueQueryDTO.setVariableKeyList(Arrays.asList(ORDERCENTER_SDK_APP_KEY_CONFIG, ORDERCENTER_SDK_APP_SECRET_CONFIG));
        List<VariableValueDTO> list = iVariableValueService.list(variableValueQueryDTO);
        // key和secret配置数量
        final int keySecretSize = 2;
        if( CollectionUtils.isEmpty(list) || list.size() < keySecretSize ){
            return null;
        }
        Map<String, String> keySecretMap = new HashMap<>(4);
        list.stream().filter(variableValueDTO -> ORDERCENTER_SDK_APP_KEY_CONFIG.equals(variableValueDTO.getVariableKey())).findAny().ifPresent(variableValueDTO -> {
            keySecretMap.put(ORDERCENTER_SDK_APP_KEY_CONFIG, variableValueDTO.getVariableData());
        });
        list.stream().filter(variableValueDTO -> ORDERCENTER_SDK_APP_SECRET_CONFIG.equals(variableValueDTO.getVariableKey())).findAny().ifPresent(variableValueDTO -> {
            keySecretMap.put(ORDERCENTER_SDK_APP_SECRET_CONFIG, variableValueDTO.getVariableData());
        });
        if( keySecretMap.size() != keySecretSize ){
            return null;
        }
        return new AppKeySecretConfig<>(keySecretMap.get(ORDERCENTER_SDK_APP_KEY_CONFIG), keySecretMap.get(ORDERCENTER_SDK_APP_SECRET_CONFIG));
    }

    public static class AppKeySecretConfig<A, B> {

        public final A appKey;
        public final B appSecret;

        public AppKeySecretConfig(A appKey, B appSecret) {
            this.appKey = appKey;
            this.appSecret = appSecret;
        }

    }

}
