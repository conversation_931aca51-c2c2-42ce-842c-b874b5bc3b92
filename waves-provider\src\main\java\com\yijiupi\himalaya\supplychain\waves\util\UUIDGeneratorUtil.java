package com.yijiupi.himalaya.supplychain.waves.util;

import com.yijiupi.himalaya.uuid.UUIDGenerator;

public class UUIDGeneratorUtil {
    /**
     * UUID生成器实例
     */
    public static final String ORDER_ITEM_CHANGE_RECORD = "outstockorderitemchangerecord";
    public static final String ORDER_ITEM_TASK_INFO = "orderitemtaskinfo";
    public static final String ORDER_ITEM_TASK_INFO_DETAIL = "orderitemtaskinfodetail";
    public static final String OUT_STOCK_ORDER_ITEM_DETAIL = "outstockorderitemdetail";
    public static final String SOW_TASK = "sowtask";
    public static final String SOW_TASK_ITEM = "sowtaskitem";
    public static final String SOW_ORDER = "soworder";

    public static final String SOW_TASK_PRE = "sowtaskpre";
    public static final String SOW_ORDER_PRE = "soworderpre";

    /**
     * 获取uuid
     * 
     * @param table
     * @return
     */
    public static long getUUID(String table) {
        return UUIDGenerator.getUUID(table);
    }
}
