package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.waves.domain.po.PackageOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.*;
import com.yijiupi.himalaya.supplychain.waves.dto.secondsort.SecondSortQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.search.PackageOrderItemSO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 产品包装
 *
 * <AUTHOR>
 * @date 2018/7/12 17:27
 */
public interface PackageOrderItemMapper {

    /**
     * 根据出库单号查询包装信息详情（分页）
     *
     * @param refOrderNo
     * @return
     */
    PageResult<PackageOrderItemDTO> listPackageOrderItem(@Param("so") PackageOrderItemSO packageOrderItemSO,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据单号查询所有包装信息
     *
     * @param refOrderNo
     * @return
     */
    List<PackageOrderItemDTO> listPackageOrderItemByOrderNo(@Param("refOrderNo") String refOrderNo,
        @Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId);

    /**
     * 根据播种任务编号查询所有包装信息
     *
     * @param refOrderNo
     * @return
     */
    List<PackageCodePrintDTO> listPackageCodePrintBySowTaskNo(@Param("orgId") Integer orgId,
        @Param("sowTaskNo") String sowTaskNo);

    /**
     * 新增包装详情
     *
     * @param packageOrderItemPO
     * @return
     */
    Integer insert(PackageOrderItemPO packageOrderItemPO);

    /**
     * 批量新增包装详情
     *
     * @param packageOrderItemPOList
     * @return
     */
    Integer insertBatch(@Param("list") List<PackageOrderItemPO> packageOrderItemPOList);

    /**
     * @param orgId
     * @param orderNos
     * @return
     */
    List<PackageCodePrintDTO> listPackageCodePrintByOrderNos(@Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId, @Param("list") List<String> orderNos);

    /**
     * 根据订单项查询已装箱数据
     */
    List<PackageOrderItemDTO> listPackageItemsSurplusByItemIds(@Param("itemIds") List<Long> itemIds,
        @Param("orgId") Integer orgId);

    /**
     * 根据订单项查询已装箱数据
     */
    List<PackageOrderItemDTO> listPackageItemsByItemIds(@Param("itemIds") List<Long> itemIds,
        @Param("orgId") Integer orgId);

    /**
     * 根据箱号完整编码查询内配单号
     */
    List<String> listOrderNoByBoxCodeNo(String boxCodeNo);

    List<PackageOrderItemDTO> listPackageOrderItemByBarCode(@Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId, @Param("boxCodeNo") String boxCodeNo,
        @Param("orderNo") String orderNo);

    List<PackageOrderItemDTO> listByOrderNos(@Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId,
        @Param("refOrderNos") List<String> refOrderNos);

    List<PackageOrderItemDTO> listSowPackageOrderItems(@Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId, @Param("refOrderNos") List<String> refOrderNos);

    PageResult<PalletOrderDTO> pageListPalletOrders(@Param("query") PalletOrderQueryDTO palletOrderQueryDTO,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    List<PalletOrderDTO> findPackageBatchTaskItem(@Param("boxCodeNos") List<String> boxCodeNos,
        @Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId);

    List<PalletOrderItemDTO> listPalletOrderItems(@Param("query") PalletOrderQueryDTO palletOrderQueryDTO);

    void batchReview(@Param("list") List<PackageOrderItemDTO> packageOrderItemDTOS);

    List<PackageOrderItemDTO> findPackageOrderItemsByBatchId(@Param("batchId") String batchId,
        @Param("orgId") Integer orgId, @Param("type") Byte type, @Param("states") List<Byte> states);

    List<String> findBatchNoByIds(@Param("packageOrderItemIds") List<Long> packageOrderItemIds);

    List<PackageOrderItemPO> findPackageOrderItemsByBatchItemIds(@Param("batchItemIds") List<String> batchItemIds,
        @Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId);

    List<PalletOrderDTO> findPackageBatchTaskItemByIds(@Param("ids") List<Long> packageItemIds);

    List<PackageOrderItemDTO> listPackageBatchTaskItemByOrderNos(@Param("orderNoList") List<String> orderNoList,
        @Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId);

    void removePackageByRefOrderNos(@Param("orderNos") List<String> refOrderNos, @Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId);

    List<PackageOrderItemDTO> listPackageItemsByOrderIds(@Param("refOrderIdList") Collection<Long> refOrderIdList,
        @Param("orgId") Integer orgId);

    /**
     * 根据订单详情id删除指定的包装信息
     */
    void batchDelete(@Param("list") List<Long> idList, @Param("orgId") Integer orgId);

    void updateBoxCodeById(PackageOrderItemDTO dto);

    List<PackageOrderItemPO> listSowBoxcodeItems(SecondSortQueryDTO queryDTO);

    PackageOrderItemPO selectByPrimaryKey(@Param("id") Long id);

    /**
     * 查询打包信息
     */
    List<PackageOrderItemPO> listPackageOrderItemByOrderIdList(PackageOrderItemSO packageOrderItemSO);

    /**
     * 获取批次中订单箱最大数
     */
    List<PackageOrderItemPO> getBoxMaxCountByOrderIdList(PackageOrderItemSO packageOrderItemSO);

    /**
     * 通过出库单 id 查询包装箱信息, 只会查询 packageType = 0 或 packageType = null 的数据
     *
     * @param ids 出库单 id
     * @param orgId 城市 id
     */
    List<PackageOrderItemDTO> listBoxesByOrderIds(@Param("refOrderIdList") Collection<Long> ids,
        @Param("orgId") Integer orgId);

    List<Long> findRefItemIdsByOrderNos(@Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId,
        @Param("refOrderNos") List<String> refOrderNos);

    List<Long> findRefIdsByOrderNos(@Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId,
        @Param("refOrderNos") List<String> refOrderNos);

    List<PackageOrderItemPO> findPackageInfoByBoxCodeNo(@Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId, @Param("boxCodeNo") String boxCodeNo,
        @Param("stateList") List<Integer> stateList);

}
