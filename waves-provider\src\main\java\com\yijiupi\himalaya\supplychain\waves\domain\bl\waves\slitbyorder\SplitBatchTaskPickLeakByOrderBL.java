package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.slitbyorder;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.SourceType;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderRequestBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 漏货补发单独生成拣货任务
 * 
 * <AUTHOR>
 * @date 2025/6/13
 */
@Component
public class SplitBatchTaskPickLeakByOrderBL extends SplitBatchTaskByOrderBL {

    @Override
    List<OutStockOrderPO> supportOrderList(SplitBatchTaskByOrderRequestBO bo) {
        List<OutStockOrderPO> pickLeakOrderList = bo.getCreateDTO().getOrders().stream()
            .filter(m -> SourceType.SUPPLY_AGAIN.getValue().equals(m.getOrderSourceType()))
            .collect(Collectors.toList());
        return pickLeakOrderList;
    }

    @Override
    protected List<SplitBatchTaskByOrderResultBO> doSplitBatchTaskByOrder(SplitBatchTaskByOrderRequestBO bo,
        List<OutStockOrderPO> outStockOrderPOList) {
        Map<String, List<OutStockOrderPO>> pickLeakOrderMap = outStockOrderPOList.stream()
            .collect(Collectors.toMap(k -> k.getId().toString(), v -> Collections.singletonList(v)));

        return SplitBatchTaskByOrderResultBO.getPickLeakOrderTaskList(pickLeakOrderMap);
    }
}
