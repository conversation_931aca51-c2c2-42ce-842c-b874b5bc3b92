package com.yijiupi;

import java.util.Collections;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchOrderProcessService;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;
import com.yijiupi.junit.runner.GeneralRunner;

/**
 * <AUTHOR>
 * @title: BatchOrderProcessPassageTest
 * @description:
 * @date 2022-12-29 15:53
 */
@RunWith(GeneralRunner.class)
public class BatchOrderProcessPassageTest {

    @Reference
    private IBatchOrderProcessService iBatchOrderProcessService;

    @Test
    public void createBatchTest() {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setCityId(998);
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setPickingType((byte)1);
        batchCreateDTO.setPickingGroupStrategy((byte)3);
        batchCreateDTO.setPassPickType((byte)1);
        batchCreateDTO.setGroupType(1);
        batchCreateDTO.setOperateUser("临时外包-张裔譞阿里");
        batchCreateDTO.setOrderIdList(Collections.singletonList("998000221229150615"));

        iBatchOrderProcessService.createBatch(batchCreateDTO);

        Assert.assertNotNull(batchCreateDTO);
    }

}
