package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 拆分波次标示
 *
 * <AUTHOR>
 * @date 2019-09-24 14:51
 */
public enum OrderPickFlagEnum {
    /**
     * 按订单拆分
     */
    按订单拆分((byte)1),
    /**
     * 按用户拆分
     */
    按用户拆分((byte)2),
    /**
     * 按集货方式拆分
     */
    按集货方式拆分((byte)3);

    /**
     * type
     */
    private Byte type;

    OrderPickFlagEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

}
