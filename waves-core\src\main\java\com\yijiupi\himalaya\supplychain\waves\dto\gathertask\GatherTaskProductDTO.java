/*
 * @ClassName GatherTaskProductDTO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-10-31 10:56:33
 */
package com.yijiupi.himalaya.supplychain.waves.dto.gathertask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class GatherTaskProductDTO implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 2356774043176587955L;
    /**
     * @Fields id id
     */
    private Long id;
    /**
     * @Fields orgId 城市id
     */
    private Integer orgId;
    /**
     * @Fields gatherTaskId 集货任务id
     */
    private Long gatherTaskId;
    /**
     * @Fields productSkuId 产品ID
     */
    private Long productSkuId;
    /**
     * @Fields productName 产品名称
     */
    private String productName;
    /**
     * @Fields specificationId 规格ID
     */
    private Long specificationId;
    /**
     * @Fields productSpecName 规格名称
     */
    private String productSpecName;
    /**
     * @Fields specQuantity 规格大小单位转换系数
     */
    private BigDecimal specQuantity;
    /**
     * @Fields totalCount 集货总数量
     */
    private BigDecimal totalCount;
    /**
     * @Fields takeCount 已集货总数量
     */
    private BigDecimal takeCount;
    /**
     * @Fields createUser 创建人
     */
    private Long createUser;
    /**
     * @Fields createTime 注册日期
     */
    private Date createTime;
    /**
     * @Fields lastUpdateUser 最后更新人
     */
    private Long lastUpdateUser;
    /**
     * @Fields lastUpdateTime 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 获取 id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 城市id
     */
    public Integer getOrgId() {
        return orgId;
    }

    /**
     * 设置 城市id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 集货任务id
     */
    public Long getGatherTaskId() {
        return gatherTaskId;
    }

    /**
     * 设置 集货任务id
     */
    public void setGatherTaskId(Long gatherTaskId) {
        this.gatherTaskId = gatherTaskId;
    }

    /**
     * 获取 产品ID
     */
    public Long getProductSkuId() {
        return productSkuId;
    }

    /**
     * 设置 产品ID
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 产品名称
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 设置 产品名称
     */
    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    /**
     * 获取 规格ID
     */
    public Long getSpecificationId() {
        return specificationId;
    }

    /**
     * 设置 规格ID
     */
    public void setSpecificationId(Long specificationId) {
        this.specificationId = specificationId;
    }

    /**
     * 获取 规格名称
     */
    public String getProductSpecName() {
        return productSpecName;
    }

    /**
     * 设置 规格名称
     */
    public void setProductSpecName(String productSpecName) {
        this.productSpecName = productSpecName == null ? null : productSpecName.trim();
    }

    /**
     * 获取 规格大小单位转换系数
     */
    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    /**
     * 设置 规格大小单位转换系数
     */
    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    /**
     * 获取 集货总数量
     */
    public BigDecimal getTotalCount() {
        return totalCount;
    }

    /**
     * 设置 集货总数量
     */
    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 获取 已集货总数量
     */
    public BigDecimal getTakeCount() {
        return takeCount;
    }

    /**
     * 设置 已集货总数量
     */
    public void setTakeCount(BigDecimal takeCount) {
        this.takeCount = takeCount;
    }

    /**
     * 获取 创建人
     */
    public Long getCreateUser() {
        return createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 注册日期
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 注册日期
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后更新人
     */
    public Long getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置 最后更新人
     */
    public void setLastUpdateUser(Long lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    /**
     * 获取 最后更新时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置 最后更新时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
}