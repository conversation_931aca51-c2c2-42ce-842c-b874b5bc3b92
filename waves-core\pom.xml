<?xml version="1.0"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yijiupi</groupId>
        <artifactId>himalaya-supplychain-microservice-waves</artifactId>
        <version>2.20.0</version>
    </parent>

    <artifactId>himalaya-supplychain-microservice-waves-core</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-warehouseproduct-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-settings-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-settings-core</artifactId>
        </dependency>
    </dependencies>
</project>
