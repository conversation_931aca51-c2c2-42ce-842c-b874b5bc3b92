package com.yijiupi.himalaya.supplychain.waves.domain.bo.orderpallet;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderPalletInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/23
 */
public class OrderPalletInBatchTaskValidateHelperBO {

    private Long orderId;

    private Long locationId;

    private String palletNo;

    /**
     * 获取
     *
     * @return orderId
     */
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * 设置
     *
     * @param orderId
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取
     *
     * @return locationId
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置
     *
     * @param locationId
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取
     *
     * @return palletNo
     */
    public String getPalletNo() {
        return this.palletNo;
    }

    /**
     * 设置
     *
     * @param palletNo
     */
    public void setPalletNo(String palletNo) {
        this.palletNo = palletNo;
    }

    public static List<OrderPalletInBatchTaskValidateHelperBO> convert(List<OrderPalletInfoDTO> orderPalletInfoDTOS,
        BatchTaskPO batchTaskPO) {
        List<OrderPalletInBatchTaskValidateHelperBO> boList = new ArrayList<>();
        orderPalletInfoDTOS.forEach(orderPalletInfoDTO -> {
            orderPalletInfoDTO.getPalletNoList().forEach(palletNo -> {
                OrderPalletInBatchTaskValidateHelperBO bo = new OrderPalletInBatchTaskValidateHelperBO();
                bo.setPalletNo(palletNo);
                bo.setLocationId(batchTaskPO.getToLocationId());
                bo.setOrderId(orderPalletInfoDTO.getOrderId());
                boList.add(bo);
            });
        });

        return boList;
    }
}
