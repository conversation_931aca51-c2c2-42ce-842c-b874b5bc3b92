package com.yijiupi.himalaya.supplychain.virtualwarehouse.domain.bl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/3/14
 */
@Service
public class TestService {

    @DistributeLock(conditions = "#orderNoList", expireMills = 300000, sleepMills = 30000,
        key = "completeSowTaskItemsPDAWithLock", lockType = DistributeLock.LockType.WAITLOCK)
    public void test(List<String> orderNoList) {
        System.err
            .println("start ================ " + Thread.currentThread().getId() + "   " + System.currentTimeMillis());
        try {
            Thread.sleep(3000);
        } catch (Exception e) {

        }
        System.err
            .println("end ================ " + Thread.currentThread().getId() + "   " + System.currentTimeMillis());
    }

}
