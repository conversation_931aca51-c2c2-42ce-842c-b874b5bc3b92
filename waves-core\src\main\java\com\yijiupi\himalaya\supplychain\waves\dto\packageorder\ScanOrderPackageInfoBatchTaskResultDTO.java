package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.io.Serializable;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
public class ScanOrderPackageInfoBatchTaskResultDTO implements Serializable {
    /**
     * 出库单id
     */
    private Long orderId;
    /**
     * 拣货任务id
     */
    private String batchTaskId;
    /**
     * 出库单号
     */
    private String orderNo;
    /**
     * 出库位id
     */
    private Long locationId;
    /**
     * 出库位名称
     */
    private String locationName;

    /**
     * 获取 出库单id
     *
     * @return orderId 出库单id
     */
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 出库单id
     *
     * @param orderId 出库单id
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 拣货任务id
     *
     * @return batchTaskId 拣货任务id
     */
    public String getBatchTaskId() {
        return this.batchTaskId;
    }

    /**
     * 设置 拣货任务id
     *
     * @param batchTaskId 拣货任务id
     */
    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    /**
     * 获取 出库单号
     *
     * @return orderNo 出库单号
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置 出库单号
     *
     * @param orderNo 出库单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取 出库位id
     *
     * @return locationId 出库位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 出库位id
     *
     * @param locationId 出库位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 出库位名称
     *
     * @return locationName 出库位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 出库位名称
     *
     * @param locationName 出库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
