package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.split;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchSowTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskKindOfPickingConstants;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.constants.WarehouseConfigConstants;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationLargePickCargoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;
import com.yijiupi.himalaya.supplychain.waves.util.OrderRebuildUtil;
import com.yijiupi.himalaya.supplychain.waves.util.RobotPickConstants;

/**
 * <AUTHOR>
 * @title: WaveSplitRobotBL
 * @description:
 * @date 2023-02-09 11:34
 */
@Service
public class WaveSplitRobotBL {

    @Reference
    private ILocationService iLocationService;
    @Reference
    private IProductLocationService iProductLocationService;

    @Autowired
    private CreateBatchLocationLargePickCargoBL createBatchLocationLargePickCargoBL;
    private static final Logger LOG = LoggerFactory.getLogger(WaveSplitRobotBL.class);

    /**
     * 如果 整件拆零 开启了机器人配置，则拆分拣货任务 如果仓库开启了WCS，且通道开启了机器人拣货，且拣货货位是周转箱，则这部分订单项汇总出来生成机器人零拣任务，否则生成人工零拣任务。
     *
     *
     * @param oriWaveCreateDTO
     * @param splitOrderList
     * @param otherItemList
     * @return
     */
    public List<WaveCreateDTO> splitOtherWave(WaveCreateDTO oriWaveCreateDTO, List<OutStockOrderPO> splitOrderList,
        List<OutStockOrderItemPO> otherItemList) {
        List<WaveCreateDTO> waveList = new ArrayList<>();
        if (BooleanUtils.isFalse(RobotPickConstants.isRobotPick(oriWaveCreateDTO, oriWaveCreateDTO.getPassageDTO()))) {
            waveList.addAll(splitUnitAndNotPickPattern(oriWaveCreateDTO, splitOrderList, otherItemList));
            return waveList;
        }
        List<Long> locationIds =
            otherItemList.stream().map(OutStockOrderItemPO::getLocationId).distinct().collect(Collectors.toList());

        List<LoactionDTO> locationList = iLocationService.findLocationByIds(locationIds);
        Map<Long,
            Long> locationMap = locationList.stream().filter(m -> Objects.nonNull(m.getCategory()))
                .filter(m -> CategoryEnum.CARGO_VESSEL.getValue() == m.getCategory().intValue())
                .collect(Collectors.toMap(LoactionDTO::getId, LoactionDTO::getId));

        if (CollectionUtils.isEmpty(locationMap)) {
            waveList.addAll(splitUnitAndNotPickPattern(oriWaveCreateDTO, splitOrderList, otherItemList));
            return waveList;
        }

        List<OutStockOrderItemPO> noRobotItemList = otherItemList.stream()
            .filter(m -> Objects.isNull(locationMap.get(m.getLocationId()))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(noRobotItemList)) {
            waveList.addAll(splitUnitAndNotPickPattern(oriWaveCreateDTO, splitOrderList, noRobotItemList));
        }

        List<OutStockOrderItemPO> robotItemList = otherItemList.stream()
            .filter(m -> Objects.nonNull(locationMap.get(m.getLocationId()))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(robotItemList)) {
            waveList.add(createDTO(oriWaveCreateDTO, splitOrderList, robotItemList,
                BatchTaskItemLargePickPatternConstants.LARGE_PICK_SMALL_ROBOT));
        }

        return waveList;
    }

    public List<WaveCreateDTO> splitUnitAndNotPickPattern(WaveCreateDTO oriWaveCreateDTO,
        List<OutStockOrderPO> splitOrderList, List<OutStockOrderItemPO> otherItemList) {
        // 区分出小件 和 没标志的
        List<OutStockOrderItemPO> smallPatternItemList = otherItemList.stream()
            .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_SMALL_HUMAN.equals(m.getLargePick()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(smallPatternItemList)) {
            return Collections.singletonList(createDTO(oriWaveCreateDTO, splitOrderList, otherItemList,
                BatchTaskItemLargePickPatternConstants.LARGE_PICK_NONE));
        }
        List<OutStockOrderItemPO> nonePatternItemList = otherItemList.stream()
            .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_NONE.equals(m.getLargePick()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nonePatternItemList)) {
            return Collections.singletonList(createDTO(oriWaveCreateDTO, splitOrderList, otherItemList,
                BatchTaskItemLargePickPatternConstants.LARGE_PICK_SMALL_HUMAN));
        }

        List<WaveCreateDTO> waveList = new ArrayList<>();
        waveList.add(createDTO(oriWaveCreateDTO, splitOrderList, smallPatternItemList,
            BatchTaskItemLargePickPatternConstants.LARGE_PICK_SMALL_HUMAN));
        waveList.add(createDTO(oriWaveCreateDTO, splitOrderList, nonePatternItemList,
            BatchTaskItemLargePickPatternConstants.LARGE_PICK_NONE));

        return waveList;
    }

    /**
     * 为了供应链客户端播种，这种不进整件拆零逻辑的订单项，也必须设置batchtaskItem种的 largePickPattern标志，不能简单设置为0，否则前端区分不开
     * 
     * @param batchTaskItemList
     * @param passageDTO
     * @param waveOrderItems
     */
    public void resetLargePickPattern(List<BatchTaskItemDTO> batchTaskItemList, PassageDTO passageDTO,
        WaveCreateDTO waveCreateDTO, List<OutStockOrderItemPO> waveOrderItems) {
        resetNoneLargePickPattern(batchTaskItemList, passageDTO, waveCreateDTO, waveOrderItems);
        resetHumanLargePickPattern(batchTaskItemList, passageDTO, waveCreateDTO, waveOrderItems);
    }

    private void resetNoneLargePickPattern(List<BatchTaskItemDTO> batchTaskItemList, PassageDTO passageDTO,
        WaveCreateDTO waveCreateDTO, List<OutStockOrderItemPO> waveOrderItems) {
        Byte largePickPattern = waveCreateDTO.getLargePick();
        if (Objects.isNull(largePickPattern)) {
            return;
        }

        if (BooleanUtils.isFalse(RobotPickConstants.isRobotPick(waveCreateDTO, passageDTO))) {
            return;
        }

        if (!BatchTaskItemLargePickPatternConstants.LARGE_PICK_NONE.equals(largePickPattern)) {
            return;
        }

        for (BatchTaskItemDTO batchTaskItemDTO : batchTaskItemList) {
            batchTaskItemDTO.setLargePickPattern(BatchTaskItemLargePickPatternConstants.LARGE_PICK_NONE);
        }
    }

    private void resetHumanLargePickPattern(List<BatchTaskItemDTO> batchTaskItemList, PassageDTO passageDTO,
        WaveCreateDTO waveCreateDTO, List<OutStockOrderItemPO> waveOrderItems) {
        Byte largePickPattern = waveCreateDTO.getLargePick();
        if (Objects.isNull(largePickPattern)) {
            return;
        }

        if (BooleanUtils.isFalse(RobotPickConstants.isRobotPick(waveCreateDTO, passageDTO))) {
            return;
        }

        if (!BatchTaskItemLargePickPatternConstants.LARGE_PICK_SMALL_HUMAN.equals(largePickPattern)) {
            return;
        }

        Map<String, List<OutStockOrderItemPO>> orderItemGroupMap = waveOrderItems.stream()
            .collect(Collectors.groupingBy(item -> item.getOutstockorderId() + String.valueOf(item.getSkuid())));

        for (BatchTaskItemDTO batchTaskItemDTO : batchTaskItemList) {
            batchTaskItemDTO.setLargePickPattern(BatchTaskItemLargePickPatternConstants.LARGE_PICK_NONE);
        }

        //
        for (Map.Entry<String, List<OutStockOrderItemPO>> entry : orderItemGroupMap.entrySet()) {
            List<OutStockOrderItemPO> outStockOrderItemList = entry.getValue();
            if (outStockOrderItemList.size() == 1) {
                continue;
            }
            BigDecimal unitTotalCount = outStockOrderItemList.stream().map(OutStockOrderItemPO::getUnittotalcount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal[] count = unitTotalCount.divideAndRemainder(outStockOrderItemList.get(0).getSpecquantity());
            if (count[0].compareTo(BigDecimal.ZERO) != 0) {
                outStockOrderItemList.forEach(item -> {
                    item.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_NONE);
                });
            }

        }

    }

    private WaveCreateDTO createDTO(WaveCreateDTO oriWaveCreateDTO, List<OutStockOrderPO> splitOrderList,
        List<OutStockOrderItemPO> otherItemList, Byte largePick) {
        WaveCreateDTO otherCreateDTO = new WaveCreateDTO();
        BeanUtils.copyProperties(oriWaveCreateDTO, otherCreateDTO);
        otherCreateDTO.setPassageDTO(oriWaveCreateDTO.getPassageDTO());
        otherCreateDTO.setWavesStrategyDTO(oriWaveCreateDTO.getWavesStrategyDTO());
        List<OutStockOrderPO> otherOrderList = OrderRebuildUtil.copyOrderItemCreateOrder(splitOrderList, otherItemList);

        otherCreateDTO.setLargePick(largePick);
        otherCreateDTO.setOrders(otherOrderList);

        return otherCreateDTO;
    }

    // 开启了液晶屏的，先把整件的全部拿出来，设置货位为存储区，按订单生成拣货任务，并设置出库位为周转区
    public WaveCreateDTO createLiquidNotPackageWaveCreateDTO(WarehouseConfigDTO warehouseConfigDTO,
        List<OutStockOrderItemPO> totalOrderItems, WaveCreateDTO createDTO, List<OutStockOrderPO> orders) {
        if (!RobotPickConstants.openLiquidNotPackage(warehouseConfigDTO, createDTO.getWavesStrategyDTO())) {
            return null;
        }

        List<OutStockOrderItemPO> packageStoreItemList = totalOrderItems.stream()
            .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(packageStoreItemList)) {
            return null;
        }

        // packageStoreItemList = createBatchLocationLargePickCargoBL.setPackageLocationByAssociateLocation(
        // packageStoreItemList, totalOrderItems, createDTO.getWavesStrategyDTO(), warehouseConfigDTO);

        WavesStrategyBO wavesStrategyDTO = createDTO.getWavesStrategyDTO();

        // FIXME
        List<OutStockOrderPO> packageOrderList = OrderRebuildUtil.getOrdersByPassageItems(orders, packageStoreItemList,
            warehouseConfigDTO, wavesStrategyDTO);

        WavesStrategyBO newWavesStrategyDTO = new WavesStrategyBO();
        BeanUtils.copyProperties(wavesStrategyDTO, newWavesStrategyDTO);
        newWavesStrategyDTO.setPickingType((byte)WavesStrategyConstants.PICKINGTYPE_ORDER);

        WaveCreateDTO newCreateDTO = new WaveCreateDTO();
        newCreateDTO.setOrders(packageOrderList);
        newCreateDTO.setWavesStrategyDTO(newWavesStrategyDTO);
        newCreateDTO.setOperateUser(createDTO.getOperateUser());
        newCreateDTO.setOperateUserId(createDTO.getOperateUserId());
        newCreateDTO.setTitle(createDTO.getTitle());
        newCreateDTO.setCityId(createDTO.getCityId());
        newCreateDTO.setLocationName(createDTO.getLocationName());
        newCreateDTO.setDriverName(createDTO.getDriverName());
        newCreateDTO.setAllocationFlag(createDTO.getAllocationFlag());
        // newCreateDTO.setToLocationId(processBatchDTO.getToLocationId());
        // newCreateDTO.setToLocationName(processBatchDTO.getToLocationName());
        // newCreateDTO.setToWarehouseId(outStockOrderList.get(0).getToWarehouseId());
        // newCreateDTO.setToWarehouseName(outStockOrderList.get(0).getToWarehouseName());
        newCreateDTO.setIsAwardOrderTask(Boolean.FALSE);

        LoactionDTO loactionDTO =
            getZzLocationInfo(warehouseConfigDTO.getWarehouse_Id(), warehouseConfigDTO.getOrg_Id());

        if (Objects.nonNull(loactionDTO)) {
            newCreateDTO.setToLocationId(loactionDTO.getId());
            newCreateDTO.setToLocationName(loactionDTO.getName());
        }

        return newCreateDTO;
    }

    private LoactionDTO getZzLocationInfo(Integer warehouseId, Integer cityId) {
        LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
        locationQueryDTO.setWarehouseId(warehouseId);
        locationQueryDTO.setCityId(cityId);
        locationQueryDTO.setLocSubcategory(LocationAreaEnum.周转区.getType().byteValue());
        List<LoactionDTO> loactionDTOList = iLocationService.findLocationListByIdAndCategory(locationQueryDTO);
        if (CollectionUtils.isEmpty(loactionDTOList)) {
            LOG.info("没有周转区，仓库id:{}", warehouseId);
            return null;
        }

        return loactionDTOList.stream().findAny().get();
    }

}
