package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 团购订单摘果列表对象
 *
 * <AUTHOR>
 * @date 2/5/21 5:12 PM
 */
public class GroupBuyBatchTaskDTO implements Serializable {

    private static final long serialVersionUID = -5352182583701080429L;

    /**
     * 拣货任务id
     */
    private String id;

    /**
     * 波次任务号
     */
    private String batchTaskNo;

    /**
     * 波次编号
     */
    private String batchNo;

    /**
     * 分拣员id
     */
    private Integer sorterId;

    /**
     * 分拣员
     */
    private String sorterName;

    /**
     * 商品种类数量
     */
    private Integer skuCount;

    /**
     * 大件总数
     */
    private BigDecimal packageAmount;

    /**
     * 小件总数
     */
    private BigDecimal unitAmount;

    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte taskState;

    /**
     * 拣货方式 1 按订单拣货 2 按产品拣货',
     */
    private Byte pickingType;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 线路名称
     */
    private String routeName;

    /**
     * 订单筛选 1 按区域 2 按线路
     */
    private Byte orderSelection;

    /**
     * 出库位id
     */
    private Long toLocationId;

    /**
     * 出库位名称
     */
    private String toLocationName;

    /**
     * 拣货任务类型 0: 按线路摘果 1: 按自提点摘果
     */
    private Byte batchTaskType;

    /**
     * 通道Id
     */
    private Long passageId;

    /**
     * 通道号
     */
    private String passageName;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 车牌号
     */
    private String carNumber;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Integer getSorterId() {
        return sorterId;
    }

    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    public String getSorterName() {
        return sorterName;
    }

    public void setSorterName(String sorterName) {
        this.sorterName = sorterName;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public BigDecimal getPackageAmount() {
        return packageAmount;
    }

    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    public BigDecimal getUnitAmount() {
        return unitAmount;
    }

    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    public Byte getTaskState() {
        return taskState;
    }

    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    public Byte getPickingType() {
        return pickingType;
    }

    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public Byte getOrderSelection() {
        return orderSelection;
    }

    public void setOrderSelection(Byte orderSelection) {
        this.orderSelection = orderSelection;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public Byte getBatchTaskType() {
        return batchTaskType;
    }

    public void setBatchTaskType(Byte batchTaskType) {
        this.batchTaskType = batchTaskType;
    }

    public Long getPassageId() {
        return passageId;
    }

    public void setPassageId(Long passageId) {
        this.passageId = passageId;
    }

    public String getPassageName() {
        return passageName;
    }

    public void setPassageName(String passageName) {
        this.passageName = passageName;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getCarNumber() {
        return carNumber;
    }

    public void setCarNumber(String carNumber) {
        this.carNumber = carNumber;
    }
}
