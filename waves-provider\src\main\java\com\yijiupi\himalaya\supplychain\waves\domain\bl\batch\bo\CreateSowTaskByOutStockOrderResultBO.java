package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo;

import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 通过订单创建播种任务的结果BO
 * 
 * <AUTHOR>
 * @date 2024/8/18
 */
public class CreateSowTaskByOutStockOrderResultBO {

    private WaveCreateDTO waveCreateDTO;

    private List<SowTaskPO> sowTaskPOList;

    private List<SowOrderPO> sowOrdersList;

    /**
     * 获取
     *
     * @return waveCreateDTO
     */
    public WaveCreateDTO getWaveCreateDTO() {
        return this.waveCreateDTO;
    }

    /**
     * 设置
     *
     * @param waveCreateDTO
     */
    public void setWaveCreateDTO(WaveCreateDTO waveCreateDTO) {
        this.waveCreateDTO = waveCreateDTO;
    }

    /**
     * 获取
     *
     * @return sowTaskPOList
     */
    public List<SowTaskPO> getSowTaskPOList() {
        return this.sowTaskPOList;
    }

    /**
     * 设置
     *
     * @param sowTaskPOList
     */
    public void setSowTaskPOList(List<SowTaskPO> sowTaskPOList) {
        this.sowTaskPOList = sowTaskPOList;
    }

    /**
     * 获取
     *
     * @return sowOrdersList
     */
    public List<SowOrderPO> getSowOrdersList() {
        return this.sowOrdersList;
    }

    /**
     * 设置
     *
     * @param sowOrdersList
     */
    public void setSowOrdersList(List<SowOrderPO> sowOrdersList) {
        this.sowOrdersList = sowOrdersList;
    }
}
