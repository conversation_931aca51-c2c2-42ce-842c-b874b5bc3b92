package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/18
 */
public class ScanOrderForReviewQueryDTO implements Serializable {

    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 订单号/箱号
     */
    private String scanCode;
    /**
     * 组织机构id
     */
    private Integer orgId;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 订单号箱号
     *
     * @return scanCode 订单号箱号
     */
    public String getScanCode() {
        return this.scanCode;
    }

    /**
     * 设置 订单号箱号
     *
     * @param scanCode 订单号箱号
     */
    public void setScanCode(String scanCode) {
        this.scanCode = scanCode;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }
}
