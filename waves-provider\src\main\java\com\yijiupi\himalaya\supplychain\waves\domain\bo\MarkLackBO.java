package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.ordercenter.consant.SyncTraceTypeConstants;
import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.PartSendWsmDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/7
 */
public class MarkLackBO {

    List<PartSendWsmDTO> partSendWsmDTOList;

    Map<String, StringBuffer> bufferMap;

    Integer operatorUserId;

    Integer warehouseId;

    private Integer traceType;

    public MarkLackBO() {}

    public MarkLackBO(List<PartSendWsmDTO> partSendWsmDTOList, Map<String, StringBuffer> bufferMap,
        Integer operatorUserId) {
        this.partSendWsmDTOList = partSendWsmDTOList;
        this.bufferMap = bufferMap;
        this.operatorUserId = operatorUserId;
    }

    public MarkLackBO(List<PartSendWsmDTO> partSendWsmDTOList, Map<String, StringBuffer> bufferMap,
        Integer operatorUserId, Integer traceType) {
        this.partSendWsmDTOList = partSendWsmDTOList;
        this.bufferMap = bufferMap;
        this.operatorUserId = operatorUserId;
        this.traceType = traceType;
    }

    public static MarkLackBO ofWarehouseLack(Map<String, StringBuffer> map, Integer userId, PartSendWsmDTO... parts) {
        ArrayList<PartSendWsmDTO> partList = new ArrayList<>(Arrays.asList(parts));
        return new MarkLackBO(partList, map, userId, SyncTraceTypeConstants.WAREHOUSE_LACK_GOODS);
    }

    /**
     * 获取
     *
     * @return partSendWsmDTOList
     */
    public List<PartSendWsmDTO> getPartSendWsmDTOList() {
        return this.partSendWsmDTOList;
    }

    /**
     * 设置
     *
     * @param partSendWsmDTOList
     */
    public void setPartSendWsmDTOList(List<PartSendWsmDTO> partSendWsmDTOList) {
        this.partSendWsmDTOList = partSendWsmDTOList;
    }

    /**
     * 获取
     *
     * @return bufferMap
     */
    public Map<String, StringBuffer> getBufferMap() {
        return this.bufferMap;
    }

    /**
     * 设置
     *
     * @param bufferMap
     */
    public void setBufferMap(Map<String, StringBuffer> bufferMap) {
        this.bufferMap = bufferMap;
    }

    /**
     * 获取
     *
     * @return operatorUserId
     */
    public Integer getOperatorUserId() {
        return this.operatorUserId;
    }

    /**
     * 设置
     *
     * @param operatorUserId
     */
    public void setOperatorUserId(Integer operatorUserId) {
        this.operatorUserId = operatorUserId;
    }

    /**
     * 获取
     *
     * @return traceType
     */
    public Integer getTraceType() {
        return this.traceType;
    }

    /**
     * 设置
     *
     * @param traceType
     */
    public void setTraceType(Integer traceType) {
        this.traceType = traceType;
    }

    /**
     * 获取
     *
     * @return warehouseId
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置
     *
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
