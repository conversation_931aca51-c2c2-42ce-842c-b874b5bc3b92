package com.yijiupi.himalaya.supplychain.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.Pager;
import com.yijiupi.himalaya.supplychain.controller.sow.BatchTaskSowNoParam;
import com.yijiupi.himalaya.supplychain.controller.sow.SowTaskReceiveParamDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.waves.batch.ISowQueryService;
import com.yijiupi.himalaya.supplychain.waves.concertsow.dto.ConcertSowTaskRequestDTO;
import com.yijiupi.himalaya.supplychain.waves.concertsow.service.IConcertSowTaskService;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.PDASowTaskInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskReceiveDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.supplychain.serviceutils.ThreadLocalUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 多人协作播种
 */
@Service
public class ConcertSowManageService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConcertSowManageService.class);

    @Reference(timeout = 30000)
    private IConcertSowTaskService iConcertSowTaskService;
    @Reference
    private IProductSkuService iProductSkuService;
    @Reference
    private ISowQueryService iSowQueryService;

    @Autowired
    private GlobalCache globalCache;

    /**
     * 领取播种任务明细
     *
     * @param request
     */
    public void getSowTask(ConcertSowTaskRequestParamDTO request) {
        ConcertSowTaskRequestDTO concertSowTaskRequestDTO = new ConcertSowTaskRequestDTO();
        BeanUtils.copyProperties(request, concertSowTaskRequestDTO);
        Integer userId = ThreadLocalUtil.getCurrentUserInfo();
        concertSowTaskRequestDTO.setUserId(Long.valueOf(userId));
        concertSowTaskRequestDTO.setUserName(globalCache.getUserName(userId));
        iConcertSowTaskService.getSowTask(concertSowTaskRequestDTO);
    }

    /**
     * 待播种任务查询
     */
    public SowTaskDTO queryWaitSowTask(SowTaskReceiveParamDTO sowTaskReceiveParam) {
        LOGGER.info("ConcertSowManageService.queryWaitSowTask 待播种任务查询，入参:{}", JSON.toJSONString(sowTaskReceiveParam));
        SowTaskReceiveDTO sowTaskReceiveDTO = new SowTaskReceiveDTO();
        BeanUtils.copyProperties(sowTaskReceiveParam, sowTaskReceiveDTO);
        Integer userId = ThreadLocalUtil.getCurrentUserInfo();
        sowTaskReceiveDTO.setSower(globalCache.getUserName(userId));
        sowTaskReceiveDTO.setSowerId(userId);
        String code = sowTaskReceiveParam.getCode();
        if (code != null) {
            List<Long> skuIds = iProductSkuService.getProductSkuIdByCode(code, sowTaskReceiveParam.getOrgId());
            sowTaskReceiveDTO.setSkuIds(skuIds);
        }
        return iConcertSowTaskService.queryWaitSowTask(sowTaskReceiveDTO);
    }

    public List<PDASowTaskInfoDTO> listBatchTaskBySowTaskNo(BatchTaskSowNoParam batchTaskSowNoParam) {
        LOGGER.info("ConcertSowManageService.listBatchTaskBySowTaskNo 查询波次任务列表，入参:{}",
            JSON.toJSONString(batchTaskSowNoParam));
        SowTaskQueryDTO sowTaskQueryDTO = new SowTaskQueryDTO();
        sowTaskQueryDTO.setSowTaskNo(batchTaskSowNoParam.getSowTaskNo());
        sowTaskQueryDTO.setOrgId(batchTaskSowNoParam.getCityId());
        Integer userId = ThreadLocalUtil.getCurrentUserInfo();
        sowTaskQueryDTO.setOperatorName(globalCache.getUserName(userId));
        sowTaskQueryDTO.setOperatorId(userId);
        sowTaskQueryDTO.setSowTaskName(batchTaskSowNoParam.getSowTaskName());
        return iConcertSowTaskService.listBatchTaskBySowTaskNos(sowTaskQueryDTO);
    }

    public PageList<BatchTaskDTO> listBatchTaskInfo(BatchTaskSowNoParam batchTaskSowNoParam) {
        LOGGER.info("二次分拣查询波次任务:{}", JSON.toJSONString(batchTaskSowNoParam));
        SowTaskQueryDTO sowTaskQueryDTO = new SowTaskQueryDTO();
        sowTaskQueryDTO.setSowTaskNo(batchTaskSowNoParam.getSowTaskNo());
        sowTaskQueryDTO.setOrgId(batchTaskSowNoParam.getCityId());
        Integer userId = ThreadLocalUtil.getCurrentUserInfo();
        // 2025-02-17 注意！！！后续只能查到操作人是当前用户的数据，不能查到其他人的数据
        sowTaskQueryDTO.setOperatorName(globalCache.getUserName(userId));
        sowTaskQueryDTO.setOperatorId(userId);
        sowTaskQueryDTO.setSowTaskName(batchTaskSowNoParam.getSowTaskName());
        sowTaskQueryDTO.setWarehouseId(batchTaskSowNoParam.getWarehouseId());


        if (StringUtils.isNotEmpty(batchTaskSowNoParam.getContent())) {
            // 1、如果查询内容全部为数字，则表示按条码查询
            if (batchTaskSowNoParam.getContent().matches("^[0-9]+$")) {
                LOGGER.info("按条码查询...");
                List<Long> skuIdList = iProductSkuService.getProductSkuIdByCode(batchTaskSowNoParam.getContent(), batchTaskSowNoParam.getCityId());
                LOGGER.info("getSkuIds - 按条码查询skuIdList：{}", JSON.toJSONString(skuIdList));
                if (CollectionUtils.isEmpty(skuIdList)) {
                    PageList<BatchTaskDTO> batchTaskDTOPageList = new PageList<>();
                    batchTaskDTOPageList.setDataList(Collections.emptyList());
                    batchTaskDTOPageList.setPager(new Pager(batchTaskSowNoParam.getPageNum(), batchTaskSowNoParam.getPageSize(), 0));
                    return batchTaskDTOPageList;
                }
                sowTaskQueryDTO.setSkuIds(skuIdList);
            } else {
                sowTaskQueryDTO.setProductName(batchTaskSowNoParam.getContent());
            }
        }

        if(CollectionUtils.isNotEmpty(batchTaskSowNoParam.getTaskStates())){
            List<Byte> states = batchTaskSowNoParam.getTaskStates().stream().map(String::valueOf).map(Byte::valueOf).collect(Collectors.toList());
            sowTaskQueryDTO.setStates(states);
        }

        sowTaskQueryDTO.setPageNum(batchTaskSowNoParam.getPageNum());
        sowTaskQueryDTO.setPageSize(batchTaskSowNoParam.getPageSize());

        if (isAlreadyPick(sowTaskQueryDTO)) {
            return findSowTaskList(sowTaskQueryDTO);
        }

        PageList<BatchTaskDTO> batchTaskDTOPageList = iConcertSowTaskService.listBatchTaskInfo(sowTaskQueryDTO);
        LOGGER.info("总单二次分拣查询:>>{}",JSON.toJSONString(batchTaskDTOPageList));
        return batchTaskDTOPageList;
    }

    private boolean isAlreadyPick(SowTaskQueryDTO sowTaskQueryDTO) {
        if (Objects.equals(sowTaskQueryDTO.getSowTaskName(), "总单二次分拣")) {
            return Boolean.FALSE;
        }
        if (CollectionUtils.isEmpty(sowTaskQueryDTO.getStates()) || sowTaskQueryDTO.getStates().size() > 1) {
            return Boolean.FALSE;
        }

        return sowTaskQueryDTO.getStates().contains(SowTaskStateEnum.已播种.getType());
    }

    private PageList<BatchTaskDTO> findSowTaskList(SowTaskQueryDTO sowTaskQueryDTO) {
        PageList<SowTaskDTO> pageList = iSowQueryService.findSowTaskList(sowTaskQueryDTO);
        PageList<BatchTaskDTO> result = new PageList<>();
        if (CollectionUtils.isEmpty(pageList.getDataList())) {
            result.setDataList(Collections.emptyList());
            result.setPager(new Pager(sowTaskQueryDTO.getCurrentPage(), sowTaskQueryDTO.getPageSize(), 0));
            return result;
        }

        result.setDataList(convertToBatchTaskDTOList(pageList.getDataList()));
        result.setPager(pageList.getPager());
        return result;
    }

    private List<BatchTaskDTO> convertToBatchTaskDTOList(List<SowTaskDTO> sowTaskDTOS) {
        return sowTaskDTOS.stream().map(sow -> {
            BatchTaskDTO batchTaskDTO = new BatchTaskDTO();
            batchTaskDTO.setId(sow.getId().toString());
            batchTaskDTO.setSowTaskNo(sow.getSowTaskNo());
            batchTaskDTO.setSowTaskId(sow.getId());
            batchTaskDTO.setBatchTaskName(sow.getSowTaskName());
            batchTaskDTO.setBatchNo(sow.getBatchNo());
            batchTaskDTO.setOrderCount(sow.getOrderCount());
            batchTaskDTO.setPackageAmount(sow.getPackageAmount());
            batchTaskDTO.setUnitAmount(sow.getUnitAmount());
            batchTaskDTO.setSorter(sow.getOperatorName());
            batchTaskDTO.setSorterId(sow.getOperatorId());
            batchTaskDTO.setSkuCount(sow.getSkuCount());
            batchTaskDTO.setSowLocationId(sow.getLocationId());
            batchTaskDTO.setSowLocationName(sow.getLocationName());

            return batchTaskDTO;
        }).collect(Collectors.toList());
    }



    private void setSowTaskQuerySkuInfo(BatchTaskSowNoParam param, SowTaskQueryDTO sowTaskQueryDTO) {
        if (StringUtils.isBlank(param.getContent())) {
            return;
        }
        // 1、如果查询内容全部为数字，则表示按条码查询
        if (param.getContent().matches("^[0-9]+$")) {
            LOGGER.info("按条码查询...");
            List<Long> skuIdList = iProductSkuService.getProductSkuIdByCode(param.getContent(), param.getCityId());
            LOGGER.info("getSkuIds - 按条码查询skuIdList：{}", JSON.toJSONString(skuIdList));
            if (CollectionUtils.isNotEmpty(skuIdList)) {
                sowTaskQueryDTO.setSkuIds(skuIdList);
            }
        } else {
            sowTaskQueryDTO.setProductName(param.getContent());
        }
    }



}
