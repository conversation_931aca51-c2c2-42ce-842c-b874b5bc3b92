package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.util.Collections;
import java.util.List;

import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderitemdetail.OutStockOrderItemDetailModBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.RecoverOrderItemDetailBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/4/2
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class OutStockOrderItemDetailModBLTest {

    @Autowired
    private OutStockOrderItemDetailModBL outStockOrderItemDetailModBL;
    @Autowired
    private OutStockOrderBL outStockOrderBL;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    @Test
    public void lackFailedRecoverDetailTest() {
        RecoverOrderItemDetailBO bo = new RecoverOrderItemDetailBO();
        bo.setBatchNo("BC998124090600001");
        bo.setBusinessIds(Collections.singletonList("5305157387501013545"));
        bo.setWarehouseId(9981);
        // bo.setRefOrderNos();

        outStockOrderItemDetailModBL.lackFailedRecoverDetail(bo);

        Assertions.assertThat(bo).isNotNull();
    }

    @Test
    public void updateOutStockOrderItemDetailTest() {
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList = orderItemTaskInfoMapper
            .listTaskInfoAndDetailByOrderItemIds(Collections.singletonList(5305157405159599724L));
        outStockOrderBL.updateOutStockOrderItemDetail(orderItemTaskInfoPOList);

    }

    @Test
    public void recoverDetailTest() {
        RecoverOrderItemDetailBO bo = new RecoverOrderItemDetailBO();
        bo.setOutStockOrderIds(Collections.singletonList(5305157404796482380L));
        bo.setWarehouseId(bo.getWarehouseId());
        bo.setBatchNo("1");
        bo.setUseOrderCenterDetail(Boolean.FALSE);
        bo.setHandleEqualDetail(Boolean.TRUE);
        outStockOrderItemDetailModBL.recoverDetail(bo);
    }


}
