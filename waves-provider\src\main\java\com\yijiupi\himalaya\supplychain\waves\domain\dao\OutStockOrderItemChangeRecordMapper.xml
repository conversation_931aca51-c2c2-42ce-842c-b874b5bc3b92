<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemChangeRecordMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemChangeRecordPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="INTEGER" property="orgId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="RefOrder_Id" jdbcType="BIGINT" property="refOrderId"/>
        <result column="RefOrderNo" jdbcType="VARCHAR" property="refOrderNo"/>
        <result column="RefOrderItem_Id" jdbcType="BIGINT" property="refOrderItemId"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productSkuId"/>
        <result column="ProductName" jdbcType="VARCHAR" property="productName"/>
        <result column="SpecName" jdbcType="VARCHAR" property="specName"/>
        <result column="SpecQuantity" jdbcType="DECIMAL" property="specQuantity"/>
        <result column="PackageName" jdbcType="VARCHAR" property="packageName"/>
        <result column="UnitName" jdbcType="VARCHAR" property="unitName"/>
        <result column="PackageCount" jdbcType="DECIMAL" property="packageCount"/>
        <result column="UnitCount" jdbcType="DECIMAL" property="unitCount"/>
        <result column="UnitTotalCount" jdbcType="DECIMAL" property="unitTotalCount"/>
        <result column="Batch_Id" jdbcType="VARCHAR" property="batchId"/>
        <result column="BatchNo" jdbcType="VARCHAR" property="batchNo"/>
        <result column="BatchTask_Id" jdbcType="VARCHAR" property="batchTaskId"/>
        <result column="BatchTaskNo" jdbcType="VARCHAR" property="batchTaskNo"/>
        <result column="SowTask_Id" jdbcType="BIGINT" property="sowTaskId"/>
        <result column="SowTaskNo" jdbcType="VARCHAR" property="sowTaskNo"/>
        <result column="TransferOrder_Id" jdbcType="BIGINT" property="transferOrderId"/>
        <result column="TransferOrderNo" jdbcType="VARCHAR" property="transferOrderNo"/>
        <result column="ChangeType" jdbcType="TINYINT" property="changeType"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="Location_Id" jdbcType="BIGINT" property="locationId"/>
        <result column="LocationName" jdbcType="VARCHAR" property="locationName"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Org_Id, Warehouse_Id, RefOrder_Id, RefOrderNo, RefOrderItem_Id, ProductSku_Id, ProductName,
        SpecName, SpecQuantity, PackageName, UnitName, PackageCount, UnitCount, UnitTotalCount,
        Batch_Id, BatchNo, BatchTask_Id, BatchTaskNo, SowTask_Id, SowTaskNo, TransferOrder_Id,
        TransferOrderNo, ChangeType, Remark, CreateTime, CreateUser, LastUpdateTime, LastUpdateUser, Location_Id,
        LocationName
    </sql>

    <select id="listOrderItemChangeRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from outstockorderitemchangerecord
        <where>
            <if test="orgId != null">
                Org_Id = #{orgId,jdbcType=INTEGER}
            </if>
            <if test="batchTaskId != null">
                and BatchTask_Id = #{batchTaskId,jdbcType=VARCHAR}
            </if>
            <if test="sowTaskId != null">
                and SowTask_Id = #{sowTaskId,jdbcType=VARCHAR}
            </if>
            <if test="transferOrderId != null">
                and TransferOrder_Id = #{transferOrderId,jdbcType=VARCHAR}
            </if>
        </where>
        order by CreateTime desc, RefOrderNo, id
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO outstockorderitemchangerecord (
        Id,
        Org_Id,
        Warehouse_Id,
        RefOrder_Id,
        RefOrderNo,
        RefOrderItem_Id,
        ProductSku_Id,
        ProductName,
        SpecName,
        SpecQuantity,
        PackageName,
        UnitName,
        PackageCount,
        UnitCount,
        UnitTotalCount,
        Batch_Id,
        BatchNo,
        BatchTask_Id,
        BatchTaskNo,
        SowTask_Id,
        SowTaskNo,
        TransferOrder_Id,
        TransferOrderNo,
        ChangeType,
        Remark,
        Location_Id,
        LocationName,
        CreateUser
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.orgId,jdbcType=INTEGER},
            #{item.warehouseId,jdbcType=INTEGER},
            #{item.refOrderId,jdbcType=BIGINT},
            #{item.refOrderNo,jdbcType=VARCHAR},
            #{item.refOrderItemId,jdbcType=BIGINT},
            #{item.productSkuId,jdbcType=BIGINT},
            #{item.productName,jdbcType=VARCHAR},
            #{item.specName,jdbcType=VARCHAR},
            #{item.specQuantity,jdbcType=DECIMAL},
            #{item.packageName,jdbcType=VARCHAR},
            #{item.unitName,jdbcType=VARCHAR},
            #{item.packageCount,jdbcType=DECIMAL},
            #{item.unitCount,jdbcType=DECIMAL},
            #{item.unitTotalCount,jdbcType=DECIMAL},
            #{item.batchId,jdbcType=VARCHAR},
            #{item.batchNo,jdbcType=VARCHAR},
            #{item.batchTaskId,jdbcType=VARCHAR},
            #{item.batchTaskNo,jdbcType=VARCHAR},
            #{item.sowTaskId,jdbcType=BIGINT},
            #{item.sowTaskNo,jdbcType=VARCHAR},
            #{item.transferOrderId,jdbcType=BIGINT},
            #{item.transferOrderNo,jdbcType=VARCHAR},
            #{item.changeType,jdbcType=TINYINT},
            #{item.remark,jdbcType=VARCHAR},
            #{item.locationId,jdbcType=BIGINT},
            #{item.locationName,jdbcType=VARCHAR},
            #{item.createUser,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemChangeRecordPO">
        insert into outstockorderitemchangerecord
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="orgId != null">
                Org_Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="refOrderId != null">
                RefOrder_Id,
            </if>
            <if test="refOrderNo != null">
                RefOrderNo,
            </if>
            <if test="refOrderItemId != null">
                RefOrderItem_Id,
            </if>
            <if test="productSkuId != null">
                ProductSku_Id,
            </if>
            <if test="productName != null">
                ProductName,
            </if>
            <if test="specName != null">
                SpecName,
            </if>
            <if test="specQuantity != null">
                SpecQuantity,
            </if>
            <if test="packageName != null">
                PackageName,
            </if>
            <if test="unitName != null">
                UnitName,
            </if>
            <if test="packageCount != null">
                PackageCount,
            </if>
            <if test="unitCount != null">
                UnitCount,
            </if>
            <if test="unitTotalCount != null">
                UnitTotalCount,
            </if>
            <if test="batchId != null">
                Batch_Id,
            </if>
            <if test="batchNo != null">
                BatchNo,
            </if>
            <if test="batchTaskId != null">
                BatchTask_Id,
            </if>
            <if test="batchTaskNo != null">
                BatchTaskNo,
            </if>
            <if test="sowTaskId != null">
                SowTask_Id,
            </if>
            <if test="sowTaskNo != null">
                SowTaskNo,
            </if>
            <if test="transferOrderId != null">
                TransferOrder_Id,
            </if>
            <if test="transferOrderNo != null">
                TransferOrderNo,
            </if>
            <if test="changeType != null">
                ChangeType,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="refOrderId != null">
                #{refOrderId,jdbcType=BIGINT},
            </if>
            <if test="refOrderNo != null">
                #{refOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="refOrderItemId != null">
                #{refOrderItemId,jdbcType=BIGINT},
            </if>
            <if test="productSkuId != null">
                #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="specName != null">
                #{specName,jdbcType=VARCHAR},
            </if>
            <if test="specQuantity != null">
                #{specQuantity,jdbcType=DECIMAL},
            </if>
            <if test="packageName != null">
                #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="unitName != null">
                #{unitName,jdbcType=VARCHAR},
            </if>
            <if test="packageCount != null">
                #{packageCount,jdbcType=DECIMAL},
            </if>
            <if test="unitCount != null">
                #{unitCount,jdbcType=DECIMAL},
            </if>
            <if test="unitTotalCount != null">
                #{unitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="batchId != null">
                #{batchId,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null">
                #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="batchTaskId != null">
                #{batchTaskId,jdbcType=VARCHAR},
            </if>
            <if test="batchTaskNo != null">
                #{batchTaskNo,jdbcType=VARCHAR},
            </if>
            <if test="sowTaskId != null">
                #{sowTaskId,jdbcType=BIGINT},
            </if>
            <if test="sowTaskNo != null">
                #{sowTaskNo,jdbcType=VARCHAR},
            </if>
            <if test="transferOrderId != null">
                #{transferOrderId,jdbcType=BIGINT},
            </if>
            <if test="transferOrderNo != null">
                #{transferOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="changeType != null">
                #{changeType,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

</mapper>