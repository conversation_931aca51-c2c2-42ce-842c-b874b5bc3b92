package com.yijiupi.himalaya.supplychain.waves.search;

import com.yijiupi.himalaya.base.search.PagerCondition;

import java.io.Serializable;
import java.util.List;

/**
 * 包装信息查询
 * 
 * <AUTHOR>
 * @since 2018/7/13 10:06
 */
public class PackageOrderItemSO extends PagerCondition implements Serializable {
    private static final long serialVersionUID = -2175479964021646953L;

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 出库单编号
     */
    private String refOrderNo;

    /**
     * 订单id集合
     */
    private List<Long> orderIdList;

    public List<Long> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Long> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }
}
