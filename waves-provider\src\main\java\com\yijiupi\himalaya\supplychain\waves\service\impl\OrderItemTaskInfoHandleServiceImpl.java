package com.yijiupi.himalaya.supplychain.waves.service.impl;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.waves.batch.IOrderItemTaskInfoHandleService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderitemtaskinfo.OrderItemTaskInfoHandleBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.OrderItemTaskInfoUpdateDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/6/5
 */
@Service
public class OrderItemTaskInfoHandleServiceImpl implements IOrderItemTaskInfoHandleService {

    @Autowired
    private OrderItemTaskInfoHandleBL orderItemTaskInfoHandleBL;

    /**
     * 更新数量工具
     *
     * @param updateDTO
     */
    @Override
    public void updateOrderItemTaskInfo(OrderItemTaskInfoUpdateDTO updateDTO) {
        orderItemTaskInfoHandleBL.updateOrderItemTaskInfo(updateDTO);
    }
}
