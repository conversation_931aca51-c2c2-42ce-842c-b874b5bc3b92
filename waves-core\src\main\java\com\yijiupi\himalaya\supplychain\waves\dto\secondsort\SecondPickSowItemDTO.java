package com.yijiupi.himalaya.supplychain.waves.dto.secondsort;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/25
 */
public class SecondPickSowItemDTO implements Serializable {
    private static final long serialVersionUID = 8803777834216003541L;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品SKU
     */
    private Long productSkuId;
    /**
     * 大单位数量
     */
    private BigDecimal packageCount;
    /**
     * 小单位数量
     */
    private BigDecimal unitCount;

    /**
     * 出库位名称
     */
    private String toLocationName;
    /**
     * 出库位ID
     */
    private Long toLocationId;
    /**
     * 出库位序号
     */
    private Integer toLocationSequence;
    /**
     * 配送员名称
     */
    private String deliveryUserName;
    /**
     * 二次分拣播种状态（当前产品）
     */
    private Byte state;
    /**
     * 出库单项ID
     */
    private Long orderItemId;
    /**
     * 分拣任务项ID
     */
    private String batchTaskItemId;
    /**
     * 大单位
     */
    private String packageName;
    /**
     * 小单位
     */
    private String unitName;
    /**
     * 出库批次号
     */
    private String outBoundBatchNo;
    /**
     * 分拣任务-分拣状态
     */
    private Byte taskPickSate;
    /**
     * 分拣任务-播种状态
     */
    private Byte taskSowSate;
    /**
     * 分拣任务项ID列表
     */
    private List<String> batchTaskItemIdList;
    /**
     * 出库单项ID列表
     */
    private List<Long> orderItemIdList;

    public List<Long> getOrderItemIdList() {
        return orderItemIdList;
    }

    public void setOrderItemIdList(List<Long> orderItemIdList) {
        this.orderItemIdList = orderItemIdList;
    }

    public List<String> getBatchTaskItemIdList() {
        return batchTaskItemIdList;
    }

    public void setBatchTaskItemIdList(List<String> batchTaskItemIdList) {
        this.batchTaskItemIdList = batchTaskItemIdList;
    }

    public Byte getTaskPickSate() {
        return taskPickSate;
    }

    public void setTaskPickSate(Byte taskPickSate) {
        this.taskPickSate = taskPickSate;
    }

    public Byte getTaskSowSate() {
        return taskSowSate;
    }

    public void setTaskSowSate(Byte taskSowSate) {
        this.taskSowSate = taskSowSate;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getOutBoundBatchNo() {
        return outBoundBatchNo;
    }

    public void setOutBoundBatchNo(String outBoundBatchNo) {
        this.outBoundBatchNo = outBoundBatchNo;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getBatchTaskItemId() {
        return batchTaskItemId;
    }

    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Integer getToLocationSequence() {
        return toLocationSequence;
    }

    public void setToLocationSequence(Integer toLocationSequence) {
        this.toLocationSequence = toLocationSequence;
    }

    public String getDeliveryUserName() {
        return deliveryUserName;
    }

    public void setDeliveryUserName(String deliveryUserName) {
        this.deliveryUserName = deliveryUserName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }
}
