package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchProgressDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchProgressType;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.PageHelperUtils;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024-02-29 15:52
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class BatchProgressBL {

    @Resource
    private OrderFeatureBL orderFeatureBL;
    @Resource
    private BatchTaskItemMapper batchTaskItemMapper;
    @Resource
    private OutStockOrderMapper outStockOrderMapper;
    @Resource
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Resource
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    private static final int SPLIT_SIZE = 2000;

    private static final Logger logger = LoggerFactory.getLogger(BatchProgressBL.class);

    private static final Cache<Integer, List<BatchProgressDTO>> BATCH_PROGRESS_CACHE = Caffeine.newBuilder()
            .initialCapacity(100)
            .maximumSize(1000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    public List<BatchProgressDTO> getBatchProgress(Integer warehouseId) {
        return BATCH_PROGRESS_CACHE.get(warehouseId, this::getBatchProgress0);
    }

    private List<BatchProgressDTO> getBatchProgress0(Integer warehouseId) {
        String taskName = "获取仓库 " + warehouseId + " 的分拣进度";
        logger.info(taskName);
        StopWatch stopWatch = new StopWatch(taskName);
        stopWatch.start("查询仓库所有作业中的订单");
        Map<Long, OutStockOrderPO> orderIdMap = queryOrderIdMap(warehouseId);
        stopWatch.stop();
        if (orderIdMap.isEmpty()) {
            return Collections.emptyList();
        }
        stopWatch.start("查询订单特征");
        Map<Long, List<Byte>> orderFeatureMap = getOrderFeatureMap(new ArrayList<>(orderIdMap.keySet()));
        stopWatch.stop();
        stopWatch.start("将订单和订单特征归并到两个 map 中");
        // 酒饮订单 波次号 -> 订单项 id
        Map<String, Set<Long>> drinkOrders = orderFeatureMap.entrySet().stream()
                .filter(it -> it.getValue().contains(OrderFeatureConstant.FEATURE_TYPE_DRINKING))
                .map(Map.Entry::getKey).map(orderIdMap::get)
                .collect(Collectors.toMap(OutStockOrderPO::getBatchId, this::mapToOrderItemIds, this::mergeSet));
        // 休食订单 波次号 -> 订单项 id
        Map<String, Set<Long>> restOrders = orderFeatureMap.entrySet().stream()
                .filter(it -> it.getValue().contains(OrderFeatureConstant.FEATURE_TYPE_REST))
                .map(Map.Entry::getKey).map(orderIdMap::get)
                .collect(Collectors.toMap(OutStockOrderPO::getBatchId, this::mapToOrderItemIds, this::mergeSet));
        stopWatch.stop();
        stopWatch.start("通过波次 id 查询拣货任务明细");
        Set<Long> drinkOrderItemIds = drinkOrders.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        Set<Long> restOrderItemIds = restOrders.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        Set<String> batchIds = Stream.concat(drinkOrders.keySet().stream(), restOrders.keySet().stream())
                .collect(Collectors.toSet());
        if (batchIds.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, BatchTaskItemPO> batchTaskItemMap = PageHelperUtils.splitWithPageQuery(batchIds,
                batchTaskItemMapper::findBatchTaskItemsByBatchIds
        ).stream().collect(Collectors.toMap(BatchTaskItemPO::getId, Function.identity()));
        stopWatch.stop();
        stopWatch.start("通过拣货任务明细 id 查询拣货任务明细与订单项的关联关系");
        Set<String> batchTaskItemIds = batchTaskItemMap.values().stream().map(BatchTaskItemPO::getId).collect(Collectors.toSet());
        // 只需要 已分捡 行数
        List<BatchTaskItemPO> pickedTakItem = batchTaskItemMap.values().stream()
                .filter(it -> TaskStateEnum.已完成.valueEquals(it.getTaskState())).collect(Collectors.toList());
        if (batchTaskItemIds.isEmpty()) {
            return Collections.emptyList();
        }
        // 使用 orderItemTaskInfo 拿到 拣货任务项 和 订单项 的关系
        Map<String, Long> batchTaskItemOrderItemMap = splitQueryTaskInfo(batchTaskItemIds).stream()
                .collect(Collectors.toMap(OrderItemTaskInfoPO::getBatchTaskItemId, OrderItemTaskInfoPO::getRefOrderItemId, (old, newV) -> old));
        stopWatch.stop();
        stopWatch.start("汇总数据");
        // 酒饮总件数
        BigDecimal drinkTotalCount = batchTaskItemIds.stream()
                .filter(id -> drinkOrderItemIds.contains(batchTaskItemOrderItemMap.get(id)))
                .map(batchTaskItemMap::get).map(BatchTaskItemPO::getUnitTotalCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 休食总行数
        long restTotalCount = batchTaskItemIds.stream()
                .filter(id -> restOrderItemIds.contains(batchTaskItemOrderItemMap.get(id)))
                .count();
        // 酒饮已分捡件数
        BigDecimal drinkPickedCount = pickedTakItem.stream()
                .filter(it -> drinkOrderItemIds.contains(batchTaskItemOrderItemMap.get(it.getId())))
                .map(BatchTaskItemPO::getUnitTotalCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 休食已分捡行数
        long restPickedCount = pickedTakItem.stream().map(BatchTaskItemPO::getId)
                .filter(id -> restOrderItemIds.contains(batchTaskItemOrderItemMap.get(id)))
                .count();
        BatchProgressDTO drinkingProgress = BatchProgressDTO.of(drinkPickedCount, drinkTotalCount, BatchProgressType.DRINK.getValue());
        BatchProgressDTO restProgress = BatchProgressDTO.of(restPickedCount, restTotalCount, BatchProgressType.REST.getValue());
        stopWatch.stop();
        logger.info("获取仓库 {} 的分拣进度结束\n{}", warehouseId, stopWatch.prettyPrint());
        return Lists.newArrayList(drinkingProgress, restProgress);
    }

    /**
     * 根据拣货任务项 id 查询出库单项拣货关联信息, 分批查询
     *
     * @param items 拣货任务项 id
     * @return 查询结果
     * @see PageHelperUtils#LIMIT_COUNT
     * @see PageHelperUtils#splitPageQuery(Collection, Function)
     * @see OrderItemTaskInfoMapper#listOrderItemTaskInfoByBatchTaskItemIds(Collection)
     */
    public List<OrderItemTaskInfoPO> splitQueryTaskInfo(Collection<String> items) {
        try {
            return PageHelperUtils.splitPageQuery(items, 500, orderItemTaskInfoMapper::listOrderItemTaskInfoByBatchTaskItemIds);
        } catch (Throwable e) {
            logger.warn("分批查询出现异常, 回退到直接调用", e);
        }
        return orderItemTaskInfoMapper.listOrderItemTaskInfoByBatchTaskItemIds(items);
    }

    /**
     * 查询指定仓库所有作业中订单
     *
     * @param warehouseId 仓库 id
     * @return 该仓库所有作业中订单
     */
    private Map<Long, OutStockOrderPO> queryOrderIdMap(Integer warehouseId) {
        Map<Long, List<OutStockOrderItemPO>> orderIdItemMap = PageHelperUtils.pageQuery(() ->
                outStockOrderItemMapper.findAllWorkingOrders(warehouseId)
        ).stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getOutstockorderId));
        if (orderIdItemMap.isEmpty()) {
            return Collections.emptyMap();
        }
        Set<Long> ids = orderIdItemMap.keySet();
        return PageHelperUtils.splitPageQuery(ids, 2000, it -> outStockOrderMapper.selectByIds(it)).stream()
                .peek(it -> it.setItems(orderIdItemMap.get(it.getId())))
                .collect(Collectors.toMap(OutStockOrderPO::getId, Function.identity()));
    }

    private Set<Long> mapToOrderItemIds(OutStockOrderPO order) {
        return order.getItems().stream().map(OutStockOrderItemPO::getId).collect(Collectors.toSet());
    }

    private <T> Set<T> mergeSet(Set<T> a, Set<T> b) {
        a.addAll(b);
        return a;
    }

    private Map<Long, List<Byte>> getOrderFeatureMap(List<Long> orderIds) {
        return Lists.partition(orderIds, SPLIT_SIZE).stream().map(orderFeatureBL::getOrderFeatureMap)
                .map(Map::entrySet).flatMap(Collection::stream)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (old, newV) -> old));
    }

}
