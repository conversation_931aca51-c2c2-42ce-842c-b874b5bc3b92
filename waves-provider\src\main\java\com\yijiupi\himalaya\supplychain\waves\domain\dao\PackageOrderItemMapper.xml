<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.PackageOrderItemMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.waves.domain.po.PackageOrderItemPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="Org_id" property="orgId" jdbcType="INTEGER"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="RefOrderNo" property="refOrderNo" jdbcType="VARCHAR"/>
        <result column="RefOrder_id" property="refOrderId" jdbcType="BIGINT"/>
        <result column="RefOrder_ItemId" property="refOrderItemId" jdbcType="BIGINT"/>
        <result column="Boxcode" property="boxCode" jdbcType="VARCHAR"/>
        <result column="BoxcodeNo" property="boxCodeNo" jdbcType="VARCHAR"/>
        <result column="ProductName" property="productName" jdbcType="VARCHAR"/>
        <result column="SkuId" property="skuId" jdbcType="BIGINT"/>
        <result column="SpecName" property="specName" jdbcType="VARCHAR"/>
        <result column="SpecQuantity" property="specQuantity" jdbcType="DECIMAL"/>
        <result column="PackageName" property="packageName" jdbcType="VARCHAR"/>
        <result column="PackageCount" property="packageCount" jdbcType="DECIMAL"/>
        <result column="UnitName" property="unitName" jdbcType="VARCHAR"/>
        <result column="UnitCount" property="unitCount" jdbcType="DECIMAL"/>
        <result column="UnitTotalCount" property="unitTotalCount" jdbcType="DECIMAL"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="Createtime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="Createuser" property="createUser" jdbcType="VARCHAR"/>
        <result column="Lastupdatetime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="Lastupdateuser" property="lastUpdateUser" jdbcType="VARCHAR"/>
        <result column="OrderSequence" property="orderSequence" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="BaseResultMapDTO"
               type="com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="Org_id" property="orgId" jdbcType="INTEGER"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="RefOrderNo" property="refOrderNo" jdbcType="VARCHAR"/>
        <result column="RefOrder_id" property="refOrderId" jdbcType="BIGINT"/>
        <result column="RefOrder_ItemId" property="refOrderItemId" jdbcType="BIGINT"/>
        <result column="Boxcode" property="boxCode" jdbcType="VARCHAR"/>
        <result column="BoxcodeNo" property="boxCodeNo" jdbcType="VARCHAR"/>
        <result column="ProductName" property="productName" jdbcType="VARCHAR"/>
        <result column="SkuId" property="skuId" jdbcType="BIGINT"/>
        <result column="SpecName" property="specName" jdbcType="VARCHAR"/>
        <result column="SpecQuantity" property="specQuantity" jdbcType="DECIMAL"/>
        <result column="PackageName" property="packageName" jdbcType="VARCHAR"/>
        <result column="PackageCount" property="packageCount" jdbcType="DECIMAL"/>
        <result column="UnitName" property="unitName" jdbcType="VARCHAR"/>
        <result column="UnitCount" property="unitCount" jdbcType="DECIMAL"/>
        <result column="UnitTotalCount" property="unitTotalCount" jdbcType="DECIMAL"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="Createuser" property="createUser" jdbcType="VARCHAR"/>
        <result column="OrderSequence" property="orderSequence" jdbcType="INTEGER"/>
        <result column="Createtime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="PackageType" property="packageType" jdbcType="TINYINT"/>
        <result column="ReviewState" property="reviewState" jdbcType="TINYINT"/>
        <result column="OverUnitTotalCount" property="overUnitTotalCount" jdbcType="DECIMAL"/>
        <result column="OperatorName" property="operatorName" jdbcType="VARCHAR"/>
        <result column="OperatorId" property="operatorId" jdbcType="INTEGER"/>
        <result column="OperatingTime" property="operatingTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, Org_id, Warehouse_Id, RefOrderNo, RefOrder_id, RefOrder_ItemId, Boxcode, BoxcodeNo,
        ProductName, SkuId, SpecName, SpecQuantity, PackageName, PackageCount, UnitName, UnitCount, UnitTotalCount,
        Remark,OrderSequence, Createuser, Createtime, Lastupdateuser, Lastupdatetime, PackageType, ReviewState,
        OverUnitTotalCount, OperatorName, OperatorId, OperatingTime
    </sql>

    <select id="listPackageOrderItem" resultMap="BaseResultMapDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM packageorderitem
        <where>
            <if test="so.orgId != null and so.orgId != ''">
                Org_id = #{so.orgId,jdbcType=INTEGER}
            </if>
            <if test="so.warehouseId != null and so.warehouseId != ''">
                AND Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER}
            </if>
            <if test="so.refOrderNo != null and so.refOrderNo != ''">
                AND RefOrderNo = #{so.refOrderNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="listPackageOrderItemByOrderNo" resultMap="BaseResultMapDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM packageorderitem
        <where>
            <if test="refOrderNo != null and refOrderNo != ''">
                RefOrderNo = #{refOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="warehouseId != null">
                AND Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="orgId != null">
                AND Org_id = #{orgId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="listPackageCodePrintBySowTaskNo"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageCodePrintDTO">
        SELECT
        pack.RefOrderNo as refOrderNo,
        pack.Boxcode as boxCode,
        pack.BoxcodeNo as boxCodeNo,
        pack.SkuId as skuId,
        pack.UnitTotalCount as unitTotalCount,
        outs.ShopName as shopName,
        outs.RouteName as routeName,
        outs.RouteSequence as routeSequence,
        outs.UserName as userName,
        outs.MobileNo as mobileNo,
        outs.Province as province,
        outs.City as city,
        outs.County as county,
        outs.Street as street,
        outs.DetailAddress as detailAddress,
        outs.orderSequence,
        sow.SowOrderSequence,
        outsi.LocationId as locationId,
        outsi.LocationName as locationName,
        st.Location_Id as sowLocationId,
        st.LocationName as sowLocationName,
        pack.Warehouse_Id as warehouseId,
        outs.Business_Id as businessId,
        outs.AllotType as allotType,
        outs.DeliveryMode as deliveryMode,
        outs.id as orderId
        FROM packageorderitem pack
        inner join outstockorder outs on pack.RefOrder_id = outs.id and outs.Org_Id = pack.Org_id
        inner join outstockorderitem outsi on outsi.id = pack.RefOrder_ItemId and outsi.Org_id = pack.Org_id
        inner join sowtask st on st.Id = outsi.SowTask_Id and st.Org_Id = pack.Org_id
        inner join soworder sow on sow.Id = outsi.SowOrder_Id and sow.Org_Id = pack.Org_id
        where st.SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR}
        and pack.Org_id = #{orgId,jdbcType=INTEGER}
    </select>
    <select id="listPackageCodePrintByOrderNos"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageCodePrintDTO">
        SELECT
        pack.RefOrderNo as refOrderNo,
        pack.Boxcode as boxCode,
        pack.BoxcodeNo as boxCodeNo,
        pack.SkuId as skuId,
        pack.UnitTotalCount as unitTotalCount,
        outs.ShopName as shopName,
        outs.RouteName as routeName,
        outs.RouteSequence as routeSequence,
        outs.UserName as userName,
        outs.MobileNo as mobileNo,
        outs.Province as province,
        outs.City as city,
        outs.County as county,
        outs.Street as street,
        outs.DetailAddress as detailAddress,
        outs.orderSequence,
        outsi.SowTaskNo,
        outsi.LocationId as locationId,
        outsi.LocationName as locationName,
        sow.SowOrderSequence as sowOrderSequence,
        st.Location_Id as sowLocationId,
        st.LocationName as sowLocationName,
        pack.Warehouse_Id as warehouseId,
        outs.Business_Id as businessId,
        outs.AllotType as allotType,
        outs.id as orderId
        FROM packageorderitem pack
        inner join outstockorder outs on pack.RefOrder_id = outs.id and outs.Org_Id = pack.Org_Id
        inner join outstockorderitem outsi on pack.RefOrder_ItemId = outsi.id and outsi.Org_Id = pack.Org_Id
        left join sowtask st on outsi.SowTask_Id = st.Id and outsi.Org_Id = st.Org_Id
        left join soworder sow on sow.Id = outsi.SowOrder_Id and sow.Org_Id = pack.Org_id
        where pack.Org_Id = #{orgId,jdbcType=INTEGER}
        and pack.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and pack.RefOrderNo in
        <foreach collection="list" item="refOrderNo" separator="," open="(" close=")">
            #{refOrderNo,jdbcType=VARCHAR}
        </foreach>
    </select>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.PackageOrderItemPO">
        INSERT INTO packageorderitem (
        id,
        Org_id,
        Warehouse_Id,
        RefOrderNo,
        RefOrder_id,
        RefOrder_ItemId,
        Boxcode,
        BoxcodeNo,
        ProductName,
        SkuId,
        SpecName,
        SpecQuantity,
        PackageName,
        PackageCount,
        UnitName,
        UnitCount,
        UnitTotalCount,
        Remark,
        Createuser,
        OrderSequence,
        PackageType,
        ReviewState,
        OverUnitTotalCount,
        OperatorName,
        OperatorId,
        OperatingTime
        )
        VALUES(
        #{id,jdbcType=BIGINT},
        #{orgId,jdbcType=INTEGER},
        #{warehouseId,jdbcType=INTEGER},
        #{refOrderNo,jdbcType=VARCHAR},
        #{refOrderId,jdbcType=BIGINT},
        #{refOrderItemId,jdbcType=BIGINT},
        #{boxCode,jdbcType=VARCHAR},
        #{boxCodeNo,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{skuId,jdbcType=BIGINT},
        #{specName,jdbcType=VARCHAR},
        #{specQuantity,jdbcType=DECIMAL},
        #{packageName,jdbcType=VARCHAR},
        #{packageCount,jdbcType=DECIMAL},
        #{unitName,jdbcType=VARCHAR},
        #{unitCount,jdbcType=DECIMAL},
        #{unitTotalCount,jdbcType=DECIMAL},
        #{remark,jdbcType=VARCHAR},
        #{createUser,jdbcType=VARCHAR},
        #{orderSequence,jdbcType=INTEGER},
        #{packageType,jdbcType=TINYINT},
        #{reviewState,jdbcType=TINYINT},
        #{overUnitTotalCount,jdbcType=DECIMAL},
        #{operatorName,jdbcType=VARCHAR},
        #{operatorId,jdbcType=INTEGER},
        now()
        )
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO packageorderitem (
        id,
        Org_id,
        Warehouse_Id,
        RefOrderNo,
        RefOrder_id,
        RefOrder_ItemId,
        Boxcode,
        BoxcodeNo,
        ProductName,
        SkuId,
        SpecName,
        SpecQuantity,
        PackageName,
        PackageCount,
        UnitName,
        UnitCount,
        UnitTotalCount,
        Remark,
        Createuser,
        OrderSequence,
        Createtime,
        PackageType,
        ReviewState,
        OverUnitTotalCount,
        OperatorName,
        OperatorId,
        OperatingTime
        )
        VALUES
        <foreach collection="list" item="package" separator=",">
            (
            #{package.id,jdbcType=BIGINT},
            #{package.orgId,jdbcType=INTEGER},
            #{package.warehouseId,jdbcType=INTEGER},
            #{package.refOrderNo,jdbcType=VARCHAR},
            #{package.refOrderId,jdbcType=BIGINT},
            #{package.refOrderItemId,jdbcType=BIGINT},
            #{package.boxCode,jdbcType=VARCHAR},
            #{package.boxCodeNo,jdbcType=VARCHAR},
            #{package.productName,jdbcType=VARCHAR},
            #{package.skuId,jdbcType=BIGINT},
            #{package.specName,jdbcType=VARCHAR},
            #{package.specQuantity,jdbcType=DECIMAL},
            #{package.packageName,jdbcType=VARCHAR},
            #{package.packageCount,jdbcType=DECIMAL},
            #{package.unitName,jdbcType=VARCHAR},
            #{package.unitCount,jdbcType=DECIMAL},
            #{package.unitTotalCount,jdbcType=DECIMAL},
            #{package.remark,jdbcType=VARCHAR},
            #{package.createUser,jdbcType=VARCHAR},
            #{package.orderSequence,jdbcType=INTEGER},
            #{package.createTime,jdbcType=TIMESTAMP},
            #{package.packageType,jdbcType=TINYINT},
            #{package.reviewState,jdbcType=TINYINT},
            #{package.overUnitTotalCount,jdbcType=DECIMAL},
            #{package.operatorName,jdbcType=VARCHAR},
            #{package.operatorId,jdbcType=INTEGER},
            now()
            )
        </foreach>
    </insert>

    <select id="listPackageItemsSurplusByItemIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO">
        select
        osi.Org_id as orgId, osi.Outstockorder_Id as refOrderId,
        osi.id as refOrderItemId,
        osi.ProductName, osi.SkuId, osi.SpecName, osi.SpecQuantity, osi.PackageName,
        (osi.PackageCount - IFNULL(poi.PackageCount,0)) as PackageCount,
        osi.UnitName,
        (osi.UnitCount - IFNULL(poi.UnitCount,0)) as UnitCount,
        (osi.UnitTotalCount - IFNULL(poi.UnitTotalCount,0)) as UnitTotalCount
        from outstockorderitem osi
        left join packageorderitem poi on poi.RefOrder_ItemId = osi.id and poi.Org_id = osi.Org_id
        where poi.Org_id = #{orgId,jdbcType=INTEGER}
        and osi.id in
        <foreach collection="itemIds" item="refOrderItemId" separator="," open="(" close=")">
            #{refOrderItemId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listPackageItemsByItemIds" resultMap="BaseResultMapDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM packageorderitem
        where Org_id = #{orgId,jdbcType=INTEGER}
        and RefOrder_ItemId in
        <foreach collection="itemIds" item="refOrderItemId" separator="," open="(" close=")">
            #{refOrderItemId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listOrderNoByBoxCodeNo" resultType="java.lang.String">
        SELECT distinct RefOrderNo
        FROM packageorderitem
        where BoxcodeNo = #{boxCodeNo,jdbcType=VARCHAR}
    </select>

    <select id="listPackageOrderItemByBarCode" resultMap="BaseResultMapDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        packageorderitem
        WHERE
        Org_id = #{orgId,jdbcType=INTEGER}
        and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="orderNo != null and orderNo != '' ">
            AND RefOrderNo = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="boxCodeNo != null and boxCodeNo != '' ">
            AND RefOrderNo IN (
            SELECT DISTINCT RefOrderNo
            FROM packageorderitem WHERE
            Org_id = #{orgId,jdbcType=INTEGER}
            AND BoxcodeNo = #{boxCodeNo,jdbcType=VARCHAR}
            )
        </if>
    </select>

    <select id="listByOrderNos" resultMap="BaseResultMapDTO">
        select
        <include refid="Base_Column_List"/>
        from packageorderitem
        where
        RefOrderNo in
        <foreach collection="refOrderNos" item="refOrderNo" open="(" separator="," close=")">
            #{refOrderNo,jdbcType=VARCHAR}
        </foreach>
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="listSowPackageOrderItems" resultMap="BaseResultMapDTO">
        select
        package.id, package.Org_id, package.Warehouse_Id, package.RefOrderNo, package.RefOrder_id,
        package.RefOrder_ItemId, package.Boxcode, package.BoxcodeNo,
        package.ProductName, package.SkuId, package.SpecName, package.SpecQuantity, package.PackageName,
        package.PackageCount, package.UnitName, package.UnitCount, package.UnitTotalCount,
        package.Remark, package.OrderSequence, package.Createuser, package.Createtime, sti.CompleteTime as
        operatingTime,package.PackageType
        from packageorderitem package
        inner join outstockorderitem osi
        on package.RefOrder_ItemId = osi.id and package.Org_id = osi.Org_id
        inner join sowtaskitem sti on sti.Id = osi.SowTaskItem_Id and sti.Org_Id = osi.Org_id
        where
        package.Org_id = #{orgId,jdbcType=INTEGER}
        and package.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and package.RefOrderNo in
        <foreach collection="refOrderNos" item="refOrderNo" open="(" separator="," close=")">
            #{refOrderNo,jdbcType=VARCHAR}
        </foreach>
        and osi.SowTask_Id is not null
    </select>

    <select id="pageListPalletOrders"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PalletOrderDTO">
        select BoxcodeNo, max(Lastupdatetime) as lastupdatetime
        from packageOrderItem
        where Org_id = #{query.orgId,jdbcType=INTEGER}
        <if test="query.warehouseId != null">
            and Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="query.boxCodeNo != null and query.boxCodeNo != ''">
            and BoxcodeNo = #{query.boxCodeNo,jdbcType=VARCHAR}
        </if>
        <if test="query.reviewState != null">
            and ReviewState = #{query.reviewState,jdbcType=TINYINT}
        </if>
        <if test="query.packageType != null">
            and PackageType = #{query.packageType,jdbcType=TINYINT}
        </if>
        <if test="query.startTime != null">
            and <![CDATA[ Lastupdatetime >= #{query.startTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="query.endTime != null">
            and <![CDATA[ Lastupdatetime <= #{query.endTime,jdbcType=TIMESTAMP} ]]>
        </if>
        group by BoxcodeNo
        order by lastupdatetime desc
    </select>

    <select id="findPackageBatchTaskItem"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PalletOrderDTO">
        select poi.BoxcodeNo as boxCodeNo, bt.SorterName as sorter, bt.ToLocation_Id as locationId,
        bt.ToLocationName as locationName, poi.PackageCount as packageCount, poi.UnitCount as unitCount, poi.SkuId as
        skuId,
        poi.Createtime as createTime, poi.OperatingTime as operatingTime, poi.id as id,
        bt.BatchNo as batchNo,poi.RefOrder_id as refOrderId
        from packageorderitem poi
        left join orderitemtaskinfo oti on oti.RefOrderItem_Id = poi.RefOrder_ItemId and oti.Org_id = poi.Org_id
        left join batchtask bt on bt.id = oti.Batchtask_Id and poi.Org_id = bt.Org_id
        where poi.BoxcodeNo in
        <foreach collection="boxCodeNos" item="boxCodeNo" open="(" close=")" separator=",">
            #{boxCodeNo,jdbcType=VARCHAR}
        </foreach>
        <if test="orgId != null">
            and poi.Org_id = #{orgId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and poi.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="listPalletOrderItems"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PalletOrderItemDTO">
        select poi.id, poi.RefOrder_ItemId as refOrderItemId, poi.SkuId as skuId,
        poi.ProductName as productName, poi.SpecName as specName, poi.specQuantity as SpecQuantity,

        <!-- 兼容旧数据 -->
        osi.PackageCount as orderPackageCount, osi.UnitCount as orderUnitCount,
        poi.PackageCount as pickingPackageCount, poi.UnitCount as pickingUnitCount,

        poi.PackageName as packageName, poi.UnitName as unitName, osi.Outstockorder_Id as outStockOrderId,
        poi.RefOrderNo as refOrderNo
        from packageorderitem poi
        left join outstockorderitem osi on osi.id = poi.RefOrder_ItemId and osi.Org_id = poi.Org_id
        where 1=1
        <if test="query.boxCodeNo != null and query.boxCodeNo != ''">
            and poi.BoxcodeNo = #{query.boxCodeNo,jdbcType=VARCHAR}
        </if>
        <if test="query.warehouseId != null">
            and poi.Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="query.orgId != null">
            and poi.Org_id = #{query.orgId,jdbcType=INTEGER}
        </if>
    </select>

    <update id="batchReview">
        update packageorderitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="OverUnitTotalCount = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.overUnitTotalCount,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="OperatingTime = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.operatingTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="operatorName = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.operatorName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operatorId = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.operatorId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="ReviewState = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.reviewState,jdbcType=TINYINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" separator="," open="(" close=")" item="item">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="findPackageOrderItemsByBatchId" resultMap="BaseResultMapDTO">
        select
        poi.id, poi.Org_id, poi.Warehouse_Id, poi.RefOrderNo, poi.RefOrder_id, poi.RefOrder_ItemId, poi.Boxcode,
        poi.BoxcodeNo,
        poi.ProductName, poi.SkuId, poi.SpecName, poi.SpecQuantity, poi.PackageName, poi.PackageCount, poi.UnitName,
        poi.UnitCount, poi.UnitTotalCount,
        poi.Remark,poi.OrderSequence, poi.Createuser, poi.Createtime, poi.Lastupdateuser, poi.Lastupdatetime,
        poi.PackageType, poi.ReviewState,
        poi.OverUnitTotalCount, poi.OperatorName, poi.OperatorId, poi.OperatingTime
        from packageorderitem poi
        inner join outstockorderitem osi
        on poi.RefOrder_ItemId = osi.id and osi.Org_id = poi.Org_id
        where
        osi.Batch_Id = #{batchId,jdbcType=VARCHAR}
        and osi.Org_id = #{orgId,jdbcType=INTEGER}
        <if test="states != null and states.size() > 0">
            and poi.ReviewState in
            <foreach collection="states" open="(" separator="," close=")" item="state">
                #{state,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="type != null">
            and poi.PackageType = #{type,jdbcType=TINYINT}
        </if>
    </select>

    <select id="findBatchNoByIds" resultType="java.lang.String">
        select distinct osi.BatchNo
        from packageorderitem poi
        inner join outstockorderitem osi
        on poi.RefOrder_ItemId = osi.id and osi.Org_id = poi.Org_id
        where
        poi.id in
        <foreach collection="packageOrderItemIds" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findPackageOrderItemsByBatchItemIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.PackageOrderItemPO">
        select
        poi.id, poi.Org_id, poi.Warehouse_Id, poi.RefOrderNo, poi.RefOrder_id, poi.RefOrder_ItemId, poi.Boxcode,
        poi.BoxcodeNo,
        poi.ProductName, poi.SkuId, poi.SpecName, poi.SpecQuantity, poi.PackageName, poi.PackageCount, poi.UnitName,
        poi.UnitCount, poi.UnitTotalCount,
        poi.Remark,poi.OrderSequence, poi.Createuser, poi.Createtime, poi.Lastupdateuser, poi.Lastupdatetime,
        poi.PackageType, poi.ReviewState,
        poi.OverUnitTotalCount, poi.OperatorName, poi.OperatorId, poi.OperatingTime, bti.id as batchTaskItemId
        from packageorderitem poi
        inner join outstockorderitem osi
        on poi.RefOrder_ItemId = osi.id and osi.Org_id = poi.Org_id
        inner join batchtaskitem bti
        on osi.BatchTaskItem_Id = bti.id and bti.Org_id = poi.Org_id
        where
        bti.Org_id = #{orgId,jdbcType=INTEGER}
        and poi.PackageType = 1
        and poi.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and bti.id in
        <foreach collection="batchItemIds" item="batchItemId" open="(" separator="," close=")">
            #{batchItemId,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findPackageBatchTaskItemByIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PalletOrderDTO">
        select poi.BoxcodeNo as boxCodeNo, bt.SorterName as sorter, bt.ToLocation_Id as locationId,
        bt.ToLocationName as locationName, poi.PackageCount as packageCount, poi.UnitCount as unitCount, poi.SkuId as
        skuId,
        poi.Createtime as createTime, poi.OperatingTime as operatingTime, poi.id as id
        from packageorderitem poi
        inner join outstockorderitem osi on osi.id = poi.RefOrder_ItemId and osi.Org_id = poi.Org_id
        inner join batchtask bt on bt.id = osi.Batchtask_Id and poi.Org_id = bt.Org_id
        where poi.id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listPackageBatchTaskItemByOrderNos"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO">
        select
        poi.id,
        poi.Org_id as orgId,
        poi.Warehouse_Id as warehouseId,
        poi.RefOrderNo as refOrderNo,
        poi.RefOrder_id as refOrderId,
        poi.RefOrder_ItemId as refOrderItemId,
        poi.Boxcode as boxCode,
        poi.BoxcodeNo as boxCodeNo,
        poi.ProductName as productName,
        poi.SkuId as skuId,
        poi.SpecName as specName,
        poi.SpecQuantity as specQuantity,
        poi.PackageName as packageName,
        poi.PackageCount as packageCount,
        poi.UnitName as unitName,
        poi.UnitCount as unitCount,
        poi.UnitTotalCount as unitTotalCount,
        poi.Remark as remark,
        poi.OrderSequence as orderSequence,
        poi.Createuser as createUser,
        poi.Createtime as createTime,
        poi.PackageType as packageType,
        poi.ReviewState as reviewState,
        poi.OverUnitTotalCount as overUnitTotalCount,
        poi.OperatorName as operatorName,
        poi.OperatorId as operatorId,
        poi.OperatingTime as operatingTime,
        bt.id as batchTaskItemId
        from packageorderitem poi
        left join orderitemtaskinfo oti on oti.RefOrderItem_Id = poi.RefOrder_ItemId and oti.Org_id = poi.Org_id
        left join batchtask bt on bt.id = oti.Batchtask_Id and poi.Org_id = bt.Org_id
        where
        poi.RefOrderNo in
        <foreach collection="orderNoList" item="orderNo" separator="," open="(" close=")">
            #{orderNo,jdbcType=VARCHAR}
        </foreach>
        and poi.Org_id = #{orgId,jdbcType=INTEGER}
        and poi.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </select>

    <delete id="removePackageByRefOrderNos">
        delete from packageorderitem
        where RefOrderNo in
        <foreach collection="orderNos" item="orderNo" separator="," open="(" close=")">
            #{orderNo,jdbcType=VARCHAR}
        </foreach>
        and Org_Id = #{orgId,jdbcType=INTEGER}
        and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </delete>

    <select id="listPackageItemsByOrderIds" resultMap="BaseResultMapDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM packageorderitem
        where
        RefOrder_id in
        <foreach collection="refOrderIdList" item="orderId" separator="," open="(" close=")">
            #{orderId,jdbcType=BIGINT}
        </foreach>
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
    </select>

    <delete id="batchDelete">
        delete from packageorderitem
        where id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
        and Org_id = #{orgId,jdbcType=INTEGER}
    </delete>


    <update id="updateBoxCodeById">
        UPDATE packageorderitem
        <set>
            <if test="boxCode!= null">
                Boxcode = #{boxCode,jdbcType=VARCHAR},
            </if>
            <if test="boxCodeNo!= null">
                BoxcodeNo = #{boxCodeNo,jdbcType=VARCHAR},
            </if>
            <if test="unitTotalCount!= null">
                UnitTotalCount = #{unitTotalCount,jdbcType=VARCHAR},
            </if>
            <if test="overUnitTotalCount!= null">
                OverUnitTotalCount = #{overUnitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="packageCount!= null">
                PackageCount = #{packageCount,jdbcType=DECIMAL},
            </if>
            <if test="unitCount!= null">
                UnitCount = #{unitCount,jdbcType=DECIMAL},
            </if>
            operatingTime = now()
        </set>
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>


    <select id="listSowBoxcodeItems"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.PackageOrderItemPO">
        select
        poi.id, t.Org_id as orgId, t.Warehouse_Id as warehouseId, o.RefOrderNo, o.id as refOrderId, osi.id as
        refOrderItemId, poi.Boxcode,
        poi.BoxcodeNo,
        osi.ProductName
        , osi.SkuId, osi.SpecName, osi.SpecQuantity, osi.PackageName, osi.UnitName,
        (oti.UnitTotalCount - oti.LackUnitCount) as UnitTotalCount,
        poi.Remark,poi.OrderSequence, poi.Createuser, poi.Createtime, poi.Lastupdateuser, poi.Lastupdatetime,
        2 as PackageType, poi.ReviewState,
        poi.OverUnitTotalCount, poi.OperatorName, poi.OperatorId, poi.OperatingTime, bti.id as batchTaskItemId
        from batchtask t
        inner join batch b on b.id =t.Batch_id and b.SowType = 2
        inner join batchtaskitem bti on t.id = bti.Batchtask_id
        inner join orderitemtaskinfo oti on oti.BatchTaskItem_Id = bti.id
        inner join outstockorderitem osi on oti.RefOrderItem_Id = osi.id and osi.Org_id = oti.Org_Id
        inner join outstockorder o on o.id = osi.Outstockorder_Id
        LEFT JOIN packageorderitem poi on osi.id = poi.RefOrder_ItemId and poi.PackageType = 2
        where
        t.Org_id = #{orgId,jdbcType=INTEGER}
        <if test="warehouseId!=null">
            and t.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="skuId!=null">
            and osi.SkuId = #{skuId,jdbcType=BIGINT}
        </if>
        <if test="refOrderNo!=null and refOrderNo!=''">
            and o.RefOrderNo = #{refOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="refOrderItemId!=null">
            and osi.Id = #{refOrderItemId,jdbcType=BIGINT}
        </if>
        <if test="refOrderIdList!=null and refOrderIdList.size!=0 ">
            and o.Id in
            <foreach collection="refOrderIdList" item="orderId" separator="," open="(" close=")">
                #{orderId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="batchTaskItemIds!=null and batchTaskItemIds.size!=0 ">
            and bti.id in
            <foreach collection="batchTaskItemIds" item="batchTaskItemId" open="(" separator="," close=")">
                #{batchTaskItemId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="batchTaskItemId!=null">
            and bti.id = #{batchTaskItemId,jdbcType=VARCHAR}
        </if>
        <if test="batchTaskId!=null">
            and bti.Batchtask_id = #{batchTaskId,jdbcType=VARCHAR}
        </if>
        <if test="batchId!=null">
            and b.Id = #{batchId,jdbcType=VARCHAR}
        </if>
    </select>


    <select id="listSowBoxcodeItemsOld"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.PackageOrderItemPO">
        select
        poi.id, poi.Org_id, poi.Warehouse_Id, poi.RefOrderNo, poi.RefOrder_id, poi.RefOrder_ItemId, poi.Boxcode,
        poi.BoxcodeNo,
        poi.ProductName, poi.SkuId, poi.SpecName, poi.SpecQuantity, poi.PackageName, poi.PackageCount, poi.UnitName,
        poi.UnitCount, poi.UnitTotalCount,
        poi.Remark,poi.OrderSequence, poi.Createuser, poi.Createtime, poi.Lastupdateuser, poi.Lastupdatetime,
        poi.PackageType, poi.ReviewState,
        poi.OverUnitTotalCount, poi.OperatorName, poi.OperatorId, poi.OperatingTime, bti.id as batchTaskItemId
        from packageorderitem poi
        inner join outstockorderitem osi
        on poi.RefOrder_ItemId = osi.id and osi.Org_id = poi.Org_id
        inner join batchtaskitem bti
        on osi.BatchTaskItem_Id = bti.id and bti.Org_id = poi.Org_id
        where
        bti.Org_id = #{orgId,jdbcType=INTEGER}
        and poi.PackageType = 2
        <if test="warehouseId!=null">
            and poi.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="skuId!=null">
            and poi.SkuId = #{skuId,jdbcType=BIGINT}
        </if>
        <if test="refOrderNo!=null and refOrderNo!=''">
            and poi.RefOrderNo = #{refOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="refOrderIdList!=null and refOrderIdList.size!=0 ">
            and poi.RefOrder_id in
            <foreach collection="refOrderIdList" item="orderId" separator="," open="(" close=")">
                #{orderId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="batchTaskItemIds!=null and batchTaskItemIds.size!=0 ">
            and bti.id in
            <foreach collection="batchTaskItemIds" item="batchTaskItemId" open="(" separator="," close=")">
                #{batchTaskItemId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="batchTaskItemId!=null">
            and bti.id = #{batchTaskItemId,jdbcType=VARCHAR}
        </if>
        <if test="batchTaskId!=null">
            and bti.Batchtask_id = #{batchTaskId,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap">+
        SELECT
        <include refid="Base_Column_List"/>
        FROM packageorderitem
        where Id = #{id,jdbcType=BIGINT}
    </select>
    <select id="listPackageOrderItemByOrderIdList"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.PackageOrderItemPO">
        select packageorderitem.Boxcode,
        packageorderitem.OrderSequence,
        packageorderitem.BoxcodeNo,
        packageorderitem.RefOrderNo,
        packageorderitem.RefOrder_id,
        packageorderitem.PackageName,
        packageorderitem.PackageCount,
        packageorderitem.UnitName,
        packageorderitem.UnitCount,
        packageorderitem.UnitTotalCount,
        packageorderitem.ProductName,
        packageorderitem.SpecName,
        packageorderitem.OverUnitTotalCount,
        packageorderitem.RefOrder_ItemId,
        packageorderitem.RefOrder_id as refOrderId,
        outstockorderitem.CategoryName,
        packageorderitem.SkuId,
        packageorderitem.PackageType
        from packageorderitem
        left join outstockorderitem on packageorderitem.RefOrder_ItemId = outstockorderitem.id
        where packageorderitem.Org_id = #{orgId}
        and packageorderitem.RefOrder_id in
        <foreach collection="orderIdList" item="orderId" separator="," open="(" close=")">
            #{orderId}
        </foreach>
    </select>
    <select id="getBoxMaxCountByOrderIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from packageorderitem
        where packageorderitem.Org_id = #{orgId}
        and packageorderitem.RefOrder_id in
        <foreach collection="orderIdList" item="orderId" separator="," open="(" close=")">
            #{orderId}
        </foreach>
    </select>

    <select id="listBoxesByOrderIds" resultMap="BaseResultMapDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM packageorderitem
        where (packageType = 0 or packageType is null)
        and RefOrder_id in
        <foreach collection="refOrderIdList" item="orderId" separator="," open="(" close=")">
            #{orderId,jdbcType=BIGINT}
        </foreach>
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="findRefItemIdsByOrderNos" resultType="java.lang.Long">
        select
        distinct RefOrder_ItemId
        from packageorderitem
        where
        RefOrderNo in
        <foreach collection="refOrderNos" item="refOrderNo" open="(" separator="," close=")">
            #{refOrderNo,jdbcType=VARCHAR}
        </foreach>
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="findRefIdsByOrderNos" resultType="java.lang.Long">
        select
        distinct RefOrder_Id
        from packageorderitem
        where
        RefOrderNo in
        <foreach collection="refOrderNos" item="refOrderNo" open="(" separator="," close=")">
            #{refOrderNo,jdbcType=VARCHAR}
        </foreach>
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="findPackageInfoByBoxCodeNo" resultMap="BaseResultMap">
        select
        a.id, a.Org_id, a.Warehouse_Id, a.RefOrderNo, a.RefOrder_id, a.RefOrder_ItemId, a.Boxcode, a.BoxcodeNo,
        a.ProductName, a.SkuId, a.SpecName, a.SpecQuantity, a.PackageName, a.PackageCount, a.UnitName, a.UnitCount,
        a.UnitTotalCount,
        a.Remark,a.OrderSequence, a.Createuser, a.Createtime, a.Lastupdateuser, a.Lastupdatetime, a.PackageType,
        a.ReviewState,
        a.OverUnitTotalCount, a.OperatorName, a.OperatorId, a.OperatingTime
        from packageOrderItem a
        inner join outstockorder b
        on a.reforder_id = b.id
        where a.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="orgId != null">
            and a.Org_id = #{orgId,jdbcType=INTEGER}
        </if>
        <if test="boxCodeNo != null">
            and a.boxCodeNo = #{boxCodeNo, jdbcType=VARCHAR}
        </if>
        <if test="stateList != null">
            and b.State in
            <foreach collection="stateList" item="state" separator="," open="(" close=")">
                #{state,jdbcType=TINYINT}
            </foreach>
        </if>
    </select>

</mapper>