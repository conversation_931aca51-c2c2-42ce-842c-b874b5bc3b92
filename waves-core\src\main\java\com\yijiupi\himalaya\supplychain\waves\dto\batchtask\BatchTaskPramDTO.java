package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class BatchTaskPramDTO implements Serializable {
    private String sowTaskNo;
    private List<Integer> taskStates;
    private Integer cityId;

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public List<Integer> getTaskStates() {
        return taskStates;
    }

    public void setTaskStates(List<Integer> taskStates) {
        this.taskStates = taskStates;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
}
