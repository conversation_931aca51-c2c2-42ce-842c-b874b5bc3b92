package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.math.BigDecimal;
import java.util.*;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.exceptions.HasLockedException;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OutStockOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.SowManagerBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskFinishBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskQueryBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.pickup.BatchTaskPickupBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtaskquery.BatchTaskSortQueryBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.PickedDetailQueryParam;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.*;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderItemDetailAllotDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;

/**
 * 波次任务列表
 *
 * <AUTHOR> 2018/3/16
 */
@Service(timeout = 120000)
public class BatchTaskQueryServiceImpl implements IBatchTaskQueryService {

    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;

    @Autowired
    private BatchTaskFinishBL batchTaskFinishBL;

    @Autowired
    private OutStockOrderBL outStockOrderBL;

    @Autowired
    private SowManagerBL sowManagerBL;

    @Resource
    private BatchTaskQueryBL batchTaskQueryBL;

    @Resource
    private BatchTaskPickupBL batchTaskPickupBL;

    @Autowired
    private BatchTaskSortQueryBL batchTaskSortQueryBL;

    @Override
    public PageList<BatchTaskDTO> findBatchTaskList(BatchTaskQueryDTO batchTaskQueryDTO) {
        AssertUtils.notNull(batchTaskQueryDTO.getWarehouseId(), "仓库id不能为空");
        return batchOrderTaskBL.findBatchOrderTaskList(batchTaskQueryDTO);
    }

    @Override
    public PageList<BatchTaskSortDTO> findBatchTaskSortList(BatchTaskSortQueryDTO batchTaskSortQueryDTO) {
        AssertUtils.notNull(batchTaskSortQueryDTO.getUserId(), "分拣员id不能为null");
        AssertUtils.notNull(batchTaskSortQueryDTO.getCityId(), "城市id不能为null");
        AssertUtils.notNull(batchTaskSortQueryDTO.getWarehouseId(), "仓库id不能为null");
        batchTaskSortQueryDTO.setPageNum(ObjectUtils.defaultIfNull(batchTaskSortQueryDTO.getPageNum(), 1));
        batchTaskSortQueryDTO.setPageSize(ObjectUtils.defaultIfNull(batchTaskSortQueryDTO.getPageSize(), 100));
        return batchOrderTaskBL.findBatchTaskSortList(batchTaskSortQueryDTO);
    }

    /**
     * 查找拣货任务列表（POA）
     *
     * @param batchTaskSortQueryDTO
     * @return
     */
    @Override
    public BatchTaskPickInfoForPDADTO findPickBatchTaskList(BatchTaskSortQueryDTO batchTaskSortQueryDTO) {
        AssertUtils.notNull(batchTaskSortQueryDTO.getUserId(), "分拣员id不能为null");
        AssertUtils.notNull(batchTaskSortQueryDTO.getCityId(), "城市id不能为null");
        AssertUtils.notNull(batchTaskSortQueryDTO.getWarehouseId(), "仓库id不能为null");
        batchTaskSortQueryDTO.setPageNum(ObjectUtils.defaultIfNull(batchTaskSortQueryDTO.getPageNum(), 1));
        batchTaskSortQueryDTO.setPageSize(ObjectUtils.defaultIfNull(batchTaskSortQueryDTO.getPageSize(), 100));

        return batchTaskSortQueryBL.findPickBatchTaskList(batchTaskSortQueryDTO);
    }

    @Override
    public PageList<BatchTaskSortItemDTO> findBatchTaskSortItemByProduct(String batchTaskId, Integer currentPage,
        Integer pageSize) {
        AssertUtils.notNull(batchTaskId, "波次任务id不能为null");
        currentPage = ObjectUtils.defaultIfNull(currentPage, 1);
        pageSize = ObjectUtils.defaultIfNull(pageSize, 100);
        return batchOrderTaskBL.findBatchTaskSortItemByProduct(batchTaskId, currentPage, pageSize);
    }

    @Override
    public PageList<BatchTaskSortOrderDTO> findBatchTaskSortItemByOrder(String batchTaskId, Integer currentPage,
        Integer pageSize) {
        AssertUtils.notNull(batchTaskId, "波次任务id不能为null");
        currentPage = ObjectUtils.defaultIfNull(currentPage, 1);
        pageSize = ObjectUtils.defaultIfNull(pageSize, 100);
        return batchOrderTaskBL.findBatchTaskSortItemByOrder(batchTaskId, currentPage, pageSize);
    }

    /**
     * 整件打包拣货完成任务明细
     *
     * @param batchTaskId
     * @return
     */
    @Override
    public BatchTaskItemPickCompleteDetailDTO
        findCompleteBatchTaskItem(BatchTaskItemPickCompleteDetailQueryDTO queryDTO) {
        AssertUtils.hasText(queryDTO.getBatchTaskId(), "拣货任务信息不能为空！");

        return batchOrderTaskBL.findCompleteBatchTaskItem(queryDTO);
    }

    /**
     * 整件打包拣货任务明细
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<BatchTaskItemPackageReviewDetailDTO>
        findPackageReviewBatchItem(BatchTaskItemPickCompleteDetailQueryDTO queryDTO) {
        AssertUtils.hasText(queryDTO.getBatchTaskId(), "拣货任务信息不能为空！");

        return batchOrderTaskBL.findPackageReviewBatchItem(queryDTO);
    }

    @Override
    public BatchTaskSowDTO beginBatchTask(String batchTaskId, String operateUser, Integer userId) {
        AssertUtils.notNull(batchTaskId, "波次任务id不能为bull");
        // 播种关联校验
        sowManagerBL.checkSowBatchTask(batchTaskId);
        return batchOrderTaskBL.beginBatchTask(batchTaskId, operateUser, userId);
    }

    @Override
    public Map<String, List<BatchTaskDTO>> updateBatchTaskItem(
        List<BatchTaskItemCompleteDTO> batchTaskItemUpdateDTOList, String batchTaskId, String userName,
        Integer warehouseId, Long locationId, String locationName, Integer cityId, Integer userId, Byte containerFlag) {
        AssertUtils.notNull(batchTaskId, "波次任务id不能为null");
        AssertUtils.notEmpty(batchTaskItemUpdateDTOList, "波次任务详情不能为空");
        // AssertUtils.notNull(locationId, "货位id不能为null");
        Map<String, List<BatchTaskDTO>> msgMap = new HashMap<>();
        try {
            msgMap = batchOrderTaskBL.updateBatchTaskItem(batchTaskItemUpdateDTOList, batchTaskId, userName,
                warehouseId, locationId, locationName, cityId, userId, containerFlag);
            // msgMap = batchTaskFinishBL.completeBatchTaskItem(batchTaskItemUpdateDTOList, batchTaskId, userName,
            // warehouseId, locationId, locationName, cityId, userId, containerFlag);
        } catch (HasLockedException e) {
            throw new BusinessValidateException("正在处理，请稍后再试！");
        }
        return msgMap;
    }

    @Override
    public PageList<BatchTaskItemDTO> findBatchTaskItemList(BatchTaskItemQueryDTO batchTaskItemQueryDTO) {
        return batchOrderTaskBL.findBatchTaskItemList(batchTaskItemQueryDTO);
    }

    @Override
    public Map<String, List<BatchTaskItemDTO>> findBatchTaskItemMap(List<String> batchTaskNo) {
        return batchOrderTaskBL.findBatchTaskItemMap(batchTaskNo);
    }

    @Override
    public int updateBatch(List<BatchTaskDTO> BatchTaskDTOS, String operateUser) {
        AssertUtils.notNull(BatchTaskDTOS, "修改波次任务list不能为空");
        return batchOrderTaskBL.updateBatch(BatchTaskDTOS, operateUser);
    }

    /**
     * 批量更新波次任务
     *
     * @param modDTO
     * @return
     */
    @Override
    public int updateBatchTaskBatch(BatchTaskModDTO modDTO) {
        AssertUtils.notNull(modDTO.getList(), "修改波次任务list不能为空");
        return batchOrderTaskBL.updateBatchTaskBatch(modDTO);
    }

    /**
     * 领取拣货任务
     */
    @Override
    public void receiveBatchTask(BatchTaskDTO batchTaskDTO) {
        AssertUtils.notNull(batchTaskDTO.getId(), "拣货任务id不能为空");
        AssertUtils.notNull(batchTaskDTO.getSorterId(), "拣货员id不能为空");
        AssertUtils.notNull(batchTaskDTO.getSorter(), "拣货员不能为空");
        try {
            batchOrderTaskBL.receiveBatchTask(batchTaskDTO);
        } catch (HasLockedException e) {
            throw new BusinessValidateException("正在处理，请稍后再试！");
        }
    }

    /**
     * 记录打印
     *
     * @param taskNoList 拣货单编号
     * <AUTHOR>
     */
    @Override
    public void recordPrint(List<String> taskNoList, String operateUser) {
        batchOrderTaskBL.recordPrint(taskNoList, operateUser);
    }

    /**
     * 根据播种任务编号查询拣波次任务
     */
    @Override
    public List<BatchTaskDTO> listBatchTaskBySowTaskNo(String sowTaskNo, List<Integer> taskStates, Integer cityId) {
        AssertUtils.notNull(sowTaskNo, "播种任务编号不能为空");
        return batchOrderTaskBL.listBatchTaskBySowTaskNos(Collections.singletonList(sowTaskNo), taskStates, cityId);
    }

    /**
     * 根据播种任务查询波次任务列表
     * 
     * @param queryParam@return
     */
    @Override
    public List<BatchTaskDTO> findBatchTaskBySowTaskNo(BatchTaskBySowTaskQueryParam queryParam) {
        AssertUtils.notEmpty(queryParam.getSowTaskNos(), "播种任务编号不能为空");
        return batchOrderTaskBL.listBatchTaskBySowTaskNos(queryParam.getSowTaskNos(), queryParam.getTaskStates(),
            queryParam.getOrgId());
    }

    @Override
    public List<BatchTaskDTO> listBatchTaskByWarehouse(Integer orgId, Integer WarehouseId) {
        AssertUtils.notNull(orgId, "城市编号不能为空");
        AssertUtils.notNull(WarehouseId, "仓库编号不能为空");

        return batchOrderTaskBL.listBatchTaskByWarehouse(orgId, WarehouseId);
    }

    /**
     * 根据SKU查询二级仓生成内配单的订单中已入库未出库数量 - 盘点计算差异
     * 
     * @return
     */
    @Override
    public Map<Long, BigDecimal> findCreateAllocationPickedCountForStoreCheck(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds, Map<Long, List<Long>> relationGroupMap) {
        return batchOrderTaskBL.findCreateAllocationPickedCountForStoreCheck(orgId, warehouseId, productSkuIds,
            relationGroupMap);
    }

    /**
     * 根据SKU查询二级仓生成内配单的订单中已入库未出库数量
     * 
     * @return
     */
    @Override
    public Map<Long, BigDecimal> findPickedCountBySkuIdForCreateAllocation(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds, boolean isForStoreCheck) {
        AssertUtils.notNull(orgId, "城市编号不能为空");
        AssertUtils.notNull(warehouseId, "仓库编号不能为空");
        AssertUtils.notEmpty(productSkuIds, "SkuId不能为空");
        return batchTaskQueryBL.findPickedCountBySkuIdForCreateAllocation(orgId, warehouseId, productSkuIds,
            isForStoreCheck);
    }

    /**
     * 根据SKU查询2.5已拣货未出库的库存数量(排除已生内配单的订单) - 盘点计算差异
     *
     */
    @Override
    public Map<Long, BigDecimal> findSCM25PickedCountForStoreCheck(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds, Map<Long, List<Long>> relationGroupMap) {
        return batchTaskQueryBL.findSCM25PickedCountForStoreCheck(orgId, warehouseId, productSkuIds, relationGroupMap);
    }

    /**
     * 根据SKU查询2.5已拣货未出库的库存数量
     *
     * @param orgId
     * @param warehouseId
     * @param productSkuIds
     * @return
     */
    @Override
    public Map<Long, BigDecimal> findPickedCountBySkuIdForSCM25(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds, boolean isForStoreCheck) {
        AssertUtils.notNull(orgId, "城市编号不能为空");
        AssertUtils.notNull(warehouseId, "仓库编号不能为空");
        AssertUtils.notEmpty(productSkuIds, "SkuId不能为空");
        return batchTaskQueryBL.findPickedCountBySkuIdForSCM25(orgId, warehouseId, productSkuIds, isForStoreCheck);
    }

    /**
     * 根据订单ID获取出库位
     * 
     * @param orderId
     * @param orgId
     * @return
     */
    @Override
    public String getToLocationByOrderId(Long orderId, Integer orgId) {
        return batchOrderTaskBL.getToLocationByOrderId(orderId, orgId);
    }

    /**
     * 根据播种任务编号查询拣波次任务
     */
    @Override
    public List<BatchTaskDTO> listBatchTaskBySowTaskNos(List<String> sowTaskNos, List<Integer> taskStates,
        Integer cityId) {
        AssertUtils.notEmpty(sowTaskNos, "播种任务编号不能为空");
        return batchOrderTaskBL.listBatchTaskBySowTaskNos(sowTaskNos, taskStates, cityId);
    }

    /**
     * 根据订单号获取播种任务
     * 
     * @param orderNos
     * @return
     */
    @Override
    public PageList<SowTaskDTO> findSowByOrderNos(List<String> orderNos, Integer orgId, Integer pageNum,
        Integer pageSize) {
        AssertUtils.notNull(orgId, "城市id不能为空");
        AssertUtils.notNull(pageNum, "pageNum不能为空");
        AssertUtils.notNull(pageSize, "pageSize不能为空");
        return batchOrderTaskBL.findSowByOrderNos(orderNos, orgId, pageNum, pageSize);
    }

    /**
     * 根据ids获取订单信息
     * 
     * @param orgId
     * @param refOrderIdList
     * @return
     */
    @Override
    public List<OutStockOrderDTO> findSimpleOutstockByIds(Integer orgId, List<Long> refOrderIdList) {
        AssertUtils.notNull(orgId, "城市id不能为空");
        AssertUtils.notEmpty(refOrderIdList, "订单id不能为空");
        return batchOrderTaskBL.findSimpleOutstockByIds(orgId, refOrderIdList);
    }

    /**
     * 查询已拣货未出库的拣货任务占用的周转区库存数量
     * 
     * @return
     */
    @Override
    public List<BatchTaskLocationUseCountDTO> listBatchTaskLocationUseCount(Integer cityId, Integer warehouseId) {
        return batchOrderTaskBL.listBatchTaskLocationUseCount(cityId, warehouseId);
    }

    /**
     * 查询出库单是否生成了内配单
     */
    @Override
    public Map<String, Boolean> findCreateAllocationByOrderId(List<String> orderIds) {
        return outStockOrderBL.findCreateAllocationByOrderId(orderIds);
    }

    /**
     * 查询订单关联拣货任务货位信息
     */
    @Override
    public List<BatchTaskDTO> findOrderRelatedBatchTaskLocation(Integer orgId, Integer warehouseId, String orderNo) {
        return batchOrderTaskBL.findOrderRelatedBatchTaskLocation(orgId, warehouseId, orderNo);
    }

    /**
     * 根据SKU查询2.5分拣占用详细信息（包含内配分拣占用）
     */
    @Override
    public List<PickedDetailDTO> findPickedDetailBySkuIdForSCM25(Integer orgId, Integer warehouseId, Long productSkuId,
        boolean isForStoreCheck) {
        AssertUtils.notNull(orgId, "城市ID不能为空");
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notNull(productSkuId, "产品SkuId不能为空");
        return batchTaskQueryBL.findPickedDetailBySkuIdForSCM25(orgId, warehouseId, productSkuId, isForStoreCheck);
    }

    /**
     * 根据订单ID，规格信息查询子项出库位
     */
    @Override
    public List<Long> findOrderItemToLocationIdBySpecId(Long orderId, Long specId, Long ownerId) {
        return outStockOrderBL.findOrderItemToLocationIdBySpecId(orderId, specId, ownerId);
    }

    /**
     * 根据订单ID查询子项出库位
     */
    @Override
    public List<Long> findToLocationIdByOrderId(Long orderId) {
        return outStockOrderBL.findToLocationIdByOrderId(orderId);
    }

    /**
     * 根据拣货任务项id获取实际分配数量
     * 
     * @return
     */
    @Override
    public Map<String, List<OrderItemDetailAllotDTO>> getBatchTaskItemAllotMap(List<String> batchTaskItemIds) {
        return batchOrderTaskBL.getBatchTaskItemAllotMap(batchTaskItemIds);
    }

    /**
     * 获取拣货任务不同状态的数量
     * 
     * @return
     */
    @Override
    public List<BatchTaskStateCountDTO> getBatchTaskStateCount(Integer cityId, Integer warehouseId, Integer userId) {
        return batchOrderTaskBL.getBatchTaskStateCount(cityId, warehouseId, userId);
    }

    /**
     * 获取拣货任务分拣中数量
     * 
     * @return
     */
    @Override
    public Integer getSortingBatchTaskCount(Integer cityId, Integer warehouseId, Integer userId) {
        return batchOrderTaskBL.getSortingBatchTaskCount(cityId, warehouseId, userId);
    }

    /**
     * 获取拣货任务最长已进行时间（天数）
     * 
     * @return
     */
    @Override
    public Integer getSortingBatchTaskMaxDays(Integer cityId, Integer warehouseId, Integer userId) {
        return batchOrderTaskBL.getSortingBatchTaskMaxDays(cityId, warehouseId, userId);
    }

    /**
     * 获取团购订单摘果任务列表
     * 
     * @return
     */
    @Override
    public PageList<GroupBuyBatchTaskDTO> listBatchTaskByGroupBuy(GroupBuyBatchTaskSO so) {
        return batchOrderTaskBL.listBatchTaskByGroupBuy(so);
    }

    /**
     * 获取团购订单作业进度列表
     * 
     * @return
     */
    @Override
    public PageList<GroupBuyWorkScheduleDTO> listWorkScheduleByGroupBuy(GroupBuyWorkScheduleSO so) {
        throw new BusinessException("NOT SUPPORT!");
    }

    /**
     * 根据产品名称查询2.5分拣占用详细信息（包含内配分拣占用）
     */
    @Override
    public List<PickedProductDTO> findPickedDetailByProductNameForSCM25(Integer orgId, Integer warehouseId,
        String productName) {
        return batchOrderTaskBL.findPickedDetailByProductNameForSCM25(orgId, warehouseId, productName);
    }

    /**
     * 查询2.5分拣占用详细信息（包含内配分拣占用）
     *
     * @param param 查询条件
     */
    @Override
    public List<PickedProductDTO> findPickedDetailForSCM25(PickedDetailQueryParam param) {
        return batchTaskPickupBL.findPickedDetailForSCM25(param);
    }

    /**
     * 查询缺货产品
     * 
     * @return
     */
    @Override
    public List<Long> getLackProductSkuId(Integer cityId, Integer warehouseId, Date startTime, Date endTime) {
        return batchOrderTaskBL.getLackProductSkuId(cityId, warehouseId, startTime, endTime);
    }

    /**
     * 获取标记缺货的校验类型
     */
    @Override
    public LackCheckMethodResultDTO getMarkLackCheckMethod(LackCheckMethodQueryDTO methodQuery) {
        AssertUtils.notNull(methodQuery.getOrgId(), "城市ID不能为空");
        AssertUtils.notNull(methodQuery.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notNull(methodQuery.getSkuId(), "产品SkuId不能为空");
        return batchOrderTaskBL.getMarkLackCheckMethod(methodQuery.getOrgId(), methodQuery.getWarehouseId(),
            methodQuery.getSkuId());
    }

    /**
     * 获取新的缺货随机码
     */
    @Override
    public Integer newLackRandomCode(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        return batchOrderTaskBL.newLackRandomCode(warehouseId);
    }

    /**
     * 获取旧的缺货随机码
     */
    @Override
    public Integer oldLackRandomCode(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        return batchOrderTaskBL.oldLackRandomCode(warehouseId);
    }

    @Override
    public List<BatchTaskDTO> listBatchTask(BatchTaskQueryDTO queryDTO) {
        return batchOrderTaskBL.listBatchTask(queryDTO);
    }

    @Override
    public List<BatchTaskDTO> listToLocationNameById(List<Long> idList, Integer orgId) {
        AssertUtils.notNull(idList, "id不能为空");
        AssertUtils.notNull(orgId, "城市id不能为空");
        return batchOrderTaskBL.listToLocationNameById(idList, orgId);
    }

    @Override
    public List<BatchTaskItemDTO> listToLocationNameByOrderNo(List<String> orderNos, Integer orgId) {
        AssertUtils.notEmpty(orderNos, "单号不能为空");
        AssertUtils.notNull(orgId, "城市id不能为空");
        return batchOrderTaskBL.listToLocationNameByOrderNo(orderNos, orgId);
    }

    @Override
    public PageList<BatchTaskSowItemDTO> findBatchTaskSowItemByProduct(String batchTaskId, Integer currentPage,
        Integer pageSize) {
        AssertUtils.notNull(batchTaskId, "波次任务id不能为null");
        currentPage = ObjectUtils.defaultIfNull(currentPage, 1);
        pageSize = ObjectUtils.defaultIfNull(pageSize, 100);
        return batchOrderTaskBL.findBatchTaskSowItemByProduct(batchTaskId, currentPage, pageSize);
    }

    @Override
    public PageList<BatchTaskItemDTO> listBatchTaskItem(BatchTaskItemQueryDTO queryDTO) {
        return batchOrderTaskBL.listBatchTaskItem(queryDTO);
    }

    @Override
    public List<BatchTaskDTO> saaSListBatchTask(BatchTaskPramDTO batchTaskPramDTO) {
        return batchOrderTaskBL.listBatchTask(batchTaskPramDTO);
    }

    @Override
    public PageList<BatchTaskDTO> saasBatchTaskList(BatchTaskQueryDTO batchTaskQueryDTO) {
        return batchOrderTaskBL.saasBatchTaskList(batchTaskQueryDTO);
    }

    @Override
    public List<BatchTaskDTO> saasListBatchTaskBySowTaskNo(BatchTaskQueryDTO batchTaskQueryDTO) {
        return listBatchTaskBySowTaskNo(batchTaskQueryDTO.getSowTaskNo(), batchTaskQueryDTO.getTaskStates(),
            batchTaskQueryDTO.getCityId());
    }

    @Override
    public List<BatchTaskDTO> saasGetBatchSowTask(BatchTaskQueryDTO batchTaskQueryDTO) {
        return batchOrderTaskBL.saasGetBatchSowTask(batchTaskQueryDTO);
    }

    @Override
    public void saveBatchTaskContainerCount(BatchTaskContainersDTO batchTaskContainersDTO) {
        AssertUtils.notNull(batchTaskContainersDTO.getBatchTaskNo(), "拣货任务号不能为空!");
        AssertUtils.notNull(batchTaskContainersDTO.getContainerCount(), "容器数量不能为空!");
        batchOrderTaskBL.saveBatchTaskContainerCount(batchTaskContainersDTO);
    }

    /**
     * 通过出库单 id 查找对应拣货任务
     *
     * @param outStockOrderIds 出库单 id
     * @return 找到的拣货任务, 找不到返回空 list
     */
    @Override
    public List<BatchTaskDTO> findBatchTaskByOrder(List<Long> outStockOrderIds) {
        return batchTaskQueryBL.findBatchTaskByOrder(outStockOrderIds);
    }

    /**
     * 根据SKU查询2.5已拣货未出库的库存数量
     *
     * @param orgId
     * @param warehouseId
     * @param productSkuIds
     * @return
     */
    @Override
    public List<PickedDetailDTO> findPickedDetailDTOSForSCM25(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds, boolean isForStoreCheck) {
        AssertUtils.notNull(orgId, "城市编号不能为空");
        AssertUtils.notNull(warehouseId, "仓库编号不能为空");
        AssertUtils.notEmpty(productSkuIds, "SkuId不能为空");
        return batchTaskQueryBL.findPickedDetailDTOSForSCM25(orgId, warehouseId, productSkuIds, isForStoreCheck);
    }

    /**
     * 根据订单号查询有组合产品缺货的订单号列表
     *
     * @param orderNos 订单号
     * @param warehouseId 仓库 id
     * <AUTHOR>
     * @since 2019年3月12日 下午4:22:31
     */
    @Override
    public List<LackOrderInfoDTO> findPartGroupOrderList(List<String> orderNos, Integer warehouseId) {
        AssertUtils.notEmpty(orderNos, "订单号不能为空");
        AssertUtils.notNull(warehouseId, "仓库 id 不能为空");
        return batchTaskQueryBL.findPartGroupOrderList(orderNos, warehouseId);
    }

    /**
     * 获取在播种中数量
     */
    @Override
    public Map<Long, BigDecimal> findSowPickedCountBySkuIdForSCM25(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds) {
        AssertUtils.notNull(orgId, "城市编号不能为空");
        AssertUtils.notNull(warehouseId, "仓库编号不能为空");
        AssertUtils.notEmpty(productSkuIds, "SkuId不能为空");
        return batchTaskQueryBL.findSowPickedCountBySkuIdForSCM25(orgId, warehouseId, productSkuIds);
    }

    /**
     * 通过订单项 id 查询拣货任务项
     *
     * @param orderItemIds 订单项 id
     * @return 查询结果
     */
    @Override
    public Map<Long, List<BatchTaskItemDTO>> findBatchTaskItemByOrderItemId(Collection<Long> orderItemIds) {
        AssertUtils.notEmpty(orderItemIds, "订单项 id 不能为空");
        return batchTaskQueryBL.findBatchTaskItemByOrderItemId(orderItemIds);
    }

    /**
     * 根据SKU查询分拣占用详细信息
     */
    @Override
    public List<PickedDetailDTO> findPickedDetailByCondition(PickedDetailQueryDTO queryDTO) {
        return batchTaskQueryBL.findPickedDetailByCondition(queryDTO);
    }

    /**
     * 拣货任务项禁止销售配置查询
     */
    @Override
    public List<BatchTaskItemPeriodConfigResultDTO>
        listBatchTaskItemPeriodConfig(BatchTaskItemPeriodConfigQueryDTO queryDTO) {
        return batchTaskQueryBL.listBatchTaskItemPeriodConfig(queryDTO);
    }

    /**
     * 通过货位信息查询分区信息，查询最近完成的拣货任务
     *
     * @param dto
     * @return
     */
    @Override
    public BatchTaskDTO getSortGroupLatestBatchTask(SortGroupLatestBatchTaskGetDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");
        AssertUtils.hasText(dto.getCallNum(), "控制器信息不能为空！");

        return batchTaskQueryBL.getSortGroupLatestBatchTask(dto);
    }
}
