package com.yijiupi.himalaya.supplychain.waves.util;

import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationLargePickCargoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.LocationSplitHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import org.apache.commons.lang3.BooleanUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: LargePickCargoUtil
 * @description:
 * @date 2022-12-14 10:12
 */
public class LargePickCargoUtil {


    /**
     * 如果sku聚合的列表任意一项都不包含存储位，则不需要合并
     * @param bo
     * @return
     */
    public static boolean needMergeUnit(LocationSplitHelperBO bo) {
       return bo.getItemList().stream().anyMatch(m -> Objects.nonNull(m.getSubCategory()) && LocationEnum.存储位.getType().byteValue() == m.getSubCategory());
    }

    /**
     * 过滤出小件数和 零拣位 数量 不相同的
     * @param bo
     * @return
     */
    @Deprecated
    public static boolean needMerge(LocationSplitHelperBO bo) {
        BigDecimal unitCount = bo.getItemList().stream().filter(m -> Objects.nonNull(m.getSubCategory()) && CreateBatchLocationLargePickCargoBL.smallLocationList.contains(m.getSubCategory().intValue()))
                .map(OutStockOrderItemPO :: getUnittotalcount).reduce(BigDecimal.ZERO, BigDecimal :: add);
        if (bo.getUnitCount().equals(unitCount)) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * 过滤是否可以合并
     * 这里的订单项packageCount 都是 0
     * unitCount 都不为0
     * @param itemList
     * @return
     */
    public static boolean filterMerge(List<OutStockOrderItemPO> itemList) {
        if (itemList.size() == 1) {
            return Boolean.FALSE;
        }

        Optional<OutStockOrderItemPO> otherItemOptional = itemList.stream().filter(m -> Objects.nonNull(m.getSubCategory()) && CreateBatchLocationLargePickCargoBL.smallLocationList.contains(m.getSubCategory().intValue())).findAny();

        if (BooleanUtils.isFalse(otherItemOptional.isPresent())) {
            return Boolean.FALSE;
        }

        OutStockOrderItemPO otherItemPO = otherItemOptional.get();
        boolean hasStoreItem = itemList.stream().anyMatch(m -> Objects.nonNull(m.getSubCategory()) && LocationEnum.存储位.getType().byteValue() == m.getSubCategory() && m.getItemDetailId().equals(otherItemPO.getItemDetailId()));

        if (BooleanUtils.isFalse(hasStoreItem)) {
            return Boolean.FALSE;
        }

        // 聚合存储位的数量，如果数量正好是规格的整数倍，就不用合并了
        //
        BigDecimal storeTotalUnitCount = itemList.stream().filter(m -> Objects.nonNull(m.getSubCategory()) && LocationEnum.存储位.getType().byteValue() == m.getSubCategory())
                .map(OutStockOrderItemPO :: getUnittotalcount)
                .reduce(BigDecimal.ZERO, BigDecimal :: add);

        BigDecimal[] count = storeTotalUnitCount.divideAndRemainder(otherItemPO.getSpecquantity());
        if (BigDecimal.ZERO.compareTo(count[1]) == 0) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

}
