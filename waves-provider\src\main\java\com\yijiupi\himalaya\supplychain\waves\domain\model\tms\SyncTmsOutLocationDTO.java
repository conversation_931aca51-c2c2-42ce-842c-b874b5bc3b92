package com.yijiupi.himalaya.supplychain.waves.domain.model.tms;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/13
 */
public class SyncTmsOutLocationDTO implements Serializable {
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 订单信息
     */
    private List<SyncTmsOutLocationOrderDTO> itemSyncParamList;
    /**
     * 操作人
     */
    private Integer optUserId;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 订单信息
     *
     * @return itemSyncParamList 订单信息
     */
    public List<SyncTmsOutLocationOrderDTO> getItemSyncParamList() {
        return this.itemSyncParamList;
    }

    /**
     * 设置 订单信息
     *
     * @param itemSyncParamList 订单信息
     */
    public void setItemSyncParamList(List<SyncTmsOutLocationOrderDTO> itemSyncParamList) {
        this.itemSyncParamList = itemSyncParamList;
    }

    /**
     * 获取 操作人
     *
     * @return optUserId 操作人
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人
     *
     * @param optUserId 操作人
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }
}
