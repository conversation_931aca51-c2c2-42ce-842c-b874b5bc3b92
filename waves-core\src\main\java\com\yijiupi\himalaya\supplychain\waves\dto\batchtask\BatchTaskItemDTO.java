
package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;

/**
 * 波次任务详情DTO
 *
 * <AUTHOR> 2018/3/17
 */
public class BatchTaskItemDTO implements Serializable {
    /**
     * 主键
     */
    private String id;
    /**
     * 城市ID，分库用（第三方订单使用一个新的自定义CityId）
     */
    private Integer orgId;
    /**
     * 波次任务编号
     */
    private String batchTaskNo;
    /**
     * 波次任务Id
     */
    private String batchTaskId;
    /**
     * 订单编号
     */
    private String refOrderNo;
    /**
     * 关联订单表id
     */
    private String refOrderId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * skuId（赠品SKUId可能为null）
     */
    private Long skuId;
    /**
     * 品牌
     */
    private String productBrand;
    /**
     * 类目
     */
    private String categoryName;
    /**
     * 包装规格
     */
    private String specName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal specQuantity;
    /**
     * 销售规格名称
     */
    private String saleSpec;
    /**
     * 销售规格系数
     */
    private BigDecimal saleSpecQuantity;
    /**
     * 大单位名称
     */
    private String packageName;
    /**
     * 大单位数量
     */
    private BigDecimal packageCount;
    /**
     * 小单位名称
     */
    private String unitName;
    /**
     * 小单位数量
     */
    private BigDecimal unitCount;
    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;
    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte taskState;
    private String taskStateText;
    /**
     * 缺货数量
     */
    private BigDecimal lackUnitCount;

    /**
     * 是否缺货 1 缺货 0不缺货'
     */
    private Byte isLack;

    /**
     * 备注
     */
    private String remark;
    /**
     * 已拣货数量
     */
    private BigDecimal overSortCount;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * 货区或货位类型：0:货位，1:货区
     */
    private Integer locationCategory;
    private String locationCategoryText;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Byte source;
    private String sourceText;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Byte channel;
    private String channelText;
    /**
     * 关联订单详情
     */
    private Long orderItemId;
    /**
     * 拣货方式
     */
    private Byte pickingType;
    private String pickingTypeText;
    /**
     * 线路顺序
     */
    private Integer sequence;

    /**
     * 按订单拣货的订单顺序
     */
    private Integer orderSquence;

    private String userName;
    private String shopName;
    private String mobileNo;
    private String province;
    private String city;
    private String county;
    private String street;
    private String detailAddress;

    /**
     * 订单序号
     */
    private Integer orderSequence;

    /**
     * 已播种小件总数量
     */
    private BigDecimal sownUnitTotalCount;

    /**
     * 播种任务Id
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    private Long productSpecificationId;

    private Long ownerId;

    private Long secOwnerId;

    /**
     * 订单类型
     */
    private Byte orderType;
    private String orderTypeText;

    /**
     * 来源货位id
     */
    private Long fromLocationId;

    /**
     * 来源货位名称
     */
    private String fromLocationName;

    /**
     * 箱码集合
     * 
     * @return
     */
    private List<String> packageCode;

    /**
     * 瓶码集合
     * 
     * @return
     */
    private List<String> unitCode;

    /**
     * 周转区id
     */
    private Long toLocationId;

    /**
     * 周转区名称
     */
    private String toLocationName;

    /**
     * 波次编号
     */
    private String batchNo;

    /**
     * 波次任务生产时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 分拣员(姓名)
     */
    private String sorter;
    /**
     * 分拣员id
     */
    private Integer sorterId;

    /**
     * 片区名称
     */
    private String areaName;

    /**
     * 线路名称
     */
    private String routeName;

    /**
     * 订单筛选策略 1 按区域 2 按线路
     */
    private Byte orderSelection;
    private String orderSelectionText;

    /**
     * 订单属性（1：大件订单 2：小件订单）
     */
    private Byte packageAttribute;

    /**
     * 配送方式（7：快递直发订单）
     */
    private Byte deliveryMode;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 关联业务单据类型1=酒批业务订单，2=酒批业务退货单，3=经纪人撮合业务，4=兑奖订单业务 5 erp业务
     */
    private Byte businessType;

    /**
     * 是否是内配原单,0:否，1:是
     */
    private Byte createAllocation;

    /**
     * 内配类型: 8.内配，9.内配退，11.中转 12.中转退
     */
    private Byte allotType;

    /**
     * 批次入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date batchTime;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 拣货序号
     */
    private Integer pickSequence;

    /**
     * 回退数量
     */
    private BigDecimal rollbackUnitCount;

    /**
     * 产品默认图片
     */
    private String defaultImageFile;

    /**
     * 产品图片
     */
    private List<String> imageFiles;

    /**
     * 控货策略id
     */
    private Long controlConfigId;

    /**
     * 控货策略名称
     */
    private String controlConfigName;

    /**
     * 产品单价
     */
    private BigDecimal unitPrice;

    /**
     * 产品合计金额（产品单价 * 小单位总数量）
     */
    private BigDecimal totalAmount;

    /**
     * 波次Id
     */
    private String batchId;
    /**
     * 条码
     */
    private String barCode;
    /**
     * 重构出库单类型
     */
    private Byte outBoundType;

    /**
     * 播种任务明细状态
     */
    private Byte sowTaskItemState;

    private String skuProperty;

    private List<Long> orderItemIdList;
    /**
     * 拣货人id
     */
    private Integer completeUserId;
    /**
     * 拣货人
     */
    private String completeUser;

    /**
     * @see BatchTaskItemLargePickPatternConstants
     */
    private Byte largePickPattern;

    /**
     * 拣货开始时间
     */
    private Date startTime;

    /**
     * 拣货完成时间
     */
    private Date completeTime;

    /**
     * 拣货任务上的目标货位
     */
    private String batchTaskToLocationName;
    /**
     * 地址id
     */
    private Integer addressId;
    /**
     * 是否是促销订单项
     */
    private Byte IsAdvent;

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getSkuProperty() {
        return skuProperty;
    }

    public void setSkuProperty(String skuProperty) {
        this.skuProperty = skuProperty;
    }

    public Byte getSowTaskItemState() {
        return sowTaskItemState;
    }

    public void setSowTaskItemState(Byte sowTaskItemState) {
        this.sowTaskItemState = sowTaskItemState;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public Byte getAllotType() {
        return allotType;
    }

    public void setAllotType(Byte allotType) {
        this.allotType = allotType;
    }

    public Byte getCreateAllocation() {
        return createAllocation;
    }

    public void setCreateAllocation(Byte createAllocation) {
        this.createAllocation = createAllocation;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    public BigDecimal getTotalAmount() {
        if (unitPrice != null && unitTotalCount != null) {
            return unitPrice.multiply(unitTotalCount).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getRollbackUnitCount() {
        return rollbackUnitCount;
    }

    public void setRollbackUnitCount(BigDecimal rollbackUnitCount) {
        this.rollbackUnitCount = rollbackUnitCount;
    }

    public Byte getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(Byte deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    public Byte getPackageAttribute() {
        return packageAttribute;
    }

    public void setPackageAttribute(Byte packageAttribute) {
        this.packageAttribute = packageAttribute;
    }

    public Byte getIsLack() {
        return isLack;
    }

    public void setIsLack(Byte isLack) {
        this.isLack = isLack;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getSorter() {
        return sorter;
    }

    public void setSorter(String sorter) {
        this.sorter = sorter;
    }

    public Integer getSorterId() {
        return sorterId;
    }

    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public Byte getOrderSelection() {
        return orderSelection;
    }

    public void setOrderSelection(Byte orderSelection) {
        this.orderSelection = orderSelection;
    }

    public Long getControlConfigId() {
        return controlConfigId;
    }

    public void setControlConfigId(Long controlConfigId) {
        this.controlConfigId = controlConfigId;
    }

    public String getControlConfigName() {
        return controlConfigName;
    }

    public void setControlConfigName(String controlConfigName) {
        this.controlConfigName = controlConfigName;
    }

    public Long getFromLocationId() {
        return fromLocationId;
    }

    public void setFromLocationId(Long fromLocationId) {
        this.fromLocationId = fromLocationId;
    }

    public String getFromLocationName() {
        return fromLocationName;
    }

    public void setFromLocationName(String fromLocationName) {
        this.fromLocationName = fromLocationName;
    }

    public Byte getOrderType() {
        return orderType;
    }

    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Integer getOrderSequence() {
        return orderSequence;
    }

    public void setOrderSequence(Integer orderSequence) {
        this.orderSequence = orderSequence;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    public Integer getOrderSquence() {
        return orderSquence;
    }

    public void setOrderSquence(Integer orderSquence) {
        this.orderSquence = orderSquence;
    }

    public Integer getSequence() {
        return sequence;
    }

    public boolean getIsWine() {
        return !(Objects.equals(getCategoryName(), "白酒") || Objects.equals(getCategoryName(), "葡萄酒")
            || Objects.equals(getCategoryName(), "啤酒"));
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public Byte getChannel() {
        return channel;
    }

    public void setChannel(Byte channel) {
        this.channel = channel;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public String getRefOrderId() {
        return refOrderId;
    }

    public void setRefOrderId(String refOrderId) {
        this.refOrderId = refOrderId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public String getSaleSpec() {
        return saleSpec;
    }

    public void setSaleSpec(String saleSpec) {
        this.saleSpec = saleSpec;
    }

    public BigDecimal getSaleSpecQuantity() {
        return saleSpecQuantity;
    }

    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Byte getTaskState() {
        return taskState;
    }

    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    public BigDecimal getLackUnitCount() {
        return lackUnitCount;
    }

    public void setLackUnitCount(BigDecimal lackUnitCount) {
        this.lackUnitCount = lackUnitCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getOverSortCount() {
        return overSortCount;
    }

    public void setOverSortCount(BigDecimal overSortCount) {
        this.overSortCount = overSortCount;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Integer getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(Integer locationCategory) {
        this.locationCategory = locationCategory;
    }

    public Byte getPickingType() {
        return pickingType;
    }

    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    /**
     * 按产品拣货GroupKey
     *
     * @return
     */
    public String getProductIdentityKey() {
        Byte isAdvent = Objects.isNull(getIsAdvent()) ? ConditionStateEnum.否.getType() : getIsAdvent();
        return String.format("%s_%s_%s_%s_%s_%s_%s", getSkuId(), getLocationId(),
            getSource() == null ? 0 : getSource().intValue(), getChannel() == null ? 0 : getChannel().intValue(),
            getSaleSpecQuantity(), getControlConfigId(), isAdvent);
    }

    public BigDecimal getSownUnitTotalCount() {
        return sownUnitTotalCount;
    }

    public void setSownUnitTotalCount(BigDecimal sownUnitTotalCount) {
        this.sownUnitTotalCount = sownUnitTotalCount;
    }

    public List<String> getPackageCode() {
        return packageCode;
    }

    public void setPackageCode(List<String> packageCode) {
        this.packageCode = packageCode;
    }

    public List<String> getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(List<String> unitCode) {
        this.unitCode = unitCode;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public String getTaskStateText() {
        return taskStateText;
    }

    public void setTaskStateText(String taskStateText) {
        this.taskStateText = taskStateText;
    }

    public String getLocationCategoryText() {
        return locationCategoryText;
    }

    public void setLocationCategoryText(String locationCategoryText) {
        this.locationCategoryText = locationCategoryText;
    }

    public String getSourceText() {
        return sourceText;
    }

    public void setSourceText(String sourceText) {
        this.sourceText = sourceText;
    }

    public String getChannelText() {
        return channelText;
    }

    public void setChannelText(String channelText) {
        this.channelText = channelText;
    }

    public String getPickingTypeText() {
        return pickingTypeText;
    }

    public void setPickingTypeText(String pickingTypeText) {
        this.pickingTypeText = pickingTypeText;
    }

    public String getOrderTypeText() {
        return orderTypeText;
    }

    public void setOrderTypeText(String orderTypeText) {
        this.orderTypeText = orderTypeText;
    }

    public String getOrderSelectionText() {
        return orderSelectionText;
    }

    public void setOrderSelectionText(String orderSelectionText) {
        this.orderSelectionText = orderSelectionText;
    }

    public Integer getPickSequence() {
        return pickSequence;
    }

    public void setPickSequence(Integer pickSequence) {
        this.pickSequence = pickSequence;
    }

    public String getDefaultImageFile() {
        return defaultImageFile;
    }

    public void setDefaultImageFile(String defaultImageFile) {
        this.defaultImageFile = defaultImageFile;
    }

    public List<String> getImageFiles() {
        return imageFiles;
    }

    public void setImageFiles(List<String> imageFiles) {
        this.imageFiles = imageFiles;
    }

    /**
     * 获取
     *
     * @return orderItemIdList
     */
    public List<Long> getOrderItemIdList() {
        return this.orderItemIdList;
    }

    /**
     * 设置
     *
     * @param orderItemIdList
     */
    public void setOrderItemIdList(List<Long> orderItemIdList) {
        this.orderItemIdList = orderItemIdList;
    }

    /**
     * 获取 拣货人id
     *
     * @return completeUserId 拣货人id
     */
    public Integer getCompleteUserId() {
        return this.completeUserId;
    }

    /**
     * 设置 拣货人id
     *
     * @param completeUserId 拣货人id
     */
    public void setCompleteUserId(Integer completeUserId) {
        this.completeUserId = completeUserId;
    }

    /**
     * 获取 拣货人
     *
     * @return completeUser 拣货人
     */
    public String getCompleteUser() {
        return this.completeUser;
    }

    /**
     * 设置 拣货人
     *
     * @param completeUser 拣货人
     */
    public void setCompleteUser(String completeUser) {
        this.completeUser = completeUser;
    }

    /**
     * 获取 @see BatchTaskItemLargePickPatternConstants
     *
     * @return largePickPattern @see BatchTaskItemLargePickPatternConstants
     */
    public Byte getLargePickPattern() {
        return this.largePickPattern;
    }

    /**
     * 设置 @see BatchTaskItemLargePickPatternConstants
     *
     * @param largePickPattern @see BatchTaskItemLargePickPatternConstants
     */
    public void setLargePickPattern(Byte largePickPattern) {
        this.largePickPattern = largePickPattern;
    }

    /**
     * 获取 重构出库单类型
     *
     * @return outBoundType 重构出库单类型
     */
    public Byte getOutBoundType() {
        return this.outBoundType;
    }

    /**
     * 设置 重构出库单类型
     *
     * @param outBoundType 重构出库单类型
     */
    public void setOutBoundType(Byte outBoundType) {
        this.outBoundType = outBoundType;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public String getBatchTaskToLocationName() {
        return batchTaskToLocationName;
    }

    public void setBatchTaskToLocationName(String batchTaskToLocationName) {
        this.batchTaskToLocationName = batchTaskToLocationName;
    }

    /**
     * 获取 地址id
     *
     * @return addressId 地址id
     */
    public Integer getAddressId() {
        return this.addressId;
    }

    /**
     * 设置 地址id
     *
     * @param addressId 地址id
     */
    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    /**
     * 获取 是否是促销订单项
     *
     * @return IsAdvent 是否是促销订单项
     */
    public Byte getIsAdvent() {
        return this.IsAdvent;
    }

    /**
     * 设置 是否是促销订单项
     *
     * @param IsAdvent 是否是促销订单项
     */
    public void setIsAdvent(Byte IsAdvent) {
        this.IsAdvent = IsAdvent;
    }
}