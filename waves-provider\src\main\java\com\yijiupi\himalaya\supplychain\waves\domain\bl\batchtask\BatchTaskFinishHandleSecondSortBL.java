package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OutStockOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.model.OutStockOrderItemQuerySO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderSearchSO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/15
 */
@Service
public class BatchTaskFinishHandleSecondSortBL {

    @Autowired
    private OutStockOrderBL outStockOrderBL;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private SowTaskItemMapper sowTaskItemMapper;
    @Autowired
    private SowTaskMapper sowTaskMapper;

    private static final Logger LOGGER = LoggerFactory.getLogger(BatchTaskFinishHandleSecondSortBL.class);

    /**
     * 二次分拣拣货任务，把拣货缺货的数量分摊到出库单项上 <br />
     * 先抄过来，有时间改
     * 
     * @see com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL#updateSecondSow
     */
    public void updateSecondSow(List<BatchTaskItemUpdatePO> batchItemUpdateList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, BatchPO batchPO, BatchTaskPO batchTaskPO, byte taskState) {
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList) || CollectionUtils.isEmpty(batchItemUpdateList)
            || batchPO == null) {
            return;
        }

        // 根据出库单明细项，找到对应的托盘项，更新已完成小单位总数量，取出库单实际出库数量
        updateSecondSortByOutOrderItem(orderItemTaskInfoPOList, batchPO);
        if (taskState == TaskStateEnum.已完成.getType()) {
            List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper
                .listOutStockOrderByBatchTaskId(batchTaskPO.getId(), batchPO.getOrgId(), batchTaskPO.getWarehouseId());
            List<Long> lstOutStockOrders =
                outStockOrderPOS.stream().filter(it -> !it.getState().equals((int)OutStockOrderStateEnum.已拣货.getType()))
                    .map(OutStockOrderPO::getId).collect(Collectors.toList());
            outStockOrderBL.updateStateByOrderIds(lstOutStockOrders, batchPO);
        }
    }

    /**
     * 根据出库单明细项，找到对应的托盘项，更新已完成小单位总数量，取出库单项实际出库数量
     *
     * @param orderItemTaskInfoPOList
     * @param batchPO
     */
    private void updateSecondSortByOutOrderItem(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, BatchPO batchPO) {
        List<BatchPO> batchPOList = batchMapper.listByBatchNos(Arrays.asList(batchPO.getBatchNo()));
        if (CollectionUtils.isEmpty(batchPOList)) {
            return;
        }
        BatchPO newBatch = batchPOList.get(0);
        // 已拣货和已出库的不用重复更新
        if (Objects.equals(BatchStateEnum.PICKINGEND.getType().byteValue(), batchPO.getState())
            || Objects.equals(BatchStateEnum.ALREADYOUTOFSTORE.getType().byteValue(), batchPO.getState())) {
            return;
        }
        Integer orgId = batchPO.getOrgId();
        String batchTaskNo = orderItemTaskInfoPOList.get(0).getBatchTaskNo();

        // 根据出库单明细项，找到对应的托盘项，更新已完成小单位总数量，取出库单实际出库数量
        List<Long> pickOutItemIds = orderItemTaskInfoPOList.stream().filter(it -> it.getRefOrderItemId() != null)
            .map(it -> it.getRefOrderItemId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickOutItemIds)) {
            LOGGER.info("[二次分拣]关联拣货信息无出库单项信息，拣货任务号：{}", batchTaskNo);
            return;
        }

        List<Long> pickOrderIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderId)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickOrderIds)) {
            LOGGER.info("[二次分拣]关联拣货信息无出库单信息，拣货任务号：{}", batchTaskNo);
            return;
        }
        // 正在分拣的出库单项
        OutStockOrderItemQuerySO itemQuerySO = new OutStockOrderItemQuerySO();
        itemQuerySO.setOrgId(orgId);
        itemQuerySO.setIds(pickOutItemIds);
        itemQuerySO.setPageSize(Integer.MAX_VALUE);
        List<OutStockOrderItemDTO> pickOutItemList = outStockOrderItemMapper.findItemAndDetailByIds(itemQuerySO);
        if (CollectionUtils.isEmpty(pickOutItemList)) {
            LOGGER.info("[二次分拣]无出库单项信息，拣货任务号：{}", batchTaskNo);
            return;
        }

        // 查询所有出库单信息
        OutStockOrderSearchSO outQuerySO = new OutStockOrderSearchSO();
        outQuerySO.setOrgId(orgId);
        outQuerySO.setOrderIds(pickOrderIds);
        List<OutStockOrderPO> outOrderList = outStockOrderMapper.listAllInfo(outQuerySO);
        if (CollectionUtils.isEmpty(outOrderList)) {
            LOGGER.info("[二次分拣]无出库单信息，拣货任务号：{}", batchTaskNo);
            return;
        }

        // 获取每一个出库单项的缺货数量
        Map<Long, OrderItemTaskInfoPO> outStockMap = orderItemTaskInfoPOList.stream()
            .collect(Collectors.toMap(OrderItemTaskInfoPO::getRefOrderItemId, it -> it, (v1, v2) -> v1));
        Set<Long> existsOutItemIdList = outStockMap.keySet();
        Map<Long, BigDecimal> outItemLackMap = new HashMap<>();
        pickOutItemList.stream().filter(it -> existsOutItemIdList.contains(it.getId())).forEach(it -> {
            OrderItemTaskInfoPO orderItemTaskInfoPO = outStockMap.get(it.getId());
            if (orderItemTaskInfoPO == null) {
                return;
            }
            outItemLackMap.put(it.getId(), orderItemTaskInfoPO.getLackUnitCount());
        });

        if (outItemLackMap.size() == 0) {
            LOGGER.info("[二次分拣]无拣货缺货，拣货任务号：{}", batchTaskNo);
            return;
        }
        LOGGER.info("[二次分拣]拣货缺货数量：{}", outItemLackMap);
        // 分配缺货数量到出库单
        Map<Long, List<OutStockOrderItemDTO>> pickItemByOrderGroup =
            pickOutItemList.stream().collect(Collectors.groupingBy(OutStockOrderItemDTO::getOutstockorder_Id));

        // 拣货数量是否有变化
        boolean isCountChange = false;
        List<OutStockOrderPO> updateOutOrdList = new ArrayList<>();
        List<OutStockOrderItemPO> updateOutItemList = new ArrayList<>();
        List<OutStockOrderItemDetailPO> addOutDetailList = new ArrayList<>();
        for (OutStockOrderPO ord : outOrderList) {
            OutStockOrderPO updateOrd = new OutStockOrderPO();
            updateOrd.setId(ord.getId());
            BigDecimal packageAmount = BigDecimal.ZERO;
            BigDecimal unitAmount = BigDecimal.ZERO;

            List<OutStockOrderItemDTO> curPickItemList = pickItemByOrderGroup.get(ord.getId());
            if (CollectionUtils.isEmpty(curPickItemList)) {
                continue;
            }
            // 正在分拣的
            for (OutStockOrderItemDTO curItem : curPickItemList) {
                BigDecimal curItemLackUnitCount = outItemLackMap.get(curItem.getId());
                BigDecimal unitTotalCount = curItem.getOriginalUnitTotalCount()
                    .subtract(curItemLackUnitCount == null ? BigDecimal.ZERO : curItemLackUnitCount);

                BigDecimal[] counts = unitTotalCount.divideAndRemainder(curItem.getSpecQuantity());

                OutStockOrderItemPO updatePO = new OutStockOrderItemPO();
                updatePO.setId(curItem.getId());
                updatePO.setUnittotalcount(unitTotalCount);
                updatePO.setPackagecount(counts[0]);
                updatePO.setUnitcount(counts[1]);
                updatePO.setRemark(String.valueOf(SowTaskStateEnum.待播种.getType()));
                updatePO.setSowTaskItemId(curItem.getSowTaskItemId());
                updatePO.setCurItemLackUnitCount(curItemLackUnitCount);
                updatePO.setOutstockorderId(ord.getId());
                updateOutItemList.add(updatePO);

                curItem.setUnitTotalCount(updatePO.getUnittotalcount());
                curItem.setPackageCount(updatePO.getPackagecount());
                curItem.setUnitCount(updatePO.getUnitcount());

                // 出库单项detail
                if (CollectionUtils.isNotEmpty(curItem.getOutStockOrderItemDetailDTOS())) {
                    List<OutStockOrderItemDetailPO> newDetailList =
                        outStockOrderBL.getOutItemDetailByOrderItem(curItem, curItem.getOrg_id(), null);
                    if (CollectionUtils.isNotEmpty(newDetailList)) {
                        addOutDetailList.addAll(newDetailList);
                    }
                }

                packageAmount = packageAmount.add(curItem.getPackageCount());
                unitAmount = unitAmount.add(curItem.getUnitCount());
            }

            // 非正在分拣的
            for (OutStockOrderItemPO oldItem : ord.getItems().stream()
                .filter(ite -> !pickOutItemIds.contains(ite.getId())).collect(Collectors.toList())) {
                packageAmount = packageAmount.add(oldItem.getPackagecount());
                unitAmount = unitAmount.add(oldItem.getUnitcount());
            }

            updateOrd.setPackageamount(packageAmount);
            updateOrd.setUnitamount(unitAmount);

            updateOutOrdList.add(updateOrd);

            if (packageAmount.compareTo(ord.getPackageamount()) != 0
                || unitAmount.compareTo(ord.getUnitamount()) != 0) {
                isCountChange = true;
            }
        }
        if (isCountChange) {
            // 更新出库单相关信息
            // if (CollectionUtils.isNotEmpty(addOutDetailList)) {
            // List<Long> delDetailIdList =
            // addOutDetailList.stream().map(OutStockOrderItemDetailPO::getId).collect(Collectors.toList());
            // LOGGER.info("[二次分拣]删除的出库单项detail：{}", JSON.toJSONString(delDetailIdList));
            // Lists.partition(delDetailIdList, 100)
            // .forEach(itemPart -> outStockOrderItemDetailCommMapper.deleteByItemDetailIds(itemPart, orgId));
            //
            // LOGGER.info("[二次分拣]更新的出库单项detail：{}", JSON.toJSONString(addOutDetailList));
            // Lists.partition(addOutDetailList, 100)
            // .forEach(itemPart -> outStockOrderItemDetailCommMapper.insertBatch(itemPart));
            // }

            if (CollectionUtils.isNotEmpty(updateOutItemList)) {
                LOGGER.info("[二次分拣]更新的出库单项：{}", JSON.toJSONString(updateOutItemList));
                Lists.partition(updateOutItemList, 50)
                    .forEach(itemPart -> outStockOrderItemMapper.updateBatchByPOList(itemPart));
                updateSowTaskItem(orgId, updateOutItemList);
            }

            LOGGER.info("[二次分拣]更新的出库单：{}", JSON.toJSONString(updateOutOrdList));
            outStockOrderMapper.updateBatchByPOList(updateOutOrdList);
        }
    }

    private void updateSowTaskItem(Integer orgId, List<OutStockOrderItemPO> updateOutItemList) {
        updateOutItemList =
            updateOutItemList.stream().filter(m -> Objects.nonNull(m.getSowTaskItemId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateOutItemList)) {
            return;
        }
        // 只要修改出库单想 就需要修改播种项
        Map<Long, List<OutStockOrderItemPO>> collect =
            updateOutItemList.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getSowTaskItemId));
        // 获取所有的播种任务项
        List<SowTaskItemPO> sowTaskItemPOList = sowTaskItemMapper.findByItemIds(orgId, updateOutItemList.stream()
            .map(OutStockOrderItemPO::getSowTaskItemId).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(sowTaskItemPOList)) {
            return;
        }
        // 减去播种项数量
        for (SowTaskItemPO sowTaskItemPO : sowTaskItemPOList) {
            if (Objects.isNull(sowTaskItemPO.getOverUnitTotalCount())) {
                Map<Long, BigDecimal> updateSowItemMap = new HashMap<>();
                Set<Long> keySet = collect.keySet();
                for (Long key : keySet) {
                    // TODO 这里会报空指针
                    List<OutStockOrderItemPO> outStockOrderItemPOS = collect.get(key);
                    updateSowItemMap.put(key, outStockOrderItemPOS.stream().map(OutStockOrderItemPO::getUnittotalcount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                BigDecimal bigDecimal = updateSowItemMap.get(sowTaskItemPO.getId());
                if (BigDecimal.ZERO.compareTo(bigDecimal) == 0) {
                    // 没有开始播种 但是修改订单数量跟播种数量已经全缺
                    sowTaskItemPO.setUnitTotalCount(BigDecimal.ZERO);
                    sowTaskItemPO.setState(SowTaskStateEnum.已播种.getType());
                    LOGGER.info("全缺修改播种任务 id={}", sowTaskItemPO.getId());
                    sowTaskItemMapper.updateByPrimaryKeySelective(sowTaskItemPO);
                } else {
                    // 没有开始播种 但是修改订单数量跟播种数量已经全缺
                    sowTaskItemPO.setUnitTotalCount(bigDecimal);
                    LOGGER.info("部分缺修改播种任务 id={}", sowTaskItemPO.getId());
                    sowTaskItemMapper.updateByPrimaryKeySelective(sowTaskItemPO);
                }
            } else {
                Map<Long, BigDecimal> updateSowItemMap = new HashMap<>();
                Set<Long> keySet = collect.keySet();
                for (Long key : keySet) {
                    List<OutStockOrderItemPO> outStockOrderItemPOS = collect.get(key);
                    updateSowItemMap.put(key, outStockOrderItemPOS.stream()
                        .map(OutStockOrderItemPO::getCurItemLackUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                BigDecimal bigDecimal = updateSowItemMap.get(sowTaskItemPO.getId());
                BigDecimal add = bigDecimal.add(sowTaskItemPO.getOverUnitTotalCount());
                if (add.compareTo(sowTaskItemPO.getUnitTotalCount()) == 0) {
                    sowTaskItemPO.setUnitTotalCount(sowTaskItemPO.getOverUnitTotalCount());
                    sowTaskItemPO.setState(SowTaskStateEnum.已播种.getType());
                    LOGGER.info("部分缺修改播种任务 id={}", sowTaskItemPO.getId());
                    sowTaskItemMapper.updateByPrimaryKeySelective(sowTaskItemPO);
                    continue;
                }
                sowTaskItemPO.setUnitTotalCount(
                    sowTaskItemPO.getUnitTotalCount().subtract(sowTaskItemPO.getOverUnitTotalCount()));
                // 正常缺货修改数据
                sowTaskItemMapper.updateByPrimaryKeySelective(sowTaskItemPO);
            }
        }
        // 获取所有的播种任务项
        List<SowTaskItemPO> sowTaskItemPOListNew =
            sowTaskItemMapper.findBySowTaskNo(orgId, sowTaskItemPOList.get(0).getSowTaskId());
        if (sowTaskItemPOListNew.stream().allMatch(s -> s.getState().compareTo(SowTaskStateEnum.已播种.getType()) == 0)) {
            // 修改播种任务为已完成
            sowTaskMapper.updateSowTaskState(sowTaskItemPOListNew.get(0).getSowTaskNo(), SowTaskStateEnum.已播种.getType(),
                orgId);
        }
    }

}
