package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.OrderCommonDetailDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OutStockOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.ThirdPartyOutStockBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation.OutStockLocationGrayBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation.bo.OutStockOrderBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchBaseBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchSplitBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/10/16
 */
@Service
public class CreateBatchByManualOperationBL extends CreateBatchBaseBL {

    @Autowired
    private OutStockOrderBL outStockOrderBL;
    @Autowired
    private ThirdPartyOutStockBL thirdPartyOutStockBL;
    @Autowired
    private OutStockLocationGrayBL outStockLocationGrayBL;

    @Override
    protected void doPreCreateBatch(CreateBatchBaseBO createBatchBaseBO) {
        BatchCreateDTO batchCreateDTO = createBatchBaseBO.getBatchCreateDTO();
        if (StringUtils.isEmpty(batchCreateDTO.getBatchName())) {
            batchCreateDTO.setBatchName("手动生成波次");
        }
        LOG.info("手动新增波次{}", JSON.toJSONString(batchCreateDTO));
    }

    @Override
    protected void validateParam(CreateBatchBaseBO createBatchBaseBO) {

    }

    @Override
    protected void validateBusiness(CreateBatchBaseBO createBatchBaseBO, List<OutStockOrderPO> outStockOrderPOList) {
        BatchCreateDTO batchCreateDTO = createBatchBaseBO.getBatchCreateDTO();
        if (!Objects.equals(batchCreateDTO.getBatchType(), BatchTypeEnum.微酒.getType())
            && !BatchTypeEnum.isApplyOrderBatch(batchCreateDTO.getBatchType())) {
            // 检验订单异常
            batchOrderProcessBL.validateOrderException(outStockOrderPOList);
        }

    }

    @Override
    protected void doCreateBatch(CreateBatchBaseBO createBatchBaseBO, List<OutStockOrderPO> outStockOrderPOList) {
        BatchCreateDTO batchCreateDTO = createBatchBaseBO.getBatchCreateDTO();

        // 组装波次参数
        WavesStrategyBO wavesStrategyDTO = batchCreateDTOConvertor.getByBatchCreateDTO(batchCreateDTO);

        createBatchValidateBL.checkStateErrorOrders(outStockOrderPOList);

        ProcessBatchDTO baseProcessBatchDTO =
            batchCreateDTOConvertor.convertProcessBatchDTOByBatchCreateDTO(batchCreateDTO);

        // 这里拆成 内配波次 和 非内配波次
        CreateBatchSplitBO createBatchSplitBO = splitOrderList(outStockOrderPOList);
        if (CollectionUtils.isNotEmpty(createBatchSplitBO.getNormalOrderList())) {
            ProcessBatchDTO normalProcessBatchDTO = new ProcessBatchDTO();
            BeanUtils.copyProperties(baseProcessBatchDTO, normalProcessBatchDTO);
            // 创建波次
            processCreateBatch(createBatchSplitBO.getNormalOrderList(), wavesStrategyDTO, normalProcessBatchDTO);
        }

        if (CollectionUtils.isNotEmpty(createBatchSplitBO.getNpOrderList())) {
            List<OutStockOrderPO> npOrderList = createBatchSplitBO.getNpOrderList();
            handleNpOrderCreateBatch(npOrderList, baseProcessBatchDTO, wavesStrategyDTO);
        }

    }

    private void handleNpOrderCreateBatch(List<OutStockOrderPO> npOrderList, ProcessBatchDTO baseProcessBatchDTO,
        WavesStrategyBO wavesStrategyDTO) {
        Map<Integer, List<OutStockOrderBO>> warehouseOrderGroupMap = splitByToWarehouseId(npOrderList);
        if (org.springframework.util.CollectionUtils.isEmpty(warehouseOrderGroupMap)) {
            batchOrderProcessTransferBL.processCreateBatch(npOrderList, wavesStrategyDTO, baseProcessBatchDTO);
            return;
        }
        Set<Integer> warehouseIds = warehouseOrderGroupMap.keySet();
        List<Warehouse> warehouseList = warehouseQueryService.listWarehouseByIds(new ArrayList<>(warehouseIds));
        Map<Integer, Warehouse> warehouseMap =
            warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));

        for (Map.Entry<Integer, List<OutStockOrderBO>> entry : warehouseOrderGroupMap.entrySet()) {
            Warehouse warehouse = warehouseMap.get(entry.getKey());
            ProcessBatchDTO npProcessBatchDTO = new ProcessBatchDTO();
            BeanUtils.copyProperties(baseProcessBatchDTO, npProcessBatchDTO);
            npProcessBatchDTO.setBatchName(getTitle(warehouse));
            List<OutStockOrderPO> orderPOList =
                entry.getValue().stream().map(OutStockOrderBO::getOrder).collect(Collectors.toList());

            processCreateBatch(orderPOList, wavesStrategyDTO, npProcessBatchDTO);
        }
    }

    private String getTitle(Warehouse warehouse) {
        return String.format("【%s】 %s", warehouse.getCity(), warehouse.getName()).concat("手动");
    }

    private Map<Integer, List<OutStockOrderBO>> splitByToWarehouseId(List<OutStockOrderPO> npOrderList) {
        try {
            List<String> businessIds =
                npOrderList.stream().map(OutStockOrderPO::getBusinessId).distinct().collect(Collectors.toList());
            Map<Long, OrderCommonDetailDTO> commonDetailDTOMap =
                outStockLocationGrayBL.findOrderCenterByPage(businessIds);

            List<OutStockOrderBO> boOrderList = npOrderList.stream()
                .map(order -> outStockLocationGrayBL.convertToOutStockOrderBO(commonDetailDTOMap, order))
                .collect(Collectors.toList());

            Map<Integer, List<OutStockOrderBO>> warehouseOrderGroupMap =
                boOrderList.stream().collect(Collectors.groupingBy(OutStockOrderBO::getToWarehouseId));

            return warehouseOrderGroupMap;
        } catch (Exception e) {
            LOG.warn("查询目标仓库失败，", e);
            return Collections.emptyMap();
        }
    }

    private CreateBatchSplitBO splitOrderList(List<OutStockOrderPO> outStockOrderPOList) {
        List<OutStockOrderPO> npOrderList = outStockOrderPOList.stream()
            .filter(m -> Objects.nonNull(m.getOutBoundType())
                && OutBoundTypeEnum.INTERNAL_DELIVERY_ORDER.getCode().byteValue() == m.getOutBoundType()
                && OrderConstant.ALLOT_TYPE_ALLOCATION.equals(m.getAllotType()))
            .collect(Collectors.toList());

        List<OutStockOrderPO> normalOrderList = outStockOrderPOList.stream()
            .filter(m -> Objects.isNull(m.getOutBoundType())
                || OutBoundTypeEnum.INTERNAL_DELIVERY_ORDER.getCode().byteValue() != m.getOutBoundType()
                || !OrderConstant.ALLOT_TYPE_ALLOCATION.equals(m.getAllotType()))
            .collect(Collectors.toList());

        CreateBatchSplitBO createBatchSplitBO = new CreateBatchSplitBO();
        createBatchSplitBO.setNpOrderList(npOrderList);
        createBatchSplitBO.setNormalOrderList(normalOrderList);

        return createBatchSplitBO;
    }

    @Override
    protected List<OutStockOrderPO> getOutStockOrder(CreateBatchBaseBO createBatchBaseBO) {
        BatchCreateDTO batchCreateDTO = createBatchBaseBO.getBatchCreateDTO();
        // 查询订单
        if (Objects.equals(batchCreateDTO.getBatchType(), BatchTypeEnum.微酒.getType())) {
            return outStockOrderBL.getOutStockOrderByWine(batchCreateDTO.getOrderIdList());
        }
        if (BatchTypeEnum.isApplyOrderBatch(batchCreateDTO.getBatchType())) {
            // 第三方出库新流程，微酒和易经销都需要拣货
            return thirdPartyOutStockBL.getOutStockOrderByThirdParty(batchCreateDTO.getOrderIdList());
        }

        return Lists.partition(batchCreateDTO.getOrderIdList(), MAX_SIZE).stream()
            .map(orderIds -> outStockOrderMapper
                .findByOrderId(orderIds.stream().map(Long::valueOf).collect(Collectors.toList())))
            .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    @Override
    protected List<OutStockOrderPO> filterOutStockOrder(List<OutStockOrderPO> outStockOrderPOList) {
        return outStockOrderPOList;
    }
}
