<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchMapper">
    <!--auto generated Code-->
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="Org_id" property="orgId" jdbcType="INTEGER"/>
        <result column="BatchNo" property="batchNo" jdbcType="VARCHAR"/>
        <result column="BatchName" property="batchName" jdbcType="VARCHAR"/>
        <result column="State" property="state" jdbcType="TINYINT"/>
        <result column="OrderAmount" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="OrderCount" property="orderCount" jdbcType="INTEGER"/>
        <result column="SkuCount" property="skuCount" jdbcType="INTEGER"/>
        <result column="PackageAmount" property="packageAmount" jdbcType="DECIMAL"/>
        <result column="UnitAmount" property="unitAmount" jdbcType="DECIMAL"/>
        <result column="PickingType" property="pickingType" jdbcType="TINYINT"/>
        <result column="PickingGroupStrategy" property="pickingGroupStrategy" jdbcType="TINYINT"/>
        <result column="OrderSelection" property="orderSelection" jdbcType="TINYINT"/>
        <result column="Area_Id" property="areaId" jdbcType="VARCHAR"/>
        <result column="AreaName" property="areaName" jdbcType="VARCHAR"/>
        <result column="Route_Id" property="routeId" jdbcType="VARCHAR"/>
        <result column="RouteName" property="routeName" jdbcType="VARCHAR"/>
        <result column="RouteSequence" property="routeSequence" jdbcType="INTEGER"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="CompleteTime" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="BatchType" property="batchType" jdbcType="TINYINT"/>
        <result column="BatchOrderType" property="batchOrderType" jdbcType="TINYINT"/>
        <result column="CrossWareHouse" property="crossWareHouse" jdbcType="TINYINT"/>
        <result column="SowType" property="sowType" jdbcType="TINYINT"/>

    </resultMap>

    <sql id="BaseResultSQL">
        id
        , Org_id, BatchNo, BatchName, State, OrderAmount, OrderCount, SkuCount, PackageAmount, UnitAmount,
        PickingType, PickingGroupStrategy,
        OrderSelection, Area_Id, AreaName, Route_Id, RouteName, RouteSequence, CreateTime, CreateUser, LastUpdateUser,
        LastUpdateTime, Warehouse_Id, Remark, CompleteTime,
        BatchType, BatchOrderType, CrossWareHouse,SowType
    </sql>


    <resultMap id="BaseLocationResultMap"
               type="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO">
        <result column="locationId" property="locationId" jdbcType="BIGINT"/>
        <result column="outStockOrderId" property="outStockOrderId" jdbcType="BIGINT"/>
        <result column="locationName" property="locationName" jdbcType="VARCHAR"/>
    </resultMap>

    <!--2018-03-15 19:30:06-->
    <select id="findList" resultMap="BaseResultMap">
        select
        bo.id,
        bo.BatchNo,
        bo.BatchName,
        bo.State,
        bo.OrderAmount,
        bo.OrderCount,
        bo.SkuCount,
        bo.PackageAmount,
        bo.UnitAmount,
        bo.CreateTime,
        bo.PickingType,
        bo.Remark
        from batch bo
        where 1 = 1
        AND bo.Warehouse_Id= #{dto.warehouseId,jdbcType=VARCHAR}
        <if test="dto.batchNo != null and dto.batchNo!=''">AND bo.BatchNo= #{dto.batchNo,jdbcType=VARCHAR}</if>
        <if test="dto.batchName != null and dto.batchName!=''">
            AND bo.BatchName like concat('%',#{dto.batchName,jdbcType=VARCHAR},'%')
        </if>
        <if test="dto.state != null">AND bo.State= #{dto.state,jdbcType=TINYINT}</if>
        <if test="dto.stateList != null and dto.stateList.size() > 0">
            AND bo.State in
            <foreach collection="dto.stateList" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="dto.startTime != null">AND bo.CreateTime <![CDATA[ >= ]]> #{dto.startTime}</if>
        <if test="dto.endTime != null">AND bo.CreateTime <![CDATA[ <= ]]> #{dto.endTime}</if>

        <if test="dto.warehouseAllocationType != null and dto.warehouseAllocationType != ''">
            and (bo.WarehouseAllocationType = #{dto.warehouseAllocationType,jdbcType=INTEGER}
            or bo.WarehouseAllocationType is null)
        </if>

        ORDER BY bo.CreateTime DESC
    </select>

    <resultMap id="findBatchOrderInfoListByBatchNoResultMap"
               type="com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOrderInfoDTO">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="BatchNo" property="batchNo" jdbcType="VARCHAR"/>
        <result column="BatchName" property="batchName" jdbcType="VARCHAR"/>
        <result column="PickingType" property="pickingType" jdbcType="TINYINT"/>
        <result column="OrderAmount" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="OrderCount" property="orderCount" jdbcType="INTEGER"/>
        <result column="SkuCount" property="skuCount" jdbcType="INTEGER"/>
        <result column="PackageAmount" property="packageAmount" jdbcType="DECIMAL"/>
        <result column="UnitAmount" property="unitAmount" jdbcType="DECIMAL"/>
        <collection property="items" ofType="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO">
            <result column="oid" property="id" jdbcType="BIGINT"/>
            <result column="RefOrderNo" property="refOrderNo" jdbcType="VARCHAR"/>
            <result column="ordercreatetime" property="orderCreateTime" jdbcType="VARCHAR"/>
            <result column="ShopName" property="shopName" jdbcType="VARCHAR"/>
            <result column="DetailAddress" property="detailAddress" jdbcType="VARCHAR"/>
            <result column="OrderType" property="orderType" jdbcType="TINYINT"/>
            <result column="osoOrderAmount" property="orderAmount" jdbcType="INTEGER"/>
            <result column="osoSkuCount" property="skuCount" jdbcType="INTEGER"/>
            <result column="osoPackageAmount" property="packageAmount" jdbcType="DECIMAL"/>
            <result column="osoUnitAmount" property="unitAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <!--根据波次编号查询该波次关联的订单列表-->
    <select id="findBatchOrderInfoListByBatchNo" resultMap="findBatchOrderInfoListByBatchNoResultMap">
        SELECT bo.id,
        bo.BatchNo,
        bo.BatchName,
        bo.PickingType,
        bo.OrderAmount,
        bo.OrderCount,
        bo.SkuCount,
        bo.PackageAmount,
        bo.UnitAmount,
        oso.id as oid,
        oso.RefOrderNo,
        DATE_FORMAT(oso.OrderCreateTime, '%Y-%m-%d %H:%i:%s') AS ordercreatetime,
        oso.ShopName,
        oso.DetailAddress,
        oso.OrderType,
        oso.OrderAmount as osoOrderAmount,
        oso.SkuCount as osoSkuCount,
        oso.PackageAmount as osoPackageAmount,
        oso.UnitAmount as osoUnitAmount
        FROM batch bo
        LEFT JOIN outstockorder oso
        ON bo.BatchNo = oso.BatchNo
        where bo.BatchNo = #{batchNo}
    </select>

    <select id="selectBatchInfoByBatchNo"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOrderInfoDTO">
        SELECT id,
        BatchNo as batchNo,
        BatchName as batchName,
        State as state,
        PickingType as pickingType,
        OrderAmount as orderAmount,
        OrderCount as orderCount,
        SkuCount as skuCount,
        PackageAmount as packageAmount,
        UnitAmount as unitAmount,
        BatchType as batchType
        FROM batch
        where BatchNo = #{batchNo}
    </select>

    <select id="findDaiJianHuoBatchNo" resultType="java.lang.String">
        SELECT BatchNo from batch where
        `State` in(0,1) and
        BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="findBatchNoByOrderId" resultType="java.lang.String">
        select BatchNo
        from outstockorder
        where Warehouse_Id = #{warehouseId}
        and RefOrderNo = #{orderNo} limit 1
    </select>

    <!--批量创建波次-->
    <insert id="insertBatch" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO">
        insert into batch (id, org_id, BatchNo,
        BatchName, State, OrderAmount,
        SkuCount, PackageAmount, UnitAmount,
        PickingType, PickingGroupStrategy, CreateTime,
        CreateUser
        )
        values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.orgId,jdbcType=INTEGER}, #{item.batchNo,jdbcType=VARCHAR},
            #{item.batchName,jdbcType=VARCHAR}, #{item.state,jdbcType=TINYINT}, #{item.orderAmount,jdbcType=INTEGER},
            #{item.skuCount,jdbcType=INTEGER}, #{item.packageAmount,jdbcType=DECIMAL},
            #{item.unitAmount,jdbcType=DECIMAL},
            #{item.pickingType,jdbcType=TINYINT}, #{item.pickingGroupStrategy,jdbcType=TINYINT}, NOW(),
            #{item.createUser,jdbcType=VARCHAR}
            )
        </foreach>

    </insert>

    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="batchPO.id">
        INSERT INTO batch
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchPO.id!=null">id,</if>
            <if test="batchPO.orgId!=null">org_id,</if>
            <if test="batchPO.batchNo!=null">BatchNo,</if>
            <if test="batchPO.batchName!=null">BatchName,</if>
            <if test="batchPO.warehouseId!=null">Warehouse_Id,</if>
            <if test="batchPO.state!=null">State,</if>
            <if test="batchPO.pickingType!=null">PickingType,</if>
            <if test="batchPO.pickingGroupStrategy!=null">PickingGroupStrategy,</if>
            <if test="batchPO.orderAmount!=null">OrderAmount,</if>
            <if test="batchPO.orderCount!=null">OrderCount,</if>
            <if test="batchPO.skuCount!=null">SkuCount,</if>
            <if test="batchPO.packageAmount!=null">PackageAmount,</if>
            <if test="batchPO.unitAmount!=null">UnitAmount,</if>
            <if test="batchPO.createUser!=null">CreateUser,</if>
            <if test="batchPO.areaId != null">
                Area_Id,
            </if>
            <if test="batchPO.areaName != null">
                AreaName,
            </if>
            <if test="batchPO.routeId != null">
                Route_Id,
            </if>
            <if test="batchPO.routeName != null">
                RouteName,
            </if>
            <if test="batchPO.routeSequence != null">
                RouteSequence,
            </if>
            <if test="batchPO.orderSelection!=null">
                OrderSelection,
            </if>
            <if test="batchPO.remark!=null">
                Remark,
            </if>
            <if test="batchPO.batchType!=null">
                BatchType,
            </if>
            <if test="batchPO.batchOrderType!=null">
                BatchOrderType,
            </if>
            <if test="batchPO.crossWareHouse!=null">
                CrossWareHouse,
            </if>
            <if test="batchPO.sowType!=null">
                sowType,
            </if>
            <if test="batchPO.warehouseAllocationType != null">
                WarehouseAllocationType,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchPO.id!=null">#{batchPO.id,jdbcType=VARCHAR},
            </if>
            <if test="batchPO.orgId!=null">#{batchPO.orgId,jdbcType=INTEGER},
            </if>
            <if test="batchPO.batchNo!=null">#{batchPO.batchNo,jdbcType=VARCHAR},
            </if>
            <if test="batchPO.batchName!=null">#{batchPO.batchName,jdbcType=VARCHAR},
            </if>
            <if test="batchPO.warehouseId!=null">#{batchPO.warehouseId,jdbcType=INTEGER},</if>
            <if test="batchPO.state!=null">#{batchPO.state,jdbcType=TINYINT},
            </if>
            <if test="batchPO.pickingType!=null">#{batchPO.pickingType,jdbcType=TINYINT},
            </if>
            <if test="batchPO.pickingGroupStrategy!=null">#{batchPO.pickingGroupStrategy,jdbcType=TINYINT},
            </if>
            <if test="batchPO.orderAmount!=null">#{batchPO.orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="batchPO.orderCount!=null">#{batchPO.orderCount,jdbcType=INTEGER},
            </if>
            <if test="batchPO.skuCount!=null">#{batchPO.skuCount,jdbcType=INTEGER},
            </if>
            <if test="batchPO.packageAmount!=null">#{batchPO.packageAmount,jdbcType=DECIMAL},
            </if>
            <if test="batchPO.unitAmount!=null">#{batchPO.unitAmount,jdbcType=DECIMAL},
            </if>
            <if test="batchPO.createUser!=null">#{batchPO.createUser,jdbcType=VARCHAR},
            </if>
            <if test="batchPO.areaId != null">
                #{batchPO.areaId,jdbcType=INTEGER},
            </if>
            <if test="batchPO.areaName != null">
                #{batchPO.areaName,jdbcType=VARCHAR},
            </if>
            <if test="batchPO.routeId != null">
                #{batchPO.routeId,jdbcType=INTEGER},
            </if>
            <if test="batchPO.routeName != null">
                #{batchPO.routeName,jdbcType=VARCHAR},
            </if>
            <if test="batchPO.routeSequence != null">
                #{batchPO.routeSequence,jdbcType=INTEGER},
            </if>
            <if test="batchPO.orderSelection!=null">
                #{batchPO.orderSelection,jdbcType=TINYINT},
            </if>
            <if test="batchPO.remark!=null">
                #{batchPO.remark,jdbcType=VARCHAR},
            </if>
            <if test="batchPO.batchType!=null">
                #{batchPO.batchType,jdbcType=TINYINT},
            </if>
            <if test="batchPO.batchOrderType!=null">
                #{batchPO.batchOrderType,jdbcType=TINYINT},
            </if>
            <if test="batchPO.crossWareHouse!=null">
                #{batchPO.crossWareHouse,jdbcType=TINYINT},
            </if>
            <if test="batchPO.sowType!=null">
                #{batchPO.sowType,jdbcType=TINYINT},
            </if>
            <if test="batchPO.warehouseAllocationType!= null">
                #{batchPO.warehouseAllocationType,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <delete id="deleteByBatchNo">
        DELETE from batch where BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteByBatchIds">
        DELETE from batch where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <update id="updateBatchState">
        update batch
        <trim prefix="set" suffixOverrides=",">
            <if test="state != null">
                State = #{state,jdbcType=INTEGER},
            </if>
            <if test="state != null and state == 3">
                CompleteTime = now()
            </if>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
    </update>
    <update id="updateBatchStateByNos">
        update batch set State = #{state,jdbcType=INTEGER}
        where BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
    </update>
    <update id="updateStateByTaskNo">
        update batch
        set State = #{state}
        where batchTaskNo = #{batchTaskNo}
    </update>

    <select id="listByBatchNos" resultMap="BaseResultMap">
        select
        <include refid="BaseResultSQL"></include>
        from batch
        where BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="listByBatchIds" resultMap="BaseResultMap">
        select id, BatchNo
        from batch
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="findBatchOrderLoctionList" resultMap="BaseLocationResultMap">
        select oi.outstockorder_id as outStockOrderId,IFNULL(bt.tolocation_id,oi.locationid) as locationId
        ,IFNULL(bt.tolocationName,oi.locationname) as locationName
        from outstockorderitem oi
        left JOIN batchtask bt on bt.id = oi.batchtask_id
        where oi.outstockorder_id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="listLackItemByBatchNos"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemLackDTO">
        select bti.id as batchTaskItemId, bc.batchNo,bc.id as batchId,bt.batchTaskNo,bt.id as batchTaskId,
        bti.refOrderNo,bti.RefOrder_id,bti.categoryName,bti.ProductBrand
        ,bti.ProductName,bti.SkuId,bti.LackUnitCount,bti.UnitTotalCount,bti.SaleSpec,bti.SaleSpecQuantity,bti.PackageName,bti.UnitName
        ,bt.SowTask_Id as sowTaskId, bt.SowTaskNo as sowTaskNo, bt.Org_id as cityId, bt.Warehouse_Id as warehouseId
        from batch bc
        inner JOIN batchtask bt on bt.Batch_id = bc.id
        INNER JOIN batchtaskitem bti on bti.Batchtask_id = bt.id
        where bti.IsLack = 1 and bc.BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="listLackItemByBatchTask"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemLackDTO">
        select bc.batchNo,bc.id as batchId,bt.batchTaskNo,bt.id as batchTaskId, bti.id as batchTaskItemId,
        bti.refOrderNo,bti.RefOrder_id,bti.categoryName,bti.ProductBrand
        ,bti.ProductName,bti.SkuId,bti.LackUnitCount,bti.UnitTotalCount,bti.SaleSpec,bti.SaleSpecQuantity,bti.PackageName,bti.UnitName
        ,bt.SowTask_Id as sowTaskId, bt.SowTaskNo as sowTaskNo, bt.Org_id as cityId, bt.Warehouse_Id as warehouseId
        from batch bc
        inner JOIN batchtask bt on bt.Batch_id = bc.id
        INNER JOIN batchtaskitem bti on bti.Batchtask_id = bt.id
        where bti.IsLack = 1
        and bti.OverSortCount = 0
        <if test="batchTaskId != null">
            and bti.Batchtask_id = #{batchTaskId,jdbcType=VARCHAR}
        </if>
        <if test="batchTaskNo != null">
            and bti.BatchtaskNo = #{batchTaskNo,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="listLackItemByBatchTaskItemIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemLackDTO">
        select bc.batchNo,bc.id as batchId,bt.batchTaskNo,bt.id as batchTaskId, bti.id as batchTaskItemId,
        bti.refOrderNo,bti.RefOrder_id,bti.categoryName,bti.ProductBrand
        ,bti.ProductName,bti.SkuId,bti.LackUnitCount,bti.UnitTotalCount,bti.SaleSpec,bti.SaleSpecQuantity,bti.PackageName,bti.UnitName
        ,bt.SowTask_Id as sowTaskId, bt.SowTaskNo as sowTaskNo, bt.Org_id as cityId, bt.Warehouse_Id as warehouseId
        from batch bc
        inner JOIN batchtask bt on bt.Batch_id = bc.id
        INNER JOIN batchtaskitem bti on bti.Batchtask_id = bt.id
        where bti.IsLack = 1
        and bti.TaskState = 2
        and bti.id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getIncludeSowTaskBatchTaskItemIds" resultType="java.lang.String">
        select bti.id
        from batch bc
        inner JOIN batchtask bt on bt.Batch_id = bc.id
        INNER JOIN batchtaskitem bti on bti.Batchtask_id = bt.id
        where bti.id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and bt.SowTaskNo is not null and bt.SowTaskNo != ''
    </select>

    <select id="selectBatchByBatchNo" resultMap="BaseResultMap">
        select
        <include refid="BaseResultSQL"></include>
        from batch
        <where>
            <if test="batchNo != null and batchNo != ''">
                BatchNo = #{batchNo,jdbcType=VARCHAR}
            </if>
            <if test="orgId != null">
                and Org_id = #{orgId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="selectBatchByTaskNo" resultMap="BaseResultMap">
        select
        ba.id, ba.Org_id, ba.BatchNo, ba.BatchName, ba.State, ba.OrderAmount, ba.OrderCount, ba.SkuCount,
        ba.PackageAmount, ba.UnitAmount, ba.PickingType, ba.PickingGroupStrategy,
        ba.OrderSelection, ba.Area_Id, ba.AreaName, ba.Route_Id, ba.RouteName, ba.RouteSequence, ba.CreateTime,
        ba.CreateUser, ba.Warehouse_Id
        from batch ba
        inner join batchtask bt on bt.batch_id = ba.id
        <where>
            <if test="taskNo != null and taskNo != ''">
                bt.batchtaskNo = #{taskNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="findBatchByNos" resultMap="BaseResultMap">
        select
        <include refid="BaseResultSQL"></include>
        from batch
        where Org_id = #{orgId, jdbcType=INTEGER}
        and BatchNo in
        <foreach collection="batchNos" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findBatchByOrderNos" resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO">
        select
        distinct
        b.id as id,
        b.Org_id as orgId,
        b.BatchNo as batchNo,
        b.State as state
        from outstockorder o
        inner join batch b on o.Batch_Id = b.id and o.Batch_Id is not null and o.Batch_Id != ''
        where o.Org_id = #{orgId, jdbcType=INTEGER}
        and o.Warehouse_Id = #{warehouseId, jdbcType=INTEGER}
        and o.RefOrderNo in
        <foreach collection="orderNos" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="listOrderIdByBatchNo" resultType="java.lang.String">
        select distinct bti.RefOrder_id
        from batch b
        inner join batchtask bt
        inner join batchtaskitem bti
        on b.id = bt.Batch_id and bt.id = bti.Batchtask_id
        where b.BatchNo = #{batchNo, jdbcType=VARCHAR}
    </select>

    <select id="findBatchByIds" resultMap="BaseResultMap">
        select
        <include refid="BaseResultSQL"/>
        from batch
        where id in
        <foreach collection="batchIds" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <update id="updateBatchStatistics" parameterType="java.util.List">
        UPDATE batch
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="OrderCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.orderCount != null ">
                        when id=#{item.id} then #{item.orderCount,jdbcType=INTEGER}
                    </if>
                    <if test="item.orderCount == null ">
                        when id=#{item.id} then OrderCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="SkuCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.skuCount != null ">
                        when id=#{item.id} then #{item.skuCount,jdbcType=INTEGER}
                    </if>
                    <if test="item.skuCount == null ">
                        when id=#{item.id} then SkuCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="PackageAmount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.packageAmount != null ">
                        when id=#{item.id} then #{item.packageAmount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.packageAmount == null ">
                        when id=#{item.id} then PackageAmount
                    </if>
                </foreach>
            </trim>
            <trim prefix="UnitAmount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.unitAmount != null ">
                        when id=#{item.id} then #{item.unitAmount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.unitAmount == null ">
                        when id=#{item.id} then UnitAmount
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="listWorkScheduleByGroupBuy"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.GroupBuyWorkScheduleDTO">
        select
        b.id as batchId,
        b.State as workState,
        b.Route_Id as routeId,
        b.RouteName as routeName,
        b.SkuCount as skuCount,
        b.PackageAmount as totalCount,
        (select o.id from outstockorder o where o.Org_Id = b.Org_id and o.batch_id = b.id limit 1) as orderId
        from batch b
        where b.Org_id = #{cityId,jdbcType=INTEGER}
        and b.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and b.BatchOrderType = 1
        <if test="startTime != null">
            AND b.CreateTime <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            AND b.CreateTime <![CDATA[ <= ]]> #{endTime}
        </if>
        order by b.CreateTime, b.id
    </select>
    <select id="listBatchStatusByBatchNos"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO">
        select BatchNo,State
        from batch where
        Org_id=#{orgId} and Warehouse_Id=#{warehouseId} and BatchNo in
        <foreach collection="batchNos" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        ;
    </select>

    <update id="updateSecondSort" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO">
        update batch
        <set>
            SowType = #{sowType,jdbcType=TINYINT}
            <if test="batchName != null and batchName != ''">
                ,BatchName = #{batchName,jdbcType=VARCHAR}
            </if>
            <if test="pickingType != null">
                ,PickingType = #{pickingType,jdbcType=TINYINT}
            </if>
            <if test="pickingGroupStrategy != null">
                ,PickingGroupStrategy = #{pickingGroupStrategy,jdbcType=TINYINT}
            </if>
        </set>
        where BatchNo = #{batchNo,jdbcType=VARCHAR}
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
    </update>


    <update id="updateBatchInfo" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO">
        update batch
        <set>
            <if test="orderCount != null">
                orderCount = #{orderCount,jdbcType=INTEGER},
            </if>
            <if test="packageAmount != null">
                packageAmount = #{packageAmount,jdbcType=DECIMAL},
            </if>
            <if test="unitAmount != null">
                unitAmount = #{unitAmount,jdbcType=DECIMAL},
            </if>
            <if test="skuCount != null">
                skuCount = #{skuCount,jdbcType=INTEGER},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="orderAmount != null">
                orderAmount = #{orderAmount,jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>


