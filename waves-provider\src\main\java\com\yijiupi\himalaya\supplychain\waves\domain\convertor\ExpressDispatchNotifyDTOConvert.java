package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter.ExpressDispatchNotifyDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter.OrderOutStockNotifyDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.DirectOutStockDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.LogisticsDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/01/15
 */
public class ExpressDispatchNotifyDTOConvert {
    public static List<ExpressDispatchNotifyDTO> convert(List<OutStockOrderPO> outStockOrderPOList,
                                                       List<DirectOutStockDTO> directOutStockDTOS, Map<Long, List<LogisticsDTO>> longListMap) {
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return new ArrayList<>();
        }

        List<ExpressDispatchNotifyDTO> notifyDTOList = new ArrayList<>();
        outStockOrderPOList.stream().filter(p -> StringUtils.isNumeric(p.getBusinessId())).forEach(directOutStock -> {
            ExpressDispatchNotifyDTO notifyDTO = new ExpressDispatchNotifyDTO();
            notifyDTO.setOrderId(Long.valueOf(directOutStock.getBusinessId()));
            notifyDTO.setOptUserId(String.valueOf(directOutStockDTOS.get(0).getOperateUserId()));
            notifyDTO.setOptUserName(directOutStockDTOS.get(0).getOperateUser());
            // 记录缺货
            if (directOutStock.getPackageamount().compareTo(directOutStock.getOriginalPackageAmount()) != 0
                    || directOutStock.getOriginalUnitAmount().compareTo(directOutStock.getUnitamount()) != 0) {
                notifyDTO.setDispatchType(1);
            } else {
                notifyDTO.setDispatchType(0);
            }
            // 记录快递信息
            if (longListMap != null && longListMap.size() > 0) {
                List<LogisticsDTO> logisticsDTOS = longListMap.get(directOutStock.getId());
                if (!CollectionUtils.isEmpty(logisticsDTOS)) {
                    List<ExpressDispatchNotifyDTO.OrderExpressDispatchDTO> expressLogisticsDTOList =
                            new ArrayList<>();
                    logisticsDTOS.stream().forEach(l -> {
                        ExpressDispatchNotifyDTO.OrderExpressDispatchDTO expressLogisticsDTO =
                                new ExpressDispatchNotifyDTO.OrderExpressDispatchDTO();
                        expressLogisticsDTO.setTrackNumber(l.getNo());
                        expressLogisticsDTO.setLogisticsId(l.getCompanyCode());
                        expressLogisticsDTO.setLogisticName(l.getCompanyName());
                        expressLogisticsDTO.setOrderId(notifyDTO.getOrderId());
                        expressLogisticsDTO.setOptUserId(notifyDTO.getOptUserId());
                        expressLogisticsDTO.setOptUserName(notifyDTO.getOptUserName());
                        expressLogisticsDTOList.add(expressLogisticsDTO);
                    });
                    notifyDTO.setOrderExpressDispatchDTOList(expressLogisticsDTOList);
                }
            }
            // 记录出库明细
            List<OrderOutStockNotifyDTO.OrderItemOutStockDTO> orderItemOutStockList = new ArrayList<>();
            List<OutStockOrderItemPO> outStockOrderItemPS = directOutStock.getItems();
            for (OutStockOrderItemPO outStockOrderItemPO : outStockOrderItemPS) {
                OrderOutStockNotifyDTO.OrderItemOutStockDTO orderItemNotifyDTO =
                        new OrderOutStockNotifyDTO.OrderItemOutStockDTO();
                orderItemNotifyDTO.setOrderItemId(Long.valueOf(outStockOrderItemPO.getBusinessItemId()));
                orderItemNotifyDTO.setUnitTotalCount(outStockOrderItemPO.getUnittotalcount());
                List<OrderOutStockNotifyDTO.OrderOutStockDealerDTO> orderOutStockDealerList = new ArrayList<>();
                List<OutStockOrderItemDetailPO> outStockOrderItemDetailPOS = outStockOrderItemPO.getItemDetails();
                if (!CollectionUtils.isEmpty(outStockOrderItemDetailPOS)) {
                    for (OutStockOrderItemDetailPO outStockOrderItemDetailPO : outStockOrderItemDetailPOS) {
                        OrderOutStockNotifyDTO.OrderOutStockDealerDTO dealerNotifyDTO =
                                new OrderOutStockNotifyDTO.OrderOutStockDealerDTO();
                        dealerNotifyDTO.setSecOwnerId(outStockOrderItemDetailPO.getSecOwnerId());
                        dealerNotifyDTO.setUnitTotalCount(outStockOrderItemDetailPO.getUnitTotalCount());
                        dealerNotifyDTO.setOwnerId(outStockOrderItemDetailPO.getOwnerId());
                        dealerNotifyDTO
                                .setProductSpecificationId(outStockOrderItemDetailPO.getProductSpecificationId());
                        orderOutStockDealerList.add(dealerNotifyDTO);
                    }
                }
                orderItemNotifyDTO.setOrderOutStockDealerList(orderOutStockDealerList);
                orderItemOutStockList.add(orderItemNotifyDTO);
            }
            notifyDTO.setOrderItemOutStockList(orderItemOutStockList);

            notifyDTOList.add(notifyDTO);
        });

        return notifyDTOList;
    }
}
