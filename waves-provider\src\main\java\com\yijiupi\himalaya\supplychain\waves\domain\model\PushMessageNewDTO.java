package com.yijiupi.himalaya.supplychain.waves.domain.model;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.dubbop.constant.PushType;

/**
 * Created by 余明 on 2018-08-07.
 */
public class PushMessageNewDTO implements Serializable {
    private String content;
    private Map<String, String> extras;
    private Integer pushAppTypeValue;
    private PushType pushType;
    private List<String> tagOrIdList;
    private Boolean contentAvailable = false;
    private String title;
    /**
     * 0：往账号的最新的 device 上推送信息
     * 1：往账号关联的所有 device 设备上推送信息
     */
    private Integer accountPushType = 0;


    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Map<String, String> getExtras() {
        return extras;
    }

    public void setExtras(Map<String, String> extras) {
        this.extras = extras;
    }

    public Integer getPushAppTypeValue() {
        return pushAppTypeValue;
    }

    public void setPushAppTypeValue(Integer pushAppTypeValue) {
        this.pushAppTypeValue = pushAppTypeValue;
    }

    public PushType getPushType() {
        return pushType;
    }

    public void setPushType(PushType pushType) {
        this.pushType = pushType;
    }

    public List<String> getTagOrIdList() {
        return tagOrIdList;
    }

    public void setTagOrIdList(List<String> tagOrIdList) {
        this.tagOrIdList = tagOrIdList;
    }

    public Boolean getContentAvailable() {
        return contentAvailable;
    }

    public void setContentAvailable(Boolean contentAvailable) {
        this.contentAvailable = contentAvailable;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getAccountPushType() {
        return accountPushType;
    }

    public void setAccountPushType(Integer accountPushType) {
        this.accountPushType = accountPushType;
    }
}
