package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.sowtask;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.GoodsCollectionLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.enums.SowLocationAllocationTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SowTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskByOutStockOrderResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.SowConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.enums.OperationModeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;
import org.springframework.util.CollectionUtils;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/18
 */
@Service
public class CreateSowTaskByOutStockOrderBL {

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private SowTaskMapper sowTaskMapper;
    @Autowired
    private GlobalCache globalCache;

    private static final Logger LOG = LoggerFactory.getLogger(CreateSowTaskByOutStockOrderBL.class);

    /**
     * 根据一个出库批次拆分后出库单list，创建一个播种任务
     */
    public CreateSowTaskByOutStockOrderResultBO createSowTaskByOutStockOrder(CreateSowTaskBO bo) {
        WavesStrategyBO wavesStrategyDTO = bo.getWavesStrategyDTO();
        PassageDTO passageDTO = bo.getPassageDTO();
        String title = bo.getTitle();
        String operateUser = bo.getOperateUser();
        Integer cityId = bo.getCityId();
        WarehouseConfigDTO warehouseConfigDTO = bo.getWarehouseConfigDTO();
        BatchBO batchBO = bo.getBatchBO();
        List<GoodsCollectionLocationBO> lstLocations = bo.getLstLocations();
        List<SowTaskPO> lstSowTaskPO = bo.getLstSowTaskPO();
        String locationName = bo.getLocationName();
        List<SowOrderPO> lstSowOrders = bo.getLstSowOrders();
        String driverName = bo.getDriverName();
        Boolean allocationFlag = bo.getAllocationFlag();
        boolean needRecheck = bo.isNeedRecheck();
        List<OutStockOrderPO> splitOrderList = bo.getSplitOrderList();
        WaveCreateDTO oriCreateDTO = bo.getOriCreateDTO();

        Byte sowType = passageDTO.getSowType();
        BatchPO batchPO = batchBO.getBatchPO();

        // 2、拆分过的订单，属于同一个播种任务
        Integer sowNum = batchBO.getSowTaskNum() + 1;
        batchBO.setSowTaskNum(sowNum);

        String sowNo = String.format("%s-%s", batchPO.getBatchNo(), sowNum);
        Long sowId = UuidUtil.getUUidInt();

        GoodsCollectionLocationBO toLocation = null;

        // 3、获取待保存的播种任务列表(开启分区分单的不设置集货位)
        Byte sowLocationAllocationType = warehouseConfigDTO.getSowLocationAllocationType();
        if ((sowType == SowTypeEnum.播种墙播种.getType() || sowType == SowTypeEnum.分区分单不播种.getType()
                || sowType == SowTypeEnum.二次分拣.getType())
                && !Objects.equals(sowLocationAllocationType, SowLocationAllocationTypeEnum.开始拣货时分配.getType())) {
            toLocation = allocationLocation(lstLocations, wavesStrategyDTO, true);
        }

        SowTaskPO sowTaskPO = null;
        // 虚仓二次分拣时的特殊处理
        if (Objects.equals(sowType, SowTypeEnum.虚仓二次分拣播种.getType())) {
            sowTaskPO = SowConverter.getVirtualSecondPickSowTaskPO(sowId, sowNo, splitOrderList, batchPO);
        } else {
            sowTaskPO = SowConverter.getSowTaskPO(sowNum, sowId, sowNo, splitOrderList, batchPO, toLocation);
        }

        if (sowType == SowTypeEnum.分区分单不播种.getType()) {
            // sowTaskPO.setState(SowTaskStateEnum.待集货.getType());
            sowTaskPO.setState(SowTaskStateEnum.已播种.getType());
        } else {
            sowTaskPO.setState(SowTaskStateEnum.待播种.getType());
        }

        if (sowType == SowTypeEnum.播种墙播种.getType()) {
            sowTaskPO.setSowTaskType(SowTaskTypeEnum.播种墙播种.getType());
        } else if (sowType == SowTypeEnum.分区分单播种.getType()) {
            sowTaskPO.setSowTaskType(SowTaskTypeEnum.打包复核.getType());
        } else if (sowType == SowTypeEnum.二次分拣.getType()) {
            sowTaskPO.setSowTaskType(SowTaskTypeEnum.二次分拣播种.getType());
        }
        /** 如果是抽核生成的复核任务，则类型为打包复核 */
        if (needRecheck) {
            sowTaskPO.setSowTaskType(SowTaskTypeEnum.打包复核.getType());
        }

        if (sowType != SowTypeEnum.分区分单拣货.getType()) {
            lstSowTaskPO.add(sowTaskPO);
        }

        List<String> orderIdList =
                splitOrderList.stream().map(OutStockOrderPO::getId).map(String::valueOf).collect(Collectors.toList());
        List<OutStockOrderPO> list =
                outStockOrderMapper.findByOrderId(orderIdList.stream().map(Long::valueOf).collect(Collectors.toList()));

        // 封装播种任务与订单关联信息
        List<SowOrderPO> sowOrderPOS = new ArrayList<>();

        if (wavesStrategyDTO.getIsOpenSecondSort()
                && Objects.equals(passageDTO.getSowType(), SowTypeEnum.二次分拣.getType())) {
            sowOrderPOS.addAll(SowConverter.getSecondSowOrderPOS(sowTaskPO, splitOrderList, warehouseConfigDTO, list));
        } else {
            sowOrderPOS.addAll(SowConverter.getSowOrderPOS(sowTaskPO, splitOrderList, warehouseConfigDTO));
        }
        if (sowType != SowTypeEnum.分区分单拣货.getType()) {
            lstSowOrders.addAll(sowOrderPOS);
        }

        WaveCreateDTO createDTO =
                SplitWaveOrderUtil.getWaveCreateDTO(splitOrderList, wavesStrategyDTO, passageDTO.getPickingType(),
                        passageDTO.getPickingGroupStrategy(), title, operateUser, cityId, locationName, driverName,
                        passageDTO.getSowType(), passageDTO.getPassageType(), allocationFlag, oriCreateDTO.getOperateUserId());
        createDTO.setPassageDTO(passageDTO);
        if (sowType != SowTypeEnum.分区分单拣货.getType()) {
            createDTO.setSowId(sowId);
            createDTO.setSowNo(sowNo);
        }
        if (Objects.equals(sowTaskPO.getOperationMode(), OperationModeEnum.不拣货播种.getType())) {
            createDTO.setPickFlag(false);
        }

        List<SowTaskPO> sowTaskPOList = new ArrayList<>();
        sowTaskPOList.add(sowTaskPO);
        CreateSowTaskByOutStockOrderResultBO resultBO = new CreateSowTaskByOutStockOrderResultBO();
        if (sowType != SowTypeEnum.分区分单拣货.getType()) {
            resultBO.setSowOrdersList(sowOrderPOS);
        }
        if (sowType != SowTypeEnum.分区分单拣货.getType()) {
            resultBO.setSowTaskPOList(sowTaskPOList);
        }

        resultBO.setWaveCreateDTO(createDTO);

        // 重置
        boolean isPickByCustomer = globalCache.getPickByCustomerFromCache(wavesStrategyDTO.getWarehouseId());
        if (isPickByCustomer) {
            SowConverter.resetSowOrderSequenceIfPickByCustomer(list, sowOrderPOS);
        }

        addSowTaskList(sowTaskPO, toLocation);

        return resultBO;
    }

    // 限制电子标签拣货时，每个播种台(集货位)只有两个播种任务
    private void addSowTaskList(SowTaskPO sowTaskPO, GoodsCollectionLocationBO toLocation) {
        if (Objects.isNull(toLocation)) {
            return;
        }

        toLocation.getSowTaskPOList().add(sowTaskPO);
    }

    /**
     * 查找播种任务要存放的集货位 needAccumulate:是否需要累加
     *
     * @return
     */
    public GoodsCollectionLocationBO allocationLocation(List<GoodsCollectionLocationBO> locationList,
                                                        WavesStrategyBO wavesStrategyDTO, boolean needAccumulate) {
        GoodsCollectionLocationBO toLocation = new GoodsCollectionLocationBO();
        List<GoodsCollectionLocationBO> locations = new ArrayList<>();

        // 去掉货位内都占满的货区
        if (org.springframework.util.CollectionUtils.isEmpty(locationList)) {
            return toLocation;
        }

        locationList = locationList.stream()
                .filter(location -> location.getArea_Id() != null && location.getLocationCapacity() != null)
                .collect(Collectors.toList());
        if (org.springframework.util.CollectionUtils.isEmpty(locationList)) {
            return toLocation;
        }
        locationList.stream().collect(Collectors.groupingBy(GoodsCollectionLocationBO::getArea_Id))
                .forEach((areaId, locationDTOS) -> {
                    List<GoodsCollectionLocationBO> redundancyLocations = locationDTOS.stream()
                            .filter(location -> location.getLocationCapacity() == 0).collect(Collectors.toList());
                    if (!org.springframework.util.CollectionUtils.isEmpty(redundancyLocations)) {
                        locations.addAll(locationDTOS);
                    }
                });

        // 可以累加的，货区货位都满了的情况下则全部进入循环重新计算
        if (org.springframework.util.CollectionUtils.isEmpty(locations) && needAccumulate) {
            locations.addAll(locationList);
        } else if (org.springframework.util.CollectionUtils.isEmpty(locations) && !needAccumulate) {
            return toLocation;
        }
        // 查询空闲的集货位
        List<GoodsCollectionLocationBO> availableLocationList =
                filterAvailableCollectionLocation(wavesStrategyDTO, locations);

        // 开启了电子标签的，校验集货位
        validateCollectLocationIsAvailable(wavesStrategyDTO, availableLocationList);

        // 默认分配一个最小的
        Optional<GoodsCollectionLocationBO> optionalLocationReturnDTO =
                availableLocationList.stream().min(Comparator.comparing(GoodsCollectionLocationBO::getLocationCapacity)
                        .thenComparing(GoodsCollectionLocationBO::getSequence));
        if (optionalLocationReturnDTO.isPresent()) {
            toLocation = optionalLocationReturnDTO.get();
        }
        Long defAreaId = toLocation.getArea_Id();

        // 获取最小货区总容量
        int areaOrderCount =
                availableLocationList.stream().filter(location -> Objects.equals(defAreaId, location.getArea_Id()))
                        .mapToInt(GoodsCollectionLocationBO::getLocationCapacity).sum();
        // 按货区平均分配
        Map<Long, List<GoodsCollectionLocationBO>> locationMap =
                availableLocationList.stream().collect(Collectors.groupingBy(GoodsCollectionLocationBO::getArea_Id));
        for (Map.Entry<Long, List<GoodsCollectionLocationBO>> entry : locationMap.entrySet()) {
            List<GoodsCollectionLocationBO> areaLocations = entry.getValue();
            // 获取当前货区总容量
            int areaCapacity = areaLocations.stream().mapToInt(GoodsCollectionLocationBO::getLocationCapacity).sum();
            areaLocations.sort(Comparator.comparing(GoodsCollectionLocationBO::getLocationCapacity)
                    .thenComparing(GoodsCollectionLocationBO::getSequence));
            GoodsCollectionLocationBO location = areaLocations.get(0);

            if (areaOrderCount > areaCapacity) {
                areaOrderCount = areaCapacity;
                toLocation = location;
            } else if (areaOrderCount == areaCapacity) {
                if (toLocation.getLocationCapacity() > location.getLocationCapacity()) {
                    toLocation = location;
                } else if (toLocation.getLocationCapacity().equals(location.getLocationCapacity())
                        && toLocation.getSequence() >= location.getSequence()) {
                    toLocation = location;
                }
            }
            LOG.info("货区平均分配货位:{}", JSON.toJSONString(toLocation));
        }
        toLocation.setLocationCapacity(toLocation.getLocationCapacity() + 1);
        return toLocation;
    }

    /**
     * 查找播种任务要存放的集货位 needAccumulate:是否需要累加
     *
     * @return
     */
    @Deprecated
    public LocationReturnDTO allocationLocationOld(List<LocationReturnDTO> locationList, boolean needAccumulate) {
        LocationReturnDTO toLocation = new LocationReturnDTO();
        List<LocationReturnDTO> locations = new ArrayList<>();

        // 去掉货位内都占满的货区
        if (org.springframework.util.CollectionUtils.isEmpty(locationList)) {
            return toLocation;
        } else {
            locationList = locationList.stream()
                    .filter(location -> location.getArea_Id() != null && location.getLocationCapacity() != null)
                    .collect(Collectors.toList());
            if (org.springframework.util.CollectionUtils.isEmpty(locationList)) {
                return toLocation;
            }
            locationList.stream().collect(Collectors.groupingBy(LocationReturnDTO::getArea_Id))
                    .forEach((areaId, locationDTOS) -> {
                        List<LocationReturnDTO> redundancyLocations = locationDTOS.stream()
                                .filter(location -> location.getLocationCapacity() == 0).collect(Collectors.toList());
                        if (!org.springframework.util.CollectionUtils.isEmpty(redundancyLocations)) {
                            locations.addAll(locationDTOS);
                        }
                    });
        }

        // 可以累加的，货区货位都满了的情况下则全部进入循环重新计算
        if (org.springframework.util.CollectionUtils.isEmpty(locations) && needAccumulate) {
            locations.addAll(locationList);
        } else if (org.springframework.util.CollectionUtils.isEmpty(locations) && !needAccumulate) {
            return toLocation;
        }

        // 默认分配一个最小的
        Optional<LocationReturnDTO> optionalLocationReturnDTO = locations.stream().min(
                Comparator.comparing(LocationReturnDTO::getLocationCapacity).thenComparing(LocationReturnDTO::getSequence));
        if (optionalLocationReturnDTO.isPresent()) {
            toLocation = optionalLocationReturnDTO.get();
        }
        Long defAreaId = toLocation.getArea_Id();

        // 获取最小货区总容量
        int areaOrderCount = locations.stream().filter(location -> Objects.equals(defAreaId, location.getArea_Id()))
                .mapToInt(LocationReturnDTO::getLocationCapacity).sum();
        // 按货区平均分配
        Map<Long, List<LocationReturnDTO>> locationMap =
                locations.stream().collect(Collectors.groupingBy(LocationReturnDTO::getArea_Id));
        for (Map.Entry<Long, List<LocationReturnDTO>> entry : locationMap.entrySet()) {
            List<LocationReturnDTO> areaLocations = entry.getValue();
            // 获取当前货区总容量
            int areaCapacity = areaLocations.stream().mapToInt(LocationReturnDTO::getLocationCapacity).sum();
            areaLocations.sort(Comparator.comparing(LocationReturnDTO::getLocationCapacity)
                    .thenComparing(LocationReturnDTO::getSequence));
            LocationReturnDTO location = areaLocations.get(0);

            if (areaOrderCount > areaCapacity) {
                areaOrderCount = areaCapacity;
                toLocation = location;
            } else if (areaOrderCount == areaCapacity) {
                if (toLocation.getLocationCapacity() > location.getLocationCapacity()) {
                    toLocation = location;
                } else if (toLocation.getLocationCapacity().equals(location.getLocationCapacity())
                        && toLocation.getSequence() >= location.getSequence()) {
                    toLocation = location;
                }
            }
            // LOG.info("货区平均分配货位:{}", JSON.toJSONString(toLocation));
        }
        toLocation.setLocationCapacity(toLocation.getLocationCapacity() + 1);
        return toLocation;
    }

    public List<GoodsCollectionLocationBO> filterAvailableCollectionLocation(WavesStrategyBO wavesStrategyDTO,
                                                                             List<GoodsCollectionLocationBO> boList) {
        try {
            if (BooleanUtils.isFalse(globalCache.openDigitalTag(wavesStrategyDTO.getWarehouseId()))) {
                return boList;
            }
            if (CollectionUtils.isEmpty(boList)) {
                return Collections.emptyList();
            }

            return filterLocationList(boList, wavesStrategyDTO.getWarehouseId());
        } catch (Exception e) {
            LOG.warn("选取集货位错误，错误信息:", e);
        }

        return boList;
    }

    private List<GoodsCollectionLocationBO> filterLocationList(List<GoodsCollectionLocationBO> boList,
                                                               Integer warehouseId) {
        if (!globalCache.isOpenDigitalTagBatchTaskOptimizeSort(warehouseId)) {
            return boList.stream().filter(this::isLocationAvailable).collect(Collectors.toList());
        }

        List<GoodsCollectionLocationBO> freeLocationList =
                boList.stream().filter(this::isLocationFree).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(freeLocationList)) {
            return freeLocationList;
        }

        return boList.stream().filter(this::isLocationAvailable).collect(Collectors.toList());
    }

    private boolean isLocationFree(GoodsCollectionLocationBO bo) {
        if (CollectionUtils.isEmpty(bo.getSowTaskPOList())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    // 一个播种为只生成两个播种任务，如果只有一个且是待播种，则不允许生成
    private boolean isLocationAvailable(GoodsCollectionLocationBO bo) {
        if (CollectionUtils.isEmpty(bo.getSowTaskPOList())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    private void validateCollectLocationIsAvailable(WavesStrategyBO wavesStrategyDTO,
                                                    List<GoodsCollectionLocationBO> boList) {
        if (BooleanUtils.isFalse(globalCache.openDigitalTag(wavesStrategyDTO.getWarehouseId()))) {
            return;
        }

        List<GoodsCollectionLocationBO> availableList = filterAvailableCollectionLocation(wavesStrategyDTO, boList);
        if (CollectionUtils.isEmpty(availableList)) {
            throw new BusinessValidateException("暂无空余集货位和播种台，请等待已有播种任务完成后再生成新任务！");
        }
    }

}
