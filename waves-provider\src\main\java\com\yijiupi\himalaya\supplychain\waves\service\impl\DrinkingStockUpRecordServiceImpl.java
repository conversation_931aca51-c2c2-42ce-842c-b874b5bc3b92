package com.yijiupi.himalaya.supplychain.waves.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.batch.IDrinkingStockUpRecordService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.stockup.DrinkingStockUpRecordBL;
import com.yijiupi.himalaya.supplychain.waves.dto.stockup.StockUpCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.stockup.StockUpRecordDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.stockup.StockUpRecordQueryParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-10-11 16:22
 **/
@Service(timeout = 30000)
public class DrinkingStockUpRecordServiceImpl implements IDrinkingStockUpRecordService {

    @Resource
    private DrinkingStockUpRecordBL drinkingStockUpRecordBL;

    private static final Logger logger = LoggerFactory.getLogger(DrinkingStockUpRecordServiceImpl.class);

    /**
     * 完成备货
     *
     * @param param 完成参数
     */
    @Override
    public void completeStockUp(StockUpCompleteDTO param) {
        AssertUtils.notEmpty(param.getBatchTaskItemIds(), "拣货任务不能为空");
        AssertUtils.notNull(param.getUserId(), "操作人不能为空");
        AssertUtils.notNull(param.getRecords(), "备货记录不能为空");
        logger.info("完成备货: {}", JSON.toJSONString(param, SerializerFeature.WriteMapNullValue));
        drinkingStockUpRecordBL.completeStockUp(param);
    }

    /**
     * 新增或更新备货记录
     *
     * @param stockUpRecord 备货记录
     */
    @Override
    public void addOrUpdateStockUpRecord(StockUpRecordDTO stockUpRecord) {
        AssertUtils.notNull(stockUpRecord.getWarehouseId(), "仓库 id 不能为空");
        AssertUtils.notNull(stockUpRecord.getOrgId(), "城市 id 不能为空");
        AssertUtils.hasText(stockUpRecord.getTaskNo(), "车次编号 不能为空");
        AssertUtils.notEmpty(stockUpRecord.getItems(), "备货明细不能为空");
        drinkingStockUpRecordBL.addOrUpdateStockUpRecord(stockUpRecord, "系统");
    }

    /**
     * 查询备货记录
     *
     * @param param 备货记录查询参数
     * @return 查询结果
     */
    @Override
    public PageList<StockUpRecordDTO> pageListStockUpRecord(StockUpRecordQueryParam param) {
        AssertUtils.notNull(param.getWarehouseId(), "仓库 id 不能为空");
        AssertUtils.notNull(param.getOrgId(), "城市 id 不能为空");
        return drinkingStockUpRecordBL.pageListStockUpRecord(param);
    }
}
