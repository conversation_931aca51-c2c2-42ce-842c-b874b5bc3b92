package com.yijiupi.himalaya.supplychain.waves.batch;

import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemLackDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSorterDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;

/**
 * 波次
 *
 * <AUTHOR> 2018/3/16
 */
public interface IBatchQueryService {

    /**
     * 查询波次列表
     *
     * @param batchQueryDTO
     * @return
     */
    PageList<BatchDTO> findBatchOrderList(BatchQueryDTO batchQueryDTO);

    /**
     * 根据订单号，查询订单存放的周转区货位
     *
     * @param orderIds
     * @return
     */
    List<OutStockOrderLocationDTO> findBatchOrderLoctionList(List<Long> orderIds);

    /**
     * 根据波次编号，查找所有标记缺货的项
     *
     * @param batchNos
     * @return
     */
    List<BatchTaskItemLackDTO> listLackItemByBatchNos(List<String> batchNos);

    /**
     * 根据仓库id查询波次打包列表
     */
    List<BatchOrderDTO> listBatchOrderByWarehouse(Integer orgId, Integer warehouseId);

    /**
     * 查询波次打包列表
     * 
     * @param queryParam
     * @return
     */
    List<BatchOrderDTO> listBatchOrder(BatchOrderQueryParam queryParam);

    /**
     * 获取仓库的波次作业设置
     * 
     * @return
     */
    BatchWorkSettingDTO getBatchWorkSetting(Integer warehouseId);

    /**
     * 根据单号list、仓库查分拣员
     * 
     * @return
     */
    Map<String, List<BatchTaskSorterDTO>> getSorterByOrderNos(List<String> orderNos, Integer warehouseId);

    /**
     * 根据波次编号 城市id,仓库id查询波次状态
     * 
     * @param batchNos
     * @param orgId
     * @param warehouseId
     * @return
     */
    List<BatchOrderDTO> listBatchStatusByBatchNos(List<String> batchNos, Integer orgId, Integer warehouseId);

    /**
     * 商家Saas
     * 
     * @param batchQueryDTO
     * @return
     */
    PageList<BatchDTO> listSaaSBatchOrderList(BatchQueryDTO batchQueryDTO);

    List<BatchDTO> findByBatchNos(List<String> batchNos, Integer orgId);
}
