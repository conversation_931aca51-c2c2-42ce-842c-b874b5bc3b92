package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.PackageOrderItemBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskFinishHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishNeedOpBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageCodeSortDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.PackageReviewStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.PackageTypeEnum;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. 打包
 * 
 * <AUTHOR>
 * @date 2024/1/12
 */
@Service
public class BatchTaskItemFinishPackageOrderDecoratorBL extends BatchTaskItemFinishDecoratorBL {

    @Override
    public int getOrder() {
        return MIDDLE_PRECEDENCE + 50;
    }

    @Override
    protected void doCompleteBatchTaskItem(BatchTaskItemFinishNeedOpBO bo,
        BatchTaskFinishHelperBO batchTaskFinishHelperBO, List<BatchTaskItemDTO> needCompleteBatchTaskItemList) {
        BatchTaskItemFinishBO batchTaskItemFinishBO = batchTaskFinishHelperBO.getBatchTaskItemFinishBO();
        String userName = batchTaskItemFinishBO.getUserName();
        Integer warehouseId = batchTaskItemFinishBO.getWarehouseId();
        Integer cityId = batchTaskItemFinishBO.getCityId();
        Integer userId = batchTaskItemFinishBO.getUserId();
        List<OrderItemTaskInfoPO> needPackageOrderItemTaskInfo =
            batchTaskFinishHelperBO.getNeedPackageOrderItemTaskInfo();

        Map<String, BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOMap =
            batchTaskFinishHelperBO.getBatchTaskItemCompleteDTOMap();

        List<PackageOrderItemDTO> packageOrderItemDTOList = new ArrayList<>();
        for (BatchTaskItemDTO batchTaskItemDTO : needCompleteBatchTaskItemList) {
            BatchTaskItemCompleteDTO dto = batchTaskItemCompleteDTOMap.get(batchTaskItemDTO.getId());
            // 拣货装箱
            List<PackageOrderItemDTO> packageOrderItemDTOS = pickingAndPacking(warehouseId, cityId, userId, userName,
                needPackageOrderItemTaskInfo, batchTaskItemDTO, dto.getBoxCodeList());
            if (CollectionUtils.isNotEmpty(packageOrderItemDTOS)) {
                packageOrderItemDTOList.addAll(packageOrderItemDTOS);
            }
        }

        bo.setPackageOrderItemDTOS(packageOrderItemDTOList);
    }

    /**
     * 拣货装箱
     *
     * @see PackageOrderItemBL#pickingAndPacking
     */
    public List<PackageOrderItemDTO> pickingAndPacking(Integer warehouseId, Integer cityId, Integer userId,
        String userName, List<OrderItemTaskInfoPO> orderItemTaskInfoPOS, BatchTaskItemDTO batchTaskItemDTO,
        List<PackageCodeSortDTO> packageCodeSortDTOS) {
        if (CollectionUtils.isEmpty(packageCodeSortDTOS)) {
            return Collections.emptyList();
        }

        List<PackageOrderItemDTO> packageOrderItemDTOS = new ArrayList<>();
        // LOGGER.info("拣货装箱参数orderItemTaskInfoPOS:{},batchTaskItemDTO:{},packageCodeSortDTOS:{}",
        // JSON.toJSONString(orderItemTaskInfoPOS), JSON.toJSONString(batchTaskItemDTO),
        // JSON.toJSON(packageCodeSortDTOS));
        for (PackageCodeSortDTO boxCodeDTO : packageCodeSortDTOS) {
            orderItemTaskInfoPOS.stream()
                .filter(item -> item.getBatchTaskItemId().equals(batchTaskItemDTO.getId())
                    && item.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId))
                .forEach((orderItemId, items) -> {
                    PackageOrderItemDTO packageOrderItemDTO = new PackageOrderItemDTO();
                    packageOrderItemDTO.setPassageCode(boxCodeDTO.getPassageCode());
                    packageOrderItemDTO.setOrgId(cityId);
                    packageOrderItemDTO.setWarehouseId(warehouseId);
                    packageOrderItemDTO.setRefOrderId(items.get(0).getRefOrderId());
                    packageOrderItemDTO.setRefOrderItemId(orderItemId);
                    packageOrderItemDTO.setBoxCode(boxCodeDTO.getBoxCode());
                    packageOrderItemDTO.setProductName(batchTaskItemDTO.getProductName());
                    packageOrderItemDTO.setSkuId(batchTaskItemDTO.getSkuId());
                    packageOrderItemDTO.setSpecName(batchTaskItemDTO.getSpecName());
                    packageOrderItemDTO.setSpecQuantity(batchTaskItemDTO.getSpecQuantity());
                    packageOrderItemDTO.setPackageName(batchTaskItemDTO.getPackageName());
                    if (boxCodeDTO.getPackageCount().compareTo(BigDecimal.ZERO) == 0
                        && boxCodeDTO.getUnitCount().compareTo(BigDecimal.ZERO) == 0) {
                        packageOrderItemDTO.setUnitTotalCount(BigDecimal.ZERO);
                        packageOrderItemDTO.setPackageCount(BigDecimal.ZERO);
                        packageOrderItemDTO.setUnitCount(BigDecimal.ZERO);
                    } else {
                        BigDecimal unitTotalCount = items.stream().map(OrderItemTaskInfoPO::getOverSortCount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        packageOrderItemDTO.setUnitTotalCount(unitTotalCount);
                        BigDecimal[] divideAndRemainder =
                            unitTotalCount.divideAndRemainder(packageOrderItemDTO.getSpecQuantity());
                        packageOrderItemDTO.setPackageCount(divideAndRemainder[0]);
                        packageOrderItemDTO.setUnitCount(divideAndRemainder[1]);
                    }
                    packageOrderItemDTO.setUnitName(batchTaskItemDTO.getUnitName());
                    packageOrderItemDTO.setRemark(null);
                    packageOrderItemDTO.setCreateUser(String.valueOf(userId));
                    packageOrderItemDTO.setPackageType(boxCodeDTO.getPackageType());
                    packageOrderItemDTO.setOperatorId(userId);
                    packageOrderItemDTO.setOperatorName(userName);
                    if (boxCodeDTO.getPackageType() != null
                        && boxCodeDTO.getPackageType() == PackageTypeEnum.托盘.getType()) {
                        packageOrderItemDTO.setReviewState(PackageReviewStateEnum.待复核.getType());
                    } else {
                        packageOrderItemDTO.setReviewState(PackageReviewStateEnum.已复核.getType());
                    }

                    Optional<PackageOrderItemDTO> first =
                        packageOrderItemDTOS.stream().filter(item -> item.getRefOrderItemId().equals(orderItemId)
                            && item.getBoxCode().equals(boxCodeDTO.getBoxCode())).findFirst();
                    if (first.isPresent()) {
                        PackageOrderItemDTO packageOrderItem = first.get();
                        packageOrderItem.setPackageCount(
                            packageOrderItem.getPackageCount().add(packageOrderItemDTO.getPackageCount()));
                        packageOrderItem
                            .setUnitCount(packageOrderItem.getUnitCount().add(packageOrderItemDTO.getUnitCount()));
                        packageOrderItem.setUnitTotalCount(
                            packageOrderItem.getUnitTotalCount().add(packageOrderItemDTO.getUnitTotalCount()));
                    } else {
                        packageOrderItemDTOS.add(packageOrderItemDTO);
                    }
                });
        }
        // LOGGER.info("拣货装箱数据:{}", JSON.toJSONString(packageOrderItemDTOS));
        return packageOrderItemDTOS;
    }

}
