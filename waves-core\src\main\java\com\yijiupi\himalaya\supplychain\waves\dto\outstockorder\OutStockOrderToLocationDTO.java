package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;

/**
 * 订单出库位
 *
 * <AUTHOR>
 * @date 2019-12-03 14:31
 */
public class OutStockOrderToLocationDTO implements Serializable {

    private static final long serialVersionUID = 7369130221982007261L;

    /**
     * 货位Id
     */
    private Long locationId;

    /**
     * 货位或货区名称
     */
    private String locationName;

    /**
     * 仓库名称
     */
    private String warehouseName;

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
