package com.yijiupi.himalaya.supplychain.waves.domain.po;

import com.yijiupi.himalaya.supplychain.waves.enums.BatchAttrSettingWayConstants;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 2018/3/15
 */
public class BatchPO implements Serializable {
    /**
     * id
     */
    private String id;
    /**
     * 城市id
     */
    private Integer orgId;
    /**
     * 波次编号
     */
    private String batchNo;
    /**
     * 波次名称
     */
    private String batchName;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 波次状态 待调度(0),待拣货(1),拣货中(2),已拣货(3),已出库(4),已取消(5),已作废(6)
     */
    private Byte state;
    /**
     * 拣货方式 1 按订单拣货 2 按产品拣货
     */
    private Byte pickingType;
    /**
     * 拣货分组策略 1货区 2货位 3类目
     */
    private Byte pickingGroupStrategy;
    /**
     * 关联的订单应付总金额
     */
    private BigDecimal orderAmount;
    /**
     * 订单数量
     */
    private Integer orderCount;
    /**
     * 商品种类
     */
    private Integer skuCount;
    /**
     * 大件总数
     */
    private BigDecimal packageAmount;
    /**
     * 小件总数
     */
    private BigDecimal unitAmount;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 区域id
     */
    private String areaId;
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 线路id
     */
    private String routeId;
    /**
     * 线路名称
     */
    private String routeName;
    /**
     * 排序号
     */
    private Integer routeSequence;
    /**
     * 订单删选 1 按区域 2 按线路
     */
    private Byte orderSelection;

    /**
     * 最后更新人
     */
    private String lastUpdateUser;

    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 波次类别 0：酒批(默认) 1：微酒
     */
    private Byte batchType;

    /**
     * 波次订单类型 0: 普通订单(默认) 1: 团购订单
     */
    private Byte batchOrderType;

    /**
     * 是否跨库：0:否 1:是
     */
    private Byte crossWareHouse;
    /**
     * 播种类型 1-总单播种 2-二次分拣 3-快速播种
     */
    private Byte sowType;

    /**
     * 操作人
     */
    private Integer createUserId;

    /**
     * 波次属性设置方式：0：无；1、线路；2、片区
     *
     * @see BatchAttrSettingWayConstants
     */
    private Byte batchAttrSettingWay;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;


    public Byte getSowType() {
        return sowType;
    }

    public void setSowType(Byte sowType) {
        this.sowType = sowType;
    }

    public Byte getCrossWareHouse() {
        return crossWareHouse;
    }

    public void setCrossWareHouse(Byte crossWareHouse) {
        this.crossWareHouse = crossWareHouse;
    }

    public Byte getBatchOrderType() {
        return batchOrderType;
    }

    public void setBatchOrderType(Byte batchOrderType) {
        this.batchOrderType = batchOrderType;
    }

    public Byte getBatchType() {
        return batchType;
    }

    public void setBatchType(Byte batchType) {
        this.batchType = batchType;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public BigDecimal getPackageAmount() {
        return packageAmount;
    }

    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    public BigDecimal getUnitAmount() {
        return unitAmount;
    }

    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Byte getPickingType() {
        return pickingType;
    }

    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Byte getPickingGroupStrategy() {
        return pickingGroupStrategy;
    }

    public void setPickingGroupStrategy(Byte pickingGroupStrategy) {
        this.pickingGroupStrategy = pickingGroupStrategy;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getRouteId() {
        return routeId;
    }

    public void setRouteId(String routeId) {
        this.routeId = routeId;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public Integer getRouteSequence() {
        return routeSequence;
    }

    public void setRouteSequence(Integer routeSequence) {
        this.routeSequence = routeSequence;
    }

    public Byte getOrderSelection() {
        return orderSelection;
    }

    public void setOrderSelection(Byte orderSelection) {
        this.orderSelection = orderSelection;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 获取 波次属性设置方式：0：无；1、线路；2、片区 @see BatchAttrSettingWayConstants
     *
     * @return batchAttrSettingWay 波次属性设置方式：0：无；1、线路；2、片区 @see BatchAttrSettingWayConstants
     */
    public Byte getBatchAttrSettingWay() {
        return this.batchAttrSettingWay;
    }

    /**
     * 设置 波次属性设置方式：0：无；1、线路；2、片区 @see BatchAttrSettingWayConstants
     *
     * @param batchAttrSettingWay 波次属性设置方式：0：无；1、线路；2、片区 @see BatchAttrSettingWayConstants
     */
    public void setBatchAttrSettingWay(Byte batchAttrSettingWay) {
        this.batchAttrSettingWay = batchAttrSettingWay;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }
}
