package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo;

import java.math.BigDecimal;
import java.util.*;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
public class BatchTaskFinishHelperBO {

    private BatchTaskItemFinishBO batchTaskItemFinishBO;

    private List<BatchTaskItemPO> pickedBatchTaskItemList;

    private List<BatchTaskItemDTO> totalBatchTaskItemList;

    private BatchTaskPO batchTaskPO;

    private BatchPO batchPO;

    private List<SowTaskDTO> sowTasks;

    private Long sowLocationId;

    private Map<String, BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOMap;

    private List<OrderItemTaskInfoPO> updateBatchTaskItemRelateOrderItemTaskInfoPOList;

    private List<OrderItemTaskInfoPO> needPackageOrderItemTaskInfo;

    private List<OutStockOrderPO> relatedOutStockOrderList;

    private Map<Long, BigDecimal> taskInfoDetailOverSortCountMap;

    private Map<Long, BigDecimal> lastTaskInfoLackCountMap;

    /**
     * 获取
     *
     * @return batchTaskItemFinishBO
     */
    public BatchTaskItemFinishBO getBatchTaskItemFinishBO() {
        return this.batchTaskItemFinishBO;
    }

    /**
     * 设置
     *
     * @param batchTaskItemFinishBO
     */
    public void setBatchTaskItemFinishBO(BatchTaskItemFinishBO batchTaskItemFinishBO) {
        this.batchTaskItemFinishBO = batchTaskItemFinishBO;
    }

    /**
     * 获取
     *
     * @return batchTaskPO
     */
    public BatchTaskPO getBatchTaskPO() {
        return this.batchTaskPO;
    }

    /**
     * 设置
     *
     * @param batchTaskPO
     */
    public void setBatchTaskPO(BatchTaskPO batchTaskPO) {
        this.batchTaskPO = batchTaskPO;
    }

    /**
     * 获取
     *
     * @return batchPO
     */
    public BatchPO getBatchPO() {
        return this.batchPO;
    }

    /**
     * 设置
     *
     * @param batchPO
     */
    public void setBatchPO(BatchPO batchPO) {
        this.batchPO = batchPO;
    }

    /**
     * 获取
     *
     * @return pickedBatchTaskItemList
     */
    public List<BatchTaskItemPO> getPickedBatchTaskItemList() {
        return this.pickedBatchTaskItemList;
    }

    /**
     * 设置
     *
     * @param pickedBatchTaskItemList
     */
    public void setPickedBatchTaskItemList(List<BatchTaskItemPO> pickedBatchTaskItemList) {
        this.pickedBatchTaskItemList = pickedBatchTaskItemList;
    }

    /**
     * 获取
     *
     * @return totalBatchTaskItemList
     */
    public List<BatchTaskItemDTO> getTotalBatchTaskItemList() {
        return this.totalBatchTaskItemList;
    }

    /**
     * 设置
     *
     * @param totalBatchTaskItemList
     */
    public void setTotalBatchTaskItemList(List<BatchTaskItemDTO> totalBatchTaskItemList) {
        this.totalBatchTaskItemList = totalBatchTaskItemList;
    }

    /**
     * 获取
     *
     * @return sowTasks
     */
    public List<SowTaskDTO> getSowTasks() {
        return this.sowTasks;
    }

    /**
     * 设置
     *
     * @param sowTasks
     */
    public void setSowTasks(List<SowTaskDTO> sowTasks) {
        this.sowTasks = sowTasks;
    }

    /**
     * 获取
     *
     * @return sowLocationId
     */
    public Long getSowLocationId() {
        return this.sowLocationId;
    }

    /**
     * 设置
     *
     * @param sowLocationId
     */
    public void setSowLocationId(Long sowLocationId) {
        this.sowLocationId = sowLocationId;
    }

    /**
     * 获取
     *
     * @return updateBatchTaskItemRelateOrderItemTaskInfoPOList
     */
    public List<OrderItemTaskInfoPO> getUpdateBatchTaskItemRelateOrderItemTaskInfoPOList() {
        return this.updateBatchTaskItemRelateOrderItemTaskInfoPOList;
    }

    /**
     * 设置
     *
     * @param updateBatchTaskItemRelateOrderItemTaskInfoPOList
     */
    public void setUpdateBatchTaskItemRelateOrderItemTaskInfoPOList(
        List<OrderItemTaskInfoPO> updateBatchTaskItemRelateOrderItemTaskInfoPOList) {
        this.updateBatchTaskItemRelateOrderItemTaskInfoPOList = updateBatchTaskItemRelateOrderItemTaskInfoPOList;
    }

    /**
     * 获取
     *
     * @return needPackageOrderItemTaskInfo
     */
    public List<OrderItemTaskInfoPO> getNeedPackageOrderItemTaskInfo() {
        return this.needPackageOrderItemTaskInfo;
    }

    /**
     * 设置
     *
     * @param needPackageOrderItemTaskInfo
     */
    public void setNeedPackageOrderItemTaskInfo(List<OrderItemTaskInfoPO> needPackageOrderItemTaskInfo) {
        this.needPackageOrderItemTaskInfo = needPackageOrderItemTaskInfo;
    }

    /**
     * 获取
     *
     * @return batchTaskItemCompleteDTOMap
     */
    public Map<String, BatchTaskItemCompleteDTO> getBatchTaskItemCompleteDTOMap() {
        return this.batchTaskItemCompleteDTOMap;
    }

    /**
     * 设置
     *
     * @param batchTaskItemCompleteDTOMap
     */
    public void setBatchTaskItemCompleteDTOMap(Map<String, BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOMap) {
        this.batchTaskItemCompleteDTOMap = batchTaskItemCompleteDTOMap;
    }

    // 来源货位id（如果客户端传过来为空，则取拣货任务详情的货位id）
    public static Long getFromLocationId(BatchTaskItemCompleteDTO dto, BatchTaskItemDTO batchTaskItemDTO) {
        Long fromLocationId = dto.getFromLocationId();
        if (Objects.isNull(fromLocationId)) {
            fromLocationId = batchTaskItemDTO.getLocationId();
        }

        return fromLocationId;
    }

    public static String getFromLocationName(BatchTaskItemCompleteDTO dto, BatchTaskItemDTO batchTaskItemDTO) {
        String fromLocationName = dto.getFromLocationName();
        if (StringUtils.isBlank(fromLocationName)) {
            fromLocationName = batchTaskItemDTO.getLocationName();
        }

        return fromLocationName;
    }

    public static BigDecimal getOverSortUnitTotalCount(BatchTaskItemCompleteDTO dto, BatchTaskItemDTO batchTaskItemPO) {
        // 数据库中拣货数量+用户拣货数量
        BigDecimal overSortCount =
            dto.getOverSortPackageCount().multiply(batchTaskItemPO.getSpecQuantity()).add(dto.getOverSortUnitCount());

        return overSortCount;
    }

    /**
     * 获取
     *
     * @return relatedOutStockOrderList
     */
    public List<OutStockOrderPO> getRelatedOutStockOrderList() {
        return this.relatedOutStockOrderList;
    }

    /**
     * 设置
     *
     * @param relatedOutStockOrderList
     */
    public void setRelatedOutStockOrderList(List<OutStockOrderPO> relatedOutStockOrderList) {
        this.relatedOutStockOrderList = relatedOutStockOrderList;
    }

    /**
     * 获取
     *
     * @return taskInfoDetailOverSortCountMap
     */
    public Map<Long, BigDecimal> getTaskInfoDetailOverSortCountMap() {
        return this.taskInfoDetailOverSortCountMap;
    }

    /**
     * 设置
     *
     * @param taskInfoDetailOverSortCountMap
     */
    public void setTaskInfoDetailOverSortCountMap(Map<Long, BigDecimal> taskInfoDetailOverSortCountMap) {
        this.taskInfoDetailOverSortCountMap = taskInfoDetailOverSortCountMap;
    }

    /**
     * 获取
     *
     * @return lastTaskInfoLackCountMap
     */
    public Map<Long, BigDecimal> getLastTaskInfoLackCountMap() {
        return this.lastTaskInfoLackCountMap;
    }

    /**
     * 设置
     *
     * @param lastTaskInfoLackCountMap
     */
    public void setLastTaskInfoLackCountMap(Map<Long, BigDecimal> lastTaskInfoLackCountMap) {
        this.lastTaskInfoLackCountMap = lastTaskInfoLackCountMap;
    }

    public static Map<Long, BigDecimal>
        initTaskInfoDetailOverSortCountMap(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return Collections.emptyMap();
        }
        Map<Long, BigDecimal> taskInfoDetailOverSortCountMap = new HashMap<>();
        // 记录上次已拣数量
        orderItemTaskInfoPOList.forEach(p -> {
            if (CollectionUtils.isNotEmpty(p.getDetailList())) {
                p.getDetailList().forEach(detail -> {
                    taskInfoDetailOverSortCountMap.put(detail.getId(), detail.getUnitTotalCount());
                });
            }
        });

        return taskInfoDetailOverSortCountMap;
    }

    public static Map<Long, BigDecimal>
        initLastTaskInfoLackCountMap(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return Collections.emptyMap();
        }
        Map<Long, BigDecimal> lastTaskInfoLackCountMap = new HashMap<>();
        // 记录上次已拣数量
        orderItemTaskInfoPOList.forEach(p -> {
            // 记录上次缺货数量
            lastTaskInfoLackCountMap.put(p.getId(), p.getLackUnitCount());
        });

        return lastTaskInfoLackCountMap;
    }
}
