package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * 订单变更
 *
 * <AUTHOR>
 * @date 2020-06-03 17:43
 */
public class OutStockOrderChangeDTO implements Serializable {

    private static final long serialVersionUID = -5460648911545549724L;

    /**
     * 订单id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String refOrderNo;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 订单变更明细
     */
    private List<OutStockOrderItemChangeDTO> itemList;

    /**
     * 订单变更类型 0：订单取消 1：订单修改 2：部分配送 3：配送失败
     */
    private Byte changeType;

    /**
     * 操作人
     */
    private String operatorUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<OutStockOrderItemChangeDTO> getItemList() {
        return itemList;
    }

    public void setItemList(List<OutStockOrderItemChangeDTO> itemList) {
        this.itemList = itemList;
    }

    public Byte getChangeType() {
        return changeType;
    }

    public void setChangeType(Byte changeType) {
        this.changeType = changeType;
    }

    public String getOperatorUser() {
        return operatorUser;
    }

    public void setOperatorUser(String operatorUser) {
        this.operatorUser = operatorUser;
    }
}
