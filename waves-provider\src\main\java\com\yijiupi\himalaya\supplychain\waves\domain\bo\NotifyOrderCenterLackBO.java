package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.PartSendWsmDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
public class NotifyOrderCenterLackBO {
    /**
     * 普通订单缺货信息
     */
    List<PartSendWsmDTO> normalPartSendWsmDTOList;
    /**
     * 调拨订单缺货信息
     */
    List<PartSendWsmDTO> allotPartSendWsmDTOList;

    /**
     * 缺货trace
     */
    Map<String, StringBuffer> bufferMap;
    /**
     * 操作人
     */
    Integer operatorUserId;

    public NotifyOrderCenterLackBO() {}

    public NotifyOrderCenterLackBO(List<PartSendWsmDTO> normalPartSendWsmDTOList,
        List<PartSendWsmDTO> allotPartSendWsmDTOList, Map<String, StringBuffer> bufferMap, Integer operatorUserId) {
        this.normalPartSendWsmDTOList = normalPartSendWsmDTOList;
        this.allotPartSendWsmDTOList = allotPartSendWsmDTOList;
        this.bufferMap = bufferMap;
        this.operatorUserId = operatorUserId;
    }

    /**
     * 获取 普通订单缺货信息
     *
     * @return normalPartSendWsmDTOList 普通订单缺货信息
     */
    public List<PartSendWsmDTO> getNormalPartSendWsmDTOList() {
        return this.normalPartSendWsmDTOList;
    }

    /**
     * 设置 普通订单缺货信息
     *
     * @param normalPartSendWsmDTOList 普通订单缺货信息
     */
    public void setNormalPartSendWsmDTOList(List<PartSendWsmDTO> normalPartSendWsmDTOList) {
        this.normalPartSendWsmDTOList = normalPartSendWsmDTOList;
    }

    /**
     * 获取 调拨订单缺货信息
     *
     * @return allotPartSendWsmDTOList 调拨订单缺货信息
     */
    public List<PartSendWsmDTO> getAllotPartSendWsmDTOList() {
        return this.allotPartSendWsmDTOList;
    }

    /**
     * 设置 调拨订单缺货信息
     *
     * @param allotPartSendWsmDTOList 调拨订单缺货信息
     */
    public void setAllotPartSendWsmDTOList(List<PartSendWsmDTO> allotPartSendWsmDTOList) {
        this.allotPartSendWsmDTOList = allotPartSendWsmDTOList;
    }

    /**
     * 获取 缺货trace
     *
     * @return bufferMap 缺货trace
     */
    public Map<String, StringBuffer> getBufferMap() {
        return this.bufferMap;
    }

    /**
     * 设置 缺货trace
     *
     * @param bufferMap 缺货trace
     */
    public void setBufferMap(Map<String, StringBuffer> bufferMap) {
        this.bufferMap = bufferMap;
    }

    /**
     * 获取 操作人
     *
     * @return operatorUserId 操作人
     */
    public Integer getOperatorUserId() {
        return this.operatorUserId;
    }

    /**
     * 设置 操作人
     *
     * @param operatorUserId 操作人
     */
    public void setOperatorUserId(Integer operatorUserId) {
        this.operatorUserId = operatorUserId;
    }

}
