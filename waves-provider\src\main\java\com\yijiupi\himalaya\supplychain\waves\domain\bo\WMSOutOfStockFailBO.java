package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter.WMSOutOfStockDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/3
 */
public class WMSOutOfStockFailBO {
    /**
     * 缺货信息
     */
    private WMSOutOfStockDTO wmsOutOfStockDTO;
    /**
     * 失败消息
     */
    private String errorMessage;

    public WMSOutOfStockFailBO() {}

    public WMSOutOfStockFailBO(WMSOutOfStockDTO wmsOutOfStockDTO, String errorMessage) {
        this.wmsOutOfStockDTO = wmsOutOfStockDTO;
        this.errorMessage = errorMessage;
    }

    /**
     * 获取 缺货信息
     *
     * @return wmsOutOfStockDTO 缺货信息
     */
    public WMSOutOfStockDTO getWmsOutOfStockDTO() {
        return this.wmsOutOfStockDTO;
    }

    /**
     * 设置 缺货信息
     *
     * @param wmsOutOfStockDTO 缺货信息
     */
    public void setWmsOutOfStockDTO(WMSOutOfStockDTO wmsOutOfStockDTO) {
        this.wmsOutOfStockDTO = wmsOutOfStockDTO;
    }

    /**
     * 获取 失败消息
     *
     * @return errorMessage 失败消息
     */
    public String getErrorMessage() {
        return this.errorMessage;
    }

    /**
     * 设置 失败消息
     *
     * @param errorMessage 失败消息
     */
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
