package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/4/27
 */
public class SortGroupLatestBatchTaskGetDTO implements Serializable {
    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 控制器按键编号
     */
    private String callNum;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 控制器按键编号
     *
     * @return callNum 控制器按键编号
     */
    public String getCallNum() {
        return this.callNum;
    }

    /**
     * 设置 控制器按键编号
     *
     * @param callNum 控制器按键编号
     */
    public void setCallNum(String callNum) {
        this.callNum = callNum;
    }
}
