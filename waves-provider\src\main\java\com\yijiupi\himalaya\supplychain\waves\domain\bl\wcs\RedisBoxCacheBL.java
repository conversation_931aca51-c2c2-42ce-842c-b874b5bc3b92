package com.yijiupi.himalaya.supplychain.waves.domain.bl.wcs;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.DPSNotifyItemCacheDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-08-29 12:43
 **/
@Component
public class RedisBoxCacheBL {
    private static final String REDIS_BOX_CACHE_KEY = "supp:wcs:boxCache-";
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private BatchTaskMapper batchTaskMapper;
    private static final Logger logger = LoggerFactory.getLogger(RedisBoxCacheBL.class);

    /**
     * 清除 wcs 物料箱缓存数据
     */
    public void cleanCachedBoxInfo(SowTaskPO sowTask) {
        logger.info("清空 wcs 物料箱缓存数据: {}", JSON.toJSONString(sowTask));
        List<BatchTaskPO> batchTaskList = batchTaskMapper.findTaskByBatchNo(Collections.singletonList(sowTask.getBatchNo()));
        Set<String> batchTaskIds = batchTaskList.stream().map(BatchTaskPO::getId).collect(Collectors.toSet());
        String key = REDIS_BOX_CACHE_KEY + sowTask.getWarehouseId();
        List<DPSNotifyItemCacheDTO> listInRedis = getCacheFromRedis(key);
        listInRedis.removeIf(it -> batchTaskIds.contains(it.getBatchTaskId()));
        if (!listInRedis.isEmpty()) {
            redisTemplate.opsForValue().set(key, JSON.toJSONString(listInRedis));
        } else {
            redisTemplate.delete(key);
        }
    }

    public List<String> listWcsBoxNosBySowTaskId(Integer warehouseId, Long sowTaskId) {
        List<BatchTaskPO> batchTasks = batchTaskMapper.selectBatchTaskBySowTaskIds(Collections.singletonList(sowTaskId));
        if (CollectionUtils.isEmpty(batchTasks)) {
            return Collections.emptyList();
        }
        Set<String> batchTaskIds = batchTasks.stream().map(BatchTaskPO::getId).collect(Collectors.toSet());
        String key = REDIS_BOX_CACHE_KEY + warehouseId;
        return getCacheFromRedis(key).stream().filter(it -> batchTaskIds.contains(it.getBatchTaskId()))
                .map(DPSNotifyItemCacheDTO::getBoxID)
                .collect(Collectors.toList());
    }

    private List<DPSNotifyItemCacheDTO> getCacheFromRedis(String key) {
        return Optional.ofNullable(redisTemplate.opsForValue().get(key))
                .map(JSON::parseArray).map(it -> it.toJavaList(DPSNotifyItemCacheDTO.class)).orElseGet(ArrayList::new);
    }

}
