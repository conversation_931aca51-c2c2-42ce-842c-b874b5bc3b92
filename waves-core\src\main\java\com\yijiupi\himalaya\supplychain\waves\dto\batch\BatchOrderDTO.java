package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.util.List;

/**
 * 波次订单信息
 */
public class BatchOrderDTO implements Serializable {

    /**
     * 波次id
     */
    private Long batchId;

    /**
     * 波次编号
     */
    private String batchNo;

    /**
     * 波次状态
     */
    private Byte batchState;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 播种任务状态
     */
    private Byte sowTaskState;

    /**
     * 订单id
     */
    private Long outStockOrderId;

    /**
     * 订单编号
     */
    private String refOrderNo;

    /**
     * 订单状态
     */
    private Byte orderState;

    /**
     * 订单序号
     */
    private Integer orderSequence;

    /**
     * 复核信息id
     */
    private Long billReviewId;

    /**
     * 复核状态 0:待复核 1:复核中 2:已复核
     */
    private Byte billReviewState;

    /**
     * 复核人名称
     */
    private String reviewer;

    /**
     * 复核人id
     */
    private Integer reviewerId;

    /**
     * 已装箱箱数
     */
    private Integer packagedBoxAmount;

    /**
     * 箱号编码
     */
    private List<String> boxCodeNos;

    /**
     * 集货位id
     */
    private Long sowLocationId;

    /**
     * 集货位名称
     */
    private String sowLocationName;

    /**
     * 拣货任务编号
     */
    private String batchTaskNo;

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Byte getBatchState() {
        return batchState;
    }

    public void setBatchState(Byte batchState) {
        this.batchState = batchState;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Byte getSowTaskState() {
        return sowTaskState;
    }

    public void setSowTaskState(Byte sowTaskState) {
        this.sowTaskState = sowTaskState;
    }

    public Long getOutStockOrderId() {
        return outStockOrderId;
    }

    public void setOutStockOrderId(Long outStockOrderId) {
        this.outStockOrderId = outStockOrderId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Byte getOrderState() {
        return orderState;
    }

    public void setOrderState(Byte orderState) {
        this.orderState = orderState;
    }

    public Long getBillReviewId() {
        return billReviewId;
    }

    public void setBillReviewId(Long billReviewId) {
        this.billReviewId = billReviewId;
    }

    public Byte getBillReviewState() {
        return billReviewState;
    }

    public void setBillReviewState(Byte billReviewState) {
        this.billReviewState = billReviewState;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    public Integer getReviewerId() {
        return reviewerId;
    }

    public void setReviewerId(Integer reviewerId) {
        this.reviewerId = reviewerId;
    }

    public Integer getPackagedBoxAmount() {
        return packagedBoxAmount;
    }

    public void setPackagedBoxAmount(Integer packagedBoxAmount) {
        this.packagedBoxAmount = packagedBoxAmount;
    }

    public Integer getOrderSequence() {
        return orderSequence;
    }

    public void setOrderSequence(Integer orderSequence) {
        this.orderSequence = orderSequence;
    }

    public List<String> getBoxCodeNos() {
        return boxCodeNos;
    }

    public void setBoxCodeNos(List<String> boxCodeNos) {
        this.boxCodeNos = boxCodeNos;
    }

    public Long getSowLocationId() {
        return sowLocationId;
    }

    public void setSowLocationId(Long sowLocationId) {
        this.sowLocationId = sowLocationId;
    }

    public String getSowLocationName() {
        return sowLocationName;
    }

    public void setSowLocationName(String sowLocationName) {
        this.sowLocationName = sowLocationName;
    }

    public String getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }
}
