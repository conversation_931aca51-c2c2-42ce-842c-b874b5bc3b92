package com.yijiupi.himalaya.supplychain.waves.dto.stockup;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-10-11 16:38
 **/
public class StockUpCompleteDTO implements Serializable {

    /**
     * 城市 id
     */
    private Integer cityId;

    /**
     * 仓库 id
     */
    private Integer warehouseId;

    /**
     * 需要完成拣货的 拣货任务项 id
     */
    private List<String> batchTaskItemIds;
    
    /**
     * 备货记录, 车次以及车次内所有产品信息
     */
    private List<StockUpRecordDTO> records;

    /**
     * 操作人 id
     */
    private Integer userId;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<String> getBatchTaskItemIds() {
        return batchTaskItemIds;
    }

    public void setBatchTaskItemIds(List<String> batchTaskItemIds) {
        this.batchTaskItemIds = batchTaskItemIds;
    }

    public List<StockUpRecordDTO> getRecords() {
        return records;
    }

    public void setRecords(List<StockUpRecordDTO> records) {
        this.records = records;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }
}
