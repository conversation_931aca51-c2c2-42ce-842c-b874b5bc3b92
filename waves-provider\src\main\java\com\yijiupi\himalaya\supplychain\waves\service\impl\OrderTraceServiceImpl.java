package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.util.Collection;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.dto.trace.MessagePushParam;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.waves.batch.OrderTraceService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderCenterBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderTraceBL;
import com.yijiupi.himalaya.supplychain.waves.dto.trace.OrderTraceDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.trace.OrderTraceParamDTO;

/**
 * <AUTHOR> 2017/11/24
 */
@Service(timeout = 120000)
public class OrderTraceServiceImpl implements OrderTraceService {

    @Autowired
    private OrderTraceBL waveTraceBL;

    @Autowired
    private OrderCenterBL orderCenterBL;

    @Override
    public void insert(OrderTraceDTO record) {
        waveTraceBL.insert(record);
    }

    @Override
    public void insertUpdateBatch(List<OrderTraceDTO> record) {
        waveTraceBL.insertUpdateBatch(record);
    }

    @Override
    public List<OrderTraceDTO> selectByBusinessIdOrNo(Integer orgId, Long businessId, String businessNo) {
        return waveTraceBL.selectByBusinessIdOrNo(orgId, businessId, businessNo);
    }

    @Override
    @Deprecated
    public List<OrderTraceDTO> selectByBusinessIdOrNo(Long businessId, String businessNo) {
        return waveTraceBL.selectByBusinessIdOrNo(null, businessId, businessNo);
    }

    @Override
    public void pushPutawayTaskMsg(Integer sortId) {
        waveTraceBL.pushPutawayTaskMsg(sortId);
    }

    /**
     * 发送移库任务消息到PDA
     *
     * @param sortId
     */
    @Override
    public void pushStoreTransferMsg(Integer sortId) {
        waveTraceBL.pushStoreTransferMsg(sortId);
    }

    /**
     * 发送消息到PDA
     */
    @Override
    public void pushMsg(Integer sortId, String content) {
        waveTraceBL.pushMsg(sortId, content);
    }

    /**
     * 批量发送消息到PDA
     */
    @Override
    public void batchPushMsg(List<Integer> sortIds, String content) {
        waveTraceBL.batchPushMsg(sortIds, content);
    }

    @Override
    public List<OrderTraceDTO> listOrderTrace(OrderTraceParamDTO orderTraceParamDTO) {
        return waveTraceBL.selectByBusinessIdOrNo(null, null, orderTraceParamDTO.getBusinessNo());
    }

    /**
     * 按角色推送消息
     *
     * @param param 推送入参
     */
    @Override
    public void pushMessageByRole(MessagePushParam param) {
        waveTraceBL.pushMessageByRole(param);
    }

    /**
     * 群发消息到 PDA
     *
     * @param param 推送入参
     */
    @Override
    public void batchPushMsg(MessagePushParam param) {
        waveTraceBL.batchPushMsg(param);
    }
}
