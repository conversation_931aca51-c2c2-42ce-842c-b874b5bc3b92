package com.yijiupi.himalaya.supplychain.waves.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-07-01 10:23
 **/
public class PushMessageConstants {

    /**
     * 前端是否需要缓存消息
     */
    public static final String CACHE_MESSAGE = "cacheMessage";

    /**
     * 消息推送给当前账号所有绑定的设备
     */
    public static final Integer ACCOUNT_TYPE_TO_ALL_DEVICE = 1;

    /**
     * 返回一个新的 {@link HashMap}, 其中包含一个前端缓存消息的标识
     */
    public static Map<String, String> cacheMessageExtra() {
        Map<String, String> map = new HashMap<>();
        // 设置成 true 前端将会缓存消息
        map.put(PushMessageConstants.CACHE_MESSAGE, "true");
        return map;
    }

}
