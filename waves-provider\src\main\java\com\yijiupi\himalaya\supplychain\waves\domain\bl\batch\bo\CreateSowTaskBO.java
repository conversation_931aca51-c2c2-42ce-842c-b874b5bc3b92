package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo;

import java.util.List;

import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.GoodsCollectionLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/18
 */
public class CreateSowTaskBO {

    private WavesStrategyBO wavesStrategyDTO;
    private PassageDTO passageDTO;
    private String title;
    private String operateUser;
    private Integer cityId;
    private WarehouseConfigDTO warehouseConfigDTO;
    private BatchBO batchBO;
    private java.util.List<GoodsCollectionLocationBO> lstLocations;
    private List<SowTaskPO> lstSowTaskPO;
    private String locationName;
    private List<SowOrderPO> lstSowOrders;
    private String driverName;
    private Boolean allocationFlag;
    private boolean needRecheck;
    private List<OutStockOrderPO> splitOrderList;
    private WaveCreateDTO oriCreateDTO;

    /**
     * 获取
     *
     * @return wavesStrategyDTO
     */
    public WavesStrategyBO getWavesStrategyDTO() {
        return this.wavesStrategyDTO;
    }

    /**
     * 设置
     *
     * @param wavesStrategyDTO
     */
    public void setWavesStrategyDTO(WavesStrategyBO wavesStrategyDTO) {
        this.wavesStrategyDTO = wavesStrategyDTO;
    }

    /**
     * 获取
     *
     * @return passageDTO
     */
    public PassageDTO getPassageDTO() {
        return this.passageDTO;
    }

    /**
     * 设置
     *
     * @param passageDTO
     */
    public void setPassageDTO(PassageDTO passageDTO) {
        this.passageDTO = passageDTO;
    }

    /**
     * 获取
     *
     * @return title
     */
    public String getTitle() {
        return this.title;
    }

    /**
     * 设置
     *
     * @param title
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 获取
     *
     * @return operateUser
     */
    public String getOperateUser() {
        return this.operateUser;
    }

    /**
     * 设置
     *
     * @param operateUser
     */
    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    /**
     * 获取
     *
     * @return cityId
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置
     *
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取
     *
     * @return warehouseConfigDTO
     */
    public WarehouseConfigDTO getWarehouseConfigDTO() {
        return this.warehouseConfigDTO;
    }

    /**
     * 设置
     *
     * @param warehouseConfigDTO
     */
    public void setWarehouseConfigDTO(WarehouseConfigDTO warehouseConfigDTO) {
        this.warehouseConfigDTO = warehouseConfigDTO;
    }

    /**
     * 获取
     *
     * @return batchBO
     */
    public BatchBO getBatchBO() {
        return this.batchBO;
    }

    /**
     * 设置
     *
     * @param batchBO
     */
    public void setBatchBO(BatchBO batchBO) {
        this.batchBO = batchBO;
    }

    /**
     * 获取
     *
     * @return lstLocations
     */
    public List<GoodsCollectionLocationBO> getLstLocations() {
        return this.lstLocations;
    }

    /**
     * 设置
     *
     * @param lstLocations
     */
    public void setLstLocations(List<GoodsCollectionLocationBO> lstLocations) {
        this.lstLocations = lstLocations;
    }

    /**
     * 获取
     *
     * @return lstSowTaskPO
     */
    public List<SowTaskPO> getLstSowTaskPO() {
        return this.lstSowTaskPO;
    }

    /**
     * 设置
     *
     * @param lstSowTaskPO
     */
    public void setLstSowTaskPO(List<SowTaskPO> lstSowTaskPO) {
        this.lstSowTaskPO = lstSowTaskPO;
    }

    /**
     * 获取
     *
     * @return locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置
     *
     * @param locationName
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取
     *
     * @return lstSowOrders
     */
    public List<SowOrderPO> getLstSowOrders() {
        return this.lstSowOrders;
    }

    /**
     * 设置
     *
     * @param lstSowOrders
     */
    public void setLstSowOrders(List<SowOrderPO> lstSowOrders) {
        this.lstSowOrders = lstSowOrders;
    }

    /**
     * 获取
     *
     * @return driverName
     */
    public String getDriverName() {
        return this.driverName;
    }

    /**
     * 设置
     *
     * @param driverName
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    /**
     * 获取
     *
     * @return allocationFlag
     */
    public Boolean getAllocationFlag() {
        return this.allocationFlag;
    }

    /**
     * 设置
     *
     * @param allocationFlag
     */
    public void setAllocationFlag(Boolean allocationFlag) {
        this.allocationFlag = allocationFlag;
    }

    /**
     * 获取
     *
     * @return needRecheck
     */
    public boolean isNeedRecheck() {
        return this.needRecheck;
    }

    /**
     * 设置
     *
     * @param needRecheck
     */
    public void setNeedRecheck(boolean needRecheck) {
        this.needRecheck = needRecheck;
    }

    /**
     * 获取
     *
     * @return splitOrderList
     */
    public List<OutStockOrderPO> getSplitOrderList() {
        return this.splitOrderList;
    }

    /**
     * 设置
     *
     * @param splitOrderList
     */
    public void setSplitOrderList(List<OutStockOrderPO> splitOrderList) {
        this.splitOrderList = splitOrderList;
    }

    /**
     * 获取
     *
     * @return oriCreateDTO
     */
    public WaveCreateDTO getOriCreateDTO() {
        return this.oriCreateDTO;
    }

    /**
     * 设置
     *
     * @param oriCreateDTO
     */
    public void setOriCreateDTO(WaveCreateDTO oriCreateDTO) {
        this.oriCreateDTO = oriCreateDTO;
    }

    public static final class CreateSowTaskBOBuilder {
        private WavesStrategyBO wavesStrategyDTO;
        private PassageDTO passageDTO;
        private String title;
        private String operateUser;
        private Integer cityId;
        private WarehouseConfigDTO warehouseConfigDTO;
        private BatchBO batchBO;
        private List<GoodsCollectionLocationBO> lstLocations;
        private List<SowTaskPO> lstSowTaskPO;
        private String locationName;
        private List<SowOrderPO> lstSowOrders;
        private String driverName;
        private Boolean allocationFlag;
        private boolean needRecheck;
        private List<OutStockOrderPO> splitOrderList;
        private WaveCreateDTO oriCreateDTO;

        private CreateSowTaskBOBuilder() {}

        public static CreateSowTaskBOBuilder aCreateSowTaskBO() {
            return new CreateSowTaskBOBuilder();
        }

        public CreateSowTaskBOBuilder withWavesStrategyDTO(WavesStrategyBO wavesStrategyDTO) {
            this.wavesStrategyDTO = wavesStrategyDTO;
            return this;
        }

        public CreateSowTaskBOBuilder withPassageDTO(PassageDTO passageDTO) {
            this.passageDTO = passageDTO;
            return this;
        }

        public CreateSowTaskBOBuilder withTitle(String title) {
            this.title = title;
            return this;
        }

        public CreateSowTaskBOBuilder withOperateUser(String operateUser) {
            this.operateUser = operateUser;
            return this;
        }

        public CreateSowTaskBOBuilder withCityId(Integer cityId) {
            this.cityId = cityId;
            return this;
        }

        public CreateSowTaskBOBuilder withWarehouseConfigDTO(WarehouseConfigDTO warehouseConfigDTO) {
            this.warehouseConfigDTO = warehouseConfigDTO;
            return this;
        }

        public CreateSowTaskBOBuilder withBatchBO(BatchBO batchBO) {
            this.batchBO = batchBO;
            return this;
        }

        public CreateSowTaskBOBuilder withLstLocations(List<GoodsCollectionLocationBO> lstLocations) {
            this.lstLocations = lstLocations;
            return this;
        }

        public CreateSowTaskBOBuilder withLstSowTaskPO(List<SowTaskPO> lstSowTaskPO) {
            this.lstSowTaskPO = lstSowTaskPO;
            return this;
        }

        public CreateSowTaskBOBuilder withLocationName(String locationName) {
            this.locationName = locationName;
            return this;
        }

        public CreateSowTaskBOBuilder withLstSowOrders(List<SowOrderPO> lstSowOrders) {
            this.lstSowOrders = lstSowOrders;
            return this;
        }

        public CreateSowTaskBOBuilder withDriverName(String driverName) {
            this.driverName = driverName;
            return this;
        }

        public CreateSowTaskBOBuilder withAllocationFlag(Boolean allocationFlag) {
            this.allocationFlag = allocationFlag;
            return this;
        }

        public CreateSowTaskBOBuilder withNeedRecheck(boolean needRecheck) {
            this.needRecheck = needRecheck;
            return this;
        }

        public CreateSowTaskBOBuilder withSplitOrderList(List<OutStockOrderPO> splitOrderList) {
            this.splitOrderList = splitOrderList;
            return this;
        }

        public CreateSowTaskBOBuilder withOriCreateDTO(WaveCreateDTO oriCreateDTO) {
            this.oriCreateDTO = oriCreateDTO;
            return this;
        }

        public CreateSowTaskBO build() {
            CreateSowTaskBO createSowTaskBO = new CreateSowTaskBO();
            createSowTaskBO.setWavesStrategyDTO(wavesStrategyDTO);
            createSowTaskBO.setPassageDTO(passageDTO);
            createSowTaskBO.setTitle(title);
            createSowTaskBO.setOperateUser(operateUser);
            createSowTaskBO.setCityId(cityId);
            createSowTaskBO.setWarehouseConfigDTO(warehouseConfigDTO);
            createSowTaskBO.setBatchBO(batchBO);
            createSowTaskBO.setLstLocations(lstLocations);
            createSowTaskBO.setLstSowTaskPO(lstSowTaskPO);
            createSowTaskBO.setLocationName(locationName);
            createSowTaskBO.setLstSowOrders(lstSowOrders);
            createSowTaskBO.setDriverName(driverName);
            createSowTaskBO.setAllocationFlag(allocationFlag);
            createSowTaskBO.setNeedRecheck(needRecheck);
            createSowTaskBO.setSplitOrderList(splitOrderList);
            createSowTaskBO.setOriCreateDTO(oriCreateDTO);
            return createSowTaskBO;
        }
    }
}
