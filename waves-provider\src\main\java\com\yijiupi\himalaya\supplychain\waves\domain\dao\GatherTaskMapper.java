/*
 * @ClassName GatherTaskMapper
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-10-30 17:42:36
 */
package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskPO;

public interface GatherTaskMapper {
    /**
     * @Title deleteByPrimaryKey
     * @param id
     * @return int
     */
    int deleteByPrimaryKey(Long id);

    /**
     * @Title insert
     * @param record
     * @return int
     */
    int insert(GatherTaskPO record);

    /**
     * @Title insertSelective
     * @param record
     * @return int
     */
    int insertSelective(GatherTaskPO record);

    /**
     * @Title selectByPrimaryKey
     * @param id
     * @return GatherTask
     */
    GatherTaskPO selectByPrimaryKey(Long id);

    /**
     * 查询拣货任务是否创建集货任务
     * 
     * @param batchTaskId
     * @return
     */
    Integer selectByBatchTaskId(String batchTaskId);

    /**
     * @Title updateByPrimaryKeySelective
     * @param record
     * @return int
     */
    int updateByPrimaryKeySelective(GatherTaskPO record);

    /**
     * @Title updateByPrimaryKey
     * @param record
     * @return int
     */
    int updateByPrimaryKey(GatherTaskPO record);

    /**
     * 修状态为已完成
     * 
     * @param record
     * @return
     */
    int updateGatherTaskStatus(GatherTaskPO record);
}