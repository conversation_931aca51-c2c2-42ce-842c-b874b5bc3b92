package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckResultDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemPeriodConfigResultDTO;

/**
 * 拣货任务项禁止销售配置
 *
 * <AUTHOR>
 * @Date 2025/4/18
 */
public class BatchTaskItemPeriodConfigResultConvertor {

    public static List<BatchTaskItemPeriodConfigResultDTO> convertTo(List<OutStockConfigCheckResultDTO> resultDTOS) {
        List<BatchTaskItemPeriodConfigResultDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(resultDTOS)) {
            return dtoList;
        }

        resultDTOS.forEach(po -> {
            BatchTaskItemPeriodConfigResultDTO dto = new BatchTaskItemPeriodConfigResultDTO();
            dto.setAlarm(po.getAlarm());
            dto.setSkuId(po.getSkuId());
            dto.setProductName(po.getProductName());
            dto.setLocationId(po.getLocationId());
            dto.setRealLocationName(po.getRealLocationName());
            dto.setTotalCountMinUnit(po.getTotalCountMinUnit());
            dto.setExpiredFlag(po.getExpiredFlag());
            dto.setExpireTime(po.getExpireTime());
            dto.setLatestProductDate(po.getLatestProductDate());
            dto.setProductTime(po.getProductTime());
            dto.setShelfLife(po.getShelfLife());
            dto.setStatisticsClass(po.getStatisticsClass());
            dto.setSecondStatisticsClass(po.getSecondStatisticsClass());
            dto.setChannel(po.getChannel());
            dto.setSource(po.getSource());
            dto.setStorageAttribute(po.getStorageAttribute());
            dto.setMonthOfShelfLife(po.getMonthOfShelfLife());
            dto.setShelfLifeUnit(po.getShelfLifeUnit());

            dtoList.add(dto);
        });

        return dtoList;
    }
}
