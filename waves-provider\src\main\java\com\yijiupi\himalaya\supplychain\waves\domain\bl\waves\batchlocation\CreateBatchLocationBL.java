package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.OutStockOrderHandleLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CreateBatchLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchWorkSettingDTO;
import com.yijiupi.himalaya.supplychain.waves.util.OrderPieceUtils;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022-09-26 15:15
 */
@Service
public class CreateBatchLocationBL {

    @Autowired
    private List<CreateBatchLocationBaseBL> createLocationList;
    @Autowired
    private Gson gson;

    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private IVariableValueService iVariableValueService;

    private static final String OPEN_FRONT_WAREHOUSE_NP_PRODUCT_PICK = "OpenFrontWarehouseNpProductPick";
    private static final Logger LOG = LoggerFactory.getLogger(CreateBatchLocationBL.class);

    public CreateBatchLocationBO handleLocation(List<OutStockOrderPO> outStockOrderList,
        BatchWorkSettingDTO workSettingDTO, WavesStrategyBO wavesStrategyDTO) {
        Integer warehouseId = wavesStrategyDTO.getWarehouseId();

        // 仓库是2.5还是3.0
        Boolean isOpenStock = warehouseConfigService.isOpenLocationStock(warehouseId);
        // 是否开启了货位组
        boolean isOpenLocationGroup = warehouseConfigService.isOpenLocationGroup(warehouseId);

        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(warehouseId);

        CreateBatchLocationBO bo = new CreateBatchLocationBO();
        bo.setWarehouseId(warehouseId);
        bo.setIsOpenStock(isOpenStock);
        bo.setIsOpenLocationGroup(isOpenLocationGroup);
        bo.setWorkSetting(workSettingDTO);
        bo.setWarehouseConfigDTO(warehouseConfigDTO);
        bo.setWavesStrategyDTO((WavesStrategyBO)wavesStrategyDTO);
        bo.setOpenFrontWarehouseOpenNPProductPick(getFrontWarehouseOpenNPProductPick(warehouseId));

        outStockOrderList.forEach(p -> {
            if (!CollectionUtils.isEmpty(p.getItems())) {
                p.getItems().removeIf(
                    q -> q.getUnittotalcount() == null || q.getUnittotalcount().compareTo(BigDecimal.ZERO) <= 0);
                // 设置订单项的产品单价
                setUnitPriceByOrderItem(p.getItems());
            }
        });

        Optional<CreateBatchLocationBaseBL> optional = createLocationList.stream().filter(m -> m.support(bo)).findAny();
        if (!optional.isPresent()) {
            LOG.warn("未找到合适的设置货位的服务，入参：{}", gson.toJson(bo));
            bo.setNormalOrderList(outStockOrderList);
            return bo;
        }

        OutStockOrderHandleLocationBO handleLocationOrderList = optional.get().setLocation(outStockOrderList, bo);

        setTempLocation(handleLocationOrderList);
        handlePreWarehouseNpOrderPackageCount(handleLocationOrderList.getNpProductOrderList());

        bo.setNormalOrderList(handleLocationOrderList.getNormalOrderList());
        bo.setNpProductOrderList(handleLocationOrderList.getNpProductOrderList());
        return bo;
    }

    private void setTempLocation(OutStockOrderHandleLocationBO handleLocationOrderList) {
        List<OutStockOrderPO> normalOrderList = handleLocationOrderList.getNormalOrderList();
        if (CollectionUtils.isEmpty(normalOrderList)) {
            return;
        }
        // SCM-18124 拣货任务明细的拣货数量，零拣位返回大小件，不返回小件数量
        handlePreWarehouseNpOrderPackageCount(normalOrderList);
    }

    // 如果有内配的，大单位还原
    private void handlePreWarehouseNpOrderPackageCount(List<OutStockOrderPO> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        orderList.stream().filter(order -> !CollectionUtils.isEmpty(order.getItems())).filter(this::isFrontWarehouse)
            .forEach(order -> {
                order.getItems().forEach(orderItem -> {
                    BigDecimal[] count = orderItem.getUnittotalcount().divideAndRemainder(orderItem.getSpecquantity());
                    orderItem.setPackagecount(count[0]);
                    orderItem.setUnitcount(count[1]);
                });
                order.setPackageamount(order.getItems().stream().map(OutStockOrderItemPO::getPackagecount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
                order.setUnitamount(order.getItems().stream().map(OutStockOrderItemPO::getUnitcount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            });
    }

    private boolean isFrontWarehouse(OutStockOrderPO outStockOrderPO) {
        if (!OrderConstant.ALLOT_TYPE_ALLOCATION.equals(outStockOrderPO.getAllotType())) {
            return Boolean.FALSE;
        }
        if (Objects.isNull(outStockOrderPO.getOutBoundType())) {
            return Boolean.FALSE;
        }
        if (OutBoundTypeEnum.SALE_ORDER.getCode().byteValue() == outStockOrderPO.getOutBoundType()) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    /**
     * 设置订单项的产品单价(四舍五入保留6位小数)
     */
    private void setUnitPriceByOrderItem(List<OutStockOrderItemPO> orderItemPOList) {
        if (CollectionUtils.isEmpty(orderItemPOList)) {
            return;
        }
        orderItemPOList.forEach(p -> {
            if (p.getTotalAmount() != null) {
                // 产品单价 = 产品总金额 / 小单位总数量
                p.setUnitPrice(p.getTotalAmount().divide(p.getUnittotalcount(), 6, RoundingMode.HALF_UP));
            }
        });
    }

    private boolean getFrontWarehouseOpenNPProductPick(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        // 获取贸易伙伴配置
        VariableValueQueryDTO variableValueQuery = new VariableValueQueryDTO();
        variableValueQuery.setWarehouseId(warehouseId);
        variableValueQuery.setVariableKey(OPEN_FRONT_WAREHOUSE_NP_PRODUCT_PICK);
        VariableDefAndValueDTO valueDTO = iVariableValueService.detailVariable(variableValueQuery);
        if (Objects.isNull(valueDTO) || StringUtils.isEmpty(valueDTO.getVariableData())) {
            return Boolean.FALSE;
        }

        try {
            return Boolean.parseBoolean(valueDTO.getVariableData());
        } catch (Exception e) {
            LOG.warn("解析失败, {}", JSON.toJSONString(valueDTO));
            return Boolean.FALSE;
        }
    }

}
