<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.BillReviewMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.BillReviewPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="INTEGER" property="orgId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="BusinessNo" jdbcType="VARCHAR" property="businessNo"/>
        <result column="Business_Id" jdbcType="VARCHAR" property="businessId"/>
        <result column="BusinessType" jdbcType="TINYINT" property="businessType"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="Reviewer" jdbcType="VARCHAR" property="reviewer"/>
        <result column="Reviewer_Id" jdbcType="INTEGER" property="reviewerId"/>
        <result column="PackageAmount" jdbcType="DECIMAL" property="packageAmount"/>
        <result column="UnitAmount" jdbcType="DECIMAL" property="unitAmount"/>
        <result column="OverPackageAmount" jdbcType="DECIMAL" property="overPackageAmount"/>
        <result column="OverUnitAmount" jdbcType="DECIMAL" property="overUnitAmount"/>
        <result column="PackagedBoxAmount" jdbcType="INTEGER" property="packagedBoxAmount"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="RelatedBusinessNo" jdbcType="VARCHAR" property="relatedBusinessNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Org_Id, Warehouse_Id, BusinessNo, Business_Id, BusinessType, State, Reviewer, Reviewer_Id,
        PackageAmount, UnitAmount, OverPackageAmount, OverUnitAmount, PackagedBoxAmount,
        Remark, CreateUser, CreateTime, LastUpdateUser, LastUpdateTime, RelatedBusinessNo
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from billreview
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BillReviewPO">
        insert into billreview (Id, Org_Id, Warehouse_Id, BusinessNo,
        Business_Id, BusinessType, State,
        Reviewer, Reviewer_Id, PackageAmount,
        UnitAmount, OverPackageAmount, OverUnitAmount,
        PackagedBoxAmount, Remark, CreateUser,
        CreateTime, LastUpdateUser, LastUpdateTime, RelatedBusinessNo
        )
        values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER},
        #{businessNo,jdbcType=VARCHAR},
        #{businessId,jdbcType=VARCHAR}, #{businessType,jdbcType=TINYINT}, #{state,jdbcType=TINYINT},
        #{reviewer,jdbcType=VARCHAR}, #{reviewerId,jdbcType=INTEGER}, #{packageAmount,jdbcType=DECIMAL},
        #{unitAmount,jdbcType=DECIMAL}, #{overPackageAmount,jdbcType=DECIMAL}, #{overUnitAmount,jdbcType=DECIMAL},
        #{packagedBoxAmount,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR},
        now(), #{lastUpdateUser,jdbcType=VARCHAR}, now(), #{relatedBusinessNo,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BillReviewPO">
        insert into billreview
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="orgId != null">
                Org_Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="businessNo != null">
                BusinessNo,
            </if>
            <if test="businessId != null">
                Business_Id,
            </if>
            <if test="businessType != null">
                BusinessType,
            </if>
            <if test="state != null">
                State,
            </if>
            <if test="reviewer != null">
                Reviewer,
            </if>
            <if test="reviewerId != null">
                Reviewer_Id,
            </if>
            <if test="packageAmount != null">
                PackageAmount,
            </if>
            <if test="unitAmount != null">
                UnitAmount,
            </if>
            <if test="overPackageAmount != null">
                OverPackageAmount,
            </if>
            <if test="overUnitAmount != null">
                OverUnitAmount,
            </if>
            <if test="packagedBoxAmount != null">
                PackagedBoxAmount,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="relatedBusinessNo != null">
                RelatedBusinessNo,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="businessNo != null">
                #{businessNo,jdbcType=VARCHAR},
            </if>
            <if test="businessId != null">
                #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="reviewer != null">
                #{reviewer,jdbcType=VARCHAR},
            </if>
            <if test="reviewerId != null">
                #{reviewerId,jdbcType=INTEGER},
            </if>
            <if test="packageAmount != null">
                #{packageAmount,jdbcType=DECIMAL},
            </if>
            <if test="unitAmount != null">
                #{unitAmount,jdbcType=DECIMAL},
            </if>
            <if test="overPackageAmount != null">
                #{overPackageAmount,jdbcType=DECIMAL},
            </if>
            <if test="overUnitAmount != null">
                #{overUnitAmount,jdbcType=DECIMAL},
            </if>
            <if test="packagedBoxAmount != null">
                #{packagedBoxAmount,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="relatedBusinessNo != null">
                #{relatedBusinessNo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BillReviewPO">
        update billreview
        <set>
            <if test="orgId != null">
                Org_Id = #{orgId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="businessNo != null">
                BusinessNo = #{businessNo,jdbcType=VARCHAR},
            </if>
            <if test="businessId != null">
                Business_Id = #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                BusinessType = #{businessType,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="reviewer != null">
                Reviewer = #{reviewer,jdbcType=VARCHAR},
            </if>
            <if test="reviewerId != null">
                Reviewer_Id = #{reviewerId,jdbcType=INTEGER},
            </if>
            <if test="packageAmount != null">
                PackageAmount = #{packageAmount,jdbcType=DECIMAL},
            </if>
            <if test="unitAmount != null">
                UnitAmount = #{unitAmount,jdbcType=DECIMAL},
            </if>
            <if test="overPackageAmount != null">
                OverPackageAmount = #{overPackageAmount,jdbcType=DECIMAL},
            </if>
            <if test="overUnitAmount != null">
                OverUnitAmount = #{overUnitAmount,jdbcType=DECIMAL},
            </if>
            <if test="packagedBoxAmount != null">
                PackagedBoxAmount = #{packagedBoxAmount,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="relatedBusinessNo != null">
                RelatedBusinessNo = #{relatedBusinessNo,jdbcType=VARCHAR},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BillReviewPO">
        update billreview
        set Org_Id = #{orgId,jdbcType=INTEGER},
        WarehouseId = #{warehouseId,jdbcType=INTEGER},
        BusinessNo = #{businessNo,jdbcType=VARCHAR},
        Business_Id = #{businessId,jdbcType=VARCHAR},
        BusinessType = #{businessType,jdbcType=TINYINT},
        State = #{state,jdbcType=TINYINT},
        Reviewer = #{reviewer,jdbcType=VARCHAR},
        Reviewer_Id = #{reviewerId,jdbcType=INTEGER},
        PackageAmount = #{packageAmount,jdbcType=DECIMAL},
        UnitAmount = #{unitAmount,jdbcType=DECIMAL},
        OverPackageAmount = #{overPackageAmount,jdbcType=DECIMAL},
        OverUnitAmount = #{overUnitAmount,jdbcType=DECIMAL},
        PackagedBoxAmount = #{packagedBoxAmount,jdbcType=INTEGER},
        Remark = #{remark,jdbcType=VARCHAR},
        CreateUser = #{createUser,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        RelatedBusinessNo = #{relatedBusinessNo,jdbcType=VARCHAR}
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getByBusinessNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from billreview
        where Org_Id = #{orgId,jdbcType=INTEGER}
        and BusinessNo = #{businessNo,jdbcType=VARCHAR}
        and RelatedBusinessNo = #{relatedBusinessNo,jdbcType=VARCHAR}
    </select>

    <insert id="insertOrUpdateBatch">
        insert into billreview (Id, Org_Id, Warehouse_Id, BusinessNo,
        Business_Id, BusinessType, State,
        Reviewer, Reviewer_Id, PackageAmount,
        UnitAmount, OverPackageAmount, OverUnitAmount,
        PackagedBoxAmount, Remark, CreateUser,
        CreateTime, LastUpdateUser, LastUpdateTime, RelatedBusinessNo
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{id,jdbcType=BIGINT}, #{orgId,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER},
            #{businessNo,jdbcType=VARCHAR},
            #{businessId,jdbcType=VARCHAR}, #{businessType,jdbcType=TINYINT}, #{state,jdbcType=TINYINT},
            #{reviewer,jdbcType=VARCHAR}, #{reviewerId,jdbcType=INTEGER}, #{packageAmount,jdbcType=DECIMAL},
            #{unitAmount,jdbcType=DECIMAL}, #{overPackageAmount,jdbcType=DECIMAL}, #{overUnitAmount,jdbcType=DECIMAL},
            #{packagedBoxAmount,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR},
            now(), #{lastUpdateUser,jdbcType=VARCHAR}, now(), #{relatedBusinessNo,jdbcType=VARCHAR}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        State=VALUES(State),
        Reviewer=VALUES(Reviewer),
        Reviewer_Id=VALUES(Reviewer_Id),
        OverPackageAmount=VALUES(OverPackageAmount),
        OverUnitAmount=VALUES(OverUnitAmount),
        PackagedBoxAmount=VALUES(PackagedBoxAmount),
        Remark=VALUES(Remark),
        LastUpdateUser=VALUES(LastUpdateUser),
        LastUpdateTime=VALUES(LastUpdateTime)
    </insert>

</mapper>