package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.allot.dto.stockin.InStockWavesUpdateDTO;
import com.yijiupi.himalaya.supplychain.allot.dto.stockout.StockOutApplyResultDTO;
import com.yijiupi.himalaya.supplychain.allot.dto.stockout.StockOutItemApplyResultDTO;
import com.yijiupi.himalaya.supplychain.allot.service.IStockService;
import com.yijiupi.himalaya.supplychain.dto.OrderPrintDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrderItem;
import com.yijiupi.himalaya.supplychain.inventory.service.IInventoryOrderBizService;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.OrderCommonDetailDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.service.OrderCenterConvertService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.*;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.StockOrderStoreDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.StockOrderStoreItemDTO;
import com.yijiupi.himalaya.supplychain.service.IOrderPrintService;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productSourceCode.UpdateProductSourceCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductSourceCodeRecordSyncDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductSourceCodeRecordBusinessTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductSourceCodeRecordStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSourceService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderconstraint.OrderConstraintCheckBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderitemdetail.OutStockOrderItemDetailModBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderpallet.OrderLocationPalletBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation.OutStockLocationManager;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder.OutStockOrderItemQueryBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder.OutStockOrderStateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.MarkLackBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.*;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter.CompletePickNotifyDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter.ExpressDispatchNotifyDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter.OrderOutStockNotifyDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOrderInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOutStockDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.WaveNoItemModel;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemLackDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.PartSendWsmDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.*;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderSearchSO;
import com.yijiupi.himalaya.supplychain.waves.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;
import com.yijiupi.himalaya.utils.QuantityShareUtils;
import com.yijiupi.supplychain.serviceutils.constant.DeliveryOrderConstant;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;

/**
 * Created by 余明 on 2018-03-16.
 */
@Service
public class OutStockOrderBL {
    private static final Logger LOG = LoggerFactory.getLogger(OutStockOrderBL.class);
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private OutStockOrderItemDetailCommMapper outStockOrderItemDetailCommMapper;
    @Autowired
    private BatchOrderBL batchOrderBL;
    @Autowired
    private SowOrderMapper sowOrderMapper;
    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;
    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private ThirdPartyOutStockBL thirdPartyOutStockBL;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BoundBatchBL boundBatchBL;
    @Autowired
    private OrderCenterBL orderCenterBL;
    @Autowired
    private BatchOrderInfoBL batchOrderInfoBL;
    @Autowired
    private OutStockOrderItemQueryBL outStockOrderItemQueryBL;
    @Resource
    private OutStockLocationManager outStockLocationManager;
    @Reference
    private IInventoryOrderBizService inventoryOrderBizService;

    @Reference
    private IProductSkuService iProductSkuService;

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;
    @Reference
    private IInventoryOrderBizService iInventoryOrderBizService;
    @Reference
    private IStockService iStockService;
    @Reference
    private IVariableValueService variableValueService;
    @Reference
    private OrderCenterConvertService orderCenterConvertService;
    @Reference
    private IOrderPrintService iOrderPrintService;
    @Resource
    private OutStockOrderStateBL outStockOrderStateBL;
    @Resource
    private OrderConstraintCheckBL orderConstraintCheckBL;
    @Reference
    private IProductSourceService iProductSourceService;

    @Autowired
    private WavesStrategyBOConvertor wavesStrategyBOConvertor;
    @Autowired
    private OutStockOrderItemDetailModBL outStockOrderItemDetailModBL;
    @Autowired
    private OrderLocationPalletBL orderLocationPalletBL;

    /**
     * 根据条件筛选所有订单
     *
     * @param dto
     * @return
     */
    public List<OutStockOrderPO> findOutStockOrderPOList(OutStockOrderSearchSO dto) {
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findOutStockOrderPOList(dto);
        if (!CollectionUtils.isEmpty(outStockOrderPOList)) {
            List<Long> outstockorderIdList =
                outStockOrderPOList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
            List<OutStockOrderItemPO> byOutstockorderIdList =
                outStockOrderItemMapper.findByOutstockorderIdList(outstockorderIdList);
            if (!CollectionUtils.isEmpty(byOutstockorderIdList)) {
                Map<Long, List<OutStockOrderItemPO>> outStockOrderItemPOMap = byOutstockorderIdList.stream()
                    .collect(Collectors.groupingBy(OutStockOrderItemPO::getOutstockorderId));
                for (OutStockOrderPO outStockOrderPO : outStockOrderPOList) {
                    outStockOrderPO.setItems(outStockOrderItemPOMap.get(outStockOrderPO.getId()));
                }

            }

        }
        return outStockOrderPOList;
    }

    public void outStockTaskProcess(List<OutStockOrderNewDTO> outStockOrderNewDTOList) {
        // List<OutStockOrderNewPO> recordList = new ArrayList<>();
        // List<OutStockOrderItemNewPO> outStockOrderItemPOs = new ArrayList<>();
        // for (OutStockOrderNewDTO dto : outStockOrderNewDTOList) {
        // OutStockOrderNewPO record = new OutStockOrderNewPO();
        // BeanUtils.copyProperties(dto, record);
        // record.setId(UuidUtil.getUUidInt());
        // record.setSkuCount(dto.getItemList().size());
        // record.setOrg_Id(dto.getOrg_Id());
        // recordList.add(record);
        // for (OutStockOrderItemDTO itemDTO : dto.getItemList()) {
        // itemDTO.setUnitTotalCount(itemDTO.getPackageCount() * itemDTO.getSpecQuantity() + itemDTO.getUnitCount());
        // OutStockOrderItemNewPO po = new OutStockOrderItemNewPO();
        // BeanUtils.copyProperties(itemDTO, po);
        // po.setOutstockorder_Id(record.getId());
        // outStockOrderItemPOs.add(po);
        // }
        // }
        // outStockOrderMapper.insertList(recordList);
        // outStockOrderItemMapper.insertList(outStockOrderItemPOs);
    }

    public void checkOrderExits(String refOrderNo, Integer warehouseId) {
        OutStockOrderPO existsOrders = outStockOrderMapper.selectByRefOrderNo(refOrderNo, null, warehouseId);
        if (existsOrders != null && existsOrders.getReforderno() != null) {
            throw new BusinessException(refOrderNo + "出库单已存在!");
        }
    }

    public void outStockProcess(OutStockDTO outStockDTO) {
        LOG.info("出库单数据收集：{}", JSON.toJSONString(outStockDTO));
        // 根据SkuId获取ProductSpecificationId
        genProductSpecificationIdBySkuId(outStockDTO);
        List<OutStockOrderNewDTO> outStockOrderNewDTOList = outStockDTO.getOutStockOrderDTOList();
        List<OutStockOrderNewPO> recordList = new ArrayList<>();
        List<OutStockOrderItemNewPO> outStockOrderItemPOs = new ArrayList<>();
        for (OutStockOrderNewDTO dto : outStockOrderNewDTOList) {
            OutStockOrderNewPO record = new OutStockOrderNewPO();
            BeanUtils.copyProperties(dto, record);
            Long uUidInt = UuidUtil.getUUidInt();
            dto.setId(uUidInt);
            record.setId(uUidInt);
            record.setSkuCount(dto.getItemList().size());
            record.setOrg_Id(dto.getOrg_Id());
            if (StringUtils.isEmpty(dto.getFromCityId())) {
                record.setFromCityId(dto.getOrg_Id());
            }
            // 如果直接减库存，或者是第三方出库，状态变更为待调度。
            // 第三方出库，特殊处理
            if (dto.getOutStockOrderType() == OutStockOrderTypeEnum.第三方出库.getType()) {
                record.setState(OutStockOrderStateEnum.待调度.getType());
            } else {
                // 如果不更新仓库库存，只做移库处理，直接更新为已出库
                if (dto.getDirectChangeStock()
                    || (dto.getNotUpdateProductStore() != null && dto.getNotUpdateProductStore())) {
                    record.setState(OutStockOrderStateEnum.已出库.getType());
                    record.setOutStockTime(record.getOutStockTime() == null ? new Date() : record.getOutStockTime());
                    record.setOutStockUser(
                        record.getOutStockUser() == null ? record.getCreateuser() : record.getOutStockUser());
                } else {
                    record.setState(OutStockOrderStateEnum.待审核.getType());
                }
            }
            recordList.add(record);
            for (OutStockOrderItemDTO itemDTO : dto.getItemList()) {
                itemDTO.setUnitTotalCount(
                    itemDTO.getPackageCount().multiply(itemDTO.getSpecQuantity()).add(itemDTO.getUnitCount()));
                OutStockOrderItemNewPO po = new OutStockOrderItemNewPO();
                BeanUtils.copyProperties(itemDTO, po);
                po.setOutstockorder_Id(record.getId());
                outStockOrderItemPOs.add(po);
            }
        }
        outStockOrderMapper.insertList(recordList);
        outStockOrderItemPOs.forEach(p -> p.setId(UUIDGeneratorUtil.getUUID(OutStockOrderItemNewPO.class.getName())));
        outStockOrderItemMapper.insertList(outStockOrderItemPOs);
    }

    /**
     * 处理订单缺货
     */
    public void processOrderLack(String batchNo, Integer operatorUserId) {
        List<BatchPO> batchPOList = batchMapper.listByBatchNos(Arrays.asList(batchNo));
        if (CollectionUtils.isEmpty(batchPOList)) {
            throw new BusinessException("找不到波次：" + batchNo);
        }
        BatchPO batchPO = batchPOList.get(0);
        //
        // // 是否跳过缺货接口
        // Byte batchOrderType = batchPO.getBatchOrderType();
        // if (batchOrderType != null && !Objects.equals(batchOrderType, BatchOrderTypeEnum.普通订单.getType())) {
        // LOG.info("跳过缺货接口：{}", batchNo);
        // return;
        // }

        // 调用TMS标记缺货接口
        List<BatchTaskItemLackDTO> batchTaskItemLackDTOS =
            batchOrderBL.listLackItemByBatchNos(Collections.singletonList(batchNo));
        LOG.info("batchTaskItemLackDTOS ={}", JSON.toJSONString(batchTaskItemLackDTOS));
        // 排除所有播种任务中的缺货信息
        if (!CollectionUtils.isEmpty(batchTaskItemLackDTOS)) {
            /** 二次分拣，播种号改为空 */
            if (batchPO.getSowType() != null && batchPO.getSowType().equals((byte)2)) {
                batchTaskItemLackDTOS.stream().filter(it -> it.getSowTaskId() != null).forEach(it -> {
                    it.setSowTaskId(null);
                    it.setSowTaskNo(null);
                });
            }
            // 调用oms标记缺货
            markOrdreLack(batchTaskItemLackDTOS, batchNo, operatorUserId);
        }
    }

    /**
     * 处理订单出库位
     */
    @Deprecated
    public void processOrderLocation(String batchNo) {
        List<BatchPO> batchPOList = batchMapper.listByBatchNos(Arrays.asList(batchNo));
        if (CollectionUtils.isEmpty(batchPOList)) {
            throw new BusinessException("找不到波次：" + batchNo);
        }
        BatchPO batchPO = batchPOList.get(0);
        String batchId = batchPO.getId();

        // 1、更新完成拣货的出库单状态
        List<Long> lstOrderIds = outStockOrderMapper.findOutStockOrderIdsByBatchId(batchId);
        LOG.info("{}波次关联的订单id：{}", batchNo, JSON.toJSONString(lstOrderIds));
        lstOrderIds = updateStateByOrderIds(lstOrderIds, batchPO);
        LOG.info("{}完成拣货的出库单：{}", batchNo, JSON.toJSONString(lstOrderIds));

        // 2、完成拣货时通知TMS，波次下所有订单对应的出库位
        List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findTaskByBatchNo(Arrays.asList(batchNo));
        syncOrderToLocation(batchNo, batchTaskPOS, lstOrderIds);
    }

    /**
     * 标记缺货
     */
    public void markOrdreLack(List<BatchTaskItemLackDTO> batchTaskItemLackDTOS, String batchNo,
        Integer operatorUserId) {
        // 波次完成的标记缺货
        List<BatchTaskItemLackDTO> filterBatchTaskItemLackDTOS = batchTaskItemLackDTOS.stream()
            .filter(p -> StringUtils.isEmpty(p.getSowTaskNo())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(filterBatchTaskItemLackDTOS)) {
            // 查询缺货的拣货任务项关联的订单项
            List<String> batchTaskItemIds =
                filterBatchTaskItemLackDTOS.stream().map(p -> p.getBatchTaskItemId()).collect(Collectors.toList());
            List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
                orderItemTaskInfoMapper.listOrderItemTaskInfoByBatchTaskItemIds(batchTaskItemIds);

            // 查不到关联时，走老的缺货逻辑
            if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
                // - 获取波次中的所有订单编号
                List<String> refOrderNos = outStockOrderMapper.listRefOrderNoByBatchNo(batchNo);
                // 转换skuId
                batchOrderTaskBL.transferSkuId(refOrderNos, filterBatchTaskItemLackDTOS);
                LOG.info(String.format("标记缺货：%s, 排除播种任务后的缺货：%s, 订单编号：%s", JSON.toJSONString(batchTaskItemLackDTOS),
                    JSON.toJSONString(filterBatchTaskItemLackDTOS), JSON.toJSONString(refOrderNos)));
                omsMarkPartOld(filterBatchTaskItemLackDTOS, refOrderNos, batchNo, operatorUserId);
            } else {
                // 找到所有缺货的订单项
                List<Long> orderItemIds =
                    orderItemTaskInfoPOList.stream().filter(p -> p.getLackUnitCount().compareTo(BigDecimal.ZERO) > 0)
                        .map(p -> p.getRefOrderItemId()).distinct().collect(Collectors.toList());
                omsMarkPartNew(orderItemTaskInfoPOList, orderItemIds, operatorUserId);
            }
        }
    }

    /**
     * oms标记缺货老接口
     */
    @Deprecated
    public void omsMarkPartOld(List<BatchTaskItemLackDTO> filterBatchTaskItemLackDTOS, List<String> refOrderNos,
        String batchNo, Integer operatorUserId) {
        // 打印缺货数量不是销售规格的倍数日志信息
        printLackCountWarningLog(batchNo, filterBatchTaskItemLackDTOS);
        // 调用新的标记缺货接口
    }

    /**
     * oms标记缺货新接口
     */
    public void omsMarkPartNew(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, List<Long> orderItemIds,
        Integer operatorUserId) {
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            throw new BusinessException("找不到关联的订单项");
        }
        if (CollectionUtils.isEmpty(orderItemIds)) {
            LOG.info("所有缺货的订单项为空！");
            return;
        }
        // 找到缺货的订单项原始数量
        List<OrderItemTaskInfoPO> originOrderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderItemIds(orderItemIds);
        if (CollectionUtils.isEmpty(originOrderItemTaskInfoPOList)) {
            LOG.info("找不到缺货的订单项原始数量：{}", JSON.toJSONString(orderItemIds));
            return;
        }
        // 一个订单项生成多个拣货任务明细的时候，orderItemTaskInfoPOList 只有一个拣货任务的，如果其他做了缺货，这里就会少，需填充 orderItemTaskInfoPOList
        List<OrderItemTaskInfoPO> addOrderItemTaskInfoList =
            fillOrderItemTaskInfoList(originOrderItemTaskInfoPOList, orderItemTaskInfoPOList);
        if (!CollectionUtils.isEmpty(addOrderItemTaskInfoList)) {
            orderItemTaskInfoPOList.addAll(addOrderItemTaskInfoList);
        }

        List<OutStockOrderItemDTO> orderItemDTOS = outStockOrderItemMapper.findByItemIds(orderItemIds, null);
        if (CollectionUtils.isEmpty(orderItemDTOS)) {
            LOG.info("找不到缺货的订单项：{}", JSON.toJSONString(orderItemIds));
            return;
        }

        List<Long> outStockOrderIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findIdByIds(outStockOrderIds, null);
        Map<String, String> notMeiTuanOrderMap = orderConstraintCheckBL.getCanLackOrders(outStockOrderPOList).stream()
            .collect(Collectors.toMap(OutStockOrderPO::getBusinessId, OutStockOrderPO::getBusinessId));
        if (CollectionUtils.isEmpty(notMeiTuanOrderMap)) {
            LOG.info("都是美团订单，不能缺货");
            return;
        }

        // 判断是否启用订单中台
        // key businessId
        Map<String, StringBuffer> orderLackInfoDesMap = new HashMap<>();
        // 过滤出新流程调拨订单
        List<Long> businessIdList = orderItemDTOS.stream()
            .filter(p -> null != p.getOrderType() && !StringUtils.isEmpty(p.getBusinessId())
                && Objects.equals((byte)JiupiOrderTypeEnum.ORDER_TYPE_ALLOT, p.getOrderType()))
            .map(OutStockOrderItemDTO::getBusinessId).map(Long::parseLong).distinct().collect(Collectors.toList());

        List<PartSendWsmDTO> partSendWsmDTOList = new ArrayList<>();
        // 按订单分组
        Map<Long, List<OutStockOrderItemDTO>> orderItemMap =
            orderItemDTOS.stream().collect(Collectors.groupingBy(p -> p.getOutstockorder_Id()));
        orderItemMap.forEach((orderId, orderItemList) -> {
            if (CollectionUtils.isEmpty(orderItemList)) {
                return;
            }
            // 订单项发货数量Map<订单项ID,订单原始数量/或者发货数量>
            Map<Long, Integer> itemShipMap = new HashMap<>();
            // 数量明细Map<订单项ID,缺货或部分配送实际数据-编辑后数量>
            Map<Long, Integer> itemMap = new HashMap<>();

            // 判断订单类型是否是内配及中转订单
            boolean isTransfer = isTransfer(orderItemList.get(0).getAllotType());

            // 遍历订单项
            for (OutStockOrderItemDTO orderItem : orderItemList) {
                // 订单项关联数量
                List<OrderItemTaskInfoPO> oldInfoList = originOrderItemTaskInfoPOList.stream()
                    .filter(p -> Objects.equals(p.getRefOrderItemId(), orderItem.getId())).collect(Collectors.toList());
                if (StringUtils.isEmpty(orderItem.getBusinessItemId())) {
                    throw new BusinessException("缺货找不到订单项id：" + orderItem.getId());
                }
                // 订单项id
                Long omsOrderItemId = Long.valueOf(orderItem.getBusinessItemId());

                // 找出订单项对应的缺货数量
                List<OrderItemTaskInfoPO> lackList = orderItemTaskInfoPOList.stream()
                    .filter(p -> Objects.equals(p.getRefOrderItemId(), orderItem.getId())).collect(Collectors.toList());
                BigDecimal lackCount =
                    lackList.stream().map(p -> p.getLackUnitCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 缺货为负的跳过
                if (lackCount.compareTo(BigDecimal.ZERO) < 0) {
                    LOG.info("缺货数量为负{}", JSON.toJSONString(lackList));
                    return;
                }
                if (lackCount.divideAndRemainder(orderItem.getSaleSpecQuantity())[1].compareTo(BigDecimal.ZERO) != 0) {
                    LOG.info("缺货数量不是销售规格的倍数: {}, saleSpecQuantity: {}", JSON.toJSONString(orderItem),
                        orderItem.getSaleSpecQuantity());
                    throw new BusinessException("缺货数量不是销售规格的倍数");
                }
                // 订单中台 缺货消息发送为小单位数量
                // 订单原数量 FIXME
                BigDecimal oldUnitTotalCount = oldInfoList.stream().map(OrderItemTaskInfoPO::getOriginalUnitTotalCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                // oldInfoList.stream().map(p -> p.getUnitTotalCount().add(p.getMoveCount())).reduce(BigDecimal.ZERO,
                // BigDecimal::add);
                BigDecimal oldPackageCount = oldUnitTotalCount;
                itemShipMap.put(omsOrderItemId, oldPackageCount.intValue());

                // 缺货后的数量
                BigDecimal updateCount = oldPackageCount.subtract(lackCount);
                itemMap.put(omsOrderItemId, updateCount.intValue() < 0 ? 0 : updateCount.intValue());
            }

            if (itemMap.isEmpty() || itemShipMap.isEmpty()) {
                return;
            }
            addParSendWmsList(partSendWsmDTOList, itemShipMap, itemMap, operatorUserId,
                orderItemDTOS.get(0).getWarehouseId(), orderItemList.get(0), isTransfer);
            for (OutStockOrderItemDTO orderItemDTO : orderItemList) {
                String desc = lackDes(orderItemDTO, itemMap, itemShipMap);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(desc)) {
                    StringBuffer lackInfo =
                        orderLackInfoDesMap.getOrDefault(orderItemDTO.getBusinessId(), new StringBuffer());
                    lackInfo.append(desc);
                    orderLackInfoDesMap.put(orderItemDTO.getBusinessId(), lackInfo);
                }
            }
        });

        LOG.info("标记缺货omsMarkPartNew,缺货数据：{}", JSON.toJSONString(partSendWsmDTOList));

        List<PartSendWsmDTO> notMeiTuanPartSendWsmDTOList = partSendWsmDTOList.stream()
            .filter(dto -> Objects.nonNull(notMeiTuanOrderMap.get(dto.getBusinessId().toString())))
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(notMeiTuanPartSendWsmDTOList)) {
            LOG.info("都是美团订单，没必要缺货");
            return;
        }

        // 调用订单中台缺货通知
        if (!CollectionUtils.isEmpty(businessIdList)) {
            // 调拨拣货缺货
            invokeOrderCenter(notMeiTuanPartSendWsmDTOList.stream()
                .filter(p -> businessIdList.contains(p.getBusinessId())).collect(Collectors.toList()));
            // 处理调拨以外类型订单拣货缺货
            invokeOrderCenterByMark(notMeiTuanPartSendWsmDTOList.stream()
                .filter(p -> !businessIdList.contains(p.getBusinessId())).collect(Collectors.toList()),
                orderLackInfoDesMap, operatorUserId);
        } else {
            // 处理调拨以外类型订单拣货缺货
            invokeOrderCenterByMark(notMeiTuanPartSendWsmDTOList, orderLackInfoDesMap, operatorUserId);
        }

    }

    private List<OrderItemTaskInfoPO> fillOrderItemTaskInfoList(List<OrderItemTaskInfoPO> originOrderItemTaskInfoPOList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        try {
            // 一个订单项生成多个拣货任务明细的时候，orderItemTaskInfoPOList 只有一个拣货任务的，如果其他做了缺货，这里就会少，需填充 orderItemTaskInfoPOList
            Map<Long, List<OrderItemTaskInfoPO>> oriOrderItemTaskInfoGroupMap = originOrderItemTaskInfoPOList.stream()
                .collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId));
            List<OrderItemTaskInfoPO> addOrderItemTaskInfoList = new ArrayList<>();
            for (OrderItemTaskInfoPO orderItemTaskInfoPO : orderItemTaskInfoPOList) {
                List<OrderItemTaskInfoPO> tmpInfoList =
                    oriOrderItemTaskInfoGroupMap.get(orderItemTaskInfoPO.getRefOrderItemId());
                if (CollectionUtils.isEmpty(tmpInfoList)) {
                    continue;
                }
                List<OrderItemTaskInfoPO> filterInfoList = tmpInfoList.stream()
                    .filter(m -> !m.getId().equals(orderItemTaskInfoPO.getId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filterInfoList)) {
                    addOrderItemTaskInfoList.addAll(filterInfoList);
                }
            }

            return addOrderItemTaskInfoList;
        } catch (Exception e) {
            LOG.warn("报错，", e);
            return Collections.emptyList();
        }
    }

    // 灰度版本缺货传的小单位数量，非灰度传的是销售规格数量
    private String lackDes(OutStockOrderItemDTO orderItem, Map<Long, Integer> itemMap, Map<Long, Integer> itemShipMap) {
        Integer oriCount = itemShipMap.get(Long.valueOf(orderItem.getBusinessItemId()));
        Integer markCount = itemMap.get(Long.valueOf(orderItem.getBusinessItemId()));

        if (Objects.isNull(oriCount) || Objects.isNull(markCount)) {
            return "";
        }
        int lackCount = oriCount - markCount;

        return orderItem.getProductName().concat(" 缺货 ").concat(Integer.toString(lackCount))
            .concat(orderItem.getUnitName());
    }

    /**
     * 判断订单类型是否是内配及中转订单
     *
     * @return
     */
    private boolean isTransfer(Byte allotType) {
        boolean isTransfer = false;
        if (Objects.equals(allotType, OutStockOrderBusinessType.中转订单.getType())
            || Objects.equals(allotType, OutStockOrderBusinessType.中转退订单.getType())
            || Objects.equals(allotType, OutStockOrderBusinessType.内配订单.getType())
            || Objects.equals(allotType, OutStockOrderBusinessType.内配退订单.getType())) {
            isTransfer = true;
        }
        return isTransfer;
    }

    /**
     * 封装缺货参数
     */
    private void addParSendWmsList(List<PartSendWsmDTO> partSendWsmDTOList, Map<Long, Integer> itemShipMap,
        Map<Long, Integer> itemMap, Integer operatorUserId, Integer warehouseId, OutStockOrderItemDTO orderItemDTO,
        boolean isTransfer) {
        // 订单类型如果是中转订单，需要转化成oms订单id

        if (StringUtils.isEmpty(orderItemDTO.getBusinessId())) {
            throw new BusinessException("缺货找不到订单id：" + orderItemDTO.getId());
        }
        Long omsOrderId = Long.valueOf(orderItemDTO.getBusinessId());
        PartSendWsmDTO partSendWsmDTO = new PartSendWsmDTO();
        partSendWsmDTO.setBusinessId(omsOrderId);
        partSendWsmDTO.setOrderNo(orderItemDTO.getRefOrderNo());
        partSendWsmDTO.setUserId(operatorUserId);
        partSendWsmDTO.setItemShipMap(itemShipMap);
        partSendWsmDTO.setItemMap(itemMap);
        // 拣货缺货标识
        Map<Object, Object> extendMap = new HashMap<>();
        extendMap.put(OrderExtendMapEnum.拣货缺货.getType(), warehouseId);
        partSendWsmDTO.setExtendMap(extendMap);
        partSendWsmDTOList.add(partSendWsmDTO);
    }

    /**
     * 调用oms缺货接口
     */
    @Deprecated
    private void invokeOMS(List<PartSendWsmDTO> partSendWsmDTOList, Integer warehouseId) {
        if (CollectionUtils.isEmpty(partSendWsmDTOList)) {
            LOG.info("标记缺货参数为空直接跳过");
            return;
        }
        partSendWsmDTOList.forEach(m -> {
            if (Objects.isNull(m.getUserId())) {
                m.setUserId(1);
            }
        });
        LOG.info("[markByWms]标记缺货参数：{}", JSON.toJSONString(partSendWsmDTOList));
    }

    /**
     * 调用订单中台缺货通知接口
     */
    @Deprecated
    private void invokeOrderCenter(List<PartSendWsmDTO> partSendWsmDTOList) {
        if (CollectionUtils.isEmpty(partSendWsmDTOList)) {
            LOG.info("订单中台标记缺货参数为空直接跳过");
            return;
        }
        partSendWsmDTOList.forEach(m -> {
            if (Objects.isNull(m.getUserId())) {
                m.setUserId(1);
            }
        });
        LOG.info("[markByWms]订单中台标记缺货参数：{}", JSON.toJSONString(partSendWsmDTOList));
        orderCenterBL.orderLackNotify(partSendWsmDTOList);
    }

    /**
     * 调用订单中台订单标记通知接口
     */
    private void invokeOrderCenterByMark(List<PartSendWsmDTO> partSendWsmDTOList, Map<String, StringBuffer> bufferMap,
        Integer operatorUserId) {
        if (CollectionUtils.isEmpty(partSendWsmDTOList)) {
            LOG.info("invokeOrderCenterByMark订单中台标记缺货参数为空直接跳过");
            return;
        }
        LOG.info("[invokeOrderCenterByMark]订单中台标记缺货参数：{}；描述信息：{}", JSON.toJSONString(partSendWsmDTOList),
            JSON.toJSONString(bufferMap));
        orderCenterBL.orderMarkNotify(new MarkLackBO(partSendWsmDTOList, bufferMap, operatorUserId));
    }

    /**
     * 有播种任务的拣货任务项实时标记缺货
     */
    public void markLackBySowTask(List<String> batchTaskItemIds, Map<Long, BigDecimal> lastTaskInfoLackCountMap,
        Integer operatorUserId) {
        if (CollectionUtils.isEmpty(batchTaskItemIds) || lastTaskInfoLackCountMap == null) {
            return;
        }
        // 查找有播种任务的拣货任务项id
        batchTaskItemIds = batchMapper.getIncludeSowTaskBatchTaskItemIds(batchTaskItemIds);
        if (CollectionUtils.isEmpty(batchTaskItemIds)) {
            return;
        }
        LOG.info("[播种任务的拣货任务标记缺货]记录上次缺货数量:{}", JSON.toJSONString(lastTaskInfoLackCountMap));

        // 1、找出本次缺货数量与上次缺货数量有差异的taskInfo找出本次缺货数量与上次缺货数量有差异的taskInfo
        Map<Long, BigDecimal> diffLackCountMap = new HashMap<>();
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByBatchTaskItemIds(batchTaskItemIds);
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return;
        }
        orderItemTaskInfoPOList.forEach(p -> {
            // 上次缺货数量
            BigDecimal lastLackCount = lastTaskInfoLackCountMap.get(p.getId());
            if (lastLackCount != null && p.getLackUnitCount() != null) {
                // 差异数量 = 本次缺货数量 - 上次缺货数量
                BigDecimal diffCount = p.getLackUnitCount().subtract(lastLackCount);
                if (diffCount.compareTo(BigDecimal.ZERO) != 0) {
                    diffLackCountMap.put(p.getId(), diffCount);
                }
            }
        });
        LOG.info("[播种任务的拣货任务标记缺货]记录缺货差异数量:{}", JSON.toJSONString(diffLackCountMap));
        if (diffLackCountMap.size() == 0) {
            return;
        }

        // 2、缺货数量有差异需要调用缺货接口
        // 找到需要缺货的订单项
        List<Long> orderItemIds = orderItemTaskInfoPOList.stream().filter(p -> diffLackCountMap.containsKey(p.getId()))
            .map(p -> p.getRefOrderItemId()).distinct().collect(Collectors.toList());
        omsMarkPartNew(orderItemTaskInfoPOList, orderItemIds, operatorUserId);
    }

    /**
     * 根据波次通知TMS更新出库位
     */
    public void syncOrderToLocationByScop(String batchNo) {
        AssertUtils.notNull(batchNo, "波次编号不能为空");

        BatchPO batchPO = batchMapper.selectBatchByBatchNo(null, batchNo);
        if (batchPO == null) {
            throw new BusinessException("找不到波次！");
        }
        LOG.info("根据波次通知TMS更新出库位：{}", batchNo);
        List<Long> lstOrderIds = outStockOrderMapper.findOutStockOrderIdsByBatchId(batchPO.getId());
        List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findTaskByBatchNo(Arrays.asList(batchNo));
        syncOrderToLocation(batchNo, batchTaskPOS, lstOrderIds);
    }

    /**
     * 完成拣货时通知TMS或订单中台，波次下所有订单对应的出库位 <br/>
     * FIXME 这里有其他逻辑，竟然还更新了orderItemDetail,现在调用的地方太多，先简单的拆成两个接口
     */
    public void syncOrderToLocation(String batchNo, List<BatchTaskPO> batchTaskPOS, List<Long> lstOrderIds) {
        if (CollectionUtils.isEmpty(lstOrderIds)) {
            return;
        }
        // 查询出库单
        List<OutStockOrderPO> orderPOList = outStockOrderMapper.listOutStockOrderByOrderId(lstOrderIds);
        // 查询订单项与拣货任务项关联
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderIds(lstOrderIds);
        if (CollectionUtils.isEmpty(orderPOList)) {
            return;
        }
        pureSyncOrderToLocation(orderPOList, orderItemTaskInfoPOList, batchNo, batchTaskPOS);
        // 更新出库单项detail
        LOG.info("[波次完成]更新出库单项detail:{}", JSON.toJSONString(orderItemTaskInfoPOList));
        updateOutStockOrderItemDetail(orderItemTaskInfoPOList);
    }

    /**
     * 通知tms更新出库位重载方法，不处理detail数据
     * 
     * @param batchNo
     * @param batchTaskPOS
     * @param lstOrderIds
     */
    public void pureSyncOrderToLocation(String batchNo, List<BatchTaskPO> batchTaskPOS, List<Long> lstOrderIds) {
        if (CollectionUtils.isEmpty(lstOrderIds)) {
            return;
        }
        // 查询出库单
        List<OutStockOrderPO> orderPOList = outStockOrderMapper.listOutStockOrderByOrderId(lstOrderIds);
        // 查询订单项与拣货任务项关联
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderIds(lstOrderIds);
        if (CollectionUtils.isEmpty(orderPOList)) {
            return;
        }
        pureSyncOrderToLocation(orderPOList, orderItemTaskInfoPOList, batchNo, batchTaskPOS);
    }

    /**
     * 单纯更新出库位
     * 
     * @param orderPOList
     * @param orderItemTaskInfoPOList
     * @param batchNo
     * @param batchTaskPOS
     */
    public void pureSyncOrderToLocation(List<OutStockOrderPO> orderPOList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, String batchNo, List<BatchTaskPO> batchTaskPOS) {
        Integer warehouseId = orderPOList.get(0).getWarehouseId();

        List<CompletePickNotifyDTO> pickNotifyList = new ArrayList<>();
        List<WaveNoItemModel> waveNoItemList = new ArrayList<>();

        boolean isNeedPallet = wavesStrategyBOConvertor.isNeedPallet(warehouseId);
        // 订单货位托盘关系数据
        List<Long> orderIdList = orderPOList.stream().filter(p -> p.getId() != null).map(p -> p.getId()).distinct()
            .collect(Collectors.toList());
        Map<Long, String> palletMap = getOrderLocationPalletMap(warehouseId, orderIdList);

        orderPOList.forEach(order -> {
            if (CollectionUtils.isEmpty(order.getItems())) {
                return;
            }
            Long locationId = null;
            Set<String> locationNameSet = new HashSet<>();

            // 1、取拣货任务中的出库位
            Set<String> batchTaskNoList = new HashSet<>();
            // (1) 先通过订单项与拣货任务项的关联表找到该订单对应的所有的拣货任务
            if (!CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
                List<Long> orderItemIds = order.getItems().stream().map(p -> p.getId()).collect(Collectors.toList());
                List<String> batchTaskNoS =
                    orderItemTaskInfoPOList.stream().filter(p -> orderItemIds.contains(p.getRefOrderItemId()))
                        .map(p -> p.getBatchTaskNo()).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(batchTaskNoS)) {
                    batchTaskNoList.addAll(batchTaskNoS);
                }
            }
            // (2) 然后去找订单项中的拣货任务（兼容旧数据）
            List<String> batchTaskNoS = order.getItems().stream().filter(p -> !StringUtils.isEmpty(p.getBatchtaskno()))
                .map(p -> p.getBatchtaskno()).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(batchTaskNoS)) {
                batchTaskNoList.addAll(batchTaskNoS);
            }

            // 去匹配拣货任务中的出库位信息
            if (!CollectionUtils.isEmpty(batchTaskNoList) && !CollectionUtils.isEmpty(batchTaskPOS)) {
                List<BatchTaskPO> filterList = batchTaskPOS.stream()
                    .filter(p -> batchTaskNoList.contains(p.getBatchTaskNo()) && p.getToLocationId() != null)
                    .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filterList)) {
                    locationId = filterList.get(0).getToLocationId();
                    filterList.forEach(batchTaskPO -> {
                        locationNameSet.add(batchTaskPO.getToLocationName());
                    });
                }
            }

            // 2、取订单项中的出库位 如果开启打印容器位配置则继续添加订单项的出库位
            if (containerConfiguration(order) || CollectionUtils.isEmpty(locationNameSet)) {
                List<OutStockOrderItemPO> outStockOrderItemList = order.getItems().stream()
                    .filter(p -> p.getLocationId() != null && !StringUtils.isEmpty(p.getLocationName()))
                    .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(outStockOrderItemList)) {
                    if (locationId == null) {
                        locationId = outStockOrderItemList.get(0).getLocationId();
                    }
                    outStockOrderItemList.forEach(outStockOrderItemPO -> {
                        locationNameSet.add(outStockOrderItemPO.getLocationName());
                    });
                }
            }

            if (locationId == null || CollectionUtils.isEmpty(locationNameSet)) {
                LOG.info("订单找不到出库位：{}", order.getReforderno());
            }

            // 货位名称
            List<String> locationNameList = new ArrayList<>(locationNameSet);

            // 分拣员信息
            List<Integer> sorterIdList = new ArrayList<>();
            List<String> sorterNameList = getBatchTaskSorter(batchTaskPOS, batchTaskNoList, order, sorterIdList);

            CompletePickNotifyDTO pickNotifyDTO = orderCenterBL.toCompletePickNotifyDTO(order);
            if (pickNotifyDTO != null) {
                if (!CollectionUtils.isEmpty(sorterNameList)) {
                    pickNotifyDTO.setStevedoreUserName(sorterNameList.get(0));
                    pickNotifyDTO
                        .setStevedoreUserId(CollectionUtils.isEmpty(sorterIdList) ? null : sorterIdList.get(0));
                }
                if (!CollectionUtils.isEmpty(locationNameList)) {
                    String palletNo =
                        !StringUtils.isEmpty(palletMap.get(order.getId())) ? palletMap.get(order.getId()) : "";
                    String defaultLocationName =
                        isNeedPallet ? locationNameList.get(0) + palletNo : locationNameList.get(0);
                    pickNotifyDTO.setDefaultLocationName(defaultLocationName);
                    pickNotifyDTO.setDefaultLocationId(locationId);
                }

                List<BatchTaskPO> sorterBatchTaskPOList = batchTaskPOS.stream()
                    .filter(taskElem -> batchTaskNoList.contains(taskElem.getBatchTaskNo())
                        && (Objects.nonNull(taskElem.getCompleteUserId())) || Objects.nonNull(taskElem.getSorterId()))
                    .collect(Collectors.toList());

                BatchTaskPO taskComplete =
                    batchTaskPOS.stream().filter(taskElem -> batchTaskNoList.contains(taskElem.getBatchTaskNo())
                        && taskElem.getCompleteUserId() != null).findFirst().orElse(null);
                pickNotifyDTO.setOptUserId(taskComplete != null && taskComplete.getCompleteUserId() != null
                    ? String.valueOf(taskComplete.getCompleteUserId()) : "1");
                if (!CollectionUtils.isEmpty(sorterBatchTaskPOList)) {
                    List<String> tmpSorterNameList = sorterBatchTaskPOList.stream().map(this::getSorterName)
                        .filter(org.apache.commons.lang3.StringUtils::isNotBlank).distinct()
                        .collect(Collectors.toList());

                    pickNotifyDTO.setPickUserNameList(tmpSorterNameList);
                }
                pickNotifyList.add(pickNotifyDTO);
            }

            order.setLocationNameList(locationNameList);
        });

        // 这里原来有更新orderItemDetail接口，现在转移出去，日后整体重构

        if (CollectionUtils.isEmpty(waveNoItemList) && CollectionUtils.isEmpty(pickNotifyList)) {
            LOG.warn("波次找不到订单：{}", batchNo);
            return;
        }

        // 更新出库批次的出库位
        boundBatchBL.sendUpdateOutLocationMsg(orderPOList);

        // 通知订单中台
        if (!CollectionUtils.isEmpty(pickNotifyList)) {
            orderCenterBL.completePickNotify(pickNotifyList);
        }
    }

    private String getSorterName(BatchTaskPO batchTaskPO) {
        if (!StringUtils.isEmpty(batchTaskPO.getCompleteUser())) {
            return batchTaskPO.getCompleteUser();
        }
        if (!StringUtils.isEmpty(batchTaskPO.getSorter())) {
            return batchTaskPO.getSorter();
        }

        return null;
    }

    /***
     * 拼接出分拣员名称，用，分割
     *
     * @param batchTaskPOS 波次任务列表
     * @param batchTaskNoList 波次任务编码
     * @param order 订单
     * @return 分拣员名称
     */
    private static List<String> getBatchTaskSorter(List<BatchTaskPO> batchTaskPOS, Set<String> batchTaskNoList,
        OutStockOrderPO order, List<Integer> sorterIdList) {
        if (CollectionUtils.isEmpty(batchTaskNoList) || CollectionUtils.isEmpty(batchTaskPOS)) {
            return null;
        }

        List<BatchTaskPO> filterList =
            batchTaskPOS.stream().filter(taskElem -> batchTaskNoList.contains(taskElem.getBatchTaskNo())
                && taskElem.getSorterId() != null && taskElem.getSorter() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            return null;
        }

        Set<String> sorterNameSet = new HashSet<>();
        Set<Integer> sorterIdSet = new HashSet<>();
        filterList.forEach(taskElem -> {
            sorterNameSet.add(taskElem.getSorter());
            sorterIdSet.add(taskElem.getSorterId());
        });

        if (CollectionUtils.isEmpty(sorterNameSet)) {
            LOG.info("订单找不到分拣员：{}", order.getReforderno());
            return null;
        }

        sorterIdList = new ArrayList<>(sorterIdSet);
        return new ArrayList<>(sorterNameSet);
    }

    /**
     * 更新出库单项detail
     */
    public void updateOutStockOrderItemDetail(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return;
        }
        List<OutStockOrderItemDetailPO> addOrderItemDetailList = new ArrayList<>();

        // 按订单项id分组
        Map<Long, List<OrderItemTaskInfoPO>> itemTaskInfoMap =
            orderItemTaskInfoPOList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId));

        // 查询订单项详情
        List<Long> itemIds =
            orderItemTaskInfoPOList.stream().map(p -> p.getRefOrderItemId()).distinct().collect(Collectors.toList());
        List<OutStockOrderItemPO> orderItemPOList = outStockOrderItemQueryBL.findOutStockOrderItemList(itemIds);
        if (CollectionUtils.isEmpty(orderItemPOList)) {
            LOG.info("查询订单项为空：{}", JSON.toJSONString(itemIds));
            return;
        }

        Integer orgId = orderItemPOList.get(0).getOrgId();
        List<Long> outStockOrderIds = orderItemPOList.stream().map(OutStockOrderItemPO::getOutstockorderId).distinct()
            .collect(Collectors.toList());

        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.listOrderByIds(outStockOrderIds, orgId, null);
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            LOG.info("查询订单为空：{}", JSON.toJSONString(outStockOrderPOList));
            return;
        }
        List<OutStockOrderItemPO> notMeiTuanOrderItemPOList =
            orderConstraintCheckBL.getCanLackOrderItems(orderItemPOList, outStockOrderPOList);
        if (CollectionUtils.isEmpty(notMeiTuanOrderItemPOList)) {
            return;
        }
        itemTaskInfoMap.forEach((orderItemId, taskInfoList) -> {
            if (CollectionUtils.isEmpty(taskInfoList)) {
                LOG.info("订单项找不到关联的拣货任务项明细：{}", orderItemId);
                return;
            }

            Optional<OutStockOrderItemPO> optionalItemPO =
                notMeiTuanOrderItemPOList.stream().filter(p -> Objects.equals(p.getId(), orderItemId)).findFirst();
            if (!optionalItemPO.isPresent()) {
                LOG.info("订单项找不到：{}", orderItemId);
                return;
            }

            OutStockOrderItemPO itemPO = optionalItemPO.get();
            // 2022-04-24 订单项数量已经是0的，Detail不删除，增加一个0
            if (itemPO.getUnittotalcount().compareTo(BigDecimal.ZERO) <= 0) {
                taskInfoList.forEach(p -> {
                    p.setUnitTotalCount(BigDecimal.ZERO);
                });
            } else {
                BigDecimal taskDetailCount =
                    taskInfoList.stream().filter(dt -> dt != null && dt.getUnitTotalCount() != null)
                        .map(dt -> dt.getUnitTotalCount()).reduce(BigDecimal.ZERO, BigDecimal::add);

                if (taskDetailCount.compareTo(itemPO.getUnittotalcount()) != 0) {
                    LOG.info("更新出库单项detail，item与Detail数量不一致！item:{}，taskDetail：{}", JSON.toJSONString(itemPO),
                        JSON.toJSONString(taskInfoList));
                }
            }

            // 1、统计不同二级货主对应的数量
            Map<Long, BigDecimal> secOwnerIdCountMap = new HashMap<>();
            taskInfoList.stream().filter(p -> !CollectionUtils.isEmpty(p.getDetailList()))
                .flatMap(p -> p.getDetailList().stream()).forEach(p -> {
                    BigDecimal count = p.getUnitTotalCount();
                    Long secOwnerId = p.getSecOwnerId();
                    if (secOwnerIdCountMap.containsKey(secOwnerId)) {
                        count = count.add(secOwnerIdCountMap.get(secOwnerId));
                    }
                    secOwnerIdCountMap.put(secOwnerId, count);
                });

            List<OutStockOrderItemDetailPO> tmpOrderItemDetailList = new ArrayList<>();
            // 2、根据二级货主分配数量更新订单项detail
            secOwnerIdCountMap.forEach((secOwnerId, count) -> {
                OutStockOrderItemDetailPO detailPO = new OutStockOrderItemDetailPO();
                detailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.OUT_STOCK_ORDER_ITEM_DETAIL));
                detailPO.setOutStockOrderItemId(orderItemId);
                detailPO.setOrgId(orgId);
                detailPO.setProductSpecificationId(itemPO.getProductSpecificationId());
                detailPO.setOwnerId(itemPO.getOwnerId());
                detailPO.setSecOwnerId(secOwnerId);
                detailPO.setUnitTotalCount(count);
                tmpOrderItemDetailList.add(detailPO);
            });

            List<OutStockOrderItemDetailPO> finalOrderItemDetailList =
                handleNegativeCountDetail(tmpOrderItemDetailList);

            if (!CollectionUtils.isEmpty(finalOrderItemDetailList)) {
                addOrderItemDetailList.addAll(finalOrderItemDetailList);
            }

        });

        // try {
        // List<Long> couldRestOrderItemIds = notMeiTuanOrderItemPOList.stream().map(OutStockOrderItemPO::getId)
        // .distinct().collect(Collectors.toList());
        // List<OutStockOrderItemDetailPO> existDetailPOList =
        // outStockOrderItemDetailCommMapper.findByItemIds(orgId, couldRestOrderItemIds);
        //
        // outStockOrderItemDetailModBL.insertOrUpdateBatch(existDetailPOList, addOrderItemDetailList);
        // } catch (Exception e) {
        // LOG.error("波次完成，修改detail，走修改逻辑报错 ， 入参 ：" + JSON.toJSONString(addOrderItemDetailList), e);
        // throw new BusinessException("波次完成，修改货主信息异常！");
        // 下面是老逻辑
        if (!CollectionUtils.isEmpty(addOrderItemDetailList)) {
            List<Long> orderItemIds = addOrderItemDetailList.stream()
                .map(OutStockOrderItemDetailPO::getOutStockOrderItemId).distinct().collect(Collectors.toList());
            List<Long> deleteOrderItemDetailIdList = outStockOrderItemDetailCommMapper.listByOrderItemIds(orderItemIds);
            // 先删后增
            if (!CollectionUtils.isEmpty(deleteOrderItemDetailIdList)) {
                int deleteCount =
                    outStockOrderItemDetailCommMapper.deleteByItemDetailIds(deleteOrderItemDetailIdList, orgId);
                if (deleteCount != deleteOrderItemDetailIdList.size()) {
                    LOG.info(String.format("Detail删除数量异常，删除：%s / %s", deleteCount, deleteOrderItemDetailIdList.size()));
                }
                LOG.info("删除订单项detail：{}", JSON.toJSONString(deleteOrderItemDetailIdList));
            }

            outStockOrderItemDetailCommMapper.insertOrUpdateBatch(addOrderItemDetailList);
            LOG.info("新增订单项detail：{}", JSON.toJSONString(addOrderItemDetailList));
            // }
        }
    }

    /**
     * 处理detail存在负数的
     *
     * @param tmpOrderItemDetailList
     * @return
     */
    private List<OutStockOrderItemDetailPO>
        handleNegativeCountDetail(List<OutStockOrderItemDetailPO> tmpOrderItemDetailList) {
        if (CollectionUtils.isEmpty(tmpOrderItemDetailList)) {
            return tmpOrderItemDetailList;
        }

        List<OutStockOrderItemDetailPO> copyDetailList = new ArrayList<>();
        tmpOrderItemDetailList.forEach(detail -> {
            OutStockOrderItemDetailPO copyDetail = new OutStockOrderItemDetailPO();
            BeanUtils.copyProperties(detail, copyDetail);
            copyDetailList.add(copyDetail);
        });

        try {
            boolean anySmallThanZero =
                tmpOrderItemDetailList.stream().anyMatch(m -> m.getUnitTotalCount().compareTo(BigDecimal.ZERO) < 0);
            if (BooleanUtils.isFalse(anySmallThanZero)) {
                return tmpOrderItemDetailList;
            }

            List<OutStockOrderItemDetailPO> positiveDetailList = tmpOrderItemDetailList.stream()
                .filter(m -> m.getUnitTotalCount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(positiveDetailList)) {
                LOG.error("波次完成，处理detail时，detail没有正的参数, 参数 {}. 全部置为0", JSON.toJSONString(tmpOrderItemDetailList));
                tmpOrderItemDetailList.forEach(m -> m.setUnitTotalCount(BigDecimal.ZERO));
                return tmpOrderItemDetailList;
            }

            List<OutStockOrderItemDetailPO> negativeDetailList = tmpOrderItemDetailList.stream()
                .filter(m -> m.getUnitTotalCount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());

            BigDecimal negativeDetailCount = negativeDetailList.stream()
                .map(OutStockOrderItemDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal positiveDetailCount = positiveDetailList.stream()
                .map(OutStockOrderItemDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);

            if (positiveDetailCount.compareTo(negativeDetailCount.abs()) < 0) {
                LOG.error("波次完成，处理detail时，detail数量分摊小于0, 参数 {}. 全部置为0", JSON.toJSONString(tmpOrderItemDetailList));
                tmpOrderItemDetailList.forEach(m -> m.setUnitTotalCount(BigDecimal.ZERO));
                return tmpOrderItemDetailList;
            }

            List<QuantityShareUtils.CountHelper> helperList = positiveDetailList.stream()
                .map(m -> new QuantityShareUtils.CountHelper(m.getId().toString(), m.getUnitTotalCount()))
                .collect(Collectors.toList());
            QuantityShareUtils.CountShareResultHelper countShareResultHelper =
                QuantityShareUtils.shareCount(helperList, negativeDetailCount.abs());

            Map<String, OutStockOrderItemDetailPO> orderItemTaskInfoPOMap =
                positiveDetailList.stream().collect(Collectors.toMap(k -> k.getId().toString(), v -> v));

            countShareResultHelper.getShareHelperList().forEach(share -> {
                OutStockOrderItemDetailPO orderItemTaskInfoPO = orderItemTaskInfoPOMap.get(share.getId());
                orderItemTaskInfoPO.setUnitTotalCount(share.getCount());
            });
            negativeDetailList.forEach(m -> m.setUnitTotalCount(BigDecimal.ZERO));
            return tmpOrderItemDetailList;
        } catch (Exception e) {
            LOG.error("波次完成，处理detail时，处理负的orderItemDetail失败，入参 " + JSON.toJSONString(tmpOrderItemDetailList), e);
            return copyDetailList;
        }
    }

    /**
     * 打印缺货数量不是销售规格的倍数日志信息
     */
    public void printLackCountWarningLog(String batchNo, List<BatchTaskItemLackDTO> filterBatchTaskItemLackDTOS) {
        try {
            if (CollectionUtils.isEmpty(filterBatchTaskItemLackDTOS)) {
                return;
            }
            Map<String,
                List<BatchTaskItemLackDTO>> lackMap = filterBatchTaskItemLackDTOS.stream()
                    .collect(Collectors.groupingBy(p -> String.format("orderNo=%s|skuId=%s|saleSpec=%s",
                        p.getRefOrderNo(), p.getSkuId(), p.getSaleSpecQuantity())));
            if (null == lackMap) {
                return;
            }
            lackMap.forEach((k, list) -> {
                BigDecimal lacktUnitTotolCount =
                    list.stream().map(p -> p.getLackUnitCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal saleSpecQuantity = list.get(0).getSaleSpecQuantity();
                if (lacktUnitTotolCount.divideAndRemainder(saleSpecQuantity)[1].compareTo(BigDecimal.ZERO) != 0) {
                    LOG.info("[{}]缺货数量不是销售规格的倍数：{}", batchNo, k);
                }
            });
        } catch (Exception e) {
            LOG.info("打印缺货数量不是销售规格的倍数日志报错", e);
        }
    }

    /**
     * 获取波次下不包含播种任务的订单编号
     *
     * @return
     */
    private List<String> getRefOrderNoList(List<BatchTaskItemLackDTO> batchTaskItemLackDTOS, String batchNo) {
        // - 1.找到该波次中的所有订单编号
        List<String> refOrderNos = outStockOrderMapper.listRefOrderNoByBatchNo(batchNo);
        // - 2.找到播种任务关联的订单编号，并且过滤掉
        List<String> sowTaskNos = batchTaskItemLackDTOS.stream().filter(p -> !StringUtils.isEmpty(p.getSowTaskNo()))
            .map(p -> p.getSowTaskNo()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sowTaskNos)) {
            List<String> refOrderNoBySowTask = sowOrderMapper.listRefOrderNoBySowTaskNo(sowTaskNos);
            LOG.info(String.format("播种任务中的订单编号：%s", JSON.toJSONString(refOrderNoBySowTask)));
            if (!CollectionUtils.isEmpty(refOrderNoBySowTask)) {
                refOrderNos =
                    refOrderNos.stream().filter(p -> !refOrderNoBySowTask.contains(p)).collect(Collectors.toList());
            }
        }
        return refOrderNos;
    }

    /**
     * 修改微酒订单状态
     */
    public void updateOrderByWine(List<Long> orderIds, String batchId, String batchNo, Byte state) {
        // 修改微酒订单状态
        InStockWavesUpdateDTO inStockWavesUpdateDTO = new InStockWavesUpdateDTO();
        inStockWavesUpdateDTO.setIds(orderIds.stream().map(p -> p.toString()).collect(Collectors.toList()));
        inStockWavesUpdateDTO.setBatchId(batchId);
        inStockWavesUpdateDTO.setBatchNO(batchNo);
        inStockWavesUpdateDTO.setBatchTaskState(state);
        iStockService.updateBatchInStockOutApply(inStockWavesUpdateDTO);
        LOG.info("修改微酒订单状态: {}", JSON.toJSONString(inStockWavesUpdateDTO));
    }

    /**
     * 更新完成拣货的出库单状态 FIXME
     */
    public List<Long> updateStateByOrderIds(List<Long> lstOrderIds, BatchPO batchPO) {
        if (batchPO == null) {
            throw new BusinessException("波次不能为空");
        }
        if (CollectionUtils.isEmpty(lstOrderIds)) {
            return Collections.emptyList();
        }

        // 微酒
        if (Objects.equals(batchPO.getBatchType(), BatchTypeEnum.微酒.getType())) {
            updateOrderByWine(lstOrderIds, batchPO.getId(), batchPO.getBatchNo(), TaskStateEnum.已完成.getType());
            // 第三方
        } else if (BatchTypeEnum.isApplyOrderBatch(batchPO.getBatchType())) {
            thirdPartyOutStockBL.updateOrderByThirdParty(lstOrderIds, batchPO.getId(), batchPO.getBatchNo(),
                TaskStateEnum.已完成.getType());
            // 酒批
        } else {
            // 过滤订单
            if (batchPO.getBatchOrderType() == null
                || Objects.equals(batchPO.getBatchOrderType(), BatchOrderTypeEnum.普通订单.getType())) {
                // 1、过滤掉没有生成拣货任务的出库单id
                List<Long> noBatchTaskOrderIds = outStockOrderItemMapper.listOrderIdNoBatchTask(lstOrderIds);
                if (!CollectionUtils.isEmpty(noBatchTaskOrderIds)) {
                    LOG.info("未全部生成拣货任务的出库单id：{}", JSON.toJSONString(noBatchTaskOrderIds));
                    lstOrderIds =
                        lstOrderIds.stream().filter(p -> !noBatchTaskOrderIds.contains(p)).collect(Collectors.toList());
                }

                // 2、过滤掉没有完成拣货的出库单id
                if (!CollectionUtils.isEmpty(lstOrderIds)) {
                    List<Long> noPickOrderIds = outStockOrderItemMapper.listOrderIdNoPick(lstOrderIds);
                    if (!CollectionUtils.isEmpty(noPickOrderIds)) {
                        LOG.info("未完成拣货的出库单id：{}", JSON.toJSONString(noPickOrderIds));
                        lstOrderIds =
                            lstOrderIds.stream().filter(p -> !noPickOrderIds.contains(p)).collect(Collectors.toList());
                    }
                }
            }

            if (CollectionUtils.isEmpty(lstOrderIds)) {
                return lstOrderIds;
            }

            List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findIdByIds(lstOrderIds, null);
            List<Long> updateOrderIds = outStockOrderPOList.stream()
                .filter(m -> m.getState().byteValue() != OutStockOrderStateEnum.已出库.getType())
                .map(OutStockOrderPO::getId).collect(Collectors.toList());
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(updateOrderIds)) {
                LOG.warn("出库单都已出库:{}", JSON.toJSONString(updateOrderIds));
                return lstOrderIds;
            }
            // 更新出库单状态为已拣货

            outStockOrderStateBL.updateStateByOrderIds(lstOrderIds, OutStockOrderStateEnum.已拣货.getType());
        }

        return lstOrderIds;
    }

    /**
     * 处理erp出库
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2018/5/8 13:56
     */
    public void processInventoryByErp(List<OutStockOrderNewDTO> inStockOrderDTOList) {
        List<StockOrderStoreDTO> stockOrderStoreDTOList = Lists.transform(inStockOrderDTOList, (input) -> {
            StockOrderStoreDTO stockOrderStoreDTO = new StockOrderStoreDTO();
            List<StockOrderStoreItemDTO> stockOrderStoreItemDTOS = Lists.newArrayList();
            Byte shelfType = input.getOrderType();
            if (shelfType == OutStockOrderTypeEnum.销售出库.getType()) {
                stockOrderStoreDTO.setErpType(ERPType.销售出库单.getType());
            } else if (shelfType == OutStockOrderTypeEnum.调拨出库.getType()) {
                stockOrderStoreDTO.setErpType(ERPType.物料调拨单.getType());
            } else if (shelfType == OutStockOrderTypeEnum.破损出库.getType()) {
                stockOrderStoreDTO.setErpType(ERPType.破损出库单.getType());
            } else if (shelfType == OutStockOrderTypeEnum.其他出库.getType()) {
                stockOrderStoreDTO.setErpType(ERPType.其他出库单.getType());
            } else if (shelfType == OutStockOrderTypeEnum.采购退货.getType()) {
                stockOrderStoreDTO.setErpType(ERPType.采购退货单.getType());
            } else if (shelfType == OutStockOrderTypeEnum.盘亏出库.getType()) {
                stockOrderStoreDTO.setErpType(ERPType.库存盘点单.getType());
            } else if (shelfType == OutStockOrderTypeEnum.第三方出库.getType()) {
                stockOrderStoreDTO.setErpType(ERPType.第三方出库.getType());
            } else if (shelfType == OutStockOrderTypeEnum.同城调拨出库.getType()) {
                stockOrderStoreDTO.setErpType(ERPType.同城调拨转出.getType());
            } else if (shelfType == OutStockOrderTypeEnum.处理品转入.getType()) {
                stockOrderStoreDTO.setErpType(ERPType.处理品转入.getType());
            } else if (shelfType == OutStockOrderTypeEnum.陈列品转入.getType()) {
                stockOrderStoreDTO.setErpType(ERPType.陈列品转入.getType());
            }
            stockOrderStoreDTO.setErpEventType(ERPEventType.单据录入.getType());
            stockOrderStoreDTO.setCityId(String.valueOf(input.getOrg_Id()));
            stockOrderStoreDTO.setErpOrderId(input.getNewNoteNo());
            stockOrderStoreDTO.setDescription("直接出库");
            stockOrderStoreDTO.setStockOrderId(String.valueOf(input.getId()));
            stockOrderStoreDTO.setWarehouseId(input.getWarehouse_Id());
            stockOrderStoreDTO.setOutInType(outInType.out);
            stockOrderStoreDTO.setNeedToChangeSaleStore(input.getNeedToChangeSaleStore());
            if (input.getNotUpdateProductStore() != null) {
                stockOrderStoreDTO.setNotChangeStock(input.getNotUpdateProductStore());
            }

            List<OutStockOrderItemDTO> inStockOrderItemDTOS = input.getItemList();
            for (OutStockOrderItemDTO inStockOrderItemPO : inStockOrderItemDTOS) {
                StockOrderStoreItemDTO stockOrderStoreItemDTO = new StockOrderStoreItemDTO();
                stockOrderStoreItemDTO.setProductSkuId(String.valueOf(inStockOrderItemPO.getSkuId()));
                stockOrderStoreItemDTO.setTotalStoreCountMinUnit(
                    new BigDecimal(-1).multiply(inStockOrderItemPO.getUnitTotalCount().abs()));
                stockOrderStoreItemDTO.setChannel(Integer.valueOf(inStockOrderItemPO.getChannel()));
                stockOrderStoreItemDTO.setSource(Integer.valueOf(inStockOrderItemPO.getSource()));
                stockOrderStoreItemDTO.setLocationId(inStockOrderItemPO.getLocationId());
                stockOrderStoreItemDTO.setLocationName(inStockOrderItemPO.getLocationName());
                stockOrderStoreItemDTO.setBatchTime(inStockOrderItemPO.getBatchTime());
                stockOrderStoreItemDTO.setProductionDate(inStockOrderItemPO.getProductionDate());
                // SCM-6845
                stockOrderStoreItemDTO.setOwnerId(inStockOrderItemPO.getOwnerId());
                stockOrderStoreItemDTO.setSecOwnerId(inStockOrderItemPO.getSecOwnerId());
                stockOrderStoreItemDTO.setProductSpecificationId(inStockOrderItemPO.getProductSpecificationId());
                stockOrderStoreItemDTOS.add(stockOrderStoreItemDTO);
            }

            stockOrderStoreDTO.setProductSkuList(stockOrderStoreItemDTOS);
            return stockOrderStoreDTO;
        });
        inventoryOrderBizService.processInventoryByErp(stockOrderStoreDTOList);
    }

    /**
     * 根据订单号模糊查询已拣货的订单号列表
     *
     * @param outStockOrderDTO
     * @return
     */
    public List<String> listRefOrderNoByLike(OutStockOrderDTO outStockOrderDTO) {
        OutStockOrderPO outStockOrderPO = new OutStockOrderPO();
        if (null != outStockOrderDTO) {
            outStockOrderPO.setWarehouseId(outStockOrderDTO.getWarehouseId());
            outStockOrderPO.setReforderno(outStockOrderDTO.getRefOrderNo());
        }
        return outStockOrderMapper.listRefOrderNoByLike(outStockOrderPO);
    }

    /**
     * 根据SkuId获取ProductSpecificationId
     *
     * @param outStockDTO
     */
    public void genProductSpecificationIdBySkuId(OutStockDTO outStockDTO) {
        if (outStockDTO == null) {
            return;
        }
        if (CollectionUtils.isEmpty(outStockDTO.getOutStockOrderDTOList())) {
            return;
        }
        List<OutStockOrderItemDTO> itemList = Lists.newArrayList();
        outStockDTO.getOutStockOrderDTOList().stream().filter(d -> !CollectionUtils.isEmpty(d.getItemList()))
            .forEach(d -> itemList.addAll(d.getItemList()));

        List<Long> skuIds = itemList.stream().filter(d -> d.getSkuId() != null).map(OutStockOrderItemDTO::getSkuId)
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }
        Map<Long, ProductSkuInfoReturnDTO> productSkuInfoMap = iProductSkuService.getProductInfoBySkuId(skuIds);
        if (CollectionUtils.isEmpty(productSkuInfoMap)) {
            return;
        }
        itemList.stream().filter(d -> d.getSkuId() != null).forEach(d -> {
            ProductSkuInfoReturnDTO skuInfoReturnDTO = productSkuInfoMap.get(d.getSkuId());
            if (d.getProductSpecificationId() == null && skuInfoReturnDTO != null) {
                d.setProductSpecificationId(skuInfoReturnDTO.getProductSpecificationId());
            }
            if (d.getOwnerId() == null && skuInfoReturnDTO != null) {
                d.setOwnerId(
                    skuInfoReturnDTO.getCompanyId() == null ? null : skuInfoReturnDTO.getCompanyId().longValue());
            }
        });
    }

    /**
     * 查询出库单是否生成了内配单
     */
    public Map<String, Boolean> findCreateAllocationByOrderId(List<String> orderIds) {
        AssertUtils.notEmpty(orderIds, "订单id不能为空");
        List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.findCreateAllocationByOrderId(orderIds);
        Map<String, Boolean> allocationMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(outStockOrderPOS)) {
            for (OutStockOrderPO po : outStockOrderPOS) {
                allocationMap.put(po.getId().toString(), BatchTaskConvertor.convertToBoolean(po.getCreateAllocation()));
            }
        }
        return allocationMap;
    }

    /**
     * 团购订单出库(不校验波次状态)
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void directOutStockGroupOrder(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new BusinessException("订单信息不能为空");
        }
        // 查找订单数据
        List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.listOutStockOrderByOrderId(orderIds);

        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(outStockOrderPOS)) {
            throw new BusinessException("找不到相应订单信息,请检查订单");
        } else {
            String errOrderNos = outStockOrderPOS.stream().filter(order -> !orderIds.contains(order.getId()))
                .map(OutStockOrderPO::getReforderno).collect(Collectors.joining(","));
            if (com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(errOrderNos)) {
                throw new BusinessException("以下订单在系统中找不到,请检查订单来源：" + errOrderNos);
            }
            errOrderNos = outStockOrderPOS.stream()
                .filter(order -> order.getState().byteValue() == OutStockOrderStateEnum.已出库.getType())
                .map(OutStockOrderPO::getReforderno).collect(Collectors.joining(","));
            if (com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(errOrderNos)) {
                throw new BusinessValidateException("以下订单已出库，不要重复操作：" + errOrderNos);
            }
        }

        List<InventoryDeliveryJiupiOrder> deliveryOrders =
            WaveOrderConvertor.outStockOrderPOS2InventoryDeliveryJiupiOrders(outStockOrderPOS);

        // 批量发货
        if (CollectionUtils.isEmpty(deliveryOrders)) {
            return;
        }
        iInventoryOrderBizService.batchDeliver(deliveryOrders);
        // 修改订单状态
        LOG.info("直接出库不校验波次订单:{}", JSON.toJSONString(deliveryOrders));
        // SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包
        // try {
        // CompleteGroupStraightOrderDTO orderDTO = new CompleteGroupStraightOrderDTO();
        // orderDTO.setCityId(deliveryOrders.get(0).getCityId());
        // orderDTO.setWarehouseId(deliveryOrders.get(0).getWarehouseId());
        // List<String> lstOrderNos =
        // deliveryOrders.stream().map(p -> p.getOrderNo()).distinct().collect(Collectors.toList());
        // orderDTO.setBusinessNoList(lstOrderNos);
        // completeOrderService.completeGroupStraightOrder(orderDTO);
        // } catch (Exception e) {
        // LOG.error("订单出库出错:" + e.getMessage());
        // }
    }

    /**
     * 获取灰度仓库的KEY
     */
    private static final String IS_DeliveryOrder_NoWave = "IsDeliveryOrderNoWave";

    /**
     * 订单直接出库
     *
     * @param directOutStockDTOS
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void directOutStockByOrder(List<DirectOutStockDTO> directOutStockDTOS, Integer orgId, Integer warehouseId,
        boolean isCheckBatch) {
        List<String> orderNos = directOutStockDTOS.stream().filter(order -> order.getOrderNo() != null)
            .map(DirectOutStockDTO::getOrderNo).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderNos)) {
            throw new DataValidateException("订单信息不能为空");
        }
        // 查找订单数据
        List<OutStockOrderPO> outStockOrderPOS =
            outStockOrderMapper.listOutStockOrderAllByOrderNo(orderNos, orgId, warehouseId);
        LOG.info("扫单出库-directOutStockByOrder 出库单查询结果:{}", JSON.toJSONString(outStockOrderPOS));

        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(outStockOrderPOS)) {
            throw new BusinessException("找不到相应订单信息,请检查订单");
        } else {
            String errOrderNos = outStockOrderPOS.stream().filter(order -> !orderNos.contains(order.getReforderno()))
                .map(OutStockOrderPO::getReforderno).collect(Collectors.joining(","));
            if (com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(errOrderNos)) {
                throw new BusinessException("以下订单在系统中找不到,请检查订单来源：" + errOrderNos);
            }
            errOrderNos = outStockOrderPOS.stream()
                .filter(order -> order.getState().byteValue() == OutStockOrderStateEnum.已出库.getType())
                .map(OutStockOrderPO::getReforderno).collect(Collectors.joining(","));
            if (com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(errOrderNos)) {
                throw new BusinessValidateException("以下订单已出库，不要重复操作：" + errOrderNos);
            }
        }

        // IsDeliveryOrderNoWave
        if (isCheckBatch) {
            VariableValueQueryDTO valueQuery = new VariableValueQueryDTO();
            valueQuery.setVariableKey(IS_DeliveryOrder_NoWave);
            valueQuery.setWarehouseId(outStockOrderPOS.get(0).getWarehouseId());
            VariableDefAndValueDTO noNeedWaveStr = variableValueService.detailVariable(valueQuery);
            boolean isNoNeedWaveCheck =
                noNeedWaveStr != null && Objects.equals("true", noNeedWaveStr.getVariableData());
            LOG.info(
                String.format("扫单出库-isNoNeedWaveCheck:%s,配置：%s", isNoNeedWaveCheck, JSON.toJSONString(noNeedWaveStr)));
            if (!isNoNeedWaveCheck) {
                List<String> batchIds = outStockOrderPOS.stream().filter(
                    outStockOrder -> com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(outStockOrder.getBatchId()))
                    .map(OutStockOrderPO::getBatchId).collect(Collectors.toList());
                if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(batchIds)) {
                    // 校验波次状态
                    List<BatchPO> batchPOS = batchMapper.findBatchByIds(batchIds);

                    List<String> errBatchIds = batchPOS.stream()
                        .filter(batchPO -> batchPO.getState() != BatchStateEnum.PICKINGEND.getType().byteValue()
                            && batchPO.getState() != BatchStateEnum.ALREADYOUTOFSTORE.getType().byteValue())
                        .map(BatchPO::getId).distinct().collect(Collectors.toList());
                    if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(errBatchIds)) {
                        String errOrderNos = outStockOrderPOS.stream()
                            .filter(outStockOrder -> com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(
                                outStockOrder.getBatchId()) && errBatchIds.contains(outStockOrder.getBatchId()))
                            .map(OutStockOrderPO::getReforderno).collect(Collectors.joining(","));
                        throw new BusinessValidateException("以下订单关联的波次未完成拣货不允许出库：" + errOrderNos);
                    }
                } else {
                    throw new BusinessValidateException("订单未进行波次作业，不允许出库");
                }
            }
        }

        // 判断是否启用订单中台
        LOG.info("扫单出库-directOutStockByOrder 仓库:{}，灰度", warehouseId);
        // 快递订单溯源码数量校验
        validateTraceCode(directOutStockDTOS, outStockOrderPOS);
        // 检查快递订单物流信息
        Map<Long, List<LogisticsDTO>> longListMap = checkLogisticsOrder(outStockOrderPOS);
        directOutStockByOrderWithOrderCenter(directOutStockDTOS, outStockOrderPOS, longListMap);
    }

    /**
     * 是否启动新流程
     *
     * @param cityId 城市ID
     * @param warehouseId 仓库ID
     * @return
     */
    public boolean isDispatchOms3Flow(Integer cityId, Integer warehouseId) {
        VariableValueQueryDTO valueQuery = new VariableValueQueryDTO();
        valueQuery.setCompanyId(0L);
        valueQuery.setOrgId(cityId);
        valueQuery.setWarehouseId(warehouseId);
        valueQuery.setVariableKey("IS_DISPATCH_OMS3");
        VariableDefAndValueDTO value = variableValueService.detailVariable(valueQuery);
        if (value == null || value.getState().intValue() == 0 || !"true".equals(value.getVariableData())) {
            return false;
        }
        return true;
    }

    /**
     * 获取微酒出库单
     *
     * @return
     */
    public List<OutStockOrderPO> getOutStockOrderByWine(List<String> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return null;
        }
        List<StockOutApplyResultDTO> stockOutApplyList = iStockService.findStockOutApply(orderIdList);
        LOG.info("获取微酒出库单: {}", JSON.toJSONString(stockOutApplyList));
        if (CollectionUtils.isEmpty(stockOutApplyList)) {
            throw new BusinessValidateException("没有满足条件的微酒订单，请刷新重试或者联系技术支持！");
        }

        // 过滤出未分拣的订单
        stockOutApplyList =
            stockOutApplyList.stream().filter(p -> Objects.equals(p.getBatchTaskState(), TaskStateEnum.未分拣.getType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockOutApplyList)) {
            throw new BusinessValidateException("不存在待拣货状态的微酒订单，请刷新重试或者联系技术支持！");
        }

        List<OutStockOrderPO> outStockOrderPOList = new ArrayList<>();
        stockOutApplyList.forEach(stockOutApply -> {
            OutStockOrderPO orderPO = getOutStockOrderPO(stockOutApply);
            if (orderPO == null) {
                return;
            }
            outStockOrderPOList.add(orderPO);
        });
        LOG.info("微酒出库单格式化: {}", JSON.toJSONString(outStockOrderPOList));
        return outStockOrderPOList;
    }

    /**
     * 获取订单
     *
     * @param stockDTO
     * @return
     */
    private OutStockOrderPO getOutStockOrderPO(StockOutApplyResultDTO stockDTO) {
        if (stockDTO == null) {
            return null;
        }
        OutStockOrderPO orderPO = new OutStockOrderPO();
        orderPO.setId(Long.valueOf(stockDTO.getId()));
        orderPO.setReforderno(stockDTO.getNoteNo());
        orderPO.setWarehouseId(stockDTO.getFromWarehouseId());
        orderPO.setState(stockDTO.getBatchTaskState() != null ? Integer.valueOf(stockDTO.getBatchTaskState()) : null);
        orderPO.setOrdertype(Integer.valueOf(OutStockOrderTypeEnum.第三方出库.getType()));
        orderPO.setOrderamount(BigDecimal.ZERO);
        orderPO.setPackageamount(new BigDecimal(stockDTO.getSpecificationscount()));
        orderPO.setUnitamount(new BigDecimal(stockDTO.getUnitcount()));
        orderPO.setSkucount(stockDTO.getProductCount());
        // 订单项
        if (!CollectionUtils.isEmpty(stockDTO.getItems())) {
            // 获取skuid信息
            List<Long> skuIds = stockDTO.getItems().stream().filter(p -> p != null && p.getProductSkuId() != null)
                .map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
            Map<Long, ProductSkuDTO> productSkuMap = iProductSkuQueryService.findBySkuWithMap(skuIds);

            List<OutStockOrderItemPO> itemPOList = new ArrayList<>();
            stockDTO.getItems().forEach(item -> {
                ProductSkuDTO skuDTO = productSkuMap != null ? productSkuMap.get(item.getProductSkuId()) : null;
                OutStockOrderItemPO itemPO = getOutStockOrderItemPO(item, skuDTO);
                if (itemPO == null) {
                    return;
                }
                itemPOList.add(itemPO);
            });
            if (!CollectionUtils.isEmpty(itemPOList)) {
                orderPO.setItems(itemPOList);
            }
        }
        return orderPO;
    }

    /**
     * 获取订单项
     *
     * @param itemDTO
     * @return
     */
    private OutStockOrderItemPO getOutStockOrderItemPO(StockOutItemApplyResultDTO itemDTO, ProductSkuDTO skuDTO) {
        if (itemDTO == null) {
            return null;
        }
        OutStockOrderItemPO itemPO = new OutStockOrderItemPO();
        itemPO.setId(Long.valueOf(itemDTO.getItemId()));
        itemPO.setOutstockorderId(Long.valueOf(itemDTO.getNoteId()));
        itemPO.setSkuid(itemDTO.getProductSkuId());
        itemPO.setProductname(skuDTO != null ? skuDTO.getName() : itemDTO.getProductName());
        itemPO.setProductbrand(skuDTO != null ? skuDTO.getProductBrand() : itemDTO.getBrandName());
        itemPO.setCategoryname(skuDTO != null ? skuDTO.getStatisticsClass() : itemDTO.getCategory());
        itemPO.setSpecname(skuDTO != null ? skuDTO.getSpecificationName() : itemDTO.getSpecificationText());
        itemPO.setSpecquantity(
            skuDTO != null ? skuDTO.getPackageQuantity() : new BigDecimal(itemDTO.getPackagequantity()));
        itemPO.setSalespec(itemPO.getSpecname());
        itemPO.setSalespecquantity(itemPO.getSpecquantity());
        itemPO.setPackagename(skuDTO != null ? skuDTO.getPackageName() : itemDTO.getSpecification());
        itemPO.setPackagecount(new BigDecimal(itemDTO.getSpecificationscount()));
        itemPO.setUnitname(skuDTO != null ? skuDTO.getUnitName() : itemDTO.getUnit());
        itemPO.setUnitcount(new BigDecimal(itemDTO.getUnitcount()));
        itemPO.setUnittotalcount(new BigDecimal(itemDTO.getCount()));
        itemPO.setChannel((byte)0);
        itemPO.setSource((byte)ProductSourceType.微酒);
        itemPO.setProductSpecificationId(
            skuDTO != null ? skuDTO.getProductSpecificationId() : Long.valueOf(itemDTO.getProductSpecificationId()));
        return itemPO;
    }

    /**
     * 查找订单出库位
     */
    public List<OutStockOrderLocationDTO> findOutStockOrderLocation(List<OutStockOrderLocationDTO> locations) {
        return outStockLocationManager.getOutLocation(locations);
    }

    /**
     * 根据订单ID，规格信息查询子项出库位
     */
    public List<Long> findOrderItemToLocationIdBySpecId(Long orderId, Long specId, Long ownerId) {
        return outStockOrderItemMapper.findOrderItemToLocationIdBySpecId(orderId, specId, ownerId);
    }

    /**
     * 根据订单ID查询子项出库位
     */
    public List<Long> findToLocationIdByOrderId(Long orderId) {
        return outStockOrderItemMapper.findToLocationIdByOrderId(orderId);
    }

    /**
     * 快递直发出库校验出库单数据
     *
     * @return
     */
    public void validateOrderByOrderCenter(List<OutStockOrderPO> orderDTOList) {
        LOG.info("快递直发出库校验出库单与中台数据，参数：{}", JSON.toJSONString(orderDTOList));
        AssertUtils.notEmpty(orderDTOList, "出库单数据不能为空");

        List<String> businessIds = orderDTOList.stream().filter(p -> !StringUtils.isEmpty(p.getBusinessId()))
            .map(OutStockOrderPO::getBusinessId).distinct().collect(Collectors.toList());
        List<Long> orderCenterIds = businessIds.stream().map(Long::valueOf).collect(Collectors.toList());
        // 记录businessId对应ordrNo
        Map<Long, String> orderCenterIdMap = orderDTOList.stream().filter(p -> !StringUtils.isEmpty(p.getBusinessId()))
            .collect(Collectors.toMap(p -> Long.valueOf(p.getBusinessId()), p -> p.getReforderno(), (v1, v2) -> v1));

        // 通过omsIds批量获取中台数据
        List<OrderCommonDetailDTO> orderCommonDetailDTOList =
            orderCenterConvertService.getOMSOrderByOrderIds(orderCenterIds);
        if (CollectionUtils.isEmpty(orderCommonDetailDTOList)) {
            String noOrderCenterIds =
                businessIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
            throw new BusinessException("订单中台数据不存在！Id:" + noOrderCenterIds);
        }

        // //异常订单校验
        // List<OrderExceptions> orderExceptionList = orderCommonDetailDTOList.stream().filter(p -> null !=
        // p.getOrderBase() && p.getOrderBase().isHaveException()).map(OrderCommonDetailDTO::getOrderExceptions)
        // .flatMap(list -> list.stream().filter(e ->
        // ExceptionTypeConstants.ORDER_EXCEPTION_TYPES.contains(e.getExceptionType()))).collect(Collectors.toList());
        // if(!CollectionUtils.isEmpty(orderExceptionList)) {
        // //中台异常订单id
        // List<Long> errorOMSId = orderExceptionList.stream().map(d ->
        // d.getOrderId()).distinct().collect(Collectors.toList());
        // //获取异常单号
        // List<String> errorOderNoList = new ArrayList<>();
        // orderCenterIds.stream().forEach(orderCenterId -> {
        // if(errorOMSId.contains(orderCenterId)) {
        // errorOderNoList.add(orderCenterIdMap.get(orderCenterId));
        // }
        // });
        //
        // if(!CollectionUtils.isEmpty(errorOderNoList)) {
        // StringBuilder errorMessage = new StringBuilder("请到异常订单中处理完毕后，再尝试出库！\n");
        // errorMessage.append(String.format("异常单号:%s %n", org.apache.commons.lang3.StringUtils.join(errorOderNoList,
        // ",")));
        // throw new BusinessValidateException(errorMessage.toString());
        // }
        // }

        // 中台数据转换
        List<com.yijiupi.himalaya.supplychain.ordercenter.dto.oms.OrderDetailDTO> omsOrderDTOList = new ArrayList<>();
        orderCommonDetailDTOList.stream().forEach(d -> {
            com.yijiupi.himalaya.supplychain.ordercenter.dto.oms.OrderDetailDTO orderDetailDTO =
                orderCenterConvertService.convertOrderDetailDTO(d);
            omsOrderDTOList.add(orderDetailDTO);
        });
        if (CollectionUtils.isEmpty(omsOrderDTOList)) {
            throw new BusinessException("id对应中台订单数据异常！");
        }

        // 检查是否上传物流单号
        List<String> errorOrderNos = omsOrderDTOList.stream().filter(p -> StringUtils.isEmpty(p.getDeliveryOrderNO()))
            .map(com.yijiupi.himalaya.supplychain.ordercenter.dto.oms.OrderDetailDTO::getBusinessNo)
            .collect(Collectors.toList());
        // if (!CollectionUtils.isEmpty(errorOrderNos)) {
        // throw new BusinessException(
        // String.format("订单未上传物流单号，orderNo:", org.apache.commons.lang3.StringUtils.join(errorOrderNos, ",")));
        // }

        List<String> errOrderNos = new ArrayList<>();
        List<Long> noOmsOrderItemIds = new ArrayList<>();
        List<Long> noOrderItemIds = new ArrayList<>();
        List<Long> errOrderItemIds = new ArrayList<>();
        List<Long> errOrderItemDetailIds = new ArrayList<>();

        // wms订单
        List<String> wmsOrderNos =
            orderDTOList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList());
        // oms订单
        List<String> omsOrderNos = omsOrderDTOList.stream()
            .map(com.yijiupi.himalaya.supplychain.ordercenter.dto.oms.OrderDetailDTO::getBusinessNo).distinct()
            .collect(Collectors.toList());
        // 获取差集 (omsOrderNos - wmsOrderNos)
        List<String> errNos = omsOrderNos.stream().filter(n -> !wmsOrderNos.contains(n)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(errNos)) {
            errOrderNos.addAll(errNos);
        }

        // oms订单项
        List<com.yijiupi.himalaya.supplychain.ordercenter.dto.oms.OrderDetaiItemlDTO> omsOrderItems =
            omsOrderDTOList.stream().flatMap(orderDetailDTO -> orderDetailDTO.getItems().stream())
                .filter(item -> null != item.getMinUnitTotalCount()).collect(Collectors.toList());
        // List<OrderDetaiItemlDTO> omsOrderItems = omsOrderDTOList.stream().flatMap(orderDetailDTO ->
        // orderDetailDTO.getItems().stream()).filter(item -> item.getMinUnitTotalCount().compareTo(BigDecimal.ZERO) !=
        // 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(omsOrderItems)) {
            throw new BusinessException("中台出库单明细出库数为空！");
        }
        Map<Long, List<com.yijiupi.himalaya.supplychain.ordercenter.dto.oms.OrderDetaiItemlDTO>> omsOrderItemMap =
            omsOrderItems.stream().collect(Collectors
                .groupingBy(com.yijiupi.himalaya.supplychain.ordercenter.dto.oms.OrderDetaiItemlDTO::getOrderItemId));

        // wms订单项
        List<OutStockOrderItemPO> wmsOrderItems =
            orderDTOList.stream().flatMap(outStockOrderPO -> outStockOrderPO.getItems().stream())
                .filter(item -> null != item.getUnittotalcount()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wmsOrderItems)) {
            throw new BusinessException("WMS出库单明细出库数为空！");
        }
        Map<String, List<OutStockOrderItemPO>> wmsOrderItemMap =
            wmsOrderItems.stream().filter(item -> item.getBusinessItemId() != null)
                .collect(Collectors.groupingBy(OutStockOrderItemPO::getBusinessItemId));

        // 遍历oms订单项
        for (Map.Entry<Long,
            List<com.yijiupi.himalaya.supplychain.ordercenter.dto.oms.OrderDetaiItemlDTO>> entry : omsOrderItemMap
                .entrySet()) {
            List<OutStockOrderItemPO> wmsItems = wmsOrderItemMap.get(entry.getKey().toString());
            if (CollectionUtils.isEmpty(wmsItems)) {
                noOmsOrderItemIds.add(entry.getKey());
                continue;
            }
            // OMS订单项总小单位数
            BigDecimal omsUnitTotalCount = entry.getValue().stream()
                .map(com.yijiupi.himalaya.supplychain.ordercenter.dto.oms.OrderDetaiItemlDTO::getMinUnitTotalCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            // WMS订单项总小单位数
            BigDecimal wmsUnitTotalCount =
                wmsItems.stream().map(OutStockOrderItemPO::getUnittotalcount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (wmsUnitTotalCount.compareTo(omsUnitTotalCount) != 0) {
                errOrderItemIds.add(entry.getKey());
                continue;
            }
            // WMS订单项详细总小单位数
            List<OutStockOrderItemDetailPO> wmsItemDetailList =
                wmsItems.stream().filter(p -> !CollectionUtils.isEmpty(p.getItemDetails()))
                    .map(OutStockOrderItemPO::getItemDetails).flatMap(list -> list.stream())
                    .filter(detail -> null != detail.getUnitTotalCount()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(wmsItemDetailList)) {
                BigDecimal wmsDetailUnitTotalCount = wmsItemDetailList.stream()
                    .map(OutStockOrderItemDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (wmsDetailUnitTotalCount.compareTo(omsUnitTotalCount) != 0) {
                    errOrderItemDetailIds.add(entry.getKey());
                    continue;
                }
            }
        }

        // 遍历wms订单项
        for (Map.Entry<String, List<OutStockOrderItemPO>> entry : wmsOrderItemMap.entrySet()) {
            List<com.yijiupi.himalaya.supplychain.ordercenter.dto.oms.OrderDetaiItemlDTO> omsItems =
                omsOrderItemMap.get(Long.valueOf(entry.getKey()));
            if (CollectionUtils.isEmpty(omsItems)) {
                noOrderItemIds.add(Long.valueOf(entry.getKey()));
                continue;
            }
            // OMS订单项总小单位数
            BigDecimal omsUnitTotalCount = omsItems.stream()
                .map(com.yijiupi.himalaya.supplychain.ordercenter.dto.oms.OrderDetaiItemlDTO::getMinUnitTotalCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            // WMS订单项总小单位数
            BigDecimal wmsUnitTotalCount = entry.getValue().stream().map(OutStockOrderItemPO::getUnittotalcount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (omsUnitTotalCount.compareTo(wmsUnitTotalCount) != 0) {
                errOrderItemIds.add(Long.valueOf(entry.getKey()));
            }
            // WMS订单项详细总小单位数
            List<OutStockOrderItemDetailPO> wmsItemDetailList =
                entry.getValue().stream().filter(p -> !CollectionUtils.isEmpty(p.getItemDetails()))
                    .map(OutStockOrderItemPO::getItemDetails).flatMap(list -> list.stream())
                    .filter(detail -> null != detail.getUnitTotalCount()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(wmsItemDetailList)) {
                BigDecimal wmsDetailUnitTotalCount = wmsItemDetailList.stream()
                    .map(OutStockOrderItemDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (omsUnitTotalCount.compareTo(wmsDetailUnitTotalCount) != 0) {
                    errOrderItemDetailIds.add(Long.valueOf(entry.getKey()));
                }
            }
        }

        if (!CollectionUtils.isEmpty(errOrderNos)) {
            errOrderNos = errOrderNos.stream().distinct().collect(Collectors.toList());
            throw new BusinessValidateException(
                "订单在WMS中不存在！OrderNo:" + org.apache.commons.lang3.StringUtils.join(errOrderNos, ","));
        }

        if (!CollectionUtils.isEmpty(noOmsOrderItemIds)) {
            String noOmsOrderItem =
                noOmsOrderItemIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
            throw new BusinessValidateException("订单项在WMS中不存在！OrderCenterItemId:" + noOmsOrderItem);
        }

        if (!CollectionUtils.isEmpty(noOrderItemIds)) {
            String noOrderItem =
                noOrderItemIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
            throw new BusinessValidateException("订单项在中台不存在！WmsItemId:" + noOrderItem);
        }

        if (!CollectionUtils.isEmpty(errOrderItemIds)) {
            String errOrderItem =
                errOrderItemIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
            throw new BusinessValidateException("中台与WMS订单项数量不一致！ItemId:" + errOrderItem);
        }

        if (!CollectionUtils.isEmpty(errOrderItemDetailIds)) {
            String errOrderItem =
                errOrderItemDetailIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
            throw new BusinessValidateException("中台与WMS订单项详细数量不一致！ItemId:" + errOrderItem);
        }
    }

    /**
     * 快递订单全缺订单处理
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void processLackOrderData(List<OutStockOrderPO> orders, Integer operateUserId) {
        // 校验订单数为0并处理
        List<Long> orderIdList = orders.stream()
            .filter(p -> p.getPackageamount().compareTo(BigDecimal.ZERO) == 0
                && p.getUnitamount().compareTo(BigDecimal.ZERO) == 0)
            .map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
        LOG.info("快递订单全缺订单查询结果：{}", JSON.toJSONString(orders));

        List<String> bussinessIdList = orders.stream()
            .filter(p -> p.getPackageamount().compareTo(BigDecimal.ZERO) == 0
                && p.getUnitamount().compareTo(BigDecimal.ZERO) == 0)
            .map(OutStockOrderPO::getBusinessId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(bussinessIdList)) {
            // 更新订单表上的下推状态
            outStockOrderMapper.updatePushStateByOrderIds(orderIdList, PushStateEnum.已下推.getType());

            orderCenterBL.sendRejectOrderMessage(bussinessIdList, operateUserId);
            orders.removeIf(p -> bussinessIdList.contains(p.getBusinessId()));
        }
    }

    /**
     * 处理订单出库位
     */
    @Deprecated
    public void processOrderLocationByProductSow(String batchNo) {
        List<BatchPO> batchPOList = batchMapper.listByBatchNos(Arrays.asList(batchNo));
        if (CollectionUtils.isEmpty(batchPOList)) {
            throw new BusinessValidateException("找不到波次：" + batchNo);
        }
        BatchPO batchPO = batchPOList.get(0);
        String batchId = batchPO.getId();

        List<Long> lstOrderIds = outStockOrderMapper.findOutStockOrderIdsByBatchId(batchId);
        if (CollectionUtils.isEmpty(lstOrderIds)) {
            LOG.info("[根据产品完成播种]波次：{},无对应出库单", batchNo);
            return;
        }

        // 1、过滤掉没有生成拣货任务的出库单id
        List<Long> noBatchTaskOrderIds = outStockOrderItemMapper.listOrderIdNoBatchTask(lstOrderIds);
        if (!CollectionUtils.isEmpty(noBatchTaskOrderIds)) {
            LOG.info("未全部生成拣货任务的出库单id：{}", JSON.toJSONString(noBatchTaskOrderIds));
            lstOrderIds =
                lstOrderIds.stream().filter(p -> !noBatchTaskOrderIds.contains(p)).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(lstOrderIds)) {
            LOG.info("[根据产品完成播种]波次：{},出库单未生成拣货任务", batchNo);
            return;
        }

        // 2、过滤掉没有完成拣货的出库单id
        List<Long> noPickOrderIds = outStockOrderItemMapper.listOrderIdNoPick(lstOrderIds);
        if (!CollectionUtils.isEmpty(noPickOrderIds)) {
            LOG.info("未完成拣货的出库单id：{}", JSON.toJSONString(noPickOrderIds));
            lstOrderIds = lstOrderIds.stream().filter(p -> !noPickOrderIds.contains(p)).collect(Collectors.toList());
        }

        LOG.info("[根据产品完成播种]波次：{},波次完成的出库单：{}", batchNo, lstOrderIds);
        if (CollectionUtils.isEmpty(lstOrderIds)) {
            return;
        }

        // 2、完成拣货时通知TMS，波次下所有订单对应的出库位
        List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findTaskByBatchNo(Arrays.asList(batchNo));
        syncOrderToLocation(batchNo, batchTaskPOS, lstOrderIds);
    }

    public List<OutStockOrderItemDetailPO>
        getOutItemDetailByOrderItemList(List<OutStockOrderItemDTO> sourceOrderItemList) {
        List<OutStockOrderItemDetailPO> orderItemDetailResult = new ArrayList<>();
        if (CollectionUtils.isEmpty(sourceOrderItemList)) {
            return orderItemDetailResult;
        }

        for (OutStockOrderItemDTO orderItemDTO : sourceOrderItemList) {
            List<OutStockOrderItemDetailPO> detailListElem =
                getOutItemDetailByOrderItem(orderItemDTO, orderItemDTO.getOrg_id(), null);
            if (!CollectionUtils.isEmpty(detailListElem)) {
                orderItemDetailResult.addAll(detailListElem);
            }
        }
        return orderItemDetailResult;
    }

    public List<OutStockOrderItemDetailPO> getOutItemDetailByOrderItem(OutStockOrderItemDTO orderItemDTO, Integer orgId,
        Long itemId) {
        if (orderItemDTO == null || CollectionUtils.isEmpty(orderItemDTO.getOutStockOrderItemDetailDTOS())) {
            return null;
        }
        // 订单item数量小于等于0时，不保存detail
        BigDecimal count = orderItemDTO.getUnitTotalCount();
        if (count == null || count.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        List<OutStockOrderItemDetailPO> detailPOList = new ArrayList<>();
        for (OutStockOrderItemDetailDTO detail : orderItemDTO.getOutStockOrderItemDetailDTOS()) {
            // item数量等于0时，直接退出
            if (count.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            OutStockOrderItemDetailPO itemDetailPO =
                OrderItemDetailDTO2OutStockOrderItemDetailPO(detail, orgId, itemId);
            if (itemDetailPO != null) {
                // 验证item数量与detail数量总和
                if (count.compareTo(itemDetailPO.getUnitTotalCount()) >= 0) {
                    count = count.subtract(itemDetailPO.getUnitTotalCount());
                } else {
                    itemDetailPO.setUnitTotalCount(count);
                    count = BigDecimal.ZERO;
                }
                detailPOList.add(itemDetailPO);
            }
        }
        if (!CollectionUtils.isEmpty(detailPOList)) {
            // 如果item数量大于detail数量总和，则把多的数量全部加到第一个detail中
            if (count.compareTo(BigDecimal.ZERO) > 0) {
                OutStockOrderItemDetailPO lastDetailPO = detailPOList.get(detailPOList.size() - 1);
                lastDetailPO.setUnitTotalCount(lastDetailPO.getUnitTotalCount().add(count));
            }
        }
        return detailPOList;
    }

    private OutStockOrderItemDetailPO OrderItemDetailDTO2OutStockOrderItemDetailPO(OutStockOrderItemDetailDTO dto,
        Integer orgId, Long itemId) {
        if (dto == null) {
            return null;
        }
        OutStockOrderItemDetailPO outStockOrderItemDetailPO = new OutStockOrderItemDetailPO();
        BeanUtils.copyProperties(dto, outStockOrderItemDetailPO);
        outStockOrderItemDetailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.OUT_STOCK_ORDER_ITEM_DETAIL));
        if (orgId != null) {
            outStockOrderItemDetailPO.setOrgId(orgId);
        }
        if (itemId != null) {
            outStockOrderItemDetailPO.setOutStockOrderItemId(itemId);
        }
        return outStockOrderItemDetailPO;
    }

    public void saasDirectOutStockByOrder(BatchOutStockDTO batchOutStockDTO) {
        Integer orgId = batchOutStockDTO.getOrgId();
        AssertUtils.notNull(orgId, "城市id不能为空");
        List<String> batchNos = batchOutStockDTO.getBatchNos();
        PageList<BatchOrderInfoDTO> batchInfoList =
            batchOrderInfoBL.findBatchOrderInfoListByBatchNo(batchNos.get(0), 1, Integer.MAX_VALUE);
        BatchOrderInfoDTO orderInfoDTO = batchInfoList.getDataList().get(0);
        if (batchInfoList == null || CollectionUtils.isEmpty(batchInfoList.getDataList())
            || CollectionUtils.isEmpty(orderInfoDTO.getItems())) {
            throw new BusinessException("波次对应的订单不存在");
        }
        LOG.info("波次数据：{}", JSON.toJSONString(batchInfoList));
        List<OutStockOrderDTO> items = orderInfoDTO.getItems();
        if (CollectionUtils.isEmpty(items)) {
            throw new BusinessValidateException("订单数据不存在");
        }
        List<Long> orderIdList = new ArrayList<>();
        items.forEach(it -> orderIdList.add(it.getId()));
        /** 查询是否为订单数据，是则获取订单打印信息 */
        Integer warehouseId = items.get(0).getWarehouseId();
        checkOrder(orderIdList, orgId);
        List<DirectOutStockDTO> directOutStockDTOS = new ArrayList<>();
        items.forEach(order -> {
            DirectOutStockDTO outStockDTO = new DirectOutStockDTO();
            outStockDTO.setOrderNo(order.getRefOrderNo());
            outStockDTO.setOrgId(orgId);
            outStockDTO.setOperateUser(batchOutStockDTO.getOperateUser());
            outStockDTO.setOperateUserId(batchOutStockDTO.getOperateUserId());
            directOutStockDTOS.add(outStockDTO);
        });
        directOutStockByOrder(directOutStockDTOS, orgId, warehouseId, Boolean.TRUE);
    }

    /**
     * 检查订单是否为快递直发，快递直发则校验订单是否打印电子面单
     *
     * @param orderIdList
     */
    private void checkOrder(List<Long> orderIdList, Integer orgId) {
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findByOrderIds(orderIdList);
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }
        List<Long> orderIds = outStockOrderPOList.stream()
            .filter(
                it -> it.getDeliveryMode() != null && it.getDeliveryMode().equals(DeliveryModeNewEnum.KDZF.getValue()))
            .map(OutStockOrderPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        OrderPrintDTO printDTO = new OrderPrintDTO();
        printDTO.setOrderList(orderIds);
        LOG.info("查询订单电子面单参数:{}", JSON.toJSONString(printDTO));
        List<OrderPrintDTO> printDTOS = iOrderPrintService.list(printDTO);
        if (CollectionUtils.isEmpty(printDTOS)) {
            throw new BusinessException("快递面单没有打印，请先打印");
        }
    }

    /**
     * 扫单出库-开启仓库灰度
     *
     * @param directOutStockDTOS
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void directOutStockByOrderWithOrderCenter(List<DirectOutStockDTO> directOutStockDTOS,
        List<OutStockOrderPO> outStockOrderPOS, Map<Long, List<LogisticsDTO>> longListMap) {
        // 处理全缺订单
        processLackOrderData(outStockOrderPOS, directOutStockDTOS.get(0).getOperateUserId());
        if (CollectionUtils.isEmpty(outStockOrderPOS)) {
            throw new BusinessValidateException("订单已出库或订单全缺，不允许出库");
        }

        // 与中台订单进行校验
        validateOrderByOrderCenter(outStockOrderPOS);

        List<InventoryDeliveryJiupiOrder> deliveryOrders =
            WaveOrderConvertor.expressOutStockOrderPOS2InventoryDeliveryJiupiOrders(outStockOrderPOS);
        LOG.info("directOutStockByOrderWithOrderCenter 订单直接出库deliveryOrders:{}", JSON.toJSONString(deliveryOrders));
        // 批量发货
        if (CollectionUtils.isEmpty(deliveryOrders)) {
            return;
        }

        List<InventoryDeliveryJiupiOrder> processOrders =
            iInventoryOrderBizService.batchDeliverByExpressOrder(deliveryOrders);
        // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 -- HANDLED

        // 根据实际出库二级货主修改通知中台参数
        processSecOwnerIdBydeliveryOrders(outStockOrderPOS, processOrders);

        // 处理快递直发发货
        List<OutStockOrderPO> lstLogisticsOrder = outStockOrderPOS.stream()
            .filter(p -> Objects.equals(p.getDeliveryMode(), DeliveryOrderConstant.DELIVERY_MODE_DIRECT)
                // 快递直发模式的调拨单临时方案
                && !Objects.equals(p.getOrdertype(), 52))
            .collect(Collectors.toList());

        // 其他类型订单出库
        List<OutStockOrderPO> lstOtherOrders = outStockOrderPOS.stream()
            .filter(p -> !Objects.equals(p.getDeliveryMode(), DeliveryOrderConstant.DELIVERY_MODE_DIRECT)
                || (Objects.equals(p.getDeliveryMode(), DeliveryOrderConstant.DELIVERY_MODE_DIRECT)
                    // 快递直发模式的调拨单临时方案
                    && Objects.equals(p.getOrdertype(), 52)))
            .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(lstLogisticsOrder)) {
            LOG.info("directOutStockByOrderWithOrderCenter 快递直发订单直接出库:{}", JSON.toJSONString(lstLogisticsOrder));
            List<ExpressDispatchNotifyDTO> notifyDTOList =
                ExpressDispatchNotifyDTOConvert.convert(lstLogisticsOrder, directOutStockDTOS, longListMap);
            orderCenterBL.expressDispatchNotify(notifyDTOList);
        }

        if (!CollectionUtils.isEmpty(lstOtherOrders)) {
            LOG.info("directOutStockByOrderWithOrderCenter 非快递直发订单直接出库:{}", JSON.toJSONString(lstOtherOrders));
            List<OrderOutStockNotifyDTO> orderOutStockNotifyDTOS =
                OrderOutStockNotifyConvert.convert(lstOtherOrders, directOutStockDTOS);
            orderCenterBL.orderOutStockNotify(orderOutStockNotifyDTOS);
        }

        // 数字分销订单
        List<OutStockOrderPO> retailOrders =
            lstLogisticsOrder.stream().filter(p -> Objects.equals(p.getOrderSourceType(), SourceType.RETAIL.getValue()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(retailOrders)) {
            LOG.info("directOutStockByOrderWithOrderCenter 数字分销快递直发订单直接出库:{}", JSON.toJSONString(retailOrders));
            changeTraceabilityEncode(directOutStockDTOS, retailOrders);
        }
    }

    /**
     * 快递订单检查是否上传物流信息
     *
     * @param outStockOrderPOS
     */
    public Map<Long, List<LogisticsDTO>> checkLogisticsOrder(List<OutStockOrderPO> outStockOrderPOS) {
        // 处理快递直发发货
        List<OutStockOrderPO> lstLogisticsOrder = outStockOrderPOS.stream()
            .filter(p -> Objects.equals(p.getDeliveryMode(), DeliveryOrderConstant.DELIVERY_MODE_DIRECT)
                // 快递直发模式的调拨单临时方案
                && !Objects.equals(p.getOrdertype(), 52))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lstLogisticsOrder)) {
            return Collections.emptyMap();
        }

        List<OutStockOrderDTO> outStockOrderDTOS =
            WaveOrderConvertor.outStockOrderPOS2OutStockOrderDTOS(lstLogisticsOrder);

        // 根据快递单号查询快递单信息
        // if (isOpenOrderCenter) {
        List<Long> omsOrderIdList = outStockOrderDTOS.stream().map(OutStockOrderDTO::getBusinessId)
            .filter(com.alibaba.dubbo.common.utils.StringUtils::isInteger).map(Long::valueOf)
            .collect(Collectors.toList());
        // if (!CollectionUtils.isEmpty(omsOrderIdList)) {
        // List<OrderCommonDetailDTO> omsOrderDetailList =
        // orderCenterConvertService.getOMSOrderByOrderIds(omsOrderIdList);
        // LOG.info("checkLogisticsOrder 根据订单id：{}，查询快递单信息：{}", JSON.toJSONString(omsOrderIdList),
        // JSON.toJSONString(omsOrderDetailList));
        // if (!CollectionUtils.isEmpty(omsOrderDetailList)) {
        // outStockOrderDTOS.forEach(order -> {
        // if (!com.alibaba.dubbo.common.utils.StringUtils.isInteger(order.getBusinessId())) {
        // return;
        // }
        // OrderCommonDetailDTO omsOrderDetail = omsOrderDetailList.stream()
        // .filter(omsOrd -> Objects.equals(Long.valueOf(order.getBusinessId()), omsOrd.getOrderId()))
        // .findFirst().orElse(null);
        // if (omsOrderDetail == null) {
        // return;
        // }
        // order.setLogisticsList(LogisticsConverter.orderCommonDetailDTOToLogisticsDTOS(omsOrderDetail));
        // });
        // }
        // }
        if (!CollectionUtils.isEmpty(omsOrderIdList)) {
            OrderPrintDTO printDTO = new OrderPrintDTO();
            printDTO.setOrderList(omsOrderIdList);
            List<OrderPrintDTO> printDTOS = iOrderPrintService.list(printDTO);
            LOG.info("checkLogisticsOrder 根据订单id：{}，查询快递单信息：{}", JSON.toJSONString(omsOrderIdList),
                JSON.toJSONString(printDTOS));
            if (!CollectionUtils.isEmpty(printDTOS)) {
                Map<Long, List<OrderPrintDTO>> printDTOMap = printDTOS.stream().filter(p -> p.getOrderId() != null)
                    .collect(Collectors.groupingBy(OrderPrintDTO::getOrderId));
                outStockOrderDTOS.forEach(order -> {
                    if (!org.apache.commons.lang3.StringUtils.isNumeric(order.getBusinessId())) {
                        return;
                    }

                    List<OrderPrintDTO> orderPrintDTOS = printDTOMap.get(Long.valueOf(order.getBusinessId()));
                    if (CollectionUtils.isEmpty(orderPrintDTOS)) {
                        return;
                    }

                    order.setLogisticsList(LogisticsConverter.orderPrintDTOToLogisticsDTOS(orderPrintDTOS));
                });
            }
        }
        // } else {
        // List<String> refOrderNoList =
        // outStockOrderDTOS.stream().map(p -> p.getRefOrderNo()).distinct().collect(Collectors.toList());
        // if (!CollectionUtils.isEmpty(refOrderNoList)) {
        // List<OrderLogisticsListDTO> orderLogisticsDTOS =
        // iOrderprocessservice.queryLogisticsInfoByBusinessNos(refOrderNoList);
        // LOG.info("checkLogisticsOrder 根据订单号：{}，查询快递单信息：{}", JSON.toJSONString(refOrderNoList),
        // JSON.toJSONString(orderLogisticsDTOS));
        // if (!CollectionUtils.isEmpty(orderLogisticsDTOS)) {
        // outStockOrderDTOS.forEach(orderDTO -> {
        // Optional<OrderLogisticsListDTO> optional = orderLogisticsDTOS.stream()
        // .filter(p -> Objects.equals(p.getBusinessNo(), orderDTO.getRefOrderNo())).findFirst();
        // if (optional.isPresent()) {
        // List<OrderLogisticsBaseDTO> omsLogisticsList = optional.get().getOrderLogistics();
        // orderDTO.setLogisticsList(
        // LogisticsConverter.orderLogisticsBaseDTOSToLogisticsDTOS(omsLogisticsList));
        // orderDTO.setLogisticsOrderType(optional.get().getLogisticsOrderType());
        // }
        // });
        // }
        // }
        // }

        List<OutStockOrderDTO> orderDTOS = outStockOrderDTOS.stream()
            .filter(p -> CollectionUtils.isEmpty(p.getLogisticsList())
                || p.getLogisticsList().stream().allMatch(l -> StringUtils.isEmpty(l.getNo())))
            .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orderDTOS)) {
            throw new BusinessValidateException("以下快递订单未上传物流信息，无法出库："
                + orderDTOS.stream().map(OutStockOrderDTO::getRefOrderNo).collect(Collectors.joining(",")));
        } ;

        Map<Long, List<LogisticsDTO>> longListMap = outStockOrderDTOS.stream()
            .collect(Collectors.toMap(p -> p.getId(), p -> p.getLogisticsList(), (v1, v2) -> v1 != null ? v1 : v2));
        LOG.info("checkLogisticsOrder 检查结果longListMap:{}", JSON.toJSONString(longListMap));
        return longListMap;
    }

    /**
     * 获取容器配置状态
     *
     * @param outStockOrderPO
     * @return
     */
    public Boolean containerConfiguration(OutStockOrderPO outStockOrderPO) {
        VariableValueQueryDTO variableValue = new VariableValueQueryDTO();
        variableValue.setOrgId(outStockOrderPO.getOrgId());
        variableValue.setWarehouseId(outStockOrderPO.getWarehouseId());
        variableValue.setVariableKey("wms_need_distribution_container");
        VariableDefAndValueDTO variableDefAndValueDTO = variableValueService.detailVariable(variableValue);
        if (Objects.isNull(variableDefAndValueDTO)) {
            LOG.info("[{}:{}]未获取到配置", outStockOrderPO.getWarehouseId(), variableValue.getVariableKey());
            return false;
        }
        String variableData = variableDefAndValueDTO.getVariableData();
        return Boolean.valueOf(variableData);
    }

    /**
     * 根据实际出库二级货主修改通知中台参数
     *
     * @return
     */
    public void processSecOwnerIdBydeliveryOrders(List<OutStockOrderPO> orders,
        List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        LOG.info("直接出库更新出库通知中台二级货主数据 processSecOwnerIdBydeliveryOrders，入参 orders：{}，deliveryOrders：{}",
            JSON.toJSONString(orders), JSON.toJSONString(deliveryOrders));
        if (CollectionUtils.isEmpty(orders) || CollectionUtils.isEmpty(deliveryOrders)) {
            return;
        }

        // 实际出库二级货主信息
        Map<Long,
            List<InventoryDeliveryJiupiOrderItem>> deliveryOrderItemMap = deliveryOrders.stream()
                .filter(p -> !CollectionUtils.isEmpty(p.getItems())).flatMap(list -> list.getItems().stream())
                .filter(p -> p.getOrderItem_Id() != null && p.getOrderItemDetailId() != null)
                .collect(Collectors.groupingBy(InventoryDeliveryJiupiOrderItem::getOrderItem_Id));

        // 出库单项数据
        List<OutStockOrderItemPO> orderItemPOList = orders.stream().filter(p -> !CollectionUtils.isEmpty(p.getItems()))
            .flatMap(list -> list.getItems().stream()).filter(p -> !CollectionUtils.isEmpty(p.getItemDetails()))
            .collect(Collectors.toList());

        orderItemPOList.forEach(item -> {
            List<InventoryDeliveryJiupiOrderItem> deliveryOrderItems = deliveryOrderItemMap.get(item.getId());
            if (CollectionUtils.isEmpty(deliveryOrderItems)) {
                return;
            }

            List<OutStockOrderItemDetailPO> newDetailPOS = new ArrayList<>();
            deliveryOrderItems.stream().forEach(deliveryOrderItem -> {
                OutStockOrderItemDetailPO newDetailPO = new OutStockOrderItemDetailPO();
                newDetailPO.setId(deliveryOrderItem.getOrderItemDetailId());
                newDetailPO.setOrgId(item.getOrgId());
                newDetailPO.setProductSpecificationId(deliveryOrderItem.getProductSpecification_Id());
                newDetailPO.setOwnerId(deliveryOrderItem.getOwnerId());
                newDetailPO.setSecOwnerId(deliveryOrderItem.getSecOwnerId());
                newDetailPO.setUnitTotalCount(deliveryOrderItem.getDeliverCount());
                newDetailPO.setLocationId(deliveryOrderItem.getLocationId());
                newDetailPO.setLocationName(deliveryOrderItem.getLocationName());
                newDetailPO.setProductionDate(deliveryOrderItem.getProductionDate());
                newDetailPOS.add(newDetailPO);
            });

            if (CollectionUtils.isEmpty(newDetailPOS)) {
                return;
            }

            item.setItemDetails(newDetailPOS);
        });

        LOG.info("直接出库更新出库通知中台二级货主数据 processSecOwnerIdBydeliveryOrders，结果 orders：{}", JSON.toJSONString(orders));
    }

    /**
     * 数字分销快递订单变更溯源码信息
     *
     * @param outStockOrderPOS
     */
    private void changeTraceabilityEncode(List<DirectOutStockDTO> directOutStockDTOS,
        List<OutStockOrderPO> outStockOrderPOS) {
        List<String> orderNos = outStockOrderPOS.stream().filter(p -> !StringUtils.isEmpty(p.getReforderno()))
            .map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList());
        List<DirectOutStockDTO> retailControllerOrderDTOS = directOutStockDTOS.stream()
            .filter(p -> !CollectionUtils.isEmpty(p.getSourceCodeIdList()) && orderNos.contains(p.getOrderNo()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(retailControllerOrderDTOS)) {
            return;
        }
        Integer warehouseId = outStockOrderPOS.get(0).getWarehouseId();
        Integer opUserId = retailControllerOrderDTOS.get(0).getOperateUserId();
        List<UpdateProductSourceCodeDTO> updateProductSourceCodeDTOS = new ArrayList<>();
        List<ProductSourceCodeRecordSyncDTO> recordSyncDTOS = new ArrayList<>();
        retailControllerOrderDTOS.stream().forEach(dto -> {
            dto.getSourceCodeIdList().stream().forEach(codeId -> {
                UpdateProductSourceCodeDTO updateProductSourceCodeDTO = new UpdateProductSourceCodeDTO();
                updateProductSourceCodeDTO.setProductSourceCodeId(codeId);
                updateProductSourceCodeDTO.setBusinessType(ProductSourceCodeRecordBusinessTypeEnum.销售订单.getType());
                updateProductSourceCodeDTO.setBusinessId(dto.getOrderId());
                updateProductSourceCodeDTO.setBusinessNo(dto.getOrderNo());
                updateProductSourceCodeDTO.setWarehouseId(warehouseId);
                updateProductSourceCodeDTO.setOperateUserId(opUserId);
                updateProductSourceCodeDTOS.add(updateProductSourceCodeDTO);
            });

            ProductSourceCodeRecordSyncDTO recordSyncDTO = new ProductSourceCodeRecordSyncDTO();
            recordSyncDTO.setProductSourceCodeIds(dto.getSourceCodeIdList());
            recordSyncDTO.setAfterState(ProductSourceCodeRecordStateEnum.出库.getType());
            recordSyncDTO.setBusinessType(ProductSourceCodeRecordBusinessTypeEnum.销售订单.getType());
            recordSyncDTO.setBusinessId(String.valueOf(dto.getOrderId()));
            recordSyncDTO.setBusinessNo(dto.getOrderNo());
            recordSyncDTO.setLastUpdateUser(opUserId);
            recordSyncDTOS.add(recordSyncDTO);
        });

        LOG.info("changeTraceabilityEncode 数字核销快递订单溯源码出库 updateProductSourceCodeDTOS={}",
            JSON.toJSONString(updateProductSourceCodeDTOS));
        iProductSourceService.updateProductSourceCodeInfo(updateProductSourceCodeDTOS);

        LOG.info("changeTraceabilityEncode 数字核销快递订单溯源码出库变更记录 recordSyncDTO={}", JSON.toJSONString(recordSyncDTOS));
        recordSyncDTOS.stream().forEach(syncDTO -> {
            iProductSourceService.syncProductSourceCodeTrace(syncDTO);
        });
    }

    /**
     * 校验溯源码数量
     *
     * @param outStockOrderPOS
     */
    public void validateTraceCode(List<DirectOutStockDTO> directOutStockDTOS, List<OutStockOrderPO> outStockOrderPOS) {
        // 处理快递直发订单
        List<OutStockOrderPO> lstLogisticsOrder = outStockOrderPOS.stream()
            .filter(p -> (Objects.equals(p.getDeliveryMode(), DeliveryOrderConstant.DELIVERY_MODE_DIRECT)
                // 快递直发模式的调拨单临时方案
                && !Objects.equals(p.getOrdertype(), 52))
                || Objects.equals(p.getOutBoundType(),
                    OutBoundTypeEnum.EXPRESS_DELIVERY_SALE_ORDER.getCode().byteValue()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lstLogisticsOrder)) {
            return;
        }

        List<OutStockOrderItemPO> itemPOS =
            outStockOrderPOS.stream().filter(p -> !CollectionUtils.isEmpty(p.getItems())).map(OutStockOrderPO::getItems)
                .flatMap(list -> list.stream()).collect(Collectors.toList());
        List<OutStockOrderItemDTO> itemDTOS = OutStockOrderItemConverter.poList2DTOList(itemPOS);
        if (CollectionUtils.isEmpty(itemDTOS)) {
            return;
        }
        // 检查控货，设置所需溯源码数量
        batchOrderInfoBL.setControlConfigInfo(itemDTOS, null);
        // 获取溯源码总数
        BigDecimal totalSourceCodeCount = itemDTOS.stream()
            .filter(p -> p.getSourceCodeCount() != null && p.getSourceCodeCount().compareTo(BigDecimal.ZERO) > 0)
            .map(OutStockOrderItemDTO::getSourceCodeCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalSourceCodeCount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        // 获取出库参数输入溯源码
        List<Long> sourceCodeIdList = directOutStockDTOS.stream()
            .filter(p -> !CollectionUtils.isEmpty(p.getSourceCodeIdList())).map(DirectOutStockDTO::getSourceCodeIdList)
            .flatMap(list -> list.stream()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sourceCodeIdList)) {
            throw new DataValidateException(String.format("溯源码数量不一致，输入溯源码数量为空，实际需要%s!", totalSourceCodeCount));

        }

        BigDecimal traceCodeParamCount = BigDecimal.valueOf(sourceCodeIdList.size());
        if (totalSourceCodeCount.compareTo(traceCodeParamCount) != 0) {
            throw new DataValidateException(
                String.format("溯源码数量不一致，输入溯源码数量%s，实际需要%s!", traceCodeParamCount, totalSourceCodeCount));
        }

    }

    /**
     * 根据订单id获取托盘号，符号#拼接
     *
     * @param orderIdList
     */
    public Map<Long, String> getOrderLocationPalletMap(Integer warehouseId, List<Long> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return Collections.EMPTY_MAP;
        }

        OrderLocationPalletQueryDTO queryDTO = new OrderLocationPalletQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setOrderIdList(orderIdList);
        List<OrderLocationPalletDTO> palletDTOS = orderLocationPalletBL.findPalletByCondition(queryDTO);
        if (CollectionUtils.isEmpty(palletDTOS)) {
            return Collections.EMPTY_MAP;
        }

        Map<Long, String> palletMap = new HashMap<>();
        palletDTOS.stream().filter(p -> p.getOrderId() != null && !StringUtils.isEmpty(p.getPalletNo()))
            .collect(Collectors.groupingBy(OrderLocationPalletDTO::getOrderId)).forEach((orderId, palletList) -> {
                String palletNoStr =
                    "#" + palletList.stream().sorted(Comparator.comparing(OrderLocationPalletDTO::getPalletNo))
                        .map(OrderLocationPalletDTO::getPalletNo).distinct().collect(Collectors.joining("#"));
                palletMap.put(orderId, palletNoStr);
            });

        return palletMap;
    }

    /**
     * 根据订单id获取托盘号集合
     *
     * @param orderIdList
     */
    public Map<Long, List<String>> getOrderPalletNoListMap(Integer warehouseId, List<Long> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return Collections.EMPTY_MAP;
        }

        OrderLocationPalletQueryDTO queryDTO = new OrderLocationPalletQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setOrderIdList(orderIdList);
        List<OrderLocationPalletDTO> palletDTOS = orderLocationPalletBL.findPalletByCondition(queryDTO);
        if (CollectionUtils.isEmpty(palletDTOS)) {
            return Collections.EMPTY_MAP;
        }

        Map<Long,
            List<String>> palletMap = palletDTOS.stream()
                .filter(p -> p.getOrderId() != null && !StringUtils.isEmpty(p.getPalletNo())).collect(Collectors
                    .groupingBy(p -> p.getOrderId(), Collectors.mapping(p -> p.getPalletNo(), Collectors.toList())));
        return palletMap;
    }

    public List<String> getOutStockOrderBusinessIds(List<Long> ids, List<String> refOrderNoList, Integer warehouseId) {
        if (!CollectionUtils.isEmpty(ids)) {
            List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.selectByIds(ids);
            if (CollectionUtils.isEmpty(outStockOrderPOList)) {
                return Collections.emptyList();
            }
            return outStockOrderPOList.stream().map(OutStockOrderPO::getBusinessId).distinct()
                .collect(Collectors.toList());
        }

        if (!CollectionUtils.isEmpty(refOrderNoList) && Objects.nonNull(warehouseId)) {
            List<OutStockOrderPO> outStockOrderPOList =
                outStockOrderMapper.findOrderByRefOrderNo(refOrderNoList, warehouseId);
            if (CollectionUtils.isEmpty(outStockOrderPOList)) {
                return Collections.emptyList();
            }
            return outStockOrderPOList.stream().map(OutStockOrderPO::getBusinessId).distinct()
                .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
