package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/4/24
 */
@Component
public class BatchTaskSortItemDTOConvertor {

    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;

    /**
     * 填充拣货任务项，是否是促销订单的拣货任务明细
     *
     * @param batchTaskSortItemDTOList
     * @param lstItems
     * @param orgId
     * @return
     */
    public List<BatchTaskSortItemDTO> fillPromotionInfo(List<BatchTaskSortItemDTO> batchTaskSortItemDTOList,
        List<BatchTaskItemDTO> lstItems, Integer orgId) {
        if (CollectionUtils.isEmpty(batchTaskSortItemDTOList)) {
            return Collections.emptyList();
        }

        List<String> batchTaskItemIds =
            lstItems.stream().map(BatchTaskItemDTO::getId).distinct().collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoByBatchTaskItemIds(batchTaskItemIds, orgId);
        List<Long> refOrderItemIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderItemId)
            .distinct().collect(Collectors.toList());

        List<OutStockOrderItemPO> outStockOrderItemPOList = outStockOrderItemMapper.listByIds(refOrderItemIds);
        Map<Long, OutStockOrderItemPO> outStockOrderItemPOMap =
            outStockOrderItemPOList.stream().collect(Collectors.toMap(OutStockOrderItemPO::getId, v -> v));

        Map<String, List<OrderItemTaskInfoPO>> taskInfoGroupByItemMap =
            orderItemTaskInfoPOList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getBatchTaskItemId));

        for (BatchTaskSortItemDTO batchTaskSortItemDTO : batchTaskSortItemDTOList) {
            List<OrderItemTaskInfoPO> taskInfoPOList = taskInfoGroupByItemMap.get(batchTaskSortItemDTO.getId());
            Long orderItemId = taskInfoPOList.stream().findAny().get().getRefOrderItemId();
            OutStockOrderItemPO outStockOrderItemPO = outStockOrderItemPOMap.get(orderItemId);
            // 生成波次的时候，保证 促销订单的产品在一个 拣货任务项
            batchTaskSortItemDTO.setIsPromotion(getIsPromotion(outStockOrderItemPO));
        }

        return batchTaskSortItemDTOList;
    }

    private Byte getIsPromotion(OutStockOrderItemPO outStockOrderItemPO) {
        if (Objects.isNull(outStockOrderItemPO)) {
            return ConditionStateEnum.否.getType();
        }

        return outStockOrderItemPO.getIsAdvent();
    }

}
