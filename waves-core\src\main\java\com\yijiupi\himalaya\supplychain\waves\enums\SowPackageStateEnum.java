package com.yijiupi.himalaya.supplychain.waves.enums;

import java.util.EnumSet;
import java.util.Objects;
import java.util.Optional;

public enum SowPackageStateEnum {
    /**
     * 枚举
     */
    待打包((byte)0), 打包中((byte)1), 已打包((byte)2);

    /**
     * type
     */
    private Byte type;

    SowPackageStateEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

    /**
     * 返回枚举
     */
    public static SowPackageStateEnum getEnum(Byte type) {
        SowPackageStateEnum e = null;

        if (type != null) {
            for (SowPackageStateEnum o : values()) {
                if (o.getType().equals(type)) {
                    e = o;
                }
            }
        }
        return e;
    }

    /**
     * 返回名称
     */
    public static String getEnumName(Byte value) {
        Optional<SowPackageStateEnum> partyType = EnumSet.allOf(SowPackageStateEnum.class).stream()
            .filter(elem -> Objects.equals(value, elem.type)).findAny();

        String name = null;
        if (partyType.isPresent()) {
            name = partyType.get().name();
        }
        return name;
    }
}
