package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;

/**
 * 订单修改出库位
 *
 * <AUTHOR>
 * @date 2020-03-18 17:33
 */
public class OutStockOrderUpdateLocationDTO implements Serializable {

    private static final long serialVersionUID = -5407504898870111187L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 原出库位id
     */
    private Long oldLocationId;

    /**
     * 原出库位名称
     */
    private String oldLocationName;

    /**
     * 新出库位id
     */
    private Long locationId;

    /**
     * 新出库位名称
     */
    private String locationName;

    /**
     * 操作人
     */
    private String operateUser;
    /**
     * 操作人id
     */
    private Integer optUserId;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getOldLocationId() {
        return oldLocationId;
    }

    public void setOldLocationId(Long oldLocationId) {
        this.oldLocationId = oldLocationId;
    }

    public String getOldLocationName() {
        return oldLocationName;
    }

    public void setOldLocationName(String oldLocationName) {
        this.oldLocationName = oldLocationName;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }
}
