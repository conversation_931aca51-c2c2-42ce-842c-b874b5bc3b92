package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;

/**
 * 物流订单信息
 * 
 * <AUTHOR>
 * @Date 2021/8/24 15:38
 */
public class LogisticsDTO implements Serializable {
    /**
     * 物流公司名称
     */
    private String companyName;

    /**
     * 物流单号
     */
    private String no;

    /**
     * 物流公司编码
     */
    private String companyCode;

    /**
     * 客户录入信息，京东商家编码或顺丰手机号后四位
     */
    private String customerNo;

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }
}
