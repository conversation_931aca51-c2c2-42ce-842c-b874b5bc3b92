package com.yijiupi.himalaya.supplychain.waves.domain.bl.lack;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.common.util.resourcelock.query.QueryResourceService;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockLackDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockMarkLackDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-11-15 12:00
 **/
public class MarkLackLockHelper {

    private static final Logger logger = LoggerFactory.getLogger(MarkLackLockHelper.class);

    @Service
    public static class MarkLackLockQuery implements QueryResourceService {

        @Resource
        private OutStockOrderMapper outStockOrderMapper;

        @Override
        public List<String> query(Object[] args) {
            if (args == null || args.length == 0) {
                logger.info("加锁失败, 参数为空");
                return Collections.emptyList();
            }
            Object arg = args[0];
            if (arg instanceof OutStockMarkLackDTO) {
                List<OutStockLackDTO> lackInfo = ((OutStockMarkLackDTO) arg).getOutStockLackDTOList();
                if (CollectionUtils.isEmpty(lackInfo)) {
                    logger.info("加锁失败, 订单数据为空: {}", JSON.toJSONString(arg, SerializerFeature.WriteMapNullValue));
                    return Collections.emptyList();
                }
                List<String> orderNos = lackInfo.stream().map(OutStockLackDTO::getOrderNo).distinct().collect(Collectors.toList());
                Integer warehouseId = lackInfo.get(0).getWarehouseId();
                return outStockOrderMapper.findIdByOrderNos(orderNos, warehouseId).stream().map(OutStockOrderPO::getBusinessId)
                        .collect(Collectors.toList());
            }
            if (arg instanceof OutStockLackDTO) {
                return Collections.singletonList(((OutStockLackDTO) arg).getBusinessId().toString());
            }
            logger.info("加锁失败, 不支持的参数: {}", arg.getClass());
            return Collections.emptyList();
        }

        @Override
        public String serviceName() {
            return getClass().getSimpleName();
        }
    }

}
