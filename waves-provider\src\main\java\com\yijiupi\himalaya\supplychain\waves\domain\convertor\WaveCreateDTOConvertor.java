package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.*;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.SplitBatchTaskByPassageBL;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchAttrSettingWayConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.BatchAreaAndRouteBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.ExpressFlagEnum;

/**
 * <AUTHOR>
 * @title: WaveCreateDTOConvertor
 * @description:
 * @date 2023-02-08 17:53
 */
public class WaveCreateDTOConvertor {

    private static final Logger LOG = LoggerFactory.getLogger(WaveCreateDTOConvertor.class);

    public static List<WaveCreateDTO> createWaveCreateDTO(WavesStrategyBO wavesStrategyDTO,
        List<OutStockOrderPO> createOrderList, WaveCreateDTO oriCreateDTO) {
        if (CollectionUtils.isEmpty(createOrderList)) {
            return Collections.emptyList();
        }

        // 有多个出库批次，但不符合二次分拣通道，则根据车次划分
        if (wavesStrategyDTO.getIsMultiOutBound()) {
            LOG.info("[多出库批次创建波次]未匹配到任何通道，出库批次号：{}，出库单号：{}",
                createOrderList.stream().map(OutStockOrderPO::getBoundNo).distinct().collect(Collectors.toList()),
                createOrderList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
            return getSameWaveCreateByDiffOrderList(oriCreateDTO, createOrderList, wavesStrategyDTO);
        }

        WaveCreateDTO otherCreateDTO = new WaveCreateDTO();
        BeanUtils.copyProperties(oriCreateDTO, otherCreateDTO);
        otherCreateDTO.setOrders(createOrderList);
        otherCreateDTO.setWavesStrategyDTO(oriCreateDTO.getWavesStrategyDTO());
        otherCreateDTO.setPassageDTO(oriCreateDTO.getPassageDTO());
        return Collections.singletonList(otherCreateDTO);
    }

    /**
     * 不同出库批次的出库单，采用相同的WaveCreateDTO
     *
     * @param createDTO
     * @param lstOtherOrders
     */
    private static List<WaveCreateDTO> getSameWaveCreateByDiffOrderList(WaveCreateDTO createDTO,
        List<OutStockOrderPO> lstOtherOrders, WavesStrategyDTO wavesStrategyDTO) {
        List<WaveCreateDTO> lstCreateDTO = new ArrayList<>();
        Map<String, List<OutStockOrderPO>> outStockOrderGroup =
            lstOtherOrders.stream().collect(Collectors.groupingBy(ele -> String.format("%s", ele.getBoundNo())));
        outStockOrderGroup.forEach((boundNo, orderList) -> {
            if (Objects.equals(boundNo, "null")) {
                LOG.info("[多出库批次创建波次]出库批次号为空，订单号：{}",
                    orderList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
                throw new BusinessValidateException("多出库批次创建波次，出库批次号为空");
            }
            WaveCreateDTO boundWaveCreate = new WaveCreateDTO();
            BeanUtils.copyProperties(createDTO, boundWaveCreate);
            boundWaveCreate.setPassageDTO(createDTO.getPassageDTO());
            boundWaveCreate.setWavesStrategyDTO(createDTO.getWavesStrategyDTO());
            boundWaveCreate.setOrders(orderList);
            String secondSortDriverName = SplitBatchTaskByPassageBL.getDriverName(wavesStrategyDTO, orderList);
            if (StringUtils.isNotEmpty(secondSortDriverName)) {
                boundWaveCreate.setDriverName(secondSortDriverName);
            }
            lstCreateDTO.add(boundWaveCreate);
        });

        return lstCreateDTO;
    }

    public static BatchAreaAndRouteBO convertBatchRouteAndAreaInfo(ProcessBatchDTO processBatchDTO,
        WavesStrategyBO wavesStrategyDTO, List<OutStockOrderPO> orderList) {
        // 内配单的波次片区或线路显示为“收货城市+收货仓库”
        if (processBatchDTO.getAllocationFlag()) {
            return new BatchAreaAndRouteBO(processBatchDTO.getBatchName(), processBatchDTO.getBatchName());
            // ERP调拨出库单生波次时，区域名称=波次名称
        }
        if (Objects.equals(processBatchDTO.getExpressFlag(), ExpressFlagEnum.ERP调拨出库单.getType())) {
            return new BatchAreaAndRouteBO(processBatchDTO.getBatchName(), null);
        }

        OutStockOrderPO outStockOrderPO = filterOutStockOrder(wavesStrategyDTO, orderList);
        if (Objects.isNull(outStockOrderPO)) {
            return new BatchAreaAndRouteBO();
        }

        BatchAreaAndRouteBO batchAreaAndRouteBO = new BatchAreaAndRouteBO();
        if (Objects.nonNull(outStockOrderPO.getAreaId())) {
            batchAreaAndRouteBO.setAreaId(outStockOrderPO.getAreaId().toString());
        }
        if (Objects.nonNull(outStockOrderPO.getRouteId())) {
            batchAreaAndRouteBO.setRouteId(outStockOrderPO.getRouteId().toString());
        }
        batchAreaAndRouteBO.setAreaName(outStockOrderPO.getAreaName());
        batchAreaAndRouteBO.setRouteName(outStockOrderPO.getRouteName());
        batchAreaAndRouteBO.setRouteSequence(outStockOrderPO.getRouteSequence());

        return batchAreaAndRouteBO;
    }

    private static OutStockOrderPO filterOutStockOrder(WavesStrategyBO wavesStrategyDTO,
        List<OutStockOrderPO> orderList) {
        List<OutStockOrderPO> filterOrderList = filterOutStockOrderList(wavesStrategyDTO, orderList);
        if (CollectionUtils.isEmpty(filterOrderList)) {
            filterOrderList = orderList;
        }

        OutStockOrderPO outStockOrderPO = filterOrderList.stream().findFirst().get();

        if (!wavesStrategyDTO.getIsMultiOutBound() || !wavesStrategyDTO.getIsOpenSecondSort()) {
            return outStockOrderPO;
        }

        long areaCount =
            filterOrderList.stream().map(OutStockOrderPO::getAreaId).filter(Objects::nonNull).distinct().count();
        long routCount =
            filterOrderList.stream().map(OutStockOrderPO::getRouteId).filter(Objects::nonNull).distinct().count();
        if ((areaCount == 0 || areaCount == 1) && (routCount == 0 || routCount == 1)) {
            return outStockOrderPO;
        }

        return null;
    }

    private static List<OutStockOrderPO> filterOutStockOrderList(WavesStrategyBO wavesStrategyDTO,
        List<OutStockOrderPO> orderList) {
        if (Objects.isNull(wavesStrategyDTO.getOrderSelection())) {
            return orderList;
        }
        if (BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_AREA == wavesStrategyDTO.getBatchAttrSettingWay()
            .intValue()) {
            return orderList.stream().filter(p -> Objects.nonNull(p.getAreaId())).collect(Collectors.toList());
        } else if (BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_ROUTE == wavesStrategyDTO
            .getBatchAttrSettingWay().intValue()) {
            return orderList.stream().filter(p -> Objects.nonNull(p.getRouteId())).collect(Collectors.toList());
        }

        return orderList;
    }

}
