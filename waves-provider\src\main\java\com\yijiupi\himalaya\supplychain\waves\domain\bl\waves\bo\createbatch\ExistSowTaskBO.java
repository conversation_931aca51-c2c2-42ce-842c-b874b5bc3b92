package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch;

import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/26
 */
public class ExistSowTaskBO {
    /**
     * 已存在的播种任务
     */
    private SowTaskPO sowTaskPO;
    /**
     * 通道id
     */
    private Long passageId;

    /**
     * 获取 已存在的播种任务
     *
     * @return sowTaskPO 已存在的播种任务
     */
    public SowTaskPO getSowTaskPO() {
        return this.sowTaskPO;
    }

    /**
     * 设置 已存在的播种任务
     *
     * @param sowTaskPO 已存在的播种任务
     */
    public void setSowTaskPO(SowTaskPO sowTaskPO) {
        this.sowTaskPO = sowTaskPO;
    }

    /**
     * 获取 通道id
     *
     * @return passageId 通道id
     */
    public Long getPassageId() {
        return this.passageId;
    }

    /**
     * 设置 通道id
     *
     * @param passageId 通道id
     */
    public void setPassageId(Long passageId) {
        this.passageId = passageId;
    }
}
