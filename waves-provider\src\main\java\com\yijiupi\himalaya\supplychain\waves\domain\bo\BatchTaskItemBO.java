package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/13 16:32
 * @Version 1.0
 */
public class BatchTaskItemBO {

	private Integer warehouseId;

	/**
	 * skuId（赠品SKUId可能为null）
	 */
	private String skuId;

	/**
	 * 商品名称
	 */
	private String productName;

	/**
	 * 包装规格
	 */
	private String specName;
	/**
	 * 包装规格大小单位转换系数
	 */
	private BigDecimal specQuantity;

	/**
	 * 大单位名称
	 */
	private String packageName;
	/**
	 * 大单位数量
	 */
	private BigDecimal packageCount;
	/**
	 * 小单位名称
	 */
	private String unitName;
	/**
	 * 小单位数量
	 */
	private BigDecimal unitCount;
	/**
	 * 小单位总数量
	 */
	private BigDecimal unitTotalCount;

	private Date createTime;

	private String createTimeStr;

	public Integer getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(Integer warehouseId) {
		this.warehouseId = warehouseId;
	}

	public String getSkuId() {
		return skuId;
	}

	public void setSkuId(String skuId) {
		this.skuId = skuId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getSpecName() {
		return specName;
	}

	public void setSpecName(String specName) {
		this.specName = specName;
	}

	public BigDecimal getSpecQuantity() {
		return specQuantity;
	}

	public void setSpecQuantity(BigDecimal specQuantity) {
		this.specQuantity = specQuantity;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public BigDecimal getPackageCount() {
		return packageCount;
	}

	public void setPackageCount(BigDecimal packageCount) {
		this.packageCount = packageCount;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public BigDecimal getUnitCount() {
		return unitCount;
	}

	public void setUnitCount(BigDecimal unitCount) {
		this.unitCount = unitCount;
	}

	public BigDecimal getUnitTotalCount() {
		return unitTotalCount;
	}

	public void setUnitTotalCount(BigDecimal unitTotalCount) {
		this.unitTotalCount = unitTotalCount;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreateTimeStr() {
		return createTimeStr;
	}

	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr;
	}
}
