package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import com.yijiupi.himalaya.base.search.PagerCondition;

/**
 * <AUTHOR>
 * @title: BatchOrderDetailQueryDTO
 * @description:
 * @date 2022-09-29 10:39
 */
public class BatchOrderDetailQueryDTO extends PagerCondition {
    /**
     * 波次号
     */
    private String batchNo;

    /**
     * 获取 波次号
     *
     * @return batchNo 波次号
     */
    public String getBatchNo() {
        return this.batchNo;
    }

    /**
     * 设置 波次号
     *
     * @param batchNo 波次号
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }
}
