/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.waves.dto.orderpallet;

import java.io.Serializable;
import java.util.List;

/**
 * 订单托盘关系数据
 * 
 * <AUTHOR>
 * @since 2024年8月20日
 */
public class OrderPalletInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 托盘号集合
     */
    private List<String> palletNoList;

    public static OrderPalletInfoDTO of(Long orderId, String orderNo, List<String> palletNoList) {
        OrderPalletInfoDTO result = new OrderPalletInfoDTO();
        result.setOrderId(orderId);
        result.setOrderNo(orderNo);
        result.setPalletNoList(palletNoList);
        return result;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<String> getPalletNoList() {
        return palletNoList;
    }

    public void setPalletNoList(List<String> palletNoList) {
        this.palletNoList = palletNoList;
    }
}
