package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.*;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationConfigDTO;
import com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigType;
import com.yijiupi.himalaya.supplychain.service.config.IWarehouseAllocationConfigService;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchAttrSettingWayConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchGroupTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.OrderPickFlagEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.PickingTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.enums.PassagePickTypeEnum;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PassageRobotPickEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderCenterBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.SecondSortBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByRefOrderNoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.DeliveryTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;

/**
 * <AUTHOR>
 * @title: WavesStrategyBOConvertor
 * @description:
 * @date 2022-10-18 11:14
 */
@Component
public class WavesStrategyBOConvertor {

    @Autowired
    private OrderCenterBL orderCenterBL;
    @Autowired
    private SecondSortBL secondSortBl;
    @Autowired
    private BatchTitleConvertor batchTitleConvertor;
    @Autowired
    private GlobalCache globalCache;

    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Reference
    private IVariableValueService iVariableValueService;
    @Reference
    private IWarehouseAllocationConfigService iWarehouseAllocationConfigService;

    private static final String OPEN_TRAY_POSITION_LOCATION_KEY = "isNeedPalletForStock";
    private static final String WAREHOUSE_PICKING_BY_ORDER = "WarehouseRealTimePickingByOrder";
    private static final String WAREHOUSE_PICKING_BY_ORDER_AREA = "WarehouseRealTimePickingByOrderWorkingArea";
    private static final Logger LOG = LoggerFactory.getLogger(WavesStrategyBOConvertor.class);

    /**
     * 拼波次策略对象
     *
     * @param warehouseId
     * @param passPickType
     * @param pickingType
     * @param pickingGroupStrategy
     * @param groupType
     * @return
     */
    public WavesStrategyBO getWavesStrategyDTOByString(Integer warehouseId, Byte passPickType, Byte pickingType,
        Byte pickingGroupStrategy, Integer groupType, Byte orderPickFlag) {
        WavesStrategyBO wavesStrategyDTO = new WavesStrategyBO();
        wavesStrategyDTO.setWarehouseId(warehouseId);
        wavesStrategyDTO.setPassPickType(passPickType);
        wavesStrategyDTO.setPickingType(pickingType);
        wavesStrategyDTO.setPickingGroupStrategy(pickingGroupStrategy);
        wavesStrategyDTO.setGroupType(groupType);
        wavesStrategyDTO.setOrderPickFlag(orderPickFlag);

        return initCommon(wavesStrategyDTO);
    }

    public WavesStrategyBO createByBuilder(WavesStrategyBO.WavesStrategyBOBuilder builder) {
        WavesStrategyBO wavesStrategyDTO = builder.build();

        return initCommon(wavesStrategyDTO);
    }

    private WavesStrategyBO initCommon(WavesStrategyBO wavesStrategyDTO) {
        if (wavesStrategyDTO.getPassPickType() == null) {
            wavesStrategyDTO.setPassPickType(PassagePickTypeEnum.不开启.getType());
        }
        if (wavesStrategyDTO.getPickingType() == null) {
            wavesStrategyDTO.setPickingType((byte)WavesStrategyConstants.PICKINGTYPE_ORDER);
        }
        // 如果是按订单拣货，拣货分组策略是没用的
        // 如果是按产品拣货，拣货分组策略默认是按类目
        if ((wavesStrategyDTO.getPickingGroupStrategy() == null
            || wavesStrategyDTO.getPickingGroupStrategy().intValue() == 0)
            && WavesStrategyConstants.PICKINGTYPE_PRODUCT == wavesStrategyDTO.getPickingType().intValue()) {
            // 如果是按产品拣货，拣货分组方式默认为按类目拣货
            wavesStrategyDTO.setPickingGroupStrategy((byte)WavesStrategyConstants.PICKINGGROUPSTRATEGY_CATEGORY);
        }

        wavesStrategyDTO.setCreateTime(new Date());
        wavesStrategyDTO.setLastUpdateTime(new Date());
        wavesStrategyDTO.setOrderSelection((byte)0);

        Integer groupType = wavesStrategyDTO.getGroupType();

        wavesStrategyDTO.setGroupType(groupType);
        wavesStrategyDTO.setBatchAttrSettingWay((byte)BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_DEFAULT);
        Boolean pickByCustomer = globalCache.getPickByCustomerFromCache(wavesStrategyDTO.getWarehouseId());
        wavesStrategyDTO.setPickByCustomer(pickByCustomer);

        // 先把乱七八糟的字段都整合到OrderSelection中，后期再对入参做处理
        // 设置波次分组方式
        resetOrderSelection(wavesStrategyDTO);
        // 设置波次属性设置方式
        resetBatchAttrSettingWay(wavesStrategyDTO);

        Integer warehouseId = wavesStrategyDTO.getWarehouseId();

        // 是否开启订单中台
        // wavesStrategyDTO
        // .setIsOpenOrderCenter(iWarehouseQueryService.isOpenOrderCenter(wavesStrategyDTO.getWarehouseId()));

        Boolean isRobotPicking = globalCache.checkWarehouseIsRobotPicking(warehouseId);
        // 是否开启机器人拣货总开关
        wavesStrategyDTO.setOpenRobotPickConfig(BooleanUtils.isTrue(isRobotPicking) ? PassageRobotPickEnum.开启.getType()
            : PassageRobotPickEnum.未开启.getType());
        // 是否开启托盘位
        Byte openTrayPositionLocation = isNeedPallet(wavesStrategyDTO.getWarehouseId()) ? ConditionStateEnum.是.getType()
            : ConditionStateEnum.否.getType();
        wavesStrategyDTO.setOpenTrayLocationConfig(openTrayPositionLocation);
        // 是否开启仓库实时分拣
        Byte openWarehouseRealTimePickingByOrder = getWarehouseRealTimePickingByOrder(warehouseId)
            ? ConditionStateEnum.是.getType() : ConditionStateEnum.否.getType();
        wavesStrategyDTO.setOpenWarehouseRealTimePickingByOrder(openWarehouseRealTimePickingByOrder);

        List<Long> areaIds = getWarehouseRealTimePickingByOrderArea(warehouseId);
        wavesStrategyDTO.setAreaIds(areaIds);

        wavesStrategyDTO.setOpenDigitalPickingSystem(globalCache.openDigitalTag(warehouseId));
        wavesStrategyDTO
            .setOpenWarehouseSeparateAttributeConfig(globalCache.openWarehouseSeparateAttributeConfig(warehouseId));

        wavesStrategyDTO.setOpenSecondSort(globalCache.isOpenSecondSortFromCache(warehouseId));

        return wavesStrategyDTO;
    }

    private void resetBatchAttrSettingWay(WavesStrategyBO wavesStrategyBO) {
        Integer groupType = wavesStrategyBO.getGroupType();
        if (Objects.isNull(groupType)) {
            return;
        }
        // 分组方式 1:按线路，2：按片区，3：按司机
        if (BatchGroupTypeEnum.按线路.getType().equals(groupType)) {
            wavesStrategyBO.setBatchAttrSettingWay((byte)BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_ROUTE);
        } else if (BatchGroupTypeEnum.按片区.getType().equals(groupType)) {
            wavesStrategyBO.setBatchAttrSettingWay((byte)BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_AREA);
        } else if (BatchGroupTypeEnum.按订单.getType().equals(groupType)) {
            wavesStrategyBO.setBatchAttrSettingWay((byte)BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_USER);
        } else if (BatchGroupTypeEnum.按用户.getType().equals(groupType)) {
            wavesStrategyBO.setBatchAttrSettingWay((byte)BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_MAP);
        } else {
            wavesStrategyBO.setBatchAttrSettingWay((byte)BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_DEFAULT);
        }

    }

    private void resetOrderSelection(WavesStrategyBO wavesStrategyBO) {
        if (Objects.nonNull(wavesStrategyBO.getOrderSelection()) && wavesStrategyBO.getOrderSelection() != 0) {
            return;
        }

        if (Objects.equals(wavesStrategyBO.getOrderPickFlag(), OrderPickFlagEnum.按订单拆分.getType())) {
            wavesStrategyBO.setOrderSelection(getOrderOrderSelection(wavesStrategyBO));
            return;
        }
        if (Objects.equals(wavesStrategyBO.getOrderPickFlag(), OrderPickFlagEnum.按用户拆分.getType())) {
            wavesStrategyBO.setOrderSelection((byte)WavesStrategyConstants.ORDERSELECTION_USER);
            return;
        }
        if (Objects.equals(wavesStrategyBO.getOrderPickFlag(), OrderPickFlagEnum.按集货方式拆分.getType())) {
            wavesStrategyBO.setOrderSelection((byte)WavesStrategyConstants.ORDERSELECTION_SHIPPING);
            return;
        }

        Integer groupType = wavesStrategyBO.getGroupType();
        if (groupType != null) {
            // 分组方式 1:按线路，2：按片区，3：按司机
            if (BatchGroupTypeEnum.按线路.getType().equals(groupType)) {
                wavesStrategyBO.setOrderSelection((byte)WavesStrategyConstants.ORDERSELECTION_WAY);
            } else if (BatchGroupTypeEnum.按片区.getType().equals(groupType)) {
                wavesStrategyBO.setOrderSelection((byte)WavesStrategyConstants.ORDERSELECTION_AREA);
            }
        }

        if (Objects.isNull(wavesStrategyBO.getOrderPickFlag())) {
            if (Objects.isNull(wavesStrategyBO.getOrderSelection())) {
                wavesStrategyBO.setOrderSelection((byte)WavesStrategyConstants.ORDERSELECTION_NO);
            }
        }
    }

    private byte getOrderOrderSelection(WavesStrategyBO wavesStrategyBO) {
        if (BooleanUtils
            .isFalse(Objects.equals(wavesStrategyBO.getOrderPickFlag(), OrderPickFlagEnum.按订单拆分.getType()))) {
            return (byte)WavesStrategyConstants.ORDERSELECTION_NO;
        }
        if (BooleanUtils.isFalse(wavesStrategyBO.isPickByCustomer())) {
            return (byte)WavesStrategyConstants.ORDERSELECTION_ORDER;
        }

        return (byte)WavesStrategyConstants.ORDERSELECTION_USER;
    }

    private Byte handleOrderSelectionIfNo(WavesStrategyBO wavesStrategyBO) {
        if (Objects.nonNull(wavesStrategyBO.getOrderSelection()) && wavesStrategyBO.getOrderSelection() != 0) {
            return wavesStrategyBO.getOrderSelection();
        }

        if (PickingTypeEnum.订单拣货.getType() == wavesStrategyBO.getPickingType() && wavesStrategyBO.isPickByCustomer()) {
            return (byte)WavesStrategyConstants.ORDERSELECTION_USER;
        }

        return (byte)WavesStrategyConstants.ORDERSELECTION_NO;
    }

    public WavesStrategyBO createByBuilder(WavesStrategyBO.WavesStrategyBOBuilder builder,
        BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO, List<OutStockOrderPO> outOrderList) {
        WavesStrategyBO wavesStrategyDTO = builder.build();

        initCommon(wavesStrategyDTO);

        List<DeliveryTaskDTO> deliveryTaskList = batchCreateByRefOrderNoDTO.getDeliveryTaskList();
        if (CollectionUtils.isEmpty(deliveryTaskList)) {
            return wavesStrategyDTO;
        }

        if (CollectionUtils.isEmpty(outOrderList)) {
            LOG.info("[通过波次创建出库批次]出库单列表为空");
            return wavesStrategyDTO;
        }
        Integer orgId = outOrderList.get(0).getOrgId();
        Integer warehouseId = outOrderList.get(0).getWarehouseId();

        // 未开启二次分拣时，注意：这里决定不了已经开启酒批二次分拣，因为通道也要为二次分拣，但能决定没有开启二次分拣
        if (deliveryTaskList.size() == 1 || !globalCache.isOpenSecondSortFromCache(warehouseId)) {
            // TODO 移代码到创建波次的地方
            // if(processBatchDTO.getBatchName() == null){
            // processBatchDTO.setBatchName(batchTitleConvertor.getBatchTitleByDeliveryTask(deliveryTaskList.get(0)));
            // }

            LOG.info("仓库未开启二次分拣，仓库：{}，车次数量：{}", warehouseId, deliveryTaskList.size());
            return wavesStrategyDTO;
        }
        wavesStrategyDTO.setIsOpenSecondSort(true);
        // 目前，只有酒饮二次分拣，才支持多车次创建波次
        wavesStrategyDTO.setIsMultiOutBound(true);

        return wavesStrategyDTO;
    }

    public boolean isOpenTrayAndPickingRealTime(Integer warehouseId) {
        boolean needPallet = isNeedPallet(warehouseId);
        boolean warehouseRealTimePickingByOrder = getWarehouseRealTimePickingByOrder(warehouseId);
        if (BooleanUtils.isTrue(needPallet) && BooleanUtils.isTrue(warehouseRealTimePickingByOrder)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    /**
     * 检查仓库是否是否开启托盘位
     */
    public boolean getOpenTrayPositionLocation(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");

        return globalCache.getOpenTrayPositionLocation(warehouseId);
    }

    /**
     * 检查仓库是否开启按单分拣
     */
    public boolean getWarehouseRealTimePickingByOrder(Integer warehouseId) {
        return globalCache.getWarehouseRealTimePickingByOrder(warehouseId);
    }

    /**
     * 检查仓库是否开启按单分拣片区
     */
    public List<Long> getWarehouseRealTimePickingByOrderArea(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");

        VariableValueQueryDTO varQuery = new VariableValueQueryDTO();
        varQuery.setVariableKey(WAREHOUSE_PICKING_BY_ORDER_AREA);
        varQuery.setWarehouseId(warehouseId);
        if (StringUtils.isEmpty(varQuery.getVariableKey())
            && com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(varQuery.getVariableKeyList())) {
            throw new BusinessValidateException("参数key不能为空");
        }
        VariableDefAndValueDTO keyConfig = iVariableValueService.detailVariable(varQuery);
        if (keyConfig == null || StringUtils.isEmpty(keyConfig.getVariableData())) {
            return Collections.emptyList();
        }

        try {
            List<Long> areaIds = Arrays.stream(keyConfig.getVariableData().split(",")).filter(Objects::nonNull)
                .map(Long::valueOf).collect(Collectors.toList());
            return areaIds;
        } catch (Exception e) {
            LOG.error("解析片区id失败，", e);
        }

        return Collections.emptyList();
    }

    /**
     * 是否开启接力分拣模式
     */
    public boolean getOpenPalletForSortingMode(Integer warehouseId) {
        return globalCache.getOpenPalletForSortingMode(warehouseId);
    }

    /**
     * 检查仓库是否是否开启托盘位
     */
    public boolean isNeedPallet(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");

        boolean isNeedPalletForStock = globalCache.getOpenTrayPositionLocation(warehouseId);
        boolean isNeedPalletForSortingMode = globalCache.getOpenPalletForSortingMode(warehouseId);
        return isNeedPalletForStock || isNeedPalletForSortingMode;
    }

    public void resetPackageOrderPickAlone(WavesStrategyBO wavesStrategyBO) {
        // 进这里的一定是开了分仓的，所以没判断分仓
        List<WarehouseAllocationConfigDTO> warehouseAllocationConfigDTOList =
            iWarehouseAllocationConfigService.getConfigWithoutItemByWarehouseId(wavesStrategyBO.getWarehouseId());

        wavesStrategyBO.setPackageOrderPickAlone(ConditionStateEnum.否.getType().intValue());

        if (CollectionUtils.isEmpty(warehouseAllocationConfigDTOList)) {
            return;
        }
        Map<Byte, WarehouseAllocationConfigDTO> configDTOMap = warehouseAllocationConfigDTOList.stream()
            .collect(Collectors.toMap(WarehouseAllocationConfigDTO::getConfigType, v -> v));

        Byte configFeatureType = FeatureConfigConvertor.getConfigFeatureType(wavesStrategyBO.getFeatureType());
        if (Objects.isNull(configFeatureType)) {
            return;
        }

        WarehouseAllocationConfigDTO configDTO = configDTOMap.get(configFeatureType);
        if (Objects.isNull(configDTO)) {
            return;
        }
        if (Objects.isNull(configDTO.getPackageOrderPickAlone())) {
            wavesStrategyBO.setPackageOrderPickAlone(ConditionStateEnum.否.getType().intValue());
            return;
        }

        wavesStrategyBO.setPackageOrderPickAlone(configDTO.getPackageOrderPickAlone());
    }
}
