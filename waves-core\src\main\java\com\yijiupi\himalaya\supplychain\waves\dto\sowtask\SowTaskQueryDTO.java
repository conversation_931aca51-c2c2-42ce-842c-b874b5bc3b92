package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yijiupi.himalaya.base.search.PageCondition;

public class SowTaskQueryDTO extends PageCondition {
    /**
     * 城市ID
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 波次编号
     */
    private String batchNo;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 播种任务名称
     */
    private String sowTaskName;

    /**
     * 集货位ID
     */
    private Long locationId;

    /**
     * 集货位名称
     */
    private String locationName;

    /**
     * 播种状态
     */
    private List<Byte> states;

    /**
     * 创建起始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 创建结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 当前页
     */
    private Integer currentPage = 1;

    /**
     * 每页的数量
     */
    private Integer pageSize = 10;

    /**
     * 播种任务类型
     */
    private Byte sowTaskType;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 播种人
     */
    private String operatorName;
    /**
     * 分拣员名称
     */
    private String sorterName;
    /**
     * 打包状态
     */
    private List<Byte> packageStates;

    /**
     * 操作人id
     */
    private Integer operatorId;
    /**
     * 产品skuId列表
     */
    private List<Long> skuIds;

    private String productInfo;

    /**
     * 播种订单类型
     */
    private Integer batchOrderType;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;
    /**
     * 仓库id列表
     */
    private List<Integer> warehouseIds;
    /**
     * 绩效所属班次 YYYY-dd-MM
     */
    private String shiftOfPerformance;

    public String getSorterName() {
        return sorterName;
    }

    public void setSorterName(String sorterName) {
        this.sorterName = sorterName;
    }

    public List<Byte> getPackageStates() {
        return packageStates;
    }

    public void setPackageStates(List<Byte> packageStates) {
        this.packageStates = packageStates;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public String getSowTaskName() {
        return sowTaskName;
    }

    public void setSowTaskName(String sowTaskName) {
        this.sowTaskName = sowTaskName;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public List<Byte> getStates() {
        return states;
    }

    public void setStates(List<Byte> states) {
        this.states = states;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Byte getSowTaskType() {
        return sowTaskType;
    }

    public void setSowTaskType(Byte sowTaskType) {
        this.sowTaskType = sowTaskType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    /**
     * 获取 产品skuId列表
     *
     * @return skuIds 产品skuId列表
     */
    public List<Long> getSkuIds() {
        return this.skuIds;
    }

    /**
     * 设置 产品skuId列表
     *
     * @param skuIds 产品skuId列表
     */
    public void setSkuIds(List<Long> skuIds) {
        this.skuIds = skuIds;
    }

    /**
     * 获取
     *
     * @return productInfo
     */
    public String getProductInfo() {
        return this.productInfo;
    }

    /**
     * 设置
     *
     * @param productInfo
     */
    public void setProductInfo(String productInfo) {
        this.productInfo = productInfo;
    }

    public Integer getBatchOrderType() {
        return batchOrderType;
    }

    public void setBatchOrderType(Integer batchOrderType) {
        this.batchOrderType = batchOrderType;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }

    /**
     * 获取 仓库id列表
     *
     * @return warehouseIds 仓库id列表
     */
    public List<Integer> getWarehouseIds() {
        return this.warehouseIds;
    }

    /**
     * 设置 仓库id列表
     *
     * @param warehouseIds 仓库id列表
     */
    public void setWarehouseIds(List<Integer> warehouseIds) {
        this.warehouseIds = warehouseIds;
    }

    /**
     * 获取 绩效所属班次 YYYY-dd-MM
     *
     * @return shiftOfPerformance 绩效所属班次 YYYY-dd-MM
     */
    public String getShiftOfPerformance() {
        return this.shiftOfPerformance;
    }

    /**
     * 设置 绩效所属班次 YYYY-dd-MM
     *
     * @param shiftOfPerformance 绩效所属班次 YYYY-dd-MM
     */
    public void setShiftOfPerformance(String shiftOfPerformance) {
        this.shiftOfPerformance = shiftOfPerformance;
    }
}
