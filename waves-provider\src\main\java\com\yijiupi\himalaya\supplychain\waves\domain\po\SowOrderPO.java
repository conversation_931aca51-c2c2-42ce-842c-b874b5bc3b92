package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 播种任务_订单关联表
 */
public class SowOrderPO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 城市ID
     */
    private Integer orgId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 播种任务ID
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 出库单ID
     */
    private Long outStockOrderId;

    /**
     * 订单编号
     */
    private String refOrderNo;

    /**
     * 商品种类数
     */
    private Integer skuCount;

    /**
     * 大件总数
     */
    private BigDecimal packageAmount;

    /**
     * 小件总数
     */
    private BigDecimal unitAmount;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String lastUpdateUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date lastUpdateTime;

    /**
     * 已播种商品总类
     */
    private Integer sownSkuCount;

    /**
     * 已播种大件数量
     */
    private BigDecimal sownPackageAmount;

    /**
     * 已播种小件数量
     */
    private BigDecimal sownUnitAmount;

    /**
     * 播种订单序号(容器编号)
     */
    private Integer sowOrderSequence;

    /**
     * 备注
     */
    private String remark;

    /**
     * 容器货位id
     */
    private Long locationId;

    /**
     * 容器货位名称
     */
    private String locationName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Long getOutStockOrderId() {
        return outStockOrderId;
    }

    public void setOutStockOrderId(Long outStockOrderId) {
        this.outStockOrderId = outStockOrderId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public BigDecimal getPackageAmount() {
        return packageAmount;
    }

    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    public BigDecimal getUnitAmount() {
        return unitAmount;
    }

    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getSownSkuCount() {
        return sownSkuCount;
    }

    public void setSownSkuCount(Integer sownSkuCount) {
        this.sownSkuCount = sownSkuCount;
    }

    public BigDecimal getSownPackageAmount() {
        return sownPackageAmount;
    }

    public void setSownPackageAmount(BigDecimal sownPackageAmount) {
        this.sownPackageAmount = sownPackageAmount;
    }

    public BigDecimal getSownUnitAmount() {
        return sownUnitAmount;
    }

    public void setSownUnitAmount(BigDecimal sownUnitAmount) {
        this.sownUnitAmount = sownUnitAmount;
    }

    public Integer getSowOrderSequence() {
        return sowOrderSequence;
    }

    public void setSowOrderSequence(Integer sowOrderSequence) {
        this.sowOrderSequence = sowOrderSequence;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
