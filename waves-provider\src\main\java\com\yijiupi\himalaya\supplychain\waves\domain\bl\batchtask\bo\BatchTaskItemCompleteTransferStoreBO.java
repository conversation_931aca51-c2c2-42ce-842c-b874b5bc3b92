package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo;

import java.io.Serializable;
import java.util.List;

import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 移库
 * 
 * <AUTHOR>
 * @date 2024/9/12
 */
public class BatchTaskItemCompleteTransferStoreBO implements Serializable {
    /**
     * 数量为正，正方向移库
     */
    private List<PickUpDTO> positiveTransferDTOList;
    /**
     * 数量为负，反方向移库
     */
    private List<PickUpDTO> negativeTransferDTOList;

    /**
     * 获取 数量为正，正方向移库
     *
     * @return positiveTransferDTOList 数量为正，正方向移库
     */
    public List<PickUpDTO> getPositiveTransferDTOList() {
        return this.positiveTransferDTOList;
    }

    /**
     * 设置 数量为正，正方向移库
     *
     * @param positiveTransferDTOList 数量为正，正方向移库
     */
    public void setPositiveTransferDTOList(List<PickUpDTO> positiveTransferDTOList) {
        this.positiveTransferDTOList = positiveTransferDTOList;
    }

    /**
     * 获取 数量为负，反方向移库
     *
     * @return negativeTransferDTOList 数量为负，反方向移库
     */
    public List<PickUpDTO> getNegativeTransferDTOList() {
        return this.negativeTransferDTOList;
    }

    /**
     * 设置 数量为负，反方向移库
     *
     * @param negativeTransferDTOList 数量为负，反方向移库
     */
    public void setNegativeTransferDTOList(List<PickUpDTO> negativeTransferDTOList) {
        this.negativeTransferDTOList = negativeTransferDTOList;
    }
}
