package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;

/**
 * 仓库作业设置
 *
 * <AUTHOR>
 * @date 2/1/21 4:41 PM
 */
public class BatchWorkSettingDTO implements Serializable {

    private static final long serialVersionUID = 8210246869751071545L;

    /**
     * 播种方式 0：播种 1：摘果
     */
    private Integer sowType;

    /**
     * 整件收货位Id
     */
    private Long largeLocationId;

    /**
     * 整件收货位
     */
    private String largeLocationName;

    /**
     * 拆零收货位Id
     */
    private Long smallLocationId;

    /**
     * 拆零收货位
     */
    private String smallLocationName;

    /**
     * 播种库区Id
     */
    private Long sowLocationId;

    /**
     * 播种库区
     */
    private String sowLocationName;

    /**
     * 作业截止时间
     */
    private String deadline;

    public Integer getSowType() {
        return sowType;
    }

    public void setSowType(Integer sowType) {
        this.sowType = sowType;
    }

    public String getLargeLocationName() {
        return largeLocationName;
    }

    public void setLargeLocationName(String largeLocationName) {
        this.largeLocationName = largeLocationName;
    }

    public String getSmallLocationName() {
        return smallLocationName;
    }

    public void setSmallLocationName(String smallLocationName) {
        this.smallLocationName = smallLocationName;
    }

    public String getSowLocationName() {
        return sowLocationName;
    }

    public void setSowLocationName(String sowLocationName) {
        this.sowLocationName = sowLocationName;
    }

    public String getDeadline() {
        return deadline;
    }

    public void setDeadline(String deadline) {
        this.deadline = deadline;
    }

    public Long getLargeLocationId() {
        return largeLocationId;
    }

    public void setLargeLocationId(Long largeLocationId) {
        this.largeLocationId = largeLocationId;
    }

    public Long getSmallLocationId() {
        return smallLocationId;
    }

    public void setSmallLocationId(Long smallLocationId) {
        this.smallLocationId = smallLocationId;
    }

    public Long getSowLocationId() {
        return sowLocationId;
    }

    public void setSowLocationId(Long sowLocationId) {
        this.sowLocationId = sowLocationId;
    }
}
