package com.yijiupi.himalaya.supplychain.waves.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductSourceEnum;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchOrderInfoQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderInfoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.model.SaasPackageAttributeEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.model.StoreChannelEnum;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOrderDetailQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOrderInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.MergePickOrderInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.MergePickOrderInfoResultDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.OrderPrintInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.*;
import com.yijiupi.himalaya.supplychain.waves.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.PackageAttributeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.PickingTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderByProductSO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderItemProductSO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderSearchSO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderWaitDeliverySO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> 2018/3/16
 */
@Service(timeout = 120000)
public class BatchOrderInfoQueryServiceImpl implements IBatchOrderInfoQueryService {

    @Autowired
    private BatchOrderInfoBL batchOrderInfoBL;

    /**
     * 查询未拣货完成订单（非已出库和已拣货状态）
     *
     * @param warehouseId
     * @param lstOrderNo
     * @return
     */
    @Override
    public List<String> findNotCompleteBatchOrder(Integer warehouseId, List<String> lstOrderNo) {
        return batchOrderInfoBL.findNotCompleteBatchOrder(warehouseId, lstOrderNo);
    }

    @Override
    public PageList<BatchOrderInfoDTO> findBatchOrderInfoListByBatchNo(String batchNo, Integer currentPage,
        Integer pageSize) {
        PageList<BatchOrderInfoDTO> batchOrderInfoListByBatchNo =
            batchOrderInfoBL.findBatchOrderInfoListByBatchNo(batchNo, currentPage, pageSize);
        return batchOrderInfoListByBatchNo;
    }

    /**
     * 根据波次编号查询该波次关联的订单列表
     *
     * @param queryDTO
     * @return
     */
    @Override
    public PageList<BatchOrderInfoDTO> findBatchOrderDetail(BatchOrderDetailQueryDTO queryDTO) {
        PageList<BatchOrderInfoDTO> batchInfoList =
            batchOrderInfoBL.findBatchOrderInfoListByBatchNo(queryDTO.getBatchNo(), 1, 1000);
        batchInfoList.getDataList().forEach(it -> {
            it.setPickingTypeText(PickingTypeEnum.getEnumByValue(it.getPickingType()));
            List<OutStockOrderDTO> items = it.getItems();
            if (!CollectionUtils.isEmpty(items)) {
                items.forEach(item -> {
                    item.setState(item.getState() == null ? (byte)0 : item.getState());
                    item.setStateText(OutStockOrderStateEnum.getEnum(item.getState().byteValue()).name());
                    if (null != item.getPackageAttribute() && !item.getPackageAttribute().isEmpty()) {
                        item.setPackageAttributeText(PackageAttributeEnum.getType(item.getPackageAttribute().get(0)));
                    }
                });
            }
            it.setItems(items);
        });

        return batchInfoList;
    }

    /**
     * 查询可用的订单
     *
     * @param dto
     * @return
     */
    @Override
    public PageList<OutStockOrderDTO> findEnableOutStockOrderList(OutStockOrderSearchSO dto) {
        return batchOrderInfoBL.findEnableOutStockOrderList(dto);
    }

    /**
     * 查询出库单列表
     */
    @Override
    public PageList<OutStockOrderDTO> findOutStockOrderList(OutStockOrderSearchSO dto) {
        return batchOrderInfoBL.findOutStockOrderList(dto);
    }

    /**
     * 查询订单打印数据
     */
    @Override
    public List<OrderPrintInfoDTO> findOrderPrintInfo(Integer orgId, Integer warehouseId) {
        AssertUtils.notNull(orgId, "城市id不能为空");
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        return batchOrderInfoBL.findOrderPrintInfo(orgId, warehouseId);
    }

    /**
     * 查找出库单待出库的产品集合
     */
    @Override
    public List<Long> listOutStockOrderProduct(OutStockOrderItemProductSO so) {
        AssertUtils.notNull(so.getCityId(), "城市id不能为空");
        AssertUtils.notNull(so.getWarehouseId(), "仓库id不能为空");
        return batchOrderInfoBL.listOutStockOrderProduct(so);
    }

    /**
     * 查找待出库订单项列表
     */
    @Override
    public PageList<OutStockOrderWaitDeliveryDTO> listOutStockOrderItemWaitDelivery(OutStockOrderWaitDeliverySO so) {
        AssertUtils.notNull(so, "查找待出库订单项参数不能为空");
        AssertUtils.notEmpty(so.getSkuIds(), "产品skuId不能为空");
        AssertUtils.notNull(so.getCityId(), "城市id不能为空");
        AssertUtils.notNull(so.getWarehouseId(), "仓库id不能为空");
        return batchOrderInfoBL.listOutStockOrderItemWaitDelivery(so);
    }

    /**
     * 根据波次id查询订单产品明细
     */
    @Override
    public PageList<OutStockOrderByProductDTO> listOrderProductByBatchId(OutStockOrderByProductSO so) {
        AssertUtils.notNull(so, "查询订单产品明细参数不能为空");
        AssertUtils.notNull(so.getBatchId(), "波次id不能为空");
        return batchOrderInfoBL.listOrderProductByBatchId(so);
    }

    /**
     * 根据波次id查询产品汇总
     */
    @Override
    public PageList<OutStockOrderByProductDTO> listProductGroupByBatchId(OutStockOrderByProductSO so) {
        AssertUtils.notNull(so, "查询产品汇总参数不能为空");
        AssertUtils.notNull(so.getBatchId(), "波次id不能为空");
        return batchOrderInfoBL.listProductGroupByBatchId(so);
    }

    /**
     * 查询待出库的延迟配送订单
     */
    @Override
    public List<OutStockOrderDTO> findDelayOrderByWarehouseId(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        return batchOrderInfoBL.findDelayOrderByWarehouseId(warehouseId);
    }

    /**
     * 获取产品分配波次的数量
     *
     * @return
     */
    @Override
    public Map<Long, ProductAllotTypeDTO> getProductAllotCountMap(Integer cityId, Integer warehouseId,
        List<Long> productSkuIds) {
        return batchOrderInfoBL.getProductAllotCountMap(cityId, warehouseId, productSkuIds);
    }

    @Override
    public PageList<OutStockOrderDTO> findOutStockOrderList(OutStockOrderSearchSO dto, boolean descending) {
        return batchOrderInfoBL.findOutStockOrderList(dto, false);
    }

    @Override
    public PageList<OutStockOrderDTO> findOutStockOrderItemList(OutStockOrderSearchSO dto, boolean descending) {
        return batchOrderInfoBL.findOutStockOrderItemList(dto, descending);
    }

    /**
     * 查询出库单项变更记录
     *
     * @return
     */
    @Override
    public PageList<OutStockOrderItemChangeRecordDTO>
        listOrderItemChangeRecord(OutStockOrderItemChangeRecordSO recordSO) {
        return batchOrderInfoBL.listOrderItemChangeRecord(recordSO);
    }

    /**
     * 根据订单ID获取订单明细
     *
     * @return
     */
    @Override
    public List<OutStockOrderItemDTO> listOutStockOrderItemByOrderId(Long orderId) {
        return batchOrderInfoBL.listOutStockOrderItemByOrderId(orderId);
    }

    /**
     * 查询订单的快递面单信息
     */
    @Override
    public String findExpressBillByOrderNo(Integer cityId, Integer warehouseId, String orderNo) {
        return batchOrderInfoBL.findExpressBillByOrderNo(cityId, warehouseId, orderNo);
    }

    @Override
    public List<OrderPickSequenceDTO> listPickSequence(OrderPickSequenceQueryDTO queryDTO) {
        return batchOrderInfoBL.listPickSequence(queryDTO);
    }

    @Override
    public PageList<OutStockOrderDTO> pageListOrder(OutStockOrderSearchSO dto) {
        PageList<OutStockOrderDTO> outStockOrderList = batchOrderInfoBL.findOutStockOrderList(dto, false);
        if (Objects.nonNull(outStockOrderList) && !CollectionUtils.isEmpty(outStockOrderList.getDataList())) {
            outStockOrderList.getDataList().forEach(it -> {
                if (it.getState() != null) {
                    OutStockOrderStateEnum outStockOrderStateEnum = OutStockOrderStateEnum.getEnum(it.getState().byteValue());
                    if (outStockOrderStateEnum != null) {
                        it.setStateText(outStockOrderStateEnum.name());
                    }
                }
                if (!CollectionUtils.isEmpty(it.getItemList())) {
                    it.getItemList().forEach(item -> {
                        item.setChannelText(StoreChannelEnum.getEnmuName(item.getChannel()));
                        item.setSourceText(ProductSourceEnum.getEnmuName(item.getSource()));
                        item.setTaskStateText(TaskStateEnum.getType(item.getTaskState()));

                    });
                }
            });
        }
        return outStockOrderList;
    }

    @Override
    public PageList<BatchOrderInfoDTO> querySaasBatchDetails(OutStockOrderSearchSO outStockOrderSearchSO) {
        PageList<BatchOrderInfoDTO> batchInfoList =
            findBatchOrderInfoListByBatchNo(outStockOrderSearchSO.getBatchNo(), 1, 1000);
        batchInfoList.getDataList().forEach(it -> {
            it.setPickingTypeText(PickingTypeEnum.getEnumByValue(it.getPickingType()));
            List<OutStockOrderDTO> items = it.getItems();
            if (!CollectionUtils.isEmpty(items)) {
                items.forEach(item -> {
                    item.setState(item.getState() == null ? (byte)0 : item.getState());
                    item.setStateText(OutStockOrderStateEnum.getEnum(item.getState().byteValue()).name());
                    if (null != item.getPackageAttribute() && !item.getPackageAttribute().isEmpty()) {
                        item.setPackageAttributeText(
                            SaasPackageAttributeEnum.getType(item.getPackageAttribute().get(0)));
                    }
                });
            }
            it.setItems(items);
        });
        return batchInfoList;
    }

    @Override
    public PageList<OutStockOrderDTO> findOutStockOrderListNew(OutStockOrderSearchSO dto) {
        return batchOrderInfoBL.findOutStockOrderList(dto, false);
    }

    @Override
    public PageList<OutStockOrderPagesDTO> getOrderByCode(OrderQueryDTO dto) {
        return batchOrderInfoBL.getOrderByCode(dto);
    }

    /**
     * 扫单复核查询
     *
     * @param queryDTO
     * @return
     */
    @Override
    public ScanOrderForReviewDTO queryScanOrderInfoFroReview(ScanOrderForReviewQueryDTO queryDTO) {
        AssertUtils.hasText(queryDTO.getScanCode(), "订单信息不能为空！");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库信息不能为空！");

        return batchOrderInfoBL.queryScanOrderInfoFroReview(queryDTO);
    }

    /**
     * 托盘复核查询
     *
     * @param queryDTO
     * @return
     */
    @Override
    public PalletReviewInfoDTO queryPalletReviewInfo(PalletReviewQueryDTO queryDTO) {
        AssertUtils.hasText(queryDTO.getPalletNo(), "托盘号不能为空！");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库信息不能为空！");
        return batchOrderInfoBL.queryPalletReviewInfo(queryDTO);
    }

    /**
     * 查询是否是合并拣货
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<MergePickOrderInfoResultDTO> queryMergePickOrderInfo(MergePickOrderInfoQueryDTO queryDTO) {
        return batchOrderInfoBL.queryMergePickOrderInfo(queryDTO);
    }
}
