package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;

/**
 * <AUTHOR> 2018/3/16
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class OutStockOrderBLTest {

    @Autowired
    private OutStockOrderBL outStockOrderBL;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;

    @Test
    public void findOutStockOrderPOList() {
        // List<OutStockOrderPO> outStockOrderPOList;// 获取所有波次订单
        // OutStockOrderSearchSO orderSearchSO = new OutStockOrderSearchSO();
        // orderSearchSO.setWareHouseId(4021);
        // orderSearchSO.setTimeS(null);
        // orderSearchSO.setTimeE(null);
        //// orderSearchSO.setOrderTypes("1,2");
        // outStockOrderPOList = outStockOrderBL.findOutStockOrderPOList(orderSearchSO);
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findByOrderId(
            Arrays.asList("5072057776367704640", "998000220624096073", "5071841468748396829", "5071835550828722457")
                .stream().map(Long::valueOf).collect(Collectors.toList()));
        outStockOrderPOList.forEach((o1) -> {
            BigDecimal o2UnitTotal = o1.getItems().stream().map(OutStockOrderItemPO::getUnittotalcount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            o1.setUnitamount(o2UnitTotal);
            o1.setSkucount(o2UnitTotal.intValue());
        });
        System.out.println("排序前：" + JSON.toJSONString(outStockOrderPOList));

        outStockOrderPOList.sort((o1, o2) -> {
            BigDecimal o1UnitTotal = BigDecimal.ONE;
            BigDecimal o2UnitTotal = BigDecimal.ONE;

            return Objects.equal(o1UnitTotal, o2UnitTotal) ? -o1.getSkucount().compareTo(o2.getSkucount())
                : -o1UnitTotal.compareTo(o2UnitTotal);
        });
        System.out.println("排序后：" + JSON.toJSONString(outStockOrderPOList));
        System.out.println("排序完");
    }

    @Test
    public void listRefOrderNoByLikeTest() {
        OutStockOrderDTO outStockOrderDTO = new OutStockOrderDTO();
        outStockOrderDTO.setWarehouseId(1001);
        outStockOrderDTO.setRefOrderNo("0010");
        List<String> list = outStockOrderBL.listRefOrderNoByLike(outStockOrderDTO);
        list.forEach(s -> {
            System.out.println(s);
        });
    }

}