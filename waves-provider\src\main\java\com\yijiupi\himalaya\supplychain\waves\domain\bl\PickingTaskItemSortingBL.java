package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.RateLimiter;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.algorithm.dto.ResultWithMetrics;
import com.yijiupi.himalaya.supplychain.algorithm.dto.pickingtaskitemsort.PickingTaskItemDTO;
import com.yijiupi.himalaya.supplychain.algorithm.dto.pickingtaskitemsort.PickingTaskItemSortingParam;
import com.yijiupi.himalaya.supplychain.algorithm.dto.pickingtaskitemsort.PointDTO;
import com.yijiupi.himalaya.supplychain.algorithm.dto.pickingtaskitemsort.UpDownFloorCorrespondingPointDTO;
import com.yijiupi.himalaya.supplychain.algorithm.enums.AlgorithmExecutionStateEnum;
import com.yijiupi.himalaya.supplychain.algorithm.service.PickingTaskItemSortingService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationPointInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarehouseRoutePointDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarehouseStarirsDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.StrairsPointDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.LocationPointAbnormalBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.pickingEndingLocationInfoBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import io.getunleash.Unleash;
import io.getunleash.UnleashContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/9 09:56
 * @Version 1.0
 */
@Component
public class PickingTaskItemSortingBL {
	private static final Logger LOGGER = LoggerFactory.getLogger(PickingTaskItemSortingBL.class);
	private static final String Cache_PREFIX = "pickingTaskItemSortingResultId";
	public static final int SNACK_FOOD = 2;
	@Autowired
	private Unleash unleash;
	@Resource
	private RedisTemplate<String, Long> redisTemplate;
	@Reference
	private PickingTaskItemSortingService pickingTaskItemSortingService;
	@Autowired
	private GlobalCache globalCache;
	@Autowired
	private BatchOrderTaskBL batchOrderTaskBL;
	@Autowired
	private BatchTaskItemMapper batchTaskItemMapper;
	@Autowired
	private BatchTaskMapper batchTaskMapper;
	// 限流器，每秒允许执行次数
	private final RateLimiter rateLimiter = RateLimiter.create(1);

	public boolean triggerPickingTaskSortingSwitch(Integer warehouseId) {
		AssertUtils.notNull(warehouseId, "warehouseId is null");

		UnleashContext context = UnleashContext.builder()
				.addProperty("warehouseId", warehouseId.toString())
				.build();

		boolean result = unleash.isEnabled("trigger_picking_task_sorting_switch", context);
		/// LOGGER.info("触发拣货任务排序开关:warehouseId={},result={}", warehouseId, result);
		return result;
	}

	public boolean usePickingTaskSortingSwitch(Integer warehouseId, BatchTaskPO batchTask) {
		AssertUtils.notNull(warehouseId, "warehouseId is null");

		UnleashContext.Builder builder = UnleashContext.builder();
		builder.addProperty("warehouseId", warehouseId.toString());
		if (batchTask.getSorter() != null) {
			builder.addProperty("userId", String.valueOf(batchTask.getSorterId()));
		}
		builder.addProperty("taskNo", batchTask.getBatchTaskNo());

		return unleash.isEnabled("use_picking_task_sorting_switch", builder.build());
	}

	public ResultWithMetrics<List<PickingTaskItemDTO>> getAlgoResultWithMetrics(List<BatchTaskItemDTO> lstItemDTO) {
		AssertUtils.notEmpty(lstItemDTO, "lstItemDTO is null");
		for (BatchTaskItemDTO batchTaskItemDTO : lstItemDTO) {
			AssertUtils.notNull(batchTaskItemDTO, "batchTaskItemDTO is null");
			AssertUtils.notNull(batchTaskItemDTO.getBatchTaskNo(), "batchTaskNo is null");
		}

		Set<String> batchTaskNoSet = lstItemDTO.stream().map(BatchTaskItemDTO::getBatchTaskNo).collect(Collectors.toSet());
		if (batchTaskNoSet.size() > 1) {
			throw new BusinessValidateException("batchTaskNoSet size > 1");
		}

		String batchTaskNo = batchTaskNoSet.stream().findFirst().orElse(null);

		String key = String.format("%s:%s", Cache_PREFIX, batchTaskNo);
		Long cacheValue = redisTemplate.opsForValue().get(key);

		if (cacheValue == null) {
			ResultWithMetrics resultWithMetrics = new ResultWithMetrics();
			resultWithMetrics.setState(AlgorithmExecutionStateEnum.NOT_START.getValue());
			return resultWithMetrics;
		}

		return pickingTaskItemSortingService.pollingResult(cacheValue);
	}

	public List<BatchTaskItemDTO> sortByAlgorithmResult(List<BatchTaskItemDTO> lstItemDTO, List<PickingTaskItemDTO> lstPickingTaskItemDTO) {
		List<BatchTaskItemDTO> lstResult = new ArrayList<>();

		// PickingTaskItemDTO.pickingTaskItemId == BatchTaskItemDTO.id
		Map<String, BatchTaskItemDTO> batchTaskItemDTOMap = lstItemDTO.stream()
				.collect(Collectors.toMap(BatchTaskItemDTO::getId, v -> v));
		for (PickingTaskItemDTO pickingTaskItemDTO : lstPickingTaskItemDTO) {
			BatchTaskItemDTO batchTaskItemDTO = batchTaskItemDTOMap.get(pickingTaskItemDTO.getPickingTaskItemId());
			if (batchTaskItemDTO != null) {
				lstResult.add(batchTaskItemDTO);
			}
		}

		return lstResult;
	}

	/**
	 * 触发算法
	 * 失败不影响主流程
	 */
	public void triggerPickingItemSorting(List<BatchTaskPO> batchTaskList, List<BatchTaskItemPO> batchTaskItemList) {
		if (!triggerPickingTaskSortingSwitch(batchTaskList.get(0).getWarehouseId())) {
			// 拣货任务排序开关关闭
			LOGGER.info("拣货任务:触发排序算法开关关闭:warehouseId={}", batchTaskList.get(0).getWarehouseId());
			return;
		}

		if (CollectionUtils.isEmpty(batchTaskList)) {
			return;
		}

		if (CollectionUtils.isEmpty(batchTaskItemList)) {
			return;
		}

		// 分组
		Map<String, List<BatchTaskItemPO>> itemMap =
				batchTaskItemList.stream().collect(Collectors.groupingBy(BatchTaskItemPO::getBatchTaskId));

		// 拣货任务对应的终点
		List<String> batchTaskNos = batchTaskList.stream().map(BatchTaskPO::getBatchTaskNo).collect(Collectors.toList());
		List<pickingEndingLocationInfoBO> pickingEndingLocationInfoBOList = batchTaskMapper.findPickingEndingLocationInfoByBatchTaskNos(batchTaskNos);
		Map<String, pickingEndingLocationInfoBO> pickingEndingLocationInfoMap = pickingEndingLocationInfoBOList.stream()
				.collect(Collectors.toMap(pickingEndingLocationInfoBO::getBatchTaskNo, v -> v));

		for (BatchTaskPO batchTaskPO : batchTaskList) {
			// 深圳暂支持休食
			if (batchTaskPO.getWarehouseAllocationType() == null || batchTaskPO.getWarehouseAllocationType() != SNACK_FOOD) {
				continue;
			}

			List<BatchTaskItemPO> batchTaskItemPOList = itemMap.get(batchTaskPO.getId());
			if (CollectionUtils.isEmpty(batchTaskItemPOList)) {
				continue;
			}

			try {
				List<BatchTaskItemDTO> batchTaskItemDTOList = batchTaskItemPOList.stream().map(po -> {
					BatchTaskItemDTO dto = new BatchTaskItemDTO();
					BeanUtils.copyProperties(po, dto);
					dto.setPickingType(batchTaskPO.getPickingType());
					dto.setOrderItemId(po.getOrderItemId());
					dto.setWarehouseId(batchTaskPO.getWarehouseId());
					return dto;
				}).collect(Collectors.toList());
				// 原始排序结果
				List<BatchTaskItemDTO> originalSortRlt = batchOrderTaskBL.processTaskItemOriginalSort(batchTaskItemDTOList);

				// 终点货位信息
				String batchTaskNo = batchTaskPO.getBatchTaskNo();
				pickingEndingLocationInfoBO pickingEndingLocationInfoBO = pickingEndingLocationInfoMap.get(batchTaskNo);
				AssertUtils.notNull(pickingEndingLocationInfoBO, "拣货任务没有终点信息，batchTaskNo=" + batchTaskNo);
				AssertUtils.notNull(pickingEndingLocationInfoBO.getLocationId(), "拣货任务终点locationId is null，batchTaskNo=" + batchTaskNo);

				LOGGER.info("开始执行拣货任务排序算法:构建算法入参:pickingEndingLocationInfoBO={}", JSON.toJSONString(pickingEndingLocationInfoBO));
				PickingTaskItemSortingParam pickingTaskItemSortingParam = buildAlgoInput(pickingEndingLocationInfoBO, originalSortRlt);

				Long algoResultId = pickingTaskItemSortingService.sortPickingTaskItemAsync(pickingTaskItemSortingParam);

				String key = String.format("%s:%s", Cache_PREFIX, batchTaskPO.getBatchTaskNo());
				redisTemplate.opsForValue().set(key, algoResultId, 12, TimeUnit.HOURS);
			} catch (Exception e) {
				// 失败不影响主流程
				// 可以通过日志监控
				LOGGER.error("触发拣货任务排序算法失败:batchTaskPO={}", JSON.toJSONString(batchTaskPO), e);
			}

		}
	}

	@Async
	public void compareHistoricalPickingSorting(List<String> batchTaskNos) {
		// 拣货任务对应的终点
		List<pickingEndingLocationInfoBO> pickingEndingLocationInfoBOList = batchTaskMapper.findPickingEndingLocationInfoByBatchTaskNos(batchTaskNos);
		Map<String, pickingEndingLocationInfoBO> pickingEndingLocationInfoMap = pickingEndingLocationInfoBOList.stream()
				.collect(Collectors.toMap(pickingEndingLocationInfoBO::getBatchTaskNo, v -> v));

		StopWatch stopWatch = new StopWatch("对比历史拣货排序");
		stopWatch.start();

		for (String batchTaskNo : batchTaskNos) {
			// 获取许可，如果没有可用许可，会阻塞直到有可用许可
			double waitTime = rateLimiter.acquire();

			PageResult<BatchTaskItemDTO> pageResult =
					batchTaskItemMapper.findBatchTaskItemDTOListByBatchTaskNo(null, batchTaskNo, null, null);
			List<BatchTaskItemDTO> lstItems = pageResult.toPageList().getDataList();
			if (CollectionUtils.isEmpty(lstItems)) {
				continue;
			}

			// 终点货位信息
			pickingEndingLocationInfoBO pickingEndingLocationInfoBO = pickingEndingLocationInfoMap.get(batchTaskNo);
			AssertUtils.notNull(pickingEndingLocationInfoBO, "拣货任务没有终点信息，batchTaskNo=" + batchTaskNo);
			AssertUtils.notNull(pickingEndingLocationInfoBO.getLocationId(), "拣货任务终点locationId is null，batchTaskNo=" + batchTaskNo);

			// 原始排序结果
			List<BatchTaskItemDTO> originalSortRlt = batchOrderTaskBL.processTaskItemOriginalSort(lstItems);

			LOGGER.info("开始执行历史拣货排序算法:构建算法入参:pickingEndingLocationInfoBO={}", JSON.toJSONString(pickingEndingLocationInfoBO));
			PickingTaskItemSortingParam pickingTaskItemSortingParam = buildAlgoInput(pickingEndingLocationInfoBO, originalSortRlt);

			// call algorithm
			pickingTaskItemSortingService.sortPickingTaskItemAsync(pickingTaskItemSortingParam);

			// 休息3秒
			try {
				Thread.sleep(3000);
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
			}
			LOGGER.info("开始执行历史拣货排序算法:batchTaskNo={}", batchTaskNo);
		}

		stopWatch.stop();
		LOGGER.info("对比历史拣货排序耗时:{},batchTaskNos={}", stopWatch.toString(), batchTaskNos);
	}

	// 构建算法入参
	private PickingTaskItemSortingParam buildAlgoInput(pickingEndingLocationInfoBO pickingEndingLocationInfoBO, List<BatchTaskItemDTO>  batchTaskItemList) {
		AssertUtils.notNull(pickingEndingLocationInfoBO, "batchTask is null");
		AssertUtils.notNull(pickingEndingLocationInfoBO.getWarehouseId(), "warehouseId is null");
		AssertUtils.notNull(pickingEndingLocationInfoBO.getLocationId(), "locationId is null");

		Integer warehouseId = pickingEndingLocationInfoBO.getWarehouseId();

		// 拣货点坐标信息
		List<LocationPointInfoDTO> locationPointInfoWithCache =
				globalCache.getLocationPointInfoWithCache(warehouseId);

		// key = locationId
		Map<Long, LocationPointInfoDTO> locationPointInfoMap =
				locationPointInfoWithCache.stream().collect(Collectors.toMap(LocationPointInfoDTO::getLocationId, Function.identity(), (oldValue, newValue) -> newValue));

		PickingTaskItemSortingParam param = new PickingTaskItemSortingParam();
		param.setWarehouseId(warehouseId);
		// 拣货任务号
		String batchTaskNo = pickingEndingLocationInfoBO.getBatchTaskNo();
		param.setBatchTaskNo(batchTaskNo);

		// 上楼点 -> 下楼点
		List<WarehouseStarirsDTO> upAndDownStairsCoordinatePointsWithCache =
				globalCache.findUpAndDownStairsCoordinatePointsWithCache(warehouseId);
		List<UpDownFloorCorrespondingPointDTO> upDownFloorCorrespondingPoints = new ArrayList<>();
		if (!CollectionUtils.isEmpty(upAndDownStairsCoordinatePointsWithCache)) {
			LOGGER.warn("上楼点 -> 下楼点坐标信息:{}", JSON.toJSONString(upAndDownStairsCoordinatePointsWithCache));
			for (WarehouseStarirsDTO warehouseStarirsDTO : upAndDownStairsCoordinatePointsWithCache) {
				UpDownFloorCorrespondingPointDTO upDownFloorCorrespondingPointDTO = new UpDownFloorCorrespondingPointDTO();
				StrairsPointDTO upPoint = warehouseStarirsDTO.getUpPoint();
				AssertUtils.notNull(upPoint, "上楼点坐标信息为空");
				AssertUtils.notNull(upPoint.getX(), "上楼点x坐标为空");
				AssertUtils.notNull(upPoint.getY(), "上楼点y坐标为空");
				AssertUtils.notNull(upPoint.getFloor(), "上楼点楼层为空");
				StrairsPointDTO downPoint = warehouseStarirsDTO.getDownPoint();
				AssertUtils.notNull(downPoint, "下楼点坐标信息为空");
				AssertUtils.notNull(downPoint.getX(), "下楼点x坐标为空");
				AssertUtils.notNull(downPoint.getY(), "下楼点y坐标为空");
				AssertUtils.notNull(downPoint.getFloor(), "下楼点楼层为空");

				upDownFloorCorrespondingPointDTO.setFloor1Point(new PointDTO(upPoint.getX(), upPoint.getY(), upPoint.getFloor()));
				upDownFloorCorrespondingPointDTO.setFloor2Point(new PointDTO(downPoint.getX(), downPoint.getY(), downPoint.getFloor()));
				upDownFloorCorrespondingPoints.add(upDownFloorCorrespondingPointDTO);
			}
			param.setUpDownFloorCorrespondingPoints(upDownFloorCorrespondingPoints);
		}

		// 打印哪些拣货点信息异常
		List<LocationPointAbnormalBO> errMsgs = new ArrayList<>();

		boolean[][] grid1F = globalCache.getGridIsPassableWithCache(warehouseId, 1);
		int maxX1F = grid1F.length - 1;
		int maxY1F = grid1F[0].length - 1;

		boolean[][] grid2F = globalCache.getGridIsPassableWithCache(warehouseId, 2);
		int maxX2F = grid2F.length - 1;
		int maxY2F = grid2F[0].length - 1;

		for (UpDownFloorCorrespondingPointDTO pointDTO : upDownFloorCorrespondingPoints) {
			// 上楼点不可达
			PointDTO floor1Point = pointDTO.getFloor1Point();
			if (!grid1F[floor1Point.getX()][floor1Point.getY()]) {
				// 拣货点异常信息
				LocationPointAbnormalBO locationPointAbnormalBO = new LocationPointAbnormalBO();
				locationPointAbnormalBO.setWarehouseId(warehouseId);
				locationPointAbnormalBO.setBatchTaskNo(batchTaskNo);
				locationPointAbnormalBO.setReason(String.format("一层上楼点(%d,%d)网格中不可达", floor1Point.getX(), floor1Point.getY()));
				errMsgs.add(locationPointAbnormalBO);
			}

			PointDTO floor2Point = pointDTO.getFloor2Point();
			if (!grid2F[floor2Point.getX()][floor2Point.getY()]) {
				// 拣货点异常信息
				LocationPointAbnormalBO locationPointAbnormalBO = new LocationPointAbnormalBO();
				locationPointAbnormalBO.setWarehouseId(warehouseId);
				locationPointAbnormalBO.setBatchTaskNo(batchTaskNo);
				locationPointAbnormalBO.setReason(String.format("二层下楼点(%d,%d)网格中不可达", floor2Point.getX(), floor2Point.getY()));
				errMsgs.add(locationPointAbnormalBO);
			}
		}

		// 拣货起点
		LocationPointInfoDTO warehouseEntranceWithCache = globalCache.getWarehouseEntranceWithCache(warehouseId);
		/// AssertUtils.notNull(warehouseEntranceWithCache, "仓库入口warehouseEntranceWithCache is null，warehouseId=" + pickingEndingLocationInfoBO.getWarehouseId());
		if (warehouseEntranceWithCache != null) {
			param.setStart(new PointDTO(warehouseEntranceWithCache.getX(), warehouseEntranceWithCache.getY(), warehouseEntranceWithCache.getFloor()));

			if (warehouseEntranceWithCache.getX() > maxX1F || warehouseEntranceWithCache.getY() > maxY1F) {
				// 拣货点异常信息
				LocationPointAbnormalBO locationPointAbnormalBO = new LocationPointAbnormalBO();
				locationPointAbnormalBO.setWarehouseId(warehouseId);
				locationPointAbnormalBO.setBatchTaskNo(batchTaskNo);
				locationPointAbnormalBO.setReason(String.format("仓库入口坐标(%d,%d)超过仓库范围，maxX=%d,maxY=%d",
						warehouseEntranceWithCache.getX(), warehouseEntranceWithCache.getY(), maxX1F, maxY1F));
				errMsgs.add(locationPointAbnormalBO);
			}

		} else {
			// 拣货点异常信息
			LocationPointAbnormalBO locationPointAbnormalBO = new LocationPointAbnormalBO();
			locationPointAbnormalBO.setWarehouseId(warehouseId);
			locationPointAbnormalBO.setBatchTaskNo(batchTaskNo);
			locationPointAbnormalBO.setReason("仓库拣货起点为空");
			errMsgs.add(locationPointAbnormalBO);
		}

		// 拣货终点
		LocationPointInfoDTO locationPointInfoDTO = locationPointInfoMap.get(pickingEndingLocationInfoBO.getLocationId());
		/// AssertUtils.notNull(locationPointInfoDTO, "拣货终点locationPointInfoDTO is null，ToLocationId=" + pickingEndingLocationInfoBO.getLocationId());
		if (locationPointInfoDTO != null) {
			param.setEnd(new PointDTO(locationPointInfoDTO.getX(), locationPointInfoDTO.getY(), locationPointInfoDTO.getFloor()));
 		} else {
			// 终点为空时保持和起点一致
			param.setEnd(new PointDTO(warehouseEntranceWithCache.getX(), warehouseEntranceWithCache.getY(), warehouseEntranceWithCache.getFloor()));
			// 拣货点异常信息
			LocationPointAbnormalBO locationPointAbnormalBO = new LocationPointAbnormalBO();
			locationPointAbnormalBO.setWarehouseId(warehouseId);
			locationPointAbnormalBO.setBatchTaskNo(batchTaskNo);
			locationPointAbnormalBO.setLocationId(pickingEndingLocationInfoBO.getLocationId());
			locationPointAbnormalBO.setLocationName(pickingEndingLocationInfoBO.getLocationName());
			locationPointAbnormalBO.setReason("仓库拣货终点坐位为空");
			/// errMsgs.add(locationPointAbnormalBO);
			LOGGER.warn("仓库拣货终点未设置:被迫调整为仓库入口坐标:batchTaskNo={}:LocationName={}:locationPointAbnormalBO={}",
					batchTaskNo, pickingEndingLocationInfoBO.getLocationName(), JSON.toJSONString(locationPointAbnormalBO));
		}

		for (BatchTaskItemDTO batchTaskItemDTO : batchTaskItemList) {
			Long locationId = batchTaskItemDTO.getLocationId();
			String locationName = batchTaskItemDTO.getLocationName();

			if (locationId == null) {
				LocationPointAbnormalBO locationPointAbnormalBO = new LocationPointAbnormalBO();
				locationPointAbnormalBO.setBatchTaskNo(batchTaskNo);
				// 拣货点异常信息
				locationPointAbnormalBO.setProductName(batchTaskItemDTO.getProductName());
				locationPointAbnormalBO.setSkuId(batchTaskItemDTO.getSkuId());
				locationPointAbnormalBO.setReason("拣货明细项上locationId为空");
				errMsgs.add(locationPointAbnormalBO);
				continue;
			}

			LocationPointInfoDTO locationPointInfo = locationPointInfoMap.get(locationId);
			if (locationPointInfo == null) {
				// 拣货点异常信息
				LocationPointAbnormalBO locationPointAbnormalBO = new LocationPointAbnormalBO();
				locationPointAbnormalBO.setLocationId(locationId);
				locationPointAbnormalBO.setLocationName(locationName);
				locationPointAbnormalBO.setProductName(batchTaskItemDTO.getProductName());
				locationPointAbnormalBO.setSkuId(batchTaskItemDTO.getSkuId());
				locationPointAbnormalBO.setReason("货位坐标信息为空");

				if (batchTaskItemDTO.getTotalAmount() == null || batchTaskItemDTO.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
					LOGGER.warn("货位坐标信息为空:是赠品:坐标设置为终点:batchTaskNo={}:locationPointAbnormalBO={}:UnitPrice={}:UnitTotalCount={}",
							batchTaskNo, JSON.toJSONString(locationPointAbnormalBO), batchTaskItemDTO.getUnitPrice(), batchTaskItemDTO.getUnitTotalCount());

					// 赠品，设置终点坐标
					locationPointInfo = new LocationPointInfoDTO();
					locationPointInfo.setLocationId(locationId);
					locationPointInfo.setX(param.getEnd().x);
					locationPointInfo.setY(param.getEnd().y);
					locationPointInfo.setFloor(param.getEnd().floor);
					locationPointInfoMap.put(locationId, locationPointInfo);

				} else {
					/// errMsgs.add(locationPointAbnormalBO);
					LOGGER.warn("货位坐标信息为空:不是赠品，需要关注是否设置错误:坐标设置为终点:batchTaskNo={}:locationPointAbnormalBO={}:UnitPrice={}:UnitTotalCount={}",
							batchTaskNo, JSON.toJSONString(locationPointAbnormalBO), batchTaskItemDTO.getUnitPrice(), batchTaskItemDTO.getUnitTotalCount());

					locationPointInfo = new LocationPointInfoDTO();
					locationPointInfo.setLocationId(locationId);
					locationPointInfo.setX(param.getEnd().x);
					locationPointInfo.setY(param.getEnd().y);
					locationPointInfo.setFloor(param.getEnd().floor);
					locationPointInfoMap.put(locationId, locationPointInfo);
				}

			} else {
				if (locationPointInfo.getX() == null || locationPointInfo.getY() == null || locationPointInfo.getFloor() == null) {
					// 拣货点异常信息
					LocationPointAbnormalBO locationPointAbnormalBO = new LocationPointAbnormalBO();
					locationPointAbnormalBO.setWarehouseId(warehouseId);
					locationPointAbnormalBO.setBatchTaskNo(batchTaskNo);
					locationPointAbnormalBO.setProductName(batchTaskItemDTO.getProductName());
					locationPointAbnormalBO.setSkuId(batchTaskItemDTO.getSkuId());
					locationPointAbnormalBO.setReason(String.format("货位坐标信息(%s,%s)不完整:Floor=%s",
							locationPointInfo.getX(), locationPointInfo.getY(), locationPointInfo.getFloor()));
					errMsgs.add(locationPointAbnormalBO);
				}

				if (locationPointInfo.getFloor() == 1) {
					if (locationPointInfo.getX() > maxX1F || locationPointInfo.getY() > maxY1F) {
						// 拣货点异常信息
						LocationPointAbnormalBO locationPointAbnormalBO = new LocationPointAbnormalBO();
						locationPointAbnormalBO.setWarehouseId(warehouseId);
						locationPointAbnormalBO.setBatchTaskNo(batchTaskNo);
						locationPointAbnormalBO.setLocationId(locationId);
						locationPointAbnormalBO.setLocationName(locationName);
						locationPointAbnormalBO.setProductName(batchTaskItemDTO.getProductName());
						locationPointAbnormalBO.setSkuId(batchTaskItemDTO.getSkuId());
						locationPointAbnormalBO.setReason(String.format("货位坐标(%d,%d)超过仓库1F范围，maxX1F=%d,maxY1F=%d",
								locationPointInfo.getX(), locationPointInfo.getY(), maxX1F, maxY1F));
						errMsgs.add(locationPointAbnormalBO);
					}
				}

				if (locationPointInfo.getFloor() == 2) {
					if (locationPointInfo.getX() > maxX2F || locationPointInfo.getY() > maxY2F) {
						// 拣货点异常信息
						LocationPointAbnormalBO locationPointAbnormalBO = new LocationPointAbnormalBO();
						locationPointAbnormalBO.setWarehouseId(warehouseId);
						locationPointAbnormalBO.setBatchTaskNo(batchTaskNo);
						locationPointAbnormalBO.setLocationId(locationId);
						locationPointAbnormalBO.setLocationName(locationName);
						locationPointAbnormalBO.setProductName(batchTaskItemDTO.getProductName());
						locationPointAbnormalBO.setSkuId(batchTaskItemDTO.getSkuId());
						locationPointAbnormalBO.setReason(String.format("货位坐标(%d,%d)超过仓库2F范围，maxX2F=%d,maxY2F=%d",
								locationPointInfo.getX(), locationPointInfo.getY(), maxX2F, maxY2F));
						errMsgs.add(locationPointAbnormalBO);
					}
				}
			}
		}
		if (!CollectionUtils.isEmpty(errMsgs)) {
			LOGGER.error("拣货项信息异常:batchTaskNo={},errMsgs={}", pickingEndingLocationInfoBO.getBatchTaskNo(), JSON.toJSONString(errMsgs));
			AssertUtils.fail("拣货项信息异常:batchTaskNo=" + pickingEndingLocationInfoBO.getBatchTaskNo());
		}
		
		// 拣货点
		List<PickingTaskItemDTO> pickingTaskItems = new ArrayList<>();
		for (BatchTaskItemDTO batchTaskItemDTO : batchTaskItemList) {
			PickingTaskItemDTO pickingTaskItemDTO = new PickingTaskItemDTO();
			pickingTaskItemDTO.setPickingTaskItemId(batchTaskItemDTO.getId());
			pickingTaskItemDTO.setLocationId(batchTaskItemDTO.getLocationId());
			pickingTaskItemDTO.setLocationName(batchTaskItemDTO.getLocationName());

			AssertUtils.notNull(batchTaskItemDTO.getLocationId(), "拣货点locationId is null.ProductName=" + batchTaskItemDTO.getProductName());
			LocationPointInfoDTO locationPointInfo = locationPointInfoMap.get(batchTaskItemDTO.getLocationId());
			AssertUtils.notNull(locationPointInfo, "拣货点locationPointInfo is null.LocationId=" + batchTaskItemDTO.getLocationId());
			pickingTaskItemDTO.setPickingPoint(new PointDTO(locationPointInfo.getX(), locationPointInfo.getY(), locationPointInfo.getFloor()));
			pickingTaskItems.add(pickingTaskItemDTO);
		}

		// floor
		Set<Integer> floorSet = pickingTaskItems.stream()
				.map(PickingTaskItemDTO::getPickingPoint).map(PointDTO::getFloor).collect(Collectors.toSet());
		if (floorSet.size() > 1 && CollectionUtils.isEmpty(param.getUpDownFloorCorrespondingPoints())) {
			LOGGER.error("拣货项中存在不同楼层的货位:未设置上下楼点信息:batchTaskNo={}:floorSet={}:pickingTaskItems={}", pickingEndingLocationInfoBO.getBatchTaskNo(),
					floorSet, JSON.toJSONString(pickingTaskItems));
			AssertUtils.fail("拣货项中存在不同楼层的货位:未设置上下楼点信息:batchTaskNo=" + pickingEndingLocationInfoBO.getBatchTaskNo());
		}

		param.setPickingTaskItems(pickingTaskItems);
		return param;
	}

	public void printWarehouseGrid(Integer warehouseId, Integer floor) {
		List<WarehouseRoutePointDTO> warehouseGrid = globalCache.getWarehouseRoutePointWithCache(warehouseId);
		// 只保留第一层的点
		warehouseGrid = warehouseGrid.stream().filter(point -> point.getFloor() == floor).collect(Collectors.toList());

		// 获取grid中X和Y的最大值
		int maxX = warehouseGrid.stream().mapToInt(WarehouseRoutePointDTO::getX).max().orElse(0);
		int maxY = warehouseGrid.stream().mapToInt(WarehouseRoutePointDTO::getY).max().orElse(0);

		// 构建boolean[][] grid
		boolean[][] grid = new boolean[maxX + 1][maxY + 1];

		// 读取仓库布局网格，并将其转换为boolean[][] grid
		for (WarehouseRoutePointDTO routePoint : warehouseGrid) {
			grid[routePoint.getX()][routePoint.getY()] = routePoint.getWalkable();
		}

		// 根据grid打印网格，并标记坐标
		for (int y = 0; y <= maxY; y++) {
			for (int x = 0; x <= maxX; x++) {
				if (grid[x][y]) {
					System.out.print(String.format("□(%03d,%03d) ", x, y));
				} else {
					System.out.print(String.format("■(%03d,%03d) ", x, y));
				}
			}
			System.out.println();
		}
	}

}
