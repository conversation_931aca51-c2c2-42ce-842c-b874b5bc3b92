<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper">
    <!--auto generated Code-->
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="org_id" property="orgId" jdbcType="INTEGER"/>
        <result column="batchtaskno" property="batchtaskno" jdbcType="VARCHAR"/>
        <result column="batchtask_id" property="batchtaskId" jdbcType="VARCHAR"/>
        <result column="outstockorder_id" property="outstockorderId" jdbcType="BIGINT"/>
        <result column="reforder_id" property="reforderId" jdbcType="VARCHAR"/>
        <result column="productname" property="productname" jdbcType="VARCHAR"/>
        <result column="skuid" property="skuid" jdbcType="VARCHAR"/>
        <result column="productbrand" property="productbrand" jdbcType="VARCHAR"/>
        <result column="categoryname" property="categoryname" jdbcType="VARCHAR"/>
        <result column="specname" property="specname" jdbcType="VARCHAR"/>
        <result column="specquantity" property="specquantity" jdbcType="DECIMAL"/>
        <result column="salespec" property="salespec" jdbcType="VARCHAR"/>
        <result column="salespecquantity" property="salespecquantity" jdbcType="DECIMAL"/>
        <result column="packagename" property="packagename" jdbcType="VARCHAR"/>
        <result column="packagecount" property="packagecount" jdbcType="DECIMAL"/>
        <result column="unitname" property="unitname" jdbcType="VARCHAR"/>
        <result column="unitcount" property="unitcount" jdbcType="DECIMAL"/>
        <result column="unittotalcount" property="unittotalcount" jdbcType="DECIMAL"/>
        <result column="salemode" property="salemodel" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="createtime" property="createtime" jdbcType="DATE"/>
        <result column="locationid" property="locationId" jdbcType="INTEGER"/>
        <result column="locationName" property="locationName" jdbcType="VARCHAR"/>
        <result column="Source" property="source" jdbcType="TINYINT"/>
        <result column="Channel" property="channel" jdbcType="TINYINT"/>
        <result column="SowTask_Id" property="sowTaskId" jdbcType="BIGINT"/>
        <result column="SowTaskNo" property="sowTaskNo" jdbcType="VARCHAR"/>
        <result column="Owner_Id" property="ownerId" jdbcType="BIGINT"/>
        <result column="SecOwner_Id" property="secOwnerId" jdbcType="BIGINT"/>
        <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
        <result column="BatchNo" property="batchno" jdbcType="VARCHAR"/>
        <result column="Batch_Id" property="batchId" jdbcType="VARCHAR"/>
        <result column="SowOrder_Id" property="sowOrderId" jdbcType="BIGINT"/>
        <result column="BatchTaskItem_Id" property="batchTaskItemId" jdbcType="VARCHAR"/>
        <result column="ControlConfig_Id" property="controlConfigId" jdbcType="BIGINT"/>
        <result column="SowTaskItem_Id" property="sowTaskItemId" jdbcType="BIGINT"/>
        <result column="TotalAmount" property="totalAmount" jdbcType="DECIMAL"/>
        <result column="BusinessItemId" property="businessItemId" jdbcType="VARCHAR"/>
        <result column="IsAdvent" property="isAdvent" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="ItemAndDetailResultMap"
               type="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="org_id" property="org_id" jdbcType="INTEGER"/>
        <result column="RefOrderNo" property="refOrderNo" jdbcType="VARCHAR"/>
        <result column="BatchTaskNo" property="batchTaskNo" jdbcType="VARCHAR"/>
        <result column="Batchtask_Id" property="batchtask_Id" jdbcType="VARCHAR"/>
        <result column="Outstockorder_Id" property="outstockorder_Id" jdbcType="BIGINT"/>
        <result column="ProductName" property="productName" jdbcType="VARCHAR"/>
        <result column="SkuId" property="skuId" jdbcType="VARCHAR"/>
        <result column="ProductBrand" property="productBrand" jdbcType="VARCHAR"/>
        <result column="CategoryName" property="categoryName" jdbcType="VARCHAR"/>
        <result column="SpecName" property="specName" jdbcType="VARCHAR"/>
        <result column="SpecQuantity" property="specQuantity" jdbcType="DECIMAL"/>
        <result column="SaleSpec" property="saleSpec" jdbcType="VARCHAR"/>
        <result column="SaleSpecQuantity" property="saleSpecQuantity" jdbcType="DECIMAL"/>
        <result column="PackageName" property="packageName" jdbcType="VARCHAR"/>
        <result column="PackageCount" property="packageCount" jdbcType="DECIMAL"/>
        <result column="UnitName" property="unitName" jdbcType="VARCHAR"/>
        <result column="UnitCount" property="unitCount" jdbcType="DECIMAL"/>
        <result column="UnitTotalCount" property="unitTotalCount" jdbcType="DECIMAL"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="SellUnit" property="sellUnit" jdbcType="VARCHAR"/>
        <result column="SaleCount" property="saleCount" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="totalAmount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="payAmount" jdbcType="DECIMAL"/>
        <result column="Source" property="source" jdbcType="TINYINT"/>
        <result column="Channel" property="channel" jdbcType="TINYINT"/>
        <result column="SowTask_Id" property="sowTaskId" jdbcType="BIGINT"/>
        <result column="SowTaskNo" property="sowTaskNo" jdbcType="VARCHAR"/>
        <result column="Owner_Id" property="ownerId" jdbcType="BIGINT"/>
        <result column="SecOwner_Id" property="secOwnerId" jdbcType="BIGINT"/>
        <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
        <result column="Batch_Id" property="batchId" jdbcType="VARCHAR"/>
        <result column="SowOrder_Id" property="sowOrderId" jdbcType="BIGINT"/>
        <result column="BatchTaskItem_Id" property="batchTaskItemId" jdbcType="VARCHAR"/>
        <result column="BusinessItemId" property="businessItemId" jdbcType="VARCHAR"/>
        <result column="RefOrderNo" property="refOrderNo" jdbcType="VARCHAR"/>
        <result column="SowTaskItem_Id" property="sowTaskItemId" jdbcType="BIGINT"/>
        <result column="OriginalUnitTotalCount" property="originalUnitTotalCount" jdbcType="DECIMAL"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <collection property="outStockOrderItemDetailDTOS"
                    ofType="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDetailDTO">
            <id column="detail_Id" jdbcType="BIGINT" property="id"/>
            <result column="detail_Org_Id" jdbcType="INTEGER" property="orgId"/>
            <result column="OutStockOrderItem_Id" jdbcType="BIGINT" property="outStockOrderItemId"/>
            <result column="detail_LocationId" jdbcType="BIGINT" property="locationId"/>
            <result column="detail_LocationName" jdbcType="VARCHAR" property="locationName"/>
            <result column="ProductionDate" jdbcType="TIMESTAMP" property="productionDate"/>
            <result column="BatchTime" jdbcType="TIMESTAMP" property="batchTime"/>
            <result column="detail_UnitTotalCount" jdbcType="DECIMAL" property="unitTotalCount"/>
            <result column="detail_OutStockUnitTotalCount" jdbcType="DECIMAL" property="outStockUnitTotalCount"/>
            <result column="detail_Remark" jdbcType="VARCHAR" property="remark"/>
            <result column="detail_CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="detail_LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
            <result column="detail_ProductSpecification_Id" jdbcType="BIGINT" property="productSpecificationId"/>
            <result column="detail_Owner_Id" jdbcType="BIGINT" property="ownerId"/>
            <result column="detail_SecOwner_Id" jdbcType="BIGINT" property="secOwnerId"/>
            <result column="BatchAttributeInfoNo" jdbcType="VARCHAR" property="batchAttributeInfoNo"/>
        </collection>
    </resultMap>

    <!--auto generated Code-->
    <sql id="Base_Column_List">
        id,
        org_id,
        batchtaskno,
        batchtask_id,
        outstockorder_id,
        productname,
        skuid,
        productbrand,
        categoryname,
        specname,
        specquantity,
        salespec,
        salespecquantity,
        packagename,
        packagecount,
        unitname,
        unitcount,
        unittotalcount,
        salemodel,
        remark,
        createtime,
        locationId,
        locationName,
        Source,
        Channel,
        SowTask_Id,
        SowTaskNo,
        Owner_Id,
        SecOwner_Id,
        ProductSpecification_Id,
        BatchNo,
        Batch_Id,
        SowOrder_Id,
        BatchTaskItem_Id,
        ControlConfig_Id,
        SowTaskItem_Id,
        TotalAmount,
        OriginalUnitTotalCount,
        BusinessItemId,
        LocationId,
        LocationName,
        IsAdvent
    </sql>

    <sql id="Alias_Column_List">
        osi.id,
        osi.org_id,
        osi.batchtaskno,
        osi.batchtask_id,
        osi.outstockorder_id,
        osi.productname,
        osi.skuid,
        osi.productbrand,
        osi.categoryname,
        osi.specname,
        osi.specquantity,
        osi.salespec,
        osi.salespecquantity,
        osi.packagename,
        osi.packagecount,
        osi.unitname,
        osi.unitcount,
        osi.unittotalcount,
        osi.salemodel,
        osi.remark,
        osi.createtime,
        osi.locationId,
        osi.locationName,
        osi.Source,
        osi.Channel,
        osi.SowTask_Id,
        osi.SowTaskNo,
        osi.Owner_Id,
        osi.SecOwner_Id,
        osi.ProductSpecification_Id,
        osi.BatchNo,
        osi.Batch_Id,
        osi.SowOrder_Id,
        osi.BatchTaskItem_Id,
        osi.ControlConfig_Id,
        osi.SowTaskItem_Id,
        osi.TotalAmount
    </sql>

    <sql id="Item_Detail_Column_List">
        osi.id, osi.Org_id, osi.BatchTaskNo, osi.Batchtask_Id, osi.Outstockorder_Id, osi.ProductName,
        osi.SkuId, osi.ProductBrand, osi.CategoryName, osi.SpecName, osi.SpecQuantity, osi.SaleSpec,
        osi.SaleSpecQuantity, osi.PackageName,
        osi.PackageCount, osi.UnitName, osi.UnitCount, osi.UnitTotalCount, osi.SaleModel, osi.Remark, osi.SellUnit,
        osi.SaleCount, osi.TotalAmount,
        osi.PayAmount, osi.CreateTime, osi.Source, osi.Channel, osi.CreateUser, osi.LastUpdateUser, osi.LastUpdateTime,
        osi.LocationId, osi.LocationName,
        osi.SowTask_Id, osi.SowTaskNo, osi.Owner_Id, osi.SecOwner_Id, osi.ProductSpecification_Id, osi.BatchNo,
        osi.Batch_Id, osi.SowOrder_Id, osi.BatchTaskItem_Id,
        osi.OutStockUnitTotalCount, osi.BusinessItemId, osi.IsGift, osi.BusinessState, osi.Price,
        osi.PriceUnit,osi.ControlConfig_Id,osi.SowTaskItem_Id,osi.OriginalUnitTotalCount,

        osid.Id as detail_Id, osid.Org_Id as detail_Org_Id, osid.OutStockOrderItem_Id,
        osid.Location_Id as detail_LocationId, osid.LocationName as detail_LocationName,
        osid.ProductionDate, osid.BatchTime, osid.UnitTotalCount as detail_UnitTotalCount,
        osid.OutStockUnitTotalCount as detail_OutStockUnitTotalCount, osid.Remark as detail_Remark,
        osid.CreateTime as detail_CreateTime, osid.LastUpdateTime as detail_LastUpdateTime,
        osid.ProductSpecification_Id as detail_ProductSpecification_Id, osid.Owner_Id as detail_Owner_Id,
        osid.SecOwner_Id as detail_SecOwner_Id, osid.BatchAttributeInfoNo
    </sql>

    <update id="batchUpdateOutStockOrderItem" parameterType="java.util.List">
        update outstockorderitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="batchtaskno =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchtaskno}
                </foreach>
            </trim>
            <trim prefix="batchtask_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchtaskId}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateOutStockOrderItemSowId" parameterType="java.util.List">
        update outstockorderitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="SowTask_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowTaskId}
                </foreach>
            </trim>
            <trim prefix="SowTaskNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowTaskNo}
                </foreach>
            </trim>
            <trim prefix="SowTaskItem_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowTaskItemId}
                </foreach>
            </trim>
            <trim prefix="SowOrder_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowOrderId}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>


    <update id="batchUpdateOutStockOrderItemOfSowTask" parameterType="java.util.List">
        update outstockorderitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="batchtaskno =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchtaskno}
                </foreach>
            </trim>
            <trim prefix="batchtask_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchtaskId}
                </foreach>
            </trim>
            <trim prefix="SowTask_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowTaskId}
                </foreach>
            </trim>
            <trim prefix="SowTaskNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowTaskNo}
                </foreach>
            </trim>
            <trim prefix="SowOrder_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowOrderId}
                </foreach>
            </trim>
            <trim prefix="BatchNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchno}
                </foreach>
            </trim>
            <trim prefix="Batch_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchId}
                </foreach>
            </trim>
            <trim prefix="BatchTaskItem_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchTaskItemId}
                </foreach>
            </trim>
            <trim prefix="SowTaskItem_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowTaskItemId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
        and (BatchNo = '' or BatchNo is null)
    </update>

    <update id="forceBatchUpdateOutStockOrderItemOfSowTask" parameterType="java.util.List">
        update outstockorderitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="batchtaskno =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchtaskno}
                </foreach>
            </trim>
            <trim prefix="batchtask_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchtaskId}
                </foreach>
            </trim>
            <trim prefix="SowTask_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowTaskId}
                </foreach>
            </trim>
            <trim prefix="SowTaskNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowTaskNo}
                </foreach>
            </trim>
            <trim prefix="SowOrder_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowOrderId}
                </foreach>
            </trim>
            <trim prefix="BatchNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchno}
                </foreach>
            </trim>
            <trim prefix="Batch_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchId}
                </foreach>
            </trim>
            <trim prefix="BatchTaskItem_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchTaskItemId}
                </foreach>
            </trim>
            <trim prefix="SowTaskItem_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowTaskItemId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateOutStockOrderItemOfBatch" parameterType="java.util.List">
        update outstockorderitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="BatchNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchno}
                </foreach>
            </trim>
            <trim prefix="Batch_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchId}
                </foreach>
            </trim>
            <trim prefix="SowTask_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowTaskId}
                </foreach>
            </trim>
            <trim prefix="SowTaskNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowTaskNo}
                </foreach>
            </trim>
            <trim prefix="SowOrder_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowOrderId}
                </foreach>
            </trim>
            <trim prefix="SowTaskItem_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sowTaskItemId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
        and (BatchNo = '' or BatchNo is null)
    </update>

    <!--auto generated Code-->
    <insert id="insertList">
        INSERT INTO outstockorderitem (
        id, Org_id, BatchTaskNo,
        Batchtask_Id, Outstockorder_Id, ProductName,
        SkuId, ProductBrand, CategoryName,
        SpecName, SpecQuantity, SaleSpec,
        SaleSpecQuantity, PackageName, PackageCount,
        UnitName, UnitCount, UnitTotalCount,
        SaleModel, Remark, SellUnit,
        SaleCount, TotalAmount, PayAmount,
        Source, Channel, LocationId,LocationName,createuser,
        createtime, lastupdateuser, lastupdatetime,
        SowTask_Id, SowTaskNo, Owner_Id, SecOwner_Id, ProductSpecification_Id,
        BatchNo, Batch_Id, BatchTaskItem_Id
        )VALUES
        <foreach collection="outStockOrderItemPOs" item="item" index="index" separator=",">
            (
            #{item.id,jdbcType=BIGINT},#{item.org_id,jdbcType=INTEGER}, #{item.batchTaskNo,jdbcType=VARCHAR},
            #{item.batchtask_Id,jdbcType=VARCHAR}, #{item.outstockorder_Id,jdbcType=BIGINT},
            #{item.productName,jdbcType=VARCHAR},
            #{item.skuId,jdbcType=BIGINT}, #{item.productBrand,jdbcType=VARCHAR}, #{item.categoryName,jdbcType=VARCHAR},
            #{item.specName,jdbcType=VARCHAR}, #{item.specQuantity,jdbcType=DECIMAL}, #{item.saleSpec,jdbcType=VARCHAR},
            #{item.saleSpecQuantity,jdbcType=DECIMAL}, #{item.packageName,jdbcType=VARCHAR},
            #{item.packageCount,jdbcType=DECIMAL},
            #{item.unitName,jdbcType=VARCHAR}, #{item.unitCount,jdbcType=DECIMAL},
            #{item.unitTotalCount,jdbcType=DECIMAL},
            #{item.saleModel,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR}, #{item.sellUnit,jdbcType=VARCHAR},
            #{item.saleCount,jdbcType=DECIMAL}, #{item.totalAmount,jdbcType=DECIMAL},
            #{item.payAmount,jdbcType=DECIMAL},
            #{item.source,jdbcType=TINYINT}, #{item.channel,jdbcType=TINYINT}
            , #{item.locationId,jdbcType=BIGINT}, #{item.locationName,jdbcType=VARCHAR},
            #{item.createuser,jdbcType=VARCHAR},
            now(), #{item.lastupdateuser,jdbcType=VARCHAR},
            now(), #{item.sowTaskId,jdbcType=BIGINT}, #{item.sowTaskNo,jdbcType=VARCHAR},
            #{item.ownerId,jdbcType=BIGINT}, #{item.secOwnerId,jdbcType=BIGINT},
            #{item.productSpecificationId,jdbcType=BIGINT},
            #{item.batchno,jdbcType=VARCHAR}, #{item.batchId,jdbcType=VARCHAR}, #{item.batchTaskItemId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--auto generated Code-->
    <update id="update">
        UPDATE outstockorderitem
        <set>
            <if test="outStockOrderItemPO.remark != null">remark= #{outStockOrderItemPO.remark,jdbcType=VARCHAR},</if>
            <if test="outStockOrderItemPO.lastUpdateUser != null">
                LastUpdateUser = #{outStockOrderItemPO.lastUpdateUser,jdbcType=VARCHAR},
            </if>
            LastUpdateTime = now()
        </set>
        WHERE id = #{outStockOrderItemPO.id,jdbcType=BIGINT}
    </update>

    <update id="updateLocation">
        UPDATE outstockorderitem set
        LocationId=#{outStockOrderItemPO.locationId,jdbcType=BIGINT},
        LocationName=#{outStockOrderItemPO.locationName,jdbcType=VARCHAR},
        LastUpdateTime=now()
        WHERE id = #{outStockOrderItemPO.id,jdbcType=BIGINT}
    </update>

    <update id="updateLocationBatch">
        UPDATE outstockorderitem set
        LocationId = #{locationId,jdbcType=BIGINT},
        LocationName = #{locationName,jdbcType=VARCHAR}
        WHERE id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>


    <select id="findById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from outstockorderitem
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="findBySkuid" resultMap="BaseResultMap" parameterType="java.util.List">
        select
        <include refid="Base_Column_List"/>
        from outstockorderitem
        where skuid in
        <foreach item="item" index="index" collection="skuids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findByOutstockorderIdList" resultMap="BaseResultMap" parameterType="java.util.List">
        select
        <include refid="Base_Column_List"/>
        from outstockorderitem
        where Outstockorder_Id in
        <foreach item="item" index="index" collection="outstockorderIdList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <update id="cleanBatchTask">
        update outstockorderitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="BatchTaskNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when Batchtask_Id=#{item} then null
                </foreach>
            </trim>
            <trim prefix="Batchtask_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when Batchtask_Id=#{item} then null
                </foreach>
            </trim>
            <trim prefix="SowTaskNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when Batchtask_Id=#{item} then null
                </foreach>
            </trim>
            <trim prefix="SowTask_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when Batchtask_Id=#{item} then null
                </foreach>
            </trim>
            <trim prefix="BatchNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when Batchtask_Id=#{item} then null
                </foreach>
            </trim>
            <trim prefix="Batch_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when Batchtask_Id=#{item} then null
                </foreach>
            </trim>
            <trim prefix="SowOrder_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when Batchtask_Id=#{item} then null
                </foreach>
            </trim>
            <trim prefix="BatchTaskItem_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when Batchtask_Id=#{item} then null
                </foreach>
            </trim>
        </trim>
        where Batchtask_Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="cleanBatchTaskByBatchNo">
        update outstockorderitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="BatchTaskNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when BatchNo=#{item} then null
                </foreach>
            </trim>
            <trim prefix="Batchtask_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when BatchNo=#{item} then null
                </foreach>
            </trim>
            <trim prefix="SowTaskNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when BatchNo=#{item} then null
                </foreach>
            </trim>
            <trim prefix="SowTask_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when BatchNo=#{item} then null
                </foreach>
            </trim>
            <trim prefix="BatchNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when BatchNo=#{item} then null
                </foreach>
            </trim>
            <trim prefix="Batch_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when BatchNo=#{item} then null
                </foreach>
            </trim>
            <trim prefix="SowOrder_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when BatchNo=#{item} then null
                </foreach>
            </trim>
            <trim prefix="BatchTaskItem_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when BatchNo=#{item} then null
                </foreach>
            </trim>
            <trim prefix="SowTaskItem_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when BatchNo=#{item} then null
                </foreach>
            </trim>
        </trim>
        where BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="listIdByBatchTaskIs" resultType="java.lang.Long">
        select id
        from outstockorderitem
        where Batchtask_Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="listByBatchTaskItemId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from outstockorderitem
        where BatchTaskItem_Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="listByBatchTaskNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from outstockorderitem
        where BatchTaskNo = #{batchTaskNo}
    </select>

    <select id="listByBatchTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from outstockorderitem
        where Batchtask_Id = #{batchTaskId}
    </select>

    <select id="findBySowTaskNo"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO">
        select distinct oso.id,
        oso.org_id,
        oso.BatchTaskNo,
        oso.Batchtask_Id,
        oso.outstockorder_id,
        oso.productname,
        oso.skuid,
        oso.productbrand,
        oso.categoryname,
        oso.specname,
        oso.specquantity,
        oso.salespec,
        oso.salespecquantity,
        oso.packagename,
        oso.packagecount,
        oso.unitname,
        oso.unitcount,
        oso.unittotalcount,
        oso.salemodel,
        oso.remark,
        oso.createtime,
        oso.locationId,
        oso.locationName,
        oso.Source,
        oso.Channel,
        oso.SowTask_Id as sowTaskId,
        oso.SowTaskNo,
        oso.SowTaskItem_Id as sowTaskItemId,
        os.RefOrderNo,
        os.ShopName,
        os.UserName,
        os.OrderSequence,
        oso.Owner_Id as ownerId,
        oso.SecOwner_Id as secOwnerId,
        oso.ProductSpecification_Id as productSpecificationId,
        so.SowOrderSequence,
        so.LocationId as containerLocationId,
        so.LocationName as containerLocationName
        from outstockorderitem oso
        inner join outstockorder os on oso.Outstockorder_Id = os.id
        inner join soworder so on so.id = oso.SowOrder_Id
        where oso.SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR} and oso.Org_id = #{orgId,jdbcType=INTEGER}
    </select>
    <select id="listItemBySowTaskNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from outstockorderitem
        where Org_id = #{orgId,jdbcType=INTEGER}
        and SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR}
    </select>

    <select id="listOrderIdByBatchNo" resultType="java.lang.Long">
        select distinct Outstockorder_Id
        from outstockorderitem
        where BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="listOrderIdNoPick" resultType="java.lang.Long">
        select distinct Outstockorder_Id
        from outstockorderitem oi
        inner join batchtaskitem bti on oi.BatchTaskItem_Id = bti.Id
        where bti.TaskState in (0,1)
        and oi.Outstockorder_Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listOrderIdNoBatchTask" resultType="java.lang.Long">
        select distinct Outstockorder_Id
        from outstockorderitem oi
        where
        oi.Outstockorder_Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and (oi.Batchtask_Id = '' or oi.Batchtask_Id is null)
        and oi.UnitTotalCount > 0
    </select>

    <select id="listByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from outstockorderitem
        where id in
        <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listByBatchIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from outstockorderitem
        where batch_Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listBatchIdByIds" resultType="java.lang.String">
        select DISTINCT batch_Id
        from outstockorderitem
        where id in
        <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listOutStockOrderPrintByOrderIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPrintPO">
        SELECT
        os.id AS outStockOrderId,
        os.RefOrderNo,
        os.PackageAmount,
        os.UnitAmount,
        bt.ToLocation_Id,
        bt.ToLocationName,
        os.DetailAddress,
        os.RouteName,
        osi.SowTaskNo,
        so.State
        FROM
        outstockorderitem osi
        INNER JOIN outstockorder os ON osi.Outstockorder_Id = os.id
        INNER JOIN batchtask bt ON osi.Batchtask_Id = bt.id
        LEFT JOIN sowtask so ON osi.SowTask_Id = so.Id
        WHERE
        osi.Org_id = #{orgId,jdbcType=INTEGER}
        AND osi.Outstockorder_Id =
        <foreach collection="outStockOrderIds" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listOutStockOrderPrintByOrderId"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPrintPO">
        SELECT
        osi.id as outStockOrderItemId,
        bt.ToLocation_Id as toLocationId,
        bt.ToLocationName,
        bt.TaskState,
        osi.SowTaskNo,
        so.State,
        osi.PackageCount,
        osi.UnitCount
        FROM
        outstockorderitem osi
        INNER JOIN batchtask bt ON osi.Batchtask_Id = bt.id
        INNER JOIN sowtask so ON osi.SowTask_Id = so.Id
        WHERE
        osi.Org_id = #{orgId,jdbcType=INTEGER}
        AND osi.Outstockorder_Id = #{orderId,jdbcType=BIGINT}
    </select>

    <select id="findBySowTaskNos"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO">
        select distinct oso.id,
        oso.org_id,
        oso.BatchTaskNo,
        oso.Batchtask_Id,
        oso.outstockorder_id,
        oso.productname,
        oso.skuid,
        oso.productbrand,
        oso.categoryname,
        oso.specname,
        oso.specquantity,
        oso.salespec,
        oso.salespecquantity,
        oso.packagename,
        oso.packagecount,
        oso.unitname,
        oso.unitcount,
        oso.unittotalcount,
        oso.salemodel,
        oso.remark,
        oso.createtime,
        oso.locationId,
        oso.locationName,
        oso.locationId as toLocationId,
        oso.locationName as toLocationName,
        oso.Source,
        oso.Channel,
        oso.SowTask_Id as sowTaskId,
        oso.SowTaskNo,
        oso.IsAdvent,
        os.RefOrderNo,
        os.ShopName,
        os.UserName,
        os.OrderSequence,
        os.County,
        os.City,
        oso.Owner_Id as ownerId,
        oso.SecOwner_Id as secOwnerId,
        oso.ProductSpecification_Id as productSpecificationId,
        so.SowOrderSequence,
        so.LocationId as containerLocationId,
        so.LocationName as containerLocationName,
        oso.SowTaskItem_Id as sowTaskItemId,
        sti.State as sowTaskItemState,
        sti.SorterId as sowTaskItemSorterId,
        os.DeliveryMode,
        os.Business_Id as businessId,
        os.AllotType as allotType,
        os.Warehouse_Id as warehouseId,
        os.CreateAllocation,
        os.RouteName,os.Route_Id As RouteId,
        os.AreaName,os.Area_Id as AreaId,
        oso.BatchTaskItem_Id as batchTaskItemId,
        os.OrderSourceType as outStockOrderSource,
        os.BusinessType,
        os.outBoundType,
        os.AddressId
        from outstockorderitem oso
        inner join outstockorder os on oso.Outstockorder_Id = os.id
        inner join soworder so on so.id = oso.SowOrder_Id
        left join sowtaskitem sti on sti.Id = oso.SowTaskItem_Id
        where oso.Org_id = #{orgId,jdbcType=INTEGER}
        and oso.UnitTotalCount !=0
        and oso.SowTaskNo in
        <foreach collection="sowTaskNos" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findLocationByOrderNo"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO">
        select
        os.RefOrderNo as orderNo, osi.LocationId as locationId, osi.LocationName as locationName
        from outstockorderitem osi
        inner join outstockorder os on osi.Outstockorder_Id = os.id
        where
        os.RefOrderNo in
        <foreach collection="orderNos" item="orderNo" open="(" close=")" separator=",">
            #{orderNo,jdbcType=VARCHAR}
        </foreach>
        and os.Org_id = #{orgId,jdbcType=INTEGER}
        and os.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </select>

    <select id="findByItemIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO">
        select
        oi.id as id,
        oi.org_id as org_id,
        oi.outstockorder_id as outstockorder_Id,
        oi.skuid as skuId,
        oi.salespecquantity as saleSpecQuantity,
        oi.specquantity as specQuantity,
        oi.packagecount as packageCount,
        oi.unitcount as unitCount,
        oi.unittotalcount as unitTotalCount,
        oi.BusinessItemId as businessItemId,
        oi.PackageName as packagename,
        oi.UnitName as unitname,
        oi.ProductName as productName,
        oi.Remark as remark,
        oi.BatchTaskItem_Id as batchTaskItemId,
        oi.LocationId as locationId,
        oi.LocationName as locationName,
        oi.IsAdvent as isAdvent,
        o.Business_Id as businessId,
        o.AllotType as allotType,
        o.Warehouse_Id as warehouseId,
        o.RefOrderNo as refOrderNo,
        o.OrderType as orderType
        from outstockorderitem oi
        inner join outstockorder o on oi.Outstockorder_Id = o.id
        where oi.id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        <if test="isOrderBy != null">
            <if test="isOrderBy == 1">
                order by o.OrderCreateTime desc
            </if>
        </if>
    </select>

    <select id="findSowOrderItemsByOrderNos"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO">
        select oso.id,
        oso.org_id,
        oso.BatchTaskNo as batchTaskNo,
        oso.Batchtask_Id as batchtask_Id,
        oso.outstockorder_id as outstockorder_Id,
        oso.productname as productName,
        oso.skuid as skuId,
        oso.productbrand as productBrand,
        oso.categoryname as categoryName,
        oso.specname as specName,
        oso.specquantity as specQuantity,
        oso.salespec as saleSpec,
        oso.salespecquantity as saleSpecQuantity,
        oso.packagename as packageName,
        oso.packagecount as packageCount,
        oso.unitname as unitName,
        oso.unitcount as unitCount,
        oso.unittotalcount as unitTotalCount,
        oso.salemodel as saleModel,
        oso.remark as remark,
        oso.createtime,
        oso.Source,
        oso.Channel,
        oso.SowTask_Id as sowTaskId,
        oso.SowTaskNo,
        oso.Owner_Id as ownerId,
        oso.SecOwner_Id as secOwnerId,
        oso.ProductSpecification_Id as productSpecificationId,
        oso.LocationId as toLocationId,
        oso.LocationName as toLocationName,
        sti.State as sowTaskItemState
        from outstockorderitem oso
        inner join sowtaskitem sti on sti.Id = oso.SowTaskItem_Id
        where oso.Org_id = #{orgId,jdbcType=INTEGER}
        and oso.Id in
        <foreach collection="orderIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findBySowTaskItemIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO">
        select oso.id,
        oso.org_id,
        oso.BatchTaskNo as batchTaskNo,
        oso.Batchtask_Id as batchtask_Id,
        oso.outstockorder_id as outstockorder_Id,
        oso.productname as productName,
        oso.skuid as skuId,
        oso.productbrand as productBrand,
        oso.categoryname as categoryName,
        oso.specname as specName,
        oso.specquantity as specQuantity,
        oso.salespec as saleSpec,
        oso.salespecquantity as saleSpecQuantity,
        oso.packagename as packageName,
        oso.packagecount as packageCount,
        oso.unitname as unitName,
        oso.unitcount as unitCount,
        oso.unittotalcount as unitTotalCount,
        oso.salemodel as saleModel,
        oso.remark as remark,
        oso.createtime,
        oso.Source as source,
        oso.Channel as channel,
        oso.SowTask_Id as sowTaskId,
        oso.SowTaskNo,
        oso.Owner_Id as ownerId,
        oso.SecOwner_Id as secOwnerId,
        oso.ProductSpecification_Id as productSpecificationId,
        oso.LocationId as toLocationId,
        oso.LocationName as toLocationName,
        oso.SowTaskItem_Id as sowTaskItemId,
        os.RefOrderNo as refOrderNo,
        os.Warehouse_Id as warehouseId,
        os.Business_Id as businessId,
        os.AllotType as allotType
        from outstockorderitem oso
        inner join outstockorder os on os.Id = oso.outstockorder_id
        where oso.Org_id = #{orgId,jdbcType=INTEGER}
        and oso.SowTaskItem_Id in
        <foreach collection="sowTaskItemIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <update id="updateOutStockOrderItemLocationByIds">
        update outstockorderitem
        set LocationId = #{locationId}, LocationName = #{locationName}
        where Org_id = #{orgId}
        and Id in
        <foreach collection="itemIds" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="listOrderItemTaskInfoByBatchTaskItemIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO">
        select
        Id as refOrderItemId, Org_Id as orgId, Outstockorder_Id as refOrderId, BatchTaskItem_Id as batchTaskItemId,
        Batchtask_Id as batchTaskId, BatchTaskNo as batchTaskNo, Batch_Id as batchId, BatchNo, UnitTotalCount as
        unitTotalCount, UnitTotalCount as overSortCount,
        CreateTime as createTime, CreateUser as createUser, LastUpdateTime as lastUpdateTime, LastUpdateUser as
        lastUpdateUser
        from outstockorderitem
        where BatchTaskItem_Id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findOrderItemToLocationIdBySpecId" resultType="java.lang.Long">
        select distinct t.toLocationId
        from
        (
        select ifnull(bt.tolocation_id, osi.LocationId) as toLocationId
        from batchtask bt
        inner join outstockorderitem osi on osi.Batchtask_Id = bt.id
        where osi.Outstockorder_Id = #{orderId}
        <if test="specId != null">
            and osi.ProductSpecification_Id = #{specId,jdbcType=BIGINT}
        </if>
        <if test="ownerId != null">
            and osi.Owner_Id = #{ownerId,jdbcType=BIGINT}
        </if>
        <if test="ownerId == null">
            and osi.Owner_Id is null
        </if>
        and (bt.tolocation_id is not null or osi.LocationId is not null)
        ) t
    </select>

    <select id="findToLocationIdByOrderId" resultType="java.lang.Long">
        select distinct t.toLocationId
        from
        (
        select ifnull(bt.tolocation_id, osi.LocationId) as toLocationId
        from outstockorderitem osi
        left join batchtask bt on osi.Batchtask_Id = bt.id
        where osi.Outstockorder_Id = #{orderId}
        and (osi.LocationId is not null or bt.tolocation_id is not null)
        ) t
    </select>

    <select id="listSowTaskNoByOrderIds" resultType="java.lang.String">
        SELECT DISTINCT SowTaskNo
        FROM outstockorderitem
        where Outstockorder_Id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listOtherOrderIdBySowTaskNos" resultType="java.lang.Long">
        SELECT DISTINCT Outstockorder_Id
        FROM outstockorderitem
        where SowTaskNo in
        <foreach collection="sowTaskNoList" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and Outstockorder_Id not in
        <foreach collection="orderIdList" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>
    <select id="queryToLocationByTaskId"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO">
        select LocationName as locationName,LocationId as locationId from outstockorderitem where SowTask_Id = #{taskId}
        and Org_id =#{cityId} limit 1
    </select>
    <select id="listToLocationByTaskIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO">
        select outstockorderitem.LocationName as locationName, outstockorderitem.SowTask_Id as sowTaskId,
        outstockorderitem.LocationId as locationId,outstockorder.boundNo,outstockorderitem.SpecQuantity as specquantity,
        outstockorderitem.SaleSpecQuantity as salespecquantity,outstockorderitem.PackageName,
        outstockorderitem.UnitName,
        outstockorderitem.SkuId,
        outstockorderitem.id,
        outstockorderitem.SowTaskItem_Id as sowTaskItemId
        from outstockorderitem
        left join outstockorder
        on outstockorderitem.Outstockorder_Id = outstockorder.id
        where outstockorderitem.SowTaskItem_Id in
        <foreach collection="taskIds" item="taskId" separator="," open="(" close=")">
            #{taskId,jdbcType=BIGINT}
        </foreach>
        and outstockorderitem.Org_id =#{cityId}
    </select>
    <select id="listOutStockOrderIdBySowNo"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO">
        select outstockorderitem.Outstockorder_Id as outstockorderId,
        outstockorderitem.LocationId as locationId,
        outstockorder.boundNo
        from outstockorderitem
        left join outstockorder on outstockorderitem.Outstockorder_Id = outstockorder.id
        where SowTaskNo in
        <foreach collection="sowNo" item="no" separator="," open="(" close=")">
            #{no}
        </foreach>
        and outstockorderitem.Org_id =#{cityId}
    </select>


    <update id="updateSowTaskById">
        UPDATE outstockorderitem
        set SowTask_Id = #{sowTaskId},
        SowTaskNo = #{sowTaskNo},
        SowTaskItem_Id= #{sowTaskItemId},
        SowOrder_Id = #{sowOrderId}
        WHERE id = #{id}
    </update>

    <update id="updateBatchByPOList" parameterType="list">
        update outstockorderitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="unitcount =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.unitcount != null ">
                        when id = #{po.id} then #{po.unitcount}
                    </if>
                    <if test="po.unitcount == null ">
                        when id = #{po.id} then outstockorderitem.UnitCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="unittotalcount =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.unittotalcount != null ">
                        when id = #{po.id} then #{po.unittotalcount}
                    </if>
                    <if test="po.unittotalcount == null ">
                        when id = #{po.id} then outstockorderitem.UnitTotalCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="packagecount =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.packagecount != null ">
                        when id = #{po.id} then #{po.packagecount}
                    </if>
                    <if test="po.packagecount == null ">
                        when id = #{po.id} then outstockorderitem.PackageCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="Remark =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.remark != null ">
                        when id = #{po.id} then #{po.remark}
                    </if>
                    <if test="po.remark == null ">
                        when id = #{po.id} then outstockorderitem.Remark
                    </if>
                </foreach>
            </trim>
            <trim prefix="LocationId =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.locationId != null ">
                        when id = #{po.id} then #{po.locationId}
                    </if>
                    <if test="po.locationId == null ">
                        when id = #{po.id} then outstockorderitem.LocationId
                    </if>
                </foreach>
            </trim>
            <trim prefix="LocationName =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.locationName != null ">
                        when id = #{po.id} then #{po.locationName}
                    </if>
                    <if test="po.locationName == null ">
                        when id = #{po.id} then outstockorderitem.LocationName
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.lastUpdateUser != null ">
                        when id = #{po.id} then #{po.lastUpdateUser}
                    </if>
                    <if test="po.lastUpdateUser == null ">
                        when id = #{po.id} then outstockorderitem.LastUpdateUser
                    </if>
                </foreach>
            </trim>
            Lastupdatetime = now()
        </trim>
        where id in
        <foreach collection="list" item="po" index="index" open="(" close=")" separator=",">
            #{po.id}
        </foreach>
    </update>

    <select id="findItemAndDetailByIds" resultMap="ItemAndDetailResultMap">
        select
        <include refid="Item_Detail_Column_List"/>
        from outstockorderitem osi
        left join outstockorderitemdetail osid
        on osi.id = osid.OutStockOrderItem_Id
        and osi.Org_id = osid.Org_Id
        where osi.Org_id = #{orgId,jdbcType=INTEGER}
        <if test="ids != null and ids.size() > 0">
            and osi.id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="listOrderItem" resultMap="BaseResultMap">
        select
        <include refid="Alias_Column_List"/>,os.RefOrderNo
        from outstockorderitem osi
        inner join outstockorder os on os.id = osi.Outstockorder_Id
        where osi.Org_id = #{orgId,jdbcType=INTEGER}
        <if test="warehouseId != null">
            and os.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="ids != null and ids.size() > 0">
            and osi.id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="refOrderNoList != null and refOrderNoList.size() > 0">
            and os.RefOrderNo in
            <foreach collection="refOrderNoList" item="refOrderNo" separator="," open="(" close=")">
                #{refOrderNo,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="batchCreateState != null and batchCreateState == 0">
            AND (os.BATCHNO = '' OR os.BATCHNO IS NULL)
            AND (osi.BATCHNO = '' OR osi.BATCHNO IS NULL)
            AND (os.PACKAGEAMOUNT !=0 or os.UNITAMOUNT !=0)
        </if>
    </select>


    <select id="findListBySowTaskNos"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO">
        select distinct oso.id,
        oso.org_id,
        oso.BatchTaskNo,
        oso.Batchtask_Id,
        oso.outstockorder_id,
        oso.productname,
        oso.skuid,
        oso.productbrand,
        oso.categoryname,
        oso.specname,
        oso.specquantity,
        oso.salespec,
        oso.salespecquantity,
        oso.packagename,
        oso.packagecount,
        oso.unitname,
        oso.unitcount,
        oso.unittotalcount,
        oso.salemodel,
        oso.remark,
        oso.createtime,
        oso.locationId,
        oso.locationName,
        oso.locationId as toLocationId,
        oso.locationName as toLocationName,
        oso.Source,
        oso.Channel,
        oso.SowTask_Id as sowTaskId,
        oso.SowTaskNo,
        os.RefOrderNo,
        os.ShopName,
        os.UserName,
        os.OrderSequence,
        os.County,
        oso.Owner_Id as ownerId,
        oso.SecOwner_Id as secOwnerId,
        oso.ProductSpecification_Id as productSpecificationId,
        so.SowOrderSequence,
        so.LocationId as containerLocationId,
        so.LocationName as containerLocationName,
        oso.SowTaskItem_Id as sowTaskItemId,
        sti.State as sowTaskItemState,
        sti.SorterId as sowTaskItemSorterId,
        os.DeliveryMode,
        os.Business_Id as businessId,
        os.AllotType as allotType,
        os.Warehouse_Id as warehouseId,
        os.CreateAllocation,
        os.RouteName,os.Route_Id As RouteId,
        oso.BatchTaskItem_Id as batchTaskItemId
        from outstockorderitem oso
        inner join outstockorder os on oso.Outstockorder_Id = os.id
        inner join soworder so on so.id = oso.SowOrder_Id
        left join sowtaskitem sti on sti.Id = oso.SowTaskItem_Id
        where oso.Org_id = #{orgId,jdbcType=INTEGER}
        and oso.UnitTotalCount !=0
        and oso.SowTaskNo in
        <foreach collection="sowTaskNos" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <update id="clearBatchInfo">
        update outstockorderitem set
        batchNo = null, batch_id = null, BatchTaskNo = null,Batchtask_Id=null
        where id in
        <foreach collection="idList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="findLackItemIdByBatchNo" resultType="java.lang.Long">
        select distinct id
        from outstockorderitem
        where BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and unittotalcount != originalUnitTotalCount
    </select>

    <select id="findAllWorkingOrders" resultMap="BaseResultMap">
        select
        <include refid="Alias_Column_List"/>
        from outstockorder outstockorder
        inner join outstockorderitem osi on outstockorder.id = osi.OUTSTOCKORDER_ID
        where outstockorder.Warehouse_Id = #{warehouseId} and outstockorder.State in (1,2,3)
        order by osi.CreateTime
    </select>


    <update id="batchUpdateOutStockOrderItemLocation" parameterType="java.util.List">
        update outstockorderitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="LocationId =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.locationId}
                </foreach>
            </trim>
            <trim prefix="LocationName =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.locationName}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

</mapper>

