package com.yijiupi.himalaya.supplychain.waves.manager;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.ProductOwnerInfoDTO;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.ProductOwnerInventoryQueryParam;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.SaleInventoryInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.ordercenter.ISaleInventoryService;
import com.yijiupi.himalaya.supplychain.omscore.saleinventory.inventory.dto.ProductOwnerDTO;
import com.yijiupi.himalaya.supplychain.omscore.saleinventory.inventory.dto.SaleInventoryDTO;
import com.yijiupi.himalaya.supplychain.omscore.saleinventory.inventory.service.SaleInventoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2024-06-11 14:20
 **/
@Service
public class OmsSaleInventoryManager {

    @Reference(timeout = 600000)
    private ISaleInventoryService saleInventoryService;

    private static final Logger logger = LoggerFactory.getLogger(OmsSaleInventoryManager.class);

    public List<SaleInventoryInfoDTO> findInventoryByProductOwners(Integer cityId, Integer warehouseId, List<ProductOwnerInfoDTO> productOwnerDTOS) {
        ProductOwnerInventoryQueryParam param = ProductOwnerInventoryQueryParam.of(cityId, warehouseId, productOwnerDTOS);
        return runCaching(() -> saleInventoryService.findInventoryByProductOwners(param), Collections::emptyList);
    }

    private <T> T runCaching(Supplier<T> supplier, Supplier<T> defaultValue) {
        try {
            return supplier.get();
        } catch (Exception e) {
            logger.warn("出现异常", e);
        }
        return defaultValue.get();
    }

}
