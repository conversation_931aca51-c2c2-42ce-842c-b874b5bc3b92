package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * 分拣占用详情查询
 */
public class PickedDetailQueryDTO implements Serializable {
    /**
     * 城市ID
     */
    private Integer orgId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * skuIds
     */
    private List<Long> skuIds;

    /**
     * locationIds
     */
    private List<Long> locationIds;

    /**
     * 是否盘点
     */
    private boolean forStoreCheck;

    /**
     * 是否查询所有订单，默认false不查内配
     */
    private boolean queryAllOrder;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<Long> getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(List<Long> skuIds) {
        this.skuIds = skuIds;
    }

    public List<Long> getLocationIds() {
        return locationIds;
    }

    public void setLocationIds(List<Long> locationIds) {
        this.locationIds = locationIds;
    }

    public boolean getForStoreCheck() {
        return forStoreCheck;
    }

    public void setForStoreCheck(boolean forStoreCheck) {
        this.forStoreCheck = forStoreCheck;
    }

    public boolean getQueryAllOrder() {
        return queryAllOrder;
    }

    public void setQueryAllOrder(boolean queryAllOrder) {
        this.queryAllOrder = queryAllOrder;
    }

}
