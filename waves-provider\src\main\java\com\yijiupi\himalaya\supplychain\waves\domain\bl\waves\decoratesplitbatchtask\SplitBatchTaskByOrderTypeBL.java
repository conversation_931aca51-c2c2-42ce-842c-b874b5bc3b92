package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.decoratesplitbatchtask;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.SplitBatchTaskDecorateHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.SplitBatchTaskByOrderTypeBO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/4
 */
@Component
public class SplitBatchTaskByOrderTypeBL implements SplitBatchTaskByOrderType {

    @Autowired
    private List<SplitBatchTaskConcreteDecorateBL> decorateBLList;

    @Override
    public void splitBatchTaskByOrderType(SplitBatchTaskByOrderTypeBO bo, SplitBatchTaskDecorateHelperBO helperBO) {
        if (Objects.isNull(bo)) {
            throw new BusinessValidateException("装饰信息不能为空！");
        }

        SplitBatchTaskByOrderTypeBO finalBo = bo;

        decorateBLList.forEach(decorateBL -> {
            decorateBL.splitBatchTaskByOrderType(finalBo, helperBO);
        });
    }
}
