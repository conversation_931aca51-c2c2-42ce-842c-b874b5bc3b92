package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.yijiupi.himalaya.supplychain.waves.domain.convertor.SowConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * <AUTHOR> 2018/4/8
 */
// @RunWith(SpringRunner.class)
// @SpringBootTest(classes = WavesApp.class)
public class BatchManageServiceImplTest {

    @Autowired
    private BatchManageServiceImpl BatchManageServiceImpl;

    @Test
    public void deleteBatchOrder() throws Exception {
        // BatchManageServiceImpl.deleteBatchOrder(Collections.singletonList("BC9991804041003296"), null);
    }

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;

    @Test
    public void sowConvertTest() {
        List<OutStockOrderPO> list = outStockOrderMapper.findByOrderId(
            Arrays.asList("998000221014178745", "998000221014178746", "998000221014178747", "998000221014178748")
                .stream().map(Long::valueOf).collect(Collectors.toList()));
        Map<String, Integer> param = SowConverter.getSowSequence(list);

        Assertions.assertThat(param).isNotNull();
    }

    public static void main(String[] args) {

        Integer[] rank = {1};
        Map<String, Integer> orderSequenceMap = new HashMap<>();
        List<OutStockOrderPO> sortOutStockOrderList = new ArrayList<>();

        OutStockOrderPO po1 = new OutStockOrderPO();
        po1.setId(1111L);
        po1.setReforderno("111");

        List<OutStockOrderItemPO> items1 = new ArrayList<>();
        OutStockOrderItemPO itemPO1 = new OutStockOrderItemPO();
        itemPO1.setLocationId(123123L);
        itemPO1.setUnittotalcount(BigDecimal.ONE);
        itemPO1.setOutstockorderId(1111L);
        items1.add(itemPO1);
        po1.setItems(items1);
        sortOutStockOrderList.add(po1);

        OutStockOrderPO po2 = new OutStockOrderPO();
        po2.setId(2222L);
        po2.setReforderno("222");

        List<OutStockOrderItemPO> items2 = new ArrayList<>();
        OutStockOrderItemPO itemPO2 = new OutStockOrderItemPO();
        itemPO2.setLocationId(123123L);
        itemPO2.setUnittotalcount(BigDecimal.TEN);
        itemPO2.setOutstockorderId(2222L);
        items2.add(itemPO2);
        po2.setItems(items2);
        sortOutStockOrderList.add(po2);

        OutStockOrderPO po3 = new OutStockOrderPO();
        po3.setId(3333L);
        po3.setReforderno("333");

        List<OutStockOrderItemPO> items3 = new ArrayList<>();
        OutStockOrderItemPO itemPO3 = new OutStockOrderItemPO();
        itemPO3.setLocationId(2222L);
        itemPO3.setUnittotalcount(BigDecimal.TEN);
        itemPO3.setOutstockorderId(3333L);
        items3.add(itemPO3);
        po3.setItems(items3);
        sortOutStockOrderList.add(po3);

        // Map<Long, Set<Long>> outOrderIdMap = sortOutStockOrderList.stream().flatMap(m ->
        // m.getItems().stream()).collect(Collectors.groupingBy(OutStockOrderItemPO :: getLocationId,
        // Collectors.mapping(OutStockOrderItemPO :: getOutstockorderId, Collectors.toSet())));

        Map<String, Integer> param = SowConverter.getSowSequence(sortOutStockOrderList);
        System.err.println(param);
    }

}