package com.yijiupi.himalaya.supplychain.waves.batch;

import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOutStockDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.*;

/**
 * <AUTHOR> 2018-04-17
 */
public interface IOutStockTaskProcessService {
    /**
     * 采集出库单数据
     *
     * @param outStockOrderNewDTOList
     */
    void outStockTaskProcess(List<OutStockOrderNewDTO> outStockOrderNewDTOList);

    void outStockProcess(OutStockDTO outStockDTO);

    /**
     * 根据订单号模糊查询已拣货的订单号列表
     * 
     * @return
     */
    List<String> listRefOrderNoByLike(OutStockOrderDTO outStockOrderDTO);

    /**
     * 订单出库(不校验波次状态)
     * 
     * @param lstOrderIds
     */
    void directOutStockGroupOrder(List<Long> lstOrderIds);

    /**
     * 订单出库
     * 
     * @param directOutStockDTOS
     * @param orgId
     * @param warehouseId
     */
    void directOutStockByOrder(List<DirectOutStockDTO> directOutStockDTOS, Integer orgId, Integer warehouseId);

    /**
     * 订单出库
     * 
     * @param directOutStockDTOS
     * @param orgId
     * @param warehouseId
     */
    void directOutStockByOrderWithNoCheckBatch(List<DirectOutStockDTO> directOutStockDTOS, Integer orgId,
        Integer warehouseId);

    /**
     * 查询订单出库位
     */
    List<OutStockOrderLocationDTO> findOutStockOrderLocation(List<OutStockOrderLocationDTO> outStockOrderLocationDTOS);

    void saasDirectOutStockByOrder(BatchOutStockDTO batch);

    /**
     * 订单出库（商户）
     * 
     * @param directOutStockSetDTO
     */
    void directOutStockByOrderNew(DirectOutStockSetDTO directOutStockSetDTO);

    /**
     * 订单缺货通知修改
     */
    @Deprecated
    void invokeOrderCenterByLack(List<OutStockLackDTO> lackDTOList, StringBuffer stringBuffer, Integer operatorUserId);

    /**
     * 订单缺货通知修改
     */
    @Deprecated
    void notifyOrderCenterLack(List<OutStockLackDTO> lackDTOList, Map<String, StringBuffer> bufferMap,
        Integer operatorUserId);

    /**
     * 订单缺货通知修改
     * 
     * @param lackDTO
     */
    void notifyOrderCenterLackProduct(NotifyOrderCenterLackDTO lackDTO);

    /**
     * 重构缺货标记：支持批量
     */
    void markPartSend(OutStockMarkLackDTO dto);

    /**
     * 重构缺货标记试算接口
     */
    List<OutStockLackCalResultDTO> calMarkPartSend(OutStockLackDTO outStockLackDTO);

    /**
     * 标记缺货的时候，获取拣货中标记缺货的数据 波次状态为待拣货 和 已拣货的不用返回。 订单项对应的拣货任务明细状态是待拣货的也不用处理
     */
    List<PartMarkPickInfoDTO> getPartMarkPickInfo(PartMarkPickInfoQueryDTO queryDTO);

    /**
     * 检测是否对装箱了的订单项标记缺货或是装箱了的赠品是否有缺货
     *
     * @param dto 标记缺货参数
     */
    List<CheckHasPackagedOrderResult> checkHasPackagedOrder(OutStockMarkLackDTO dto);
}
