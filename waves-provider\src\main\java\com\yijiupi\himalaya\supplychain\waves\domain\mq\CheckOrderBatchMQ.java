package com.yijiupi.himalaya.supplychain.waves.domain.mq;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.CheckOrderRepeatDTO;

/**
 * 检查订单是否重复生波次
 *
 * <AUTHOR>
 * @date 2020/12/17 11:36
 */
@Component
public class CheckOrderBatchMQ {

    private static final Logger LOG = LoggerFactory.getLogger(CheckOrderBatchMQ.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${ex.supplychain.createBatch.checkOrder}")
    private String checkOrderBatchExchange;

    /**
     * 发送消息
     */
    public void send(CheckOrderRepeatDTO checkOrderRepeatDTO) {
        if (checkOrderRepeatDTO != null && CollectionUtils.isNotEmpty(checkOrderRepeatDTO.getRefOrderNos())) {
            LOG.info("检查订单是否重复生波次发送消息:{}", JSON.toJSONString(checkOrderRepeatDTO));
            rabbitTemplate.convertAndSend(checkOrderBatchExchange, null, checkOrderRepeatDTO);
        }
    }
}
