package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Title: himalaya-supplychain-microservice-waves
 * @Package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder
 * @Description:
 * @date 2018/5/16 20:11
 */
public class OutStockDTO implements Serializable {

    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 出库单
     */
    List<OutStockOrderNewDTO> outStockOrderDTOList;

    public List<OutStockOrderNewDTO> getOutStockOrderDTOList() {
        return outStockOrderDTOList;
    }

    public void setOutStockOrderDTOList(List<OutStockOrderNewDTO> outStockOrderDTOList) {
        this.outStockOrderDTOList = outStockOrderDTOList;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
