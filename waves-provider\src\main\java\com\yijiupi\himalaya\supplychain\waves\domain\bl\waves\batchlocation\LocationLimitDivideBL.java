package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CreateBatchLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.LocationSplitHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.LocationSplitHelperResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor.LocationSplitHelperBOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor.LocationSplitHelperResultBOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;
import com.yijiupi.himalaya.supplychain.waves.util.RobotPickConstants;

/**
 * <AUTHOR>
 * @title: LocationLimitDivideBL
 * @description: 按照库存上下限拆分订单项 小件分配完货位后，将订单项产品数量汇总出来，与原始拣货货位的补货上限比较，超过上限则转换成大件规格，将转换后的整件数量汇总出来，生成多项整件拣货任务；
 *               http://jira.yijiupidev.com/browse/SCM2-29732
 * @date 2023-02-08 10:58
 */
@Service
public class LocationLimitDivideBL {

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;

    /**
     * 小件分配完货位后，将订单项产品数量汇总出来，与原始拣货货位的补货上限比较， 超过上限则转换成大件规格，将转换后的整件数量汇总出来，生成多项整件拣货任务；
     *
     * @param bo 仓库id
     * @param unitItemList 小件订单项汇总（已设置货位）
     * @return
     */
    public List<LocationSplitHelperResultBO> divideByLimit(CreateBatchLocationBO bo,
        List<OutStockOrderItemPO> unitItemList) {
        // 设置默认值
        unitItemList.forEach(m -> m.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_SMALL_HUMAN));
        // 如果没开启机器人，不拆分
        if (BooleanUtils.isFalse(RobotPickConstants.isGlobalRobotPickOpen(bo.getWavesStrategyDTO()))) {
            return Collections.singletonList(LocationSplitHelperResultBOConvertor.convertDefault(unitItemList));
        }
        Map<Long, List<OutStockOrderItemPO>> skuGroupMap =
            unitItemList.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid));
        List<LocationSplitHelperBO> splitHelperBOList = LocationSplitHelperBOConvertor.convert(skuGroupMap, bo);

        // 将聚合后的数量 和 货位补货上下限比较
        Map<Long, ProductSkuDTO> productSkuMap = getSkuMap(bo.getWarehouseId(), splitHelperBOList);

        // 排除packageCount 为 0 的
        List<LocationSplitHelperResultBO> splitHelperResultList =
            splitHelperBOList.stream().map(m -> divideCount(m, productSkuMap)).collect(Collectors.toList());

        setDefaultUUID(splitHelperResultList);

        return splitHelperResultList;
    }

    /**
     * 获取sku货位补货上下限信息
     *
     * @param warehouseId
     * @param splitHelperBOList
     * @return
     */
    private Map<Long, ProductSkuDTO> getSkuMap(Integer warehouseId, List<LocationSplitHelperBO> splitHelperBOList) {
        List<Long> skuIds =
            splitHelperBOList.stream().map(LocationSplitHelperBO::getSkuId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuIds)) {
            return new HashMap<>(1);
        }
        List<ProductSkuDTO> productSkuList = iProductSkuQueryService.findProductBySkuFull(warehouseId, skuIds);

        return productSkuList.stream().collect(Collectors.toMap(ProductSkuDTO::getProductSkuId, v -> v));
    }

    /**
     * 拆分订单项
     * 
     * @param bo
     * @param productSkuMap
     * @return
     */
    private LocationSplitHelperResultBO divideCount(LocationSplitHelperBO bo, Map<Long, ProductSkuDTO> productSkuMap) {
        // 全是小件，直接返回
        if (bo.getPackageCount().compareTo(BigDecimal.ZERO) == 0) {
            return LocationSplitHelperResultBOConvertor.convertUnit(bo);
        }

        // 聚合后全是大件，也需要拆分，因为要走通道生成播种任务，比较库存上下限
        if (bo.getUnitCount().compareTo(BigDecimal.ZERO) == 0) {
            return LocationSplitHelperResultBOConvertor.convertPackage(bo, productSkuMap);
        }

        // 有大件有小件的是需要分摊的
        // 1、规格6， 买了4和3的；2、规格6，买了6和1的
        return LocationSplitHelperResultBOConvertor.convertSplit(bo, productSkuMap);
    }

    // 设置uuid，后面不会再拆分了，可以用这个来移除订单
    private void setDefaultUUID(List<LocationSplitHelperResultBO> splitHelperResultList) {
        splitHelperResultList.forEach(m -> {
            if (!CollectionUtils.isEmpty(m.getPackageItemList())) {
                m.getPackageItemList().stream().filter(item -> StringUtils.isBlank(item.getUuid()))
                    .forEach(item -> item.setUuid(UUID.randomUUID().toString()));
            }
            if (!CollectionUtils.isEmpty(m.getUnitItemList())) {
                m.getUnitItemList().stream().filter(item -> StringUtils.isBlank(item.getUuid()))
                    .forEach(item -> item.setUuid(UUID.randomUUID().toString()));
            }
        });
    }

}
