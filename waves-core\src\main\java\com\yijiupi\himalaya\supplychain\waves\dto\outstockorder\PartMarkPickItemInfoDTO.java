package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/7
 */
public class PartMarkPickItemInfoDTO implements Serializable {
    /**
     * businessItemId
     */
    private Long orderItemId;
    /**
     * 缺货数量
     */
    private BigDecimal lackCount;

    public PartMarkPickItemInfoDTO() {
    }

    public PartMarkPickItemInfoDTO(Long orderItemId, BigDecimal lackCount) {
        this.orderItemId = orderItemId;
        this.lackCount = lackCount;
    }

    /**
     * 获取 businessItemId
     *
     * @return orderItemId businessItemId
     */
    public Long getOrderItemId() {
        return this.orderItemId;
    }

    /**
     * 设置 businessItemId
     *
     * @param orderItemId businessItemId
     */
    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    /**
     * 获取 缺货数量
     *
     * @return lackCount 缺货数量
     */
    public BigDecimal getLackCount() {
        return this.lackCount;
    }

    /**
     * 设置 缺货数量
     *
     * @param lackCount 缺货数量
     */
    public void setLackCount(BigDecimal lackCount) {
        this.lackCount = lackCount;
    }

    public static PartMarkPickItemInfoDTO getDefault(Long businessItemId) {
        PartMarkPickItemInfoDTO partMarkPickItemInfoDTO = new PartMarkPickItemInfoDTO();
        partMarkPickItemInfoDTO.setLackCount(BigDecimal.ZERO);
        partMarkPickItemInfoDTO.setOrderItemId(businessItemId);
        return partMarkPickItemInfoDTO;
    }
}
