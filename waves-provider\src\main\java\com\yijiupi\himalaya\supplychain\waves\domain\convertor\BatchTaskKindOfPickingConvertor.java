package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SowTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchSowTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskKindOfPickingConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import org.apache.commons.lang3.BooleanUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/1/8
 */
public class BatchTaskKindOfPickingConvertor {

    public static boolean isSecPickOrSortPicking(BatchTaskSortPO batchTaskSortPO) {

        return isSecPickOrSortPicking(batchTaskSortPO.getSowType(), batchTaskSortPO.getKindOfPicking());
    }

    public static boolean isSecPickOrSortPicking(Integer sowType, Byte kindOfPicking) {
        if (Objects.equals(sowType, (int)BatchSowTypeEnum.二次分拣.getType())) {
            return Boolean.TRUE;
        }
        if (BatchTaskKindOfPickingConstants.PICKING_SORTING.equals(kindOfPicking)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    public static Integer getBatchTaskSowType(Integer sowType, Byte kindOfPicking) {
        if (BatchTaskKindOfPickingConstants.PICKING_SORTING.equals(kindOfPicking)) {
            return BatchTaskSortDTO.BATCHTASKSORT_PICKSORT_DEFAULT;
        }

        if (Objects.equals(sowType, (int)BatchSowTypeEnum.二次分拣.getType())) {
            return BatchTaskSortDTO.BATCHTASKSORT_SECPICK_DEFAULT;
        }

        return BatchTaskSortDTO.BATCHTASKSORT_SOWTYPE_DEFAULT;
    }

    public static boolean isSecondSort(BatchPO batchPO, BatchTaskPO batchTaskPO) {
        if (Objects.equals(batchPO.getSowType(), BatchSowTypeEnum.二次分拣.getType())) {
            return Boolean.TRUE;
        }
        if (BatchTaskKindOfPickingConstants.PICKING_SORTING.equals(batchTaskPO.getKindOfPicking())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    /**
     * 如果是二次分拣、大件拣货通道，且分仓仓库开启了整件单独分拣配置
     *
     * @param passageDTO
     * @param wavesStrategyBO
     * @return
     */
    public static boolean isFitRestOrderPackagePickAlone(PassageDTO passageDTO, WavesStrategyBO wavesStrategyBO,
        List<OutStockOrderPO> orderList) {
        if (Objects.isNull(passageDTO.getSowType())) {
            return Boolean.FALSE;
        }
        if (BooleanUtils.isFalse(Objects.equals(passageDTO.getSowType(), SowTypeEnum.二次分拣.getType()))) {
            return Boolean.FALSE;
        }
        if (ConditionStateEnum.否.getType().intValue() == wavesStrategyBO.getPackageOrderPickAlone()) {
            return Boolean.FALSE;
        }
        boolean isAllPackageCount = orderList.stream().flatMap(m -> m.getItems().stream())
            .allMatch(BatchTaskKindOfPickingConvertor::isPackageOrderItem);

        if (!isAllPackageCount) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }


    /**
     * 如果是二次分拣、大件拣货通道，且分仓仓库开启了整件单独分拣配置
     *
     * @param passageDTO
     * @param wavesStrategyBO
     * @return
     */
    public static List<OutStockOrderPO> hasFitRestOrderPackagePickAloneOrderList(PassageDTO passageDTO, WavesStrategyBO wavesStrategyBO,
                                                         List<OutStockOrderPO> orderList) {
        if (Objects.isNull(passageDTO.getSowType())) {
            return Collections.emptyList();
        }
        if (BooleanUtils.isFalse(Objects.equals(passageDTO.getSowType(), SowTypeEnum.二次分拣.getType()))) {
            return Collections.emptyList();
        }
        if (ConditionStateEnum.否.getType().intValue() == wavesStrategyBO.getPackageOrderPickAlone()) {
            return Collections.emptyList();
        }
        List<OutStockOrderPO> packageOrderList = orderList.stream().filter(m -> m.getItems().stream().allMatch(BatchTaskKindOfPickingConvertor::isPackageOrderItem))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(packageOrderList)) {
            return Collections.emptyList();
        }

        return packageOrderList;
    }

    public static List<OutStockOrderPO> notFitRestOrderPackagePickAloneOrderList(PassageDTO passageDTO, WavesStrategyBO wavesStrategyBO,
                                                                                 List<OutStockOrderPO> orderList) {
        if (Objects.isNull(passageDTO.getSowType())) {
            return orderList;
        }
        if (BooleanUtils.isFalse(Objects.equals(passageDTO.getSowType(), SowTypeEnum.二次分拣.getType()))) {
            return orderList;
        }

        if (ConditionStateEnum.否.getType().intValue() == wavesStrategyBO.getPackageOrderPickAlone()) {
            return orderList;
        }

        List<OutStockOrderPO> notPackageOrderList = orderList.stream().filter(m -> m.getItems().stream().anyMatch(item -> !BatchTaskKindOfPickingConvertor.isPackageOrderItem(item)))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(notPackageOrderList)) {
            return Collections.emptyList();
        }

        return notPackageOrderList;
    }

    private static boolean isPackageOrderItem(OutStockOrderItemPO orderItemPO) {
        BigDecimal[] count = orderItemPO.getUnittotalcount().divideAndRemainder(orderItemPO.getSpecquantity());

        if (count[1].compareTo(BigDecimal.ZERO) != 0) {
            return Boolean.FALSE;
        }
        if (count[0].compareTo(BigDecimal.ZERO) == 0) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

}
