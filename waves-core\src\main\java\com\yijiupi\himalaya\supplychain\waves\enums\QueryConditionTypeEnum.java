package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 条件状态
 *
 * <AUTHOR>
 * @date 2019/04/16 17:30
 */
public enum QueryConditionTypeEnum {
    /**
     * 列表查询
     */
    列表查询((byte)0),
    /**
     * 明细查询
     */
    明细查询((byte)1);

    /**
     * type
     */
    private Byte type;

    QueryConditionTypeEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

}
