package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
public class OrderItemPickCountInfoDTO implements Serializable {
    /**
     * 订单项id
     */
    private Long orderItemId;
    /**
     * 拣货数量
     */
    private BigDecimal count;
    /**
     * 原始数量
     */
    private BigDecimal itemOriCount;
    /**
     * 拣货大件
     */
    private String pickPackageCount;
    /**
     * 拣货小件
     */
    private String pickUnitCount;

    /**
     * 获取 订单项id
     *
     * @return orderItemId 订单项id
     */
    public Long getOrderItemId() {
        return this.orderItemId;
    }

    /**
     * 设置 订单项id
     *
     * @param orderItemId 订单项id
     */
    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    /**
     * 获取 拣货数量
     *
     * @return count 拣货数量
     */
    public BigDecimal getCount() {
        return this.count;
    }

    /**
     * 设置 拣货数量
     *
     * @param count 拣货数量
     */
    public void setCount(BigDecimal count) {
        this.count = count;
    }


    /**
     * 获取 原始数量
     *
     * @return itemOriCount 原始数量
     */
    public BigDecimal getItemOriCount() {
        return this.itemOriCount;
    }

    /**
     * 设置 原始数量
     *
     * @param itemOriCount 原始数量
     */
    public void setItemOriCount(BigDecimal itemOriCount) {
        this.itemOriCount = itemOriCount;
    }

    /**
     * 获取 拣货大件
     *
     * @return pickPackageCount 拣货大件
     */
    public String getPickPackageCount() {
        return this.pickPackageCount;
    }

    /**
     * 设置 拣货大件
     *
     * @param pickPackageCount 拣货大件
     */
    public void setPickPackageCount(String pickPackageCount) {
        this.pickPackageCount = pickPackageCount;
    }

    /**
     * 获取 拣货小件
     *
     * @return pickUnitCount 拣货小件
     */
    public String getPickUnitCount() {
        return this.pickUnitCount;
    }

    /**
     * 设置 拣货小件
     *
     * @param pickUnitCount 拣货小件
     */
    public void setPickUnitCount(String pickUnitCount) {
        this.pickUnitCount = pickUnitCount;
    }
}
