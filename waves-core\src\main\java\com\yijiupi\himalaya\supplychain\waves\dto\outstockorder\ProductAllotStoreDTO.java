package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 产品分配数量
 *
 * <AUTHOR>
 * @date 2019-08-21 21:21
 */
public class ProductAllotStoreDTO implements Serializable {

    private static final long serialVersionUID = 46190018881817338L;

    /**
     * 产品skuId
     */
    private Long productSkuId;

    /**
     * 类型 0：未分配波次 1：已分配波次
     */
    private Byte allotType;

    /**
     * 库存数量
     */
    private BigDecimal unitTotalCount;

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Byte getAllotType() {
        return allotType;
    }

    public void setAllotType(Byte allotType) {
        this.allotType = allotType;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }
}