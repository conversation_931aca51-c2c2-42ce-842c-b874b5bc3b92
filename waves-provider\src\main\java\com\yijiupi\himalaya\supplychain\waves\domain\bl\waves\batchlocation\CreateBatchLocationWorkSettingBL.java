package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductFeatureEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CreateBatchLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchWorkSettingDTO;

/**
 * <AUTHOR>
 * @title: CreateBatchLocationWorkSettingBL
 * @description:
 * @date 2022-09-26 14:35
 */
@Component
public class CreateBatchLocationWorkSettingBL extends CreateBatchLocationBaseBL {

    @Reference
    private IProductLocationService iProductLocationService;

    @Override
    public boolean support(CreateBatchLocationBO bo) {
        if (Objects.nonNull(bo.getWorkSetting())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    protected boolean doSupport(CreateBatchLocationBO bo) {
        return Boolean.TRUE;
    }

    @Override
    public List<OutStockOrderPO> doSetLocation(List<OutStockOrderPO> outStockOrderPOList, CreateBatchLocationBO bo) {
        String msg = "";
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return outStockOrderPOList;
        }

        Integer warehouseId = bo.getWarehouseId();
        BatchWorkSettingDTO workSetting = bo.getWorkSetting();

        // 1、大件直接分配整件收货位
        outStockOrderPOList.stream().filter(p -> !CollectionUtils.isEmpty(p.getItems()))
            .flatMap(p -> p.getItems().stream()).filter(p -> p.getProductFeature() == null
                || Objects.equals(p.getProductFeature(), ProductFeatureEnum.大件.getType()))
            .forEach(itemPO -> {
                itemPO.setLocationId(workSetting.getLargeLocationId());
                itemPO.setLocationName(workSetting.getLargeLocationName());
            });

        // 2、小件先分配关联货位，找不到则分配拆零收货位
        List<OutStockOrderItemPO> smallOrderItemPOList = outStockOrderPOList.stream()
            .filter(p -> !CollectionUtils.isEmpty(p.getItems())).flatMap(p -> p.getItems().stream())
            .filter(p -> Objects.equals(p.getProductFeature(), ProductFeatureEnum.小件.getType()))
            .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(smallOrderItemPOList)) {
            // 查询关联货位
            List<Long> productSkuIds = smallOrderItemPOList.stream().map(OutStockOrderItemPO::getSkuid).distinct()
                .collect(Collectors.toList());
            List<ProductLoactionItemDTO> locationBySkuId =
                iProductLocationService.findLocationBySkuId(warehouseId, productSkuIds);

            smallOrderItemPOList.forEach(itemPO -> {
                List<ProductLoactionItemDTO> productLocationItemDTOS = locationBySkuId.stream()
                    .filter(p -> Objects.equals(p.getProductSkuId(), itemPO.getSkuid())).collect(Collectors.toList());
                // 如果产品配置了多个货位，优先使用零拣位和分拣位，再存储位
                ProductLoactionItemDTO productLoactionItemDTO = getProductLocationItemDTO(productLocationItemDTOS);
                if (productLoactionItemDTO != null) {
                    // 先分配关联货位
                    itemPO.setLocationId(productLoactionItemDTO.getLocationId());
                    itemPO.setLocationName(productLoactionItemDTO.getLocationName());
                } else {
                    // 找不到则分配拆零收货位
                    itemPO.setLocationId(workSetting.getSmallLocationId());
                    itemPO.setLocationName(workSetting.getSmallLocationName());
                }
            });
        }

        // 3、记录找不到货位的产品
        List<String> productNameList = outStockOrderPOList.stream().filter(p -> !CollectionUtils.isEmpty(p.getItems()))
            .flatMap(p -> p.getItems().stream())
            .filter(p -> p.getLocationId() == null || StringUtils.isEmpty(p.getLocationName()))
            .map(p -> p.getProductname()).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(productNameList)) {
            msg = String.join(",", productNameList);
        }

        // 将订单项按详细拆分
        setOutStockOrderItemDetail(outStockOrderPOList);

        return outStockOrderPOList;
    }
}
