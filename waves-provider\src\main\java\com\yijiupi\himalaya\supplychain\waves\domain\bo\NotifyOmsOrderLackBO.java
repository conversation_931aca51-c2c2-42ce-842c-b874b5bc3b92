package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.PartSendWsmDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
public class NotifyOmsOrderLackBO {
    /**
     * 缺货信息
     */
    private List<PartSendWsmDTO> partSendWsmDTOList;
    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 获取 缺货信息
     *
     * @return partSendWsmDTOList 缺货信息
     */
    public List<PartSendWsmDTO> getPartSendWsmDTOList() {
        return this.partSendWsmDTOList;
    }

    /**
     * 设置 缺货信息
     *
     * @param partSendWsmDTOList 缺货信息
     */
    public void setPartSendWsmDTOList(List<PartSendWsmDTO> partSendWsmDTOList) {
        this.partSendWsmDTOList = partSendWsmDTOList;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
