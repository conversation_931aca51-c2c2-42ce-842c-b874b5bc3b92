package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yijiupi.himalaya.base.search.PageCondition;

/**
 * 团购订单摘果列表查询
 *
 * <AUTHOR>
 * @date 2/5/21 5:12 PM
 */
public class GroupBuyBatchTaskSO extends PageCondition implements Serializable {

    private static final long serialVersionUID = 6649559783322732272L;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 拣货任务类型 0: 按线路摘果 1: 按自提点摘果
     */
    private Byte batchTaskType;

    /**
     * 出库位/周转箱
     */
    private String toLocationName;

    /**
     * 通道号
     */
    private String passage;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Byte getBatchTaskType() {
        return batchTaskType;
    }

    public void setBatchTaskType(Byte batchTaskType) {
        this.batchTaskType = batchTaskType;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public String getPassage() {
        return passage;
    }

    public void setPassage(String passage) {
        this.passage = passage;
    }
}
