package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单项实际分配数量
 *
 * <AUTHOR>
 * @date 2020-09-05 09:57
 */
public class OrderItemDetailAllotDTO implements Serializable {
    private static final long serialVersionUID = 5308492663346033893L;

    /**
     * 二级货主ID
     */
    private Long secOwnerId;

    /**
     * 二级货主名称
     */
    private String secOwnerName;

    /**
     * 货主ID
     */
    private Long ownerId;

    /**
     * 产品规格ID
     */
    private Long productSpecificationId;

    /**
     * 分配小单位总数量
     */
    private BigDecimal unitTotalCount;

    public String getSecOwnerName() {
        return secOwnerName;
    }

    public void setSecOwnerName(String secOwnerName) {
        this.secOwnerName = secOwnerName;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }
}
