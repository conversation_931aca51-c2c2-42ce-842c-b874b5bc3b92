package com.yijiupi.himalaya.supplychain.waves.dto.digital;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/10/30
 */
public class DigitalBatchTaskQueryDTO implements Serializable {
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 货位id列表
     */
    private List<Long> locationIdList;
    /**
     * 拣货任务号列表
     */
    private List<String> batchTaskNoList;
    /**
     * 状态列表
     */
    private List<Byte> stateList;
    /**
     * 拣货方式
     */
    private Byte kindOfPicking;
    /**
     * 分区id
     */
    private Long sortGroupId;
    /**
     * 拣货任务id列表
     */
    private List<String> batchTaskIds;
    /**
     * 分区id列表
     */
    private List<Long> sortGroupIds;
    /**
     * 排除拣货任务id列表
     */
    private List<String> excludeBatchTaskIds;
    /**
     * 开始时间
     */
    private String timeS;
    /**
     * 截止时间
     */
    private String timeE;
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 拣货任务方式
     */
    private Byte pickPattern;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 货位id列表
     *
     * @return locationIdList 货位id列表
     */
    public List<Long> getLocationIdList() {
        return this.locationIdList;
    }

    /**
     * 设置 货位id列表
     *
     * @param locationIdList 货位id列表
     */
    public void setLocationIdList(List<Long> locationIdList) {
        this.locationIdList = locationIdList;
    }

    /**
     * 获取 拣货任务号列表
     *
     * @return batchTaskNoList 拣货任务号列表
     */
    public List<String> getBatchTaskNoList() {
        return this.batchTaskNoList;
    }

    /**
     * 设置 拣货任务号列表
     *
     * @param batchTaskNoList 拣货任务号列表
     */
    public void setBatchTaskNoList(List<String> batchTaskNoList) {
        this.batchTaskNoList = batchTaskNoList;
    }

    /**
     * 获取 状态列表
     *
     * @return stateList 状态列表
     */
    public List<Byte> getStateList() {
        return this.stateList;
    }

    /**
     * 设置 状态列表
     *
     * @param stateList 状态列表
     */
    public void setStateList(List<Byte> stateList) {
        this.stateList = stateList;
    }

    /**
     * 获取 拣货方式
     *
     * @return kindOfPicking 拣货方式
     */
    public Byte getKindOfPicking() {
        return this.kindOfPicking;
    }

    /**
     * 设置 拣货方式
     *
     * @param kindOfPicking 拣货方式
     */
    public void setKindOfPicking(Byte kindOfPicking) {
        this.kindOfPicking = kindOfPicking;
    }

    /**
     * 获取 分区id
     *
     * @return sortGroupId 分区id
     */
    public Long getSortGroupId() {
        return this.sortGroupId;
    }

    /**
     * 设置 分区id
     *
     * @param sortGroupId 分区id
     */
    public void setSortGroupId(Long sortGroupId) {
        this.sortGroupId = sortGroupId;
    }

    /**
     * 获取 拣货任务id列表
     *
     * @return batchTaskIds 拣货任务id列表
     */
    public List<String> getBatchTaskIds() {
        return this.batchTaskIds;
    }

    /**
     * 设置 拣货任务id列表
     *
     * @param batchTaskIds 拣货任务id列表
     */
    public void setBatchTaskIds(List<String> batchTaskIds) {
        this.batchTaskIds = batchTaskIds;
    }

    public List<Long> getSortGroupIds() {
        return sortGroupIds;
    }

    public void setSortGroupIds(List<Long> sortGroupIds) {
        this.sortGroupIds = sortGroupIds;
    }

    public List<String> getExcludeBatchTaskIds() {
        return excludeBatchTaskIds;
    }

    public void setExcludeBatchTaskIds(List<String> excludeBatchTaskIds) {
        this.excludeBatchTaskIds = excludeBatchTaskIds;
    }

    /**
     * 获取 开始时间
     *
     * @return timeS 开始时间
     */
    public String getTimeS() {
        return this.timeS;
    }

    /**
     * 设置 开始时间
     *
     * @param timeS 开始时间
     */
    public void setTimeS(String timeS) {
        this.timeS = timeS;
    }

    /**
     * 获取 截止时间
     *
     * @return timeE 截止时间
     */
    public String getTimeE() {
        return this.timeE;
    }

    /**
     * 设置 截止时间
     *
     * @param timeE 截止时间
     */
    public void setTimeE(String timeE) {
        this.timeE = timeE;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 拣货任务方式
     *
     * @return pickPattern 拣货任务方式
     */
    public Byte getPickPattern() {
        return this.pickPattern;
    }

    /**
     * 设置 拣货任务方式
     *
     * @param pickPattern 拣货任务方式
     */
    public void setPickPattern(Byte pickPattern) {
        this.pickPattern = pickPattern;
    }
}
