package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.SowCalculationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.SowManagerBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchWorkSettingDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderSaveDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SownOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.ProductSowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.util.RedisKeyConstant;
import com.yijiupi.himalaya.supplychain.waves.util.RedisUtil;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class SowManageServiceImplTest {

    @Autowired
    private SowManageServiceImpl sowManageService;

    @Autowired
    private RedisUtil<String> redisUtil;

    @Autowired
    private SowCalculationBL sowCalculationBL;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private SowManagerBL sowManagerBL;

    @Test
    public void saveSowOrderTest() {
        SowOrderSaveDTO sowOrderSaveDTO = new SowOrderSaveDTO();
        sowOrderSaveDTO.setOrgId(999);
        sowOrderSaveDTO.setWarehouseId(0);
        sowOrderSaveDTO.setSowTaskNo("BZ201811020001");
        sowOrderSaveDTO.setSowTaskId(201811020001L);
        sowOrderSaveDTO.setOperatorUserName("小明");

        List<SownOrderItemDTO> sownOrderItems = new ArrayList<>();
        SownOrderItemDTO sownOrderItem1 = new SownOrderItemDTO();
        sownOrderItem1.setOutStockOrderId(999118050200109L);
        sownOrderItem1.setRefOrderNo("CS201804260050");
        sownOrderItem1.setSownPackageAmount(new BigDecimal(80));
        sownOrderItem1.setSownSkuCount(1);
        sownOrderItem1.setSownUnitAmount(new BigDecimal(0));
        sownOrderItems.add(sownOrderItem1);

        sowOrderSaveDTO.setSownOrderItems(sownOrderItems);

        sowManageService.saveSowOrder(sowOrderSaveDTO);
    }

    @Test
    public void redisTest() {
        BatchWorkSettingDTO batchWorkSettingDTO = new BatchWorkSettingDTO();
        batchWorkSettingDTO.setDeadline("17:50:00");
        // Map<Date, Date> workTime = sowCalculationBL.getWorkTime(batchWorkSettingDTO);
        // long timeout = workTime.values().stream().findFirst().get().getTime() - System.currentTimeMillis();
        String redisKey = RedisKeyConstant.WAREHOUSE_ADDRESS_COUNT + 18723;
        // String s = redisUtil.get(redisKey);
        // redisUtil.set(redisKey, redisUtil.get(redisKey), timeout, TimeUnit.MILLISECONDS);
        // Long expire = redisUtil.getExpire(RedisKeyConstant.WAREHOUSE_ADDRESS_COUNT + 18723);
        List<String> keys = new ArrayList<>();
        keys.add(redisKey);
        redisUtil.batchDel(keys);
    }

    @Test
    public void completeSowTaskByProductTest() {
        String json = "";
        // ProductSowTaskDTO completeDTO = JSON.parseObject(json,ProductSowTaskDTO.class);
        ProductSowTaskDTO completeDTO = new ProductSowTaskDTO();
        completeDTO.setLackUnitCount(BigDecimal.ZERO);
        completeDTO.setOptUserId(14399);
        completeDTO.setOrgId(998);
        completeDTO.setWarehouseId(9981);
        completeDTO.setProductSkuId(4941609289731570118L);
        completeDTO.setLackUnitCount(new BigDecimal(2));

        List<SowTaskInfoDTO> sowTaskList = new ArrayList<>();
        SowTaskInfoDTO one = new SowTaskInfoDTO();
        one.setSowTaskNo("BC998121071600025-1");
        one.setId(1626426173494100133L);
        // item.
        String jsonItem = " [\n" + "    {\n" + "      \"toLocationId\": \"1683793659387301653\",\n"
            + "      \"specQuantity\": 50,\n" + "      \"source\": 0,\n" + "      \"id\": \"1626426173494100134\",\n"
            + "      \"productSkuId\": \"4941609289731570118\",\n" + "      \"toLocationName\": \"JH21-1\",\n"
            + "      \"channel\": 0,\n" + "      \"outStockOrderItemId\": \"998001210716148565\"\n" + "    }\n" + "  ]";
        List<SowTaskItemDTO> sowTaskItemDTOS = JSON.parseArray(jsonItem, SowTaskItemDTO.class);
        one.setItems(sowTaskItemDTOS);
        sowTaskList.add(one);

        completeDTO.setSowTaskList(sowTaskList);

        sowManagerBL.completeSowTaskByProduct(completeDTO);
        System.out.println("播种任务完成");
    }
}
