package com.yijiupi.himalaya.supplychain.waves.dto.soworder;

import java.io.Serializable;

public class SowAddressQueryDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 容器货位id
     */
    private Long locationId;

    /**
     * 容器货位名称
     */
    private String locationName;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
