package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @title: WCSAssignItemSorterDTO
 * @description: 指派
 * @date 2022-11-07 09:05
 */
public class WCSAssignItemSorterDTO implements Serializable {

    /**
     * 拣货任务明细id
     */
    @Deprecated
    private String batchTaskItemId;
    /**
     * 操作人id
     */
    private Integer optUserId;
    /**
     * 操作人名称
     */
    private String optUserName;
    /**
     * 拣货任务明细id列表
     */
    private List<String> batchTaskItemIds;

    /**
     * 获取 拣货任务明细id
     *
     * @return batchTaskItemId 拣货任务明细id
     */
    public String getBatchTaskItemId() {
        return this.batchTaskItemId;
    }

    /**
     * 设置 拣货任务明细id
     *
     * @param batchTaskItemId 拣货任务明细id
     */
    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }

    /**
     * 获取 操作人名称
     *
     * @return optUserName 操作人名称
     */
    public String getOptUserName() {
        return this.optUserName;
    }

    /**
     * 设置 操作人名称
     *
     * @param optUserName 操作人名称
     */
    public void setOptUserName(String optUserName) {
        this.optUserName = optUserName;
    }

    /**
     * 获取 拣货任务明细id列表
     *
     * @return batchTaskItemIds 拣货任务明细id列表
     */
    public List<String> getBatchTaskItemIds() {
        return this.batchTaskItemIds;
    }

    /**
     * 设置 拣货任务明细id列表
     *
     * @param batchTaskItemIds 拣货任务明细id列表
     */
    public void setBatchTaskItemIds(List<String> batchTaskItemIds) {
        this.batchTaskItemIds = batchTaskItemIds;
    }

    public void resetBatchTaskItemIdsIfEmpty() {
        if (CollectionUtils.isEmpty(this.batchTaskItemIds) && !StringUtils.isEmpty(this.batchTaskItemId)) {
            this.batchTaskItemIds = new ArrayList<>();
            this.batchTaskItemIds.add(this.batchTaskItemId);
        }
    }
}
