package com.yijiupi.himalaya.supplychain.controller.sow;

import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class SowPackageProductItem implements Serializable {

    /**
     * 产品id
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 包装规格
     */
    private String specName;

    /**
     * 包装规格系数
     */
    private BigDecimal specQuantity;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 未包装数量
     */
    private BigDecimal unPackagedTotalCount;

    /**
     * 箱码
     */
    private List<String> packageCode;

    /**
     * 品码
     */
    private List<String> unitCode;

    /**
     * 已包装信息
     */
    private List<PackageOrderItemDTO> packageOrderItems;

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnPackagedTotalCount() {
        return unPackagedTotalCount;
    }

    public void setUnPackagedTotalCount(BigDecimal unPackagedTotalCount) {
        this.unPackagedTotalCount = unPackagedTotalCount;
    }

    public List<String> getPackageCode() {
        return packageCode;
    }

    public void setPackageCode(List<String> packageCode) {
        this.packageCode = packageCode;
    }

    public List<String> getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(List<String> unitCode) {
        this.unitCode = unitCode;
    }

    public List<PackageOrderItemDTO> getPackageOrderItems() {
        return packageOrderItems;
    }

    public void setPackageOrderItems(List<PackageOrderItemDTO> packageOrderItems) {
        this.packageOrderItems = packageOrderItems;
    }
}
