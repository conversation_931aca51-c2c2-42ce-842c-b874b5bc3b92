package com.yijiupi.himalaya.supplychain.waves.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.batch.IRestockRequestService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.RestockRequestBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.RestockRequestParam;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-09-04 16:45
 **/
@Service
public class RestockRequestServiceImpl implements IRestockRequestService {

    @Resource
    private RestockRequestBL restockRequestBL;

    /**
     * 分拣呼叫补货<br/> 给仓管发送一条补货通知
     *
     * @param param 通知参数
     */
    @Override
    public void sendRequestMessage(RestockRequestParam param) {
        AssertUtils.notNull(param, "参数不能为空");
        AssertUtils.notNull(param.getWarehouseId(), "仓库 id 不能为空");
        AssertUtils.notEmpty(param.getItems(), "产品明细不能为空");
        restockRequestBL.sendRequestMessage(param);
    }
}
