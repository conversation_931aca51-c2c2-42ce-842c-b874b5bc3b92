package com.yijiupi.himalaya.supplychain.waves.domain.model.tms;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-12-19 17:38
 **/
public class PackageCancelParam {
    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 订单号
     */
    private List<String> orderNoList;

    public static PackageCancelParam of(Integer warehouseId, Collection<String> orderNoList) {
        PackageCancelParam param = new PackageCancelParam();
        param.setWarehouseId(warehouseId);
        param.setOrderNoList(new ArrayList<>(orderNoList));
        return param;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public void setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
    }
}
