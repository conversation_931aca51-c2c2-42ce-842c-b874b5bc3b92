package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.decoratesplitbatchtask;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderFeatureBL;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/4
 */
public abstract class SplitBatchTaskConcreteDecorateBL implements SplitBatchTaskByOrderType, Ordered {

    @Autowired
    protected OrderFeatureBL orderFeatureBL;
    protected static final Logger LOG = LoggerFactory.getLogger(SplitBatchTaskConcreteDecorateBL.class);

}
