package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.LocationDivideHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.LocationDivideHelperResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: LocationSplitHelperResultConvertor
 * @description: 武汉仓的大总单播种，要求订单项中超过1件的产品，将整件直接拿出来进打包；那么上机器人后，一个波次最多要拆成4种拣货任务：
 * <p>
 * 1、单项超1件的产品（简称单项整件拣货任务）、
 * <p>
 * 2、单项不超1件但汇总超1件（或者是货位上限）的整件部分产品（简称多项整件拣货任务）、
 * <p>
 * 3、零散库存产品的人工分拣任务（简称人工零拣任务）、
 * <p>
 * 4、零散库存产品的机器人分拣任务（简称机器人零拣任务）。
 * <p>
 * 整体逻辑描述：
 * <p>
 * 1、多个订单创建1个波次；
 * <p>
 * 2、根据订单产品特征（大件或小件）查找分拣位/零拣位库存，查不到则查关联货位，据此确定原始拣货货位；
 * <p>
 * 3、查找原始拣货货位是否有通道，通道是否开启了播种，开了则生成播种打包任务；
 * <p>
 * 4、如果仓库开启了整件拆零拣货，则将订单中单个订单项超过1大件的产品整件数量汇总出来，生成单项整件拣货任务；
 * <p>
 * 5、将经过步骤4处理后的订单项产品数量汇总出来，与原始拣货货位的补货上限比较，超过上限则转换成大件规格，将转换后的整件数量汇总出来，生成多项整件拣货任务；
 * （只有汇总后的拆零数量超过了零拣位货位补货上限，才拆分出整件拣货，不超过补货上限的，仍然生成拆零拣货任务）
 * <p>
 * 6、经过步骤5处理后的订单项，如果仓库开启了WCS，且通道开启了机器人拣货，且拣货货位是周转箱，则这部分订单项汇总出来生成机器人零拣任务，否则生成人工零拣任务。
 * <p>
 * 单项整件拣货任务应跳过播种，直接进入打包作业；多项整件拣货任务需要进入播种，人工零拣任务和机器人零拣任务也需要进入播种；
 * 4种任务都在一个波次中，因此无论直接打包，还是分别进播种后再打包，打包任务应该是一个整体，打印出来的标签箱号需要完整且连贯。
 * @date 2023-02-07 17:23
 */
public class LocationDivideHelperResultConvertor {

    /**
     * 处理订单项里全都是大件，unitCount 都为0
     *
     * @param bo
     * @return
     */
    public static LocationDivideHelperResultBO convertPackage(LocationDivideHelperBO bo) {
        LocationDivideHelperResultBO resultBO = convertBase(bo);
        List<OutStockOrderItemPO> packageItemList = bo.getItemList().stream().filter(m -> m.getPackagecount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(packageItemList)) {
            packageItemList.forEach(m -> m.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE));
        }
        resultBO.setPackageItemList(packageItemList);
        resultBO.setUnitItemList(Collections.emptyList());

        return resultBO;
    }

    /**
     * 处理订单项里全都是小件，packageCount 都为0
     *
     * @param bo
     * @return
     */
    public static LocationDivideHelperResultBO convertUnit(LocationDivideHelperBO bo) {
        LocationDivideHelperResultBO resultBO = convertBase(bo);
        List<OutStockOrderItemPO> unitItemList = bo.getItemList().stream().filter(m -> m.getUnitcount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(unitItemList)) {
            unitItemList.forEach(m -> m.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_NONE));
        }
        resultBO.setPackageItemList(Collections.emptyList());
        resultBO.setUnitItemList(unitItemList);

        return resultBO;
    }

    public static LocationDivideHelperResultBO convertBase(LocationDivideHelperBO bo) {
        LocationDivideHelperResultBO resultBO = new LocationDivideHelperResultBO();
        resultBO.setSkuId(bo.getSkuId());
        resultBO.setUnitTotalCount(bo.getUnitTotalCount());
        resultBO.setSpecQuantity(bo.getSpecQuantity());
        resultBO.setSaleSpecQuantity(bo.getSaleSpecQuantity());
        resultBO.setPackageCount(bo.getPackageCount());
        resultBO.setUnitCount(bo.getUnitCount());

        return resultBO;
    }

    /**
     * 1、把订单里的大件 > 0 的订单项捞出来，安排上存储位；
     * 2、把小件聚合为整件的 订单项捞出来，安排上存储位；
     * 3、
     *
     * @param bo
     * @return
     */
    public static LocationDivideHelperResultBO convertDivide(LocationDivideHelperBO bo) {
        List<OutStockOrderItemPO> rightPackageItemList = bo.getItemList().stream().filter(m -> m.getUnitcount().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
        List<OutStockOrderItemPO> rightUnitItemList = bo.getItemList().stream().filter(m -> m.getPackagecount().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());

        // 大小件都包含的需要拆分
        List<OutStockOrderItemPO> unitPackageItemList = bo.getItemList().stream().filter(m -> m.getUnitcount().compareTo(BigDecimal.ZERO) != 0 && m.getPackagecount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());

        List<OutStockOrderItemPO> splitPackageItemList = unitPackageItemList.stream().map(orderItem -> LocationSplitHelperResultBOConvertor.getPackageItem(orderItem, orderItem.getUnittotalcount().subtract(orderItem.getUnitcount()))).collect(Collectors.toList());
        List<OutStockOrderItemPO> splitUnitItemList = unitPackageItemList.stream().map(orderItem -> LocationSplitHelperResultBOConvertor.getUnitItem(orderItem, orderItem.getUnittotalcount().subtract(orderItem.getUnitcount()))).collect(Collectors.toList());

        rightPackageItemList.addAll(splitPackageItemList);
        rightUnitItemList.addAll(splitUnitItemList);

        LocationDivideHelperResultBO resultBO = new LocationDivideHelperResultBO();
        resultBO.setPackageCount(rightPackageItemList.stream().map(OutStockOrderItemPO::getPackagecount).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultBO.setUnitCount(rightPackageItemList.stream().map(OutStockOrderItemPO::getUnitcount).reduce(BigDecimal.ZERO, BigDecimal::add));
        if (!CollectionUtils.isEmpty(rightPackageItemList)) {
            rightPackageItemList.forEach(m -> m.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE));
        }

        if (!CollectionUtils.isEmpty(rightUnitItemList)) {
            rightUnitItemList.forEach(m -> m.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_NONE));
        }

        resultBO.setPackageItemList(rightPackageItemList);
        resultBO.setUnitItemList(rightUnitItemList);

        OutStockOrderItemPO orderItem = bo.getItemList().get(0);
        resultBO.setSaleSpecQuantity(orderItem.getSalespecquantity());
        resultBO.setSpecQuantity(orderItem.getSpecquantity());
        resultBO.setSkuId(orderItem.getSkuid());
        resultBO.setUnitTotalCount(bo.getItemList().stream().map(OutStockOrderItemPO::getUnittotalcount).reduce(BigDecimal.ZERO, BigDecimal::add));

        return resultBO;
    }

}
