package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.decoratesplitbatchtask;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.SplitBatchTaskDecorateHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor.WaveCreateDTOSplitConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.SplitBatchTaskByOrderTypeBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 
 * 
 * <AUTHOR>
 * @date 2025/6/4
 */
@Component
public class SplitBatchTaskByNormalOrderDecorateBL extends SplitBatchTaskConcreteDecorateBL {

    @Override
    public void splitBatchTaskByOrderType(SplitBatchTaskByOrderTypeBO bo, SplitBatchTaskDecorateHelperBO helperBO) {
        WavesStrategyBO wavesStrategyDTO = helperBO.getWavesStrategyDTO();
        List<OutStockOrderPO> orders = helperBO.getOrders();
        ProcessBatchDTO processBatchDTO = helperBO.getProcessBatchDTO();

        List<OutStockOrderPO> normalOrders = getNormalOrders(orders, bo);

        if (CollectionUtils.isEmpty(normalOrders)) {
            return;
        }

        // 按目的仓库拆分拣货任务
        List<WaveCreateDTO> waveCreateDTOList =
            WaveCreateDTOSplitConvertor.splitWaveByWarehouse(normalOrders, wavesStrategyDTO, processBatchDTO);
        LOG.info("按目的仓库拆分拣货任务：{}", JSON.toJSONString(waveCreateDTOList));
        // 如果启用实时分拣， 而且选了 按用户拆分波次， 则按订单特征和用户 进行拣货任务的拆分
        waveCreateDTOList = WaveCreateDTOSplitConvertor.splitWaveByTrayLocation(wavesStrategyDTO, processBatchDTO,
            waveCreateDTOList, getOrderFeatureMap());
        bo.setNormalWaveCreateDTO(waveCreateDTOList);
    }

    private Function<List<Long>, Map<Long, List<Byte>>> getOrderFeatureMap() {
        return orderIds -> orderFeatureBL.getOrderFeatureMap(orderIds);
    }

    private List<OutStockOrderPO> getNormalOrders(List<OutStockOrderPO> orders, SplitBatchTaskByOrderTypeBO bo) {
        List<OutStockOrderPO> normalOrders = orders.stream()
            .filter(p -> Objects.isNull(p.getOutBoundType())
                || OutBoundTypeEnum.SALE_ORDER.getCode().byteValue() != p.getOutBoundType()
                || !OrderConstant.ALLOT_TYPE_ALLOCATION.equals(p.getAllotType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(normalOrders)) {
            return Collections.emptyList();
        }

        List<WaveCreateDTO> promotionWaveCreateList = bo.getPromotionWaveCreateDTO();

        if (CollectionUtils.isEmpty(promotionWaveCreateList)) {
            return normalOrders;
        }

        List<OutStockOrderPO> promotionOrderList =
            promotionWaveCreateList.stream().flatMap(m -> m.getOrders().stream()).collect(Collectors.toList());
        return filterNormalOutStockOrderList(promotionOrderList, normalOrders);
    }

    private List<OutStockOrderPO> filterNormalOutStockOrderList(List<OutStockOrderPO> promotionOrderList,
        List<OutStockOrderPO> normalOrderList) {
        if (CollectionUtils.isEmpty(promotionOrderList)) {
            return normalOrderList;
        }
        Map<Long, Long> promotionOrderIdMap =
            promotionOrderList.stream().collect(Collectors.toMap(OutStockOrderPO::getId, OutStockOrderPO::getId));

        return normalOrderList.stream().filter(o -> Objects.isNull(promotionOrderIdMap.get(o.getId())))
            .collect(Collectors.toList());
    }

    /**
     * Get the order value of this object.
     * <p>
     * Higher values are interpreted as lower priority. As a consequence, the object with the lowest value has the
     * highest priority (somewhat analogous to Servlet {@code load-on-startup} values).
     * <p>
     * Same order values will result in arbitrary sort positions for the affected objects.
     *
     * @return the order value
     * @see #HIGHEST_PRECEDENCE
     * @see #LOWEST_PRECEDENCE
     */
    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}
