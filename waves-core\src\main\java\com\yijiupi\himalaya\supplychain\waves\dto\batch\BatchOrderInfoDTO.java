package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;

/**
 * 波次订单信息
 *
 * <AUTHOR> 2018/3/16
 */
public class BatchOrderInfoDTO implements Serializable {
    /**
     * id
     */
    private String id;
    /**
     * 波次编号
     */
    private String batchNo;
    /**
     * 波次名称
     */
    private String batchName;

    /**
     * 波次状态
     */
    private Integer state;

    /**
     * 拣货方式 1 按订单拣货 2 按产品拣货
     */
    private Byte pickingType;
    /**
     * 拣货方式 1 按订单拣货 2 按产品拣货
     */
    private String pickingTypeText;
    /**
     * 商品种类
     */
    private Integer skuCount;
    /**
     * 大件总数
     */
    private BigDecimal packageAmount;
    /**
     * 小件总数
     */
    private BigDecimal unitAmount;
    /**
     * 订单数量
     */
    private Integer orderCount;
    /**
     * 关联订单的应付总金额
     */
    private BigDecimal orderAmount;
    /**
     * 关联的订单
     */
    private List<OutStockOrderDTO> items;

    /**
     * 波次类别 0：酒批(默认) 1：微酒
     */
    private Byte batchType;

    public Byte getBatchType() {
        return batchType;
    }

    public void setBatchType(Byte batchType) {
        this.batchType = batchType;
    }

    /**
     * 获取 波次编号
     *
     * @return batchNo 波次编号
     */
    public String getBatchNo() {
        return this.batchNo;
    }

    /**
     * 设置 波次编号
     *
     * @param batchNo 波次编号
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * 获取 波次名称
     *
     * @return batchName 波次名称
     */
    public String getBatchName() {
        return this.batchName;
    }

    /**
     * 设置 波次名称
     *
     * @param batchName 波次名称
     */
    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    /**
     * 获取 拣货方式 1 按订单拣货 2 按产品拣货
     *
     * @return pickingType 拣货方式 1 按订单拣货 2 按产品拣货
     */
    public Byte getPickingType() {
        return this.pickingType;
    }

    /**
     * 设置 拣货方式 1 按订单拣货 2 按产品拣货
     *
     * @param pickingType 拣货方式 1 按订单拣货 2 按产品拣货
     */
    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    /**
     * 获取 商品种类
     *
     * @return skuCount 商品种类
     */
    public Integer getSkuCount() {
        return this.skuCount;
    }

    /**
     * 设置 商品种类
     *
     * @param skuCount 商品种类
     */
    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    /**
     * 获取 大件总数
     *
     * @return packageAmount 大件总数
     */
    public BigDecimal getPackageAmount() {
        return this.packageAmount;
    }

    /**
     * 设置 大件总数
     *
     * @param packageAmount 大件总数
     */
    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    /**
     * 获取 小件总数
     *
     * @return unitAmount 小件总数
     */
    public BigDecimal getUnitAmount() {
        return this.unitAmount;
    }

    /**
     * 设置 小件总数
     *
     * @param unitAmount 小件总数
     */
    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    /**
     * 获取 关联的订单
     *
     * @return items 关联的订单
     */
    public List<OutStockOrderDTO> getItems() {
        return this.items;
    }

    /**
     * 设置 关联的订单
     *
     * @param items 关联的订单
     */
    public void setItems(List<OutStockOrderDTO> items) {
        this.items = items;
    }

    /**
     * 获取 id
     *
     * @return id id
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 id
     *
     * @param id id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 订单数量
     *
     * @return orderCount 订单数量
     */
    public Integer getOrderCount() {
        return this.orderCount;
    }

    /**
     * 设置 订单数量
     *
     * @param orderCount 订单数量
     */
    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    /**
     * 获取 关联订单的应付总金额
     *
     * @return orderAmount 关联订单的应付总金额
     */
    public BigDecimal getOrderAmount() {
        return this.orderAmount;
    }

    /**
     * 设置 关联订单的应付总金额
     *
     * @param orderAmount 关联订单的应付总金额
     */
    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    /**
     * @return the pickingTypeText
     */
    public String getPickingTypeText() {
        return pickingTypeText;
    }

    /**
     * @param pickingTypeText the pickingTypeText to set
     */
    public void setPickingTypeText(String pickingTypeText) {
        this.pickingTypeText = pickingTypeText;
    }
}
