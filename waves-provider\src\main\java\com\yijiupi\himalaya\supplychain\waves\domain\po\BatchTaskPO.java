package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskKindOfPickingConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskWarehouseFeatureTypeConstants;

/**
 * 波次任务
 *
 * <AUTHOR> 2018/3/16
 */
public class BatchTaskPO implements Serializable {
    /**
     * id
     */
    private String id;
    /**
     * 城市id
     */
    private String orgId;
    /**
     * 波次任务号
     */
    private String batchTaskNo;
    /**
     * 拣货属性
     */
    private String batchTaskName;
    /**
     * 波次id
     */
    private String batchId;
    /**
     * 波次编号
     */
    private String batchNo;
    /**
     * 波次任务生产时间
     */
    private Date createTime;
    /**
     * 分拣员(姓名)
     */
    private String sorter;
    /**
     * 分拣员id
     */
    private Integer sorterId;
    /**
     * 关联的订单应付总金额
     */
    private BigDecimal orderAmount;
    /**
     * 订单数量
     */
    private Integer orderCount;
    /**
     * 商品种类数量
     */
    private Integer skuCount;
    /**
     * 大件总数
     */
    private BigDecimal packageAmount;
    /**
     * 小件总数
     */
    private BigDecimal unitAmount;
    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte taskState;
    /**
     * 拣货方式 1 按订单拣货 2 按产品拣货',
     */
    private Byte pickingType;
    /**
     * 拣货分组策略 1货区 2货位 3类目',
     */
    private Byte pickingGroupStrategy;
    /**
     * 类目名称
     */
    private String categoryName;
    /**
     * 货位或者货区id
     */
    private Long locationId;
    /**
     * 货位或者货区名称
     */
    private String locationName;
    /**
     * 货区或货位类型：0:货位，1:货区'
     */
    private Byte locationCategory;
    /**
     * 货位或者货区id
     */
    private Long toLocationId;
    /**
     * 货位或者货区名称
     */
    private String toLocationName;
    /**
     * 打印次数
     */
    private Integer printTimes;
    /**
     * 是否打印
     */
    private Boolean isPrinted;

    /**
     * 片区名称
     */
    private String areaName;

    /**
     * 线路名称
     */
    private String routeName;

    /**
     * 订单筛选策略 1 按区域 2 按线路
     */
    private Byte orderSelection;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 集货位id
     */
    private Long sowLocationId;

    /**
     * 集货位名称
     */
    private String sowLocationName;

    /**
     * 仓库Id
     */
    private Integer warehouseId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 播种订单序号
     */
    private Integer sowOrderSequence;

    /**
     * 订单编号
     */
    private String refOrderNo;

    /**
     * 分区id
     */
    private Long sortGroupId;

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 是否需要拣货，默认需要 0:不需要 1:需要
     */
    private Byte needPick;

    /**
     * 拣货操作人id
     */
    private Integer completeUserId;

    /**
     * 拣货操作人姓名
     */
    private String completeUser;

    /**
     * 波次名称
     */
    private String batchName;

    /**
     * 拣货任务类型 1: 按自提点
     */
    private Byte BatchTaskType;

    /**
     * 通道id
     */
    private Long passageId;

    /**
     * 通道名称
     */
    private String passageName;
    /**
     * 拣货模式
     */
    private Byte pickPattern;

    /**
     * 拣货任务详情列表
     */
    private List<BatchTaskItemPO> batchTaskItemList;

    /**
     * 托盘号
     */
    private String toPalletNo;
    /**
     * 拣货方式:1、默认；2、电子标签拣货
     *
     * @see BatchTaskKindOfPickingConstants
     */
    private Byte kindOfPicking;
    /**
     * 名称
     */
    private String sortGroupName;
    /**
     * 第几次追加拣货任务
     */
    private Byte taskAppendSequence;
    /**
     * 拣货任务特征
     *
     * @see TaskWarehouseFeatureTypeConstants
     */
    private Byte taskWarehouseFeatureType;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;
    /**
     * 绩效所属班次
     */
    private String shiftOfPerformance;

    public List<BatchTaskItemPO> getBatchTaskItemList() {
        return batchTaskItemList;
    }

    public void setBatchTaskItemList(List<BatchTaskItemPO> batchTaskItemList) {
        this.batchTaskItemList = batchTaskItemList;
    }

    public Long getPassageId() {
        return passageId;
    }

    public void setPassageId(Long passageId) {
        this.passageId = passageId;
    }

    public String getPassageName() {
        return passageName;
    }

    public void setPassageName(String passageName) {
        this.passageName = passageName;
    }

    public Byte getBatchTaskType() {
        return BatchTaskType;
    }

    public void setBatchTaskType(Byte batchTaskType) {
        BatchTaskType = batchTaskType;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Byte getNeedPick() {
        return needPick;
    }

    public void setNeedPick(Byte needPick) {
        this.needPick = needPick;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public Long getSortGroupId() {
        return sortGroupId;
    }

    public void setSortGroupId(Long sortGroupId) {
        this.sortGroupId = sortGroupId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Byte getOrderSelection() {
        return orderSelection;
    }

    public void setOrderSelection(Byte orderSelection) {
        this.orderSelection = orderSelection;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public Boolean getPrinted() {
        return isPrinted;
    }

    public void setPrinted(Boolean printed) {
        isPrinted = printed;
    }

    /**
     * 获取 id
     *
     * @return id id
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 id
     *
     * @param id id
     */
    public void setId(String id) {
        this.id = id;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    /**
     * 获取 波次任务号
     *
     * @return batchTaskNo 波次任务号
     */
    public String getBatchTaskNo() {
        return this.batchTaskNo;
    }

    /**
     * 设置 波次任务号
     *
     * @param batchTaskNo 波次任务号
     */
    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    /**
     * 获取 波次任务生产时间
     *
     * @return createTime 波次任务生产时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置 波次任务生产时间
     *
     * @param createTime 波次任务生产时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 分拣员(姓名)
     *
     * @return sorter 分拣员(姓名)
     */
    public String getSorter() {
        return this.sorter;
    }

    /**
     * 设置 分拣员(姓名)
     *
     * @param sorter 分拣员(姓名)
     */
    public void setSorter(String sorter) {
        this.sorter = sorter;
    }

    /**
     * 获取 商品种类数量
     *
     * @return skuCount 商品种类数量
     */
    public Integer getSkuCount() {
        return this.skuCount;
    }

    /**
     * 设置 商品种类数量
     *
     * @param skuCount 商品种类数量
     */
    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    /**
     * 获取 大件总数
     *
     * @return packageAmount 大件总数
     */
    public BigDecimal getPackageAmount() {
        return this.packageAmount;
    }

    /**
     * 设置 大件总数
     *
     * @param packageAmount 大件总数
     */
    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    /**
     * 获取 小件总数
     *
     * @return unitAmount 小件总数
     */
    public BigDecimal getUnitAmount() {
        return this.unitAmount;
    }

    /**
     * 设置 小件总数
     *
     * @param unitAmount 小件总数
     */
    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    /**
     * 获取 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     *
     * @return taskState 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     */
    public Byte getTaskState() {
        return this.taskState;
    }

    /**
     * 设置 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     *
     * @param taskState 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     */
    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    /**
     * 获取 分拣员id
     *
     * @return sorterId 分拣员id
     */
    public Integer getSorterId() {
        return this.sorterId;
    }

    /**
     * 设置 分拣员id
     *
     * @param sorterId 分拣员id
     */
    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    /**
     * 获取 波次编号
     *
     * @return batchNo 波次编号
     */
    public String getBatchNo() {
        return this.batchNo;
    }

    /**
     * 设置 波次编号
     *
     * @param batchNo 波次编号
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * 获取 关联的订单应付总金额
     *
     * @return orderAmount 关联的订单应付总金额
     */
    public BigDecimal getOrderAmount() {
        return this.orderAmount;
    }

    /**
     * 设置 关联的订单应付总金额
     *
     * @param orderAmount 关联的订单应付总金额
     */
    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    /**
     * 获取 订单数量
     *
     * @return orderCount 订单数量
     */
    public Integer getOrderCount() {
        return this.orderCount;
    }

    /**
     * 设置 订单数量
     *
     * @param orderCount 订单数量
     */
    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    /**
     * 获取 拣货方式 1 按订单拣货 2 按产品拣货',
     */
    public Byte getPickingType() {
        return this.pickingType;
    }

    /**
     * 设置 拣货方式 1 按订单拣货 2 按产品拣货',
     */
    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    /**
     * 获取 拣货分组策略 1货区 2货位 3类目',
     */
    public Byte getPickingGroupStrategy() {
        return this.pickingGroupStrategy;
    }

    /**
     * 设置 拣货分组策略 1货区 2货位 3类目',
     */
    public void setPickingGroupStrategy(Byte pickingGroupStrategy) {
        this.pickingGroupStrategy = pickingGroupStrategy;
    }

    /**
     * 获取 货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 类目名称
     */
    public String getCategoryName() {
        return this.categoryName;
    }

    /**
     * 设置 类目名称
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    /**
     * 获取 货位或者货区id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位或者货区id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 货区或货位类型：0:货位，1:货区'
     */
    public Byte getLocationCategory() {
        return this.locationCategory;
    }

    /**
     * 设置 货区或货位类型：0:货位，1:货区'
     */
    public void setLocationCategory(Byte locationCategory) {
        this.locationCategory = locationCategory;
    }

    /**
     * 获取 打印次数
     */
    public Integer getPrintTimes() {
        return this.printTimes;
    }

    /**
     * 设置 打印次数
     */
    public void setPrintTimes(Integer printTimes) {
        this.printTimes = printTimes;
    }

    /**
     * 获取 是否打印
     */
    public Boolean getIsPrinted() {
        return this.isPrinted;
    }

    /**
     * 设置 是否打印
     */
    public void setIsPrinted(Boolean IsPrinted) {
        this.isPrinted = IsPrinted;
    }

    public String getBatchTaskName() {
        return batchTaskName;
    }

    public void setBatchTaskName(String batchTaskName) {
        this.batchTaskName = batchTaskName;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Long getSowLocationId() {
        return sowLocationId;
    }

    public void setSowLocationId(Long sowLocationId) {
        this.sowLocationId = sowLocationId;
    }

    public String getSowLocationName() {
        return sowLocationName;
    }

    public void setSowLocationName(String sowLocationName) {
        this.sowLocationName = sowLocationName;
    }

    public Integer getSowOrderSequence() {
        return sowOrderSequence;
    }

    public void setSowOrderSequence(Integer sowOrderSequence) {
        this.sowOrderSequence = sowOrderSequence;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Integer getCompleteUserId() {
        return completeUserId;
    }

    public void setCompleteUserId(Integer completeUserId) {
        this.completeUserId = completeUserId;
    }

    public String getCompleteUser() {
        return completeUser;
    }

    public void setCompleteUser(String completeUser) {
        this.completeUser = completeUser;
    }

    /**
     * 获取 拣货模式
     *
     * @return pickPattern 拣货模式
     */
    public Byte getPickPattern() {
        return this.pickPattern;
    }

    /**
     * 设置 拣货模式
     *
     * @param pickPattern 拣货模式
     */
    public void setPickPattern(Byte pickPattern) {
        this.pickPattern = pickPattern;
    }

    public String getToPalletNo() {
        return toPalletNo;
    }

    public void setToPalletNo(String toPalletNo) {
        this.toPalletNo = toPalletNo;
    }

    /**
     * 获取 拣货方式:1、默认；2、电子标签拣货
     *
     * @return kindOfPicking 拣货方式:1、默认；2、电子标签拣货
     */
    public Byte getKindOfPicking() {
        return this.kindOfPicking;
    }

    /**
     * 设置 拣货方式:1、默认；2、电子标签拣货
     *
     * @param kindOfPicking 拣货方式:1、默认；2、电子标签拣货
     */
    public void setKindOfPicking(Byte kindOfPicking) {
        this.kindOfPicking = kindOfPicking;
    }

    /**
     * 获取 名称
     *
     * @return sortGroupName 名称
     */
    public String getSortGroupName() {
        return this.sortGroupName;
    }

    /**
     * 设置 名称
     *
     * @param sortGroupName 名称
     */
    public void setSortGroupName(String sortGroupName) {
        this.sortGroupName = sortGroupName;
    }

    /**
     * 获取 第几次追加拣货任务
     *
     * @return taskAppendSequence 第几次追加拣货任务
     */
    public Byte getTaskAppendSequence() {
        return this.taskAppendSequence;
    }

    /**
     * 设置 第几次追加拣货任务
     *
     * @param taskAppendSequence 第几次追加拣货任务
     */
    public void setTaskAppendSequence(Byte taskAppendSequence) {
        this.taskAppendSequence = taskAppendSequence;
    }

    /**
     * 获取 拣货任务特征 @see TaskWarehouseFeatureTypeConstants
     *
     * @return taskWarehouseFeatureType 拣货任务特征 @see TaskWarehouseFeatureTypeConstants
     */
    public Byte getTaskWarehouseFeatureType() {
        return this.taskWarehouseFeatureType;
    }

    /**
     * 设置 拣货任务特征 @see TaskWarehouseFeatureTypeConstants
     *
     * @param taskWarehouseFeatureType 拣货任务特征 @see TaskWarehouseFeatureTypeConstants
     */
    public void setTaskWarehouseFeatureType(Byte taskWarehouseFeatureType) {
        this.taskWarehouseFeatureType = taskWarehouseFeatureType;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }

    /**
     * 获取 绩效所属班次
     *
     * @return shiftOfPerformance 绩效所属班次
     */
    public String getShiftOfPerformance() {
        return this.shiftOfPerformance;
    }

    /**
     * 设置 绩效所属班次
     *
     * @param shiftOfPerformance 绩效所属班次
     */
    public void setShiftOfPerformance(String shiftOfPerformance) {
        this.shiftOfPerformance = shiftOfPerformance;
    }
}
