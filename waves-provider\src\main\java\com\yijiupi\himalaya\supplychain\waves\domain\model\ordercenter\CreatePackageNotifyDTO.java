package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.io.Serializable;
import java.util.List;

/**
 * 装箱时通知订单中台消息体DTO
 * 
 * <AUTHOR>
 * @Date 2022/5/25
 */
public class CreatePackageNotifyDTO implements Serializable {
    private static final long serialVersionUID = -5500962637009152505L;
    /**
     * oms订单ID
     */
    private Long orderId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 箱码编号
     */
    private String boxCode;
    /**
     * 箱号完整编码
     */
    private String boxCodeNo;
    /**
     * 装箱项list
     */
    private List<CreatePackageItemNotifyDTO> items;

    public List<CreatePackageItemNotifyDTO> getItems() {
        return items;
    }

    public void setItems(List<CreatePackageItemNotifyDTO> items) {
        this.items = items;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getBoxCode() {
        return boxCode;
    }

    public void setBoxCode(String boxCode) {
        this.boxCode = boxCode;
    }

    public String getBoxCodeNo() {
        return boxCodeNo;
    }

    public void setBoxCodeNo(String boxCodeNo) {
        this.boxCodeNo = boxCodeNo;
    }
}
