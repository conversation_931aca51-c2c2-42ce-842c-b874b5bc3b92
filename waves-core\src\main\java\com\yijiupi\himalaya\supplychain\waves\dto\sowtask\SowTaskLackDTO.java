package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;

/**
 * 播种任务缺货复核接口
 * 
 * <AUTHOR>
 * @Date 2022/6/21
 */
public class SowTaskLackDTO implements Serializable {
    private static final long serialVersionUID = 8429392611121838497L;
    /**
     * 播种任务号
     */
    private String sowTaskNo;
    /**
     * 播种任务项
     */
    private Long sowTaskItemId;

    public Long getSowTaskItemId() {
        return sowTaskItemId;
    }

    public void setSowTaskItemId(Long sowTaskItemId) {
        this.sowTaskItemId = sowTaskItemId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }
}
