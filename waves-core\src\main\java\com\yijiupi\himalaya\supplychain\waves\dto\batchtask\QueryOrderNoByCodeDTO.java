package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/12
 */
public class QueryOrderNoByCodeDTO implements Serializable {
    /**
     *
     */
    private String code;
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;


    /**
     * 获取
     *
     * @return code
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 设置
     *
     * @param code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
