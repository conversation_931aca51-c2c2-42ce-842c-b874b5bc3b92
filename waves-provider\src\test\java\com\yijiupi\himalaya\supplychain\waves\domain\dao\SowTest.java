package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.digitaltag.SortGroupBatchTaskSortBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchTaskSortBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.utils.DateUtil;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import java.util.*;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/7
 */
@RunWith(JUnit4.class)
public class SowTest {

    @Test
    public void digitalBatchTaskTest() {
        List<BatchTaskSortBO> boList = createBatchTaskSortBO();
        List<SowTaskPO> sowTaskPOList = createSowTaskList();
        SortGroupBatchTaskSortBL sortGroupBatchTaskSortBL = new SortGroupBatchTaskSortBL();
        // Map<Long, List<BatchTaskSortBO>> result =
        // sortGroupBatchTaskSortBL.sortDigitalBatchTaskByShareByTest(boList, sowTaskPOList);

        // Assertions.assertThat(result).isNotNull();
    }

    // 三个播种台，locationId 111；222；333
    private static List<BatchTaskSortBO> createBatchTaskSortBO() {
        List<BatchTaskSortBO> batchTaskSortBOS = new ArrayList<>();

        // 第一分区两个拣货任务，每个拣货任务属于一个播种任务
        BatchTaskSortBO bo1 = new BatchTaskSortBO();
        bo1.setBatchTaskId("1111");
        bo1.setLocationId(111L);
        bo1.setSowTaskId(1111L);
        bo1.setSortGroupId(11111L);
        bo1.setState(TaskStateEnum.未分拣.getType());
        bo1.setCreateTime(DateUtils.getAddMinutes(new Date(), 1));
        batchTaskSortBOS.add(bo1);

        BatchTaskSortBO bo2 = new BatchTaskSortBO();
        bo2.setBatchTaskId("2222");
        bo2.setLocationId(222L);
        bo2.setSowTaskId(2222L);
        bo2.setSortGroupId(11111L);
        bo2.setState(TaskStateEnum.未分拣.getType());
        bo2.setCreateTime(DateUtils.getAddMinutes(new Date(), 2));
        batchTaskSortBOS.add(bo2);

        // 第二分区三个拣货任务，每个拣货任务属于一个播种任务
        BatchTaskSortBO bo21 = new BatchTaskSortBO();
        bo21.setBatchTaskId("3333");
        bo21.setLocationId(111L);
        bo21.setSowTaskId(1111L);
        bo21.setSortGroupId(22222L);
        bo21.setState(TaskStateEnum.未分拣.getType());
        bo21.setCreateTime(DateUtils.getAddMinutes(new Date(), 3));
        batchTaskSortBOS.add(bo21);

        BatchTaskSortBO bo22 = new BatchTaskSortBO();
        bo22.setBatchTaskId("4444");
        bo22.setLocationId(222L);
        bo22.setSowTaskId(2222L);
        bo22.setSortGroupId(22222L);
        bo22.setState(TaskStateEnum.未分拣.getType());
        bo22.setCreateTime(DateUtils.getAddMinutes(new Date(), 4));
        batchTaskSortBOS.add(bo22);

        BatchTaskSortBO bo23 = new BatchTaskSortBO();
        bo23.setBatchTaskId("5555");
        bo23.setLocationId(333L);
        bo23.setSowTaskId(3333L);
        bo23.setSortGroupId(22222L);
        bo23.setState(TaskStateEnum.未分拣.getType());
        bo23.setCreateTime(DateUtils.getAddMinutes(new Date(), 5));
        batchTaskSortBOS.add(bo23);

        // 第三分区四个拣货任务，每个拣货任务属于一个播种任务
        BatchTaskSortBO bo31 = new BatchTaskSortBO();
        bo31.setBatchTaskId("6666");
        bo31.setLocationId(111L);
        bo31.setSowTaskId(3333L);
        bo31.setSortGroupId(33333L);
        bo31.setState(TaskStateEnum.分拣中.getType());
        bo31.setCreateTime(DateUtils.getAddMinutes(new Date(), 6));
        batchTaskSortBOS.add(bo31);

        BatchTaskSortBO bo32 = new BatchTaskSortBO();
        bo32.setBatchTaskId("7777");
        bo32.setLocationId(222L);
        bo32.setSowTaskId(1111L);
        bo32.setSortGroupId(33333L);
        bo32.setState(TaskStateEnum.未分拣.getType());
        bo32.setCreateTime(DateUtils.getAddMinutes(new Date(), 7));
        batchTaskSortBOS.add(bo32);

        BatchTaskSortBO bo33 = new BatchTaskSortBO();
        bo33.setBatchTaskId("8888");
        bo33.setLocationId(333L);
        bo33.setSowTaskId(3333L);
        bo33.setSortGroupId(33333L);
        bo33.setState(TaskStateEnum.未分拣.getType());
        bo33.setCreateTime(DateUtils.getAddMinutes(new Date(), 8));
        batchTaskSortBOS.add(bo33);

        return batchTaskSortBOS;
    }

    private static List<SowTaskPO> createSowTaskList() {
        List<SowTaskPO> sowTaskPOList = new ArrayList<>();
        SowTaskPO sowTaskPO1 = new SowTaskPO();
        sowTaskPO1.setId(1111L);
        sowTaskPO1.setState(SowTaskStateEnum.待播种.getType());
        sowTaskPO1.setLocationId(111L);
        sowTaskPOList.add(sowTaskPO1);

        SowTaskPO sowTaskPO2 = new SowTaskPO();
        sowTaskPO2.setId(2222L);
        sowTaskPO2.setState(SowTaskStateEnum.待播种.getType());
        sowTaskPO2.setLocationId(222L);
        sowTaskPOList.add(sowTaskPO2);

        SowTaskPO sowTaskPO3 = new SowTaskPO();
        sowTaskPO3.setId(3333L);
        sowTaskPO3.setState(SowTaskStateEnum.待播种.getType());
        sowTaskPO3.setLocationId(333L);
        sowTaskPOList.add(sowTaskPO3);

        return sowTaskPOList;
    }

    @Test
    public void npeTest() {
        List<BatchTaskPO> batchTaskPOList = new ArrayList<>();
        BatchTaskPO batchTaskPO1 = new BatchTaskPO();

        BatchTaskPO batchTaskPO2 = new BatchTaskPO();
        batchTaskPOList.add(batchTaskPO1);
        batchTaskPOList.add(batchTaskPO2);
        Optional<Date> completeTimeOptional = batchTaskPOList.stream().map(BatchTaskPO::getCompleteTime)
            .filter(Objects::nonNull).max(Comparator.naturalOrder());
        if (completeTimeOptional.isPresent()) {
            DateUtils.getDatetimeFormat(completeTimeOptional.get());
        }

    }

}
