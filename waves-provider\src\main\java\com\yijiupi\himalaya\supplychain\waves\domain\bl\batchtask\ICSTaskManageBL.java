package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.common.constant.RedisConstant;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.split.SplitBatchTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.agv.AgvTaskCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.agv.AgvTaskMoveDTO;
import com.yijiupi.himalaya.supplychain.wcs.dto.ics.ICSDrinkTaskCompleteDTO;
import com.yijiupi.himalaya.supplychain.wcs.dto.ics.ICSDrinkTaskCreateDTO;
import com.yijiupi.himalaya.supplychain.wcs.dto.ics.ICSDrinkTaskCreateItemDTO;
import com.yijiupi.himalaya.supplychain.wcs.service.task.IICSTaskManageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Service
public class ICSTaskManageBL {

    @Reference
    private IICSTaskManageService iicsTaskManageService;

    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    private static final Logger LOG = LoggerFactory.getLogger(ICSTaskManageBL.class);

    /**
     * 完成
     *
     * @param agvTaskCompleteDTO
     */
    @DistributeLock(conditions = "#agvTaskCompleteDTO.batchTaskId", sleepMills = 30000,
        key = RedisConstant.SUP_F + "ICSTaskManage:", lockType = DistributeLock.LockType.MUTEXLOCK, expireMills = 30000)
    @Transactional(rollbackFor = Exception.class)
    public void completeICSTask(AgvTaskCompleteDTO agvTaskCompleteDTO) {
        LOG.info("PDA完成AGV任务，入参:{}", JSON.toJSONString(agvTaskCompleteDTO));
        AssertUtils.hasText(agvTaskCompleteDTO.getBatchTaskId(), "拣货任务信息不能为空！");
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(agvTaskCompleteDTO.getBatchTaskId());
        if (Objects.isNull(batchTaskPO)) {
            throw new BusinessValidateException("拣货任务信息不存在！");
        }

        AssertUtils.notNull(batchTaskPO.getToLocationId(),
            "拣货任务" + batchTaskPO.getBatchTaskNo() + "出库位信息为空，请设置后再完成agv任务！");

        ICSDrinkTaskCompleteDTO completeDTO = new ICSDrinkTaskCompleteDTO();
        completeDTO.setBatchTaskId(batchTaskPO.getId());
        completeDTO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
        completeDTO.setWarehouseId(batchTaskPO.getWarehouseId());
        completeDTO.setLocationId(batchTaskPO.getToLocationId());
        completeDTO.setLocationName(batchTaskPO.getToLocationName());
        iicsTaskManageService.completeICSDrinkTask(completeDTO);
    }

    @DistributeLock(conditions = "#agvTaskMoveDTO.batchTaskId", sleepMills = 30000,
        key = RedisConstant.SUP_F + "ICSTaskManage:", lockType = DistributeLock.LockType.MUTEXLOCK, expireMills = 30000)
    @Transactional(rollbackFor = Exception.class)
    public void moveICSTask(AgvTaskMoveDTO agvTaskMoveDTO) {
        LOG.info("PDA开始下一个拣货任务，入参:{}", JSON.toJSONString(agvTaskMoveDTO));
        AssertUtils.hasText(agvTaskMoveDTO.getBatchTaskId(), "拣货任务信息不能为空！");
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(agvTaskMoveDTO.getBatchTaskId());
        if (Objects.isNull(batchTaskPO)) {
            throw new BusinessValidateException("拣货任务信息不存在！");
        }

        BatchTaskItemPO batchTaskItemPO =
            batchTaskItemMapper.selectBatchTaskItemById(agvTaskMoveDTO.getBatchTaskItemId());
        if (Objects.isNull(batchTaskItemPO)) {
            throw new BusinessValidateException("拣货任务明细信息不存在！");
        }
        ICSDrinkTaskCreateDTO ICSTaskCreateDTO = new ICSDrinkTaskCreateDTO();
        ICSTaskCreateDTO.setBatchTaskId(batchTaskPO.getId());
        ICSTaskCreateDTO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
        ICSTaskCreateDTO.setWarehouseId(batchTaskPO.getWarehouseId());
        ICSTaskCreateDTO.setOrgId(Integer.parseInt(batchTaskPO.getOrgId()));

        ICSDrinkTaskCreateItemDTO itemDTO = new ICSDrinkTaskCreateItemDTO();
        itemDTO.setBatchTaskItemId(batchTaskItemPO.getId());
        itemDTO.setLocationId(batchTaskItemPO.getLocationId());
        itemDTO.setLocationName(batchTaskItemPO.getLocationName());
        itemDTO.setSkuId(batchTaskItemPO.getSkuId());
        itemDTO.setProductName(batchTaskItemPO.getProductName());
        // itemDTO.setFromLocationId(item.get);
        // itemDTO.setFromLocationName();
        itemDTO.setPackageName(batchTaskItemPO.getPackageName());
        itemDTO.setUnitName(batchTaskItemPO.getUnitName());
        itemDTO.setUnitTotalCount(batchTaskItemPO.getUnitTotalCount());
        itemDTO.setSpecQuantity(batchTaskItemPO.getSpecQuantity());
        itemDTO.setBatchTaskItemId(batchTaskItemPO.getId());
        ICSTaskCreateDTO.setItem(itemDTO);
        iicsTaskManageService.createICSDrinkTask(ICSTaskCreateDTO);
    }
}
