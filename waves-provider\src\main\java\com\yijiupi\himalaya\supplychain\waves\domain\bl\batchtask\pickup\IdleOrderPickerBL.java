package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.pickup;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationConfigItemDTO;
import com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigRelateType;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.user.service.sign.IdleOrderPickerService;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.warehousesplit.WarehouseSplitHelper;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.QueryAssignOrderSorterResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.himalaya.supplychain.waves.utils.StreamUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigType.REST;
import static com.yijiupi.himalaya.supplychain.waves.enums.TaskWarehouseFeatureTypeConstants.TASK_WAREHOUSE_FEATURE_REST;

/**
 * <AUTHOR>
 * @since 2024-11-06 15:09
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class IdleOrderPickerBL {

    public static final String AUTO_ASSIGN_BATCH_TASK = "AutoAssignBatchTask";
    @Resource
    private BatchTaskMapper batchTaskMapper;

    @Resource
    private GlobalCache globalCache;

    @Resource
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    @Resource
    private WarehouseSplitHelper warehouseSplitHelper;
    
    @Reference
    private IBatchTaskQueryService batchTaskQueryService;

    @Reference
    private IdleOrderPickerService idleOrderPickerService;

    @Reference
    private IVariableValueService iVariableValueService;

    private static final Logger logger = LoggerFactory.getLogger(IdleOrderPickerBL.class);

    //兑奖订单类型
    private final Integer AWARD_ORDER_TYPE = 53;

    /**
     * 是否开启自动分配订单
     *
     * @param warehouseId 仓库id
     * @return 是否开启自动分配订单
     */
    public boolean enableAutoAssignOrder(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        VariableValueQueryDTO variableValueQuery = new VariableValueQueryDTO();
        variableValueQuery.setWarehouseId(warehouseId);
        variableValueQuery.setVariableKey(AUTO_ASSIGN_BATCH_TASK);
        VariableDefAndValueDTO valueDTO = iVariableValueService.detailVariable(variableValueQuery);
        if (valueDTO == null || StringUtils.isEmpty(valueDTO.getVariableData())) {
            return false;
        }else{
            return Boolean.parseBoolean(valueDTO.getVariableData());
        }
    }

    /**
     * 波次创建完毕后自动指派空闲分拣工
     *
     * @param orders 出库单
     */
    @Async(value = "waveTaskExecutor")
    public void assignIdleOrderPicker(List<OutStockOrderPO> orders, Integer warehouseId) {
        if(CollectionUtils.isEmpty(orders)) {
            return;
        }
        boolean isCanAutoAssign = warehouseSplitHelper.enableWarehouseSplit(warehouseId) && enableAutoAssignOrder(warehouseId);
        if (!isCanAutoAssign) {
            logger.info("没开分仓+自动分配订单配置, 跳过后续处理");
            return;
        }else{
            logger.info("按订单({})-自动指派分拣工", warehouseId);
        }
        WarehouseAllocationConfigDTO config = warehouseSplitHelper.getConfigByType(REST, warehouseId);
        if (config == null) {
            logger.info("没有休食分仓配置, 跳过后续处理");
            return;
        }

        //SCM-19926 自动指派拣货任务优化，处理赠品和兑奖的拣货任务
        // 1、分配赠品单及兑奖出库单，优先分配给相同客户已分配的拣货工
        // 订单类型=53（兑奖）或订单金额=0的订单
        List<OutStockOrderPO> lstGiftOrder = orders.stream()
                .filter(p-> p.getAddressId() != null &&
                        (Objects.equals(p.getOrdertype(), AWARD_ORDER_TYPE) || p.getOrderamount().compareTo(BigDecimal.ZERO) == 0))
                .collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(lstGiftOrder)){
            //查找相同地址未完成拣货任务的拣货工
            //结构：TaskID，OrderID，SorterID
            List<QueryAssignOrderSorterResultBO> sorterResultBOS = queryAssignSorterByOrder(warehouseId, lstGiftOrder);
            logger.info("相同用户正在进行中任务的分拣员：{}", JSON.toJSONString(sorterResultBOS));
            if(!CollectionUtils.isEmpty(sorterResultBOS)) {
                List<Integer> idleUsers = new ArrayList<>();
                List<String> lstTaskId = sorterResultBOS.stream().map(p -> String.valueOf(p.getTaskID())).collect(Collectors.toList());
                Deque<BatchTaskDTO> lstGiftTask = queryRestTasksById(lstTaskId);
                lstGiftTask.forEach(p -> {
                    //按拣货任务ID进行匹配，查找当前用户是否有未完成且已分配的订单，分配给同一个人
                    sorterResultBOS.stream().filter(q -> Objects.equals(String.valueOf(q.getTaskID()), p.getId())).findAny()
                            .ifPresent(q -> {
                                //按拣货任务顺序添加分配人员
                                idleUsers.add(q.getSorterId());
                                //移除已成功分配的订单
                                orders.removeIf(order -> Objects.equals(order.getId(), q.getOrderId()));
                            });
                });
                if (!CollectionUtils.isEmpty(lstGiftTask) && !CollectionUtils.isEmpty(idleUsers)) {
                    //分配拣货任务给当前用户未完成的拣货任务对应的分拣员
                    idleUserByTasks(idleUsers, lstGiftTask);
                }
            }
        }

        // 2、分配其他订单的拣货员
        List<Integer> idleUsers = getIdleUsers(config, warehouseId);
        if (idleUsers.isEmpty()) {
            logger.info("没有空闲的休食分仓的分拣工, 跳过后续处理");
            return;
        }

        // 根据订单过滤休食拣货任务
        if(!CollectionUtils.isEmpty(orders)){
            Deque<BatchTaskDTO> tasks = queryRestTasksByOrder(orders);
            idleUserByTasks(idleUsers, tasks);
        }
        logger.info("任务指派完成");
    }

    private void idleUserByTasks(List<Integer> idleUsers, Deque<BatchTaskDTO> tasks) {
        logger.info("分配拣货任务 空闲用户：{},待分配任务：{}", JSON.toJSONString(idleUsers), JSON.toJSONString(tasks));
        for (Integer userId : idleUsers) {
            if (tasks.isEmpty()) {
                break;
            }
            BatchTaskDTO task = tasks.pop();
            String userName = globalCache.getAdminTrueName(userId);
            task.setSorter(userName);
            task.setSorterId(userId);
            batchTaskQueryService.updateBatch(Collections.singletonList(task), "系统自动分配");
            //标识用户开始工作
            idleOrderPickerService.onOrderPickerWorkingEvent(task.getWarehouseId(), userId, true);
            logger.info("自动指派 {} 给 {}", task.getBatchTaskNo(), userName);
        }
    }

    /**
     * 重新分配所有未分配的拣货任务
     *
     * @param warehouseId 仓库ID
     */
    @Async(value = "waveTaskExecutor")
    public void assignIdleOrderPickerByWarehouse(List<Integer> lstUserId, Integer warehouseId,List<String> lstExpTaskNo) {
        boolean isCanAutoAssign = warehouseSplitHelper.enableWarehouseSplit(warehouseId) && enableAutoAssignOrder(warehouseId);
        if (!isCanAutoAssign) {
            logger.info("没开分仓+自动分配订单配置, 跳过后续处理");
            return;
        }else{
            logger.info("按仓库({})-自动指派分拣工", warehouseId);
        }
        WarehouseAllocationConfigDTO config = warehouseSplitHelper.getConfigByType(REST, warehouseId);
        if (config == null) {
            logger.info("没有休食分仓配置, 跳过后续处理");
            return;
        }
        logger.info("完成拣货分拣工:{}", JSON.toJSON(lstUserId));
        if(com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(lstUserId)) {
            //有未完成任务的分拣工不能释放，排除当前正在操作的任务
            List<Integer> lstNotFinishedSorter = batchTaskMapper.findNotFinishedBatchTaskBySorterAndWarehouseId(warehouseId, lstUserId, lstExpTaskNo);
            lstUserId.forEach(userId -> {
                if (userId != null && !lstNotFinishedSorter.contains(userId)) {
                    logger.info("释放分拣工:{}", userId);
                    //标识用户已空闲
                    idleOrderPickerService.onOrderPickerWorkingEvent(warehouseId, userId, false);
                }
            });
        }
        // 指派拣货员
        List<Integer> idleUsers = getIdleUsers(config, warehouseId);
        if (idleUsers.isEmpty()) {
            logger.info("没有空闲的休食分仓的分拣工, 跳过后续处理");
            return;
        }
        //查询所有未分配的休食拣货任务
        Deque<BatchTaskDTO> tasks = queryRestTasksByWarehouseId(warehouseId);
        //排除当前正在操作的任务，防止由于事务未提交导致重复处理
        if(!CollectionUtils.isEmpty(lstExpTaskNo)) {
            tasks.removeIf(p -> lstExpTaskNo.contains(p.getBatchTaskNo()));
        }
        idleUserByTasks(idleUsers, tasks);
        logger.info("任务指派完成");
    }

    /**
     * 根据订单查找相同地址已分配的拣货工
     *
     */
    private List<QueryAssignOrderSorterResultBO> queryAssignSorterByOrder(Integer warehouseId, List<OutStockOrderPO> orders) {
        List<Long> lstOrderId = orders.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
        return batchTaskMapper.findAssignOrderSorter(warehouseId, lstOrderId);
    }

    /**
     * 查询休食分仓的拣货任务
     *
     * @param orders 出库单数据
     * @return 休食分仓的拣货任务
     */
    private Deque<BatchTaskDTO> queryRestTasksByOrder(List<OutStockOrderPO> orders) {
        Set<Long> orderIds = orders.stream().map(OutStockOrderPO::getId).collect(Collectors.toSet());
        List<String> taskIds = orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(orderIds)
                .stream().map(OrderItemTaskInfoPO::getBatchTaskId).collect(Collectors.toList());
        return queryRestTasksById(taskIds);
    }

    /**
     * 查询休食分仓的拣货任务
     *
     * @param taskIds 拣货任务ID
     * @return 休食分仓的拣货任务
     */
    private Deque<BatchTaskDTO> queryRestTasksById(List<String> taskIds) {
        return batchTaskMapper.findBatchTaskByIds(taskIds).stream()
                .filter(it -> TASK_WAREHOUSE_FEATURE_REST.equals(it.getTaskWarehouseFeatureType()) && StringUtils.isEmpty(it.getSorter()))
                .map(it -> StreamUtils.copy(it, BatchTaskDTO::new))
                .collect(Collectors.toCollection(ArrayDeque::new));
    }

    /**
     * 查询休食分仓的未分配的拣货任务
     *
     * @param warehouseId 仓库ID
     * @return 休食分仓的拣货任务
     */
    private Deque<BatchTaskDTO> queryRestTasksByWarehouseId(Integer warehouseId) {
        return batchTaskMapper.findNoSorterBatchTaskByWarehouseId(warehouseId).stream()
                .map(it -> StreamUtils.copy(it, BatchTaskDTO::new))
                .collect(Collectors.toCollection(ArrayDeque::new));
    }

    /**
     * 获取空闲的当前分仓的分拣工
     *
     * @param config      分仓配置
     * @param warehouseId 仓库 id
     * @return 空闲的当前分仓的分拣工
     */
    private List<Integer> getIdleUsers(WarehouseAllocationConfigDTO config, Integer warehouseId) {
        Set<Integer> workerIds = config.getItems().stream()
                .filter(it -> WarehouseAllocationConfigRelateType.WAREHOUSE_WORKER.valueEquals(it.getRelateType()))
                .map(WarehouseAllocationConfigItemDTO::getRelateId)
                .filter(NumberUtils::isNumber).map(Integer::valueOf)
                .collect(Collectors.toSet());
        return idleOrderPickerService.listIdleOrderPickers(warehouseId).stream()
                .filter(workerIds::contains).collect(Collectors.toList());
    }

}
