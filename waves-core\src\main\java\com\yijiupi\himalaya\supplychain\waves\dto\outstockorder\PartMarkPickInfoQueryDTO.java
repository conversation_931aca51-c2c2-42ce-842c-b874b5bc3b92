package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/7
 */
public class PartMarkPickInfoQueryDTO implements Serializable {
    /**
     * oms的订单id
     */
    private List<Long> businessIds;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 组织机构id
     */
    private Integer orgId;

    /**
     * 获取 oms的订单id
     *
     * @return businessIds oms的订单id
     */
    public List<Long> getBusinessIds() {
        return this.businessIds;
    }

    /**
     * 设置 oms的订单id
     *
     * @param businessIds oms的订单id
     */
    public void setBusinessIds(List<Long> businessIds) {
        this.businessIds = businessIds;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }
}
