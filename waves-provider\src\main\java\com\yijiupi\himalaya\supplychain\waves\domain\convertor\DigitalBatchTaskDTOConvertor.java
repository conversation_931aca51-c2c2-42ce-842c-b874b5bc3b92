package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskItemDTO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/7
 */
public class DigitalBatchTaskDTOConvertor {

    public static List<DigitalBatchTaskDTO> convert(List<DigitalBatchTaskDTO> batchTaskDTOS,
        List<BatchTaskItemPO> batchTaskItemPOList, List<LoactionDTO> locationDTOS) {

        Map<String, List<BatchTaskItemPO>> batchTaskItemGroupMap =
            batchTaskItemPOList.stream().collect(Collectors.groupingBy(BatchTaskItemPO::getBatchTaskId));

        Map<Long, LoactionDTO> loactionDTOMap = locationDTOS.stream().filter(m -> Objects.nonNull(m.getAisleId()))
            .collect(Collectors.toMap(LoactionDTO::getId, v -> v));

        for (DigitalBatchTaskDTO batchTaskDTO : batchTaskDTOS) {
            List<BatchTaskItemPO> itemPOList = batchTaskItemGroupMap.get(batchTaskDTO.getBatchTaskId());

            batchTaskDTO.setItemList(itemPOList.stream().map(item -> {
                DigitalBatchTaskItemDTO itemDTO = new DigitalBatchTaskItemDTO();
                itemDTO.setBatchTaskItemId(item.getId());
                itemDTO.setLocationId(item.getLocationId());
                itemDTO.setLocationName(item.getLocationName());
                itemDTO.setUnitTotalCount(item.getUnitTotalCount());
                itemDTO.setSaleSpecQuantity(item.getSaleSpecQuantity());
                LoactionDTO loactionDTO = loactionDTOMap.get(item.getLocationId());
                if (Objects.nonNull(loactionDTO)) {
                    itemDTO.setRoadWayId(loactionDTO.getAisleId());
                    itemDTO.setRoadWayName(loactionDTO.getAisleNo());
                }
                itemDTO.setState(item.getTaskState());
                return itemDTO;
            }).collect(Collectors.toList()));
        }

        return batchTaskDTOS;
    }

}
