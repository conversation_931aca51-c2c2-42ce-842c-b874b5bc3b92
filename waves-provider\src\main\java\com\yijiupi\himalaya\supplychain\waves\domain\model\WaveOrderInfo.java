package com.yijiupi.himalaya.supplychain.waves.domain.model;

import java.util.Objects;

/**
 * 波次订单的共用信息
 * 
 * <AUTHOR>
 * @Date 2022/4/1
 */
public class WaveOrderInfo {
    /**
     * 序号
     */
    private Integer sequence;
    /**
     * 出库位
     */
    private Long toLocationId;

    public WaveOrderInfo(Integer sequence) {
        this.sequence = sequence;
    }

    public WaveOrderInfo(Integer sequence, Long toLocationId) {
        this.sequence = sequence;
        this.toLocationId = toLocationId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        WaveOrderInfo that = (WaveOrderInfo)o;
        return Objects.equals(sequence, that.sequence) && Objects.equals(toLocationId, that.toLocationId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sequence, toLocationId);
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }
}
