package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023-09-11 10:08
 **/
public class ConfirmReturnDTO implements Serializable {

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品数量, 大件
     */
    private BigDecimal packageCount;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 产品数量, 小件
     */
    private BigDecimal unitCount;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 包装规格
     */
    private String specName;

    /**
     * 来源货位
     */
    private String fromLocationName;

    /**
     * 现存货位
     */
    private String toLocationName;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public String getFromLocationName() {
        return fromLocationName;
    }

    public void setFromLocationName(String fromLocationName) {
        this.fromLocationName = fromLocationName;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }
}
