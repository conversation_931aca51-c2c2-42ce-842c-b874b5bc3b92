package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 这里根据出库批次号 和 车次号没用，很多都是中心仓的数据有问题，但表现为前置仓的车次信息
 * 
 * <AUTHOR>
 * @date 2024/2/26
 */
public class ModPackageInfoByOutBoundInfoDTO implements Serializable {
    /**
     * 出库批次号
     */
    private List<String> boundNos;
    /**
     * 取货单id
     */
    private List<Long> pickOrderIds;
    /**
     * 取货单号
     */
    private List<String> pickOrderNos;
    /**
     * 仓库id，如果是内配，一般是前置仓的仓库id
     */
    private Integer warehouseId;
    /**
     * 组织机构id ，如果是内配，一般是前置仓的组织机构id
     */
    private Integer orgId;
    /**
     * 订单号
     */
    private List<String> orderNos;
    /**
     * 出库单号
     */
    private List<Long> outStockOrderIds;
    /**
     * 一般是前置仓的车次号
     */
    private List<String> relatedBatchNos;
    /**
     * 一般是前置仓的车次id
     */
    private List<String> relatedBatchIds;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 订单号
     *
     * @return orderNos 订单号
     */
    public List<String> getOrderNos() {
        return this.orderNos;
    }

    /**
     * 设置 订单号
     *
     * @param orderNos 订单号
     */
    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }

    /**
     * 获取 出库单号
     *
     * @return outStockOrderIds 出库单号
     */
    public List<Long> getOutStockOrderIds() {
        return this.outStockOrderIds;
    }

    /**
     * 设置 出库单号
     *
     * @param outStockOrderIds 出库单号
     */
    public void setOutStockOrderIds(List<Long> outStockOrderIds) {
        this.outStockOrderIds = outStockOrderIds;
    }

    /**
     * 获取 出库批次号
     *
     * @return boundNos 出库批次号
     */
    public List<String> getBoundNos() {
        return this.boundNos;
    }

    /**
     * 设置 出库批次号
     *
     * @param boundNos 出库批次号
     */
    public void setBoundNos(List<String> boundNos) {
        this.boundNos = boundNos;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 取货单id
     *
     * @return pickOrderIds 取货单id
     */
    public List<Long> getPickOrderIds() {
        return this.pickOrderIds;
    }

    /**
     * 设置 取货单id
     *
     * @param pickOrderIds 取货单id
     */
    public void setPickOrderIds(List<Long> pickOrderIds) {
        this.pickOrderIds = pickOrderIds;
    }

    /**
     * 获取 一般是前置仓的车次号
     *
     * @return relatedBatchNos 一般是前置仓的车次号
     */
    public List<String> getRelatedBatchNos() {
        return this.relatedBatchNos;
    }

    /**
     * 设置 一般是前置仓的车次号
     *
     * @param relatedBatchNos 一般是前置仓的车次号
     */
    public void setRelatedBatchNos(List<String> relatedBatchNos) {
        this.relatedBatchNos = relatedBatchNos;
    }

    /**
     * 获取 一般是前置仓的车次id
     *
     * @return relatedBatchIds 一般是前置仓的车次id
     */
    public List<String> getRelatedBatchIds() {
        return this.relatedBatchIds;
    }

    /**
     * 设置 一般是前置仓的车次id
     *
     * @param relatedBatchIds 一般是前置仓的车次id
     */
    public void setRelatedBatchIds(List<String> relatedBatchIds) {
        this.relatedBatchIds = relatedBatchIds;
    }

    /**
     * 获取 取货单号
     *
     * @return pickOrderNos 取货单号
     */
    public List<String> getPickOrderNos() {
        return this.pickOrderNos;
    }

    /**
     * 设置 取货单号
     *
     * @param pickOrderNos 取货单号
     */
    public void setPickOrderNos(List<String> pickOrderNos) {
        this.pickOrderNos = pickOrderNos;
    }
}
