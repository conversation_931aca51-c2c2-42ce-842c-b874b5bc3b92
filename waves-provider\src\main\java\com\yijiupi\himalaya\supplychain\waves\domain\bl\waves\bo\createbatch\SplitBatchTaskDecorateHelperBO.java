package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch;

import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/4
 */
public class SplitBatchTaskDecorateHelperBO {

    private WavesStrategyBO wavesStrategyDTO;

    private List<OutStockOrderPO> orders;

    private ProcessBatchDTO processBatchDTO;

    /**
     * 获取
     *
     * @return wavesStrategyDTO
     */
    public WavesStrategyBO getWavesStrategyDTO() {
        return this.wavesStrategyDTO;
    }

    /**
     * 设置
     *
     * @param wavesStrategyDTO
     */
    public void setWavesStrategyDTO(WavesStrategyBO wavesStrategyDTO) {
        this.wavesStrategyDTO = wavesStrategyDTO;
    }

    /**
     * 获取
     *
     * @return orders
     */
    public List<OutStockOrderPO> getOrders() {
        return this.orders;
    }

    /**
     * 设置
     *
     * @param orders
     */
    public void setOrders(List<OutStockOrderPO> orders) {
        this.orders = orders;
    }

    /**
     * 获取
     *
     * @return processBatchDTO
     */
    public ProcessBatchDTO getProcessBatchDTO() {
        return this.processBatchDTO;
    }

    /**
     * 设置
     *
     * @param processBatchDTO
     */
    public void setProcessBatchDTO(ProcessBatchDTO processBatchDTO) {
        this.processBatchDTO = processBatchDTO;
    }
}
