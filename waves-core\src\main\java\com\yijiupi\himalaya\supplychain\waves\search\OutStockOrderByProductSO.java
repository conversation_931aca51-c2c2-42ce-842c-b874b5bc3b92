package com.yijiupi.himalaya.supplychain.waves.search;

import java.io.Serializable;

import com.yijiupi.himalaya.base.search.PageCondition;

/**
 * 查询波次下的订单产品明细
 *
 * <AUTHOR>
 * @date 2019/4/25 23:19
 */
public class OutStockOrderByProductSO extends PageCondition implements Serializable {
    private static final long serialVersionUID = -3206683398150609177L;

    /**
     * 波次id
     */
    private String batchId;

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }
}
