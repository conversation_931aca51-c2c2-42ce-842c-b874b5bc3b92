package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum;
import com.yijiupi.himalaya.supplychain.wcs.dto.task.DPSCancelPickTaskBatchCreateDTO;
import com.yijiupi.himalaya.supplychain.wcs.dto.task.DPSCancelPickTaskCreateDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/11/14
 */
public class CancelDPSTaskConvertor {

    public static DPSCancelPickTaskBatchCreateDTO convert(List<BatchTaskPO> batchTaskPOList, Integer optUserId) {
        DPSCancelPickTaskBatchCreateDTO batchCreateDTO = new DPSCancelPickTaskBatchCreateDTO();

        List<DPSCancelPickTaskCreateDTO> cancelList =
            batchTaskPOList.stream()
                .filter(m -> Objects.nonNull(m.getPickPattern())
                    && !Objects.equals(BatchTaskPickPatternEnum.人工拣货.getType(), m.getPickPattern()))
                .map(batchTaskPO -> {
                    DPSCancelPickTaskCreateDTO cancelDTO = new DPSCancelPickTaskCreateDTO();
                    cancelDTO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
                    cancelDTO.setBatchTaskId(batchTaskPO.getId());
                    cancelDTO.setOrgId(Integer.valueOf(batchTaskPO.getOrgId()));
                    cancelDTO.setWarehouseId(batchTaskPO.getWarehouseId());
                    cancelDTO.setOptUserId(optUserId);

                    return cancelDTO;
                }).collect(Collectors.toList());

        batchCreateDTO.setCancelList(cancelList);
        batchCreateDTO.setOptUserId(optUserId);

        return batchCreateDTO;
    }

}
