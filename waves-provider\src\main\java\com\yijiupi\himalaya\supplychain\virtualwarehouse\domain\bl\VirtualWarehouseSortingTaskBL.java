package com.yijiupi.himalaya.supplychain.virtualwarehouse.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.Pager;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderState;
import com.yijiupi.himalaya.supplychain.virtualwarehouse.domain.convert.VirtualWarehouseSortingTaskConvert;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder.OutStockOrderStateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto.VirtualWarehouseSortingTaskDetailsDTO;
import com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto.VirtualWarehouseSortingTaskListDTO;
import com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto.VirtualWarehouseSortingTaskRequestDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 虚仓二次分拣
 */
@Service
public class VirtualWarehouseSortingTaskBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(VirtualWarehouseSortingTaskBL.class);

    @Autowired
    private SowTaskMapper sowTaskMapper;

    @Autowired
    private SowTaskItemMapper sowTaskItemMapper;

    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Reference
    private IProductSkuService productSkuService;
    @Reference
    private ILocationService iLocationService;

    @Resource
    private OutStockOrderStateBL outStockOrderStateBL;

    /**
     * 查询分拣任务列表信息
     */
    public PageList<VirtualWarehouseSortingTaskListDTO>
    queryWaitSortingTaskList(VirtualWarehouseSortingTaskRequestDTO request) {
        PageList<VirtualWarehouseSortingTaskListDTO> pageList = new PageList<>();
        List<String> countList =
                Optional.ofNullable(sowTaskMapper.queryWaitSortingTaskCount(request)).orElse(new ArrayList<>());
        Pager pager = new Pager();
        if (countList.size() == 0) {
            pageList.setPager(pager);
            return pageList;
        }
        int index = (request.getCurrentPage() - 1) * request.getPageSize();
        request.setIndex(index);
        List<Long> locationIdList = sowTaskMapper.queryWaitSortingTaskPage(request);
        request.setLocationIdList(locationIdList);
        List<SowTaskPO> sowTaskPOS =
                Optional.ofNullable(sowTaskMapper.queryWaitSortingTaskList(request)).orElse(new PageResult<>());
        // 查询未播种项数量
        List<Long> collect = sowTaskPOS.stream().map(SowTaskPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            pageList.setPager(pager);
            return pageList;
        }
        List<SowTaskItemPO> sowTaskItemPOS = sowTaskItemMapper.queryNotSkuCount(
                sowTaskPOS.stream().map(SowTaskPO::getId).collect(Collectors.toList()), request.getCityId());
        if (CollectionUtils.isEmpty(sowTaskItemPOS)) {
            pageList.setPager(pager);
            return pageList;
        }
        pager.setPageSize(request.getPageSize());
        pager.setCurrentPage(request.getCurrentPage());
        pager.setRecordCount(countList.size());
        pageList.setPager(pager);
        LOGGER.info("查询分拣任务列表信息 sowTaskPOS={}", JSON.toJSONString(sowTaskPOS));
        pageList.setDataList(VirtualWarehouseSortingTaskConvert.convert(sowTaskPOS, sowTaskItemPOS, countList));
        return pageList;
    }

    /**
     * 查询分拣任务明细 按照产品维度
     *
     * @param request
     * @return
     */
    public List<VirtualWarehouseSortingTaskDetailsDTO>
    querySortingTaskListDetails(VirtualWarehouseSortingTaskRequestDTO request) {
        List<VirtualWarehouseSortingTaskDetailsDTO> virtualWarehouseSortingTaskDetailsDTOS = new ArrayList<>();
        // 查询播种明细
        List<SowTaskItemPO> sowTaskItemPOS = sowTaskItemMapper.querySortingTaskListDetails(request);
        if (CollectionUtils.isEmpty(sowTaskItemPOS)) {
            return virtualWarehouseSortingTaskDetailsDTOS;
        }
        // 获取出库位
        List<OutStockOrderItemPO> outStockOrderItemPOS = outStockOrderItemMapper.listToLocationByTaskIds(
                request.getCityId(), sowTaskItemPOS.stream().map(SowTaskItemPO::getId).collect(Collectors.toList()));
        // 查询条码信息
        // 获取箱码条码
        Set<Long> collect = sowTaskItemPOS.stream().map(SowTaskItemPO::getProductSkuId).collect(Collectors.toSet());
        Map<Long, ProductCodeDTO> packageAndUnitCode =
                productSkuService.getPackageAndUnitCode(collect, request.getCityId());
        return VirtualWarehouseSortingTaskConvert.convert(sowTaskItemPOS, outStockOrderItemPOS, packageAndUnitCode);
    }

    /**
     * 领取分拣任务
     *
     * @param request
     */
    @Transactional(rollbackFor = Throwable.class)
    public void getSortingTask(VirtualWarehouseSortingTaskRequestDTO request) {
        LOGGER.info("虚仓二次分拣领取播种任务，入参{}", JSON.toJSONString(request));
        List<SowTaskItemPO> sowTaskItemPOS = sowTaskItemMapper.querySowTaskNoByItemId(request);
        if (CollectionUtils.isEmpty(sowTaskItemPOS)) {
            throw new BusinessValidateException("暂无可领取任务");
        }
        if (sowTaskItemPOS.stream().allMatch(s -> s.getState().compareTo(SowTaskStateEnum.待播种.getType()) != 0)) {
            throw new BusinessValidateException("该产品已经被" + sowTaskItemPOS.get(0).getSortingUserName() + "领取分拣，请移交给对方进行分拣");
        }
        List<Long> sowTaskItemIds = sowTaskItemPOS.stream().filter(m -> m.getState() != SowTaskStateEnum.已播种.getType()).map(SowTaskItemPO::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sowTaskItemIds)) {
            throw new BusinessValidateException("产品都已播种！");
        }
        // 修改播种任务
        sowTaskItemMapper.getSortingTask(request.getCityId(), sowTaskItemIds, request.getUserId(), request.getUserName());
        // 查询出库单id
        List<String> collect = sowTaskItemPOS.stream().map(SowTaskItemPO::getSowTaskNo).collect(Collectors.toList());
        List<OutStockOrderItemPO> outStockOrderItemPOS =
                outStockOrderItemMapper.listOutStockOrderIdBySowNo(collect, request.getCityId());
        List<OutStockOrderItemPO> locationIdList = outStockOrderItemPOS.stream()
                .filter(s -> ObjectUtils.isEmpty(s.getLocationId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(locationIdList)) {
            String s = locationIdList.stream().map(OutStockOrderItemPO::getBoundNo).distinct()
                    .collect(Collectors.joining(","));
            throw new BusinessValidateException("出库批次：" + s + "需要设置出库位");
        }
        for (String no : collect) {
            sowTaskMapper.updateSowTaskState(no, SowTaskStateEnum.播种中.getType(), request.getCityId());
        }
        // 修改出库单状态
//        List<Long> orderIds = outStockOrderItemPOS.stream().map(OutStockOrderItemPO::getOutstockorderId).collect(Collectors.toList());
//        outStockOrderStateBL.updateStateByOrderIds(orderIds, OutStockOrderState.拣货中);
    }

    /**
     * 查询用户分拣任务信息
     *
     * @param request
     * @return
     */
    public List<VirtualWarehouseSortingTaskDetailsDTO>
    queryUserSortingTaskListDetails(VirtualWarehouseSortingTaskRequestDTO request) {
        List<VirtualWarehouseSortingTaskDetailsDTO> virtualWarehouseSortingTaskDetailsDTOS = new ArrayList<>();
        List<SowTaskItemPO> sowTaskItemPOS = sowTaskItemMapper.queryUserSortingTaskListDetails(request);
        if (CollectionUtils.isEmpty(sowTaskItemPOS)) {
            return virtualWarehouseSortingTaskDetailsDTOS;
        }
        List<OutStockOrderItemPO> outStockOrderItemPOS = outStockOrderItemMapper.listToLocationByTaskIds(
                request.getCityId(), sowTaskItemPOS.stream().map(SowTaskItemPO::getId).collect(Collectors.toList()));

        Map<Long, Integer> locationSeq = new HashMap<>();
        List<Long> toLocationIdList = outStockOrderItemPOS.stream().map(OutStockOrderItemPO::getLocationId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(toLocationIdList)) {
            LocationInfoQueryDTO locationQueryDTO = new LocationInfoQueryDTO();
            locationQueryDTO.setCityId(request.getCityId());
            locationQueryDTO.setWarehouseId(request.getWarehouseId());
            locationQueryDTO.setLocationIdList(toLocationIdList);
            List<LoactionDTO> toLocationList = iLocationService.pageListLocation(locationQueryDTO).getDataList();
            if (!CollectionUtils.isEmpty(toLocationList)) {
                locationSeq = toLocationList.stream().filter(loc -> loc.getSequence() != null)
                        .collect(Collectors.toMap(LoactionDTO::getId, LoactionDTO::getSequence, (k1, k2) -> k1));
            }
        }
        return VirtualWarehouseSortingTaskConvert.convertItem(sowTaskItemPOS, outStockOrderItemPOS, locationSeq);
    }
}
