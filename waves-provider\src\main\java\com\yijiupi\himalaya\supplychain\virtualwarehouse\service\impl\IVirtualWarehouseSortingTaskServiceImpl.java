package com.yijiupi.himalaya.supplychain.virtualwarehouse.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.virtualwarehouse.domain.bl.VirtualWarehouseSortingTaskBL;
import com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto.VirtualWarehouseSortingTaskDetailsDTO;
import com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto.VirtualWarehouseSortingTaskListDTO;
import com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto.VirtualWarehouseSortingTaskRequestDTO;
import com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.service.IVirtualWarehouseSortingTaskService;

/**
 * <AUTHOR> 虚仓二次分拣
 */
@Service
public class IVirtualWarehouseSortingTaskServiceImpl implements IVirtualWarehouseSortingTaskService {

    @Autowired
    private VirtualWarehouseSortingTaskBL virtualWarehouseSortingTaskBL;

    /**
     * 虚仓二次分拣查询分拣任务信息
     * 
     * @param request
     * @return
     */
    @Override
    public PageList<VirtualWarehouseSortingTaskListDTO>
        queryWaitSortingTaskList(VirtualWarehouseSortingTaskRequestDTO request) {
        return virtualWarehouseSortingTaskBL.queryWaitSortingTaskList(request);
    }

    /**
     * 查询分拣明细 根据产品维度
     * 
     * @param request
     * @return
     */
    @Override
    public List<VirtualWarehouseSortingTaskDetailsDTO>
        querySortingTaskListDetails(VirtualWarehouseSortingTaskRequestDTO request) {
        return virtualWarehouseSortingTaskBL.querySortingTaskListDetails(request);
    }

    /**
     * 领取分拣任务
     * 
     * @param request
     */
    @Override
    public void getSortingTask(VirtualWarehouseSortingTaskRequestDTO request) {
        virtualWarehouseSortingTaskBL.getSortingTask(request);
    }

    /**
     * 查询用户分拣任务信息
     * 
     * @param request
     * @return
     */
    @Override
    public List<VirtualWarehouseSortingTaskDetailsDTO>
        queryUserSortingTaskListDetails(VirtualWarehouseSortingTaskRequestDTO request) {
        return virtualWarehouseSortingTaskBL.queryUserSortingTaskListDetails(request);
    }
}
