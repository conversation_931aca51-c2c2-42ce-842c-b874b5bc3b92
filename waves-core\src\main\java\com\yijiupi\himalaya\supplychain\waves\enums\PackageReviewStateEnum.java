package com.yijiupi.himalaya.supplychain.waves.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

public enum PackageReviewStateEnum {
    /**
     * 枚举
     */
    待复核((byte)0), 复核中((byte)1), 已复核((byte)2);

    /**
     * type
     */
    private byte type;

    PackageReviewStateEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     * 
     * @param value
     * @return
     */
    public static String getEnumByValue(Byte value) {
        PackageReviewStateEnum packageReviewStateEnum = null;
        if (value != null) {
            packageReviewStateEnum = cache.get(value);
        }
        return packageReviewStateEnum == null ? null : packageReviewStateEnum.name();
    }

    private static Map<Byte, PackageReviewStateEnum> cache =
        EnumSet.allOf(PackageReviewStateEnum.class).stream().collect(Collectors.toMap(p -> p.type, p -> p));
}
