package com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch;

import java.util.List;
import java.util.stream.Collectors;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/16
 */
public class CreateBatchTaskByOrderResultHelperBO {

    private List<BatchTaskPO> batchTaskPOList;

    private List<BatchTaskItemDTO> batchTaskItemDTOList;

    private List<OrderItemTaskInfoPO> orderItemTaskInfoPOList;

    private List<OutStockOrderPO> lstNoLocationOrder;

    /**
     * 获取
     *
     * @return batchTaskPOList
     */
    public List<BatchTaskPO> getBatchTaskPOList() {
        return this.batchTaskPOList;
    }

    /**
     * 设置
     *
     * @param batchTaskPOList
     */
    public void setBatchTaskPOList(List<BatchTaskPO> batchTaskPOList) {
        this.batchTaskPOList = batchTaskPOList;
    }

    /**
     * 获取
     *
     * @return orderItemTaskInfoPOList
     */
    public List<OrderItemTaskInfoPO> getOrderItemTaskInfoPOList() {
        return this.orderItemTaskInfoPOList;
    }

    /**
     * 设置
     *
     * @param orderItemTaskInfoPOList
     */
    public void setOrderItemTaskInfoPOList(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        this.orderItemTaskInfoPOList = orderItemTaskInfoPOList;
    }

    /**
     * 获取
     *
     * @return batchTaskItemDTOList
     */
    public List<BatchTaskItemDTO> getBatchTaskItemDTOList() {
        return this.batchTaskItemDTOList;
    }

    /**
     * 设置
     *
     * @param batchTaskItemDTOList
     */
    public void setBatchTaskItemDTOList(List<BatchTaskItemDTO> batchTaskItemDTOList) {
        this.batchTaskItemDTOList = batchTaskItemDTOList;
    }

    /**
     * 获取
     *
     * @return lstNoLocationOrder
     */
    public List<OutStockOrderPO> getLstNoLocationOrder() {
        return this.lstNoLocationOrder;
    }

    /**
     * 设置
     *
     * @param lstNoLocationOrder
     */
    public void setLstNoLocationOrder(List<OutStockOrderPO> lstNoLocationOrder) {
        this.lstNoLocationOrder = lstNoLocationOrder;
    }

    public static CreateBatchTaskByOrderResultHelperBO
        getBOByBOList(List<CreateBatchTaskByOrderResultHelperBO> helperBOList) {
        List<BatchTaskPO> batchTaskPOList =
            helperBOList.stream().flatMap(bo -> bo.getBatchTaskPOList().stream()).collect(Collectors.toList());
        List<BatchTaskItemDTO> batchTaskItemDTOList =
            helperBOList.stream().flatMap(bo -> bo.getBatchTaskItemDTOList().stream()).collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            helperBOList.stream().flatMap(bo -> bo.getOrderItemTaskInfoPOList().stream()).collect(Collectors.toList());
        List<OutStockOrderPO> lstNoLocationOrder =
            helperBOList.stream().filter(m -> CollectionUtils.isNotEmpty(m.getLstNoLocationOrder()))
                .flatMap(bo -> bo.getLstNoLocationOrder().stream()).collect(Collectors.toList());

        CreateBatchTaskByOrderResultHelperBO createBatchTaskByOrderResultHelperBO =
            new CreateBatchTaskByOrderResultHelperBO();
        createBatchTaskByOrderResultHelperBO.setBatchTaskPOList(batchTaskPOList);
        createBatchTaskByOrderResultHelperBO.setLstNoLocationOrder(lstNoLocationOrder);
        createBatchTaskByOrderResultHelperBO.setBatchTaskItemDTOList(batchTaskItemDTOList);
        createBatchTaskByOrderResultHelperBO.setOrderItemTaskInfoPOList(orderItemTaskInfoPOList);

        return createBatchTaskByOrderResultHelperBO;
    }

    public void resetBatchListInfo(List<BatchTaskPO> lstBatchTask, List<BatchTaskItemDTO> lstBatchTaskItem,
        List<OutStockOrderPO> lstNoLocationOrder, List<OrderItemTaskInfoPO> lstOrderItemTask) {
        if (!org.springframework.util.CollectionUtils.isEmpty(getBatchTaskPOList())) {
            lstBatchTask.addAll(getBatchTaskPOList());
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(getBatchTaskItemDTOList())) {
            lstBatchTaskItem.addAll(getBatchTaskItemDTOList());
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(getOrderItemTaskInfoPOList())) {
            lstOrderItemTask.addAll(getOrderItemTaskInfoPOList());
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(getLstNoLocationOrder())) {
            lstNoLocationOrder.addAll(getLstNoLocationOrder());
        }
    }

}
