package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo;

import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
public class BatchTaskItemFinishBO {

    private List<BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOS;
    private String batchTaskId;
    private String userName;
    private Integer warehouseId;
    private Long locationId;
    private String locationName;
    private Integer cityId;
    private Integer userId;
    /**
     * 开启容器位拣货
     * 
     * @see ConditionStateEnum
     */
    private Byte containerFlag;

    /**
     * 获取
     *
     * @return batchTaskId
     */
    public String getBatchTaskId() {
        return this.batchTaskId;
    }

    /**
     * 设置
     *
     * @param batchTaskId
     */
    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    /**
     * 获取
     *
     * @return userName
     */
    public String getUserName() {
        return this.userName;
    }

    /**
     * 设置
     *
     * @param userName
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * 获取
     *
     * @return warehouseId
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置
     *
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取
     *
     * @return locationId
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置
     *
     * @param locationId
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取
     *
     * @return locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置
     *
     * @param locationName
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取
     *
     * @return cityId
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置
     *
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取
     *
     * @return userId
     */
    public Integer getUserId() {
        return this.userId;
    }

    /**
     * 设置
     *
     * @param userId
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取
     *
     * @return containerFlag
     */
    public Byte getContainerFlag() {
        return this.containerFlag;
    }

    /**
     * 设置
     *
     * @param containerFlag
     */
    public void setContainerFlag(Byte containerFlag) {
        this.containerFlag = containerFlag;
    }

    /**
     * 获取
     *
     * @return batchTaskItemCompleteDTOS
     */
    public List<BatchTaskItemCompleteDTO> getBatchTaskItemCompleteDTOS() {
        return this.batchTaskItemCompleteDTOS;
    }

    /**
     * 设置
     *
     * @param batchTaskItemCompleteDTOS
     */
    public void setBatchTaskItemCompleteDTOS(List<BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOS) {
        this.batchTaskItemCompleteDTOS = batchTaskItemCompleteDTOS;
    }

    public static final class BatchTaskItemFinishBOBuilder {
        private List<BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOS;
        private String batchTaskId;
        private String userName;
        private Integer warehouseId;
        private Long locationId;
        private String locationName;
        private Integer cityId;
        private Integer userId;
        private Byte containerFlag;

        private BatchTaskItemFinishBOBuilder() {}

        public static BatchTaskItemFinishBOBuilder aBatchTaskItemFinishBO() {
            return new BatchTaskItemFinishBOBuilder();
        }

        public BatchTaskItemFinishBOBuilder
            withBatchTaskItemCompleteDTOS(List<BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOS) {
            this.batchTaskItemCompleteDTOS = batchTaskItemCompleteDTOS;
            return this;
        }

        public BatchTaskItemFinishBOBuilder withBatchTaskId(String batchTaskId) {
            this.batchTaskId = batchTaskId;
            return this;
        }

        public BatchTaskItemFinishBOBuilder withUserName(String userName) {
            this.userName = userName;
            return this;
        }

        public BatchTaskItemFinishBOBuilder withWarehouseId(Integer warehouseId) {
            this.warehouseId = warehouseId;
            return this;
        }

        public BatchTaskItemFinishBOBuilder withLocationId(Long locationId) {
            this.locationId = locationId;
            return this;
        }

        public BatchTaskItemFinishBOBuilder withLocationName(String locationName) {
            this.locationName = locationName;
            return this;
        }

        public BatchTaskItemFinishBOBuilder withCityId(Integer cityId) {
            this.cityId = cityId;
            return this;
        }

        public BatchTaskItemFinishBOBuilder withUserId(Integer userId) {
            this.userId = userId;
            return this;
        }

        public BatchTaskItemFinishBOBuilder withContainerFlag(Byte containerFlag) {
            this.containerFlag = containerFlag;
            return this;
        }

        public BatchTaskItemFinishBO build() {
            BatchTaskItemFinishBO batchTaskItemFinishBO = new BatchTaskItemFinishBO();
            batchTaskItemFinishBO.setBatchTaskItemCompleteDTOS(batchTaskItemCompleteDTOS);
            batchTaskItemFinishBO.setBatchTaskId(batchTaskId);
            batchTaskItemFinishBO.setUserName(userName);
            batchTaskItemFinishBO.setWarehouseId(warehouseId);
            batchTaskItemFinishBO.setLocationId(locationId);
            batchTaskItemFinishBO.setLocationName(locationName);
            batchTaskItemFinishBO.setCityId(cityId);
            batchTaskItemFinishBO.setUserId(userId);
            batchTaskItemFinishBO.setContainerFlag(containerFlag);
            return batchTaskItemFinishBO;
        }
    }
}
