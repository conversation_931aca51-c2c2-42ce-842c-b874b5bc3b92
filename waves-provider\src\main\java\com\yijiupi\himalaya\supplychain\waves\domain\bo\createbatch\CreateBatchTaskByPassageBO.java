package com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch;

import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 通过通道创建拣货任务的入参BO
 * 
 * <AUTHOR>
 * @date 2024/11/25
 */
public class CreateBatchTaskByPassageBO {

    private BatchBO batchBO;
    private List<OutStockOrderPO> orders;
    private java.util.List<OutStockOrderItemPO> lstOrderItems;
    private WavesStrategyBO wavesStrategyDTO;
    private Integer cityId;
    private String title;
    private String operateUser;
    private String locationName;
    private String driverName;
    private Boolean allocationFlag;
    private WarehouseConfigDTO warehouseConfigDTO;
    private WaveCreateDTO oriCreateDTO;
    private SowTaskPO existSowTaskPO;

    /**
     * 获取
     *
     * @return batchBO
     */
    public BatchBO getBatchBO() {
        return this.batchBO;
    }

    /**
     * 设置
     *
     * @param batchBO
     */
    public void setBatchBO(BatchBO batchBO) {
        this.batchBO = batchBO;
    }

    /**
     * 获取
     *
     * @return orders
     */
    public List<OutStockOrderPO> getOrders() {
        return this.orders;
    }

    /**
     * 设置
     *
     * @param orders
     */
    public void setOrders(List<OutStockOrderPO> orders) {
        this.orders = orders;
    }

    /**
     * 获取
     *
     * @return lstOrderItems
     */
    public List<OutStockOrderItemPO> getLstOrderItems() {
        return this.lstOrderItems;
    }

    /**
     * 设置
     *
     * @param lstOrderItems
     */
    public void setLstOrderItems(List<OutStockOrderItemPO> lstOrderItems) {
        this.lstOrderItems = lstOrderItems;
    }

    /**
     * 获取
     *
     * @return wavesStrategyDTO
     */
    public WavesStrategyBO getWavesStrategyDTO() {
        return this.wavesStrategyDTO;
    }

    /**
     * 设置
     *
     * @param wavesStrategyDTO
     */
    public void setWavesStrategyDTO(WavesStrategyBO wavesStrategyDTO) {
        this.wavesStrategyDTO = wavesStrategyDTO;
    }

    /**
     * 获取
     *
     * @return cityId
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置
     *
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取
     *
     * @return title
     */
    public String getTitle() {
        return this.title;
    }

    /**
     * 设置
     *
     * @param title
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 获取
     *
     * @return operateUser
     */
    public String getOperateUser() {
        return this.operateUser;
    }

    /**
     * 设置
     *
     * @param operateUser
     */
    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    /**
     * 获取
     *
     * @return locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置
     *
     * @param locationName
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取
     *
     * @return driverName
     */
    public String getDriverName() {
        return this.driverName;
    }

    /**
     * 设置
     *
     * @param driverName
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    /**
     * 获取
     *
     * @return allocationFlag
     */
    public Boolean getAllocationFlag() {
        return this.allocationFlag;
    }

    /**
     * 设置
     *
     * @param allocationFlag
     */
    public void setAllocationFlag(Boolean allocationFlag) {
        this.allocationFlag = allocationFlag;
    }

    /**
     * 获取
     *
     * @return warehouseConfigDTO
     */
    public WarehouseConfigDTO getWarehouseConfigDTO() {
        return this.warehouseConfigDTO;
    }

    /**
     * 设置
     *
     * @param warehouseConfigDTO
     */
    public void setWarehouseConfigDTO(WarehouseConfigDTO warehouseConfigDTO) {
        this.warehouseConfigDTO = warehouseConfigDTO;
    }

    /**
     * 获取
     *
     * @return oriCreateDTO
     */
    public WaveCreateDTO getOriCreateDTO() {
        return this.oriCreateDTO;
    }

    /**
     * 设置
     *
     * @param oriCreateDTO
     */
    public void setOriCreateDTO(WaveCreateDTO oriCreateDTO) {
        this.oriCreateDTO = oriCreateDTO;
    }

    /**
     * 获取
     *
     * @return existSowTaskPO
     */
    public SowTaskPO getExistSowTaskPO() {
        return this.existSowTaskPO;
    }

    /**
     * 设置
     *
     * @param existSowTaskPO
     */
    public void setExistSowTaskPO(SowTaskPO existSowTaskPO) {
        this.existSowTaskPO = existSowTaskPO;
    }

    public static final class CreateBatchTaskByPassageBOBuilder {
        private BatchBO batchBO;
        private List<OutStockOrderPO> orders;
        private List<OutStockOrderItemPO> lstOrderItems;
        private WavesStrategyBO wavesStrategyDTO;
        private Integer cityId;
        private String title;
        private String operateUser;
        private String locationName;
        private String driverName;
        private Boolean allocationFlag;
        private WarehouseConfigDTO warehouseConfigDTO;
        private WaveCreateDTO oriCreateDTO;
        private SowTaskPO existSowTaskPO;

        private CreateBatchTaskByPassageBOBuilder() {}

        public static CreateBatchTaskByPassageBOBuilder aCreateBatchTaskByPassageBO() {
            return new CreateBatchTaskByPassageBOBuilder();
        }

        public CreateBatchTaskByPassageBOBuilder withBatchBO(BatchBO batchBO) {
            this.batchBO = batchBO;
            return this;
        }

        public CreateBatchTaskByPassageBOBuilder withOrders(List<OutStockOrderPO> orders) {
            this.orders = orders;
            return this;
        }

        public CreateBatchTaskByPassageBOBuilder withLstOrderItems(List<OutStockOrderItemPO> lstOrderItems) {
            this.lstOrderItems = lstOrderItems;
            return this;
        }

        public CreateBatchTaskByPassageBOBuilder withWavesStrategyDTO(WavesStrategyBO wavesStrategyDTO) {
            this.wavesStrategyDTO = wavesStrategyDTO;
            return this;
        }

        public CreateBatchTaskByPassageBOBuilder withCityId(Integer cityId) {
            this.cityId = cityId;
            return this;
        }

        public CreateBatchTaskByPassageBOBuilder withTitle(String title) {
            this.title = title;
            return this;
        }

        public CreateBatchTaskByPassageBOBuilder withOperateUser(String operateUser) {
            this.operateUser = operateUser;
            return this;
        }

        public CreateBatchTaskByPassageBOBuilder withLocationName(String locationName) {
            this.locationName = locationName;
            return this;
        }

        public CreateBatchTaskByPassageBOBuilder withDriverName(String driverName) {
            this.driverName = driverName;
            return this;
        }

        public CreateBatchTaskByPassageBOBuilder withAllocationFlag(Boolean allocationFlag) {
            this.allocationFlag = allocationFlag;
            return this;
        }

        public CreateBatchTaskByPassageBOBuilder withWarehouseConfigDTO(WarehouseConfigDTO warehouseConfigDTO) {
            this.warehouseConfigDTO = warehouseConfigDTO;
            return this;
        }

        public CreateBatchTaskByPassageBOBuilder withOriCreateDTO(WaveCreateDTO oriCreateDTO) {
            this.oriCreateDTO = oriCreateDTO;
            return this;
        }

        public CreateBatchTaskByPassageBOBuilder withExistSowTaskPO(SowTaskPO existSowTaskPO) {
            this.existSowTaskPO = existSowTaskPO;
            return this;
        }

        public CreateBatchTaskByPassageBO build() {
            CreateBatchTaskByPassageBO createBatchTaskByPassageBO = new CreateBatchTaskByPassageBO();
            createBatchTaskByPassageBO.setBatchBO(batchBO);
            createBatchTaskByPassageBO.setOrders(orders);
            createBatchTaskByPassageBO.setLstOrderItems(lstOrderItems);
            createBatchTaskByPassageBO.setWavesStrategyDTO(wavesStrategyDTO);
            createBatchTaskByPassageBO.setCityId(cityId);
            createBatchTaskByPassageBO.setTitle(title);
            createBatchTaskByPassageBO.setOperateUser(operateUser);
            createBatchTaskByPassageBO.setLocationName(locationName);
            createBatchTaskByPassageBO.setDriverName(driverName);
            createBatchTaskByPassageBO.setAllocationFlag(allocationFlag);
            createBatchTaskByPassageBO.setWarehouseConfigDTO(warehouseConfigDTO);
            createBatchTaskByPassageBO.setOriCreateDTO(oriCreateDTO);
            createBatchTaskByPassageBO.setExistSowTaskPO(existSowTaskPO);
            return createBatchTaskByPassageBO;
        }
    }
}
