package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.constants.WarehouseConfigConstants;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchTaskItemByLocationInfoQueryBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickLocationModToBatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickLocationModToBatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/7/2
 */
@Service
public class BatchTaskLocationModBL {

    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private ILocationService iLocationService;

    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private GlobalCache globalCache;
    @Autowired
    private BatchTaskMapper batchTaskMapper;

    private static final int MAX_SIZE = 150;
    protected static final Logger LOGGER = LoggerFactory.getLogger(BatchTaskLocationModBL.class);

    @Transactional(rollbackFor = Exception.class)
    public void batchTaskLocationMod(PickLocationModToBatchTaskDTO dto) {
        LOGGER.info("修改拣货任务货位入参为:{}", JSON.toJSONString(dto));
        if (CollectionUtils.isEmpty(dto.getLocationInfoList())) {
            return;
        }

        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(dto.getWarehouseId());
        if (!is2p5plusOr2p5(warehouseConfigDTO)) {
            return;
        }

        List<String> locationIds =
            dto.getLocationInfoList().stream().map(PickLocationModToBatchTaskItemDTO::getFromLocationId)
                .filter(Objects::nonNull).distinct().map(String::valueOf).collect(Collectors.toList());
        List<String> skuIds = dto.getLocationInfoList().stream().map(PickLocationModToBatchTaskItemDTO::getSkuId)
            .map(String::valueOf).distinct().collect(Collectors.toList());

        BatchTaskItemByLocationInfoQueryBO queryBO = new BatchTaskItemByLocationInfoQueryBO();
        queryBO.setWarehouseId(dto.getWarehouseId());
        queryBO.setProductSkuIds(skuIds);
        queryBO.setLocationIds(locationIds);
        queryBO.setOrgId(warehouseConfigDTO.getOrg_Id());
        queryBO.setTaskStateList(Collections.singletonList(TaskStateEnum.未分拣.getType()));

        List<BatchTaskItemPO> batchTaskItemPOList = batchTaskItemMapper.findBatchTaskItemByLocationInfo(queryBO);
        LOGGER.info("修改拣货任务货位,查询拣货任务明细为:{}", JSON.toJSONString(batchTaskItemPOList));
        if (CollectionUtils.isEmpty(batchTaskItemPOList)) {
            return;
        }

        Map<Long, String> toLocationMap = getToLocationName(dto);
        // 这里应该过滤batchTaskItemPOList
        handleFromLocationIsNull(batchTaskItemPOList, dto, toLocationMap);
        handleFromLocationIsNotNull(batchTaskItemPOList, dto, toLocationMap);
    }

    private void handleFromLocationIsNull(List<BatchTaskItemPO> batchTaskItemPOList, PickLocationModToBatchTaskDTO dto,
        Map<Long, String> toLocationMap) {
        List<PickLocationModToBatchTaskItemDTO> nullLocationInfoList = dto.getLocationInfoList().stream()
            .filter(m -> Objects.isNull(m.getFromLocationId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nullLocationInfoList)) {
            return;
        }

        Map<Long, List<BatchTaskItemPO>> taskItemGroupMap =
            batchTaskItemPOList.stream().collect(Collectors.groupingBy(BatchTaskItemPO::getSkuId));

        Map<Long, PickLocationModToBatchTaskItemDTO> locationMap = dto.getLocationInfoList().stream()
            .collect(Collectors.toMap(PickLocationModToBatchTaskItemDTO::getSkuId, k -> k, (v1, v2) -> v1));
        List<BatchTaskItemPO> updateBatchTaskItemList = new ArrayList<>();
        for (Map.Entry<Long, List<BatchTaskItemPO>> entry : taskItemGroupMap.entrySet()) {
            List<BatchTaskItemPO> itemList = entry.getValue();
            PickLocationModToBatchTaskItemDTO locationInfo = locationMap.get(entry.getKey());
            if (Objects.isNull(locationInfo)) {
                continue;
            }

            updateBatchTaskItemList
                .addAll(getUpdateBatchTaskItemPO(locationInfo, itemList, toLocationMap, dto.getOptUserId()));
        }

        updateBatchTaskItemList(updateBatchTaskItemList);
    }

    private List<BatchTaskItemPO> getUpdateBatchTaskItemPO(PickLocationModToBatchTaskItemDTO locationInfo,
        List<BatchTaskItemPO> itemList, Map<Long, String> toLocationMap, Integer optUserId) {
        List<BatchTaskItemPO> updateBatchTaskItemList = new ArrayList<>();
        itemList.forEach(m -> {
            BatchTaskItemPO updateItem = new BatchTaskItemPO();
            updateItem.setId(m.getId());
            updateItem.setLocationId(locationInfo.getToLocationId());
            updateItem.setLastUpdateUser(getUserName(optUserId));
            updateItem.setLocationName(toLocationMap.get(locationInfo.getToLocationId()));

            updateBatchTaskItemList.add(updateItem);
        });

        return updateBatchTaskItemList;
    }

    private void handleFromLocationIsNotNull(List<BatchTaskItemPO> batchTaskItemPOList,
        PickLocationModToBatchTaskDTO dto, Map<Long, String> toLocationMap) {
        List<PickLocationModToBatchTaskItemDTO> locationInfoList = dto.getLocationInfoList().stream()
            .filter(m -> Objects.nonNull(m.getFromLocationId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationInfoList)) {
            return;
        }

        Map<String, List<BatchTaskItemPO>> taskItemGroupMap =
            batchTaskItemPOList.stream().collect(Collectors.groupingBy(k -> getKey(k.getLocationId(), k.getSkuId())));

        Map<String, PickLocationModToBatchTaskItemDTO> locationMap = dto.getLocationInfoList().stream()
            .collect(Collectors.toMap(k -> getKey(k.getFromLocationId(), k.getSkuId()), k -> k, (v1, v2) -> v1));

        List<BatchTaskItemPO> updateBatchTaskItemList = new ArrayList<>();
        for (Map.Entry<String, List<BatchTaskItemPO>> entry : taskItemGroupMap.entrySet()) {
            List<BatchTaskItemPO> itemList = entry.getValue();
            PickLocationModToBatchTaskItemDTO locationInfo = locationMap.get(entry.getKey());

            if (Objects.isNull(locationInfo)) {
                continue;
            }

            if (Objects.isNull(locationInfo.getToLocationId())) {
                continue;
            }

            updateBatchTaskItemList
                .addAll(getUpdateBatchTaskItemPO(locationInfo, itemList, toLocationMap, dto.getOptUserId()));
        }

        updateBatchTaskItemList(updateBatchTaskItemList);
    }

    private void updateBatchTaskItemList(List<BatchTaskItemPO> updateBatchTaskItemList) {
        if (CollectionUtils.isEmpty(updateBatchTaskItemList)) {
            return;
        }

        LOGGER.info("更新拣货任务项为:{}", JSON.toJSONString(updateBatchTaskItemList));

        Lists.partition(updateBatchTaskItemList, MAX_SIZE).forEach(itemList -> {
            batchTaskItemMapper.batchUpdateBatchTaskItem(itemList);
        });
    }

    private Map<Long, String> getToLocationName(PickLocationModToBatchTaskDTO dto) {
        List<Long> toLocationIds = dto.getLocationInfoList().stream()
            .map(PickLocationModToBatchTaskItemDTO::getToLocationId).distinct().collect(Collectors.toList());
        List<LoactionDTO> loactionDTOList = iLocationService.findLocationByIds(toLocationIds);

        if (CollectionUtils.isEmpty(loactionDTOList)) {
            return Collections.emptyMap();
        }

        return loactionDTOList.stream().collect(Collectors.toMap(LoactionDTO::getId, LoactionDTO::getName));
    }

    private String getUserName(Integer optUserId) {
        if (Objects.isNull(optUserId)) {
            return null;
        }
        String userName = globalCache.getAdminTrueName(optUserId);
        if (StringUtils.isBlank(userName)) {
            return null;
        }

        return userName;
    }

    private String getKey(Long locationId, long skuId) {
        if (Objects.isNull(locationId)) {
            return String.format("%s-%s", "0", skuId);
        }
        return String.format("%s-%s", locationId, skuId);
    }

    /**
     * 是否是2.5 或者 2.5+
     * 
     * @param warehouseConfigDTO
     * @return
     */
    private boolean is2p5plusOr2p5(WarehouseConfigDTO warehouseConfigDTO) {
        if (warehouseConfigDTO.isOpen2p5plus()) {
            return Boolean.TRUE;
        }
        if (WarehouseConfigConstants.SCM_VERSION_2_5.equals(warehouseConfigDTO.getScmVersion())
            && WarehouseConfigConstants.CARGO_STOCK_OFF.equals(warehouseConfigDTO.getIsOpenCargoStock())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    public void updateBatchTask(BatchTaskDTO updateBatchTask) {
        if (CollectionUtils.isEmpty(updateBatchTask.getBatchTaskIds())) {
            return;
        }
        updateBatchTask.getBatchTaskIds().forEach(id -> {
            BatchTaskPO batchTaskPO = new BatchTaskPO();
            batchTaskPO.setId(id);
            batchTaskPO.setTaskState(updateBatchTask.getTaskState());
            batchTaskPO.setToLocationId(updateBatchTask.getToLocationId());
            batchTaskPO.setToLocationName(updateBatchTask.getToLocationName());
            batchTaskMapper.updateByPrimaryKeySelective(batchTaskPO);
        });

    }

}
