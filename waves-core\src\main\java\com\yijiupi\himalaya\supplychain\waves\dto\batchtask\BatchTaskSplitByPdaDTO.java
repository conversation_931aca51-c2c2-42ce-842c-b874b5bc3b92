package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * PDA拆分拣货任务（PDA）
 * 
 * <AUTHOR>
 * @date 2018/7/9 16:08
 */
public class BatchTaskSplitByPdaDTO implements Serializable {
    private static final long serialVersionUID = -351257301196844622L;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 拣货任务编号
     */
    private String batchTaskNo;

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 操作人Id
     */
    private Integer operateUserId;

    /**
     * 拣货员id
     */
    private Integer sorterId;

    /**
     * 拣货员
     */
    private String sorter;

    /**
     * 拣货任务详情id集合
     */
    private List<String> batchTaskItemIds;

    /**
     * 备货区id
     */
    private Long locationId;

    /**
     * 备货区名称
     */
    private String locationName;

    public Integer getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(Integer operateUserId) {
        this.operateUserId = operateUserId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public List<String> getBatchTaskItemIds() {
        return batchTaskItemIds;
    }

    public void setBatchTaskItemIds(List<String> batchTaskItemIds) {
        this.batchTaskItemIds = batchTaskItemIds;
    }

    public Integer getSorterId() {
        return sorterId;
    }

    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    public String getSorter() {
        return sorter;
    }

    public void setSorter(String sorter) {
        this.sorter = sorter;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
}
