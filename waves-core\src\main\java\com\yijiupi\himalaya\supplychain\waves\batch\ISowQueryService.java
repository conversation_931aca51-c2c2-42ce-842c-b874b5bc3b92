package com.yijiupi.himalaya.supplychain.waves.batch;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.*;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.*;

import java.util.Collections;
import java.util.List;

public interface ISowQueryService {

    /**
     * 查询播种任务列表
     */
    PageList<SowTaskDTO> findSowTaskList(SowTaskQueryDTO sowTaskQueryDTO);

    /**
     * 根据播种任务编号查询播种任务中的订单信息
     */
    List<SowOrderInfoDTO> listOutStockOrderBySowTaskNo(String sowTaskNo, Integer orgId);

    /**
     * 根据播种单号，查询播种信息
     */
    List<SowTaskDTO> findSowTaskByTaskNos(Integer orgId, List<String> sowTaskNos);

    /**
     * 条件查询打包订单列表
     */
    PageList<UnpackOrderDTO> findUnpackOrderList(UnpackOrderQueryDTO unpackOrderQueryDTO);

    /**
     * 播种任务打印
     */
    List<SowingPrintInfoDTO> listSowingPrintInfo(List<String> sowTaskNos, Integer orgId);

    /**
     * 根据播种任务、容器编码查询产品信息
     */
    List<SowProductItemDTO> listSowProductItem(SowProductItemQueryDTO sowProductItemQueryDTO);

    /**
     * 查询播种任务自提点信息
     */
    List<SowAddressDTO> listSowAddress(SowAddressQueryDTO sowAddressQueryDTO);

    /**
     * 根据播种任务查询订单包装信息
     */
    List<SowProductItemDTO> listSowPackageProductItem(SowProductItemQueryDTO sowProductItemQueryDTO);

    /**
     * 分页查询播种订单明细数据
     */
    PageList<SowOrderItemDTO> pageListSowOrderItems(SowOrderItemQueryDTO sowOrderItemQueryDTO);

    /**
     * 分页查询自提点播种明细数据
     */
    PageList<AddressSowTaskItemDTO> pageListAddressSowTaskItems(AddressSowTaskQueryDTO addressSowTaskQueryDTO);

    /**
     * 查询播种任务项
     */
    List<SowTaskItemDTO> listSowOTaskItems(SowTaskItemQueryDTO queryDTO);

    /**
     * 播种任务的打包明细
     */
    List<SowPackageItemDTO> findSowPackageItems(SowPackageItemQueryDTO queryDTO);

    /**
     * 查询播种任务项
     */
    List<SowTaskItemDTO> findByOrderItemIds(List<Long> orderItemIds);

    /**
     * 查询状态为待播种和播种中的货位id
     */
    List<Long> findByLocationIds(Integer orgId, Integer warehouseId, List<Long> locationIds);

    /**
     * 根据播种任务编号查询播种任务中的订单信息
     */
    List<SowOrderInfoDTO> listOutStockOrderDetails(SowTaskQueryDTO sowTaskQueryDTO);

    /**
     * 根据 skuIds 和 states 查询播种任务
     *
     * @param queryDTO 查询条件, 只有 skuId 和 states 有用
     * @return 查询到的结果, 如果查不到就返回不可变的空集合 {@link Collections#emptyList()}
     */
    List<SowTaskLocationDTO> findSowTaskBySkuIds(SowTaskQueryDTO queryDTO);

    /**
     * 通过播种任务 id 查询缓存的物料箱号
     *
     * @param sowTaskId   播种任务 id
     * @param warehouseId 仓库 id
     */
    List<String> listWcsBoxNosBySowTaskId(Integer warehouseId, Long sowTaskId);

    /**
     * 分页查询可追加的播种任务
     *
     * @param query 查询条件
     * @return 查询结果
     */
    PageList<SowTaskDTO> pageListAppendableSowTask(SowTaskQueryDTO query);
}
