package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.*;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.domain.model.tms.CreateWaveNotifyTmsDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.yijiupi.himalaya.supplychain.waves.domain.model.tms.SyncTmsOutLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.tms.SyncTmsOutLocationOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/13
 */
public class SyncTmsOutLocationDTOConvertor {
    private static final Logger LOG = LoggerFactory.getLogger(SyncTmsOutLocationDTOConvertor.class);


    public static SyncTmsOutLocationDTO convert(Integer warehouseId, Integer optUserId, List<OutStockOrderPO> outStockOrderPOList,
                                                Map<String, BatchTaskPO> batchTaskMap, List<OrderItemTaskInfoPO> orderItemTaskInfoList) {
        SyncTmsOutLocationDTO syncTmsOutLocationDTO = new SyncTmsOutLocationDTO();


        Map<Long, String> orderBatchTaskIdMap = orderItemTaskInfoList.stream().collect(Collectors.toMap(OrderItemTaskInfoPO :: getRefOrderId, OrderItemTaskInfoPO :: getBatchTaskId, (v1,v2) -> v1));

        List<SyncTmsOutLocationOrderDTO> itemSyncParamList = outStockOrderPOList.stream()
                .map(order -> {
                    String batchTaskId = orderBatchTaskIdMap.get(order.getId());
                    BatchTaskPO batchTaskPO = batchTaskMap.get(batchTaskId);
                    if (Objects.isNull(batchTaskPO)) {
                        LOG.info("未找到拣货任务，拣货任务编号:{}, 订单信息:{}", batchTaskId, order.getReforderno());
                        return null;
                    }
                    SyncTmsOutLocationOrderDTO locationOrderDTO = new SyncTmsOutLocationOrderDTO();
                    locationOrderDTO.setDefaultLocationId(batchTaskPO.getToLocationId());
                    locationOrderDTO.setDefaultLocationName(batchTaskPO.getToLocationName());
                    locationOrderDTO.setOrderId(order.getBusinessId());
                    return locationOrderDTO;
                }).collect(Collectors.toList());
        itemSyncParamList = itemSyncParamList.stream().filter(Objects :: nonNull).filter(m -> Objects.nonNull(m.getDefaultLocationId())).collect(Collectors.toList());

        syncTmsOutLocationDTO.setItemSyncParamList(itemSyncParamList);
        syncTmsOutLocationDTO.setWarehouseId(warehouseId);
        syncTmsOutLocationDTO.setOptUserId(optUserId);

        return syncTmsOutLocationDTO;
    }

    public static List<CreateWaveNotifyTmsDTO> convertToCreateWaveNotifyTmsDTOList(List<OutStockOrderPO> orderList, BatchPO batchPO, Integer optUserId) {
        Map<Integer, List<OutStockOrderPO>> warehouseGroupOrderList = orderList.stream().collect(Collectors.groupingBy(OutStockOrderPO :: getWarehouseId));

        Date currentDate = new Date();
        List<CreateWaveNotifyTmsDTO> totalList = new ArrayList<>();
        for (Map.Entry<Integer, List<OutStockOrderPO>> entry : warehouseGroupOrderList.entrySet()) {
            List<String> businessIds = entry.getValue().stream().map(OutStockOrderPO :: getBusinessId).distinct().collect(Collectors.toList());

            List<CreateWaveNotifyTmsDTO> tmpDtoList = businessIds.stream().map(businessId -> {
                CreateWaveNotifyTmsDTO createWaveNotifyTmsDTO = new CreateWaveNotifyTmsDTO();
                createWaveNotifyTmsDTO.setWaveNo(batchPO.getBatchNo());
                createWaveNotifyTmsDTO.setOrderId(Long.valueOf(businessId));
                createWaveNotifyTmsDTO.setOptUserId(optUserId.toString());
                createWaveNotifyTmsDTO.setWarehouseId(batchPO.getWarehouseId());
                createWaveNotifyTmsDTO.setCreateWaveTime(currentDate);
                return createWaveNotifyTmsDTO;
            }).collect(Collectors.toList());

            totalList.addAll(tmpDtoList);
        }

        return totalList;
    }

}
