package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;
import java.util.List;

/**
 * 修改播种任务下拣货任务的周转区
 *
 * <AUTHOR>
 * @date 2018/11/6 15:11
 */
public class UpdateBatchTaskLocaitonDTO implements Serializable {
    private static final long serialVersionUID = 3794221323697333845L;

    /**
     * 播种任务编号
     */
    private List<String> SowTaskNo;

    /**
     * 周转区id
     */
    private Long locationId;

    /**
     * 周转区名称
     */
    private String locationName;

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 城市id
     */
    private Integer orgId;

    public List<String> getSowTaskNo() {
        return SowTaskNo;
    }

    public void setSowTaskNo(List<String> sowTaskNo) {
        SowTaskNo = sowTaskNo;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }
}
