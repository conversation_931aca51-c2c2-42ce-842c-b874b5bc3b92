package com.yijiupi.himalaya.supplychain.waves.dto.soworder;

import java.util.List;

import com.yijiupi.himalaya.base.search.PageCondition;

public class SowOrderItemQueryDTO extends PageCondition {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务项id列表
     */
    private List<Long> sowTaskItemIds;
    /**
     * 播种任务项id
     */
    private Long sowTaskItemId;
    /**
     * 订单号
     */
    private String orderNo;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public List<Long> getSowTaskItemIds() {
        return sowTaskItemIds;
    }

    public void setSowTaskItemIds(List<Long> sowTaskItemIds) {
        this.sowTaskItemIds = sowTaskItemIds;
    }

    public Long getSowTaskItemId() {
        return sowTaskItemId;
    }

    public void setSowTaskItemId(Long sowTaskItemId) {
        this.sowTaskItemId = sowTaskItemId;
    }

    /**
     * 获取 订单号
     *
     * @return orderNo 订单号
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置 订单号
     *
     * @param orderNo 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
