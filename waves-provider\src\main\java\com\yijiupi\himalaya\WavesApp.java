/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

import com.yijiupi.himalaya.common.util.resourcelock.annotation.EnableResourceLock;
import com.yijiupi.himalaya.postcommit.TaskPoolConfiguration;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RestTemplate;

import com.yijiupi.himalaya.distributedlock.annotation.EnableDistributedLock;
import com.yijiupi.himalaya.distributedlock.enums.DistributedLockType;

/**
 * <AUTHOR>
 * @since 2017年10月16日 下午3:34:07
 */
@EnableAsync
@EnableResourceLock
@EnableDistributedLock(lockType = DistributedLockType.Redis)
@SpringBootApplication(scanBasePackages = "com.yijiupi.himalaya")
@MapperScan(basePackages = "com.yijiupi.himalaya.supplychain.waves.domain.dao")
public class WavesApp {
    public static void main(String[] args) throws Exception {
        SpringApplication app = new SpringApplication(WavesApp.class);
        // app.setWebEnvironment(false);// 不启动WEB 环境
        app.run(args);
        CountDownLatch latch = new CountDownLatch(1);
        latch.await();
    }

    @Bean(name = "waveTaskExecutor")
    public Executor outStockOrderSyncTaskExecutor() {
        int coreSize = Runtime.getRuntime().availableProcessors();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreSize);
        executor.setMaxPoolSize(coreSize + 20);
        executor.setQueueCapacity(1000);

        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("waveTaskExecutor-");
        executor.setTaskDecorator(TaskPoolConfiguration::decorateTask);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        return executor;
    }

    @Bean
    public RestTemplate restTemplate() {
        RestTemplate template = new RestTemplate();
        template.getMessageConverters().stream().filter(it -> it instanceof StringHttpMessageConverter)
            .map(it -> (StringHttpMessageConverter)it).findFirst()
            .ifPresent(it -> it.setDefaultCharset(StandardCharsets.UTF_8));
        return template;
    }
}
