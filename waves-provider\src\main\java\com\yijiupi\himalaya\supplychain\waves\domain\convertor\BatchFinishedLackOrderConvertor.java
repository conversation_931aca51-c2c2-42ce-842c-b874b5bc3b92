package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.JiupiOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OrderExtendMapEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderBusinessType;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderconstraint.OrderConstraintCheckBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.*;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemLackDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.PartSendWsmDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
@Component
public class BatchFinishedLackOrderConvertor {

    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private BatchOrderBL batchOrderBL;
    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;
    @Autowired
    private BatchFinishedLackOrderConvertor batchFinishedLackOrderConvertor;
    @Resource
    private OrderConstraintCheckBL orderConstraintCheckBL;

    private static final Logger LOG = LoggerFactory.getLogger(BatchFinishedLackOrderConvertor.class);

    /**
     * @see com.yijiupi.himalaya.supplychain.waves.domain.bl.OutStockOrderBL#processOrderLack <br/>
     *      这里就是根据orderItemTaskInfo和orderItemTaskInfoDetail的数量去组装缺货的订单<br/>
     * @param batchNo
     * @param operatorUserId
     * @return
     */
    // 调用老缺货代码马上废除了，代码结构不费力气大调整了
    @Transactional(rollbackFor = Exception.class)
    public NotifyOrderLackBO convertOrderLackByBatch(String batchNo, Integer operatorUserId) {
        List<BatchPO> batchPOList = batchMapper.listByBatchNos(Arrays.asList(batchNo));
        if (CollectionUtils.isEmpty(batchPOList)) {
            throw new BusinessException("找不到波次：" + batchNo);
        }
        BatchPO batchPO = batchPOList.get(0);

        List<BatchTaskItemLackDTO> batchTaskItemLackDTOS =
            batchOrderBL.listLackItemByBatchNos(Collections.singletonList(batchNo));
        LOG.info("拣货任务中的缺货信息为 batchTaskItemLackDTOS ={}", JSON.toJSONString(batchTaskItemLackDTOS));

        if (CollectionUtils.isEmpty(batchTaskItemLackDTOS)) {
            NotifyOrderLackBO notifyOrderLackBO = new NotifyOrderLackBO();
            fulfillLackOrderItems(notifyOrderLackBO, batchNo);
            return notifyOrderLackBO;
        }

        /** 二次分拣，播种号改为空 */
        if (batchPO.getSowType() != null && batchPO.getSowType().equals((byte)2)) {
            batchTaskItemLackDTOS.stream().filter(it -> it.getSowTaskId() != null).forEach(it -> {
                it.setSowTaskId(null);
                it.setSowTaskNo(null);
            });
        }
        NotifyOrderLackBO notifyOrderLackBO = batchFinishedLackOrderConvertor.convertOrderLackDTO(batchTaskItemLackDTOS,
            batchNo, batchPO.getWarehouseId(), operatorUserId);

        fulfillLackOrderItems(notifyOrderLackBO, batchNo);
        return notifyOrderLackBO;
    }

    private void fulfillLackOrderItems(NotifyOrderLackBO notifyOrderLackBO, String batchNo) {
        if (Objects.isNull(notifyOrderLackBO)) {
            return;
        }
        // 拣货的时候，拣货任务明细中没缺，但供应链客户端标记缺货了。这时候不会改拣货任务项的缺货信息，所以要查缺货的出库单项（数量和原始数量不一致），处理这种场景下的数据
        List<Long> outStockOrderItemIds =
            outStockOrderItemMapper.findLackItemIdByBatchNo(Collections.singletonList(batchNo));

        if (CollectionUtils.isEmpty(notifyOrderLackBO.getLackOrderItemIds())) {
            notifyOrderLackBO.setLackOrderItemIds(outStockOrderItemIds);
            return;
        }

        Set<Long> difOrderItemIds = Sets.difference(new HashSet<>(outStockOrderItemIds),
            new HashSet<>(notifyOrderLackBO.getLackOrderItemIds()));

        notifyOrderLackBO.getLackOrderItemIds().addAll(difOrderItemIds);
    }

    /**
     * 现在拣货缺货场景下，给中台的订单项并不全，比如一个订单十个订单项，只有一个订单项缺货，就只给了中台一个的。
     * 
     * @param batchTaskItemLackDTOS
     * @param batchNo
     * @param warehouseId
     * @param operatorUserId
     * @return
     */
    public NotifyOrderLackBO convertOrderLackDTO(List<BatchTaskItemLackDTO> batchTaskItemLackDTOS, String batchNo,
        Integer warehouseId, Integer operatorUserId) {
        // 波次完成的标记缺货
        List<BatchTaskItemLackDTO> filterBatchTaskItemLackDTOS = batchTaskItemLackDTOS.stream()
            .filter(p -> StringUtils.isEmpty(p.getSowTaskNo())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterBatchTaskItemLackDTOS)) {
            List<OutStockOrderPO> outStockOrderPOList = getAllLackOrderList(batchTaskItemLackDTOS, null, warehouseId);
            return NotifyOrderLackBO.getLackOrderNotifyBO(operatorUserId, warehouseId, outStockOrderPOList);
        }
        // 查询缺货的拣货任务项关联的订单项
        List<String> batchTaskItemIds = filterBatchTaskItemLackDTOS.stream()
            .map(BatchTaskItemLackDTO::getBatchTaskItemId).collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByBatchTaskItemIds(batchTaskItemIds);

        // 查不到关联时，走老的缺货逻辑,马上废弃了
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            // - 获取波次中的所有订单编号
            List<String> refOrderNos = outStockOrderMapper.listRefOrderNoByBatchNo(batchNo);
            // 转换skuId
            batchOrderTaskBL.transferSkuId(refOrderNos, filterBatchTaskItemLackDTOS);
            LOG.info(String.format("标记缺货：%s, 排除播种任务后的缺货：%s, 订单编号：%s", JSON.toJSONString(batchTaskItemLackDTOS),
                JSON.toJSONString(filterBatchTaskItemLackDTOS), JSON.toJSONString(refOrderNos)));
            NotifyOmsOldOrderLackBO notifyOmsOldOrderLackBO =
                new NotifyOmsOldOrderLackBO(filterBatchTaskItemLackDTOS, refOrderNos, batchNo, operatorUserId);
            notifyOmsOldOrderLackBO.setWarehouseId(warehouseId);
            return new NotifyOrderLackBO(operatorUserId, warehouseId, Boolean.FALSE, notifyOmsOldOrderLackBO);

        }

        // 找到所有缺货的订单项
        List<Long> orderItemIds =
            orderItemTaskInfoPOList.stream().filter(p -> p.getLackUnitCount().compareTo(BigDecimal.ZERO) > 0)
                .map(OrderItemTaskInfoPO::getRefOrderItemId).distinct().collect(Collectors.toList());

        NotifyOrderLackBO notifyOrderLackBO = new NotifyOrderLackBO();
        notifyOrderLackBO.setLackOrderItemIds(orderItemIds);
        List<OutStockOrderPO> totalLackOutStockOrderPOList =
            getAllLackOrderList(batchTaskItemLackDTOS, null, warehouseId);
        notifyOrderLackBO.setTotalLackOrderList(totalLackOutStockOrderPOList);

        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            throw new BusinessException("找不到关联的订单项");
        }
        if (CollectionUtils.isEmpty(orderItemIds)) {
            LOG.info("所有缺货的订单项为空！");
            return notifyOrderLackBO;
        }
        // 找到缺货的订单项原始数量
        List<OrderItemTaskInfoPO> originOrderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderItemIds(orderItemIds);
        if (CollectionUtils.isEmpty(originOrderItemTaskInfoPOList)) {
            LOG.info("找不到缺货的订单项原始数量：{}", JSON.toJSONString(orderItemIds));
            return notifyOrderLackBO;
        }

        List<OutStockOrderItemDTO> orderItemDTOS = outStockOrderItemMapper.findByItemIds(orderItemIds, null);
        if (CollectionUtils.isEmpty(orderItemDTOS)) {
            LOG.info("找不到缺货的订单项：{}", JSON.toJSONString(orderItemIds));
            return notifyOrderLackBO;
        }

        List<Long> outStockOrderIds = orderItemDTOS.stream().map(OutStockOrderItemDTO::getOutstockorder_Id).distinct()
            .collect(Collectors.toList());
        List<OutStockOrderPO> orderList = outStockOrderMapper.listOrderByIds(outStockOrderIds, null, warehouseId);

        Map<Long, List<OrderItemTaskInfoPO>> originOrderItemTaskInfoGroupMap = originOrderItemTaskInfoPOList.stream()
            .collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId));
        Map<Long, List<OrderItemTaskInfoPO>> lackOrderItemTaskInfoGroupMap =
            orderItemTaskInfoPOList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId));

        // key businessId,缺货信息描述
        Map<String, StringBuffer> orderLackInfoDesMap = new HashMap<>();

        NotifyOrderLackHelperBO notifyOrderLackHelperBO = new NotifyOrderLackHelperBO();
        notifyOrderLackHelperBO.setOriginOrderItemTaskInfoGroupMap(originOrderItemTaskInfoGroupMap);
        notifyOrderLackHelperBO.setLackOrderItemTaskInfoGroupMap(lackOrderItemTaskInfoGroupMap);
        notifyOrderLackHelperBO.setOrderLackInfoDesMap(orderLackInfoDesMap);
        notifyOrderLackHelperBO.setIsOrderCenter(Boolean.TRUE);
        notifyOrderLackHelperBO.setWarehouseId(warehouseId);
        notifyOrderLackHelperBO.setOptUserId(operatorUserId);
        notifyOrderLackHelperBO.setOrderList(orderList);

        Map<Long, Long> meiTuanOrderIdMap = orderConstraintCheckBL.getCanNotLackOrders(orderList).stream()
            .collect(Collectors.toMap(OutStockOrderPO::getId, OutStockOrderPO::getId));

        List<OutStockOrderItemDTO> couldLackOrderItemList =
            orderItemDTOS.stream().filter(item -> Objects.isNull(meiTuanOrderIdMap.get(item.getOutstockorder_Id())))
                .collect(Collectors.toList());

        List<OutStockOrderItemDTO> meiTuanLackOrderItemList =
            orderItemDTOS.stream().filter(item -> Objects.nonNull(meiTuanOrderIdMap.get(item.getOutstockorder_Id())))
                .collect(Collectors.toList());

        // 处理非美团缺货
        List<PartSendWsmDTO> partSendWsmDTOList =
            convertPartSendWmsDTOList(couldLackOrderItemList, notifyOrderLackHelperBO);
        // 处理美团缺货
        List<PartSendWsmDTO> meiTuanSendWsmDTOList =
            convertPartSendWmsDTOList(meiTuanLackOrderItemList, notifyOrderLackHelperBO);

        if (CollectionUtils.isEmpty(partSendWsmDTOList) && CollectionUtils.isEmpty(meiTuanSendWsmDTOList)) {
            notifyOrderLackBO.setLackOrderList(orderList);
            return notifyOrderLackBO;
        }

        orderLackInfoDesMap = notifyOrderLackHelperBO.getOrderLackInfoDesMap();

        LOG.info("标记缺货omsMarkPartNew,缺货数据：{}", JSON.toJSONString(partSendWsmDTOList));

        NotifyOrderCenterLackBO notifyOrderCenterLackBO =
            convertOrderCenterLackInfo(couldLackOrderItemList, partSendWsmDTOList, orderLackInfoDesMap, operatorUserId);

        NotifyMeiTuanLackBO meiTuanLackBO = new NotifyMeiTuanLackBO();
        meiTuanLackBO.setBufferMap(orderLackInfoDesMap);
        meiTuanLackBO.setOperatorUserId(operatorUserId);
        meiTuanLackBO.setPartSendWsmDTOList(meiTuanSendWsmDTOList);
        meiTuanLackBO.setWarehouseId(warehouseId);

        notifyOrderLackBO.setIsOpenOrderCenter(Boolean.TRUE);
        notifyOrderLackBO.setWarehouseId(orderItemDTOS.get(0).getWarehouseId());
        notifyOrderLackBO.setNotifyOrderCenterLackBO(notifyOrderCenterLackBO);
        notifyOrderLackBO.setMeiTuanLackBO(meiTuanLackBO);
        notifyOrderLackBO.setLackOrderItemIds(orderItemIds);
        notifyOrderLackBO.setLackOrderList(orderList);

        return notifyOrderLackBO;
    }

    // 这里组装了 orderLackInfoDesMap 描述
    private List<PartSendWsmDTO> convertPartSendWmsDTOList(List<OutStockOrderItemDTO> couldLackOrderItemList,
        NotifyOrderLackHelperBO notifyOrderLackHelperBO) {
        if (CollectionUtils.isEmpty(couldLackOrderItemList)) {
            return Collections.emptyList();
        }

        Map<Long, List<OutStockOrderItemDTO>> orderItemMap =
            couldLackOrderItemList.stream().collect(Collectors.groupingBy(OutStockOrderItemDTO::getOutstockorder_Id));
        List<PartSendWsmDTO> partSendWsmDTOList = new ArrayList<>();
        for (Map.Entry<Long, List<OutStockOrderItemDTO>> entry : orderItemMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }

            PartSendWsmDTO partSendWsmDTO = lackByOrder(notifyOrderLackHelperBO, entry.getValue());
            if (Objects.nonNull(partSendWsmDTO)) {
                partSendWsmDTOList.add(partSendWsmDTO);
            }
        }

        return partSendWsmDTOList;
    }

    private NotifyOrderCenterLackBO convertOrderCenterLackInfo(List<OutStockOrderItemDTO> couldLackOrderItemList,
        List<PartSendWsmDTO> partSendWsmDTOList, Map<String, StringBuffer> orderLackInfoDesMap,
        Integer operatorUserId) {

        if (CollectionUtils.isEmpty(couldLackOrderItemList)) {
            return null;
        }

        NotifyOrderCenterLackBO notifyOrderCenterLackBO = new NotifyOrderCenterLackBO();
        // 过滤出新流程调拨订单
        Map<Long,
            Long> businessIdMap = couldLackOrderItemList.stream()
                .filter(p -> null != p.getOrderType() && !StringUtils.isEmpty(p.getBusinessId())
                    && Objects.equals((byte)JiupiOrderTypeEnum.ORDER_TYPE_ALLOT, p.getOrderType()))
                .map(OutStockOrderItemDTO::getBusinessId).map(Long::parseLong).distinct()
                .collect(Collectors.toMap(k -> k, v -> v));

        notifyOrderCenterLackBO
            .setNormalPartSendWsmDTOList(getNormalPartSendWmsList(partSendWsmDTOList, businessIdMap));
        notifyOrderCenterLackBO.setAllotPartSendWsmDTOList(getAllotPartSendWmsList(partSendWsmDTOList, businessIdMap));
        notifyOrderCenterLackBO.setBufferMap(orderLackInfoDesMap);
        notifyOrderCenterLackBO.setOperatorUserId(operatorUserId);

        return notifyOrderCenterLackBO;
    }

    private List<PartSendWsmDTO> getMeiTuanPartSendWmsList(List<PartSendWsmDTO> partSendWsmDTOList,
        Map<Long, Long> businessIdMap) {
        if (CollectionUtils.isEmpty(businessIdMap)) {
            return partSendWsmDTOList;
        }

        return partSendWsmDTOList.stream().filter(m -> Objects.isNull(businessIdMap.get(m.getBusinessId())))
            .collect(Collectors.toList());
    }

    // 内配
    private List<PartSendWsmDTO> getAllotPartSendWmsList(List<PartSendWsmDTO> partSendWsmDTOList,
        Map<Long, Long> businessIdMap) {
        if (CollectionUtils.isEmpty(businessIdMap)) {
            return Collections.emptyList();
        }

        return partSendWsmDTOList.stream().filter(m -> Objects.nonNull(businessIdMap.get(m.getBusinessId())))
            .collect(Collectors.toList());
    }

    // 非内配
    private List<PartSendWsmDTO> getNormalPartSendWmsList(List<PartSendWsmDTO> partSendWsmDTOList,
        Map<Long, Long> businessIdMap) {
        if (CollectionUtils.isEmpty(businessIdMap)) {
            return partSendWsmDTOList;
        }

        return partSendWsmDTOList.stream().filter(m -> Objects.isNull(businessIdMap.get(m.getBusinessId())))
            .collect(Collectors.toList());
    }

    // 组装缺货信息
    private PartSendWsmDTO lackByOrder(NotifyOrderLackHelperBO notifyOrderLackHelperBO,
        List<OutStockOrderItemDTO> orderItemList) {
        Map<Long, List<OrderItemTaskInfoPO>> originOrderItemTaskInfoGroupMap =
            notifyOrderLackHelperBO.getOriginOrderItemTaskInfoGroupMap();
        Map<Long, List<OrderItemTaskInfoPO>> lackOrderItemTaskInfoGroupMap =
            notifyOrderLackHelperBO.getLackOrderItemTaskInfoGroupMap();
        Map<String, StringBuffer> orderLackInfoDesMap = notifyOrderLackHelperBO.getOrderLackInfoDesMap();
        boolean isOrderCenter = notifyOrderLackHelperBO.isIsOrderCenter();
        Integer warehouseId = notifyOrderLackHelperBO.getWarehouseId();
        Integer optUserId = notifyOrderLackHelperBO.getOptUserId();
        if (CollectionUtils.isEmpty(orderItemList)) {
            return null;
        }
        // 订单项发货数量Map<订单项ID,订单原始数量/或者发货数量>
        Map<Long, Integer> itemShipMap = new HashMap<>();
        // 数量明细Map<订单项ID,缺货或部分配送实际数据-编辑后数量>
        Map<Long, Integer> itemMap = new HashMap<>();

        // 判断订单类型是否是内配及中转订单
        boolean isTransfer = isTransfer(orderItemList.get(0).getAllotType());

        // 遍历订单项
        for (OutStockOrderItemDTO orderItem : orderItemList) {
            // 订单项关联数量
            List<OrderItemTaskInfoPO> oldInfoList = originOrderItemTaskInfoGroupMap.get(orderItem.getId());
            if (StringUtils.isEmpty(orderItem.getBusinessItemId())) {
                throw new BusinessException("缺货找不到订单项id：" + orderItem.getId());
            }
            // 订单项id
            Long omsOrderItemId = Long.valueOf(orderItem.getBusinessItemId());

            // 找出订单项对应的缺货数量
            List<OrderItemTaskInfoPO> lackList = lackOrderItemTaskInfoGroupMap.get(orderItem.getId());
            BigDecimal lackCount =
                lackList.stream().map(OrderItemTaskInfoPO::getLackUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 缺货为负的跳过
            if (lackCount.compareTo(BigDecimal.ZERO) < 0) {
                LOG.info("缺货数量为负{}", JSON.toJSONString(lackList));
                return null;
            }
            if (lackCount.divideAndRemainder(orderItem.getSaleSpecQuantity())[1].compareTo(BigDecimal.ZERO) != 0) {
                LOG.info("缺货数量不是销售规格的倍数: {}, saleSpecQuantity: {}；缺货信息：{}", JSON.toJSONString(orderItem),
                    orderItem.getSaleSpecQuantity(), JSON.toJSONString(lackList));
                throw new BusinessException("缺货数量不是销售规格的倍数");
            }
            // 订单中台 缺货消息发送为小单位数量
            // 订单原数量
            BigDecimal oldUnitTotalCount = getLackBeforeCount(oldInfoList);
            // oldInfoList.stream().map(p -> p.getUnitTotalCount().add(p.getMoveCount())).reduce(BigDecimal.ZERO,
            // BigDecimal::add);
            BigDecimal oldPackageCount = oldUnitTotalCount;
            itemShipMap.put(omsOrderItemId, oldPackageCount.intValue());

            // 缺货后的数量
            BigDecimal updateCount = oldPackageCount.subtract(lackCount);
            itemMap.put(omsOrderItemId, Math.max(updateCount.intValue(), 0));
        }

        if (itemMap.isEmpty() || itemShipMap.isEmpty()) {
            return null;
        }
        PartSendWsmDTO partSendWsmDTO =
            convertParSendWms(itemShipMap, itemMap, optUserId, warehouseId, orderItemList.get(0), isTransfer);

        for (OutStockOrderItemDTO orderItemDTO : orderItemList) {
            String desc = lackDes(orderItemDTO, itemMap, itemShipMap);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(desc)) {
                StringBuffer lackInfo =
                    orderLackInfoDesMap.getOrDefault(orderItemDTO.getBusinessId(), new StringBuffer());
                lackInfo.append(desc);
                lackInfo.append("；");
                orderLackInfoDesMap.put(orderItemDTO.getBusinessId(), lackInfo);
            }
        }

        return partSendWsmDTO;
    }

    private BigDecimal getLackBeforeCount(List<OrderItemTaskInfoPO> oldInfoList) {
        return oldInfoList.stream().map(p -> {
            BigDecimal count = p.getUnitTotalCount().add(p.getMoveCount());
            if (Objects.isNull(p.getOriginalUnitTotalCount())
                || p.getOriginalUnitTotalCount().compareTo(BigDecimal.ZERO) == 0) {
                return count;
            }
            if (count.compareTo(p.getOriginalUnitTotalCount()) > 0) {
                return p.getOriginalUnitTotalCount();
            }
            if (count.compareTo(BigDecimal.ZERO) <= 0) {
                return BigDecimal.ZERO;
            }
            return count;
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private PartSendWsmDTO convertParSendWms(Map<Long, Integer> itemShipMap, Map<Long, Integer> itemMap,
        Integer operatorUserId, Integer warehouseId, OutStockOrderItemDTO orderItemDTO, boolean isTransfer) {
        // 订单类型如果是中转订单，需要转化成oms订单id
        if (StringUtils.isEmpty(orderItemDTO.getBusinessId())) {
            throw new BusinessException("缺货找不到订单id：" + orderItemDTO.getId());
        }
        Long omsOrderId = Long.valueOf(orderItemDTO.getBusinessId());
        PartSendWsmDTO partSendWsmDTO = new PartSendWsmDTO();
        partSendWsmDTO.setBusinessId(omsOrderId);
        partSendWsmDTO.setOrderNo(orderItemDTO.getRefOrderNo());
        partSendWsmDTO.setUserId(operatorUserId);
        partSendWsmDTO.setItemShipMap(itemShipMap);
        partSendWsmDTO.setItemMap(itemMap);
        // 拣货缺货标识
        Map<Object, Object> extendMap = new HashMap<>();
        extendMap.put(OrderExtendMapEnum.拣货缺货.getType(), warehouseId);
        partSendWsmDTO.setExtendMap(extendMap);
        return partSendWsmDTO;
    }

    private boolean isTransfer(Byte allotType) {
        boolean isTransfer = false;
        if (Objects.equals(allotType, OutStockOrderBusinessType.中转订单.getType())
            || Objects.equals(allotType, OutStockOrderBusinessType.中转退订单.getType())
            || Objects.equals(allotType, OutStockOrderBusinessType.内配订单.getType())
            || Objects.equals(allotType, OutStockOrderBusinessType.内配退订单.getType())) {
            isTransfer = true;
        }
        return isTransfer;
    }

    // 灰度版本缺货传的小单位数量，非灰度传的是销售规格数量
    private String lackDes(OutStockOrderItemDTO orderItem, Map<Long, Integer> itemMap, Map<Long, Integer> itemShipMap) {
        Integer oriCount = itemShipMap.get(Long.valueOf(orderItem.getBusinessItemId()));
        Integer markCount = itemMap.get(Long.valueOf(orderItem.getBusinessItemId()));

        if (Objects.isNull(oriCount) || Objects.isNull(markCount)) {
            return "";
        }
        int lackCount = oriCount - markCount;

        return orderItem.getProductName().concat(" 缺货 ").concat(Integer.toString(lackCount))
            .concat(orderItem.getUnitName());
    }

    private List<OutStockOrderPO> getAllLackOrderList(List<BatchTaskItemLackDTO> filterBatchTaskItemLackDTOS,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, Integer warehouseId) {
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            List<String> batchTaskItemIds = filterBatchTaskItemLackDTOS.stream()
                .map(BatchTaskItemLackDTO::getBatchTaskItemId).collect(Collectors.toList());
            orderItemTaskInfoPOList = orderItemTaskInfoMapper.listOrderItemTaskInfoByBatchTaskItemIds(batchTaskItemIds);
        }
        List<Long> orderIds =
            orderItemTaskInfoPOList.stream().filter(p -> p.getLackUnitCount().compareTo(BigDecimal.ZERO) > 0)
                .map(OrderItemTaskInfoPO::getRefOrderId).distinct().collect(Collectors.toList());

        List<OutStockOrderPO> orderList = outStockOrderMapper.listOrderByIds(orderIds, null, warehouseId);

        return orderList;
    }

}
