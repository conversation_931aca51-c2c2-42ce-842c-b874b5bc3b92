package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.order.OrderItemDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.constants.WarehouseConfigConstants;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.DefaultLocationConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.DefaultLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.enums.DefaultTypeEnum;
import com.yijiupi.himalaya.supplychain.enums.PassagePickTypeEnum;
import com.yijiupi.himalaya.supplychain.inventory.constant.OtherConstTypes;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryQueryService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.InStockOrderBusinessType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockCommManageService;
import com.yijiupi.himalaya.supplychain.service.IDefaultLocationConfigService;
import com.yijiupi.himalaya.supplychain.service.IOrderReCheckStrategyService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.traceablecode.dto.centerorder.OrderTraceableItemDTO;
import com.yijiupi.himalaya.supplychain.traceablecode.service.ordersync.ITransferOrderQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductFeatureEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortGroupFlagEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SowTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.*;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.BatchAreaAndRouteBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskHelper;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation.RecommendOutLocationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder.OutStockOrderItemQueryBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder.OutStockOrderStateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.tms.NotifyTmsBatchCreateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationLargePickCargoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchtask.BatchTaskCreateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CreateBatchLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor.WaveCreateConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.ComputerStrategyConditionBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.CreateBatchChangeOrderSequenceBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.SplitBatchTaskByPassageBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.split.SplitBatchTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.split.WaveSplitRobotBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.BatchCreateUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.GoodsCollectionLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.CreateBatchTaskByOrderResultHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.CreateBatchTaskByPassageResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderTypeBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchTaskConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.WaveCreateDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveOrderInfo;
import com.yijiupi.himalaya.supplychain.waves.domain.mq.OrderPrintSequenceSyncMQ;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByProductAndOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchWorkSettingDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.OrderPrintSequenceDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderWaitDeliveryDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderSearchSO;
import com.yijiupi.himalaya.supplychain.waves.util.*;
import com.yijiupi.supplychain.serviceutils.constant.DeliveryOrderConstant;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;

/**
 * 波次策略执行类
 *
 * <AUTHOR>
 * @since 2018-03-16
 */
@Service
@SuppressWarnings("NonAsciiCharacters")
public class BatchOrderProcessBL {

    private static final Logger LOG = LoggerFactory.getLogger(BatchOrderProcessBL.class);

    @Reference
    private IStoreWareHouseService iStoreWareHouseService;
    @Reference
    private IBatchInventoryQueryService batchInventoryQueryService;
    @Reference
    private IWarehouseInventoryQueryService iWarehouseInventoryQueryService;
    @Autowired
    private OutStockOrderBL outStockOrderBL;
    @Reference
    private IProductLocationService iProductLocationService;
    @Reference
    private LocationAreaService locationAreaService;
    @Autowired
    private NotifyTmsBatchCreateBL notifyTmsBatchCreateBL;
    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private OrderItemTaskInfoDetailMapper orderItemTaskInfoDetailMapper;
    @Autowired
    private RecommendOutLocationBL recommendOutLocationBL;
    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;
    @Autowired
    private OrderTraceBL orderTraceBL;
    @Autowired
    private SowManagerBL sowManagerBL;
    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;
    @Autowired
    private BatchNoGenerator batchNoGenerator;
    @Autowired
    private OrderPrintSequenceSyncMQ orderPrintSequenceSyncMQ;
    @Autowired
    private BatchOrderInfoBL batchOrderInfoBL;
    @Autowired
    private SowCalculationBL sowCalculationBL;
    @Autowired
    private ThirdPartyOutStockBL thirdPartyOutStockBL;
    @Autowired
    private CreateBatchLocationBL createBatchLocationBL;
    @Autowired
    private CreateBatchLocationLargePickCargoBL createBatchLocationLargePickCargoBL;
    @Autowired
    private WaveSplitRobotBL waveSplitRobotBL;
    @Autowired
    private ComputerStrategyConditionBL computerStrategyConditionBL;
    @Resource
    private OutStockOrderStateBL outStockOrderStateBL;
    @Autowired
    private SplitBatchTaskBL splitBatchTaskBL;
    @Autowired
    private OrderFeatureBL orderFeatureBL;
    @Autowired
    private OutStockOrderItemQueryBL outStockOrderItemQueryBL;
    @Autowired
    private GlobalCache globalCache;
    @Resource
    private BatchTaskHelper batchTaskHelper;
    @Autowired
    private SplitBatchTaskByPassageBL splitBatchTaskByPassageBL;
    @Autowired
    private CreateBatchChangeOrderSequenceBL createBatchChangeOrderSequenceBL;
    @Autowired
    private BatchTaskCreateBL batchTaskCreateBL;

    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private IPassageService iPassageService;
    @Reference
    private ISortGroupService iSortGroupService;
    @Reference
    private IProductCategoryService iProductCategoryService;
    @Reference
    private IDefaultLocationConfigService iDefaultLocationConfigService;
    @Reference
    private ILocationService iLocationService;
    @Reference(timeout = 60000)
    private IOrderReCheckStrategyService iOrderReCheckStrategyService;
    @Reference
    private ITransferOrderQueryService iTransferOrderQueryService;
    @Reference
    private IOutStockCommManageService iOutStockCommManageService;
    @Autowired
    private WarehouseAllocationTypeManageBL warehouseAllocationTypeManageBL;
    @Autowired
    private PickingTaskItemSortingBL pickingTaskItemSortingBL;

    public static final String AWARD_NAME = "兑奖产品分拣任务";

    /**
     * @param wareStrategyId 波次策略id
     * @param orderStartTime 订单开始时间
     * @param orderEndTime 订单结束时间
     * @return
     * @Description: 根据波次策略Id生成波次及波次任务
     * <AUTHOR>
     * @date 2018/4/2 15:07
     */
    @Deprecated
    @Transactional(rollbackFor = RuntimeException.class)
    public void processBatchOrderByStrateId(Integer wareStrategyId, String orderStartTime, String orderEndTime) {
        throw new BusinessValidateException("波次策略已作废");
    }

    /**
     * 检验订单异常
     */
    public void validateOrderException(List<OutStockOrderPO> outStockOrderPOList) {}

    /**
     * 给内配单原单的产品设置关联货位
     */
    public void addProductLocationByAllocationOrder(List<OutStockOrderPO> outStockOrderPOList) {
        if (!CollectionUtils.isEmpty(outStockOrderPOList)) {
            Integer cityId = outStockOrderPOList.get(0).getOrgId();
            Integer warehouseId = outStockOrderPOList.get(0).getWarehouseId();
            // 查找内配单原单的产品skuid
            List<Long> skuIds = outStockOrderPOList.stream()
                .filter(p -> Objects.equals(p.getCreateAllocation(), ConditionStateEnum.是.getType())
                    && !CollectionUtils.isEmpty(p.getItems()))
                .flatMap(p -> p.getItems().stream()).map(p -> p.getSkuid()).distinct().collect(Collectors.toList());
            LOG.info("查找内配单原单的产品skuid：{}", JSON.toJSONString(skuIds));
            if (CollectionUtils.isEmpty(skuIds)) {
                return;
            }
            // 1、查询产品的关联货位
            Map<Long, List<LoactionDTO>> locationMap =
                iProductLocationService.findLocationDTOBySkuId(warehouseId, skuIds);

            // 2、找出没有关联货位的产品
            List<Long> noLocationSkuIds = new ArrayList<>();
            skuIds.forEach(skuId -> {
                if (locationMap != null && !CollectionUtils.isEmpty(locationMap.get(skuId))) {
                    return;
                }
                noLocationSkuIds.add(skuId);
            });
            if (CollectionUtils.isEmpty(noLocationSkuIds)) {
                return;
            }

            // 3、设置关联货位，优先关联收货配置中心的内配单收获暂存位，若没配置，则取仓库任意暂存位
            DefaultLocationQueryDTO defaultLocationQueryDTO = new DefaultLocationQueryDTO();
            defaultLocationQueryDTO.setWarehouseId(warehouseId);
            defaultLocationQueryDTO.setTemporaryLocationType(DefaultTypeEnum.内配单收货暂存位.getType());
            List<DefaultLocationConfigDTO> defaultLocationList =
                iDefaultLocationConfigService.findDefaultLocationConfig(defaultLocationQueryDTO);
            Long locationId = null;
            if (!CollectionUtils.isEmpty(defaultLocationList)) {
                // 关联内配单收获暂存位
                locationId = defaultLocationList.get(0).getTemporaryLocationId();
            } else {
                // 关联仓库任意暂存位
                LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
                locationQueryDTO.setCityId(cityId);
                locationQueryDTO.setWarehouseId(warehouseId);
                locationQueryDTO.setLocSubcategory(LocationEnum.暂存位.getType().byteValue());
                List<LoactionDTO> loactionDTOS = iLocationService.findLocationListByIdAndCategory(locationQueryDTO);
                if (!CollectionUtils.isEmpty(loactionDTOS)) {
                    locationId = loactionDTOS.get(0).getId();
                }
            }
            if (locationId == null) {
                LOG.info("找不到内配单收获暂存位和仓库暂存位！");
                return;
            }
            List<ProductLocationDTO> productLocationDTOList = new ArrayList<>();
            for (Long skuId : noLocationSkuIds) {
                ProductLocationDTO productLocationDTO = new ProductLocationDTO();
                productLocationDTO.setCityId(cityId);
                productLocationDTO.setWarehouseId(warehouseId);
                productLocationDTO.setProductSkuId(skuId);
                productLocationDTO.setLocationId(locationId);
                productLocationDTOList.add(productLocationDTO);
            }
            if (!CollectionUtils.isEmpty(productLocationDTOList)) {
                LOG.info("给内配单原单的产品设置关联货位：{}", JSON.toJSONString(productLocationDTOList));
                iProductLocationService.insertBatch(productLocationDTOList);
            }
        }
    }

    /**
     * 根据订单项id查询订单
     */
    public List<OutStockOrderPO>
        getOrderListByItemIds(BatchCreateByProductAndOrderDTO batchCreateByProductAndOrderDTO) {
        // 一键全选
        if (batchCreateByProductAndOrderDTO.getCheckAll().equals(ConditionStateEnum.是.getType())) {
            PageList<OutStockOrderWaitDeliveryDTO> pageList =
                batchOrderInfoBL.listOutStockOrderItemWaitDelivery(batchCreateByProductAndOrderDTO.getOrderSO());
            if (pageList != null && !CollectionUtils.isEmpty(pageList.getDataList())) {
                List<Long> orderItemIdList =
                    pageList.getDataList().stream().map(p -> p.getOrderItemId()).collect(Collectors.toList());
                LOG.info("全选的订单项条数：{}", orderItemIdList.size());
                batchCreateByProductAndOrderDTO.setOrderItemIdList(orderItemIdList);
            }
        }
        // 根据订单项id查询订单
        return outStockOrderMapper.findByOrderItemId(batchCreateByProductAndOrderDTO.getOrderItemIdList());
    }

    /**
     * 创建波次
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void createBatchByOrders(List<OutStockOrderPO> outStockOrderPOList, WavesStrategyBO wavesStrategyDTO,
        ProcessBatchDTO processBatchDTO) {
        Map<WaveOrderInfo, List<OutStockOrderPO>> waveOrders = Maps.newHashMap();
        try {
            if (CollectionUtils.isEmpty(outStockOrderPOList)) {
                LOG.info("没有满足条件的订单，请刷新重试或者联系技术支持！");
                if (!Objects.equals(processBatchDTO.getNotThrowException(), ConditionStateEnum.是.getType())) {
                    throw new BusinessValidateException("没有满足条件的订单，请刷新重试或者联系技术支持！");
                }
                return;
            }
            // 1、清除订单项数量与detail数量不一致的detail
            claerErrorOrderItemDetail(outStockOrderPOList);

            // 2、处理招商或者长株潭产品
            processZhaoShangSku(outStockOrderPOList);

            // 3、设置对应的货位
            // String msg = processLocation(outStockOrderPOList, wavesStrategyDTO, processBatchDTO);
            // LOG.info("以下商品未找到合适货位: {}", msg);

            // 3、设置对应的货位 替换原processLocation方法
            CreateBatchLocationBO createBatchLocationBO = createBatchLocationBL.handleLocation(outStockOrderPOList,
                processBatchDTO.getWorkSetting(), wavesStrategyDTO);

            LOG.info("处理设置locationid订单：{}", JSON.toJSONString(createBatchLocationBO));

            processBatchDTO
                .setOpenFrontWarehouseOpenNPProductPick(createBatchLocationBO.getOpenFrontWarehouseOpenNPProductPick());

            // TODO
            Map<WaveOrderInfo, List<OutStockOrderPO>> wavesMap =
                splitBatchTaskBL.splitBatchInfo(createBatchLocationBO, wavesStrategyDTO, processBatchDTO);
            if (Objects.isNull(wavesMap)) {
                wavesMap = Maps.newHashMap();
            }

            processBatchResultPre(wavesStrategyDTO, wavesMap, processBatchDTO);
        } catch (BusinessValidateException ex) {
            throw new BusinessException(ex);
        } catch (Exception ex) {
            LOG.warn("出现异常，", ex);
            throw new BusinessException(ex);
        }
    }

    /**
     * 清除订单项数量与detail数量不一致的detail
     *
     * @return
     */
    private void claerErrorOrderItemDetail(List<OutStockOrderPO> outStockOrderPOList) {
        if (!CollectionUtils.isEmpty(outStockOrderPOList)) {
            outStockOrderPOList.forEach(orderPO -> {
                if (orderPO == null || CollectionUtils.isEmpty(orderPO.getItems())) {
                    return;
                }
                orderPO.getItems().forEach(itemPO -> {
                    if (CollectionUtils.isEmpty(itemPO.getItemDetails())) {
                        return;
                    }
                    // 判断订单项数量是否等于detail总数量
                    BigDecimal sumDetailCount = itemPO.getItemDetails().stream().map(p -> p.getUnitTotalCount())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (sumDetailCount.compareTo(itemPO.getUnittotalcount()) != 0) {
                        itemPO.setItemDetails(null);
                        LOG.info("生成波次时清除订单项数量与detail数量不一致的detail，orderNo: {}, orderItemId: {}",
                            orderPO.getReforderno(), itemPO.getId());
                    }
                });
            });
        }
    }

    /**
     * 设置订单项的产品单价(四舍五入保留6位小数)
     */
    private void setUnitPriceByOrderItem(List<OutStockOrderItemPO> orderItemPOList) {
        if (CollectionUtils.isEmpty(orderItemPOList)) {
            return;
        }
        orderItemPOList.forEach(p -> {
            if (p.getTotalAmount() != null) {
                // 产品单价 = 产品总金额 / 小单位总数量
                p.setUnitPrice(p.getTotalAmount().divide(p.getUnittotalcount(), 6, BigDecimal.ROUND_HALF_UP));
            }
        });
    }

    /**
     * 根据是否开启货位扣，设置对应的货位
     *
     * @param outStockOrderPOList
     * @param wavesStrategyDTO
     * @return
     */
    public String processLocation(List<OutStockOrderPO> outStockOrderPOList, WavesStrategyDTO wavesStrategyDTO,
        ProcessBatchDTO processBatchDTO) {
        String msg;
        outStockOrderPOList.forEach(p -> {
            if (!CollectionUtils.isEmpty(p.getItems())) {
                p.getItems().removeIf(
                    q -> q.getUnittotalcount() == null || q.getUnittotalcount().compareTo(BigDecimal.ZERO) <= 0);
                // 设置订单项的产品单价
                setUnitPriceByOrderItem(p.getItems());
            }
        });
        outStockOrderPOList.removeIf(p -> CollectionUtils.isEmpty(p.getItems()));
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            throw new BusinessValidateException("生成波次失败，没有有效的订单！");
        } else {

            // 网格仓分配货位处理
            if (processBatchDTO.getWorkSetting() != null) {
                msg = setLocationIdOnWorkSetting(outStockOrderPOList, wavesStrategyDTO.getWarehouseId(),
                    processBatchDTO.getWorkSetting());
            } else {
                WarehouseConfigDTO warehouseConfigDTO =
                    globalCache.getWarehouseConfigDTO(wavesStrategyDTO.getWarehouseId());
                // 仓库是2.5还是3.0
                Boolean isOpenStock = warehouseConfigDTO.getIsOpenLocationStock();
                // 是否开启了货位组
                boolean isOpenLocationGroup = warehouseConfigDTO.isOpen2p5plus();

                // 设置locationid 未开启货位库存的只能使用推荐配置
                if (isOpenStock && !isOpenLocationGroup) {
                    msg = setLocationId(outStockOrderPOList, wavesStrategyDTO);
                } else {
                    msg = setLocationIdOnRecommend(outStockOrderPOList, wavesStrategyDTO);
                }
            }

            // 处理零拣位，拣货数量算成小单位数量
            // LOG.info("处理零拣位：{}", JSON.toJSONString(outStockOrderPOList));
            if (!CollectionUtils.isEmpty(outStockOrderPOList)) {
                outStockOrderPOList.forEach(order -> {
                    if (!CollectionUtils.isEmpty(order.getItems())) {
                        order.getItems().forEach(orderItem -> {
                            if (null != orderItem.getSubCategory()
                                && LocationEnum.零拣位.getType().byteValue() == orderItem.getSubCategory()) {
                                orderItem.setUnitcount(orderItem.getPackagecount().multiply(orderItem.getSpecquantity())
                                    .add(orderItem.getUnitcount()));
                                orderItem.setPackagecount(new BigDecimal(0));
                            }
                        });
                        order.setPackageamount(order.getItems().stream().map(p -> p.getPackagecount())
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                        order.setUnitamount(order.getItems().stream().map(p -> p.getUnitcount()).reduce(BigDecimal.ZERO,
                            BigDecimal::add));
                    }
                });
            }
        }
        return msg;
    }

    /**
     * 网格仓根据作业设置分配货位
     *
     * @return
     */
    private String setLocationIdOnWorkSetting(List<OutStockOrderPO> outStockOrderPOList, Integer warehouseId,
        BatchWorkSettingDTO workSetting) {
        String msg = "";
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return msg;
        }

        // 1、大件直接分配整件收货位
        outStockOrderPOList.stream().filter(p -> !CollectionUtils.isEmpty(p.getItems()))
            .flatMap(p -> p.getItems().stream()).filter(p -> p.getProductFeature() == null
                || Objects.equals(p.getProductFeature(), ProductFeatureEnum.大件.getType()))
            .forEach(itemPO -> {
                itemPO.setLocationId(workSetting.getLargeLocationId());
                itemPO.setLocationName(workSetting.getLargeLocationName());
            });

        // 2、小件先分配关联货位，找不到则分配拆零收货位
        List<OutStockOrderItemPO> smallOrderItemPOList = outStockOrderPOList.stream()
            .filter(p -> !CollectionUtils.isEmpty(p.getItems())).flatMap(p -> p.getItems().stream())
            .filter(p -> Objects.equals(p.getProductFeature(), ProductFeatureEnum.小件.getType()))
            .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(smallOrderItemPOList)) {
            // 查询关联货位
            List<Long> productSkuIds =
                smallOrderItemPOList.stream().map(p -> p.getSkuid()).distinct().collect(Collectors.toList());
            List<ProductLoactionItemDTO> locationBySkuId =
                iProductLocationService.findLocationBySkuId(warehouseId, productSkuIds);

            smallOrderItemPOList.forEach(itemPO -> {
                List<ProductLoactionItemDTO> productLoactionItemDTOS = locationBySkuId.stream()
                    .filter(p -> Objects.equals(p.getProductSkuId(), itemPO.getSkuid())).collect(Collectors.toList());
                // 如果产品配置了多个货位，优先使用零拣位和分拣位，再存储位
                ProductLoactionItemDTO productLoactionItemDTO = getProductLoactionItemDTO(productLoactionItemDTOS);
                if (productLoactionItemDTO != null) {
                    // 先分配关联货位
                    itemPO.setLocationId(productLoactionItemDTO.getLocationId());
                    itemPO.setLocationName(productLoactionItemDTO.getLocationName());
                } else {
                    // 找不到则分配拆零收货位
                    itemPO.setLocationId(workSetting.getSmallLocationId());
                    itemPO.setLocationName(workSetting.getSmallLocationName());
                }
            });
        }

        // 3、记录找不到货位的产品
        List<String> productNameList = outStockOrderPOList.stream().filter(p -> !CollectionUtils.isEmpty(p.getItems()))
            .flatMap(p -> p.getItems().stream())
            .filter(p -> p.getLocationId() == null || StringUtils.isEmpty(p.getLocationName()))
            .map(p -> p.getProductname()).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(productNameList)) {
            msg = String.join(",", productNameList);
        }

        // 将订单项按详细拆分
        setOutStockOrderItemDetail(outStockOrderPOList);
        return msg;
    }

    /**
     * 推荐货位配置 根据skuid获取推荐货位
     *
     * @param outStockOrderPOList
     * @param wavesStrategyDTO
     * @return
     */
    private String setLocationIdOnRecommend(List<OutStockOrderPO> outStockOrderPOList,
        WavesStrategyDTO wavesStrategyDTO) {
        // 是否开启整箱拆零拣货
        boolean isLargePick = false;
        WarehouseConfigDTO warehouseConfigDTO = globalCache.getWarehouseConfigDTO(wavesStrategyDTO.getWarehouseId());
        if (warehouseConfigDTO != null) {
            isLargePick =
                Objects.equals(warehouseConfigDTO.getLargePick(), ConditionStateEnum.是.getType()) ? true : false;
        }

        // 给没开启货位库存的产品设置最老的生产日期和批次时间
        setProductDateAndBatchTime(outStockOrderPOList);
        // 将订单项按详细拆分
        setOutStockOrderItemDetail(outStockOrderPOList);

        // 查询产品关联货位
        List<Long> productSkuIds = new ArrayList<>();
        List<ProductLoactionItemDTO> locationBySkuId = new ArrayList<>();
        for (OutStockOrderPO order : outStockOrderPOList) {
            if (!CollectionUtils.isEmpty(order.getItems())) {
                productSkuIds
                    .addAll(order.getItems().stream().map(p -> p.getSkuid()).distinct().collect(Collectors.toList()));
            }
        }
        if (productSkuIds.size() > 0) {
            productSkuIds = productSkuIds.stream().distinct().collect(Collectors.toList());
            locationBySkuId =
                iProductLocationService.findLocationBySkuId(wavesStrategyDTO.getWarehouseId(), productSkuIds);
        }
        Map<Long, List<ProductLoactionItemDTO>> productLoactionIMap =
            locationBySkuId.stream().collect(Collectors.groupingBy(ProductLoactionItemDTO::getProductSkuId));

        // 记录找不到推荐货位配置的产品名称
        Set<String> nameSet = new HashSet<>();
        for (int s = outStockOrderPOList.size() - 1; s >= 0; s--) {
            OutStockOrderPO outStockOrderPO = outStockOrderPOList.get(s);
            List<OutStockOrderItemPO> outStockOrderItemPOList = outStockOrderPO.getItems();
            if (isLargePick) {
                LOG.info("开启了整箱拆零拣货，可能按货位拆分");
                // 开启整箱拆零拣货，可能按货位拆分
                List<OutStockOrderItemPO> newOutStockOrderItemPOList =
                    getOrderItemByLargePick(outStockOrderItemPOList, productLoactionIMap, nameSet);
                outStockOrderPO.setItems(newOutStockOrderItemPOList);
            } else {
                for (OutStockOrderItemPO stockOrderItemPO : outStockOrderItemPOList) {
                    List<ProductLoactionItemDTO> productLoactionItemDTOS =
                        productLoactionIMap.get(stockOrderItemPO.getSkuid());
                    ProductLoactionItemDTO productLoactionItemDTO = null;
                    if (!CollectionUtils.isEmpty(productLoactionItemDTOS)) {
                        // 如果产品配置了多个货位，优先使用零拣位和分拣位，再存储位
                        productLoactionItemDTO = getProductLoactionItemDTO(productLoactionItemDTOS);
                        setOrderItemByLocation(stockOrderItemPO, productLoactionItemDTO);
                    } else {
                        // 找不到推荐货位配置则记录产品名称
                        nameSet.add(stockOrderItemPO.getProductname());
                    }
                }
            }
        }
        return String.join(",", nameSet);
    }

    /**
     * 开启整箱拆零拣货，需要按货位拆分
     */
    private List<OutStockOrderItemPO> getOrderItemByLargePick(List<OutStockOrderItemPO> outStockOrderItemPOList,
        Map<Long, List<ProductLoactionItemDTO>> productLoactionMap, Set<String> nameSet) {
        // 整件货位
        List<Integer> largeLocationList = Arrays.asList(LocationEnum.存储位.getType());
        // 拆零货位
        List<Integer> smallLocationList = Arrays.asList(LocationEnum.零拣位.getType(), LocationEnum.分拣位.getType());

        List<OutStockOrderItemPO> largePickItemList = new ArrayList<>();
        outStockOrderItemPOList.forEach(item -> {
            OutStockOrderItemPO newItemPO = new OutStockOrderItemPO();
            BeanUtils.copyProperties(item, newItemPO);
            // 当前产品的关联货位
            List<ProductLoactionItemDTO> productLoactionDTOS = productLoactionMap.get(newItemPO.getSkuid());
            if (CollectionUtils.isEmpty(productLoactionDTOS)) {
                // 找不到关联货位
                nameSet.add(newItemPO.getProductname());
            } else {
                // 小件优先查找货位
                ProductLoactionItemDTO smallProductLoactionDTO =
                    getProductLoactionItemDTOByCondition(productLoactionDTOS, smallLocationList, largeLocationList);
                // 大件优先查找货位
                ProductLoactionItemDTO largeProductLoactionDTO =
                    getProductLoactionItemDTOByCondition(productLoactionDTOS, largeLocationList, smallLocationList);

                // 如果产品包含大件和小件，同时关联了整件货位和拆零货位，则需要拆分
                if (newItemPO.getPackagecount() != null && newItemPO.getPackagecount().compareTo(BigDecimal.ZERO) > 0
                    && newItemPO.getUnitcount() != null && newItemPO.getUnitcount().compareTo(BigDecimal.ZERO) > 0
                    && productLoactionDTOS.stream().anyMatch(
                        p -> p.getSubcategory() != null && largeLocationList.contains(p.getSubcategory().intValue()))
                    && productLoactionDTOS.stream().anyMatch(
                        p -> p.getSubcategory() != null && smallLocationList.contains(p.getSubcategory().intValue()))) {
                    // 1、拆分出一个只有小件数量的订单项
                    OutStockOrderItemPO smallItemPO = new OutStockOrderItemPO();
                    BeanUtils.copyProperties(newItemPO, smallItemPO);
                    smallItemPO.setPackagecount(BigDecimal.ZERO);
                    smallItemPO.setUnittotalcount(smallItemPO.getPackagecount().multiply(smallItemPO.getSpecquantity())
                        .add(smallItemPO.getUnitcount()));
                    // 小件订单项分配拆零货位
                    setOrderItemByLocation(smallItemPO, smallProductLoactionDTO);
                    largePickItemList.add(smallItemPO);

                    // 2、处理只有大件的订单项
                    newItemPO.setUnitcount(BigDecimal.ZERO);
                    newItemPO.setUnittotalcount(newItemPO.getPackagecount().multiply(newItemPO.getSpecquantity())
                        .add(newItemPO.getUnitcount()));
                    // 大件订单项分配整件货位
                    setOrderItemByLocation(newItemPO, largeProductLoactionDTO);

                } else if (item.getPackagecount() != null && item.getPackagecount().compareTo(BigDecimal.ZERO) > 0) {
                    // 大件优先分配整件货位
                    setOrderItemByLocation(newItemPO, largeProductLoactionDTO);
                } else {
                    // 小件优先分配拆零货位
                    setOrderItemByLocation(newItemPO, smallProductLoactionDTO);
                }
            }
            // 记录处理了分配货位后的订单项
            largePickItemList.add(newItemPO);
        });
        return largePickItemList;
    }

    private void setOrderItemByLocation(OutStockOrderItemPO itemPO, ProductLoactionItemDTO productLoactionDTO) {
        if (productLoactionDTO != null) {
            itemPO.setLocationId(productLoactionDTO.getLocationId());
            itemPO.setLocationName(productLoactionDTO.getLocationName());
            itemPO.setSubCategory(productLoactionDTO.getSubcategory());
            itemPO.setAreaId(productLoactionDTO.getAreaId());
            itemPO.setAreaName(productLoactionDTO.getAreaName());
        }
    }

    /**
     * 自定义优先级获取拣货位
     *
     * @return
     */
    private ProductLoactionItemDTO getProductLoactionItemDTOByCondition(
        List<ProductLoactionItemDTO> productLoactionItemDTOS, List<Integer> firstLocationList,
        List<Integer> secondLocationList) {
        if (CollectionUtils.isEmpty(productLoactionItemDTOS)) {
            return null;
        }
        // 第一优先级
        Optional<ProductLoactionItemDTO> firstOptional =
            productLoactionItemDTOS.stream().filter(p -> p.getSubcategory() != null && firstLocationList != null
                && firstLocationList.contains(p.getSubcategory().intValue())).findFirst();
        if (firstOptional.isPresent()) {
            return firstOptional.get();
        }
        // 第二优先级
        Optional<ProductLoactionItemDTO> secondOptional =
            productLoactionItemDTOS.stream().filter(p -> p.getSubcategory() != null && secondLocationList != null
                && secondLocationList.contains(p.getSubcategory().intValue())).findFirst();
        if (secondOptional.isPresent()) {
            return secondOptional.get();
        }
        // 随便取一个
        return productLoactionItemDTOS.get(0);
    }

    /**
     * 按配置货位拣货出库的时候，如果产品配置了多个货位，优先使用零拣位和分拣位，再存储位
     *
     * @return
     */
    private ProductLoactionItemDTO getProductLoactionItemDTO(List<ProductLoactionItemDTO> productLoactionItemDTOS) {
        if (CollectionUtils.isEmpty(productLoactionItemDTOS)) {
            return null;
        }

        ProductLoactionItemDTO productLoactionItemDTO = null;
        // 按配置货位拣货出库的时候，如果产品配置了多个货位，优先使用零拣位和分拣位，再存储位
        if (productLoactionItemDTOS.stream()
            .anyMatch(p -> p.getSubcategory() != null && LocationEnum.零拣位.getType() == p.getSubcategory().intValue())) {
            Optional<ProductLoactionItemDTO> optional = productLoactionItemDTOS.stream()
                .filter(p -> p.getSubcategory() != null && LocationEnum.零拣位.getType() == p.getSubcategory().intValue())
                .findFirst();
            if (optional.isPresent()) {
                productLoactionItemDTO = optional.get();
            }
        } else if (productLoactionItemDTOS.stream()
            .anyMatch(p -> p.getSubcategory() != null && LocationEnum.分拣位.getType() == p.getSubcategory().intValue())) {
            Optional<ProductLoactionItemDTO> optional = productLoactionItemDTOS.stream()
                .filter(p -> p.getSubcategory() != null && LocationEnum.分拣位.getType() == p.getSubcategory().intValue())
                .findFirst();
            if (optional.isPresent()) {
                productLoactionItemDTO = optional.get();
            }
        } else if (productLoactionItemDTOS.stream()
            .anyMatch(p -> p.getSubcategory() != null && LocationEnum.存储位.getType() == p.getSubcategory().intValue())) {
            Optional<ProductLoactionItemDTO> optional = productLoactionItemDTOS.stream()
                .filter(p -> p.getSubcategory() != null && LocationEnum.存储位.getType() == p.getSubcategory().intValue())
                .findFirst();
            if (optional.isPresent()) {
                productLoactionItemDTO = optional.get();
            }
        }
        if (productLoactionItemDTO == null) {
            productLoactionItemDTO = productLoactionItemDTOS.get(0);
        }
        return productLoactionItemDTO;
    }

    /**
     * 将订单项按详细拆分
     */
    private void setOutStockOrderItemDetail(List<OutStockOrderPO> outStockOrderPOList) {
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }
        outStockOrderPOList.forEach(order -> {
            if (order != null && !CollectionUtils.isEmpty(order.getItems())) {
                List<OutStockOrderItemPO> newItemPOList = new ArrayList<>();
                order.getItems().forEach(item -> {
                    if (item == null) {
                        return;
                    }
                    if (!CollectionUtils.isEmpty(item.getItemDetails())) {
                        // 按detail复制多个item
                        item.getItemDetails().forEach(detail -> {
                            OutStockOrderItemPO outStockOrderItemPO = new OutStockOrderItemPO();
                            BeanUtils.copyProperties(item, outStockOrderItemPO);
                            outStockOrderItemPO.setItemDetailId(detail.getId());
                            outStockOrderItemPO.setSecOwnerId(detail.getSecOwnerId());
                            outStockOrderItemPO.setUnittotalcount(detail.getUnitTotalCount());
                            BigDecimal[] pickUpCountRemainder = outStockOrderItemPO.getUnittotalcount()
                                .divideAndRemainder(outStockOrderItemPO.getSpecquantity());
                            outStockOrderItemPO.setPackagecount(pickUpCountRemainder[0]);
                            outStockOrderItemPO.setUnitcount(pickUpCountRemainder[1]);
                            newItemPOList.add(outStockOrderItemPO);
                        });
                    } else {
                        newItemPOList.add(item);
                    }
                });
                if (!CollectionUtils.isEmpty(newItemPOList)) {
                    order.setItems(newItemPOList);
                }
            }
        });
    }

    /**
     * 给没开启货位库存的产品设置最老的生产日期和批次时间
     *
     * @param outStockOrderPOList
     */
    private void setProductDateAndBatchTime(List<OutStockOrderPO> outStockOrderPOList) {
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }
        Integer warehouseId = outStockOrderPOList.get(0).getWarehouseId();
        Set<Long> skuIds = new HashSet<>();
        outStockOrderPOList.forEach(order -> {
            if (order != null && !CollectionUtils.isEmpty(order.getItems())) {
                order.getItems().forEach(item -> {
                    if (item != null && item.getSkuid() != null) {
                        skuIds.add(item.getSkuid());
                    }
                });
            }
        });
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        // 查询货位库存
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setWarehouseId(warehouseId);
        batchInventoryQueryDTO.setSkuIds(new ArrayList<>(skuIds));
        PageList<BatchInventoryDTO> pageList =
            batchInventoryQueryService.findBatchInventoryListBatchNew(batchInventoryQueryDTO);
        if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
            return;
        }
        // 分别找到订单项skuid已分配货位的批次库存
        outStockOrderPOList.forEach(order -> {
            if (order != null && !CollectionUtils.isEmpty(order.getItems())) {
                order.getItems().forEach(item -> {
                    if (item != null && item.getSkuid() != null) {
                        List<BatchInventoryDTO> fitlerList = pageList.getDataList().stream()
                            .filter(p -> Objects.equals(p.getProductSkuId(), item.getSkuid()))
                            .collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(fitlerList)) {
                            // 存在多条时，按生产日期+批次日期先入先出排序
                            if (fitlerList.size() > 1) {
                                fitlerList = fitlerList.stream()
                                    .sorted(Comparator.nullsFirst(Comparator
                                        .comparing(BatchInventoryDTO::getProductionDate,
                                            Comparator.nullsFirst(Date::compareTo))
                                        .thenComparing(BatchInventoryDTO::getBatchTime,
                                            Comparator.nullsFirst(Date::compareTo))))
                                    .collect(Collectors.toList());
                            }
                            item.setProductionDate(fitlerList.get(0).getProductionDate());
                            item.setBatchTime(fitlerList.get(0).getBatchTime());
                        }
                    }
                });
            }
        });
    }

    /**
     * 给OutStockOrderItem设置location
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2018/4/25 13:51
     */
    private String setLocationId(List<OutStockOrderPO> outStockOrderPOList, WavesStrategyDTO wavesStrategyDTO) {
        String msg = "";
        List<OutStockOrderItemPO> outStockOrderItemPOList = Lists.newArrayList();
        List<OutStockOrderItemPO> outStockDelayOrderItemPOList = Lists.newArrayList();
        List<OutStockOrderItemPO> totalOrderItemPOList = Lists.newArrayList();
        outStockOrderPOList.forEach(e -> {
            totalOrderItemPOList.addAll(e.getItems());
            // 延迟配送订单
            if (Objects.equals(e.getDeliveryMarkState(), DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue())) {
                outStockDelayOrderItemPOList.addAll(e.getItems());
            } else {
                outStockOrderItemPOList.addAll(e.getItems());
            }
        });

        List<OrderItemDTO> orderItemDTOS = new ArrayList<>();
        if (outStockOrderItemPOList.size() > 0) {
            List<OrderItemDTO> orderItemDTO =
                BatchTaskConvertor.getOrderItemDTO(outStockOrderItemPOList, wavesStrategyDTO.getWarehouseId());
            LOG.info("获取货位货区参数1{}", JSON.toJSONString(orderItemDTO));
            List<OrderItemDTO> normalOrderItemDTOS =
                batchInventoryQueryService.findOutStockStrategyRule(orderItemDTO, "1");
            LOG.info("获取货位货区结果1{}", JSON.toJSONString(normalOrderItemDTOS));
            orderItemDTOS.addAll(normalOrderItemDTOS);
        }

        // 延迟配送，单独从退货暂存区取货
        if (outStockDelayOrderItemPOList.size() > 0) {
            List<OrderItemDTO> delayOrderItemDTO =
                BatchTaskConvertor.getOrderItemDTO(outStockDelayOrderItemPOList, wavesStrategyDTO.getWarehouseId());
            LOG.info("获取货位货区参数2{}", JSON.toJSONString(delayOrderItemDTO));
            List<OrderItemDTO> delayOrderItemDTOS =
                batchInventoryQueryService.findDelayOutStockStrategyRule(delayOrderItemDTO, "1");
            LOG.info("获取货位货区结果2{}", JSON.toJSONString(delayOrderItemDTOS));
            orderItemDTOS.addAll(delayOrderItemDTOS);
        }

        // 组装新的订单项
        Map<String, List<OutStockOrderItemPO>> resultLocation =
            getOutStockOrderItemPOListByInventoryBak(orderItemDTOS, totalOrderItemPOList);
        List<OutStockOrderItemPO> haveLocation = resultLocation.get("haveLocation");
        LOG.info("haveLocationcollect{}", JSON.toJSONString(haveLocation));
        List<OutStockOrderItemPO> noLocation = resultLocation.get("noLocation");
        LOG.info("noLocationcollect{}", JSON.toJSONString(noLocation));
        Map<Long, List<OutStockOrderItemPO>> noLocationcollect = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(noLocation)) {
            noLocationcollect = noLocation.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getId));
        }

        List<OutStockOrderItemPO> allResultLocation = new ArrayList<>();
        allResultLocation.addAll(noLocation);
        allResultLocation.addAll(haveLocation);
        Map<Long, List<OutStockOrderItemPO>> allResultLocationMap =
            allResultLocation.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getId));

        for (int s = outStockOrderPOList.size() - 1; s >= 0; s--) {
            OutStockOrderPO outStockOrderPO = outStockOrderPOList.get(s);
            List<OutStockOrderItemPO> lstOrderItems = outStockOrderPO.getItems();
            List<OutStockOrderItemPO> newItemPo = Lists.newArrayList();
            for (OutStockOrderItemPO orderItemPo : lstOrderItems) {
                // 记录无货位商品
                if (noLocationcollect.containsKey(orderItemPo.getId())) {
                    // newItemPo.clear();
                    // outStockOrderPOList.remove(outStockOrderPO);
                    msg = msg + orderItemPo.getProductname() + ",";
                    // break;
                }
                // if (haveLocationcollect.containsKey(orderItemPo.getId())) {
                // newItemPo.addAll(haveLocationcollect.get(orderItemPo.getId()));
                // }
                newItemPo.addAll(allResultLocationMap.get(orderItemPo.getId()));
            }
            outStockOrderPO.setItems(newItemPo);
        }
        return msg;
    }

    /**
     * 给订单上锁 并过滤
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2018/4/19 14:32
     */
    public List<RedisLock> setOrderLock(List<OutStockOrderPO> outStockOrderPOList) {
        List<RedisLock> redisLocks = Lists.newArrayList();
        for (int s = outStockOrderPOList.size() - 1; s >= 0; s--) {
            OutStockOrderPO outStockOrderPO = outStockOrderPOList.get(s);
            RedisLock lock = new RedisLock(redisTemplate, "supc:stockorder:BatchOrder:" + outStockOrderPO.getId());
            try {
                if (lock.lock()) {
                    LOG.info("加锁成功：{}", outStockOrderPO.getId());
                    redisLocks.add(lock);
                } else {
                    LOG.info("加锁失败：{}", outStockOrderPO.getId());
                    outStockOrderPOList.remove(outStockOrderPO);
                }
            } catch (Exception e) {
                throw new BusinessException("过滤订单异常", e);
            }
        }

        return redisLocks;
    }

    private void processBatchResultPre(WavesStrategyBO wavesStrategyDTO,
        Map<WaveOrderInfo, List<OutStockOrderPO>> waveOrders, ProcessBatchDTO processBatchDTO) {
        if (waveOrders == null || waveOrders.size() <= 0) {
            LOG.info("没有生成波次信息，不需要保存！{}", wavesStrategyDTO);
            return;
        }
        // 根据仓库Id查找默认城市Id
        Integer cityId = iStoreWareHouseService.getMajorCityByWarehouseId(wavesStrategyDTO.getWarehouseId());
        processBatchDTO.setCityId(cityId);
        waveOrders.forEach((k, orders) -> {
            // 重新计算订单的大小单位数量
            orders.forEach(p -> {
                p.getItems().removeIf(
                    q -> q.getUnittotalcount() == null || q.getUnittotalcount().compareTo(BigDecimal.ZERO) <= 0);
                p.setPackageamount(p.getItems().stream().map(OutStockOrderItemPO::getPackagecount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
                p.setUnitamount(p.getItems().stream().map(OutStockOrderItemPO::getUnitcount).reduce(BigDecimal.ZERO,
                    BigDecimal::add));
            });
            orders.removeIf(p -> p.getItems() == null || CollectionUtils.isEmpty(p.getItems()));
            if (!CollectionUtils.isEmpty(orders)) {
                // 以行政区域划分波次时，此toLocationId才不会为空
                if (k.getToLocationId() != null) {
                    processBatchDTO.setToLocationId(k.getToLocationId());
                }
                processSingleBatch(wavesStrategyDTO, orders, processBatchDTO);
                orders.clear();
                orders = null;
            }
        });
    }

    private void processSingleBatch(WavesStrategyBO wavesStrategyDTO, List<OutStockOrderPO> orders,
        ProcessBatchDTO processBatchDTO) {
        Boolean allocationFlag = processBatchDTO.getAllocationFlag();
        String title = processBatchDTO.getBatchName();
        Integer cityId = processBatchDTO.getCityId();
        String operateUser = processBatchDTO.getOperateUser();
        String locationName = processBatchDTO.getLocationName();
        String driverName = processBatchDTO.getDriverName();

        BatchPO batchPO = new BatchPO();

        BatchAreaAndRouteBO batchAreaAndRouteBO =
            WaveCreateDTOConvertor.convertBatchRouteAndAreaInfo(processBatchDTO, wavesStrategyDTO, orders);
        batchPO.setAreaId(batchAreaAndRouteBO.getAreaId());
        batchPO.setAreaName(batchAreaAndRouteBO.getAreaName());
        batchPO.setRouteId(batchAreaAndRouteBO.getRouteId());
        batchPO.setRouteName(batchAreaAndRouteBO.getRouteName());
        batchPO.setRouteSequence(batchAreaAndRouteBO.getRouteSequence());

        // 订单筛选 1 按区域 2 按线路
        // 拣货方式 1 按订单拣货 2 按产品拣货 3 按通道拣货

        BatchCreateUtils.setBatchName(wavesStrategyDTO, processBatchDTO, batchPO, orders);
        // SCM-18124 拣货任务明细的拣货数量，零拣位返回大小件，不返回小件数量
        BigDecimal[] pieceInfo = OrderPieceUtils.reCalcOrderPiece(orders);
        BigDecimal orderAmount =
            orders.stream().map(OutStockOrderPO::getOrderamount).reduce(BigDecimal.ZERO, BigDecimal::add);
        int skuCount = (int)orders.stream().map(OutStockOrderPO::getItems).filter(Objects::nonNull)
            .flatMap(Collection::stream).map(OutStockOrderItemPO::getSkuid).distinct().count();

        batchPO.setBatchNo(UuidUtil.generator(wavesStrategyDTO.getWarehouseId(), BatchOrderTypeConstans.BATCH_NO));
        batchPO.setId(UuidUtil.generatorId());
        batchPO.setOrderSelection(wavesStrategyDTO.getOrderSelection());
        batchPO.setOrderAmount(orderAmount);
        batchPO.setOrderCount(orders.size());
        batchPO.setPackageAmount(pieceInfo[0]);
        batchPO.setSkuCount(skuCount);
        batchPO.setUnitAmount(pieceInfo[1]);
        batchPO.setPickingType(wavesStrategyDTO.getPickingType());
        batchPO.setPickingGroupStrategy(wavesStrategyDTO.getPickingGroupStrategy());
        batchPO.setState(BatchStateEnum.PENDINGPICKING.getType().byteValue());
        batchPO.setOrgId(cityId);
        batchPO.setBatchAttrSettingWay(wavesStrategyDTO.getBatchAttrSettingWay());
        BatchCreateUtils.setBatchName(wavesStrategyDTO, processBatchDTO, batchPO, orders);
        final int maxLength = 30;
        if (StringUtils.isNotEmpty(batchPO.getAreaName()) && batchPO.getAreaName().length() > maxLength) {
            batchPO.setAreaName(batchPO.getAreaName().substring(0, maxLength));
        }
        if (StringUtils.isNotEmpty(batchPO.getRouteName()) && batchPO.getRouteName().length() > maxLength) {
            batchPO.setRouteName(batchPO.getRouteName().substring(0, maxLength));
        }
        if (batchPO.getBatchName().length() > 49) {
            batchPO.setBatchName(batchPO.getBatchName().substring(0, 49));
        }
        batchPO.setWarehouseId(wavesStrategyDTO.getWarehouseId());
        batchPO.setState(BatchStateEnum.PENDINGPICKING.getType().byteValue());
        batchPO.setCreateUser(operateUser);
        batchPO.setCreateUserId(processBatchDTO.getOperateUserId());
        // 快递直发波次，备注写死为“快递直发”
        if (Objects.equals(processBatchDTO.getExpressFlag(), ExpressFlagEnum.快递直发订单.getType())) {
            batchPO.setRemark("快递直发");
        }
        batchPO.setBatchType(processBatchDTO.getBatchType());
        batchPO.setBatchOrderType(WaveCreateConvertor.getBatchOrderType(processBatchDTO, orders));
        batchPO.setCrossWareHouse(processBatchDTO.getCrossWareHouse());
        batchPO.setSowType(BatchSowTypeEnum.普通波次.getType());

        Set<Long> OutStockOrderIds =
            orders.stream().map(OutStockOrderPO::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        // 设置分仓属性
        warehouseAllocationTypeManageBL.setBatchWarehouseAllocationType(batchPO, OutStockOrderIds);

        // 保存波次
        this.batchMapper.insertSelective(batchPO);
        // -添加操作记录（组建波次成功）
        orderTraceBL.insertBatchTrace(batchPO, processBatchDTO.getAutoCreateFlag());

        // 出库单设置波次编号
        orders.forEach(e -> {
            e.setBatchno(batchPO.getBatchNo());
            e.setBatchId(batchPO.getId());
        });

        // 处理拣货任务和播种任务
        List<String> locationNames = new ArrayList<>();

        SplitBatchTaskByOrderTypeBO splitBatchTaskByOrderTypeBO =
            splitBatchTaskBL.splitBatchTaskByOrderType(wavesStrategyDTO, orders, processBatchDTO);

        LOG.info("拆分结果为:{}", JSON.toJSONString(splitBatchTaskByOrderTypeBO));

        List<WaveCreateDTO> allotWaveCreateDTO = splitBatchTaskByOrderTypeBO.getAllotWaveCreateDTO();
        if (!CollectionUtils.isEmpty(allotWaveCreateDTO)) {
            allotWaveCreateDTO.forEach(m -> processBatchTaskResult(m, batchPO, locationNames));
        }

        // 混合批次库存的促销订单，单独生成按订单拣货的任务
        List<WaveCreateDTO> promotionWaveCreateList = splitBatchTaskByOrderTypeBO.getPromotionWaveCreateDTO();
        if (!CollectionUtils.isEmpty(promotionWaveCreateList)) {
            promotionWaveCreateList.forEach(waveCreateDTO -> {
                processBatchTaskResult(waveCreateDTO, batchPO, locationNames);
            });
        }

        // 2、普通订单（排除内配单原单）
        List<OutStockOrderPO> normalOrders = orders.stream()
            .filter(p -> Objects.isNull(p.getOutBoundType())
                || OutBoundTypeEnum.SALE_ORDER.getCode().byteValue() != p.getOutBoundType()
                || !OrderConstant.ALLOT_TYPE_ALLOCATION.equals(p.getAllotType()))
            .collect(Collectors.toList());
        List<WaveCreateDTO> normalWaveCreateDTO = splitBatchTaskByOrderTypeBO.getNormalWaveCreateDTO();
        if (!CollectionUtils.isEmpty(normalWaveCreateDTO)) {
            BatchBO batchBO = new BatchBO(batchPO);
            normalWaveCreateDTO.forEach(createDTO -> {
                // 网格仓 待优化
                if (processBatchDTO.getWorkSetting() != null) {
                    LOG.info("网格仓处理拣货任务...");
                    if (wavesStrategyDTO.getIsMultiOutBound()) {
                        processBatchTaskWorkSettingByMultiOutBound(createDTO, batchPO, locationNames);
                    } else {
                        processBatchTaskWorkSetting(createDTO, batchPO, locationNames);
                    }
                } else {
                    // 酒批仓库
                    if (PassagePickTypeEnum.不开启.getType() == wavesStrategyDTO.getPassPickType()) {
                        LOG.info("不开启拣货通道...");
                        if (wavesStrategyDTO.getIsMultiOutBound()) {
                            processBatchTaskResultByMultiOutBound(createDTO, batchPO, locationNames);
                        } else {
                            processBatchTaskResult(createDTO, batchPO, locationNames);
                        }
                    } else {
                        LOG.info("开启拣货通道...");
                        // processByPassage(createDTO, batchBO, locationNames);
                        createBatchTaskByPassage(createDTO, batchBO, locationNames);
                    }
                }
            });
        }

        // 微酒生波次
        if (Objects.equals(processBatchDTO.getBatchType(), BatchTypeEnum.微酒.getType())) {
            LOG.info("微酒订单生波次，不执行oms调度");
        } else if (BatchTypeEnum.isApplyOrderBatch(processBatchDTO.getBatchType())) {
            LOG.info("第三方出库申请单生波次，不执行oms调度");
        }

        if (Objects.equals(batchPO.getSowType(), SowTypeEnum.二次分拣.getType())) {
            BatchPO updateBatch = new BatchPO();
            updateBatch.setOrgId(batchPO.getOrgId());
            updateBatch.setBatchNo(batchPO.getBatchNo());
            updateBatch.setSowType(BatchSowTypeEnum.二次分拣.getType());
            updateBatch.setBatchName("总单二次分拣");
            updateBatch.setPickingType(PickingTypeEnum.产品拣货.getType());
            updateBatch.setPickingGroupStrategy((byte)WavesStrategyConstants.PICKINGGROUPSTRATEGY_CATEGORY);
            batchMapper.updateSecondSort(updateBatch);
        }
    }

    /**
     * 按通道拆分拣货任务
     */
    private void createBatchTaskByPassage(WaveCreateDTO createDTO, BatchBO batchBO, List<String> locationNames) {
        CreateBatchTaskByPassageResultBO passageResultBO =
            splitBatchTaskByPassageBL.createBatchTaskByPassage(createDTO, batchBO, null, locationNames);
        for (WaveCreateDTO waveDTO : passageResultBO.getWaveCreateDTOList()) {
            processBatchTaskResult(waveDTO, batchBO.getBatchPO(), locationNames);
        }
        createBatchChangeOrderSequenceBL.updateOrderSequenceIfCreateSow(passageResultBO.getWaveCreateDTOList());
    }

    /**
     * 网格仓处理拣货任务
     */
    private void processBatchTaskWorkSetting(WaveCreateDTO createDTO, BatchPO batchPO, List<String> locationNames) {
        // 按产品特征大小件拆分订单
        Map<Byte, List<OutStockOrderPO>> splitOrderMap = getOrderMapByProductFeatureSplit(createDTO.getOrders());
        splitOrderMap.forEach((productFeature, orderList) -> {
            if (CollectionUtils.isEmpty(orderList)) {
                return;
            }
            // 1、小件按自提点摘果或播种
            if (Objects.equals(productFeature, ProductFeatureEnum.小件.getType())) {
                // (1)摘果模式
                if (createDTO.getWorkSetting() != null && createDTO.getWorkSetting().getSowType() == 1) {
                    // 小件按自提点摘果
                    LOG.info("小件按自提点摘果...");
                    processBatchTaskByAddress(orderList, createDTO, batchPO, locationNames);

                    // (2)播种模式
                } else {
                    sowCalculationBL.processAddressSowTask(createDTO, orderList, batchPO, createDTO.getWorkSetting());
                    updateOrderWorkSetting(orderList, batchPO);
                }

                // 2、大件按线路摘果
            } else {
                WaveCreateDTO largeCreateDTO = new WaveCreateDTO();
                BeanUtils.copyProperties(createDTO, largeCreateDTO);
                largeCreateDTO.setOrders(orderList);
                LOG.info("大件按线路摘果...");
                processBatchTaskResult(largeCreateDTO, batchPO, locationNames);
            }
        });
    }

    /**
     * 小件按自提点摘果
     */
    private void processBatchTaskByAddress(List<OutStockOrderPO> orderList, WaveCreateDTO createDTO, BatchPO batchPO,
        List<String> locationNames) {
        Map<PassageDTO, List<OutStockOrderPO>> passageOrderMap = new HashMap<>();
        // 符合通道的订单项
        List<OutStockOrderItemPO> lstAdded = new ArrayList<>();

        List<OutStockOrderItemPO> lstOrderItems = new ArrayList<>();
        for (OutStockOrderPO order : orderList) {
            lstOrderItems.addAll(order.getItems());
        }

        // 1、开启通道，按通道拆分订单
        if (createDTO.getWavesStrategyDTO().getPassPickType() != null
            && PassagePickTypeEnum.不开启.getType() != createDTO.getWavesStrategyDTO().getPassPickType()) {
            // 根据仓库Id查找对应的仓库通道类型
            Integer warehouseId = createDTO.getWavesStrategyDTO().getWarehouseId();
            Byte pickType = iPassageService.getPassageTypeByWarehouseId(warehouseId);
            if (null != pickType) {
                LOG.info("当前仓库的通道类型：{}", pickType);
                // 1、根据类目或货位查询满足条件的通道配置
                List<PassageDTO> lstPassStrategyDTO =
                    splitBatchTaskByPassageBL.getPassageDTOS(lstOrderItems, warehouseId, pickType);
                LOG.info("找到通道配置：{}", JSON.toJSONString(lstPassStrategyDTO));
                // 2、遍历满足条件的通道配置，查找符合条件的订单
                if (!CollectionUtils.isEmpty(lstPassStrategyDTO)) {
                    lstPassStrategyDTO.forEach(passageDTO -> {
                        // 根据通道策咯查找所有符合条件的订单详情
                        List<OutStockOrderItemPO> lstItems = splitBatchTaskByPassageBL
                            .getOutStockOrderItemByPassage(lstOrderItems, pickType, lstAdded, passageDTO);
                        if (!CollectionUtils.isEmpty(lstItems)) {
                            // 据通道，把订单项拆分，然后合并成新的订单对象
                            List<OutStockOrderPO> lstOrders =
                                OrderRebuildUtil.getOrdersByPassageItems(orderList, lstItems);
                            passageOrderMap.put(passageDTO, lstOrders);
                            // 记录符合通道的订单项
                            lstAdded.addAll(lstItems);
                        }
                    });
                }
            } else {
                LOG.info(String.format("没有配置通道！仓库Id：%s", warehouseId));
            }
        }

        // 2、不符合通道策略的订单项单独生拣货任务
        if (!CollectionUtils.isEmpty(lstAdded)) {
            List<OutStockOrderItemPO> lstOtherItems = lstOrderItems.stream()
                .filter(p -> !lstAdded.stream()
                    .anyMatch(q -> q.getId().equals(p.getId()) && Objects.equals(q.getLocationId(), p.getLocationId())))
                .collect(Collectors.toList());
            LOG.info("不符合通道策略的订单项: {}", JSON.toJSONString(lstOtherItems));
            List<OutStockOrderPO> otherOrderList = OrderRebuildUtil.getOrdersByPassageItems(orderList, lstOtherItems);
            passageOrderMap.put(null, otherOrderList);
        } else {
            passageOrderMap.put(null, orderList);
        }

        // 3、生拣货任务
        // (1)按通道拆分
        passageOrderMap.forEach((passage, passageOrderList) -> {
            // (2)按自提点拆分
            Map<Integer, List<OutStockOrderPO>> addressOrderMap = passageOrderList.stream()
                .collect(Collectors.groupingBy(p -> p.getAddressId() == null ? 0 : p.getAddressId()));
            addressOrderMap.forEach((addressId, addressOrderList) -> {
                WaveCreateDTO smallCreateDTO = new WaveCreateDTO();
                BeanUtils.copyProperties(createDTO, smallCreateDTO);
                smallCreateDTO.setOrders(addressOrderList);
                smallCreateDTO.setBatchTaskType(BatchTaskTypeEnum.按自提点.getType());
                smallCreateDTO.setPassageDTO(passage);
                smallCreateDTO.setAddressId(addressId);
                processBatchTaskResult(smallCreateDTO, batchPO, locationNames);
            });
        });

    }

    /**
     * 按产品特征大小件拆分订单
     *
     * @return
     */
    private Map<Byte, List<OutStockOrderPO>>
        getOrderMapByProductFeatureSplit(List<OutStockOrderPO> outStockOrderPOList) {
        Map<Byte, List<OutStockOrderPO>> orderMap = new HashMap<>();
        // 按产品特征分组
        Map<Byte, List<OutStockOrderItemPO>> splitMap =
            outStockOrderPOList.stream().filter(p -> !CollectionUtils.isEmpty(p.getItems()))
                .flatMap(p -> p.getItems().stream()).collect(Collectors.groupingBy(
                    p -> p.getProductFeature() == null ? ProductFeatureEnum.大件.getType() : p.getProductFeature()));
        splitMap.forEach((productFeature, list) -> {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            // 组装新的订单
            List<OutStockOrderPO> orderPOList = new ArrayList<>();
            Map<Long, List<OutStockOrderItemPO>> splitOrderMap =
                list.stream().collect(Collectors.groupingBy(p -> p.getOutstockorderId()));
            splitOrderMap.forEach((orderId, itemPOList) -> {
                Optional<OutStockOrderPO> optional =
                    outStockOrderPOList.stream().filter(p -> Objects.equals(p.getId(), orderId)).findFirst();
                if (optional.isPresent()) {
                    OutStockOrderPO orderPO = new OutStockOrderPO();
                    BeanUtils.copyProperties(optional.get(), orderPO);
                    orderPO.setItems(itemPOList);
                    // 添加新的订单
                    orderPOList.add(orderPO);
                } else {
                    LOG.error("按产品特征大小件拆分订单时找不到订单：{}", orderId);
                }
            });
            if (CollectionUtils.isEmpty(orderPOList)) {
                return;
            }
            if (productFeature == 0) {
                LOG.warn("存在产品特征为空的订单: {}", JSON.toJSONString(orderPOList));
            }
            orderMap.put(productFeature, orderPOList);
        });
        LOG.info("按产品特征大小件拆分订单: {}", JSON.toJSONString(orderMap));
        return orderMap;
    }

    /**
     * 处理波次状态
     */
    private Byte processBatchState(List<BatchTaskPO> lstBatchTasks) {
        // 默认“待拣货”状态
        Byte batchState = BatchStateEnum.PENDINGPICKING.getType().byteValue();
        if (!CollectionUtils.isEmpty(lstBatchTasks)) {
            // 如果任意一个拣货任务是“已完成”，则波次状态为“拣货中”
            if (lstBatchTasks.stream().anyMatch(p -> TaskStateEnum.已完成.getType() == p.getTaskState())) {
                batchState = BatchStateEnum.PICKING.getType().byteValue();
                // 如果所有拣货任务都是“已完成”，则波次状态为“播种中”
                if (lstBatchTasks.stream().allMatch(p -> TaskStateEnum.已完成.getType() == p.getTaskState())) {
                    batchState = BatchStateEnum.SOWN.getType().byteValue();
                }
            }
        }
        return batchState;
    }

    /**
     * 按通道拆分拣货任务
     */
    @Deprecated
    private void processByPassage(WaveCreateDTO createDTO, BatchBO batchBO, List<String> locationNames) {
        List<OutStockOrderPO> orders = createDTO.getOrders();
        WavesStrategyBO wavesStrategyDTO = createDTO.getWavesStrategyDTO();
        Integer cityId = createDTO.getCityId();
        String title = createDTO.getTitle();
        String operateUser = createDTO.getOperateUser();
        String locationName = createDTO.getLocationName();
        String driverName = createDTO.getDriverName();
        Boolean allocationFlag = createDTO.getAllocationFlag();
        Long toLocationId = createDTO.getToLocationId();
        String toLocationName = createDTO.getToLocationName();
        Integer toWarehouseId = createDTO.getToWarehouseId();
        String toWarehouseName = createDTO.getToWarehouseName();

        List<OutStockOrderItemPO> lstOrderItems = new ArrayList<>();
        for (OutStockOrderPO order : orders) {
            splitBatchTaskByPassageBL.setVirtualSecondPickTakeLocation(wavesStrategyDTO.getPassPickType(), order,
                toLocationId, toLocationName);
            lstOrderItems.addAll(order.getItems());
        }

        // 获取仓库配置
        Integer warehouseId = wavesStrategyDTO.getWarehouseId();
        WarehouseConfigDTO warehouseConfigDTO = globalCache.getWarehouseConfigDTO(warehouseId);

        List<WaveCreateDTO> totalWaveCreateDTO = new ArrayList<>();

        // createBatchLocationLargePickCargoBL.setTotalItemPackageLocationByAssociateLocation(lstOrderItems,
        // wavesStrategyDTO, warehouseConfigDTO, warehouseId);

        // 根据类目或者货位，查找对应的通道策略，并拼接成生拣货任务的对象
        List<WaveCreateDTO> lstCreateDTO = processPassageByCategory(batchBO, orders, lstOrderItems, wavesStrategyDTO,
            cityId, title, operateUser, locationName, driverName, allocationFlag, warehouseConfigDTO, createDTO);
        if (!CollectionUtils.isEmpty(lstCreateDTO)) {
            totalWaveCreateDTO.addAll(lstCreateDTO);
        }

        /**
         * 其他没有匹配到规则的订单项，单独生一个拣货任务（二次分拣，一个车次一个拣货任务）
         */
        List<OutStockOrderPO> lstOtherOrders =
            splitBatchTaskByPassageBL.getOtherOrdersByPassageItems(orders, lstOrderItems, totalWaveCreateDTO);

        List<WaveCreateDTO> otherWaveCreateList =
            WaveCreateDTOConvertor.createWaveCreateDTO(wavesStrategyDTO, lstOtherOrders, createDTO);

        if (!CollectionUtils.isEmpty(otherWaveCreateList)) {
            totalWaveCreateDTO.addAll(otherWaveCreateList);
        }

        LOG.info("创建波次列表的信息为：{}", JSON.toJSONString(totalWaveCreateDTO));
        // 根据拆分过的订单，再生成拣货任务
        for (WaveCreateDTO waveDTO : totalWaveCreateDTO) {
            // 指定出库位（虚仓二次分拣时，为收货位，不是出库位）
            if (!Objects.equals(wavesStrategyDTO.getPassPickType(), PassagePickTypeEnum.虚仓二次分拣通道.getType())) {
                if (Objects.isNull(waveDTO.getToLocationId())) {
                    waveDTO.setToLocationId(toLocationId);
                    waveDTO.setToLocationName(toLocationName);
                }
            }
            waveDTO.setToWarehouseId(toWarehouseId);
            waveDTO.setToWarehouseName(toWarehouseName);
            processBatchTaskResult(waveDTO, batchBO.getBatchPO(), locationNames);
        }
    }

    /**
     * 根据类目查找所有符合条件的通道策略
     *
     * @see SplitBatchTaskByPassageBL#processPassageByCategory
     */
    @Deprecated
    public List<WaveCreateDTO> processPassageByCategory(BatchBO batchBO, List<OutStockOrderPO> orders,
        List<OutStockOrderItemPO> lstOrderItems, WavesStrategyBO wavesStrategyDTO, Integer cityId, String title,
        String operateUser, String locationName, String driverName, Boolean allocationFlag,
        WarehouseConfigDTO warehouseConfigDTO, WaveCreateDTO oriCreateDTO) {
        List<WaveCreateDTO> lstCreateDTO = new ArrayList<>();
        List<OutStockOrderItemPO> lstAdded = new ArrayList<>();
        // 播种任务集合
        List<SowTaskPO> lstSowTaskPO = new ArrayList<>();

        List<SowOrderPO> lstSowOrders = new ArrayList<>();

        // 获取仓库配置
        Integer warehouseId = wavesStrategyDTO.getWarehouseId();

        // 根据仓库Id查找对应的仓库通道类型
        Byte pickType = null;
        List<PassageDTO> lstPassStrategyDTO = null;
        if (Objects.equals(wavesStrategyDTO.getPassPickType(), PassagePickTypeEnum.虚仓二次分拣通道.getType())) {
            pickType = PassagePickTypeEnum.虚仓二次分拣通道.getType();
            lstPassStrategyDTO =
                Collections.singletonList(splitBatchTaskByPassageBL.createVirtualWarehouseSecondPickPassage());
        } else {
            pickType = iPassageService.getPassageTypeByWarehouseId(warehouseId);
            if (pickType == null) {
                LOG.info(String.format("没有配置通道！仓库Id：%s", warehouseId));
                return lstCreateDTO;
            }
            // 1、根据类目或货位查询满足条件的通道配置
            lstPassStrategyDTO = splitBatchTaskByPassageBL.getPassageDTOS(lstOrderItems, warehouseId, pickType);
        }

        LOG.info("当前仓库的通道类型：{}", pickType);
        LOG.info("找到通道配置：{}", JSON.toJSONString(lstPassStrategyDTO));

        // 2、如果开启播种，查找当前仓库所有集货区，按数量平均分配
        List<GoodsCollectionLocationBO> locationList =
            sowCalculationBL.findSowCollectLocation(batchBO, lstPassStrategyDTO, wavesStrategyDTO);
        Map<String, Boolean> needReCheckMap =
            splitBatchTaskByPassageBL.checkIsRandomRecheck(orders, warehouseId, cityId);
        // 3、遍历满足条件的通道配置，查找符合条件的订单
        if (!CollectionUtils.isEmpty(lstPassStrategyDTO)) {
            for (PassageDTO passageDTO : lstPassStrategyDTO) {
                // ((!wavesStrategyDTO.getIsMultiOutBound() || !wavesStrategyDTO.getIsOpenSecondSort()) &&
                // Objects.equals(passageDTO.getSowType(),SowTypeEnum.二次分拣.getType()))
                // if((!wavesStrategyDTO.getIsMultiOutBound())){
                // LOG.info("[多出库批次创建波次]单个出库批次或未开启二次分拣，二次分拣通道，则忽略该通道");
                // continue;
                // }
                // 根据通道策咯查找所有符合条件的订单详情 TODO 过滤
                List<OutStockOrderItemPO> lstItems = splitBatchTaskByPassageBL
                    .getOutStockOrderItemByPassage(lstOrderItems, pickType, lstAdded, passageDTO);
                if (CollectionUtils.isEmpty(lstItems)) {
                    continue;
                }

                //
                // 合并开启货位库存和 拣货 合并订单项 到这里面
                // lstItems = MergeSplitItemConvertor.mergerItemListByUuid(lstItems, lstOrderItems, lstAdded,
                // warehouseConfigDTO, wavesStrategyDTO, passageDTO);

                // 进通道之后，根据 是否开启整件拆零 和 是否是大件 重新分配货位，并过滤掉单项整件的
                lstItems = createBatchLocationLargePickCargoBL.setPackageLocationByAssociateLocation(lstItems,
                    lstOrderItems, wavesStrategyDTO, warehouseConfigDTO);

                // 据通道，把订单项拆分，然后合并成新的订单对象
                List<OutStockOrderPO> lstOrders =
                    OrderRebuildUtil.getOrdersByPassageItems(orders, lstItems, warehouseConfigDTO, wavesStrategyDTO);

                // 如果通道开启播种或分区分单，处理播种信息（按配置格子数量，拆分拣货任务）

                // 获取通道下配置的集货位信息
                List<GoodsCollectionLocationBO> passageCollectionList =
                    splitBatchTaskByPassageBL.getPassageCollectLocationList(locationList, passageDTO, wavesStrategyDTO);
                LOG.info("过滤后的集货位为:{}", JSON.toJSONString(passageCollectionList));
                if ((passageDTO.getSowType() != null && passageDTO.getSowType() != SowTypeEnum.不开启.getType())) {
                    // TODO 这里的orders是不是有问题？
                    splitBatchTaskByPassageBL.createReRecheckOrderRecord(orders, warehouseId, cityId);
                    processSowTask(lstOrders, wavesStrategyDTO, passageDTO, title, operateUser, cityId,
                        warehouseConfigDTO, batchBO, lstCreateDTO, passageCollectionList, lstSowTaskPO, locationName,
                        lstSowOrders, driverName, allocationFlag, oriCreateDTO);
                } else {
                    List<OutStockOrderPO> notLstOrders =
                        randomCreateProcess(needReCheckMap, warehouseId, lstOrders, wavesStrategyDTO, passageDTO, title,
                            operateUser, cityId, warehouseConfigDTO, batchBO, lstCreateDTO, passageCollectionList,
                            lstSowTaskPO, locationName, lstSowOrders, driverName, allocationFlag, oriCreateDTO);
                    // 有多个出库批次，但不符合二次分拣通道，则根据出库批次拆分出库单
                    if (!CollectionUtils.isEmpty(notLstOrders)) {
                        if (wavesStrategyDTO.getIsMultiOutBound()) {
                            LOG.info("[多出库批次创建波次]未开启播种，通道：{}，出库批次号：{}，出库单号：{}", passageDTO.getPassageName(),
                                lstOrders.stream().map(OutStockOrderPO::getBoundNo).distinct()
                                    .collect(Collectors.toList()),
                                lstOrders.stream().map(OutStockOrderPO::getReforderno).distinct()
                                    .collect(Collectors.toList()));
                            splitBatchTaskByPassageBL.createWaveCreateByMultiOutBound(notLstOrders, wavesStrategyDTO,
                                title, operateUser, cityId, locationName, driverName, allocationFlag, passageDTO,
                                lstCreateDTO, oriCreateDTO);
                        } else {
                            WaveCreateDTO createDTO = splitBatchTaskByPassageBL.getWaveCreateDTOByPassage(notLstOrders,
                                wavesStrategyDTO, passageDTO, title, operateUser, cityId, locationName, driverName,
                                allocationFlag, oriCreateDTO);
                            createDTO.setPassageDTO(passageDTO);
                            lstCreateDTO.add(createDTO);
                        }
                    }
                }
                lstAdded.addAll(lstItems);
            }
        }
        lstAdded.clear();

        // 4、按货位通道拣货，如果存在按订单拣货的任务，开始分拆
        if (PassagePickTypeEnum.按货位通道.getType() == pickType) {
            splitBatchTaskByPassageBL.mergeOrderByLocationPassage(orders, lstOrderItems, lstCreateDTO);
        }
        // 批量保存播种任务、播种任务订单关联信息
        sowManagerBL.insertSowTaskList(lstSowTaskPO);
        sowManagerBL.insertSowOrderList(lstSowOrders);
        // 记录操作日志
        orderTraceBL.insertSowTaskList(lstSowTaskPO);

        return lstCreateDTO;
    }

    /**
     * 抽核逻辑判断
     */
    @Deprecated
    public List<OutStockOrderPO> randomCreateProcess(Map<String, Boolean> needReCheckMap, Integer warehouseId,
        List<OutStockOrderPO> lstOrders, WavesStrategyBO wavesStrategyDTO, PassageDTO passageDTO, String title,
        String operateUser, Integer cityId, WarehouseConfigDTO warehouseConfigDTO, BatchBO batchBO,
        List<WaveCreateDTO> lstCreateDTO, List<GoodsCollectionLocationBO> locationList, List<SowTaskPO> sowTaskPOList,
        String locationName, List<SowOrderPO> sowOrderPOList, String driverName, Boolean allocationFlag,
        WaveCreateDTO oriCreateDTO) {
        if (CollectionUtils.isEmpty(needReCheckMap)) {
            return lstOrders;
        }

        List<OutStockOrderPO> notLstOrders = new ArrayList<>();
        List<OutStockOrderPO> newLstOrders = new ArrayList<>();
        lstOrders.forEach(it -> {
            Boolean needReCheck = needReCheckMap.get(it.getReforderno());
            if (needReCheck != null && needReCheck) {
                newLstOrders.add(it);
            } else {
                notLstOrders.add(it);
            }
        });
        if (!CollectionUtils.isEmpty(newLstOrders)) {
            splitBatchTaskByPassageBL.createReRecheckOrderRecord(newLstOrders, warehouseId, cityId);
            sowCalculationBL.processSowTask(newLstOrders, wavesStrategyDTO, passageDTO, title, operateUser, cityId,
                warehouseConfigDTO, batchBO, lstCreateDTO, locationList, sowTaskPOList, locationName, sowOrderPOList,
                driverName, allocationFlag, true, oriCreateDTO);
        }
        return notLstOrders;
    }

    /**
     * 处理播种信息（按配置格子数量，拆分拣货任务）
     */
    private void processSowTask(List<OutStockOrderPO> lstOrders, WavesStrategyBO wavesStrategyDTO,
        PassageDTO passageDTO, String title, String operateUser, Integer cityId, WarehouseConfigDTO warehouseConfigDTO,
        BatchBO batchBO, List<WaveCreateDTO> lstCreateDTO, List<GoodsCollectionLocationBO> lstLocations,
        List<SowTaskPO> lstSowTaskPO, String locationName, List<SowOrderPO> lstSowOrders, String driverName,
        Boolean allocationFlag, WaveCreateDTO oriCreateDTO) {
        sowCalculationBL.processSowTask(lstOrders, wavesStrategyDTO, passageDTO, title, operateUser, cityId,
            warehouseConfigDTO, batchBO, lstCreateDTO, lstLocations, lstSowTaskPO, locationName, lstSowOrders,
            driverName, allocationFlag, false, oriCreateDTO);
    }

    /**
     * 网格仓更新订单的波次信息
     */
    public void updateOrderWorkSetting(List<OutStockOrderPO> orderPOList, BatchPO batchPO) {
        if (CollectionUtils.isEmpty(orderPOList) || batchPO == null) {
            return;
        }
        orderPOList.forEach(p -> {
            p.setBatchId(batchPO.getId());
            p.setBatchno(batchPO.getBatchNo());
            if (!CollectionUtils.isEmpty(p.getItems())) {
                p.getItems().forEach(item -> {
                    item.setBatchId(batchPO.getId());
                    item.setBatchno(batchPO.getBatchNo());
                });
            }
        });

        // 1、更新订单
        outStockOrderStateBL.batchUpdateOutStockOrder(orderPOList);
        LOG.info("网格仓更新订单的波次信息：{}", JSON.toJSONString(
            orderPOList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList())));

        // 2、更新订单项
        List<OutStockOrderItemPO> orderItemPOList =
            orderPOList.stream().filter(p -> !CollectionUtils.isEmpty(p.getItems())).flatMap(p -> p.getItems().stream())
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orderItemPOList)) {
            outStockOrderItemMapper.batchUpdateOutStockOrderItemOfBatch(orderItemPOList);
        }

        // 3.通知订单中台
        // orderCenterBL.createWaveNotify(orderPOList, batchPO);
    }

    /**
     * 处理波次任务的结果
     *
     * @param createDTO 单个拣货任务对应的数据
     * @param batchPO 波次对象
     * @param locationNames 拣货任务出库位集合（同步给tms）
     * <AUTHOR>
     * @date 2018/4/2 16:45
     */
    public void processBatchTaskResult(WaveCreateDTO createDTO, BatchPO batchPO, List<String> locationNames) {
        List<BatchTaskPO> lstBatchTask = new ArrayList<>();
        List<BatchTaskItemDTO> lstBatchTaskItem = new ArrayList<>();
        List<OutStockOrderPO> lstNoLocationOrder = new ArrayList<>();
        List<OrderItemTaskInfoPO> lstOrderItemTask = new ArrayList<>();

        List<OutStockOrderPO> waveOrders = createDTO.getOrders();
        WavesStrategyBO wavesStrategyDTO = createDTO.getWavesStrategyDTO();
        PassageDTO passageDTO = createDTO.getPassageDTO();
        // 播种任务id和编号
        Long sowId = createDTO.getSowId();
        String sowNo = createDTO.getSowNo();
        // 是否需要拣货
        Boolean pickFlag = createDTO.getPickFlag();

        if (CollectionUtils.isEmpty(waveOrders)) {
            LOG.info("波次没有有效订单:{}，{}", JSON.toJSONString(wavesStrategyDTO), JSON.toJSONString(batchPO));
            return;
        }
        LOG.info("当前波次策略:{}", JSON.toJSONString(wavesStrategyDTO));
        LOG.info("当前波次订单:{}",
            JSON.toJSONString(waveOrders.stream().map(p -> p.getReforderno()).distinct().collect(Collectors.toList())));
        // 仓库是2.5还是3.0
        Byte scmVersionByWarehouseId =
            globalCache.getWarehouseConfigDTO(wavesStrategyDTO.getWarehouseId()).getScmVersion();

        CreateBatchTaskByOrderResultHelperBO resultHelperBO = null;
        if (PickingTypeEnum.订单拣货.getType() == wavesStrategyDTO.getPickingType()) {
            LOG.info("按订单拣货。。。");
            // 设置locationid 2.0/2.5只能使用推荐配置，3.0能开启货位库存或者使用推荐配置
            if (!WarehouseConfigConstants.SCM_VERSION_3.equals(scmVersionByWarehouseId)) {
                // 如果按线路顺序或者司机拣货，订单需要先根据线路排序，再根据线路顺序排序
                // 如果是按片区，根据订单传入顺序排序
                // 分组方式 1:按线路，2：按片区，3：按司机
                if (wavesStrategyDTO.isSettingWayAboutDriver()) {
                    waveOrders.forEach(p -> {
                        if (p.getRouteSequence() == null) {
                            p.setRouteSequence(0);
                        }
                        if (p.getRouteId() == null) {
                            p.setRouteId(0L);
                        }
                    });
                    // LOG.info(String.format("排序前结果：%s", JSON.toJSONString(waveOrders.stream().map(p ->
                    // p.getReforderno() + ":" + p.getRouteId() + "-" +
                    // p.getRouteSequence()).collect(Collectors.toList()))));
                    // waveOrders.sort(Comparator.comparing(OutStockOrderPO::getRouteSequence));
                    waveOrders.sort(Comparator.comparing(OutStockOrderPO::getRouteId)
                        .thenComparing(OutStockOrderPO::getRouteSequence));
                    // LOG.info(String.format("排序后结果：%s", JSON.toJSONString(waveOrders.stream().map(p ->
                    // p.getReforderno() + ":" + p.getRouteId() + "-" +
                    // p.getRouteSequence()).collect(Collectors.toList()))));
                }

            }
            // 分区分单按拣货组拆分
            if (passageDTO != null && (passageDTO.getSowType() == SowTypeEnum.分区分单播种.getType()
                || passageDTO.getSowType() == SowTypeEnum.分区分单不播种.getType()
                || passageDTO.getSowType() == SowTypeEnum.分区分单拣货.getType())) {
                resultHelperBO = batchTaskCreateBL.processBatchTaskByProduct(batchPO, waveOrders, wavesStrategyDTO,
                    scmVersionByWarehouseId, sowId, sowNo, createDTO);
                // 设置订单编号
                Map<Long, List<OutStockOrderPO>> orderMap =
                    waveOrders.stream().collect(Collectors.groupingBy(OutStockOrderPO::getId));
                resultHelperBO.getBatchTaskItemDTOList().forEach(batchTaskItemDTO -> {
                    List<OutStockOrderPO> orderPOS = orderMap.get(Long.valueOf(batchTaskItemDTO.getRefOrderId()));
                    if (!CollectionUtils.isEmpty(orderPOS)) {
                        batchTaskItemDTO.setRefOrderNo(orderPOS.get(0).getReforderno());
                    }
                });
            } else {
                // 按地址拆按订单拣货的拣货任务
                // Map<String, List<OutStockOrderPO>> orderGroupMap =
                // splitBatchTaskBL.splitBatchTaskByOrder(createDTO);
                // for (Map.Entry<String, List<OutStockOrderPO>> entry : orderGroupMap.entrySet()) {
                // processBatchTaskByOrder(batchPO, entry.getValue(), lstBatchTask, lstBatchTaskItem, wavesStrategyDTO,
                // lstOrderItemTask, createDTO);

                resultHelperBO = batchTaskCreateBL.createBatchTaskByOrder(batchPO, wavesStrategyDTO, createDTO);
            }
        } else {
            LOG.info("按产品拣货。。。");
            resultHelperBO = batchTaskCreateBL.processBatchTaskByProduct(batchPO, waveOrders, wavesStrategyDTO,
                scmVersionByWarehouseId, sowId, sowNo, createDTO);
        }

        resultHelperBO.resetBatchListInfo(lstBatchTask, lstBatchTaskItem, lstNoLocationOrder, lstOrderItemTask);

        // LOG.info(String.format("波次：%s，开始设置订单顺序…", batchPO.getBatchNo()));

        // 组装需要更新订单 包括2.5的
        List<OutStockOrderPO> updateOutStockOrderPOS = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(waveOrders)) {
            updateOutStockOrderPOS.addAll(waveOrders);
        }
        if (!CollectionUtils.isEmpty(lstNoLocationOrder)) {
            updateOutStockOrderPOS.addAll(lstNoLocationOrder);
        }

        if (!Objects.equals(batchPO.getBatchType(), BatchTypeEnum.微酒.getType())
            || !BatchTypeEnum.isApplyOrderBatch(batchPO.getBatchType())) {
            if (lstBatchTask.stream()
                .anyMatch(p -> Objects.equals(p.getPickingType(), PickingTypeEnum.订单拣货.getType()))) {
                // 如果按订单拣货，且是3.0，调TMS接口，获取订单预排序顺序
                // if (WarehouseConfigConstants.SCM_VERSION_3.equals(scmVersionByWarehouseId)) {
                updateOutStockOrderPOS = updatePreOrderSequence(batchPO.getWarehouseId(), updateOutStockOrderPOS);
                // LOG.info(String.format("排序后结果：%s", JSON.toJSONString(updateOutStockOrderPOS)));
                // }
            }
        }

        List<BatchTaskItemPO> batchTaskItemPOs =
            BatchTaskConvertor.convertToListBatchTaskItemPO(updateOutStockOrderPOS, lstBatchTask, lstBatchTaskItem);

        // LOG.info(String.format("波次：%s，结束设置订单顺序，详细信息：%s", batchPO.getBatchNo(), JSON.toJSONString(batchTaskItemPOs)));

        // 拣货任务生成时，取默认备货区
        if (!wavesStrategyDTO.getIsMultiOutBound()) {
            setBatchTaskToLocation(createDTO, batchPO, lstBatchTask);
        }

        recommendOutLocationBL.handlePickAndSowTaskOutStockLocation(lstBatchTask, updateOutStockOrderPOS,
            lstOrderItemTask, batchPO.getWarehouseId());

        // 设置波次任务的创建时间
        setBatchTaskOtherAttribute(lstBatchTask, createDTO);

        // 处理不需要拣货的拣货任务，将状态改为“已完成”
        processNotNeedPick(pickFlag, lstBatchTask, batchTaskItemPOs);

        // 设置分仓属性
        warehouseAllocationTypeManageBL.setBatchTaskWarehouseAllocationType(lstBatchTask, batchTaskItemPOs);

        // 保存波次任务
        batchTaskMapper.insertList(lstBatchTask);
        // -添加操作记录（新建拣货任务成功）
        orderTraceBL.insertBatchTaskTrace(batchPO.getCreateUser(), lstBatchTask);

        // 保存波次任务项
        Lists.partition(batchTaskItemPOs, 100).forEach(p -> {
            batchTaskItemMapper.insertList(p);
        });

        // 触发拣货任务项排序任务
        pickingTaskItemSortingBL.triggerPickingItemSorting(lstBatchTask, batchTaskItemPOs);

        // 保存订单项和拣货任务项的关联
        if (!CollectionUtils.isEmpty(lstOrderItemTask)) {
            orderItemTaskInfoMapper.insertBatch(lstOrderItemTask);
            // 保存订单项和拣货任务项的关联明细
            List<OrderItemTaskInfoDetailPO> lstOrderItemTaskDetail = new ArrayList<>();
            lstOrderItemTask.forEach(p -> {
                if (!CollectionUtils.isEmpty(p.getDetailList())) {
                    lstOrderItemTaskDetail.addAll(p.getDetailList());
                }
            });
            if (!CollectionUtils.isEmpty(lstOrderItemTaskDetail)) {
                Lists.partition(lstOrderItemTaskDetail, 100).forEach(p -> {
                    orderItemTaskInfoDetailMapper.insertBatch(p);
                });
            }
        }

        // 微酒生波次
        if (Objects.equals(batchPO.getBatchType(), BatchTypeEnum.微酒.getType())) {
            // 修改微酒订单状态
            List<Long> orderIds =
                updateOutStockOrderPOS.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
            outStockOrderBL.updateOrderByWine(orderIds, batchPO.getId(), batchPO.getBatchNo(),
                TaskStateEnum.分拣中.getType());
        } else if (BatchTypeEnum.isApplyOrderBatch(batchPO.getBatchType())) {
            // 修改第三方订单状态
            List<Long> orderIds =
                updateOutStockOrderPOS.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
            thirdPartyOutStockBL.updateOrderByThirdParty(orderIds, batchPO.getId(), batchPO.getBatchNo(),
                TaskStateEnum.分拣中.getType());
        } else {

            updateOrderInfo(updateOutStockOrderPOS, lstBatchTask, lstOrderItemTask, batchPO, createDTO);

            // 同步订单项打印顺序给tms
            orderPrintSequenceSyncMQ.send(this.getOrderPrintSequenceList(lstBatchTask, batchTaskItemPOs));
        }

        // 记录拣货位置
        List<String> lstTmpLocationNames =
            lstBatchTask.stream().filter(p -> !StringUtils.isEmpty(p.getToLocationName()))
                .map(p -> p.getToLocationName()).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(lstTmpLocationNames)) {
            locationNames.addAll(lstTmpLocationNames);
        }

        List<Integer> lstSorter = lstBatchTask.stream().filter(p -> p.getSorterId() != null).map(p -> p.getSorterId())
            .distinct().collect(Collectors.toList());
        // 发送消息到PDA
        lstSorter.forEach(p -> {
            // 消息推送
            orderTraceBL.pushTaskMsg(p);
            // orderTraceBL.sendPushMsg(p, 1);
        });

        // List<String> robotBatchTaskIds = lstBatchTask.stream().filter(m ->
        // BatchTaskPickPatternEnum.机器人拣货.getType().equals(m.getPickPattern())).map(BatchTaskPO ::
        // getId).collect(Collectors.toList());
        // notifyWCSBL.createWCSTaskByIds(robotBatchTaskIds, createDTO.getOperateUser());
    }

    private void updateOrderInfo(List<OutStockOrderPO> updateOutStockOrderPOS, List<BatchTaskPO> batchTaskPOList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, BatchPO batchPO, WaveCreateDTO createDTO) {

        List<OutStockOrderItemPO> pickSortOrderItemList = SplitWaveOrderUtil
            .setPickSortingOutStockLocation(batchTaskPOList, updateOutStockOrderPOS, orderItemTaskInfoPOList);

        if (CollectionUtils.isEmpty(pickSortOrderItemList)) {
            updateOrderItem(updateOutStockOrderPOS, batchPO);
            // 更新订单信息 batchId batchno
            updateOrderState(updateOutStockOrderPOS, batchPO, createDTO.getOperateUserId());
            return;
        }

        updateOrderItem(updateOutStockOrderPOS, batchPO);

        updateOutStockOrderItemToLocation(pickSortOrderItemList, batchPO);
        // 更新订单信息 batchId batchno
        updateOrderState(updateOutStockOrderPOS, batchPO, createDTO.getOperateUserId());

    }

    /**
     * 获取订单项打印顺序
     */
    public List<OrderPrintSequenceDTO> getOrderPrintSequenceList(List<BatchTaskPO> lstBatchTask,
        List<BatchTaskItemPO> batchTaskItemPOs) {
        List<OrderPrintSequenceDTO> printSequenceDTOList = new ArrayList<>();
        lstBatchTask.forEach(batchTask -> {
            // 按订单拣货的拣货任务
            if (null != batchTask.getPickingType()
                && WavesStrategyConstants.PICKINGTYPE_ORDER == batchTask.getPickingType().intValue()) {
                // 找到拣货任务项
                List<BatchTaskItemPO> batchTaskItemPOList = batchTaskItemPOs.stream()
                    .filter(p -> Objects.equals(p.getBatchTaskId(), batchTask.getId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(batchTaskItemPOList)) {

                    List<BatchTaskItemDTO> batchTaskItemDTOList = batchTaskItemPOList.stream().map(po -> {
                        BatchTaskItemDTO dto = new BatchTaskItemDTO();
                        BeanUtils.copyProperties(po, dto);
                        dto.setPickingType(batchTask.getPickingType());
                        dto.setOrderItemId(po.getOrderItemId());
                        dto.setWarehouseId(batchTask.getWarehouseId());
                        return dto;
                    }).collect(Collectors.toList());

                    // 给拣货任务项进行排序
                    List<BatchTaskItemDTO> sequenceBatchTaskItemDTOList =
                        batchOrderTaskBL.processTaskItemIndex(batchTaskItemDTOList);
                    if (!CollectionUtils.isEmpty(sequenceBatchTaskItemDTOList)) {

                        // 按订单分组，加上序号
                        Map<String, List<BatchTaskItemDTO>> orderMap =
                            sequenceBatchTaskItemDTOList.stream().filter(p -> p.getRefOrderId() != null)
                                .collect(Collectors.groupingBy(p -> p.getRefOrderId()));
                        if (null != orderMap) {
                            orderMap.forEach((orderId, itemDTOS) -> {
                                Integer sequence = 1;
                                for (BatchTaskItemDTO item : itemDTOS) {
                                    Long businessId = orderId != null ? Long.valueOf(orderId) : null;
                                    Long businessItemId = item.getOrderItemId();
                                    if (printSequenceDTOList.stream()
                                        .anyMatch(p -> Objects.equals(p.getBusinessId(), businessId)
                                            && Objects.equals(p.getBusinessItemId(), businessItemId))) {
                                        continue;
                                    }
                                    OrderPrintSequenceDTO orderPrintSequenceDTO = new OrderPrintSequenceDTO();
                                    orderPrintSequenceDTO.setBusinessId(businessId);
                                    orderPrintSequenceDTO.setBusinessItemId(businessItemId);
                                    orderPrintSequenceDTO.setSequnce(sequence++);
                                    printSequenceDTOList.add(orderPrintSequenceDTO);
                                }
                            });
                        }
                    }
                }
            }
        });
        return printSequenceDTOList;
    }

    /**
     * 更新订单状态
     */
    public void updateOrderState(List<OutStockOrderPO> updateOutStockOrderPOS, BatchPO batchPO, Integer optUserId) {
        List<Long> orderIds =
            updateOutStockOrderPOS.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());
        // 查找订单项全部生成了拣货任务的订单id
        List<Long> updateOrderIds = outStockOrderMapper.findOrderIdByItemState(orderIds);
        if (!CollectionUtils.isEmpty(updateOrderIds)) {
            LOG.info("更新的订单id：{}", JSON.toJSONString(updateOrderIds));
            List<OutStockOrderPO> updateOrderList =
                updateOutStockOrderPOS.stream().filter(p -> updateOrderIds.contains(p.getId())).map(p -> {
                    OutStockOrderPO outStockOrderPO = new OutStockOrderPO();
                    outStockOrderPO.setId(p.getId());
                    outStockOrderPO.setBatchId(batchPO.getId());
                    outStockOrderPO.setBatchno(batchPO.getBatchNo());
                    outStockOrderPO.setState((int)OutStockOrderStateEnum.待拣货.getType());
                    return outStockOrderPO;
                }).collect(Collectors.toList());
            // 更新订单信息 batchId batchno
            int count = outStockOrderStateBL.batchUpdateOutStockOrder(updateOrderList);

            // orderCenterBL.createWaveNotify(updateOutStockOrderPOS, batchPO);
            // notifyTmsBatchCreateBL.notifyTmsOrderCreateBatch(updateOutStockOrderPOS, batchPO, optUserId);
            // if (count == 0) {
            // LOG.info("单据状态可能已经变更：{}", JSON.toJSONString(updateOrderList));
            // throw new BusinessValidateException("单据状态可能已经变更，请刷新重试");
            // }

        }
    }

    private void updateOutStockOrderItemToLocation(List<OutStockOrderItemPO> orderItemPOList, BatchPO batchPO) {
        if (CollectionUtils.isEmpty(orderItemPOList)) {
            return;
        }
        final List<BatchUpdateOutStockOrderItemPO> batchUpdateOutStockOrderItemPOList =
            mergeItem(orderItemPOList, batchPO);
        if (CollectionUtils.isEmpty(batchUpdateOutStockOrderItemPOList)) {
            return;
        }

        // 这里一个订单项如果被分到多个拣货任务（比如整件拆零），则会以最后一次的更新为准。
        // 更新订单项信息 batchTaskNo batchTaskId batchNo batchId batchTaskItemId
        for (List<BatchUpdateOutStockOrderItemPO> p : Lists.partition(batchUpdateOutStockOrderItemPOList, 100)) {
            outStockOrderItemMapper.batchUpdateOutStockOrderItemLocation(p);
        }
    }

    /**
     * 更新订单项信息 batchTaskNo batchTaskId batchNo batchId batchTaskItemId
     */
    @Deprecated
    private void updateOrderItem(List<OutStockOrderPO> updateOutStockOrderPOS, BatchPO batchPO) {
        List<OutStockOrderItemPO> lstOrderItems = new ArrayList<>();
        updateOutStockOrderPOS.forEach(e -> {
            if (e != null && !CollectionUtils.isEmpty(e.getItems())) {
                lstOrderItems.addAll(e.getItems());
            }
        });
        final List<BatchUpdateOutStockOrderItemPO> batchUpdateOutStockOrderItemPOList =
            mergeItem(lstOrderItems, batchPO);
        if (!CollectionUtils.isEmpty(batchUpdateOutStockOrderItemPOList)) {
            List<BatchUpdateOutStockOrderItemPO> filteredBatchUpdateOutStockOrderItemPO =
                filterBatchUpdateOutStockOrderItemPO(batchUpdateOutStockOrderItemPOList);
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(filteredBatchUpdateOutStockOrderItemPO)) {
                return;
            }

            // 这里一个订单项如果被分到多个拣货任务（比如整件拆零），则会以最后一次的更新为准。
            // 更新订单项信息 batchTaskNo batchTaskId batchNo batchId batchTaskItemId
            int count = 0;
            for (List<BatchUpdateOutStockOrderItemPO> p : Lists.partition(filteredBatchUpdateOutStockOrderItemPO,
                100)) {
                int tempCount = outStockOrderItemMapper.forceBatchUpdateOutStockOrderItemOfSowTask(p);
                count += tempCount;
            }
            if (count == 0) {
                LOG.info("单据项状态可能已经变更：{}", JSON.toJSONString(filteredBatchUpdateOutStockOrderItemPO));
                // throw new BusinessValidateException("单据项状态可能已经变更，请刷新重试");
            }
        }
    }

    private List<BatchUpdateOutStockOrderItemPO> mergeItem(List<OutStockOrderItemPO> lstOrderItems, BatchPO batchPO) {
        final List<BatchUpdateOutStockOrderItemPO> batchUpdateOutStockOrderItemPOList = new ArrayList<>();
        lstOrderItems.forEach(input -> {
            // 一个订单项被分到不同拣货任务和播种任务时，合并信息
            if (batchUpdateOutStockOrderItemPOList.stream().anyMatch(p -> Objects.equals(p.getId(), input.getId()))) {
                BatchUpdateOutStockOrderItemPO oldUpdatePO = batchUpdateOutStockOrderItemPOList.stream()
                    .filter(p -> Objects.equals(p.getId(), input.getId())).findFirst().get();
                if (StringUtils.isEmpty(oldUpdatePO.getBatchtaskId())) {
                    oldUpdatePO.setBatchtaskId(input.getBatchtaskId());
                    oldUpdatePO.setBatchtaskno(input.getBatchtaskno());
                    oldUpdatePO.setBatchTaskItemId(input.getBatchTaskItemId());
                }
                if (oldUpdatePO.getSowTaskId() == null) {
                    oldUpdatePO.setSowTaskId(input.getSowTaskId());
                    oldUpdatePO.setSowTaskNo(input.getSowTaskNo());
                    oldUpdatePO.setSowOrderId(input.getSowOrderId());
                    oldUpdatePO.setSowTaskItemId(input.getSowTaskItemId());
                }
                if (StringUtils.isEmpty(oldUpdatePO.getBatchId())) {
                    oldUpdatePO.setBatchId(batchPO.getId());
                    oldUpdatePO.setBatchno(batchPO.getBatchNo());
                }
            } else {
                BatchUpdateOutStockOrderItemPO batchUpdateOutStockOrderItemPO = new BatchUpdateOutStockOrderItemPO();
                batchUpdateOutStockOrderItemPO.setId(input.getId());
                batchUpdateOutStockOrderItemPO.setBatchtaskId(input.getBatchtaskId());
                batchUpdateOutStockOrderItemPO.setBatchtaskno(input.getBatchtaskno());
                batchUpdateOutStockOrderItemPO.setSowTaskId(input.getSowTaskId());
                batchUpdateOutStockOrderItemPO.setSowTaskNo(input.getSowTaskNo());
                batchUpdateOutStockOrderItemPO.setBatchId(batchPO.getId());
                batchUpdateOutStockOrderItemPO.setBatchno(batchPO.getBatchNo());
                batchUpdateOutStockOrderItemPO.setSowOrderId(input.getSowOrderId());
                batchUpdateOutStockOrderItemPO.setBatchTaskItemId(input.getBatchTaskItemId());
                batchUpdateOutStockOrderItemPO.setSowTaskItemId(input.getSowTaskItemId());
                batchUpdateOutStockOrderItemPO.setLocationId(input.getLocationId());
                batchUpdateOutStockOrderItemPO.setLocationName(input.getLocationName());
                batchUpdateOutStockOrderItemPOList.add(batchUpdateOutStockOrderItemPO);
            }
        });

        return batchUpdateOutStockOrderItemPOList;
    }

    private List<BatchUpdateOutStockOrderItemPO>
        filterBatchUpdateOutStockOrderItemPO(List<BatchUpdateOutStockOrderItemPO> batchUpdateOutStockOrderItemPOList) {
        if (CollectionUtils.isEmpty(batchUpdateOutStockOrderItemPOList)) {
            return Collections.emptyList();
        }
        List<Long> orderItemIds = batchUpdateOutStockOrderItemPOList.stream().map(BatchUpdateOutStockOrderItemPO::getId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderItemPO> outStockOrderItemPOList =
            outStockOrderItemQueryBL.findOutStockOrderItemList(orderItemIds);
        Map<Long, OutStockOrderItemPO> orderItemBatchNoMap =
            outStockOrderItemPOList.stream().collect(Collectors.toMap(OutStockOrderItemPO::getId, v -> v));
        batchUpdateOutStockOrderItemPOList = batchUpdateOutStockOrderItemPOList.stream().filter(m -> {
            OutStockOrderItemPO existOrderItem = orderItemBatchNoMap.get(m.getId());
            if (Objects.isNull(existOrderItem)) {
                return Boolean.TRUE;
            }
            if (StringUtils.isBlank(existOrderItem.getBatchno())) {
                return Boolean.TRUE;
            }
            if (Objects.isNull(existOrderItem.getSowTaskItemId())) {
                return Boolean.TRUE;
            }
            if (m.getBatchno().equals(existOrderItem.getBatchno())) {
                return Boolean.TRUE;
            }

            return Boolean.FALSE;
        }).collect(Collectors.toList());

        return batchUpdateOutStockOrderItemPOList;
    }

    /**
     * 处理不需要拣货的拣货任务，将状态改为“已完成”
     */
    private void processNotNeedPick(Boolean pickFlag, List<BatchTaskPO> lstBatchTask,
        List<BatchTaskItemPO> batchTaskItemPOs) {
        if (!pickFlag) {
            if (!CollectionUtils.isEmpty(lstBatchTask)) {
                lstBatchTask.forEach(p -> {
                    p.setTaskState(TaskStateEnum.已完成.getType());
                    p.setNeedPick(ConditionStateEnum.否.getType());
                });
            }
            if (!CollectionUtils.isEmpty(batchTaskItemPOs)) {
                batchTaskItemPOs.forEach(p -> {
                    p.setTaskState(TaskItemStateEnum.已完成.getType());
                    p.setOverSortCount(p.getUnitTotalCount());
                });
            }
        }
    }

    /**
     * 3.0且按订单拣货的，调TMS接口，更新订单预排序
     */
    private List<OutStockOrderPO> updatePreOrderSequence(Integer warehouseId, List<OutStockOrderPO> lstOrders) {
        List<String> lstOrderNos =
            lstOrders.stream().map(p -> p.getReforderno()).distinct().collect(Collectors.toList());
        // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包
        try {
            // List<String> lstSequence =
            // iDeliveryAddressSequenceService.querySequenceByBusinessNo(lstOrderNos, warehouseId);
            // LOG.info(String.format("3.0按订单生成波次，获取TMS预排序结果，单号：%s，结果：%s", JSON.toJSONString(lstOrderNos),
            // JSON.toJSONString(lstSequence)));
            // if (!CollectionUtils.isEmpty(lstSequence)) {
            // lstOrders = getOrderByIndex(lstOrders, lstSequence, false);
            // }
        } catch (Exception oe) {
            LOG.info("3.0获取TMS订单预排序失败！参数：" + JSON.toJSONString(lstOrderNos), oe);
        }
        return lstOrders;
    }

    /**
     * 设置拣货任务创建时间
     *
     * @param lstBatchTask
     */
    private void setBatchTaskOtherAttribute(List<BatchTaskPO> lstBatchTask, WaveCreateDTO createDTO) {
        Calendar calendar = Calendar.getInstance();
        if (CollectionUtils.isEmpty(lstBatchTask)) {
            return;
        }
        lstBatchTask.forEach(batchTask -> {
            batchTask.setCreateTime(calendar.getTime());
            calendar.add(Calendar.SECOND, 1);
            batchTask.setRemark(createDTO.getDriverName());
        });
    }

    /**
     * 处理按产品拣货的拣货任务
     *
     * @param batchPO 波次对象
     * @param lstBatchTask 存放新建拣货任务的集合
     * @param lstBatchTaskItem 存放新建拣货任务项的集合
     * @param lstNoLocationOrder 找不到货位的订单集合
     * @param waveOrders 拣货任务关联的订单集合
     * @param wavesStrategyDTO 拆分波次的条件
     * @param scmVersionByWarehouseId 仓库版本
     * @param sowId 播种任务id
     * @param sowNo 播种任务编号
     * @param createDTO 生成拣货任务需要的数据对象
     * @param lstOrderItemTask 存放新建拣货任务项和订单项关联表的集合
     * @return
     */
    @Deprecated
    public List<OutStockOrderPO> processBatchTaskByProduct(BatchPO batchPO, List<BatchTaskPO> lstBatchTask,
        List<BatchTaskItemDTO> lstBatchTaskItem, List<OutStockOrderPO> lstNoLocationOrder,
        List<OutStockOrderPO> waveOrders, WavesStrategyBO wavesStrategyDTO, Byte scmVersionByWarehouseId, Long sowId,
        String sowNo, WaveCreateDTO createDTO, List<OrderItemTaskInfoPO> lstOrderItemTask) {
        if (PickingGroupStrategyEnum.货位.getType() == wavesStrategyDTO.getPickingGroupStrategy()
            || PickingGroupStrategyEnum.货区.getType() == wavesStrategyDTO.getPickingGroupStrategy()) {
            // if (WarehouseConfigConstants.SCM_VERSION_2_5.equals(scmVersionByWarehouseId)) {
            // //获取商品无货位的订单，waveOrders过滤无货位订单项
            // lstNoLocationOrder = getNoLocationOrder(waveOrders);
            // LOG.info("noLocatioOrderPOS:{}", JSON.toJSONString(lstNoLocationOrder));
            // }
            lstNoLocationOrder = SplitWaveOrderUtil.getNoLocationOrder(waveOrders);
            LOG.info("noLocatioOrderPOS:{}", JSON.toJSONString(lstNoLocationOrder));
        }

        List<OutStockOrderItemPO> lstOrderItems = new ArrayList<>();
        waveOrders.forEach(p -> {
            lstOrderItems.addAll(p.getItems());
        });

        // 按分区拆分拣货任务
        if (SplitWaveOrderUtil.couldSplitBySortGroup(wavesStrategyDTO, createDTO)) {
            SortGroupSO so = new SortGroupSO();
            so.setWarehouseId(batchPO.getWarehouseId());
            // 根据拣货分组策略 1货区 2货位 3类目
            so.setGroupType(wavesStrategyDTO.getPickingGroupStrategy());
            // 分区标识
            so.setFlag(SortGroupFlagEnum.分区拣货.getType());
            so.setState(ConditionStateEnum.是.getType());
            PageList<SortGroupListDTO> listGroup = iSortGroupService.listGroup(so);
            if (!listGroup.getDataList().isEmpty()) {
                // 如果按类目分组，获取一二级类目ID
                if (WavesStrategyConstants.PICKINGGROUPSTRATEGY_CATEGORY == wavesStrategyDTO.getPickingGroupStrategy()
                    .intValue()) {
                    splitBatchTaskByPassageBL.setCategoryId(lstOrderItems);
                }
                listGroup.getDataList().forEach(group -> {
                    Long groupId = group.getId();
                    SortGroupDTO groupDTO = iSortGroupService.getGroup(groupId);
                    List<String> lstIds = groupDTO.getGroupSettingList().stream().filter(p -> p.getSortId() != null)
                        .map(SortGroupSettingDTO::getSortId).distinct().collect(Collectors.toList());
                    List<OutStockOrderItemPO> lstTmpItems = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(lstIds)) {
                        // 根据拣货分组策略 1货区 2货位 3类目
                        switch (wavesStrategyDTO.getPickingGroupStrategy().intValue()) {
                            case WavesStrategyConstants.PICKINGGROUPSTRATEGY_CARGOAREA:
                                LOG.info(String.format("按货区拣货，分区：%s", JSON.toJSONString(groupDTO)));
                                lstTmpItems = lstOrderItems.stream()
                                    .filter(p -> p.getAreaId() != null && lstIds.contains(p.getAreaId().toString()))
                                    .collect(Collectors.toList());
                                break;
                            case WavesStrategyConstants.PICKINGGROUPSTRATEGY_LOCATION:
                                LOG.info(String.format("按货位拣货，分区：%s", JSON.toJSONString(groupDTO)));
                                lstTmpItems = lstOrderItems.stream()
                                    .filter(
                                        p -> p.getLocationId() != null && lstIds.contains(p.getLocationId().toString()))
                                    .collect(Collectors.toList());
                                break;
                            // TODO 感觉这里有bug；lstTmpItems 上面都是重新赋值，这里没有。多个类目会越来越多
                            case WavesStrategyConstants.PICKINGGROUPSTRATEGY_CATEGORY:
                                LOG.info(String.format("按类目拣货，分区：%s", JSON.toJSONString(groupDTO)));
                                List<OutStockOrderItemPO> lstSecCategoryItems = lstOrderItems.stream()
                                    .filter(p -> p.getSecCategoryId() != null
                                        && lstIds.contains(p.getSecCategoryId().toString()))
                                    .collect(Collectors.toList());
                                if (!CollectionUtils.isEmpty(lstSecCategoryItems)) {
                                    lstTmpItems.addAll(lstSecCategoryItems);
                                }
                                List<Long> itemIds =
                                    lstTmpItems.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());
                                List<OutStockOrderItemPO> lstFirstCategoryItems = lstOrderItems.stream()
                                    .filter(p -> p.getFirstCategoryId() != null
                                        && lstIds.contains(p.getFirstCategoryId().toString())
                                        && !itemIds.contains(p.getId()))
                                    .collect(Collectors.toList());
                                if (!CollectionUtils.isEmpty(lstFirstCategoryItems)) {
                                    lstTmpItems.addAll(lstFirstCategoryItems);
                                }
                                break;
                            default:
                                break;
                        }
                    }
                    if (!CollectionUtils.isEmpty(lstTmpItems)) {
                        // 移除匹配到的订单项
                        lstOrderItems.removeAll(lstTmpItems);
                        processBatchTaskByOrderItem(group, batchPO, lstTmpItems, lstBatchTask, lstBatchTaskItem,
                            wavesStrategyDTO, createDTO, lstOrderItemTask);
                    }
                });
            } else {
                LOG.info(String.format("仓库%s没有找到分区！", batchPO.getWarehouseId()));
            }
        }
        PassageDTO passageDTO = createDTO.getPassageDTO();
        if (!lstOrderItems.isEmpty()) {
            // 剩余订单项与之前获取的无货区订单匹配
            List<Long> lstOrderItemIds = lstOrderItems.stream().map(OutStockOrderItemPO::getOutstockorderId).distinct()
                .collect(Collectors.toList());
            List<Long> lstNoLocationOrderIds =
                lstNoLocationOrder.stream().map(OutStockOrderPO::getId).collect(Collectors.toList());
            // 交集
            List<Long> intersection =
                lstOrderItemIds.stream().filter(id -> lstNoLocationOrderIds.contains(id)).collect(Collectors.toList());
            // 提取相同订单id的订单项
            List<OutStockOrderItemPO> intersectionOrders = lstOrderItems.stream()
                .filter(item -> intersection.contains(item.getOutstockorderId())).collect(Collectors.toList());
            // 无货区订单添加同订单id的订单项
            lstNoLocationOrder.forEach(order -> {
                if (intersection.contains(order.getId())) {
                    order.getItems().addAll(intersectionOrders.stream()
                        .filter(item -> item.getOutstockorderId().equals(order.getId())).collect(Collectors.toList()));
                }
            });

            lstOrderItems.removeAll(intersectionOrders);
            // 没有匹配到分区的，单独生一个拣货任务
            // if (lstOrderItems.size() > 0 && passageDTO != null && (passageDTO.getSowType() ==
            // SowTypeEnum.分区分单播种.getType() || passageDTO.getSowType() == SowTypeEnum.分区分单不播种.getType() ||
            // passageDTO.getSowType() == SowTypeEnum.分区分单拣货.getType())) {
            // processBatchTaskByOrderItem(null, batchPO, lstOrderItems, lstBatchTask, lstBatchTaskItem,
            // wavesStrategyDTO, passageName, null, null, createDTO, lstOrderItemTask);
            // } else
            if (!lstOrderItems.isEmpty()) {
                processBatchTaskByOrderItem(null, batchPO, lstOrderItems, lstBatchTask, lstBatchTaskItem,
                    wavesStrategyDTO, createDTO, lstOrderItemTask);
            }

        }
        // 单独按订单生成拣货任务
        if (!CollectionUtils.isEmpty(lstNoLocationOrder)) {
            WavesStrategyBO wavesStrategyDTONew = new WavesStrategyBO();
            BeanUtils.copyProperties(wavesStrategyDTO, wavesStrategyDTONew);
            wavesStrategyDTONew.setPickingType(PickingTypeEnum.订单拣货.getType());
            // if(passageDTO != null && (passageDTO.getSowType() == SowTypeEnum.分区分单播种.getType() ||
            // passageDTO.getSowType() == SowTypeEnum.分区分单不播种.getType() || passageDTO.getSowType() ==
            // SowTypeEnum.分区分单拣货.getType())) {
            // processBatchTaskByOrder(batchPO, lstNoLocationOrder, lstBatchTask, lstBatchTaskItem, wavesStrategyDTONew,
            // passageName, null, null, lstOrderItemTask);
            // } else {
            processBatchTaskByOrder(batchPO, lstNoLocationOrder, lstBatchTask, lstBatchTaskItem, wavesStrategyDTONew,
                lstOrderItemTask, createDTO);
            // }
        }
        lstOrderItems.clear();
        return lstNoLocationOrder;
    }

    /**
     * 设置备货区货位
     */
    public void setBatchTaskToLocation(WaveCreateDTO createDTO, BatchPO batchPO, List<BatchTaskPO> lstBatchTask) {
        if (createDTO.getSowNo() != null) {
            LOG.info("有播种任务的拣货任务不分配出库位：{}", createDTO.getSowNo());
            return;
        }
        // 指定出库位
        if (createDTO.getToLocationId() != null) {
            // 获取当前仓库实际的出库位
            List<Long> locationIdList = new ArrayList<>();
            locationIdList.add(createDTO.getToLocationId());
            List<LoactionDTO> loactionDTOList =
                batchOrderTaskBL.getRealToLocation(locationIdList, batchPO.getWarehouseId());
            if (!CollectionUtils.isEmpty(loactionDTOList)) {
                lstBatchTask.forEach(p -> {
                    p.setToLocationId(loactionDTOList.get(0).getId());
                    p.setToLocationName(loactionDTOList.get(0).getName());
                });
            }
            // 内配单周转区，查找内配单出库位
        } else if (createDTO.getAllocationFlag()) {
            LocationReturnDTO locationReturnDTO = getAllocation(batchPO);
            if (null != locationReturnDTO) {
                lstBatchTask.forEach(p -> {
                    p.setToLocationId(locationReturnDTO.getId());
                    p.setToLocationName(locationReturnDTO.getName());
                });
            }
            // 按自提点
        } else if (Objects.equals(createDTO.getBatchTaskType(), BatchTaskTypeEnum.按自提点.getType())
            && createDTO.getWorkSetting() != null) {
            // 获取当天的截止时间
            Date deadLine = LocalDateTimeUtil.getTodayDeadline(null, createDTO.getWorkSetting().getDeadline());
            LOG.info("获取当天的截止时间：{}", deadLine);
            if (deadLine != null && createDTO.getAddressId() != null) {
                // 获取自提点的周转箱编号
                String containerNo = batchNoGenerator.getAddressContainerNo(batchPO.getWarehouseId(), deadLine,
                    createDTO.getAddressId());
                lstBatchTask.forEach(p -> {
                    p.setToLocationId(Long.valueOf(containerNo));
                    p.setToLocationName(containerNo);
                });
            }
            // 根据线路或片区去查找对应的出库位
        } else if (batchPO.getOrderSelection() != null) {
            // 同一个波次备货区一样
            List<LocationInfoDTO> defaultLocationList = getDefaultLocation(createDTO, batchPO);
            if (!CollectionUtils.isEmpty(defaultLocationList)) {
                List<Long> locationIdList = defaultLocationList.stream().filter(p -> p.getLocationId() != null)
                    .map(p -> p.getLocationId()).distinct().collect(Collectors.toList());
                // 获取当前仓库实际的出库位
                List<LoactionDTO> loactionDTOList =
                    batchOrderTaskBL.getRealToLocation(locationIdList, batchPO.getWarehouseId());
                if (!CollectionUtils.isEmpty(loactionDTOList)) {
                    lstBatchTask.forEach(p -> {
                        p.setToLocationId(loactionDTOList.get(0).getId());
                        p.setToLocationName(loactionDTOList.get(0).getName());
                    });
                }
                LOG.info("拣货任务出库位为：{}", JSON.toJSONString(loactionDTOList));
            }
        }
    }

    /**
     * 获取内配单出库位
     *
     * @return
     */
    private LocationReturnDTO getAllocation(BatchPO batchPO) {
        // 查找内配单出库位所有货位
        LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
        locationQueryDTO.setCityId(batchPO.getOrgId());
        locationQueryDTO.setWarehouseId(batchPO.getWarehouseId());
        locationQueryDTO.setSubcategoryList(Arrays.asList(LocationEnum.内配单出库位.getType().byteValue()));
        List<LocationReturnDTO> locationList = iProductLocationService.listLocationByCondition(locationQueryDTO);
        if (CollectionUtils.isEmpty(locationList)) {
            return null;
        }
        return locationList.get(0);
    }

    private List<LocationInfoDTO> getDefaultLocation(WaveCreateDTO createDTO, BatchPO batchPO) {
        List<LocationInfoDTO> locationInfoDTOList = new ArrayList<>();
        // 按区域/按线路
        if (BatchCreateUtils.isRouteOrArea(createDTO.getWavesStrategyDTO())) {
            locationInfoDTOList = recommendOutLocationBL.getLocationByAreaOrRoute(batchPO);
            LOG.info("灰度获取出库位，结果为：{}", JSON.toJSONString(locationInfoDTOList));
        } else {
            if (StringUtils.isNotEmpty(createDTO.getLocationName())) {
                LOG.info(String.format("获取货位详细信息Param：%s", createDTO.getLocationName()));
                // 追加到指定车次
                if (createDTO.getLocationName().contains(",")) {
                    createDTO.setLocationName(
                        createDTO.getLocationName().substring(0, createDTO.getLocationName().indexOf(",")));
                }
                if (StringUtils.isNotEmpty(createDTO.getLocationName())) {
                    LocationInfoDTO locationDTO =
                        locationAreaService.getLocationByName(createDTO.getLocationName(), batchPO.getWarehouseId());
                    LOG.info(
                        String.format("获取货位详细信息：%s , %s", createDTO.getLocationName(), JSON.toJSONString(locationDTO)));
                    locationInfoDTOList.add(locationDTO);
                }
            }
        }
        return locationInfoDTOList;
    }

    /**
     * 组装新的订单项
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2018/4/17 15:10
     */
    private Map<String, List<OutStockOrderItemPO>> getOutStockOrderItemPOListByInventoryBak(
        List<OrderItemDTO> orderItemDTOS, List<OutStockOrderItemPO> lstOrderItems) {
        LOG.info("组装新的订单项：{}", JSON.toJSONString(lstOrderItems));
        Map<String, List<OutStockOrderItemPO>> resultMap = Maps.newHashMap();
        Map<Long, OutStockOrderItemPO> itemPOImmutableMap =
            lstOrderItems.stream().collect(Collectors.toMap(OutStockOrderItemPO::getId, (p) -> p));
        List<OutStockOrderItemPO> haveLocation = new ArrayList<>();
        List<OutStockOrderItemPO> noLocation = new ArrayList<>();
        for (OrderItemDTO orderItemDTO : orderItemDTOS) {
            OutStockOrderItemPO outStockOrderItemPO = itemPOImmutableMap.get(orderItemDTO.getId());

            OutStockOrderItemPO stockOrderItemPO = new OutStockOrderItemPO();
            BeanUtils.copyProperties(outStockOrderItemPO, stockOrderItemPO);
            stockOrderItemPO.setAreaId(orderItemDTO.getAreaId());
            stockOrderItemPO.setAreaName(orderItemDTO.getAreaName());
            stockOrderItemPO.setUnittotalcount(orderItemDTO.getPickUpCount() != null ? orderItemDTO.getPickUpCount()
                : orderItemDTO.getUnitTotalCount());
            BigDecimal[] pickUpCountRemainder =
                stockOrderItemPO.getUnittotalcount().divideAndRemainder(stockOrderItemPO.getSpecquantity());
            stockOrderItemPO.setPackagecount(pickUpCountRemainder[0]);
            stockOrderItemPO.setUnitcount(pickUpCountRemainder[1]);
            stockOrderItemPO.setCategoryname(outStockOrderItemPO.getCategoryname());
            stockOrderItemPO.setSubCategory(orderItemDTO.getSubCategory());
            stockOrderItemPO.setBatchTime(orderItemDTO.getBatchTime());
            stockOrderItemPO.setProductionDate(orderItemDTO.getProductionDate());
            stockOrderItemPO.setItemDetailId(orderItemDTO.getItemDetailId());
            stockOrderItemPO.setSecOwnerId(orderItemDTO.getSecOwnerId());
            if (orderItemDTO.getLocationId() != null) {
                stockOrderItemPO.setLocationId(orderItemDTO.getLocationId());
                stockOrderItemPO.setLocationName(orderItemDTO.getLocationName());
                haveLocation.add(stockOrderItemPO);
            } else {
                // 没有货位库存的产品设置小单位总数量
                if (null == stockOrderItemPO.getUnittotalcount()) {
                    stockOrderItemPO.setUnittotalcount(stockOrderItemPO.getPackagecount()
                        .multiply(stockOrderItemPO.getSpecquantity()).add(stockOrderItemPO.getUnitcount()));
                }
                stockOrderItemPO.setLocationId(null);
                stockOrderItemPO.setLocationName(null);
                noLocation.add(stockOrderItemPO);
            }
        }
        resultMap.put("haveLocation", haveLocation);
        resultMap.put("noLocation", noLocation);
        return resultMap;
    }

    /**
     * 按货区生成拣货任务
     *
     * @param batchPO
     * @param lstOrderItems
     * @param lstBatchTask
     * @param lstBatchTaskItem
     */
    // private void processBatchTaskByArea(BatchPO batchPO, List<OutStockOrderItemPO> lstOrderItems, List<BatchTaskPO>
    // lstBatchTask, List<BatchTaskItemDTO> lstBatchTaskItem, WavesStrategyDTO wavesStrategyDTO,
    // String passageName) {
    //
    // // 根据货位GroupBy
    // Map<Long, List<OutStockOrderItemPO>> orderByCategoryMap =
    // lstOrderItems.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getAreaId));
    // for (Long key : orderByCategoryMap.keySet()) {
    // List<OutStockOrderItemPO> orderItems = orderByCategoryMap.get(key);
    // processBatchTaskByOrderItem(null, batchPO, orderItems, lstBatchTask, lstBatchTaskItem, wavesStrategyDTO,
    // passageName);
    // }
    // }

    /**
     * 按货位生成拣货任务
     *
     * @param batchPO
     * @param lstOrderItems
     * @param lstBatchTask
     * @param lstBatchTaskItem
     */
    // private void processBatchTaskByLocation(BatchPO batchPO, List<OutStockOrderItemPO> lstOrderItems,
    // List<BatchTaskPO> lstBatchTask, List<BatchTaskItemDTO> lstBatchTaskItem, WavesStrategyDTO wavesStrategyDTO,
    // String passageName) {
    // // 根据货位GroupBy
    // Map<Long, List<OutStockOrderItemPO>> orderByCategoryMap =
    // lstOrderItems.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getLocationId));
    // for (Long key : orderByCategoryMap.keySet()) {
    // List<OutStockOrderItemPO> orderItems = orderByCategoryMap.get(key);
    // processBatchTaskByOrderItem(null, batchPO, orderItems, lstBatchTask, lstBatchTaskItem, wavesStrategyDTO,
    // passageName);
    // }
    // }

    /**
     * 按类目生成拣货任务
     *
     * @param batchPO
     * @param lstOrderItems
     * @param lstBatchTask
     * @param lstBatchTaskItem
     */
    // private void processBatchTaskByCategory(BatchPO batchPO, List<OutStockOrderItemPO> lstOrderItems,
    // List<BatchTaskPO> lstBatchTask, List<BatchTaskItemDTO> lstBatchTaskItem, WavesStrategyDTO wavesStrategyDTO,
    // String passageName) {
    // // 根据产品类目GroupBy
    // Map<String, List<OutStockOrderItemPO>> orderByCategoryMap =
    // lstOrderItems.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getCategoryname));
    // for (String key : orderByCategoryMap.keySet()) {
    // List<OutStockOrderItemPO> orderItems = orderByCategoryMap.get(key);
    // processBatchTaskByOrderItem(null, batchPO, orderItems, lstBatchTask, lstBatchTaskItem, wavesStrategyDTO,
    // passageName);
    // }
    // }

    /**
     * 处理按产品拣货（订单项维度）
     *
     * @param sortGroupListDTO 拣货分区id
     * @param batchPO 波次对象
     * @param waveOrderItems 拣货任务关联的订单项集合
     * @param lstBatchTask 存放新建拣货任务的集合
     * @param lstBatchTaskItem 存放新建拣货任务项的集合
     * @param wavesStrategyDTO 拆分波次的条件
     * @param createDTO 生成拣货任务需要的数据对象
     * @param lstOrderItemTask 存放新建拣货任务项和订单项关联表的集合
     */
    private void processBatchTaskByOrderItem(SortGroupListDTO sortGroupListDTO, BatchPO batchPO,
        List<OutStockOrderItemPO> waveOrderItems, List<BatchTaskPO> lstBatchTask,
        List<BatchTaskItemDTO> lstBatchTaskItem, WavesStrategyBO wavesStrategyDTO, WaveCreateDTO createDTO,
        List<OrderItemTaskInfoPO> lstOrderItemTask) {
        Long sowId = null;
        String sowNo = null;

        Optional<OutStockOrderItemPO> first =
            waveOrderItems.stream().filter(item -> item.getSowTaskId() != null).findFirst();
        if (first.isPresent()) {
            sowId = first.get().getSowTaskId();
            sowNo = first.get().getSowTaskNo();
        }
        PassageDTO passageDTO = createDTO.getPassageDTO();
        // 按订单拣货： 一个波次一个拣货任务
        // 按产品拣货：具体按产品类目、货位、货区来分
        BatchTaskPO batchTaskPO = new BatchTaskPO();
        batchTaskPO.setBatchTaskNo(UuidUtil.generator(batchPO.getWarehouseId(), BatchOrderTypeConstans.BATCHTASK_NO));
        batchTaskPO.setId(UuidUtil.generatorId());
        BigDecimal packageCount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.piecePackageNumberLimitType);
        batchTaskPO.setPackageAmount(packageCount);
        BigDecimal unitCount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.pieceUnitNumberLimitType);
        batchTaskPO.setUnitAmount(unitCount);
        BigDecimal orderCount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.orderCountLimitType);
        batchTaskPO.setOrderCount(orderCount.intValue());
        BigDecimal orderAmount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.orderAmountLimitType);
        batchTaskPO.setOrderAmount(orderAmount);
        BigDecimal skuCount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.skuCountLimitType);
        batchTaskPO.setSkuCount(skuCount.intValue());
        // 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
        batchTaskPO.setTaskState(TaskStateEnum.未分拣.getType());
        batchTaskPO.setOrgId(String.valueOf(batchPO.getOrgId()));
        batchTaskPO.setPickPattern(RobotPickConstants.getBatchTaskRobotPickType(createDTO, passageDTO));

        batchTaskHelper.handleSorterGroupBatchTask(sortGroupListDTO, batchTaskPO, sowId, wavesStrategyDTO);
        batchTaskHelper.setTaskWarehouseFeatureType(batchTaskPO, wavesStrategyDTO);
        batchTaskPO.setTaskAppendSequence((byte)0);

        if (Objects.nonNull(createDTO.getAppendSequence())) {
            batchTaskPO.setTaskAppendSequence(createDTO.getAppendSequence().byteValue());
        }

        if (BatchTaskPickPatternEnum.机器人拣货.getType().equals(batchTaskPO.getPickPattern())) {
            batchTaskPO.setSorter(RobotSorterConstants.ROBOT_NAME);
            batchTaskPO.setSorterId(RobotSorterConstants.ROBOT_ID);
        }

        batchTaskPO.setBatchId(batchPO.getId());
        batchTaskPO.setBatchNo(batchPO.getBatchNo());
        boolean hasAllotOrder = SplitWaveOrderUtil.hasAllotOrderByItem(createDTO.getOrders(), waveOrderItems);
        // 添加拣货属性和拣货方式
        String passageName = passageDTO != null ? passageDTO.getPassageName() : null;
        batchTaskHelper.setBatchTaskName(batchTaskPO, wavesStrategyDTO, passageName, createDTO, hasAllotOrder);
        batchTaskPO.setSowTaskId(sowId);
        batchTaskPO.setSowTaskNo(sowNo);
        // 是否需要拣货
        batchTaskPO.setNeedPick(ConditionStateEnum.是.getType());
        batchTaskPO.setKindOfPicking(
            Optional.ofNullable(createDTO.getKindOfPicking()).orElse(BatchTaskKindOfPickingConstants.DEFAULT));

        Byte pickingGroupStrategy = wavesStrategyDTO.getPickingGroupStrategy();
        if (pickingGroupStrategy.intValue() == WavesStrategyConstants.PICKINGGROUPSTRATEGY_CARGOAREA) {
            batchTaskPO.setLocationId(waveOrderItems.stream().findAny().get().getAreaId());
            batchTaskPO.setLocationName(waveOrderItems.stream().findAny().get().getAreaName());
        } else if (pickingGroupStrategy.intValue() == WavesStrategyConstants.PICKINGGROUPSTRATEGY_LOCATION) {
            batchTaskPO.setLocationId(waveOrderItems.stream().findAny().get().getLocationId());
            batchTaskPO.setLocationName(waveOrderItems.stream().findAny().get().getLocationName());
        } else if (pickingGroupStrategy.intValue() == WavesStrategyConstants.PICKINGGROUPSTRATEGY_CATEGORY) {
            batchTaskPO.setCategoryName(waveOrderItems.stream().findAny().get().getCategoryname());
        }

        // 处理二次分拣出库位逻辑
        setSecondSortBatchTaskLocation(batchTaskPO, wavesStrategyDTO, waveOrderItems);
        // 设置拣货任务所属仓库
        batchTaskPO.setWarehouseId(batchPO.getWarehouseId());
        // 拣货任务类型
        batchTaskPO.setBatchTaskType(
            createDTO.getBatchTaskType() == null ? BatchTaskTypeEnum.默认.getType() : createDTO.getBatchTaskType());
        batchTaskHelper.setTaskWarehouseFeatureType(batchTaskPO, wavesStrategyDTO);
        batchTaskPO.setTaskAppendSequence((byte)0);
        batchTaskHelper.handleSorterGroupBatchTask(sortGroupListDTO, batchTaskPO, sowId, wavesStrategyDTO);
        if (Objects.nonNull(createDTO.getAppendSequence())) {
            batchTaskPO.setTaskAppendSequence(createDTO.getAppendSequence().byteValue());
        }

        // 通道
        if (passageDTO != null) {
            batchTaskPO.setPassageId(passageDTO.getId());
            batchTaskPO.setPassageName(passageDTO.getPassageName());
        }
        lstBatchTask.add(batchTaskPO);

        // 接订单中台时，需通过TMS接口获取订单项的控货策略ID
        Map<Long, OrderTraceableItemDTO> orderTraceableItemMap = null;
        List<Long> businessItemIdList = waveOrderItems.stream().map(OutStockOrderItemPO::getBusinessItemId)
            .filter(Objects::nonNull).map(Long::valueOf).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(businessItemIdList)) {
            orderTraceableItemMap =
                iTransferOrderQueryService.selectOrderTraceableItemInfoByOrderItemIds(businessItemIdList).stream()
                    .filter(elem -> elem.getOrderItemId() != null && elem.getProductControlId() != null).collect(
                        Collectors.toMap(OrderTraceableItemDTO::getOrderItemId, Function.identity(), (k1, k2) -> k1));
            LOG.info("[生成波次]接订单中台时，查TMS的控货策略：{}", JSON.toJSONString(orderTraceableItemMap));
        }

        List<BatchTaskItemDTO> currBatchTaskItemList = new ArrayList<>();
        for (OutStockOrderItemPO q : waveOrderItems) {
            q.setBatchtaskId(batchTaskPO.getId());
            q.setBatchtaskno(batchTaskPO.getBatchTaskNo());
            // // 播种任务
            // q.setSowTaskId(sowId);
            // q.setSowTaskNo(sowNo);

            BatchTaskItemDTO itemDTO = new BatchTaskItemDTO();
            // 如果是分区分单则细分到订单
            if (passageDTO != null && (passageDTO.getSowType() == SowTypeEnum.分区分单播种.getType()
                || passageDTO.getSowType() == SowTypeEnum.分区分单不播种.getType()
                || passageDTO.getSowType() == SowTypeEnum.分区分单拣货.getType())) {
                itemDTO.setOrderItemId(q.getId());
                itemDTO.setRefOrderId(q.getOutstockorderId().toString());
                itemDTO.setRefOrderNo("");
            } else {
                // SCM-21027 临过期产品商城促销方案
                // 促销产品单独拆出一个拣货任务项。这样拣货的时候，可以区分 正品 和 促销产品
                if (lstBatchTaskItem.stream()
                    .anyMatch(p -> q.getProductIdentityKey().equals(p.getProductIdentityKey()))) {
                    itemDTO = lstBatchTaskItem.stream()
                        .filter(p -> q.getProductIdentityKey().equals(p.getProductIdentityKey())).findAny().get();

                    // 按产品拣货，不区分订单，将相同SKU货位及来源渠道的产品项合并
                    itemDTO.setUnitTotalCount(itemDTO.getUnitTotalCount().add(q.getUnittotalcount()));
                    itemDTO.setPackageCount(itemDTO.getPackageCount().add(q.getPackagecount()));
                    itemDTO.setUnitCount(itemDTO.getUnitCount().add(q.getUnitcount()));
                    // LOG.info(String.format("相同产品合并：%s", JSON.toJSONString(itemDTO)));
                    // 订单详情表中保存拣货任务项id
                    q.setBatchTaskItemId(itemDTO.getId());
                    // 订单项关联拣货任务项
                    addOrderItemTaskInfoPO(lstOrderItemTask, q, itemDTO, batchTaskPO);
                    continue;
                }
                // 按产品拣货，不需要将订单项与拣货任务关联。
                // 因为多个订单包含同一个产品，无法区分
                itemDTO.setRefOrderId("");
                itemDTO.setRefOrderNo("");
            }
            itemDTO.setBatchTaskId(batchTaskPO.getId());
            itemDTO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
            itemDTO.setId(UuidUtil.generatorId());
            itemDTO.setLocationId(q.getLocationId());
            itemDTO.setLocationName(q.getLocationName());
            itemDTO.setLocationCategory(q.getLocationCategory());
            itemDTO.setOrgId(batchPO.getOrgId());
            itemDTO.setPackageCount(q.getPackagecount());
            itemDTO.setPackageName(q.getPackagename());
            itemDTO.setUnitCount(q.getUnitcount());
            itemDTO.setUnitName(q.getUnitname());
            itemDTO.setProductBrand(q.getProductbrand());
            itemDTO.setCategoryName(q.getCategoryname());
            itemDTO.setProductName(q.getProductname());
            itemDTO.setSaleSpec(q.getSalespec());
            itemDTO.setSaleSpecQuantity(q.getSalespecquantity());
            itemDTO.setSkuId(q.getSkuid());
            // 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
            itemDTO.setTaskState(TaskItemStateEnum.未分拣.getType());
            itemDTO.setSpecName(q.getSpecname());
            itemDTO.setOverSortCount(BigDecimal.ZERO);
            itemDTO.setLackUnitCount(BigDecimal.ZERO);
            itemDTO.setUnitTotalCount(q.getUnittotalcount());
            // itemDTO.setOrderItemId(q.getId());
            itemDTO.setSpecQuantity(q.getSpecquantity());
            itemDTO.setSource(q.getSource());
            itemDTO.setChannel(q.getChannel());

            itemDTO.setOwnerId(q.getOwnerId());
            itemDTO.setSecOwnerId(q.getSecOwnerId());
            itemDTO.setProductSpecificationId(q.getProductSpecificationId());
            if (orderTraceableItemMap != null && StringUtils.isNotEmpty(q.getBusinessItemId())) {
                OrderTraceableItemDTO traceableItemDTO = orderTraceableItemMap.get(Long.valueOf(q.getBusinessItemId()));
                if (traceableItemDTO != null) {
                    itemDTO.setControlConfigId(traceableItemDTO.getProductControlId());
                }
            } else {
                itemDTO.setControlConfigId(q.getControlConfigId());
            }
            // 生产日期和入库时间
            itemDTO.setBatchTime(q.getBatchTime());
            itemDTO.setProductionDate(q.getProductionDate());
            // 产品单价
            itemDTO.setUnitPrice(q.getUnitPrice());

            itemDTO.setCompleteUser(batchTaskPO.getSorter());
            itemDTO.setCompleteUserId(batchTaskPO.getSorterId());
            itemDTO.setLargePickPattern(createDTO.getLargePick());
            itemDTO.setIsAdvent(q.getIsAdvent());
            lstBatchTaskItem.add(itemDTO);
            currBatchTaskItemList.add(itemDTO);

            // 订单详情表中保存拣货任务项id
            q.setBatchTaskItemId(itemDTO.getId());

            // 订单项关联拣货任务项
            addOrderItemTaskInfoPO(lstOrderItemTask, q, itemDTO, batchTaskPO);
        }

        waveSplitRobotBL.resetLargePickPattern(currBatchTaskItemList, passageDTO, createDTO, waveOrderItems);
    }

    private void setSecondSortBatchTaskLocation(BatchTaskPO batchTaskPO, WavesStrategyDTO wavesStrategyDTO,
        List<OutStockOrderItemPO> orderItemPOList) {
        if (Objects.nonNull(batchTaskPO.getToLocationId())) {
            return;
        }
        if (BooleanUtils.isFalse(wavesStrategyDTO.getIsOpenSecondSort())) {
            return;
        }
        if (BooleanUtils.isFalse(wavesStrategyDTO.getIsMultiOutBound())) {
            return;
        }

        List<Long> itemIds = orderItemPOList.stream().map(OutStockOrderItemPO::getId).collect(Collectors.toList());
        List<OutStockOrderItemPO> locationOrderList = Lists.partition(itemIds, 300).stream()
            .map(outStockOrderItemMapper::listByIds).filter(itemList -> !CollectionUtils.isEmpty(itemList))
            .flatMap(m -> m.stream()).collect(Collectors.toList());
        // LOG.info("设置总单二次分拣出库位查询出的订单项信息为：{}", JSON.toJSONString(locationOrderList));
        locationOrderList =
            locationOrderList.stream().filter(m -> Objects.nonNull(m.getLocationId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationOrderList)) {
            return;
        }
        OutStockOrderItemPO outStockOrderItemPO = locationOrderList.get(0);

        batchTaskPO.setToLocationId(outStockOrderItemPO.getLocationId());
        batchTaskPO.setToLocationName(outStockOrderItemPO.getLocationName());
    }

    /**
     * 组装订单项关联拣货任务项
     *
     * @return
     */
    @Deprecated
    private void addOrderItemTaskInfoPO(List<OrderItemTaskInfoPO> taskInfoPOList, OutStockOrderItemPO orderItemPO,
        BatchTaskItemDTO itemDTO, BatchTaskPO batchTaskPO) {
        // 去重
        if (taskInfoPOList.stream()
            .anyMatch(p -> Objects.equals(String.format("%s_%s", p.getRefOrderItemId(), p.getBatchTaskItemId()),
                String.format("%s_%s", orderItemPO.getId(), itemDTO.getId())))) {
            // 存在则更新数量
            OrderItemTaskInfoPO oldPO = taskInfoPOList.stream()
                .filter(p -> Objects.equals(String.format("%s_%s", p.getRefOrderItemId(), p.getBatchTaskItemId()),
                    String.format("%s_%s", orderItemPO.getId(), itemDTO.getId())))
                .findFirst().get();
            oldPO.setUnitTotalCount(oldPO.getUnitTotalCount().add(orderItemPO.getUnittotalcount()));
            oldPO.setOriginalUnitTotalCount(oldPO.getOriginalUnitTotalCount().add(orderItemPO.getUnittotalcount()));
            oldPO.setOriginalUnitTotalCount(oldPO.getUnitTotalCount());

            // 明细去重
            List<OrderItemTaskInfoDetailPO> oldDetailList = oldPO.getDetailList();
            if (oldDetailList == null) {
                oldDetailList = new ArrayList<>();
                oldPO.setDetailList(oldDetailList);
            }
            Optional<OrderItemTaskInfoDetailPO> detailOptional = oldDetailList.stream()
                .filter(p -> Objects.equals(p.getSecOwnerId(), orderItemPO.getSecOwnerId())).findFirst();
            if (detailOptional.isPresent()) {
                detailOptional.get()
                    .setUnitTotalCount(detailOptional.get().getUnitTotalCount().add(orderItemPO.getUnittotalcount()));
                detailOptional.get().setOriginalUnitTotalCount(
                    detailOptional.get().getOriginalUnitTotalCount().add(orderItemPO.getUnittotalcount()));
            } else {
                OrderItemTaskInfoDetailPO detail = new OrderItemTaskInfoDetailPO();
                detail.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.ORDER_ITEM_TASK_INFO_DETAIL));
                detail.setOrgId(oldPO.getOrgId());
                detail.setTaskInfoId(oldPO.getId());
                detail.setSecOwnerId(orderItemPO.getSecOwnerId());
                detail.setOwnerId(orderItemPO.getOwnerId());
                detail.setProductSpecificationId(orderItemPO.getProductSpecificationId());
                detail.setUnitTotalCount(orderItemPO.getUnittotalcount());
                detail.setOriginalUnitTotalCount(orderItemPO.getUnittotalcount());
                oldDetailList.add(detail);
            }
            return;
        }
        // 新增关联拣货任务项
        OrderItemTaskInfoPO orderItemTaskPO = new OrderItemTaskInfoPO();
        orderItemTaskPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.ORDER_ITEM_TASK_INFO));
        orderItemTaskPO.setOrgId(Integer.valueOf(batchTaskPO.getOrgId()));
        orderItemTaskPO.setRefOrderItemId(orderItemPO.getId());
        orderItemTaskPO.setRefOrderId(orderItemPO.getOutstockorderId());
        orderItemTaskPO.setBatchTaskItemId(itemDTO.getId());
        orderItemTaskPO.setBatchTaskId(batchTaskPO.getId());
        orderItemTaskPO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
        orderItemTaskPO.setBatchId(batchTaskPO.getBatchId());
        orderItemTaskPO.setBatchNo(batchTaskPO.getBatchNo());
        orderItemTaskPO.setUnitTotalCount(orderItemPO.getUnittotalcount());
        orderItemTaskPO.setOriginalUnitTotalCount(orderItemPO.getUnittotalcount());

        // 新增明细
        List<OrderItemTaskInfoDetailPO> detailPOList = new ArrayList<>();
        OrderItemTaskInfoDetailPO detailPO = new OrderItemTaskInfoDetailPO();
        detailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.ORDER_ITEM_TASK_INFO_DETAIL));
        detailPO.setOrgId(orderItemTaskPO.getOrgId());
        detailPO.setTaskInfoId(orderItemTaskPO.getId());
        detailPO.setSecOwnerId(orderItemPO.getSecOwnerId());
        detailPO.setOwnerId(orderItemPO.getOwnerId());
        detailPO.setProductSpecificationId(orderItemPO.getProductSpecificationId());
        detailPO.setUnitTotalCount(orderItemTaskPO.getUnitTotalCount());
        detailPO.setOriginalUnitTotalCount(orderItemTaskPO.getUnitTotalCount());
        detailPOList.add(detailPO);

        orderItemTaskPO.setDetailList(detailPOList);
        taskInfoPOList.add(orderItemTaskPO);
    }

    /**
     * 处理按订单拣货
     *
     * @param batchPO 波次对象
     * @param waveOrders 拣货任务关联的订单集合
     * @param lstBatchTask 存放新建拣货任务的集合
     * @param lstBatchTaskItem 存放新建拣货任务项的集合
     * @param wavesStrategyDTO 拆分波次的条件
     * @param lstOrderItemTask 存放新建拣货任务项和订单项关联表的集合
     * @param createDTO 生成拣货任务需要的数据对象
     */
    private void processBatchTaskByOrder(BatchPO batchPO, List<OutStockOrderPO> waveOrders,
        List<BatchTaskPO> lstBatchTask, List<BatchTaskItemDTO> lstBatchTaskItem, WavesStrategyBO wavesStrategyDTO,
        List<OrderItemTaskInfoPO> lstOrderItemTask, WaveCreateDTO createDTO) {
        PassageDTO passageDTO = createDTO.getPassageDTO();

        Long sowId = null;
        String sowNo = null;
        Optional<OutStockOrderItemPO> first = waveOrders.stream().flatMap(order -> order.getItems().stream())
            .filter(item -> item.getSowTaskNo() != null).findFirst();
        if (first.isPresent()) {
            sowId = first.get().getSowTaskId();
            sowNo = first.get().getSowTaskNo();
        }

        // 按订单拣货： 一个波次一个拣货任务
        // 按产品拣货：具体按产品类目、货位、货区来

        BatchTaskPO batchTaskPO = new BatchTaskPO();
        batchTaskPO
            .setBatchTaskNo(UuidUtil.generator(wavesStrategyDTO.getWarehouseId(), BatchOrderTypeConstans.BATCHTASK_NO));
        batchTaskPO.setId(UuidUtil.generatorId());
        BigDecimal packageCount = computerStrategyConditionBL.computerStrategyConditionByOrders(waveOrders,
            WavesStrategyLimitType.piecePackageNumberLimitType);
        batchTaskPO.setPackageAmount(packageCount);
        BigDecimal unitCount = computerStrategyConditionBL.computerStrategyConditionByOrders(waveOrders,
            WavesStrategyLimitType.pieceUnitNumberLimitType);
        batchTaskPO.setUnitAmount(unitCount);
        BigDecimal orderCount = computerStrategyConditionBL.computerStrategyConditionByOrders(waveOrders,
            WavesStrategyLimitType.orderCountLimitType);
        batchTaskPO.setOrderCount(orderCount.intValue());
        BigDecimal orderAmount = computerStrategyConditionBL.computerStrategyConditionByOrders(waveOrders,
            WavesStrategyLimitType.orderAmountLimitType);
        batchTaskPO.setOrderAmount(orderAmount);

        final List<Long> lstSkuIds = new ArrayList<>();
        waveOrders.forEach(p -> {
            p.getItems().forEach(q -> {
                if (!lstSkuIds.contains(q.getSkuid())) {
                    lstSkuIds.add(q.getSkuid());
                }
            });
        });
        batchTaskPO.setSkuCount(lstSkuIds.size());
        // 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
        batchTaskPO.setTaskState(TaskStateEnum.未分拣.getType());
        // batchTaskPO.setSorter(null);
        // batchTaskPO.setSorterId(null);
        batchTaskPO.setOrgId(String.valueOf(batchPO.getOrgId()));
        batchTaskPO.setBatchId(batchPO.getId());
        batchTaskPO.setBatchNo(batchPO.getBatchNo());
        boolean hasAllotOrder = SplitWaveOrderUtil.hasAllotOrderByItem(waveOrders);
        // 添加拣货属性和拣货方式
        String passageName = passageDTO != null ? passageDTO.getPassageName() : null;
        batchTaskHelper.setBatchTaskName(batchTaskPO, wavesStrategyDTO, passageName, createDTO, hasAllotOrder);
        batchTaskPO.setSowTaskId(sowId);
        batchTaskPO.setSowTaskNo(sowNo);
        // 是否需要拣货
        batchTaskPO.setNeedPick(ConditionStateEnum.是.getType());

        // 设置拣货任务所属仓库
        batchTaskPO.setWarehouseId(batchPO.getWarehouseId());
        // 拣货任务类型
        batchTaskPO.setBatchTaskType(
            createDTO.getBatchTaskType() == null ? BatchTaskTypeEnum.默认.getType() : createDTO.getBatchTaskType());
        batchTaskPO.setPickPattern(RobotPickConstants.getBatchTaskRobotPickType(createDTO, passageDTO));
        batchTaskHelper.setTaskWarehouseFeatureType(batchTaskPO, wavesStrategyDTO);
        batchTaskHelper.handleSorterGroupBatchTask(null, batchTaskPO, sowId, wavesStrategyDTO);
        batchTaskPO.setTaskAppendSequence((byte)0);
        if (Objects.nonNull(createDTO.getAppendSequence())) {
            batchTaskPO.setTaskAppendSequence(createDTO.getAppendSequence().byteValue());
        }
        batchTaskPO.setKindOfPicking(
            Optional.ofNullable(createDTO.getKindOfPicking()).orElse(BatchTaskKindOfPickingConstants.DEFAULT));

        // 通道
        if (passageDTO != null) {
            batchTaskPO.setPassageId(passageDTO.getId());
            batchTaskPO.setPassageName(passageDTO.getPassageName());
        }
        lstBatchTask.add(batchTaskPO);

        // 接订单中台时，需通过TMS接口获取订单项的控货策略ID
        Map<Long, OrderTraceableItemDTO> orderTraceableItemMap = null;
        List<Long> businessItemIdList = waveOrders.stream().filter(ord -> !CollectionUtils.isEmpty(ord.getItems()))
            .flatMap(ord -> ord.getItems().stream()).map(OutStockOrderItemPO::getBusinessItemId)
            .filter(Objects::nonNull).map(Long::valueOf).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(businessItemIdList)) {
            orderTraceableItemMap =
                iTransferOrderQueryService.selectOrderTraceableItemInfoByOrderItemIds(businessItemIdList).stream()
                    .filter(elem -> elem.getOrderItemId() != null && elem.getProductControlId() != null).collect(
                        Collectors.toMap(OrderTraceableItemDTO::getOrderItemId, Function.identity(), (k1, k2) -> k1));
            LOG.info("[生成波次]接订单中台时，查TMS的控货策略：{}", JSON.toJSONString(orderTraceableItemMap));
        }

        if (BatchTaskPickPatternEnum.机器人拣货.getType().equals(batchTaskPO.getPickPattern())) {
            batchTaskPO.setSorter(RobotSorterConstants.ROBOT_NAME);
            batchTaskPO.setSorterId(RobotSorterConstants.ROBOT_ID);
        }

        // 设置按订单拣货-按订单分组的出库位
        recommendOutLocationBL.setOutLocationByGroup(batchTaskPO, waveOrders, wavesStrategyDTO, createDTO);

        for (OutStockOrderPO p : waveOrders) {
            if (CollectionUtils.isEmpty(p.getItems())) {
                continue;
            }
            for (OutStockOrderItemPO q : p.getItems()) {
                // 出库单详情关联拣货任务相关信息
                q.setBatchtaskId(batchTaskPO.getId());
                q.setBatchtaskno(batchTaskPO.getBatchTaskNo());
                // // 播种任务
                // q.setSowTaskId(sowId);
                // q.setSowTaskNo(sowNo);

                // 没开启按客户拣货 相同订单项+货位进行合并；开启了 按用户拣货，订单拣货时数量不合并
                if (lstBatchTaskItem.stream()
                    .anyMatch(batchTaskItemDTO -> Objects.equals(batchTaskItemDTO.getOrderItemId(), q.getId())
                        && Objects.equals(batchTaskItemDTO.getLocationId(), q.getLocationId()))
                    && BooleanUtils.isFalse(wavesStrategyDTO.isPickByCustomer())) {
                    BatchTaskItemDTO itemDTO = lstBatchTaskItem.stream()
                        .filter(batchTaskItemDTO -> Objects.equals(batchTaskItemDTO.getOrderItemId(), q.getId())
                            && Objects.equals(batchTaskItemDTO.getLocationId(), q.getLocationId()))
                        .findAny().get();
                    itemDTO.setUnitTotalCount(itemDTO.getUnitTotalCount().add(q.getUnittotalcount()));
                    itemDTO.setPackageCount(itemDTO.getPackageCount().add(q.getPackagecount()));
                    itemDTO.setUnitCount(itemDTO.getUnitCount().add(q.getUnitcount()));
                    // 出库单详情关联拣货任务项id
                    q.setBatchTaskItemId(itemDTO.getId());
                    // 订单项关联拣货任务项
                    addOrderItemTaskInfoPO(lstOrderItemTask, q, itemDTO, batchTaskPO);
                    continue;
                }
                BatchTaskItemDTO itemDTO = new BatchTaskItemDTO();
                itemDTO.setBatchTaskId(batchTaskPO.getId());
                itemDTO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
                itemDTO.setId(UuidUtil.generatorId());
                itemDTO.setLocationId(q.getLocationId());
                itemDTO.setLocationName(q.getLocationName());
                itemDTO.setLocationCategory(q.getLocationCategory());
                itemDTO.setOrgId(batchPO.getOrgId());
                itemDTO.setPackageCount(q.getPackagecount());
                itemDTO.setPackageName(q.getPackagename());
                itemDTO.setUnitCount(q.getUnitcount());
                itemDTO.setUnitName(q.getUnitname());
                itemDTO.setProductBrand(q.getProductbrand());
                itemDTO.setCategoryName(q.getCategoryname());
                itemDTO.setProductName(q.getProductname());
                itemDTO.setRefOrderId(String.valueOf(p.getId()));
                itemDTO.setSaleSpec(q.getSalespec());
                itemDTO.setRefOrderNo(p.getReforderno());
                itemDTO.setSaleSpecQuantity(q.getSalespecquantity());
                itemDTO.setSkuId(q.getSkuid());
                // 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
                itemDTO.setTaskState(TaskItemStateEnum.未分拣.getType());
                itemDTO.setSpecName(q.getSpecname());
                itemDTO.setOverSortCount(BigDecimal.ZERO);
                itemDTO.setLackUnitCount(BigDecimal.ZERO);
                itemDTO.setUnitTotalCount(q.getUnittotalcount());
                itemDTO.setOrderItemId(q.getId());
                itemDTO.setSpecQuantity(q.getSpecquantity());
                itemDTO.setSource(q.getSource());
                itemDTO.setChannel(q.getChannel());

                itemDTO.setOwnerId(q.getOwnerId());
                itemDTO.setSecOwnerId(q.getSecOwnerId());
                itemDTO.setProductSpecificationId(q.getProductSpecificationId());
                if (orderTraceableItemMap != null && StringUtils.isNotEmpty(q.getBusinessItemId())) {
                    OrderTraceableItemDTO traceableItemDTO =
                        orderTraceableItemMap.get(Long.valueOf(q.getBusinessItemId()));
                    if (traceableItemDTO != null) {
                        itemDTO.setControlConfigId(traceableItemDTO.getProductControlId());
                    }
                } else {
                    itemDTO.setControlConfigId(q.getControlConfigId());
                }
                // 生产日期和入库时间
                itemDTO.setBatchTime(q.getBatchTime());
                itemDTO.setProductionDate(q.getProductionDate());
                // 产品单价
                itemDTO.setUnitPrice(q.getUnitPrice());
                itemDTO.setCompleteUser(batchTaskPO.getSorter());
                itemDTO.setCompleteUserId(batchTaskPO.getSorterId());
                itemDTO.setLargePickPattern(createDTO.getLargePick());
                lstBatchTaskItem.add(itemDTO);
                // 出库单详情关联拣货任务相关信息
                q.setBatchtaskId(itemDTO.getBatchTaskId());
                q.setBatchtaskno(itemDTO.getBatchTaskNo());
                q.setBatchTaskItemId(itemDTO.getId());
                // // 播种任务
                // q.setSowTaskId(sowId);
                // q.setSowTaskNo(sowNo);

                // 订单项关联拣货任务项
                addOrderItemTaskInfoPO(lstOrderItemTask, q, itemDTO, batchTaskPO);
            }
        }

        createBatchChangeOrderSequenceBL.updateOrderSequenceIfSplitByUser(createDTO, waveOrders);
    }

    private boolean processBatchOrders(List<OutStockOrderPO> outStockOrderPOList, List<OutStockOrderPO> lstItemOrders,
        WavesStrategyDTO wavesStrategyDTO) {
        // 订单总数限制
        if (!computerCountByType(wavesStrategyDTO, outStockOrderPOList, lstItemOrders,
            WavesStrategyLimitType.orderCountLimitType)) {
            return false;
        }
        // 订单明细行限制
        if (!computerCountByType(wavesStrategyDTO, outStockOrderPOList, lstItemOrders,
            WavesStrategyLimitType.skuCountLimitType)) {
            return false;
        }
        // 件数限制
        if (!computerCountByType(wavesStrategyDTO, outStockOrderPOList, lstItemOrders,
            WavesStrategyLimitType.piecePackageNumberLimitType)) {
            return false;
        }
        // 货值限制
        if (!computerCountByType(wavesStrategyDTO, outStockOrderPOList, lstItemOrders,
            WavesStrategyLimitType.orderAmountLimitType)) {
            return false;
        }
        return true;
    }

    private boolean computerCountByType(WavesStrategyDTO wavesStrategyDTO, List<OutStockOrderPO> outStockOrderPOList,
        List<OutStockOrderPO> lstItemOrders, String wavesStrategyLimitType) {
        BigDecimal totalCount =
            computerStrategyConditionBL.computerStrategyConditionByOrders(lstItemOrders, wavesStrategyLimitType);

        Integer valLower = getStrategyValueByType(wavesStrategyDTO, wavesStrategyLimitType, false);
        Integer valUpper = getStrategyValueByType(wavesStrategyDTO, wavesStrategyLimitType, true);

        // 订单明细行下限
        if (valLower != null && totalCount.compareTo(BigDecimal.valueOf(valLower)) < 0) {
            LOG.info(" {}下限不满足策略,lstItemOrders{}执行结束：波次{}", wavesStrategyLimitType, JSON.toJSONString(lstItemOrders),
                JSON.toJSONString(wavesStrategyDTO));
            return false;
        }
        // 订单明细行上限
        if (valUpper != null && valUpper > 0 && totalCount.compareTo(BigDecimal.valueOf(valUpper)) > 0) {
            LOG.info("{}上限大于策略要求,自动调整…", wavesStrategyLimitType);
            lstItemOrders
                .removeIf(p -> DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue() == p.getDeliveryMarkState());
            // 移除多余的订单，直到满足订单项总数上限
            while (lstItemOrders.size() > 0 && totalCount.compareTo(BigDecimal.valueOf(valUpper)) > 0) {
                OutStockOrderPO outStockOrderPO = lstItemOrders.get(lstItemOrders.size() - 1);
                lstItemOrders.remove(outStockOrderPO);
                BigDecimal bigDecimal = computerStrategyConditionBL.computerStrategyConditionByOrder(outStockOrderPO,
                    wavesStrategyLimitType);
                if (valUpper != null && valUpper > 0 && bigDecimal.compareTo(BigDecimal.valueOf(valUpper)) > 0) {
                    LOG.info("单个订单：{}大于{}限制的上限进行排除：{}", outStockOrderPO.getId(), wavesStrategyLimitType, valUpper);
                    continue;
                }
                outStockOrderPOList.add(outStockOrderPO);
                totalCount = computerStrategyConditionBL.computerStrategyConditionByOrders(lstItemOrders,
                    wavesStrategyLimitType);
            }
        }
        return true;
    }

    private Integer getStrategyValueByType(WavesStrategyDTO wavesStrategyDTO, String wavesStrategyLimitType,
        boolean isUpper) {
        Integer valTmp = null;
        switch (wavesStrategyLimitType) {
            case WavesStrategyLimitType.orderCountLimitType:
                valTmp = isUpper ? wavesStrategyDTO.getOrderUpper() : wavesStrategyDTO.getOrderLower();
                break;
            case WavesStrategyLimitType.skuCountLimitType:
                valTmp =
                    isUpper ? wavesStrategyDTO.getOrderDetailLineUpper() : wavesStrategyDTO.getOrderDetailLineLower();
                break;
            case WavesStrategyLimitType.piecePackageNumberLimitType:
                valTmp = isUpper ? wavesStrategyDTO.getPieceNumberUpper() : wavesStrategyDTO.getPieceNumberLower();
                break;
            case WavesStrategyLimitType.orderAmountLimitType:
                valTmp = isUpper ? wavesStrategyDTO.getValueUpper() : wavesStrategyDTO.getValueLower();
                break;
            default:
                break;
        }
        return valTmp;
    }

    /**
     * 根据策略查找所有满足条件的订单
     *
     * @param wavesStrategyDTO
     * @return
     */
    public List<OutStockOrderPO> findOutStockOrderPOSByStrate(WavesStrategyDTO wavesStrategyDTO, String orderStartTime,
        String orderEndTime) {
        List<OutStockOrderPO> outStockOrderPOList;// 获取所有波次订单
        OutStockOrderSearchSO orderSearchSO = new OutStockOrderSearchSO();
        orderSearchSO.setWareHouseId(wavesStrategyDTO.getWarehouseId());
        orderSearchSO.setTimeS(orderStartTime);
        orderSearchSO.setTimeE(orderEndTime);

        List<Integer> lstOrderTypes = new ArrayList<>();
        String[] split = wavesStrategyDTO.getOrderType().trim().split(",");
        for (String orderType : split) {
            if (StringUtils.isNotEmpty(orderType)) {
                lstOrderTypes.add(Integer.valueOf(orderType));
            }
        }
        if (!CollectionUtils.isEmpty(lstOrderTypes)) {
            orderSearchSO.setOrderTypes(lstOrderTypes);
        }
        // 排除内配单
        orderSearchSO.setNotBusinessTypes(
            Arrays.asList(InStockOrderBusinessType.内配单.getType(), InStockOrderBusinessType.内配退货单.getType()));
        outStockOrderPOList = outStockOrderBL.findOutStockOrderPOList(orderSearchSO);
        lstOrderTypes.clear();

        processZhaoShangSku(outStockOrderPOList);
        return outStockOrderPOList;
    }

    /**
     * 转换招商或者长株潭产品
     *
     * @param outStockOrderPOList
     */
    private void processZhaoShangSku(List<OutStockOrderPO> outStockOrderPOList) {
        List<Long> lstSkuIds = new ArrayList<>();
        outStockOrderPOList.forEach(order -> {
            if (isNeedProcessSku(order)) {
                order.getItems().forEach(item -> {
                    if (item.getSkuid() != null) {
                        lstSkuIds.add(item.getSkuid());
                    }
                });
            }
        });
        if (lstSkuIds.size() > 0) {
            Integer warehouseId = outStockOrderPOList.get(0).getWarehouseId();
            Integer deliveryCityId = outStockOrderPOList.get(0).getOrgId();
            // 将招商或者长株潭城市SKU转换为实际发货的SKU
            Map<Long, Long> actualDeliverySkuIdMap =
                iWarehouseInventoryQueryService.getActualDeliverySkuIdMap(lstSkuIds, warehouseId, deliveryCityId);
            if (actualDeliverySkuIdMap.size() > 0) {
                outStockOrderPOList.forEach(order -> {
                    if (isNeedProcessSku(order)) {
                        order.getItems().forEach(item -> {
                            if (item.getSkuid() != null) {
                                Long skuNew = actualDeliverySkuIdMap.get(item.getSkuid());
                                if (skuNew != null) {
                                    item.setSkuid(skuNew);
                                }
                            }
                        });
                    }
                });
            }
        }
    }

    private boolean isNeedProcessSku(OutStockOrderPO order) {
        return order.getFromCityId() != null && (!order.getFromCityId().equals(order.getOrgId())
            || (OtherConstTypes.ZHAOSHANG_CITYID.equals(order.getOrgId())
                && !OtherConstTypes.ZHAOSHANG_CITYID.equals(order.getFromCityId())));
    }

    /**
     * 调整订单顺序，跟传入顺序一致
     *
     * @param lstOrders
     * @param lstOrderBy
     * @param isById
     * @return
     */
    public List<OutStockOrderPO> getOrderByIndex(List<OutStockOrderPO> lstOrders, List<String> lstOrderBy,
        boolean isById) {
        List<OutStockOrderPO> lstResult = new ArrayList<>();
        for (String str : lstOrderBy) {
            List<OutStockOrderPO> outStockOrderPO = new ArrayList<>();
            if (isById) {
                if (lstOrders.stream().anyMatch(p -> p.getId().toString().equals(str))) {
                    outStockOrderPO =
                        lstOrders.stream().filter(p -> p.getId().toString().equals(str)).collect(Collectors.toList());
                }
            } else {
                if (lstOrders.stream().anyMatch(p -> p.getReforderno().equals(str))) {
                    outStockOrderPO =
                        lstOrders.stream().filter(p -> p.getReforderno().equals(str)).collect(Collectors.toList());
                }
            }
            if (outStockOrderPO != null) {
                lstResult.addAll(outStockOrderPO);
            }
        }
        // 如果排序结果不包含某些订单，需要补充进来
        List<OutStockOrderPO> lstNoSequenceOrders =
            lstOrders.stream().filter(p -> !lstOrderBy.contains(isById ? p.getId().toString() : p.getReforderno()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(lstNoSequenceOrders)) {
            lstResult.addAll(lstNoSequenceOrders);
        }
        return lstResult;
    }

    /**
     * 获取无货位订单,订单集合删除无货位订单项
     *
     * @param waveOrders
     */
    private List<OutStockOrderPO> getNoLocationOrder(List<OutStockOrderPO> waveOrders) {
        List<OutStockOrderPO> noLocation = new ArrayList<>();

        List<OutStockOrderPO> lstNoLocation = waveOrders.stream()
            .filter(outStockOrderPO -> outStockOrderPO.getItems().stream()
                .anyMatch(outStockOrderItemPO -> outStockOrderItemPO.getLocationId() == null))
            .collect(Collectors.toList());

        lstNoLocation.forEach(outStockOrderPO -> {
            OutStockOrderPO noLocationOrder = new OutStockOrderPO();
            BeanUtils.copyProperties(outStockOrderPO, noLocationOrder);

            noLocationOrder.setItems(noLocationOrder.getItems().stream().filter(o -> o.getLocationId() == null)
                .collect(Collectors.toList()));
            noLocation.add(noLocationOrder);

            outStockOrderPO.setItems(outStockOrderPO.getItems().stream().filter(o -> o.getLocationId() != null)
                .collect(Collectors.toList()));
        });

        return noLocation;
    }

    /**
     * 根据多个出库批次，创建分拣任务
     */
    private void processBatchTaskResultByMultiOutBound(WaveCreateDTO createDTO, BatchPO batchPO,
        List<String> locationNames) {
        Map<String, List<OutStockOrderPO>> outStockOrderGroup =
            createDTO.getOrders().stream().collect(Collectors.groupingBy(ele -> String.format("%s", ele.getBoundNo())));
        outStockOrderGroup.forEach((boundNo, orderList) -> {
            if (Objects.equals(boundNo, "null")) {
                LOG.info("[多出库批次创建波次]出库批次号为空，订单号：{}",
                    orderList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
                throw new BusinessValidateException("多个出库批次创建波次，出库批次号为空");
            }
            WaveCreateDTO newCreateDTO = new WaveCreateDTO();
            BeanUtils.copyProperties(createDTO, newCreateDTO);
            newCreateDTO.setOrders(orderList);

            LOG.info("[多出库批次创建波次]出库批次号：{}，出库单：{}", boundNo,
                orderList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
            processBatchTaskResult(newCreateDTO, batchPO, locationNames);
        });
    }

    /**
     * 网格仓根据多个出库批次，创建分拣任务
     */
    private void processBatchTaskWorkSettingByMultiOutBound(WaveCreateDTO createDTO, BatchPO batchPO,
        List<String> locationNames) {
        Map<String, List<OutStockOrderPO>> outStockOrderGroup =
            createDTO.getOrders().stream().collect(Collectors.groupingBy(ele -> String.format("%s", ele.getBoundNo())));
        outStockOrderGroup.forEach((boundNo, orderList) -> {
            if (Objects.equals(boundNo, "null")) {
                LOG.info("[多出库批次创建波次]网格仓，出库批次号为空，订单号：{}",
                    orderList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
                throw new BusinessValidateException("多个出库批次创建波次，出库批次号为空");
            }
            WaveCreateDTO newCreateDTO = new WaveCreateDTO();
            BeanUtils.copyProperties(createDTO, newCreateDTO);
            newCreateDTO.setOrders(orderList);

            LOG.info("[多出库批次创建波次]网格仓，出库批次号：{}，出库单：{}", boundNo,
                orderList.stream().map(OutStockOrderPO::getReforderno).collect(Collectors.toList()));
            processBatchTaskWorkSetting(newCreateDTO, batchPO, locationNames);
        });
    }

    /**
     * @param createDTO
     * @param lstOtherOrders
     * @param lstCreateDTO
     */
    private void getSameWaveCreateByDiffOrderList(WaveCreateDTO createDTO, List<OutStockOrderPO> lstOtherOrders,
        List<WaveCreateDTO> lstCreateDTO) {
        Map<String, List<OutStockOrderPO>> outStockOrderGroup =
            lstOtherOrders.stream().collect(Collectors.groupingBy(ele -> String.format("%s", ele.getBoundNo())));
        outStockOrderGroup.forEach((boundNo, orderList) -> {
            if (Objects.equals(boundNo, "null")) {
                LOG.info("[多出库批次创建波次]出库批次号为空，订单号：{}",
                    orderList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
                throw new BusinessValidateException("多出库批次创建波次，出库批次号为空");
            }
            WaveCreateDTO boundWaveCreate = new WaveCreateDTO();
            BeanUtils.copyProperties(createDTO, boundWaveCreate);
            boundWaveCreate.setOrders(orderList);
            lstCreateDTO.add(boundWaveCreate);
        });
    }

    public void clearOrderItemTaskInfo(List<Long> ids) {
        orderItemTaskInfoMapper.deleteByIds(ids);
        List<Long> detailIds = orderItemTaskInfoDetailMapper.selectIdsByTaskInfoIds(ids);
        if (!CollectionUtils.isEmpty(detailIds)) {
            orderItemTaskInfoDetailMapper.deleteByIds(detailIds);
        }
    }

}
