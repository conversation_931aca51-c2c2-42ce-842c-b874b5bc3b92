package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BillReviewConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BillReviewMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BillReviewPO;
import com.yijiupi.himalaya.supplychain.waves.dto.billreview.BillReviewDTO;

@Service
@Transactional(rollbackFor = Exception.class)
public class BillReviewBL {

    @Autowired
    private BillReviewMapper billReviewMapper;

    @Autowired
    private OrderTraceBL orderTraceBL;

    public BillReviewDTO startBillReview(BillReviewDTO billReviewDTO) {
        BillReviewPO oldBillReviewPO = billReviewMapper.getByBusinessNo(billReviewDTO.getBusinessNo(),
            billReviewDTO.getRelatedBusinessNo(), billReviewDTO.getOrgId());
        if (oldBillReviewPO != null && oldBillReviewPO.getReviewerId().equals(billReviewDTO.getReviewerId())) {
            billReviewDTO.setId(oldBillReviewPO.getId());
            return billReviewDTO;
        } else if (oldBillReviewPO != null && !oldBillReviewPO.getReviewerId().equals(billReviewDTO.getReviewerId())) {
            throw new BusinessValidateException("该订单由" + oldBillReviewPO.getReviewer() + "在进行复核,请务多人操作");
        }
        BillReviewPO billReviewPO = BillReviewConvertor.billReviewDTO2NewBillReviewPO(billReviewDTO);
        billReviewMapper.insertSelective(billReviewPO);

        // 便与后续添加操作日志
        billReviewDTO.setId(null);
        updateBillReview(billReviewDTO);
        billReviewDTO.setId(billReviewPO.getId());
        return billReviewDTO;
    }

    public void updateBillReview(BillReviewDTO billReviewDTO) {
        if (billReviewDTO.getId() != null) {
            BillReviewPO billReviewPO = BillReviewConvertor.billReviewDTO2BillReviewPO(billReviewDTO);
            billReviewMapper.updateByPrimaryKeySelective(billReviewPO);
            BillReviewPO billReview = billReviewMapper.selectByPrimaryKey(billReviewPO.getId());
            billReviewDTO = BillReviewConvertor.billReviewPO2BillReviewDTO(billReview);
        }
        // 增加操作记录
        orderTraceBL.updateBillReviewState(billReviewDTO);
    }

    public void batchUpdateBillReview(List<BillReviewDTO> billReviewDTOS) {
        List<BillReviewPO> billReviewPOS = BillReviewConvertor.billReviewDTOS2BillReviewPOS(billReviewDTOS);
        billReviewMapper.insertOrUpdateBatch(billReviewPOS);
    }
}
