package com.yijiupi.himalaya.supplychain.waves.search;

import java.io.Serializable;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/4/20
 * 货位容器查询  仓库+单号+货位ID+货位名称
 */
public class LocationContainerSO implements Serializable {

    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 城市id
     */
    private Integer orgId;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;

    /**
     * 订单号
     */
    private String refOrderNo;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }
}
