package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 出库单项变更记录
 *
 * <AUTHOR>
 * @date 2020-06-09 15:21
 */
public class OutStockOrderItemChangeRecordPO implements Serializable {

    private static final long serialVersionUID = 2204616928827078217L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 城市ID
     */
    private Integer orgId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 订单id
     */
    private Long refOrderId;

    /**
     * 订单编号
     */
    private String refOrderNo;

    /**
     * 订单项id
     */
    private Long refOrderItemId;

    /**
     * 产品skuId
     */
    private Long productSkuId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 包装规格
     */
    private String specName;

    /**
     * 包装规格系数
     */
    private BigDecimal specQuantity;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 变更大单位数量
     */
    private BigDecimal packageCount;

    /**
     * 变更小单位数量
     */
    private BigDecimal unitCount;

    /**
     * 变更小单位总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 波次id
     */
    private String batchId;

    /**
     * 波次编号
     */
    private String batchNo;

    /**
     * 拣货任务id
     */
    private String batchTaskId;

    /**
     * 拣货任务编号
     */
    private String batchTaskNo;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 移库单id
     */
    private Long transferOrderId;

    /**
     * 移库单号
     */
    private String transferOrderNo;

    /**
     * 变更类型 0：订单取消 1：订单修改 2：部分配送 3：配送失败
     */
    private Byte changeType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 最后修改人
     */
    private String lastUpdateUser;

    /**
     * 是否移库
     */
    private Boolean moveFlag;

    /**
     * 货位id
     */
    private Long locationId;

    /**
     * 货位名称
     */
    private String locationName;

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Boolean getMoveFlag() {
        return moveFlag;
    }

    public void setMoveFlag(Boolean moveFlag) {
        this.moveFlag = moveFlag;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getRefOrderId() {
        return refOrderId;
    }

    public void setRefOrderId(Long refOrderId) {
        this.refOrderId = refOrderId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Long getRefOrderItemId() {
        return refOrderItemId;
    }

    public void setRefOrderItemId(Long refOrderItemId) {
        this.refOrderItemId = refOrderItemId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public String getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Long getTransferOrderId() {
        return transferOrderId;
    }

    public void setTransferOrderId(Long transferOrderId) {
        this.transferOrderId = transferOrderId;
    }

    public String getTransferOrderNo() {
        return transferOrderNo;
    }

    public void setTransferOrderNo(String transferOrderNo) {
        this.transferOrderNo = transferOrderNo;
    }

    public Byte getChangeType() {
        return changeType;
    }

    public void setChangeType(Byte changeType) {
        this.changeType = changeType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }
}
