package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.SyncOrderReCheckDTO;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.enums.PassagePickTypeEnum;
import com.yijiupi.himalaya.supplychain.service.IOrderReCheckStrategyService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCategoryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PassageRelateTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SowTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IPassageService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductCategoryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderTraceBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.SowCalculationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.SowManagerBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskFinalResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.sowtask.CreateSowTaskByOutStockOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationLargePickCargoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.ExistSowTaskBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.RandomCreateProcessBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.CreateBatchTaskByPassageResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.CreateSowTaskByPassageBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.GoodsCollectionLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchTaskKindOfPickingConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.WaveCreateDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.enums.PickingTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.himalaya.supplychain.waves.util.OrderRebuildUtil;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/25
 */
@Service
public class SplitBatchTaskByPassageBL {

    @Autowired
    private SowCalculationBL sowCalculationBL;
    @Autowired
    private GlobalCache globalCache;
    @Autowired
    private CreateBatchLocationLargePickCargoBL createBatchLocationLargePickCargoBL;
    @Autowired
    private SowTaskCreateBL sowTaskCreateBL;
    @Autowired
    private SplitBatchTaskByPassageBL splitBatchTaskByPassageBL;
    @Autowired
    private SowManagerBL sowManagerBL;
    @Autowired
    private OrderTraceBL orderTraceBL;

    @Reference
    private IPassageService iPassageService;
    @Reference
    private IProductCategoryService iProductCategoryService;
    @Reference(timeout = 60000)
    private IOrderReCheckStrategyService iOrderReCheckStrategyService;

    private static final Logger LOG = LoggerFactory.getLogger(SplitBatchTaskByPassageBL.class);
    @Autowired
    private CreateSowTaskByOutStockOrderBL createSowTaskByOutStockOrderBL;

    /**
     * 按通道拆分拣货任务
     */
    public CreateBatchTaskByPassageResultBO createBatchTaskByPassage(WaveCreateDTO createDTO, BatchBO batchBO,
        ExistSowTaskBO existSowTaskPO, List<String> locationNames) {
        List<OutStockOrderPO> orders = createDTO.getOrders();
        WavesStrategyBO wavesStrategyDTO = createDTO.getWavesStrategyDTO();
        Integer cityId = batchBO.getBatchPO().getOrgId();
        String title = createDTO.getTitle();
        String operateUser = createDTO.getOperateUser();
        String locationName = createDTO.getLocationName();
        String driverName = createDTO.getDriverName();
        Boolean allocationFlag = createDTO.getAllocationFlag();
        Long toLocationId = createDTO.getToLocationId();
        String toLocationName = createDTO.getToLocationName();
        Integer toWarehouseId = createDTO.getToWarehouseId();
        String toWarehouseName = createDTO.getToWarehouseName();

        List<OutStockOrderItemPO> totalOrderItemList = new ArrayList<>();
        for (OutStockOrderPO order : orders) {
            setVirtualSecondPickTakeLocation(wavesStrategyDTO.getPassPickType(), order, toLocationId, toLocationName);
            totalOrderItemList.addAll(order.getItems());
        }

        // 获取仓库配置
        Integer warehouseId = wavesStrategyDTO.getWarehouseId();
        WarehouseConfigDTO warehouseConfigDTO = globalCache.getWarehouseConfigDTO(warehouseId);

        List<WaveCreateDTO> totalWaveCreateDTO = new ArrayList<>();

        // createBatchLocationLargePickCargoBL.setTotalItemPackageLocationByAssociateLocation(lstOrderItems,
        // wavesStrategyDTO, warehouseConfigDTO, warehouseId);

        // 根据类目或者货位，查找对应的通道策略，并拼接成生拣货任务的对象
        CreateBatchTaskByPassageResultBO createBatchTaskByPassageResultBO = buildBatchTaskAndSowByPassage(batchBO,
            orders, totalOrderItemList, warehouseConfigDTO, createDTO, existSowTaskPO);

        List<WaveCreateDTO> lstCreateDTO = createBatchTaskByPassageResultBO.getWaveCreateDTOList();
        if (!CollectionUtils.isEmpty(lstCreateDTO)) {
            totalWaveCreateDTO.addAll(lstCreateDTO);
        }
        List<CreateSowTaskResultBO> existSowTaskList = createBatchTaskByPassageResultBO.getExistSowTaskList();

        List<WaveCreateDTO> existSowTaskWaveCreateList =
            getExistSowTaskWaveCreateInfo(createBatchTaskByPassageResultBO);
        if (!CollectionUtils.isEmpty(existSowTaskWaveCreateList)) {
            totalWaveCreateDTO.addAll(existSowTaskWaveCreateList);
        }

        /**
         * 其他没有匹配到规则的订单项，单独生一个拣货任务（二次分拣，一个车次一个拣货任务）
         */
        List<OutStockOrderPO> lstOtherOrders =
            splitBatchTaskByPassageBL.getOtherOrdersByPassageItems(orders, totalOrderItemList, totalWaveCreateDTO);

        List<WaveCreateDTO> otherWaveCreateList =
            WaveCreateDTOConvertor.createWaveCreateDTO(wavesStrategyDTO, lstOtherOrders, createDTO);

        if (!CollectionUtils.isEmpty(otherWaveCreateList)) {
            totalWaveCreateDTO.addAll(otherWaveCreateList);
        }

        // 移除 存在播种任务里的拣货任务。单独处理
        totalWaveCreateDTO = removeExistSowTaskWaveCreateInfo(totalWaveCreateDTO, createBatchTaskByPassageResultBO);

        LOG.info("创建波次列表的信息为：{}", JSON.toJSONString(totalWaveCreateDTO));
        // 根据拆分过的订单，再生成拣货任务
        for (WaveCreateDTO waveDTO : totalWaveCreateDTO) {
            // 指定出库位（虚仓二次分拣时，为收货位，不是出库位）
            if (!Objects.equals(wavesStrategyDTO.getPassPickType(), PassagePickTypeEnum.虚仓二次分拣通道.getType())) {
                if (Objects.isNull(waveDTO.getToLocationId())) {
                    waveDTO.setToLocationId(toLocationId);
                    waveDTO.setToLocationName(toLocationName);
                }
            }
            waveDTO.setToWarehouseId(toWarehouseId);
            waveDTO.setToWarehouseName(toWarehouseName);
            // processBatchTaskResult(waveDTO, batchBO.getBatchPO(), locationNames);
        }

        createBatchTaskByPassageResultBO.setWaveCreateDTOList(totalWaveCreateDTO);

        handleExistSowTaskInfo(createBatchTaskByPassageResultBO);
        saveSowInfo(createBatchTaskByPassageResultBO);

        return createBatchTaskByPassageResultBO;
    }

    private void saveSowInfo(CreateBatchTaskByPassageResultBO createBatchTaskByPassageResultBO) {
        // 批量保存播种任务、播种任务订单关联信息
        if (!CollectionUtils.isEmpty(createBatchTaskByPassageResultBO.getSowOrderPOList())) {
            sowManagerBL.insertSowOrderList(createBatchTaskByPassageResultBO.getSowOrderPOList());
        }

        if (!CollectionUtils.isEmpty(createBatchTaskByPassageResultBO.getSowTaskPOList())) {
            sowManagerBL.insertSowTaskList(createBatchTaskByPassageResultBO.getSowTaskPOList());
            // 记录操作日志
            orderTraceBL.insertSowTaskList(createBatchTaskByPassageResultBO.getSowTaskPOList());
        }
    }

    // 把追加的播种任务 里包含的 waveCreateDTO也拿出来。因为里面有订单项，后面需要根据订单项 过滤 ，没有有拣货任务的订单项 会单独生成拣货任务。
    private List<WaveCreateDTO>
        getExistSowTaskWaveCreateInfo(CreateBatchTaskByPassageResultBO createBatchTaskByPassageResultBO) {
        List<WaveCreateDTO> totalWaveCreateList = new ArrayList<>();
        List<CreateSowTaskResultBO> existSowTaskList = createBatchTaskByPassageResultBO.getExistSowTaskList();
        if (!CollectionUtils.isEmpty(existSowTaskList)) {
            List<WaveCreateDTO> existSowWaveCreateList = existSowTaskList.stream().map(m -> m.getWaveCreateDTOList())
                .filter(m -> !CollectionUtils.isEmpty(m)).flatMap(m -> m.stream()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(existSowWaveCreateList)) {
                totalWaveCreateList.addAll(existSowWaveCreateList);
            }
        }

        return totalWaveCreateList;
    }

    private List<WaveCreateDTO> removeExistSowTaskWaveCreateInfo(List<WaveCreateDTO> totalWaveCreateDTO,
        CreateBatchTaskByPassageResultBO createBatchTaskByPassageResultBO) {
        List<CreateSowTaskResultBO> existSowTaskList = createBatchTaskByPassageResultBO.getExistSowTaskList();

        if (CollectionUtils.isEmpty(existSowTaskList)) {
            return totalWaveCreateDTO;
        }

        List<WaveCreateDTO> existSowWaveCreateList = existSowTaskList.stream().map(m -> m.getWaveCreateDTOList())
            .filter(m -> !CollectionUtils.isEmpty(m)).flatMap(m -> m.stream()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(existSowWaveCreateList)) {
            totalWaveCreateDTO.removeIf(m -> Objects.nonNull(m.getSowId())
                && existSowWaveCreateList.stream().anyMatch(t -> m.getSowId().equals(t.getSowId())));
        }

        return totalWaveCreateDTO;
    }

    // 移除要追加的存在的sowTask的信息
    private void handleExistSowTaskInfo(CreateBatchTaskByPassageResultBO passageResultBO) {
        if (CollectionUtils.isEmpty(passageResultBO.getExistSowTaskList())) {
            return;
        }

        List<CreateSowTaskResultBO> existSowTaskBOList = passageResultBO.getExistSowTaskList();
        Map<Long, SowTaskPO> existSowTaskMap = existSowTaskBOList.stream().flatMap(m -> m.getSowTaskPOList().stream())
            .collect(Collectors.toMap(SowTaskPO::getId, v -> v));
        Map<Long, SowOrderPO> existSowOrderMap = existSowTaskBOList.stream()
            .flatMap(m -> m.getSowOrderPOList().stream()).collect(Collectors.toMap(SowOrderPO::getId, v -> v));
        if (!CollectionUtils.isEmpty(passageResultBO.getSowTaskPOList())) {
            passageResultBO.getSowTaskPOList().removeIf(po -> Objects.nonNull(existSowTaskMap.get(po.getId())));
        }

        if (!CollectionUtils.isEmpty(passageResultBO.getSowOrderPOList())) {
            passageResultBO.getSowOrderPOList().removeIf(po -> Objects.nonNull(existSowOrderMap.get(po.getId())));
        }
    }

    /**
     * 根据类目查找所有符合条件的通道策略
     *
     * @see com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderProcessBL#processPassageByCategory(BatchBO, List,
     *      List, WavesStrategyBO, Integer, String, String, String, String, Boolean, WarehouseConfigDTO, WaveCreateDTO)
     */
    public CreateBatchTaskByPassageResultBO buildBatchTaskAndSowByPassage(BatchBO batchBO, List<OutStockOrderPO> orders,
        List<OutStockOrderItemPO> totalOrderItemList, WarehouseConfigDTO warehouseConfigDTO, WaveCreateDTO oriCreateDTO,
        ExistSowTaskBO existSowTaskPO) {
        WavesStrategyBO wavesStrategyDTO = oriCreateDTO.getWavesStrategyDTO();
        Integer cityId = oriCreateDTO.getCityId();
        String title = oriCreateDTO.getTitle();
        String operateUser = oriCreateDTO.getOperateUser();
        String locationName = oriCreateDTO.getLocationName();
        String driverName = oriCreateDTO.getDriverName();
        Boolean allocationFlag = oriCreateDTO.getAllocationFlag();

        List<WaveCreateDTO> lstCreateDTO = new ArrayList<>();
        List<OutStockOrderItemPO> lstAdded = new ArrayList<>();
        // 播种任务集合
        List<SowTaskPO> sowTaskPOList = new ArrayList<>();

        List<SowOrderPO> sowOrderPOList = new ArrayList<>();

        // 获取仓库配置
        Integer warehouseId = wavesStrategyDTO.getWarehouseId();

        // 根据仓库Id查找对应的仓库通道类型
        Byte pickType = null;
        List<PassageDTO> passageList = null;
        if (Objects.equals(wavesStrategyDTO.getPassPickType(), PassagePickTypeEnum.虚仓二次分拣通道.getType())) {
            pickType = PassagePickTypeEnum.虚仓二次分拣通道.getType();
            passageList = Collections.singletonList(this.createVirtualWarehouseSecondPickPassage());
        } else {
            pickType = iPassageService.getPassageTypeByWarehouseId(warehouseId);
            if (pickType == null) {
                LOG.info(String.format("没有配置通道！仓库Id：%s", warehouseId));
                return CreateBatchTaskByPassageResultBO.getDefault();
            }
            // 1、根据类目或货位查询满足条件的通道配置
            passageList = getPassageDTOS(totalOrderItemList, warehouseId, pickType);
        }

        LOG.info("当前仓库的通道类型：{}", pickType);
        LOG.info("找到通道配置：{}", JSON.toJSONString(passageList));

        // 2、如果开启播种，查找当前仓库所有集货区，按数量平均分配
        List<GoodsCollectionLocationBO> locationList =
            sowCalculationBL.findSowCollectLocation(batchBO, passageList, wavesStrategyDTO);

        List<CreateSowTaskFinalResultBO> finalResultBOList = new ArrayList<>();
        // 3、遍历满足条件的通道配置，查找符合条件的订单
        if (!CollectionUtils.isEmpty(passageList)) {
            for (PassageDTO passageDTO : passageList) {
                // 根据通道策咯查找所有符合条件的订单详情 TODO 过滤
                List<OutStockOrderItemPO> lstItems =
                    getOutStockOrderItemByPassage(totalOrderItemList, pickType, lstAdded, passageDTO);
                if (CollectionUtils.isEmpty(lstItems)) {
                    continue;
                }

                //
                // 合并开启货位库存和 拣货 合并订单项 到这里面
                // lstItems = MergeSplitItemConvertor.mergerItemListByUuid(lstItems, lstOrderItems, lstAdded,
                // warehouseConfigDTO, wavesStrategyDTO, passageDTO);

                // 进通道之后，根据 是否开启整件拆零 和 是否是大件 重新分配货位，并过滤掉单项整件的
                lstItems = createBatchLocationLargePickCargoBL.setPackageLocationByAssociateLocation(lstItems,
                    totalOrderItemList, wavesStrategyDTO, warehouseConfigDTO);

                // 据通道，把订单项拆分，然后合并成新的订单对象
                List<OutStockOrderPO> createSowOrderList =
                    OrderRebuildUtil.getOrdersByPassageItems(orders, lstItems, warehouseConfigDTO, wavesStrategyDTO);

                // 如果通道开启播种或分区分单，处理播种信息（按配置格子数量，拆分拣货任务）

                // 获取通道下配置的集货位信息
                List<GoodsCollectionLocationBO> passageCollectionList =
                        getPassageCollectLocationList(locationList, passageDTO, wavesStrategyDTO);
                LOG.info("通道 {} 配置下的集货位为：{}", passageDTO.getPassageName(), JSON.toJSONString(passageCollectionList));
                CreateSowTaskFinalResultBO restOrderPackagePickAloneBO =
                    handleRestOrderPackagePickAlone(createSowOrderList, passageDTO, warehouseConfigDTO, batchBO,
                        passageCollectionList, oriCreateDTO, existSowTaskPO);
                if (Objects.nonNull(restOrderPackagePickAloneBO)) {
                    finalResultBOList.add(restOrderPackagePickAloneBO);
                }

                CreateSowTaskFinalResultBO createSowTaskFinalResultBO = handleNormalPassage(createSowOrderList,
                    passageDTO, warehouseConfigDTO, batchBO, passageCollectionList, oriCreateDTO, existSowTaskPO);

                if (Objects.nonNull(createSowTaskFinalResultBO)) {
                    finalResultBOList.add(createSowTaskFinalResultBO);
                }

                lstAdded.addAll(lstItems);
            }
        }
        lstAdded.clear();

        // 4、按货位通道拣货，如果存在按订单拣货的任务，开始分拆
        if (PassagePickTypeEnum.按货位通道.getType() == pickType) {
            mergeOrderByLocationPassage(orders, totalOrderItemList, lstCreateDTO);
        }

        return CreateBatchTaskByPassageResultBO.buildCreateBatchTaskByPassageResultBO(finalResultBOList);
    }

    // 处理休百整件分拨
    private CreateSowTaskFinalResultBO handleRestOrderPackagePickAlone(List<OutStockOrderPO> outStockOrderPOList,
        PassageDTO passageDTO, WarehouseConfigDTO warehouseConfigDTO, BatchBO batchBO,
        List<GoodsCollectionLocationBO> passageCollectionList, WaveCreateDTO oriCreateDTO,
        ExistSowTaskBO existSowTaskPO) {
        List<OutStockOrderPO> restOrderPackagePickAloneOrderList =
            BatchTaskKindOfPickingConvertor.hasFitRestOrderPackagePickAloneOrderList(passageDTO,
                oriCreateDTO.getWavesStrategyDTO(), outStockOrderPOList);

        if (CollectionUtils.isEmpty(restOrderPackagePickAloneOrderList)) {
            return null;
        }

        // 如果开了休百整件单独分拣，不生成播种任务，都根据拣货任务的数据处理；
        CreateSowTaskByPassageBO.CreateSowTaskByPassageBOBuilder builder =
            CreateSowTaskByPassageBO.CreateSowTaskByPassageBOBuilder.createSowTaskByPassageBO()
                .withAllocationFlag(oriCreateDTO.getAllocationFlag()).withBatchBO(batchBO)
                .withCityId(oriCreateDTO.getCityId()).withDriverName(oriCreateDTO.getDriverName())
                .withExistSowTaskPO(existSowTaskPO).withLocationName(oriCreateDTO.getLocationName())
                .withLstLocations(passageCollectionList).withWaveCreateDTOList(new ArrayList<>())
                .withNeedRecheck(Boolean.FALSE).withOperateUser(oriCreateDTO.getOperateUser())
                .withPassageDTO(passageDTO).withOriCreateDTO(oriCreateDTO)
                .withOrderList(restOrderPackagePickAloneOrderList).withSowOrderPOList(new ArrayList<>())
                .withSowTaskPOList(new ArrayList<>()).withTitle(oriCreateDTO.getTitle())
                .withWavesStrategyDTO(oriCreateDTO.getWavesStrategyDTO()).withWarehouseConfigDTO(warehouseConfigDTO);
        return CreateSowTaskFinalResultBO.createNotSowResultBO(builder.build());
    }

    // 处理正常播种订单
    private CreateSowTaskFinalResultBO handleNormalPassage(List<OutStockOrderPO> outStockOrderPOList,
        PassageDTO passageDTO, WarehouseConfigDTO warehouseConfigDTO, BatchBO batchBO,
        List<GoodsCollectionLocationBO> passageCollectionList, WaveCreateDTO oriCreateDTO,
        ExistSowTaskBO existSowTaskPO) {
        List<OutStockOrderPO> normalOrderList =
            BatchTaskKindOfPickingConvertor.notFitRestOrderPackagePickAloneOrderList(passageDTO,
                oriCreateDTO.getWavesStrategyDTO(), outStockOrderPOList);

        if (CollectionUtils.isEmpty(normalOrderList)) {
            return null;
        }

        CreateSowTaskFinalResultBO createSowTaskFinalResultBO = createSowTaskBySinglePassage(normalOrderList,
            passageDTO, warehouseConfigDTO, batchBO, passageCollectionList, oriCreateDTO, existSowTaskPO);

        return createSowTaskFinalResultBO;
    }

    /**
     *
     * @param outStockOrderPOList 出库单列表
     * @param passageDTO 通道
     * @param warehouseConfigDTO 仓库配置
     * @param batchBO 波次信息
     * @param passageCollectionList 集货位信息
     * @param oriCreateDTO 原始参数对象
     * @param existSowTaskPO 追加播种时，已经存在的播种任务
     * @return
     */
    private CreateSowTaskFinalResultBO createSowTaskBySinglePassage(List<OutStockOrderPO> outStockOrderPOList,
        PassageDTO passageDTO, WarehouseConfigDTO warehouseConfigDTO, BatchBO batchBO,
        List<GoodsCollectionLocationBO> passageCollectionList, WaveCreateDTO oriCreateDTO,
        ExistSowTaskBO existSowTaskPO) {

        WavesStrategyBO wavesStrategyBO = oriCreateDTO.getWavesStrategyDTO();
        String driverName = oriCreateDTO.getDriverName();
        Boolean allocationFlag = oriCreateDTO.getAllocationFlag();
        Integer cityId = oriCreateDTO.getCityId();
        String operateUser = oriCreateDTO.getOperateUser();
        String title = oriCreateDTO.getTitle();
        String locationName = oriCreateDTO.getLocationName();
        Integer warehouseId = wavesStrategyBO.getWarehouseId();

        if (needCreateSow(passageDTO)) {
            // FIXME 这里的orders是不是有问题？
            createReRecheckOrderRecord(outStockOrderPOList, warehouseConfigDTO.getWarehouse_Id(),
                oriCreateDTO.getCityId());
            CreateSowTaskByPassageBO.CreateSowTaskByPassageBOBuilder builder =
                CreateSowTaskByPassageBO.CreateSowTaskByPassageBOBuilder.createSowTaskByPassageBO()
                    .withAllocationFlag(allocationFlag).withBatchBO(batchBO).withCityId(cityId)
                    .withDriverName(driverName).withExistSowTaskPO(existSowTaskPO).withLocationName(locationName)
                    .withLstLocations(passageCollectionList).withWaveCreateDTOList(new ArrayList<>())
                    .withNeedRecheck(Boolean.FALSE).withOperateUser(operateUser).withPassageDTO(passageDTO)
                    .withOriCreateDTO(oriCreateDTO).withOrderList(outStockOrderPOList)
                    .withSowOrderPOList(new ArrayList<>()).withSowTaskPOList(new ArrayList<>()).withTitle(title)
                    .withWavesStrategyDTO(wavesStrategyBO).withWarehouseConfigDTO(warehouseConfigDTO);

            CreateSowTaskFinalResultBO createSowTaskFinalResultBO = sowTaskCreateBL.createSowTask(builder.build());
            // LOG.info("创建播种任务，日志:{}", JSON.toJSONString(createSowTaskFinalResultBO));
            return createSowTaskFinalResultBO;
        }

        CreateSowTaskFinalResultBO createSowTaskFinalResultBO = createWaveCreateForNotSowAndNotCheck(
            outStockOrderPOList, passageDTO, warehouseConfigDTO, batchBO, passageCollectionList, oriCreateDTO);

        return createSowTaskFinalResultBO;
    }

    // 处理走通道，但不需要创建播种任务的场景
    private CreateSowTaskFinalResultBO createWaveCreateForNotSowAndNotCheck(List<OutStockOrderPO> outStockOrderPOList,
        PassageDTO passageDTO, WarehouseConfigDTO warehouseConfigDTO, BatchBO batchBO,
        List<GoodsCollectionLocationBO> locationList, WaveCreateDTO oriCreateDTO) {
        List<WaveCreateDTO> totalWaveCreateDTOList = new ArrayList<>();
        RandomCreateProcessBO randomCreateProcessBO = randomCreateProcess(outStockOrderPOList, passageDTO,
            warehouseConfigDTO, batchBO, locationList, oriCreateDTO);

        WavesStrategyBO wavesStrategyDTO = oriCreateDTO.getWavesStrategyDTO();

        List<OutStockOrderPO> notCheckOrderList = randomCreateProcessBO.getNotCheckOrderList();
        if (CollectionUtils.isEmpty(notCheckOrderList)) {
            return CreateSowTaskFinalResultBO.getFromRandomCreateProcessBO(randomCreateProcessBO);
        }

        List<WaveCreateDTO> totalWaveCreateList = new ArrayList<>();
        // 有多个出库批次，但不符合二次分拣通道，则根据出库批次拆分出库单
        if (wavesStrategyDTO.getIsMultiOutBound()) {
            logMultiBound(passageDTO, notCheckOrderList);
            List<WaveCreateDTO> multiWaveCreateList =
                this.createWaveCreateByMultiOutBound(notCheckOrderList, wavesStrategyDTO, passageDTO, oriCreateDTO);
            totalWaveCreateList.addAll(multiWaveCreateList);
        } else {
            WaveCreateDTO createDTO = getWaveCreateDTOByPassage(notCheckOrderList, passageDTO, oriCreateDTO);
            createDTO.setPassageDTO(passageDTO);
            totalWaveCreateList.add(createDTO);
        }

        return CreateSowTaskFinalResultBO.getFromRandomCreateProcessBO(randomCreateProcessBO, totalWaveCreateList);
    }

    private boolean needCreateSow(PassageDTO passageDTO) {
        return passageDTO.getSowType() != null && passageDTO.getSowType() != SowTypeEnum.不开启.getType();
    }

    private void logMultiBound(PassageDTO passageDTO, List<OutStockOrderPO> outStockOrderPOList) {
        LOG.info("[多出库批次创建波次]未开启播种，通道：{}，出库批次号：{}，出库单号：{}", passageDTO.getPassageName(),
            outStockOrderPOList.stream().map(OutStockOrderPO::getBoundNo).distinct().collect(Collectors.toList()),
            outStockOrderPOList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
    }

    /**
     * 创建虚仓二次分拣通道
     */
    public PassageDTO createVirtualWarehouseSecondPickPassage() {
        PassageDTO virtual = new PassageDTO();
        virtual.setPassageName("虚仓二次分拣");
        virtual.setPassageType(PassagePickTypeEnum.虚仓二次分拣通道.getType());
        virtual.setSowType(SowTypeEnum.虚仓二次分拣播种.getType());
        virtual.setPickingType(PickingTypeEnum.产品拣货.getType());
        return virtual;
    }

    /**
     * 根据通道策咯查找所有符合条件的订单详情 <br />
     * 直接拷贝
     * 
     * @param lstOrderItems
     * @param pickType
     * @param lstAdded
     * @param passageDTO
     * @return
     */
    public List<OutStockOrderItemPO> getOutStockOrderItemByPassage(List<OutStockOrderItemPO> lstOrderItems,
        Byte pickType, List<OutStockOrderItemPO> lstAdded, PassageDTO passageDTO) {
        List<OutStockOrderItemPO> lstItems = new ArrayList<>();
        if (PassagePickTypeEnum.按类目通道.getType() == pickType) {
            // 根据类目Id查找所有符合条件的通道策略
            List<OutStockOrderItemPO> lstSecCagegoryItems =
                lstOrderItems.stream()
                    .filter(p -> lstAdded.stream().noneMatch(q -> q.getId().equals(p.getId()))
                        && p.getSecCategoryId() != null
                        && passageDTO.getItemList().stream()
                            .anyMatch(q -> q.getRelateId().equals(p.getSecCategoryId().toString())))
                    .collect(Collectors.toList());
            List<OutStockOrderItemPO> lstFirstCagegoryItems =
                lstOrderItems.stream()
                    .filter(p -> lstAdded.stream().noneMatch(q -> q.getId().equals(p.getId()))
                        && p.getFirstCategoryId() != null
                        && passageDTO.getItemList().stream()
                            .anyMatch(q -> q.getRelateId().equals(p.getFirstCategoryId().toString())))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(lstFirstCagegoryItems)) {
                lstItems.addAll(lstFirstCagegoryItems);
            }
            if (!CollectionUtils.isEmpty(lstSecCagegoryItems)) {
                lstItems.addAll(lstSecCagegoryItems);
            }
        } else if (PassagePickTypeEnum.按货位通道.getType() == pickType) {
            // 根据货位Id查找所有符合条件的通道策略
            lstItems = lstOrderItems.stream()
                .filter(p -> lstAdded.stream()
                    .noneMatch(q -> q.getId().equals(p.getId()) && Objects.equals(q.getLocationId(), p.getLocationId()))
                    && p.getLocationId() != null
                    && passageDTO.getItemList().stream()
                        .anyMatch(q -> q.getRelateId().equals(p.getLocationId().toString())))
                .collect(Collectors.toList());
        } else if (PassagePickTypeEnum.虚仓二次分拣通道.getType() == pickType) {
            lstItems.addAll(lstOrderItems);
        }
        return lstItems;
    }

    /**
     * 按货位通道拣货，存在按订单拣货的任务，开始分拆
     *
     * @param orders
     * @param lstOrderItems
     * @param lstCreateDTO
     */
    public void mergeOrderByLocationPassage(List<OutStockOrderPO> orders, List<OutStockOrderItemPO> lstOrderItems,
        List<WaveCreateDTO> lstCreateDTO) {
        LOG.info("按货位通道拣货，开始合并分拆订单…");
        // 先检查是否存在按订单的拣货任务，如果存在，查找拆分过的，没有分配完的项，分配到按订单拣货任务中
        List<WaveCreateDTO> lstPickByOrderDTO = lstCreateDTO.stream()
            .filter(p -> p.getWavesStrategyDTO().getPickingType() == PickingTypeEnum.订单拣货.getType())
            .collect(Collectors.toList());
        if (lstPickByOrderDTO.size() > 0) {
            LOG.info("按货位通道拣货，存在按订单拣货的任务，开始分拆…");
            lstPickByOrderDTO.forEach(p -> {
                // 重新获取所有没有分配的项，避免重复添加
                List<OutStockOrderPO> lstOtherOrders =
                    getOtherOrdersByPassageItems(orders, lstOrderItems, lstCreateDTO);
                if (CollectionUtils.isEmpty(lstOtherOrders)) {
                    return;
                }
                for (OutStockOrderPO q : p.getOrders()) {
                    // 原单有播种任务的不合并
                    Optional<OutStockOrderItemPO> sowItem =
                        q.getItems().stream().filter(item -> item.getSowTaskItemId() != null).findAny();
                    if (sowItem.isPresent()) {
                        continue;
                    }

                    Optional<OutStockOrderPO> outStockOrderPO =
                        lstOtherOrders.stream().filter(r -> r.getId().equals(q.getId())).findAny();
                    if (outStockOrderPO.isPresent()) {
                        // 添加没有分配的项，到按订单拣货的通道中
                        q.getItems().addAll(outStockOrderPO.get().getItems());
                        LOG.info(String.format("按货位通道拣货，合并拆分订单到拣货任务，%s", JSON.toJSONString(outStockOrderPO.get())));
                        // 移除
                        lstOtherOrders.remove(outStockOrderPO.get());
                    }
                }
            });
        }
        LOG.info("按货位通道拣货，结束合并分拆订单…");
    }

    public List<OutStockOrderPO> getOtherOrdersByPassageItems(List<OutStockOrderPO> orders,
        List<OutStockOrderItemPO> lstOrderItems, List<WaveCreateDTO> lstCreateDTO) {
        // 如果存在不符合通道策略的订单项，单独成一个任务
        List<OutStockOrderItemPO> lstTmpItems = new ArrayList<>();
        lstCreateDTO.forEach(p -> {
            p.getOrders().forEach(order -> {
                lstTmpItems.addAll(order.getItems());
            });
        });

        List<OutStockOrderItemPO> lstOtherItems = lstOrderItems.stream()
            .filter(p -> lstTmpItems.stream()
                .noneMatch(q -> q.getId().equals(p.getId()) && Objects.equals(q.getLocationId(), p.getLocationId())))
            .collect(Collectors.toList());
        LOG.info("不符合通道策略的订单项: {}", JSON.toJSONString(lstOtherItems));
        return OrderRebuildUtil.getOrdersByPassageItems(orders, lstOtherItems);
    }

    /**
     * 根据多个出库批次，创建WaveCreateDTO
     */
    public List<WaveCreateDTO> createWaveCreateByMultiOutBound(List<OutStockOrderPO> multiOutBoundOrderList,
        WavesStrategyBO wavesStrategyDTO, PassageDTO passageDTO, WaveCreateDTO oriCreateDTO) {

        String driverName = oriCreateDTO.getDriverName();
        Boolean allocationFlag = oriCreateDTO.getAllocationFlag();
        Integer cityId = oriCreateDTO.getCityId();
        String operateUser = oriCreateDTO.getOperateUser();
        String title = oriCreateDTO.getTitle();
        String locationName = oriCreateDTO.getLocationName();
        Integer warehouseId = wavesStrategyDTO.getWarehouseId();

        List<WaveCreateDTO> waveCreateDTOList = new ArrayList<>();
        Map<String, List<OutStockOrderPO>> outStockOrderGroup = multiOutBoundOrderList.stream()
            .collect(Collectors.groupingBy(ele -> String.format("%s", ele.getBoundNo())));
        outStockOrderGroup.forEach((boundNo, orderList) -> {
            if (Objects.equals(boundNo, "null")) {
                LOG.info("[多个出库批次创建WaveCreateDTO]出库批次号为空，订单号：{}",
                    orderList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
                throw new BusinessValidateException("多个出库批次创建波次，出库批次号为空");
            }
            WaveCreateDTO createDTO = getWaveCreateDTOByPassage(orderList, passageDTO, oriCreateDTO);

            createDTO.setPassageDTO(passageDTO);
            waveCreateDTOList.add(createDTO);
        });

        return waveCreateDTOList;
    }

    // 老接口废弃
    @Deprecated
    public void createWaveCreateByMultiOutBound(List<OutStockOrderPO> multiOutBoundOrderList,
        WavesStrategyBO wavesStrategyDTO, String title, String operateUser, Integer cityId, String locationName,
        String driverName, Boolean allocationFlag, PassageDTO passageDTO, List<WaveCreateDTO> lstCreateDTO,
        WaveCreateDTO oriCreateDTO) {
        Map<String, List<OutStockOrderPO>> outStockOrderGroup = multiOutBoundOrderList.stream()
            .collect(Collectors.groupingBy(ele -> String.format("%s", ele.getBoundNo())));
        outStockOrderGroup.forEach((boundNo, orderList) -> {
            if (Objects.equals(boundNo, "null")) {
                LOG.info("[多个出库批次创建WaveCreateDTO]出库批次号为空，订单号：{}",
                    orderList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
                throw new BusinessValidateException("多个出库批次创建波次，出库批次号为空");
            }
            WaveCreateDTO createDTO = getWaveCreateDTOByPassage(orderList, passageDTO, oriCreateDTO);

            createDTO.setPassageDTO(passageDTO);
            lstCreateDTO.add(createDTO);
        });
    }

    /**
     * 根据类目或货位查询满足条件的通道配置
     *
     * @return
     */
    public List<PassageDTO> getPassageDTOS(List<OutStockOrderItemPO> lstOrderItems, Integer warehouseId,
        Byte pickType) {
        List<String> lstRelateList = new ArrayList<>();
        if (PassagePickTypeEnum.按类目通道.getType() == pickType) {
            setCategoryId(lstOrderItems);
            // 获取所有类目Id集合
            List<String> lstSecCategory = lstOrderItems.stream().filter(p -> p.getSecCategoryId() != null)
                .map(p -> p.getSecCategoryId().toString()).distinct().collect(Collectors.toList());
            List<String> lstFirstCategory = lstOrderItems.stream().filter(p -> p.getFirstCategoryId() != null)
                .map(p -> p.getFirstCategoryId().toString()).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(lstSecCategory)) {
                lstRelateList.addAll(lstSecCategory);
            }
            if (!CollectionUtils.isEmpty(lstFirstCategory)) {
                lstRelateList.addAll(lstFirstCategory);
            }
        } else if (PassagePickTypeEnum.按货位通道.getType() == pickType) {
            // 获取所有货位Id集合
            lstRelateList = lstOrderItems.stream().filter(p -> p.getLocationId() != null)
                .map(p -> p.getLocationId().toString()).distinct().collect(Collectors.toList());
        }
        // 根据类目或货位查询满足条件的通道配置
        PassageItemSO passageItemSO = new PassageItemSO();
        passageItemSO.setWarehouseId(warehouseId);
        passageItemSO.setRelateType(pickType);
        passageItemSO.setRelateIdList(lstRelateList);
        passageItemSO.setState(ConditionStateEnum.是.getType());
        return iPassageService.listPassageByRelate(passageItemSO);
    }

    public void setCategoryId(List<OutStockOrderItemPO> lstOrderItems) {
        List<Long> skuIdList = lstOrderItems.stream().filter(p -> p.getSkuid() != null)
            .map(OutStockOrderItemPO::getSkuid).distinct().collect(Collectors.toList());
        LOG.info("根据skuid查询类目:skuIdSet:{}", JSON.toJSONString(skuIdList));
        // Map<Long, ProductInfoSpecification> map =
        // productInfoSpecificationQueryService.getProductInfoSpecificationMap(skuIdSet);
        Map<Long, ProductCategoryDTO> categoryBySkuIds =
            iProductCategoryService.findCategoryBySkuIds(Lists.newArrayList(skuIdList));
        // LOG.info("根据skuid查询类目map:{}", JSON.toJSONString(map));
        lstOrderItems.forEach(e -> {
            ProductCategoryDTO categoryDTO = categoryBySkuIds.get(e.getSkuid());
            if (categoryDTO != null) {
                e.setFirstCategoryId(categoryDTO.getStatisticsClass());
                e.setSecCategoryId(categoryDTO.getSecondStatisticsClass());
                // 类目名称
                e.setFirstCategoryName(categoryDTO.getStatisticsClassName());
                e.setSecCategoryName(categoryDTO.getSecondStatisticsClassName());
            }
        });
    }

    public List<GoodsCollectionLocationBO> getPassageCollectLocationList(List<GoodsCollectionLocationBO> locationList,
        PassageDTO passageDTO, WavesStrategyBO wavesStrategyBO) {
        PassageItemSO passageItemSO = new PassageItemSO();
        passageItemSO.setPassageId(passageDTO.getId());
        passageItemSO.setRelateType(PassageRelateTypeEnum.集货位.getType());
        passageItemSO.setWarehouseId(passageDTO.getWarehouseId());
        List<PassageItemDTO> collectLocationList = iPassageService.findPassageItem(passageItemSO);
        if (CollectionUtils.isEmpty(collectLocationList)) {
            return locationList;
        }

        Map<String, String> collectLocationMap = collectLocationList.stream()
            .collect(Collectors.toMap(PassageItemDTO::getRelateId, PassageItemDTO::getRelateId, (k1, k2) -> k1));
        List<GoodsCollectionLocationBO> filterList = locationList.stream()
            .filter(m -> Objects.nonNull(collectLocationMap.get(m.getId().toString()))).collect(Collectors.toList());

        filterList = createSowTaskByOutStockOrderBL.filterAvailableCollectionLocation(wavesStrategyBO, filterList);
        if (CollectionUtils.isEmpty(filterList)) {
            // 开启了通道的
            if ((passageDTO.getSowType() != null && passageDTO.getSowType() != SowTypeEnum.不开启.getType())) {
                if (globalCache.openDigitalTag(wavesStrategyBO.getWarehouseId())) {
                    throw new BusinessValidateException("暂无空余集货位和播种台，请等待已有播种任务完成后再生成新任务！");
                }

                return locationList;
            }

            return locationList;
        }

        // 没开启通道的
        return filterList;
    }

    public WaveCreateDTO getWaveCreateDTOByPassage(List<OutStockOrderPO> orders, PassageDTO passageDTO,
        WaveCreateDTO oriCreateDTO) {
        WavesStrategyBO wavesStrategyDTO = oriCreateDTO.getWavesStrategyDTO();
        String title = oriCreateDTO.getTitle();
        String operateUser = oriCreateDTO.getOperateUser();
        Integer cityId = oriCreateDTO.getCityId();
        String locationName = oriCreateDTO.getLocationName();
        String driverName = oriCreateDTO.getDriverName();
        Boolean allocationFlag = oriCreateDTO.getAllocationFlag();

        return getWaveCreateDTOByPassage(orders, wavesStrategyDTO, passageDTO, title, operateUser, cityId, locationName,
            driverName, allocationFlag, oriCreateDTO);
    }

    public WaveCreateDTO getWaveCreateDTOByPassage(List<OutStockOrderPO> orders, WavesStrategyBO wavesStrategyDTO,
        PassageDTO passageDTO, String title, String operateUser, Integer cityId, String locationName, String driverName,
        Boolean allocationFlag, WaveCreateDTO oriCreateDTO) {
        WaveCreateDTO createDTO = new WaveCreateDTO();
        createDTO.setCityId(cityId);
        createDTO.setTitle(title);
        createDTO.setOperateUser(operateUser);
        createDTO.setOperateUserId(oriCreateDTO.getOperateUserId());
        createDTO.setOrders(orders);
        createDTO.setLocationName(locationName);
        createDTO.setDriverName(driverName);
        createDTO.setAllocationFlag(allocationFlag);
        createDTO.setPassageDTO(passageDTO);
        WavesStrategyBO waveDTO = new WavesStrategyBO();
        wavesStrategyDTO.setCreateTime(new Date());
        wavesStrategyDTO.setLastUpdateTime(new Date());
        BeanUtils.copyProperties(wavesStrategyDTO, waveDTO);
        waveDTO.setPickingType(passageDTO.getPickingType());

        String secondSortDriverName = getDriverName(wavesStrategyDTO, orders);
        if (StringUtils.isNotEmpty(secondSortDriverName)) {
            createDTO.setDriverName(secondSortDriverName);
        }

        // 如果是按订单拣货，拣货分组策略是没用的
        // 如果是按产品拣货，拣货分组策略默认是按类目
        Byte pickingGroupStrategy = passageDTO.getPickingGroupStrategy();
        if ((pickingGroupStrategy == null || pickingGroupStrategy.intValue() == 0)
            && WavesStrategyConstants.PICKINGTYPE_PRODUCT == passageDTO.getPickingType().intValue()) {
            // 如果是按产品拣货，拣货分组方式默认为按类目拣货
            pickingGroupStrategy = (byte)WavesStrategyConstants.PICKINGGROUPSTRATEGY_CATEGORY;
        }
        Byte sowType = passageDTO.getSowType();
        Byte passageType = passageDTO.getPassageType();
        Byte pickingType = passageDTO.getPickingType();

        // 如果是分区分单的订单拣货，拣货分组策略不变
        if (sowType != null
            && (sowType == SowTypeEnum.分区分单播种.getType() || sowType == SowTypeEnum.分区分单不播种.getType()
                || sowType == SowTypeEnum.分区分单拣货.getType())
            && WavesStrategyConstants.PICKINGTYPE_ORDER == pickingType.intValue()) {
            if (passageType != null && passageType == PassagePickTypeEnum.按货位通道.getType()) {
                pickingGroupStrategy = (byte)WavesStrategyConstants.PICKINGGROUPSTRATEGY_LOCATION;
            } else {
                pickingGroupStrategy = (byte)WavesStrategyConstants.PICKINGGROUPSTRATEGY_CATEGORY;
            }
        }
        waveDTO.setPickingGroupStrategy(pickingGroupStrategy);
        createDTO.setWavesStrategyDTO(waveDTO);
        createDTO.setIsRobotPick(passageDTO.getIsRobotPick());
        return createDTO;
    }

    public static String getDriverName(WavesStrategyDTO wavesStrategyDTO, List<OutStockOrderPO> orders) {
        if (Objects.isNull(wavesStrategyDTO)) {
            return null;
        }
        if (BooleanUtils.isFalse(wavesStrategyDTO.getIsOpenSecondSort())) {
            return null;
        }
        if (BooleanUtils.isFalse(wavesStrategyDTO.getIsMultiOutBound())) {
            return null;
        }
        Optional<OutStockOrderPO> optional =
            orders.stream().filter(m -> StringUtils.isNotEmpty(m.getPickUpUserName())).findAny();
        if (!optional.isPresent()) {
            return null;
        }
        OutStockOrderPO outStockOrderPO = optional.get();
        return outStockOrderPO.getPickUpUserName();
    }

    /**
     * 校验是否需要复核,需要则插入复核记录表
     *
     * @param orderPOS
     * @param warehouseId
     * @param orgId
     */
    public void createReRecheckOrderRecord(List<OutStockOrderPO> orderPOS, Integer warehouseId, Integer orgId) {
        try {
            List<String> orderList = orderPOS.stream().filter(it -> StringUtils.isNotEmpty(it.getReforderno()))
                .map(OutStockOrderPO::getReforderno).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderList)) {
                return;
            }
            SyncOrderReCheckDTO syncOrderReCheckDTO = new SyncOrderReCheckDTO();
            syncOrderReCheckDTO.setOrderNos(orderList);
            syncOrderReCheckDTO.setOrgId(orgId);
            syncOrderReCheckDTO.setWarehouseId(warehouseId);
            LOG.info("复核插入记录:{}", JSON.toJSONString(syncOrderReCheckDTO));
            iOrderReCheckStrategyService.syncOrderReCheck(syncOrderReCheckDTO);
        } catch (Exception e) {
            LOG.info("复核插入记录失败", e);
        }
    }

    /**
     * 确认是否抽检
     *
     * @param orderPOS
     * @param warehouseId
     * @param orgId
     * @return
     */
    public Map<String, Boolean> checkIsRandomRecheck(List<OutStockOrderPO> orderPOS, Integer warehouseId,
        Integer orgId) {
        Map<String, Boolean> checkOrderRecheckMap = new HashMap<>();
        try {
            List<String> orderList = orderPOS.stream().filter(it -> StringUtils.isNotEmpty(it.getReforderno()))
                .map(OutStockOrderPO::getReforderno).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderList)) {
                return null;
            }
            SyncOrderReCheckDTO syncOrderReCheckDTO = new SyncOrderReCheckDTO();
            syncOrderReCheckDTO.setOrderNos(orderList);
            syncOrderReCheckDTO.setOrgId(orgId);
            syncOrderReCheckDTO.setWarehouseId(warehouseId);
            LOG.info("确认是否抽检参数:{}", JSON.toJSONString(syncOrderReCheckDTO));
            checkOrderRecheckMap = iOrderReCheckStrategyService.checkOrderRecheck(syncOrderReCheckDTO);
            LOG.info("确认是否抽检结果:{}", JSON.toJSONString(checkOrderRecheckMap));
        } catch (Exception e) {
            LOG.info("确认是否抽检失败", e);
            for (OutStockOrderPO orderPO : orderPOS) {
                checkOrderRecheckMap.put(orderPO.getReforderno(), false);
            }
        }
        return checkOrderRecheckMap;
    }

    public RandomCreateProcessBO randomCreateProcess(List<OutStockOrderPO> outStockOrderPOList, PassageDTO passageDTO,
        WarehouseConfigDTO warehouseConfigDTO, BatchBO batchBO, List<GoodsCollectionLocationBO> locationList,
        WaveCreateDTO oriCreateDTO) {
        WavesStrategyBO wavesStrategyDTO = oriCreateDTO.getWavesStrategyDTO();
        String driverName = oriCreateDTO.getDriverName();
        Boolean allocationFlag = oriCreateDTO.getAllocationFlag();
        Integer cityId = oriCreateDTO.getCityId();
        String operateUser = oriCreateDTO.getOperateUser();
        String title = oriCreateDTO.getTitle();
        String locationName = oriCreateDTO.getLocationName();
        Integer warehouseId = wavesStrategyDTO.getWarehouseId();

        Map<String, Boolean> needReCheckMap = checkIsRandomRecheck(outStockOrderPOList, warehouseId, cityId);

        if (CollectionUtils.isEmpty(needReCheckMap)) {
            return RandomCreateProcessBO.getDefault(outStockOrderPOList);
        }

        // fixme 优化
        List<OutStockOrderPO> checkOrderList = new ArrayList<>();
        List<OutStockOrderPO> notCheckOrderList = new ArrayList<>();
        outStockOrderPOList.forEach(it -> {
            Boolean needReCheck = needReCheckMap.get(it.getReforderno());
            if (needReCheck != null && needReCheck) {
                checkOrderList.add(it);
            } else {
                notCheckOrderList.add(it);
            }
        });
        if (CollectionUtils.isEmpty(checkOrderList)) {
            return RandomCreateProcessBO.getDefault(outStockOrderPOList);
        }

        splitBatchTaskByPassageBL.createReRecheckOrderRecord(checkOrderList, warehouseId, cityId);
        CreateSowTaskByPassageBO.CreateSowTaskByPassageBOBuilder builder =
            CreateSowTaskByPassageBO.CreateSowTaskByPassageBOBuilder.createSowTaskByPassageBO()
                .withAllocationFlag(allocationFlag).withBatchBO(batchBO).withCityId(cityId).withDriverName(driverName)
                .withExistSowTaskPO(null).withLocationName(locationName).withLstLocations(locationList)
                .withWaveCreateDTOList(new ArrayList<>()).withNeedRecheck(Boolean.FALSE).withOperateUser(operateUser)
                .withPassageDTO(passageDTO).withOriCreateDTO(oriCreateDTO).withOrderList(checkOrderList)
                .withSowOrderPOList(new ArrayList<>()).withSowTaskPOList(new ArrayList<>()).withTitle(title)
                .withWavesStrategyDTO(wavesStrategyDTO).withWarehouseConfigDTO(warehouseConfigDTO);

        CreateSowTaskFinalResultBO createSowTaskFinalResultBO = sowTaskCreateBL.createSowTask(builder.build());
        // sowCalculationBL.processSowTask(newLstOrders, wavesStrategyDTO, passageDTO, title, operateUser, cityId,
        // warehouseConfigDTO, batchBO, lstCreateDTO, locationList, lstSowTaskPO, locationName, lstSowOrders,
        // driverName, allocationFlag, true, oriCreateDTO);

        return RandomCreateProcessBO.getFromCreateSowTaskFinalResultBO(notCheckOrderList, createSowTaskFinalResultBO);
    }

    /**
     * 设置虚仓二次分拣的收货位
     */
    public void setVirtualSecondPickTakeLocation(Byte passPickType, OutStockOrderPO order, Long toLocationId,
        String toLocationName) {
        // 虚仓二次分拣、出库单没有收货位，则设置收货位
        if (!Objects.equals(passPickType, PassagePickTypeEnum.虚仓二次分拣通道.getType()) || toLocationId == null
            || order.getAreaId() != null) {
            return;
        }
        order.setAreaId(toLocationId);
        order.setAreaName(toLocationName);
        LOG.info("[虚仓二次分拣]设置收货位，出库单号：{}，收货位：{}", order.getReforderno(), toLocationName);
    }

}
