package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.io.Serializable;
import java.util.List;

/**
 * 销售单快递批量发货通知订单中台DTO
 * 
 * <AUTHOR>
 * @Date 2022/1/20
 */
public class ExpressDispatchNotifyDTO implements Serializable {

    private static final long serialVersionUID = -920200857488448168L;
    /**
     * 订单id
     */

    private Long orderId;
    /**
     * 操作人Id
     */
    private String optUserId;

    /**
     * 操作人
     */
    private String optUserName;

    /**
     * 发货类型(0正常发货，1缺货发货)
     */
    private Integer dispatchType;

    private List<OrderExpressDispatchDTO> orderExpressDispatchDTOList;

    /**
     * 订单出库明细
     */
    private List<OrderOutStockNotifyDTO.OrderItemOutStockDTO> orderItemOutStockList;

    public static class OrderExpressDispatchDTO implements Serializable {
        private static final long serialVersionUID = -8478489820252630098L;
        /**
         * 包裹id
         */
        private Long expressId;
        /**
         * 订单id
         */
        private Long orderId;
        /**
         * 物流商id（编码）
         */
        private String logisticsId;
        /**
         * 物流商
         */
        private String logisticName;
        /**
         * 物流快递单号
         */
        private String trackNumber;
        /**
         * 发货人手机号
         */
        private String customerNo;
        /**
         * 配送状态
         */
        private Integer deliveryState;
        /**
         * 操作人id
         */
        private String optUserId;
        /**
         * 操作人名称
         */
        private String optUserName;

        public Long getExpressId() {
            return expressId;
        }

        public void setExpressId(Long expressId) {
            this.expressId = expressId;
        }

        public Long getOrderId() {
            return orderId;
        }

        public void setOrderId(Long orderId) {
            this.orderId = orderId;
        }

        public String getLogisticsId() {
            return logisticsId;
        }

        public void setLogisticsId(String logisticsId) {
            this.logisticsId = logisticsId;
        }

        public String getLogisticName() {
            return logisticName;
        }

        public void setLogisticName(String logisticName) {
            this.logisticName = logisticName;
        }

        public String getTrackNumber() {
            return trackNumber;
        }

        public void setTrackNumber(String trackNumber) {
            this.trackNumber = trackNumber;
        }

        public String getCustomerNo() {
            return customerNo;
        }

        public void setCustomerNo(String customerNo) {
            this.customerNo = customerNo;
        }

        public Integer getDeliveryState() {
            return deliveryState;
        }

        public void setDeliveryState(Integer deliveryState) {
            this.deliveryState = deliveryState;
        }

        public String getOptUserId() {
            return optUserId;
        }

        public void setOptUserId(String optUserId) {
            this.optUserId = optUserId;
        }

        public String getOptUserName() {
            return optUserName;
        }

        public void setOptUserName(String optUserName) {
            this.optUserName = optUserName;
        }
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(String optUserId) {
        this.optUserId = optUserId;
    }

    public String getOptUserName() {
        return optUserName;
    }

    public void setOptUserName(String optUserName) {
        this.optUserName = optUserName;
    }

    public Integer getDispatchType() {
        return dispatchType;
    }

    public void setDispatchType(Integer dispatchType) {
        this.dispatchType = dispatchType;
    }

    public List<OrderExpressDispatchDTO> getOrderExpressDispatchDTOList() {
        return orderExpressDispatchDTOList;
    }

    public void setOrderExpressDispatchDTOList(List<OrderExpressDispatchDTO> orderExpressDispatchDTOList) {
        this.orderExpressDispatchDTOList = orderExpressDispatchDTOList;
    }

    public List<OrderOutStockNotifyDTO.OrderItemOutStockDTO> getOrderItemOutStockList() {
        return orderItemOutStockList;
    }

    public void setOrderItemOutStockList(List<OrderOutStockNotifyDTO.OrderItemOutStockDTO> orderItemOutStockList) {
        this.orderItemOutStockList = orderItemOutStockList;
    }
}
