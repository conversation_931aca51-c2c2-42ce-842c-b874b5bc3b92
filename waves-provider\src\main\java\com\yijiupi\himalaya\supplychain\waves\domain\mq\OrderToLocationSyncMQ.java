package com.yijiupi.himalaya.supplychain.waves.domain.mq;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.WaveNoModel;

/**
 * 订单对应的出库位同步
 *
 * <AUTHOR>
 * @date 2019/12/2 11:36
 */
@Component
public class OrderToLocationSyncMQ {

    private static final Logger LOG = LoggerFactory.getLogger(OrderToLocationSyncMQ.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${ex.supplychain.batchTask.orderToLocation}")
    private String orderToLocationExchange;

    /**
     * 发送消息
     */
    public void send(WaveNoModel waveNoModel) {
        if (waveNoModel != null) {
            LOG.info("订单对应的出库位同步发送消息:{}", JSON.toJSONString(waveNoModel));
            rabbitTemplate.convertAndSend(orderToLocationExchange, null, waveNoModel);
        }
    }
}
