package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/25
 */
public class SowTaskInfoDTO implements Serializable {
    private static final long serialVersionUID = -9216784580392734964L;
    /**
     * 播种任务ID
     */
    private Long id;
    /**
     * 播种任务号
     */
    private String sowTaskNo;
    /**
     * 播种任务项ID
     */
    private List<SowTaskItemDTO> items;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public List<SowTaskItemDTO> getItems() {
        return items;
    }

    public void setItems(List<SowTaskItemDTO> items) {
        this.items = items;
    }
}
