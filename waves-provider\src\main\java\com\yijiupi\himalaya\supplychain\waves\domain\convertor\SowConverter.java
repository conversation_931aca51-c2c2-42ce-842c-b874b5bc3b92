package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.google.common.base.Objects;
import com.google.gson.Gson;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor.MergeSplitItemConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.GoodsCollectionLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.SowSequenceResetBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderChangeDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemChangeDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskCancelDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;

public class SowConverter {
    private static final Gson GSON = new Gson();
    private static final Logger LOG = LoggerFactory.getLogger(SowConverter.class);

    public static List<SowTaskDTO> sowTaskPOS2SowtaskDTOS(List<SowTaskPO> dataList) {
        List<SowTaskDTO> sowTaskDTOS = new ArrayList<>();
        dataList.forEach(sowTaskPO -> {
            SowTaskDTO sowTaskDTO = new SowTaskDTO();
            BeanUtils.copyProperties(sowTaskPO, sowTaskDTO);
            sowTaskDTOS.add(sowTaskDTO);
        });
        return sowTaskDTOS;
    }

    public static List<SowTaskItemDTO> sowTaskItemPOS2SowtaskItemDTOS(List<SowTaskItemPO> dataList) {
        List<SowTaskItemDTO> sowTaskDTOS = new ArrayList<>();
        dataList.forEach(sowTaskItemPO -> {
            SowTaskItemDTO sowTaskItemDTO = new SowTaskItemDTO();
            BeanUtils.copyProperties(sowTaskItemPO, sowTaskItemDTO);
            sowTaskItemDTO.setToLocationId(sowTaskItemPO.getLocationId());
            sowTaskItemDTO.setToLocationName(sowTaskItemPO.getLocationName());
            sowTaskDTOS.add(sowTaskItemDTO);
        });
        return sowTaskDTOS;
    }

    public static List<SowOrderInfoDTO> sowOrderInfoPOS2SowOrderInfoDTOS(List<SowOrderInfoPO> dataList) {
        List<SowOrderInfoDTO> sowOrderInfoDTOS = new ArrayList<>();
        dataList.forEach(sowOrderInfoPO -> {
            SowOrderInfoDTO sowOrderInfoDTO = new SowOrderInfoDTO();
            BeanUtils.copyProperties(sowOrderInfoPO, sowOrderInfoDTO);
            sowOrderInfoDTOS.add(sowOrderInfoDTO);
        });
        return sowOrderInfoDTOS;
    }

    public static BatchStateEnum setId() {
        return BatchStateEnum.SOWN;
    }

    public static List<OutStockOrderChangeDTO>
        sowTaskCancelDTOS2OutStockOrderChangeDTOS(List<SowTaskCancelDTO> sowTaskCancelDTOS) {
        List<OutStockOrderChangeDTO> outStockOrderChangeDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(sowTaskCancelDTOS)) {
            return outStockOrderChangeDTOS;
        }

        sowTaskCancelDTOS.forEach(task -> {
            OutStockOrderChangeDTO outStockOrderChangeDTO = new OutStockOrderChangeDTO();
            outStockOrderChangeDTO.setRefOrderNo(task.getOrderNo());
            outStockOrderChangeDTO.setId(task.getOrderId());
            outStockOrderChangeDTO.setChangeType(task.getChangeType());
            outStockOrderChangeDTO.setOperatorUser(task.getOperatorName());
            outStockOrderChangeDTO.setCityId(task.getCityId());
            outStockOrderChangeDTO.setWarehouseId(task.getWarehouseId());
            List<OutStockOrderItemChangeDTO> itemList = new ArrayList<>();
            task.getSowTaskCancelItemDTOS().forEach(item -> {
                OutStockOrderItemChangeDTO itemChangeDTO = new OutStockOrderItemChangeDTO();
                itemChangeDTO.setId(item.getOrderItemId());
                itemChangeDTO.setSkuId(item.getSkuId());
                itemChangeDTO.setChangeCount(item.getBatchTaskChangeCount());
                itemChangeDTO.setSowTaskId(item.getSowTaskId());
                itemChangeDTO.setSowTaskNo(item.getSowTaskNo());
                itemList.add(itemChangeDTO);
            });

            if (CollectionUtils.isNotEmpty(itemList)) {
                outStockOrderChangeDTO.setItemList(itemList);
                outStockOrderChangeDTOS.add(outStockOrderChangeDTO);
            }
        });

        return outStockOrderChangeDTOS;
    }

    public static SowTaskDTO sowTaskPO2SowTaskDTO(SowTaskPO sowTaskPO) {
        if (sowTaskPO == null) {
            return null;
        }
        SowTaskDTO sowTaskDTO = new SowTaskDTO();
        BeanUtils.copyProperties(sowTaskPO, sowTaskDTO);
        if (CollectionUtils.isNotEmpty(sowTaskPO.getSowTaskItemPOS())) {
            sowTaskDTO.setSowTaskItemDTOS(sowTaskItemPOS2SowTaskItemDTOS(sowTaskPO.getSowTaskItemPOS()));
        }
        return sowTaskDTO;
    }

    private static List<SowTaskItemDTO> sowTaskItemPOS2SowTaskItemDTOS(List<SowTaskItemPO> sowTaskItemPOS) {
        if (CollectionUtils.isEmpty(sowTaskItemPOS)) {
            return null;
        }
        List<SowTaskItemDTO> sowTaskItemDTOS = new ArrayList<>();
        sowTaskItemPOS.forEach(po -> {
            SowTaskItemDTO dto = new SowTaskItemDTO();
            BeanUtils.copyProperties(po, dto);

            sowTaskItemDTOS.add(dto);
        });
        return sowTaskItemDTOS;
    }

    public static SowTaskPO getSowTaskPO(Integer sowNum, Long sowId, String sowNo,
        List<OutStockOrderPO> lstSowTaskOrders, BatchPO batchPO, GoodsCollectionLocationBO toLocation) {
        List<OutStockOrderItemPO> lstTmpItems = new ArrayList<>();
        lstSowTaskOrders.forEach(p -> {
            lstTmpItems.addAll(p.getItems());
        });

        SowTaskPO sowTaskPO = new SowTaskPO();
        String sowTaskName = String.format("%s-%s", batchPO.getBatchName(), sowNum);
        int skuCount =
            lstTmpItems.stream().map(OutStockOrderItemPO::getSkuid).distinct().collect(Collectors.toList()).size();
        sowTaskPO.setId(sowId);
        sowTaskPO.setOrgId(batchPO.getOrgId());
        sowTaskPO.setWarehouseId(batchPO.getWarehouseId());
        sowTaskPO.setBatchId(batchPO.getId());
        sowTaskPO.setBatchNo(batchPO.getBatchNo());
        sowTaskPO.setSowTaskNo(sowNo);
        sowTaskPO.setOrderCount(lstTmpItems.stream().map(OutStockOrderItemPO::getOutstockorderId).distinct()
            .collect(Collectors.toList()).size());
        sowTaskPO.setSkuCount(skuCount);
        sowTaskPO.setCreateUser(batchPO.getCreateUser());
        if (null != toLocation) {
            sowTaskPO.setLocationId(toLocation.getId());
            sowTaskPO.setLocationName(toLocation.getName());
        }

        if (lstTmpItems.get(0).getSow() != null) {
            if (ConditionStateEnum.是.getType().equals(lstTmpItems.get(0).getSow())
                && ConditionStateEnum.是.getType().equals(lstTmpItems.get(0).getPick())) {
                sowTaskPO.setOperationMode(OperationModeEnum.拣货播种.getType());
            } else {
                sowTaskPO.setOperationMode(OperationModeEnum.不拣货播种.getType());
            }
            if (skuCount == 1) {
                sowTaskName = lstTmpItems.get(0).getProductname();
            } else {
                sowTaskName = lstTmpItems.get(0).getProductname() + "等" + skuCount + "个产品";
            }
        }
        sowTaskPO.setSowTaskName(sowTaskName);

        // 增加明细
        List<SowTaskItemPO> sowTaskItemPOS = new ArrayList<>();
        lstSowTaskOrders.stream().flatMap(order -> order.getItems().stream())
            .collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid)).forEach((skuId, items) -> {
                SowTaskItemPO sowTaskItemPO = new SowTaskItemPO();
                sowTaskItemPO.setId(UuidUtil.getUUidInt());
                sowTaskItemPO.setOrgId(sowTaskPO.getOrgId());
                sowTaskItemPO.setWarehouseId(sowTaskPO.getWarehouseId());
                sowTaskItemPO.setState(SowTaskStateEnum.待播种.getType());
                sowTaskItemPO.setSowTaskId(sowId);
                sowTaskItemPO.setSowTaskNo(sowNo);
                sowTaskItemPO.setProductSkuId(skuId);
                sowTaskItemPO.setProductName(items.get(0).getProductname());
                sowTaskItemPO.setProductSpecificationId(items.get(0).getProductSpecificationId());
                sowTaskItemPO.setOwnerId(items.get(0).getOwnerId());
                sowTaskItemPO.setSecOwnerId(items.get(0).getSecOwnerId());
                sowTaskItemPO.setSpecQuantity(items.get(0).getSpecquantity());
                sowTaskItemPO.setSpecName(items.get(0).getSpecname());
                sowTaskItemPO.setPackageName(items.get(0).getPackagename());
                sowTaskItemPO.setUnitName(items.get(0).getUnitname());
                BigDecimal unitTotalCount =
                    items.stream().map(OutStockOrderItemPO::getUnittotalcount).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal[] divideAndRemainder = unitTotalCount.divideAndRemainder(items.get(0).getSpecquantity());
                BigDecimal packageCount = divideAndRemainder[0];
                BigDecimal unitCount = divideAndRemainder[1];
                sowTaskItemPO.setPackageCount(packageCount);
                sowTaskItemPO.setUnitCount(unitCount);
                sowTaskItemPO.setUnitTotalCount(unitTotalCount);

                sowTaskItemPOS.add(sowTaskItemPO);

                items.forEach(item -> {
                    item.setSowTaskItemId(sowTaskItemPO.getId());
                    item.setSowTaskId(sowId);
                    item.setSowTaskNo(sowNo);
                });
            });
        BigDecimal packageAmount =
            sowTaskItemPOS.stream().map(SowTaskItemPO::getPackageCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal unitAmount =
            sowTaskItemPOS.stream().map(SowTaskItemPO::getUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        sowTaskPO.setPackageAmount(packageAmount);
        sowTaskPO.setUnitAmount(unitAmount);
        sowTaskPO.setSowTaskItemPOS(sowTaskItemPOS);
        sowTaskPO.setPackageState(SowPackageStateEnum.待打包.getType());
        return sowTaskPO;
    }

    public static List<SowTaskItemPO> getSowTaskItemPO(SowTaskPO sowTaskPO, List<OutStockOrderPO> lstSowTaskOrders) {
        List<OutStockOrderItemPO> lstTmpItems = lstSowTaskOrders.stream()
            .filter(it -> CollectionUtils.isNotEmpty(it.getItems())).flatMap(it -> it.getItems().stream())
            .filter(it -> it.getSowTaskItemId() == null).collect(Collectors.toList());

        // 增加明细
        List<SowTaskItemPO> sowTaskItemPOS = new ArrayList<>();
        lstTmpItems.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid)).forEach((skuId, items) -> {
            SowTaskItemPO sowTaskItemPO = new SowTaskItemPO();
            sowTaskItemPO.setId(UuidUtil.getUUidInt());
            sowTaskItemPO.setOrgId(sowTaskPO.getOrgId());
            sowTaskItemPO.setWarehouseId(sowTaskPO.getWarehouseId());
            sowTaskItemPO.setState(SowTaskStateEnum.待播种.getType());
            sowTaskItemPO.setSowTaskId(sowTaskPO.getId());
            sowTaskItemPO.setSowTaskNo(sowTaskPO.getSowTaskNo());
            sowTaskItemPO.setProductSkuId(skuId);
            sowTaskItemPO.setProductName(items.get(0).getProductname());
            sowTaskItemPO.setProductSpecificationId(items.get(0).getProductSpecificationId());
            sowTaskItemPO.setOwnerId(items.get(0).getOwnerId());
            sowTaskItemPO.setSecOwnerId(items.get(0).getSecOwnerId());
            sowTaskItemPO.setSpecQuantity(items.get(0).getSpecquantity());
            sowTaskItemPO.setSpecName(items.get(0).getSpecname());
            sowTaskItemPO.setPackageName(items.get(0).getPackagename());
            sowTaskItemPO.setUnitName(items.get(0).getUnitname());
            BigDecimal unitTotalCount =
                items.stream().map(OutStockOrderItemPO::getUnittotalcount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal[] divideAndRemainder = unitTotalCount.divideAndRemainder(items.get(0).getSpecquantity());
            BigDecimal packageCount = divideAndRemainder[0];
            BigDecimal unitCount = divideAndRemainder[1];
            sowTaskItemPO.setPackageCount(packageCount);
            sowTaskItemPO.setUnitCount(unitCount);
            sowTaskItemPO.setUnitTotalCount(unitTotalCount);
            sowTaskItemPOS.add(sowTaskItemPO);
            items.forEach(item -> {
                item.setSowTaskItemId(sowTaskItemPO.getId());
                item.setSowTaskId(sowTaskPO.getId());
                item.setSowTaskNo(sowTaskPO.getSowTaskNo());
            });
        });
        return sowTaskItemPOS;
    }

    public static List<SowOrderPO> getSowOrderPOS(SowTaskPO sowTaskPO, List<OutStockOrderPO> outStockOrderPOs,
        WarehouseConfigDTO warehouseConfigDTO) {
        return getDefaultSowOrderPOS(sowTaskPO, outStockOrderPOs, warehouseConfigDTO);
    }

    /**
     * 根据播种任务和波次任务封装播种任务与订单信息列表
     *
     * @param sowTaskPO
     * @return
     */
    public static List<SowOrderPO> getSecondSowOrderPOS(SowTaskPO sowTaskPO, List<OutStockOrderPO> outStockOrderPOs,
        WarehouseConfigDTO warehouseConfigDTO, List<OutStockOrderPO> oriOutStockOrderPOs) {
        List<SowOrderPO> sowOrderPOS = new ArrayList<>();

        // key 订单id；value 排序号
        Map<String, Integer> sequenceMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(oriOutStockOrderPOs)) {
            sequenceMap.putAll(getSowSequence(oriOutStockOrderPOs));
        }

        // outStockOrderPOs.sort(Comparator.comparing(OutStockOrderPO::getCreatetime));
        // 播种任务和出库单关联转换
        outStockOrderPOs.forEach(outStockOrderPO -> {
            SowOrderPO sowOrderPO = new SowOrderPO();
            sowOrderPO.setId(UuidUtil.getUUidInt());
            sowOrderPO.setOrgId(sowTaskPO.getOrgId());
            sowOrderPO.setSowTaskId(sowTaskPO.getId());
            sowOrderPO.setSowTaskNo(sowTaskPO.getSowTaskNo());
            sowOrderPO.setWarehouseId(sowTaskPO.getWarehouseId());
            sowOrderPO.setCreateTime(sowTaskPO.getCreateTime());
            sowOrderPO.setCreateUser(sowTaskPO.getCreateUser());
            sowOrderPO.setOutStockOrderId(outStockOrderPO.getId());
            sowOrderPO.setRefOrderNo(outStockOrderPO.getReforderno());
            sowOrderPO.setSkuCount(outStockOrderPO.getSkucount());
            sowOrderPO.setPackageAmount(outStockOrderPO.getPackageamount());
            sowOrderPO.setUnitAmount(outStockOrderPO.getUnitamount());
            Integer sequence = sequenceMap.get(outStockOrderPO.getReforderno());
            if (java.util.Objects.isNull(sequence)) {
                sequence = sowOrderPOS.size() + 1;
            }
            sowOrderPO.setSowOrderSequence(sequence);
            // sowOrderPO.setRemark(outStockOrderPO.getShopname());

            outStockOrderPO.getItems().forEach(item -> item.setSowOrderId(sowOrderPO.getId()));

            sowOrderPOS.add(sowOrderPO);
        });

        return sowOrderPOS;
    }

    /**
     * 根据播种任务和波次任务封装播种任务与订单信息列表
     *
     * @param sowTaskPO
     * @return
     */
    public static List<SowOrderPO> getDefaultSowOrderPOS(SowTaskPO sowTaskPO, List<OutStockOrderPO> outStockOrderPOs,
        WarehouseConfigDTO warehouseConfigDTO) {
        List<SowOrderPO> sowOrderPOS = new ArrayList<>();

        List<OutStockOrderPO> newOrderList = MergeSplitItemConvertor.copyAndMergeSameOrder(outStockOrderPOs);

        newOrderList.sort((o1, o2) -> {
            BigDecimal o1UnitTotal = o1.getItems().stream().map(OutStockOrderItemPO::getUnittotalcount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal o2UnitTotal = o2.getItems().stream().map(OutStockOrderItemPO::getUnittotalcount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            return Objects.equal(o1UnitTotal, o2UnitTotal) ? -o1.getSkucount().compareTo(o2.getSkucount())
                : -o1UnitTotal.compareTo(o2UnitTotal);
        });
        // outStockOrderPOs.sort(Comparator.comparing(OutStockOrderPO::getCreatetime));

        // 播种任务和出库单关联转换
        newOrderList.forEach(outStockOrderPO -> {
            SowOrderPO sowOrderPO = new SowOrderPO();
            sowOrderPO.setId(UuidUtil.getUUidInt());
            sowOrderPO.setOrgId(sowTaskPO.getOrgId());
            sowOrderPO.setSowTaskId(sowTaskPO.getId());
            sowOrderPO.setSowTaskNo(sowTaskPO.getSowTaskNo());
            sowOrderPO.setWarehouseId(sowTaskPO.getWarehouseId());
            sowOrderPO.setCreateTime(sowTaskPO.getCreateTime());
            sowOrderPO.setCreateUser(sowTaskPO.getCreateUser());
            sowOrderPO.setOutStockOrderId(outStockOrderPO.getId());
            sowOrderPO.setRefOrderNo(outStockOrderPO.getReforderno());
            sowOrderPO.setSkuCount(outStockOrderPO.getSkucount());
            sowOrderPO.setPackageAmount(outStockOrderPO.getPackageamount());
            sowOrderPO.setUnitAmount(outStockOrderPO.getUnitamount());
            sowOrderPO.setSowOrderSequence(sowOrderPOS.size() + 1);
            // sowOrderPO.setRemark(outStockOrderPO.getShopname());
            outStockOrderPO.getItems().forEach(item -> item.setSowOrderId(sowOrderPO.getId()));

            sowOrderPOS.add(sowOrderPO);
        });

        return sowOrderPOS;
    }

    // 返回值：key 订单号；value 排序号
    // 先按出库位，聚合；然后同一个出库位的订单，再重新进行排序
    public static Map<String, Integer> getSowSequence(List<OutStockOrderPO> outStockOrderPOs) {
        // LOG.info("排序数据 {}", GSON.toJson(outStockOrderPOs));
        Map<String, Integer> orderSequenceMap = new HashMap<>();
        Map<Long, OutStockOrderPO> outStockOrderMap =
            outStockOrderPOs.stream().collect(Collectors.toMap(OutStockOrderPO::getId, v -> v));
        Map<Long,
            Set<Long>> haveLocationOutOrderIdMap = outStockOrderPOs.stream().flatMap(m -> m.getItems().stream())
                .filter(m -> java.util.Objects.nonNull(m.getLocationId()))
                .collect(Collectors.groupingBy(OutStockOrderItemPO::getLocationId,
                    Collectors.mapping(OutStockOrderItemPO::getOutstockorderId, Collectors.toSet())));
        Set<Long> notHaveLocationOutOrderIdList = outStockOrderPOs.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> java.util.Objects.isNull(m.getLocationId())).map(OutStockOrderItemPO::getOutstockorderId)
            .collect(Collectors.toSet());

        for (Map.Entry<Long, Set<Long>> entry : haveLocationOutOrderIdMap.entrySet()) {
            List<OutStockOrderPO> sortOutStockOrderList =
                entry.getValue().stream().map(outStockOrderMap::get).collect(Collectors.toList());
            Map<String, Integer> sequenceMap = getSequence(sortOutStockOrderList);
            if (!org.springframework.util.CollectionUtils.isEmpty(sequenceMap)) {
                orderSequenceMap.putAll(sequenceMap);
            }
        }
        if (CollectionUtils.isNotEmpty(notHaveLocationOutOrderIdList)) {
            List<OutStockOrderPO> sortOutStockOrderList =
                notHaveLocationOutOrderIdList.stream().map(outStockOrderMap::get).collect(Collectors.toList());
            Map<String, Integer> sequenceMap = getSequence(sortOutStockOrderList);
            if (!org.springframework.util.CollectionUtils.isEmpty(sequenceMap)) {
                orderSequenceMap.putAll(sequenceMap);
            }
        }

        LOG.info("排序结果 sequenceMap : {}", GSON.toJson(orderSequenceMap));
        return orderSequenceMap;
    }

    public static Map<String, Integer> getSequence(List<OutStockOrderPO> sortOutStockOrderList) {
        if (CollectionUtils.isEmpty(sortOutStockOrderList)) {
            return Collections.emptyMap();
        }
        Map<String, Integer> orderSequenceMap = new HashMap<>();
        Integer[] rank = {1};
        // 优先按商品件数排序：数量多的优先
        Comparator<OutStockOrderPO> skuCountFirstComparator = Comparator.comparingInt(OutStockOrderPO::getSkucount).reversed();
        sortOutStockOrderList.sort(skuCountFirstComparator);
        sortOutStockOrderList.forEach(m -> orderSequenceMap.put(m.getReforderno(), rank[0]++));

        return orderSequenceMap;
    }

    /**
     * 根据产品拆分获取播种任务
     * 
     * @return
     */
    public static List<SowTaskPO> getSowTaskPOByProduct(Byte sowTaskType, Integer count, BatchPO batchPO,
        LoactionDTO location, String passageName, List<OutStockOrderPO> lstSowTaskOrders) {
        List<SowTaskPO> sowTaskPOS = new ArrayList<>();
        Map<Long, List<OutStockOrderItemPO>> skuItemMap = lstSowTaskOrders.stream()
            .flatMap(order -> order.getItems().stream()).collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid));
        for (Map.Entry<Long, List<OutStockOrderItemPO>> entry : skuItemMap.entrySet()) {
            List<OutStockOrderItemPO> items = entry.getValue();

            SowTaskPO sowTaskPO = new SowTaskPO();

            // 2、拆分过的订单，属于同一个播种任务
            count++;
            String sowNo = String.format("%s-%s", batchPO.getBatchNo(), count);
            Long sowId = UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.SOW_TASK);
            String sowTaskName = String.format("%s%s", items.get(0).getProductname(), items.get(0).getSpecname());
            if (StringUtils.isNotEmpty(passageName)) {
                sowTaskName = String.format("%s-%s", sowTaskName, passageName);
            }
            sowTaskPO.setSowTaskName(sowTaskName.length() <= 50 ? sowTaskName : sowTaskName.substring(0, 50));
            sowTaskPO.setId(sowId);
            sowTaskPO.setOrgId(batchPO.getOrgId());
            sowTaskPO.setWarehouseId(batchPO.getWarehouseId());
            sowTaskPO.setBatchId(batchPO.getId());
            sowTaskPO.setBatchNo(batchPO.getBatchNo());
            sowTaskPO.setSowTaskNo(sowNo);
            sowTaskPO.setSkuCount(1);
            sowTaskPO.setCreateUser(batchPO.getCreateUser());
            sowTaskPO.setSowTaskType(sowTaskType);
            sowTaskPO.setState(SowTaskStateEnum.待播种.getType());
            sowTaskPO.setPackageState(SowPackageStateEnum.待打包.getType());
            if (null != location) {
                sowTaskPO.setLocationId(location.getId());
                sowTaskPO.setLocationName(location.getName());
            }
            List<Long> orderIds =
                items.stream().map(OutStockOrderItemPO::getOutstockorderId).distinct().collect(Collectors.toList());
            sowTaskPO.setOrderCount(orderIds.size());
            // 记录自提点数量
            long addressCount = lstSowTaskOrders.stream().filter(order -> orderIds.contains(order.getId()))
                .map(OutStockOrderPO::getAddressId).distinct().count();
            sowTaskPO.setAddressCount((int)addressCount);

            // 增加明细
            SowTaskItemPO sowTaskItemPO = new SowTaskItemPO();
            sowTaskItemPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.SOW_TASK_ITEM));
            sowTaskItemPO.setOrgId(sowTaskPO.getOrgId());
            sowTaskItemPO.setWarehouseId(sowTaskPO.getWarehouseId());
            sowTaskItemPO.setState(SowTaskStateEnum.待播种.getType());
            sowTaskItemPO.setSowTaskId(sowId);
            sowTaskItemPO.setSowTaskNo(sowNo);
            sowTaskItemPO.setProductSkuId(entry.getKey());
            sowTaskItemPO.setProductName(items.get(0).getProductname());
            sowTaskItemPO.setProductSpecificationId(items.get(0).getProductSpecificationId());
            sowTaskItemPO.setOwnerId(items.get(0).getOwnerId());
            sowTaskItemPO.setSecOwnerId(items.get(0).getSecOwnerId());
            sowTaskItemPO.setSpecQuantity(items.get(0).getSpecquantity());
            sowTaskItemPO.setSpecName(items.get(0).getSpecname());
            sowTaskItemPO.setPackageName(items.get(0).getPackagename());
            sowTaskItemPO.setUnitName(items.get(0).getUnitname());
            BigDecimal unitTotalCount =
                items.stream().map(OutStockOrderItemPO::getUnittotalcount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal[] divideAndRemainder = unitTotalCount.divideAndRemainder(items.get(0).getSpecquantity());
            BigDecimal packageCount = divideAndRemainder[0];
            BigDecimal unitCount = divideAndRemainder[1];
            sowTaskItemPO.setPackageCount(packageCount);
            sowTaskItemPO.setUnitCount(unitCount);
            sowTaskItemPO.setUnitTotalCount(unitTotalCount);
            sowTaskItemPO.setOverUnitTotalCount(BigDecimal.ZERO);
            sowTaskItemPO.setLackUnitTotalCount(BigDecimal.ZERO);

            sowTaskPO.setPackageAmount(sowTaskItemPO.getPackageCount());
            sowTaskPO.setUnitAmount(sowTaskItemPO.getUnitCount());
            List<SowTaskItemPO> sowTaskItemPOS = new ArrayList<>();
            sowTaskItemPOS.add(sowTaskItemPO);
            sowTaskPO.setSowTaskItemPOS(sowTaskItemPOS);

            items.forEach(item -> {
                item.setSowTaskItemId(sowTaskItemPO.getId());
                item.setSowTaskId(sowId);
                item.setSowTaskNo(sowNo);
            });

            sowTaskPOS.add(sowTaskPO);
        }
        return sowTaskPOS;
    }

    /**
     * 获取订单和播种货位关系
     */
    public static List<SowOrderPO> getSowOrderPOSByOrders(Integer orgId, Integer warehouseId, String createUser,
        List<OutStockOrderPO> lstSowTaskOrders) {
        List<SowOrderPO> sowOrderPOS = new ArrayList<>();
        lstSowTaskOrders.forEach(order -> {
            SowOrderPO sowOrderPO = new SowOrderPO();

            sowOrderPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.SOW_ORDER));
            sowOrderPO.setOrgId(orgId);
            sowOrderPO.setWarehouseId(warehouseId);
            sowOrderPO.setSowTaskId(1L);
            sowOrderPO.setSowTaskNo("");
            sowOrderPO.setOutStockOrderId(order.getId());
            sowOrderPO.setRefOrderNo(order.getReforderno());
            sowOrderPO.setSkuCount(order.getSkucount());
            sowOrderPO.setPackageAmount(order.getPackageamount());
            sowOrderPO.setUnitAmount(order.getUnitamount());
            sowOrderPO.setCreateUser(createUser);
            sowOrderPO.setSowOrderSequence(order.getLocationSequence());
            sowOrderPO.setLocationId(order.getSowLocationId());
            sowOrderPO.setLocationName(order.getSowLocationName());

            order.getItems().forEach(item -> item.setSowOrderId(sowOrderPO.getId()));

            sowOrderPOS.add(sowOrderPO);
        });

        return sowOrderPOS;
    }

    public static SowTaskPO getVirtualSecondPickSowTaskPO(Long sowId, String sowNo,
        List<OutStockOrderPO> lstSowTaskOrders, BatchPO batchPO) {
        List<OutStockOrderItemPO> lstTmpItems = new ArrayList<>();
        lstSowTaskOrders.forEach(p -> {
            lstTmpItems.addAll(p.getItems());
        });

        SowTaskPO sowTaskPO = new SowTaskPO();
        Integer skuCount =
            lstTmpItems.stream().map(OutStockOrderItemPO::getSkuid).distinct().collect(Collectors.toList()).size();
        sowTaskPO.setId(sowId);
        sowTaskPO.setOrgId(batchPO.getOrgId());
        sowTaskPO.setWarehouseId(batchPO.getWarehouseId());
        sowTaskPO.setBatchId(batchPO.getId());
        sowTaskPO.setBatchNo(batchPO.getBatchNo());
        sowTaskPO.setSowTaskNo(sowNo);
        sowTaskPO.setOrderCount(lstTmpItems.stream().map(OutStockOrderItemPO::getOutstockorderId).distinct()
            .collect(Collectors.toList()).size());
        sowTaskPO.setSkuCount(skuCount);
        sowTaskPO.setCreateUser(batchPO.getCreateUser());

        sowTaskPO.setSowTaskName(String.format("虚仓二次分拣-%s", lstSowTaskOrders.get(0).getCreateuser()));
        sowTaskPO.setSowTaskType(SowTaskTypeEnum.虚仓二次分拣播种.getType());
        sowTaskPO.setOperationMode(OperationModeEnum.不拣货播种.getType());

        if (lstSowTaskOrders.get(0).getAreaId() != null) {
            sowTaskPO.setLocationId(lstSowTaskOrders.get(0).getAreaId());
            sowTaskPO.setLocationName(lstSowTaskOrders.get(0).getAreaName());
        } else {
            throw new BusinessValidateException("虚仓二次分拣，收货时未设置收货位");
        }

        // 增加明细
        List<SowTaskItemPO> sowTaskItemPOS = new ArrayList<>();
        lstSowTaskOrders.stream().collect(Collectors.groupingBy(OutStockOrderPO::getBoundNo))
            .forEach((boundNo, boundOrderList) -> {
                boundOrderList.stream().flatMap(order -> order.getItems().stream())
                    .collect(Collectors
                        .groupingBy(item -> String.format("%s_%s", item.getSkuid(), item.getSalespecquantity())))
                    .forEach((skuInfo, items) -> {
                        OutStockOrderItemPO firstOrderItem = items.get(0);

                        SowTaskItemPO sowTaskItemPO = new SowTaskItemPO();
                        sowTaskItemPO.setId(UuidUtil.getUUidInt());
                        sowTaskItemPO.setOrgId(sowTaskPO.getOrgId());
                        sowTaskItemPO.setWarehouseId(sowTaskPO.getWarehouseId());
                        sowTaskItemPO.setState(SowTaskStateEnum.待播种.getType());
                        sowTaskItemPO.setSowTaskId(sowId);
                        sowTaskItemPO.setSowTaskNo(sowNo);
                        sowTaskItemPO.setProductSkuId(firstOrderItem.getSkuid());
                        sowTaskItemPO.setProductName(firstOrderItem.getProductname());
                        sowTaskItemPO.setProductSpecificationId(firstOrderItem.getProductSpecificationId());
                        sowTaskItemPO.setOwnerId(firstOrderItem.getOwnerId());
                        sowTaskItemPO.setSecOwnerId(firstOrderItem.getSecOwnerId());
                        sowTaskItemPO.setSpecQuantity(firstOrderItem.getSpecquantity());
                        sowTaskItemPO.setSpecName(firstOrderItem.getSpecname());
                        sowTaskItemPO.setPackageName(firstOrderItem.getPackagename());
                        sowTaskItemPO.setUnitName(firstOrderItem.getUnitname());
                        BigDecimal unitTotalCount = items.stream().map(OutStockOrderItemPO::getUnittotalcount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal[] divideAndRemainder =
                            unitTotalCount.divideAndRemainder(firstOrderItem.getSpecquantity());
                        BigDecimal packageCount = divideAndRemainder[0];
                        BigDecimal unitCount = divideAndRemainder[1];
                        sowTaskItemPO.setPackageCount(packageCount);
                        sowTaskItemPO.setUnitCount(unitCount);
                        sowTaskItemPO.setUnitTotalCount(unitTotalCount);
                        sowTaskItemPO.setSaleSpecQuantity(firstOrderItem.getSalespecquantity());
                        sowTaskItemPO.setRemark(boundNo);

                        sowTaskItemPOS.add(sowTaskItemPO);

                        items.forEach(item -> {
                            item.setSowTaskItemId(sowTaskItemPO.getId());
                            item.setSowTaskId(sowId);
                            item.setSowTaskNo(sowNo);
                        });
                    });
            });

        BigDecimal packageAmount =
            sowTaskItemPOS.stream().map(SowTaskItemPO::getPackageCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal unitAmount =
            sowTaskItemPOS.stream().map(SowTaskItemPO::getUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        sowTaskPO.setPackageAmount(packageAmount);
        sowTaskPO.setUnitAmount(unitAmount);
        sowTaskPO.setSowTaskItemPOS(sowTaskItemPOS);
        sowTaskPO.setPackageState(SowPackageStateEnum.待打包.getType());
        return sowTaskPO;
    }

    public static void resetSowOrderSequenceIfPickByCustomer(List<OutStockOrderPO> orderList,
        List<SowOrderPO> sowOrderPOList) {
        Map<String, Integer> orderNoAddressMap = orderList.stream().filter(m -> isNeedHandleSequence(m))
            .collect(Collectors.toMap(OutStockOrderPO::getReforderno, OutStockOrderPO::getAddressId));

        if (org.springframework.util.CollectionUtils.isEmpty(orderNoAddressMap)) {
            return;
        }

        if (CollectionUtils.isEmpty(sowOrderPOList)) {
            return;
        }

        // key 是 addressId
        Map<Integer, List<SowOrderPO>> sowOrderGroupMap = sowOrderPOList.stream().collect(Collectors.groupingBy(k -> {
            return orderNoAddressMap.get(k.getRefOrderNo());
        }));

        List<SowSequenceResetBO> sowSequenceResetBOS = new ArrayList<>();
        for (Map.Entry<Integer, List<SowOrderPO>> entry : sowOrderGroupMap.entrySet()) {
            Optional<Integer> sowOrderSequenceOptional = entry.getValue().stream().map(SowOrderPO::getSowOrderSequence)
                .filter(java.util.Objects::nonNull).min(Integer::compareTo);
            Integer sequence = sowOrderSequenceOptional.orElseGet(() -> 0);

            entry.getValue().forEach(item -> {
                item.setSowOrderSequence(sequence);
            });

            sowSequenceResetBOS.add(new SowSequenceResetBO(sequence, entry.getKey()));
        }

        // 全部重新排序，否则合并会导致跳号
        sowSequenceResetBOS.sort((o1, o2) -> o1.getSequenece() - o2.getSequenece());
        for (int i = 0; i < sowSequenceResetBOS.size(); i++) {
            sowSequenceResetBOS.get(i).setSequenece(i + 1);
        }

        Map<Integer, Integer> addressSequenceMap = sowSequenceResetBOS.stream()
            .collect(Collectors.toMap(SowSequenceResetBO::getAddressId, SowSequenceResetBO::getSequenece));

        for (Map.Entry<Integer, List<SowOrderPO>> entry : sowOrderGroupMap.entrySet()) {
            Integer sequence = addressSequenceMap.get(entry.getKey());

            entry.getValue().forEach(item -> {
                item.setSowOrderSequence(sequence);
            });
        }
    }

    public static boolean isNeedHandleSequence(OutStockOrderPO outStockOrderPO) {
        if (java.util.Objects.isNull(outStockOrderPO.getAddressId()) || outStockOrderPO.getAddressId() == 0) {
            return Boolean.FALSE;
        }

        // 内配单前置仓订单不合并
        if (OutBoundTypeEnum.SALE_ORDER.getCode().intValue() == outStockOrderPO.getOutBoundType()
            && OrderConstant.ALLOT_TYPE_ALLOCATION.equals(outStockOrderPO.getAllotType())) {
            return Boolean.FALSE;
        }

        // 【促销订单】合并拣货不处理orderSequence
        boolean isPromotion = SplitWaveOrderUtil.orderIsPromotion(outStockOrderPO);
        if (isPromotion) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }
}
