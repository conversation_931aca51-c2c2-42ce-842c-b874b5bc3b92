package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo;

import java.util.List;

import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutBoundBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskItemUpdateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskUpdateDTO;

/**
 * <AUTHOR>
 * @title: OutStockOrderAdminCancelHandlePickResultBO
 * @description:
 * @date 2023-05-25 09:12
 */
public class OutStockOrderAdminCancelHandlePickResultBO {
    /**
     * 待更新的拣货任务
     */
    private List<BatchTaskPO> updateBatchTaskList;
    /**
     * 待删除的拣货任务id
     */
    private List<String> deleteBatchTaskIds;
    /**
     * 待更新的拣货任务明细
     */
    private List<BatchTaskItemUpdatePO> updateBatchTaskItemList;
    /**
     * 待删除的拣货任务明细id
     */
    private List<String> deleteBatchTaskItemIds;
    /**
     * 待更新的播种任务明细列表
     */
    private List<SowTaskItemUpdateDTO> sowTaskItemUpdateDTOS;
    /**
     * 待删除的播种任务明细id
     */
    private List<Long> deleteSowTaskItemIds;
    /**
     * 待更新的播种任务
     */
    private List<SowTaskUpdateDTO> updateSowTaskList;
    /**
     * 待删除的播种任务id列表
     */
    private List<Long> deleteSowTaskIds;
    /**
     * 待删除的播种订单id
     */
    private List<Long> deleteSowOrderIds;
    /**
     * 关联项列表
     */
    private List<OrderItemTaskInfoPO> updateOrderItemTaskInfoList;
    /**
     * 关联项明细列表
     */
    private List<OrderItemTaskInfoDetailPO> updateOrderItemTaskInfoDetailList;
    /**
     * 待更新波次列表
     */
    private List<BatchPO> updateBatchList;

    /**
     * 待删除波次列表
     */
    private List<String> deleteBatchIds;
    /**
     * 待删除OrderItemTaskInfo列表
     */
    private List<Long> deleteOrderItemTaskInfoIds;
    /**
     * 待删除OrderItemTaskInfoDetail列表
     */
    private List<Long> deleteOrderItemTaskInfoDetailIds;

    /**
     * 获取 待更新的拣货任务
     *
     * @return updateBatchTaskList 待更新的拣货任务
     */
    public List<BatchTaskPO> getUpdateBatchTaskList() {
        return this.updateBatchTaskList;
    }

    /**
     * 待更新出库批次
     */
    private OutBoundBatchDTO updateOutBoundBatchDTO;

    /**
     * 设置 待更新的拣货任务
     *
     * @param updateBatchTaskList 待更新的拣货任务
     */
    public void setUpdateBatchTaskList(List<BatchTaskPO> updateBatchTaskList) {
        this.updateBatchTaskList = updateBatchTaskList;
    }

    /**
     * 获取 待删除的拣货任务id
     *
     * @return deleteBatchTaskIds 待删除的拣货任务id
     */
    public List<String> getDeleteBatchTaskIds() {
        return this.deleteBatchTaskIds;
    }

    /**
     * 设置 待删除的拣货任务id
     *
     * @param deleteBatchTaskIds 待删除的拣货任务id
     */
    public void setDeleteBatchTaskIds(List<String> deleteBatchTaskIds) {
        this.deleteBatchTaskIds = deleteBatchTaskIds;
    }

    /**
     * 获取 待更新的拣货任务明细
     *
     * @return updateBatchTaskItemList 待更新的拣货任务明细
     */
    public List<BatchTaskItemUpdatePO> getUpdateBatchTaskItemList() {
        return this.updateBatchTaskItemList;
    }

    /**
     * 设置 待更新的拣货任务明细
     *
     * @param updateBatchTaskItemList 待更新的拣货任务明细
     */
    public void setUpdateBatchTaskItemList(List<BatchTaskItemUpdatePO> updateBatchTaskItemList) {
        this.updateBatchTaskItemList = updateBatchTaskItemList;
    }

    /**
     * 获取 待删除的拣货任务明细id
     *
     * @return deleteBatchTaskItemIds 待删除的拣货任务明细id
     */
    public List<String> getDeleteBatchTaskItemIds() {
        return this.deleteBatchTaskItemIds;
    }

    /**
     * 设置 待删除的拣货任务明细id
     *
     * @param deleteBatchTaskItemIds 待删除的拣货任务明细id
     */
    public void setDeleteBatchTaskItemIds(List<String> deleteBatchTaskItemIds) {
        this.deleteBatchTaskItemIds = deleteBatchTaskItemIds;
    }

    /**
     * 获取 待更新的播种任务明细列表
     *
     * @return sowTaskItemUpdateDTOS 待更新的播种任务明细列表
     */
    public List<SowTaskItemUpdateDTO> getSowTaskItemUpdateDTOS() {
        return this.sowTaskItemUpdateDTOS;
    }

    /**
     * 设置 待更新的播种任务明细列表
     *
     * @param sowTaskItemUpdateDTOS 待更新的播种任务明细列表
     */
    public void setSowTaskItemUpdateDTOS(List<SowTaskItemUpdateDTO> sowTaskItemUpdateDTOS) {
        this.sowTaskItemUpdateDTOS = sowTaskItemUpdateDTOS;
    }

    /**
     * 获取 待删除的播种任务明细id
     *
     * @return deleteSowTaskItemIds 待删除的播种任务明细id
     */
    public List<Long> getDeleteSowTaskItemIds() {
        return this.deleteSowTaskItemIds;
    }

    /**
     * 设置 待删除的播种任务明细id
     *
     * @param deleteSowTaskItemIds 待删除的播种任务明细id
     */
    public void setDeleteSowTaskItemIds(List<Long> deleteSowTaskItemIds) {
        this.deleteSowTaskItemIds = deleteSowTaskItemIds;
    }

    /**
     * 获取 待更新的播种任务
     *
     * @return updateSowTaskList 待更新的播种任务
     */
    public List<SowTaskUpdateDTO> getUpdateSowTaskList() {
        return this.updateSowTaskList;
    }

    /**
     * 设置 待更新的播种任务
     *
     * @param updateSowTaskList 待更新的播种任务
     */
    public void setUpdateSowTaskList(List<SowTaskUpdateDTO> updateSowTaskList) {
        this.updateSowTaskList = updateSowTaskList;
    }

    /**
     * 获取 待删除的播种任务id列表
     *
     * @return deleteSowTaskIds 待删除的播种任务id列表
     */
    public List<Long> getDeleteSowTaskIds() {
        return this.deleteSowTaskIds;
    }

    /**
     * 设置 待删除的播种任务id列表
     *
     * @param deleteSowTaskIds 待删除的播种任务id列表
     */
    public void setDeleteSowTaskIds(List<Long> deleteSowTaskIds) {
        this.deleteSowTaskIds = deleteSowTaskIds;
    }

    /**
     * 获取 待删除的播种订单id
     *
     * @return deleteSowOrderIds 待删除的播种订单id
     */
    public List<Long> getDeleteSowOrderIds() {
        return this.deleteSowOrderIds;
    }

    /**
     * 设置 待删除的播种订单id
     *
     * @param deleteSowOrderIds 待删除的播种订单id
     */
    public void setDeleteSowOrderIds(List<Long> deleteSowOrderIds) {
        this.deleteSowOrderIds = deleteSowOrderIds;
    }

    /**
     * 获取 关联项列表
     *
     * @return updateOrderItemTaskInfoList 关联项列表
     */
    public List<OrderItemTaskInfoPO> getUpdateOrderItemTaskInfoList() {
        return this.updateOrderItemTaskInfoList;
    }

    /**
     * 设置 关联项列表
     *
     * @param updateOrderItemTaskInfoList 关联项列表
     */
    public void setUpdateOrderItemTaskInfoList(List<OrderItemTaskInfoPO> updateOrderItemTaskInfoList) {
        this.updateOrderItemTaskInfoList = updateOrderItemTaskInfoList;
    }

    /**
     * 获取 关联项明细列表
     *
     * @return updateOrderItemTaskInfoDetailList 关联项明细列表
     */
    public List<OrderItemTaskInfoDetailPO> getUpdateOrderItemTaskInfoDetailList() {
        return this.updateOrderItemTaskInfoDetailList;
    }

    /**
     * 设置 关联项明细列表
     *
     * @param updateOrderItemTaskInfoDetailList 关联项明细列表
     */
    public void
        setUpdateOrderItemTaskInfoDetailList(List<OrderItemTaskInfoDetailPO> updateOrderItemTaskInfoDetailList) {
        this.updateOrderItemTaskInfoDetailList = updateOrderItemTaskInfoDetailList;
    }

    public static OutStockOrderAdminCancelHandlePickResultBO newInstance() {
        return new OutStockOrderAdminCancelHandlePickResultBO();
    }

    /**
     * 获取 待更新波次列表
     *
     * @return updateBatchList 待更新波次列表
     */
    public List<BatchPO> getUpdateBatchList() {
        return this.updateBatchList;
    }

    /**
     * 设置 待更新波次列表
     *
     * @param updateBatchList 待更新波次列表
     */
    public void setUpdateBatchList(List<BatchPO> updateBatchList) {
        this.updateBatchList = updateBatchList;
    }

    /**
     * 获取 待删除波次列表
     *
     * @return deleteBatchIds 待删除波次列表
     */
    public List<String> getDeleteBatchIds() {
        return this.deleteBatchIds;
    }

    /**
     * 设置 待删除波次列表
     *
     * @param deleteBatchIds 待删除波次列表
     */
    public void setDeleteBatchIds(List<String> deleteBatchIds) {
        this.deleteBatchIds = deleteBatchIds;
    }

    /**
     * 获取 待更新出库批次
     *
     * @return updateOutBoundBatchDTO 待更新出库批次
     */
    public OutBoundBatchDTO getUpdateOutBoundBatchDTO() {
        return this.updateOutBoundBatchDTO;
    }

    /**
     * 设置 待更新出库批次
     *
     * @param updateOutBoundBatchDTO 待更新出库批次
     */
    public void setUpdateOutBoundBatchDTO(OutBoundBatchDTO updateOutBoundBatchDTO) {
        this.updateOutBoundBatchDTO = updateOutBoundBatchDTO;
    }

    /**
     * 获取 待删除OrderItemTaskInfo列表
     *
     * @return deleteOrderItemTaskInfoIds 待删除OrderItemTaskInfo列表
     */
    public List<Long> getDeleteOrderItemTaskInfoIds() {
        return this.deleteOrderItemTaskInfoIds;
    }

    /**
     * 设置 待删除OrderItemTaskInfo列表
     *
     * @param deleteOrderItemTaskInfoIds 待删除OrderItemTaskInfo列表
     */
    public void setDeleteOrderItemTaskInfoIds(List<Long> deleteOrderItemTaskInfoIds) {
        this.deleteOrderItemTaskInfoIds = deleteOrderItemTaskInfoIds;
    }

    /**
     * 获取 待删除OrderItemTaskInfoDetail列表
     *
     * @return deleteOrderItemTaskInfoDetailIds 待删除OrderItemTaskInfoDetail列表
     */
    public List<Long> getDeleteOrderItemTaskInfoDetailIds() {
        return this.deleteOrderItemTaskInfoDetailIds;
    }

    /**
     * 设置 待删除OrderItemTaskInfoDetail列表
     *
     * @param deleteOrderItemTaskInfoDetailIds 待删除OrderItemTaskInfoDetail列表
     */
    public void setDeleteOrderItemTaskInfoDetailIds(List<Long> deleteOrderItemTaskInfoDetailIds) {
        this.deleteOrderItemTaskInfoDetailIds = deleteOrderItemTaskInfoDetailIds;
    }
}
