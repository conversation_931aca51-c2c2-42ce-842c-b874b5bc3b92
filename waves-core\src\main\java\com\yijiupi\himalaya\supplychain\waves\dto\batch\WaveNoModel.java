package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.util.List;

/**
 * 出库单对应的出库位
 */
public class WaveNoModel implements Serializable {

    private static final long serialVersionUID = 1389025430756714450L;

    /**
     * 波次号
     */
    private String waveNo;

    /**
     * 订单对应的出库位
     */
    private List<WaveNoItemModel> orderItems;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public List<WaveNoItemModel> getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(List<WaveNoItemModel> orderItems) {
        this.orderItems = orderItems;
    }
}
