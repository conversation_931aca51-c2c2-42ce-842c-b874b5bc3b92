package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils;

import java.util.*;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskKindOfPickingConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;

import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.enums.PassagePickTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SowTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;
import com.yijiupi.himalaya.supplychain.waves.util.OrderRebuildUtil;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @title: SpltWaveOrderUtil
 * @description:
 * @date 2023-02-16 17:01
 */
public class SplitWaveOrderUtil {

    /**
     * 是否是大件订单项（单项拣货任务订单项）
     * 
     * @param item
     * @return
     */
    public static boolean isPackageItem(OutStockOrderItemPO item) {
        if (Objects.isNull(item.getSubCategory())) {
            return Boolean.FALSE;
        }
        if (LocationEnum.存储位.getType().byteValue() != item.getSubCategory()) {
            return Boolean.FALSE;
        }
        if (BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(item.getLargePick())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    public static boolean isStoreItem(OutStockOrderItemPO item) {
        if (Objects.isNull(item.getSubCategory())) {
            return Boolean.FALSE;
        }
        if (LocationEnum.分拣位.getType().byteValue() != item.getSubCategory()) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * 是否是聚合成为大件的订单项（多件拣货任务订单项）
     * 
     * @param item
     * @return
     */
    public static boolean isPackageUnitItem(OutStockOrderItemPO item) {
        if (Objects.isNull(item.getSubCategory())) {
            return Boolean.FALSE;
        }
        if (LocationEnum.存储位.getType().byteValue() != item.getSubCategory()) {
            return Boolean.FALSE;
        }
        if (BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY.equals(item.getLargePick())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    /**
     * 是否是小件订单项
     * 
     * @param item
     * @return
     */
    public static boolean isUnitItem(OutStockOrderItemPO item) {
        if (Objects.isNull(item.getSubCategory())) {
            return Boolean.TRUE;
        }
        if (LocationEnum.存储位.getType().byteValue() == item.getSubCategory()) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    public static WaveCreateDTO copyWaveCreateDTO(WaveCreateDTO ori, List<OutStockOrderPO> splitOrderList,
        List<OutStockOrderItemPO> orderItemList) {
        WaveCreateDTO waveCreateDTO = new WaveCreateDTO();
        BeanUtils.copyProperties(ori, waveCreateDTO);
        waveCreateDTO.setPassageDTO(ori.getPassageDTO());
        waveCreateDTO.setWavesStrategyDTO(ori.getWavesStrategyDTO());
        List<OutStockOrderPO> packageOrderList =
            OrderRebuildUtil.copyOrderItemCreateOrder(splitOrderList, orderItemList);
        waveCreateDTO.setOrders(packageOrderList);

        return waveCreateDTO;
    }

    public static boolean hasAllotOrderByItem(List<OutStockOrderPO> totalOrderList,
        List<OutStockOrderItemPO> createTaskItem) {
        Map<Long, Long> itemOrderIdMap = createTaskItem.stream().collect(Collectors
            .toMap(OutStockOrderItemPO::getOutstockorderId, OutStockOrderItemPO::getOutstockorderId, (v1, v2) -> v1));
        return totalOrderList.stream()
            .filter(m -> OrderConstant.ALLOT_TYPE_ALLOCATION.equals(m.getAllotType())
                && OutBoundTypeEnum.SALE_ORDER.getCode().byteValue() == m.getOutBoundType())
            .anyMatch(m -> Objects.nonNull(itemOrderIdMap.get(m.getId())));
    }

    public static boolean hasAllotOrderByItem(List<OutStockOrderPO> createTaskOrder) {
        return createTaskOrder.stream().anyMatch(m -> OrderConstant.ALLOT_TYPE_ALLOCATION.equals(m.getAllotType())
            && OutBoundTypeEnum.SALE_ORDER.getCode().byteValue() == m.getOutBoundType());
    }

    public static WaveCreateDTO getWaveCreateDTO(List<OutStockOrderPO> orders, WavesStrategyDTO wavesStrategyDTO,
        Byte pickingType, Byte pickingGroupStrategy, String title, String operateUser, Integer cityId,
        String locationName, String driverName, Byte sowType, Byte passageType, Boolean allocationFlag,
        ProcessBatchDTO processBatchDTO) {
        return getWaveCreateDTO(orders, wavesStrategyDTO, pickingType, pickingGroupStrategy, title, operateUser, cityId,
            locationName, driverName, sowType, passageType, allocationFlag, processBatchDTO.getOperateUserId());
    }

    public static WaveCreateDTO getWaveCreateDTO(List<OutStockOrderPO> orders, WavesStrategyDTO wavesStrategyDTO,
        Byte pickingType, Byte pickingGroupStrategy, String title, String operateUser, Integer cityId,
        String locationName, String driverName, Byte sowType, Byte passageType, Boolean allocationFlag,
        Integer optUserId) {
        WaveCreateDTO createDTO = new WaveCreateDTO();
        createDTO.setCityId(cityId);
        createDTO.setTitle(title);
        createDTO.setOperateUser(operateUser);
        createDTO.setOperateUserId(optUserId);
        createDTO.setOrders(orders);
        createDTO.setLocationName(locationName);
        createDTO.setDriverName(driverName);
        createDTO.setAllocationFlag(allocationFlag);
        WavesStrategyBO waveDTO = new WavesStrategyBO();
        wavesStrategyDTO.setCreateTime(new Date());
        wavesStrategyDTO.setLastUpdateTime(new Date());
        BeanUtils.copyProperties(wavesStrategyDTO, waveDTO);
        waveDTO.setPickingType(pickingType);

        // 如果是按订单拣货，拣货分组策略是没用的
        // 如果是按产品拣货，拣货分组策略默认是按类目
        if ((pickingGroupStrategy == null || pickingGroupStrategy.intValue() == 0)
            && WavesStrategyConstants.PICKINGTYPE_PRODUCT == pickingType.intValue()) {
            // 如果是按产品拣货，拣货分组方式默认为按类目拣货
            pickingGroupStrategy = (byte)WavesStrategyConstants.PICKINGGROUPSTRATEGY_CATEGORY;
        }
        // 如果是分区分单的订单拣货，拣货分组策略不变
        if (sowType != null
            && (sowType == SowTypeEnum.分区分单播种.getType() || sowType == SowTypeEnum.分区分单不播种.getType()
                || sowType == SowTypeEnum.分区分单拣货.getType())
            && WavesStrategyConstants.PICKINGTYPE_ORDER == pickingType.intValue()) {
            if (passageType != null && passageType == PassagePickTypeEnum.按货位通道.getType()) {
                pickingGroupStrategy = (byte)WavesStrategyConstants.PICKINGGROUPSTRATEGY_LOCATION;
            } else {
                pickingGroupStrategy = (byte)WavesStrategyConstants.PICKINGGROUPSTRATEGY_CATEGORY;
            }
        }
        waveDTO.setPickingGroupStrategy(pickingGroupStrategy);
        createDTO.setWavesStrategyDTO(waveDTO);
        return createDTO;
    }

    public static List<OutStockOrderItemPO> getPackageStoreItemList(List<OutStockOrderPO> splitOrderList) {
        List<OutStockOrderItemPO> packageStoreItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick()))
            .filter(SplitWaveOrderUtil::isStoreItem).collect(Collectors.toList());

        return packageStoreItemList;
    }

    public static List<OutStockOrderItemPO> getPackageNotStoreItemList(List<OutStockOrderPO> splitOrderList) {
        List<OutStockOrderItemPO> packageNotStoreItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick()))
            .filter(m -> BooleanUtils.isFalse(SplitWaveOrderUtil.isStoreItem(m))).collect(Collectors.toList());

        return packageNotStoreItemList;
    }

    public static List<OutStockOrderItemPO> getMultiStorePackageItemList(List<OutStockOrderPO> splitOrderList) {
        List<OutStockOrderItemPO> multiStorePackageItemList =
            splitOrderList.stream().flatMap(m -> m.getItems().stream())
                .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY.equals(m.getLargePick()))
                .filter(SplitWaveOrderUtil::isStoreItem).collect(Collectors.toList());

        return multiStorePackageItemList;
    }

    public static List<OutStockOrderItemPO> getMultiNotStorePackageItemList(List<OutStockOrderPO> splitOrderList) {
        List<OutStockOrderItemPO> multiNotStorePackageItemList =
            splitOrderList.stream().flatMap(m -> m.getItems().stream())
                .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY.equals(m.getLargePick()))
                .filter(m -> !SplitWaveOrderUtil.isStoreItem(m)).collect(Collectors.toList());

        return multiNotStorePackageItemList;
    }

    public static List<OutStockOrderItemPO> getUnitItemList(List<OutStockOrderPO> splitOrderList) {
        List<OutStockOrderItemPO> unitItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> !BatchTaskItemLargePickPatternConstants.isLargePick(m.getLargePick()))
            .collect(Collectors.toList());

        return unitItemList;
    }

    /**
     * 是否按分区拆分拣货任务
     * 
     * @param wavesStrategyBO
     * @param waveCreateDTO
     * @return
     */
    public static boolean couldSplitBySortGroup(WavesStrategyBO wavesStrategyBO, WaveCreateDTO waveCreateDTO) {
        if (Objects.isNull(wavesStrategyBO.getPickingGroupStrategy())) {
            return Boolean.FALSE;
        }

        if (wavesStrategyBO.getPickingGroupStrategy().intValue() == 0) {
            return Boolean.FALSE;
        }
        if (Objects.isNull(waveCreateDTO.getKindOfPicking())) {
            return Boolean.TRUE;
        }

        if (BatchTaskKindOfPickingConstants.PICKING_SORTING.equals(waveCreateDTO.getKindOfPicking())) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * 通过拣货任务获取实时分拣的订单项，并设置订单项的出库位
     * 
     * @param batchTaskList
     * @param outStockOrderPOList
     * @param orderItemTaskInfoPOList
     * @return
     */
    public static List<OutStockOrderItemPO> setPickSortingOutStockLocation(List<BatchTaskPO> batchTaskList,
        List<OutStockOrderPO> outStockOrderPOList, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(batchTaskList)) {
            return Collections.emptyList();
        }

        Map<String, BatchTaskPO> pickSortBatchTaskMap = batchTaskList.stream()
            .filter(m -> BatchTaskKindOfPickingConstants.PICKING_SORTING.equals(m.getKindOfPicking()))
            .collect(Collectors.toMap(BatchTaskPO::getId, v -> v));

        if (CollectionUtils.isEmpty(pickSortBatchTaskMap)) {
            return Collections.emptyList();
        }

        Map<Long, OutStockOrderItemPO> itemMap =
            outStockOrderPOList.stream().filter(m -> !CollectionUtils.isEmpty(m.getItems()))
                .flatMap(m -> m.getItems().stream()).collect(Collectors.toMap(OutStockOrderItemPO::getId, v -> v));

        Map<String,
            List<OrderItemTaskInfoPO>> orderItemTaskInfoGroupMap = orderItemTaskInfoPOList.stream()
                .filter(item -> Objects.nonNull(pickSortBatchTaskMap.get(item.getBatchTaskId())))
                .collect(Collectors.groupingBy(OrderItemTaskInfoPO::getBatchTaskId));

        List<OutStockOrderItemPO> updateItemList = new ArrayList<>();
        for (Map.Entry<String, List<OrderItemTaskInfoPO>> entry : orderItemTaskInfoGroupMap.entrySet()) {
            BatchTaskPO batchTaskPO = pickSortBatchTaskMap.get(entry.getKey());

            List<OutStockOrderItemPO> itemPOList =
                entry.getValue().stream().map(m -> itemMap.get(m.getRefOrderItemId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(itemPOList)) {
                updateItemList.addAll(itemPOList);
            }

        }

        return updateItemList;
    }

    public static boolean orderIsPromotion(OutStockOrderPO order) {
        boolean isPromotion = order.getItems().stream().anyMatch(m -> ConditionStateEnum.是.getType().equals(m.getIsAdvent()));

        return isPromotion;
    }

    /**
     * 获取无货位订单,订单集合删除无货位订单项
     *
     * @param waveOrders
     */
    public static List<OutStockOrderPO> getNoLocationOrder(List<OutStockOrderPO> waveOrders) {
        List<OutStockOrderPO> noLocation = new ArrayList<>();

        List<OutStockOrderPO> lstNoLocation = waveOrders.stream()
                .filter(outStockOrderPO -> outStockOrderPO.getItems().stream()
                        .anyMatch(outStockOrderItemPO -> outStockOrderItemPO.getLocationId() == null))
                .collect(Collectors.toList());

        lstNoLocation.forEach(outStockOrderPO -> {
            OutStockOrderPO noLocationOrder = new OutStockOrderPO();
            BeanUtils.copyProperties(outStockOrderPO, noLocationOrder);

            noLocationOrder.setItems(noLocationOrder.getItems().stream().filter(o -> o.getLocationId() == null)
                    .collect(Collectors.toList()));
            noLocation.add(noLocationOrder);

            outStockOrderPO.setItems(outStockOrderPO.getItems().stream().filter(o -> o.getLocationId() != null)
                    .collect(Collectors.toList()));
        });

        return noLocation;
    }

}
