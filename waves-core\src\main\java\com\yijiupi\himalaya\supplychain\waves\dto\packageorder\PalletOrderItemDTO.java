package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.io.Serializable;
import java.math.BigDecimal;

public class PalletOrderItemDTO implements Serializable {

    /**
     * 包装箱详情id
     */
    private Long id;

    /**
     * 订单项id
     */
    private Long refOrderItemId;

    /**
     * 产品id
     */
    private Long skuId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 订单大单位数量
     */
    private BigDecimal orderPackageCount;

    /**
     * 订单小单位数量
     */
    private BigDecimal orderUnitCount;

    /**
     * 订单小单位总数量
     */
    private BigDecimal orderUnitTotalCount;

    /**
     * 拣货大单位数量
     */
    private BigDecimal pickingPackageCount;

    /**
     * 拣货小单位数量
     */
    private BigDecimal pickingUnitCount;

    /**
     * 拣货小单位数量
     */
    private BigDecimal pickingUnitTotalCount;

    /**
     * 复核小单位总数量
     */
    private BigDecimal overUnitTotalCount;

    /**
     * 包装规格
     */
    private String specName;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal specQuantity;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 订单id
     */
    private Long outStockOrderId;

    /**
     * 订单号
     */
    private String refOrderNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRefOrderItemId() {
        return refOrderItemId;
    }

    public void setRefOrderItemId(Long refOrderItemId) {
        this.refOrderItemId = refOrderItemId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getOrderPackageCount() {
        return orderPackageCount;
    }

    public void setOrderPackageCount(BigDecimal orderPackageCount) {
        this.orderPackageCount = orderPackageCount;
    }

    public BigDecimal getOrderUnitCount() {
        return orderUnitCount;
    }

    public void setOrderUnitCount(BigDecimal orderUnitCount) {
        this.orderUnitCount = orderUnitCount;
    }

    public BigDecimal getPickingPackageCount() {
        return pickingPackageCount;
    }

    public void setPickingPackageCount(BigDecimal pickingPackageCount) {
        this.pickingPackageCount = pickingPackageCount;
    }

    public BigDecimal getPickingUnitCount() {
        return pickingUnitCount;
    }

    public void setPickingUnitCount(BigDecimal pickingUnitCount) {
        this.pickingUnitCount = pickingUnitCount;
    }

    public BigDecimal getOverUnitTotalCount() {
        return overUnitTotalCount;
    }

    public void setOverUnitTotalCount(BigDecimal overUnitTotalCount) {
        this.overUnitTotalCount = overUnitTotalCount;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Long getOutStockOrderId() {
        return outStockOrderId;
    }

    public void setOutStockOrderId(Long outStockOrderId) {
        this.outStockOrderId = outStockOrderId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public BigDecimal getOrderUnitTotalCount() {
        return orderUnitTotalCount;
    }

    public void setOrderUnitTotalCount(BigDecimal orderUnitTotalCount) {
        this.orderUnitTotalCount = orderUnitTotalCount;
    }

    public BigDecimal getPickingUnitTotalCount() {
        return pickingUnitTotalCount;
    }

    public void setPickingUnitTotalCount(BigDecimal pickingUnitTotalCount) {
        this.pickingUnitTotalCount = pickingUnitTotalCount;
    }
}
