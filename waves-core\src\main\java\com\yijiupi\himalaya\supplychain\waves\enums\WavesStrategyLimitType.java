package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * Created by 余明 on 2018-03-19.
 */
public class WavesStrategyLimitType {
    /**
     * 订单数限制
     */
    public static final String orderCountLimitType = "订单总数量";

    /**
     * 订单明细行限制
     */
    public static final String skuCountLimitType = "订单明细行数量";

    /**
     * 件数限制
     */
    public static final String piecePackageNumberLimitType = "大件数量";

    /**
     * 件数限制
     */
    public static final String pieceUnitNumberLimitType = "小件数量";

    /**
     * 货值限制
     */
    public static final String orderAmountLimitType = "货值";
}
