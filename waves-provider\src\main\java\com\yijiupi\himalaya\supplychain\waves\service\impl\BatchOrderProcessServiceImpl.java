package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.util.List;

import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.lock.BatchInfoDistributeLockBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.AppendSowTaskBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.*;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.ScanReviewToSetLocationPalletInfoDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchOrderProcessService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderAllotBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderInfoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderProcessBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderProcessTransferBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.tms.NotifyTmsBatchCreateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationLargePickCargoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationOpenLocationGroupBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationRecommendBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationWorkSettingBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchBaseBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.CreateBatchByManualOperationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.CreateBatchByRefOrderNoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.wcs.NotifyWCSBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.UpdateOrderItemCountDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.OrderPrintInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.UpdateLocationDTO;

/**
 * Created by 余明 on 2018-03-15.
 */
@Service(timeout = 120000)
public class BatchOrderProcessServiceImpl implements IBatchOrderProcessService {
    private static final Logger LOG = LoggerFactory.getLogger(IBatchOrderProcessService.class);
    @Autowired
    private BatchOrderProcessBL batchOrderProcessBL;
    @Autowired
    private BatchOrderProcessTransferBL batchOrderProcessTransferBL;
    @Autowired
    private BatchOrderAllotBL batchOrderAllotBL;
    @Autowired
    private BatchOrderInfoBL batchOrderInfoBL;
    @Autowired
    private NotifyWCSBL notifyWCSBL;
    @Autowired
    private CreateBatchLocationOpenLocationGroupBL createBatchLocationOpenLocationGroupBL;
    @Autowired
    private CreateBatchLocationRecommendBL createBatchLocationRecommendBL;
    @Autowired
    private CreateBatchLocationLargePickCargoBL createBatchLocationLargePickCargoBL;
    @Autowired
    private CreateBatchLocationWorkSettingBL createBatchLocationWorkSettingBL;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderBL batchOrderBL;
    @Autowired
    private NotifyTmsBatchCreateBL notifyTmsBatchCreateBL;
    @Autowired
    private CreateBatchByRefOrderNoBL createBatchByRefOrderNoBL;
    @Autowired
    private CreateBatchByManualOperationBL createBatchByManualOperationBL;
    @Autowired
    private AppendSowTaskBL appendSowTaskBL;
    @Autowired
    private BatchInfoDistributeLockBL batchInfoDistributeLockBL;

    @Reference
    private WarehouseConfigService warehouseConfigService;

    /**
     * @param wareStrategyId 波次策略id
     * @param orderStartTime 订单开始时间
     * @param orderEndTime 订单结束时间
     * @return
     * @Description: 根据波次策略Id生成波次及波次任务
     * <AUTHOR>
     * @date 2018/4/2 15:07
     */
    @Override
    public void processBatchOrderByStrateId(Integer wareStrategyId, String orderStartTime, String orderEndTime) {
        AssertUtils.notNull(wareStrategyId, "波次策略ID不能为空！");
        batchOrderProcessBL.processBatchOrderByStrateId(wareStrategyId, orderStartTime, orderEndTime);
    }

    /**
     * @param batchCreateDTO
     * @return
     * @Description: 创建波次：手动新增波次
     * <AUTHOR>
     * @date 2018/4/2 15:06
     */
    @Override
    public void createBatch(BatchCreateDTO batchCreateDTO) {
        AssertUtils.notNull(batchCreateDTO, "波次不能为空");
        AssertUtils.notEmpty(batchCreateDTO.getOrderIdList(), "订单idList不能为空");
        AssertUtils.notNull(batchCreateDTO.getWarehouseId(), "仓库id不能为空");
        CreateBatchBaseBO createBatchBaseBO = new CreateBatchBaseBO(batchCreateDTO.getWarehouseId(), batchCreateDTO);
        createBatchByManualOperationBL.createBatch(createBatchBaseBO);

        notifyWCSBL.notifyBatchCreateByOrderIds(batchCreateDTO);
        notifyTmsBatchCreateBL.notifyTmsOutStoreLocationInfoByOrderIds(batchCreateDTO);
    }

    /**
     * 创建波次：按订单新增波次
     * 
     * @param batchCreateByRefOrderNoDTO
     */
    @Override
    public void createBatchByRefOrderNo(BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO) {
        AssertUtils.notNull(batchCreateByRefOrderNoDTO, "波次不能为空");
        AssertUtils.notNull(batchCreateByRefOrderNoDTO.getWarehouseId(), "仓库id不能为空");
        if (CollectionUtils.isEmpty(batchCreateByRefOrderNoDTO.getRefOrderNos())
            && CollectionUtils.isEmpty(batchCreateByRefOrderNoDTO.getOrderList())) {
            throw new BusinessException("订单号List不能为空");
        }
        CreateBatchBaseBO createBatchBaseBO =
            new CreateBatchBaseBO(batchCreateByRefOrderNoDTO.getWarehouseId(), batchCreateByRefOrderNoDTO);
        createBatchByRefOrderNoBL.createBatch(createBatchBaseBO);
        notifyWCSBL.notifyBatchCreateByOrders(batchCreateByRefOrderNoDTO);
        notifyTmsBatchCreateBL.notifyTmsOutStoreLocationInfoByOrderNo(batchCreateByRefOrderNoDTO);
    }

    /**
     * 追加播种任务
     *
     * @param appendSowTaskDTO
     */
    @Override
    public void appendSowTask(AppendSowTaskDTO appendSowTaskDTO) {
        AssertUtils.notNull(appendSowTaskDTO, "追加信息不能为空");
        AssertUtils.notNull(appendSowTaskDTO.getOrgId(), "城市信息不能为空");
        AssertUtils.notNull(appendSowTaskDTO.getWarehouseId(), "仓库信息不能为空");
        AssertUtils.notNull(appendSowTaskDTO.getSowTaskId(), "播种信息不能为空");
        AssertUtils.notNull(appendSowTaskDTO.getOptUserId(), "操作人信息不能为空");
        AssertUtils.notEmpty(appendSowTaskDTO.getOutStockOrderIds(), "出库单信息不能为空");
        String batchNo = appendSowTaskBL.getSowTaskBatchNo(appendSowTaskDTO);
        try {
            batchInfoDistributeLockBL.lockAppendSowTask(batchNo);
            appendSowTaskBL.appendSowTask(appendSowTaskDTO);
            notifyWCSBL.notifyByAppendBatch(appendSowTaskDTO);
        } catch (Exception e) {
            if (e.getMessage().contains("已经加锁")) {
                throw new BusinessValidateException("选中订单已被处理，请刷新界面后，重新选择订单追加！");
            }
            throw e;
        } finally {
            batchInfoDistributeLockBL.releaseLock(batchNo);
        }
    }

    /**
     * 按产品+订单 手动新增波次（知花知果）
     */
    @Override
    public void createBatchByProductAndOrder(BatchCreateByProductAndOrderDTO batchCreateByProductAndOrderDTO) {
        AssertUtils.notNull(batchCreateByProductAndOrderDTO, "波次不能为空");
        AssertUtils.notEmpty(batchCreateByProductAndOrderDTO.getProductList(), "产品不能为空");
        batchOrderProcessTransferBL.createBatchByProductAndOrder(batchCreateByProductAndOrderDTO);
    }

    /**
     * 获取波次分配的优先级及缺货产品
     */
    @Override
    public BatchAllotPriorityDTO
        getBatchAllotPriority(BatchCreateByProductAndOrderDTO batchCreateByProductAndOrderDTO) {
        AssertUtils.notNull(batchCreateByProductAndOrderDTO, "参数不能为空");
        AssertUtils.notNull(batchCreateByProductAndOrderDTO.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(batchCreateByProductAndOrderDTO.getWarehouseId(), "仓库ID不能为空");
        return batchOrderAllotBL.getBatchAllotPriority(batchCreateByProductAndOrderDTO);
    }

    @Override
    public void saveOrderPrintInfo(OrderPrintInfoDTO orderPrintInfoDTO) {
        batchOrderInfoBL.saveOrderPrintInfo(orderPrintInfoDTO);
    }

    @Override
    public void fixCount(UpdateOrderItemCountDTO dto) {
        batchOrderInfoBL.fixCount(dto);
    }

    @Override
    public void updateLocation(UpdateLocationDTO updateLocationDTO) {
        batchOrderProcessTransferBL.updateLocationByRefOrder(updateLocationDTO);
    }

    /**
     * 扫码复核后设置出库位和托盘位
     *
     * @param dto
     */
    @Override
    public void scanReviewToSetLocationPalletInfo(ScanReviewToSetLocationPalletInfoDTO dto) {
        AssertUtils.notNull(dto.getOptUserId(), "操作人信息不能为空！");
        AssertUtils.notNull(dto.getLocationId(), "出库位信息不能为空！");
        AssertUtils.notEmpty(dto.getPalletNoList(), "托盘信息不能为空！");
        AssertUtils.notNull(dto.getOrderNo(), "订单信息不能为空！");
        AssertUtils.hasText(dto.getLocationName(), "出库位名称信息不能为空！");

        batchOrderInfoBL.scanReviewToSetLocationPalletInfo(dto);
    }

    @Override
    public List<OutStockOrderDTO> checkCreateBatchByCustomer(BatchCreateDTO batchCreateDTO) {
        AssertUtils.notNull(batchCreateDTO, "波次不能为空");
        AssertUtils.notEmpty(batchCreateDTO.getOrderIdList(), "订单idList不能为空");
        AssertUtils.notNull(batchCreateDTO.getWarehouseId(), "仓库id不能为空");
        CreateBatchBaseBO createBatchBaseBO = new CreateBatchBaseBO(batchCreateDTO.getWarehouseId(), batchCreateDTO);
        return createBatchByManualOperationBL.checkCreateBatchByCustomer(createBatchBaseBO);
    }
}
