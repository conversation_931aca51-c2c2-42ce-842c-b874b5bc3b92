package com.yijiupi.himalaya.supplychain.waves.util;

import com.yijiupi.himalaya.base.exception.BusinessCodeException;
import com.yijiupi.himalaya.base.exception.BusinessCodeValidateException;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-08-15 10:00
 **/
public class BusinessCodeExceptionUtils {

    /**
     * 构造一个 {@link BusinessCodeException}, 参考 {@link BusinessCodeException#BusinessCodeException(String, String, Map)}
     *
     * @param errorCode    参考 {@link BusinessCodeException#getErrorCode()}
     * @param errorMessage 参考 {@link BusinessCodeException#getErrorMessage()}
     * @param params       参考 {@link BusinessCodeException#BusinessCodeException(String, String, Map)}
     * @return {@link BusinessCodeException} 对象
     */
    public static BusinessCodeException newException(String errorCode, String errorMessage, Object... params) {
        return new BusinessCodeException(errorCode, errorMessage, buildParam(params));
    }

    /**
     * 构造一个 {@link BusinessCodeValidateException}
     *
     * @return {@link BusinessCodeValidateException} 对象
     * @see #newException(String, String, Object...)
     */
    public static BusinessCodeValidateException newValidateException(String errorCode, String errorMessage, Object... params) {
        return new BusinessCodeValidateException(errorCode, errorMessage, buildParam(params));
    }

    private static Map<String, String> buildParam(Object... params) {
        if (params == null || params.length == 0) {
            return Collections.emptyMap();
        }
        if (params.length % 2 != 0) {
            throw new IllegalArgumentException("参数必须是偶数个");
        }
        Map<String, String> paramMap = new HashMap<>();
        for (int i = 0; i < params.length; i += 2) {
            paramMap.put(String.valueOf(params[i]), String.valueOf(params[i + 1]));
        }
        return paramMap;
    }

}
