package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import static com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum.分拣中;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryManageService;
import com.yijiupi.himalaya.supplychain.enums.InventoryChangeTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.advice.MarkMethodInvokeFlag;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.*;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchFinishedBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderconstraint.OrderConstraintCheckBL;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchTaskItemCompleteConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchTaskKindOfPickingConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskCheckSaleSpecDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemContainerDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.CompleteBatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.himalaya.supplychain.waves.util.UUIDGeneratorUtil;
import com.yijiupi.supplychain.serviceutils.constant.redis.RedisConstant;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/19
 */
@Service
public class BatchTaskItemCompleteBL {

    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private BatchTaskItemContainerMapper batchTaskItemContainerMapper;
    @Autowired
    private OrderItemTaskInfoDetailMapper orderItemTaskInfoDetailMapper;
    @Resource
    private OrderConstraintCheckBL orderConstraintCheckBL;
    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;
    @Autowired
    private PackageOrderItemBL packageOrderItemBL;
    @Autowired
    private OrderTraceBL orderTraceBL;
    @Autowired
    private OutStockOrderBL outStockOrderBL;
    @Autowired
    private BatchFinishedBL batchFinishedBL;
    @Autowired
    private BatchTaskFinishValidateBL batchTaskFinishValidateBL;
    @Autowired
    private BatchOrderBL batchOrderBL;
    @Autowired
    private BatchInventoryTransferBL batchInventoryTransferBL;
    @Autowired
    private BatchTaskChangeNotifyBL batchTaskChangeNotifyBL;
    @Autowired
    private BatchTaskItemCompleteConvertor batchTaskItemCompleteConvertor;
    @Autowired
    private BatchTaskItemFinishValidateBL batchTaskItemFinishValidateBL;

    @Reference
    private IBatchInventoryManageService iBatchInventoryManageService;

    private static final String PROMPT_MSG = "产品总库存不足，请联系仓管确认缺货或者盘点加库存。";
    private static final Logger LOGGER = LoggerFactory.getLogger(BatchTaskItemCompleteBL.class);
    @Autowired
    private GlobalCache globalCache;

    @MarkMethodInvokeFlag(key = RedisConstant.SUP_F + "UserOnlineCache",
        warehouseId = "#batchTaskItemCompleteDTO.warehouseId", userId = "#batchTaskItemCompleteDTO.userId")
    @DistributeLock(conditions = "#batchTaskItemCompleteDTO.batchTaskId", sleepMills = 3000, expireMills = 60000,
        key = BatchTaskConstants.BATCH_TASK_COMPLETE_KEY)
    @Transactional(rollbackFor = RuntimeException.class)
    public Map<String, List<BatchTaskDTO>> completeBatchTaskItem(CompleteBatchTaskItemDTO batchTaskItemCompleteDTO) {
        List<BatchTaskItemCompleteDTO> batchTaskItemUpdateDTOList =
            batchTaskItemCompleteDTO.getBatchTaskItemUpdateDTOList();
        String batchTaskId = batchTaskItemCompleteDTO.getBatchTaskId();
        String userName = batchTaskItemCompleteDTO.getUserName();
        Integer warehouseId = batchTaskItemCompleteDTO.getWarehouseId();
        Long locationId = batchTaskItemCompleteDTO.getLocationId();
        String locationName = batchTaskItemCompleteDTO.getLocationName();
        Integer cityId = batchTaskItemCompleteDTO.getCityId();
        Integer userId = batchTaskItemCompleteDTO.getUserId();
        Byte containerFlag = batchTaskItemCompleteDTO.getContainerFlag();

        LOGGER.info("提交拣货：{}, batchTaskId:{}", JSON.toJSONString(batchTaskItemCompleteDTO), batchTaskId);
        List<String> updateBatchTaskItemIds =
            batchTaskItemUpdateDTOList.stream().map(BatchTaskItemCompleteDTO::getId).collect(Collectors.toList());
        List<BatchTaskItemPO> oldBatchTaskItemPOS =
            batchTaskItemMapper.listBatchTaskItemByIds(updateBatchTaskItemIds, null);
        if (CollectionUtils.isEmpty(oldBatchTaskItemPOS)) {
            throw new BusinessValidateException("拣货详情不存在，请刷新重试！");
        }
        if (oldBatchTaskItemPOS.size() != updateBatchTaskItemIds.size()) {
            throw new BusinessValidateException("部分拣货详情不存在，请刷新重试！");
        }
        BatchTaskPO batchTaskOld = batchTaskMapper.findBatchTaskById(batchTaskId);
        AssertUtils.notNull(batchTaskOld, "拣货任务不存在！Id：" + batchTaskId);
        if (batchTaskOld.getTaskState() == TaskStateEnum.已完成.getType()) {
            throw new BusinessValidateException("拣货已完成，请勿重复操作！Id：" + batchTaskId);
        }
        if (TaskStateEnum.已作废.valueEquals(batchTaskOld.getTaskState())) {
            LOGGER.info("拣货任务已作废: {}", JSON.toJSONString(batchTaskOld, SerializerFeature.WriteMapNullValue));
            return Collections.emptyMap();
        }

        // 电子标签任务，不允许使用PDA提交，提示先转人工再操作
        if (Objects.equals(BatchTaskPickPatternEnum.电子标签.getType(), batchTaskOld.getPickPattern())
            && Objects.equals(batchTaskItemCompleteDTO.getOperateSource(), BatchTaskCompleteDTO.SOURCE_PDA)) {
            throw new BusinessValidateException("电子标签任务，不允许使用PDA提交，请先将任务转人工后重试！");
        }

        // 验证拣货员
        validateSorterInfo(batchTaskOld, userId);
        userId = getSorterId(batchTaskOld, userId);
        BatchPO batchPO = batchMapper.selectBatchByBatchNo(
            StringUtils.isEmpty(batchTaskOld.getOrgId()) ? null : Integer.valueOf(batchTaskOld.getOrgId()),
            batchTaskOld.getBatchNo());
        if (batchPO == null) {
            throw new BusinessException("拣货任务对应的波次任务不存在！Id：" + batchTaskId);
        }
        // 前端传过来的出库位跟拣货任务出库位不一样时，提示报错
        if (batchTaskOld.getToLocationId() != null && locationId != null) {
            if (!Objects.equals(batchTaskOld.getToLocationId(), locationId)) {
                LOGGER.info("拣货任务的出库位已变更, ToLocationId: {}, ToLocationName: {}, locationId: {}, locationName: {}",
                    batchTaskOld.getToLocationId(), batchTaskOld.getToLocationName(), locationId, locationName);
                throw new BusinessValidateException("拣货任务的出库位已变更，请回退到待拣货列表重试！");
            }
        }

        batchTaskItemUpdateDTOList =
            batchTaskItemCompleteConvertor.resetBatchTaskItemCompleteDTO(batchTaskItemUpdateDTOList, batchTaskOld);
        LOGGER.info("提交拣货，重组后参数为：{}", JSON.toJSONString(batchTaskItemUpdateDTOList));
        updateBatchTaskItemIds =
            batchTaskItemUpdateDTOList.stream().map(BatchTaskItemCompleteDTO::getId).collect(Collectors.toList());

        // 获取拣货任务项关联的订单项
        List<OrderItemTaskInfoDetailPO> updateTaskInfoDetailList = new ArrayList<>();
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByBatchTaskItemIds(updateBatchTaskItemIds);
        List<Long> outStockOrderIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findIdByIds(outStockOrderIds, warehouseId);
        // 校验订单能否缺货
        orderConstraintCheckBL.checkNormalOrderPickLack(warehouseId, batchTaskItemUpdateDTOList, outStockOrderPOList,
            orderItemTaskInfoPOList);
        Map<Long, BigDecimal> taskInfoDetailOverSortCountMap = new HashMap<>();
        Map<Long, BigDecimal> lastTaskInfoLackCountMap = new HashMap<>();
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            LOGGER.info("[PDA拣货]获取拣货任务项关联的订单项为空：{}", JSON.toJSONString(updateBatchTaskItemIds));
        } else {
            // 记录上次已拣数量
            orderItemTaskInfoPOList.forEach(p -> {
                if (CollectionUtils.isNotEmpty(p.getDetailList())) {
                    p.getDetailList().forEach(detail -> {
                        taskInfoDetailOverSortCountMap.put(detail.getId(), detail.getUnitTotalCount());
                    });
                }
                // 记录上次缺货数量
                lastTaskInfoLackCountMap.put(p.getId(), p.getLackUnitCount());
            });
        }

        // 查找该波次任务所有商品详情
        List<BatchTaskItemDTO> batchTaskItemDTOList = batchTaskItemMapper.findBatchTaskItemDtoListById(batchTaskId);
        initParam(batchTaskItemDTOList, batchTaskItemCompleteDTO);
        Map<String, BatchTaskItemCompleteDTO> map = new HashMap<>();
        batchTaskItemUpdateDTOList.forEach(p -> {
            map.put(p.getId(), p);
        });
        // 波次任务的状态.默认分拣完成
        byte taskState = TaskStateEnum.已完成.getType();
        List<BatchTaskItemUpdatePO> poList = new ArrayList<>();
        List<Long> lstOutStockOrders = new ArrayList<>();
        List<PackageOrderItemDTO> packageOrderItemDTOList = new ArrayList<>();
        List<PickUpDTO> pickUpDTOList = new ArrayList<>();
        List<BatchTaskCheckSaleSpecDTO> checkSaleSpecDTOList = new ArrayList<>();
        // 拣货任务明细项容器位集合
        List<BatchTaskItemContainerPO> containerPOList = new ArrayList<BatchTaskItemContainerPO>();

        // 获取集货位信息
        List<SowTaskDTO> sowTasks = batchOrderTaskBL.getSowTasks(batchTaskItemDTOList);

        // 获取集货区
        Long sowLocationId = null;
        if (batchTaskOld.getSowTaskId() != null) {
            Optional<SowTaskDTO> sowTaskDTO =
                sowTasks.stream().filter(p -> p.getId().equals(batchTaskOld.getSowTaskId())).findAny();
            if (sowTaskDTO.isPresent() && sowTaskDTO.get().getLocationId() != null) {
                sowLocationId = sowTaskDTO.get().getLocationId();
            }
        }

        // 分单拣货的合单通知集合
        List<String> orderNos = new ArrayList<>();
        Map<String, List<BatchTaskDTO>> msgMap = new HashMap<>();

        // 查找该波次任务所有出库单详情列表和转换sku列表,后续装箱需用到
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOS = new ArrayList<>();
        // Map<Integer, Map<Long, Long>> actualDeliverySkuIdMap = new HashMap<>();
        List<BatchTaskItemCompleteDTO> packageUpdateList = batchTaskItemUpdateDTOList.stream()
            .filter(dto -> CollectionUtils.isNotEmpty(dto.getBoxCodeList())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(packageUpdateList)) {
            // 查询订单拣货任务关联表
            orderItemTaskInfoPOS = batchOrderTaskBL.getCurBatchTaskItem(batchTaskItemDTOList, orderItemTaskInfoPOList);
            // LOGGER.info("拣货订单数据:{}", JSON.toJSONString(orderItemTaskInfoPOS));
        }

        for (BatchTaskItemDTO batchTaskItemDTO : batchTaskItemDTOList) {
            // 已完成的项，不需要再处理
            // if (TaskStateEnum.已完成.getType() == batchTaskItemDTO.getTaskState()) {
            // continue;
            // }
            BatchTaskItemCompleteDTO dto = map.get(batchTaskItemDTO.getId());
            // 当存在波次任务中的一个商品详情用户没有对其做改变时,分析该商品
            if (dto == null) {
                // 判断在数据库中的状态是否为已完成
                if (TaskStateEnum.已完成.getType() != batchTaskItemDTO.getTaskState()) {
                    taskState = 分拣中.getType();
                }
                continue;
            }
            // 初次提交时，如果拣货任务项状态不是“未分拣”，则报异常
            if (Objects.equals(dto.getSubmitFlag(), SubmitFlagEnum.初次提交)
                && !Objects.equals(batchTaskItemDTO.getTaskState(), TaskStateEnum.未分拣.getType())) {
                throw new BusinessValidateException("不能重复提交，请刷新重试或者联系技术支持！");
            }
            BigDecimal specQuantity = batchTaskItemDTO.getSpecQuantity();
            // 数据库中拣货数量+用户拣货数量
            BigDecimal overSortCount =
                dto.getOverSortPackageCount().multiply(specQuantity).add(dto.getOverSortUnitCount());
            // 数据库中缺省数量+用户缺省数量
            BigDecimal lackCount = dto.getLackPackageCount().multiply(specQuantity).add(dto.getLackUnitCount());
            if (overSortCount.add(lackCount).compareTo(batchTaskItemDTO.getUnitTotalCount()) != 0) {
                LOGGER.info("已拣货数量 加 缺货数量 不等于 待拣货数量: {}", JSON.toJSONString(batchTaskItemDTO));
                throw new BusinessValidateException("拣货数量有误，请返回拣货任务列表刷新重试！");
            }

            if (StringUtils.isNotEmpty(batchTaskItemDTO.getRefOrderId())
                && PickingTypeEnum.订单拣货.getType() == batchTaskOld.getPickingType()) {
                // 按订单拣货，一次至少完成一个订单
                lstOutStockOrders.add(Long.valueOf(batchTaskItemDTO.getRefOrderId()));
            }
            if (StringUtils.isNotEmpty(batchTaskItemDTO.getRefOrderNo())
                && PickingTypeEnum.订单拣货.getType() == batchTaskOld.getPickingType()) {
                orderNos.add(batchTaskItemDTO.getRefOrderNo());
            }

            // 对用户做改变的商品构建PO
            BatchTaskItemUpdatePO po = new BatchTaskItemUpdatePO();
            po.setId(dto.getId());
            po.setOverSortCount(overSortCount);
            po.setLackCount(lackCount);
            po.setTaskState(TaskStateEnum.已完成.getType());
            po.setLastUpdateUser(userName);
            if (lackCount.compareTo(BigDecimal.ZERO) > 0) {
                // 缺货
                po.setIsLack((byte)1);
            } else {
                po.setIsLack((byte)0);
            }
            // 来源货位id（如果客户端传过来为空，则取拣货任务详情的货位id）
            Long fromLocationId = dto.getFromLocationId();
            String fromLocationName = dto.getFromLocationName();
            if (Objects.isNull(fromLocationId)) {
                fromLocationId = batchTaskItemDTO.getLocationId();
            }
            if (StringUtils.isBlank(fromLocationName)) {
                fromLocationName = batchTaskItemDTO.getLocationName();
            }
            po.setFromLocationId(fromLocationId);
            po.setFromLocationName(fromLocationName);
            po.setStartTime(dto.getStartTime());
            po.setCompleteTime(dto.getCompleteTime());
            po.setRemark(dto.getRemark());
            if (BatchTaskKindOfPickingConvertor.isSecondSort(batchPO, batchTaskOld)) {
                po.setSownUnitTotalCount(new BigDecimal(SowTaskStateEnum.待播种.getType()));
            }
            po.setCompleteUserId(userId);
            po.setCompleteUser(userName);
            poList.add(po);

            // 处理拣货任务项关联的订单项的已拣货数量和缺货数量
            List<OrderItemTaskInfoPO> nowBatchTaskItemInfoList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
                nowBatchTaskItemInfoList = orderItemTaskInfoPOList.stream()
                    .filter(p -> Objects.equals(p.getBatchTaskItemId(), dto.getId())).collect(Collectors.toList());
                batchOrderTaskBL.processOrderItemTaskInfoPOList(nowBatchTaskItemInfoList, lackCount,
                    updateTaskInfoDetailList, batchTaskItemDTO, outStockOrderPOList);
            }

            // 移库
            // 开启容器位拣货
            if (Objects.equals(containerFlag, ConditionStateEnum.是.getType())) {
                if (CollectionUtils.isEmpty(dto.getContainerList())) {
                    throw new BusinessException("容器位不能为空");
                }
                List<BatchTaskItemContainerPO> containerList =
                    batchTaskItemContainerMapper.selectByBatchtaskitemId(batchTaskItemDTO.getId());
                /*if (TaskStateEnum.已完成.getType() == batchTaskItemDTO.getTaskState()) {
                	containerMap = batchTaskItemContainerMapper.selectByBatchtaskitemId(batchTaskItemDTO.getId());
                }*/
                for (BatchTaskItemContainerDTO containerDTO : dto.getContainerList()) {
                    BigDecimal pickUnitTotalCount =
                        containerDTO.getPickPackageCount().multiply(specQuantity).add(containerDTO.getPickUnitCount());
                    BatchTaskItemContainerPO containerPO = new BatchTaskItemContainerPO();
                    containerPO.setOrgId(cityId);
                    containerPO.setWarehouseId(warehouseId);
                    containerPO.setBatchtaskitemId(Long.valueOf(batchTaskItemDTO.getId()));
                    containerPO.setLocationId(containerDTO.getLocationId());
                    containerPO.setLocationName(containerDTO.getLocationName());
                    containerPO.setPickUnitTotalCount(pickUnitTotalCount);
                    containerPO.setCreateUserId(userId);
                    containerPO.setLastUpdateUserId(userId);
                    containerPO.setRemark("PDA");

                    // 本次移库小单位数量
                    overSortCount = pickUnitTotalCount;
                    // 重复拣货时，移库数量 = 本次拣货数量 - 上次拣货数量
                    if (containerList != null
                        && containerList.size() > 0/*containerMap.get(containerDTO.getLocationId()) != null*/) {
                        BatchTaskItemContainerPO oldContainerPO = containerList.get(0);
                        fromLocationId = oldContainerPO.getLocationId();
                        containerPO.setId(oldContainerPO.getId());
                        // overSortCount = overSortCount.subtract(oldContainerPO.getPickUnitTotalCount());
                        batchTaskItemContainerMapper.updateByPrimaryKeySelective(containerPO);
                    } else {
                        containerPOList.add(containerPO);
                    }
                    PickUpDTO pickUpDTO = batchOrderTaskBL.getContainerPickUpDTO(warehouseId,
                        containerDTO.getLocationId(), batchTaskItemDTO, overSortCount, fromLocationId);
                    if (null != pickUpDTO) {
                        pickUpDTOList.add(pickUpDTO);
                    }
                }
            } else {
                if (BatchTaskKindOfPickingConvertor.isSecondSort(batchPO, batchTaskOld)) {
                    batchOrderTaskBL.getSecondSortPickUpDTO(pickUpDTOList, warehouseId, batchTaskItemDTO,
                        fromLocationId, dto, sowLocationId, nowBatchTaskItemInfoList, taskInfoDetailOverSortCountMap,
                        sowTasks);
                } else {
                    batchOrderTaskBL.getPickUpDTO(pickUpDTOList, warehouseId, locationId, batchTaskItemDTO,
                        overSortCount, fromLocationId, dto, sowLocationId, nowBatchTaskItemInfoList,
                        taskInfoDetailOverSortCountMap);
                }
            }

            // 拣货装箱
            packageOrderItemBL.pickingAndPacking(warehouseId, cityId, userId, userName, orderItemTaskInfoPOS,
                batchTaskItemDTO, dto.getBoxCodeList(), packageOrderItemDTOList);

            // 校验销售规格数量参数
            BatchTaskCheckSaleSpecDTO batchTaskCheckSaleSpecDTO =
                batchOrderTaskBL.getCheckSaleSpec(batchTaskItemDTO, overSortCount);
            checkSaleSpecDTOList.add(batchTaskCheckSaleSpecDTO);
        }

        // 校验销售规格
        batchOrderTaskBL.checkSaleSpec(checkSaleSpecDTOList);

        // 1、修改波次任务详情状态
        if (!CollectionUtils.isEmpty(poList)) {
            // 校验是否标缺货
            batchOrderTaskBL.checkIsLack(poList, batchPO.getBatchType());

            batchTaskItemMapper.updateBatchTaskByItemIncludeLocation(poList, cityId);
        }
        // 新增拣货明细项容器位
        if (containerPOList.size() > 0) {
            containerPOList.forEach(p -> p.setId(UUIDGeneratorUtil.getUUID(BatchTaskItemContainerPO.class.getName())));
            batchTaskItemContainerMapper.insertList(containerPOList);
        }

        // 2、修改波次任务状态
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);

        // 存在集货位时，出库位设置为空
        if (sowLocationId != null) {
            locationId = null;
            locationName = null;
        }
        batchTaskMapper.updateBatchTaskById(batchTaskPO.getId(), 分拣中.getType(), locationId, locationName, null, null,
            null, cityId, null);
        // // 存在集货位时，出库位设置为空
        // if (sowLocationId != null) {
        // locationId = null;
        // locationName = null;
        // }
        // batchTaskMapper.updateBatchTaskById(batchTaskPO.getId(), taskState, locationId, locationName, null, null,
        // null,
        // cityId);
        // if (taskState == TaskStateEnum.已完成.getType()) {
        // // -添加操作记录（完成拣货）
        // orderTraceBL.updateBatchTaskTrace(batchTaskPO, OrderTraceDescriptionEnum.拣货完成.name(), userName);
        // }
        // 更新拣货任务项关联的订单项的拣货数量和缺货数量
        if (CollectionUtils.isNotEmpty(orderItemTaskInfoPOList)) {
            // 修改播种明细数量
            // List<String> batchItemIds = batchTaskItemDTOList.stream().filter(item -> item.getSowTaskId() !=
            // null).map(BatchTaskItemDTO::getId).collect(Collectors.toList());
            // List<OrderItemTaskInfoPO> sowOrderItemTaskInfoPOS = orderItemTaskInfoPOList.stream().filter(item ->
            // batchItemIds.contains(item.getBatchTaskItemId())).collect(Collectors.toList());
            // updateSowTaskItemCount(sowOrderItemTaskInfoPOS);

            Lists.partition(orderItemTaskInfoPOList, 50).forEach(p -> {
                orderItemTaskInfoMapper.updateBatch(p);
            });
            LOGGER.info("[PDA拣货]更新【orderItemTaskInfo】拣货数量和缺货数量：{}", JSON.toJSONString(orderItemTaskInfoPOList));
        }

        // 是否跨库
        boolean isCrossWareHouse =
            Objects.equals(batchPO.getCrossWareHouse(), ConditionStateEnum.是.getType()) ? true : false;

        // 3、校验移库
        if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
            pickUpDTOList = batchInventoryTransferBL.checkInventoryTransferByPickUp(pickUpDTOList, poList, batchTaskPO,
                isCrossWareHouse);
            // 根据实际移库数量更新出库单项关联明细表
            batchOrderTaskBL.processOrderItemTaskInfoDetail(orderItemTaskInfoPOList, updateTaskInfoDetailList,
                pickUpDTOList, taskInfoDetailOverSortCountMap);
        }
        // 更新出库单项关联明细表
        if (CollectionUtils.isNotEmpty(updateTaskInfoDetailList)) {
            orderItemTaskInfoDetailMapper.updateBatch(updateTaskInfoDetailList);
            LOGGER.info("[PDA拣货]更新【orderItemTaskInfoDetail】分配数量：{}", JSON.toJSONString(updateTaskInfoDetailList));
        }

        batchTaskItemFinishValidateBL.validatePromotionTransferLegal(pickUpDTOList, oldBatchTaskItemPOS, batchTaskPO,
            orderItemTaskInfoPOList);

        // 4、有播种任务的拣货任务项实时标记缺货
        List<String> batchTaskItemIds = poList.stream().map(p -> p.getId()).collect(Collectors.toList());

        /** 二次分拣，不实时同步oms */
        if (!BatchTaskKindOfPickingConvertor.isSecondSort(batchPO, batchTaskOld)) {
            outStockOrderBL.markLackBySowTask(batchTaskItemIds, lastTaskInfoLackCountMap, userId);
        }

        // 5、保存装箱信息
        if (CollectionUtils.isNotEmpty(packageOrderItemDTOList)) {
            packageOrderItemBL.savePackageBatch(packageOrderItemDTOList, true);
        }
        if (CollectionUtils.isEmpty(packageUpdateList)) {
            batchOrderTaskBL.handleAllPackageRemove(orderItemTaskInfoPOList, batchTaskItemDTOList, map);
        }

        // 6、修改波次状态
        // try {
        // batchFinishedBL.completeWave(batchTaskOld.getBatchNo(), userId, Integer.valueOf(batchTaskOld.getOrgId()));
        // // batchOrderBL.updateBatchStateByBatchNo(batchTaskOld.getBatchNo(), userName, userId);
        // } catch (HasLockedException e) {
        // throw new BusinessValidateException("正在处理，请稍后再试！");
        // }

        // 7、按订单拣货时，一个订单拣完时更新出库单状态
        if (lstOutStockOrders.size() > 0) {
            outStockOrderBL.updateStateByOrderIds(lstOutStockOrders, batchPO);
        }

        // 8、分单拣货(整单完成需保存打印数据，整单未完成通知PDA将拆分出去的单合过来)
        if (batchTaskPO.getPickingType() == PickingTypeEnum.订单拣货.getType() && CollectionUtils.isNotEmpty(orderNos)) {
            orderNos = orderNos.stream().distinct().collect(Collectors.toList());
            msgMap =
                batchOrderBL.separateOrderPicking(batchTaskId, map.keySet(), orderNos, userName, warehouseId, cityId);
        }
        batchTaskFinishValidateBL.validateIsMeiTuan(orderItemTaskInfoPOList, Collections.singletonList(batchTaskPO));
        // 9、移库操作
        if (CollectionUtils.isNotEmpty(pickUpDTOList) && !isCrossWareHouse) {
            LOGGER.info("拣货移库入参：{}，batchTaskNO：{}", new Gson().toJson(pickUpDTOList), batchTaskOld.getBatchTaskNo());
            PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
            pickUpChangeRecordDTO.setCityId(cityId);
            pickUpChangeRecordDTO.setOrderId(batchTaskOld.getId());
            pickUpChangeRecordDTO.setOrderNo(batchTaskOld.getBatchTaskNo());
            pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.PDA提交拣货.name());
            pickUpChangeRecordDTO.setCreateUser(userName);
            iBatchInventoryManageService.pickupCompleteBySku(pickUpDTOList, pickUpChangeRecordDTO);
        }
        // 二次分拣拣货任务，则缺货更新出库单
        if (BatchTaskKindOfPickingConvertor.isSecondSort(batchPO, batchTaskOld)) {
            batchOrderTaskBL.updateSecondSow(poList, orderItemTaskInfoPOList, batchPO, batchTaskOld, taskState);
        }

        batchTaskChangeNotifyBL.notifyBatchTaskItemComplete(oldBatchTaskItemPOS, batchTaskPO);
        // 更新出库单为拣货中
        updateOutStockOrder(outStockOrderPOList);
        return msgMap;
    }

    private void updateOutStockOrder(List<OutStockOrderPO> outStockOrderPOList) {
        List<OutStockOrderPO> waitPickOrderList = outStockOrderPOList.stream()
            .filter(m -> m.getState().byteValue() == OutStockOrderStateEnum.待拣货.getType()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(waitPickOrderList)) {
            return;
        }

        List<Long> ids = waitPickOrderList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
        outStockOrderMapper.updateStateByOrderIds(ids, OutStockOrderStateEnum.拣货中.getType());
    }

    private List<PickUpDTO> checkInventoryTransferByPickUp(List<PickUpDTO> pickUpDTOList, boolean isCrossWareHouse) {
        if (!isCrossWareHouse) {
            LOGGER.info("校验移库入参：{}", JSON.toJSONString(pickUpDTOList));
            try {
                pickUpDTOList = iBatchInventoryManageService.checkInventoryTransferByPickUp(pickUpDTOList);
            } catch (DataValidateException e) {
                if (e.getMessage().contains("库存不足")) {

                } else {
                    throw e;
                }
            }
            return pickUpDTOList;
        }

        return pickUpDTOList;

    }

    private void validateSorterInfo(BatchTaskPO batchTaskOld, Integer userId) {
        // 1111111为机器人拣货
        if (RobotSorterConstants.ROBOT_ID.equals(batchTaskOld.getSorterId())) {
            return;
        }
        if (Objects.equals(userId, batchTaskOld.getSorterId())) {
            return;
        }
        if (BatchTaskPickPatternEnum.电子标签.getType() != batchTaskOld.getPickPattern()) {
            throw new BusinessValidateException("提交失败，此拣货任务已经指派给拣货员：" + batchTaskOld.getSorter());
        }
        boolean openDigitalTag = globalCache.openDigitalTag(batchTaskOld.getWarehouseId());

        if (BooleanUtils.isFalse(openDigitalTag)) {
            throw new BusinessValidateException("提交失败，此拣货任务已经指派给拣货员：" + batchTaskOld.getSorter());
        }
    }

    private Integer getSorterId(BatchTaskPO batchTaskOld, Integer userId) {
        if (RobotSorterConstants.ROBOT_ID.equals(batchTaskOld.getSorterId())) {
            return batchTaskOld.getSorterId();
        }
        if (Objects.equals(userId, batchTaskOld.getSorterId())) {
            return batchTaskOld.getSorterId();
        }
        boolean isDigital = BatchTaskPickPatternEnum.电子标签.getType() == batchTaskOld.getPickPattern();
        boolean openDigitalTag = globalCache.openDigitalTag(batchTaskOld.getWarehouseId());

        if (openDigitalTag && isDigital) {
            return batchTaskOld.getSorterId();
        }

        return userId;
    }

    private void initParam(List<BatchTaskItemDTO> batchTaskItemDTOList,
        CompleteBatchTaskItemDTO batchTaskItemCompleteDTO) {
        if (!BatchTaskCompleteDTO.SOURCE_DIGITAl.equals(batchTaskItemCompleteDTO.getOperateSource())) {
            return;
        }
        Date currentTime = new Date();
        Map<String, BatchTaskItemDTO> batchTaskItemDTOMap =
            batchTaskItemDTOList.stream().collect(Collectors.toMap(BatchTaskItemDTO::getId, v -> v));
        batchTaskItemCompleteDTO.getBatchTaskItemUpdateDTOList().forEach(item -> {
            BatchTaskItemDTO batchTaskItemDTO = batchTaskItemDTOMap.get(item.getId());
            BigDecimal[] count = item.getOverSortUnitCount().divideAndRemainder(batchTaskItemDTO.getSpecQuantity());

            item.setOverSortPackageCount(count[0]);
            item.setOverSortUnitCount(count[1]);

            BigDecimal[] lackCount = item.getLackUnitCount().divideAndRemainder(batchTaskItemDTO.getSpecQuantity());

            item.setLackPackageCount(lackCount[0]);
            item.setLackUnitCount(lackCount[1]);
            item.setCompleteTime(currentTime);
        });

    }

}
