package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.pickup;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskItemFinishDecoratorBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskFinishHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishNeedOpBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/12
 */
public abstract class BatchTaskItemFinishPickUpBaseDecoratorBL {

    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;

    protected static final Logger LOGGER = LoggerFactory.getLogger(BatchTaskItemFinishPickUpBaseDecoratorBL.class);

    abstract boolean support(BatchTaskFinishHelperBO batchTaskFinishHelperBO);

    public void completeBatchTaskItem(BatchTaskItemFinishNeedOpBO bo, BatchTaskFinishHelperBO batchTaskFinishHelperBO) {
        List<BatchTaskItemDTO> needCompleteBatchTaskItemList =
            BatchTaskItemFinishDecoratorBL.getNeedCompleteBatchTaskItemList(batchTaskFinishHelperBO);
        if (CollectionUtils.isEmpty(needCompleteBatchTaskItemList)) {
            return;
        }
        doCompleteBatchTaskItem(bo, batchTaskFinishHelperBO, needCompleteBatchTaskItemList);
    }

    protected void doCompleteBatchTaskItem(BatchTaskItemFinishNeedOpBO bo,
        BatchTaskFinishHelperBO batchTaskFinishHelperBO, List<BatchTaskItemDTO> needCompleteBatchTaskItemList) {
        if (!support(batchTaskFinishHelperBO)) {
            return;
        }
        if (CollectionUtils.isEmpty(needCompleteBatchTaskItemList)) {
            return;
        }
        doCompleteBatchTaskItemPickUp(bo, batchTaskFinishHelperBO, needCompleteBatchTaskItemList);
    }

    protected abstract void doCompleteBatchTaskItemPickUp(BatchTaskItemFinishNeedOpBO bo,
        BatchTaskFinishHelperBO batchTaskFinishHelperBO, List<BatchTaskItemDTO> needCompleteBatchTaskItemList);

}
