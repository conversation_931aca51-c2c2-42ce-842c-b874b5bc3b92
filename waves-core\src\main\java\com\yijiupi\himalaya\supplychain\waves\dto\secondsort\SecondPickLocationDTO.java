package com.yijiupi.himalaya.supplychain.waves.dto.secondsort;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/4
 */
public class SecondPickLocationDTO implements Serializable {
    private static final long serialVersionUID = 3337190863907810922L;
    /**
     * 单号列表
     */
    private List<String> refOrderNoList;
    /**
     * 货位ID
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;

    public List<String> getRefOrderNoList() {
        return refOrderNoList;
    }

    public void setRefOrderNoList(List<String> refOrderNoList) {
        this.refOrderNoList = refOrderNoList;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
