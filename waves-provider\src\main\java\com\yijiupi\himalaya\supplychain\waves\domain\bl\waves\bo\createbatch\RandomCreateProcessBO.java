package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskFinalResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
public class RandomCreateProcessBO {

    private List<OutStockOrderPO> notCheckOrderList;

    private CreateSowTaskFinalResultBO createSowTaskFinalResultBO;

    public RandomCreateProcessBO() {}

    public RandomCreateProcessBO(List<OutStockOrderPO> notCheckOrderList) {
        this.notCheckOrderList = notCheckOrderList;
    }

    public RandomCreateProcessBO(List<OutStockOrderPO> notCheckOrderList,
        CreateSowTaskFinalResultBO createSowTaskFinalResultBO) {
        this.notCheckOrderList = notCheckOrderList;
        this.createSowTaskFinalResultBO = createSowTaskFinalResultBO;
    }

    /**
     * 获取
     *
     * @return notCheckOrderList
     */
    public List<OutStockOrderPO> getNotCheckOrderList() {
        return this.notCheckOrderList;
    }

    /**
     * 设置
     *
     * @param notCheckOrderList
     */
    public void setNotCheckOrderList(List<OutStockOrderPO> notCheckOrderList) {
        this.notCheckOrderList = notCheckOrderList;
    }

    public static RandomCreateProcessBO getDefault(List<OutStockOrderPO> notCheckOrderList) {
        return new RandomCreateProcessBO(notCheckOrderList);
    }

    public static RandomCreateProcessBO getFromCreateSowTaskFinalResultBO(List<OutStockOrderPO> notCheckOrderList,
        CreateSowTaskFinalResultBO createSowTaskFinalResultBO) {

        RandomCreateProcessBO randomCreateProcessBO = new RandomCreateProcessBO(notCheckOrderList);

        randomCreateProcessBO.setCreateSowTaskFinalResultBO(createSowTaskFinalResultBO);
        randomCreateProcessBO.setNotCheckOrderList(notCheckOrderList);
        return randomCreateProcessBO;
    }

    /**
     * 获取
     *
     * @return createSowTaskFinalResultBO
     */
    public CreateSowTaskFinalResultBO getCreateSowTaskFinalResultBO() {
        return this.createSowTaskFinalResultBO;
    }

    /**
     * 设置
     *
     * @param createSowTaskFinalResultBO
     */
    public void setCreateSowTaskFinalResultBO(CreateSowTaskFinalResultBO createSowTaskFinalResultBO) {
        this.createSowTaskFinalResultBO = createSowTaskFinalResultBO;
    }
}
