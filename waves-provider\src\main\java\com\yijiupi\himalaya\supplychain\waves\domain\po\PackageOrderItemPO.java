package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品包装类
 * 
 * <AUTHOR>
 * @date 2018/7/12 17:29
 */
public class PackageOrderItemPO {

    /**
     * id
     */
    private Long id;

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 出库单编号
     */
    private String refOrderNo;

    /**
     * 出库单编号
     */
    private Long refOrderId;

    /**
     * 出库单项Id
     */
    private Long refOrderItemId;

    /**
     * 箱码编号
     */
    private String boxCode;

    /**
     * 箱号完整编码
     */
    private String boxCodeNo;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 产品SKUid
     */
    private Long skuId;

    /**
     * 包装规格
     */
    private String specName;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal specQuantity;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 大单位数量
     */
    private BigDecimal packageCount;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 小单位数量
     */
    private BigDecimal unitCount;

    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String lastUpdateUser;

    /**
     * 更新时间
     */
    private Date lastUpdateTime;

    /**
     * 订单序号
     */
    private Integer orderSequence;

    /**
     * 包装类型: 0.包装箱 1.托盘
     */
    private Byte packageType;

    /**
     * 复核状态: 0.待复核 1.复核中 2.已复核
     */
    private Byte reviewState;

    /**
     * 已复核小单位总数量
     */
    private BigDecimal overUnitTotalCount;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作人id
     */
    private Integer operatorId;

    /**
     * 操作时间
     */
    private Date operatingTime;

    /**
     * 拣货任务项id
     */
    private String batchTaskItemId;

    /**
     * 业务订单id
     */
    private String businessId;

    /**
     * 业务订单项id
     */
    private String businessItemId;

    private String categoryName;
    /**
     * 出库单的业务id
     */
    private String orderBusinessId;
    /**
     * 出库单项的业务id
     */
    private String orderItemBusinessId;

    public String getOrderBusinessId() {
        return orderBusinessId;
    }

    public void setOrderBusinessId(String orderBusinessId) {
        this.orderBusinessId = orderBusinessId;
    }

    public String getOrderItemBusinessId() {
        return orderItemBusinessId;
    }

    public void setOrderItemBusinessId(String orderItemBusinessId) {
        this.orderItemBusinessId = orderItemBusinessId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Integer getOrderSequence() {
        return orderSequence;
    }

    public void setOrderSequence(Integer orderSequence) {
        this.orderSequence = orderSequence;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Long getRefOrderId() {
        return refOrderId;
    }

    public void setRefOrderId(Long refOrderId) {
        this.refOrderId = refOrderId;
    }

    public String getBoxCode() {
        return boxCode;
    }

    public void setBoxCode(String boxCode) {
        this.boxCode = boxCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Long getRefOrderItemId() {
        return refOrderItemId;
    }

    public void setRefOrderItemId(Long refOrderItemId) {
        this.refOrderItemId = refOrderItemId;
    }

    public String getBoxCodeNo() {
        return boxCodeNo;
    }

    public void setBoxCodeNo(String boxCodeNo) {
        this.boxCodeNo = boxCodeNo;
    }

    public Byte getPackageType() {
        return packageType;
    }

    public void setPackageType(Byte packageType) {
        this.packageType = packageType;
    }

    public Byte getReviewState() {
        return reviewState;
    }

    public void setReviewState(Byte reviewState) {
        this.reviewState = reviewState;
    }

    public BigDecimal getOverUnitTotalCount() {
        return overUnitTotalCount;
    }

    public void setOverUnitTotalCount(BigDecimal overUnitTotalCount) {
        this.overUnitTotalCount = overUnitTotalCount;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public Date getOperatingTime() {
        return operatingTime;
    }

    public void setOperatingTime(Date operatingTime) {
        this.operatingTime = operatingTime;
    }

    public String getBatchTaskItemId() {
        return batchTaskItemId;
    }

    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getBusinessItemId() {
        return businessItemId;
    }

    public void setBusinessItemId(String businessItemId) {
        this.businessItemId = businessItemId;
    }
}
