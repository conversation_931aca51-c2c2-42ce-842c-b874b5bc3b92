package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/21
 */
public class AppendSowTaskDTO implements Serializable {
    /**
     * 出库单id列表
     */
    private List<String> outStockOrderIds;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 获取 出库单id列表
     *
     * @return outStockOrderIds 出库单id列表
     */
    public List<String> getOutStockOrderIds() {
        return this.outStockOrderIds;
    }

    /**
     * 设置 出库单id列表
     *
     * @param outStockOrderIds 出库单id列表
     */
    public void setOutStockOrderIds(List<String> outStockOrderIds) {
        this.outStockOrderIds = outStockOrderIds;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }

    /**
     * 获取 播种任务id
     *
     * @return sowTaskId 播种任务id
     */
    public Long getSowTaskId() {
        return this.sowTaskId;
    }

    /**
     * 设置 播种任务id
     *
     * @param sowTaskId 播种任务id
     */
    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }
}
