package com.yijiupi.himalaya.supplychain.waves.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 播种任务状态枚举
 *
 * <AUTHOR> 2018/3/13
 */
public enum SowTaskTypeEnum {
    /**
     * 枚举
     */
    播种墙播种((byte)0), 打包复核((byte)1), 按自提点播种((byte)2), 二次分拣播种((byte)3), 虚仓二次分拣播种((byte)4);

    /**
     * type
     */
    private byte type;

    SowTaskTypeEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     * 
     * @param value
     * @return
     */
    public static String getEnumByValue(Byte value) {
        SowTaskTypeEnum sowTaskStateEnum = null;
        if (value != null) {
            sowTaskStateEnum = cache.get(value);
        }
        return sowTaskStateEnum == null ? null : sowTaskStateEnum.name();
    }

    private static Map<Byte, SowTaskTypeEnum> cache =
        EnumSet.allOf(SowTaskTypeEnum.class).stream().collect(Collectors.toMap(p -> p.type, p -> p));
}
