package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchTaskItemByLocationInfoQueryBO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.UpdateNotPickedBatchTaskItemLocationFromOldToNewDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskBatchUpdateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemBatchUpdateDTO;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
@Service
public class BatchTaskItemBL {

    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    private static final Logger LOG = LoggerFactory.getLogger(BatchTaskItemBL.class);

    @Transactional(rollbackFor = Exception.class)
    public void updateBatchTaskItem(BatchTaskItemBatchUpdateDTO batchTaskItemBatchUpdateDTO) {
        LOG.info("手动刷新拣货任务明细信息:{}", JSON.toJSONString(batchTaskItemBatchUpdateDTO));
        batchTaskItemBatchUpdateDTO.getBatchTaskItemIds().forEach(id -> {
            BatchTaskItemPO batchTaskItemPO = new BatchTaskItemPO();
            batchTaskItemPO.setId(id);
            batchTaskItemPO.setTaskState(batchTaskItemBatchUpdateDTO.getTaskState());
            batchTaskItemPO.setLargePickPattern(batchTaskItemBatchUpdateDTO.getLargePickPattern());
            batchTaskItemPO.setLocationId(batchTaskItemBatchUpdateDTO.getLocationId());
            batchTaskItemPO.setLocationName(batchTaskItemBatchUpdateDTO.getLocationName());
            batchTaskItemMapper.updateBatchTaskItem(batchTaskItemPO);
        });

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBatchTask(BatchTaskBatchUpdateDTO batchTaskBatchUpdateDTO) {
        if (CollectionUtils.isEmpty(batchTaskBatchUpdateDTO.getBatchTaskIds())) {
            return;
        }
        LOG.info("手动刷新拣货任务信息:{}", JSON.toJSONString(batchTaskBatchUpdateDTO));
        batchTaskBatchUpdateDTO.getBatchTaskIds().forEach(id -> {
            BatchTaskPO batchTaskPO = new BatchTaskPO();
            batchTaskPO.setId(id);
            batchTaskPO.setTaskState(batchTaskBatchUpdateDTO.getTaskState());
            batchTaskPO.setToLocationName(batchTaskBatchUpdateDTO.getToLocationName());
            batchTaskPO.setToLocationId(batchTaskBatchUpdateDTO.getToLocationId());
            batchTaskPO.setKindOfPicking(batchTaskBatchUpdateDTO.getKindOfPicking());
            batchTaskPO.setPickPattern(batchTaskBatchUpdateDTO.getPickPattern());
            batchTaskPO.setTaskAppendSequence(batchTaskBatchUpdateDTO.getTaskAppendSequence());
            batchTaskPO.setTaskWarehouseFeatureType(batchTaskBatchUpdateDTO.getTaskWarehouseFeatureType());
            batchTaskMapper.updateByPrimaryKeySelective(batchTaskPO);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void
        updateNotPickedBatchTaskItemLocationFromOldToNew(UpdateNotPickedBatchTaskItemLocationFromOldToNewDTO dto) {

        BatchTaskItemByLocationInfoQueryBO query = new BatchTaskItemByLocationInfoQueryBO();
        query.setLocationIds(Collections.singletonList(dto.getOldLocationId().toString()));
        query.setTaskStateList(Arrays.asList(TaskStateEnum.分拣中.getType(), TaskStateEnum.未分拣.getType()));
        query.setOrgId(dto.getOrgId());

        List<String> batchTaskItemIds = batchTaskItemMapper.findBatchTaskItemIdByLocationInfo(query);

        if (CollectionUtils.isEmpty(batchTaskItemIds)) {
            return;
        }

        List<BatchTaskItemPO> batchTaskItemPOs = batchTaskItemIds.stream().map(itemId -> {
            BatchTaskItemPO updatePO = new BatchTaskItemPO();
            updatePO.setId(itemId);
            updatePO.setLocationId(dto.getNewLocationId());
            updatePO.setLocationName(dto.getNewLocationName());
            return updatePO;
        }).collect(Collectors.toList());

        batchTaskItemPOs.forEach(item -> {
            batchTaskItemMapper.updateByPrimaryKeySelective(item);
        });
    }

}
