package com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 分拣任务明细信息
 * 
 * <AUTHOR>
 */
public class VirtualWarehouseSortingTaskDetailsDTO implements Serializable {
    private static final long serialVersionUID = -9140757665996996312L;
    /**
     * 任务明细id
     */
    private List<Long> taskItemId;
    private Long orderItemId;

    /**
     * 产品名称
     */
    private String productName;
    /**
     * skuid
     */
    private Long skuId;
    /**
     * 规格信息
     */
    private String specInfo;
    /**
     * 条码
     */
    private List<String> packageCode;
    /**
     * 箱码
     */
    private List<String> unitCode;
    /**
     * 待拣大件数
     */
    private BigDecimal packageCount;
    /**
     * 待拣小件数
     */
    private BigDecimal unitCount;
    /**
     * 缺货数量
     */
    private BigDecimal LackUnitTotalCount;
    /**
     * 播种任务编号
     */
    private String sowNo;
    /**
     * 播种任务id
     */
    private Long sowId;
    private String packageName;
    private String unitName;
    private Long toLocationId;
    private String toLocationName;
    private String boundNo;
    private BigDecimal specQuality;
    private BigDecimal saleSpecQuality;
    private Integer taskState;
    /**
     * 出库位序号
     */
    private Integer toLocationSequence;

    public Integer getToLocationSequence() {
        return toLocationSequence;
    }

    public void setToLocationSequence(Integer toLocationSequence) {
        this.toLocationSequence = toLocationSequence;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Integer getTaskState() {
        return taskState;
    }

    public void setTaskState(Integer taskState) {
        this.taskState = taskState;
    }

    public BigDecimal getSpecQuality() {
        return specQuality;
    }

    public void setSpecQuality(BigDecimal specQuality) {
        this.specQuality = specQuality;
    }

    public BigDecimal getSaleSpecQuality() {
        return saleSpecQuality;
    }

    public void setSaleSpecQuality(BigDecimal saleSpecQuality) {
        this.saleSpecQuality = saleSpecQuality;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public String getBoundNo() {
        return boundNo;
    }

    public void setBoundNo(String boundNo) {
        this.boundNo = boundNo;
    }

    public String getSowNo() {
        return sowNo;
    }

    public void setSowNo(String sowNo) {
        this.sowNo = sowNo;
    }

    public Long getSowId() {
        return sowId;
    }

    public void setSowId(Long sowId) {
        this.sowId = sowId;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getLackUnitTotalCount() {
        return LackUnitTotalCount;
    }

    public void setLackUnitTotalCount(BigDecimal lackUnitTotalCount) {
        LackUnitTotalCount = lackUnitTotalCount;
    }

    public List<Long> getTaskItemId() {
        return taskItemId;
    }

    public void setTaskItemId(List<Long> taskItemId) {
        this.taskItemId = taskItemId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecInfo() {
        return specInfo;
    }

    public void setSpecInfo(String specInfo) {
        this.specInfo = specInfo;
    }

    public List<String> getPackageCode() {
        return packageCode;
    }

    public void setPackageCode(List<String> packageCode) {
        this.packageCode = packageCode;
    }

    public List<String> getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(List<String> unitCode) {
        this.unitCode = unitCode;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

}
