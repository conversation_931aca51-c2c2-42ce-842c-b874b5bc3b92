package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/20
 */
public class UserSamePalletInfoQueryDTO implements Serializable {
    /**
     * 出库单号
     */
    private String orderNo;
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 获取 出库单号
     *
     * @return orderNo 出库单号
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置 出库单号
     *
     * @param orderNo 出库单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
