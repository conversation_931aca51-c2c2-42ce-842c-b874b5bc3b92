package com.yijiupi.himalaya.supplychain.waves.dto.performance;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
public class TaskPerformanceCalculateDTO implements Serializable {

    /**
     * 任务id列表
     */
    private List<String> taskIds;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 类型
     */
    private Byte type;
    /**
     * 播种任务
     */
    public static final Byte TYPE_SOW_TASK = 1;
    /**
     * 拣货任务
     */
    public static final Byte TYPE_BATCH_TASK = 2;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 类型
     *
     * @return type 类型
     */
    public Byte getType() {
        return this.type;
    }

    /**
     * 设置 类型
     *
     * @param type 类型
     */
    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * 获取 任务id列表
     *
     * @return taskIds 任务id列表
     */
    public List<String> getTaskIds() {
        return this.taskIds;
    }

    /**
     * 设置 任务id列表
     *
     * @param taskIds 任务id列表
     */
    public void setTaskIds(List<String> taskIds) {
        this.taskIds = taskIds;
    }
}
