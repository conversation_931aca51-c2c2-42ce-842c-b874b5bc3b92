package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.io.Serializable;
import java.util.List;

public class PackageOrderSaveDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 包装项
     */
    private List<PackageOrderItemDTO> packageOrderItemList;

    /**
     * 是否pda装箱
     */
    private Boolean pda;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 容器货位名称
     */
    private String locationName;

    /**
     * 播种状态
     */
    private Byte state;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public List<PackageOrderItemDTO> getPackageOrderItemList() {
        return packageOrderItemList;
    }

    public void setPackageOrderItemList(List<PackageOrderItemDTO> packageOrderItemList) {
        this.packageOrderItemList = packageOrderItemList;
    }

    public Boolean getPda() {
        return pda;
    }

    public void setPda(Boolean pda) {
        this.pda = pda;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }
}
