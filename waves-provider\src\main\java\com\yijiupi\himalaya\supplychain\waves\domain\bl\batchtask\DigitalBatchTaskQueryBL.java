package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.digitaltag.SortGroupBatchTaskSortBL;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.DigitalBatchTaskDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskKindOfPickingConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/10/30
 */
@Service
public class DigitalBatchTaskQueryBL {

    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private SowTaskMapper sowTaskMapper;
    @Autowired
    private SortGroupBatchTaskSortBL sortGroupBatchTaskSortBL;

    @Reference
    private ILocationService iLocationService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    private static final Logger LOGGER = LoggerFactory.getLogger(DigitalBatchTaskQueryBL.class);

    /**
     * 查询电子标签拣货任务信息
     *
     * @param queryDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<DigitalBatchTaskDTO> findDigitalBatchTaskList(DigitalBatchTaskQueryDTO queryDTO) {
        initState(queryDTO);
        List<DigitalBatchTaskDTO> batchTaskDTOS = batchTaskMapper.findDigitalBatchTaskInfo(queryDTO);
        if (CollectionUtils.isEmpty(batchTaskDTOS)) {
            return Collections.emptyList();
        }

        List<String> batchTaskIds =
            batchTaskDTOS.stream().map(DigitalBatchTaskDTO::getBatchTaskId).distinct().collect(Collectors.toList());

        sortGroupBatchTaskSortBL.fillToLocationInfo(batchTaskDTOS);

        List<BatchTaskItemPO> batchTaskItemPOList = batchTaskItemMapper.listBatchTaskItemByTaskId(batchTaskIds, null);

        List<Long> locationIds =
            batchTaskItemPOList.stream().map(BatchTaskItemPO::getLocationId).distinct().collect(Collectors.toList());

        List<LoactionDTO> locationDTOS = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(locationIds)) {
            locationDTOS = iLocationService.findLocationByIds(locationIds);
        }

        DigitalBatchTaskDTOConvertor.convert(batchTaskDTOS, batchTaskItemPOList, locationDTOS);
        batchTaskDTOS = sortGroupBatchTaskSortBL.sortBatchTask(batchTaskDTOS, queryDTO);
        return batchTaskDTOS;
    }

    private void initState(DigitalBatchTaskQueryDTO queryDTO) {
        if (CollectionUtils.isEmpty(queryDTO.getStateList())) {
            queryDTO.setStateList(Arrays.asList(TaskStateEnum.未分拣.getType(), TaskStateEnum.分拣中.getType()));
        }
        if (Objects.isNull(queryDTO.getPickPattern())) {
            queryDTO.setPickPattern(BatchTaskPickPatternEnum.电子标签.getType());
        }

        // 兼容逻辑，等同步完要删掉 TODO
        if (Objects.isNull(queryDTO.getKindOfPicking())) {
            queryDTO.setKindOfPicking(BatchTaskKindOfPickingConstants.DIGITAL);
        }

        if (Objects.isNull(queryDTO.getOrgId())) {
            Warehouse warehouse = iWarehouseQueryService.findWarehouseById(queryDTO.getWarehouseId());

            queryDTO.setOrgId(warehouse.getCityId());
        }

        if (StringUtils.isBlank(queryDTO.getTimeS())) {
            queryDTO.setTimeS(DateUtils.getMinusMonthFirstDay(2));
            queryDTO.setTimeE(DateUtils.getCurrentDateTimeE(0));
        }

    }

}
