package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;

/**
 * 出库单明细(OUTSTOCKORDERITEM)
 *
 * <AUTHOR>
 * @version 1.0.0 2018-03-15
 */
public class OutStockOrderItemPO implements java.io.Serializable {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -485542654884699491L;

    /**
     * 订单项id
     */
    private Long id;

    /**
     * 城市ID，分库用（第三方订单使用一个新的自定义CityId）
     */
    private Integer orgId;

    /**
     * 波次任务编号
     */
    private String batchtaskno;

    /**
     * 波次任务Id
     */
    private String batchtaskId;

    /**
     * 关联出库单表id
     */
    private Long outstockorderId;

    /**
     * 关联订单表id
     */
    private String reforderId;

    /**
     * 商品名称
     */
    private String productname;

    /**
     * skuid（赠品SKUId可能为null）
     */
    private Long skuid;

    /**
     * 品牌
     */
    private String productbrand;

    /**
     * 类目
     */
    private String categoryname;

    /**
     * 包装规格
     */
    private String specname;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal specquantity;

    /**
     * 销售规格名称
     */
    private String salespec;

    /**
     * 销售规格系数
     */
    private BigDecimal salespecquantity;

    /**
     * 大单位名称
     */
    private String packagename;

    /**
     * 大单位数量
     */
    private BigDecimal packagecount;

    /**
     * 小单位名称
     */
    private String unitname;

    /**
     * 小单位数量
     */
    private BigDecimal unitcount;

    /**
     * 小单位总数量
     */
    private BigDecimal unittotalcount;

    /**
     * 销售模式 代营(0),自营(1),合作(2),寄售(3),大商转自营(4),大商转配送(5),入驻(6),总部寄售(7),独家包销(8)
     */
    private Integer salemodel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createtime;
    /**
     * 货位Id
     */
    private Long locationId;

    /**
     * 货位或货区名称
     */
    private String locationName;
    /**
     * 货区Id
     */
    private Long areaId;
    /**
     * 货区名称
     */
    private String areaName;

    /**
     * 货位：0，货区：1
     */
    private Integer locationCategory;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Byte source;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Byte channel;

    private Long firstCategoryId;

    private Long secCategoryId;

    private String firstCategoryName;

    private String secCategoryName;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 所属人(货主)id
     */
    private Long ownerId;

    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    /**
     * 产品规格ID
     */
    private Long productSpecificationId;

    private Byte subCategory;

    /**
     * 播种订单id
     */
    private Long sowOrderId;

    /**
     * 波次编号
     */
    private String batchno;

    /**
     * 波次Id
     */
    private String batchId;

    /**
     * 是否拣货 0:否 1:是
     */
    private Byte pick;

    /**
     * 是否播种 0:否 1:是
     */
    private Byte sow;

    /**
     * 拣货任务详情id
     */
    private String batchTaskItemId;

    /**
     * 是否分配标示
     */
    private Boolean isAllot;

    /**
     * 控货策略id
     */
    private Long controlConfigId;

    /**
     * 批次入库时间
     */
    private Date batchTime;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 播种任务明细id
     */
    private Long sowTaskItemId;

    /**
     * 业务单项id
     */
    private String businessItemId;

    /**
     * 产品总金额
     */
    private BigDecimal totalAmount;

    /**
     * 产品单价
     */
    private BigDecimal unitPrice;

    /**
     * 订单项详细集合
     */
    private List<OutStockOrderItemDetailPO> itemDetails;

    /**
     * 订单项详细id
     */
    private Long itemDetailId;

    /**
     * 商品特征:1:大件,2:小件
     */
    private Byte productFeature;
    private String boundNo;
    /**
     * 最后更新人
     */
    private String lastUpdateUser;
    /**
     * 出库单号
     */
    private String refOrderNo;

    private BigDecimal curItemLackUnitCount;

    /**
     * 用于合并大件数的uuid
     */
    private String uuid;
    /**
     * 原始数量
     */
    private BigDecimal originalUnitTotalCount;

    /**
     * 判断是拆分出的什么类型的订单项：是单项整件的，还是多项整件的，还是小件的
     *
     * @see BatchTaskItemLargePickPatternConstants
     */
    private Byte largePick;

    /**
     * 是否赠品
     */
    private Boolean isGift;
    /**
     * 是否是促销订单项
     */
    private Byte IsAdvent;

    public BigDecimal getCurItemLackUnitCount() {
        return curItemLackUnitCount;
    }

    public void setCurItemLackUnitCount(BigDecimal curItemLackUnitCount) {
        this.curItemLackUnitCount = curItemLackUnitCount;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public String getBoundNo() {
        return boundNo;
    }

    public void setBoundNo(String boundNo) {
        this.boundNo = boundNo;
    }

    public Byte getProductFeature() {
        return productFeature;
    }

    public void setProductFeature(Byte productFeature) {
        this.productFeature = productFeature;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Long getItemDetailId() {
        return itemDetailId;
    }

    public void setItemDetailId(Long itemDetailId) {
        this.itemDetailId = itemDetailId;
    }

    public List<OutStockOrderItemDetailPO> getItemDetails() {
        return itemDetails;
    }

    public void setItemDetails(List<OutStockOrderItemDetailPO> itemDetails) {
        this.itemDetails = itemDetails;
    }

    /**
     * 订单明细
     */
    private List<OutStockOrderItemDetailPO> outStockOrderItemDetailPOS;

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Long getControlConfigId() {
        return controlConfigId;
    }

    public void setControlConfigId(Long controlConfigId) {
        this.controlConfigId = controlConfigId;
    }

    public Boolean getIsAllot() {
        return isAllot;
    }

    public void setIsAllot(Boolean isAllot) {
        this.isAllot = isAllot;
    }

    public String getBatchTaskItemId() {
        return batchTaskItemId;
    }

    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public Byte getPick() {
        return pick;
    }

    public void setPick(Byte pick) {
        this.pick = pick;
    }

    public Byte getSow() {
        return sow;
    }

    public void setSow(Byte sow) {
        this.sow = sow;
    }

    public Byte getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(Byte subCategory) {
        this.subCategory = subCategory;
    }

    public Long getFirstCategoryId() {
        return firstCategoryId;
    }

    public void setFirstCategoryId(Long firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
    }

    public Long getSecCategoryId() {
        return secCategoryId;
    }

    public void setSecCategoryId(Long secCategoryId) {
        this.secCategoryId = secCategoryId;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public void setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
    }

    public String getSecCategoryName() {
        return secCategoryName;
    }

    public void setSecCategoryName(String secCategoryName) {
        this.secCategoryName = secCategoryName;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public Byte getChannel() {
        return channel;
    }

    public void setChannel(Byte channel) {
        this.channel = channel;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    /**
     * 获取订单项id
     *
     * @return 订单项id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置订单项id
     *
     * @param id 订单项id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取城市ID，分库用（第三方订单使用一个新的自定义CityId）
     *
     * @return 城市ID
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置城市ID，分库用（第三方订单使用一个新的自定义CityId）
     *
     * @param orgId 城市ID
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取波次任务编号
     *
     * @return 波次任务编号
     */
    public String getBatchtaskno() {
        return this.batchtaskno;
    }

    /**
     * 设置波次任务编号
     *
     * @param batchtaskno 波次任务编号
     */
    public void setBatchtaskno(String batchtaskno) {
        this.batchtaskno = batchtaskno;
    }

    /**
     * 获取波次任务Id
     *
     * @return 波次任务Id
     */
    public String getBatchtaskId() {
        return this.batchtaskId;
    }

    /**
     * 设置波次任务Id
     *
     * @param batchtaskId 波次任务Id
     */
    public void setBatchtaskId(String batchtaskId) {
        this.batchtaskId = batchtaskId;
    }

    /**
     * 获取关联出库单表id
     *
     * @return 关联出库单表id
     */
    public Long getOutstockorderId() {
        return this.outstockorderId;
    }

    /**
     * 设置关联出库单表id
     *
     * @param outstockorderId 关联出库单表id
     */
    public void setOutstockorderId(Long outstockorderId) {
        this.outstockorderId = outstockorderId;
    }

    /**
     * 获取关联订单表id
     *
     * @return 关联订单表id
     */
    public String getReforderId() {
        return this.reforderId;
    }

    /**
     * 设置关联订单表id
     *
     * @param reforderId 关联订单表id
     */
    public void setReforderId(String reforderId) {
        this.reforderId = reforderId;
    }

    /**
     * 获取商品名称
     *
     * @return 商品名称
     */
    public String getProductname() {
        return this.productname;
    }

    /**
     * 设置商品名称
     *
     * @param productname 商品名称
     */
    public void setProductname(String productname) {
        this.productname = productname;
    }

    /**
     * 获取skuid（赠品SKUId可能为null）
     *
     * @return skuid（赠品SKUId可能为null）
     */
    public Long getSkuid() {
        return this.skuid;
    }

    /**
     * 设置skuid（赠品SKUId可能为null）
     *
     * @param skuid skuid（赠品SKUId可能为null）
     */
    public void setSkuid(Long skuid) {
        this.skuid = skuid;
    }

    /**
     * 获取品牌
     *
     * @return 品牌
     */
    public String getProductbrand() {
        return this.productbrand;
    }

    /**
     * 设置品牌
     *
     * @param productbrand 品牌
     */
    public void setProductbrand(String productbrand) {
        this.productbrand = productbrand;
    }

    /**
     * 获取类目
     *
     * @return 类目
     */
    public String getCategoryname() {
        return this.categoryname;
    }

    /**
     * 设置类目
     *
     * @param categoryname 类目
     */
    public void setCategoryname(String categoryname) {
        this.categoryname = categoryname;
    }

    /**
     * 获取包装规格
     *
     * @return 包装规格
     */
    public String getSpecname() {
        return this.specname;
    }

    /**
     * 设置包装规格
     *
     * @param specname 包装规格
     */
    public void setSpecname(String specname) {
        this.specname = specname;
    }

    /**
     * 获取包装规格大小单位转换系数
     *
     * @return 包装规格大小单位转换系数
     */
    public BigDecimal getSpecquantity() {
        return this.specquantity;
    }

    /**
     * 设置包装规格大小单位转换系数
     *
     * @param specquantity 包装规格大小单位转换系数
     */
    public void setSpecquantity(BigDecimal specquantity) {
        this.specquantity = specquantity;
    }

    /**
     * 获取销售规格名称
     *
     * @return 销售规格名称
     */
    public String getSalespec() {
        return this.salespec;
    }

    /**
     * 设置销售规格名称
     *
     * @param salespec 销售规格名称
     */
    public void setSalespec(String salespec) {
        this.salespec = salespec;
    }

    /**
     * 获取销售规格系数
     *
     * @return 销售规格系数
     */
    public BigDecimal getSalespecquantity() {
        return this.salespecquantity;
    }

    /**
     * 设置销售规格系数
     *
     * @param salespecquantity 销售规格系数
     */
    public void setSalespecquantity(BigDecimal salespecquantity) {
        this.salespecquantity = salespecquantity;
    }

    /**
     * 获取大单位名称
     *
     * @return 大单位名称
     */
    public String getPackagename() {
        return this.packagename;
    }

    /**
     * 设置大单位名称
     *
     * @param packagename 大单位名称
     */
    public void setPackagename(String packagename) {
        this.packagename = packagename;
    }

    /**
     * 获取大单位数量
     *
     * @return 大单位数量
     */
    public BigDecimal getPackagecount() {
        return this.packagecount;
    }

    /**
     * 设置大单位数量
     *
     * @param packagecount 大单位数量
     */
    public void setPackagecount(BigDecimal packagecount) {
        this.packagecount = packagecount;
    }

    /**
     * 获取小单位名称
     *
     * @return 小单位名称
     */
    public String getUnitname() {
        return this.unitname;
    }

    /**
     * 设置小单位名称
     *
     * @param unitname 小单位名称
     */
    public void setUnitname(String unitname) {
        this.unitname = unitname;
    }

    /**
     * 获取小单位数量
     *
     * @return 小单位数量
     */
    public BigDecimal getUnitcount() {
        return this.unitcount;
    }

    /**
     * 设置小单位数量
     *
     * @param unitcount 小单位数量
     */
    public void setUnitcount(BigDecimal unitcount) {
        this.unitcount = unitcount;
    }

    /**
     * 获取小单位总数量
     *
     * @return 小单位总数量
     */
    public BigDecimal getUnittotalcount() {
        return this.unittotalcount;
    }

    /**
     * 设置小单位总数量
     *
     * @param unittotalcount 小单位总数量
     */
    public void setUnittotalcount(BigDecimal unittotalcount) {
        this.unittotalcount = unittotalcount;
    }

    public Integer getSalemodel() {
        return salemodel;
    }

    public void setSalemodel(Integer salemodel) {
        this.salemodel = salemodel;
    }

    /**
     * 获取备注
     *
     * @return 备注
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * 设置备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Date getCreatetime() {
        return this.createtime;
    }

    /**
     * 设置创建时间
     *
     * @param createtime 创建时间
     */
    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    /**
     * 获取 货位：0，货区：1
     */
    public Integer getLocationCategory() {
        return this.locationCategory;
    }

    /**
     * 设置 货位：0，货区：1
     */
    public void setLocationCategory(Integer locationCategory) {
        this.locationCategory = locationCategory;
    }

    public Long getSowOrderId() {
        return sowOrderId;
    }

    public void setSowOrderId(Long sowOrderId) {
        this.sowOrderId = sowOrderId;
    }

    /**
     * 按产品拣货GroupKey
     *
     * @return
     */
    public String getProductIdentityKey() {
        Byte isAdvent = getIsAdvent() == null ? ConditionStateEnum.否.getType() : getIsAdvent();
        return String.format("%s_%s_%s_%s_%s_%s_%s", getSkuid(), getLocationId(),
            getSource() == null ? 0 : getSource().intValue(), getChannel() == null ? 0 : getChannel().intValue(),
            getSalespecquantity(), getControlConfigId(), isAdvent);
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getSowTaskItemId() {
        return sowTaskItemId;
    }

    public void setSowTaskItemId(Long sowTaskItemId) {
        this.sowTaskItemId = sowTaskItemId;
    }

    public String getBusinessItemId() {
        return businessItemId;
    }

    public void setBusinessItemId(String businessItemId) {
        this.businessItemId = businessItemId;
    }

    public List<OutStockOrderItemDetailPO> getOutStockOrderItemDetailPOS() {
        return outStockOrderItemDetailPOS;
    }

    public void setOutStockOrderItemDetailPOS(List<OutStockOrderItemDetailPO> outStockOrderItemDetailPOS) {
        this.outStockOrderItemDetailPOS = outStockOrderItemDetailPOS;
    }

    /**
     * 获取 用于合并大件数的uuid
     *
     * @return uuid 用于合并大件数的uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * 设置 用于合并大件数的uuid
     *
     * @param uuid 用于合并大件数的uuid
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * 获取 判断是拆分出的什么类型的订单项：是单项整件的，还是多项整件的，还是小件的
     *
     * @return largePick 判断是拆分出的什么类型的订单项：是单项整件的，还是多项整件的，还是小件的
     * @see BatchTaskItemLargePickPatternConstants
     */
    public Byte getLargePick() {
        return this.largePick;
    }

    /**
     * 设置 判断是拆分出的什么类型的订单项：是单项整件的，还是多项整件的，还是小件的
     *
     * @param largePick 判断是拆分出的什么类型的订单项：是单项整件的，还是多项整件的，还是小件的
     * @see BatchTaskItemLargePickPatternConstants
     */
    public void setLargePick(Byte largePick) {
        this.largePick = largePick;
    }

    /**
     * 获取 原始数量
     *
     * @return originalUnitTotalCount 原始数量
     */
    public BigDecimal getOriginalUnitTotalCount() {
        return this.originalUnitTotalCount;
    }

    /**
     * 设置 原始数量
     *
     * @param originalUnitTotalCount 原始数量
     */
    public void setOriginalUnitTotalCount(BigDecimal originalUnitTotalCount) {
        this.originalUnitTotalCount = originalUnitTotalCount;
    }

    public Boolean getIsGift() {
        return isGift;
    }

    public void setIsGift(Boolean gift) {
        isGift = gift;
    }

    /**
     * 获取 是否是促销订单项
     *
     * @return IsAdvent 是否是促销订单项
     */
    public Byte getIsAdvent() {
        return Objects.isNull(this.IsAdvent) ? ConditionStateEnum.否.getType() : this.IsAdvent;
    }

    /**
     * 设置 是否是促销订单项
     *
     * @param IsAdvent 是否是促销订单项
     */
    public void setIsAdvent(Byte IsAdvent) {
        this.IsAdvent = IsAdvent;
    }

}