package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.ScanOrderForReviewDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.ScanOrderForReviewLocationInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.ScanOrderForReviewOrderInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.ScanOrderForReviewWineOrderInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.OutStockOrderStateEnum;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/18
 */
public class ScanOrderForReviewDTOConvertor {

    public static ScanOrderForReviewDTO convertWithoutWine(OutStockOrderPO outStockOrderPO,
        List<BatchTaskPO> batchTaskPOList, List<PackageOrderItemDTO> packageOrderItemDTOList,
        List<OrderLocationPalletDTO> restPalletDTOList) {

        ScanOrderForReviewOrderInfoDTO orderDTO = new ScanOrderForReviewOrderInfoDTO();
        orderDTO.setOrderId(outStockOrderPO.getId());
        orderDTO.setOrderNo(outStockOrderPO.getReforderno());
        orderDTO.setSorterName(getSorterNameList(batchTaskPOList));
        orderDTO.setCompleteTime(getCompleteTime(batchTaskPOList));
        orderDTO.setAreaRouteName(getAreaRouteName(outStockOrderPO));
        orderDTO.setPackageCount(getPackageCount(packageOrderItemDTOList));

        orderDTO.setLocationList(getOrderOutLocationList(batchTaskPOList));
        orderDTO.setWinePackageCount("0");
        ScanOrderForReviewDTO scanOrderForReviewDTO = new ScanOrderForReviewDTO();
        scanOrderForReviewDTO.setOrderDTO(orderDTO);

        scanOrderForReviewDTO.setOrderLocationList(getOrderOutLocationList(batchTaskPOList));
        return scanOrderForReviewDTO;
    }

    private static List<ScanOrderForReviewLocationInfoDTO> getOrderOutLocationList(List<BatchTaskPO> batchTaskPOList) {
        if (CollectionUtils.isEmpty(batchTaskPOList)) {
            return Collections.emptyList();
        }
        List<ScanOrderForReviewLocationInfoDTO> locationInfoDTOS =
            batchTaskPOList.stream().filter(m -> Objects.nonNull(m.getToLocationId())).map(batchTaskPO -> {
                ScanOrderForReviewLocationInfoDTO scanOrderForReviewLocationInfoDTO =
                    new ScanOrderForReviewLocationInfoDTO();
                scanOrderForReviewLocationInfoDTO.setLocationId(batchTaskPO.getToLocationId());
                scanOrderForReviewLocationInfoDTO.setLocationName(batchTaskPO.getToLocationName());
                scanOrderForReviewLocationInfoDTO.setPalletNo(batchTaskPO.getToPalletNo());

                return scanOrderForReviewLocationInfoDTO;
            }).collect(Collectors.toList());

        return locationInfoDTOS;
    }

    /**
     * 处理酒饮订单有绑定时
     * 
     * @param outStockOrderPO 休食出库单
     * @param restBatchTaskPOList 休食对应的拣货任务
     * @param restPackageOrderItemDTOList 休食打包信息
     * @param winePalletDTOList 酒饮托盘信息
     * @param wineOutStockOrderItemList 酒饮出库单项信息
     * @Param wineOutStockOrderPOList 对应的酒饮订单
     * @return
     */
    public static ScanOrderForReviewDTO convertWithWineBind(OutStockOrderPO outStockOrderPO,
        List<BatchTaskPO> restBatchTaskPOList, List<PackageOrderItemDTO> restPackageOrderItemDTOList,
        List<OrderLocationPalletDTO> winePalletDTOList, List<OutStockOrderItemPO> wineOutStockOrderItemList,
        List<OutStockOrderPO> wineOutStockOrderPOList, List<OrderLocationPalletDTO> restPalletDTOList) {

        List<ScanOrderForReviewLocationInfoDTO> wineLocationPalletDTOList =
            getOrderLocationPalletDTO(winePalletDTOList);

        ScanOrderForReviewOrderInfoDTO orderDTO = new ScanOrderForReviewOrderInfoDTO();
        orderDTO.setOrderId(outStockOrderPO.getId());
        orderDTO.setOrderNo(outStockOrderPO.getReforderno());
        orderDTO.setSorterName(getSorterNameList(restBatchTaskPOList));
        orderDTO.setCompleteTime(getCompleteTime(restBatchTaskPOList));
        orderDTO.setAreaRouteName(getAreaRouteName(outStockOrderPO));
        orderDTO.setLocationList(wineLocationPalletDTOList);
        orderDTO.setPackageCount(getPackageCount(restPackageOrderItemDTOList));
        orderDTO.setWinePackageCount(getWinePackageCount(wineOutStockOrderItemList));

        ScanOrderForReviewWineOrderInfoDTO wineOrderInfoDTO = new ScanOrderForReviewWineOrderInfoDTO();

        ScanOrderForReviewDTO scanOrderForReviewDTO = new ScanOrderForReviewDTO();
        scanOrderForReviewDTO.setOrderDTO(orderDTO);
        scanOrderForReviewDTO.setWineOrderDTOList(convertWineDTOList(winePalletDTOList, wineOutStockOrderPOList,
            Collections.singletonList(OutStockOrderStateEnum.已拣货.getType())));
        scanOrderForReviewDTO.setNotPickWineOrderDTOList(convertWineDTOList(winePalletDTOList, wineOutStockOrderPOList,
            Arrays.asList(OutStockOrderStateEnum.待调度.getType(), OutStockOrderStateEnum.待拣货.getType(),
                OutStockOrderStateEnum.拣货中.getType())));

        List<ScanOrderForReviewWineOrderInfoDTO> totalOrderList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(scanOrderForReviewDTO.getWineOrderDTOList())) {
            totalOrderList.addAll(scanOrderForReviewDTO.getWineOrderDTOList());
        }
        if (!CollectionUtils.isEmpty(scanOrderForReviewDTO.getNotPickWineOrderDTOList())) {
            totalOrderList.addAll(scanOrderForReviewDTO.getNotPickWineOrderDTOList());
        }

        scanOrderForReviewDTO.setWineOrderDTOList(totalOrderList);

        scanOrderForReviewDTO.setOrderLocationList(getOrderOutLocationList(restBatchTaskPOList));

        return scanOrderForReviewDTO;
    }

    /**
     * 处理酒饮订单未绑定时
     * 
     * @param outStockOrderPO
     * @param restBatchTaskPOList
     * @param restPackageOrderItemDTOList
     * @param wineOutStockOrderItemList
     * @param wineOutStockOrderPOList
     * @return
     */
    public static ScanOrderForReviewDTO convertWithWineNotBind(OutStockOrderPO outStockOrderPO,
        List<BatchTaskPO> restBatchTaskPOList, List<PackageOrderItemDTO> restPackageOrderItemDTOList,
        List<OutStockOrderItemPO> wineOutStockOrderItemList, List<OutStockOrderPO> wineOutStockOrderPOList,
        List<OrderLocationPalletDTO> restPalletDTOList) {
        ScanOrderForReviewOrderInfoDTO orderDTO = new ScanOrderForReviewOrderInfoDTO();
        orderDTO.setOrderId(outStockOrderPO.getId());
        orderDTO.setOrderNo(outStockOrderPO.getReforderno());
        orderDTO.setSorterName(getSorterNameList(restBatchTaskPOList));
        orderDTO.setCompleteTime(getCompleteTime(restBatchTaskPOList));
        orderDTO.setAreaRouteName(getAreaRouteName(outStockOrderPO));
        if (CollectionUtils.isNotEmpty(restPalletDTOList)) {
            orderDTO.setLocationList(restPalletDTOList.stream().map(m -> {
                ScanOrderForReviewLocationInfoDTO infoDTO = new ScanOrderForReviewLocationInfoDTO();
                infoDTO.setLocationName(m.getLocationName());
                infoDTO.setLocationId(m.getLocationId());
                infoDTO.setPalletNo(m.getPalletNo());
                return infoDTO;
            }).collect(Collectors.toList()));
        }
        orderDTO.setPackageCount(getPackageCount(restPackageOrderItemDTOList));
        orderDTO.setWinePackageCount(getWinePackageCount(wineOutStockOrderItemList));

        if (CollectionUtils.isEmpty(orderDTO.getLocationList())) {
            orderDTO.setLocationList(getOrderOutLocationList(restBatchTaskPOList));
        }

        ScanOrderForReviewWineOrderInfoDTO wineOrderInfoDTO = new ScanOrderForReviewWineOrderInfoDTO();

        ScanOrderForReviewDTO scanOrderForReviewDTO = new ScanOrderForReviewDTO();
        scanOrderForReviewDTO.setOrderDTO(orderDTO);

        scanOrderForReviewDTO.setWineOrderDTOList(convertWineDTOList(Collections.emptyList(), wineOutStockOrderPOList,
            Collections.singletonList(OutStockOrderStateEnum.已拣货.getType())));
        scanOrderForReviewDTO.setNotPickWineOrderDTOList(convertWineDTOList(Collections.emptyList(),
            wineOutStockOrderPOList, Arrays.asList(OutStockOrderStateEnum.待调度.getType(),
                OutStockOrderStateEnum.待拣货.getType(), OutStockOrderStateEnum.拣货中.getType())));

        List<ScanOrderForReviewWineOrderInfoDTO> totalOrderList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(scanOrderForReviewDTO.getWineOrderDTOList())) {
            totalOrderList.addAll(scanOrderForReviewDTO.getWineOrderDTOList());
        }
        if (!CollectionUtils.isEmpty(scanOrderForReviewDTO.getNotPickWineOrderDTOList())) {
            totalOrderList.addAll(scanOrderForReviewDTO.getNotPickWineOrderDTOList());
        }

        scanOrderForReviewDTO.setWineOrderDTOList(totalOrderList);

        scanOrderForReviewDTO.setOrderLocationList(getOrderOutLocationList(restBatchTaskPOList));
        return scanOrderForReviewDTO;
    }

    private static List<ScanOrderForReviewWineOrderInfoDTO> convertWineDTOList(
        List<OrderLocationPalletDTO> winePalletDTOList, List<OutStockOrderPO> wineOutStockOrderPOList,
        List<Byte> stateList) {
        if (CollectionUtils.isEmpty(winePalletDTOList)) {
            return wineOutStockOrderPOList.stream().filter(m -> stateList.contains(m.getState().byteValue()))
                .map(order -> {
                    ScanOrderForReviewWineOrderInfoDTO wineOrderInfoDTO = new ScanOrderForReviewWineOrderInfoDTO();
                    wineOrderInfoDTO.setRefOrderNo(order.getReforderno());
                    return wineOrderInfoDTO;
                }).collect(Collectors.toList());
        }

        Map<String, List<OrderLocationPalletDTO>> groupPalletDTOMap =
            winePalletDTOList.stream().collect(Collectors.groupingBy(OrderLocationPalletDTO::getOrderNo));

        return wineOutStockOrderPOList.stream().filter(m -> stateList.contains(m.getState().byteValue())).map(order -> {
            ScanOrderForReviewWineOrderInfoDTO wineOrderInfoDTO = new ScanOrderForReviewWineOrderInfoDTO();
            wineOrderInfoDTO.setRefOrderNo(order.getReforderno());
            List<OrderLocationPalletDTO> palletDTOList = groupPalletDTOMap.get(order.getReforderno());
            if (CollectionUtils.isEmpty(palletDTOList)) {
                return wineOrderInfoDTO;
            }
            List<ScanOrderForReviewLocationInfoDTO> locationList = palletDTOList.stream().map(palletDTO -> {
                ScanOrderForReviewLocationInfoDTO locationInfoDTO = new ScanOrderForReviewLocationInfoDTO();
                locationInfoDTO.setPalletNo(palletDTO.getPalletNo());
                locationInfoDTO.setLocationId(palletDTO.getLocationId());
                locationInfoDTO.setLocationName(palletDTO.getLocationName());
                return locationInfoDTO;
            }).collect(Collectors.toList());

            wineOrderInfoDTO.setLocationList(locationList);
            return wineOrderInfoDTO;
        }).collect(Collectors.toList());
    }

    private static List<ScanOrderForReviewLocationInfoDTO>
        getOrderLocationPalletDTO(List<OrderLocationPalletDTO> orderLocationPalletDTOList) {
        return orderLocationPalletDTOList.stream().map(dto -> {
            ScanOrderForReviewLocationInfoDTO locationInfoDTO = new ScanOrderForReviewLocationInfoDTO();
            locationInfoDTO.setPalletNo(dto.getPalletNo());
            locationInfoDTO.setLocationName(dto.getLocationName());
            locationInfoDTO.setLocationId(dto.getLocationId());
            return locationInfoDTO;
        }).collect(Collectors.toList());
    }

    private static String getWinePackageCount(List<OutStockOrderItemPO> wineOutStockOrderItemList) {
        Map<Long, List<OutStockOrderItemPO>> orderItemSkuMap =
            wineOutStockOrderItemList.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid));

        BigDecimal[] packageCount = {BigDecimal.ZERO, BigDecimal.ZERO};
        for (Map.Entry<Long, List<OutStockOrderItemPO>> entry : orderItemSkuMap.entrySet()) {
            BigDecimal totalCount = entry.getValue().stream().map(OutStockOrderItemPO::getUnittotalcount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal[] count = totalCount.divideAndRemainder(entry.getValue().get(0).getSpecquantity());
            packageCount[0] = packageCount[0].add(count[0]);
            packageCount[1] = packageCount[1].add(count[1]);
        }

        return packageCount[0].stripTrailingZeros().toPlainString() + "大件"
            + packageCount[1].stripTrailingZeros().toPlainString() + "小件";
    }

    private static String getAreaRouteName(OutStockOrderPO outStockOrderPO) {
        if (StringUtils.isBlank(outStockOrderPO.getRouteName())) {
            return outStockOrderPO.getAreaName();
        }
        if (StringUtils.isBlank(outStockOrderPO.getAreaName())) {
            return outStockOrderPO.getRouteName();
        }

        return outStockOrderPO.getAreaName().concat("/").concat(outStockOrderPO.getRouteName());
    }

    private static String getSorterNameList(List<BatchTaskPO> batchTaskPOList) {
        String sorterNameList = batchTaskPOList.stream().map(BatchTaskPO::getSorter).filter(Objects::nonNull).distinct()
            .collect(Collectors.joining(","));

        return sorterNameList;
    }

    private static String getCompleteTime(List<BatchTaskPO> batchTaskPOList) {
        Optional<Date> completeTimeOptional = batchTaskPOList.stream().map(BatchTaskPO::getCompleteTime)
            .filter(Objects::nonNull).max(Comparator.naturalOrder());
        if (completeTimeOptional.isPresent()) {
            return DateUtils.getDatetimeFormat(completeTimeOptional.get());
        }

        return "";
    }

    private static int getPackageCount(List<PackageOrderItemDTO> packageOrderItemDTOList) {
        if (CollectionUtils.isEmpty(packageOrderItemDTOList)) {
            return 0;
        }

        Map<String, List<PackageOrderItemDTO>> packageMap =
            packageOrderItemDTOList.stream().collect(Collectors.groupingBy(PackageOrderItemDTO::getBoxCodeNo));
        int packageCount = packageMap.keySet().size();

        return packageCount;
    }

}
