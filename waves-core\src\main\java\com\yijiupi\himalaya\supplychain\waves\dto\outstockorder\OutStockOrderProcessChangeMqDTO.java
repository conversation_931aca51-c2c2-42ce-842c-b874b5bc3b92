package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * 处理订单取消或修改消息对象
 *
 * <AUTHOR>
 * @date 2020-06-22 16:09
 */
public class OutStockOrderProcessChangeMqDTO implements Serializable {

    private static final long serialVersionUID = 2621514158683824084L;

    /**
     * 处理订单取消或修改实体
     */
    private List<OutStockOrderProcessChangeDTO> processChangeDTOS;

    /**
     * 订单变更类型 0：订单取消 1：订单修改
     */
    private Byte changeType;

    /**
     * 操作人
     */
    private String operatorUser;

    public List<OutStockOrderProcessChangeDTO> getProcessChangeDTOS() {
        return processChangeDTOS;
    }

    public void setProcessChangeDTOS(List<OutStockOrderProcessChangeDTO> processChangeDTOS) {
        this.processChangeDTOS = processChangeDTOS;
    }

    public Byte getChangeType() {
        return changeType;
    }

    public void setChangeType(Byte changeType) {
        this.changeType = changeType;
    }

    public String getOperatorUser() {
        return operatorUser;
    }

    public void setOperatorUser(String operatorUser) {
        this.operatorUser = operatorUser;
    }
}
