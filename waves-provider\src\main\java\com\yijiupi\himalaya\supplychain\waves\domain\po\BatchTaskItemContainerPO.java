package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 拣货容器位关系
 */
public class BatchTaskItemContainerPO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 城市ID
     */
    private Integer orgId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 拣货任务详情ID
     */
    private Long batchtaskitemId;

    /**
     * 容器位ID
     */
    private Long locationId;

    /**
     * 容器位名称
     */
    private String locationName;

    /**
     * 已拣货小单位总数量
     */
    private BigDecimal pickUnitTotalCount;

    /**
     * 已移库小单位总数量
     */
    private BigDecimal moveUnitTotalCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Integer createUserId;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 最后修改人
     */
    private Integer lastUpdateUserId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getBatchtaskitemId() {
        return batchtaskitemId;
    }

    public void setBatchtaskitemId(Long batchtaskitemId) {
        this.batchtaskitemId = batchtaskitemId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public BigDecimal getPickUnitTotalCount() {
        return pickUnitTotalCount;
    }

    public void setPickUnitTotalCount(BigDecimal pickUnitTotalCount) {
        this.pickUnitTotalCount = pickUnitTotalCount;
    }

    public BigDecimal getMoveUnitTotalCount() {
        return moveUnitTotalCount;
    }

    public void setMoveUnitTotalCount(BigDecimal moveUnitTotalCount) {
        this.moveUnitTotalCount = moveUnitTotalCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }
}