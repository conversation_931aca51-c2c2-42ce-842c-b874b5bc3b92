package com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch;

import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchOrderTypeEnum;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/5
 */
public class BatchTaskSortBO {
    /**
     * 拣货任务id
     */
    private String batchTaskId;
    /**
     * 排序
     */
    private Integer sequence;
    /**
     * 状态
     */
    private Byte state;
    /**
     * 分组id
     */
    private Long sortGroupId;
    /**
     * 播种任务id
     */
    private Long sowTaskId;
    /**
     * 播种任务名称
     */
    private String sowTaskNo;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 集货位id
     */
    private Long locationId;
    /**
     * 集货位名称
     */
    private String locationName;
    /**
     * 播种台序号
     */
    private Integer sowTableSequence;
    /**
     * 波次订单类型
     */
    private Integer batchOrderType;

    /**
     * 获取 拣货任务id
     *
     * @return batchTaskId 拣货任务id
     */
    public String getBatchTaskId() {
        return this.batchTaskId;
    }

    /**
     * 设置 拣货任务id
     *
     * @param batchTaskId 拣货任务id
     */
    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    /**
     * 获取 排序
     *
     * @return sequence 排序
     */
    public Integer getSequence() {
        return this.sequence;
    }

    /**
     * 设置 排序
     *
     * @param sequence 排序
     */
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    /**
     * 获取 状态
     *
     * @return state 状态
     */
    public Byte getState() {
        return this.state;
    }

    /**
     * 设置 状态
     *
     * @param state 状态
     */
    public void setState(Byte state) {
        this.state = state;
    }

    /**
     * 获取 播种任务id
     *
     * @return sowTaskId 播种任务id
     */
    public Long getSowTaskId() {
        return this.sowTaskId;
    }

    /**
     * 设置 播种任务id
     *
     * @param sowTaskId 播种任务id
     */
    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    /**
     * 获取 播种任务名称
     *
     * @return sowTaskNo 播种任务名称
     */
    public String getSowTaskNo() {
        return this.sowTaskNo;
    }

    /**
     * 设置 播种任务名称
     *
     * @param sowTaskNo 播种任务名称
     */
    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    /**
     * 获取 分组id
     *
     * @return sortGroupId 分组id
     */
    public Long getSortGroupId() {
        return this.sortGroupId;
    }

    /**
     * 设置 分组id
     *
     * @param sortGroupId 分组id
     */
    public void setSortGroupId(Long sortGroupId) {
        this.sortGroupId = sortGroupId;
    }

    public static List<BatchTaskSortBO> convert(List<DigitalBatchTaskDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }

        return dtoList.stream().map(dto -> {
            BatchTaskSortBO sortBO = new BatchTaskSortBO();
            sortBO.setBatchTaskId(dto.getBatchTaskId());
            sortBO.setState(dto.getTaskState());
            sortBO.setSortGroupId(dto.getSortGroupId());
            sortBO.setSowTaskId(dto.getSowTaskId());
            sortBO.setSowTaskNo(dto.getSowTaskNo());
            sortBO.setCreateTime(dto.getCreateTime());
            sortBO.setLocationId(dto.getToLocationId());
            sortBO.setLocationName(dto.getToLocationName());
            Integer batchOrderType =
                Optional.ofNullable(dto.getBatchOrderType()).orElse(BatchOrderTypeEnum.普通订单.getType().intValue());
            sortBO.setBatchOrderType(batchOrderType);

            return sortBO;
        }).collect(Collectors.toList());
    }

    public static void revert(List<BatchTaskSortBO> boList, List<DigitalBatchTaskDTO> dtoList) {
        if (CollectionUtils.isEmpty(boList)) {
            return;
        }
        Map<String, BatchTaskSortBO> boMap =
            boList.stream().collect(Collectors.toMap(BatchTaskSortBO::getBatchTaskId, v -> v));

        dtoList.forEach(dto -> {
            BatchTaskSortBO batchTaskSortBO = boMap.get(dto.getBatchTaskId());
            if (Objects.nonNull(batchTaskSortBO)) {
                dto.setSequence(batchTaskSortBO.getSequence());
            }
        });

    }

    /**
     * 获取 创建时间
     *
     * @return createTime 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置 创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 集货位id
     *
     * @return locationId 集货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 集货位id
     *
     * @param locationId 集货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 集货位名称
     *
     * @return locationName 集货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 集货位名称
     *
     * @param locationName 集货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 播种台序号
     *
     * @return sowTableSequence 播种台序号
     */
    public Integer getSowTableSequence() {
        return this.sowTableSequence;
    }

    /**
     * 设置 播种台序号
     *
     * @param sowTableSequence 播种台序号
     */
    public void setSowTableSequence(Integer sowTableSequence) {
        this.sowTableSequence = sowTableSequence;
    }

    /**
     * 获取 波次订单类型
     *
     * @return batchOrderType 波次订单类型
     */
    public Integer getBatchOrderType() {
        return this.batchOrderType;
    }

    /**
     * 设置 波次订单类型
     *
     * @param batchOrderType 波次订单类型
     */
    public void setBatchOrderType(Integer batchOrderType) {
        this.batchOrderType = batchOrderType;
    }
}
