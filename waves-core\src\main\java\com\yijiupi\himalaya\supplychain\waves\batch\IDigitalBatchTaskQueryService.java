package com.yijiupi.himalaya.supplychain.waves.batch;

import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskQueryDTO;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/10/30
 */
public interface IDigitalBatchTaskQueryService {

    /**
     * 查询电子标签拣货任务信息
     *
     * @param queryDTO
     * @return
     */
    List<DigitalBatchTaskDTO> findDigitalBatchTaskList(DigitalBatchTaskQueryDTO queryDTO);

}
