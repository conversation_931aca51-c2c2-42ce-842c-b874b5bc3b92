package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * 拣货任务项禁止销售配置查询
 *
 * <AUTHOR>
 * @Date 2025/4/18
 */
public class BatchTaskItemPeriodConfigQueryDTO implements Serializable {
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 产品集合
     */
    private List<String> batchTaskItemIdList;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<String> getBatchTaskItemIdList() {
        return batchTaskItemIdList;
    }

    public void setBatchTaskItemIdList(List<String> batchTaskItemIdList) {
        this.batchTaskItemIdList = batchTaskItemIdList;
    }
}
