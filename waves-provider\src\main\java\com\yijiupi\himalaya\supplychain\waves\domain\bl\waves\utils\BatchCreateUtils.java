package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchAttrSettingWayConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchCreateTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.ExpressFlagEnum;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;
import com.yijiupi.supplychain.serviceutils.constant.DeliveryOrderConstant;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/24
 */
public final class BatchCreateUtils {

    public static void setBatchName(WavesStrategyBO wavesStrategyDTO, ProcessBatchDTO processBatchDTO, BatchPO batchPO,
        List<OutStockOrderPO> orders) {
        String title = processBatchDTO.getBatchName();
        // 内配单的波次名称显示为“收货城市+收货仓库”
        if (processBatchDTO.getAllocationFlag()) {
            batchPO.setBatchName(title);
            // 按产品和订单生成波次时，波次名称：年+月+日+时+份+3位流水
        } else if (Objects.equals(processBatchDTO.getBatchCreateType(), BatchCreateTypeEnum.按产品和订单组合.getType())) {
            batchPO.setBatchName(UuidUtil.generatorBatchName(wavesStrategyDTO.getWarehouseId()));
            // ERP调拨出库单生波次时，波次名称：芜湖->武汉、北京->武汉
        } else if (Objects.equals(processBatchDTO.getExpressFlag(), ExpressFlagEnum.ERP调拨出库单.getType())) {
            List<String> areaNameList = orders.stream().map(OutStockOrderPO::getAreaName)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(areaNameList)) {
                StringBuilder sb = new StringBuilder();
                for (String s : areaNameList) {
                    if (sb.length() + s.length() <= 30) {
                        sb.append("、").append(s);
                    }
                }
                if (sb.length() > 0) {
                    batchPO.setBatchName(sb.substring(1));
                }
            }
        } else {
            String strategyName = "";
            String pickTypeName =
                WavesStrategyConstants.PICKINGTYPE_ORDER == wavesStrategyDTO.getPickingType().intValue() ? "订单" : "产品";
            if (wavesStrategyDTO.getOrderSelection() == null
                || WavesStrategyConstants.ORDERSELECTION_NO == wavesStrategyDTO.getOrderSelection().intValue()) {
                strategyName = StringUtils.isEmpty(wavesStrategyDTO.getStrategyName()) ? title
                    : wavesStrategyDTO.getStrategyName();
            } else {
                strategyName = getStrategyNameByOrderSelection(wavesStrategyDTO, batchPO, processBatchDTO);
            }
            if (StringUtils.isEmpty(strategyName)) {
                strategyName = "手动生成波次";
            }
            String batchName = String.format("%s-%s", strategyName, pickTypeName);
            if (Objects.equals(DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue(),
                orders.get(0).getDeliveryMarkState())) {
                batchName += "-延迟配送";
            }
            batchPO.setBatchName(batchName);
        }
    }

    private static String getStrategyNameByOrderSelection(WavesStrategyBO wavesStrategyDTO, BatchPO batchPO,
        ProcessBatchDTO processBatchDTO) {
        if (Objects.isNull(wavesStrategyDTO.getOrderSelection())) {
            return processBatchDTO.getBatchName();
        }

        if (WavesStrategyConstants.ORDERSELECTION_SHIPPING == wavesStrategyDTO.getOrderSelection().intValue()) {
            return processBatchDTO.getBatchName();
        }
        if (WavesStrategyConstants.ORDERSELECTION_WAY == wavesStrategyDTO.getOrderSelection().intValue()) {
            return batchPO.getRouteName();
        }
        if (WavesStrategyConstants.ORDERSELECTION_AREA == wavesStrategyDTO.getOrderSelection().intValue()) {
            return batchPO.getAreaName();
        }

        if (WavesStrategyConstants.ORDERSELECTION_USER == wavesStrategyDTO.getOrderSelection().intValue()
            || WavesStrategyConstants.ORDERSELECTION_ORDER == wavesStrategyDTO.getOrderSelection().intValue()) {
            if (BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_AREA
                .equals(wavesStrategyDTO.getBatchAttrSettingWay())) {
                return batchPO.getAreaName();
            } else if (BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_ROUTE
                .equals(wavesStrategyDTO.getBatchAttrSettingWay())) {
                return batchPO.getRouteName();
            }
        }

        return "";
    }

    public static boolean isBatchHasRoute(BatchPO batchPO) {
        if (BatchAttrSettingWayConstants.isRouteBatch(batchPO.getOrderSelection(), batchPO.getBatchAttrSettingWay())) {
            if (StringUtils.isNotEmpty(batchPO.getRouteId())) {
                return Boolean.TRUE;
            }
        }

        return Boolean.FALSE;
    }

    public static boolean isBatchHasArea(BatchPO batchPO) {
        if (BatchAttrSettingWayConstants.isAreaBatch(batchPO.getOrderSelection(), batchPO.getBatchAttrSettingWay())) {
            if (StringUtils.isNotEmpty(batchPO.getAreaId())) {
                return Boolean.TRUE;
            }
        }

        return Boolean.FALSE;
    }

    public static boolean isRouteOrArea(WavesStrategyBO wavesStrategyDTO) {
        if (Objects.isNull(wavesStrategyDTO.getOrderSelection())) {
            return Boolean.FALSE;
        }
        if (WavesStrategyConstants.ORDERSELECTION_WAY == wavesStrategyDTO.getOrderSelection().intValue()) {
            return Boolean.TRUE;
        }
        if (WavesStrategyConstants.ORDERSELECTION_AREA == wavesStrategyDTO.getOrderSelection().intValue()) {
            return Boolean.TRUE;
        }

        if (WavesStrategyConstants.ORDERSELECTION_USER == wavesStrategyDTO.getOrderSelection().intValue()
            || WavesStrategyConstants.ORDERSELECTION_ORDER == wavesStrategyDTO.getOrderSelection().intValue()) {
            if (BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_AREA
                .equals(wavesStrategyDTO.getBatchAttrSettingWay())) {
                return Boolean.TRUE;
            } else if (BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_ROUTE
                .equals(wavesStrategyDTO.getBatchAttrSettingWay())) {
                return Boolean.TRUE;
            }
        }

        return Boolean.FALSE;
    }

}
