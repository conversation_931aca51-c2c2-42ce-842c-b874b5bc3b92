package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderitemdetail.OutStockOrderItemDetailModBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.RecoverOrderItemDetailBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.trace.OrderTraceDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.OrderTraceBusinessTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.OrderTraceDescriptionEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.OrderTraceEventTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;

/**
 * <AUTHOR>
 * @date 2018/5/23 11:51
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class OrderTraceBLTest {

    @Autowired
    private OrderTraceBL orderTraceBL;

    @Test
    public void test() {
        List<BatchPO> batchPOList = new ArrayList<>();
        BatchPO po = new BatchPO();
        po.setId("123456");
        po.setBatchNo("ZB123456");
        batchPOList.add(po);

        BatchPO po2 = new BatchPO();
        po2.setId("1234567");
        po2.setBatchNo("ZB1234567");
        batchPOList.add(po2);
        orderTraceBL.deleteBatchTrace(batchPOList, null);
    }

    @Test
    public void insertTest() {
        OrderTraceDTO dto = new OrderTraceDTO();
        dto.setId(new Long(UuidUtil.generatorId()));
        dto.setBusinesstype(OrderTraceBusinessTypeEnum.波次.getType());
        dto.setBusinessId(new Long(123456));
        dto.setBusinessno("WXP1345");
        dto.setEventtype(OrderTraceEventTypeEnum.新增.getType());
        dto.setDescription(OrderTraceDescriptionEnum.手动组建波次成功.name());
        dto.setCreateuser("1");
        dto.setCreatetime(new Date());
        dto.setOrgId(997);
        orderTraceBL.insert(dto);
    }

    @Test
    public void insertBatchTest() {
        List<OrderTraceDTO> list = new ArrayList<>();
        OrderTraceDTO dto = new OrderTraceDTO();
        dto.setId(new Long(UuidUtil.generatorId()));
        dto.setBusinesstype(OrderTraceBusinessTypeEnum.波次.getType());
        dto.setBusinessId(new Long(123456));
        dto.setBusinessno("WXP1345");
        dto.setEventtype(OrderTraceEventTypeEnum.新增.getType());
        dto.setDescription(OrderTraceDescriptionEnum.手动组建波次成功.name());
        dto.setCreateuser("1");
        dto.setCreatetime(new Date());
        dto.setOrgId(9971);
        list.add(dto);

        OrderTraceDTO dto1 = new OrderTraceDTO();
        dto1.setId(new Long(UuidUtil.generatorId()));
        dto1.setBusinesstype(OrderTraceBusinessTypeEnum.拣货任务.getType());
        dto1.setBusinessId(new Long(123456));
        dto1.setBusinessno("WXP1345");
        dto1.setEventtype(OrderTraceEventTypeEnum.新增.getType());
        dto1.setDescription(OrderTraceDescriptionEnum.新建拣货任务成功.name());
        dto1.setCreateuser("1");
        dto1.setCreatetime(new Date());
        dto1.setOrgId(9972);
        list.add(dto1);

        orderTraceBL.insertUpdateBatch(list);
    }

    @Test
    public void insertBatchTaskTraceTest() {
        List<BatchTaskPO> batchTaskPOS = new ArrayList<>();
        BatchTaskPO batchTaskPO1 = new BatchTaskPO();
        batchTaskPO1.setId("123");
        batchTaskPO1.setBatchTaskNo("WXP234");
        batchTaskPO1.setBatchId("ID345");
        batchTaskPO1.setBatchTaskType((byte)1);
        batchTaskPO1.setBatchTaskName("WXP456");
        batchTaskPO1.setBatchTaskNo("WXP567");
        batchTaskPO1.setOrgId("12345");

        BatchTaskPO batchTaskPO2 = new BatchTaskPO();
        batchTaskPO2.setId("456");
        batchTaskPO2.setBatchTaskNo("WXP234");
        batchTaskPO2.setBatchId("ID345");
        batchTaskPO2.setBatchTaskType((byte)1);
        batchTaskPO2.setBatchTaskName("WXP456");
        batchTaskPO2.setBatchTaskNo("WXP567");
        batchTaskPO2.setOrgId("");

        BatchTaskPO batchTaskPO3 = new BatchTaskPO();
        batchTaskPO3.setId("789");
        batchTaskPO3.setBatchTaskNo("WXP234");
        batchTaskPO3.setBatchId("ID345");
        batchTaskPO3.setBatchTaskType((byte)1);
        batchTaskPO3.setBatchTaskName("WXP456");
        batchTaskPO3.setBatchTaskNo("WXP567");
        batchTaskPO3.setOrgId(null);

        batchTaskPOS.add(batchTaskPO1);
        batchTaskPOS.add(batchTaskPO2);
        batchTaskPOS.add(batchTaskPO3);
        orderTraceBL.insertBatchTaskTrace("WXP", batchTaskPOS);
    }

    @Test
    public void selectByBusinessIdOrNoTest() {
        List<OrderTraceDTO> OrderTraceDTOS = orderTraceBL.selectByBusinessIdOrNo(12345, 123L, "WXP567");
        System.out.println(OrderTraceDTOS);
        List<OrderTraceDTO> OrderTraceDTOS2 = orderTraceBL.selectByBusinessIdOrNo(null, 123L, "WXP567");
        System.out.println(OrderTraceDTOS2);
    }

    @Test
    public void pushTaskMsg() {
        orderTraceBL.pushTaskMsg(123);
    }

    @Autowired
    private OutStockOrderItemDetailModBL outStockOrderItemDetailModBL;

    @Test
    public void modTest() {
        RecoverOrderItemDetailBO bo = new RecoverOrderItemDetailBO();
        bo.setRefOrderNos(Collections.singletonList("711400400013"));
        bo.setBusinessIds(Collections.singletonList("5274754835544802888"));
        bo.setWarehouseId(7111);
        bo.setBatchNo("2024010400080");
        outStockOrderItemDetailModBL.lackFailedRecoverDetail(bo);
    }

    @Autowired
    private BatchTaskMapper batchTaskMapper;
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderTraceBLTest.class);

    @Test
    public void mapperTest() {
        Map<String, Function<Collection<String>, List<BatchTaskPO>>> batchTaskMapperMap = new HashMap<>();

        batchTaskMapperMap.put("getTaskIds", new Function<Collection<String>, List<BatchTaskPO>>() {
            @Override
            public List<BatchTaskPO> apply(Collection<String> batchTaskIds) {
                return batchTaskMapper.findBatchTaskByIds(batchTaskIds);
            }
        });

        List<List<String>> batchTaskIds = new ArrayList<>();
        batchTaskIds.add(Arrays.asList("2025070400275", "2025070400263"));
        batchTaskIds.add(Arrays.asList("2025070400157", "2025070400150"));
        batchTaskIds.add(Arrays.asList("2025070400127", "2025070300337"));

        List<List<BatchTaskPO>> totalBatchTaskPOS = new ArrayList<>();

        CountDownLatch countDownLatch = new CountDownLatch(1);

        Thread[] threads = new Thread[3];
        for (int i = 0; i < threads.length; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                LOGGER.info(Thread.currentThread().getName() + " 查询条件为:{}", JSON.toJSONString(batchTaskIds.get(index)));

                try {
                    countDownLatch.await();
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                List<BatchTaskPO> batchTaskPOList = batchTaskMapperMap.get("getTaskIds").apply(batchTaskIds.get(index));
                LOGGER.info(Thread.currentThread().getName() + " 查询结果为:{}", JSON.toJSONString(batchTaskPOList));
                totalBatchTaskPOS.add(batchTaskPOList);
            });
        }

        for (int i = 0; i < threads.length; i++) {
            threads[i].start();
        }

        countDownLatch.countDown();

        CountDownLatch countDownLatch1 = new CountDownLatch(1);
        try {
            countDownLatch1.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

}
