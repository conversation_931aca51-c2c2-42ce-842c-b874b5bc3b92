/*
 * @ClassName GatherTaskProductMapper
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-10-31 10:56:33
 */
package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskProductPO;
import com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskLocationScaleDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskProductScaleDTO;
import com.yijiupi.himalaya.supplychain.waves.search.GatherTaskSO;

public interface GatherTaskProductMapper {
    /**
     * @Title deleteByPrimaryKey
     * @param id
     * @return int
     */
    int deleteByPrimaryKey(Long id);

    /**
     * @Title insert
     * @param record
     * @return int
     */
    int insert(GatherTaskProductPO record);

    /**
     * @Title insertSelective
     * @param record
     * @return int
     */
    int insertSelective(GatherTaskProductPO record);

    /**
     * 批量新增集货任务产品
     * 
     * @param pos
     * @return
     */
    int insertList(@Param("pos") List<GatherTaskProductPO> pos);

    /**
     * @Title selectByPrimaryKey
     * @param id
     * @return GatherTaskProduct
     */
    GatherTaskProductPO selectByPrimaryKey(Long id);

    /**
     * 查询拣货任务对应的集货商品
     * 
     * @param so
     * @return
     */
    List<GatherTaskProductScaleDTO> selectProduct(@Param("so") GatherTaskSO so);

    /**
     * 查询集货商品出库位明细
     * 
     * @param LocationId
     * @param gatherTaskProductIds
     * @return
     */
    List<GatherTaskLocationScaleDTO> selectProductDetail(@Param("gatherTaskProductId") Long gatherTaskProductId,
        @Param("orgId") Integer orgId);

    /**
     * @Title updateByPrimaryKeySelective
     * @param record
     * @return int
     */
    int updateByPrimaryKeySelective(GatherTaskProductPO record);

    /**
     * @Title updateByPrimaryKey
     * @param record
     * @return int
     */
    int updateByPrimaryKey(GatherTaskProductPO record);
}