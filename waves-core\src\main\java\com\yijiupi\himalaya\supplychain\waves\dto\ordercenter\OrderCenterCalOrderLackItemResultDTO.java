package com.yijiupi.himalaya.supplychain.waves.dto.ordercenter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/8/15
 */
public class OrderCenterCalOrderLackItemResultDTO implements Serializable {
    /**
     * 订单明细id businessitemid
     */
    private Long orderItemId;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 作业小单位数量
     */
    private BigDecimal workingUnitCount;
    /**
     * 产品类型；0-普通产品，1-赠品，2-组合产品
     */
    private Integer productType;
    /**
     * 组合id，产品类型为组合产品时有值
     */
    private Long compositeId;

    /**
     * 实际小单位数量
     */
    private BigDecimal actualUnitCount;

    /**
     * 获取 订单明细id businessitemid
     *
     * @return orderItemId 订单明细id businessitemid
     */
    public Long getOrderItemId() {
        return this.orderItemId;
    }

    /**
     * 设置 订单明细id businessitemid
     *
     * @param orderItemId 订单明细id businessitemid
     */
    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    /**
     * 获取 订单id
     *
     * @return orderId 订单id
     */
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 订单id
     *
     * @param orderId 订单id
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 作业小单位数量
     *
     * @return workingUnitCount 作业小单位数量
     */
    public BigDecimal getWorkingUnitCount() {
        return this.workingUnitCount;
    }

    /**
     * 设置 作业小单位数量
     *
     * @param workingUnitCount 作业小单位数量
     */
    public void setWorkingUnitCount(BigDecimal workingUnitCount) {
        this.workingUnitCount = workingUnitCount;
    }

    /**
     * 获取 产品类型；0-普通产品，1-赠品，2-组合产品
     *
     * @return productType 产品类型；0-普通产品，1-赠品，2-组合产品
     */
    public Integer getProductType() {
        return this.productType;
    }

    /**
     * 设置 产品类型；0-普通产品，1-赠品，2-组合产品
     *
     * @param productType 产品类型；0-普通产品，1-赠品，2-组合产品
     */
    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    /**
     * 获取 组合id，产品类型为组合产品时有值
     *
     * @return compositeId 组合id，产品类型为组合产品时有值
     */
    public Long getCompositeId() {
        return this.compositeId;
    }

    /**
     * 设置 组合id，产品类型为组合产品时有值
     *
     * @param compositeId 组合id，产品类型为组合产品时有值
     */
    public void setCompositeId(Long compositeId) {
        this.compositeId = compositeId;
    }

    public BigDecimal getActualUnitCount() {
        return actualUnitCount;
    }

    public void setActualUnitCount(BigDecimal actualUnitCount) {
        this.actualUnitCount = actualUnitCount;
    }
}
