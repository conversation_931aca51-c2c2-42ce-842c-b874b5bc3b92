package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskCompleteDTO;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/19
 */
public class CompleteBatchTaskItemDTO implements Serializable {

    private List<BatchTaskItemCompleteDTO> batchTaskItemUpdateDTOList;
    private String batchTaskId;
    private String userName;
    private Integer warehouseId;
    private Long locationId;
    private String locationName;
    private Integer cityId;
    private Integer userId;
    private Byte containerFlag;

    /**
     * 来源端：PDA，控制器
     * 
     * @see BatchTaskCompleteDTO
     */
    private Byte operateSource;

    /**
     * 获取
     *
     * @return batchTaskItemUpdateDTOList
     */
    public List<BatchTaskItemCompleteDTO> getBatchTaskItemUpdateDTOList() {
        return this.batchTaskItemUpdateDTOList;
    }

    /**
     * 设置
     *
     * @param batchTaskItemUpdateDTOList
     */
    public void setBatchTaskItemUpdateDTOList(List<BatchTaskItemCompleteDTO> batchTaskItemUpdateDTOList) {
        this.batchTaskItemUpdateDTOList = batchTaskItemUpdateDTOList;
    }

    /**
     * 获取
     *
     * @return batchTaskId
     */
    public String getBatchTaskId() {
        return this.batchTaskId;
    }

    /**
     * 设置
     *
     * @param batchTaskId
     */
    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    /**
     * 获取
     *
     * @return userName
     */
    public String getUserName() {
        return this.userName;
    }

    /**
     * 设置
     *
     * @param userName
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * 获取
     *
     * @return warehouseId
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置
     *
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取
     *
     * @return locationId
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置
     *
     * @param locationId
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取
     *
     * @return locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置
     *
     * @param locationName
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取
     *
     * @return cityId
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置
     *
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取
     *
     * @return userId
     */
    public Integer getUserId() {
        return this.userId;
    }

    /**
     * 设置
     *
     * @param userId
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取
     *
     * @return containerFlag
     */
    public Byte getContainerFlag() {
        return this.containerFlag;
    }

    /**
     * 设置
     *
     * @param containerFlag
     */
    public void setContainerFlag(Byte containerFlag) {
        this.containerFlag = containerFlag;
    }

    /**
     * 获取 来源端：PDA，控制器 @see BatchTaskCompleteDTO
     *
     * @return operateSource 来源端：PDA，控制器 @see BatchTaskCompleteDTO
     */
    public Byte getOperateSource() {
        return this.operateSource;
    }

    /**
     * 设置 来源端：PDA，控制器 @see BatchTaskCompleteDTO
     *
     * @param operateSource 来源端：PDA，控制器 @see BatchTaskCompleteDTO
     */
    public void setOperateSource(Byte operateSource) {
        this.operateSource = operateSource;
    }
}
