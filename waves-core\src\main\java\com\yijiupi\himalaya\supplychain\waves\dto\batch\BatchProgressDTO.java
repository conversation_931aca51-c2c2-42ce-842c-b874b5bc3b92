package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import com.yijiupi.himalaya.supplychain.waves.enums.BatchProgressType;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-02-29 10:08
 **/
public class BatchProgressDTO implements Serializable {

    /**
     * 已拣货拣货任务明细行数
     */
    private BigDecimal pickedRowCount;

    /**
     * 总共拣货任务明细行数
     */
    private BigDecimal totalRowCount;

    /**
     * 订单类型 {@link BatchProgressType}
     */
    private Integer type;

    public static BatchProgressDTO of(Number pickedRowCount, Number totalRowCount, Integer type) {
        BatchProgressDTO batchProgressDTO = new BatchProgressDTO();
        batchProgressDTO.setPickedRowCount(new BigDecimal(String.valueOf(pickedRowCount)));
        batchProgressDTO.setTotalRowCount(new BigDecimal(String.valueOf(totalRowCount)));
        batchProgressDTO.setType(type);
        return batchProgressDTO;
    }

    public BigDecimal getPickedRowCount() {
        return pickedRowCount;
    }

    public void setPickedRowCount(BigDecimal pickedRowCount) {
        this.pickedRowCount = pickedRowCount;
    }

    public BigDecimal getTotalRowCount() {
        return totalRowCount;
    }

    public void setTotalRowCount(BigDecimal totalRowCount) {
        this.totalRowCount = totalRowCount;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
