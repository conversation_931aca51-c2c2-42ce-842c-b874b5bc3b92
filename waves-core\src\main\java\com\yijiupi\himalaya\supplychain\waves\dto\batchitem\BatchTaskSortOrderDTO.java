package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortItemDTO;

import java.io.Serializable;
import java.util.List;

/**
 * 订单维度的拣货任务订单
 *
 * <AUTHOR>
 * @since 2018-03-26
 */
public class BatchTaskSortOrderDTO implements Serializable {
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 订单编号
     */
    private String orderNoteno;
    /**
     * 订单详情
     */
    private List<BatchTaskSortItemDTO> batchTaskItemDTOList;
    private String userName;
    private String shopName;
    private String mobileNo;
    private String province;
    private String city;
    private String county;
    private String street;
    private String detailAddress;

    /**
     * 订单序号
     */
    private Integer orderSequence;

    /**
     * 箱号
     */
    private Integer sowOrderSequence;

    /**
     * 集货位id
     */
    private Long sowLocationId;

    /**
     * 集货位名称
     */
    private String sowLocationName;

    /**
     * 如果这个订单分了多个拣货任务，则显示其他拣货任务的拣货件数。
     */
    private List<BatchTaskSortOrderOtherDTO> otherBatchTaskDTOList;

    /**
     * 订单类型
     */
    private Byte orderType;

    /**
     * 是否生成了内配单标识, true: 内配单, false：非内配单
     */
    private Boolean allocationFlag = false;

    /**
     * 二级仓出库位id
     */
    private Long secondToLocationId;

    /**
     * 二级仓出库位名称
     */
    private String secondToLocationName;

    /**
     * 二级仓库名称
     */
    private String secondWarehouseName;

    /**
     * 订单属性（1：大件订单 2：小件订单）
     */
    private Byte packageAttribute;

    /**
     * 配送方式（7：快递直发订单）
     */
    private Byte deliveryMode;

    /**
     * 关联业务单据类型1=酒批业务订单，2=酒批业务退货单，3=经纪人撮合业务，4=兑奖订单业务 5 erp业务
     */
    private Byte businessType;

    /**
     * 内配类型: 8.内配，9.内配退，11.中转 12.中转退
     */
    private Byte allotType;

    /**
     * 片区名称
     */
    private String areaName;

    /**
     * 线路名称
     */
    private String routeName;

    /**
     * 订单特征, 1=大件;2=小件;3=单品;4=休食;
     */
    private List<Byte> featureType;

    /**
     * 托盘号集合
     */
    private List<String> palletNoList;
    /**
     * 地址id
     */
    private Integer addressId;

    /**
     * 重构出库单类型
     */
    private Byte outBoundType;

    public List<Byte> getFeatureType() {
        return featureType;
    }

    public void setFeatureType(List<Byte> featureType) {
        this.featureType = featureType;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    public Byte getAllotType() {
        return allotType;
    }

    public void setAllotType(Byte allotType) {
        this.allotType = allotType;
    }

    public Byte getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(Byte deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    public Byte getPackageAttribute() {
        return packageAttribute;
    }

    public void setPackageAttribute(Byte packageAttribute) {
        this.packageAttribute = packageAttribute;
    }

    public String getSecondWarehouseName() {
        return secondWarehouseName;
    }

    public void setSecondWarehouseName(String secondWarehouseName) {
        this.secondWarehouseName = secondWarehouseName;
    }

    public Long getSecondToLocationId() {
        return secondToLocationId;
    }

    public void setSecondToLocationId(Long secondToLocationId) {
        this.secondToLocationId = secondToLocationId;
    }

    public String getSecondToLocationName() {
        return secondToLocationName;
    }

    public void setSecondToLocationName(String secondToLocationName) {
        this.secondToLocationName = secondToLocationName;
    }

    public List<BatchTaskSortOrderOtherDTO> getOtherBatchTaskDTOList() {
        return otherBatchTaskDTOList;
    }

    public void setOtherBatchTaskDTOList(List<BatchTaskSortOrderOtherDTO> otherBatchTaskDTOList) {
        this.otherBatchTaskDTOList = otherBatchTaskDTOList;
    }

    public Boolean getAllocationFlag() {
        return allocationFlag;
    }

    public void setAllocationFlag(Boolean allocationFlag) {
        this.allocationFlag = allocationFlag;
    }

    public Byte getOrderType() {
        return orderType;
    }

    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }

    public Integer getOrderSequence() {
        return orderSequence;
    }

    public void setOrderSequence(Integer orderSequence) {
        this.orderSequence = orderSequence;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    /**
     * 获取 订单id
     */
    public String getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 订单id
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 订单编号
     */
    public String getOrderNoteno() {
        return this.orderNoteno;
    }

    /**
     * 设置 订单编号
     */
    public void setOrderNoteno(String orderNoteno) {
        this.orderNoteno = orderNoteno;
    }

    /**
     * 获取 订单详情
     */
    public List<BatchTaskSortItemDTO> getBatchTaskItemDTOList() {
        return this.batchTaskItemDTOList;
    }

    /**
     * 设置 订单详情
     */
    public void setBatchTaskItemDTOList(List<BatchTaskSortItemDTO> batchTaskItemDTOList) {
        this.batchTaskItemDTOList = batchTaskItemDTOList;
    }

    public Integer getSowOrderSequence() {
        return sowOrderSequence;
    }

    public void setSowOrderSequence(Integer sowOrderSequence) {
        this.sowOrderSequence = sowOrderSequence;
    }

    public Long getSowLocationId() {
        return sowLocationId;
    }

    public void setSowLocationId(Long sowLocationId) {
        this.sowLocationId = sowLocationId;
    }

    public String getSowLocationName() {
        return sowLocationName;
    }

    public void setSowLocationName(String sowLocationName) {
        this.sowLocationName = sowLocationName;
    }

    public List<String> getPalletNoList() {
        return palletNoList;
    }

    public void setPalletNoList(List<String> palletNoList) {
        this.palletNoList = palletNoList;
    }

    /**
     * 获取 地址id
     *
     * @return addressId 地址id
     */
    public Integer getAddressId() {
        return this.addressId;
    }

    /**
     * 设置 地址id
     *
     * @param addressId 地址id
     */
    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    public Byte getOutBoundType() {
        return outBoundType;
    }

    public void setOutBoundType(Byte outBoundType) {
        this.outBoundType = outBoundType;
    }
}
