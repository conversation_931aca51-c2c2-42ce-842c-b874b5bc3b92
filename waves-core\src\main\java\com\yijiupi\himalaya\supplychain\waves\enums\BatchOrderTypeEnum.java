package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 波次订单类型
 *
 * <AUTHOR>
 * @date 2019-09-24 14:51
 */
public enum BatchOrderTypeEnum {
    /**
     * 普通订单
     */
    普通订单((byte)0),
    /**
     * 普通订单
     */
    团购订单((byte)1),
    /**
     * 内配/调拨单
     */
    内配_调拨订单((byte)2),
    /**
     * 快递直发订单
     */
    快递直发订单((byte)3),;

    private Byte type;

    BatchOrderTypeEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

}
