package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.util.List;

public class BatchUpdateDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 订单号
     */
    private List<String> orderNos;

    /**
     * 状态
     */
    private Integer batchState;

    /**
     * 操作人
     */
    private String operateUser;
    /**
     * 配送车辆ID
     */
    private Long deliveryCarId;
    /**
     * 物流公司ID
     */
    private Long logisticsCompanyId;

    public Long getDeliveryCarId() {
        return deliveryCarId;
    }

    public void setDeliveryCarId(Long deliveryCarId) {
        this.deliveryCarId = deliveryCarId;
    }

    public Long getLogisticsCompanyId() {
        return logisticsCompanyId;
    }

    public void setLogisticsCompanyId(Long logisticsCompanyId) {
        this.logisticsCompanyId = logisticsCompanyId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public List<String> getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }

    public Integer getBatchState() {
        return batchState;
    }

    public void setBatchState(Integer batchState) {
        this.batchState = batchState;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

}
