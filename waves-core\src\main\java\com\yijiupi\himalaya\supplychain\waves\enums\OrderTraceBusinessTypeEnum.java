package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 操作记录 - 单据类型
 *
 * <AUTHOR>
 * @date 2018/5/22 16:54
 */
public enum OrderTraceBusinessTypeEnum {
    /**
     * 枚举
     */
    波次策略((byte)1), 分配策略((byte)2), 上架策略((byte)3), 波次((byte)4), 拣货任务((byte)5), 上架单((byte)6), 上架任务((byte)7), 移库单((byte)8),
    出库单((byte)9), 入库单((byte)10), 播种任务((byte)11), 加工单((byte)12), 补货任务((byte)13), 打包复核((byte)14), 异常订单((byte)15),
    出库批次((byte)16);

    /**
     * type
     */
    private Byte type;

    OrderTraceBusinessTypeEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

}
