package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemContainerPO;

/**
 * 拣货容器位
 */
@Mapper
public interface BatchTaskItemContainerMapper {

    int deleteByPrimaryKey(Long id);

    int insert(BatchTaskItemContainerPO record);

    int insertSelective(BatchTaskItemContainerPO record);

    /**
     * 批量新增拣货任务明细项容器位
     * 
     * @param pos
     * @return
     */
    int insertList(@Param("pos") List<BatchTaskItemContainerPO> pos);

    BatchTaskItemContainerPO selectByPrimaryKey(Long id);

    // @MapKey("locationId")
    List<BatchTaskItemContainerPO> selectByBatchtaskitemId(String batchtaskitemId);

    /**
     * 查询拣货任务明细项容器位集合
     * 
     * @param list
     * @return
     */
    List<BatchTaskItemContainerPO> selectByBatchtaskitemIdList(List<Long> list);

    int updateByPrimaryKeySelective(BatchTaskItemContainerPO record);

    int updateByPrimaryKey(BatchTaskItemContainerPO record);

}