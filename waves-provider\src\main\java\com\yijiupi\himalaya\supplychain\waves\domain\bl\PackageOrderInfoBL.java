package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.OrderCommonDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.ordercenter.OrderCenterQueryBL;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.PackageOrderInfoConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.search.PackageOrderItemSO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023-11-02 15:21
 **/
@Service
public class PackageOrderInfoBL {

    @Resource
    private OutStockOrderMapper outStockOrderMapper;

    @Resource
    private OrderCenterQueryBL orderCenterQueryBL;

    @Resource
    private PackageOrderInfoConverter packageOrderInfoConverter;

    public PackageOrderInfoDTO findPackageOrderByOrderNo(PackageOrderItemSO param) {
        String refOrderNo = param.getRefOrderNo();
        Integer orgId = param.getOrgId();
        Integer warehouseId = param.getWarehouseId();
        OutStockOrderPO order = outStockOrderMapper.selectByRefOrderNo(refOrderNo, orgId, warehouseId);
        if (order == null) {
            throw new BusinessValidateException("订单不存在");
        }
        OrderCommonDetailDTO orderDetail = orderCenterQueryBL.getByOrderId(Long.valueOf(order.getBusinessId()));
        return packageOrderInfoConverter.toDTO(order, orderDetail);
    }

}
