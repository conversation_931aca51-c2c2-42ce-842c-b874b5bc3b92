package com.yijiupi.himalaya.supplychain.waves.dto.soworder;

import java.io.Serializable;
import java.util.List;

public class SowingPrintInfoDTO implements Serializable {

    /**
     * 播种任务id
     */
    private Long id;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 播种任务名称
     */
    private String sowTaskName;

    /**
     * 播种人
     */
    private String sower;

    /**
     * 播种任务种类总数
     */
    private Integer sowTotalCount;

    /**
     * 容器总数
     */
    private Integer containerCount;

    /**
     * 播种任务明细
     */
    private List<SowProductItemDTO> sowProductItemDTOS;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public String getSowTaskName() {
        return sowTaskName;
    }

    public void setSowTaskName(String sowTaskName) {
        this.sowTaskName = sowTaskName;
    }

    public String getSower() {
        return sower;
    }

    public void setSower(String sower) {
        this.sower = sower;
    }

    public Integer getSowTotalCount() {
        return sowTotalCount;
    }

    public void setSowTotalCount(Integer sowTotalCount) {
        this.sowTotalCount = sowTotalCount;
    }

    public Integer getContainerCount() {
        return containerCount;
    }

    public void setContainerCount(Integer containerCount) {
        this.containerCount = containerCount;
    }

    public List<SowProductItemDTO> getSowProductItemDTOS() {
        return sowProductItemDTOS;
    }

    public void setSowProductItemDTOS(List<SowProductItemDTO> sowProductItemDTOS) {
        this.sowProductItemDTOS = sowProductItemDTOS;
    }
}
