package com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder;

import java.util.*;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchTaskConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.OrderItemTaskInfoDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/6/11
 */
@Service
public class OutStockOrderQueryBL {

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    public OutStockOrderDTO findByOrderId(Long id) {
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.selectByIds(Collections.singletonList(id));
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return null;
        }

        OutStockOrderPO outStockOrderPO = outStockOrderPOList.stream().findFirst().get();
        OutStockOrderDTO outStockOrderDTO = new OutStockOrderDTO();
        outStockOrderDTO.setId(outStockOrderPO.getId());
        outStockOrderDTO.setBatchno(outStockOrderPO.getBatchno());

        return outStockOrderDTO;
    }

    public List<OrderItemTaskInfoDTO> findRelateBathTaskId(List<Long> outStockOrderItemIds) {
        if (CollectionUtils.isEmpty(outStockOrderItemIds)) {
            return Collections.emptyList();
        }
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderItemIds(outStockOrderItemIds);
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return Collections.emptyList();
        }

        Map<String, OrderItemTaskInfoPO> distinctBatchMap = orderItemTaskInfoPOList.stream()
            .collect(Collectors.toMap(OrderItemTaskInfoPO::getBatchTaskId, v -> v, (v1, v2) -> v1));

        return BatchTaskConvertor.orderItemTaskInfoPOToDTO(new ArrayList<>(distinctBatchMap.values()));
    }

    public List<OrderItemTaskInfoDTO> findRelateBathTaskIdByOrderNo(List<String> refOrderNos, Integer warehouseId) {
        if (CollectionUtils.isEmpty(refOrderNos)) {
            return Collections.emptyList();
        }

        List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.findIdByOrderNos(refOrderNos, warehouseId);
        if (CollectionUtils.isEmpty(outStockOrderPOS)) {
            return Collections.emptyList();
        }
        List<Long> ids = outStockOrderPOS.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());;
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(ids);
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return Collections.emptyList();
        }

        Map<String, OrderItemTaskInfoPO> distinctBatchMap = orderItemTaskInfoPOList.stream()
            .collect(Collectors.toMap(OrderItemTaskInfoPO::getBatchTaskId, v -> v, (v1, v2) -> v1));

        return BatchTaskConvertor.orderItemTaskInfoPOToDTO(new ArrayList<>(distinctBatchMap.values()));
    }

}