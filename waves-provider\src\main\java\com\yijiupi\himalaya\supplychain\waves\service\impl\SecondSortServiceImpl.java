package com.yijiupi.himalaya.supplychain.waves.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.batch.ISecondSortService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.SecondSortBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderItemPickCountInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderItemPickCountInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.secondsort.*;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-06-02
 */
@Service(timeout = 600000)
public class SecondSortServiceImpl implements ISecondSortService {

    @Autowired
    private SecondSortBL secondSortBl;
    @Autowired
    private GlobalCache globalCache;

    @Override
    public SowSecondDTO beginSecondSortTask(SecondSortQueryDTO queryDTO) {
        return secondSortBl.beginSecondSortTask(queryDTO);
    }

    @Override
    public void updateSecondSortTaskPallet(SecondSortDTO dto) {
        secondSortBl.updateSecondSortTaskPallet(dto);
    }

    @Override
    public SowSecondDTO findSecondSortByParams(SecondSortQueryDTO queryDTO) {
        return secondSortBl.listSowPackageOrderItems(queryDTO);
    }

    @Override
    public List<PackageOrderItemDTO> listSowBoxcodeItems(SecondSortQueryDTO queryDTO) {
        return secondSortBl.listSowBoxcodeItems(queryDTO);
    }

    @Override
    public void completeSecondSow(SecondSowCompleteDTO completeDTO) {
        secondSortBl.completeSecondSow(completeDTO);
    }

    @Override
    public PageList<BatchTaskSortDTO> findBatchTaskSortList(BatchTaskSortQueryDTO queryDTO) {
        return secondSortBl.findBatchTaskSecondSortList(queryDTO);
    }

    @Override
    public List<SecondPickSowItemDTO> querySecondPickSow(SecondPickSowQueryDTO queryDTO) {
        return secondSortBl.querySecondPickSow(queryDTO);
    }

    @Override
    public void completeSecondPickSowItem(CompleteSecondPickSowItemDTO completeDTO) {
        secondSortBl.completeSecondPickSowItem(completeDTO);
    }

    @Override
    public Boolean isOpenSecondSort(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库Id不能为空");
        return globalCache.isOpenSecondSort(warehouseId);
    }

    @Override
    public void completeSecondPickSow(CompleteSecondPickSowDTO completeDTO) {
        secondSortBl.completeSecondPickSow(completeDTO);
    }

    @Override
    public void secondPickLocationSync(SecondPickLocationSyncDTO syncDTO) {
        secondSortBl.secondPickLocationSync(syncDTO);
    }

    /**
     * 查询订单项实际拣货数量
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<OrderItemPickCountInfoDTO> findOrderItemPickCount(OrderItemPickCountInfoQueryDTO queryDTO) {
        AssertUtils.notEmpty(queryDTO.getOrderItemIdList(), "订单信息不能为空！");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库信息不能为空！");
        return secondSortBl.findOrderItemPickCount(queryDTO);
    }

}
