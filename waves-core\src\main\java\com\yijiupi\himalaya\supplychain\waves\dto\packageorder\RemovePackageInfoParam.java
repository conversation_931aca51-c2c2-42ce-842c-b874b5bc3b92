package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-09-23 10:13
 */
public class RemovePackageInfoParam implements Serializable {
    /**
     * 出库单编号列表
     */
    private List<String> refOrderNos;
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 操作人 id, 同步 trace 用
     */
    private Integer opUserId;

    /**
     * 获取 出库单编号列表
     *
     * @return refOrderNos 出库单编号列表
     */
    public List<String> getRefOrderNos() {
        return this.refOrderNos;
    }

    /**
     * 设置 出库单编号列表
     *
     * @param refOrderNos 出库单编号列表
     */
    public void setRefOrderNos(List<String> refOrderNos) {
        this.refOrderNos = refOrderNos;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getOpUserId() {
        return opUserId;
    }

    public void setOpUserId(Integer opUserId) {
        this.opUserId = opUserId;
    }
}
