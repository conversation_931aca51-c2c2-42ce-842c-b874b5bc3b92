package com.yijiupi.himalaya.supplychain.waves.domain.mq;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.google.gson.Gson;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.ReplenishmentTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.PushMessageNewDTO;

/**
 * 推送消息MQ
 *
 */
@Component
public class PushMsgMQ {

    private static final Logger LOG = LoggerFactory.getLogger(PushMsgMQ.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private Gson gson;

    @Value("${ex.baseservice.message.yjp_Message_Push}")
    private String appMsgPushExchange;
    @Value("ex.supplychain.replenishment.add")
    private String replenishmentAddEx;

    public void pushAppMessage(PushMessageNewDTO msg) {
        rabbitTemplate.convertAndSend(appMsgPushExchange, null, msg);
    }

    /**
     * 发送补货消息
     * 
     * @param replenishmentTaskItemDTOS
     */
    public void pushReplenishmentAdd(List<ReplenishmentTaskItemDTO> replenishmentTaskItemDTOS) {
        LOG.info("发送补货消息:{}", gson.toJson(replenishmentTaskItemDTOS));
        try {
            rabbitTemplate.convertAndSend(replenishmentAddEx, "", replenishmentTaskItemDTOS);
        } catch (Exception e) {
            LOG.warn("发送补货消息失败，参数：" + gson.toJson(replenishmentTaskItemDTOS), e);
        }
    }
}
