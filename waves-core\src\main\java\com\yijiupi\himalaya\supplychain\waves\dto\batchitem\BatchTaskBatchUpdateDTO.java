package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskKindOfPickingConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskWarehouseFeatureTypeConstants;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/2/21
 */
public class BatchTaskBatchUpdateDTO implements Serializable {

    private List<String> batchTaskIds;

    private Byte taskState;

    private Long toLocationId;

    private String toLocationName;
    /**
     * 拣货方式:1、默认；2、电子标签拣货
     *
     * @see BatchTaskKindOfPickingConstants
     */
    private Byte kindOfPicking;
    /**
     * 拣货模式
     */
    private Byte pickPattern;
    /**
     * 第几次追加拣货任务
     */
    private Byte taskAppendSequence;
    /**
     * 拣货任务特征
     *
     * @see TaskWarehouseFeatureTypeConstants
     */
    private Byte taskWarehouseFeatureType;

    /**
     * 获取
     *
     * @return batchTaskIds
     */
    public List<String> getBatchTaskIds() {
        return this.batchTaskIds;
    }

    /**
     * 设置
     *
     * @param batchTaskIds
     */
    public void setBatchTaskIds(List<String> batchTaskIds) {
        this.batchTaskIds = batchTaskIds;
    }

    /**
     * 获取
     *
     * @return taskState
     */
    public Byte getTaskState() {
        return this.taskState;
    }

    /**
     * 设置
     *
     * @param taskState
     */
    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    /**
     * 获取
     *
     * @return toLocationId
     */
    public Long getToLocationId() {
        return this.toLocationId;
    }

    /**
     * 设置
     *
     * @param toLocationId
     */
    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    /**
     * 获取
     *
     * @return toLocationName
     */
    public String getToLocationName() {
        return this.toLocationName;
    }

    /**
     * 设置
     *
     * @param toLocationName
     */
    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    /**
     * 获取 拣货方式:1、默认；2、电子标签拣货 @see BatchTaskKindOfPickingConstants
     *
     * @return kindOfPicking 拣货方式:1、默认；2、电子标签拣货 @see BatchTaskKindOfPickingConstants
     */
    public Byte getKindOfPicking() {
        return this.kindOfPicking;
    }

    /**
     * 设置 拣货方式:1、默认；2、电子标签拣货 @see BatchTaskKindOfPickingConstants
     *
     * @param kindOfPicking 拣货方式:1、默认；2、电子标签拣货 @see BatchTaskKindOfPickingConstants
     */
    public void setKindOfPicking(Byte kindOfPicking) {
        this.kindOfPicking = kindOfPicking;
    }

    /**
     * 获取 拣货模式
     *
     * @return pickPattern 拣货模式
     */
    public Byte getPickPattern() {
        return this.pickPattern;
    }

    /**
     * 设置 拣货模式
     *
     * @param pickPattern 拣货模式
     */
    public void setPickPattern(Byte pickPattern) {
        this.pickPattern = pickPattern;
    }

    /**
     * 获取 第几次追加拣货任务
     *
     * @return taskAppendSequence 第几次追加拣货任务
     */
    public Byte getTaskAppendSequence() {
        return this.taskAppendSequence;
    }

    /**
     * 设置 第几次追加拣货任务
     *
     * @param taskAppendSequence 第几次追加拣货任务
     */
    public void setTaskAppendSequence(Byte taskAppendSequence) {
        this.taskAppendSequence = taskAppendSequence;
    }

    /**
     * 获取 拣货任务特征 @see TaskWarehouseFeatureTypeConstants
     *
     * @return taskWarehouseFeatureType 拣货任务特征 @see TaskWarehouseFeatureTypeConstants
     */
    public Byte getTaskWarehouseFeatureType() {
        return this.taskWarehouseFeatureType;
    }

    /**
     * 设置 拣货任务特征 @see TaskWarehouseFeatureTypeConstants
     *
     * @param taskWarehouseFeatureType 拣货任务特征 @see TaskWarehouseFeatureTypeConstants
     */
    public void setTaskWarehouseFeatureType(Byte taskWarehouseFeatureType) {
        this.taskWarehouseFeatureType = taskWarehouseFeatureType;
    }
}
