<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration>
    <classPathEntry
            location="/Users/<USER>/dev_tools/mysql-connector-java-5.1.39.jar"/>

    <context id="mySql" targetRuntime="MyBatis3" defaultModelType="flat">

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="***********************************************************" userId="root"
                        password="yijiupi"/>


        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- 生成模型的包名和位置 -->
        <javaModelGenerator
                targetPackage="com.yijiupi.himalaya.supplychain.waves.domain.po.sow"
                targetProject="/Users/<USER>/IdeaProjects/wms/supplychain-microservice-waves/waves-provider/src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- 生成映射文件的包名和位置 -->
        <sqlMapGenerator targetPackage="mappings.wcs"
                         targetProject="/Users/<USER>/IdeaProjects/wms/supplychain-microservice-waves/waves-provider/src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- 生成DAO的包名和位置 -->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="com.yijiupi.himalaya.supplychain.waves.domain.dao.sow"
                             targetProject="/Users/<USER>/IdeaProjects/wms//Users/<USER>/IdeaProjects/wms/supplychain-microservice-waves/waves-provider/src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>


        <table tableName="BatchTaskItem11" domainObjectName="BatchTaskPO11"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
            <property name="useActualColumnNames" value="true"/>
        </table>


    </context>
</generatorConfiguration>