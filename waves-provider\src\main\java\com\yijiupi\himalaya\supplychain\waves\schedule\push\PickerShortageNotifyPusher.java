package com.yijiupi.himalaya.supplychain.waves.schedule.push;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.assignment.dto.push.MessagePushParam;
import com.yijiupi.himalaya.assignment.dto.push.MessagePushTarget;
import com.yijiupi.himalaya.assignment.service.IMessagePushService;
import com.yijiupi.himalaya.supplychain.waves.util.StringTemplateUtils;
import com.yijiupi.supplychain.serviceutils.constant.AdminUserAuthRoleConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-11-07 11:30
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class PickerShortageNotifyPusher {

    @Reference
    private IMessagePushService messagePushService;

    private static final String TEMPLATE = "截止到 X 点, 本仓待分拣订单共 Y 单, %s当前在岗分拣人员 A 名, 预计需要 B 小时才能分拣完, 请立即补充分拣人员！";

    private static final String TEMPLATE2 = "预计到 ? 点将达到 Z 单, ";

    private static final String TITLE = "分拣不足预警";

    private static final Logger logger = LoggerFactory.getLogger(PickerShortageNotifyPusher.class);

    /**
     * 推送分拣不足预警
     */
    public void pushNotifyMessage(Builder builder, Map<String, ?> param) {
        String template = StringTemplateUtils.replace(TEMPLATE, param, "X", "Y", "A", "B");
        if (param.get("?") != null) {
            template = String.format(template, StringTemplateUtils.replace(TEMPLATE2, param, "?", "Z"));
        } else {
            template = String.format(template, "");
        }
        logger.info("推送消息模板: {}", template);
        MessagePushParam.newBuilder()
                .setWarehouseId(builder.warehouseId)
                .setTitle(TITLE)
                .setContent(template)
                .setValidityTime(1)
                .addRoleCode(AdminUserAuthRoleConstant.ROLE_CODE_仓库管理员新流程)
                .build(MessagePushTarget.PDA, MessagePushTarget.CLIENT)
                .forEach(messagePushService::pushMessage);
    }

    public Builder builder() {
        return new Builder();
    }

    public class Builder {
        /**
         * 仓库 id
         */
        private int warehouseId;
        /**
         * 分仓下 待调度, 待拣货的订单数量
         */
        private int orderCount;
        /**
         * 分仓截单时间
         */
        private LocalDateTime orderCutoffTime;
        /**
         * 当前在岗分拣员数量
         */
        private int onlineWorkerCount;
        /**
         * 预计完成所有工作所需时间
         */
        private BigDecimal aboutFinishHours;
        /**
         * 预计到截单时间后, 当前待分拣单量 + 新增单量
         */
        private int aboutNewOrderCount;

        public Builder setWarehouseId(int warehouseId) {
            this.warehouseId = warehouseId;
            return this;
        }

        public Builder setOrderCount(int orderCount) {
            this.orderCount = orderCount;
            return this;
        }

        public Builder setOrderCutoffTime(LocalDateTime orderCutoffTime) {
            this.orderCutoffTime = orderCutoffTime;
            return this;
        }

        public Builder setOnlineWorkerCount(int onlineWorkerCount) {
            this.onlineWorkerCount = onlineWorkerCount;
            return this;
        }

        public Builder setAboutFinishHours(BigDecimal aboutFinishHours) {
            this.aboutFinishHours = aboutFinishHours;
            return this;
        }

        public Builder setAboutNewOrderCount(int aboutNewOrderCount) {
            this.aboutNewOrderCount = aboutNewOrderCount;
            return this;
        }

        public void buildAndPush() {
            LocalDateTime now = LocalDateTime.now();
            Map<String, Object> map = new HashMap<>();
            map.put("X", now.getHour());
            map.put("Y", orderCount);
            // 如果当前时间超过截单时间, 就不展示 ?
            if (now.isAfter(orderCutoffTime)) {
                map.put("?", null);
            } else {
                // 截单时间 24:00 的特殊处理
                int hour = orderCutoffTime.getHour() == 23 && orderCutoffTime.getMinute() == 59 ? 24
                        : orderCutoffTime.getHour();
                map.put("?", hour);
            }
            map.put("Z", aboutNewOrderCount);
            map.put("A", onlineWorkerCount);
            map.put("B", aboutFinishHours);
            pushNotifyMessage(this, map);
        }
    }

}
