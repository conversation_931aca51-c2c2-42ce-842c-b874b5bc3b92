package com.yijiupi.himalaya.supplychain.waves.domain.bo.orderpallet;

import java.util.Arrays;
import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/23
 */
public class OrderInPalletQueryBO {
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 出库位id
     */
    private Long locationId;
    /**
     * 托盘号
     */
    private List<String> palletNoList;
    /**
     * 订单状态
     */
    private List<Byte> orderState = Arrays.asList(Byte.valueOf("1"), Byte.valueOf("2"), Byte.valueOf("3"));

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 出库位id
     *
     * @return locationId 出库位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 出库位id
     *
     * @param locationId 出库位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 订单状态
     *
     * @return orderState 订单状态
     */
    public List<Byte> getOrderState() {
        return this.orderState;
    }

    /**
     * 设置 订单状态
     *
     * @param orderState 订单状态
     */
    public void setOrderState(List<Byte> orderState) {
        this.orderState = orderState;
    }

    /**
     * 获取 托盘号
     *
     * @return palletNoList 托盘号
     */
    public List<String> getPalletNoList() {
        return this.palletNoList;
    }

    /**
     * 设置 托盘号
     *
     * @param palletNoList 托盘号
     */
    public void setPalletNoList(List<String> palletNoList) {
        this.palletNoList = palletNoList;
    }
}
