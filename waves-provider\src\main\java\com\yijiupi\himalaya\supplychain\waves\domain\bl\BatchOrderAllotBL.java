package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchAllotPriorityDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByProductAndOrderDTO;

/**
 * 波次分配
 *
 * <AUTHOR>
 * @date 2019-08-22 17:50
 */
@Service
public class BatchOrderAllotBL {

    private static Logger LOGGER = LoggerFactory.getLogger(BatchOrderAllotBL.class);

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;

    @Autowired
    private BatchOrderProcessBL batchOrderProcessBL;

    @Reference
    private IWarehouseInventoryQueryService iWarehouseInventoryQueryService;

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;

    /**
     * 获取波次分配的优先级及缺货产品
     */
    public BatchAllotPriorityDTO
        getBatchAllotPriority(BatchCreateByProductAndOrderDTO batchCreateByProductAndOrderDTO) {
        // 1、获取待生成波次的订单
        List<OutStockOrderPO> outStockOrderPOList =
            batchOrderProcessBL.getOrderListByItemIds(batchCreateByProductAndOrderDTO);
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            throw new BusinessException("找不到订单！");
        }

        // 2、找出缺货产品的skuId
        List<Long> productSkuIdList = getLackProductSkuId(batchCreateByProductAndOrderDTO.getCityId(),
            batchCreateByProductAndOrderDTO.getWarehouseId(), outStockOrderPOList);
        if (CollectionUtils.isEmpty(productSkuIdList)) {
            return null;
        }

        // 3、返回列表数据
        BatchAllotPriorityDTO batchAllotPriorityDTO = new BatchAllotPriorityDTO();
        // 缺货产品
        List<ProductSkuDTO> productSkuDTOList = iProductSkuQueryService.findBySku(productSkuIdList);
        List<String> productList = productSkuDTOList.stream().map(p -> p.getName()).collect(Collectors.toList());
        batchAllotPriorityDTO.setProductList(productList);
        // 城市优先级
        List<String> cityList =
            outStockOrderPOList.stream().map(p -> p.getCity()).distinct().collect(Collectors.toList());
        batchAllotPriorityDTO.setCityList(cityList);
        // 线路优先级
        List<String> routeList =
            outStockOrderPOList.stream().map(p -> p.getRouteName()).distinct().collect(Collectors.toList());
        batchAllotPriorityDTO.setRouteList(routeList);

        return batchAllotPriorityDTO;
    }

    /**
     * 获取缺货产品的skuId
     * 
     * @return
     */
    private List<Long> getLackProductSkuId(Integer cityId, Integer warehouseId,
        List<OutStockOrderPO> outStockOrderPOList) {
        // 获取所有订单项
        List<OutStockOrderItemPO> outStockOrderItemPOList = getOutStockOrderItemPOS(outStockOrderPOList);

        // 按产品skuId分组
        Map<Long, List<OutStockOrderItemPO>> skuMap =
            outStockOrderItemPOList.stream().collect(Collectors.groupingBy(p -> p.getSkuid()));

        // 查询可用库存
        List<Long> productSkuIds = new ArrayList<>(skuMap.keySet());
        Map<Long, BigDecimal> enableStoreMap =
            iWarehouseInventoryQueryService.getEnableStoreCountMap(cityId, warehouseId, productSkuIds);
        LOGGER.info("查询出可用库存：{}", JSON.toJSONString(enableStoreMap));

        // 找出缺货产品（可用库存 < 订单项的产品数量）
        List<Long> productSkuIdList = new ArrayList<>();
        skuMap.forEach((skuId, itemList) -> {
            // 订单项的产品数量
            BigDecimal orderCount =
                itemList.stream().map(p -> p.getUnittotalcount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (enableStoreMap.get(skuId) == null || enableStoreMap.get(skuId).compareTo(orderCount) < 0) {
                productSkuIdList.add(skuId);
                LOGGER.info("缺货产品skuId: {}, 可用库存: {}, 订单数量: {}", skuId, enableStoreMap.get(skuId), orderCount);
            }
        });
        return productSkuIdList;
    }

    /**
     * 获取所有订单项
     * 
     * @param outStockOrderPOList
     * @return
     */
    private List<OutStockOrderItemPO> getOutStockOrderItemPOS(List<OutStockOrderPO> outStockOrderPOList) {
        // 获取所有订单项
        List<OutStockOrderItemPO> outStockOrderItemPOList = new ArrayList<>();
        outStockOrderPOList.forEach(order -> {
            if (CollectionUtils.isNotEmpty(order.getItems())) {
                outStockOrderItemPOList.addAll(order.getItems());
            }
        });
        return outStockOrderItemPOList;
    }

    /**
     * 根据优先级分配订单 城市优先级 -> 线路优先级
     * 
     * @return
     */
    public List<OutStockOrderPO> listOutStockOrderByAllotPriority(Integer cityId, Integer warehouseId,
        BatchAllotPriorityDTO allotPriority, List<OutStockOrderPO> outStockOrderPOList) {
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return null;
        }
        long startTime = System.currentTimeMillis();

        // 查询可用库存
        Set<Long> setProductSkuIds = new HashSet<>();
        outStockOrderPOList.forEach(order -> {
            if (CollectionUtils.isNotEmpty(order.getItems())) {
                order.getItems().forEach(item -> {
                    setProductSkuIds.add(item.getSkuid());
                });
            }
        });
        Map<Long, BigDecimal> enableStoreMap = iWarehouseInventoryQueryService.getEnableStoreCountMap(cityId,
            warehouseId, new ArrayList<>(setProductSkuIds));

        // 1、按城市优先级分配订单
        if (CollectionUtils.isNotEmpty(allotPriority.getCityList())) {
            allotPriority.getCityList().forEach(city -> {
                // 获取当前城市下所有订单
                List<OutStockOrderPO> cityOrderList = outStockOrderPOList.stream()
                    .filter(p -> Objects.equals(p.getCity(), city)).collect(Collectors.toList());

                // 2、按线路优先级分配订单
                if (CollectionUtils.isNotEmpty(allotPriority.getRouteList())) {
                    allotPriority.getRouteList().forEach(route -> {
                        // 当前城市下指定线路的订单
                        List<OutStockOrderPO> routeOrderList = cityOrderList.stream()
                            .filter(p -> Objects.equals(p.getRouteName(), route)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(routeOrderList)) {
                            allotOrderItem(enableStoreMap, routeOrderList);
                        }
                    });
                } else {
                    allotOrderItem(enableStoreMap, cityOrderList);
                }
            });
        }

        // 3、找出已分配的所有订单
        List<OutStockOrderPO> allotOutStockOrderList = new ArrayList<>();
        List<OutStockOrderItemPO> notAllotOutStockOrderItemList = new ArrayList<>();
        outStockOrderPOList.forEach(order -> {
            List<OutStockOrderItemPO> allotOutStockOrderItemList = new ArrayList<>();
            order.getItems().forEach(item -> {
                // 未分配
                if (item.getIsAllot() == null) {
                    notAllotOutStockOrderItemList.add(item);
                    // 已分配
                } else {
                    allotOutStockOrderItemList.add(item);
                }
            });
            if (CollectionUtils.isNotEmpty(allotOutStockOrderItemList)) {
                order.setItems(allotOutStockOrderItemList);
                allotOutStockOrderList.add(order);
            }
        });

        long endTime = System.currentTimeMillis();
        LOGGER.info("未分配的订单项：{}, 总计：{}条, 耗时：{}ms", JSON.toJSONString(notAllotOutStockOrderItemList),
            notAllotOutStockOrderItemList.size(), endTime - startTime);

        return allotOutStockOrderList;
    }

    /**
     * 分配订单（库存足够就分配，否则不分配）
     * 
     * @param enableStoreMap
     * @param citySortList
     */
    private void allotOrderItem(Map<Long, BigDecimal> enableStoreMap, List<OutStockOrderPO> outStockOrderPOList) {
        outStockOrderPOList.forEach(order -> {
            order.getItems().forEach(item -> {
                // 未分配的订单项
                if (item.getIsAllot() == null) {
                    BigDecimal count = enableStoreMap.get(item.getSkuid()) != null ? enableStoreMap.get(item.getSkuid())
                        : BigDecimal.ZERO;
                    // 当库存足够时，标示已分配
                    if (item.getUnittotalcount().compareTo(count) <= 0) {
                        item.setIsAllot(true);
                        enableStoreMap.put(item.getSkuid(), count.subtract(item.getUnittotalcount()));
                    }
                }
            });
        });
    }

}
