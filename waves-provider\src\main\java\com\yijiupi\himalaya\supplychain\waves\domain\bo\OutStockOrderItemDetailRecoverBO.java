package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/27
 */
public class OutStockOrderItemDetailRecoverBO {

    private List<OutStockOrderPO> outStockOrderPOS;

    private List<OrderItemTaskInfoPO> orderItemTaskInfoPOList;

    private Integer warehouseId;

    /**
     * 是否处理item和detail数量相等的数据
     */
    private boolean handleEqualCountItem = Boolean.FALSE;
    /**
     * 是否强制使用订单中台的数据创建detail
     */
    private boolean useOrderCenterDetail = Boolean.FALSE;
    /**
     * 是否用库存记录创建detail
     */
    private boolean useInventoryRecord = Boolean.TRUE;

    /**
     * 获取
     *
     * @return warehouseId
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置
     *
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 是否处理item和detail数量相等的数据
     *
     * @return handleEqualCountItem 是否处理item和detail数量相等的数据
     */
    public boolean isHandleEqualCountItem() {
        return this.handleEqualCountItem;
    }

    /**
     * 设置 是否处理item和detail数量相等的数据
     *
     * @param handleEqualCountItem 是否处理item和detail数量相等的数据
     */
    public void setHandleEqualCountItem(boolean handleEqualCountItem) {
        this.handleEqualCountItem = handleEqualCountItem;
    }

    /**
     * 获取 是否用订单中台的数据创建
     *
     * @return useOrderCenterDetail 是否用订单中台的数据创建
     */
    public boolean isUseOrderCenterDetail() {
        return this.useOrderCenterDetail;
    }

    /**
     * 设置 是否用订单中台的数据创建
     *
     * @param useOrderCenterDetail 是否用订单中台的数据创建
     */
    public void setUseOrderCenterDetail(boolean useOrderCenterDetail) {
        this.useOrderCenterDetail = useOrderCenterDetail;
    }

    /**
     * 获取 是否用库存记录创建detail
     *
     * @return useInventoryRecord 是否用库存记录创建detail
     */
    public boolean isUseInventoryRecord() {
        return this.useInventoryRecord;
    }

    /**
     * 设置 是否用库存记录创建detail
     *
     * @param useInventoryRecord 是否用库存记录创建detail
     */
    public void setUseInventoryRecord(boolean useInventoryRecord) {
        this.useInventoryRecord = useInventoryRecord;
    }

    /**
     * 获取
     *
     * @return outStockOrderPOS
     */
    public List<OutStockOrderPO> getOutStockOrderPOS() {
        return this.outStockOrderPOS;
    }

    /**
     * 设置
     *
     * @param outStockOrderPOS
     */
    public void setOutStockOrderPOS(List<OutStockOrderPO> outStockOrderPOS) {
        this.outStockOrderPOS = outStockOrderPOS;
    }

    /**
     * 获取
     *
     * @return orderItemTaskInfoPOList
     */
    public List<OrderItemTaskInfoPO> getOrderItemTaskInfoPOList() {
        return this.orderItemTaskInfoPOList;
    }

    /**
     * 设置
     *
     * @param orderItemTaskInfoPOList
     */
    public void setOrderItemTaskInfoPOList(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        this.orderItemTaskInfoPOList = orderItemTaskInfoPOList;
    }
}
