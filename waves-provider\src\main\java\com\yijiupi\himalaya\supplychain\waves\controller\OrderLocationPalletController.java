package com.yijiupi.himalaya.supplychain.waves.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IOrderLocationPalletService;
import com.yijiupi.himalaya.supplychain.waves.batch.IOrderLocationPalletManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderpallet.OrderLocationPalletBL;
import com.yijiupi.himalaya.supplychain.waves.dto.base.BaseResult;
import com.yijiupi.himalaya.supplychain.waves.dto.base.ROResult;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.BatchOrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletQueryDTO;
import com.yijiupi.supplychain.serviceutils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 订单货位托盘关系
 *
 * <AUTHOR>
 * @since 2024年11月27
 */
@RestController
public class OrderLocationPalletController {

    @Autowired
    private OrderLocationPalletBL orderLocationPalletBL;

    /**
     * 根据订单id新增订单货位托盘关系
     */
    @PostMapping("/productlocation/addPalletByOrderId")
    public BaseResult addPalletByOrderId(@RequestBody OrderLocationPalletDTO dto) {
        dto.setLastUpdateUser(String.valueOf(ThreadLocalUtil.getCurrentUserInfo()));
        orderLocationPalletBL.addPalletByOrderId(dto);
        return BaseResult.getSuccessResult();
    }

    /**
     * 查询订单货位托盘关系
     */
    @PostMapping("/productlocation/findPalletByCondition")
    public BaseResult findPalletByCondition(@RequestBody OrderLocationPalletQueryDTO dto) {
        return new ROResult<>(orderLocationPalletBL.findPalletByCondition(dto));
    }

    /**
     * 删除订单货位托盘关系
     */
    @PostMapping("/productlocation/deletePalletByCondition")
    public BaseResult deletePalletByCondition(@RequestBody OrderLocationPalletQueryDTO dto) {
        orderLocationPalletBL.deletePalletByCondition(dto);
        return BaseResult.getSuccessResult();
    }

    /**
     * 根据订单id新增订单货位托盘关系
     */
    @PostMapping("/productlocation/addPalletByOrderIdBatch")
    public BaseResult addPalletByOrderIdBatch(@RequestBody BatchOrderLocationPalletDTO dto) {
        dto.setOptUserId(ThreadLocalUtil.getCurrentUserInfo());
        orderLocationPalletBL.addPalletByOrderIdBatch(dto);
        return BaseResult.getSuccessResult();
    }
}
