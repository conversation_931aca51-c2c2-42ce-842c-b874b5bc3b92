package com.yijiupi.himalaya.supplychain.waves.domain.bl.itemdetail;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.secowner.OrderWithItemOwnersItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.OutStockOrderItemDetailRecoverBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.OutStockOrderItemDetailRecoverHandleByItemBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.OutStockOrderItemDetailConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 已生成波次，未出库。根据orderItemTaskInfo修复detail
 * 
 * <AUTHOR>
 * @date 2024/9/27
 */
@Service
public class OutStockOrderItemDetailRecoverHaveBatchBL extends OutStockOrderItemDetailRecoverBaseBL {

    private static final List<Byte> WAIT_ORDER_STATE =
        Arrays.asList(OutStockOrderStateEnum.待拣货.getType(), OutStockOrderStateEnum.拣货中.getType());

    @Override
    List<OutStockOrderPO> doFilterOrderList(OutStockOrderItemDetailRecoverBO bo) {
        List<OutStockOrderPO> orderList = bo.getOutStockOrderPOS().stream()
            .filter(m -> WAIT_ORDER_STATE.contains(m.getState().byteValue()) || isSupport(m, bo)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyList();
        }

        return orderList;
    }

    private boolean isSupport(OutStockOrderPO outStockOrderPO, OutStockOrderItemDetailRecoverBO bo) {
        if (outStockOrderPO.getState().byteValue() != OutStockOrderStateEnum.已出库.getType()) {
            return Boolean.FALSE;
        }
        if (BooleanUtils.isFalse(bo.isUseInventoryRecord())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    public List<OutStockOrderItemDetailPO> doRecover(List<OutStockOrderItemDetailRecoverHandleByItemBO> itemBOList) {
        List<OutStockOrderPO> outStockOrderPOS = itemBOList.stream()
            .map(OutStockOrderItemDetailRecoverHandleByItemBO::getOutStockOrderPO).collect(Collectors.toList());

        List<OutStockOrderItemPO> needUpdateItemList = itemBOList.stream().map(this::getNeedHandleItemList)
            .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        // key 是 outStockOrderItemId
        Map<Long, OrderWithItemOwnersItemDTO> orderWithItemOwnersItemDTOMap =
            getOrderCenterSecOwnerByOrderIds(outStockOrderPOS, needUpdateItemList);

        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            itemBOList.stream().filter(m -> CollectionUtils.isNotEmpty(m.getOrderItemTaskInfoPOList()))
                .flatMap(m -> m.getOrderItemTaskInfoPOList().stream()).collect(Collectors.toList());

        Map<Long, List<OrderItemTaskInfoPO>> orderItemTaskInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderItemTaskInfoPOList)) {
            orderItemTaskInfoMap.putAll(orderItemTaskInfoPOList.stream()
                .collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId)));
        }

        return needUpdateItemList.stream().map(item -> {
            OrderWithItemOwnersItemDTO itemOwnersItemDTO = orderWithItemOwnersItemDTOMap.get(item.getId());
            List<OrderItemTaskInfoPO> orderItemTaskInfoPOS = orderItemTaskInfoMap.get(item.getId());

            List<OutStockOrderItemDetailPO> detailPOS = genDetail(item, orderItemTaskInfoPOS, itemOwnersItemDTO);
            return detailPOS;
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<OutStockOrderItemDetailPO> genDetail(OutStockOrderItemPO itemPO,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, OrderWithItemOwnersItemDTO orderWithItemOwnersItemDTO) {
        // 如果orderItemTaskInfoPOList，用中台数据生成detail
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return genDetailWithOrderCenterInfo(orderWithItemOwnersItemDTO, itemPO);
        }

        List<OutStockOrderItemDetailPO> outStockOrderItemDetailPOS = itemPO.getItemDetails();
        // 如果出库单项的detail不存在，走新增逻辑
        if (CollectionUtils.isEmpty(outStockOrderItemDetailPOS)) {
            return OutStockOrderItemDetailConverter.convertNewOutStockOrderItemDetail(itemPO, orderItemTaskInfoPOList);
        }

        // 如果不相等，这里需要改orderItemTaskInfo的数据了。把判断移到生成波次那里，如果detail不一致，不能生成波次
        // BigDecimal unitTotalCount = orderItemTaskInfoPOList.stream()
        // .filter(m -> CollectionUtils.isNotEmpty(m.getDetailList())).flatMap(m -> m.getDetailList().stream())
        // .map(OrderItemTaskInfoDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        //
        // if (unitTotalCount.compareTo(itemPO.getUnittotalcount()) != 0) {
        //
        // }
        //
        return OutStockOrderItemDetailConverter.convertNewOutStockOrderItemDetail(itemPO, orderItemTaskInfoPOList);
    }

}
