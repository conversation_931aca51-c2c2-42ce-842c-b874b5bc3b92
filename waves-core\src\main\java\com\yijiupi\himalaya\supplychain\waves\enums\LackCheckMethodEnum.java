package com.yijiupi.himalaya.supplychain.waves.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

public enum LackCheckMethodEnum {
    /**
     * 枚举
     */
    不校验((byte)0), 二维码校验((byte)1), 人脸识别校验((byte)2);

    /**
     * type
     */
    private byte type;

    LackCheckMethodEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     * 
     * @param value
     * @return
     */
    public static String getEnumByValue(Byte value) {
        LackCheckMethodEnum lackCheckMethodEnum = null;
        if (value != null) {
            lackCheckMethodEnum = cache.get(value);
        }
        return lackCheckMethodEnum == null ? null : lackCheckMethodEnum.name();
    }

    private static Map<Byte, LackCheckMethodEnum> cache =
        EnumSet.allOf(LackCheckMethodEnum.class).stream().collect(Collectors.toMap(p -> p.type, p -> p));
}
