package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;

/**
 * @description
 * <AUTHOR>
 * @Date 2021/7/8 11:52
 */
public class LackCheckMethodQueryDTO implements Serializable {
    private static final long serialVersionUID = -4880296044801375730L;
    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 产品skuId
     */
    private String skuId;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }
}
