package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * <AUTHOR>
 * @date 2019/11/25 10:49
 */
public enum PackageAttributeEnum {
    /**
     * 枚举
     */
    大件订单((byte)1), 小件订单((byte)2), 快递直发((byte)3), 统采优先((byte)4);

    /**
     * type
     */
    private Byte type;

    PackageAttributeEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    public static String getType(Byte type) {
        if (null == type) {
            return null;
        }
        for (PackageAttributeEnum attributeEnum : PackageAttributeEnum.values()) {
            if (attributeEnum.getType() == type) {
                return attributeEnum.toString();
            }
        }
        return null;
    }
}
