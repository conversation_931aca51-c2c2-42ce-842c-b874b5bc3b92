<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.GatherTaskLocationMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskLocationPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Org_Id" property="orgId" jdbcType="INTEGER"/>
        <result column="Business_Id" property="businessId" jdbcType="BIGINT"/>
        <result column="GatherTask_Id" property="gatherTaskId" jdbcType="BIGINT"/>
        <result column="GatherTaskProduct_Id" property="gatherTaskProductId" jdbcType="BIGINT"/>
        <result column="TaskNumber" property="taskNumber" jdbcType="VARCHAR"/>
        <result column="Car_Id" property="carId" jdbcType="BIGINT"/>
        <result column="CarName" property="carName" jdbcType="VARCHAR"/>
        <result column="LicensePlate" property="licensePlate" jdbcType="VARCHAR"/>
        <result column="Location_Id" property="locationId" jdbcType="BIGINT"/>
        <result column="LocationName" property="locationName" jdbcType="VARCHAR"/>
        <result column="GatherType" property="gatherType" jdbcType="TINYINT"/>
        <result column="GatherType_Id" property="gatherTypeId" jdbcType="BIGINT"/>
        <result column="GatherTypeName" property="gatherTypeName" jdbcType="VARCHAR"/>
        <result column="DriverName" property="driverName" jdbcType="VARCHAR"/>
        <result column="SpecQuantity" property="specQuantity" jdbcType="DECIMAL"/>
        <result column="TotalCount" property="totalCount" jdbcType="DECIMAL"/>
        <result column="GatherCount" property="gatherCount" jdbcType="DECIMAL"/>
        <result column="Status" property="status" jdbcType="TINYINT"/>
        <result column="CreateUser" property="createUser" jdbcType="BIGINT"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="BIGINT"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Org_Id,Business_Id, GatherTask_Id, GatherTaskProduct_Id, TaskNumber, Car_Id, CarName, LicensePlate,
        Location_Id, LocationName, GatherType, GatherType_Id, GatherTypeName, DriverName,
        SpecQuantity, TotalCount, GatherCount, Status, CreateUser, CreateTime, LastUpdateUser,
        LastUpdateTime
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from gathertasklocation
        where Id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectLocaltion"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskLocationScaleDTO">
        SELECT
        l.GatherTask_Id gatherTaskId,
        l.Location_Id locationId,
        l.LocationName locationName,
        l.TaskNumber taskNumber,
        l.LicensePlate licensePlate,
        l.DriverName driverName,
        group_concat(l.GatherTaskProduct_Id SEPARATOR '_') gatherTaskProductIds,
        CONCAT_WS('/',sum(l.`Status`),count(1)) statusScale
        FROM
        gathertasklocation l
        LEFT JOIN gathertask g ON l.GatherTask_Id = g.Id
        WHERE
        g.BatchTask_Id = #{so.batchTaskId,jdbcType=VARCHAR}
        <if test="so.orgId!=null">
            and l.Org_Id=#{so.orgId,jdbcType=INTEGER}
        </if>
        <if test="so.locationId!=null">
            and l.Location_Id=#{so.locationId,jdbcType=BIGINT}
        </if>
        <if test="so.locationName!=null">
            and l.LocationName like concat('%',#{so.locationName,jdbcType=VARCHAR},'%')
        </if>
        GROUP BY
        l.GatherTask_Id,
        l.Location_Id,
        l.LocationName,
        l.TaskNumber,
        l.LicensePlate,
        l.DriverName
    </select>
    <select id="selectLocaltionDetail"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskProductScaleDTO">
        SELECT
        l.Id id,
        p.ProductName productName,
        p.ProductSpecName productSpecName,
        l.SpecQuantity specQuantity,
        l.TotalCount totalCount,
        l.Status status
        FROM
        gathertasklocation l
        LEFT JOIN gathertaskproduct p ON l.GatherTaskProduct_Id = p.Id
        WHERE
        l.Location_Id = #{locationId,jdbcType=BIGINT}
        <if test="orgId!=null">
            and l.Org_Id=#{orgId,jdbcType=BIGINT}
        </if>
        AND l.GatherTaskProduct_Id IN
        <foreach collection="gatherTaskProductIds" item="gatherTaskProductId" separator="," open="(" close=")">
            #{gatherTaskProductId,jdbcType=VARCHAR}
        </foreach>
        order by status desc
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from gathertasklocation
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskLocationPO">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into gathertasklocation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">
                Org_Id,
            </if>
            <if test="businessId != null">
                Business_Id,
            </if>
            <if test="gatherTaskId != null">
                GatherTask_Id,
            </if>
            <if test="gatherTaskProductId != null">
                GatherTaskProduct_Id,
            </if>
            <if test="taskNumber != null">
                TaskNumber,
            </if>
            <if test="carId != null">
                Car_Id,
            </if>
            <if test="carName != null">
                CarName,
            </if>
            <if test="licensePlate != null">
                LicensePlate,
            </if>
            <if test="locationId != null">
                Location_Id,
            </if>
            <if test="locationName != null">
                LocationName,
            </if>
            <if test="gatherType != null">
                GatherType,
            </if>
            <if test="gatherTypeId != null">
                GatherType_Id,
            </if>
            <if test="gatherTypeName != null">
                GatherTypeName,
            </if>
            <if test="driverName != null">
                DriverName,
            </if>
            <if test="specQuantity != null">
                SpecQuantity,
            </if>
            <if test="totalCount != null">
                TotalCount,
            </if>
            <if test="gatherCount != null">
                GatherCount,
            </if>
            <if test="status != null">
                Status,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="businessId != null">
                #{businessId,jdbcType=INTEGER},
            </if>
            <if test="gatherTaskId != null">
                #{gatherTaskId,jdbcType=BIGINT},
            </if>
            <if test="gatherTaskProductId != null">
                #{gatherTaskProductId,jdbcType=BIGINT},
            </if>
            <if test="taskNumber != null">
                #{taskNumber,jdbcType=VARCHAR},
            </if>
            <if test="carId != null">
                #{carId,jdbcType=BIGINT},
            </if>
            <if test="carName != null">
                #{carName,jdbcType=VARCHAR},
            </if>
            <if test="licensePlate != null">
                #{licensePlate,jdbcType=VARCHAR},
            </if>
            <if test="locationId != null">
                #{locationId,jdbcType=BIGINT},
            </if>
            <if test="locationName != null">
                #{locationName,jdbcType=VARCHAR},
            </if>
            <if test="gatherType != null">
                #{gatherType,jdbcType=TINYINT},
            </if>
            <if test="gatherTypeId != null">
                #{gatherTypeId,jdbcType=BIGINT},
            </if>
            <if test="gatherTypeName != null">
                #{gatherTypeName,jdbcType=VARCHAR},
            </if>
            <if test="driverName != null">
                #{driverName,jdbcType=VARCHAR},
            </if>
            <if test="specQuantity != null">
                #{specQuantity,jdbcType=DECIMAL},
            </if>
            <if test="totalCount != null">
                #{totalCount,jdbcType=DECIMAL},
            </if>
            <if test="gatherCount != null">
                #{gatherCount,jdbcType=DECIMAL},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                now(),
            </if>
            <if test="lastUpdateUser != null">
                #{createUser,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateTime != null">
                now(),
            </if>
        </trim>
    </insert>
    <insert id="insertList">
        insert into gathertasklocation (
        Id,
        Org_Id,
        Business_Id,
        GatherTask_Id,
        GatherTaskProduct_Id,
        TaskNumber,
        Car_Id,
        CarName,
        LicensePlate,
        Location_Id,
        LocationName,
        GatherType,
        GatherType_Id,
        GatherTypeName,
        DriverName,
        SpecQuantity,
        TotalCount,
        GatherCount,
        CreateUser,
        CreateTime,
        LastUpdateUser,
        LastUpdateTime
        )VALUES
        <foreach collection="pos" item="po" index="index" separator=",">
            (
            #{po.id,jdbcType=BIGINT},
            #{po.orgId,jdbcType=INTEGER},
            #{po.businessId,jdbcType=BIGINT},
            #{po.gatherTaskId,jdbcType=BIGINT},
            #{po.gatherTaskProductId,jdbcType=BIGINT},
            #{po.taskNumber,jdbcType=VARCHAR},
            #{po.carId,jdbcType=BIGINT},
            #{po.carName,jdbcType=VARCHAR},
            #{po.licensePlate,jdbcType=VARCHAR},
            #{po.locationId,jdbcType=BIGINT},
            #{po.locationName,jdbcType=VARCHAR},
            #{po.gatherType,jdbcType=TINYINT},
            #{po.gatherTypeId,jdbcType=BIGINT},
            #{po.gatherTypeName,jdbcType=VARCHAR},
            #{po.driverName,jdbcType=VARCHAR},
            #{po.specQuantity,jdbcType=DECIMAL},
            #{po.totalCount,jdbcType=DECIMAL},
            #{po.gatherCount,jdbcType=DECIMAL},
            #{po.createUser,jdbcType=BIGINT},
            now(),
            #{po.createUser,jdbcType=BIGINT},
            now()
            )
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskLocationPO">
        update gathertasklocation
        <set>
            <if test="businessId != null">
                Business_Id = #{businessId,jdbcType=INTEGER},
            </if>
            <if test="gatherTaskId != null">
                GatherTask_Id = #{gatherTaskId,jdbcType=BIGINT},
            </if>
            <if test="gatherTaskProductId != null">
                GatherTaskProduct_Id = #{gatherTaskProductId,jdbcType=BIGINT},
            </if>
            <if test="taskNumber != null">
                TaskNumber = #{taskNumber,jdbcType=VARCHAR},
            </if>
            <if test="carId != null">
                Car_Id = #{carId,jdbcType=BIGINT},
            </if>
            <if test="carName != null">
                CarName = #{carName,jdbcType=VARCHAR},
            </if>
            <if test="licensePlate != null">
                LicensePlate = #{licensePlate,jdbcType=VARCHAR},
            </if>
            <if test="locationId != null">
                Location_Id = #{locationId,jdbcType=BIGINT},
            </if>
            <if test="locationName != null">
                LocationName = #{locationName,jdbcType=VARCHAR},
            </if>
            <if test="gatherType != null">
                GatherType = #{gatherType,jdbcType=TINYINT},
            </if>
            <if test="gatherTypeId != null">
                GatherType_Id = #{gatherTypeId,jdbcType=BIGINT},
            </if>
            <if test="gatherTypeName != null">
                GatherTypeName = #{gatherTypeName,jdbcType=VARCHAR},
            </if>
            <if test="driverName != null">
                DriverName = #{driverName,jdbcType=VARCHAR},
            </if>
            <if test="specQuantity != null">
                SpecQuantity = #{specQuantity,jdbcType=DECIMAL},
            </if>
            <if test="totalCount != null">
                TotalCount = #{totalCount,jdbcType=DECIMAL},
            </if>
            <if test="gatherCount != null">
                GatherCount = #{gatherCount,jdbcType=DECIMAL},
            </if>
            <if test="status != null">
                Status = #{status,jdbcType=TINYINT},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = now(),
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateGatherTaskLocationStatus"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskLocationPO">
        update gathertasklocation set
        Status = 1,
        GatherCount = TotalCount,
        LastUpdateUser = #{lastUpdateUser,jdbcType=BIGINT},
        LastUpdateTime = now()
        where Id = #{id,jdbcType=BIGINT}
        <if test="orgId!=null">
            and Org_Id=#{orgId,jdbcType=INTEGER}
        </if>
    </update>
    <select id="selectStatusZeroCount" resultType="java.lang.Integer">
        select
        count(1)
        from gathertasklocation
        where status=0
        and GatherTask_Id = #{gatherTaskId,jdbcType=BIGINT}
        and Org_Id=#{orgId,jdbcType=INTEGER}
    </select>
</mapper>