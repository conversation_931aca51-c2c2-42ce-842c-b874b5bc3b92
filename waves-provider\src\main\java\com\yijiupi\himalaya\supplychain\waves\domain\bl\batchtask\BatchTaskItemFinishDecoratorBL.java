package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskFinishHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishNeedOpBO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/12
 */
public abstract class BatchTaskItemFinishDecoratorBL implements Ordered {

    public final static int MIDDLE_PRECEDENCE = 500;

    protected static final Logger LOGGER = LoggerFactory.getLogger(BatchTaskItemFinishDecoratorBL.class);

    protected void completeBatchTaskItem(BatchTaskItemFinishNeedOpBO bo,
        BatchTaskFinishHelperBO batchTaskFinishHelperBO) {
        List<BatchTaskItemDTO> needCompleteBatchTaskItemList =
            getNeedCompleteBatchTaskItemList(batchTaskFinishHelperBO);
        if (CollectionUtils.isEmpty(needCompleteBatchTaskItemList)) {
            return;
        }
        doCompleteBatchTaskItem(bo, batchTaskFinishHelperBO, needCompleteBatchTaskItemList);
    }

    protected abstract void doCompleteBatchTaskItem(BatchTaskItemFinishNeedOpBO bo,
        BatchTaskFinishHelperBO batchTaskFinishHelperBO, List<BatchTaskItemDTO> needCompleteBatchTaskItemList);

    public static List<BatchTaskItemDTO>
        getNeedCompleteBatchTaskItemList(BatchTaskFinishHelperBO batchTaskFinishHelperBO) {
        List<BatchTaskItemDTO> totalBatchTaskItemList = batchTaskFinishHelperBO.getTotalBatchTaskItemList();

        Map<String, BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOMap =
            batchTaskFinishHelperBO.getBatchTaskItemCompleteDTOMap();
        List<BatchTaskItemDTO> needFinishBatchTaskItemList = totalBatchTaskItemList.stream()
            .filter(m -> Objects.nonNull(batchTaskItemCompleteDTOMap.get(m.getId()))).collect(Collectors.toList());

        return needFinishBatchTaskItemList;
    }

    @Override
    public int getOrder() {
        return MIDDLE_PRECEDENCE;
    }
}
