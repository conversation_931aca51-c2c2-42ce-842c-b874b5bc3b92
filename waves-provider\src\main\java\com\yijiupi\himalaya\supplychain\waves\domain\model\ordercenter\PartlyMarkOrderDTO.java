package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.io.Serializable;
import java.util.List;

/**
 * 订单标记数据模型
 *
 * <AUTHOR>
 * @Date 2022/5/19
 */
public class PartlyMarkOrderDTO implements Serializable {
    private static final long serialVersionUID = 66009920658239037L;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 标记类型 1-缺货发货，10-全部配送，11-部分配送，12-配送失败，13-延迟配送
     */
    private Integer markType;

    /**
     * 订单重算数据明细
     */
    private List<PartlyMarkOrderItemDTO> items;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Integer getMarkType() {
        return markType;
    }

    public void setMarkType(Integer markType) {
        this.markType = markType;
    }

    public List<PartlyMarkOrderItemDTO> getItems() {
        return items;
    }

    public void setItems(List<PartlyMarkOrderItemDTO> items) {
        this.items = items;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
