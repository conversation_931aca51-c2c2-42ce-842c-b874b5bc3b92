package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;
import java.math.BigDecimal;

public class SowTaskItemUpdateDTO implements Serializable {

    /**
     * 播种任务明细id
     */
    private Long sowTaskItemId;

    /**
     * 大件数量
     */
    private BigDecimal packageCount;

    /**
     * 小件数量
     */
    private BigDecimal unitCount;

    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 已播种小单位总数量
     */
    private BigDecimal overUnitTotalCount;

    /**
     * 播种任务明细状态
     */
    private Byte state;
    /**
     * 缺货小单位总数量
     */
    private BigDecimal lackUnitTotalCount;
    /**
     * 最后更新人
     */
    private String lastUpdateUser;

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public BigDecimal getLackUnitTotalCount() {
        return lackUnitTotalCount;
    }

    public void setLackUnitTotalCount(BigDecimal lackUnitTotalCount) {
        this.lackUnitTotalCount = lackUnitTotalCount;
    }

    public BigDecimal getOverUnitTotalCount() {
        return overUnitTotalCount;
    }

    public void setOverUnitTotalCount(BigDecimal overUnitTotalCount) {
        this.overUnitTotalCount = overUnitTotalCount;
    }

    public Long getSowTaskItemId() {
        return sowTaskItemId;
    }

    public void setSowTaskItemId(Long sowTaskItemId) {
        this.sowTaskItemId = sowTaskItemId;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }
}
