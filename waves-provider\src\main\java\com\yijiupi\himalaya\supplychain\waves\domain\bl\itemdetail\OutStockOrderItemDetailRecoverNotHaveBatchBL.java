package com.yijiupi.himalaya.supplychain.waves.domain.bl.itemdetail;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.secowner.OrderWithItemOwnersItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.OutStockOrderItemDetailRecoverBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.OutStockOrderItemDetailRecoverHandleByItemBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 未生成波次，根据中台数据，修复detail
 * 
 * <AUTHOR>
 * @date 2024/9/27
 */
@Service
public class OutStockOrderItemDetailRecoverNotHaveBatchBL extends OutStockOrderItemDetailRecoverBaseBL {

    private static final List<Byte> WAIT_ORDER_STATE = Arrays.asList(OutStockOrderStateEnum.待调度.getType(),
        OutStockOrderStateEnum.待审核.getType(), OutStockOrderStateEnum.调拨中.getType());

    @Override
    List<OutStockOrderPO> doFilterOrderList(OutStockOrderItemDetailRecoverBO bo) {
        List<OutStockOrderPO> orderList = bo.getOutStockOrderPOS().stream()
            .filter(m -> WAIT_ORDER_STATE.contains(m.getState().byteValue())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyList();
        }

        return orderList;
    }

    @Override
    public List<OutStockOrderItemDetailPO> doRecover(List<OutStockOrderItemDetailRecoverHandleByItemBO> itemBOList) {
        List<OutStockOrderPO> outStockOrderPOS = itemBOList.stream()
            .map(OutStockOrderItemDetailRecoverHandleByItemBO::getOutStockOrderPO).collect(Collectors.toList());

        List<OutStockOrderItemPO> needUpdateItemList = itemBOList.stream().map(this::getNeedHandleItemList)
            .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        // key 是 outStockOrderItemId
        Map<Long, OrderWithItemOwnersItemDTO> orderWithItemOwnersItemDTOMap =
            getOrderCenterSecOwnerByOrderIds(outStockOrderPOS, needUpdateItemList);

        return needUpdateItemList.stream().map(item -> {
            OrderWithItemOwnersItemDTO itemOwnersItemDTO = orderWithItemOwnersItemDTOMap.get(item.getId());
            List<OutStockOrderItemDetailPO> detailPOS = genDetailWithOrderCenterInfo(itemOwnersItemDTO, item);
            return detailPOS;
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

}
