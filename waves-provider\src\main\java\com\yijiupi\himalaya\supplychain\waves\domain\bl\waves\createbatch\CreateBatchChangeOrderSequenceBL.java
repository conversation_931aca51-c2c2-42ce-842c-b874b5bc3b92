package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockCommManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.split.SplitBatchTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchUpdateOutStockOrderSequenceBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.SowConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 创建波次更新出库单的orderSequence
 *
 * <AUTHOR>
 * @date 2024/12/24
 */
@Service
public class CreateBatchChangeOrderSequenceBL {

    @Autowired
    private SplitBatchTaskBL splitBatchTaskBL;
    @Autowired
    private GlobalCache globalCache;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;

    @Reference
    private IOutStockCommManageService iOutStockCommManageService;

    public void updateOrderSequenceIfSplitByUser(WaveCreateDTO createDTO, List<OutStockOrderPO> orderList) {
        // 开启按客户合并分拣的，按订单拣货的更新订单序号为一样的
        // 其他情况不合并，还是按单拣货
        if (BooleanUtils.isFalse(createDTO.getWavesStrategyDTO().isPickByCustomer())) {
            return;
        }
        // 按用户分组，如果同一个用户的订单数大于1，更新订单序号为一样的
        Map<Integer, List<OutStockOrderPO>> orderMapByAddressId =
            orderList.stream().filter(p -> SowConverter.isNeedHandleSequence(p))
                .collect(Collectors.groupingBy(OutStockOrderPO::getAddressId));
        for (Map.Entry<Integer, List<OutStockOrderPO>> entry : orderMapByAddressId.entrySet()) {
            if (entry.getValue() != null && entry.getValue().size() > 1) {
                List<Long> outStockOrderIds =
                    entry.getValue().stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
                Integer orderSequence = entry.getValue().stream().map(OutStockOrderPO::getOrderSequence)
                    .max(Comparator.naturalOrder()).get();
                outStockOrderMapper.updateOrderSequenceByOrderIds(outStockOrderIds, orderSequence);
            }
        }
    }

    /**
     * 开启按客户拣货，同一个播种任务下的订单，短码更新成一致,
     *
     * @param waveCreateDTOList
     */
    public void updateOrderSequenceIfCreateSow(List<WaveCreateDTO> waveCreateDTOList) {
        if (CollectionUtils.isEmpty(waveCreateDTOList)) {
            return;
        }
        WavesStrategyBO wavesStrategyBO = waveCreateDTOList.get(0).getWavesStrategyDTO();

        if (BooleanUtils.isFalse(wavesStrategyBO.isPickByCustomer())) {
            return;
        }
        List<WaveCreateDTO> sowWaveCreateList =
                waveCreateDTOList.stream().filter(m -> Objects.nonNull(m.getSowId())).collect(Collectors.toList());

        Map<Long, List<WaveCreateDTO>> sowWaveCreateMap =
                sowWaveCreateList.stream().collect(Collectors.groupingBy(WaveCreateDTO::getSowId));

        List<BatchUpdateOutStockOrderSequenceBO> totalBOList = new ArrayList<>();
        for (Map.Entry<Long, List<WaveCreateDTO>> entry : sowWaveCreateMap.entrySet()) {
            List<BatchUpdateOutStockOrderSequenceBO> boList = genBatchUpdateOutStockOrderSequenceBO(entry.getValue());
            if (!CollectionUtils.isEmpty(boList)) {
                totalBOList.addAll(boList);
            }
        }
        totalBOList.forEach(bo -> {
            outStockOrderMapper.updateOrderSequenceByOrderIds(bo.getOutStockOrderIds(), bo.getOrderSequence());
        });

    }

    private List<BatchUpdateOutStockOrderSequenceBO>
        genBatchUpdateOutStockOrderSequenceBO(List<WaveCreateDTO> waveCreateDTOList) {
        Map<Integer,
            List<OutStockOrderPO>> addressOrderMap = waveCreateDTOList.stream().flatMap(m -> m.getOrders().stream())
                .filter(m -> SowConverter.isNeedHandleSequence(m))
                .collect(Collectors.groupingBy(OutStockOrderPO::getAddressId));
        if (CollectionUtils.isEmpty(addressOrderMap)) {
            return Collections.emptyList();
        }
        List<BatchUpdateOutStockOrderSequenceBO> boList =
            addressOrderMap.values().stream().filter(m -> m.size() >= 2).map(m -> {
                List<Long> orderIds = m.stream().map(order -> order.getId()).distinct().collect(Collectors.toList());
                Integer orderSequence = m.stream().map(OutStockOrderPO::getOrderSequence).filter(Objects::nonNull)
                    .min(Comparator.naturalOrder()).get();
                BatchUpdateOutStockOrderSequenceBO bo = new BatchUpdateOutStockOrderSequenceBO();
                bo.setOrderSequence(orderSequence);
                bo.setOutStockOrderIds(orderIds);
                return bo;
            }).collect(Collectors.toList());

        return boList;
    }

}
