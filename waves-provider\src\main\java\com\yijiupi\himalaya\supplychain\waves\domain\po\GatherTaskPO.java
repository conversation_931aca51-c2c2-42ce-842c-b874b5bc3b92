/*
 * @ClassName GatherTask
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-10-30 17:42:36
 */
package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.util.Date;

public class GatherTaskPO {
    /**
     * @Fields id 集货任务ID
     */
    private Long id;
    /**
     * @Fields orgId 城市id
     */
    private Integer orgId;
    /**
     * @Fields warehouseId 仓库id
     */
    private Integer warehouseId;
    /**
     * @Fields batchTaskId 波次任务Id
     */
    private String batchTaskId;
    /**
     * @Fields batchtaskNo 波次任务编号
     */
    private String batchtaskNo;
    /**
     * @Fields taskName 集货任务名称
     */
    private String taskName;
    /**
     * @Fields status 状态 0=待完成 1=已完成
     */
    private Byte status;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields createUser 创建人
     */
    private Long createUser;
    /**
     * @Fields createTime 注册日期
     */
    private Date createTime;
    /**
     * @Fields lastUpdateUser 最后更新人
     */
    private Long lastUpdateUser;
    /**
     * @Fields lastUpdateTime 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 获取 集货任务ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 集货任务ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 城市id
     */
    public Integer getOrgId() {
        return orgId;
    }

    /**
     * 设置 城市id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 仓库id
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 波次任务Id
     */
    public String getBatchTaskId() {
        return batchTaskId;
    }

    /**
     * 设置 波次任务Id
     */
    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId == null ? null : batchTaskId.trim();
    }

    /**
     * 获取 波次任务编号
     */
    public String getBatchtaskNo() {
        return batchtaskNo;
    }

    /**
     * 设置 波次任务编号
     */
    public void setBatchtaskNo(String batchtaskNo) {
        this.batchtaskNo = batchtaskNo == null ? null : batchtaskNo.trim();
    }

    /**
     * 获取 集货任务名称
     */
    public String getTaskName() {
        return taskName;
    }

    /**
     * 设置 集货任务名称
     */
    public void setTaskName(String taskName) {
        this.taskName = taskName == null ? null : taskName.trim();
    }

    /**
     * 获取 状态 0=待完成 1=已完成
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置 状态 0=待完成 1=已完成
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置 备注
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * 获取 创建人
     */
    public Long getCreateUser() {
        return createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 注册日期
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 注册日期
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后更新人
     */
    public Long getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置 最后更新人
     */
    public void setLastUpdateUser(Long lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    /**
     * 获取 最后更新时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置 最后更新时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
}