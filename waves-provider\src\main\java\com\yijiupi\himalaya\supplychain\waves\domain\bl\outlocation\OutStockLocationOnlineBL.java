package com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/11/22
 */
@Service
public class OutStockLocationOnlineBL extends OutStockLocationBaseBL {

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    private static final Logger logger = LoggerFactory.getLogger(OutStockLocationOnlineBL.class);

    /**
     * 当前策略是否支持指定仓库
     *
     * @param warehouseId 仓库 id
     * @return 当前策略是否支持指定仓库, 默认实现为仅支持灰度仓库
     */
    @Override
    public boolean support(Integer warehouseId) {
        return false;
    }

    /**
     * 查找订单出库位<br/>
     * 代码直接抄过来
     */
    @Override
    public List<OutStockOrderLocationDTO> getOutLocation(List<OutStockOrderLocationDTO> outStockOrderLocationDTOS) {
        throw new BusinessException("NOT SUPPORT!");
    }
}
