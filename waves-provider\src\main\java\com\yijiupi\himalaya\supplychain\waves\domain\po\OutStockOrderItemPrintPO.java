package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.math.BigDecimal;

public class OutStockOrderItemPrintPO {

    /**
     * 订单id
     */
    private Long outStockOrderId;

    /**
     * 订单项大件数量
     */
    private BigDecimal packageCount;

    /**
     * 订单项小件总数量
     */
    private BigDecimal unitCount;

    /**
     * 出库位id
     */
    private Long toLocationId;

    /**
     * 出库位名称
     */
    private String toLocationName;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 播种任务状态
     */
    private Byte state;

    /**
     * 拣货任务状态
     */
    private Byte taskState;

    /**
     * 订单项id
     */
    private Long outStockOrderItemId;

    public Long getOutStockOrderId() {
        return outStockOrderId;
    }

    public void setOutStockOrderId(Long outStockOrderId) {
        this.outStockOrderId = outStockOrderId;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Byte getTaskState() {
        return taskState;
    }

    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    public Long getOutStockOrderItemId() {
        return outStockOrderItemId;
    }

    public void setOutStockOrderItemId(Long outStockOrderItemId) {
        this.outStockOrderItemId = outStockOrderItemId;
    }
}
