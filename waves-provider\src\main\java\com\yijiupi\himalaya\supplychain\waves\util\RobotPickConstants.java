package com.yijiupi.himalaya.supplychain.waves.util;

import com.google.gson.Gson;
import com.yijiupi.himalaya.supplychain.constants.WarehouseConfigConstants;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.enums.PassagePickTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.AgvPickConfigTypeConstants;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PassageRobotPickEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SowTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.PickingTypeEnum;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * <AUTHOR>
 * @title: RobotPickConstants
 * @description:
 * @date 2022-12-05 09:23
 */
public final class RobotPickConstants {

    private static final Gson GSON = new Gson();
    private static final Logger LOG = LoggerFactory.getLogger(RobotPickConstants.class);

    /**
     * 是否全局开启机器人拣货
     *
     * @param wavesStrategyDTO
     * @return
     */
    public static Boolean isGlobalRobotPickOpen(WavesStrategyBO wavesStrategyDTO) {
        if (Objects.isNull(wavesStrategyDTO.getOpenRobotPickConfig())) {
            return Boolean.FALSE;
        }
        if (!PassageRobotPickEnum.开启.getType().equals(wavesStrategyDTO.getOpenRobotPickConfig())) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    public static Boolean isPassageRobotPickOpen(WavesStrategyDTO wavesStrategyDTO, PassageDTO passageDTO) {

        if (!(wavesStrategyDTO instanceof WavesStrategyBO)) {
            return Boolean.FALSE;
        }
        WavesStrategyBO wavesStrategyBO = (WavesStrategyBO)wavesStrategyDTO;

        if (Objects.isNull(wavesStrategyBO.getOpenRobotPickConfig())) {
            return Boolean.FALSE;
        }
        if (!PassageRobotPickEnum.开启.getType().equals(wavesStrategyBO.getOpenRobotPickConfig())) {
            return Boolean.FALSE;
        }

        if (Objects.isNull(passageDTO.getIsRobotPick())
            || PassageRobotPickEnum.未开启.getType().equals(passageDTO.getIsRobotPick())) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * 生成机器人波次拣货任务，判断是否按照产品拣货，如果按照产品拣货时，判断是否开启播种作业并且是否总单分拣并且开启机器人分拣
     *
     * @param createDTO
     * @param passageDTO
     * @return
     */
    public static Byte getRobotPickType(WaveCreateDTO createDTO, PassageDTO passageDTO) {
        LOG.info("getRobotPickType 参数：{}, 通道:{}", GSON.toJson(createDTO), GSON.toJson(passageDTO));

        WavesStrategyDTO wavesStrategyDTO = createDTO.getWavesStrategyDTO();

        if (!(wavesStrategyDTO instanceof WavesStrategyBO)) {
            return BatchTaskPickPatternEnum.人工拣货.getType();
        }
        WavesStrategyBO wavesStrategyBO = (WavesStrategyBO)wavesStrategyDTO;

        if (Objects.isNull(wavesStrategyBO.getOpenRobotPickConfig())) {
            return BatchTaskPickPatternEnum.人工拣货.getType();
        }
        if (!PassageRobotPickEnum.开启.getType().equals(wavesStrategyBO.getOpenRobotPickConfig())) {
            return BatchTaskPickPatternEnum.人工拣货.getType();
        }
        if (Objects.isNull(passageDTO)) {
            return BatchTaskPickPatternEnum.人工拣货.getType();
        }
        if (PickingTypeEnum.产品拣货.getType() != passageDTO.getPickingType()) {
            return BatchTaskPickPatternEnum.人工拣货.getType();
        }
        if (Objects.isNull(passageDTO.getSowType()) || SowTypeEnum.不开启.getType() == passageDTO.getSowType()) {
            return BatchTaskPickPatternEnum.人工拣货.getType();
        }
        if (SowTypeEnum.播种墙播种.getType() != passageDTO.getSowType()) {
            return BatchTaskPickPatternEnum.人工拣货.getType();
        }
        if (Objects.isNull(passageDTO.getIsRobotPick())
            || PassageRobotPickEnum.未开启.getType().equals(passageDTO.getIsRobotPick())) {
            return BatchTaskPickPatternEnum.人工拣货.getType();
        }

        return BatchTaskPickPatternEnum.机器人拣货.getType();
    }

    public static boolean isWarehouseOpenLargePick(WavesStrategyDTO wavesStrategyDTO,
        WarehouseConfigDTO warehouseConfigDTO) {
        if (!WarehouseConfigConstants.CARGO_STOCK_ON.equals(warehouseConfigDTO.getIsOpenCargoStock())) {
            return Boolean.FALSE;
        }

        if (PassagePickTypeEnum.不开启.getType() == wavesStrategyDTO.getPassPickType()) {
            return Boolean.FALSE;
        }

        if (!ConditionStateEnum.是.getType().equals(warehouseConfigDTO.getRobotLargePick())) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    public static Byte getBatchTaskRobotPickType(WaveCreateDTO createDTO, PassageDTO passageDTO) {
        if (Objects.nonNull(passageDTO)
            && AgvPickConfigTypeConstants.AGV_DH.equals(passageDTO.getAgvPickConfigType())) {
            return BatchTaskPickPatternEnum.agv拣货.getType();
        }
        if (!BatchTaskItemLargePickPatternConstants.LARGE_PICK_SMALL_ROBOT.equals(createDTO.getLargePick())) {
            return BatchTaskPickPatternEnum.人工拣货.getType();
        }

        return getRobotPickType(createDTO, passageDTO);
    }

    /**
     * @param createDTO
     * @param passageDTO
     * @return
     */
    public static boolean isRobotPick(WaveCreateDTO createDTO, PassageDTO passageDTO) {
        Byte pickType = getRobotPickType(createDTO, passageDTO);
        if (BatchTaskPickPatternEnum.机器人拣货.getType().equals(pickType)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    /**
     * 是否开启液晶不打包
     *
     * @param warehouseConfigDTO
     * @param wavesStrategyDTO
     * @return
     */
    public static boolean openLiquidNotPackage(WarehouseConfigDTO warehouseConfigDTO,
        WavesStrategyDTO wavesStrategyDTO) {
        if (!WarehouseConfigConstants.SOW_CONTROL_TYPE_LIQUID_NOT_PACK.equals(warehouseConfigDTO.getSowControlType())) {
            return Boolean.FALSE;
        }

        if (BooleanUtils.isFalse(RobotPickConstants.isWarehouseOpenLargePick(wavesStrategyDTO, warehouseConfigDTO))) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

}
