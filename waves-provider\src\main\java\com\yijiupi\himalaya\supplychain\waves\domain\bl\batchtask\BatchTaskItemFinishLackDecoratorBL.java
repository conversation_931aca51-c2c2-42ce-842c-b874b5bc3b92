package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderconstraint.OrderConstraintCheckBL;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.dto.OrderProcessRuleConfigDTO;
import com.yijiupi.himalaya.supplychain.enums.ProcessOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.enums.ProcessRuleOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.SourceType;
import com.yijiupi.himalaya.supplychain.service.IOrderProcessRuleConfigService;
import com.yijiupi.himalaya.supplychain.util.SecOwnerIdComparator;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskFinishHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishNeedOpBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.OrderItemTaskInfoDetailLackHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.OrderItemTaskInfoDetailLackHelperBOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;

import javax.annotation.Resource;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 处理缺货数量逻辑
 * 
 * <AUTHOR>
 * @since 2024/1/15
 */
@Service
public class BatchTaskItemFinishLackDecoratorBL extends BatchTaskItemFinishDecoratorBL {

    @Resource
    private OrderConstraintCheckBL orderConstraintCheckBL;

    @Reference
    private IOrderProcessRuleConfigService iOrderProcessRuleConfigService;

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }

    @Override
    protected void doCompleteBatchTaskItem(BatchTaskItemFinishNeedOpBO bo,
        BatchTaskFinishHelperBO batchTaskFinishHelperBO, List<BatchTaskItemDTO> needCompleteBatchTaskItemList) {

        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            batchTaskFinishHelperBO.getUpdateBatchTaskItemRelateOrderItemTaskInfoPOList();

        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return;
        }
        // 拣货任务项关联的订单项明细
        List<OrderItemTaskInfoDetailPO> taskInfoDetailPOList = new ArrayList<>();
        for (OrderItemTaskInfoPO taskInfoPO : orderItemTaskInfoPOList) {
            if (CollectionUtils.isNotEmpty(taskInfoPO.getDetailList())) {
                taskInfoDetailPOList.addAll(taskInfoPO.getDetailList());
            }
        }

        List<BatchTaskItemDTO> totalBatchTaskItemList = batchTaskFinishHelperBO.getTotalBatchTaskItemList();
        Map<String, BatchTaskItemDTO> batchTaskItemDTOMap =
            totalBatchTaskItemList.stream().collect(Collectors.toMap(BatchTaskItemDTO::getId, v -> v));

        List<BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOS =
            batchTaskFinishHelperBO.getBatchTaskItemFinishBO().getBatchTaskItemCompleteDTOS();
        List<OutStockOrderPO> relatedOutStockOrderList = batchTaskFinishHelperBO.getRelatedOutStockOrderList();

        LOGGER.info("[拣货缺货前] orderItemTaskInfoPOList ：{}", JSON.toJSONString(orderItemTaskInfoPOList));
        List<OrderItemTaskInfoDetailLackHelperBO> totalBoList = new ArrayList<>();
        // 处理都是按拣货任务明细处理的
        for (BatchTaskItemCompleteDTO completeDTO : batchTaskItemCompleteDTOS) {
            BatchTaskItemDTO batchTaskItemDTO = batchTaskItemDTOMap.get(completeDTO.getId());
            List<OrderItemTaskInfoPO> currentOrderItemTaskInfoList = orderItemTaskInfoPOList.stream()
                .filter(m -> m.getBatchTaskItemId().equals(batchTaskItemDTO.getId())).collect(Collectors.toList());
            // 处理缺货
            List<OrderItemTaskInfoDetailLackHelperBO> boList = processOrderItemTaskInfoLack(completeDTO,
                batchTaskItemDTO, currentOrderItemTaskInfoList, relatedOutStockOrderList);
            if (CollectionUtils.isNotEmpty(boList)) {
                totalBoList.addAll(boList);
            }
        }

        List<OrderItemTaskInfoDetailPO> updateOrderItemTaskInfoDetailPO =
            getUpdateOrderItemTaskInfoDetailList(totalBoList);
        validateRepeat(updateOrderItemTaskInfoDetailPO);
        resetOrderItemTaskInfoDetailList(orderItemTaskInfoPOList, totalBoList);

        LOGGER.info("[拣货缺货后] orderItemTaskInfoPOList ：{}", JSON.toJSONString(orderItemTaskInfoPOList));

        List<OrderItemTaskInfoPO> updateOrderItemTaskInfoList =
            resetAndGetUpdateOrderItemTaskInfoPO(totalBoList, orderItemTaskInfoPOList);

        bo.setUpdateOrderItemTaskInfoList(updateOrderItemTaskInfoList);
        bo.setUpdateOrderItemTaskInfoDetailPOList(updateOrderItemTaskInfoDetailPO);

    }

    private List<OrderItemTaskInfoPO> resetAndGetUpdateOrderItemTaskInfoPO(
        List<OrderItemTaskInfoDetailLackHelperBO> totalBoList, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        List<OrderItemTaskInfoPO> updateOrderItemTaskInfoList = new ArrayList<>();

        // Map<Long, OrderItemTaskInfoDetailLackHelperBO> orderItemTaskInfoMap = totalBoList.stream().collect(
        // Collectors.toMap(OrderItemTaskInfoDetailLackHelperBO::getOrderItemTaskInfoId, v -> v, (v1, v2) -> v1));

        // 根据关联明细项的分配数量，计算已拣货数量和缺货数量
        for (OrderItemTaskInfoPO p : orderItemTaskInfoPOList) {
            if (CollectionUtils.isNotEmpty(p.getDetailList())) {
                BigDecimal overSortCount = p.getDetailList().stream().map(OrderItemTaskInfoDetailPO::getUnitTotalCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 已拣货数量（明细项的分配数量）
                p.setOverSortCount(overSortCount);
                // 缺货数量
                p.setLackUnitCount(p.getUnitTotalCount().subtract(p.getOverSortCount()));
                // 移库数量清0
                p.setMoveCount(BigDecimal.ZERO);

                updateOrderItemTaskInfoList.add(p);
            }
        }

        return updateOrderItemTaskInfoList;
    }

    private void resetOrderItemTaskInfoDetailList(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        List<OrderItemTaskInfoDetailLackHelperBO> totalBoList) {
        if (CollectionUtils.isEmpty(totalBoList)) {
            return;
        }
        Map<Long, OrderItemTaskInfoDetailLackHelperBO> helperBOMap = totalBoList.stream()
            .collect(Collectors.toMap(OrderItemTaskInfoDetailLackHelperBO::getOrderItemTaskInfoDetailId, v -> v));
        for (OrderItemTaskInfoPO orderItemTaskInfoPO : orderItemTaskInfoPOList) {
            for (OrderItemTaskInfoDetailPO detailPO : orderItemTaskInfoPO.getDetailList()) {
                OrderItemTaskInfoDetailLackHelperBO helperBO = helperBOMap.get(detailPO.getId());
                if (Objects.isNull(helperBO)) {
                    continue;
                }

                // 保存缺货后数量
                detailPO.setUnitTotalCount(detailPO.getUnitTotalCount().subtract(helperBO.getLackCount()));
            }
        }

    }

    /**
     * @see com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL#addUpdateTaskInfoDetailList
     * @param totalBoList
     * @return FIXME
     */
    private List<OrderItemTaskInfoDetailPO>
        getUpdateOrderItemTaskInfoDetailList(List<OrderItemTaskInfoDetailLackHelperBO> totalBoList) {
        if (CollectionUtils.isEmpty(totalBoList)) {
            return Collections.emptyList();
        }
        return totalBoList.stream().map(bo -> {
            OrderItemTaskInfoDetailPO orderItemTaskInfoDetailPO = new OrderItemTaskInfoDetailPO();
            orderItemTaskInfoDetailPO.setId(bo.getOrderItemTaskInfoDetailId());
            orderItemTaskInfoDetailPO.setUnitTotalCount(bo.getUnitTotalCount());

            return orderItemTaskInfoDetailPO;
        }).collect(Collectors.toList());

    }

    private List<OrderItemTaskInfoDetailLackHelperBO> processOrderItemTaskInfoLack(BatchTaskItemCompleteDTO completeDTO,
        BatchTaskItemDTO batchTaskItemDTO, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        List<OutStockOrderPO> outStockOrderPOList) {
        BigDecimal lackCount = getLackCount(completeDTO, batchTaskItemDTO, orderItemTaskInfoPOList);

        if (lackCount.compareTo(BigDecimal.ZERO) == 0) {
            return Collections.emptyList();
        }

        List<OrderItemTaskInfoDetailLackHelperBO> boList =
            OrderItemTaskInfoDetailLackHelperBOConvertor.convert(orderItemTaskInfoPOList, outStockOrderPOList, batchTaskItemDTO);

        // FIXME 这里cityId不知道是否有值
        boList = sortOrderItemTaskInfoDetail(boList, batchTaskItemDTO.getOrgId());

        LackInfo lackInfo = new LackInfo(lackCount);

        LOGGER.info("[拣货缺货前]：{}", JSON.toJSONString(boList));
        processLack(boList, lackInfo, 0);
        LOGGER.info("[拣货缺货后]：{}", JSON.toJSONString(boList));

        if (lackInfo.lackCount.compareTo(BigDecimal.ZERO) != 0) {
            throw new BusinessValidateException("拣货任务项数量可能存在变更，请刷新重新拣货！");
        }

        return boList;
    }

    // 具体缺货逻辑
    private void processLack(List<OrderItemTaskInfoDetailLackHelperBO> helperList, LackInfo lackInfo, int i) {
        if (lackInfo.lackCount.compareTo(BigDecimal.ZERO) == 0 || i >= helperList.size()) {
            return;
        }
        BigDecimal detailUnitTotalCount = getCurrentDetailUnitTotalCount(helperList.get(i), lackInfo.lackCount);
        BigDecimal currentDetailLackCount = getCurrentDetailLackCount(lackInfo.lackCount, detailUnitTotalCount);
        lackInfo.lackCount = lackInfo.lackCount.subtract(currentDetailLackCount);
        OrderItemTaskInfoDetailLackHelperBO lackHelperBO = helperList.get(i);

        lackHelperBO.setUnitTotalCount(lackHelperBO.getUnitTotalCount().subtract(currentDetailLackCount));
        lackHelperBO.setLackCount(currentDetailLackCount);

        processLack(helperList, lackInfo, i + 1);
    }

    private BigDecimal getCurrentDetailLackCount(BigDecimal lackCount, BigDecimal detailCount) {
        BigDecimal detailLackCount = lackCount;
        if (lackCount.abs().compareTo(detailCount) > 0) {
            detailLackCount = detailCount;
            if (lackCount.compareTo(BigDecimal.ZERO) < 0) {
                detailLackCount = detailLackCount.negate();
            }
        }

        return detailLackCount;
    }

    private BigDecimal getCurrentDetailUnitTotalCount(OrderItemTaskInfoDetailLackHelperBO lackHelperBO,
        BigDecimal lackCount) {
        BigDecimal allotCount = lackHelperBO.getUnitTotalCount();
        // 缺货数量小于0，说明重复拣货，缺货数量变少了，需要增加明细项的分配数量
        if (lackCount.compareTo(BigDecimal.ZERO) < 0) {
            LOGGER.info("缺货数量小于0，说明重复拣货，缺货数量变少了，需要增加明细项的分配数量，入参:{}; 缺货数量:{}", JSON.toJSONString(lackHelperBO),
                lackCount);
            // 限制最多增加当前订单项的缺货数量
            allotCount = lackHelperBO.getOrderItemTaskInfoPO().getLackUnitCount();
        }

        return allotCount;
    }

    private BigDecimal getLackCount(BatchTaskItemCompleteDTO completeDTO, BatchTaskItemDTO batchTaskItemDTO,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        BigDecimal specQuantity = batchTaskItemDTO.getSpecQuantity();
        BigDecimal lackCount =
            completeDTO.getLackPackageCount().multiply(specQuantity).add(completeDTO.getLackUnitCount());
        // 重复拣货时，缺货移库数量 = 本次缺货数量 - 上次缺货数量
        if (TaskStateEnum.已完成.getType() == batchTaskItemDTO.getTaskState()) {
            lackCount = lackCount.subtract(batchTaskItemDTO.getLackUnitCount());
        }

        // 增加移库数量（为了后面把移库数量清0）
        BigDecimal moveCount = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getMoveCount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        lackCount = lackCount.add(moveCount);

        return lackCount;
    }

    /**
     * 缺货优先级排序
     */
    private List<OrderItemTaskInfoDetailLackHelperBO> sortOrderItemTaskInfoDetail(List<OrderItemTaskInfoDetailLackHelperBO> boList, Integer cityId) {
        List<OrderItemTaskInfoDetailLackHelperBO> meituanBoList = orderConstraintCheckBL.getCanNotLackOrderInfo(boList);
        List<OrderItemTaskInfoDetailLackHelperBO> normalBoList = orderConstraintCheckBL.getCanLackOrderInfo(boList);
        if (CollectionUtils.isEmpty(normalBoList)) {
            return boList;
        }
        if (CollectionUtils.isEmpty(meituanBoList)) {
            return sortItemList(boList, cityId);
        }
        normalBoList = sortItemList(normalBoList, cityId);
        normalBoList.addAll(meituanBoList);
        return normalBoList;
    }

    private List<OrderItemTaskInfoDetailLackHelperBO> sortItemList(List<OrderItemTaskInfoDetailLackHelperBO> boList,
        Integer cityId) {
        OrderProcessRuleConfigDTO orderProcessRuleConfigDTO = iOrderProcessRuleConfigService
            .findByOrgIdAndOrderType(cityId, ProcessRuleOrderTypeEnum.拣货缺货.getType(), true);
        if (orderProcessRuleConfigDTO == null) {
            LOGGER.info("[拣货缺货]优先级为空！");
            return boList;
        }
        ProcessOrderTypeEnum orderTypeEnum = ProcessOrderTypeEnum.getEnum(orderProcessRuleConfigDTO.getProcessType());
        LOGGER.info("[拣货缺货]优先级：{}", orderTypeEnum);
        boList = boList.stream()
            .sorted(Comparator.nullsFirst(Comparator
                .comparing(OrderItemTaskInfoDetailLackHelperBO::getSecOwnerId, new SecOwnerIdComparator(orderTypeEnum))
                .thenComparing(OrderItemTaskInfoDetailLackHelperBO::getUnitTotalCount, Comparator.reverseOrder())))
            .collect(Collectors.toList());
        return boList;
    }

    private void validateRepeat(List<OrderItemTaskInfoDetailPO> updateOrderItemTaskInfoDetailPO) {
        Map<Long, List<OrderItemTaskInfoDetailPO>> detailMap =
            updateOrderItemTaskInfoDetailPO.stream().collect(Collectors.groupingBy(OrderItemTaskInfoDetailPO::getId));
        for (Map.Entry<Long, List<OrderItemTaskInfoDetailPO>> entry : detailMap.entrySet()) {
            if (entry.getValue().size() >= 2) {
                LOGGER.warn("detail缺货数量重复：{}", JSON.toJSONString(entry.getValue()));
            }
        }
    }

    private static class LackInfo {
        BigDecimal lackCount;

        public LackInfo(BigDecimal lackCount) {
            this.lackCount = lackCount;
        }
    }
}
