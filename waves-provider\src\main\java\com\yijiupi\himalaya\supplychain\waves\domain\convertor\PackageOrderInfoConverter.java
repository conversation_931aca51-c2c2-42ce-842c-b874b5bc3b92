package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.*;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.tms.TmsApiManager;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.tms.DeliveryOrderInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemInfoDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-11-02 15:34
 **/
@Component
public class PackageOrderInfoConverter {

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    @Resource
    private OutStockOrderItemMapper outStockOrderItemMapper;

    @Resource
    private TmsApiManager tmsApiManager;

    // 兑奖配送单 和 实物兑奖单, 随便取一个就行
    private static final Map<Integer, String> ORDER_TYPES = EnumSet.allOf(OutStockOrderTypeEnum.class).stream()
            .collect(Collectors.toMap(it -> (int) it.getType(), Enum::name, (a, b) -> b));

    public PackageOrderInfoDTO toDTO(OutStockOrderPO order, OrderCommonDetailDTO orderDetail) {
        if (order == null) {
            return null;
        }
        OrderBase orderBase = orderDetail.getOrderBase();
        OrderContact orderContact = orderDetail.getOrderContact();
        OrderDelivery orderDelivery = orderDetail.getOrderDelivery();
        OrderPick orderPick = orderDetail.getOrderPick();
        Long businessId = Long.valueOf(order.getBusinessId());
        Integer warehouseId = order.getWarehouseId();
        Optional<DeliveryOrderInfoDTO> deliveryOrder = tmsApiManager.findByBusinessId(businessId, warehouseId);
        PackageOrderInfoDTO packageOrderInfoDTO = new PackageOrderInfoDTO();
        packageOrderInfoDTO.setOutStockOrderId(order.getId());
        packageOrderInfoDTO.setRefOrderNo(order.getReforderno());
        Optional.ofNullable(order.getOrdertype()).map(Integer::byteValue)
                .ifPresent(packageOrderInfoDTO::setOrderType);
        packageOrderInfoDTO.setOrderTypeName(ORDER_TYPES.get(order.getOrdertype()));
        packageOrderInfoDTO.setDriverName(orderDelivery.getDeliveryUserName());
        packageOrderInfoDTO.setCompanyName(orderContact.getContactCompanyName());
        packageOrderInfoDTO.setOrderTime(DateUtils.getDatetimeFormat(orderBase.getOrderCreateTime()));
        deliveryOrder.map(DeliveryOrderInfoDTO::getDeliverySignState).ifPresent(packageOrderInfoDTO::setOrderMark);
        deliveryOrder.map(DeliveryOrderInfoDTO::getDeliverySignStateDes).ifPresent(packageOrderInfoDTO::setOrderMarkDes);
        packageOrderInfoDTO.setContactName(orderContact.getContact());
        packageOrderInfoDTO.setContactMobile(orderContact.getContactPhone());
        deliveryOrder.map(DeliveryOrderInfoDTO::getState).ifPresent(packageOrderInfoDTO::setOrderStatus);
        deliveryOrder.map(DeliveryOrderInfoDTO::getStateDes).ifPresent(packageOrderInfoDTO::setOrderStatusDes);
        packageOrderInfoDTO.setProvince(orderContact.getProvince());
        packageOrderInfoDTO.setCity(orderContact.getCity());
        packageOrderInfoDTO.setDistrict(orderContact.getCounty());
        packageOrderInfoDTO.setDetailAddress(orderContact.getDetailAddress());
        Optional.ofNullable(orderDetail.getOrderAmount()).ifPresent(it -> {
            packageOrderInfoDTO.setPayableAmount(it.getPayableAmount());
            packageOrderInfoDTO.setDiscountAmount(it.getTotalDiscount());
        });
        Optional.ofNullable(orderPick.getWarehouseId()).map(Long::intValue)
                .map(warehouseQueryService::findWarehouseById).map(Warehouse::getName)
                .ifPresent(packageOrderInfoDTO::setSenderWarehouse);
        packageOrderInfoDTO.setPackageOrderItems(toPackageOrderItems(order.getId()));
        return packageOrderInfoDTO;
    }

    private List<PackageOrderItemInfoDTO> toPackageOrderItems(Long outStockOrderId) {
        if (outStockOrderId == null) {
            return Collections.emptyList();
        }
        List<Long> outstockorderIdList = Collections.singletonList(outStockOrderId);
        List<OutStockOrderItemPO> items = outStockOrderItemMapper.findByOutstockorderIdList(outstockorderIdList);
        return items.stream().map(it -> {
            PackageOrderItemInfoDTO packageOrderItemInfoDTO = new PackageOrderItemInfoDTO();
            packageOrderItemInfoDTO.setPackageOrderItemId(it.getId());
            packageOrderItemInfoDTO.setProductName(it.getProductname());
            packageOrderItemInfoDTO.setProductSkuId(it.getSkuid());
            packageOrderItemInfoDTO.setProductSpec(it.getSpecname());
            packageOrderItemInfoDTO.setSpecQuantity(it.getSpecquantity());
            packageOrderItemInfoDTO.setSaleSpecName(it.getSalespec());
            packageOrderItemInfoDTO.setSaleSpecQuantity(it.getSalespecquantity());
            packageOrderItemInfoDTO.setUnitTotalCount(it.getUnittotalcount());
            packageOrderItemInfoDTO.setPackageName(it.getPackagename());
            packageOrderItemInfoDTO.setUnitName(it.getUnitname());
            return packageOrderItemInfoDTO;
        }).collect(Collectors.toList());
    }

}
