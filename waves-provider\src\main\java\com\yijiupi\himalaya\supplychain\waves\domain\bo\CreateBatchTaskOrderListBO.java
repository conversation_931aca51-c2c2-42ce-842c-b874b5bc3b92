package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
public class CreateBatchTaskOrderListBO {
    /**
     * 出库单列表
     */
    private List<OutStockOrderPO> orderList;
    /**
     * 出库位id
     */
    private Long locationId;
    /**
     * 出库位名称
     */
    private String locationName;

    public CreateBatchTaskOrderListBO() {}

    public CreateBatchTaskOrderListBO(List<OutStockOrderPO> orderList) {
        this.orderList = orderList;
    }

    public static Map<String, CreateBatchTaskOrderListBO>
        getCreateBatchTaskOrderListBOMap(Map<String, List<OutStockOrderPO>> totalMap) {
        if (CollectionUtils.isEmpty(totalMap)) {
            return Collections.emptyMap();
        }

        Map<String, CreateBatchTaskOrderListBO> createBatchTaskOrderListBOMap = new HashMap<>();
        for (Map.Entry<String, List<OutStockOrderPO>> entry : totalMap.entrySet()) {
            CreateBatchTaskOrderListBO bo = new CreateBatchTaskOrderListBO();
            bo.setOrderList(entry.getValue());
            createBatchTaskOrderListBOMap.put(entry.getKey(), bo);
        }

        return createBatchTaskOrderListBOMap;
    }

    public static CreateBatchTaskOrderListBO createInstance(List<OutStockOrderPO> orderList) {
        return new CreateBatchTaskOrderListBO(orderList);
    }

    /**
     * 获取 出库单列表
     *
     * @return orderList 出库单列表
     */
    public List<OutStockOrderPO> getOrderList() {
        return this.orderList;
    }

    /**
     * 设置 出库单列表
     *
     * @param orderList 出库单列表
     */
    public void setOrderList(List<OutStockOrderPO> orderList) {
        this.orderList = orderList;
    }

    /**
     * 获取 出库位id
     *
     * @return locationId 出库位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 出库位id
     *
     * @param locationId 出库位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 出库位名称
     *
     * @return locationName 出库位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 出库位名称
     *
     * @param locationName 出库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
