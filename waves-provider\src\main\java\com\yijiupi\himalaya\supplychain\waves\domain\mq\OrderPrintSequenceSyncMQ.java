package com.yijiupi.himalaya.supplychain.waves.domain.mq;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.OrderPrintSequenceDTO;

/**
 * 订单打印顺序同步
 *
 * <AUTHOR>
 * @date 2019/6/5 11:36
 */
@Component
public class OrderPrintSequenceSyncMQ {

    private static final Logger LOG = LoggerFactory.getLogger(OrderPrintSequenceSyncMQ.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${ex.supplychain.orderprint.printSequence}")
    private String orderPrintSequenceSyncExchange;

    /**
     * 发送消息
     */
    public void send(List<OrderPrintSequenceDTO> orderPrintSequenceDTOList) {
        if (!CollectionUtils.isEmpty(orderPrintSequenceDTOList)) {
            LOG.info("订单打印顺序同步发送消息:{}", JSON.toJSONString(orderPrintSequenceDTOList));
            rabbitTemplate.convertAndSend(orderPrintSequenceSyncExchange, null, orderPrintSequenceDTOList);
        }
    }
}
