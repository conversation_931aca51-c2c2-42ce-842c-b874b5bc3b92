package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class OutStockOrderNewDTO implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 采购入库单id
     */
    private String newNoteId;
    /**
     * 采购入库单No
     */
    private String newNoteNo;
    /**
     * 是否直接改变库存
     */
    public Boolean directChangeStock;
    /**
     * 适口跨库 0 否 1 是
     */
    private Byte crossWareHouse;
    /**
     * 订单编号
     */
    private String refOrderNo;

    /**
     * 城市ID，分库用（第三方订单使用一个新的自定义CityId）
     */
    private Integer org_Id;
    /**
     * 来源城市
     */
    private Integer fromCityId;

    /**
     * 波次编号
     */
    private String batchNo;

    /**
     * 波次Id
     */
    private String batch_Id;

    /**
     * 波次状态： 待调度(0),待拣货(1),拣货中(2),已拣货(3),已出库(4),已取消(5),已作废(6)
     */
    private Byte state;

    /**
     * 订单状态【（酒批退货单）1申请退货/2
     * 已取消退货/3区域审核通过/4区域审核拒绝/5运营审核通过/6运营审核拒绝/7待取货/8已取货/9拒绝取货/10已退货/11待结账/12取货中/延迟退货】【（酒批订单）1已下单/2已取消/3审核通过/5待发货/6已发货/7已完成/8配送失败/10待结账/11待支付/12支付成功/13支付失败/14延迟配送/16作废】【（经纪人撮合）
     * 1审核通过/2已发货/3待结账/4已经完成/5已取消】【（兑奖订单）1 待打印、2 待发货、3 待结账、4已完成、5已取消 、6配送失败 、7已发货、8延迟配送】
     */
    private Byte orderState;

    /**
     * 企业编码（SAAS用）：易酒批(YJP)
     */
    private String companyCode;

    /**
     * 订单类型0=酒批订单，1=招商订单，2=经销商直配订单，3=大货批发订单，4=经济人撮合订单，10=大商转配送订单，11=轻加盟订单，12=经销商订单，13=易经销订单，14=兑奖订单'
     */
    private Byte orderType;

    /**
     * 关联业务单据类型1=酒批业务订单，2=酒批业务退货单，3=经纪人撮合业务，4=兑奖订单业务'
     */
    private Byte businessType;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 应付金额
     */
    private BigDecimal payableAmount;

    /**
     * 商品种类数
     */
    private Integer skuCount;

    /**
     * 大件总数
     */
    private BigDecimal packageAmount;

    /**
     * 小件总数
     */
    private BigDecimal unitAmount;

    /**
     * 发货仓库id
     */
    private Integer warehouse_Id;

    /**
     * 销售，调拨，破损，其他，采购退货，盘亏，第三方出
     */
    private Byte outStockOrderType;

    /**
     * 收货人姓名
     */
    private String userName;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 收货人手机号
     */
    private String mobileNo;

    /**
     * 收货地址
     */
    private String detailAddress;

    /**
     * 地址id
     */
    private Integer addressId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String county;

    /**
     * 街道
     */
    private String street;

    /**
     * 合作商id
     */
    private Long parterId;

    /**
     * 托管方名称
     */
    private String parterName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 下单时间
     */
    private Date orderCreateTime;

    /**
     * 配送方式（0=酒批配送，1=合作商配送，2=配送商配送，4=客户自提，5=总部物流，6=区域代配送，20=门店转配送，-1=不配送）
     */
    private Byte deliveryMode;

    /**
     * 开始拣货时间
     */
    private Date pickTime;

    /**
     * 出库时间
     */
    private Date outStockTime;

    /**
     * 出库操作人
     */
    private String outStockUser;

    /**
     * 区域id
     */
    private Long area_Id;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 线路id
     */
    private Long route_Id;

    /**
     * 线路名称
     */
    private String routeName;

    /**
     * 线路排序号
     */
    private Integer routeSequence;

    /**
     * 创建人
     */
    private String createuser;

    /**
     * 创建时间
     */
    private Date createtime;

    /**
     * 更新人
     */
    private String lastupdateuser;

    /**
     * 更新时间
     */
    private Date lastupdatetime;

    /**
     * 是否不更新库存，只处理批次库存
     */
    private Boolean isNotUpdateProductStore;

    /**
     * 是否变更销售库存,默认需要
     */
    private Boolean needToChangeSaleStore = true;

    public Boolean getNotUpdateProductStore() {
        return isNotUpdateProductStore;
    }

    public void setNotUpdateProductStore(Boolean notUpdateProductStore) {
        isNotUpdateProductStore = notUpdateProductStore;
    }

    public String getNewNoteId() {
        return newNoteId;
    }

    public void setNewNoteId(String newNoteId) {
        this.newNoteId = newNoteId;
    }

    public String getNewNoteNo() {
        return newNoteNo;
    }

    public void setNewNoteNo(String newNoteNo) {
        this.newNoteNo = newNoteNo;
    }

    public Boolean getDirectChangeStock() {
        return directChangeStock;
    }

    public void setDirectChangeStock(Boolean directChangeStock) {
        this.directChangeStock = directChangeStock;
    }

    public Byte getCrossWareHouse() {
        return crossWareHouse;
    }

    public void setCrossWareHouse(Byte crossWareHouse) {
        this.crossWareHouse = crossWareHouse;
    }

    private List<OutStockOrderItemDTO> itemList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo == null ? null : refOrderNo.trim();
    }

    public Integer getOrg_Id() {
        return org_Id;
    }

    public void setOrg_Id(Integer org_Id) {
        this.org_Id = org_Id;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo == null ? null : batchNo.trim();
    }

    public String getBatch_Id() {
        return batch_Id;
    }

    public void setBatch_Id(String batch_Id) {
        this.batch_Id = batch_Id == null ? null : batch_Id.trim();
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Byte getOrderState() {
        return orderState;
    }

    public void setOrderState(Byte orderState) {
        this.orderState = orderState;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }

    public Byte getOrderType() {
        return orderType;
    }

    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getPayableAmount() {
        return payableAmount;
    }

    public void setPayableAmount(BigDecimal payableAmount) {
        this.payableAmount = payableAmount;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public BigDecimal getPackageAmount() {
        return packageAmount;
    }

    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    public BigDecimal getUnitAmount() {
        return unitAmount;
    }

    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    public Integer getWarehouse_Id() {
        return warehouse_Id;
    }

    public void setWarehouse_Id(Integer warehouse_Id) {
        this.warehouse_Id = warehouse_Id;
    }

    public Byte getOutStockOrderType() {
        return outStockOrderType;
    }

    public void setOutStockOrderType(Byte outStockOrderType) {
        this.outStockOrderType = outStockOrderType;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName == null ? null : shopName.trim();
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo == null ? null : mobileNo.trim();
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress == null ? null : detailAddress.trim();
    }

    public Integer getAddressId() {
        return addressId;
    }

    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county == null ? null : county.trim();
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street == null ? null : street.trim();
    }

    public Long getParterId() {
        return parterId;
    }

    public void setParterId(Long parterId) {
        this.parterId = parterId;
    }

    public String getParterName() {
        return parterName;
    }

    public void setParterName(String parterName) {
        this.parterName = parterName == null ? null : parterName.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Date getOrderCreateTime() {
        return orderCreateTime;
    }

    public void setOrderCreateTime(Date orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    public Byte getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(Byte deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    public Date getPickTime() {
        return pickTime;
    }

    public void setPickTime(Date pickTime) {
        this.pickTime = pickTime;
    }

    public Date getOutStockTime() {
        return outStockTime;
    }

    public void setOutStockTime(Date outStockTime) {
        this.outStockTime = outStockTime;
    }

    public String getOutStockUser() {
        return outStockUser;
    }

    public void setOutStockUser(String outStockUser) {
        this.outStockUser = outStockUser == null ? null : outStockUser.trim();
    }

    public Long getArea_Id() {
        return area_Id;
    }

    public void setArea_Id(Long area_Id) {
        this.area_Id = area_Id;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName == null ? null : areaName.trim();
    }

    public Long getRoute_Id() {
        return route_Id;
    }

    public void setRoute_Id(Long route_Id) {
        this.route_Id = route_Id;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName == null ? null : routeName.trim();
    }

    public Integer getRouteSequence() {
        return routeSequence;
    }

    public void setRouteSequence(Integer routeSequence) {
        this.routeSequence = routeSequence;
    }

    public String getCreateuser() {
        return createuser;
    }

    public void setCreateuser(String createuser) {
        this.createuser = createuser == null ? null : createuser.trim();
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getLastupdateuser() {
        return lastupdateuser;
    }

    public void setLastupdateuser(String lastupdateuser) {
        this.lastupdateuser = lastupdateuser == null ? null : lastupdateuser.trim();
    }

    public Date getLastupdatetime() {
        return lastupdatetime;
    }

    public void setLastupdatetime(Date lastupdatetime) {
        this.lastupdatetime = lastupdatetime;
    }

    public List<OutStockOrderItemDTO> getItemList() {
        return this.itemList;
    }

    public void setItemList(List<OutStockOrderItemDTO> itemList) {
        this.itemList = itemList;
    }

    /**
     * 获取 来源城市
     */
    public Integer getFromCityId() {
        return this.fromCityId;
    }

    /**
     * 设置 来源城市
     */
    public void setFromCityId(Integer fromCityId) {
        this.fromCityId = fromCityId;
    }

    public Boolean getNeedToChangeSaleStore() {
        return needToChangeSaleStore;
    }

    public void setNeedToChangeSaleStore(Boolean needToChangeSaleStore) {
        this.needToChangeSaleStore = needToChangeSaleStore;
    }
}