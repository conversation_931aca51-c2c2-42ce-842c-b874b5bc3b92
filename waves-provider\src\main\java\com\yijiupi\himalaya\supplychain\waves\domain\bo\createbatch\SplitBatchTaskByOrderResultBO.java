package com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 按订单拆分拣货任务，拆分结果
 *
 * <AUTHOR>
 * @date 2025/5/16
 */
public class SplitBatchTaskByOrderResultBO {

    private Integer batchTaskType;
    /**
     * -1 : 默认; 剩下的跟随OrderFeatureConstants
     */
    private Byte featureType;

    private List<OutStockOrderPO> outStockOrderPOList;

    /**
     * 普通任务
     */
    public static final Integer TYPE_NORMAL_TASK = 1;
    /**
     * 漏货补发
     */
    public static final Integer TYPE_PICK_LEAK_TASK = 2;
    /**
     * 错发换货
     */
    public static final Integer TYPE_WRONG_DELIVERY_TASK = 3;

    /**
     * 获取
     *
     * @return batchTaskType
     */
    public Integer getBatchTaskType() {
        return this.batchTaskType;
    }

    /**
     * 设置
     *
     * @param batchTaskType
     */
    public void setBatchTaskType(Integer batchTaskType) {
        this.batchTaskType = batchTaskType;
    }

    /**
     * 获取
     *
     * @return outStockOrderPOList
     */
    public List<OutStockOrderPO> getOutStockOrderPOList() {
        return this.outStockOrderPOList;
    }

    /**
     * 设置
     *
     * @param outStockOrderPOList
     */
    public void setOutStockOrderPOList(List<OutStockOrderPO> outStockOrderPOList) {
        this.outStockOrderPOList = outStockOrderPOList;
    }

    /**
     * 获取
     *
     * @return featureType
     */
    public Byte getFeatureType() {
        return this.featureType;
    }

    /**
     * 设置
     *
     * @param featureType
     */
    public void setFeatureType(Byte featureType) {
        this.featureType = featureType;
    }

    public static List<SplitBatchTaskByOrderResultBO>
        getPickLeakOrderTaskList(Map<String, List<OutStockOrderPO>> orderGroupMap) {
        List<SplitBatchTaskByOrderResultBO> result = new ArrayList<SplitBatchTaskByOrderResultBO>();
        for (Map.Entry<String, List<OutStockOrderPO>> entry : orderGroupMap.entrySet()) {
            SplitBatchTaskByOrderResultBO splitBatchTaskByOrderResultBO = new SplitBatchTaskByOrderResultBO();
            splitBatchTaskByOrderResultBO.setBatchTaskType(SplitBatchTaskByOrderResultBO.TYPE_PICK_LEAK_TASK);
            splitBatchTaskByOrderResultBO.setOutStockOrderPOList(entry.getValue());
            result.add(splitBatchTaskByOrderResultBO);
        }

        return result;
    }

    public static List<SplitBatchTaskByOrderResultBO>
        getWrongDeliveryOrderTaskList(Map<String, List<OutStockOrderPO>> orderGroupMap) {
        List<SplitBatchTaskByOrderResultBO> result = new ArrayList<SplitBatchTaskByOrderResultBO>();
        for (Map.Entry<String, List<OutStockOrderPO>> entry : orderGroupMap.entrySet()) {
            SplitBatchTaskByOrderResultBO splitBatchTaskByOrderResultBO = new SplitBatchTaskByOrderResultBO();
            splitBatchTaskByOrderResultBO.setBatchTaskType(SplitBatchTaskByOrderResultBO.TYPE_WRONG_DELIVERY_TASK);
            splitBatchTaskByOrderResultBO.setOutStockOrderPOList(entry.getValue());
            result.add(splitBatchTaskByOrderResultBO);
        }

        return result;
    }

    public static List<SplitBatchTaskByOrderResultBO>
        getNormalTaskList(Map<String, List<OutStockOrderPO>> orderGroupMap) {
        List<SplitBatchTaskByOrderResultBO> boList = new ArrayList<>();
        for (Map.Entry<String, List<OutStockOrderPO>> entry : orderGroupMap.entrySet()) {
            SplitBatchTaskByOrderResultBO bo = new SplitBatchTaskByOrderResultBO();
            bo.setOutStockOrderPOList(entry.getValue());
            bo.setBatchTaskType(SplitBatchTaskByOrderResultBO.TYPE_NORMAL_TASK);

            boList.add(bo);
        }
        return boList;
    }

}
