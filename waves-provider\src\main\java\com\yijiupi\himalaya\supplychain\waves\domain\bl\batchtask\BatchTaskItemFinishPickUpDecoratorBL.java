package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskFinishHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishNeedOpBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.pickup.BatchTaskItemFinishPickUpBaseDecoratorBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. 移库
 * 
 * <AUTHOR>
 * @date 2024/1/12
 */
// @Service
public class BatchTaskItemFinishPickUpDecoratorBL extends BatchTaskItemFinishDecoratorBL {

    @Autowired
    private List<BatchTaskItemFinishPickUpBaseDecoratorBL> decoratorList;

    @Override
    protected void doCompleteBatchTaskItem(BatchTaskItemFinishNeedOpBO bo,
        BatchTaskFinishHelperBO batchTaskFinishHelperBO, List<BatchTaskItemDTO> needCompleteBatchTaskItemList) {
        decoratorList.forEach(decorator -> {
            decorator.completeBatchTaskItem(bo, batchTaskFinishHelperBO);
        });
    }
}
