package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/6
 */
public class SowTaskProductLackReviewDTO implements Serializable {
    private static final long serialVersionUID = 5426226007556392604L;
    /**
     * 城市ID
     */
    private Integer orgId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 操作人名称
     */
    private String optUserName;
    /**
     * 操作人id
     */
    private Integer optUserId;
    /**
     * 产品列表
     */
    private List<SowTaskProductLackDTO> productList;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getOptUserName() {
        return optUserName;
    }

    public void setOptUserName(String optUserName) {
        this.optUserName = optUserName;
    }

    public Integer getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }

    public List<SowTaskProductLackDTO> getProductList() {
        return productList;
    }

    public void setProductList(List<SowTaskProductLackDTO> productList) {
        this.productList = productList;
    }
}
