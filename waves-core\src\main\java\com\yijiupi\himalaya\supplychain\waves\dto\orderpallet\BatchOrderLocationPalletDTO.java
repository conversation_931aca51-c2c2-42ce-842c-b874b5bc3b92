package com.yijiupi.himalaya.supplychain.waves.dto.orderpallet;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
public class BatchOrderLocationPalletDTO implements Serializable {
    /**
     * 订单托盘关系列表
     */
    private List<OrderLocationPalletDTO> palletDTOList;
    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 获取 订单托盘关系列表
     *
     * @return palletDTOList 订单托盘关系列表
     */
    public List<OrderLocationPalletDTO> getPalletDTOList() {
        return this.palletDTOList;
    }

    /**
     * 设置 订单托盘关系列表
     *
     * @param palletDTOList 订单托盘关系列表
     */
    public void setPalletDTOList(List<OrderLocationPalletDTO> palletDTOList) {
        this.palletDTOList = palletDTOList;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }
}
