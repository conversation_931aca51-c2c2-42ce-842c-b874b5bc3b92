package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 拣货任务类型
 *
 * <AUTHOR>
 * @date 2019-09-24 14:51
 */
public enum BatchTaskTypeEnum {
    /**
     * 默认
     */
    默认((byte)0),
    /**
     * 按自提点
     */
    按自提点((byte)1),
    /**
     *
     */
    酒饮按订单分区拣货((byte)2);

    /**
     * type
     */
    private Byte type;

    BatchTaskTypeEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

}
