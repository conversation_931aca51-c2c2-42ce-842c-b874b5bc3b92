package com.yijiupi.himalaya.supplychain.waves.dto.base;

import com.yijiupi.himalaya.supplychain.waves.constant.WebConstants;

public class ROResult<T> extends BaseResult {

    T data;

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <T> ROResult<T> getResult(T data) {
        ROResult<T> result = new ROResult<>();
        result.setData(data);
        result.setResult(WebConstants.RESULT_SUCCESS);
        return result;
    }

    public ROResult() {
        super();
    }

    public ROResult(T data) {
        super.setResult("success");
        this.data = data;
        super.setSuccess(Boolean.TRUE);
        super.setMessage(WebConstants.RESULT_SUCCESS);
    }

    public static <T> ROResult<T> getFailRoResult(T data, String message) {
        ROResult<T> result = new ROResult<>();
        result.setData(data);
        result.setResult(WebConstants.RESULT_FAILED);
        result.setMessage(message);
        return result;
    }

    public void getSuccess(Integer totalCount) {
        super.setTotalCount(totalCount);
        super.setResult("success");
        super.setMessage(WebConstants.RESULT_SUCCESS);
        super.setSuccess(Boolean.TRUE);
    }

    public static <T> ROResult<T> getFailRoResult(String message) {
        ROResult<T> result = new ROResult<>();
        result.setResult(WebConstants.RESULT_FAILED);
        result.setMessage(message);
        return result;
    }
}
