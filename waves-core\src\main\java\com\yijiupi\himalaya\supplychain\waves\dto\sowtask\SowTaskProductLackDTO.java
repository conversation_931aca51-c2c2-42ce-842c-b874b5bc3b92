package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/6
 */
public class SowTaskProductLackDTO implements Serializable {
    private static final long serialVersionUID = -6752320857464968639L;

    /**
     * 产品skuId
     */
    private Long productSkuId;
    /**
     * 产品skuId
     */
    private BigDecimal saleSpecQuantity;
    /**
     * 实际播种大单位数量
     */
    private BigDecimal packageCount;
    /**
     * 实际播种小单位数量
     */
    private BigDecimal unitCount;
    /**
     * 大小单位转化系数
     */
    private BigDecimal specQuality;
    /**
     * 播种列表
     */
    private List<SowTaskLackDTO> sowTaskList;

    public BigDecimal getSaleSpecQuantity() {
        return saleSpecQuantity;
    }

    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    public BigDecimal getSpecQuality() {
        return specQuality;
    }

    public void setSpecQuality(BigDecimal specQuality) {
        this.specQuality = specQuality;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public List<SowTaskLackDTO> getSowTaskList() {
        return sowTaskList;
    }

    public void setSowTaskList(List<SowTaskLackDTO> sowTaskList) {
        this.sowTaskList = sowTaskList;
    }
}
