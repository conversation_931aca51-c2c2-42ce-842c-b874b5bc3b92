package com.yijiupi.himalaya.supplychain.controller;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.controller.sow.BatchTaskSowNoParam;
import com.yijiupi.himalaya.supplychain.controller.sow.SowTaskReceiveParamDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.base.BaseResult;
import com.yijiupi.himalaya.supplychain.waves.dto.base.ROResult;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 多人协作播种任务
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping({"/pda/concertSow", "/oldpda/concertSow"})
public class ConcertSowManageController {

    @Autowired
    private ConcertSowManageService concertSowManageService;

    /**
     * 领取播种任务明细
     *
     * @return
     */
    @PostMapping("/getSowTask")
    public BaseResult getSowTask(@RequestBody ConcertSowTaskRequestParamDTO request) {
        concertSowManageService.getSowTask(request);
        return BaseResult.getSuccessResult();
    }

    /**
     * 待播种任务查询
     */
    @RequestMapping(value = "/queryWaitSowTask", method = RequestMethod.POST)
    public BaseResult queryWaitSowTask(@RequestBody SowTaskReceiveParamDTO sowTaskReceiveParamDTO) {
        SowTaskDTO sowTask = concertSowManageService.queryWaitSowTask(sowTaskReceiveParamDTO);
        return new ROResult<>(sowTask);
    }

    /**
     * 根据播种任务查询波次任务列表
     */
    @RequestMapping(value = "/listBatchTaskBySowTaskNos", method = RequestMethod.POST)
    public BaseResult listBatchTaskBySowTaskNo(@RequestBody BatchTaskSowNoParam batchTaskSowNoParam) {
        return new ROResult<>(concertSowManageService.listBatchTaskBySowTaskNo(batchTaskSowNoParam));
    }

    /**
     * 二次分拣通用查询
     */
    @RequestMapping(value = "/listBatchTaskInfo", method = RequestMethod.POST)
    public BaseResult listBatchTaskInfo(@RequestBody BatchTaskSowNoParam batchTaskSowNoParam) {
        PageList<BatchTaskDTO> batchTaskDTOS = concertSowManageService.listBatchTaskInfo(batchTaskSowNoParam);
        ROResult<PageList<BatchTaskDTO>> listROResult = new ROResult<>(batchTaskDTOS);
        List<BatchTaskDTO> dataList = batchTaskDTOS.getDataList();
        if(CollectionUtils.isEmpty(dataList)){
            listROResult.setTotalCount(0);
            return listROResult;
        }
        listROResult.setTotalCount(batchTaskDTOS.getPager().getRecordCount());
        return listROResult;
    }

}
