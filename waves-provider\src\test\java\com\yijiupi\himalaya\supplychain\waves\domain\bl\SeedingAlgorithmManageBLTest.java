package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.SowingOrderBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.SowingSkuBO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/10 16:03
 * @Version 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class SeedingAlgorithmManageBLTest {

	@Autowired
	private SeedingAlgorithmManageBL seedingAlgorithmManageBL;
	@Autowired
	private PickingTaskItemSortingBL pickingTaskItemSortingBL;


	@Test
	public void sortSowingOrdersBySKURepeatRateTest1() {
		// 示例订单数据
		List<SowingOrderBO> allSowingOrders = new ArrayList<>();
		allSowingOrders.add(new SowingOrderBO("ORDER_1",
				Arrays.asList(new SowingSkuBO(111L), new SowingSkuBO(222L), new SowingSkuBO(333L))));

		allSowingOrders.add(new SowingOrderBO("ORDER_2",
				Arrays.asList(new SowingSkuBO(111L), new SowingSkuBO(444L), new SowingSkuBO(555L),
						new SowingSkuBO(666L), new SowingSkuBO(555L), new SowingSkuBO(777L))));

		allSowingOrders.add(new SowingOrderBO("ORDER_3",
				Arrays.asList(new SowingSkuBO(111L), new SowingSkuBO(222L), new SowingSkuBO(333L), new SowingSkuBO(444L))));

		// 排序订单
		List<SowingOrderBO> sortedSowingOrders = seedingAlgorithmManageBL.sortSowingOrdersBySKURepeatRate(allSowingOrders);

		// 输出排序后的订单 ID
		for (SowingOrderBO sowingOrder : sortedSowingOrders) {
			System.out.println("Order ID: " + sowingOrder.getRefOrderNo());
		}
	}

	@Test
	public void replaySowingTaskByBatchNoTest() {
		seedingAlgorithmManageBL.replaySowingTaskByBatchNos(Collections.singletonList("BC998125012000023"), 998, 1);
		Assert.assertTrue(true);
	}

	@Test
	public void clearSowingTaskReplayDataTest() {
		seedingAlgorithmManageBL.clearSowingTaskReplayData(Collections.singletonList("BC998125012000023"), 998, 1);
		Assert.assertTrue(true);
	}

	@Test
	public void compareHistoricalPickingSortingTest() {
		List<String> batchTaskNos = Arrays.asList("BT998125043000004");
		pickingTaskItemSortingBL.compareHistoricalPickingSorting(batchTaskNos);
		Assert.assertTrue(true);
	}
}
