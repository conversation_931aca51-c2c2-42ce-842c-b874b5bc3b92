package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.lock;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.common.constant.RedisConstant;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchBaseBO;
import com.yijiupi.himalaya.supplychain.waves.util.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.distributedlock.service.RedisLockService;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/10/16
 */
@Service
public class LockCreateBatchBL {

    public static final String BATCH_PARAM_CACHE_KEY = RedisConstant.SUP_C + "BatchParamInfo:";
    private static final Long LOCK_TIME = 10 * 60 * 1000L;
    private static final String LOCK_KEY = "supc:stockorder:BatchCreateOrder:";
    private static final String WAIT_LOCK_KEY = "supc:createBatch:createBatchLock:";
    protected static final Logger LOG = LoggerFactory.getLogger(LockCreateBatchBL.class);

    @Autowired
    private RedisLockService redisLockService;
    @Autowired
    private RedisUtil<String> redisUtil;

    @DistributeLock(conditions = "#warehouseId", key = WAIT_LOCK_KEY, sleepMills = 5000, retryTimes = 2,
        lockType = DistributeLock.LockType.WAITLOCK)
    public List<Long> lock(List<OutStockOrderPO> outStockOrderPOList, Integer warehouseId) {
        List<Long> redisLocks = Lists.newArrayList();
        Map<Long, Long> failIdMap = new HashMap<>();
        for (int s = outStockOrderPOList.size() - 1; s >= 0; s--) {
            OutStockOrderPO outStockOrderPO = outStockOrderPOList.get(s);
            try {
                redisLockService.distributedLock(LOCK_KEY + outStockOrderPO.getId().toString(), "", LOCK_TIME);
                LOG.info("加锁成功：{}", outStockOrderPO.getId());
                redisLocks.add(outStockOrderPO.getId());
            } catch (BusinessValidateException exception) {
                LOG.info("加锁失败，重复加锁：" + outStockOrderPO.getId(), exception);
                failIdMap.put(outStockOrderPO.getId(), outStockOrderPO.getId());
            } catch (Exception e) {
                throw new BusinessException("过滤订单异常", e);
            }
        }

        if (!CollectionUtils.isEmpty(failIdMap)) {
            failIdMap.values().forEach(id -> {
                outStockOrderPOList.removeIf(m -> m.getId().equals(id));
            });

        }

        return redisLocks;
    }

    @DistributeLock(conditions = "#warehouseId", key = WAIT_LOCK_KEY, sleepMills = 3000, retryTimes = 2,
        lockType = DistributeLock.LockType.WAITLOCK)
    public void releaseLock(List<Long> ids, Integer warehouseId) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        ids.forEach(id -> {
            try {
                redisLockService.releaseLock(LOCK_KEY + id.toString(), "");
            } catch (Exception e) {
                LOG.info("释放锁失败：{}", id);
            }
        });
    }

    public void cacheParam(List<OutStockOrderPO> outStockOrderPOList, CreateBatchBaseBO createBatchBaseBO) {
        List<String> batchNoList =
            outStockOrderPOList.stream().map(OutStockOrderPO::getBatchno).distinct().collect(Collectors.toList());

        batchNoList.forEach(batchNo -> {
            LOG.info("缓存参数信息为:{}", JSON.toJSONString(createBatchBaseBO));
            redisUtil.set(BATCH_PARAM_CACHE_KEY + batchNo, JSON.toJSONString(createBatchBaseBO), 2, TimeUnit.DAYS);
        });

    }

    public void removeBatchCache(List<String> batchNoList) {
        if (CollectionUtils.isEmpty(batchNoList)) {
            return;
        }
        List<String> keyList =
            batchNoList.stream().map(batchNo -> BATCH_PARAM_CACHE_KEY + batchNo).collect(Collectors.toList());

        redisUtil.batchDel(keyList);
    }

}
