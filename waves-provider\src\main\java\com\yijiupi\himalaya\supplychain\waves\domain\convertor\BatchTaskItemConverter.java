package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.BeanUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;

/**
 * <AUTHOR>
 * @since 2024-10-10 14:03
 **/
public class BatchTaskItemConverter {

    public static BatchTaskItemDTO toDTO(BatchTaskItemPO po) {
        BatchTaskItemDTO batchTaskItemDTO = new BatchTaskItemDTO();
        batchTaskItemDTO.setBarCode(null);
        batchTaskItemDTO.setSkuProperty(null);
        batchTaskItemDTO.setSowTaskItemState(null);
        batchTaskItemDTO.setAllotType(null);
        batchTaskItemDTO.setCreateAllocation(null);
        batchTaskItemDTO.setBusinessId(null);
        batchTaskItemDTO.setBusinessType(null);
        batchTaskItemDTO.setTotalAmount(null);
        batchTaskItemDTO.setDeliveryMode(null);
        batchTaskItemDTO.setPackageAttribute(null);
        batchTaskItemDTO.setCreateTime(null);
        batchTaskItemDTO.setAreaName(null);
        batchTaskItemDTO.setRouteName(null);
        batchTaskItemDTO.setOrderSelection(null);
        batchTaskItemDTO.setControlConfigName(null);
        batchTaskItemDTO.setOrderType(null);
        batchTaskItemDTO.setOrderSequence(null);
        batchTaskItemDTO.setUserName(null);
        batchTaskItemDTO.setShopName(null);
        batchTaskItemDTO.setMobileNo(null);
        batchTaskItemDTO.setProvince(null);
        batchTaskItemDTO.setCity(null);
        batchTaskItemDTO.setCounty(null);
        batchTaskItemDTO.setStreet(null);
        batchTaskItemDTO.setDetailAddress(null);
        batchTaskItemDTO.setSequence(null);
        batchTaskItemDTO.setPackageCode(null);
        batchTaskItemDTO.setUnitCode(null);
        batchTaskItemDTO.setTaskStateText(null);
        batchTaskItemDTO.setLocationCategoryText(null);
        batchTaskItemDTO.setSourceText(null);
        batchTaskItemDTO.setChannelText(null);
        batchTaskItemDTO.setPickingTypeText(null);
        batchTaskItemDTO.setOrderTypeText(null);
        batchTaskItemDTO.setOrderSelectionText(null);
        batchTaskItemDTO.setPickSequence(null);
        batchTaskItemDTO.setDefaultImageFile(null);
        batchTaskItemDTO.setImageFiles(null);
        batchTaskItemDTO.setOrderItemIdList(null);
        batchTaskItemDTO.setOutBoundType(null);
        batchTaskItemDTO.setBatchId(po.getBatchId());
        batchTaskItemDTO.setUnitPrice(po.getUnitPrice());
        batchTaskItemDTO.setRollbackUnitCount(po.getRollbackUnitCount());
        batchTaskItemDTO.setIsLack(po.getIsLack());
        batchTaskItemDTO.setWarehouseId(po.getWarehouseId());
        batchTaskItemDTO.setBatchTime(po.getBatchTime());
        batchTaskItemDTO.setProductionDate(po.getProductionDate());
        batchTaskItemDTO.setBatchNo(po.getBatchNo());
        batchTaskItemDTO.setSorter(po.getCompleteUser());
        batchTaskItemDTO.setSorterId(po.getCompleteUserId());
        batchTaskItemDTO.setControlConfigId(po.getControlConfigId());
        batchTaskItemDTO.setFromLocationId(po.getLocationId());
        batchTaskItemDTO.setFromLocationName(po.getLocationName());
        batchTaskItemDTO.setProductSpecificationId(po.getProductSpecificationId());
        batchTaskItemDTO.setOwnerId(po.getOwnerId());
        batchTaskItemDTO.setSecOwnerId(po.getSecOwnerId());
        batchTaskItemDTO.setSowTaskId(po.getSowTaskId());
        batchTaskItemDTO.setSowTaskNo(po.getSowTaskNo());
        batchTaskItemDTO.setOrderSquence(po.getOrderSquence());
        batchTaskItemDTO.setSource(po.getSource());
        batchTaskItemDTO.setChannel(po.getChannel());
        batchTaskItemDTO.setOrderItemId(po.getOrderItemId());
        batchTaskItemDTO.setId(po.getId());
        batchTaskItemDTO.setOrgId(po.getOrgId());
        batchTaskItemDTO.setBatchTaskNo(po.getBatchTaskNo());
        batchTaskItemDTO.setBatchTaskId(po.getBatchTaskId());
        batchTaskItemDTO.setRefOrderNo(po.getRefOrderNo());
        batchTaskItemDTO.setRefOrderId(po.getRefOrderId());
        batchTaskItemDTO.setProductName(po.getProductName());
        batchTaskItemDTO.setSkuId(po.getSkuId());
        batchTaskItemDTO.setProductBrand(po.getProductBrand());
        batchTaskItemDTO.setCategoryName(po.getCategoryName());
        batchTaskItemDTO.setSpecName(po.getSpecName());
        batchTaskItemDTO.setSpecQuantity(po.getSpecQuantity());
        batchTaskItemDTO.setSaleSpec(po.getSaleSpec());
        batchTaskItemDTO.setSaleSpecQuantity(po.getSaleSpecQuantity());
        batchTaskItemDTO.setPackageName(po.getPackageName());
        batchTaskItemDTO.setPackageCount(po.getPackageCount());
        batchTaskItemDTO.setUnitName(po.getUnitName());
        batchTaskItemDTO.setUnitCount(po.getUnitCount());
        batchTaskItemDTO.setUnitTotalCount(po.getUnitTotalCount());
        batchTaskItemDTO.setTaskState(po.getTaskState());
        batchTaskItemDTO.setLackUnitCount(po.getLackUnitCount());
        batchTaskItemDTO.setRemark(po.getRemark());
        batchTaskItemDTO.setOverSortCount(po.getOverSortCount());
        batchTaskItemDTO.setLocationId(po.getLocationId());
        batchTaskItemDTO.setLocationName(po.getLocationName());
        batchTaskItemDTO
            .setLocationCategory(po.getLocationCategory() == null ? null : po.getLocationCategory().intValue());
        batchTaskItemDTO.setPickingType(po.getPickingType());
        batchTaskItemDTO.setSownUnitTotalCount(po.getSownUnitTotalCount());
        batchTaskItemDTO.setToLocationId(po.getToLocationId());
        batchTaskItemDTO.setToLocationName(po.getToLocationName());
        batchTaskItemDTO.setCompleteUserId(po.getCompleteUserId());
        batchTaskItemDTO.setCompleteUser(po.getCompleteUser());
        batchTaskItemDTO.setLargePickPattern(po.getLargePickPattern());
        batchTaskItemDTO.setStartTime(po.getStartTime());
        batchTaskItemDTO.setCompleteTime(po.getCompleteTime());
        return batchTaskItemDTO;
    }

    public static List<BatchTaskItemDTO> convertToBatchTaskItemDTO(List<BatchTaskItemPO> poList) {
        List<BatchTaskItemDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(poList)) {
            return dtoList;
        }

        poList.forEach(po -> {
            BatchTaskItemDTO dto = new BatchTaskItemDTO();
            BeanUtils.copyProperties(po, dto);
            dtoList.add(dto);
        });

        return dtoList;
    }
}
