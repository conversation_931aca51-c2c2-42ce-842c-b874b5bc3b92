package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * <AUTHOR>
 * @since 2024-02-29 15:49
 **/
public enum BatchProgressType {

    /**
     * 酒饮
     */
    DRINK(0),

    /**
     * 休食
     */
    REST(1),
    ;
    private final Integer value;

    BatchProgressType(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public boolean valueEquals(Number number) {
        return number != null && this.value.equals(number.intValue());
    }

}
