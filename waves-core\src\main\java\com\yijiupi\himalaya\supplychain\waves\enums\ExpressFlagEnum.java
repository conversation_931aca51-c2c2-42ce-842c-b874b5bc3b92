package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 波次区分标示
 *
 * <AUTHOR>
 * @date 2019-09-24 14:51
 */
public enum ExpressFlagEnum {
    /**
     * 快递直发订单
     */
    快递直发订单((byte)1),
    /**
     * ERP调拨出库单
     */
    ERP调拨出库单((byte)2);

    /**
     * type
     */
    private Byte type;

    ExpressFlagEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

}
