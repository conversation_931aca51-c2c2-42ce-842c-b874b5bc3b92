package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/6/5
 */
public class OrderItemTaskInfoUpdateDTO implements Serializable {

    private Long id;

    private BigDecimal overSortCount;

    private BigDecimal lackUnitCount;

    private BigDecimal moveCount;

    private List<OrderItemTaskInfoDetailUpdateDTO> detailList;

    /**
     * 获取
     *
     * @return id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置
     *
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取
     *
     * @return overSortCount
     */
    public BigDecimal getOverSortCount() {
        return this.overSortCount;
    }

    /**
     * 设置
     *
     * @param overSortCount
     */
    public void setOverSortCount(BigDecimal overSortCount) {
        this.overSortCount = overSortCount;
    }

    /**
     * 获取
     *
     * @return lackUnitCount
     */
    public BigDecimal getLackUnitCount() {
        return this.lackUnitCount;
    }

    /**
     * 设置
     *
     * @param lackUnitCount
     */
    public void setLackUnitCount(BigDecimal lackUnitCount) {
        this.lackUnitCount = lackUnitCount;
    }

    /**
     * 获取
     *
     * @return moveCount
     */
    public BigDecimal getMoveCount() {
        return this.moveCount;
    }

    /**
     * 设置
     *
     * @param moveCount
     */
    public void setMoveCount(BigDecimal moveCount) {
        this.moveCount = moveCount;
    }

    /**
     * 获取
     *
     * @return detailList
     */
    public List<OrderItemTaskInfoDetailUpdateDTO> getDetailList() {
        return this.detailList;
    }

    /**
     * 设置
     *
     * @param detailList
     */
    public void setDetailList(List<OrderItemTaskInfoDetailUpdateDTO> detailList) {
        this.detailList = detailList;
    }
}
