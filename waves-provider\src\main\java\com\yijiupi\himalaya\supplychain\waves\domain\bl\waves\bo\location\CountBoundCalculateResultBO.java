package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location;

import java.util.List;

/**
 * <AUTHOR>
 * @title: CountBoundCalculateResultBO
 * @description: 拆分结果
 * @date 2022-12-02 10:22
 */
public class CountBoundCalculateResultBO {
    /**
     * 大件数列表
     */
    private List<CountBoundCalculateBO> packageList;
    /**
     * 小件数列表
     */
    private List<CountBoundCalculateBO> unitList;

    /**
     * 获取 大件数列表
     *
     * @return packageList 大件数列表
     */
    public List<CountBoundCalculateBO> getPackageList() {
        return this.packageList;
    }

    /**
     * 设置 大件数列表
     *
     * @param packageList 大件数列表
     */
    public void setPackageList(List<CountBoundCalculateBO> packageList) {
        this.packageList = packageList;
    }

    /**
     * 获取 小件数列表
     *
     * @return unitList 小件数列表
     */
    public List<CountBoundCalculateBO> getUnitList() {
        return this.unitList;
    }

    /**
     * 设置 小件数列表
     *
     * @param unitList 小件数列表
     */
    public void setUnitList(List<CountBoundCalculateBO> unitList) {
        this.unitList = unitList;
    }
}
