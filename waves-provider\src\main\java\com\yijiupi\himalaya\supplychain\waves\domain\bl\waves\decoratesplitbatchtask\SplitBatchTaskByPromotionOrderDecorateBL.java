package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.decoratesplitbatchtask;

import java.util.*;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.enums.PickingTypeEnum;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.PromotionStoreBatchQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.PromotionStoreBatchResultDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IPromotionStoreBatchService;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.SplitBatchTaskDecorateHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.SplitBatchTaskByOrderTypeBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/4
 */
@Component
public class SplitBatchTaskByPromotionOrderDecorateBL extends SplitBatchTaskConcreteDecorateBL {

    @Reference
    private IPromotionStoreBatchService iPromotionStoreBatchService;

    @Override
    public void splitBatchTaskByOrderType(SplitBatchTaskByOrderTypeBO bo, SplitBatchTaskDecorateHelperBO helperBO) {
        WavesStrategyBO wavesStrategyDTO = helperBO.getWavesStrategyDTO();
        List<OutStockOrderPO> orders = helperBO.getOrders();
        ProcessBatchDTO processBatchDTO = helperBO.getProcessBatchDTO();

        List<OutStockOrderPO> normalOrders = orders.stream()
            .filter(p -> Objects.isNull(p.getOutBoundType())
                || OutBoundTypeEnum.SALE_ORDER.getCode().byteValue() != p.getOutBoundType()
                || !OrderConstant.ALLOT_TYPE_ALLOCATION.equals(p.getAllotType()))
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(normalOrders)) {
            return;
        }

        // 过滤出促销订单、且批次库存有混合的 出库单
        List<OutStockOrderPO> promotionOrderList =
            getPromotionNeedSplitOrderList(normalOrders, wavesStrategyDTO.getWarehouseId());

        if (CollectionUtils.isEmpty(promotionOrderList)) {
            return;
        }

        // 创建按订单拣货的拣货任务
        List<WaveCreateDTO> promotionWaveCreteList =
            getPromotionWaveCreateDTO(wavesStrategyDTO, promotionOrderList, processBatchDTO);
        if (CollectionUtils.isEmpty(promotionWaveCreteList)) {
            return;
        }

        bo.setPromotionWaveCreateDTO(promotionWaveCreteList);
    }

    private List<OutStockOrderPO> getPromotionNeedSplitOrderList(List<OutStockOrderPO> normalOrderList,
        Integer warehouseId) {
        List<OutStockOrderPO> promotionOrderPOList =
            normalOrderList.stream().filter(SplitWaveOrderUtil::orderIsPromotion).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(promotionOrderPOList)) {
            return Collections.emptyList();
        }

        List<OutStockOrderItemPO> promotionItemList = promotionOrderPOList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> ConditionStateEnum.是.getType().equals(m.getIsAdvent())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(promotionItemList)) {
            return Collections.emptyList();
        }

        List<Long> skuIds =
            promotionItemList.stream().map(OutStockOrderItemPO::getSkuid).distinct().collect(Collectors.toList());
        // 【临过期产品商城促销方案】 查询批次库存
        PromotionStoreBatchQueryDTO queryDTO = new PromotionStoreBatchQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setSkuIdList(skuIds);
        List<PromotionStoreBatchResultDTO> promotionStoreBatchResultDTOS =
            iPromotionStoreBatchService.listProductMixedBatchFlag(queryDTO);

        if (CollectionUtils.isEmpty(promotionStoreBatchResultDTOS)) {
            return Collections.emptyList();
        }

        Map<Long,
            Long> mixInventorySkuIdMap = promotionStoreBatchResultDTOS.stream()
                .filter(m -> ConditionStateEnum.是.getType().equals(m.getIsMixedBatch())).collect(
                    Collectors.toMap(PromotionStoreBatchResultDTO::getSkuId, PromotionStoreBatchResultDTO::getSkuId));

        if (CollectionUtils.isEmpty(mixInventorySkuIdMap)) {
            return Collections.emptyList();
        }
        Map<Long, Long> mixInventoryOrderIdMap =
            promotionItemList.stream().filter(m -> Objects.nonNull(mixInventorySkuIdMap.get(m.getSkuid())))
                .map(OutStockOrderItemPO::getOutstockorderId).distinct().collect(Collectors.toMap(k -> k, v -> v));

        return promotionOrderPOList.stream().filter(m -> Objects.nonNull(m.getId())).collect(Collectors.toList());
    }

    /**
     * 创建促销订单拣货任务dto
     *
     * @param wavesStrategyDTO
     * @param promotionOrderList
     * @param processBatchDTO
     * @return
     */
    private List<WaveCreateDTO> getPromotionWaveCreateDTO(WavesStrategyBO wavesStrategyDTO,
        List<OutStockOrderPO> promotionOrderList, ProcessBatchDTO processBatchDTO) {
        if (CollectionUtils.isEmpty(promotionOrderList)) {
            return Collections.emptyList();
        }
        String title = processBatchDTO.getBatchName();
        Integer cityId = processBatchDTO.getCityId();
        String operateUser = processBatchDTO.getOperateUser();
        String locationName = processBatchDTO.getLocationName();
        String driverName = processBatchDTO.getDriverName();

        Map<Integer, List<OutStockOrderPO>> orderMap = promotionOrderList.stream()
            .collect(Collectors.groupingBy(p -> p.getToWarehouseId() == null ? 0 : p.getToWarehouseId()));
        List<WaveCreateDTO> waveCreateDTOList = new ArrayList<>();
        orderMap.forEach((toWarehouseId, warehouseOrderList) -> {
            WaveCreateDTO promotionCreateDTO = SplitWaveOrderUtil.getWaveCreateDTO(promotionOrderList, wavesStrategyDTO,
                (byte)WavesStrategyConstants.PICKINGTYPE_ORDER, wavesStrategyDTO.getPickingGroupStrategy(), title,
                operateUser, cityId, locationName, driverName, null, null, false, processBatchDTO);
            promotionCreateDTO.setToLocationId(processBatchDTO.getToLocationId());
            promotionCreateDTO.setToLocationName(processBatchDTO.getToLocationName());
            waveCreateDTOList.add(promotionCreateDTO);
        });

        return waveCreateDTOList;
    }

    /**
     * Get the order value of this object.
     * <p>
     * Higher values are interpreted as lower priority. As a consequence, the object with the lowest value has the
     * highest priority (somewhat analogous to Servlet {@code load-on-startup} values).
     * <p>
     * Same order values will result in arbitrary sort positions for the affected objects.
     *
     * @return the order value
     * @see #HIGHEST_PRECEDENCE
     * @see #LOWEST_PRECEDENCE
     */
    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }
}
