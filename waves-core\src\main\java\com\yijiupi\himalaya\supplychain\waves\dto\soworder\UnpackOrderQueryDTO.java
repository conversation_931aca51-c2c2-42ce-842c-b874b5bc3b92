package com.yijiupi.himalaya.supplychain.waves.dto.soworder;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.yijiupi.himalaya.supplychain.waves.utils.CustomJsonDateDeserializer;

/**
 * 打包订单查询参数
 */
public class UnpackOrderQueryDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 订单编号
     */
    private String refOrderNo;

    /**
     * 波次编号
     */
    private String batchNo;

    /**
     * 周转区名称
     */
    private String toLocationName;

    /**
     * 集货位名称
     */
    private String sowLocationName;

    /**
     * 开始时间
     */
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date startTime;

    /**
     * 截止时间
     */
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date endTime;

    /**
     * 当前页
     */
    private Integer currentPage = 1;

    /**
     * @Fields 每页的数量
     */
    private Integer pageSize = 10;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public String getSowLocationName() {
        return sowLocationName;
    }

    public void setSowLocationName(String sowLocationName) {
        this.sowLocationName = sowLocationName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
