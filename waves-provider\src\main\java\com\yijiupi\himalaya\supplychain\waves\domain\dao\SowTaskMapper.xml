<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_id" jdbcType="INTEGER" property="orgId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="Batch_id" jdbcType="VARCHAR" property="batchId"/>
        <result column="BatchNo" jdbcType="VARCHAR" property="batchNo"/>
        <result column="SowTaskNo" jdbcType="VARCHAR" property="sowTaskNo"/>
        <result column="SowTaskName" jdbcType="VARCHAR" property="sowTaskName"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="OrderCount" jdbcType="INTEGER" property="orderCount"/>
        <result column="SkuCount" jdbcType="INTEGER" property="skuCount"/>
        <result column="PackageAmount" jdbcType="DECIMAL" property="packageAmount"/>
        <result column="UnitAmount" jdbcType="DECIMAL" property="unitAmount"/>
        <result column="Location_Id" jdbcType="BIGINT" property="locationId"/>
        <result column="LocationName" jdbcType="VARCHAR" property="locationName"/>
        <result column="OperationMode" jdbcType="TINYINT" property="operationMode"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="StartTime" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="CompleteTime" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="SowTaskType" jdbcType="TINYINT" property="sowTaskType"/>
        <result column="OperatorName" jdbcType="VARCHAR" property="operatorName"/>
        <result column="Operator_Id" jdbcType="VARCHAR" property="operatorId"/>
        <result column="AddressCount" jdbcType="INTEGER" property="addressCount"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="SorterId" jdbcType="INTEGER" property="sorterId"/>
        <result column="SorterName" jdbcType="VARCHAR" property="sorterName"/>
        <result column="PackageState" jdbcType="TINYINT" property="packageState"/>
        <result column="shiftOfPerformance" jdbcType="VARCHAR" property="shiftOfPerformance"/>
        <collection property="sowTaskItemPOS" ofType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO">
            <id column="sti_Id" jdbcType="BIGINT" property="id"/>
            <result column="sti_Org_Id" jdbcType="VARCHAR" property="orgId"/>
            <result column="sti_Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
            <result column="sti_SowTaskNo" jdbcType="VARCHAR" property="sowTaskNo"/>
            <result column="SowTask_Id" jdbcType="BIGINT" property="sowTaskId"/>
            <result column="sti_State" jdbcType="TINYINT" property="state"/>
            <result column="ProductSku_Id" jdbcType="BIGINT" property="productSkuId"/>
            <result column="ProductSpecification_Id" jdbcType="BIGINT" property="productSpecificationId"/>
            <result column="Owner_Id" jdbcType="BIGINT" property="ownerId"/>
            <result column="SecOwner_Id" jdbcType="BIGINT" property="secOwnerId"/>
            <result column="ProductName" jdbcType="VARCHAR" property="productName"/>
            <result column="SpecQuantity" jdbcType="DECIMAL" property="specQuantity"/>
            <result column="SpecName" jdbcType="VARCHAR" property="specName"/>
            <result column="PackageCount" jdbcType="DECIMAL" property="packageCount"/>
            <result column="PackageName" jdbcType="VARCHAR" property="packageName"/>
            <result column="UnitCount" jdbcType="DECIMAL" property="unitCount"/>
            <result column="UnitName" jdbcType="VARCHAR" property="unitName"/>
            <result column="UnitTotalCount" jdbcType="DECIMAL" property="unitTotalCount"/>
            <result column="OverUnitTotalCount" jdbcType="DECIMAL" property="overUnitTotalCount"/>
            <result column="sti_StartTime" jdbcType="TIMESTAMP" property="startTime"/>
            <result column="sti_CompleteTime" jdbcType="TIMESTAMP" property="completeTime"/>
            <result column="sti_Remark" jdbcType="VARCHAR" property="remark"/>
            <result column="sti_CreateUser" jdbcType="VARCHAR" property="createUser"/>
            <result column="sti_CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="sti_LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
            <result column="sti_LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        </collection>
    </resultMap>

    <resultMap id="BaseResultMapWithoutItem" type="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_id" jdbcType="INTEGER" property="orgId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="Batch_id" jdbcType="VARCHAR" property="batchId"/>
        <result column="BatchNo" jdbcType="VARCHAR" property="batchNo"/>
        <result column="SowTaskNo" jdbcType="VARCHAR" property="sowTaskNo"/>
        <result column="SowTaskName" jdbcType="VARCHAR" property="sowTaskName"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="OrderCount" jdbcType="INTEGER" property="orderCount"/>
        <result column="SkuCount" jdbcType="INTEGER" property="skuCount"/>
        <result column="PackageAmount" jdbcType="DECIMAL" property="packageAmount"/>
        <result column="UnitAmount" jdbcType="DECIMAL" property="unitAmount"/>
        <result column="Location_Id" jdbcType="BIGINT" property="locationId"/>
        <result column="LocationName" jdbcType="VARCHAR" property="locationName"/>
        <result column="OperationMode" jdbcType="TINYINT" property="operationMode"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="StartTime" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="CompleteTime" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="SowTaskType" jdbcType="TINYINT" property="sowTaskType"/>
        <result column="OperatorName" jdbcType="VARCHAR" property="operatorName"/>
        <result column="Operator_Id" jdbcType="VARCHAR" property="operatorId"/>
        <result column="AddressCount" jdbcType="INTEGER" property="addressCount"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="SorterId" jdbcType="INTEGER" property="sorterId"/>
        <result column="SorterName" jdbcType="VARCHAR" property="sorterName"/>
        <result column="PackageState" jdbcType="TINYINT" property="packageState"/>
        <result column="shiftOfPerformance" jdbcType="VARCHAR" property="shiftOfPerformance"/>
    </resultMap>

    <resultMap id="SowJoinOrderResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderInfoPO">
        <result column="RefOrderNo" jdbcType="VARCHAR" property="refOrderNo"/>
        <result column="OrderCreateTime" jdbcType="TIMESTAMP" property="orderCreateTime"/>
        <result column="ShopName" jdbcType="VARCHAR" property="shopName"/>
        <result column="DetailAddress" jdbcType="VARCHAR" property="detailAddress"/>
        <result column="OrderType" jdbcType="TINYINT" property="orderType"/>
        <result column="SkuCount" jdbcType="INTEGER" property="skuCount"/>
        <result column="PackageAmount" jdbcType="DECIMAL" property="packageAmount"/>
        <result column="UnitAmount" jdbcType="DECIMAL" property="unitAmount"/>
        <result column="SownSkuCount" jdbcType="INTEGER" property="sownSkuCount"/>
        <result column="SownPackageAmount" jdbcType="DECIMAL" property="sownPackageAmount"/>
        <result column="SownUnitAmount" jdbcType="DECIMAL" property="sownUnitAmount"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Org_id, Warehouse_Id, Batch_id, BatchNo, SowTaskNo, SowTaskName, State, OrderCount,
        SkuCount, PackageAmount, UnitAmount, Location_Id, LocationName, OperationMode,
        CreateTime, CreateUser, LastUpdateTime, LastUpdateUser, StartTime, CompleteTime,
        SowTaskType, OperatorName, Operator_Id, Remark, AddressCount, shiftOfPerformance
    </sql>

    <sql id="Column_List">
        st.Id, st.Org_id, st.Warehouse_Id, st.Batch_id, st.BatchNo, st.SowTaskNo, st.SowTaskName, st.State,
        st.OrderCount,
        st.SkuCount, st.PackageAmount, st.UnitAmount, st.Location_Id, st.LocationName, st.OperationMode,
        st.CreateTime, st.CreateUser, st.LastUpdateTime, st.LastUpdateUser, st.StartTime, st.CompleteTime,
        st.SowTaskType, st.OperatorName, st.Operator_Id, st.Remark, st.AddressCount, st.shiftOfPerformance,
        sti.Id as sti_Id, sti.Org_Id as sti_Org_Id, sti.Warehouse_Id as sti_Warehouse_Id, sti.SowTaskNo as
        sti_SowTaskNo, sti.SowTask_Id, sti.State as sti_State,
        sti.ProductSku_Id, sti.ProductSpecification_Id, sti.Owner_Id,
        sti.SecOwner_Id, sti.ProductName, sti.SpecQuantity, sti.SpecName, sti.PackageCount, sti.PackageName,
        sti.UnitCount, sti.UnitName, sti.UnitTotalCount, sti.OverUnitTotalCount, sti.StartTime as sti_StartTime,
        sti.CompleteTime as sti_CompleteTime,
        sti.Remark as sti_Remark, sti.CreateUser as sti_CreateUser, sti.CreateTime as sti_CreateTime, sti.LastUpdateUser
        as sti_LastUpdateUser, sti.LastUpdateTime as sti_LastUpdateTime
    </sql>

    <sql id="Prefix_Column_List">
        st.Id, st.Org_id, st.Warehouse_Id, st.Batch_id, st.BatchNo, st.SowTaskNo, st.SowTaskName, st.State,
        st.OrderCount,
        st.SkuCount, st.PackageAmount, st.UnitAmount, st.Location_Id, st.LocationName, st.OperationMode,
        st.CreateTime, st.CreateUser, st.LastUpdateTime, st.LastUpdateUser, st.StartTime, st.CompleteTime,
        st.SowTaskType, st.OperatorName, st.Operator_Id, st.Remark,
        st.AddressCount,st.SorterId,st.sorterName,st.packageState, st.shiftOfPerformance
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtask
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="listSowTaskByBatchId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtask
        where Batch_id = #{batchId,jdbcType=VARCHAR}
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="listNoFinishSowTaskCount" resultMap="BaseResultMap">
        select
        Location_Id,count(1) as OrderCount
        from sowtask
        where Warehouse_Id = #{warehouseId,jdbcType=INTEGER} AND State in (0,1,3) and Location_Id is not null
        GROUP BY Location_Id
    </select>

    <select id="listNoFinishSowTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtask
        where Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND State in (0,1,3)
        and Location_Id is not null
    </select>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO">
        insert into sowtask (
        Id, Org_id, Warehouse_Id,
        Batch_id, BatchNo, SowTaskNo,
        SowTaskName, State, OrderCount,
        SkuCount, PackageAmount, UnitAmount,
        Location_Id, LocationName, OperationMode,
        CreateTime, CreateUser, LastUpdateTime,
        LastUpdateUser, StartTime, CompleteTime,
        SowTaskType, OperatorName, Operator_Id,
        AddressCount, Remark, shiftOfPerformance
        ) values (
        #{id,jdbcType=BIGINT}, #{orgId,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER},
        #{batchId,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{sowTaskNo,jdbcType=VARCHAR},
        #{sowTaskName,jdbcType=VARCHAR}, #{state,jdbcType=TINYINT}, #{orderCount,jdbcType=INTEGER},
        #{skuCount,jdbcType=INTEGER}, #{packageAmount,jdbcType=DECIMAL}, #{unitAmount,jdbcType=DECIMAL},
        #{locationId,jdbcType=BIGINT}, #{locationName,jdbcType=VARCHAR}, #{operationMode,jdbcType=TINYINT}
        now(), #{createUser,jdbcType=VARCHAR}, now(),
        #{lastUpdateUser,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{completeTime,jdbcType=TIMESTAMP},
        #{sowTaskType,jdbcType=TINYINT}, #{operatorName,jdbcType=VARCHAR}, #{operatorId,jdbcType=INTEGER},
        #{addressCount,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR},
        #{shiftOfPerformance,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO">
        insert into sowtask
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="orgId != null">
                Org_id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="batchId != null">
                Batch_id,
            </if>
            <if test="batchNo != null">
                BatchNo,
            </if>
            <if test="sowTaskNo != null">
                SowTaskNo,
            </if>
            <if test="sowTaskName != null">
                SowTaskName,
            </if>
            <if test="state != null">
                State,
            </if>
            <if test="orderCount != null">
                OrderCount,
            </if>
            <if test="skuCount != null">
                SkuCount,
            </if>
            <if test="packageAmount != null">
                PackageAmount,
            </if>
            <if test="unitAmount != null">
                UnitAmount,
            </if>
            <if test="locationId != null">
                Location_Id,
            </if>
            <if test="locationName != null">
                LocationName,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="operationMode != null">
                OperationMode,
            </if>
            <if test="startTime != null">
                StartTime,
            </if>
            <if test="completeTime != null">
                CompleteTime,
            </if>
            <if test="sowTaskType != null">
                SowTaskType,
            </if>
            <if test="operatorName != null and operatorName != ''">
                OperatorName,
            </if>
            <if test="operatorId != null">
                Operator_Id,
            </if>
            <if test="addressCount != null">
                AddressCount,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="shiftOfPerformance != null">
                shiftOfPerformance,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="batchId != null">
                #{batchId,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null">
                #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="sowTaskNo != null">
                #{sowTaskNo,jdbcType=VARCHAR},
            </if>
            <if test="sowTaskName != null">
                #{sowTaskName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="orderCount != null">
                #{orderCount,jdbcType=INTEGER},
            </if>
            <if test="skuCount != null">
                #{skuCount,jdbcType=INTEGER},
            </if>
            <if test="packageAmount != null">
                #{packageAmount,jdbcType=DECIMAL},
            </if>
            <if test="unitAmount != null">
                #{unitAmount,jdbcType=DECIMAL},
            </if>
            <if test="locationId != null">
                #{locationId,jdbcType=BIGINT},
            </if>
            <if test="locationName != null">
                #{locationName,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operationMode != null">
                #{operationMode,jdbcType=TINYINT},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="completeTime != null">
                #{completeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sowTaskType != null != null">
                #{sowTaskType,jdbcType=TINYINT},
            </if>
            <if test="operatorName != null and operatorName != '' ">
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                #{operatorId,jdbcType=INTEGER},
            </if>
            <if test="addressCount != null">
                #{addressCount,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="shiftOfPerformance != null">
                #{shiftOfPerformance,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO">
        update sowtask
        <set>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="batchId != null">
                Batch_id = #{batchId,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null">
                BatchNo = #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="sowTaskNo != null">
                SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR},
            </if>
            <if test="sowTaskName != null">
                SowTaskName = #{sowTaskName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="orderCount != null">
                OrderCount = #{orderCount,jdbcType=INTEGER},
            </if>
            <if test="skuCount != null">
                SkuCount = #{skuCount,jdbcType=INTEGER},
            </if>
            <if test="packageAmount != null">
                PackageAmount = #{packageAmount,jdbcType=DECIMAL},
            </if>
            <if test="unitAmount != null">
                UnitAmount = #{unitAmount,jdbcType=DECIMAL},
            </if>
            <if test="locationId != null">
                Location_Id = #{locationId,jdbcType=BIGINT},
            </if>
            <if test="locationName != null">
                LocationName = #{locationName,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="operationMode != null">
                OperationMode = #{operationMode,jdbcType=TINYINT},
            </if>
            <if test="startTime != null">
                StartTime = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="completeTime != null">
                CompleteTime = #{completeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sowTaskType != null">
                SowTaskType = #{sowTaskType,jdbcType=TINYINT},
            </if>
            <if test="operatorName != null and operatorName != '' ">
                OperatorName = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                Operator_Id = #{operatorId,jdbcType=INTEGER},
            </if>
            <if test="addressCount != null">
                AddressCount = #{addressCount,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="packageState != null">
                PackageState = #{packageState,jdbcType=TINYINT},
            </if>
            <if test="sorterId != null">
                SorterId = #{sorterId,jdbcType=INTEGER},
            </if>
            <if test="sorterName != null and sorterName != ''">
                SorterName = #{sorterName,jdbcType=VARCHAR},
            </if>
            <if test="shiftOfPerformance != null and shiftOfPerformance != ''">
                ShiftOfPerformance = #{shiftOfPerformance,jdbcType=VARCHAR},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <select id="listSowTaskByBatchNos" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtask
        where BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findList" resultMap="BaseResultMap">
        select
        <include refid="Prefix_Column_List"/>
        from sowtask st
        <if test="(dto.productName != null and dto.productName != '') or dto.skuIds != null">
            inner join sowtaskitem sti on sti.SowTask_Id = st.Id
        </if>
        <if test="dto.orderNo != null and dto.orderNo != ''">
            inner join outstockorderitem osi on osi.SowTask_Id = st.Id
            inner join outstockorder os on os.Id = osi.Outstockorder_Id
        </if>
        where 1 = 1
        <if test="dto.orgId != null">
            AND st.Org_id = #{dto.orgId,jdbcType=INTEGER}
        </if>
        <if test="dto.warehouseId != null and dto.warehouseId !=0 ">
            AND st.Warehouse_Id= #{dto.warehouseId,jdbcType=VARCHAR}
        </if>
        <if test="dto.batchNo != null and dto.batchNo !='' ">
            AND st.BatchNo= #{dto.batchNo,jdbcType=VARCHAR}
        </if>
        <if test="dto.sowTaskNo != null and dto.sowTaskNo != '' ">
            AND st.SowTaskNo= #{dto.sowTaskNo,jdbcType=VARCHAR}
        </if>
        <if test="dto.sowTaskName != null and dto.sowTaskName != '' ">
            AND st.SowTaskName like concat('%',#{dto.sowTaskName,jdbcType=VARCHAR},'%')
        </if>
        <if test="dto.locationId != null and dto.locationId != 0 ">
            AND st.Location_Id= #{dto.locationId,jdbcType=VARCHAR}
        </if>
        <if test="dto.locationName != null and dto.locationName != '' ">
            AND st.LocationName like concat('%',#{dto.locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="dto.states != null">
            AND st.State in
            <foreach collection="dto.states" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="dto.startTime != null">
            AND st.CreateTime <![CDATA[ >= ]]> #{dto.startTime}
        </if>
        <if test="dto.endTime != null">
            AND st.CreateTime <![CDATA[ <= ]]> #{dto.endTime}
        </if>
        <if test="dto.sowTaskType != null">
            AND st.sowTaskType = #{dto.sowTaskType,jdbcType=TINYINT}
        </if>
        <if test="dto.operatorName != null and dto.operatorName != ''">
            AND st.OperatorName like concat('%',#{dto.operatorName,jdbcType=VARCHAR},'%')
        </if>
        <if test="dto.orderNo != null and dto.orderNo != ''">
            AND os.RefOrderNo = #{dto.orderNo,jdbcType=VARCHAR}
        </if>
        <if test="dto.productName != null and dto.productName != ''">
            AND sti.ProductName like concat('%',#{dto.productName,jdbcType=VARCHAR},'%')
        </if>
        <if test="dto.skuIds != null">
            AND sti.ProductSku_Id in
            <foreach collection="dto.skuIds" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="dto.sorterName != null and dto.sorterName != ''">
            AND st.SorterName like concat('%',#{dto.sorterName,jdbcType=VARCHAR},'%')
        </if>
        <if test="dto.packageStates != null and dto.packageStates.size() > 0">
            AND st.PackageState in
            <foreach collection="dto.packageStates" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>

        <if test="dto.warehouseAllocationType != null and dto.warehouseAllocationType != ''">
            and (st.WarehouseAllocationType = #{dto.warehouseAllocationType,jdbcType=INTEGER}
            or st.WarehouseAllocationType is null)
        </if>
        <if test="dto.shiftOfPerformance != null and dto.shiftOfPerformance != ''">
            AND st.shiftOfPerformance=#{dto.shiftOfPerformance}
        </if>
        ORDER BY st.CreateTime DESC
    </select>

    <select id="listOutStockOrderBySowTaskNo" resultMap="SowJoinOrderResultMap">
        SELECT
        DISTINCT oso.RefOrderNo,
        oso.OrderCreateTime,
        oso.ShopName,
        oso.DetailAddress,
        oso.OrderType,
        so.SkuCount,
        so.PackageAmount,
        so.UnitAmount,
        so.SownSkuCount,
        so.SownPackageAmount,
        so.SownUnitAmount
        FROM
        soworder so
        INNER JOIN outstockorderitem osoi ON osoi.SowOrder_id = so.id
        INNER JOIN outstockorder oso ON osoi.outstockorder_id = oso.id
        AND so.Org_Id = oso.Org_Id
        WHERE
        osoi.SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR} AND so.Org_Id = #{orgId,jdbcType=INTEGER}
    </select>

    <select id="findSowTaskByTaskNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtask
        where
        SowTaskNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="orgId != null ">
            AND Org_id = #{orgId}
        </if>
    </select>

    <insert id="insertSowTaskList" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO">
        insert into sowtask (Id, Org_id, Warehouse_Id,
        Batch_id, BatchNo, SowTaskNo,
        SowTaskName, State, OrderCount,
        SkuCount, PackageAmount, UnitAmount,
        Location_Id, LocationName, OperationMode,
        CreateUser, sowTaskType,
        OperatorName, Operator_Id,
        AddressCount, Remark,PackageState,
        warehouseAllocationType,
        shiftOfPerformance
        )
        values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.orgId,jdbcType=INTEGER}, #{item.warehouseId,jdbcType=INTEGER},
            #{item.batchId,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR}, #{item.sowTaskNo,jdbcType=VARCHAR},
            #{item.sowTaskName,jdbcType=VARCHAR}, #{item.state,jdbcType=TINYINT}, #{item.orderCount,jdbcType=INTEGER},
            #{item.skuCount,jdbcType=INTEGER}, #{item.packageAmount,jdbcType=DECIMAL},
            #{item.unitAmount,jdbcType=DECIMAL},
            #{item.locationId,jdbcType=BIGINT}, #{item.locationName,jdbcType=VARCHAR},
            #{item.operationMode,jdbcType=TINYINT},
            #{item.createUser,jdbcType=VARCHAR}, #{item.sowTaskType,jdbcType=TINYINT},
            #{item.operatorName,jdbcType=VARCHAR}, #{item.operatorId,jdbcType=INTEGER},
            #{item.addressCount,jdbcType=INTEGER}, #{item.remark,jdbcType=VARCHAR},
            #{item.packageState,jdbcType=TINYINT},
            #{item.warehouseAllocationType,jdbcType=INTEGER},
            #{item.shiftOfPerformance,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="updateSowTaskState">
        update sowtask set State = #{state,jdbcType=TINYINT} where SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR} and Org_Id
        = #{orgId,jdbcType=INTEGER}
    </update>

    <update id="updateSowTaskStateByDTO">
        update sowtask
        set
        State = #{updateSowerDTO.state,jdbcType=TINYINT},
        OperatorName = #{updateSowerDTO.operatorUserName,jdbcType=VARCHAR},
        Operator_Id = #{updateSowerDTO.operatorUserId,jdbcType=INTEGER},
        SowTaskNo = #{updateSowerDTO.sowTaskNo,jdbcType=VARCHAR}
        where Id = #{updateSowerDTO.sowTaskId, jdbcType=BIGINT}
        and Org_Id = #{updateSowerDTO.orgId,jdbcType=INTEGER}
    </update>

    <select id="findCanSowingTaskByLocationName" resultMap="BaseResultMap">
        SELECT
        st.Id, st.Org_id, st.Warehouse_Id, st.Batch_id, st.BatchNo, st.SowTaskNo, st.SowTaskName, st.State,
        st.OrderCount,
        st.SkuCount, st.PackageAmount, st.UnitAmount, st.Location_Id, st.LocationName,
        st.CreateTime, st.CreateUser, st.LastUpdateTime, st.LastUpdateUser,
        st.Operator_Id, st.OperatorName
        FROM
        sowtask st
        LEFT JOIN batchtask bt ON st.Id = bt.SowTask_Id AND st.Org_id = bt.Org_id
        LEFT JOIN batchtaskitem bti ON bti.Batchtask_id = bt.id AND st.Org_id = bti.Org_id
        WHERE
        st.Org_id = #{orgId,jdbcType=INTEGER}
        AND st.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND st.LocationName LIKE concat(#{locationName,jdbcType=VARCHAR},'%')
        AND bti.TaskState = 2
        AND st.State = 0
        AND st.SowTaskType in (0,3)
        ORDER BY st.CreateTime ASC LIMIT 1
    </select>


    <select id="findCanSowingTaskListByLocationName" resultMap="BaseResultMap">
        SELECT
        st.Id, st.Org_id, st.Warehouse_Id, st.Batch_id, st.BatchNo, st.SowTaskNo, st.SowTaskName, st.State,
        st.OrderCount,
        st.SkuCount, st.PackageAmount, st.UnitAmount, st.Location_Id, st.LocationName,
        st.CreateTime, st.CreateUser, st.LastUpdateTime, st.LastUpdateUser,
        st.Operator_Id, st.OperatorName, st.shiftOfPerformance
        FROM
        sowtask st
        LEFT JOIN batchtask bt ON st.Id = bt.SowTask_Id AND st.Org_id = bt.Org_id
        LEFT JOIN batchtaskitem bti ON bti.Batchtask_id = bt.id AND st.Org_id = bti.Org_id
        WHERE
        st.Org_id = #{orgId,jdbcType=INTEGER}
        AND st.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND st.LocationName LIKE concat(#{locationName,jdbcType=VARCHAR},'%')
        AND st.State = 0
        AND st.SowTaskType in
        <foreach collection="sowTaskType" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteBySowTaskIds">
        delete from sowtask
        where Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and Org_id = #{orgId,jdbcType=INTEGER}
    </delete>

    <select id="findSowTaskByBatchNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtask
        where BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findSowTaskByWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtask
        where Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and Org_id = #{orgId,jdbcType=INTEGER}
        and State = 0
        and Location_Id is not null
    </select>
    <select id="findSowTaskInfoByBatchTaskIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskInfoPO">
        select bt.id as batchTaskId, bt.BatchTaskNo as batchTaskNo, st.SowTaskNo as sowTaskNo, st.id as sowTaskId,
        st.Location_Id as sowLocationId, st.LocationName as sowLocationName
        from batchtask bt
        left join sowtask st on bt.SowTask_Id = st.id and st.Org_id = bt.Org_id
        where bt.Org_id = #{orgId,jdbcType=VARCHAR}
        and bt.id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
    <select id="findSowTaskByIdOrNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtask
        where Org_id = #{orgId,jdbcType=INTEGER}
        <if test="id != null">
            and Id = #{id,jdbcType=BIGINT}
        </if>
        <if test="sowTaskNo != null">
            and SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="findUsedContainerLocationIds" resultType="_long">
        SELECT
        so.LocationId
        FROM
        soworder so
        INNER JOIN sowtask st ON st.id = so.SowTask_Id
        AND st.Org_id = so.Org_Id
        AND st.Warehouse_Id = so.Warehouse_Id
        WHERE
        st.State = 1
        AND so.Org_Id = #{orgId,jdbcType=INTEGER}
        AND so.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </select>

    <select id="listSowTaskByNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtask
        where Org_id = #{orgId,jdbcType=INTEGER}
        and SowTaskNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <update id="assignmentSower">
        update sowtask
        set OperatorName = #{updateSowerDTO.sower,jdbcType=VARCHAR},
        Operator_Id = #{updateSowerDTO.sowerId,jdbcType=INTEGER},
        LastUpdateUser = #{updateSowerDTO.lastUpdateUser,jdbcType=VARCHAR},
        SorterId = #{updateSowerDTO.sorterId,jdbcType=INTEGER},
        SorterName = #{updateSowerDTO.sorterName,jdbcType=VARCHAR}
        where Org_id = #{updateSowerDTO.orgId,jdbcType=INTEGER}
        and SowTaskNo in
        <foreach collection="updateSowerDTO.sowTaskNos" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="findPickingSowTaskByWarehouseId" resultMap="BaseResultMap">
        select
        st.Id, st.Org_id, st.Warehouse_Id, st.Batch_id, st.BatchNo, st.SowTaskNo, st.SowTaskName, st.State,
        st.OrderCount,
        st.SkuCount, st.PackageAmount, st.UnitAmount, st.Location_Id, st.LocationName, st.OperationMode,
        st.CreateTime, st.CreateUser, st.LastUpdateTime, st.LastUpdateUser, st.StartTime, st.CompleteTime
        from sowtask st
        inner join batch b
        on b.Id = st.Batch_id
        where st.Org_id = #{orgId,jdbcType=INTEGER}
        and st.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and b.State in (2,7)
    </select>

    <select id="listBatchOrderByWarehouse"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOrderDTO">
        select
        distinct osi.Outstockorder_Id as outStockOrderId, os.RefOrderNo, os.State as orderState, os.OrderSequence,
        st.Batch_id as batchId, st.BatchNo AS batchNo, b.State as batchState,
        st.Id as sowtaskId, st.SowTaskNo, st.State as sowtaskState, st.Location_Id as sowLocationId, st.LocationName as
        sowLocationName,
        br.Id as billReviewId, br.State as billReviewState, br.Reviewer, br.Reviewer_Id as reviewerId,
        br.PackagedBoxAmount
        from sowtask st
        inner join batch b on b.Id = st.Batch_id
        inner join outstockorderitem osi on osi.Batch_id = b.Id and osi.SowTask_Id = st.Id
        inner join outstockorder os on os.id = osi.Outstockorder_Id
        left join billreview br on br.BusinessNo = os.RefOrderNo and br.RelatedBusinessNo = st.SowTaskNo
        where st.Org_id = #{orgId,jdbcType=INTEGER}
        and st.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and st.SowTaskType = 1
        and b.State in (2, 7)
    </select>

    <select id="getSowTaskByBatchTaskId" resultMap="BaseResultMap">
        select
        st.Id, st.Org_id, st.Warehouse_Id, st.Batch_id, st.BatchNo, st.SowTaskNo, st.SowTaskName, st.State,
        st.OrderCount,
        st.SkuCount, st.PackageAmount, st.UnitAmount, st.Location_Id, st.LocationName, st.OperationMode,
        st.CreateTime, st.CreateUser, st.LastUpdateTime, st.LastUpdateUser, st.StartTime, st.CompleteTime
        from sowtask st
        inner join batchtask bt on bt.SowTask_Id = st.id and bt.Org_id = st.Org_id
        where bt.id = #{batchTaskId,jdbcType=VARCHAR}
    </select>

    <select id="findSowingByLocationId" resultMap="BaseResultMap">
        select
        st.Id, st.Org_id, st.Warehouse_Id, st.Batch_id, st.BatchNo, st.SowTaskNo, st.SowTaskName, st.State,
        st.OrderCount,
        st.SkuCount, st.PackageAmount, st.UnitAmount, st.Location_Id, st.LocationName, st.OperationMode,
        st.CreateTime, st.CreateUser, st.LastUpdateTime, st.LastUpdateUser, st.StartTime, st.CompleteTime
        from sowtask st
        inner join batchtask bt on bt.SowTask_Id = st.id and bt.Org_id = st.Org_id
        where st.Org_id = #{orgId,jdbcType=INTEGER}
        and st.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and st.Location_Id = #{locationId,jdbcType=BIGINT}
        and st.State = 1
        <if test="sowTaskId != null">
            and st.id != #{sowTaskId,jdbcType=BIGINT}
        </if>
        union all
        select
        st.Id, st.Org_id, st.Warehouse_Id, st.Batch_id, st.BatchNo, st.SowTaskNo, st.SowTaskName, st.State,
        st.OrderCount,
        st.SkuCount, st.PackageAmount, st.UnitAmount, st.Location_Id, st.LocationName, st.OperationMode,
        st.CreateTime, st.CreateUser, st.LastUpdateTime, st.LastUpdateUser, st.StartTime, st.CompleteTime
        from sowtask st
        inner join batchtask bt on bt.SowTask_Id = st.id and bt.Org_id = st.Org_id
        where st.Org_id = #{orgId,jdbcType=INTEGER}
        and st.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and st.Location_Id = #{locationId,jdbcType=BIGINT}
        and st.State = 0
        and bt.TaskState in (1,2)
        <if test="sowTaskId != null">
            and st.id != #{sowTaskId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="findAllSowTaskByIds" resultMap="BaseResultMap">
        select
        <include refid="Column_List"/>
        from sowtask st
        inner join sowtaskitem sti on st.Id = sti.SowTask_Id
        where
        st.Id in
        <foreach collection="sowTaskIds" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
        <if test="orgId != null">
            and st.Org_id = #{orgId,jdbcType=INTEGER}
        </if>
    </select>

    <update id="batchUpdateCount">
        update sowtask
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="OrderCount =case" suffix="end,">
                <foreach collection="sowTaskUpdateDTOS" item="item" index="index">
                    when id=#{item.sowTaskId,jdbcType=BIGINT} then #{item.orderCount,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="SkuCount =case" suffix="end,">
                <foreach collection="sowTaskUpdateDTOS" item="item" index="index">
                    when id=#{item.sowTaskId,jdbcType=BIGINT} then #{item.skuCount,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="PackageAmount =case" suffix="end,">
                <foreach collection="sowTaskUpdateDTOS" item="item" index="index">
                    when id=#{item.sowTaskId,jdbcType=BIGINT} then #{item.packageAmount,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="UnitAmount =case" suffix="end,">
                <foreach collection="sowTaskUpdateDTOS" item="item" index="index">
                    when id=#{item.sowTaskId,jdbcType=BIGINT} then #{item.unitAmount,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="State =case" suffix="end,">
                <foreach collection="sowTaskUpdateDTOS" item="item" index="index">
                    <if test="item.state != null">
                        when id=#{item.sowTaskId,jdbcType=BIGINT} then #{item.state,jdbcType=TINYINT}
                    </if>
                    <if test="item.state == null">
                        when id=#{item.sowTaskId,jdbcType=BIGINT} then State
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="sowTaskUpdateDTOS" item="item" separator="," open="(" close=")">
            #{item.sowTaskId,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getAllById" resultMap="BaseResultMap">
        select
        <include refid="Column_List"/>
        from sowtask st
        left join sowtaskitem sti on st.Id = sti.SowTask_Id
        where st.Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getAllBySowTaskNo" resultMap="BaseResultMap">
        select
        <include refid="Column_List"/>
        from sowtask st
        left join sowtaskitem sti on st.Id = sti.SowTask_Id
        where st.SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR}
    </select>

    <update id="completeByIds">
        update sowtask
        set State = 2, StartTime = now(), CompleteTime = now()
        where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="receiveSowTask">
        update sowtask
        set LastUpdateUser = #{update.lastUpdateUser,jdbcType=VARCHAR},
        OperatorName = #{update.sower,jdbcType=VARCHAR},
        Operator_Id = #{update.sowerId,jdbcType=INTEGER}
        where Org_id = #{update.orgId,jdbcType=INTEGER}
        and SowTaskNo in
        <foreach collection="update.sowTaskNos" open="(" close=")" separator="," item="sowTaskNo">
            #{sowTaskNo,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="batchUpdateState">
        update sowtask
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="State =case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when id=#{item.id,jdbcType=BIGINT} then #{item.state,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="StartTime =case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when id=#{item.id,jdbcType=BIGINT} then #{item.startTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="CompleteTime =case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when id=#{item.id,jdbcType=BIGINT} then #{item.completeTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="OperatorName =case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.operatorName != null ">
                        when id=#{item.id,jdbcType=BIGINT} then #{item.operatorName,jdbcType=VARCHAR}
                    </if>
                    <if test="item.operatorName == null ">
                        when id=#{item.id,jdbcType=BIGINT} then OperatorName
                    </if>
                </foreach>
            </trim>
            <trim prefix="Operator_Id =case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.operatorId != null ">
                        when id=#{item.id,jdbcType=BIGINT} then #{item.operatorId,jdbcType=BIGINT}
                    </if>
                    <if test="item.operatorId == null ">
                        when id=#{item.id,jdbcType=BIGINT} then Operator_Id
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="updateList" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="findBatchNos" resultType="java.lang.String">
        select distinct BatchNo
        from sowtask
        where Org_id = #{orgId,jdbcType=INTEGER}
        and SowTaskNo in
        <foreach collection="sowTaskNos" item="sowTaskNo" open="(" separator="," close=")">
            #{sowTaskNo,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getSowTaskByStatus" resultMap="BaseResultMap">
        select SowTaskNo,SowTaskName
        from sowtask
        where Org_id = #{sow.orgId,jdbcType=INTEGER}
        AND Warehouse_Id = #{sow.warehouseId,jdbcType=INTEGER}
        AND LocationName LIKE concat(#{sow.locationName,jdbcType=VARCHAR},'%')
        AND State = #{status}
        AND SowTaskType in (0,3)
        and Operator_Id=#{sow.sowerId}
        ORDER BY CreateTime ASC LIMIT 1
    </select>
    <select id="queryWaitSortingTaskList"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO">
        select id, Org_id as orgId, Warehouse_Id as warehouseId, SkuCount as skuCount,
        PackageAmount as packageAmount, UnitAmount as unitAmount,Location_Id as locationId,LocationName as locationName
        from sowtask
        where Org_id = #{cityId}
        and Warehouse_Id = #{warehouseId} and State!=2 and Location_Id in
        <foreach collection="locationIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="queryWaitSortingTaskCount" resultType="java.lang.String">
        select Location_Id
        from sowtask
        left join sowtaskitem on sowtask.id = sowtaskitem.SowTask_Id
        where sowtaskitem.State != 2 and Location_Id is not null
        and sowtaskitem.Org_id = #{cityId}
        and sowtaskitem.Warehouse_Id = #{warehouseId}
        group by Location_Id
    </select>

    <select id="listSowTaskAndOrderItem" resultMap="BaseResultMap">
        select
        <include refid="Column_List"/>
        from sowtask st
        left join sowtaskitem sti on st.Id = sti.SowTask_Id
        where st.Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="findByLocationIds" resultType="_long">
        SELECT distinct st.Location_Id
        FROM sowtask st
        WHERE st.State in (0,1)
        AND st.Org_Id = #{orgId,jdbcType=INTEGER}
        AND st.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND st.Location_Id in
        <foreach collection="locationIds" item="locationId" open="(" separator="," close=")">
            #{locationId,jdbcType=BIGINT}
        </foreach>
    </select>
    <select id="queryWaitSortingTaskPage" resultType="java.lang.Long">
        select Location_Id
        from sowtask
        left join sowtaskitem on sowtask.id = sowtaskitem.SowTask_Id
        where sowtaskitem.State != 2
        and sowtaskitem.Org_id = #{cityId}
        and sowtaskitem.Warehouse_Id = #{warehouseId}
        group by Location_Id
        limit #{index},#{pageSize}
    </select>
    <select id="getSowTaskById" resultMap="BaseResultMap">
        select
        <include refid="Column_List"/>
        from sowtask st
        left join sowtaskitem sti on st.Id = sti.SowTask_Id
        where st.Id = #{id,jdbcType=BIGINT} AND st.Org_id=#{orgId}
    </select>

    <select id="findSowTaskByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtask
        where
        Id in
        <foreach collection="sowTaskIds" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="queryWaitSowTask" resultMap="BaseResultMap">
        SELECT
        st.Id, st.Org_id, st.Warehouse_Id, st.Batch_id, st.BatchNo, st.SowTaskNo, st.SowTaskName, st.State,
        st.OrderCount,
        st.SkuCount, st.PackageAmount, st.UnitAmount, st.Location_Id, st.LocationName,
        st.CreateTime, st.CreateUser, st.LastUpdateTime, st.LastUpdateUser,
        st.Operator_Id, st.OperatorName
        FROM sowtask st
        INNER JOIN sowtaskitem sti on st.Id = sti.SowTask_Id
        LEFT JOIN batchtask bt ON st.Id = bt.SowTask_Id AND st.Org_id = bt.Org_id
        LEFT JOIN batchtaskitem bti ON bti.Batchtask_id = bt.id AND st.Org_id = bti.Org_id
        WHERE
        st.Org_id = #{orgId,jdbcType=INTEGER}
        AND st.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND bti.TaskState = 2
        AND st.State in (0,1)
        AND st.SowTaskType in
        <foreach collection="sowTaskType" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="sowerId != null">
            AND sti.SorterId = #{sowerId} AND sti.State = 1
        </if>
        <if test="sowerId == null">
            AND sti.State = 0
            AND st.LocationName LIKE concat(#{locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="skuIds != null and !skuIds.isEmpty()">
            AND sti.ProductSku_Id in
            <foreach collection="skuIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY st.State DESC, st.CreateTime LIMIT 1
    </select>

    <select id="findSowTaskBySkuIds" resultMap="BaseResultMapWithoutItem">
        select
        task.Location_Id, task.LocationName
        from sowtask task
        left join sowtaskitem item on task.id = item.SowTask_Id
        where
        task.Org_id = #{orgId}
        and (task.Location_Id is not null or task.LocationName is not null)
        and ((item.State = 0 and item.SorterId is null) or (item.State = 1 and item.SorterId = #{operatorId}))
        and item.ProductSku_Id in
        <foreach collection="skuIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and task.State in
        <foreach collection="states" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by task.Location_Id,task.LocationName
    </select>

    <select id="pageListAppendableSowTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtask where Id in (
        select st.id
        from batchtask bt
        inner join sowtask st on bt.SowTask_Id = st.Id
        inner join batch bc on bt.BatchNo = bc.BatchNo
        <where>
            <if test="orgId != null">
                st.Org_id = #{orgId,jdbcType=INTEGER}
            </if>
            and st.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            and st.State in (0, 1)
            and bt.pickPattern = 2
            and st.sowTaskType = 0
            and bc.BatchOrderType = #{batchOrderType,jdbcType=INTEGER}
        </where>
        ) order by createtime desc
    </select>

    <select id="findSowTaskCountByBatchNo" resultType="java.lang.Integer">
        select
        count(1)
        from sowtask
        where BatchNo = #{batchNo,jdbcType=VARCHAR}
        and org_id = #{orgId, jdbcType=INTEGER}
    </select>

    <select id="findSowTaskByCon" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtask
        where warehouse_id in
        <foreach collection="warehouseIds" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and completetime between #{startTime} and #{endTime}
        <if test="states != null and states.size() > 0 ">
            and state in
            <foreach collection="states" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        order by createtime desc
    </select>
</mapper>