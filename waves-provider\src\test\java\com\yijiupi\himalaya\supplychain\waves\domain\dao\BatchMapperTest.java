package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.pickingEndingLocationInfoBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> 2018/3/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class BatchMapperTest {

    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;

    @Test
    public void insert() throws Exception {
        BatchPO batchPO = new BatchPO();
        batchPO.setId("123144");
        batchPO.setBatchName("波次1");
        batchPO.setBatchNo("bc123");
        // batchPO.setOrderAmount(1);
        batchPO.setSkuCount(1);
        batchPO.setUnitAmount(new BigDecimal(1));
        batchPO.setPackageAmount(new BigDecimal(2));
        batchPO.setOrgId(999);
        batchPO.setPickingType((byte)1);
        batchPO.setState((byte)1);
        batchPO.setPickingGroupStrategy((byte)2);

        BatchPO batchPO1 = new BatchPO();
        batchPO1.setId("1231442");
        batchPO1.setBatchName("波次1");
        batchPO1.setBatchNo("bc123");
        batchPO1.setState((byte)1);
        // batchPO1.setOrderAmount(1);
        batchPO1.setSkuCount(1);
        batchPO1.setUnitAmount(new BigDecimal(1));
        batchPO1.setPackageAmount(new BigDecimal(2));
        batchPO1.setOrgId(999);
        batchPO1.setPickingType((byte)1);
        batchPO1.setPickingGroupStrategy((byte)2);
        ArrayList<BatchPO> batchPOS = new ArrayList<>();
        batchPOS.add(batchPO);
        batchPOS.add(batchPO1);
        batchMapper.insertBatch(batchPOS);
    }

    @Test
    public void findPickingEndingLocationInfoByBatchTaskNosTest() {
        List<pickingEndingLocationInfoBO> rltList =
                batchTaskMapper.findPickingEndingLocationInfoByBatchTaskNos(Arrays.asList("BT998125052200002", "1231442"));
        Assert.assertNotNull(rltList);
    }
}