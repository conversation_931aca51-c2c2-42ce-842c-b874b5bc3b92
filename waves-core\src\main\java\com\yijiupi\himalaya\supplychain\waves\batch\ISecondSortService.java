package com.yijiupi.himalaya.supplychain.waves.batch;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderItemPickCountInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderItemPickCountInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.secondsort.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-06-02
 */
public interface ISecondSortService {

    SowSecondDTO beginSecondSortTask(SecondSortQueryDTO queryDTO);

    void updateSecondSortTaskPallet(SecondSortDTO dto);

    SowSecondDTO findSecondSortByParams(SecondSortQueryDTO queryDTO);

    List<PackageOrderItemDTO> listSowBoxcodeItems(SecondSortQueryDTO queryDTO);

    void completeSecondSow(SecondSowCompleteDTO completeDTO);

    /**
     * 查找未完成二次分拣拣货任务（PDA）
     *
     * @return
     */
    PageList<BatchTaskSortDTO> findBatchTaskSortList(BatchTaskSortQueryDTO batchTaskSortQueryDTO);

    /**
     * 查询二次分拣播种界面
     */
    List<SecondPickSowItemDTO> querySecondPickSow(SecondPickSowQueryDTO queryDTO);

    /**
     * 完成二次分拣播种项
     */
    void completeSecondPickSowItem(CompleteSecondPickSowItemDTO completeDTO);

    /**
     * 是否开启酒批二次分拣
     */
    Boolean isOpenSecondSort(Integer warehouseId);

    /**
     * 完成二次分拣播种
     */
    void completeSecondPickSow(CompleteSecondPickSowDTO completeDTO);

    /**
     * 二次分拣出库位同步
     */
    void secondPickLocationSync(SecondPickLocationSyncDTO syncDTO);

    /**
     * 查询订单项实际拣货数量
     *
     * @param queryDTO
     * @return
     */
    List<OrderItemPickCountInfoDTO> findOrderItemPickCount(OrderItemPickCountInfoQueryDTO queryDTO);
}
