package com.yijiupi.himalaya.supplychain.waves.enums;

import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * BatchCreateDTO.groupType 这个值
 * 
 * <AUTHOR>
 * @date 2024/1/2
 */
public enum BatchGroupTypeEnum {
    /**
     * 按线路
     */
    按线路(1),
    /**
     * 按片区
     */
    按片区(2),
    /**
     * 按司机
     */
    按司机(3),
    /**
     * 按用户(实际上是按地图调度)
     */
    按用户(4),
    /**
     * 按订单
     */
    按订单(5),
    /**
     * 不分组
     */
    不分组(6);

    private Integer type;

    BatchGroupTypeEnum(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public static int convertToOrderSelection(Integer type) {
        if (按线路.getType().equals(type)) {
            return WavesStrategyConstants.ORDERSELECTION_WAY;
        }
        if (按片区.getType().equals(type)) {
            return WavesStrategyConstants.ORDERSELECTION_AREA;
        }
        if (按司机.getType().equals(type)) {
            return WavesStrategyConstants.ORDERSELECTION_DRIVER;
        }
        if (按用户.getType().equals(type)) {
            return WavesStrategyConstants.ORDERSELECTION_USER;
        }
        if (按订单.getType().equals(type)) {
            return WavesStrategyConstants.ORDERSELECTION_ORDER;
        }
        if (不分组.getType().equals(type)) {
            return WavesStrategyConstants.ORDERSELECTION_NO;
        }

        return WavesStrategyConstants.ORDERSELECTION_NO;
    }

    public static BatchGroupTypeEnum convertFromOrderSelection(Integer type) {
        if (WavesStrategyConstants.ORDERSELECTION_WAY == type) {
            return 按线路;
        }
        if (WavesStrategyConstants.ORDERSELECTION_AREA == type) {
            return 按片区;
        }
        if (WavesStrategyConstants.ORDERSELECTION_DRIVER == type) {
            return 按司机;
        }
        if (WavesStrategyConstants.ORDERSELECTION_USER == type) {
            return 按用户;
        }
        if (WavesStrategyConstants.ORDERSELECTION_ORDER == type) {
            return 按订单;
        }
        if (WavesStrategyConstants.ORDERSELECTION_NO == type) {
            return 不分组;
        }

        return 不分组;
    }

}
