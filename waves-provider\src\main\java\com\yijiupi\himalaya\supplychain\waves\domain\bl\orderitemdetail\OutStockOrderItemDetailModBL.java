package com.yijiupi.himalaya.supplychain.waves.domain.bl.orderitemdetail;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.common.constant.RedisConstant;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeInfoResultDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.secowner.OrderWithItemOwnersDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.secowner.OrderWithItemOwnersItemDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.secowner.OrderWithItemOwnersItemDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.ordercenter.OrderCenterQueryBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.RecoverOrderItemDetailBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.OutStockOrderItemDetailConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemDetailCommMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.RecoverOrderStockOrderItemDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.utils.QuantityShareUtils;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/3/11
 */
@Service
public class OutStockOrderItemDetailModBL {

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OutStockOrderItemDetailCommMapper outStockOrderItemDetailCommMapper;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private OrderCenterQueryBL orderCenterQueryBL;

    @Reference
    private IBatchInventoryQueryService iBatchInventoryQueryService;
    private static final Logger LOGGER = LoggerFactory.getLogger(OutStockOrderItemDetailModBL.class);

    /**
     * 修复缺货detail数据，以中台的数据为准，中台的数据没有、或者数量与wms不一致，以orderItemTaskDetail的数据的数据，进行分摊
     *
     * @param bo
     */
    @DistributeLock(conditions = "#bo.batchNo", sleepMills = 3000, expireMills = 60000,
        key = RedisConstant.SUP_F + "updateBatchStateByBatchNo", lockType = DistributeLock.LockType.WAITLOCK)
    @Transactional(rollbackFor = Exception.class)
    public void lackFailedRecoverDetail(RecoverOrderItemDetailBO bo) {
        AssertUtils.notNull(bo.getBatchNo(), "波次信息不能为空！");
        try {
            List<OutStockOrderPO> outStockOrderPOS =
                outStockOrderMapper.findByBusinessIds(bo.getBusinessIds(), bo.getWarehouseId());
            // List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.findByOrderIds(bo.getOutStockOrderIds());
            if (CollectionUtils.isEmpty(outStockOrderPOS)) {
                return;
            }

            OutStockOrderPO outStockOrderPO = outStockOrderPOS.get(0);

            List<OutStockOrderItemPO> itemList =
                outStockOrderPOS.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList());
            // Map<Long,
            // BigDecimal> detailCountMap = itemList.stream().flatMap(m -> m.getItemDetails().stream())
            // .collect(Collectors.groupingBy(OutStockOrderItemDetailPO::getOutStockOrderItemId,
            // Collectors.mapping(OutStockOrderItemDetailPO::getUnitTotalCount,
            // Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

            Map<Long, List<OutStockOrderItemDetailPO>> detailGroupMap =
                itemList.stream().filter(item -> CollectionUtils.isNotEmpty(item.getItemDetails()))
                    .flatMap(m -> m.getItemDetails().stream())
                    .collect(Collectors.groupingBy(OutStockOrderItemDetailPO::getOutStockOrderItemId));

            // 找出需要处理的订单项
            List<OutStockOrderItemPO> needUpdateItemList = itemList.stream().filter(item -> {
                List<OutStockOrderItemDetailPO> detailList =
                    detailGroupMap.getOrDefault(item.getId(), Collections.emptyList());
                if (CollectionUtils.isEmpty(detailList)) {
                    return Boolean.TRUE;
                }
                BigDecimal detailCount = detailList.stream().map(OutStockOrderItemDetailPO::getUnitTotalCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                boolean anySmallThanZero = detailList.stream().map(OutStockOrderItemDetailPO::getUnitTotalCount)
                    .anyMatch(m -> m.compareTo(BigDecimal.ZERO) < 0);

                return item.getUnittotalcount().compareTo(detailCount) != 0
                    || detailCount.compareTo(BigDecimal.ZERO) < 0 || anySmallThanZero;
            }).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(needUpdateItemList)) {
                return;
            }

            Map<Long, OrderWithItemOwnersItemDTO> orderWithItemOwnersItemDTOMap =
                getOrderCenterSecOwnerByOrderIds(needUpdateItemList, outStockOrderPOS, bo);

            // Map<Long, OrderWithItemOwnersItemDTO> orderWithItemOwnersItemDTOMap = Collections.emptyMap();

            List<OutStockOrderItemDetailPO> updateDetailPOS = new ArrayList<>();
            // 区分场景：1、不管什么场景，供应链客户端直接标记缺货，不改订单，所以不会有订单项和明细总数不一致的情况，所以不用处理
            // 2、最简单的场景，可以覆盖大部分场景，就是 单独 的 拣货缺货 失败了。单独的拣货缺货失败了，detail 还在，只需更改数量
            //
            // 有两种场景：一个是订单明细被干掉了，一个是数量不一致
            // 只有触发了订单重新下推，重新下推的时候，把 订单项数量为0的 detail 给删除了
            List<OutStockOrderItemDetailPO> updateNotEqualDetailPOS =
                handleCountNotEqual(needUpdateItemList, orderWithItemOwnersItemDTOMap, bo);

            if (CollectionUtils.isNotEmpty(updateNotEqualDetailPOS)) {
                updateDetailPOS.addAll(updateNotEqualDetailPOS);
            }

            if (CollectionUtils.isEmpty(updateDetailPOS)) {
                LOGGER.info("没有要处理的数据");
                return;
            }

            List<Long> outStockOrderItemIds =
                needUpdateItemList.stream().map(OutStockOrderItemPO::getId).distinct().collect(Collectors.toList());
            List<OutStockOrderItemDetailPO> existDetailPOList =
                outStockOrderItemDetailCommMapper.findByItemIds(null, outStockOrderItemIds);

            // LOGGER.info("出库单数据比对处理detail数据:{}", JSON.toJSONString(updateDetailPOS));
            // 原来没有，直接插入
            insertOrUpdateBatch(existDetailPOList, updateDetailPOS);

            // Map<Long, Long> existDetailIdMap = existDetailPOList.stream()
            // .collect(Collectors.toMap(OutStockOrderItemDetailPO::getId, OutStockOrderItemDetailPO::getId));
            //
            // List<OutStockOrderItemDetailPO> newDetailList = updateDetailPOS.stream()
            // .filter(detail -> Objects.isNull(existDetailIdMap.get(detail.getId()))).collect(Collectors.toList());
            // List<OutStockOrderItemDetailPO> needUpdateDetailList = updateDetailPOS.stream()
            // .filter(detail -> Objects.nonNull(existDetailIdMap.get(detail.getId()))).collect(Collectors.toList());
            // if (CollectionUtils.isNotEmpty(newDetailList)) {
            // LOGGER.info("插入的新的detail数据:{}", JSON.toJSONString(newDetailList));
            // outStockOrderItemDetailCommMapper.insertBatch(newDetailList);
            // }
            // if (CollectionUtils.isNotEmpty(needUpdateDetailList)) {
            // outStockOrderItemDetailCommMapper.updateBatchByPOList(needUpdateDetailList);
            // }
            //
            // Map<Long, Long> updateIdMap = updateDetailPOS.stream()
            // .collect(Collectors.toMap(OutStockOrderItemDetailPO::getId, OutStockOrderItemDetailPO::getId));
            //
            // List<Long> deleteDetailIds = existDetailPOList.stream().map(OutStockOrderItemDetailPO::getId)
            // .filter(id -> Objects.isNull(updateIdMap.get(id))).collect(Collectors.toList());
            // if (CollectionUtils.isNotEmpty(deleteDetailIds)) {
            // LOGGER.info("删除的处理detail数据:{}", JSON.toJSONString(deleteDetailIds));
            // outStockOrderItemDetailCommMapper.deleteByItemDetailIds(deleteDetailIds, null);
            // }

        } catch (Exception e) {
            LOGGER.warn("缺货异常回滚出错: " + JSON.toJSONString(bo), e);
        }
    }

    /**
     * 在原来的基础是，去修改，新增，而不是简单的删除
     *
     * @param existDetailPOList
     * @param updateDetailPOS
     */
    public void insertOrUpdateBatch(List<OutStockOrderItemDetailPO> existDetailPOList,
        List<OutStockOrderItemDetailPO> updateDetailPOS) {

        if (CollectionUtils.isEmpty(updateDetailPOS)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(existDetailPOList)) {
            existDetailPOList.forEach(detailPO -> {
                outStockOrderItemDetailCommMapper.deleteByPrimaryKey(detailPO.getId());
            });
        }

        LOGGER.info("插入的新的detail数据:{}", JSON.toJSONString(updateDetailPOS));
        outStockOrderItemDetailCommMapper.insertBatch(updateDetailPOS);

        // if (CollectionUtils.isEmpty(existDetailPOList)) {
        // LOGGER.info("插入新的detail数据:{}", JSON.toJSONString(existDetailPOList));
        // outStockOrderItemDetailCommMapper.insertBatch(updateDetailPOS);
        // return;
        // }
        //
        // Map<String, OutStockOrderItemDetailPO> existDetailGroupMap =
        // existDetailPOList.stream().collect(Collectors.toMap(this::getKey, v -> v));
        //
        // Map<String, OutStockOrderItemDetailPO> updateDetailGroupMap =
        // updateDetailPOS.stream().collect(Collectors.toMap(this::getKey, v -> v));
        //
        // List<OutStockOrderItemDetailPO> finalUpdateDetailList = new ArrayList<>();
        // List<OutStockOrderItemDetailPO> finalUpdateToZeroList = new ArrayList<>();
        // List<OutStockOrderItemDetailPO> finalInsertDetailList = new ArrayList<>();
        // for (Map.Entry<String, OutStockOrderItemDetailPO> entry : updateDetailGroupMap.entrySet()) {
        // OutStockOrderItemDetailPO existDetailPO = existDetailGroupMap.get(entry.getKey());
        // if (Objects.nonNull(existDetailPO)) {
        // existDetailPO.setUnitTotalCount(entry.getValue().getUnitTotalCount());
        // finalUpdateDetailList.add(existDetailPO);
        // existDetailGroupMap.remove(entry.getKey());
        // } else {
        // finalInsertDetailList.add(entry.getValue());
        // }
        // }
        //
        // if (!org.springframework.util.CollectionUtils.isEmpty(existDetailGroupMap)) {
        // existDetailGroupMap.values().forEach(detail -> {
        // detail.setUnitTotalCount(BigDecimal.ZERO);
        // finalUpdateToZeroList.add(detail);
        // });
        // }
        //
        // if (CollectionUtils.isNotEmpty(finalUpdateDetailList)) {
        // LOGGER.info("更新成其他值的detail数据:{}", JSON.toJSONString(finalUpdateDetailList));
        // outStockOrderItemDetailCommMapper.updateBatchByPOList(finalUpdateDetailList);
        // }
        //
        // if (CollectionUtils.isNotEmpty(finalUpdateToZeroList)) {
        // LOGGER.info("更新成零的detail数据:{}", JSON.toJSONString(finalUpdateToZeroList));
        // outStockOrderItemDetailCommMapper.updateBatchByPOList(finalUpdateToZeroList);
        // }
        //
        // if (CollectionUtils.isNotEmpty(finalInsertDetailList)) {
        // LOGGER.info("插入的新的detail数据:{}", JSON.toJSONString(finalInsertDetailList));
        // outStockOrderItemDetailCommMapper.insertBatch(finalInsertDetailList);
        // }
    }

    private String getKey(OutStockOrderItemDetailPO detailPO) {
        return String.format("%s-%s", detailPO.getOutStockOrderItemId(), detailPO.getSecOwnerId());
    }

    // 返回值 key 是 outStockOrderItemId
    private Map<Long, OrderWithItemOwnersItemDTO> getOrderCenterSecOwnerByOrderIds(
        List<OutStockOrderItemPO> needUpdateItemList, List<OutStockOrderPO> outStockOrderPOS,
        RecoverOrderItemDetailBO bo) {
        if (BooleanUtils.isFalse(bo.getUseOrderCenterDetail())) {
            return Collections.emptyMap();
        }
        List<Long> outStockOrderIds = needUpdateItemList.stream().map(OutStockOrderItemPO::getOutstockorderId)
            .distinct().collect(Collectors.toList());
        Map<Long, String> idToBusinessIdMap =
            outStockOrderPOS.stream().collect(Collectors.toMap(OutStockOrderPO::getId, OutStockOrderPO::getBusinessId));
        Map<String, Long> businessIdToIdMap =
            outStockOrderPOS.stream().collect(Collectors.toMap(OutStockOrderPO::getBusinessId, OutStockOrderPO::getId));

        List<Long> businessIds =
            outStockOrderIds.stream().map(idToBusinessIdMap::get).map(Long::valueOf).collect(Collectors.toList());
        List<OrderWithItemOwnersDTO> orderWithItemOwnersDTOList = orderCenterQueryBL.getSecOwnerByOrderIds(businessIds);
        if (CollectionUtils.isEmpty(orderWithItemOwnersDTOList)) {
            return Collections.emptyMap();
        }

        Map<String, Long> businessItemIdToItemIdMap = needUpdateItemList.stream()
            .collect(Collectors.toMap(OutStockOrderItemPO::getBusinessItemId, OutStockOrderItemPO::getId));
        // key 是 outStockOrderItemId
        Map<Long, OrderWithItemOwnersItemDTO> orderWithItemOwnersItemDTOMap = orderWithItemOwnersDTOList.stream()
            .flatMap(m -> m.getOrderItems().stream())
            .filter(item -> Objects.nonNull(businessItemIdToItemIdMap.get(item.getOrderItemId().toString())))
            .collect(Collectors.toMap(item -> businessItemIdToItemIdMap.get(item.getOrderItemId().toString()), v -> v));

        return orderWithItemOwnersItemDTOMap;
    }

    //
    private List<OutStockOrderItemDetailPO> handleCountNotEqual(List<OutStockOrderItemPO> needUpdateItemList,
        Map<Long, OrderWithItemOwnersItemDTO> orderWithItemOwnersItemDTOMap, RecoverOrderItemDetailBO bo) {
        if (CollectionUtils.isEmpty(needUpdateItemList)) {
            return Collections.emptyList();
        }

        Map<Long, List<OrderItemTaskInfoPO>> orderItemTaskInfoGroupOrderItemIdMap =
            getOrderItemTaskInfoMap(needUpdateItemList);
        LOGGER.info("orderItemTaskInfoGroupOrderItemIdMap : {}",
            JSON.toJSONString(orderItemTaskInfoGroupOrderItemIdMap));

        List<OutStockOrderItemDetailPO> updateDetailPOS = new ArrayList<>();
        for (OutStockOrderItemPO item : needUpdateItemList) {
            OrderWithItemOwnersItemDTO orderWithItemOwnersItemDTO = orderWithItemOwnersItemDTOMap.get(item.getId());

            List<OrderItemTaskInfoPO> orderItemTaskInfoPOList = orderItemTaskInfoGroupOrderItemIdMap.get(item.getId());
            List<OutStockOrderItemDetailPO> detailPOList =
                genDetailWithOrderCenterInfo(orderWithItemOwnersItemDTO, orderItemTaskInfoPOList, item);
            if (CollectionUtils.isNotEmpty(detailPOList)) {
                updateDetailPOS.addAll(detailPOList);
            }
        }

        return updateDetailPOS;
    }

    private List<OutStockOrderItemDetailPO> genDetailWithOrderCenterInfo(
        OrderWithItemOwnersItemDTO orderWithItemOwnersItemDTO, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        OutStockOrderItemPO item) {
        if (Objects.isNull(orderWithItemOwnersItemDTO)
            || CollectionUtils.isEmpty(orderWithItemOwnersItemDTO.getOrderItemOwners())) {
            // 走自己生成的逻辑
            return genDetailWithWmsInfo(orderItemTaskInfoPOList, item);
        }

        List<OrderWithItemOwnersItemDetailDTO> orderCenterItemDetailList =
            orderWithItemOwnersItemDTO.getOrderItemOwners();

        BigDecimal centerUnitTotalCount = orderCenterItemDetailList.stream()
            .map(OrderWithItemOwnersItemDetailDTO::getCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (centerUnitTotalCount.compareTo(item.getUnittotalcount()) != 0) {
            // 走自己生成的逻辑
            return genDetailWithWmsInfo(orderItemTaskInfoPOList, item);
        }

        // 直接用中台的货主信息
        return OutStockOrderItemDetailConverter
            .convertOutStockOrderItemDetailWithOrderCenterDetail(orderWithItemOwnersItemDTO, item);
    }

    private List<OutStockOrderItemDetailPO> genDetailWithWmsInfo(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        OutStockOrderItemPO item) {
        List<OutStockOrderItemDetailPO> outStockOrderItemDetailPOS = item.getItemDetails();
        if (CollectionUtils.isEmpty(outStockOrderItemDetailPOS)) {
            List<OutStockOrderItemDetailPO> detailPOS = handleEmptyOrderItemDetail(orderItemTaskInfoPOList, item);
            return detailPOS;
        }

        List<OutStockOrderItemDetailPO> detailPOS = handleNotEmptyOrderItemDetail(orderItemTaskInfoPOList, item);
        return detailPOS;
    }

    private List<OutStockOrderItemDetailPO> handleNotEmptyOrderItemDetail(
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, OutStockOrderItemPO outStockOrderItemPO) {
        // 这里也分两种情况，第一种，detail数量
        BigDecimal unitTotalCount = orderItemTaskInfoPOList.stream().flatMap(m -> m.getDetailList().stream())
            .map(OrderItemTaskInfoDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);

        return OutStockOrderItemDetailConverter.convertExistOutStockOrderItemDetail(outStockOrderItemPO,
            orderItemTaskInfoPOList);
    }

    // 处理orderItemDetail为空的场景
    private List<OutStockOrderItemDetailPO> handleEmptyOrderItemDetail(
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, OutStockOrderItemPO outStockOrderItemPO) {
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return OutStockOrderItemDetailConverter.convertNewOutStockOrderItemDetail(outStockOrderItemPO);
        }

        return OutStockOrderItemDetailConverter.convertNewOutStockOrderItemDetail(outStockOrderItemPO,
            orderItemTaskInfoPOList);
    }

    // 处理orderItemTaskInfoPOList有负数的情况（同一个拣货任务项，多次拣货，有可能触发）
    // private List<OrderItemTaskInfoPO> resetOrderItemTaskInfoPOList(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList)
    // {
    // if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
    // return Collections.emptyList();
    // }
    // List<OrderItemTaskInfoDetailPO> detailPOList =
    // orderItemTaskInfoPOList.stream().flatMap(m -> m.getDetailList().stream()).collect(Collectors.toList());
    // if (CollectionUtils.isEmpty(detailPOList)) {
    // return Collections.emptyList();
    // }
    //
    // boolean hasNegative =
    // detailPOList.stream().anyMatch(detail -> detail.getUnitTotalCount().compareTo(BigDecimal.ZERO) < 0);
    // if (BooleanUtils.isFalse(hasNegative)) {
    // return orderItemTaskInfoPOList;
    // }
    //
    // List<OrderItemTaskInfoDetailPO> negativeList = detailPOList.stream()
    // .filter(detail -> detail.getUnitTotalCount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
    // List<OrderItemTaskInfoDetailPO> positiveList = detailPOList.stream()
    // .filter(detail -> detail.getUnitTotalCount().compareTo(BigDecimal.ZERO) >= 0).collect(Collectors.toList());
    // if (CollectionUtils.isEmpty(positiveList)) {
    // negativeList.forEach(m -> m.setUnitTotalCount(BigDecimal.ZERO));
    // return orderItemTaskInfoPOList;
    // }
    //
    // BigDecimal negativeCount = negativeList.stream().map(OrderItemTaskInfoDetailPO::getUnitTotalCount)
    // .reduce(BigDecimal.ZERO, BigDecimal::add);
    // BigDecimal positiveCount = positiveList.stream().map(OrderItemTaskInfoDetailPO::getUnitTotalCount)
    // .reduce(BigDecimal.ZERO, BigDecimal::add);
    // if (positiveCount.compareTo(negativeCount.abs()) <= 0) {
    //
    // }
    //
    // orderItemTaskInfoPOList.stream().collect(Collectors.toList());
    //
    // }

    private Map<Long, List<OrderItemTaskInfoPO>> getOrderItemTaskInfoMap(List<OutStockOrderItemPO> needUpdateItemList) {
        List<Long> orderItemIds =
            needUpdateItemList.stream().map(OutStockOrderItemPO::getId).distinct().collect(Collectors.toList());

        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(orderItemIds);
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return Collections.emptyMap();
        }
        Map<Long, List<OrderItemTaskInfoPO>> orderItemTaskInfoGroupOrderItemIdMap =
            orderItemTaskInfoPOList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId));

        return orderItemTaskInfoGroupOrderItemIdMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public void recoverDetailOutStockCount(RecoverOrderStockOrderItemDetailDTO dto) {
        List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.findByOrderIds(dto.getOutStockOrderIds());
        if (CollectionUtils.isEmpty(outStockOrderPOS)) {
            return;
        }
        List<OutStockOrderItemDetailPO> detailPOS = outStockOrderPOS.stream().flatMap(m -> m.getItems().stream())
            .flatMap(m -> m.getItemDetails().stream()).collect(Collectors.toList());
        List<OutStockOrderItemDetailPO> updateDetails = new ArrayList<>();

        detailPOS.forEach(detailPO -> {
            OutStockOrderItemDetailPO newDetail = new OutStockOrderItemDetailPO();
            newDetail.setId(detailPO.getId());
            newDetail.setOutStockUnitTotalCount(detailPO.getUnitTotalCount());
            updateDetails.add(newDetail);
        });

        if (CollectionUtils.isEmpty(updateDetails)) {
            return;
        }
        updateDetails.forEach(detailPO -> {
            outStockOrderItemDetailCommMapper.updateByPrimaryKeySelective(detailPO);
        });

    }

    /**
     * 修复detail
     * 
     * @param bo
     */
    @Transactional(rollbackFor = Exception.class)
    public void recoverDetail(RecoverOrderItemDetailBO bo) {
        List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.findByOrderIds(bo.getOutStockOrderIds());
        if (CollectionUtils.isEmpty(outStockOrderPOS)) {
            return;
        }
        LOGGER.info("查询出库单为:{}", JSON.toJSONString(bo.getOutStockOrderIds()));

        OutStockOrderPO outStockOrderPO = outStockOrderPOS.get(0);

        List<OutStockOrderItemDetailPO> totalList = new ArrayList<>();

        List<OutStockOrderItemDetailPO> notHaveBatchOrderList = handleNotHaveBatchList(outStockOrderPOS, bo);
        if (CollectionUtils.isNotEmpty(notHaveBatchOrderList)) {
            totalList.addAll(notHaveBatchOrderList);
        }

        List<OutStockOrderItemDetailPO> haveBatchOrderList = handleHaveBatchList(outStockOrderPOS, bo);
        if (CollectionUtils.isNotEmpty(haveBatchOrderList)) {
            totalList.addAll(haveBatchOrderList);
        }

        if (CollectionUtils.isEmpty(totalList)) {
            return;
        }

        LOGGER.info("生成detail为:{}", JSON.toJSONString(totalList));
        List<Long> outStockOrderItemIds = totalList.stream().map(OutStockOrderItemDetailPO::getOutStockOrderItemId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderItemDetailPO> existDetailPOList =
            outStockOrderItemDetailCommMapper.findByItemIds(null, outStockOrderItemIds);
        if (CollectionUtils.isNotEmpty(existDetailPOList)) {
            existDetailPOList.forEach(detailPO -> {
                outStockOrderItemDetailCommMapper.deleteByPrimaryKey(detailPO.getId());
            });
        }

        outStockOrderItemDetailCommMapper.insertBatch(totalList);

    }

    private List<OutStockOrderItemDetailPO> handleHaveBatchList(List<OutStockOrderPO> outStockOrderPOS,
        RecoverOrderItemDetailBO bo) {
        List<OutStockOrderPO> orderList =
            outStockOrderPOS.stream().filter(m -> StringUtils.isNotBlank(m.getBatchno())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyList();
        }

        List<OutStockOrderItemPO> needUpdateItemList =
            orderList.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList());
        needUpdateItemList = filterOutStockOrderItemList(needUpdateItemList, bo);

        LOGGER.info("需要处理的item为:{}", JSON.toJSONString(needUpdateItemList));

        Map<Long, OrderWithItemOwnersItemDTO> orderWithItemOwnersItemDTOMap = Collections.emptyMap();
        if (bo.getUseOrderCenterDetail()) {
            orderWithItemOwnersItemDTOMap = getOrderCenterSecOwnerByOrderIds(needUpdateItemList, outStockOrderPOS, bo);
        }

        // FIXME 直接查询orderItemTaskInfo，如果数量和item相等，直接取orderItemTaskInfodetail的数据
        List<Long> outStockOrderItemIds =
            needUpdateItemList.stream().map(OutStockOrderItemPO::getId).distinct().collect(Collectors.toList());

        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(outStockOrderItemIds);

        List<OutStockOrderItemDetailPO> updateNotEqualDetailPOS =
            handleCountNotEqual(needUpdateItemList, orderWithItemOwnersItemDTOMap, bo);
        List<OutStockOrderPO> outOrderList = outStockOrderPOS.stream()
            .filter(m -> m.getState() == OutStockOrderStateEnum.已出库.getType()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outOrderList)) {
            return updateNotEqualDetailPOS;
        }
        Map<Long, Long> orderItemIdMap = outOrderList.stream().flatMap(m -> m.getItems().stream())
            .collect(Collectors.toMap(OutStockOrderItemPO::getId, OutStockOrderItemPO::getId));
        updateNotEqualDetailPOS.forEach(detailPO -> {
            if (Objects.nonNull(orderItemIdMap.get(detailPO.getOutStockOrderItemId()))) {
                detailPO.setOutStockUnitTotalCount(detailPO.getUnitTotalCount());
            }
        });

        return updateNotEqualDetailPOS;
    }

    private List<OutStockOrderItemPO> filterOutStockOrderItemList(List<OutStockOrderItemPO> itemList,
        RecoverOrderItemDetailBO bo) {
        Map<Long, List<OutStockOrderItemDetailPO>> detailGroupMap = itemList.stream()
            .filter(item -> CollectionUtils.isNotEmpty(item.getItemDetails())).flatMap(m -> m.getItemDetails().stream())
            .collect(Collectors.groupingBy(OutStockOrderItemDetailPO::getOutStockOrderItemId));

        // 找出需要处理的订单项
        List<OutStockOrderItemPO> needUpdateItemList = itemList.stream().filter(item -> {
            List<OutStockOrderItemDetailPO> detailList =
                detailGroupMap.getOrDefault(item.getId(), Collections.emptyList());
            if (CollectionUtils.isEmpty(detailList)) {
                return Boolean.TRUE;
            }
            BigDecimal detailCount = detailList.stream().map(OutStockOrderItemDetailPO::getUnitTotalCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            boolean anySmallThanZero = detailList.stream().map(OutStockOrderItemDetailPO::getUnitTotalCount)
                .anyMatch(m -> m.compareTo(BigDecimal.ZERO) < 0);

            return item.getUnittotalcount().compareTo(detailCount) != 0 || detailCount.compareTo(BigDecimal.ZERO) < 0
                || anySmallThanZero || bo.isHandleEqualDetail();
        }).collect(Collectors.toList());

        return needUpdateItemList;
    }

    private List<OutStockOrderItemDetailPO> handleNotHaveBatchList(List<OutStockOrderPO> outStockOrderPOS,
        RecoverOrderItemDetailBO bo) {
        List<OutStockOrderPO> orderList =
            outStockOrderPOS.stream().filter(m -> StringUtils.isBlank(m.getBatchno())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyList();
        }

        List<OutStockOrderItemPO> needUpdateItemList =
            orderList.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList());
        needUpdateItemList = filterOutStockOrderItemList(needUpdateItemList, bo);
        Map<Long, OrderWithItemOwnersItemDTO> orderWithItemOwnersItemDTOMap = Collections.emptyMap();
        if (bo.getUseOrderCenterDetail()) {
            orderWithItemOwnersItemDTOMap = getOrderCenterSecOwnerByOrderIds(needUpdateItemList, outStockOrderPOS, bo);
        }

        if (org.springframework.util.CollectionUtils.isEmpty(orderWithItemOwnersItemDTOMap)) {
            return Collections.emptyList();
        }

        List<OutStockOrderItemDetailPO> totalList = new ArrayList<>();
        for (OutStockOrderItemPO item : needUpdateItemList) {
            OrderWithItemOwnersItemDTO orderWithItemOwnersItemDTO = orderWithItemOwnersItemDTOMap.get(item.getId());

            BigDecimal detailTotalCount = item.getItemDetails().stream()
                .map(OutStockOrderItemDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (detailTotalCount.compareTo(item.getUnittotalcount()) == 0) {
                if (BooleanUtils.isFalse(bo.isHandleEqualDetail())) {
                    continue;
                }
            }

            List<OrderWithItemOwnersItemDetailDTO> orderCenterItemDetailList =
                orderWithItemOwnersItemDTO.getOrderItemOwners();

            BigDecimal centerUnitTotalCount = orderCenterItemDetailList.stream()
                .map(OrderWithItemOwnersItemDetailDTO::getCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 直接用中台的货主信息
            List<OutStockOrderItemDetailPO> newDetailList = OutStockOrderItemDetailConverter
                .convertOutStockOrderItemDetailWithOrderCenterDetail(orderWithItemOwnersItemDTO, item);

            // 如果detail和item的数量相等
            if (centerUnitTotalCount.compareTo(item.getUnittotalcount()) == 0) {
                totalList.addAll(newDetailList);
                continue;
            }

            List<QuantityShareUtils.CountHelper> helperList = newDetailList.stream()
                .map(m -> new QuantityShareUtils.CountHelper(m.getId().toString(), m.getUnitTotalCount()))
                .collect(Collectors.toList());
            QuantityShareUtils.CountShareResultHelper countShareResultHelper =
                QuantityShareUtils.shareCount(helperList, item.getUnittotalcount());

            Map<String, OutStockOrderItemDetailPO> orderItemTaskInfoPOMap =
                newDetailList.stream().collect(Collectors.toMap(k -> k.getId().toString(), v -> v));

            countShareResultHelper.getShareHelperList().forEach(share -> {
                OutStockOrderItemDetailPO orderItemTaskInfoPO = orderItemTaskInfoPOMap.get(share.getId());
                orderItemTaskInfoPO.setUnitTotalCount(share.getCount());
            });

            totalList.addAll(newDetailList);
        }

        return totalList;
    }

    private boolean doNotHaveBatch(Integer state) {
        if (state.byteValue() == OutStockOrderStateEnum.待调度.getType()) {
            return Boolean.TRUE;
        }
        if (state.byteValue() == OutStockOrderStateEnum.调拨中.getType()) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    public List<OutStockOrderItemDetailDTO> recoverByBatchInventoryRecord(RecoverOrderItemDetailBO bo) {
        List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.findByOrderIds(bo.getOutStockOrderIds());
        if (CollectionUtils.isEmpty(outStockOrderPOS)) {
            return Collections.emptyList();
        }

        List<OutStockOrderItemDetailPO> outStockOrderItemDetailPOS = new ArrayList<>();
        for (OutStockOrderPO outStockOrderPO : outStockOrderPOS) {
            ProductStoreBatchChangeInfoQueryDTO queryDTO = new ProductStoreBatchChangeInfoQueryDTO();
            queryDTO.setOrderNo(outStockOrderPO.getReforderno());
            // queryDTO.setJiupiEventType();
            queryDTO.setWarehouseId(outStockOrderPO.getWarehouseId());
            queryDTO.setOrgId(outStockOrderPO.getOrgId());
            List<ProductStoreBatchChangeInfoResultDTO> recordList =
                iBatchInventoryQueryService.findChangeRecordInfoByOrderInfo(queryDTO);
            if (CollectionUtils.isEmpty(recordList)) {
                LOGGER.info("订单{} 库存变更记录不存在", outStockOrderPO.getReforderno());
                continue;
            }
            LOGGER.info("查询出的订单为{}", JSON.toJSONString(outStockOrderPO));
            LOGGER.info("订单{} 过滤前的记录为：{}", outStockOrderPO.getReforderno(), JSON.toJSONString(recordList));
            List<ProductStoreBatchChangeInfoResultDTO> filterRecordList = filterRecordList(recordList);
            // LOGGER.info("订单{} 过滤后的记录为：{}", outStockOrderPO.getReforderno(), JSON.toJSONString(filterRecordList));
            Map<Long, List<ProductStoreBatchChangeInfoResultDTO>> skuRecordGroupMap = filterRecordList.stream()
                .collect(Collectors.groupingBy(ProductStoreBatchChangeInfoResultDTO::getProductSkuId));
            List<OutStockOrderItemDetailPO> tmpList = splitDetail(skuRecordGroupMap, outStockOrderPO);
            if (CollectionUtils.isNotEmpty(tmpList)) {
                outStockOrderItemDetailPOS.addAll(tmpList);
            }
        }

        List<Long> outStockOrderItemIds = outStockOrderItemDetailPOS.stream()
            .map(OutStockOrderItemDetailPO::getOutStockOrderItemId).distinct().collect(Collectors.toList());
        List<OutStockOrderItemDetailPO> existDetailPOList =
            outStockOrderItemDetailCommMapper.findByItemIds(null, outStockOrderItemIds);
        if (CollectionUtils.isNotEmpty(existDetailPOList)) {
            existDetailPOList.forEach(detailPO -> {
                outStockOrderItemDetailCommMapper.deleteByPrimaryKey(detailPO.getId());
            });
        }

        outStockOrderItemDetailCommMapper.insertBatch(outStockOrderItemDetailPOS);

        return OutStockOrderItemDetailConverter.convertToDTO(outStockOrderItemDetailPOS);
    }

    // 这里有特殊场景。 比如订单项有12件，但变更记录有四条。 A货位 6 件，B 货位 -6件，A 货位 6件，A货位6件
    private List<ProductStoreBatchChangeInfoResultDTO>
        filterRecordList(List<ProductStoreBatchChangeInfoResultDTO> recordList) {
        Map<String, List<ProductStoreBatchChangeInfoResultDTO>> recordResultList =
            recordList.stream().collect(Collectors.groupingBy(
                k -> String.format("%s-%s-%s", k.getProductSkuId(), k.getProductSpecificationId(), k.getSecOwnerId())));
        List<ProductStoreBatchChangeInfoResultDTO> totalList = new ArrayList<>();
        for (Map.Entry<String, List<ProductStoreBatchChangeInfoResultDTO>> entry : recordResultList.entrySet()) {
            List<ProductStoreBatchChangeInfoResultDTO> valueList = entry.getValue();
            BigDecimal positiveCount =
                valueList.stream().map(ProductStoreBatchChangeInfoResultDTO::getTotalCountMinUnit)
                    .filter(totalCountMinUnit -> totalCountMinUnit.compareTo(BigDecimal.ZERO) > 0)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (positiveCount.compareTo(BigDecimal.ZERO) == 0) {
                valueList.forEach(record -> record.setTotalCountMinUnit(record.getTotalCountMinUnit().abs()));
                totalList.addAll(valueList);
                continue;
            }

            List<ProductStoreBatchChangeInfoResultDTO> negativeList = valueList.stream()
                .filter(m -> m.getTotalCountMinUnit().compareTo(BigDecimal.ZERO) <= 0).collect(Collectors.toList());
            negativeList.forEach(item -> item.setTotalCountMinUnit(item.getTotalCountMinUnit().abs()));

            List<QuantityShareUtils.CountHelper> helperList = negativeList.stream()
                .map(m -> new QuantityShareUtils.CountHelper(m.getId(), m.getTotalCountMinUnit().abs()))
                .collect(Collectors.toList());
            QuantityShareUtils.CountShareResultHelper countShareResultHelper =
                QuantityShareUtils.shareCount(helperList, positiveCount.abs());

            Map<String, ProductStoreBatchChangeInfoResultDTO> changeRecordMap =
                negativeList.stream().collect(Collectors.toMap(ProductStoreBatchChangeInfoResultDTO::getId, v -> v));

            countShareResultHelper.getShareHelperList().forEach(share -> {
                ProductStoreBatchChangeInfoResultDTO resultDTO = changeRecordMap.get(share.getId());
                resultDTO.setTotalCountMinUnit(share.getCount().abs());
                totalList.add(resultDTO);
            });
        }

        return totalList;
    }

    private List<OutStockOrderItemDetailPO> splitDetail(
        Map<Long, List<ProductStoreBatchChangeInfoResultDTO>> skuRecordGroupMap, OutStockOrderPO outStockOrderPO) {
        List<OutStockOrderItemDetailPO> totalList = new ArrayList<>();
        for (OutStockOrderItemPO outStockOrderItem : outStockOrderPO.getItems()) {
            List<ProductStoreBatchChangeInfoResultDTO> changeInfoResultDTOS =
                skuRecordGroupMap.get(outStockOrderItem.getSkuid());
            if (CollectionUtils.isEmpty(changeInfoResultDTOS)) {
                LOGGER.info("订单{} 的 订单项 {} {} 库存变更记录不存在", outStockOrderPO.getReforderno(), outStockOrderItem.getId(),
                    outStockOrderItem.getProductname());
                continue;
            }
            BigDecimal totalUnitCount =
                changeInfoResultDTOS.stream().map(ProductStoreBatchChangeInfoResultDTO::getTotalCountMinUnit)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).abs();
            if (totalUnitCount.compareTo(outStockOrderItem.getUnittotalcount().abs()) == 0) {
                List<OutStockOrderItemDetailPO> tmpList = changeInfoResultDTOS.stream()
                    .filter(m -> m.getTotalCountMinUnit().compareTo(BigDecimal.ZERO) != 0).map(record -> {
                        OutStockOrderItemDetailPO detailPO = new OutStockOrderItemDetailPO();

                        detailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.OUT_STOCK_ORDER_ITEM_DETAIL));
                        detailPO.setOrgId(outStockOrderItem.getOrgId());
                        detailPO.setOutStockOrderItemId(outStockOrderItem.getId());
                        detailPO.setLocationId(outStockOrderItem.getLocationId());
                        detailPO.setLocationName(outStockOrderItem.getLocationName());
                        detailPO.setBatchTime(outStockOrderItem.getBatchTime());
                        detailPO.setProductionDate(outStockOrderItem.getProductionDate());

                        detailPO.setUnitTotalCount(record.getTotalCountMinUnit().abs());
                        detailPO.setProductSpecificationId(outStockOrderItem.getProductSpecificationId());
                        detailPO.setOwnerId(record.getOwnerId());
                        detailPO.setSecOwnerId(record.getSecOwnerId());
                        return detailPO;
                    }).collect(Collectors.toList());

                totalList.addAll(tmpList);
            } else {
                List<QuantityShareUtils.CountHelper> helperList = changeInfoResultDTOS.stream()
                    .map(m -> new QuantityShareUtils.CountHelper(m.getId(), m.getTotalCountMinUnit().abs()))
                    .collect(Collectors.toList());
                QuantityShareUtils.CountShareResultHelper countShareResultHelper =
                    QuantityShareUtils.shareCount(helperList, outStockOrderItem.getUnittotalcount());

                Map<String, ProductStoreBatchChangeInfoResultDTO> changeRecordMap = changeInfoResultDTOS.stream()
                    .collect(Collectors.toMap(ProductStoreBatchChangeInfoResultDTO::getId, v -> v));

                countShareResultHelper.getShareHelperList().stream()
                    .filter(QuantityShareUtils.CountHelper::isHaveChanged).forEach(share -> {
                        ProductStoreBatchChangeInfoResultDTO productStoreBatchChangeInfoResultDTO =
                            changeRecordMap.get(share.getId());

                        OutStockOrderItemDetailPO detailPO = new OutStockOrderItemDetailPO();

                        detailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.OUT_STOCK_ORDER_ITEM_DETAIL));
                        detailPO.setOrgId(outStockOrderItem.getOrgId());
                        detailPO.setOutStockOrderItemId(outStockOrderItem.getId());
                        detailPO.setLocationId(outStockOrderItem.getLocationId());
                        detailPO.setLocationName(outStockOrderItem.getLocationName());
                        detailPO.setBatchTime(outStockOrderItem.getBatchTime());
                        detailPO.setProductionDate(outStockOrderItem.getProductionDate());

                        detailPO.setUnitTotalCount(productStoreBatchChangeInfoResultDTO.getTotalCountMinUnit().abs()
                            .subtract(share.getCount()));
                        detailPO.setProductSpecificationId(outStockOrderItem.getProductSpecificationId());
                        detailPO.setOwnerId(productStoreBatchChangeInfoResultDTO.getOwnerId());
                        detailPO.setSecOwnerId(productStoreBatchChangeInfoResultDTO.getSecOwnerId());

                        productStoreBatchChangeInfoResultDTO.setTotalCountMinUnit(share.getCount());

                        totalList.add(detailPO);
                    });
            }
        }

        return totalList;
    }

}
