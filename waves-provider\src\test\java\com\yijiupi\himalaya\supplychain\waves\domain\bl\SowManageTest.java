package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchOrderProcessService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchFinishedBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.DigitalBatchTaskQueryBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtaskquery.BatchTaskSortQueryBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchBaseBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.AppendSowTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.CreateBatchByManualOperationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.CreateBatchByRefOrderNoBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.AppendSowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByRefOrderNoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.DeleteBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskPickInfoForPDADTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.ScanOrderForReviewDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.ScanOrderForReviewQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.ScanReviewToSetLocationPalletInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskReceiveDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;

/**
 * <AUTHOR>
 * @title: SowManageTest
 * @description:
 * @date 2022-11-10 14:36
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class SowManageTest {

    @Autowired
    private SowManagerBL sowManagerBL;
    @Autowired
    private AppendSowTaskBL appendSowTaskBL;
    @Autowired
    private CreateBatchByManualOperationBL createBatchByManualOperationBL;
    @Autowired
    private CreateBatchByRefOrderNoBL createBatchByRefOrderNoBL;
    @Autowired
    private IBatchOrderProcessService iBatchOrderProcessService;
    @Autowired
    private BatchOrderBL batchOrderBL;

    @Test
    public void findCanSowingTaskTest() {
        SowTaskReceiveDTO taskReceiveDTO = new SowTaskReceiveDTO();
        taskReceiveDTO.setLocationName("JH2123-231");
        taskReceiveDTO.setWarehouseId(9981);
        taskReceiveDTO.setOrgId(998);
        SowTaskDTO sowTaskDTO = sowManagerBL.findCanSowingTask(taskReceiveDTO);

        Assertions.assertThat(sowTaskDTO).isNotNull();
    }

    @Test
    public void pickInTimeTest() {
        String json =
            "{\"allocationFlag\":false,\"deliveryCarId\":96947491317020675,\"driverName\":\"生产加盟测试肖\",\"groupType\":4,\"operateUser\":\"测试李\",\"operateUserId\":67795309,\"orderList\":[],\"orderPickFlag\":3,\"passPickType\":1,\"pickingGroupStrategy\":3,\"pickingType\":2,\"refOrderNos\":[\"998433700020\",\"998434100001\",\"998434100003\"],\"title\":\"金杯KK4-生产加盟测试肖\",\"warehouseId\":9981}";

        BatchCreateByRefOrderNoDTO refOrderNoDTO = JSON.parseObject(json, BatchCreateByRefOrderNoDTO.class);
        CreateBatchBaseBO createBatchBaseBO = new CreateBatchBaseBO();
        createBatchBaseBO.setBatchCreateByRefOrderNoDTO(refOrderNoDTO);

        iBatchOrderProcessService.createBatchByRefOrderNo(refOrderNoDTO);
    }

    // 按产品，普通生成波次
    @Test
    public void createBatchTest() {
        String createJson =
            "{\"orderIdList\":[\"5387771748839159458\"],\"cityId\":998,\"warehouseId\":9981,\"pickingType\":2,\"pickingGroupStrategy\":2,\"operateUser\":\"李响\",\"passPickType\":1}";
        BatchCreateDTO batchCreateDTO = JSON.parseObject(createJson, new TypeReference<BatchCreateDTO>() {}.getType());
        iBatchOrderProcessService.createBatch(batchCreateDTO);
    }

    // 按产品，线路生成波次
    // :{"orderIdList":["5362345310605082277"],"cityId":998,"warehouseId":9981,"pickingType":2,"pickingGroupStrategy":2,"operateUser":"李响","passPickType":1,"groupType":1}
    @Test
    public void createBatchProductRoute() {
        String createJson =
            "{\"orderIdList\":[\"5362345310605082277\"],\"cityId\":998,\"warehouseId\":9981,\"pickingType\":2,\"pickingGroupStrategy\":2,\"operateUser\":\"李响\",\"passPickType\":1,\"groupType\":1}";
        BatchCreateDTO batchCreateDTO = JSON.parseObject(createJson, new TypeReference<BatchCreateDTO>() {}.getType());
        iBatchOrderProcessService.createBatch(batchCreateDTO);
    }

    // 按产品，线路生成波次
    // :{"orderIdList":["5362345310605082277"],"cityId":998,"warehouseId":9981,"pickingType":2,"pickingGroupStrategy":2,"operateUser":"李响","passPickType":1,"groupType":1}
    @Test
    public void createBatchProductRoute1() {
        String createJson =
            "{\"orderIdList\":[\"5380834445509704235\"],\"cityId\":998,\"warehouseId\":9981,\"pickingType\":2,\"pickingGroupStrategy\":2,\"operateUser\":\"李响\",\"passPickType\":1,\"groupType\":1}";
        BatchCreateDTO batchCreateDTO = JSON.parseObject(createJson, new TypeReference<BatchCreateDTO>() {}.getType());
        iBatchOrderProcessService.createBatch(batchCreateDTO);
    }

    @Test
    public void appendBatchTest() {
        AppendSowTaskDTO appendSowTaskDTO = new AppendSowTaskDTO();
        appendSowTaskDTO.setSowTaskId(1732691985451100001L);
        appendSowTaskDTO.setOptUserId(125);
        appendSowTaskDTO.setOrgId(998);
        appendSowTaskDTO.setWarehouseId(9981);
        appendSowTaskDTO.setOutStockOrderIds(Collections.singletonList("5378374891623003468"));
        appendSowTaskBL.appendSowTask(appendSowTaskDTO);
    }

    @Test
    public void appendBatchAfterBeginTest() {
        AppendSowTaskDTO appendSowTaskDTO = new AppendSowTaskDTO();
        appendSowTaskDTO.setSowTaskId(1732759382406100001L);
        appendSowTaskDTO.setOptUserId(125);
        appendSowTaskDTO.setOrgId(998);
        appendSowTaskDTO.setWarehouseId(9981);
        appendSowTaskDTO.setOutStockOrderIds(Collections.singletonList("5375840950194039456"));
        appendSowTaskBL.appendSowTask(appendSowTaskDTO);
    }

    @Test
    public void appendBatchAfterBeginProductRouteTest() {
        AppendSowTaskDTO appendSowTaskDTO = new AppendSowTaskDTO();
        appendSowTaskDTO.setSowTaskId(1733126409179100001L);
        appendSowTaskDTO.setOptUserId(125);
        appendSowTaskDTO.setOrgId(998);
        appendSowTaskDTO.setWarehouseId(9981);
        appendSowTaskDTO.setOutStockOrderIds(Arrays.asList("5378374891623003468", "5387771748839159458"));
        appendSowTaskBL.appendSowTask(appendSowTaskDTO);
    }

    @Test
    public void appendBatchAfterBeginProductRouteTest2() {
        AppendSowTaskDTO appendSowTaskDTO = new AppendSowTaskDTO();
        appendSowTaskDTO.setSowTaskId(1733129258748100005L);
        appendSowTaskDTO.setOptUserId(125);
        appendSowTaskDTO.setOrgId(998);
        appendSowTaskDTO.setWarehouseId(9981);
        appendSowTaskDTO.setOutStockOrderIds(Arrays.asList("5395413739012422765"));
        appendSowTaskBL.appendSowTask(appendSowTaskDTO);
    }

    @Test
    public void deleteBatchTest() {
        DeleteBatchDTO deleteBatchDTO = new DeleteBatchDTO();
        deleteBatchDTO.setBatchNoList(Arrays.asList("BC998124120200007", "BC998124120200008", "BC998124120200009"));
        deleteBatchDTO.setOperateUserId(125);

        batchOrderBL.deleteBatchOrder(deleteBatchDTO);
    }

    // 调度生成波次，3 的场景，进行追加："5395766394628930657", "5380834445509704235"，998433800028
    @Test
    public void createBatchByTmsSchedule() {
        String json =
            "{\"allocationFlag\":false,\"deliveryCarId\":1,\"driverName\":\"test周啊橙\",\"groupType\":4,\"operateUser\":\"测试账号\",\"operateUserId\":14399,\"orderList\":[],\"orderPickFlag\":3,\"passPickType\":1,\"pickingGroupStrategy\":3,\"pickingType\":2,\"refOrderNos\":[\"998433800028\"],\"title\":\"测试自带车-test周啊橙\",\"warehouseId\":9981}";
        BatchCreateByRefOrderNoDTO dto = JSON.parseObject(json, BatchCreateByRefOrderNoDTO.class);
        iBatchOrderProcessService.createBatchByRefOrderNo(dto);

        Assertions.assertThat(json).isNotNull();
    }

    @Test
    public void appendBatchByTmsSchedule() {
        AppendSowTaskDTO appendSowTaskDTO = new AppendSowTaskDTO();
        appendSowTaskDTO.setSowTaskId(1733366626590100048L);
        appendSowTaskDTO.setOptUserId(125);
        appendSowTaskDTO.setOrgId(998);
        appendSowTaskDTO.setWarehouseId(9981);
        appendSowTaskDTO.setOutStockOrderIds(Arrays.asList("5395766394628930657", "5380834445509704235"));
        appendSowTaskBL.appendSowTask(appendSowTaskDTO);
    }

    @Test
    public void createBatchProductRoute2() {
        String createJson =
            "{\"orderIdList\":[\"5395766432205700195\"],\"cityId\":998,\"warehouseId\":9981,\"pickingType\":2,\"pickingGroupStrategy\":2,\"operateUser\":\"李响\",\"passPickType\":1,\"groupType\":1}";
        BatchCreateDTO batchCreateDTO = JSON.parseObject(createJson, new TypeReference<BatchCreateDTO>() {}.getType());
        iBatchOrderProcessService.createBatch(batchCreateDTO);
    }

    @Test
    public void appendCreateBatchProductRoute2() {
        AppendSowTaskDTO appendSowTaskDTO = new AppendSowTaskDTO();
        appendSowTaskDTO.setSowTaskId(1733469864112100023L);
        appendSowTaskDTO.setOptUserId(125);
        appendSowTaskDTO.setOrgId(998);
        appendSowTaskDTO.setWarehouseId(9981);
        appendSowTaskDTO
            .setOutStockOrderIds(Arrays.asList("5395766464346651747", "5395766495413861480", "5395767719387266152"));
        appendSowTaskBL.appendSowTask(appendSowTaskDTO);
    }

    @Autowired
    private BatchTaskSortQueryBL batchTaskSortQueryBL;

    @Test
    public void findPickBatchTaskList() {
        BatchTaskSortQueryDTO batchTaskSortQueryDTO = new BatchTaskSortQueryDTO();
        batchTaskSortQueryDTO.setCityId(998);
        batchTaskSortQueryDTO.setPageNum(1);
        batchTaskSortQueryDTO.setPageSize(100);
        batchTaskSortQueryDTO.setType(1);
        batchTaskSortQueryDTO.setUserId(125);
        batchTaskSortQueryDTO.setWarehouseId(9981);
        BatchTaskPickInfoForPDADTO result = batchTaskSortQueryBL.findPickBatchTaskList(batchTaskSortQueryDTO);
        Assertions.assertThat(result).isNotNull();
    }

    @Reference
    private IProductLocationService iProductLocationService;

    @Test
    public void locationTest() {
        LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
        Byte express = ConditionStateEnum.否.getType();
        locationQueryDTO.setExpress(express);
        locationQueryDTO.setCityId(998);
        locationQueryDTO.setWarehouseId(9981);
        locationQueryDTO.setSubcategory(LocationAreaEnum.集货区.getType().byteValue());
        locationQueryDTO.setAreaState(LocationStateEnum.启用.getType());
        locationQueryDTO.setState(LocationStateEnum.启用.getType());
        List<LocationReturnDTO> locationList = iProductLocationService.findLocationList(locationQueryDTO);

        Assertions.assertThat(locationList).isNotNull();
    }

    @Autowired
    private BatchFinishedBL batchFinishedBL;

    @Test
    public void lockTest() throws InterruptedException {
        System.err.println("=======");
        batchFinishedBL.completeWave("123", null, null);
        // CountDownLatch latch = new CountDownLatch(1);
        // Thread[] threads = new Thread[10];
        // for (int i = 0; i < threads.length; i++) {
        // threads[i] = new Thread() {
        // @Override
        // public void run() {
        // try {
        // latch.await();
        // } catch (Exception e) {
        // System.out.println(e.getMessage());
        // }
        // System.err.println(Thread.currentThread() + "开始执行");
        // batchFinishedBL.completeWave("123", null, null);
        // System.err.println(Thread.currentThread() + "执行==");
        // }
        // };
        // }
        // for (int i = 0; i < threads.length; i++) {
        // threads[i].start();
        // }
        // latch.countDown();
        // CountDownLatch latch2 = new CountDownLatch(1);
        // latch2.await();
    }

    @Autowired
    private BatchOrderInfoBL batchOrderInfoBL;

    @Test
    public void queryScanOrderInfoFroReviewTest() {
        ScanOrderForReviewQueryDTO queryDTO = new ScanOrderForReviewQueryDTO();
        queryDTO.setOrgId(998);
        queryDTO.setWarehouseId(9981);
        queryDTO.setScanCode("998429700002");
        ScanOrderForReviewDTO result = batchOrderInfoBL.queryScanOrderInfoFroReview(queryDTO);

        Assertions.assertThat(result).isNotNull();
    }

    @Test
    public void scanReviewToSetLocationPalletInfoTest() {
        ScanReviewToSetLocationPalletInfoDTO dto = new ScanReviewToSetLocationPalletInfoDTO();
        dto.setLocationId(5112738417253127192L);
        dto.setLocationName("CKA09-05");
        dto.setWarehouseId(9981);
        dto.setOptUserId(125);
        dto.setPalletNoList(Arrays.asList("4444", "3333"));
        dto.setOrderNo("998429700002");
        batchOrderInfoBL.scanReviewToSetLocationPalletInfo(dto);
    }

    @Autowired
    private DigitalBatchTaskQueryBL digitalBatchTaskQueryBL;

    @Test
    public void findDigitalBatchTaskListTest() {
        DigitalBatchTaskQueryDTO queryDTO = new DigitalBatchTaskQueryDTO();
        queryDTO.setWarehouseId(9981);
        // queryDTO.setBatchTaskIds(Arrays.asList("2025011500284", "2025011500288", "2025012100196", "2025012100169",
        // "2025012000378", "2025012000365", "2025012000103"));
        queryDTO.setStateList(Arrays.asList((byte)0, (byte)1));
        List<DigitalBatchTaskDTO> batchTaskDTOList = digitalBatchTaskQueryBL.findDigitalBatchTaskList(queryDTO);

        Assertions.assertThat(batchTaskDTOList).isNotNull();
    }

    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;

}
