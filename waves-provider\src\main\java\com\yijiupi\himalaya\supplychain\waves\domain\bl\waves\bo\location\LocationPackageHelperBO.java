package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;

import java.util.List;

/**
 * <AUTHOR>
 * @title: LocationPackageHelperBO
 * @description:
 * @date 2023-02-16 15:58
 */
public class LocationPackageHelperBO {
    /**
     * 大件订单项
     */
    private List<OutStockOrderItemPO> packageItemList;
    /**
     *  拆出来匹配通道的的小件订单项
     */
    private List<OutStockOrderItemPO> unitItemList;

    public LocationPackageHelperBO() {
    }

    public LocationPackageHelperBO(List<OutStockOrderItemPO> packageItemList, List<OutStockOrderItemPO> unitItemList) {
        this.packageItemList = packageItemList;
        this.unitItemList = unitItemList;
    }

    /**
     * 获取 大件订单项
     *
     * @return packageItemList 大件订单项
     */
    public List<OutStockOrderItemPO> getPackageItemList() {
        return this.packageItemList;
    }

    /**
     * 设置 大件订单项
     *
     * @param packageItemList 大件订单项
     */
    public void setPackageItemList(List<OutStockOrderItemPO> packageItemList) {
        this.packageItemList = packageItemList;
    }

    /**
     * 获取 拆出来匹配通道的的小件订单项
     *
     * @return unitItemList 拆出来匹配通道的的小件订单项
     */
    public List<OutStockOrderItemPO> getUnitItemList() {
        return this.unitItemList;
    }

    /**
     * 设置 拆出来匹配通道的的小件订单项
     *
     * @param unitItemList 拆出来匹配通道的的小件订单项
     */
    public void setUnitItemList(List<OutStockOrderItemPO> unitItemList) {
        this.unitItemList = unitItemList;
    }
}
