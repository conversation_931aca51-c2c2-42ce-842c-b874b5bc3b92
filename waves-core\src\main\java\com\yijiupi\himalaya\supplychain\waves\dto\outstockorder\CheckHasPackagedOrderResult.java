package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-02-22 15:05
 **/
public class CheckHasPackagedOrderResult implements Serializable {

    /**
     * 箱号
     */
    private Set<String> boxCodeNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 是否赠品
     */
    private Boolean isGift;

    /**
     * 是否打包
     */
    private Boolean hasPackaged;

    /**
     * 缺货小单位数量
     */
    private Integer lackUnitCount;

    /**
     * 包装规格系数
     */
    private BigDecimal packageSpec;

    /**
     * 销售规格系数
     */
    private BigDecimal saleSpec;

    public Set<String> getBoxCodeNo() {
        return boxCodeNo;
    }

    public void setBoxCodeNo(Set<String> boxCodeNo) {
        this.boxCodeNo = boxCodeNo;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Boolean getGift() {
        return isGift;
    }

    public void setGift(Boolean gift) {
        isGift = gift;
    }

    public Boolean getHasPackaged() {
        return hasPackaged;
    }

    public void setHasPackaged(Boolean hasPackaged) {
        this.hasPackaged = hasPackaged;
    }

    public Integer getLackUnitCount() {
        return lackUnitCount;
    }

    public void setLackUnitCount(Integer lackUnitCount) {
        this.lackUnitCount = lackUnitCount;
    }

    public BigDecimal getPackageSpec() {
        return packageSpec;
    }

    public void setPackageSpec(BigDecimal packageSpec) {
        this.packageSpec = packageSpec;
    }

    public BigDecimal getSaleSpec() {
        return saleSpec;
    }

    public void setSaleSpec(BigDecimal saleSpec) {
        this.saleSpec = saleSpec;
    }
}
