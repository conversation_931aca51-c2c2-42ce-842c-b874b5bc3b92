package com.yijiupi.himalaya.supplychain.waves.domain.bl.meituan;

import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.SourceType;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.ErrorCodeEnum;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-01-15 17:21
 **/
@Service
public class MTOrderVerifyBL {

    /**
     * 美团订单不允许标记缺货
     *
     * @param orders 出库单
     */
    public void markPartSendVerify(List<OutStockOrderPO> orders) {
        List<OutStockOrderPO> mtOrders = orders.stream()
                .filter(it -> SourceType.isMeiTuanLikeOrder(it.getOrderSourceType()))
                .collect(Collectors.toList());
        if (mtOrders.isEmpty()) {
            return;
        }
        List<String> orderNos = mtOrders.stream().map(OutStockOrderPO::getReforderno).collect(Collectors.toList());
        throw ErrorCodeEnum.XX_ORDER_CANNOT_LACK.newValidateException("X", orderNos, "T", getSourceTypeName(mtOrders));
    }

    public static String getSourceTypeName(List<OutStockOrderPO> orders) {
        boolean hasMTOrder = orders.stream().anyMatch(it -> SourceType.MEI_TUAN.valueEquals(it.getOrderSourceType()));
        boolean hasZPOrder = orders.stream().anyMatch(it -> SourceType.ZHANG_PI.valueEquals(it.getOrderSourceType()));
        String sourceTypeName = "";
        if (hasMTOrder) {
            sourceTypeName += SourceType.MEI_TUAN.getName();
        }
        if (hasZPOrder) {
            if (!sourceTypeName.isEmpty()) {
                sourceTypeName += "、";
            }
            sourceTypeName += SourceType.ZHANG_PI.getName();
        }
        // 三种情况
        //1. 只有美团订单, 美团
        //2. 只有掌批订单, 掌批多多
        //3. 同时包含美团、掌批订单, 美团、掌批多多
        return sourceTypeName;
    }

}
