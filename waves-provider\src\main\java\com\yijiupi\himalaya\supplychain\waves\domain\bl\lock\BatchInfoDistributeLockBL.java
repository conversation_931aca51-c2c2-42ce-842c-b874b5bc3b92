package com.yijiupi.himalaya.supplychain.waves.domain.bl.lock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.common.constant.RedisConstant;
import com.yijiupi.himalaya.distributedlock.service.RedisLockService;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/21
 */
@Service
public class BatchInfoDistributeLockBL {

    @Autowired
    private RedisLockService redisLockService;

    public void lockAppendSowTask(String batchNo) {
        String key = RedisConstant.SUP_F + "batchTaskComplete:";
        redisLockService.distributedLock(key, batchNo, 6 * 1000);
    }

    public void releaseLock(String batchNo) {
        String key = RedisConstant.SUP_F + "batchTaskComplete:";
        redisLockService.releaseLock(key, batchNo);
    }

    public void releaseLockBatch(List<String> batchNoList) {
        String key = RedisConstant.SUP_F + "batchTaskComplete:";
        for (String s : batchNoList) {
            redisLockService.releaseLock(key, s);
        }
    }
}
