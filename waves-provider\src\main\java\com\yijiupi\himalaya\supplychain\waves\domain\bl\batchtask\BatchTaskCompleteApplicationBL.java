package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.distributedlock.constants.Constants;
import com.yijiupi.himalaya.supplychain.waves.advice.MarkMethodInvokeFlag;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.CompleteBatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskCompleteResultDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskConfirmDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskConstants;
import com.yijiupi.supplychain.serviceutils.constant.redis.RedisConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 拣货任务完成聚合BL，为了波次维度加锁
 * 
 * <AUTHOR>
 * @date 2024/9/2
 */
@Service
public class BatchTaskCompleteApplicationBL {
    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;
    @Autowired
    private BatchTaskCompleteBL batchTaskCompleteBL;
    @Autowired
    private BatchTaskMapper batchTaskMapper;

    /**
     * 供应链客户端完成拣货
     * 
     * @param batchTaskConfirmDTO
     * @param batchNo
     * @return
     */
    @MarkMethodInvokeFlag(key = RedisConstant.SUP_F + "UserOnlineCache", warehouseId = "#batchTaskConfirmDTO.warehouseId", userId = "#batchTaskConfirmDTO.operateUserId")
    @DistributeLock(conditions = "#batchNo", sleepMills = 3000, key = RedisConstant.SUP_F + "batchTaskComplete:")
    public BatchTaskCompleteResultDTO batchTaskComplete(BatchTaskConfirmDTO batchTaskConfirmDTO, String batchNo) {
        List<BatchTaskPO> batchTaskPOList =
            batchTaskMapper.findTasksByBatchTaskNo(batchTaskConfirmDTO.getBatchTaskNo());
        if (CollectionUtils.isEmpty(batchTaskPOList)) {
            throw new BusinessException("获取拣货任务为空");
        }
        batchTaskConfirmDTO
            .setBatchTaskIds(batchTaskPOList.stream().map(BatchTaskPO::getId).distinct().collect(Collectors.toList()));
        return batchOrderTaskBL.batchTaskComplete(batchTaskConfirmDTO);
    }

    /**
     * PDA完成拣货
     * 
     * @param batchTaskCompleteDTO
     * @param batchNo
     */
    @MarkMethodInvokeFlag(key = RedisConstant.SUP_F + "UserOnlineCache", warehouseId = "#batchTaskCompleteDTO.warehouseId", userId = "#batchTaskCompleteDTO.optUserId")
    @DistributeLock(conditions = "#batchNo", sleepMills = 3000, key = RedisConstant.SUP_F + "batchTaskComplete:")
    public void completeBatchTask(BatchTaskCompleteDTO batchTaskCompleteDTO, String batchNo) {
        batchTaskCompleteBL.completeBatchTask(batchTaskCompleteDTO);
    }

}
