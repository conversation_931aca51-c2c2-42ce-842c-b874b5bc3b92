package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * <AUTHOR>
 * @title: BatchTaskPickPatternEnum
 * @description: 拣货模式
 * @date 2022-10-19 10:41
 */
public enum BatchTaskPickPatternEnum {
    /**
     * 枚举
     */
    人机混拣((byte) 1), 人工拣货((byte) 2), 机器人拣货((byte) 3), 电子标签((byte) 4), agv拣货((byte) 5);

    /**
     * type
     */
    private Byte type;

    BatchTaskPickPatternEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }
}
