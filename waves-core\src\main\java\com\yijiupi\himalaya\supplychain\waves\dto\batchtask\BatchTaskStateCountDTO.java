package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;

/**
 * 拣货任务状态数量
 *
 * <AUTHOR>
 * @date 3/3/21 2:19 PM
 */
public class BatchTaskStateCountDTO implements Serializable {
    private static final long serialVersionUID = 5070994068414072164L;

    /**
     * 拣货任务状态
     */
    private Byte taskState;

    /**
     * 任务数量
     */
    private Integer count;

    public Byte getTaskState() {
        return taskState;
    }

    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
