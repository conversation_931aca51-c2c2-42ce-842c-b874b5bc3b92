package com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-11-23 10:51
 **/
@Service
public class OutStockLocationManager {

    @Autowired
    private List<OutStockLocationBaseBL> outStockLocationStrategies;

    @Resource
    private OutStockLocationOnlineBL outStockLocationOnlineBL;

    private static final Logger logger = LoggerFactory.getLogger(OutStockLocationManager.class);

    public List<OutStockOrderLocationDTO> getOutLocation(List<OutStockOrderLocationDTO> locations) {
        // logger.info("获取出库位, 入参: {}", JSON.toJSONString(locations));
        if (CollectionUtils.isEmpty(locations)) {
            return Collections.emptyList();
        }
        Integer warehouseId = locations.get(0).getWarehouseId();
        OutStockLocationBaseBL locationBL = outStockLocationStrategies.stream().filter(it -> it.support(warehouseId))
            .findFirst().orElse(outStockLocationOnlineBL);
        // logger.info("获取出库位, handler: {}", locationBL.getClass());
        List<OutStockOrderLocationDTO> outStockLocation;
        try {
            outStockLocation = locationBL.getOutLocation(locations);
        } catch (Throwable t) {
            logger.error("获取出库位失败", t);
            return outStockLocationOnlineBL.getOutLocation(locations);
        }
        // logger.info("获取出库位结果: {}", JSON.toJSONString(outStockLocation, SerializerFeature.WriteMapNullValue));
        return outStockLocation;
    }

}
