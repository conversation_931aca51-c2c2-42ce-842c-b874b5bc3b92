package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/19
 */
public class BatchTaskPickInfoForPDADTO implements Serializable {
    /**
     * 拣货任务信息
     */
    private List<BatchTaskSortDTO> batchTaskSortDTOList;
    /**
     * 总数量
     */
    private Integer totalCount;
    /**
     * 订单信息
     */
    private List<BatchTaskPickOrderInfoForPDADTO> orderInfoList;

    /**
     * 获取 拣货任务信息
     *
     * @return batchTaskSortDTOList 拣货任务信息
     */
    public List<BatchTaskSortDTO> getBatchTaskSortDTOList() {
        return this.batchTaskSortDTOList;
    }

    /**
     * 设置 拣货任务信息
     *
     * @param batchTaskSortDTOList 拣货任务信息
     */
    public void setBatchTaskSortDTOList(List<BatchTaskSortDTO> batchTaskSortDTOList) {
        this.batchTaskSortDTOList = batchTaskSortDTOList;
    }

    /**
     * 获取 总数量
     *
     * @return totalCount 总数量
     */
    public Integer getTotalCount() {
        return this.totalCount;
    }

    /**
     * 设置 总数量
     *
     * @param totalCount 总数量
     */
    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 获取 订单信息
     *
     * @return orderInfoList 订单信息
     */
    public List<BatchTaskPickOrderInfoForPDADTO> getOrderInfoList() {
        return this.orderInfoList;
    }

    /**
     * 设置 订单信息
     *
     * @param orderInfoList 订单信息
     */
    public void setOrderInfoList(List<BatchTaskPickOrderInfoForPDADTO> orderInfoList) {
        this.orderInfoList = orderInfoList;
    }

    public static BatchTaskPickInfoForPDADTO defaultInstance(PageList<BatchTaskSortDTO> batchTaskSortDTOList) {
        if (CollectionUtils.isEmpty(batchTaskSortDTOList.getDataList())) {
            return ofInstance();
        }
        BatchTaskPickInfoForPDADTO batchTaskPickInfoForPDADTO = new BatchTaskPickInfoForPDADTO();

        batchTaskPickInfoForPDADTO.setBatchTaskSortDTOList(batchTaskSortDTOList.getDataList());
        batchTaskPickInfoForPDADTO.setTotalCount(batchTaskSortDTOList.getPager().getRecordCount());
        return batchTaskPickInfoForPDADTO;
    }

    public static BatchTaskPickInfoForPDADTO defaultOrderInstance(PageList<OutStockOrderDTO> pageList) {
        if (CollectionUtils.isEmpty(pageList.getDataList())) {
            return ofInstance();
        }

        List<BatchTaskPickOrderInfoForPDADTO> orderInfoForPDADTOS = pageList.getDataList().stream().map(order -> {
            BatchTaskPickOrderInfoForPDADTO orderInfoForPDADTO = new BatchTaskPickOrderInfoForPDADTO();
            orderInfoForPDADTO.setRefOrderNo(order.getRefOrderNo());
            orderInfoForPDADTO.setOutStockOrderId(order.getId());
            orderInfoForPDADTO.setRoute(order.getRouteName());
            orderInfoForPDADTO.setArea(order.getAreaName());
            orderInfoForPDADTO.setSkuCount(order.getSkuCount());
            orderInfoForPDADTO.setPackageCount(order.getPackageAmount());
            orderInfoForPDADTO.setUnitCount(order.getUnitAmount());

            return orderInfoForPDADTO;
        }).collect(Collectors.toList());
        BatchTaskPickInfoForPDADTO batchTaskPickInfoForPDADTO = new BatchTaskPickInfoForPDADTO();

        batchTaskPickInfoForPDADTO.setOrderInfoList(orderInfoForPDADTOS);
        batchTaskPickInfoForPDADTO.setTotalCount(pageList.getPager().getRecordCount());
        return batchTaskPickInfoForPDADTO;
    }

    public static BatchTaskPickInfoForPDADTO ofInstance() {
        BatchTaskPickInfoForPDADTO batchTaskPickInfoForPDADTO = new BatchTaskPickInfoForPDADTO();

        batchTaskPickInfoForPDADTO.setTotalCount(0);
        return batchTaskPickInfoForPDADTO;
    }

}
