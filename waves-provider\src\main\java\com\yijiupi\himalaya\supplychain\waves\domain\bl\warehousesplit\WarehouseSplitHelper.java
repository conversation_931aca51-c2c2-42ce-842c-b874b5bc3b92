package com.yijiupi.himalaya.supplychain.waves.domain.bl.warehousesplit;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationConfigDTO;
import com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigType;
import com.yijiupi.himalaya.supplychain.service.config.IWarehouseAllocationConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-11-06 17:51
 **/
@Service
public class WarehouseSplitHelper {

    @Reference
    private IWarehouseAllocationConfigService warehouseAllocationConfigService;

    /**
     * 判断仓库是否启用了分仓配置
     *
     * @param warehouseId 仓库 id
     * @return 仓库是否启用分仓配置
     */
    public boolean enableWarehouseSplit(Integer warehouseId) {
        List<Integer> warehouseIds = Collections.singletonList(warehouseId);
        Map<Integer, Boolean> config = warehouseAllocationConfigService.listWarehouseSplitConfig(warehouseIds);
        return config.get(warehouseId);
    }

    /**
     * 获取指定类型的分仓配置
     *
     * @param type        配置类型
     * @param warehouseId 仓库 id
     * @return 指定类型的分仓配置
     */
    @Nullable
    public WarehouseAllocationConfigDTO getConfigByType(WarehouseAllocationConfigType type, Integer warehouseId) {
        return warehouseAllocationConfigService.getConfigByWarehouseId(warehouseId).stream()
                .filter(it -> type.valueEquals(it.getConfigType()))
                .findFirst().orElse(null);
    }
}
