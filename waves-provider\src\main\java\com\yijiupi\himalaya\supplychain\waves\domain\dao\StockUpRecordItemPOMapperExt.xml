<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.StockUpRecordItemPOMapper">
    <select id="selectByRecordIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stockuprecorditem
        <where>
            StockUpRecordId in
            <foreach collection="collection" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </where>
    </select>

    <delete id="deleteByRecordId">
        delete from stockuprecorditem where StockUpRecordId = #{recordId,jdbcType=BIGINT}
    </delete>
</mapper>