package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemChangeRecordPO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemChangeRecordSO;

/**
 * 出库单项变更记录
 *
 * <AUTHOR>
 * @date 2020-06-09 15:32
 */
public interface OutStockOrderItemChangeRecordMapper {

    void insertSelective(OutStockOrderItemChangeRecordPO record);

    void insertBatch(@Param("list") List<OutStockOrderItemChangeRecordPO> recordPOList);

    /**
     * 查询出库单项变更记录
     * 
     * @return
     */
    PageResult<OutStockOrderItemChangeRecordPO> listOrderItemChangeRecord(OutStockOrderItemChangeRecordSO recordSO);

}
