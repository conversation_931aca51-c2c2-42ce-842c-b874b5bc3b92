<assembly
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3"
        xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3 http://maven.apache.org/xsd/assembly-1.1.3.xsd">
    <id>distribution</id>
    <formats>
        <format>tar</format>
    </formats>
    <dependencySets>
        <dependencySet>
            <useProjectArtifact>true</useProjectArtifact>
            <outputDirectory>lib</outputDirectory>
        </dependencySet>
    </dependencySets>
    <fileSets>
        <fileSet>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>README.txt</include>
            </includes>
        </fileSet>
        <!-- 把项目的配置文件，打包进tar文件的config目录 -->
        <!-- <fileSet> <directory>src/main/resources</directory> <outputDirectory>config</outputDirectory>
            <includes> <include>*.xml</include> <include>*.properties</include> <include>*.yml</include>
            </includes> <excludes> <exclude>test/*</exclude> <exclude>production/*</exclude>
            <exclude>development/*</exclude> </excludes> </fileSet> -->
        <fileSet>
            <directory>src/main/resources/${profiles.active}</directory>
            <outputDirectory>config</outputDirectory>
            <includes>
                <include>**</include>
            </includes>
        </fileSet>

        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory>config</outputDirectory>
            <includes>
                <include>**</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/test-classes</directory>
            <outputDirectory>/test-classes</outputDirectory>
            <includes>
                <include>**/*.class</include>
                <include>**/*.properties</include>
            </includes>
        </fileSet>

        <!-- 把项目的启动脚本，打包进tar文件的bin目录 -->
        <fileSet>
            <directory>src/main/scripts</directory>
            <outputDirectory>/bin</outputDirectory>
            <includes>
                <include>*.cmd</include>
                <include>*.sh</include>
                <include>*.properties</include>
            </includes>
        </fileSet>

        <!-- 把项目自己编译出来的jar文件，打包进tar文件的根目录 -->
        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory>lib</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>