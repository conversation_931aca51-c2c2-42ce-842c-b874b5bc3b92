package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 拣货装箱
 *
 * <AUTHOR>
 * @since 2019/3/25 14:57
 */
public class PackageCodeSortDTO implements Serializable {
    private static final long serialVersionUID = 6478865937407940905L;

    /**
     * 箱号
     */
    private String boxCode;

    /**
     * 装箱大数量
     */
    private BigDecimal packageCount;

    /**
     * 装箱小数量
     */
    private BigDecimal unitCount;

    /**
     * 包装类型: 0.包装箱 1.托盘
     */
    private Byte packageType;

    /**
     * 复核状态: 0.待复核 1.复核中 2.已复核
     */
    private Byte reviewState;

    /**
     * 通道编码
     */
    private String passageCode;
    /**
     * 箱号
     */
    private String boxCodeNo;

    public String getBoxCode() {
        return boxCode;
    }

    public void setBoxCode(String boxCode) {
        this.boxCode = boxCode;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public Byte getPackageType() {
        return packageType;
    }

    public void setPackageType(Byte packageType) {
        this.packageType = packageType;
    }

    public Byte getReviewState() {
        return reviewState;
    }

    public void setReviewState(Byte reviewState) {
        this.reviewState = reviewState;
    }

    public String getPassageCode() {
        return passageCode;
    }

    public void setPassageCode(String passageCode) {
        this.passageCode = passageCode;
    }

    /**
     * 获取 箱号
     *
     * @return boxCodeNo 箱号
     */
    public String getBoxCodeNo() {
        return this.boxCodeNo;
    }

    /**
     * 设置 箱号
     *
     * @param boxCodeNo 箱号
     */
    public void setBoxCodeNo(String boxCodeNo) {
        this.boxCodeNo = boxCodeNo;
    }
}
