package com.yijiupi.himalaya.supplychain.waves.dto.soworder;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yijiupi.himalaya.supplychain.waves.dto.billreview.BillReviewDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskItemDTO;

public class SowOrderSaveDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 波次编号
     */
    private String batchNo;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 操作人
     */
    private String operatorUserName;

    /**
     * 操作人id
     */
    private Integer operatorUserId;

    /**
     * 产品包装信息
     */
    private List<PackageOrderItemDTO> packageOrderItems;

    /**
     * 产品播种信息
     */
    private List<SownOrderItemDTO> sownOrderItems;

    /**
     * 复核信息
     */
    private BillReviewDTO billReviewDTO;

    /**
     * 播种任务明细
     */
    private List<SowTaskItemDTO> sowTaskItemDTOS;

    /**
     * 播种开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 播种完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeTime;

    /**
     * 播种类型 播种墙播种(0),打包复核(1),按自提点播种(2),二次分拣播种(3),虚仓二次分拣播种(4)
     */
    private Byte sowTaskType;

    /**
     * 播种状态 待播种(0),播种中(1),已播种(2)
     */
    private Byte state;

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Byte getSowTaskType() {
        return sowTaskType;
    }

    public void setSowTaskType(Byte sowTaskType) {
        this.sowTaskType = sowTaskType;
    }

    public Integer getOperatorUserId() {
        return operatorUserId;
    }

    public void setOperatorUserId(Integer operatorUserId) {
        this.operatorUserId = operatorUserId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public String getOperatorUserName() {
        return operatorUserName;
    }

    public void setOperatorUserName(String operatorUserName) {
        this.operatorUserName = operatorUserName;
    }

    public List<PackageOrderItemDTO> getPackageOrderItems() {
        return packageOrderItems;
    }

    public void setPackageOrderItems(List<PackageOrderItemDTO> packageOrderItems) {
        this.packageOrderItems = packageOrderItems;
    }

    public List<SownOrderItemDTO> getSownOrderItems() {
        return sownOrderItems;
    }

    public void setSownOrderItems(List<SownOrderItemDTO> sownOrderItems) {
        this.sownOrderItems = sownOrderItems;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public BillReviewDTO getBillReviewDTO() {
        return billReviewDTO;
    }

    public void setBillReviewDTO(BillReviewDTO billReviewDTO) {
        this.billReviewDTO = billReviewDTO;
    }

    public List<SowTaskItemDTO> getSowTaskItemDTOS() {
        return sowTaskItemDTOS;
    }

    public void setSowTaskItemDTOS(List<SowTaskItemDTO> sowTaskItemDTOS) {
        this.sowTaskItemDTOS = sowTaskItemDTOS;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }
}
