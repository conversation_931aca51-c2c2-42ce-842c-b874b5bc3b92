package com.yijiupi.himalaya.supplychain.waves.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

public enum BillReviewBusinessTypeEnum {
    /**
     * 枚举
     */
    订单((byte)0), 拣货任务((byte)1);

    /**
     * type
     */
    private byte type;

    BillReviewBusinessTypeEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     * 
     * @param value
     * @return
     */
    public static String getEnumByValue(Byte value) {
        BillReviewBusinessTypeEnum billReviewBusinessTypeEnum = null;
        if (value != null) {
            billReviewBusinessTypeEnum = cache.get(value);
        }
        return billReviewBusinessTypeEnum == null ? null : billReviewBusinessTypeEnum.name();
    }

    private static Map<Byte, BillReviewBusinessTypeEnum> cache =
        EnumSet.allOf(BillReviewBusinessTypeEnum.class).stream().collect(Collectors.toMap(p -> p.type, p -> p));
}
