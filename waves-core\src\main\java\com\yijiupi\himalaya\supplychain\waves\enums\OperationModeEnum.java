package com.yijiupi.himalaya.supplychain.waves.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

public enum OperationModeEnum {
    /**
     * 枚举
     */
    拣货播种((byte)0), 不拣货播种((byte)1), 拣货不播种((byte)2);

    /**
     * type
     */
    private byte type;

    OperationModeEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     * 
     * @param value
     * @return
     */
    public static String getEnumByValue(Byte value) {
        OperationModeEnum operationModeEnum = null;
        if (value != null) {
            operationModeEnum = cache.get(value);
        }
        return operationModeEnum == null ? null : operationModeEnum.name();
    }

    private static Map<Byte, OperationModeEnum> cache =
        EnumSet.allOf(OperationModeEnum.class).stream().collect(Collectors.toMap(p -> p.type, p -> p));
}
