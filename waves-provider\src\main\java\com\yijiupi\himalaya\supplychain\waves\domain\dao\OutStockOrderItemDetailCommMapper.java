package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;

@Mapper
public interface OutStockOrderItemDetailCommMapper {

    int deleteByPrimaryKey(Long id);

    int insert(OutStockOrderItemDetailPO record);

    int insertSelective(OutStockOrderItemDetailPO record);

    OutStockOrderItemDetailPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OutStockOrderItemDetailPO record);

    void insertBatch(@Param("itemDetails") List<OutStockOrderItemDetailPO> convertOrderItemDetailPOS);

    void insertOrUpdateBatch(@Param("itemDetails") List<OutStockOrderItemDetailPO> convertOrderItemDetailPOS);

    int deleteByItemDetailIds(@Param("delItemDetailIds") List<Long> delItemDetailIds, @Param("orgId") Integer orgId);

    List<OutStockOrderItemDetailPO> findByItemIds(@Param("orgId") Integer orgId, @Param("itemIds") List<Long> itemIds);

    /**
     * 根据订单项ID查询detail
     * 
     * @return
     */
    List<Long> listByOrderItemIds(@Param("list") List<Long> orderItemIds);

    int updateBatchByPOList(@Param("list") List<OutStockOrderItemDetailPO> poList);

    int updateByItemDetailIdsToZero(@Param("delItemDetailIds") List<Long> delItemDetailIds, @Param("orgId") Integer orgId);
}