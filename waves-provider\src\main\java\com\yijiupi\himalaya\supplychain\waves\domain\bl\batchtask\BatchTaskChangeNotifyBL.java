package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.postcommit.CommitAfterTransactionAnn;
import com.yijiupi.himalaya.supplychain.audit.dto.batchtask.BatchTaskNotifyDTO;
import com.yijiupi.himalaya.supplychain.audit.dto.batchtask.BatchTaskNotifyTaskInfoDTO;
import com.yijiupi.himalaya.supplychain.audit.dto.batchtask.SupplyChainClientMessageDTO;
import com.yijiupi.himalaya.supplychain.audit.service.IDigitalDeviceMqttService;
import com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigType;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskKindOfPickingConstants;
import com.yijiupi.himalaya.supplychain.audit.enums.DigitalLabelNotifyBatchTaskConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum;
import com.yijiupi.himalaya.supplychain.wcs.dto.ics.CompleteWcsDrinkTaskItemDTO;
import com.yijiupi.himalaya.supplychain.wcs.service.task.IICSTaskManageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 监护任务变更通知客户端
 *
 * <AUTHOR>
 * @date 2024/10/28
 */
@Service
public class BatchTaskChangeNotifyBL {

    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;

    @Reference
    private IDigitalDeviceMqttService iDigitalDeviceMqttService;
    @Reference
    private IICSTaskManageService iicsTaskManageService;
    private static final Logger LOG = LoggerFactory.getLogger(BatchTaskChangeNotifyBL.class);

    /**
     * 波次创建
     *
     * @param batchTaskIds
     */
    @CommitAfterTransactionAnn
    public void notifyDigitalBatchCreate(List<String> batchTaskIds) {
        List<BatchTaskPO> batchTaskList = batchTaskMapper.findTasksByBatchTaskId(batchTaskIds);

        notifyDigitalBatchTaskCreate(batchTaskList);
    }

    /**
     * 拣货任务创建
     *
     * @param batchTaskList
     */
    @CommitAfterTransactionAnn
    public void notifyDigitalBatchTaskCreate(List<BatchTaskPO> batchTaskList) {
        if (CollectionUtils.isEmpty(batchTaskList)) {
            return;
        }
        batchTaskList = batchTaskList.stream()
                .filter(m -> BatchTaskPickPatternEnum.电子标签.getType() == m.getPickPattern()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(batchTaskList)) {
            return;
        }
        try {
            LOG.info("通知创建拣货任务：{}", JSON.toJSONString(batchTaskList));
            BatchTaskPO batchTaskPO = batchTaskList.get(0);
            Integer warehouseId = batchTaskPO.getWarehouseId();
            Integer orgId = Integer.valueOf(batchTaskPO.getOrgId());

            List<BatchTaskNotifyTaskInfoDTO> notifyDTOList = convertList(batchTaskList);

            if (CollectionUtils.isNotEmpty(notifyDTOList)) {
                iDigitalDeviceMqttService.notifyBatchTaskInfo(BatchTaskNotifyDTO
                        .newInstance(DigitalLabelNotifyBatchTaskConstants.TASK_CREATE, warehouseId, notifyDTOList));
            }
        } catch (Exception e) {
            LOG.warn("通知控制器失败，原因:", e);
        }
    }

    /**
     * 拣货任务完成
     *
     * @param batchTaskPO
     */
    @CommitAfterTransactionAnn
    public void notifyBatchTaskComplete(BatchTaskPO batchTaskPO) {
        if (BatchTaskPickPatternEnum.电子标签.getType() == batchTaskPO.getPickPattern()) {
            return;
        }
        LOG.info("通知拣货任务完成：{}", JSON.toJSONString(batchTaskPO));
        try {
            List<BatchTaskNotifyTaskInfoDTO> notifyDTOList = convertList(Collections.singletonList(batchTaskPO));
            if (CollectionUtils.isEmpty(notifyDTOList)) {
                return;
            }
            iDigitalDeviceMqttService.notifyBatchTaskInfo(BatchTaskNotifyDTO.newInstance(
                    DigitalLabelNotifyBatchTaskConstants.TASK_COMPLETE, batchTaskPO.getWarehouseId(), notifyDTOList));
        } catch (Exception e) {
            LOG.warn("通知控制器失败，原因:", e);
        }
    }

    /**
     * 波次删除
     *
     * @param batchTaskList
     */
    @CommitAfterTransactionAnn
    public void notifyBatchDelete(List<BatchTaskPO> batchTaskList) {
        try {
            List<BatchTaskNotifyTaskInfoDTO> notifyDTOList = convertList(batchTaskList);
            LOG.info("通知拣货任务删除：{}", JSON.toJSONString(batchTaskList));
            if (CollectionUtils.isEmpty(notifyDTOList)) {
                return;
            }
            iDigitalDeviceMqttService
                    .notifyBatchTaskInfo(BatchTaskNotifyDTO.newInstance(DigitalLabelNotifyBatchTaskConstants.TASK_DELETE,
                            batchTaskList.get(0).getWarehouseId(), notifyDTOList));
        } catch (Exception e) {
            LOG.warn("通知控制器失败，原因:", e);
        }
    }

    /**
     * 订单取消
     *
     * @param oriBatchTaskPOList 原始拣货任务
     * @param warehouseId
     */
    @CommitAfterTransactionAnn
    public void notifyOrderCanceled(List<BatchTaskPO> oriBatchTaskPOList, Integer warehouseId) {
        if (CollectionUtils.isEmpty(oriBatchTaskPOList)) {
            return;
        }
        try {
            List<String> batchTaskNoList =
                    oriBatchTaskPOList.stream().map(BatchTaskPO::getBatchTaskNo).distinct().collect(Collectors.toList());
            List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findTasksByBatchTaskNo(batchTaskNoList);

            if (CollectionUtils.isEmpty(batchTaskPOS)) {
                iDigitalDeviceMqttService.notifyBatchTaskInfo(BatchTaskNotifyDTO.newInstance(
                        DigitalLabelNotifyBatchTaskConstants.TASK_DELETE, warehouseId, convertList(oriBatchTaskPOList)));
                return;
            }

            Map<String, BatchTaskPO> batchTaskPOMap =
                    batchTaskPOS.stream().collect(Collectors.toMap(BatchTaskPO::getBatchTaskNo, v -> v));

            List<BatchTaskPO> existBatchTaskList = oriBatchTaskPOList.stream()
                    .filter(m -> Objects.nonNull(batchTaskPOMap.get(m.getBatchTaskNo()))).collect(Collectors.toList());
            List<BatchTaskPO> notExistBatchTaskList = oriBatchTaskPOList.stream()
                    .filter(m -> Objects.isNull(batchTaskPOMap.get(m.getBatchTaskNo()))).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(existBatchTaskList)) {
                LOG.info("通知拣货任务变更：{}", JSON.toJSONString(existBatchTaskList));
                List<BatchTaskNotifyTaskInfoDTO> list = convertList(existBatchTaskList);
                if (CollectionUtils.isNotEmpty(list)) {
                    iDigitalDeviceMqttService.notifyBatchTaskInfo(BatchTaskNotifyDTO
                            .newInstance(DigitalLabelNotifyBatchTaskConstants.TASK_CHANGE, warehouseId, list));
                }

            }
            if (CollectionUtils.isNotEmpty(notExistBatchTaskList)) {
                LOG.info("通知拣货任务删除：{}", JSON.toJSONString(notExistBatchTaskList));
                List<BatchTaskNotifyTaskInfoDTO> list = convertList(notExistBatchTaskList);
                if (CollectionUtils.isNotEmpty(list)) {
                    iDigitalDeviceMqttService.notifyBatchTaskInfo(BatchTaskNotifyDTO
                            .newInstance(DigitalLabelNotifyBatchTaskConstants.TASK_DELETE, warehouseId, list));
                }

            }
        } catch (Exception e) {
            LOG.warn("处理拣货任务变更失败", e);
        }

    }

    @CommitAfterTransactionAnn
    public void notifyBatchTaskChanged(List<String> batchTaskIds) {
        try {
            List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findBatchTaskByIds(batchTaskIds);
            if (CollectionUtils.isEmpty(batchTaskPOList)) {
                return;
            }

            List<BatchTaskNotifyTaskInfoDTO> list = convertList(batchTaskPOList);
            if (CollectionUtils.isNotEmpty(list)) {
                iDigitalDeviceMqttService.notifyBatchTaskInfo(BatchTaskNotifyDTO.newInstance(
                        DigitalLabelNotifyBatchTaskConstants.TASK_CHANGE, batchTaskPOList.get(0).getWarehouseId(), list));
            }
        } catch (Exception e) {
            LOG.warn("出现异常", e);
        }
    }

    /**
     * 拣货任务明细完成
     *
     * @param itemPOList
     * @param batchTaskPO
     */
    public void notifyBatchTaskItemComplete(List<BatchTaskItemPO> itemPOList, BatchTaskPO batchTaskPO) {
        notifyDhWcsTaskItemComplete(itemPOList, batchTaskPO);
    }


    /**
     * 通知wcs完成大华wcs任务明细
     *
     * @param itemPOList
     * @param batchTaskPO
     */
    private void notifyDhWcsTaskItemComplete(List<BatchTaskItemPO> itemPOList, BatchTaskPO batchTaskPO) {
        // 非酒饮，返回
        if (!WarehouseAllocationConfigType.DRINKING.getValue().equals(batchTaskPO.getWarehouseAllocationType())) {
            return;
        }
        // 非agv，返回
        if (BatchTaskPickPatternEnum.agv拣货.getType() != batchTaskPO.getPickPattern()) {
            return;
        }
        CompleteWcsDrinkTaskItemDTO completeWcsDrinkTaskItemDTO = new CompleteWcsDrinkTaskItemDTO();
        completeWcsDrinkTaskItemDTO.setBatchTaskId(batchTaskPO.getId());
        List<String> batchTaskItemIds = itemPOList.stream().map(BatchTaskItemPO::getId).distinct().collect(Collectors.toList());
        completeWcsDrinkTaskItemDTO.setBatchTaskItemIds(batchTaskItemIds);
        completeWcsDrinkTaskItemDTO.setOrgId(Integer.parseInt(batchTaskPO.getOrgId()));
        completeWcsDrinkTaskItemDTO.setWarehouseId(batchTaskPO.getWarehouseId());

        iicsTaskManageService.completeWcsDrinkTaskItem(completeWcsDrinkTaskItemDTO);
    }

    /**
     * 拣货任务拆分
     *
     * @param oldBatchTaskPO
     * @param batchTaskList
     */
    @CommitAfterTransactionAnn
    public void notifyBatchTaskSplit(BatchTaskPO oldBatchTaskPO, List<BatchTaskPO> batchTaskList) {
        if (BatchTaskPickPatternEnum.电子标签.getType() != oldBatchTaskPO.getPickPattern()) {
            return;
        }

        notifyBatchDelete(Collections.singletonList(oldBatchTaskPO));
        notifyDigitalBatchTaskCreate(batchTaskList);
    }

    /**
     * 领取拣货任务
     *
     * @param batchTaskList
     */
    @CommitAfterTransactionAnn
    public void notifyBatchTaskGet(List<BatchTaskPO> batchTaskList) {
        List<BatchTaskNotifyTaskInfoDTO> notifyDTOList = convertList(batchTaskList);
        LOG.info("通知领取拣货任务：{}", JSON.toJSONString(batchTaskList));
        if (CollectionUtils.isEmpty(notifyDTOList)) {
            return;
        }
        try {
            iDigitalDeviceMqttService.notifyBatchTaskInfo(BatchTaskNotifyDTO.newInstance(
                    DigitalLabelNotifyBatchTaskConstants.TASK_GET, batchTaskList.get(0).getWarehouseId(), notifyDTOList));
        } catch (Exception e) {
            LOG.warn("处理拣货任务变更失败", e);
        }
    }

    private List<BatchTaskNotifyTaskInfoDTO> convertList(List<BatchTaskPO> batchTaskList) {
        List<BatchTaskNotifyTaskInfoDTO> notifyDTOList =
                batchTaskList.stream().filter(m -> Objects.nonNull(m.getSortGroupId()))
                        .filter(m -> BatchTaskPickPatternEnum.电子标签.getType() == m.getPickPattern()).map(po -> {
                            BatchTaskNotifyTaskInfoDTO notifyDTO = new BatchTaskNotifyTaskInfoDTO();
                            notifyDTO.setWarehouseId(po.getWarehouseId());
                            notifyDTO.setBatchTaskNo(po.getBatchTaskNo());
                            notifyDTO.setSortGroupId(po.getSortGroupId());
                            // notifyDTO.setSequence();

                            return notifyDTO;
                        }).collect(Collectors.toList());
        return notifyDTOList;
    }

    /**
     * 播种任务变更消息
     *
     * @param sowTaskPO
     */
    @CommitAfterTransactionAnn
    public void notifySupplyChainClientSowTaskChange(SowTaskPO sowTaskPO) {
        SupplyChainClientMessageDTO messageDTO = new SupplyChainClientMessageDTO();
        messageDTO.setWarehouseId(sowTaskPO.getWarehouseId());
        messageDTO.setEventType(DigitalLabelNotifyBatchTaskConstants.SOW_TASK_CHANGE);
        messageDTO.setSowTaskIds(Collections.singletonList(sowTaskPO.getId().toString()));
        iDigitalDeviceMqttService.notifySupplyChainClient(messageDTO);
    }

}
