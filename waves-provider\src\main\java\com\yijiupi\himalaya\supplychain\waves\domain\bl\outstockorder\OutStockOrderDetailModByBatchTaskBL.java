package com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OutStockOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemDetailCommMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
@Service
public class OutStockOrderDetailModByBatchTaskBL {

    @Autowired
    private OutStockOrderItemDetailCommMapper outStockOrderItemDetailCommMapper;
    private static final Logger LOG = LoggerFactory.getLogger(OutStockOrderDetailModByBatchTaskBL.class);


    public void resetOrderItemDetail(List<OutStockOrderItemDetailPO> detailPOList, List<OutStockOrderItemDetailPO> addOrderItemDetailList, List<OutStockOrderItemPO> outStockOrderItemPOList, Integer orgId) {

        List<OutStockOrderItemDetailPO> copyAddOrderItemDetailList = new ArrayList<>();
        addOrderItemDetailList.forEach(detail -> {
            OutStockOrderItemDetailPO newDetail = new OutStockOrderItemDetailPO();
            BeanUtils.copyProperties(detail, newDetail);
            copyAddOrderItemDetailList.add(newDetail);
        });

        Map<String, OutStockOrderItemDetailPO> oriDetailMap = detailPOList.stream().collect(Collectors.toMap(this::getKey, v -> v));
        Map<String, OutStockOrderItemDetailPO> newDetailMap = copyAddOrderItemDetailList.stream().collect(Collectors.toMap(this::getKey, v -> v));

        // 先覆盖已经存在的
        for (Map.Entry<String, OutStockOrderItemDetailPO> entry : newDetailMap.entrySet()) {
            OutStockOrderItemDetailPO oriDetailPO = oriDetailMap.get(entry.getKey());
            if (Objects.nonNull(oriDetailPO)) {
                OutStockOrderItemDetailPO newDetailPO = entry.getValue();
                newDetailPO.setId(oriDetailPO.getId());
            }
        }

        // 把没有在新增detail列表里的数据，数量都更新成0
        Map<Long ,OutStockOrderItemDetailPO> newMap = copyAddOrderItemDetailList.stream().collect(Collectors.toMap(OutStockOrderItemDetailPO :: getId, v -> v));
        List<OutStockOrderItemDetailPO> updateToZeroList = detailPOList.stream().filter(detail -> Objects.isNull(newMap.get(detail.getId()))).collect(Collectors.toList());

        // 查验数据中是否有负数
        List<OutStockOrderItemDetailPO> negativeCountDetailList = copyAddOrderItemDetailList.stream().filter(m -> m.getUnitTotalCount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
        handleNegativeCountDetail(negativeCountDetailList, copyAddOrderItemDetailList);

        // 验证detail数量和订单item数量是否匹配，不匹配，走老方法
        if (validateItemAndDetailEquals(copyAddOrderItemDetailList, outStockOrderItemPOList, orgId)) {
            updateOrderItemDetailInfo(updateToZeroList, copyAddOrderItemDetailList, orgId);
            return;
        }

        List<Long> orderItemIds = outStockOrderItemPOList.stream().map(OutStockOrderItemPO :: getId).distinct().collect(Collectors.toList());
        List<Long> deleteOrderItemDetailIdList = outStockOrderItemDetailCommMapper.listByOrderItemIds(orderItemIds);
        // 先删后增
        if (!org.springframework.util.CollectionUtils.isEmpty(deleteOrderItemDetailIdList)) {
            int deleteCount =
                    outStockOrderItemDetailCommMapper.deleteByItemDetailIds(deleteOrderItemDetailIdList, orgId);
            if (deleteCount != deleteOrderItemDetailIdList.size()) {
                LOG.info(String.format("Detail删除数量异常，删除：%s / %s", deleteCount, deleteOrderItemDetailIdList.size()));
            }
            LOG.info("删除订单项detail：{}", JSON.toJSONString(deleteOrderItemDetailIdList));
        }

        outStockOrderItemDetailCommMapper.insertOrUpdateBatch(addOrderItemDetailList);
        LOG.info("新增订单项detail：{}", JSON.toJSONString(addOrderItemDetailList));
    }

    private boolean validateItemAndDetailEquals(List<OutStockOrderItemDetailPO> addOrderItemDetailList, List<OutStockOrderItemPO> outStockOrderItemPOList, Integer orgId) {
        Map<Long, List<OutStockOrderItemDetailPO>> orderItemGroupMap = addOrderItemDetailList.stream().collect(Collectors.groupingBy(OutStockOrderItemDetailPO :: getOutStockOrderItemId));
        for (OutStockOrderItemPO orderItemPO : outStockOrderItemPOList) {
            List<OutStockOrderItemDetailPO> detailPOList = orderItemGroupMap.get(orderItemPO.getId());
            if (CollectionUtils.isEmpty(detailPOList)) {
                continue;
            }

            BigDecimal detailUnitTotalCount = detailPOList.stream().map(OutStockOrderItemDetailPO :: getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal :: add);
            if (!detailUnitTotalCount.equals(orderItemPO.getUnittotalcount())) {
                return Boolean.FALSE;
            }
        }

        return Boolean.TRUE;
    }

    private void updateOrderItemDetailInfo(List<OutStockOrderItemDetailPO> updateToZeroList, List<OutStockOrderItemDetailPO> addOrderItemDetailList, Integer orgId) {
        if (CollectionUtils.isNotEmpty(updateToZeroList)) {
            List<Long> detailIds = updateToZeroList.stream().map(OutStockOrderItemDetailPO :: getId).collect(Collectors.toList());
            outStockOrderItemDetailCommMapper.updateByItemDetailIdsToZero(detailIds, orgId);
        }

        outStockOrderItemDetailCommMapper.insertOrUpdateBatch(addOrderItemDetailList);
    }

    private String getKey(OutStockOrderItemDetailPO detailPO) {
        return String.format("%s-%s-%s", detailPO.getOutStockOrderItemId(), detailPO.getOwnerId(), detailPO.getSecOwnerId());
    }

    private void handleNegativeCountDetail(List<OutStockOrderItemDetailPO> negativeCountDetailList, List<OutStockOrderItemDetailPO> addOrderItemDetailList) {
        if (CollectionUtils.isEmpty(negativeCountDetailList)) {
            return;
        }
        Map<Long, List<OutStockOrderItemDetailPO>> orderItemGroupMap = addOrderItemDetailList.stream().collect(Collectors.groupingBy(OutStockOrderItemDetailPO::getOutStockOrderItemId));
        Map<Long, List<OutStockOrderItemDetailPO>> negativeOrderItemGroupMap = negativeCountDetailList.stream().collect(Collectors.groupingBy(OutStockOrderItemDetailPO::getOutStockOrderItemId));

        for (Map.Entry<Long, List<OutStockOrderItemDetailPO>> entry : negativeOrderItemGroupMap.entrySet()) {
            List<OutStockOrderItemDetailPO> detailPOList = orderItemGroupMap.get(entry.getKey());
            BigDecimal totalUnitCount = detailPOList.stream().map(OutStockOrderItemDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (totalUnitCount.compareTo(BigDecimal.ZERO) <= 0) {
                LOG.warn("detail之和小于0，{} ；全部置为0！", JSON.toJSONString(detailPOList));
                detailPOList.forEach(m -> m.setUnitTotalCount(BigDecimal.ZERO));
                continue;
            }

            BigDecimal negativeCount = entry.getValue().stream().map(OutStockOrderItemDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);

            List<OutStockOrderItemDetailPO> positiveDetailList = detailPOList.stream().filter(m -> m.getUnitTotalCount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            for (OutStockOrderItemDetailPO detail : positiveDetailList) {
                if (detail.getUnitTotalCount().compareTo(negativeCount) < 0) {
                    negativeCount = detail.getUnitTotalCount().subtract(negativeCount.abs());
                    detail.setUnitTotalCount(BigDecimal.ZERO);
                } else {
                    detail.setUnitTotalCount(detail.getUnitTotalCount().subtract(negativeCount.abs()));
                }
            }
        }
    }

}
