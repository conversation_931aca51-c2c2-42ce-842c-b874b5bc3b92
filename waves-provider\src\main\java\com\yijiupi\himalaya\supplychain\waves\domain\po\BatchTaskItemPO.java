package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @since 2018/3/26 11:39
 */
public class BatchTaskItemPO implements Serializable {
    /**
     * 主键
     */
    private String id;
    /**
     * 城市ID，分库用（第三方订单使用一个新的自定义CityId）
     */
    private Integer orgId;
    /**
     * 波次任务编号
     */
    private String batchTaskNo;
    /**
     * 波次任务Id
     */
    private String batchTaskId;
    /**
     * 订单编号
     */
    private String refOrderNo;
    /**
     * 关联订单表id
     */
    private String refOrderId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * skuId（赠品SKUId可能为null）
     */
    private Long skuId;
    /**
     * 品牌
     */
    private String productBrand;
    /**
     * 类目
     */
    private String categoryName;
    /**
     * 包装规格
     */
    private String specName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal specQuantity;
    /**
     * 销售规格名称
     */
    private String saleSpec;
    /**
     * 销售规格系数
     */
    private BigDecimal saleSpecQuantity;
    /**
     * 大单位名称
     */
    private String packageName;
    /**
     * 大单位数量
     */
    private BigDecimal packageCount;
    /**
     * 小单位名称
     */
    private String unitName;
    /**
     * 小单位数量
     */
    private BigDecimal unitCount;
    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;
    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte taskState;
    /**
     * 缺货小数量
     */
    private BigDecimal lackUnitCount;

    /**
     * 是否缺货 1 缺货 0不缺货'
     */
    private Byte isLack;

    /**
     * 备注
     */
    private String remark;
    /**
     * 已拣货数量
     */
    private BigDecimal overSortCount;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * '类型，0：货位，1：货区',
     */
    private Byte locationCategory;

    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Byte source;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Byte channel;
    /**
     * 关联订单详情
     */
    private Long orderItemId;

    /**
     * 按订单拣货的订单顺序
     */
    private Integer orderSquence;

    /**
     * 已播种小件总数量
     */
    private BigDecimal sownUnitTotalCount;

    private Long productSpecificationId;

    private Long ownerId;

    private Long secOwnerId;

    /**
     * 控货策略id
     */
    private Long controlConfigId;

    /**
     * 批次入库时间
     */
    private Date batchTime;
    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 回退数量
     */
    private BigDecimal rollbackUnitCount;

    /**
     * 波次id
     */
    private String batchId;

    /**
     * 波次编号, 非标准模型
     */
    private String batchNo;

    /**
     * 仓库id, 非标准模型
     */
    private Integer warehouseId;

    /**
     * 出库位id
     */
    private Long toLocationId;
    /**
     * 出库位名称
     */
    private String toLocationName;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 拣货任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte batchTaskState;

    /**
     * 产品单价
     */
    private BigDecimal unitPrice;

    /**
     * 播种任务明细id
     */
    private Long sowTaskItemId;

    private Integer completeUserId;
    private String completeUser;

    private String lastUpdateUser;
    /**
     * @see BatchTaskItemLargePickPatternConstants
     */
    private Byte largePickPattern;

    private Byte pickingType;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date lastUpdateTime;

    /**
     * 拣货开始时间
     */
    private Date startTime;

    /**
     * 拣货完成时间
     */
    private Date completeTime;

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Long getSowTaskItemId() {
        return sowTaskItemId;
    }

    public void setSowTaskItemId(Long sowTaskItemId) {
        this.sowTaskItemId = sowTaskItemId;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Byte getIsLack() {
        return isLack;
    }

    public void setIsLack(Byte isLack) {
        this.isLack = isLack;
    }

    public Byte getBatchTaskState() {
        return batchTaskState;
    }

    public void setBatchTaskState(Byte batchTaskState) {
        this.batchTaskState = batchTaskState;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public BigDecimal getRollbackUnitCount() {
        return rollbackUnitCount;
    }

    public void setRollbackUnitCount(BigDecimal rollbackUnitCount) {
        this.rollbackUnitCount = rollbackUnitCount;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Long getControlConfigId() {
        return controlConfigId;
    }

    public void setControlConfigId(Long controlConfigId) {
        this.controlConfigId = controlConfigId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getOrderSquence() {
        return orderSquence;
    }

    public void setOrderSquence(Integer orderSquence) {
        this.orderSquence = orderSquence;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public Byte getChannel() {
        return channel;
    }

    public void setChannel(Byte channel) {
        this.channel = channel;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public String getRefOrderId() {
        return refOrderId;
    }

    public void setRefOrderId(String refOrderId) {
        this.refOrderId = refOrderId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public String getSaleSpec() {
        return saleSpec;
    }

    public void setSaleSpec(String saleSpec) {
        this.saleSpec = saleSpec;
    }

    public BigDecimal getSaleSpecQuantity() {
        return saleSpecQuantity;
    }

    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Byte getTaskState() {
        return taskState;
    }

    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    public BigDecimal getLackUnitCount() {
        return lackUnitCount;
    }

    public void setLackUnitCount(BigDecimal lackUnitCount) {
        this.lackUnitCount = lackUnitCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getOverSortCount() {
        return overSortCount;
    }

    public void setOverSortCount(BigDecimal overSortCount) {
        this.overSortCount = overSortCount;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 '类型，0：货位，1：货区',
     */
    public Byte getLocationCategory() {
        return this.locationCategory;
    }

    /**
     * 设置 '类型，0：货位，1：货区',
     */
    public void setLocationCategory(Byte locationCategory) {
        this.locationCategory = locationCategory;
    }

    public BigDecimal getSownUnitTotalCount() {
        return sownUnitTotalCount;
    }

    public void setSownUnitTotalCount(BigDecimal sownUnitTotalCount) {
        this.sownUnitTotalCount = sownUnitTotalCount;
    }

    /**
     * 获取
     *
     * @return completeUserId
     */
    public Integer getCompleteUserId() {
        return this.completeUserId;
    }

    /**
     * 设置
     *
     * @param completeUserId
     */
    public void setCompleteUserId(Integer completeUserId) {
        this.completeUserId = completeUserId;
    }

    /**
     * 获取
     *
     * @return completeUser
     */
    public String getCompleteUser() {
        return this.completeUser;
    }

    /**
     * 设置
     *
     * @param completeUser
     */
    public void setCompleteUser(String completeUser) {
        this.completeUser = completeUser;
    }

    /**
     * 获取
     *
     * @return largePickPattern
     */
    public Byte getLargePickPattern() {
        return this.largePickPattern;
    }

    /**
     * 设置
     *
     * @param largePickPattern
     */
    public void setLargePickPattern(Byte largePickPattern) {
        this.largePickPattern = largePickPattern;
    }

    /**
     * 获取
     *
     * @return pickingType
     */
    public Byte getPickingType() {
        return this.pickingType;
    }

    /**
     * 设置
     *
     * @param pickingType
     */
    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }
}