package com.yijiupi.himalaya.supplychain.waves.enums;

import com.yijiupi.himalaya.base.exception.BusinessCodeException;
import com.yijiupi.himalaya.base.exception.BusinessCodeValidateException;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskFinishValidateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.meituan.MTOrderVerifyBL;
import com.yijiupi.himalaya.supplychain.waves.util.BusinessCodeExceptionUtils;

/**
 * {@link BusinessCodeException} 工具类
 *
 * <AUTHOR>
 * @since 2023-08-24 15:57
 **/
public enum ErrorCodeEnum {

    XX_ORDER_CANNOT_LACK(MTOrderVerifyBL.class, "markPartSendVerify", "[T]订单不允许缺货: [X]"),
    XX_ORDER_CANNOT_LACK_2(BatchTaskFinishValidateBL.class, "validateIsMeiTuan",
            "[T]订单不允许缺货, 是否确认缺货? 如订单已拣货, 请放置到一边, 该订单及相关子单需要一起返库! (订单号: [X])"
    ),
    ORDER_CANNOT_LACK(BatchTaskFinishValidateBL.class, "validateIsMeiTuan",
            "该类型订单不允许缺货, 是否确认缺货? 如订单已拣货, 请放置到一边, 该订单及相关子单需要一起返库! (订单号: [X])"
    ),
    ;

    public final String code;
    public final String message;

    ErrorCodeEnum(Class<?> targetClass, String methodName, String message) {
        this.code = targetClass.getSimpleName() + "#" + methodName;
        this.message = message;
    }

    /**
     * 构造一个新的 {@link BusinessCodeException} 参数必须是偶数个, 奇数的参数是要替换的 key, 偶数的参数是要替换的内容<br/>
     * 可以没有参数, 即 args == null || args.length == 0
     */
    public BusinessCodeException newException(Object... args) {
        return BusinessCodeExceptionUtils.newException(code, message, args);
    }

    /**
     * 构造一个新的 {@link BusinessCodeValidateException} 参数必须是偶数个, 奇数的参数是要替换的 key, 偶数的参数是要替换的内容<br/>
     * 可以没有参数, 即 args == null || args.length == 0
     */
    public BusinessCodeValidateException newValidateException(Object... args) {
        return BusinessCodeExceptionUtils.newValidateException(code, message, args);
    }

}
