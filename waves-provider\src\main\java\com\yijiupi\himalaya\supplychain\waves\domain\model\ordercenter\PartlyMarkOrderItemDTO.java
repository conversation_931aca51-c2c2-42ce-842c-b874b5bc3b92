package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单重算数据明细
 *
 * <AUTHOR>
 * @Date 2022/5/19
 */
public class PartlyMarkOrderItemDTO implements Serializable {

    private static final long serialVersionUID = 8975411113800284768L;

    /**
     * 订单明细Id
     */
    private Long orderItemId;

    /**
     * 标记实际数量（小单位）
     */
    private BigDecimal unitCount;

    private List<PartlyMarkOrderItemSecOwnerDTO> partlyMarkOrderItemSecOwnerList;

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public List<PartlyMarkOrderItemSecOwnerDTO> getPartlyMarkOrderItemSecOwnerList() {
        return partlyMarkOrderItemSecOwnerList;
    }

    public void
        setPartlyMarkOrderItemSecOwnerList(List<PartlyMarkOrderItemSecOwnerDTO> partlyMarkOrderItemSecOwnerList) {
        this.partlyMarkOrderItemSecOwnerList = partlyMarkOrderItemSecOwnerList;
    }
}
