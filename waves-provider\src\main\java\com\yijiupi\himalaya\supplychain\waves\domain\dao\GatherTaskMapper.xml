<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.GatherTaskMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Org_Id" property="orgId" jdbcType="INTEGER"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="BatchTask_Id" property="batchTaskId" jdbcType="VARCHAR"/>
        <result column="BatchtaskNo" property="batchtaskNo" jdbcType="VARCHAR"/>
        <result column="TaskName" property="taskName" jdbcType="VARCHAR"/>
        <result column="Status" property="status" jdbcType="TINYINT"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="CreateUser" property="createUser" jdbcType="BIGINT"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="BIGINT"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Org_Id, Warehouse_Id, BatchTask_Id, BatchtaskNo, TaskName, Status, Remark, CreateUser,
        CreateTime, LastUpdateUser, LastUpdateTime
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from gathertask
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByBatchTaskId" resultType="java.lang.Integer" parameterType="java.lang.String">
        select
        count(1) num
        from gathertask
        where BatchTask_Id = #{batchTaskId,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from gathertask
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskPO"
            useGeneratedKeys="true" keyProperty="id">
        insert into gathertask
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="orgId != null">
                Org_Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="batchTaskId != null">
                BatchTask_Id,
            </if>
            <if test="batchtaskNo != null">
                BatchtaskNo,
            </if>
            <if test="taskName != null">
                TaskName,
            </if>
            <if test="status != null">
                Status,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="batchTaskId != null">
                #{batchTaskId,jdbcType=VARCHAR},
            </if>
            <if test="batchtaskNo != null">
                #{batchtaskNo,jdbcType=VARCHAR},
            </if>
            <if test="taskName != null">
                #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                now(),
            </if>
            <if test="lastUpdateUser != null">
                #{createUser,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateTime != null">
                now(),
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskPO">
        update gathertask
        <set>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="batchTaskId != null">
                BatchTask_Id = #{batchTaskId,jdbcType=VARCHAR},
            </if>
            <if test="batchtaskNo != null">
                BatchtaskNo = #{batchtaskNo,jdbcType=VARCHAR},
            </if>
            <if test="taskName != null">
                TaskName = #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                Status = #{status,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = now(),
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateGatherTaskStatus" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskPO">
        update gathertask set
        Status = 1,
        LastUpdateUser = #{lastUpdateUser,jdbcType=BIGINT},
        LastUpdateTime = now()
        where Id = #{id,jdbcType=BIGINT}
        <if test="orgId!=null">
            and Org_Id=#{orgId,jdbcType=INTEGER}
        </if>
    </update>
</mapper>