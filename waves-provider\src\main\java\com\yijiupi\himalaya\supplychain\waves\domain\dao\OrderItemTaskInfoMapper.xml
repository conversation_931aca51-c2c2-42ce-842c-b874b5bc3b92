<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="INTEGER" property="orgId"/>
        <result column="RefOrderItem_Id" jdbcType="BIGINT" property="refOrderItemId"/>
        <result column="RefOrder_Id" jdbcType="BIGINT" property="refOrderId"/>
        <result column="BatchTaskItem_Id" jdbcType="VARCHAR" property="batchTaskItemId"/>
        <result column="BatchTask_Id" jdbcType="VARCHAR" property="batchTaskId"/>
        <result column="BatchTaskNo" jdbcType="VARCHAR" property="batchTaskNo"/>
        <result column="Batch_Id" jdbcType="VARCHAR" property="batchId"/>
        <result column="BatchNo" jdbcType="VARCHAR" property="batchNo"/>
        <result column="UnitTotalCount" jdbcType="DECIMAL" property="unitTotalCount"/>
        <result column="OverSortCount" jdbcType="DECIMAL" property="overSortCount"/>
        <result column="LackUnitCount" jdbcType="DECIMAL" property="lackUnitCount"/>
        <result column="MoveCount" jdbcType="DECIMAL" property="moveCount"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="OriginalUnitTotalCount" property="originalUnitTotalCount" jdbcType="DECIMAL"/>
    </resultMap>

    <resultMap id="BaseResultDetailMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="INTEGER" property="orgId"/>
        <result column="RefOrderItem_Id" jdbcType="BIGINT" property="refOrderItemId"/>
        <result column="RefOrder_Id" jdbcType="BIGINT" property="refOrderId"/>
        <result column="BatchTaskItem_Id" jdbcType="VARCHAR" property="batchTaskItemId"/>
        <result column="BatchTask_Id" jdbcType="VARCHAR" property="batchTaskId"/>
        <result column="BatchTaskNo" jdbcType="VARCHAR" property="batchTaskNo"/>
        <result column="Batch_Id" jdbcType="VARCHAR" property="batchId"/>
        <result column="BatchNo" jdbcType="VARCHAR" property="batchNo"/>
        <result column="UnitTotalCount" jdbcType="DECIMAL" property="unitTotalCount"/>
        <result column="OverSortCount" jdbcType="DECIMAL" property="overSortCount"/>
        <result column="LackUnitCount" jdbcType="DECIMAL" property="lackUnitCount"/>
        <result column="MoveCount" jdbcType="DECIMAL" property="moveCount"/>
        <result column="TaskState" jdbcType="TINYINT" property="batchTaskItemState"/>
        <result column="OriginalUnitTotalCount" property="originalUnitTotalCount" jdbcType="DECIMAL"/>
        <result column="SkuId" jdbcType="BIGINT" property="skuId"/>
        <result column="ProductName" jdbcType="VARCHAR" property="productName"/>
        <collection property="detailList"
                    ofType="com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO">
            <id column="Detail_Id" property="id" jdbcType="BIGINT"/>
            <result column="Detail_TaskInfo_Id" jdbcType="BIGINT" property="taskInfoId"/>
            <result column="Detail_SecOwner_Id" jdbcType="BIGINT" property="secOwnerId"/>
            <result column="Detail_Owner_Id" jdbcType="BIGINT" property="ownerId"/>
            <result column="Detail_ProductSpecification_Id" jdbcType="BIGINT" property="productSpecificationId"/>
            <result column="Detail_UnitTotalCount" jdbcType="DECIMAL" property="unitTotalCount"/>
            <result column="Detail_OriginalUnitTotalCount" property="originalUnitTotalCount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Org_Id, RefOrderItem_Id, RefOrder_Id, BatchTaskItem_Id, BatchTask_Id, BatchTaskNo,
        Batch_Id, BatchNo, UnitTotalCount, CreateTime, CreateUser, LastUpdateTime, LastUpdateUser,
        OverSortCount, LackUnitCount, MoveCount, OriginalUnitTotalCount
    </sql>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO orderitemtaskinfo (
        Id,
        Org_Id,
        RefOrderItem_Id,
        RefOrder_Id,
        BatchTaskItem_Id,
        BatchTask_Id,
        BatchTaskNo,
        Batch_Id,
        BatchNo,
        UnitTotalCount,
        CreateUser,
        OriginalUnitTotalCount
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.orgId,jdbcType=INTEGER},
            #{item.refOrderItemId,jdbcType=BIGINT},
            #{item.refOrderId,jdbcType=BIGINT},
            #{item.batchTaskItemId,jdbcType=VARCHAR},
            #{item.batchTaskId,jdbcType=VARCHAR},
            #{item.batchTaskNo,jdbcType=VARCHAR},
            #{item.batchId,jdbcType=VARCHAR},
            #{item.batchNo,jdbcType=VARCHAR},
            #{item.unitTotalCount,jdbcType=DECIMAL},
            #{item.createUser,jdbcType=VARCHAR},
            #{item.originalUnitTotalCount,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

    <select id="listOrderItemTaskInfoByBatchNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from orderitemtaskinfo
        where BatchNo in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="checkBatchCanDeleteByBatchNoAndOutBoundType" resultType="java.lang.Integer">
        select count(0)
        from orderitemtaskinfo ot
        inner join outstockorder os on os.id = ot.RefOrder_Id
        where ot.BatchNo in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and os.outBoundType = #{outBoundType,jdbcType=INTEGER}
    </select>

    <select id="listOrderItemTaskInfoByOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from orderitemtaskinfo
        where RefOrder_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listOrderItemTaskInfoByOrderItemIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from orderitemtaskinfo
        where RefOrderItem_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listOrderItemTaskInfoByBatchTaskItemIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from orderitemtaskinfo
        where BatchTaskItem_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <sql id="taskInfoDetailSql">
        info.Id,
        info.Org_Id,
        info.RefOrderItem_Id,
        info.RefOrder_Id,
        info.BatchTaskItem_Id,
        info.BatchTask_Id,
        info.BatchTaskNo,
        info.Batch_Id,
        info.BatchNo,
        info.UnitTotalCount,
        info.OverSortCount,
        info.LackUnitCount,
        info.MoveCount,
        info.OriginalUnitTotalCount,
        detail.Id as Detail_Id,
        detail.TaskInfo_Id as Detail_TaskInfo_Id,
        detail.SecOwner_Id as Detail_SecOwner_Id,
        detail.Owner_Id as Detail_Owner_Id,
        detail.ProductSpecification_Id as Detail_ProductSpecification_Id,
        detail.UnitTotalCount as Detail_UnitTotalCount,
        detail.OriginalUnitTotalCount as Detail_OriginalUnitTotalCount
    </sql>

    <select id="listTaskInfoAndDetailByOrderIds" resultMap="BaseResultDetailMap">
        select
        <include refid="taskInfoDetailSql"/>
        from orderitemtaskinfo info
        left join orderitemtaskinfodetail detail on info.Id = detail.TaskInfo_Id
        where info.RefOrder_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listTaskInfoAndDetailByOrderItemIds" resultMap="BaseResultDetailMap">
        select
        <include refid="taskInfoDetailSql"/>
        from orderitemtaskinfo info
        left join orderitemtaskinfodetail detail on info.Id = detail.TaskInfo_Id
        where info.RefOrderItem_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listTaskInfoAndDetailByBatchTaskItemIds" resultMap="BaseResultDetailMap">
        select
        <include refid="taskInfoDetailSql"/>
        , bti.TaskState
        , bti.SkuId
        , bti.ProductName
        from orderitemtaskinfo info
        left join orderitemtaskinfodetail detail on info.Id = detail.TaskInfo_Id
        inner join batchtaskitem bti on info.BatchTaskItem_Id = bti.id
        where info.BatchTaskItem_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="listTaskInfoAndDetailByBatchTaskIds" resultMap="BaseResultDetailMap">
        select
        <include refid="taskInfoDetailSql"/>
        from orderitemtaskinfo info
        left join orderitemtaskinfodetail detail on info.Id = detail.TaskInfo_Id
        where info.BatchTask_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="listTaskInfoByBatchTaskIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from orderitemtaskinfo
        where BatchTask_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <delete id="deleteByIds" parameterType="java.util.List">
        delete from orderitemtaskinfo
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </delete>

    <delete id="deleteByBatchNos" parameterType="java.util.List">
        delete from orderitemtaskinfo
        where BatchNo in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <update id="updateBatch" parameterType="java.util.List">
        UPDATE orderitemtaskinfo
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="UnitTotalCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.unitTotalCount != null ">
                        when id=#{item.id} then #{item.unitTotalCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.unitTotalCount == null ">
                        when id=#{item.id} then UnitTotalCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="OverSortCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.overSortCount != null ">
                        when id=#{item.id} then #{item.overSortCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.overSortCount == null ">
                        when id=#{item.id} then OverSortCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="LackUnitCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.lackUnitCount != null ">
                        when id=#{item.id} then #{item.lackUnitCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.lackUnitCount == null ">
                        when id=#{item.id} then LackUnitCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="MoveCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.moveCount != null ">
                        when id=#{item.id} then #{item.moveCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.moveCount == null ">
                        when id=#{item.id} then MoveCount
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateBatchByBatchTask" parameterType="java.util.List">
        UPDATE orderitemtaskinfo
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="BatchTask_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.batchTaskId != null ">
                        when id=#{item.id} then #{item.batchTaskId,jdbcType=VARCHAR}
                    </if>
                    <if test="item.batchTaskId == null ">
                        when id=#{item.id} then BatchTask_Id
                    </if>
                </foreach>
            </trim>
            <trim prefix="BatchTaskNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.batchTaskNo != null ">
                        when id=#{item.id} then #{item.batchTaskNo,jdbcType=VARCHAR}
                    </if>
                    <if test="item.batchTaskNo == null ">
                        when id=#{item.id} then BatchTaskNo
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="listOrderRelateBatch" resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO">
        SELECT
        DISTINCT oo.RefOrderNo as refOrderNo, info.BatchNo as batchNo
        FROM orderitemtaskinfo info
        inner join outstockorder oo on info.RefOrder_Id = oo.id
        where oo.RefOrderNo in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and oo.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </select>

    <select id="getLackProductSkuId" resultType="java.lang.Long">
        select
        DISTINCT oi.SkuId
        from orderitemtaskinfo info
        inner join outstockorderitem oi on info.RefOrderItem_Id = oi.id
        inner join outstockorder o on info.RefOrder_Id = o.id
        where info.Org_Id = #{cityId,jdbcType=INTEGER}
        and o.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and (info.LackUnitCount != 0 or info.MoveCount != 0)
        and info.LastUpdateTime >= #{startTime,jdbcType=TIMESTAMP}
        and info.LastUpdateTime <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
    </select>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO">
        update orderItemTaskInfo
        <set>
            <if test="overSortCount != null">
                OverSortCount = #{overSortCount,jdbcType=DECIMAL},
            </if>
            <if test="lackUnitCount != null">
                LackUnitCount = #{lackUnitCount,jdbcType=DECIMAL},
            </if>
            <if test="moveCount != null">
                MoveCount = #{moveCount,jdbcType=DECIMAL},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <select id="listRefOrderIdByBatchTaskIds" resultType="java.lang.Long">
        select
        distinct reforder_id
        from orderitemtaskinfo
        where BatchTask_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="listTaskInfoAndDetailByBatchTaskItemIdsAndOrgId" resultMap="BaseResultDetailMap">
        select
        <include refid="taskInfoDetailSql"/>
        , bti.TaskState
        from orderitemtaskinfo info
        left join orderitemtaskinfodetail detail on info.Id = detail.TaskInfo_Id
        inner join batchtaskitem bti on info.BatchTaskItem_Id = bti.id
        where info.BatchTaskItem_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and info.org_Id = #{orgId, jdbcType=INTEGER}
    </select>


    <select id="listTaskInfoByBatchTaskItemIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from orderitemtaskinfo
        where BatchTaskItem_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and org_Id = #{orgId, jdbcType=INTEGER}
    </select>

    <select id="findByBatchTaskAndOrder" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from orderitemtaskinfo
        where bachtask_Id in
        <foreach collection="batchTaskIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and reforder_id in
        <foreach collection="orderIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

</mapper>