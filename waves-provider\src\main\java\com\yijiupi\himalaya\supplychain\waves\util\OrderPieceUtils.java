package com.yijiupi.himalaya.supplychain.waves.util;

import com.yijiupi.himalaya.common.dto.GetItemProductSizeDTO;
import com.yijiupi.himalaya.common.util.GetItemProductSize;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-11-06 11:41
 **/
public class OrderPieceUtils {

    /**
     * 重算出库单大小件
     *
     * @param orders 出库单数据
     * @return 重算结果 0 是大件, 1 是小件
     */
    public static BigDecimal[] reCalcOrderPiece(@Nullable List<OutStockOrderPO> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO};
        }
        List<OutStockOrderItemPO> items = orders.stream().map(OutStockOrderPO::getItems).filter(Objects::nonNull)
                .flatMap(Collection::stream).collect(Collectors.toList());
        return calculateSize(items);
    }

    /**
     * 计算大件数量
     *
     * @param items 订单项
     * @return 大件数量
     */
    public static BigDecimal calcPackageCount(@Nullable List<OutStockOrderItemPO> items) {
        return calculateSize(items)[0];
    }

    /**
     * 计算小件数量
     *
     * @param items 订单项
     * @return 小件数量
     */
    public static BigDecimal calcUnitCount(@Nullable List<OutStockOrderItemPO> items) {
        return calculateSize(items)[1];
    }

    private static BigDecimal[] calculateSize(@Nullable List<OutStockOrderItemPO> items) {
        List<GetItemProductSizeDTO> list = Optional.ofNullable(items).orElseGet(Collections::emptyList).stream().map(it -> {
            GetItemProductSizeDTO dto = new GetItemProductSizeDTO();
            dto.setProductSkuId(it.getSkuid());
            dto.setMinUnitTotalCount(it.getUnittotalcount());
            dto.setSpecQuantity(it.getSpecquantity());
            return dto;
        }).collect(Collectors.toList());
        return GetItemProductSize.getDeliverySizeByMin(list);
    }

}
