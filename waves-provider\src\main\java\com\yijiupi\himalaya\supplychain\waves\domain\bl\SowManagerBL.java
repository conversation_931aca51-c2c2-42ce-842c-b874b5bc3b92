package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.common.constant.RedisConstant;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.distributedlock.exceptions.HasLockedException;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryManageService;
import com.yijiupi.himalaya.supplychain.constants.WarehouseConfigConstants;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.enums.InventoryChangeTypeEnum;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.waves.advice.MarkMethodInvokeFlag;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchFinishedBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.sowtask.CreateSowTaskByOutStockOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchInventoryTransferBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder.OutStockOrderStateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.perform.TaskPerformanceCalculateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.wcs.NotifyWCSBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.TaskPerformanceCalculateBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.SowConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderItemDetailAllotDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderChangeDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderSaveDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.secondsort.SecondSortQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.*;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.*;
import com.yijiupi.himalaya.supplychain.waves.dto.trace.OrderTraceDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderSearchSO;
import com.yijiupi.himalaya.supplychain.waves.util.RedisKeyConstant;
import com.yijiupi.himalaya.supplychain.waves.util.RedisLock;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;
import com.yijiupi.himalaya.supplychain.waves.utils.StreamUtils;

@Service
@Transactional(rollbackFor = Exception.class)
public class SowManagerBL {
    private static final Logger LOG = LoggerFactory.getLogger(SowManagerBL.class);

    @Autowired
    private SowTaskMapper sowTaskMapper;

    @Autowired
    private SowOrderMapper sowOrderMapper;

    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;

    @Autowired
    private PackageOrderItemBL packageOrderItemBL;

    @Autowired
    private OrderTraceBL orderTraceBL;

    @Reference
    private IBatchInventoryManageService iBatchInventoryManageService;
    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Reference
    private ILocationService iLocationService;

    @Autowired
    private BatchTaskMapper batchTaskMapper;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;

    @Autowired
    private BillReviewBL billReviewBL;

    @Autowired
    private BatchOrderBL batchOrderBL;

    @Autowired
    private SowTaskItemMapper sowTaskItemMapper;

    @Autowired
    private SowQueryBL sowQueryBL;

    @Autowired
    private OutStockOrderBL outStockOrderBL;

    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @Autowired
    private SowCalculationBL sowCalculationBL;

    @Autowired
    private IdempotenceBL idempotenceBL;

    @Autowired
    private OrderTracePOMapper orderTracePOMapper;

    @Autowired
    private BatchFinishedBL batchFinishedBL;

    @Autowired
    private CreateSowTaskByOutStockOrderBL createSowTaskByOutStockOrderBL;

    @Reference
    private IProductSkuService productSkuService;

    @Resource
    private NotifyWCSBL notifyWCSBL;

    @Resource
    private OutStockOrderStateBL outStockOrderStateBL;
    @Autowired
    private BatchInventoryTransferBL batchInventoryTransferBL;
    @Autowired
    private WarehouseAllocationTypeManageBL warehouseAllocationTypeManageBL;
    @Autowired
    private TaskPerformanceCalculateBL taskPerformanceCalculateBL;

    private static final String DESCRIPTION = "播种缺货复核";

    /**
     * 根据波次编号查询播种任务
     */
    public List<SowTaskDTO> listSowTaskByBatchNos(List<String> batchNos) {
        List<SowTaskPO> sowTaskPOS = sowTaskMapper.listSowTaskByBatchNos(batchNos);
        return SowConverter.sowTaskPOS2SowtaskDTOS(sowTaskPOS);
    }

    /**
     * 批量保存播种任务
     *
     * @param lstSowTaskPO
     */
    public void insertSowTaskList(List<SowTaskPO> lstSowTaskPO) {
        if (CollectionUtils.isNotEmpty(lstSowTaskPO)) {
            // LOG.info("新增播种任务数据:{}", JSON.toJSONString(lstSowTaskPO));
            // 设置分仓属性
            warehouseAllocationTypeManageBL.setSowTaskWarehouseAllocationType(lstSowTaskPO);

            sowTaskMapper.insertSowTaskList(lstSowTaskPO);
            List<SowTaskItemPO> sowTaskItemPOS =
                lstSowTaskPO.stream().filter(sowTask -> CollectionUtils.isNotEmpty(sowTask.getSowTaskItemPOS()))
                    .flatMap(sowTask -> sowTask.getSowTaskItemPOS().stream()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sowTaskItemPOS)) {
                sowTaskItemMapper.batchInsert(sowTaskItemPOS);
            }
        }
    }

    /**
     * 批量保存播种任务与订单关联信息
     *
     * @param lstSowOrders
     */
    public void insertSowOrderList(List<SowOrderPO> lstSowOrders) {
        if (CollectionUtils.isEmpty(lstSowOrders)) {
            return;
        }
        sowOrderMapper.insertSowOrderList(lstSowOrders);
    }

    /**
     * 批量保存播种任务与订单关联信息
     *
     * @param lstSowOrders
     */
    public void insertSowOrderList(List<SowOrderPO> lstSowOrders, List<String> orderNos) {
        if (CollectionUtils.isEmpty(lstSowOrders)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(orderNos)) {
            lstSowOrders =
                lstSowOrders.stream().filter(it -> !orderNos.contains(it.getRefOrderNo())).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(lstSowOrders)) {
            sowOrderMapper.insertSowOrderList(lstSowOrders);
        }
    }

    /**
     * 提交播种任务并保存 <br/>
     * FIXME 这个方法没搜到日志，是不是可以废弃了
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveSowOrder(SowOrderSaveDTO sowOrderSaveDTO) {
        Integer orgId = sowOrderSaveDTO.getOrgId();
        String sowTaskNo = sowOrderSaveDTO.getSowTaskNo();
        Integer warehouseId = sowOrderSaveDTO.getWarehouseId();
        String lastUpdateUser = sowOrderSaveDTO.getOperatorUserName();
        Integer lastUpdateUserId = sowOrderSaveDTO.getOperatorUserId();

        // 播种任务已完成的不能保存
        SowTaskPO sowTaskPO = sowTaskMapper.findSowTaskByIdOrNo(orgId, null, sowTaskNo);

        if (sowTaskPO == null) {
            throw new BusinessException("该播种任务:" + sowTaskNo + "不存在");
        }
        if (sowTaskPO.getState() == SowTaskStateEnum.已播种.getType()) {
            throw new BusinessValidateException("该播种任务:" + sowTaskNo + "已完成,不能重复提交");
        }

        // 通过播种任务编号查询未完成拣货的拣货任务
        List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findNoFinishTaskBySowTaskNo(sowTaskNo, orgId);
        if (CollectionUtils.isNotEmpty(batchTaskPOS)) {
            throw new BusinessValidateException("请将播种任务:" + sowTaskNo + "下的拣货任务全部完成后再进行提交");
        }

        // pda装箱时需要判断装箱是否全部完成
        WarehouseConfigDTO warehouseConfig = warehouseConfigService.getConfigByWareHouseId(warehouseId);
        if (warehouseConfig != null && warehouseConfig.getWhetherPacking() == 1) {
            checkSowPackage(sowTaskNo, orgId);
        }

        String batchNo = sowTaskPO.getBatchNo();
        SowTaskDTO sowTaskDTO = new SowTaskDTO();
        sowTaskDTO.setId(sowTaskPO.getId());
        sowTaskDTO.setOrgId(orgId);
        sowTaskDTO.setWarehouseId(warehouseId);
        sowTaskDTO.setLastUpdateUser(lastUpdateUser);
        sowTaskDTO.setSowTaskNo(sowTaskNo);
        sowTaskDTO.setBatchNo(batchNo);
        sowTaskDTO.setState(SowTaskStateEnum.已播种.getType());
        sowTaskDTO.setCompleteTime(
            sowOrderSaveDTO.getCompleteTime() == null ? new Date() : sowOrderSaveDTO.getCompleteTime());
        if (sowTaskPO.getStartTime() == null) {
            sowTaskDTO
                .setStartTime(sowOrderSaveDTO.getStartTime() == null ? new Date() : sowOrderSaveDTO.getStartTime());
        }

        // 得到播种任务的播种数量信息
        List<SownOrderItemDTO> sownOrderItems = sowOrderSaveDTO.getSownOrderItems();
        if (CollectionUtils.isNotEmpty(sownOrderItems)) {
            // 修改播种订单关联信息表的播种数量信息
            if (sowTaskPO.getOperationMode() == null) {
                sowOrderMapper.updateSownAmountByOrderId(sowTaskNo, sownOrderItems, lastUpdateUser, orgId);
            } else {
                sowOrderMapper.updateSownAmount(sowTaskNo, sownOrderItems, lastUpdateUser, orgId);
            }
        }
        if (sowOrderSaveDTO.getBillReviewDTO() != null) {
            billReviewBL.updateBillReview(sowOrderSaveDTO.getBillReviewDTO());
            sowTaskDTO.setNeedLog(false);
        }

        // 更新播种任务信息,增加操作日志
        updateSowTaskById(sowTaskDTO);

        // 得到播种任务下的订单包装信息
        List<PackageOrderItemDTO> packageOrderItems = sowOrderSaveDTO.getPackageOrderItems();
        if (CollectionUtils.isNotEmpty(packageOrderItems)) {
            // 保存包装信息
            packageOrderItemBL.savePackageBatch(packageOrderItems, false, false);
        }

        // 播种移库数据
        List<PickUpDTO> pickUpDTOList = processSowTaskCompleteItems(warehouseId, orgId, sowTaskNo);

        // 校验移库
        if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
            pickUpDTOList = batchInventoryTransferBL.checkInventoryTransferByPickUp(pickUpDTOList);
        }

        // 更新波次状态
        try {
            batchOrderBL.updateBatchStateByBatchNo(batchNo, lastUpdateUser, lastUpdateUserId);
        } catch (Exception e) {
            throw new BusinessValidateException("正在处理，请稍后再试！");
        }

        // 移库
        LOG.info("拣货移库入参{}，sowTaskNo：{}", new Gson().toJson(pickUpDTOList), sowTaskNo);
        if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
            PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
            pickUpChangeRecordDTO.setCityId(orgId);
            pickUpChangeRecordDTO.setOrderNo(sowTaskNo);
            pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.播种任务完成.name());
            pickUpChangeRecordDTO.setCreateUser(lastUpdateUser);
            iBatchInventoryManageService.pickupCompleteBySku(pickUpDTOList, pickUpChangeRecordDTO);
        }
    }

    /**
     * 播种任务完成后，移库
     */
    private List<PickUpDTO> processSowTaskCompleteItems(Integer warehouseId, Integer orgId, String sowTaskNo) {
        SowTaskPO sowTaskPO = sowTaskMapper.findSowTaskByIdOrNo(orgId, null, sowTaskNo);
        AssertUtils.notNull(sowTaskPO, "播种任务不存在！");
        List<BatchTaskItemDTO> batchTaskItemDTOList =
            batchTaskItemMapper.findBatchTaskItemDtoListBySowTaskNo(sowTaskNo);
        AssertUtils.notEmpty(batchTaskItemDTOList, "拣货任务不存在！");
        List<PickUpDTO> pickUpDTOList = new ArrayList<>();

        if (sowTaskPO.getOperationMode() != null) {
            // 查询播种订单明细
            List<SowProductItemDTO> sowProductItemDTOS =
                sowOrderMapper.listSowingPrintItems(Lists.newArrayList(sowTaskNo), orgId);

            // 查询播种任务容器对应出库位信息
            SowAddressQueryDTO sowAddressQueryDTO = new SowAddressQueryDTO();
            sowAddressQueryDTO.setOrgId(orgId);
            sowAddressQueryDTO.setSowTaskNo(sowTaskNo);
            Map<Integer, List<SowAddressDTO>> sowAddressMap = sowQueryBL.listSowAddress(sowAddressQueryDTO).stream()
                .collect(Collectors.groupingBy(SowAddressDTO::getContainerNo));

            // 从容器货位挪到出库位
            sowProductItemDTOS.forEach(item -> {
                List<SowAddressDTO> sowAddressDTOS = sowAddressMap.get(item.getContainerNo());
                if (CollectionUtils.isEmpty(sowAddressDTOS)) {
                    LOG.info("播种任务容器项:{}", JSON.toJSONString(item));
                    throw new BusinessValidateException(item.getContainerNo() + "号容器中有订单未分配周转区,请联系仓管,指定周转区");
                }
                PickUpDTO pickUpDTO = new PickUpDTO();
                pickUpDTO.setProductSkuId(item.getSkuId());
                pickUpDTO.setWarehouseId(warehouseId);
                pickUpDTO.setFromChannel(item.getChannel().intValue());
                pickUpDTO.setFromSource(item.getSource().intValue());
                // 容器中该商品总播种数量
                pickUpDTO.setCount(item.getUnitTotalCount());
                // 知花知果播种任务，放到容器
                pickUpDTO.setFromLocationId(item.getContainerLocationId());

                // 从容器放到出库位
                pickUpDTO.setLocationId(sowAddressDTOS.get(0).getToLocationId());

                pickUpDTO.setToSource(item.getSource().intValue());
                pickUpDTO.setToChannel(item.getChannel().intValue());
                pickUpDTOList.add(pickUpDTO);
            });

        } else {
            // 从集货区挪到备货区
            for (BatchTaskItemDTO batchTaskItemDTO : batchTaskItemDTOList) {
                if (batchTaskItemDTO.getToLocationId() == null) {
                    throw new BusinessValidateException("本组订单未分配周转区,请联系仓管,在播种任务列表中,为播种任务:" + sowTaskNo + "指定周转区");
                }
                PickUpDTO pickUpDTO = new PickUpDTO();
                pickUpDTO.setProductSkuId(batchTaskItemDTO.getSkuId());
                pickUpDTO.setWarehouseId(warehouseId);
                pickUpDTO.setFromChannel(batchTaskItemDTO.getChannel().intValue());
                pickUpDTO.setFromSource(batchTaskItemDTO.getSource().intValue());
                // 实际拣货数量
                pickUpDTO.setCount(batchTaskItemDTO.getUnitTotalCount().subtract(batchTaskItemDTO.getLackUnitCount()));
                // 播种任务，拣货完成，先放到集货区
                pickUpDTO.setFromLocationId(sowTaskPO.getLocationId());
                // 播种任务完成后，从集货区挪到备货区
                pickUpDTO.setLocationId(batchTaskItemDTO.getToLocationId());

                pickUpDTO.setToSource(batchTaskItemDTO.getSource().intValue());
                pickUpDTO.setToChannel(batchTaskItemDTO.getChannel().intValue());
                if (pickUpDTO.getCount().compareTo(BigDecimal.ZERO) == 0) {
                    LOG.info("该拣货任务详情(id:{})的拣货数量为空！", batchTaskItemDTO.getId());
                } else {
                    pickUpDTOList.add(pickUpDTO);
                }
            }
        }
        return pickUpDTOList;
    }

    /**
     * 查找集货位下可播种的播种任务(通过时间倒序找到第一个拣货完成的播种任务)
     */
    public SowTaskDTO findCanSowingTask(SowTaskReceiveDTO taskReceiveDTO) {
        List<SowTaskPO> sowTaskPOList = sowTaskMapper.findCanSowingTaskListByLocationName(taskReceiveDTO);
        if (CollectionUtils.isEmpty(sowTaskPOList)) {
            throw new BusinessValidateException("没有可领取的播种任务，请检查播种任务下的拣货任务项是否完成");
        }

        List<Long> sowTaskIds = sowTaskPOList.stream().map(SowTaskPO::getId).collect(Collectors.toList());
        List<BatchTaskPO> batchTaskList = batchTaskMapper.selectBatchTaskBySowTaskIds(sowTaskIds);
        if (CollectionUtils.isEmpty(batchTaskList)) {
            throw new BusinessValidateException("没有可领取的播种任务，请检查播种任务下的拣货任务项是否完成");
        }

        List<String> batchTaskIds = batchTaskList.stream().map(BatchTaskPO::getId).collect(Collectors.toList());
        List<BatchTaskItemPO> batchTaskItemList = batchTaskItemMapper.findBatchTaskItemDTOListByTaskIds(batchTaskIds);

        // //获取拣货任务信息
        // List<BatchTaskPO> batchTaskPOS = batchTaskMapper.listBatchTaskBySowNo(sowTaskPO.getSowTaskNo(), orgId);
        // //查找未分配周转区的拣货任务编号
        // List<String> toLocationBatchTaskNos = batchTaskPOS.stream().filter(batchTaskPO ->
        // batchTaskPO.getToLocationId() != null)
        // .map(BatchTaskPO::getBatchTaskNo).collect(Collectors.toList());
        // if (CollectionUtils.isEmpty(toLocationBatchTaskNos)) {
        // throw new BusinessValidateException("请仓库管理员为播种任务:" + sowTaskPO.getSowTaskNo() + "分配周转区");
        // }
        //
        // //周转区字符串拼接
        // List<Long> toLocationIds = batchTaskPOS.stream().filter(batchTask -> batchTask.getToLocationId() !=
        // null).map(BatchTaskPO::getToLocationId).collect(Collectors.toList());
        // String toLocationNames = batchTaskPOS.stream().filter(batchTask -> batchTask.getToLocationId() !=
        // null).map(BatchTaskPO::getToLocationName).collect(Collectors.joining(","));
        SowTaskPO sowTaskPO = getSowTaskId(batchTaskList, batchTaskItemList, sowTaskPOList);

        SowTaskDTO sowTaskDTO = new SowTaskDTO();
        BeanUtils.copyProperties(sowTaskPO, sowTaskDTO);
        // sowTaskDTO.setToLocationIds(Joiner.on(",").join(toLocationIds));
        // sowTaskDTO.setToLocationNames(toLocationNames);
        return sowTaskDTO;
    }

    private SowTaskPO getSowTaskId(List<BatchTaskPO> batchTaskList, List<BatchTaskItemPO> batchTaskItemList,
        List<SowTaskPO> sowTaskPOList) {
        Map<Long, SowTaskPO> sowTaskMap = sowTaskPOList.stream().collect(Collectors.toMap(SowTaskPO::getId, v -> v));
        // 按时间排序找，如果第一个有 机器人的，直接返回；如果没有，过滤出
        List<BatchTaskPO> robotBatchTaskList = batchTaskList.stream().filter(m -> Objects.nonNull(m.getPickPattern()))
            .filter(m -> !BatchTaskPickPatternEnum.人工拣货.getType().equals(m.getPickPattern()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(robotBatchTaskList)) {
            List<SowTaskPO> robotSowTaskList = robotBatchTaskList.stream().map(BatchTaskPO::getSowTaskId)
                .map(sowTaskMap::get).filter(sowTask -> SowTaskStateEnum.已播种.getType() != sowTask.getState())
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(robotSowTaskList)) {
                robotSowTaskList.sort(Comparator.comparing(SowTaskPO::getCreateTime));
                return robotSowTaskList.get(0);
            }
        }

        Map<String, BatchTaskPO> batchTaskMap =
            batchTaskList.stream().collect(Collectors.toMap(BatchTaskPO::getId, v -> v));
        List<BatchTaskItemPO> finishTaskItemList = batchTaskItemList.stream()
            .filter(m -> TaskItemStateEnum.已完成.getType() == m.getTaskState()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(finishTaskItemList)) {
            throw new BusinessValidateException("没有可领取的播种任务，请检查播种任务下的拣货任务项是否完成");
        }
        List<SowTaskPO> normalSowTaskList = finishTaskItemList.stream().map(BatchTaskItemPO::getBatchTaskId).distinct()
            .map(batchTaskMap::get).map(BatchTaskPO::getSowTaskId).map(sowTaskMap::get).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(normalSowTaskList)) {
            throw new BusinessValidateException("没有可领取的播种任务，请检查播种任务下的拣货任务项是否完成");
        }
        normalSowTaskList.sort(Comparator.comparing(SowTaskPO::getCreateTime));
        return normalSowTaskList.get(0);
    }

    /**
     * 更新播种任务信息,添加操作记录
     *
     * @param sowTaskDTO
     */
    @MarkMethodInvokeFlag(key = RedisConstant.SUP_F + "UserOnlineCache", warehouseId = "#sowTaskDTO.warehouseId",
        userId = "#sowTaskDTO.operatorId")
    public void updateSowTaskById(SowTaskDTO sowTaskDTO) {
        LOG.info("播种任务更新参数信息:{}", JSON.toJSONString(sowTaskDTO));
        Long id = sowTaskDTO.getId();
        if (id != null) {
            SowTaskPO po = sowTaskMapper.selectByPrimaryKey(id);
            SowTaskPO sowTaskPO = new SowTaskPO();
            BeanUtils.copyProperties(sowTaskDTO, sowTaskPO);
            if (sowTaskDTO.getLocationId() == null) {
                sowTaskDTO.setLocationId(po.getLocationId());
            }
            if (sowTaskDTO.getLocationName() == null) {
                sowTaskDTO.setLocationName(po.getLocationName());
            }
            // 更新操作日志
            Byte state = sowTaskPO.getState();
            if (state != null && state == SowTaskStateEnum.已播种.getType()) {
                if (sowTaskDTO.getCompleteTime() == null) {
                    sowTaskDTO.setCompleteTime(new Date());
                    sowTaskPO.setCompleteTime(new Date());
                }
                orderTraceBL.updateSowTaskState(sowTaskDTO, OrderTraceDescriptionEnum.播种任务完成.name());
            } else if (state != null && state == SowTaskStateEnum.播种中.getType()) {
                if (sowTaskDTO.getStartTime() == null) {
                    sowTaskDTO.setStartTime(new Date());
                    sowTaskPO.setStartTime(new Date());
                }
                orderTraceBL.updateSowTaskState(sowTaskDTO, OrderTraceDescriptionEnum.播种任务领取成功.name());
            }

            sowTaskMapper.updateByPrimaryKeySelective(sowTaskPO);
        }
    }

    // 追加播种任务
    public void appendSowTask(SowTaskPO sowTaskPO) {
        SowTaskDTO sowTaskDTO = new SowTaskDTO();
        BeanUtils.copyProperties(sowTaskPO, sowTaskDTO);
        LOG.info("播种任务更新参数信息:{}", JSON.toJSONString(sowTaskDTO));
        // 更新操作日志
        orderTraceBL.updateSowTaskState(sowTaskDTO, OrderTraceDescriptionEnum.播种任务追加成功.name());

        List<SowTaskItemPO> sowTaskItemPOS = sowTaskPO.getSowTaskItemPOS();
        if (CollectionUtils.isNotEmpty(sowTaskItemPOS)) {
            sowTaskItemMapper.batchInsert(sowTaskItemPOS);
        }

        int skuCount = sowTaskItemMapper.findBySowTaskItemSkuCount(sowTaskDTO.getId());
        //
        // BigDecimal packageAmount =
        // sowTaskItemPOS.stream().map(SowTaskItemPO::getPackageCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // BigDecimal unitAmount =
        // sowTaskItemPOS.stream().map(SowTaskItemPO::getUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        //
        sowTaskPO.setSkuCount(skuCount);
        // sowTaskPO.setPackageAmount(packageAmount);
        // sowTaskPO.setUnitAmount(unitAmount);

        sowTaskMapper.updateByPrimaryKeySelective(sowTaskPO);

    }

    /**
     * 给没有集货位的播种任务设置集货位(通道开启分区分单)
     *
     * @param batchTaskPO
     */
    public void updateSowTaskLocation(BatchTaskPO batchTaskPO) {
        SowTaskDTO sowTaskDTO = setSowLocationByBatchTask(batchTaskPO);
        if (sowTaskDTO.getLocationId() == null && sowTaskDTO.getId() != null) {
            throw new BusinessValidateException("当前仓库没有空闲集货位");
        }
    }

    /**
     * 查找开启分区分单的播种任务要存放的集货位
     *
     * @return
     */
    private SowTaskDTO setSowLocationByBatchTask(BatchTaskPO batchTaskPO) {
        SowTaskDTO sowTaskDTO = new SowTaskDTO();
        Long sowTaskId = batchTaskPO.getSowTaskId();
        if (sowTaskId != null) {
            SowTaskPO sowTaskPO = sowTaskMapper.selectByPrimaryKey(sowTaskId);
            Long locationId = sowTaskPO.getLocationId();
            if (locationId == null) {
                sowTaskDTO.setId(sowTaskId);
                Integer orgId = sowTaskPO.getOrgId();
                BatchPO batchPO = batchMapper.selectBatchByBatchNo(orgId, batchTaskPO.getBatchNo());
                BatchBO batchBO = new BatchBO(batchPO);
                // 如果开启分区分单，查找空闲的集货位，
                List<LocationReturnDTO> locationList = sowCalculationBL.findSowLocation(batchBO, null, sowTaskId);
                LocationReturnDTO toLocation = new LocationReturnDTO();
                if (!CollectionUtils.isEmpty(locationList)) {
                    boolean needAccumulate =
                        Objects.equals(sowTaskPO.getSowTaskType(), SowTaskTypeEnum.播种墙播种.getType());
                    toLocation = createSowTaskByOutStockOrderBL.allocationLocationOld(locationList, needAccumulate);
                }

                // 为播种任务设置集货位
                if (null != toLocation.getId()) {
                    sowTaskDTO.setOrgId(orgId);
                    sowTaskDTO.setLocationId(toLocation.getId());
                    sowTaskDTO.setLocationName(toLocation.getName());
                    sowTaskDTO.setLastUpdateUser(batchTaskPO.getSorter());
                    updateSowTaskById(sowTaskDTO);
                    LOG.info("开启分单分区的播种任务：{},找到集货位：{}", JSON.toJSONString(sowTaskDTO), JSON.toJSONString(toLocation));
                }
            }
        }
        return sowTaskDTO;

    }

    public Long setSowLocation(String batchTaskId) {
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);
        SowTaskDTO sowTaskDTO = setSowLocationByBatchTask(batchTaskPO);

        return sowTaskDTO.getLocationId();
    }

    /**
     * 获取播种任务的拣货任务数量
     *
     * @param sowTaskNo
     * @param orgId
     * @return
     */
    public List<SownOrderItemDTO> findSownOrderItems(String sowTaskNo, Integer orgId) {
        return sowOrderMapper.findSownOrderItems(sowTaskNo, orgId);
    }

    /**
     * 给播种任务分配货位并移库
     */
    public void startSowing(String sowTaskNo, Integer orgId, String sower) {
        SowTaskPO sowTaskPO = sowTaskMapper.findSowTaskByIdOrNo(orgId, null, sowTaskNo);

        if (sowTaskPO.getState() == SowTaskStateEnum.待播种.getType()) {
            // 已拣货的才可以开始播种
            List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findFinishTaskBySowTaskNo(sowTaskNo, orgId);
            if (CollectionUtils.isEmpty(batchTaskPOS)) {
                throw new BusinessValidateException("请先完成该播种任务下的拣货任务");
            }

            // 查询可分配容器
            // 查询已被占用的容器id
            List<Long> usedLocationIds = sowTaskMapper.findUsedContainerLocationIds(orgId, sowTaskPO.getWarehouseId());
            // 查询该仓库下所有容器的货位，过滤已被占用的容器
            LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
            locationQueryDTO.setWarehouseId(sowTaskPO.getWarehouseId());
            locationQueryDTO.setCityId(orgId);
            locationQueryDTO.setSubcategory(CategoryEnum.CARGO_LOCATION.getValue().byteValue());
            locationQueryDTO.setLocSubcategory(LocationEnum.容器位.getType().byteValue());
            locationQueryDTO.setLocationId(sowTaskPO.getLocationId());
            List<LoactionDTO> locationList = iLocationService.findLocationListByIdAndCategory(locationQueryDTO);
            if (CollectionUtils.isNotEmpty(usedLocationIds)) {
                locationList = locationList.stream().filter(location -> !usedLocationIds.contains(location.getId()))
                    .collect(Collectors.toList());
            }

            if (CollectionUtils.isEmpty(locationList)) {
                LOG.info("已使用的容器货位id:{}", JSON.toJSONString(usedLocationIds));
                throw new BusinessValidateException("未找到空闲容器,请将未完成的播种任务处理完,空出容器");
            }

            // 查询播种订单信息
            List<SowOrderPO> sowOrderPOS = sowOrderMapper.listBySowTaskNo(sowTaskNo, orgId);
            if (locationList.size() < sowOrderPOS.size()) {
                LOG.info("空闲的容器货位:{}", JSON.toJSONString(locationList));
                throw new BusinessValidateException(
                    "空闲容器数量不够,该播种任务有" + sowOrderPOS.size() + "个自提点订单,空闲容器数量只有" + locationList.size() + "个");
            }

            // 分配容器
            for (int i = 0; i < sowOrderPOS.size(); i++) {
                SowOrderPO sowOrderPO = sowOrderPOS.get(i);
                LoactionDTO location = locationList.get(i);
                if (sowOrderPO.getLocationId() == null) {
                    sowOrderPO.setLocationId(location.getId());
                    sowOrderPO.setLocationName(location.getName());
                }
                sowOrderPO.setLastUpdateUser(sower);
            }

            // 修改播种订单信息
            LOG.info("容器货位分配信息:{}", JSON.toJSONString(sowOrderPOS));
            sowOrderMapper.batchUpdateLocation(sowOrderPOS, orgId, sower);
            // 将播种任务改为播种中
            SowTaskDTO sowTaskDTO = new SowTaskDTO();
            sowTaskDTO.setId(sowTaskPO.getId());
            sowTaskDTO.setSowTaskNo(sowTaskPO.getSowTaskNo());
            sowTaskDTO.setLastUpdateUser(sower);
            sowTaskDTO.setOrgId(sowTaskPO.getOrgId());
            sowTaskDTO.setState(SowTaskStateEnum.播种中.getType());
            updateSowTaskById(sowTaskDTO);

            // 移库
            pickupCompleteBySow(sowTaskPO, sowOrderPOS, sower);
        }

    }

    /**
     * 根据操作方式移库到容器货位
     */
    private void pickupCompleteBySow(SowTaskPO sowTaskPO, List<SowOrderPO> sowOrderPOS, String createUser) {
        List<PickUpDTO> pickUpDTOList = new ArrayList<>();
        String sowTaskNo = sowTaskPO.getSowTaskNo();

        // 查订单项
        List<OutStockOrderItemPO> outStockOrderItemPOS =
            outStockOrderItemMapper.listItemBySowTaskNo(sowTaskPO.getSowTaskNo(), sowTaskPO.getOrgId());
        // 判断操作方式移库
        if (OperationModeEnum.拣货播种.getType() == sowTaskPO.getOperationMode()) {
            // 集货位移到容器货位，给订单项设置集货位id
            outStockOrderItemPOS.forEach(item -> item.setLocationId(sowTaskPO.getLocationId()));
        } else if (OperationModeEnum.不拣货播种.getType() == sowTaskPO.getOperationMode()) {
            // 分拣位移到容器货位，给订单项设置分拣位id
            List<BatchTaskItemDTO> batchTaskItemDTOList =
                batchTaskItemMapper.findBatchTaskItemDtoListBySowTaskNo(sowTaskNo);
            outStockOrderItemPOS.forEach(item -> {
                for (BatchTaskItemDTO taskItem : batchTaskItemDTOList) {
                    if (item.getSkuid().equals(taskItem.getSkuId())) {
                        item.setLocationId(taskItem.getLocationId());
                        break;
                    }
                }
            });
        }

        // 组装移库数据
        sowOrderPOS.forEach(sowOrder -> {
            List<OutStockOrderItemPO> itemPOS = outStockOrderItemPOS.stream()
                .filter(item -> sowOrder.getId().equals(item.getSowOrderId())).collect(Collectors.toList());
            itemPOS.forEach(item -> {
                PickUpDTO pickUpDTO = new PickUpDTO();
                if (item.getLocationId() == null) {
                    LOG.info("该拣货任务存在分拣位为空的数据,拣货任务编号:{}", item.getBatchtaskno());
                }
                pickUpDTO.setFromLocationId(item.getLocationId());
                pickUpDTO.setLocationId(sowOrder.getLocationId());
                pickUpDTO.setProductSkuId(item.getSkuid());
                pickUpDTO.setWarehouseId(sowOrder.getWarehouseId());
                pickUpDTO.setCount(item.getUnittotalcount());
                pickUpDTO.setFromChannel(item.getChannel().intValue());
                pickUpDTO.setFromSource(item.getSource().intValue());
                pickUpDTO.setToChannel(item.getChannel().intValue());
                pickUpDTO.setToSource(item.getSource().intValue());

                pickUpDTOList.add(pickUpDTO);
            });
        });

        LOG.info("播种移库入参{}", JSON.toJSONString(pickUpDTOList));
        PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
        pickUpChangeRecordDTO.setCityId(sowTaskPO.getOrgId());
        pickUpChangeRecordDTO.setOrderNo(sowTaskNo);
        pickUpChangeRecordDTO.setOrderId(sowTaskPO.getId().toString());
        pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.播种任务完成.name());
        pickUpChangeRecordDTO.setCreateUser(createUser);
        iBatchInventoryManageService.pickupCompleteBySku(pickUpDTOList, pickUpChangeRecordDTO);
    }

    /**
     * 指定播种人
     */
    @Transactional(rollbackFor = Exception.class)
    public void assignmentSower(UpdateSowerDTO updateSowerDTO) {
        sowTaskMapper.assignmentSower(updateSowerDTO);
        List<SowTaskItemPO> sowTaskItemPOS =
            sowTaskItemMapper.findBySowTaskNos(updateSowerDTO.getOrgId(), updateSowerDTO.getSowTaskNos());
        if (CollectionUtils.isEmpty(sowTaskItemPOS)) {
            return;
        }
        if (Objects.isNull(updateSowerDTO.getSorterId()) && Objects.isNull(updateSowerDTO.getSowerId())) {
            return;
        }
        final Integer sowerId =
            Objects.nonNull(updateSowerDTO.getSowerId()) ? updateSowerDTO.getSowerId() : updateSowerDTO.getSorterId();
        final String sowerName = StringUtils.isNotEmpty(updateSowerDTO.getSower()) ? updateSowerDTO.getSower()
            : updateSowerDTO.getSorterName();
        sowTaskItemPOS.forEach(item -> {
            SowTaskItemPO record = new SowTaskItemPO();
            record.setId(item.getId());
            record.setSorterId(sowerId);
            record.setSorterName(sowerName);
            sowTaskItemMapper.updateByPrimaryKeySelective(record);
        });

    }

    /**
     * 播种包装数据判断
     */
    public String validateSowPackageItems(List<PackageOrderItemDTO> packageOrderItemList, String sowTaskNo,
        Integer orgId, String operator) {
        String msg = null;

        // 根据播种任务编号查询关联订单详情信息
        List<OutStockOrderItemPO> outStockOrderItemPOS = outStockOrderItemMapper.listItemBySowTaskNo(sowTaskNo, orgId);

        if (CollectionUtils.isNotEmpty(outStockOrderItemPOS) && CollectionUtils.isNotEmpty(packageOrderItemList)) {

            Map<Long, List<PackageOrderItemDTO>> packageOrderItemMap =
                packageOrderItemList.stream().collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderItemId));

            // 关联订单详情信息与包装信息比对
            for (OutStockOrderItemPO item : outStockOrderItemPOS) {
                List<PackageOrderItemDTO> overPackageOrderItems = packageOrderItemMap.get(item.getId());

                // 如果存在未完成包装的订单项，直接返回
                if (CollectionUtils.isNotEmpty(overPackageOrderItems)) {
                    BigDecimal overPackageCount = overPackageOrderItems.stream()
                        .map(PackageOrderItemDTO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal surplusCount = item.getUnittotalcount().subtract(overPackageCount);
                    if (surplusCount.compareTo(BigDecimal.ZERO) != 0) {
                        return msg;
                    }
                } else {
                    return msg;
                }
            }

            msg = "播种任务:" + sowTaskNo + "完成,可以封箱";
        }

        return msg;
    }

    private void checkSowPackage(String sowTaskNo, Integer orgId) {
        // 未装箱产品名称集合
        List<String> productNameList = new ArrayList<>();

        // 根据播种任务编号查询关联订单详情信息
        List<OutStockOrderItemPO> outStockOrderItemPOS = outStockOrderItemMapper.listItemBySowTaskNo(sowTaskNo, orgId);
        List<Long> itemIds = outStockOrderItemPOS.stream().map(OutStockOrderItemPO::getId).collect(Collectors.toList());

        List<PackageOrderItemDTO> packageOrderItemList =
            packageOrderItemBL.listPackageOrderItemByOrderItemId(itemIds, orgId);

        if (CollectionUtils.isNotEmpty(outStockOrderItemPOS) && CollectionUtils.isNotEmpty(packageOrderItemList)) {

            Map<Long, List<PackageOrderItemDTO>> packageOrderItemMap =
                packageOrderItemList.stream().collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderItemId));

            // 关联订单详情信息与包装信息比对
            for (OutStockOrderItemPO item : outStockOrderItemPOS) {
                List<PackageOrderItemDTO> overPackageOrderItems = packageOrderItemMap.get(item.getId());

                // 如果存在未完成包装的订单项，直接返回
                if (CollectionUtils.isNotEmpty(overPackageOrderItems)) {
                    BigDecimal overPackageCount = overPackageOrderItems.stream()
                        .map(PackageOrderItemDTO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal surplusCount = item.getUnittotalcount().subtract(overPackageCount);
                    if (surplusCount.compareTo(BigDecimal.ZERO) != 0) {
                        productNameList.add(item.getProductname());
                    }
                } else {
                    productNameList.add(item.getProductname());
                }
            }
        } else if (CollectionUtils.isNotEmpty(outStockOrderItemPOS) && CollectionUtils.isEmpty(packageOrderItemList)) {
            productNameList = outStockOrderItemPOS.stream().map(OutStockOrderItemPO::getProductname).distinct()
                .collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(productNameList)) {
            String productNames = productNameList.stream().distinct().collect(Collectors.joining(","));
            throw new BusinessValidateException("请先完成该播种任务下的装箱任务,未装箱的产品有:" + productNames);
        }
    }

    /**
     * 根据播种编号分摊包装数据,校验数据
     */
    public List<PackageOrderItemDTO> sharePackageItemsBySowTaskNo(PackageOrderSaveDTO packageOrderSaveDTO) {
        List<PackageOrderItemDTO> packageOrderItems = new ArrayList<>();
        List<String> productNameList = new ArrayList<>();

        Integer orgId = packageOrderSaveDTO.getOrgId();
        Integer warehouseId = packageOrderSaveDTO.getWarehouseId();
        String operator = packageOrderSaveDTO.getOperator();
        Map<Long, List<PackageOrderItemDTO>> packageOrderItemMap = packageOrderSaveDTO.getPackageOrderItemList()
            .stream().collect(Collectors.groupingBy(PackageOrderItemDTO::getSkuId));
        // 根据播种任务编号查询关联订单详情信息
        SowProductItemQueryDTO sowProductItemQueryDTO = new SowProductItemQueryDTO();
        sowProductItemQueryDTO.setOrgId(orgId);
        sowProductItemQueryDTO.setLocationName(packageOrderSaveDTO.getLocationName());
        sowProductItemQueryDTO.setState(packageOrderSaveDTO.getState());
        List<SowProductItemDTO> sowProductItemDTOS = sowOrderMapper.listSowProductItem(sowProductItemQueryDTO);

        // 筛选出此次包装的原订单信息
        List<Long> itemIds =
            sowProductItemDTOS.stream().map(SowProductItemDTO::getOutStockOrderItemId).collect(Collectors.toList());
        sowProductItemDTOS = sowProductItemDTOS.stream()
            .filter(item -> packageOrderItemMap.containsKey(item.getSkuId())).collect(Collectors.toList());

        // 根据订单项id查询已包装数据
        List<PackageOrderItemDTO> overPackageOrderItems =
            packageOrderItemBL.listPackageOrderItemByOrderItemId(itemIds, orgId);

        if (CollectionUtils.isNotEmpty(overPackageOrderItems)) {
            // 加入原包装数据
            packageOrderItems.addAll(overPackageOrderItems);

            // 扣减已包装数量
            Map<Long, List<SowProductItemDTO>> sowProductItemMap =
                sowProductItemDTOS.stream().collect(Collectors.groupingBy(SowProductItemDTO::getOutStockOrderItemId));
            overPackageOrderItems.stream().filter(item -> sowProductItemMap.containsKey(item.getRefOrderItemId()))
                .forEach(item -> {
                    SowProductItemDTO sowProductItem = sowProductItemMap.get(item.getRefOrderItemId()).get(0);
                    sowProductItem
                        .setUnitTotalCount(sowProductItem.getUnitTotalCount().subtract(item.getUnitTotalCount()));
                });
        }

        // 组装新数据
        Map<Long, List<SowProductItemDTO>> sowProductMap =
            sowProductItemDTOS.stream().filter(item -> item.getUnitTotalCount().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.groupingBy(SowProductItemDTO::getSkuId));

        for (Map.Entry<Long, List<PackageOrderItemDTO>> entry : packageOrderItemMap.entrySet()) {
            Long skuId = entry.getKey();
            PackageOrderItemDTO packageOrderItem = entry.getValue().get(0);
            List<SowProductItemDTO> sowProductItems = sowProductMap.get(skuId);

            String productName = packageOrderItem.getProductName();
            if (CollectionUtils.isEmpty(sowProductItems)) {
                productNameList.add(productName);
                break;
            }

            BigDecimal packageItemCount = packageOrderItem.getUnitTotalCount();
            for (SowProductItemDTO sowProductItem : sowProductItems) {
                PackageOrderItemDTO item = new PackageOrderItemDTO();
                item.setRefOrderId(sowProductItem.getOutStockOrderId());
                item.setRefOrderItemId(sowProductItem.getOutStockOrderItemId());
                item.setBoxCode(packageOrderItem.getBoxCode());
                item.setBoxCodeNo(packageOrderItem.getBoxCodeNo());
                item.setSkuId(skuId);
                item.setOrgId(orgId);
                item.setProductName(sowProductItem.getProductName());
                item.setSpecName(sowProductItem.getSpecName());
                item.setSpecQuantity(sowProductItem.getSpecQuantity());
                item.setWarehouseId(warehouseId);
                item.setCreateUser(operator);

                BigDecimal itemCount = sowProductItem.getUnitTotalCount();
                BigDecimal subtract = packageItemCount.subtract(itemCount);

                // 当前包装数量小于等于订单项数量时，只需创建一条包装信息
                // 当前包装数量大于订单项数量时，需要依次扣减生成多条包装信息
                if (subtract.compareTo(BigDecimal.ZERO) <= 0) {
                    item.setUnitTotalCount(packageItemCount);
                    BigDecimal[] counts = packageItemCount.divideAndRemainder(sowProductItem.getSpecQuantity());
                    item.setPackageCount(counts[0]);
                    item.setUnitCount(counts[1]);

                    packageOrderItems.add(item);
                    packageItemCount = BigDecimal.ZERO;
                    break;
                } else {
                    BigDecimal unitTotalcount = sowProductItem.getUnitTotalCount();
                    item.setUnitTotalCount(unitTotalcount);
                    BigDecimal[] counts = unitTotalcount.divideAndRemainder(sowProductItem.getSpecQuantity());
                    item.setPackageCount(counts[0]);
                    item.setUnitCount(counts[1]);

                    packageItemCount = packageItemCount.subtract(unitTotalcount);
                    packageOrderItems.add(item);
                }
            }

            if (packageItemCount.compareTo(BigDecimal.ZERO) > 0) {
                productNameList.add(productName);
            }
        }

        if (CollectionUtils.isNotEmpty(productNameList)) {
            String productNames = productNameList.stream().distinct().collect(Collectors.joining(","));
            throw new BusinessValidateException("装箱数量超出应装箱数量，请检查下列产品装箱数量:" + productNames);
        }

        return packageOrderItems;
    }

    public void delSowTask(List<String> sowTaskNos) {
        List<SowTaskPO> sowTaskPOS = sowTaskMapper.findSowTaskByTaskNos(null, sowTaskNos);
        if (CollectionUtils.isEmpty(sowTaskPOS)) {
            return;
        }
        List<String> batchNos =
            sowTaskPOS.stream().filter(sowTaskPO -> sowTaskPO.getState() != SowTaskStateEnum.已播种.getType())
                .map(SowTaskPO::getBatchNo).distinct().collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(batchNos)) {
            sowTaskPOS
                .stream().filter(sowTaskPO -> sowTaskPO.getState() != SowTaskStateEnum.已播种.getType()).collect(Collectors
                    .groupingBy(SowTaskPO::getOrgId, Collectors.mapping(SowTaskPO::getId, Collectors.toList())))
                .forEach((orgId, sowTaskIds) -> {
                    sowTaskMapper.deleteBySowTaskIds(sowTaskIds, orgId);
                    sowTaskItemMapper.deleteBySowTaskIds(sowTaskIds, orgId);
                    batchTaskMapper.deleteSowTaskBySowTaskIds(sowTaskIds);
                });

            // 播种任务订单关联表删除
            List<SowOrderPO> sowOrderPOS = sowOrderMapper.findByBatchNos(batchNos);
            if (CollectionUtils.isNotEmpty(sowOrderPOS)) {
                List<Long> sowOrderIds =
                    sowOrderPOS.stream().map(SowOrderPO::getId).distinct().collect(Collectors.toList());
                sowOrderMapper.deleteByIds(sowOrderIds, sowOrderPOS.get(0).getOrgId());
            }

            batchNos.forEach(batchNo -> {
                batchOrderBL.updateBatchStateByBatchNo(batchNo, "系统", null);
            });
        }
    }

    /**
     * 判断是否可以开始拣货
     *
     * @param batchTaskId
     */
    public void checkSowBatchTask(String batchTaskId) {
        // 查询拣货任务的播种任务信息
        SowTaskPO sowTaskPO = sowTaskMapper.getSowTaskByBatchTaskId(batchTaskId);
        if (sowTaskPO != null && sowTaskPO.getState() == SowTaskStateEnum.待播种.getType()) {
            checkSowLocation(sowTaskPO.getId(), sowTaskPO.getOrgId(), sowTaskPO.getWarehouseId(),
                sowTaskPO.getLocationId());
        }

    }

    /**
     * 修改播种任务的集货位
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSowLocation(SowTaskDTO sowTaskDTO) {
        // 根据集货位查找
        Integer warehouseId = sowTaskDTO.getWarehouseId();
        checkSowLocation(sowTaskDTO.getId(), sowTaskDTO.getOrgId(), warehouseId, sowTaskDTO.getLocationId());
        // 开启货位库存的出库需将已完成的拣货任务项的库存移到集货位
        if (warehouseConfigService.isOpenLocationStock(warehouseId)) {
            List<PickUpDTO> pickUpDTOList = new ArrayList<>();
            List<BatchTaskItemDTO> batchTaskItemDTOList = batchTaskItemMapper
                .findBatchTaskItemDtoListBySowTaskId(sowTaskDTO.getId(), TaskStateEnum.已完成.getType());
            SowTaskPO sowTaskPO = sowTaskMapper.selectByPrimaryKey(sowTaskDTO.getId());
            batchTaskItemDTOList.forEach(item -> {
                PickUpDTO pickUpDTO = new PickUpDTO();
                pickUpDTO.setProductSkuId(item.getSkuId());
                pickUpDTO.setWarehouseId(warehouseId);
                pickUpDTO.setFromChannel(item.getChannel().intValue());
                pickUpDTO.setFromSource(item.getSource().intValue());
                // 实际拣货数量
                pickUpDTO.setCount(item.getUnitTotalCount().subtract(item.getLackUnitCount()));
                pickUpDTO.setFromLocationId(sowTaskPO.getLocationId());
                pickUpDTO.setLocationId(sowTaskDTO.getLocationId());

                pickUpDTO.setToSource(item.getSource().intValue());
                pickUpDTO.setToChannel(item.getChannel().intValue());

                // 播种集货位更改移库，自动分配二级货主
                pickUpDTO.setAutoAllotFlag(true);
                if (pickUpDTO.getCount().compareTo(BigDecimal.ZERO) == 0) {
                    LOG.info("修改集货位，该拣货任务详情(id:{})的拣货数量为空！", item.getId());
                } else {
                    pickUpDTOList.add(pickUpDTO);
                }
            });
            if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
                LOG.info("修改集货位，移动库存参数:{}", JSON.toJSONString(pickUpDTOList));
                PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
                pickUpChangeRecordDTO.setCityId(sowTaskDTO.getOrgId());
                pickUpChangeRecordDTO.setOrderNo(sowTaskDTO.getSowTaskNo());
                pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.修改集货位.name());
                pickUpChangeRecordDTO.setCreateUser(sowTaskDTO.getLastUpdateUser());
                iBatchInventoryManageService.pickupCompleteBySku(pickUpDTOList, pickUpChangeRecordDTO);
            }
        }
        updateSowTaskById(sowTaskDTO);
    }

    /**
     * 检查集货位是否被占用
     */
    private void checkSowLocation(Long sowTaskId, Integer orgId, Integer warehouseId, Long locationId) {
        // 查询是否有相同集货位的拣货任务已经开始
        List<SowTaskPO> list = sowTaskMapper.findSowingByLocationId(orgId, warehouseId, locationId, sowTaskId);
        if (CollectionUtils.isNotEmpty(list)) {
            throw new BusinessValidateException("当前集货位" + list.get(0).getLocationName() + "还有未播种完成任务，请等待播种任务"
                + list.get(0).getSowTaskNo() + "播种完成后再开始该拣货任务");
        }
    }

    /**
     * 取消播种任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelSowTaskByOrder(List<SowTaskCancelDTO> sowTaskCancelDTOS) {
        LOG.info("取消播种任务,参数:{}", JSON.toJSONString(sowTaskCancelDTOS));
        if (CollectionUtils.isEmpty(sowTaskCancelDTOS)) {
            return;
        }
        // 负数是往小了缺货（买10件缺8件，这里就是-8），整数是还原订单
        List<SowTaskCancelItemDTO> sowTaskCancelItemDTOS =
            sowTaskCancelDTOS.stream().flatMap(order -> order.getSowTaskCancelItemDTOS().stream())
                .filter(item -> item.getSowTaskId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sowTaskCancelItemDTOS)) {
            return;
        }
        int warehouseId = sowTaskCancelDTOS.get(0).getWarehouseId();
        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(warehouseId);
        Boolean isRobotPicking = warehouseConfigService.checkWarehouseIsRobotPicking(warehouseId);

        List<RedisLock> redisLocks = new ArrayList<>();
        try {
            List<Long> sowTaskIds = sowTaskCancelItemDTOS.stream().map(SowTaskCancelItemDTO::getSowTaskId).distinct()
                .collect(Collectors.toList());

            // 手动加锁
            redisLocks = setIdWaitLock(sowTaskIds, RedisKeyConstant.SOWTASK, 3000);

            List<SowTaskPO> sowTaskPOS = sowTaskMapper.findAllSowTaskByIds(null, sowTaskIds);
            if (CollectionUtils.isEmpty(sowTaskPOS)) {
                LOG.warn("取消播种任务，播种任务不存在:{}", JSON.toJSONString(sowTaskCancelDTOS));
                return;
            }
            LOG.info("取消播种任务,播种任务数据:{}", JSON.toJSONString(sowTaskPOS));

            List<OutStockOrderChangeDTO> outStockOrderChangeDTOS =
                SowConverter.sowTaskCancelDTOS2OutStockOrderChangeDTOS(sowTaskCancelDTOS);

            List<Long> orderIds =
                sowTaskCancelDTOS.stream().map(SowTaskCancelDTO::getOrderId).collect(Collectors.toList());
            List<OutStockOrderItemPO> outStockOrderItemPOS =
                outStockOrderItemMapper.findByOutstockorderIdList(orderIds);
            LOG.info("取消播种任务,订单明细数据:{}", JSON.toJSONString(outStockOrderItemPOS));

            Map<Long, List<SowTaskCancelItemDTO>> sowTaskCancelItemMap =
                sowTaskCancelItemDTOS.stream().collect(Collectors.groupingBy(SowTaskCancelItemDTO::getSowTaskId));
            sowTaskPOS.sort(Comparator.comparing(SowTaskPO::getId));
            sowTaskPOS.forEach(sowTask -> {
                List<SowTaskUpdateDTO> sowTaskUpdateDTOS = new ArrayList<>();
                List<SowTaskItemUpdateDTO> sowTaskItemUpdateDTOS = new ArrayList<>();
                List<Long> cancelSowTaskItemIds = new ArrayList<>();
                List<Long> cancelSowTaskIds = new ArrayList<>();

                Long sowTaskId = sowTask.getId();
                List<SowTaskCancelItemDTO> sowTaskCancelItemList = sowTaskCancelItemMap.get(sowTaskId);
                List<OutStockOrderItemPO> sowOutStockOrderItemList = outStockOrderItemPOS.stream()
                    .filter(item -> Objects.equals(item.getSowTaskId(), sowTaskId)).collect(Collectors.toList());
                List<SowTaskItemPO> sowTaskItemPOS = sowTask.getSowTaskItemPOS();
                if (CollectionUtils.isNotEmpty(sowTaskItemPOS) && CollectionUtils.isNotEmpty(sowTaskCancelItemList)
                    && CollectionUtils.isNotEmpty(sowOutStockOrderItemList)) {
                    Map<Long, SowTaskItemPO> sowTaskItemMap = sowTaskItemPOS.stream().collect(Collectors
                        .toMap(SowTaskItemPO::getId, Function.identity(), (key1, key2) -> key2 != null ? key2 : key1));
                    Set<Long> sowTaskItemIds = sowTaskItemMap.keySet();
                    sowTaskCancelItemList.stream().filter(item -> sowTaskItemIds.contains(item.getSowTaskItemId()))
                        .collect(Collectors.groupingBy(SowTaskCancelItemDTO::getSowTaskItemId))
                        .forEach((sowTaskItemId, cancelItems) -> {
                            SowTaskItemPO sowTaskItemPO = sowTaskItemMap.get(sowTaskItemId);
                            BigDecimal changeUnitTotalCount = cancelItems.stream()
                                .map(SowTaskCancelItemDTO::getChangeCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal changePackageCount =
                                cancelItems.stream().map(SowTaskCancelItemDTO::getChangePackageCount)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal changeUnitCount = cancelItems.stream()
                                .map(SowTaskCancelItemDTO::getChangeUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal unitTotalCount = sowTaskItemPO.getUnitTotalCount().add(changeUnitTotalCount);
                            // 处理机器人的整件拆零。整件不进播种，
                            unitTotalCount = getSowItemUnitTotalCount(unitTotalCount, warehouseConfigDTO,
                                isRobotPicking, sowOutStockOrderItemList);

                            BigDecimal[] count = unitTotalCount.divideAndRemainder(sowTaskItemPO.getSpecQuantity());
                            sowTaskItemPO.setPackageCount(count[0]);
                            sowTaskItemPO.setUnitCount(count[1]);
                            sowTaskItemPO.setUnitTotalCount(unitTotalCount);
                            if (unitTotalCount.compareTo(BigDecimal.ZERO) == 0
                                && sowTaskItemPO.getState() == SowTaskStateEnum.待播种.getType()) {
                                cancelSowTaskItemIds.add(sowTaskItemId);
                            } else {
                                SowTaskItemUpdateDTO sowTaskItemUpdateDTO = new SowTaskItemUpdateDTO();
                                sowTaskItemUpdateDTO.setSowTaskItemId(sowTaskItemId);
                                sowTaskItemUpdateDTO.setPackageCount(sowTaskItemPO.getPackageCount());
                                sowTaskItemUpdateDTO.setUnitCount(sowTaskItemPO.getUnitCount());
                                sowTaskItemUpdateDTO.setUnitTotalCount(unitTotalCount);
                                if (unitTotalCount.compareTo(BigDecimal.ZERO) == 0) {
                                    sowTaskItemUpdateDTO.setState(SowTaskStateEnum.已播种.getType());
                                }

                                sowTaskItemUpdateDTOS.add(sowTaskItemUpdateDTO);
                            }
                        });

                    List<Long> unseededSowTaskItemIds =
                        sowTaskItemPOS.stream().filter(item -> item.getState() == SowTaskStateEnum.待播种.getType())
                            .map(SowTaskItemPO::getId).collect(Collectors.toList());
                    List<Long> orderItemIds = sowTaskCancelItemList.stream()
                        .filter(item -> unseededSowTaskItemIds.contains(item.getSowTaskItemId()))
                        .map(SowTaskCancelItemDTO::getOrderItemId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(orderItemIds)) {
                        outStockOrderChangeDTOS.stream().flatMap(order -> order.getItemList().stream())
                            .filter(item -> orderItemIds.contains(item.getId())).forEach(item -> {
                                item.setSowLocationId(sowTask.getLocationId());
                                item.setSowLocationName(sowTask.getLocationName());
                            });
                    }

                    sowTaskItemPOS = sowTaskItemPOS.stream()
                        .filter(item -> !cancelSowTaskItemIds.contains(item.getId())).collect(Collectors.toList());
                    List<Long> lstSowTaskItemIds =
                        sowTaskItemPOS.stream().map(SowTaskItemPO::getId).collect(Collectors.toList());
                    List<SowTaskItemPO> unseededSowTaskItems =
                        sowTaskItemPOS.stream().filter(item -> item.getState() == SowTaskStateEnum.待播种.getType()
                            || item.getState() == SowTaskStateEnum.播种中.getType()).collect(Collectors.toList());
                    List<SowTaskItemPO> seededSowTaskItems = sowTaskItemPOS.stream()
                        .filter(item -> item.getState() == SowTaskStateEnum.已播种.getType()).collect(Collectors.toList());

                    SowTaskUpdateDTO sowTaskUpdateDTO = new SowTaskUpdateDTO();
                    long orderCount = sowTask.getOrderCount();
                    long count = sowOutStockOrderItemList.stream()
                        .filter(item -> lstSowTaskItemIds.contains(item.getSowTaskItemId()))
                        .map(OutStockOrderItemPO::getOutstockorderId).distinct().count();
                    if (count == 0) {
                        orderCount = orderCount - 1;
                    }
                    long skuCount = sowTaskItemPOS.stream().map(SowTaskItemPO::getProductSkuId).distinct().count();
                    BigDecimal packageCount = sowTaskItemPOS.stream().map(SowTaskItemPO::getPackageCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal unitCount = sowTaskItemPOS.stream().map(SowTaskItemPO::getUnitCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    sowTaskUpdateDTO.setSowTaskId(sowTaskId);
                    sowTaskUpdateDTO.setOrderCount((int)orderCount);
                    sowTaskUpdateDTO.setSkuCount((int)skuCount);
                    sowTaskUpdateDTO.setPackageAmount(packageCount);
                    sowTaskUpdateDTO.setUnitAmount(unitCount);
                    if (CollectionUtils.isEmpty(unseededSowTaskItems)
                        && CollectionUtils.isNotEmpty(seededSowTaskItems)) {
                        sowTaskUpdateDTO.setState(SowTaskStateEnum.已播种.getType());
                        sowTaskUpdateDTOS.add(sowTaskUpdateDTO);
                    } else if (CollectionUtils.isEmpty(sowTaskItemPOS)) {
                        cancelSowTaskIds.add(sowTaskId);
                    } else {
                        sowTaskUpdateDTOS.add(sowTaskUpdateDTO);
                    }
                } else {
                    // 过渡兼容旧数据
                    buildCancelSowTask(sowTask, sowTaskCancelItemList, sowOutStockOrderItemList,
                        outStockOrderChangeDTOS, sowTaskUpdateDTOS, cancelSowTaskIds);
                }

                // 分批处理数据
                processCancelSowTakeData(sowTaskId, cancelSowTaskIds, cancelSowTaskItemIds, sowTaskUpdateDTOS,
                    sowTaskItemUpdateDTOS, sowTask.getOrgId());
            });

            // 更新拣货任务数据
            if (CollectionUtils.isNotEmpty(outStockOrderChangeDTOS)) {
                LOG.info("取消播种任务,修改拣货任务数据:{}", JSON.toJSONString(outStockOrderChangeDTOS));
                batchOrderBL.updateBatchByOrderChange(outStockOrderChangeDTOS);
            }
        } catch (Exception e) {
            LOG.error("取消播种任务异常：{}", JSON.toJSONString(sowTaskCancelDTOS), e);
        } finally {
            afterProcessUnlock(redisLocks);
        }

    }

    private BigDecimal getSowItemUnitTotalCount(BigDecimal unitTotalCount, WarehouseConfigDTO warehouseConfigDTO,
        Boolean isRobotPicking, List<OutStockOrderItemPO> sowOutStockOrderItemList) {
        try {
            if (BooleanUtils.isFalse(isRobotPicking)) {
                return unitTotalCount;
            }
            if (!WavesStrategyConstants.PACKAGEREVIEWTYPE_OPEN.equals(warehouseConfigDTO.getRobotLargePick())) {
                return unitTotalCount;
            }

            if (!WarehouseConfigConstants.SOW_CONTROL_TYPE_LIQUID_NOT_PACK
                .equals(warehouseConfigDTO.getSowControlType())) {
                return unitTotalCount;
            }

            if (unitTotalCount.compareTo(BigDecimal.ZERO) >= 0) {
                return unitTotalCount;
            }

            return sowOutStockOrderItemList.stream().map(OutStockOrderItemPO::getUnittotalcount).reduce(BigDecimal.ZERO,
                BigDecimal::add);
        } catch (Exception e) {
            LOG.warn("计算错误", e);
        }

        return unitTotalCount;
    }

    /**
     * 手动释放锁
     */
    private void afterProcessUnlock(List<RedisLock> redisLocks) {
        if (CollectionUtils.isEmpty(redisLocks)) {
            return;
        }
        redisLocks.forEach(RedisLock::unlock);
    }

    /**
     * 手动加锁
     */
    public List<RedisLock> setIdWaitLock(List<Long> ids, String lockKey, long waitTime) {
        List<RedisLock> redisLocks = new ArrayList<>();
        ids.forEach(id -> {
            RedisLock lock = new RedisLock(redisTemplate, lockKey + id);
            try {
                if (lock.lock()) {
                    LOG.info("加锁成功：{}", id);
                    redisLocks.add(lock);
                } else {
                    Thread.sleep(waitTime);
                    if (lock.lock()) {
                        redisLocks.add(lock);
                    } else {
                        LOG.info("加锁失败：{}", id);
                    }
                }
            } catch (Exception e) {
                throw new BusinessException("加锁异常", e);
            }
        });

        return redisLocks;
    }

    private void processCancelSowTakeData(Long sowTaskId, List<Long> cancelSowTaskIds, List<Long> cancelSowTaskItemIds,
        List<SowTaskUpdateDTO> sowTaskUpdateDTOS, List<SowTaskItemUpdateDTO> sowTaskItemUpdateDTOS, Integer orgId) {

        // 删除播种任务
        if (CollectionUtils.isNotEmpty(cancelSowTaskIds)) {
            LOG.info("取消播种任务,删除播种任务数据:{}", cancelSowTaskIds);
            sowTaskMapper.deleteBySowTaskIds(cancelSowTaskIds, orgId);
        }

        // 删除播种任务明细
        if (CollectionUtils.isNotEmpty(cancelSowTaskItemIds)) {
            LOG.info("取消播种任务,删除播种任务明细数据:{}", JSON.toJSONString(cancelSowTaskItemIds));
            sowTaskItemMapper.deleteByIds(cancelSowTaskItemIds, orgId);
        }

        // 更新播种任务
        if (CollectionUtils.isNotEmpty(sowTaskUpdateDTOS)) {
            LOG.info("取消播种任务,修改播种任务数据:{}", JSON.toJSONString(sowTaskUpdateDTOS));
            sowTaskMapper.batchUpdateCount(sowTaskUpdateDTOS);
        }

        // 更新播种任务明细
        if (CollectionUtils.isNotEmpty(sowTaskItemUpdateDTOS)) {
            LOG.info("取消播种任务,修改播种任务明细数据:{}", JSON.toJSONString(sowTaskItemUpdateDTOS));
            sowTaskItemMapper.batchUpdateItem(sowTaskItemUpdateDTOS);
        }
    }

    /**
     * 按订单组装播种取消数据
     */
    private void buildCancelSowTask(SowTaskPO sowTask, List<SowTaskCancelItemDTO> sowTaskCancelItemList,
        List<OutStockOrderItemPO> sowOutStockOrderItemList, List<OutStockOrderChangeDTO> outStockOrderChangeDTOS,
        List<SowTaskUpdateDTO> sowTaskUpdateDTOS, List<Long> cancelSowTaskIds) {
        Long sowTaskId = sowTask.getId();
        if (CollectionUtils.isNotEmpty(sowTaskCancelItemList) && CollectionUtils.isNotEmpty(sowOutStockOrderItemList)) {
            // 计算当前播种任务变更后的大小数量
            BigDecimal changPackageCount = sowTaskCancelItemList.stream()
                .map(SowTaskCancelItemDTO::getChangePackageCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal changUintCount = sowTaskCancelItemList.stream().map(SowTaskCancelItemDTO::getChangeUnitCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal overPackageCount = sowTask.getPackageAmount().subtract(changPackageCount);
            BigDecimal overUnitCount = sowTask.getUnitAmount().subtract(changUintCount);

            if (overPackageCount.equals(BigDecimal.ZERO) && overUnitCount.equals(BigDecimal.ZERO)
                && sowTask.getState().equals(SowTaskStateEnum.待播种.getType())) {
                // 变更后的数量为0时且播种任务状态为待播种的任务需要直接删除掉
                cancelSowTaskIds.add(sowTaskId);
            } else {
                // 变更后的数量为0的订单项认为是需要删除的订单项,计算产品种类数时需剔除掉
                List<Long> cancelSkuIds =
                    sowTaskCancelItemList.stream().filter(item -> item.getOverUnitTotalCount().equals(BigDecimal.ZERO))
                        .map(SowTaskCancelItemDTO::getSkuId).distinct().collect(Collectors.toList());
                long skuCount =
                    sowOutStockOrderItemList.stream().filter(item -> !cancelSkuIds.contains(item.getSkuid()))
                        .map(OutStockOrderItemPO::getSkuid).distinct().count();

                // 计算变更后的订单数量
                long orderCount =
                    sowOutStockOrderItemList.stream().filter(item -> !cancelSkuIds.contains(item.getSkuid()))
                        .map(OutStockOrderItemPO::getOutstockorderId).distinct().count();
                SowTaskUpdateDTO sowTaskUpdateDTO = new SowTaskUpdateDTO();
                sowTaskUpdateDTO.setOrderCount((int)orderCount);
                sowTaskUpdateDTO.setSowTaskId(sowTaskId);
                sowTaskUpdateDTO.setPackageAmount(overPackageCount);
                sowTaskUpdateDTO.setUnitAmount(overUnitCount);
                sowTaskUpdateDTO.setSkuCount((int)skuCount);
                sowTaskUpdateDTO.setState(sowTask.getState());

                sowTaskUpdateDTOS.add(sowTaskUpdateDTO);
            }
        }

        // 更新拣货数据时，待播种的任务需要告知源货位信息
        List<Long> orderItemIds =
            sowTaskCancelItemList.stream().map(SowTaskCancelItemDTO::getOrderItemId).collect(Collectors.toList());
        outStockOrderChangeDTOS.stream().flatMap(order -> order.getItemList().stream()).forEach(changItem -> {
            if (orderItemIds.contains(changItem.getId())) {
                changItem.setSowTaskId(sowTaskId);
                changItem.setSowTaskNo(sowTask.getSowTaskNo());
                if (sowTask.getState().equals(SowTaskStateEnum.待播种.getType())) {
                    changItem.setSowLocationId(sowTask.getLocationId());
                    changItem.setSowLocationName(sowTask.getLocationName());
                }
            }
        });
    }

    /**
     * 播种明细完成
     */
    @MarkMethodInvokeFlag(key = RedisConstant.SUP_F + "UserOnlineCache", warehouseId = "#sowOrderSaveDTO.warehouseId",
        userId = "#sowOrderSaveDTO.operatorUserId")
    @Transactional(rollbackFor = Exception.class)
    @DistributeLock(conditions = "#sowOrderSaveDTO.batchNo", sleepMills = 30000,
        key = RedisConstant.SUP_F + "batchTaskComplete:", lockType = DistributeLock.LockType.WAITLOCK,
        expireMills = 30000)
    public void completeSowTaskItems(SowOrderSaveDTO sowOrderSaveDTO) {
        List<RedisLock> redisLocks = new ArrayList<>();
        try {
            final Long sowTaskId = sowOrderSaveDTO.getSowTaskId();
            final Byte sowTaskType = sowOrderSaveDTO.getSowTaskType();
            SowTaskPO sowTaskPO = sowTaskMapper.getAllById(sowTaskId);
            if (sowTaskPO == null) {
                throw new BusinessException("该播种任务不存在!");
            }
            if (sowTaskPO.getState() == SowTaskStateEnum.已播种.getType()
                && (sowTaskType == null || SowTaskTypeEnum.二次分拣播种.getType() != sowTaskType)) {
                throw new BusinessValidateException("该播种任务已完成!");
            }
            redisLocks = setIdWaitLock(Collections.singletonList(sowTaskId), RedisKeyConstant.SOWTASK, 3000);

            List<SowTaskItemDTO> sowTaskItemDTOS = sowOrderSaveDTO.getSowTaskItemDTOS();
            List<SowTaskItemPO> sowTaskItemPOS = sowTaskPO.getSowTaskItemPOS();
            // 清空 wcs 物料箱缓存
            notifyWCSBL.cleanCachedBoxInfo(sowTaskPO);
            // 兼容旧流程
            // 这里不会调用，可以不管
            if (CollectionUtils.isEmpty(sowTaskItemPOS)) {
                LOG.info("播种完成兼容旧流程:{}", JSON.toJSONString(sowOrderSaveDTO));
                saveSowOrder(sowOrderSaveDTO);
                return;
            }

            // 组装数据、如果播种项全部完成，播种任务未完成，这个参数为空
            List<SowTaskItemPO> updateSowTaskItemPOS =
                buildCompleteSowTaskItemPOS(sowTaskItemPOS, sowTaskItemDTOS, sowOrderSaveDTO.getOperatorUserName());

            // 完成播种写明细数据
            if (CollectionUtils.isNotEmpty(updateSowTaskItemPOS)) {
                updateSowTaskItemPOS.forEach(m -> {
                    m.setSorterId(sowOrderSaveDTO.getOperatorUserId());
                    m.setSorterName(sowOrderSaveDTO.getOperatorUserName());
                });
            }
            // 判断主单状态
            SowTaskDTO sowTaskDTO = new SowTaskDTO();
            sowTaskDTO.setSowTaskType(sowTaskType);
            sowTaskDTO.setId(sowTaskId);
            sowTaskDTO.setOrgId(sowTaskPO.getOrgId());
            sowTaskDTO.setSowTaskNo(sowTaskPO.getSowTaskNo());
            sowTaskDTO.setOperatorName(sowOrderSaveDTO.getOperatorUserName());
            sowTaskDTO.setOperatorId(sowOrderSaveDTO.getOperatorUserId());

            // 二次分拣状态特殊处理
            if (sowTaskType != null && sowTaskType.equals(SowTaskTypeEnum.二次分拣播种.getType())) {
                sowTaskDTO.setCompleteTime(sowOrderSaveDTO.getCompleteTime());
                sowTaskDTO.setState(sowOrderSaveDTO.getState());
            } else {
                List<SowTaskItemPO> undoneItems = sowTaskItemPOS.stream()
                    .filter(item -> item.getState() != SowTaskStateEnum.已播种.getType()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(undoneItems) && sowTaskPO.getState() != SowTaskStateEnum.已播种.getType()) {
                    sowTaskDTO.setCompleteTime(sowOrderSaveDTO.getCompleteTime());
                    sowTaskDTO.setState(SowTaskStateEnum.已播种.getType());
                }
            }

            // 修改播种任务信息
            if (sowTaskDTO.getState() != null) {
                updateSowTaskById(sowTaskDTO);
            }

            if (CollectionUtils.isNotEmpty(updateSowTaskItemPOS)) {
                // 修改播种任务明细
                sowTaskItemMapper.completeSowTaskItems(updateSowTaskItemPOS);
            }

            // 播种移库数据
            // 播种墙不播大件
            List<PickUpDTO> pickUpDTOList = processCompleteSowTaskItems(sowTaskItemDTOS, sowTaskPO.getLocationId(),
                sowOrderSaveDTO.getWarehouseId(), false);
            // 校验移库
            if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
                pickUpDTOList = batchInventoryTransferBL.checkInventoryTransferByPickUp(pickUpDTOList);
                // 修改拣货关联明细数据
                batchOrderTaskBL.rebuildOrderItemTaskInfoDetail(pickUpDTOList);
            }

            // 得到播种任务的播种数量信息
            List<SownOrderItemDTO> sownOrderItems = sowOrderSaveDTO.getSownOrderItems();
            if (CollectionUtils.isNotEmpty(sownOrderItems)) {
                // 修改播种订单关联信息表的播种数量信息
                if (sowTaskPO.getOperationMode() == null) {
                    sowOrderMapper.updateSownAmountByOrderId(sowOrderSaveDTO.getSowTaskNo(), sownOrderItems,
                        sowOrderSaveDTO.getOperatorUserName(), sowOrderSaveDTO.getOrgId());
                } else {
                    sowOrderMapper.updateSownAmount(sowOrderSaveDTO.getSowTaskNo(), sownOrderItems,
                        sowOrderSaveDTO.getOperatorUserName(), sowOrderSaveDTO.getOrgId());
                }
            }

            // 修改复核信息
            if (sowOrderSaveDTO.getBillReviewDTO() != null) {
                billReviewBL.updateBillReview(sowOrderSaveDTO.getBillReviewDTO());
                sowTaskDTO.setNeedLog(false);
            }

            // 保存包装信息
            List<PackageOrderItemDTO> packageOrderItems = sowOrderSaveDTO.getPackageOrderItems();
            if (CollectionUtils.isNotEmpty(packageOrderItems)) {
                packageOrderItemBL.savePackageBatch(packageOrderItems, false);
            }

            // 判断波次状态,修改波次状态
            String batchNo = sowTaskPO.getBatchNo();
            // boolean batchStateFinish = false;
            // if (sowTaskDTO.getState() != null && sowTaskDTO.getState() == SowTaskStateEnum.已播种.getType()) {
            // batchStateFinish =
            // batchOrderBL.isBatchStateFinish(batchNo, sowOrderSaveDTO.getOperatorUserName(), sowTaskType);
            // }

            // 修改订单出库位
            sowTaskItemDTOS.stream().filter(item -> item.getToLocationId() != null)
                .collect(Collectors.groupingBy(SowTaskItemDTO::getToLocationId)).forEach((toLocationId, items) -> {
                    List<Long> outStockOrderItemIds = items.stream().map(SowTaskItemDTO::getOutStockOrderItemId)
                        .distinct().collect(Collectors.toList());
                    outStockOrderItemMapper.updateOutStockOrderItemLocationByIds(sowTaskPO.getOrgId(), toLocationId,
                        items.get(0).getToLocationName(), outStockOrderItemIds);
                });
            if (sowTaskDTO.getState() != null && sowTaskDTO.getState() == SowTaskStateEnum.已播种.getType()) {
                try {
                    batchFinishedBL.completeWave(batchNo, sowOrderSaveDTO.getOperatorUserId(),
                        sowOrderSaveDTO.getOrgId());
                } catch (HasLockedException e) {
                    throw new BusinessValidateException("正在处理，请稍后再试！");
                }
            }

            taskPerformanceCalculateBL.calculateAndModTaskPerformance(
                TaskPerformanceCalculateBO.getSowTaskInstance(Collections.singletonList(sowTaskPO.getId().toString()),
                    sowOrderSaveDTO.getOperatorUserId(), sowTaskPO.getWarehouseId(), sowTaskPO.getOrgId(),
                    updateSowTaskPO -> sowTaskMapper.updateByPrimaryKeySelective(updateSowTaskPO)));

            // 移库
            LOG.info("播种移库入参{}，sowTaskNo：{}", new Gson().toJson(pickUpDTOList), sowOrderSaveDTO.getSowTaskNo());
            if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
                PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
                pickUpChangeRecordDTO.setCityId(sowOrderSaveDTO.getOrgId());
                pickUpChangeRecordDTO.setOrderNo(sowOrderSaveDTO.getSowTaskNo());
                pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.提交播种任务.name());
                pickUpChangeRecordDTO.setCreateUser(sowOrderSaveDTO.getOperatorUserName());
                iBatchInventoryManageService.pickupCompleteBySku(pickUpDTOList, pickUpChangeRecordDTO);
            }

            // 同步订单出库位
            // if (batchStateFinish) {
            // outStockOrderBL.processOrderLocation(batchNo);
            // }
            //
            // // 处理订单缺货
            // if (batchStateFinish) {
            // outStockOrderBL.processOrderLack(batchNo, sowOrderSaveDTO.getOperatorUserId());
            // }

        } finally {
            afterProcessUnlock(redisLocks);
        }
    }

    /**
     * 播种完成数据组装
     */
    private List<SowTaskItemPO> buildCompleteSowTaskItemPOS(List<SowTaskItemPO> sowTaskItemPOS,
        List<SowTaskItemDTO> sowTaskItemDTOS, String operatorUserName) {
        boolean allSowed = sowTaskItemPOS.stream().allMatch(m -> m.getState().equals(SowTaskStateEnum.已播种.getType()));
        if (CollectionUtils.isEmpty(sowTaskItemDTOS) && allSowed) {
            return Collections.emptyList();
        }
        List<SowTaskItemPO> updateSowTaskItemPOS = new ArrayList<>();
        Map<Long, List<SowTaskItemDTO>> sowTaskItemDTOMap =
            sowTaskItemDTOS.stream().collect(Collectors.groupingBy(SowTaskItemDTO::getId));
        for (SowTaskItemPO item : sowTaskItemPOS) {
            List<SowTaskItemDTO> sowTaskItems = sowTaskItemDTOMap.get(item.getId());
            if (CollectionUtils.isNotEmpty(sowTaskItems)) {
                SowTaskItemDTO sowTaskItem = sowTaskItems.get(0);
                SowTaskItemPO update = new SowTaskItemPO();
                update.setId(sowTaskItem.getId());
                update.setStartTime(sowTaskItem.getStartTime() == null ? new Date() : sowTaskItem.getStartTime());
                update.setCompleteTime(
                    sowTaskItem.getCompleteTime() == null ? new Date() : sowTaskItem.getCompleteTime());
                update.setState(SowTaskStateEnum.已播种.getType());
                BigDecimal overUnitTotalCount = sowTaskItems.stream().map(SowTaskItemDTO::getOverUnitTotalCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                update.setOverUnitTotalCount(overUnitTotalCount);
                if (update.getLastUpdateUser() == null) {
                    update.setLastUpdateUser(operatorUserName);
                }
                // todo 后续是否考虑根据数量判断？
                item.setState(SowTaskStateEnum.已播种.getType());

                updateSowTaskItemPOS.add(update);
            } else if (item.getState() == SowTaskStateEnum.待播种.getType()) {
                // todo 目前默认全部提交，后续分段提交时需去掉
                LOG.info("以下播种任务明细未提交：{}", JSON.toJSONString(item));

                // 订单缺货，导致播种任务部分完成情况兼容
                SowTaskItemPO update = new SowTaskItemPO();
                update.setId(item.getId());
                update.setStartTime(item.getStartTime() == null ? new Date() : item.getStartTime());
                update.setCompleteTime(item.getCompleteTime() == null ? new Date() : item.getCompleteTime());
                update.setState(SowTaskStateEnum.已播种.getType());
                update.setOverUnitTotalCount(item.getUnitTotalCount());
                if (update.getLastUpdateUser() == null) {
                    update.setLastUpdateUser(operatorUserName);
                }
                item.setState(SowTaskStateEnum.已播种.getType());
                updateSowTaskItemPOS.add(update);
            }
        }

        return updateSowTaskItemPOS;
    }

    /**
     * 组装播种移库项 播种容器位需求(待接入)
     */
    private List<PickUpDTO> processCompleteSowTaskItems(List<SowTaskItemDTO> sowTaskItemDTOS, Long locationId,
        Integer warehouseId, boolean isPDAOpreate) {
        if (CollectionUtils.isEmpty(sowTaskItemDTOS) && !warehouseConfigService.isOpenLocationStock(warehouseId)) {
            return null;
        }

        // 根据订单项查询实际拣货数量和货主关系
        List<Long> outStockOrderItemIds = sowTaskItemDTOS.stream().map(SowTaskItemDTO::getOutStockOrderItemId)
            .distinct().collect(Collectors.toList());
        Map<Long, List<OrderItemDetailAllotDTO>> orderItemDetailAllotMap =
            batchOrderTaskBL.getOrderItemDetailAllotMap(outStockOrderItemIds, warehouseId, isPDAOpreate);

        List<PickUpDTO> pickUpDTOS = new ArrayList<>();
        /** 如果移库位和出库位一样，则不移库 */
        sowTaskItemDTOS.stream().filter(item -> !Objects.equals(item.getToLocationId(), locationId)).forEach(item -> {
            // 补充二级货主
            List<OrderItemDetailAllotDTO> orderItemDetailAllotDTOS =
                orderItemDetailAllotMap.get(item.getOutStockOrderItemId());
            if (CollectionUtils.isNotEmpty(orderItemDetailAllotDTOS)) {
                orderItemDetailAllotDTOS.forEach(detail -> {
                    PickUpDTO pickUpDTO = new PickUpDTO();
                    pickUpDTO.setProductSkuId(item.getProductSkuId());
                    pickUpDTO.setWarehouseId(warehouseId);
                    pickUpDTO.setFromChannel(item.getChannel().intValue());
                    pickUpDTO.setFromSource(item.getSource().intValue());
                    pickUpDTO.setCount(detail.getUnitTotalCount());
                    pickUpDTO.setFromLocationId(locationId);
                    pickUpDTO.setLocationId(item.getToLocationId());
                    pickUpDTO.setSecOwnerId(detail.getSecOwnerId());
                    pickUpDTO.setBusinessId(item.getOutStockOrderItemId().toString());
                    // 是否自动分配库存
                    pickUpDTO.setAutoAllotFlag(true);
                    pickUpDTO.setToSource(item.getSource().intValue());
                    pickUpDTO.setToChannel(item.getChannel().intValue());
                    // 排除负库存
                    pickUpDTO.setExcludeNegativeFlag(true);
                    if (pickUpDTO.getCount().compareTo(BigDecimal.ZERO) == 0) {
                        LOG.info("播种任务明细关联的订单详情(id:{})的数量为空！", item.getOutStockOrderItemId());
                    } else {
                        pickUpDTOS.add(pickUpDTO);
                    }
                });
            } else {
                PickUpDTO pickUpDTO = new PickUpDTO();
                pickUpDTO.setProductSkuId(item.getProductSkuId());
                pickUpDTO.setWarehouseId(warehouseId);
                pickUpDTO.setFromChannel(item.getChannel().intValue());
                pickUpDTO.setFromSource(item.getSource().intValue());
                pickUpDTO.setCount(item.getOverUnitTotalCount());
                pickUpDTO.setFromLocationId(locationId);
                pickUpDTO.setLocationId(item.getToLocationId());
                pickUpDTO.setBusinessId(item.getOutStockOrderItemId().toString());
                // 是否自动分配库存
                pickUpDTO.setAutoAllotFlag(true);
                pickUpDTO.setToSource(item.getSource().intValue());
                pickUpDTO.setToChannel(item.getChannel().intValue());
                // 排除负库存
                pickUpDTO.setExcludeNegativeFlag(true);
                LOG.info("else pickUpDTO={}", JSON.toJSONString(pickUpDTO));
                if (pickUpDTO.getCount().compareTo(BigDecimal.ZERO) == 0) {
                    LOG.info("播种任务明细关联的订单详情(id:{})的数量为空！", item.getOutStockOrderItemId());
                } else {
                    pickUpDTOS.add(pickUpDTO);
                }
            }
        });
        return pickUpDTOS;
    }

    /**
     * 获取播种任务数据
     *
     * @param orgId
     * @param sowTaskNo
     * @return
     */
    public SowTaskDTO getAllBySowTaskNo(Integer orgId, String sowTaskNo) {
        SowTaskPO sowTaskPO = sowTaskMapper.getAllBySowTaskNo(orgId, sowTaskNo);
        return SowConverter.sowTaskPO2SowTaskDTO(sowTaskPO);
    }

    public SowTaskDTO getAllBySowTaskId(Integer orgId, Long id) {
        SowTaskPO sowTaskPO = sowTaskMapper.getSowTaskById(id, orgId);
        return SowConverter.sowTaskPO2SowTaskDTO(sowTaskPO);
    }

    /**
     * 修改播种任务订单出库位
     *
     * @param sowOrderUpdateDTO
     */
    public void updateSowOrderLocation(SowOrderUpdateDTO sowOrderUpdateDTO) {
        List<OutStockOrderItemDTO> outStockOrderItemDTOS = outStockOrderItemMapper
            .findSowOrderItemsByOrderNos(sowOrderUpdateDTO.getOrgId(), sowOrderUpdateDTO.getOrderItemIds());
        List<OutStockOrderItemDTO> seededOrderItems = outStockOrderItemDTOS.stream()
            .filter(
                item -> item.getToLocationId() != null && item.getSowTaskItemState() == SowTaskStateEnum.已播种.getType())
            .collect(Collectors.toList());

        // 修改订单出库位
        List<Long> outStockOrderItemIds =
            outStockOrderItemDTOS.stream().map(OutStockOrderItemDTO::getId).collect(Collectors.toList());

        // 校验数据
        List<OutStockOrderItemDTO> validateDTOS =
            outStockOrderItemMapper.findSowOrderItemsByOrderNos(sowOrderUpdateDTO.getOrgId(), outStockOrderItemIds);
        List<OutStockOrderItemDTO> notUpdateDTOS = validateDTOS.stream()
            .filter(item -> item.getToLocationId() != null
                || Objects.equals(item.getSowTaskItemState(), SowTaskStateEnum.已播种.getType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notUpdateDTOS)) {
            LOG.info("updateSowOrderLocationBySowTaskItemIds 修改播种任务订单出库位，已播种或已设置出库位数据：{}",
                JSON.toJSONString(notUpdateDTOS));
            throw new BusinessValidateException("存在已完成状态播种明细数据或已设置过出库位，无法设置");
        }

        // 仓库开启货位库存且已完成播种的需要进行移库
        if (CollectionUtils.isNotEmpty(seededOrderItems)
            && warehouseConfigService.isOpenLocationStock(sowOrderUpdateDTO.getWarehouseId())) {
            List<PickUpDTO> pickUpDTOS = new ArrayList<>();
            seededOrderItems.forEach(item -> {
                PickUpDTO pickUpDTO = new PickUpDTO();
                pickUpDTO.setProductSkuId(item.getSkuId());
                pickUpDTO.setWarehouseId(sowOrderUpdateDTO.getWarehouseId());
                pickUpDTO.setFromChannel(item.getChannel().intValue());
                pickUpDTO.setFromSource(item.getSource().intValue());
                pickUpDTO.setCount(item.getUnitTotalCount());
                pickUpDTO.setFromLocationId(item.getToLocationId());
                pickUpDTO.setLocationId(sowOrderUpdateDTO.getLocationId());

                pickUpDTO.setToSource(item.getSource().intValue());
                pickUpDTO.setToChannel(item.getChannel().intValue());
                if (pickUpDTO.getCount().compareTo(BigDecimal.ZERO) == 0) {
                    LOG.info("关联的订单详情(id:{})的数量为空！", item.getId());
                } else {
                    pickUpDTOS.add(pickUpDTO);
                }
            });

            if (CollectionUtils.isNotEmpty(pickUpDTOS)) {
                PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
                pickUpChangeRecordDTO.setCityId(sowOrderUpdateDTO.getOrgId());
                pickUpChangeRecordDTO.setOrderNo(sowOrderUpdateDTO.getSowTaskNo());
                pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.修改出库位.name());
                pickUpChangeRecordDTO.setCreateUser(sowOrderUpdateDTO.getOperatorName());
                iBatchInventoryManageService.pickupCompleteBySku(pickUpDTOS, pickUpChangeRecordDTO);
            }
        }

        outStockOrderItemMapper.updateOutStockOrderItemLocationByIds(sowOrderUpdateDTO.getOrgId(),
            sowOrderUpdateDTO.getLocationId(), sowOrderUpdateDTO.getLocationName(), outStockOrderItemIds);
    }

    /**
     * 添加出库位到播种任务明细项
     *
     * @param sowTaskItemDTOS
     */
    public List<SowTaskItemDTO> assignmentSowTaskItemLocation(Integer orgId, List<SowTaskItemDTO> sowTaskItemDTOS) {
        if (CollectionUtils.isEmpty(sowTaskItemDTOS)) {
            return null;
        }
        List<SowTaskItemDTO> lstSowTaskItemDTOS = new ArrayList<>();
        Map<Long, SowTaskItemDTO> sowTaskItemMap = sowTaskItemDTOS.stream().collect(
            Collectors.toMap(SowTaskItemDTO::getId, Function.identity(), (key1, key2) -> key2 != null ? key2 : key1));
        List<Long> sowTaskItemIds = new ArrayList<>(sowTaskItemMap.keySet());
        List<OutStockOrderItemDTO> outStockOrderItemDTOS =
            outStockOrderItemMapper.findBySowTaskItemIds(orgId, sowTaskItemIds);
        if (CollectionUtils.isEmpty(outStockOrderItemDTOS)) {
            return null;
        }
        List<OutStockOrderLocationDTO> outStockOrderLocationDTOS = new ArrayList<>();
        outStockOrderItemDTOS.stream().collect(Collectors.groupingBy(OutStockOrderItemDTO::getRefOrderNo))
            .forEach((orderNo, items) -> {
                OutStockOrderLocationDTO outStockOrderLocationDTO = new OutStockOrderLocationDTO();
                outStockOrderLocationDTO.setWarehouseId(items.get(0).getWarehouseId());
                outStockOrderLocationDTO.setOrderNo(orderNo);
                outStockOrderLocationDTO.setOmsOrderId(StreamUtils.isNum(items.get(0).getBusinessId())
                    ? Long.valueOf(items.get(0).getBusinessId()) : items.get(0).getOutstockorder_Id());

                outStockOrderLocationDTOS.add(outStockOrderLocationDTO);
            });
        List<OutStockOrderLocationDTO> outStockOrderLocation =
            outStockOrderBL.findOutStockOrderLocation(outStockOrderLocationDTOS);
        Map<String, OutStockOrderLocationDTO> outStockOrderLocationMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(outStockOrderLocation)) {
            outStockOrderLocationMap =
                outStockOrderLocation.stream().collect(Collectors.toMap(OutStockOrderLocationDTO::getOrderNo,
                    Function.identity(), (key1, key2) -> key2 != null ? key2 : key1));
        }

        List<String> noLocationOrderNos = new ArrayList<>();
        Map<String, OutStockOrderLocationDTO> finalOutStockOrderLocationMap = outStockOrderLocationMap;
        outStockOrderItemDTOS.forEach(item -> {
            OutStockOrderLocationDTO outStockOrderLocationDTO = finalOutStockOrderLocationMap.get(item.getRefOrderNo());
            if (outStockOrderLocationDTO != null) {
                SowTaskItemDTO sowTaskItem = sowTaskItemMap.get(item.getSowTaskItemId());
                if (sowTaskItem != null) {
                    SowTaskItemDTO sowTaskItemDTO = new SowTaskItemDTO();
                    BeanUtils.copyProperties(sowTaskItem, sowTaskItemDTO);
                    sowTaskItemDTO.setChannel(item.getChannel());
                    sowTaskItemDTO.setSource(item.getSource());
                    sowTaskItemDTO.setOverUnitTotalCount(item.getUnitTotalCount());
                    sowTaskItemDTO.setToLocationId(outStockOrderLocationDTO.getLocationId());
                    sowTaskItemDTO.setToLocationName(outStockOrderLocationDTO.getLocationName());
                    sowTaskItemDTO.setOutStockOrderItemId(item.getId());
                    if (sowTaskItemDTO.getStartTime() == null) {
                        sowTaskItemDTO.setStartTime(new Date());
                    }
                    if (sowTaskItemDTO.getCompleteTime() == null) {
                        sowTaskItemDTO.setCompleteTime(new Date());
                    }
                    lstSowTaskItemDTOS.add(sowTaskItemDTO);
                }
            } else {
                noLocationOrderNos.add(item.getRefOrderNo());
            }

        });
        if (CollectionUtils.isNotEmpty(noLocationOrderNos)) {
            String orderNos = noLocationOrderNos.stream().distinct().collect(Collectors.joining(","));
            throw new BusinessValidateException("请进入播种界面，给下列订单设置出库位:" + orderNos);
        }
        return lstSowTaskItemDTOS;
    }

    /**
     * 播种任务领取
     */
    public void receiveSowTask(SowTaskReceiveDTO sowTaskReceiveDTO) {
        List<SowTaskPO> sowTaskPOS = new ArrayList<>();
        if (StringUtils.isNotEmpty(sowTaskReceiveDTO.getLocationName())) {
            SowTaskPO sowTaskPO = sowTaskMapper.findCanSowingTaskByLocationName(sowTaskReceiveDTO.getLocationName(),
                sowTaskReceiveDTO.getWarehouseId(), sowTaskReceiveDTO.getOrgId());
            AssertUtils.notNull(sowTaskPO, "没有可领取的播种任务，请检查播种任务下的拣货任务项是否完成");
            sowTaskPOS.add(sowTaskPO);
        } else {
            sowTaskPOS =
                sowTaskMapper.findSowTaskByTaskNos(sowTaskReceiveDTO.getOrgId(), sowTaskReceiveDTO.getSowTaskNos());
        }
        if (CollectionUtils.isEmpty(sowTaskPOS)) {
            throw new BusinessValidateException("没有可领取的播种任务,请刷新界面重新操作");
        }
        String operatedTaskNos = sowTaskPOS.stream().filter(task -> task.getOperatorId() != null)
            .map(SowTaskPO::getSowTaskNo).collect(Collectors.joining(","));
        if (StringUtils.isNotEmpty(operatedTaskNos)) {
            throw new BusinessValidateException("以下播种任务已被领取，请刷新重新操作:" + operatedTaskNos);
        }
        List<String> sowTaskNos =
            sowTaskPOS.stream().map(SowTaskPO::getSowTaskNo).distinct().collect(Collectors.toList());
        UpdateSowerDTO updateSowerDTO = new UpdateSowerDTO();
        updateSowerDTO.setOrgId(sowTaskReceiveDTO.getOrgId());
        updateSowerDTO.setSowerId(sowTaskReceiveDTO.getSowerId());
        updateSowerDTO.setSower(sowTaskReceiveDTO.getSower());
        updateSowerDTO.setSowTaskNos(sowTaskNos);
        updateSowerDTO.setState(SowTaskStateEnum.播种中.getType());
        sowTaskMapper.receiveSowTask(updateSowerDTO);
    }

    /**
     * 自提点播种任务完成
     */
    public List<Long> completeAddressSowTaskItems(SowOrderSaveDTO sowOrderSaveDTO) {
        List<Long> completedSowTaskItemIds = new ArrayList<>();
        List<SowTaskItemDTO> sowTaskItemDTOS = sowOrderSaveDTO.getSowTaskItemDTOS();
        try {
            sowTaskItemDTOS = idempotenceBL.getNoProcessSowTaskItems(sowTaskItemDTOS);
            if (CollectionUtils.isEmpty(sowTaskItemDTOS)) {
                LOG.info("播种任务已经全部处理过:{}", JSON.toJSONString(sowOrderSaveDTO));
                return completedSowTaskItemIds;
            }
            Map<Long, List<SowTaskItemDTO>> sowTaskItemMap =
                sowTaskItemDTOS.stream().collect(Collectors.groupingBy(SowTaskItemDTO::getId));
            List<SowTaskItemPO> sowTaskItemPOS =
                sowTaskItemMapper.findByItemIds(sowOrderSaveDTO.getOrgId(), new ArrayList<>(sowTaskItemMap.keySet()));
            String completedName =
                sowTaskItemPOS.stream().filter(item -> item.getState() == SowTaskStateEnum.已播种.getType())
                    .map(SowTaskItemPO::getProductName).collect(Collectors.joining(","));
            if (StringUtils.isNotEmpty(completedName)) {
                throw new BusinessValidateException("播种任务已经完成，无需重复确认:" + completedName);
            }

            List<SowTaskItemPO> updateItems = new ArrayList<>();
            List<SowTaskPO> updateTaskList = new ArrayList<>();
            Map<Long, SowTaskItemPO> sowTaskItemPOMap = sowTaskItemPOS.stream()
                .collect(Collectors.toMap(SowTaskItemPO::getId, Function.identity(), (key1, key2) -> key2));
            sowTaskItemMap.forEach((id, items) -> {
                SowTaskItemPO sowTaskItemPO = sowTaskItemPOMap.get(id);
                SowTaskItemPO update = new SowTaskItemPO();
                update.setId(sowTaskItemPO.getId());
                update.setOrgId(sowTaskItemPO.getOrgId());
                update.setSowTaskNo(sowTaskItemPO.getSowTaskNo());

                BigDecimal overUnitTotalCount =
                    items.stream().map(SowTaskItemDTO::getOverUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal lackUnitTotalCount =
                    items.stream().map(SowTaskItemDTO::getLackUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                lackUnitTotalCount = sowTaskItemPO.getLackUnitTotalCount().add(lackUnitTotalCount);
                update.setLackUnitTotalCount(lackUnitTotalCount);

                BigDecimal resultCount = sowTaskItemPO.getUnitTotalCount().subtract(lackUnitTotalCount)
                    .subtract(sowTaskItemPO.getOverUnitTotalCount());
                if (overUnitTotalCount.compareTo(resultCount) >= 0) {
                    Date completeTime =
                        items.get(0).getCompleteTime() == null ? new Date() : items.get(0).getCompleteTime();

                    update.setOverUnitTotalCount(sowTaskItemPO.getUnitTotalCount().subtract(lackUnitTotalCount));
                    update.setState(SowTaskStateEnum.已播种.getType());
                    update.setCompleteTime(completeTime);

                    completedSowTaskItemIds.add(id);

                    SowTaskPO updateTask = new SowTaskPO();
                    updateTask.setId(sowTaskItemPO.getSowTaskId());
                    updateTask.setOrgId(sowTaskItemPO.getOrgId());
                    updateTask.setSowTaskNo(sowTaskItemPO.getSowTaskNo());
                    updateTask.setState(SowTaskStateEnum.已播种.getType());
                    updateTask.setCompleteTime(completeTime);

                    updateTaskList.add(updateTask);
                } else {
                    update.setState(SowTaskStateEnum.播种中.getType());
                    update.setOverUnitTotalCount(sowTaskItemPO.getOverUnitTotalCount().add(overUnitTotalCount));

                    if (items.get(0).getStartTime() != null
                        || sowTaskItemPO.getOverUnitTotalCount().compareTo(BigDecimal.ZERO) == 0) {
                        Date startTime = items.get(0).getStartTime() == null ? new Date() : items.get(0).getStartTime();
                        update.setStartTime(startTime);

                        SowTaskPO updateTask = new SowTaskPO();
                        updateTask.setId(sowTaskItemPO.getSowTaskId());
                        updateTask.setOrgId(sowTaskItemPO.getOrgId());
                        updateTask.setSowTaskNo(sowTaskItemPO.getSowTaskNo());
                        updateTask.setState(SowTaskStateEnum.播种中.getType());
                        updateTask.setCompleteTime(startTime);

                        updateTaskList.add(updateTask);
                    }
                }

                updateItems.add(update);
            });

            sowTaskItemMapper.completeSowTaskItems(updateItems);

            if (CollectionUtils.isNotEmpty(updateTaskList)) {
                sowTaskMapper.batchUpdateState(updateTaskList);

                // 播种完成的需要更新波次状态
                List<String> completeSowTaskNos =
                    updateTaskList.stream().filter(task -> task.getState() == SowTaskStateEnum.已播种.getType())
                        .map(SowTaskPO::getSowTaskNo).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(completeSowTaskNos)) {
                    List<String> batchNos = sowTaskMapper.findBatchNos(sowOrderSaveDTO.getOrgId(), completeSowTaskNos);
                    // batchNos.forEach(batchNo -> batchOrderBL.updateBatchStateByBatchNo(batchNo,
                    // sowOrderSaveDTO.getOperatorUserName(), sowOrderSaveDTO.getOperatorUserId()));
                    batchNos.forEach(batchNo -> batchFinishedBL.completeWave(batchNo,
                        sowOrderSaveDTO.getOperatorUserId(), sowOrderSaveDTO.getOrgId()));

                    List<SowTaskPO> completeSowTaskList = updateTaskList.stream()
                        .filter(task -> task.getState() == SowTaskStateEnum.已播种.getType()).collect(Collectors.toList());
                    orderTraceBL.batchUpdateSowTaskState(completeSowTaskList, OrderTraceDescriptionEnum.播种任务完成.name(),
                        sowOrderSaveDTO.getOperatorUserId(), sowOrderSaveDTO.getOperatorUserName());
                }
            }
        } catch (Exception e) {
            idempotenceBL.delSowTaskItemsKey(sowTaskItemDTOS);
            if (e instanceof BusinessValidateException) {
                throw e;
            } else {
                LOG.warn("自提点播种任务提交失败：{}", JSON.toJSONString(sowOrderSaveDTO), e);
                throw new BusinessValidateException("自提点播种任务提交失败");
            }
        }

        return completedSowTaskItemIds;
    }

    /**
     * * 一个波次对应多个拣货任务，一个波次对应一个播种任务。拣货详情关联播种任务id * packageorderitem，保存二次分拣订单明细项与播种号关系 * orderitemtaskinfo
     * ，拣货任务项，订单项，波次之间关联关系 * 1、需要出库单数据 * 2、需要仓库配置，根据仓库id获取配置 * 3、需要货位数据，前端传递，取出库位，且再次播种的时候，出库位不能变更 * 4、需要波次数据 创建二次分拣播种任务
     *
     * @param outStockOrderList
     * @param locationId
     * @param locationName
     * @param sowTaskPO
     * @param batchTaskItemList
     * @return
     */
    public List<PackageOrderItemDTO> createSecondSow(List<OutStockOrderPO> outStockOrderList, Long locationId,
        String locationName, SowTaskPO sowTaskPO, List<BatchTaskItemPO> batchTaskItemList, BatchPO batchPO) {
        LocationReturnDTO locationReturnDTO = new LocationReturnDTO();
        locationReturnDTO.setId(locationId);
        locationReturnDTO.setName(locationName);
        List<PackageOrderItemDTO> packageorderItems;
        if (sowTaskPO == null) {
            List<SowTaskPO> lstSowTaskPOs = new ArrayList<>();
            List<SowOrderPO> lstSowOrders = new ArrayList<>();
            /** 获取播种任务以及明细，播种任务与订单关联关系 */
            sowCalculationBL.processSowTask(outStockOrderList, batchPO, locationReturnDTO, lstSowTaskPOs, lstSowOrders);
            /** 批量保存播种任务 */
            insertSowTaskList(lstSowTaskPOs);
            /** 批量保存播种播种任务订单关联信息 */
            insertSowOrderList(lstSowOrders, null);
            /** 批量更新出库单明细播种信息 */
            batchUpdateOutstockOrderItem(outStockOrderList, null, new HashMap<>());
            /** 批量保存播种托盘号信息 */
            packageorderItems = findPackageOrderItem(batchPO.getOrgId(), batchPO.getWarehouseId(), batchTaskItemList);
            /** 记录日志 */
            orderTraceBL.insertSowTaskList(lstSowTaskPOs);
            /** 回填播种信息到拣货任务明细 */
            rewriteBatchTaskItem(lstSowTaskPOs, batchTaskItemList);
        } else {
            Integer orgId = sowTaskPO.getOrgId();
            List<SowTaskItemPO> lstSowTaskPOs = new ArrayList<>();
            List<SowOrderPO> lstSowOrders = new ArrayList<>();
            /** 获取播种任务以及明细，播种任务与订单关联关系 */
            sowCalculationBL.processSowTask(outStockOrderList, sowTaskPO, lstSowTaskPOs, lstSowOrders);
            /** 已生成播种任务项 */
            List<SowTaskItemPO> oldSowTaskItems =
                sowTaskItemMapper.findBySowTaskIds(Collections.singletonList(sowTaskPO.getId()));
            /** 已关联播种订单 */
            // List<String> orderNos =
            // sowOrderMapper.listRefOrderNoBySowTaskNo(Collections.singletonList(sowTaskPO.getSowTaskNo()));
            Map<String, Long> sowOrderMap = listBySowTaskNo(sowTaskPO.getSowTaskNo(), orgId);
            /** 获取已经存在托盘信息 */
            List<Long> orderItemIds = outStockOrderList.stream().filter(it -> CollectionUtils.isNotEmpty(it.getItems()))
                .flatMap(it -> it.getItems().stream()).map(OutStockOrderItemPO::getId).collect(Collectors.toList());
            List<PackageOrderItemDTO> oldPackageOrderItems =
                listSecondPackageOrderItemByOrderItemId(orderItemIds, orgId);
            /** 批量保存播种任务明细,如果已存在，则不新增 */
            List<SowTaskItemPO> sowTaskItemPOS = insertSowTaskItemList(lstSowTaskPOs, oldSowTaskItems);
            sowTaskPO.setSowTaskItemPOS(sowTaskItemPOS);
            /** 批量保存播种播种任务订单关联信息,如果已存在，则不新增 */
            insertSowOrderList(lstSowOrders, new ArrayList<>(sowOrderMap.keySet()));
            /** 批量更新出库单明细播种信息 */
            batchUpdateOutstockOrderItem(outStockOrderList, Collections.singletonList(sowTaskPO), sowOrderMap);
            /** 批量保存播种托盘号信息,如果订单项已存在，则不新增 */
            packageorderItems = findPackageOrderItem(batchPO.getOrgId(), batchPO.getWarehouseId(), batchTaskItemList);
            /** 更新播种任务 */
            updateSowTask(sowTaskPO);
            /** 回填播种信息到拣货任务明细 */
            rewriteBatchTaskItem(Collections.singletonList(sowTaskPO), batchTaskItemList);
        }
        return packageorderItems;
    }

    private Map<String, Long> listBySowTaskNo(String sowTaskNo, Integer orgId) {
        Map<String, Long> sowOrderMap = new HashMap<>();
        List<SowOrderPO> sowOrderPOS = sowOrderMapper.listBySowTaskNo(sowTaskNo, orgId);
        if (CollectionUtils.isEmpty(sowOrderPOS)) {
            return sowOrderMap;
        }
        sowOrderMap = sowOrderPOS.stream().filter(it -> StringUtils.isNotEmpty(it.getRefOrderNo()))
            .collect(Collectors.toMap(it -> it.getRefOrderNo(), it -> it.getId(), (v1, v2) -> v1));
        return sowOrderMap;
    }

    List<PackageOrderItemDTO> listSecondPackageOrderItemByOrderItemId(List<Long> orderItemIds, Integer orgId) {
        List<PackageOrderItemDTO> packageOrderItemDTOS =
            packageOrderItemBL.listPackageOrderItemByOrderItemId(orderItemIds, orgId);
        if (CollectionUtils.isEmpty(packageOrderItemDTOS)) {
            return new ArrayList<>();
        }
        return packageOrderItemDTOS.stream()
            .filter(it -> it.getPackageType() != null && it.getPackageType().equals(PackageTypeEnum.二次分拣.getType()))
            .collect(Collectors.toList());
    }

    /**
     * 更新播种任务
     *
     * @param sowTaskPO
     */
    private void updateSowTask(SowTaskPO sowTaskPO) {
        List<SowTaskItemPO> sowTaskItemPOS =
            sowTaskItemMapper.findBySowTaskIds(Collections.singletonList(sowTaskPO.getId()));
        List<String> orderNos =
            sowOrderMapper.listRefOrderNoBySowTaskNo(Collections.singletonList(sowTaskPO.getSowTaskNo()));
        sowTaskPO.setOrderCount(orderNos.size());
        sowTaskPO.setSkuCount(sowTaskItemPOS.size());
        BigDecimal packageAmount =
            sowTaskItemPOS.stream().map(SowTaskItemPO::getPackageCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal unitAmount =
            sowTaskItemPOS.stream().map(SowTaskItemPO::getUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        sowTaskPO.setPackageAmount(packageAmount);
        sowTaskPO.setUnitAmount(unitAmount);
        sowTaskMapper.updateByPrimaryKeySelective(sowTaskPO);
    }

    /**
     * 回填播种信息到拣货任务明细
     *
     * @param lstSowTaskPOs
     * @param batchTaskItemList
     */
    private void rewriteBatchTaskItem(List<SowTaskPO> lstSowTaskPOs, List<BatchTaskItemPO> batchTaskItemList) {
        Map<Long, List<SowTaskItemPO>> skuSowItemMap = lstSowTaskPOs.stream()
            .filter(it -> CollectionUtils.isNotEmpty(it.getSowTaskItemPOS()))
            .flatMap(it -> it.getSowTaskItemPOS().stream()).collect(Collectors.groupingBy(it -> it.getProductSkuId()));
        Set<String> batchTaskIds =
            batchTaskItemList.stream().map(BatchTaskItemPO::getBatchTaskId).collect(Collectors.toSet());

        for (BatchTaskItemPO itemPO : batchTaskItemList) {
            List<SowTaskItemPO> sowTaskItemPOS = skuSowItemMap.get(itemPO.getSkuId());
            if (CollectionUtils.isNotEmpty(sowTaskItemPOS)) {
                itemPO.setSowTaskItemId(sowTaskItemPOS.get(0).getId());
                itemPO.setSowTaskId(sowTaskItemPOS.get(0).getSowTaskId());
                itemPO.setSowTaskNo(sowTaskItemPOS.get(0).getSowTaskNo());
                batchTaskItemMapper.updateBatchItemSowTaskId(itemPO);
            }
        }
        if (CollectionUtils.isNotEmpty(batchTaskIds)) {
            batchTaskMapper.updateBatchTaskSowTaskById(new ArrayList<>(batchTaskIds), lstSowTaskPOs.get(0).getId(),
                lstSowTaskPOs.get(0).getSowTaskNo());
        }
    }

    /**
     * 批量更新出库单播种信息
     *
     * @param outStockOrderList
     */
    private void batchUpdateOutstockOrderItem(List<OutStockOrderPO> outStockOrderList, List<SowTaskPO> lstSowTaskPOs,
        Map<String, Long> sowOrderMap) {
        if (CollectionUtils.isEmpty(outStockOrderList)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(lstSowTaskPOs)) {
            Map<Long, List<SowTaskItemPO>> sowSkuMap =
                lstSowTaskPOs.stream().filter(it -> CollectionUtils.isNotEmpty(it.getSowTaskItemPOS()))
                    .flatMap(it -> it.getSowTaskItemPOS().stream())
                    .collect(Collectors.groupingBy(SowTaskItemPO::getProductSkuId));
            outStockOrderList.forEach(it -> it.getItems().forEach(item -> {
                List<SowTaskItemPO> sowTaskItemPOS = sowSkuMap.get(item.getSkuid());
                if (CollectionUtils.isNotEmpty(sowTaskItemPOS)) {
                    Long sowOrderId = sowOrderMap.get(it.getReforderno());
                    item.setSowTaskItemId(sowTaskItemPOS.get(0).getId());
                    item.setSowTaskId(sowTaskItemPOS.get(0).getId());
                    item.setSowTaskNo(sowTaskItemPOS.get(0).getSowTaskNo());
                    item.setSowOrderId(sowOrderId);
                }
            }));
        }

        List<OutStockOrderItemPO> orderItemPOS = outStockOrderList.stream()
            .filter(it -> CollectionUtils.isNotEmpty(it.getItems())).flatMap(it -> it.getItems().stream())
            .filter(it -> it.getSowTaskId() != null).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(orderItemPOS)) {
            outStockOrderItemMapper.batchUpdateOutStockOrderItemSowId(orderItemPOS);
        }
    }

    private List<PackageOrderItemDTO> findPackageOrderItem(Integer orgId, Integer warehouseId,
        List<BatchTaskItemPO> batchTaskItemList) {
        if (CollectionUtils.isEmpty(batchTaskItemList)) {
            return new ArrayList<>();
        }
        List<String> batchTaskItemIds = batchTaskItemList.stream().map(it -> it.getId()).collect(Collectors.toList());
        SecondSortQueryDTO queryDTO = new SecondSortQueryDTO();
        queryDTO.setOrgId(orgId);
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setBatchTaskId(batchTaskItemList.get(0).getBatchTaskId());
        queryDTO.setBatchTaskItemIds(batchTaskItemIds);
        return packageOrderItemBL.findPackageOrderItem(queryDTO);
    }

    /**
     * 批量保存播种任务与订单关联信息 todo 不预先创建PackageOrderItem
     *
     * @param outStockOrderList
     */
    public List<PackageOrderItemDTO> insertPackageorderItem(List<OutStockOrderPO> outStockOrderList,
        List<PackageOrderItemDTO> oldPackageOrderItems) {
        List<PackageOrderItemDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(outStockOrderList)) {
            return result;
        }
        /** 已存在订单项id */
        List<Long> orderItemIds = new ArrayList<>();
        /** 订单项id集合 */
        List<Long> outstockItemIds = outStockOrderList.stream().filter(it -> CollectionUtils.isNotEmpty(it.getItems()))
            .flatMap(it -> it.getItems().stream()).map(OutStockOrderItemPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(oldPackageOrderItems)) {
            /** 已经存在的托盘的订单项id */
            orderItemIds = oldPackageOrderItems.stream().filter(it -> it.getRefOrderItemId() != null)
                .map(PackageOrderItemDTO::getRefOrderItemId).collect(Collectors.toList());

            /** 已经存在的托盘项则直接添加 */
            oldPackageOrderItems.stream().filter(it -> outstockItemIds.contains(it.getRefOrderItemId()))
                .forEach(result::add);
        }

        /** 未创建的托盘项则新增 */
        List<PackageOrderItemPO> packageOrderItems = new ArrayList<>();
        List<Long> finalOrderItemIds = orderItemIds;
        outStockOrderList.forEach(
            it -> it.getItems().stream().filter(item -> !finalOrderItemIds.contains(item.getId())).forEach(item -> {
                PackageOrderItemPO packageOrderItemPO = new PackageOrderItemPO();
                packageOrderItemPO.setId(UuidUtil.getUUidInt());
                packageOrderItemPO.setOrgId(it.getOrgId());
                packageOrderItemPO.setWarehouseId(it.getWarehouseId());
                packageOrderItemPO.setRefOrderId(it.getId());
                packageOrderItemPO.setRefOrderItemId(item.getId());
                packageOrderItemPO.setRefOrderNo(it.getBusinessNo());
                packageOrderItemPO.setPackageType(PackageTypeEnum.二次分拣.getType());
                packageOrderItemPO.setSkuId(item.getSkuid());
                packageOrderItemPO.setProductName(item.getProductname());
                packageOrderItemPO.setSpecName(item.getSpecname());
                packageOrderItemPO.setSpecQuantity(item.getSpecquantity());
                packageOrderItemPO.setPackageName(item.getPackagename());
                packageOrderItemPO.setPackageCount(item.getPackagecount());
                packageOrderItemPO.setUnitCount(item.getUnitcount());
                packageOrderItemPO.setUnitTotalCount(item.getUnittotalcount());
                packageOrderItemPO.setUnitName(item.getUnitname());
                packageOrderItemPO.setCreateTime(new Date());
                packageOrderItemPO.setLastUpdateTime(new Date());
                packageOrderItems.add(packageOrderItemPO);
            }));

        if (CollectionUtils.isNotEmpty(packageOrderItems)) {
            result.addAll(packageOrderItemBL.createPackageorderItem(packageOrderItems));
        }
        return result;
    }

    /**
     * 批量保存播种任务明细
     *
     * @param sowTaskItemPOS
     */
    public List<SowTaskItemPO> insertSowTaskItemList(List<SowTaskItemPO> sowTaskItemPOS,
        List<SowTaskItemPO> existItems) {
        List<SowTaskItemPO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(sowTaskItemPOS)) {
            return result;
        }
        if (CollectionUtils.isNotEmpty(existItems)) {
            /** 已存在的则不新增 */
            Set<Long> checkSkuSet =
                sowTaskItemPOS.stream().map(SowTaskItemPO::getProductSkuId).collect(Collectors.toSet());
            existItems.stream().filter(it -> checkSkuSet.contains(it.getProductSkuId())).forEach(it -> {
                result.add(it);
            });
            /** 不存在的则新增 */
            Set<Long> skuSet = existItems.stream().map(SowTaskItemPO::getProductSkuId).collect(Collectors.toSet());
            sowTaskItemPOS = sowTaskItemPOS.stream().filter(it -> !skuSet.contains(it.getProductSkuId()))
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(sowTaskItemPOS)) {
            sowTaskItemMapper.batchInsert(sowTaskItemPOS);
            result.addAll(sowTaskItemPOS);
        }
        return result;
    }

    /**
     * 播种任务缺货复核
     */
    public void sowTaskLack(SowTaskProductLackReviewDTO lackReviewDTO) {
        AssertUtils.notNull(lackReviewDTO, "请求不能为空");
        AssertUtils.notNull(lackReviewDTO.getOrgId(), "城市不能为空");
        AssertUtils.notNull(lackReviewDTO.getWarehouseId(), "仓库不能为空");
        AssertUtils.notEmpty(lackReviewDTO.getProductList(), "产品列表不能为空");
        lackReviewDTO.getProductList().forEach(pro -> {
            AssertUtils.notNull(pro.getPackageCount(), "大单位数量不能为空");
            AssertUtils.notNull(pro.getUnitCount(), "小单位数量不能为空");
            AssertUtils.notNull(pro.getSpecQuality(), "规格系数不能为空");
            AssertUtils.notEmpty(pro.getSowTaskList(), "播种列表不能为空");
            pro.getSowTaskList().forEach(sow -> {
                AssertUtils.notNull(sow.getSowTaskNo(), "播种任务号不能为空");
                AssertUtils.notNull(sow.getSowTaskItemId(), "播种任务项ID不能为空");
            });
        });
        List<Long> inSowTaskItemIds =
            lackReviewDTO.getProductList().stream().flatMap(pro -> pro.getSowTaskList().stream())
                .map(SowTaskLackDTO::getSowTaskItemId).distinct().collect(Collectors.toList());
        List<String> inSowTaskNos =
            lackReviewDTO.getProductList().stream().flatMap(pro -> pro.getSowTaskList().stream())
                .map(SowTaskLackDTO::getSowTaskNo).distinct().collect(Collectors.toList());
        List<SowTaskPO> sowTaskList = sowTaskMapper.findSowTaskByTaskNos(lackReviewDTO.getOrgId(), inSowTaskNos);
        // 1.基本校验
        if (CollectionUtils.isEmpty(sowTaskList)) {
            throw new BusinessValidateException("播种任务不存在，播种任务：" + inSowTaskNos);
        }

        // 2.分配缺货数量
        SowTaskItemQueryDTO taskItemQueryDTO = new SowTaskItemQueryDTO();
        taskItemQueryDTO.setOrgId(lackReviewDTO.getOrgId());
        taskItemQueryDTO.setWarehouseId(lackReviewDTO.getWarehouseId());
        taskItemQueryDTO.setSowTaskItemIds(inSowTaskItemIds);
        List<SowTaskItemPO> sowTaskItemList = sowTaskItemMapper.listSowOTaskItems(taskItemQueryDTO);
        if (CollectionUtils.isEmpty(sowTaskItemList)) {
            throw new BusinessException("播种任务项不存在!，播种任务：" + inSowTaskNos);
        }
        OutStockOrderSearchSO orderQuery = new OutStockOrderSearchSO();
        orderQuery.setOrgId(lackReviewDTO.getOrgId());
        orderQuery.setWareHouseId(lackReviewDTO.getWarehouseId());
        orderQuery.setSowTaskItemIds(inSowTaskItemIds);
        List<OutStockOrderPO> outOrderList = outStockOrderMapper.listByOrderItem(orderQuery);
        if (CollectionUtils.isEmpty(outOrderList)) {
            throw new BusinessException("出库单不存在，播种任务号：" + inSowTaskNos);
        }
        if (outOrderList.stream().map(OutStockOrderPO::getBoundNo).filter(StringUtils::isNotEmpty).distinct()
            .count() > 1) {
            throw new BusinessValidateException("出库单不属于同一个批次，播种任务号：" + inSowTaskNos);
        }

        List<SowTaskItemUpdateDTO> itemUpdateList = new ArrayList<>();
        lackReviewDTO.getProductList().forEach(product -> {
            List<Long> curSowTaskItemIds =
                product.getSowTaskList().stream().map(SowTaskLackDTO::getSowTaskItemId).collect(Collectors.toList());
            List<SowTaskItemPO> curSowTaskItemList = sowTaskItemList.stream()
                .filter(item -> curSowTaskItemIds.contains(item.getId())).collect(Collectors.toList());
            // 实际播种小数量
            BigDecimal realSowUnitCount =
                product.getPackageCount().multiply(product.getSpecQuality()).add(product.getUnitCount());
            // 应播种小数量
            BigDecimal unitTotalCount = curSowTaskItemList.stream().map(SowTaskItemPO::getUnitTotalCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 缺货小数量
            BigDecimal skuLackUnitCount = unitTotalCount.subtract(realSowUnitCount);

            curSowTaskItemList.sort(Comparator.comparing(SowTaskItemPO::getCreateTime).reversed());
            for (SowTaskItemPO taskItem : curSowTaskItemList) {
                BigDecimal overUnitTotalCount = BigDecimal.ZERO;
                if (skuLackUnitCount.compareTo(taskItem.getUnitTotalCount()) >= 0) {
                    overUnitTotalCount = BigDecimal.ZERO;

                    skuLackUnitCount = skuLackUnitCount.subtract(taskItem.getUnitTotalCount());
                } else {
                    overUnitTotalCount = taskItem.getUnitTotalCount().subtract(skuLackUnitCount);
                    skuLackUnitCount = BigDecimal.ZERO;
                }
                SowTaskItemUpdateDTO updatePO = new SowTaskItemUpdateDTO();
                updatePO.setSowTaskItemId(taskItem.getId());
                updatePO.setOverUnitTotalCount(overUnitTotalCount);
                updatePO.setLackUnitTotalCount(taskItem.getUnitTotalCount().subtract(overUnitTotalCount));

                if (updatePO.getLackUnitTotalCount().compareTo(BigDecimal.ZERO) > 0) {
                    if (updatePO.getOverUnitTotalCount().compareTo(BigDecimal.ZERO) < 0) {
                        LOG.info("实际拣货数量不能小于0，skuId：{}，播种任务项：{}，当前缺货数量：{}", taskItem.getProductSkuId(),
                            taskItem.getId(), updatePO.getLackUnitTotalCount());
                        throw new BusinessValidateException(
                            String.format("实际拣货数量不能小于0，产品：%s", taskItem.getProductName()));
                    }
                    if (updatePO.getOverUnitTotalCount().compareTo(BigDecimal.ZERO) != 0
                        && updatePO.getOverUnitTotalCount().divideAndRemainder(taskItem.getSaleSpecQuantity())[1]
                            .compareTo(BigDecimal.ZERO) != 0) {
                        throw new BusinessValidateException(String.format("实际拣货数量不是销售规格的整数倍，产品：%s，销售规格系数：%s，实际拣货数量：%s",
                            taskItem.getProductName(), taskItem.getSaleSpecQuantity(), overUnitTotalCount));
                    }
                }
                updatePO.setLastUpdateUser(lackReviewDTO.getOptUserName());
                itemUpdateList.add(updatePO);
            }
        });
        // 缺货时
        if (CollectionUtils.isNotEmpty(itemUpdateList)) {
            sowTaskItemMapper.batchUpdateItem(itemUpdateList);
        }

        // 记录日志
        List<OrderTraceDTO> traceList = new ArrayList<>();
        sowTaskItemList.forEach(item -> {
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setId(Long.valueOf(UuidUtil.generatorId()));
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.播种任务.getType());
            dto.setBusinessId(item.getId());
            dto.setBusinessno(item.getSowTaskNo());
            dto.setEventtype(OrderTraceEventTypeEnum.修改.getType());
            dto.setDescription(DESCRIPTION);
            dto.setCreateuser(
                StringUtils.isEmpty(lackReviewDTO.getOptUserName()) ? "1" : lackReviewDTO.getOptUserName());
            dto.setOrgId(lackReviewDTO.getOrgId());
            traceList.add(dto);
        });
        orderTracePOMapper.insertUpdateBatch(traceList);
    }

    /**
     * 播种明细完成PDA
     *
     * @param sowOrderSaveDTO
     */
    @MarkMethodInvokeFlag(
        key = com.yijiupi.supplychain.serviceutils.constant.redis.RedisConstant.SUP_F + "UserOnlineCache",
        warehouseId = "#sowOrderSaveDTO.warehouseId", userId = "#sowOrderSaveDTO.operatorUserId")
    @DistributeLock(conditions = "#sowOrderSaveDTO.sowTaskId", expireMills = 300000, sleepMills = 30000,
        key = "completeSowTaskItemsPDA", lockType = DistributeLock.LockType.WAITLOCK)
    @Transactional(rollbackFor = Exception.class)
    public void completeSowTaskItemsPDA(SowOrderSaveDTO sowOrderSaveDTO) {
        LOG.info("PDA播种明细完成-completeSowTaskItems:{}", JSON.toJSONString(sowOrderSaveDTO));
        List<RedisLock> redisLocks = new ArrayList<>();
        try {
            SowTaskPO sowTaskPO =
                sowTaskMapper.getSowTaskById(sowOrderSaveDTO.getSowTaskId(), sowOrderSaveDTO.getOrgId());
            if (sowTaskPO == null) {
                throw new BusinessValidateException("该播种任务不存在!");
            }
            List<SowTaskItemPO> sowTaskItemPOS = sowTaskPO.getSowTaskItemPOS();
            if (CollectionUtils.isEmpty(sowTaskItemPOS)) {
                throw new BusinessValidateException("播种明细为空!");
            }
            List<SowTaskItemDTO> sowTaskItemDTOS = sowOrderSaveDTO.getSowTaskItemDTOS();
            List<Long> collect = sowTaskItemDTOS.stream().map(SowTaskItemDTO::getId).collect(Collectors.toList());
            List<SowTaskItemPO> sowTasIdList =
                sowTaskItemPOS.stream().filter(s -> collect.contains(s.getId())).collect(Collectors.toList());
            if (sowTasIdList.stream().anyMatch(s -> s.getState().compareTo(SowTaskStateEnum.已播种.getType()) == 0)) {
                throw new BusinessValidateException("该播种任务已完成!");
            }
            redisLocks = setIdWaitLock(Arrays.asList(sowOrderSaveDTO.getSowTaskId()), RedisKeyConstant.SOWTASK, 3000);
            // 组装数据
            List<SowTaskItemPO> updateSowTaskItemPOS =
                buildCompleteSowTaskItemPOSByPDA(sowTaskItemPOS, sowTaskItemDTOS);
            if (CollectionUtils.isNotEmpty(updateSowTaskItemPOS)) {
                updateSowTaskItemPOS.forEach(m -> {
                    m.setSorterId(sowOrderSaveDTO.getOperatorUserId());
                    m.setSorterName(sowOrderSaveDTO.getOperatorUserName());
                });
            }

            // 判断主单状态
            SowTaskDTO sowTaskDTO = new SowTaskDTO();
            sowTaskDTO.setSowTaskType(sowOrderSaveDTO.getSowTaskType());
            sowTaskDTO.setId(sowOrderSaveDTO.getSowTaskId());
            sowTaskDTO.setOrgId(sowTaskPO.getOrgId());
            sowTaskDTO.setSowTaskNo(sowTaskPO.getSowTaskNo());
            sowTaskDTO.setOperatorName(sowOrderSaveDTO.getOperatorUserName());
            sowTaskDTO.setOperatorId(sowOrderSaveDTO.getOperatorUserId());

            /** 二次分拣状态特殊处理 */
            if (sowOrderSaveDTO.getSowTaskType() != null
                && sowOrderSaveDTO.getSowTaskType().equals(SowTaskTypeEnum.二次分拣播种.getType())) {
                sowTaskDTO.setCompleteTime(sowOrderSaveDTO.getCompleteTime());
                sowTaskDTO.setState(sowOrderSaveDTO.getState());
            } else {
                List<SowTaskItemPO> undoneItems = sowTaskItemPOS.stream()
                    .filter(item -> item.getState() != SowTaskStateEnum.已播种.getType()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(undoneItems) && sowTaskPO.getState() != SowTaskStateEnum.已播种.getType()) {
                    sowTaskDTO.setCompleteTime(sowOrderSaveDTO.getCompleteTime());
                    sowTaskDTO.setState(SowTaskStateEnum.已播种.getType());
                } else if (!CollectionUtils.isEmpty(undoneItems)
                    && sowTaskPO.getState() == SowTaskStateEnum.待播种.getType()) {
                    sowTaskDTO.setCompleteTime(sowOrderSaveDTO.getCompleteTime());
                    sowTaskDTO.setState(SowTaskStateEnum.播种中.getType());
                }
            }

            // 修改播种任务信息
            if (sowTaskDTO.getState() != null) {
                updateSowTaskById(sowTaskDTO);
            }

            if (CollectionUtils.isNotEmpty(updateSowTaskItemPOS)) {
                // 修改播种任务明细
                sowTaskItemMapper.completeSowTaskItems(updateSowTaskItemPOS);
            }

            // 播种移库数据
            // PDA播种不区分大小件
            List<PickUpDTO> pickUpDTOList = processCompleteSowTaskItems(sowTaskItemDTOS, sowTaskPO.getLocationId(),
                sowOrderSaveDTO.getWarehouseId(), true);
            // 校验移库
            if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
                pickUpDTOList = batchInventoryTransferBL.checkInventoryTransferByPickUp(pickUpDTOList);
                // 修改拣货关联明细数据
                batchOrderTaskBL.rebuildOrderItemTaskInfoDetail(pickUpDTOList);
            }

            // 得到播种任务的播种数量信息
            List<SownOrderItemDTO> sownOrderItems = sowOrderSaveDTO.getSownOrderItems();
            if (CollectionUtils.isNotEmpty(sownOrderItems)) {
                // 修改播种订单关联信息表的播种数量信息
                if (sowTaskPO.getOperationMode() == null) {
                    sowOrderMapper.updateSownAmountByOrderId(sowOrderSaveDTO.getSowTaskNo(), sownOrderItems,
                        sowOrderSaveDTO.getOperatorUserName(), sowOrderSaveDTO.getOrgId());
                } else {
                    sowOrderMapper.updateSownAmount(sowOrderSaveDTO.getSowTaskNo(), sownOrderItems,
                        sowOrderSaveDTO.getOperatorUserName(), sowOrderSaveDTO.getOrgId());
                }
            }

            // 修改复核信息
            if (sowOrderSaveDTO.getBillReviewDTO() != null) {
                billReviewBL.updateBillReview(sowOrderSaveDTO.getBillReviewDTO());
                sowTaskDTO.setNeedLog(false);
            }

            // 保存包装信息
            List<PackageOrderItemDTO> packageOrderItems = sowOrderSaveDTO.getPackageOrderItems();
            if (CollectionUtils.isNotEmpty(packageOrderItems)) {
                packageOrderItemBL.savePackageBatch(packageOrderItems, true);
            }

            // 判断波次状态,修改波次状态
            String batchNo = sowTaskPO.getBatchNo();
            // boolean batchStateFinish = false;
            // if (sowTaskDTO.getState() != null && sowTaskDTO.getState() == SowTaskStateEnum.已播种.getType()) {
            // batchStateFinish = batchOrderBL.isBatchStateFinish(batchNo, sowOrderSaveDTO.getOperatorUserName(),
            // sowOrderSaveDTO.getSowTaskType());
            // }
            //
            // // 处理订单缺货
            // if (batchStateFinish) {
            // outStockOrderBL.processOrderLack(batchNo, sowOrderSaveDTO.getOperatorUserId());
            // }

            // 修改订单出库位
            sowTaskItemDTOS.stream().filter(item -> item.getToLocationId() != null)
                .collect(Collectors.groupingBy(SowTaskItemDTO::getToLocationId)).forEach((toLocationId, items) -> {
                    List<Long> outStockOrderItemIds = items.stream().map(SowTaskItemDTO::getOutStockOrderItemId)
                        .distinct().collect(Collectors.toList());
                    outStockOrderItemMapper.updateOutStockOrderItemLocationByIds(sowTaskPO.getOrgId(), toLocationId,
                        items.get(0).getToLocationName(), outStockOrderItemIds);
                });

            if (sowTaskDTO.getState() != null && sowTaskDTO.getState() == SowTaskStateEnum.已播种.getType()) {
                try {
                    batchFinishedBL.completeWave(batchNo, sowOrderSaveDTO.getOperatorUserId(),
                        sowOrderSaveDTO.getOrgId());
                } catch (HasLockedException e) {
                    throw new BusinessValidateException("正在处理，请稍后再试！");
                }
            }

            taskPerformanceCalculateBL.calculateAndModTaskPerformance(
                TaskPerformanceCalculateBO.getSowTaskInstance(Collections.singletonList(sowTaskPO.getId().toString()),
                    sowOrderSaveDTO.getOperatorUserId(), sowTaskPO.getWarehouseId(), sowTaskPO.getOrgId(),
                    updateSowTaskPO -> sowTaskMapper.updateByPrimaryKeySelective(updateSowTaskPO)));

            // 同步订单出库位
            // if (batchStateFinish) {
            // outStockOrderBL.processOrderLocation(batchNo);
            // }

            // 移库
            LOG.info("播种移库入参{}，sowTaskNo：{}", new Gson().toJson(pickUpDTOList), sowOrderSaveDTO.getSowTaskNo());
            if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
                PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
                pickUpChangeRecordDTO.setCityId(sowOrderSaveDTO.getOrgId());
                pickUpChangeRecordDTO.setOrderNo(sowOrderSaveDTO.getSowTaskNo());
                pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.提交播种任务.name());
                pickUpChangeRecordDTO.setCreateUser(sowOrderSaveDTO.getOperatorUserName());
                iBatchInventoryManageService.pickupCompleteBySku(pickUpDTOList, pickUpChangeRecordDTO);
            }
        } finally {
            afterProcessUnlock(redisLocks);
        }
    }

    /**
     * 播种完成数据组装(PDA)
     *
     * @param sowTaskItemPOS
     * @param sowTaskItemDTOS
     * @return
     */
    private List<SowTaskItemPO> buildCompleteSowTaskItemPOSByPDA(List<SowTaskItemPO> sowTaskItemPOS,
        List<SowTaskItemDTO> sowTaskItemDTOS) {
        List<SowTaskItemPO> updateSowTaskItemPOS = new ArrayList<>();
        Map<Long, List<SowTaskItemDTO>> sowTaskItemDTOMap =
            sowTaskItemDTOS.stream().collect(Collectors.groupingBy(SowTaskItemDTO::getId));
        for (SowTaskItemPO item : sowTaskItemPOS) {
            List<SowTaskItemDTO> sowTaskItems = sowTaskItemDTOMap.get(item.getId());
            if (CollectionUtils.isNotEmpty(sowTaskItems)) {
                SowTaskItemDTO sowTaskItem = sowTaskItems.get(0);
                SowTaskItemPO update = new SowTaskItemPO();
                update.setId(sowTaskItem.getId());
                update.setStartTime(sowTaskItem.getStartTime() == null ? new Date() : sowTaskItem.getStartTime());

                BigDecimal overUnitTotalCount = sowTaskItems.stream().map(SowTaskItemDTO::getOverUnitTotalCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal lackUnitTotalCount = sowTaskItems.stream().map(SowTaskItemDTO::getLackUnitTotalCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                lackUnitTotalCount =
                    Optional.ofNullable(item.getLackUnitTotalCount()).orElse(BigDecimal.ZERO).add(lackUnitTotalCount);
                update.setLackUnitTotalCount(lackUnitTotalCount);
                // 剩余未播种的
                BigDecimal resultCount = item.getUnitTotalCount().subtract(lackUnitTotalCount)
                    .subtract(Optional.ofNullable(item.getOverUnitTotalCount()).orElse(BigDecimal.ZERO));
                if (overUnitTotalCount.compareTo(resultCount) >= 0) {
                    update.setOverUnitTotalCount(item.getUnitTotalCount().subtract(lackUnitTotalCount));
                    update.setState(SowTaskStateEnum.已播种.getType());
                    update.setCompleteTime(
                        sowTaskItem.getCompleteTime() == null ? new Date() : sowTaskItem.getCompleteTime());

                    item.setState(SowTaskStateEnum.已播种.getType());
                } else {
                    update.setState(SowTaskStateEnum.播种中.getType());
                    update.setOverUnitTotalCount(Optional.ofNullable(item.getOverUnitTotalCount())
                        .orElse(BigDecimal.ZERO).add(overUnitTotalCount));

                    item.setState(SowTaskStateEnum.播种中.getType());
                }

                updateSowTaskItemPOS.add(update);
            }
        }

        return updateSowTaskItemPOS;
    }

    /**
     * 完成产品聚合的播种任务项
     */
    @Transactional(rollbackFor = Throwable.class)
    public void completeSowTaskByProduct(ProductSowTaskDTO completeDTO) {
        AssertUtils.notNull(completeDTO, "请求不能为空");
        AssertUtils.notNull(completeDTO.getOrgId(), "城市ID不能为空");
        AssertUtils.notNull(completeDTO.getProductSkuId(), "skuID不能为空");
        // AssertUtils.notNull(completeDTO.getSaleSpecQuantity(),"销售规格系数不能为空");
        AssertUtils.notEmpty(completeDTO.getSowTaskList(), "播种信息不能为空");
        completeDTO.getSowTaskList().forEach(elem -> {
            AssertUtils.notNull(elem.getSowTaskNo(), "城市ID不能为空");
            AssertUtils.notEmpty(elem.getItems(), "播种任务项不能为空");
        });
        List<RedisLock> redisLocks = new ArrayList<>();
        try {
            List<Long> inSowTaskIds = completeDTO.getSowTaskList().stream().map(SowTaskInfoDTO::getId).distinct()
                .collect(Collectors.toList());
            List<Long> inSowTaskItemIds = completeDTO.getSowTaskList().stream()
                .flatMap(elem -> elem.getItems().stream()).map(SowTaskItemDTO::getId).collect(Collectors.toList());
            List<String> inSowTaskNos = completeDTO.getSowTaskList().stream().map(SowTaskInfoDTO::getSowTaskNo)
                .distinct().collect(Collectors.toList());

            List<SowTaskPO> sowTaskList = sowTaskMapper.findAllSowTaskByIds(completeDTO.getOrgId(), inSowTaskIds);
            // 1.基本校验
            if (CollectionUtils.isEmpty(sowTaskList)) {
                throw new BusinessValidateException("播种任务不存在，播种任务：" + inSowTaskNos);
            }
            if (sowTaskList.stream()
                .anyMatch(task -> Objects.equals(task.getState(), SowTaskStateEnum.已播种.getType()))) {
                throw new BusinessValidateException("播种任务已完成，播种任务：" + sowTaskList.stream()
                    .filter(task -> Objects.equals(task.getState(), SowTaskStateEnum.已播种.getType()))
                    .map(SowTaskPO::getSowTaskNo).collect(Collectors.toList()));
            }

            redisLocks = setIdWaitLock(sowTaskList.stream().map(SowTaskPO::getId).collect(Collectors.toList()),
                RedisKeyConstant.SOWTASK, 6000);

            // 2.分配缺货数量
            SowTaskItemQueryDTO taskItemQueryDTO = new SowTaskItemQueryDTO();
            taskItemQueryDTO.setOrgId(completeDTO.getOrgId());
            taskItemQueryDTO.setWarehouseId(completeDTO.getWarehouseId());
            taskItemQueryDTO.setSowTaskItemIds(inSowTaskItemIds);
            List<SowTaskItemPO> sowTaskItemList = sowTaskItemMapper.listSowOTaskItems(taskItemQueryDTO);
            if (CollectionUtils.isEmpty(sowTaskItemList)) {
                throw new BusinessValidateException("播种任务项不存在!，播种任务：" + inSowTaskNos);
            }
            OutStockOrderSearchSO orderQuery = new OutStockOrderSearchSO();
            orderQuery.setOrgId(completeDTO.getOrgId());
            orderQuery.setWareHouseId(completeDTO.getWarehouseId());
            orderQuery.setSowTaskItemIds(inSowTaskItemIds);
            List<OutStockOrderPO> outOrderList = outStockOrderMapper.listByOrderItem(orderQuery);
            if (CollectionUtils.isEmpty(outOrderList)) {
                throw new BusinessValidateException("出库单不存在，播种任务号：" + inSowTaskNos);
            }
            if (outOrderList.stream().map(OutStockOrderPO::getBoundNo).filter(StringUtils::isNotEmpty).distinct()
                .count() > 1) {
                throw new BusinessValidateException("出库单不属于同一个批次，播种任务号：" + inSowTaskNos);
            }
            // 播种项更新
            List<SowTaskItemPO> sowItemUpdateList =
                packageSowTaskUpdatePO(sowTaskItemList, completeDTO.getLackUnitCount(), completeDTO.getOptUserName());
            if (CollectionUtils.isEmpty(sowItemUpdateList)) {
                throw new BusinessValidateException(String.format("当前产品已经完成播种!，产品名称：%s，播种任务：%s",
                    sowTaskItemList.get(0).getProductName(), inSowTaskNos));
            }

            // 播种更新
            Date curDate = new Date();
            List<SowTaskPO> updateSowList = new ArrayList<>();
            for (SowTaskPO sowTaskPO : sowTaskList) {
                boolean isComplete =
                    sowTaskPO.getSowTaskItemPOS().stream().filter(item -> !inSowTaskItemIds.contains(item.getId()))
                        .allMatch(item -> Objects.equals(item.getState(), SowTaskStateEnum.已播种.getType()));
                if (isComplete) {
                    SowTaskPO updateSow = new SowTaskPO();
                    updateSow.setId(sowTaskPO.getId());
                    updateSow.setOperatorName(completeDTO.getOptUserName());
                    updateSow.setOperatorId(completeDTO.getOptUserId());
                    updateSow.setCompleteTime(curDate);
                    updateSow.setState(SowTaskStateEnum.已播种.getType());
                    updateSow.setLastUpdateUser(completeDTO.getOptUserName());

                    // 以下不更新，仅记录
                    updateSow.setSowTaskNo(sowTaskPO.getSowTaskNo());
                    updateSow.setOrgId(sowTaskPO.getOrgId());
                    updateSowList.add(updateSow);
                }
            }
            // 3.播种任务项完成
            sowTaskItemMapper.completeSowTaskItems(sowItemUpdateList);

            // 4.播种任务完成
            if (CollectionUtils.isNotEmpty(updateSowList)) {
                sowTaskMapper.batchUpdateState(updateSowList);

                // 记录日志
                updateSowList.forEach(update -> {
                    SowTaskDTO trace = new SowTaskDTO();
                    BeanUtils.copyProperties(update, trace);
                    trace.setNeedLog(true);
                    orderTraceBL.updateSowTaskState(trace, OrderTraceDescriptionEnum.播种任务完成.name());
                });
            }

            // 5.更改出库单的拣货状态（所有出库单项都已播种时）

            // 5.2 查出”出库单“对应的”所有出库单项“对应的”播种任务项“
            List<SowTaskItemPO> allSowTaskItemList =
                sowTaskList.stream().flatMap(task -> task.getSowTaskItemPOS().stream()).collect(Collectors.toList());
            // 5.3 ”出库单“的所有”播种任务项“都已完成，则出库单已完成
            List<Long> sowOverOrderIds = new ArrayList<>();
            for (OutStockOrderPO outOrder : outOrderList) {
                List<Long> curTaskItemIds = outOrder.getItems().stream().map(OutStockOrderItemPO::getSowTaskItemId)
                    .collect(Collectors.toList());

                List<SowTaskItemPO> curTaskItems = allSowTaskItemList.stream()
                    .filter(taskItem -> curTaskItemIds.contains(taskItem.getId())).collect(Collectors.toList());
                // 除去当前正在播种的任务，其余任务都完成，则认为已播种
                boolean isAllSow = curTaskItems.stream().filter(item -> !inSowTaskItemIds.contains(item.getId()))
                    .allMatch(item -> Objects.equals(item.getState(), SowTaskStateEnum.已播种.getType()));
                if (isAllSow) {
                    sowOverOrderIds.add(outOrder.getId());
                }
            }
            if (CollectionUtils.isNotEmpty(sowOverOrderIds)) {
                LOG.info("[根据产品完成播种]播种完成的出库单：{}", sowOverOrderIds);
                outStockOrderStateBL.updateStateByOrderIds(sowOverOrderIds, OutStockOrderStateEnum.已拣货.getType());
            }

            for (SowTaskInfoDTO inSowTask : completeDTO.getSowTaskList()) {
                // 数据库数据
                SowTaskPO sourceSowTsk = sowTaskList.stream()
                    .filter(task -> Objects.equals(task.getId(), inSowTask.getId())).findFirst().orElse(null);
                if (sourceSowTsk == null) {
                    LOG.info("[根据产品完成播种]播种任务不存在，播种任务号：{}", inSowTask.getSowTaskNo());
                    continue;
                }

                // 6.修改播种订单信息
                List<SownOrderItemDTO> updateSowOrderItemList = new ArrayList<>();
                for (OutStockOrderPO outOrder : outOrderList) {
                    // 当前播种任务、当前出库单的播种任务项
                    List<Long> curTaskItemIds = outOrder.getItems().stream().map(OutStockOrderItemPO::getSowTaskItemId)
                        .collect(Collectors.toList());
                    // 历史已经播种完成的
                    List<SowTaskItemPO> oldSowTaskItemList = sourceSowTsk.getSowTaskItemPOS().stream()
                        .filter(item -> !curTaskItemIds.contains(item.getId())
                            && Objects.equals(item.getState(), SowTaskStateEnum.已播种.getType()))
                        .collect(Collectors.toList());
                    // 当前更新的
                    List<SowTaskItemPO> curSowTaskItemList = sowItemUpdateList.stream()
                        .filter(item -> curTaskItemIds.contains(item.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(curSowTaskItemList)) {
                        oldSowTaskItemList.addAll(curSowTaskItemList);

                        BigDecimal sownPackageAmount = BigDecimal.ZERO;
                        BigDecimal sownUnitAmount = BigDecimal.ZERO;
                        for (SowTaskItemPO sowTaskItemPO : oldSowTaskItemList) {
                            BigDecimal[] count = sowTaskItemPO.getOverUnitTotalCount()
                                .divideAndRemainder(sowTaskItemPO.getSpecQuantity());
                            sownPackageAmount = sownPackageAmount.add(count[0]);
                            sownUnitAmount = sownUnitAmount.add(count[1]);
                        }
                        SownOrderItemDTO updateSowOrderItemDTO = new SownOrderItemDTO();
                        updateSowOrderItemDTO.setSownPackageAmount(sownPackageAmount);
                        updateSowOrderItemDTO.setSownUnitAmount(sownUnitAmount);
                        updateSowOrderItemDTO.setSownSkuCount(oldSowTaskItemList.size());
                        updateSowOrderItemDTO.setRefOrderNo(outOrder.getReforderno());
                        updateSowOrderItemDTO.setOutStockOrderId(outOrder.getId());
                        updateSowOrderItemList.add(updateSowOrderItemDTO);
                    }
                }
                sowOrderMapper.updateSownAmountByOrderId(sourceSowTsk.getSowTaskNo(), updateSowOrderItemList,
                    completeDTO.getOptUserName(), completeDTO.getOrgId());

                // 7.校验移库
                // 虚仓二次分拣，PDA操作，按产品播种不区分大小件
                List<PickUpDTO> pickUpDTOList = processCompleteSowTaskItems(inSowTask.getItems(),
                    sourceSowTsk.getLocationId(), completeDTO.getWarehouseId(), true);
                if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
                    pickUpDTOList = batchInventoryTransferBL.checkInventoryTransferByPickUp(pickUpDTOList);
                    // 修改拣货关联明细数据
                    batchOrderTaskBL.rebuildOrderItemTaskInfoDetail(pickUpDTOList);
                }

                // 8.修改波次状态
                // Boolean isBatchOver = batchOrderBL.isBatchStateFinish(sourceSowTsk.getBatchNo(),
                // completeDTO.getOptUserName(), sourceSowTsk.getSowTaskType());

                // 9.修改订单出库位
                inSowTask.getItems().stream().filter(item -> item.getToLocationId() != null)
                    .collect(Collectors.groupingBy(SowTaskItemDTO::getToLocationId)).forEach((toLocationId, items) -> {
                        List<Long> outStockOrderItemIds = items.stream().map(SowTaskItemDTO::getOutStockOrderItemId)
                            .distinct().collect(Collectors.toList());
                        outStockOrderItemMapper.updateOutStockOrderItemLocationByIds(completeDTO.getOrgId(),
                            toLocationId, items.get(0).getToLocationName(), outStockOrderItemIds);
                    });

                // 10.同步订单出库位
                // if (isBatchOver) {
                // outStockOrderBL.processOrderLocationByProductSow(sourceSowTsk.getBatchNo());
                // }
                batchFinishedBL.secondPickCompleteWave(sourceSowTsk.getBatchNo(), completeDTO.getOptUserId(),
                    sourceSowTsk.getSowTaskType(), completeDTO.getOrgId());

                // 11.移库
                LOG.info("[根据产品完成播种]播种移库入参{}，播种任务号：{}", JSON.toJSONString(pickUpDTOList), inSowTask.getSowTaskNo());
                if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
                    PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
                    pickUpChangeRecordDTO.setCityId(completeDTO.getOrgId());
                    pickUpChangeRecordDTO.setOrderNo(inSowTask.getSowTaskNo());
                    pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.提交播种任务.name());
                    pickUpChangeRecordDTO.setCreateUser(completeDTO.getOptUserName());
                    iBatchInventoryManageService.pickupCompleteBySku(pickUpDTOList, pickUpChangeRecordDTO);
                }
            }
        } finally {
            afterProcessUnlock(redisLocks);
        }

    }

    /**
     * 组装播种任务更新项，并分配缺货数量
     */
    private List<SowTaskItemPO> packageSowTaskUpdatePO(List<SowTaskItemPO> sowTaskItemList, BigDecimal skuLackCount,
        String optUser) {
        List<SowTaskItemPO> sowItemUpdateList = new ArrayList<>();

        Date curDate = new Date();
        sowTaskItemList.sort(Comparator.comparing(SowTaskItemPO::getCreateTime).reversed());

        for (SowTaskItemPO item : sowTaskItemList) {
            if (Objects.equals(item.getState(), SowTaskStateEnum.已播种.getType())) {
                continue;
            }
            SowTaskItemPO updatePO = new SowTaskItemPO();
            updatePO.setId(item.getId());
            updatePO.setStartTime(curDate);
            BigDecimal overUnitTotalCount = BigDecimal.ZERO;
            // 缺货数量
            // 不缺货、缺货数量分配完毕，则全部可入库
            if (skuLackCount == null || skuLackCount.compareTo(BigDecimal.ZERO) == 0) {
                overUnitTotalCount = item.getUnitTotalCount();
            } else {
                if (skuLackCount.compareTo(item.getUnitTotalCount()) >= 0) {
                    overUnitTotalCount = BigDecimal.ZERO;

                    skuLackCount = skuLackCount.subtract(item.getUnitTotalCount());
                } else {
                    overUnitTotalCount = item.getUnitTotalCount().subtract(skuLackCount);

                    skuLackCount = BigDecimal.ZERO;
                }
            }
            updatePO.setOverUnitTotalCount(overUnitTotalCount);
            updatePO.setLackUnitTotalCount(item.getUnitTotalCount().subtract(overUnitTotalCount));

            if (updatePO.getLackUnitTotalCount().compareTo(BigDecimal.ZERO) > 0) {
                if (updatePO.getOverUnitTotalCount().compareTo(BigDecimal.ZERO) < 0) {
                    LOG.info("实际拣货数量小于0，skuId：{}，播种任务项：{}，当前缺货数量：{}", item.getProductSkuId(), item.getId(),
                        updatePO.getLackUnitTotalCount());
                    throw new BusinessValidateException(String.format("实际拣货数量不能小于0，产品：%s", item.getProductName()));
                }
                if (updatePO.getOverUnitTotalCount().compareTo(BigDecimal.ZERO) != 0
                    && updatePO.getOverUnitTotalCount().divideAndRemainder(item.getSaleSpecQuantity())[1]
                        .compareTo(BigDecimal.ZERO) != 0) {
                    throw new BusinessValidateException(String.format("实际拣货数量不是销售规格的整数倍，产品：%s，销售规格系数：%s，实际拣货数量：%s",
                        item.getProductName(), item.getSaleSpecQuantity(), overUnitTotalCount));
                }
            }
            updatePO.setState(SowTaskStateEnum.已播种.getType());
            updatePO.setCompleteTime(curDate);
            updatePO.setLastUpdateUser(optUser);

            // 后面用
            updatePO.setSpecQuantity(item.getSpecQuantity());
            sowItemUpdateList.add(updatePO);
        }

        return sowItemUpdateList;
    }

    public List<CheckSowReviewDTO> checkSowReview(List<OrderTraceDTO> traceDTOList) {
        List<CheckSowReviewDTO> checkSowReviewDTOS = new ArrayList<>();
        for (OrderTraceDTO orderTraceDTO : traceDTOList) {
            CheckSowReviewDTO checkSowReviewDTO = new CheckSowReviewDTO();
            checkSowReviewDTO.setSowId(orderTraceDTO.getBusinessId());
            checkSowReviewDTO.setCheckSowReview(Boolean.FALSE);
            List<OrderTraceDTO> orderTraceDTOS = orderTracePOMapper.selectByBusinessIdOrNo(orderTraceDTO.getOrgId(),
                orderTraceDTO.getBusinessId(), orderTraceDTO.getBusinessno());
            if (CollectionUtils.isNotEmpty(orderTraceDTOS)) {
                List<OrderTraceDTO> collect = orderTraceDTOS.stream()
                    .filter(o -> o.getEventtype().compareTo(OrderTraceEventTypeEnum.修改.getType()) == 0
                        && DESCRIPTION.equals(o.getDescription()))
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    checkSowReviewDTO.setCheckSowReview(Boolean.TRUE);
                }
            }
            checkSowReviewDTOS.add(checkSowReviewDTO);
        }

        return checkSowReviewDTOS;
    }

    /**
     * 查询播种明细
     *
     * @param orgId
     * @param taskIds
     * @return
     */
    public List<SowTaskItemDTO> listSowTaskItemBySowTaskIds(Integer orgId, List<Long> taskIds) {
        List<SowTaskItemDTO> sowTaskItemDTOS = new ArrayList<>();
        List<SowTaskItemPO> sowTaskItemPOS = sowTaskItemMapper.listSowTaskItemBySowTaskIds(orgId, taskIds);
        if (CollectionUtils.isEmpty(sowTaskItemPOS)) {
            return sowTaskItemDTOS;
        }
        for (SowTaskItemPO sowTaskItemPO : sowTaskItemPOS) {
            SowTaskItemDTO sowTaskItemDTO = new SowTaskItemDTO();
            BeanUtils.copyProperties(sowTaskItemPO, sowTaskItemDTO);
            sowTaskItemDTOS.add(sowTaskItemDTO);
        }
        return sowTaskItemDTOS;
    }

    /**
     * 领取播种任务 如果已经存在并且没用播种完成 直接返回该没有播种完成播种编号
     *
     * @param sowTaskReceiveDTO
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public SowTaskDTO getSowTask(SowTaskReceiveDTO sowTaskReceiveDTO) {
        SowTaskPO sowTaskPO = null;
        sowTaskPO = sowTaskMapper.findCanSowingTaskByLocationName(sowTaskReceiveDTO.getLocationName(),
            sowTaskReceiveDTO.getWarehouseId(), sowTaskReceiveDTO.getOrgId());
        // 如果没有可领取的播种任务 则查询有没有播种中的任务
        SowTaskDTO sowTaskDTO = new SowTaskDTO();
        if (ObjectUtils.isEmpty(sowTaskPO)) {
            sowTaskPO = sowTaskMapper.getSowTaskByStatus(sowTaskReceiveDTO, SowTaskStateEnum.播种中.getType());
            if (!ObjectUtils.isEmpty(sowTaskPO)) {
                sowTaskDTO.setSowTaskNo(sowTaskPO.getSowTaskNo());
                sowTaskDTO.setSowTaskName(sowTaskPO.getSowTaskName());
                return sowTaskDTO;
            }
            throw new BusinessException("没有可领取的播种任务，请检查播种任务下的拣货任务项是否完成");
        }
        // 更新该播种任务信息
        BeanUtils.copyProperties(sowTaskPO, sowTaskDTO);
        sowTaskDTO.setState(SowTaskStateEnum.播种中.getType());
        sowTaskDTO.setOperatorName(sowTaskReceiveDTO.getSower());
        sowTaskDTO.setOperatorId(sowTaskReceiveDTO.getSowerId());
        updateSowTaskById(sowTaskDTO);
        // 查询播种任务下sku信息
        List<String> list = batchTaskItemMapper.listBatchTaskSkuId(sowTaskPO.getId(), sowTaskReceiveDTO.getOrgId());
        // 查询sku属于大件还是小件
        if (CollectionUtils.isNotEmpty(list)) {
            sowTaskDTO.setOrderFeature(String.valueOf(productSkuService.getProductFeatureBySkuId(
                list.stream().map(Long::valueOf).collect(Collectors.toList()), sowTaskReceiveDTO.getWarehouseId())));
        }
        return sowTaskDTO;
    }

    /**
     * 修改播种任务订单出库位
     *
     * @param sowOrderUpdateDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSowOrderLocationBySowTaskItemIds(SowOrderUpdateDTO sowOrderUpdateDTO) {
        LOG.info("updateSowOrderLocationBySowTaskItemIds 修改播种任务订单出库位，入参：{}", JSON.toJSONString(sowOrderUpdateDTO));
        AssertUtils.notNull(sowOrderUpdateDTO, "参数不能为空");
        AssertUtils.notNull(sowOrderUpdateDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notEmpty(sowOrderUpdateDTO.getSowTaskItemIds(), "播种任务明细id不能为空");
        AssertUtils.notNull(sowOrderUpdateDTO.getLocationId(), "货位id不能为空");
        AssertUtils.notNull(sowOrderUpdateDTO.getLocationName(), "货位名称不能为空");

        List<OutStockOrderItemDTO> outStockOrderItemDTOS = outStockOrderItemMapper
            .findBySowTaskItemIds(sowOrderUpdateDTO.getOrgId(), sowOrderUpdateDTO.getSowTaskItemIds());
        if (CollectionUtils.isEmpty(outStockOrderItemDTOS)) {
            LOG.info("城市[{}]播种任务明细id[{}]没有找到出库单据信息！", sowOrderUpdateDTO.getOrgId(),
                sowOrderUpdateDTO.getSowTaskItemIds());
            throw new BusinessValidateException("没有找到出库单明细信息");
        }

        List<Long> outStockOrderItemIds =
            outStockOrderItemDTOS.stream().map(OutStockOrderItemDTO::getId).collect(Collectors.toList());

        // 校验数据
        List<OutStockOrderItemDTO> validateDTOS =
            outStockOrderItemMapper.findSowOrderItemsByOrderNos(sowOrderUpdateDTO.getOrgId(), outStockOrderItemIds);
        List<OutStockOrderItemDTO> notUpdateDTOS = validateDTOS.stream()
            .filter(item -> item.getToLocationId() != null
                || Objects.equals(item.getSowTaskItemState(), SowTaskStateEnum.已播种.getType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notUpdateDTOS)) {
            LOG.info("updateSowOrderLocationBySowTaskItemIds 修改播种任务订单出库位，已播种或已设置出库位数据：{}",
                JSON.toJSONString(notUpdateDTOS));
            throw new BusinessValidateException("存在已完成状态播种明细数据或已设置过出库位，无法设置");
        }

        // 修改订单出库位
        outStockOrderItemMapper.updateOutStockOrderItemLocationByIds(sowOrderUpdateDTO.getOrgId(),
            sowOrderUpdateDTO.getLocationId(), sowOrderUpdateDTO.getLocationName(), outStockOrderItemIds);
    }

    /**
     * 查询播种任务线路司机 sowTaskDTO.getLocationId()这里传的locationId是出库位id
     *
     * @param sowTaskDTO
     */
    public SowTaskRouteAndDriverDTO getRouteAndDriver(SowTaskDTO sowTaskDTO) {
        LOG.info("getRouteAndDriver 查询播种任务线路司机，入参：{}", JSON.toJSONString(sowTaskDTO));
        AssertUtils.notNull(sowTaskDTO, "参数不能为空");
        AssertUtils.notNull(sowTaskDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notEmpty(sowTaskDTO.getSowTaskItemIds(), "播种任务明细id不能为空");

        SowTaskRouteAndDriverDTO routeAndDriverDTO = new SowTaskRouteAndDriverDTO();

        List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.queryRouteAndDriverByOrder(
            sowTaskDTO.getSowTaskItemIds(), sowTaskDTO.getOrgId(), sowTaskDTO.getLocationId());
        LOG.info("getRouteAndDriver 查询播种任务线路司机，结果：{}", JSON.toJSONString(outStockOrderPOS));
        if (CollectionUtils.isNotEmpty(outStockOrderPOS)) {
            OutStockOrderPO po = outStockOrderPOS.get(0);
            routeAndDriverDTO.setRouteName(po.getRouteName());
            routeAndDriverDTO.setDriverName(po.getPickUpUserName());
        }

        return routeAndDriverDTO;
    }

    /**
     * 修复sowOrder上的SowOrderSequence
     *
     * @param sowTaskId
     */
    @Transactional(rollbackFor = Exception.class)
    public void fixSowOrderSequence(Long sowTaskId) {
        List<SowOrderPO> sowOrders = sowOrderMapper.findBySowTaskId(sowTaskId);
        List<Long> outStockOrderIds =
            sowOrders.stream().map(SowOrderPO::getOutStockOrderId).collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findByOrderIds(outStockOrderIds);

        Map<String, Integer> sequenceMap = SowConverter.getSequence(outStockOrderPOList);

        List<SowOrderPO> updateOrderList = sowOrders.stream().map(sowOrderPO -> {
            SowOrderPO updateOrder = new SowOrderPO();
            updateOrder.setId(sowOrderPO.getId());
            updateOrder.setSowOrderSequence(sequenceMap.get(sowOrderPO.getRefOrderNo()));

            return updateOrder;
        }).collect(Collectors.toList());

        updateOrderList.forEach(sowOrder -> sowOrderMapper.updateByPrimaryKeySelective(sowOrder));

        LOG.info("修复播种单排序号, 播种批次为：{}", sowTaskId);
    }

    public void updateSowTask(SowOrderSaveDTO sowOrderSaveDTO) {
        SowTaskPO sowTaskPO = sowTaskMapper.getSowTaskById(sowOrderSaveDTO.getSowTaskId(), sowOrderSaveDTO.getOrgId());
        if (Objects.isNull(sowTaskPO)) {
            return;
        }

        sowTaskMapper.updateSowTaskStateByDTO(sowOrderSaveDTO);
    }

    /**
     * 播种明细完成PDA
     *
     * @param sowOrderSaveDTO
     */
    @MarkMethodInvokeFlag(
        key = com.yijiupi.supplychain.serviceutils.constant.redis.RedisConstant.SUP_F + "UserOnlineCache",
        warehouseId = "#sowOrderSaveDTO.warehouseId", userId = "#sowOrderSaveDTO.operatorUserId")
    @DistributeLock(conditions = "#sowOrderSaveDTO.sowTaskId", expireMills = 300000, sleepMills = 30000,
        key = "completeSowTaskItemsPDAWithLock", lockType = DistributeLock.LockType.WAITLOCK)
    public void completeSowTaskItemsPDAWithLock(SowOrderSaveDTO sowOrderSaveDTO) {
        completeSowTaskItemsPDA(sowOrderSaveDTO);
    }

}
