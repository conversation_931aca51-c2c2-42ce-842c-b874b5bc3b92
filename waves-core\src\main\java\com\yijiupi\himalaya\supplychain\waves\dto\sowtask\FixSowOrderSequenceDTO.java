package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: FixSowOrderSequenceDTO
 * @description:
 * @date 2022-10-25 15:10
 */
public class FixSowOrderSequenceDTO implements Serializable {

    private List<Integer> warehouseIds;

    private List<Long> sowTaskIds;

    private String timeS;

    private String timeE;

    /**
     * 获取
     *
     * @return warehouseIds
     */
    public List<Integer> getWarehouseIds() {
        return this.warehouseIds;
    }

    /**
     * 设置
     *
     * @param warehouseIds
     */
    public void setWarehouseIds(List<Integer> warehouseIds) {
        this.warehouseIds = warehouseIds;
    }

    /**
     * 获取
     *
     * @return sowTaskIds
     */
    public List<Long> getSowTaskIds() {
        return this.sowTaskIds;
    }

    /**
     * 设置
     *
     * @param sowTaskIds
     */
    public void setSowTaskIds(List<Long> sowTaskIds) {
        this.sowTaskIds = sowTaskIds;
    }

    /**
     * 获取
     *
     * @return timeS
     */
    public String getTimeS() {
        return this.timeS;
    }

    /**
     * 设置
     *
     * @param timeS
     */
    public void setTimeS(String timeS) {
        this.timeS = timeS;
    }

    /**
     * 获取
     *
     * @return timeE
     */
    public String getTimeE() {
        return this.timeE;
    }

    /**
     * 设置
     *
     * @param timeE
     */
    public void setTimeE(String timeE) {
        this.timeE = timeE;
    }
}
