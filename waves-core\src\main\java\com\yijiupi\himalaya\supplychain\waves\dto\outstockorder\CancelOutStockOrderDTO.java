package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: CancelOutStockOrderDTO
 * @description:
 * @date 2023-05-25 10:23
 */
public class CancelOutStockOrderDTO implements Serializable {
    /**
     * 取消出库单列表
     */
    private List<OutStockOrderProcessChangeDTO> processChangeDTOS;
    /**
     * 操作人id
     */
    private String optUserId;
    /**
     * 波次号
     */
    private String batchNo;

    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 锁
     */
    private String lockKey;

    /**
     * 获取 取消出库单列表
     *
     * @return processChangeDTOS 取消出库单列表
     */
    public List<OutStockOrderProcessChangeDTO> getProcessChangeDTOS() {
        return this.processChangeDTOS;
    }

    /**
     * 设置 取消出库单列表
     *
     * @param processChangeDTOS 取消出库单列表
     */
    public void setProcessChangeDTOS(List<OutStockOrderProcessChangeDTO> processChangeDTOS) {
        this.processChangeDTOS = processChangeDTOS;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public String getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(String optUserId) {
        this.optUserId = optUserId;
    }

    /**
     * 获取 波次号
     *
     * @return batchNo 波次号
     */
    public String getBatchNo() {
        return this.batchNo;
    }

    /**
     * 设置 波次号
     *
     * @param batchNo 波次号
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 锁
     *
     * @return lockKey 锁
     */
    public String getLockKey() {
        return this.lockKey;
    }

    /**
     * 设置 锁
     *
     * @param lockKey 锁
     */
    public void setLockKey(String lockKey) {
        this.lockKey = lockKey;
    }

}
