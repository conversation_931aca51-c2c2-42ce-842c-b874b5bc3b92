package com.yijiupi.himalaya.supplychain.waves.domain.bl.tms;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.common.constant.RedisConstant;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchBaseBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.lock.LockCreateBatchBL;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchCreateDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.util.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.SyncTmsOutLocationDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.WCSTaskCreateConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.tms.SyncTmsOutLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByRefOrderNoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/13
 */
@Service
public class NotifyTmsBatchCreateBL {

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private RedisUtil<String> redisUtil;
    @Autowired
    private BatchCreateDTOConvertor batchCreateDTOConvertor;
    @Autowired
    private LockCreateBatchBL lockCreateBatchBL;

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    private static final String NOTIFY_TMS_LOCATION_URL =
        "http://in-apitms.yjp.com/tms/tmscombine/synchronize/IOrderOutLocationSyncService/syncOutLocation";
    public static final String NOTIFY_TMS_CREATE_BATCH =
        "http://in-scapi.yjp.com/apitms/newservice/IWMSCombineNewService/syncWaveNo";

    private static final Logger LOG = LoggerFactory.getLogger(NotifyTmsBatchCreateBL.class);

    @Transactional(rollbackFor = Exception.class)
    public void notifyTmsOutStoreLocationInfoByOrderNo(BatchCreateByRefOrderNoDTO dto) {
        List<String> refOrderNoList = WCSTaskCreateConvertor.getOrderNoList(dto);
        List<OutStockOrderPO> outStockOrderPOList =
            outStockOrderMapper.findIdByOrderNos(refOrderNoList, dto.getWarehouseId());

        notifyTmsOutStoreLocationInfo(outStockOrderPOList, dto.getWarehouseId(), dto.getOperateUserId());
        lockCreateBatchBL.cacheParam(outStockOrderPOList, new CreateBatchBaseBO(dto.getWarehouseId(), dto));
    }

    public void notifyTmsOutStoreLocationInfo(List<OutStockOrderPO> outStockOrderPOList, Integer warehouseId,
        Integer optUserId) {
        LOG.warn("通知tms出库位 订单:{}", JSON.toJSONString(outStockOrderPOList));
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }
        List<Long> outStockOrderIds =
            outStockOrderPOList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(outStockOrderIds);

        if (CollectionUtils.isEmpty(orderItemTaskInfoList)) {
            return;
        }
        List<String> batchTaskIds = orderItemTaskInfoList.stream().map(OrderItemTaskInfoPO::getBatchTaskId).distinct()
            .collect(Collectors.toList());

        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findBatchTaskByIds(batchTaskIds);
        if (CollectionUtils.isEmpty(batchTaskPOList)) {
            LOG.warn("通知tms出库位,未查到拣货任务，拣货任务编号：{} ", JSON.toJSONString(batchTaskIds));
            return;
        }

        Map<String, BatchTaskPO> batchTaskMap =
            batchTaskPOList.stream().collect(Collectors.toMap(BatchTaskPO::getId, v -> v));

        SyncTmsOutLocationDTO syncTmsOutLocationDTO = SyncTmsOutLocationDTOConvertor.convert(warehouseId, optUserId,
            outStockOrderPOList, batchTaskMap, orderItemTaskInfoList);
        if (CollectionUtils.isEmpty(syncTmsOutLocationDTO.getItemSyncParamList())) {
            return;
        }

        try {
            restTemplate.postForEntity(NOTIFY_TMS_LOCATION_URL, JSON.toJSON(syncTmsOutLocationDTO), Object.class);

            LOG.info("通知tms出库位成功，入参： {}", JSON.toJSONString(syncTmsOutLocationDTO));
        } catch (Exception e) {
            LOG.warn("通知tms出库位报错, 入参:" + JSON.toJSONString(syncTmsOutLocationDTO), e);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void notifyTmsOutStoreLocationInfoByOrderIds(BatchCreateDTO batchCreateDTO) {
        List<Long> orderIds = batchCreateDTO.getOrderIdList().stream().filter(StringUtils::isNotBlank)
            .map(Long::valueOf).collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderPOList =
            outStockOrderMapper.findIdByIds(orderIds, batchCreateDTO.getWarehouseId());
        notifyTmsOutStoreLocationInfo(outStockOrderPOList, batchCreateDTO.getWarehouseId(),
            batchCreateDTO.getOperateUserId());
        lockCreateBatchBL.cacheParam(outStockOrderPOList,
            new CreateBatchBaseBO(batchCreateDTO.getWarehouseId(), batchCreateDTO));
    }

    @Async
    public void notifyTmsOrderCreateBatch(List<OutStockOrderPO> orderList, BatchPO batchPO, Integer optUserId) {
        //
        List<String> orderNoList =
            orderList.stream().map(OutStockOrderPO::getBusinessNo).distinct().collect(Collectors.toList());
        try {
            restTemplate.postForEntity(NOTIFY_TMS_CREATE_BATCH,
                JSON.toJSON(
                    SyncTmsOutLocationDTOConvertor.convertToCreateWaveNotifyTmsDTOList(orderList, batchPO, optUserId)),
                Object.class);

            LOG.info("通知tms创建波次，入参： {} ; 波次号: {}", JSON.toJSONString(orderNoList), batchPO.getBatchNo());
        } catch (Exception e) {
            LOG.warn("通知tms创建波次报错, " + batchPO.getBatchNo() + "; 入参: " + JSON.toJSONString(orderNoList), e);
        }

        // CreateWaveNotifyTmsDTO
    }

}
