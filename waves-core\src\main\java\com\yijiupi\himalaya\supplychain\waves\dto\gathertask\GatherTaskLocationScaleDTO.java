/*
 * @ClassName GatherTaskLocationDTO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-10-31 10:56:55
 */
package com.yijiupi.himalaya.supplychain.waves.dto.gathertask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class GatherTaskLocationScaleDTO implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = -2239606462328154287L;

    private Long id;
    /**
     * @Fields specQuantity 规格大小单位转换系数
     */
    private BigDecimal specQuantity;
    /**
     * @Fields totalCount 集货总数量
     */
    private BigDecimal totalCount;

    /**
     * 集货状态
     */
    private Integer status;
    /**
     * @Fields gatherTaskId 集货任务号ID
     */
    private Long gatherTaskId;
    /**
     * @Fields gatherTaskProductId 集货任务产品id
     */
    private String gatherTaskProductIds;
    /**
     * @Fields taskNumber 车次编号
     */
    private String taskNumber;

    /**
     * @Fields 集货进度
     */
    private String statusScale;
    /**
     * @Fields licensePlate 车牌号
     */
    private String licensePlate;
    /**
     * @Fields locationId 出库位Id
     */
    private Long locationId;
    /**
     * @Fields locationName 出库位名称
     */
    private String locationName;
    /**
     * @Fields driverName 司机名
     */
    private String driverName;

    /**
     * @return the id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id the id to set
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return the @FieldsspecQuantity规格大小单位转换系数
     */
    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    /**
     * @param @FieldsspecQuantity规格大小单位转换系数 the @FieldsspecQuantity规格大小单位转换系数 to set
     */
    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    /**
     * @return the @FieldstotalCount集货总数量
     */
    public BigDecimal getTotalCount() {
        return totalCount;
    }

    /**
     * @param @FieldstotalCount集货总数量 the @FieldstotalCount集货总数量 to set
     */
    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * @return the 集货状态
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * @param 集货状态 the status to set
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 出库位产品明细
     */
    private List<GatherTaskProductScaleDTO> productScaleList;

    /**
     * 获取 集货任务号ID
     */
    public Long getGatherTaskId() {
        return gatherTaskId;
    }

    /**
     * 设置 集货任务号ID
     */
    public void setGatherTaskId(Long gatherTaskId) {
        this.gatherTaskId = gatherTaskId;
    }

    /**
     * 获取 车次编号
     */
    public String getTaskNumber() {
        return taskNumber;
    }

    /**
     * 设置 车次编号
     */
    public void setTaskNumber(String taskNumber) {
        this.taskNumber = taskNumber == null ? null : taskNumber.trim();
    }

    /**
     * @return the @FieldsgatherTaskProductId集货任务产品id
     */
    public String getGatherTaskProductIds() {
        return gatherTaskProductIds;
    }

    /**
     * @param @FieldsgatherTaskProductId集货任务产品id the gatherTaskProductIds to set
     */
    public void setGatherTaskProductIds(String gatherTaskProductIds) {
        this.gatherTaskProductIds = gatherTaskProductIds;
    }

    /**
     * @return the @Fields集货进度
     */
    public String getStatusScale() {
        return statusScale;
    }

    /**
     * @param @Fields集货进度 the statusScale to set
     */
    public void setStatusScale(String statusScale) {
        this.statusScale = statusScale;
    }

    /**
     * 获取 车牌号
     */
    public String getLicensePlate() {
        return licensePlate;
    }

    /**
     * 设置 车牌号
     */
    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate == null ? null : licensePlate.trim();
    }

    /**
     * 获取 出库位Id
     */
    public Long getLocationId() {
        return locationId;
    }

    /**
     * 设置 出库位Id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 出库位名称
     */
    public String getLocationName() {
        return locationName;
    }

    /**
     * 设置 出库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName == null ? null : locationName.trim();
    }

    /**
     * 获取 司机名
     */
    public String getDriverName() {
        return driverName;
    }

    /**
     * 设置 司机名
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    /**
     * @return the productScaleList
     */
    public List<GatherTaskProductScaleDTO> getProductScaleList() {
        return productScaleList;
    }

    /**
     * @param productScaleList the productScaleList to set
     */
    public void setProductScaleList(List<GatherTaskProductScaleDTO> productScaleList) {
        this.productScaleList = productScaleList;
    }

}