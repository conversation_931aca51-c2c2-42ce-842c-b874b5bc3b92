package com.yijiupi.himalaya.supplychain.waves.dto.secondsort;

import java.io.Serializable;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;

/**
 * <AUTHOR>
 * @date 2021/6/8 15:49
 */
public class SowSecondDTO implements Serializable {
    private static final long serialVersionUID = 9016631500127238890L;

    private List<String> boxCodeList;

    private List<PackageOrderItemDTO> packageOrderItemList;

    public List<String> getBoxCodeList() {
        return boxCodeList;
    }

    public void setBoxCodeList(List<String> boxCodeList) {
        this.boxCodeList = boxCodeList;
    }

    public List<PackageOrderItemDTO> getPackageOrderItemList() {
        return packageOrderItemList;
    }

    public void setPackageOrderItemList(List<PackageOrderItemDTO> packageOrderItemList) {
        this.packageOrderItemList = packageOrderItemList;
    }
}
