package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.supplychain.dto.OrderPrintDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.LogisticsDTO;

/**
 * @description
 * <AUTHOR>
 * @Date 2021/8/26 15:00
 */
public class LogisticsConverter {

    public static List<LogisticsDTO> orderPrintDTOToLogisticsDTOS(List<OrderPrintDTO> orderPrintDTOS) {
        List<LogisticsDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderPrintDTOS)) {
            return result;
        }

        orderPrintDTOS.stream().filter(p -> StringUtils.isNotEmpty(p.getLogisticCode())).filter(Objects::nonNull)
            .forEach(printDTO -> {
                String[] split = printDTO.getLogisticCode().trim().split(",");
                for (String logisticCode : split) {
                    if (StringUtils.isNotEmpty(logisticCode)) {
                        LogisticsDTO logisticsDTO = new LogisticsDTO();
                        logisticsDTO.setCompanyCode(printDTO.getShipperCode());
                        logisticsDTO.setCompanyName(printDTO.getShipperName());
                        // logisticsDTO.setCustomerNo();
                        logisticsDTO.setNo(logisticCode);
                        result.add(logisticsDTO);
                    }
                }
            });

        return result;
    }
}
