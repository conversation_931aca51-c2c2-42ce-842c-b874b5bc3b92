<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemContainerMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemContainerPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="INTEGER" property="orgId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="Batchtaskitem_id" jdbcType="BIGINT" property="batchtaskitemId"/>
        <result column="LocationId" jdbcType="BIGINT" property="locationId"/>
        <result column="LocationName" jdbcType="VARCHAR" property="locationName"/>
        <result column="PickUnitTotalCount" jdbcType="DECIMAL" property="pickUnitTotalCount"/>
        <result column="MoveUnitTotalCount" jdbcType="DECIMAL" property="moveUnitTotalCount"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUserId"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Org_Id, Warehouse_Id, Batchtaskitem_id, LocationId, LocationName, PickUnitTotalCount,
        MoveUnitTotalCount, Remark, CreateTime, CreateUser_Id, LastUpdateTime, LastUpdateUser_Id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtaskitemcontainer
        where Id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByBatchtaskitemId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtaskitemcontainer
        where Batchtaskitem_id = #{batchtaskitemId,jdbcType=VARCHAR}
    </select>
    <select id="selectByBatchtaskitemIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtaskitemcontainer
        where Batchtaskitem_id in
        <foreach collection="list" item="batchtaskitemId" open="(" close=")" separator=",">
            #{batchtaskitemId,jdbcType=BIGINT}
        </foreach>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from batchtaskitemcontainer
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemContainerPO">
        insert into batchtaskitemcontainer (Id, Org_Id, Warehouse_Id,
        Batchtaskitem_id, LocationId, LocationName,
        PickUnitTotalCount, MoveUnitTotalCount,
        Remark, CreateTime, CreateUser_Id,
        LastUpdateTime, LastUpdateUser_Id)
        values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER},
        #{batchtaskitemId,jdbcType=BIGINT}, #{locationId,jdbcType=BIGINT}, #{locationName,jdbcType=VARCHAR},
        #{pickUnitTotalCount,jdbcType=DECIMAL}, #{moveUnitTotalCount,jdbcType=DECIMAL},
        #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=INTEGER},
        #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUserId,jdbcType=INTEGER})
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemContainerPO">
        insert into batchtaskitemcontainer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="orgId != null">
                Org_Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="batchtaskitemId != null">
                Batchtaskitem_id,
            </if>
            <if test="locationId != null">
                LocationId,
            </if>
            <if test="locationName != null">
                LocationName,
            </if>
            <if test="pickUnitTotalCount != null">
                PickUnitTotalCount,
            </if>
            <if test="moveUnitTotalCount != null">
                MoveUnitTotalCount,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUserId != null">
                CreateUser_Id,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="batchtaskitemId != null">
                #{batchtaskitemId,jdbcType=BIGINT},
            </if>
            <if test="locationId != null">
                #{locationId,jdbcType=BIGINT},
            </if>
            <if test="locationName != null">
                #{locationName,jdbcType=VARCHAR},
            </if>
            <if test="pickUnitTotalCount != null">
                #{pickUnitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="moveUnitTotalCount != null">
                #{moveUnitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <insert id="insertList">
        insert into batchtaskitemcontainer (
        Id,
        Org_Id,
        Warehouse_Id,
        Batchtaskitem_id,
        LocationId,
        LocationName,
        PickUnitTotalCount,
        MoveUnitTotalCount,
        Remark,
        CreateTime,
        CreateUser_Id,
        LastUpdateTime,
        LastUpdateUser_Id
        )VALUES
        <foreach collection="pos" item="po" index="index" separator=",">
            (
            #{po.id,jdbcType=BIGINT},
            #{po.orgId,jdbcType=INTEGER},
            #{po.warehouseId,jdbcType=INTEGER},
            #{po.batchtaskitemId,jdbcType=BIGINT},
            #{po.locationId,jdbcType=BIGINT},
            #{po.locationName,jdbcType=VARCHAR},
            #{po.pickUnitTotalCount,jdbcType=DECIMAL},
            #{po.moveUnitTotalCount,jdbcType=DECIMAL},
            #{po.remark,jdbcType=VARCHAR},
            now(),
            #{po.createUserId,jdbcType=INTEGER},
            now(),
            #{po.createUserId,jdbcType=INTEGER}
            )
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemContainerPO">
        update batchtaskitemcontainer
        <set>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="batchtaskitemId != null">
                Batchtaskitem_id = #{batchtaskitemId,jdbcType=BIGINT},
            </if>
            <if test="locationId != null">
                LocationId = #{locationId,jdbcType=BIGINT},
            </if>
            <if test="locationName != null">
                LocationName = #{locationName,jdbcType=VARCHAR},
            </if>
            <if test="pickUnitTotalCount != null">
                PickUnitTotalCount = #{pickUnitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="moveUnitTotalCount != null">
                MoveUnitTotalCount = #{moveUnitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = now(),
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemContainerPO">
        update batchtaskitemcontainer
        set Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
        Batchtaskitem_id = #{batchtaskitemId,jdbcType=BIGINT},
        LocationId = #{locationId,jdbcType=BIGINT},
        LocationName = #{locationName,jdbcType=VARCHAR},
        PickUnitTotalCount = #{pickUnitTotalCount,jdbcType=DECIMAL},
        MoveUnitTotalCount = #{moveUnitTotalCount,jdbcType=DECIMAL},
        Remark = #{remark,jdbcType=VARCHAR},
        LastUpdateTime = now(),
        LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER}
        where Id = #{id,jdbcType=BIGINT}
    </update>
</mapper>