package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

public class PickedProductDTO implements Serializable {

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 产品名称
     */
    private String productName;

    private List<PickedDetailDTO> detailDTOList;

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public List<PickedDetailDTO> getDetailDTOList() {
        return detailDTOList;
    }

    public void setDetailDTOList(List<PickedDetailDTO> detailDTOList) {
        this.detailDTOList = detailDTOList;
    }
}
