package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * 处理订单取消或修改
 *
 * <AUTHOR>
 * @date 2020-06-22 16:09
 */
public class OutStockOrderProcessChangeDTO implements Serializable {

    private static final long serialVersionUID = -2443591516237254935L;

    /**
     * 订单id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String refOrderNo;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 订单明细
     */
    private List<OutStockOrderItemProcessChangeDTO> itemList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<OutStockOrderItemProcessChangeDTO> getItemList() {
        return itemList;
    }

    public void setItemList(List<OutStockOrderItemProcessChangeDTO> itemList) {
        this.itemList = itemList;
    }
}
