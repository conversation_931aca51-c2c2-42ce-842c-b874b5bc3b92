package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.enums.SowLocationAllocationTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IPassageService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.sowtask.CreateSowTaskByOutStockOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.sowtask.CreateSowTaskContextBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.split.WaveSplitRobotBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.GoodsCollectionLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.SowConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchWorkSettingDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.util.*;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class SowCalculationBL {

    private static final Logger LOG = LoggerFactory.getLogger(SowCalculationBL.class);

    @Reference
    private ILocationService locationService;
    @Reference
    private IProductLocationService iProductLocationService;
    @Reference
    private IPassageService iPassageService;

    @Autowired
    private SowOrderMapper sowOrderMapper;
    @Autowired
    private BatchOrderProcessBL batchOrderProcessBL;
    @Autowired
    private SowTaskMapper sowTaskMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private RedisUtil<String> redisUtil;
    @Autowired
    private SowTaskItemMapper sowTaskItemMapper;
    @Autowired
    private OrderTraceBL orderTraceBL;
    @Autowired
    private SowCalculationBL _this;
    @Autowired
    private SecondSortBL secondSortBL;
    @Autowired
    private WaveSplitRobotBL waveSplitRobotBL;
    @Autowired
    private CreateSowTaskContextBL createSowTaskContextBL;
    @Autowired
    private CreateSowTaskByOutStockOrderBL createSowTaskByOutStockOrderBL;
    @Autowired
    private WarehouseAllocationTypeManageBL warehouseAllocationTypeManageBL;

    /**
     * 处理播种信息（按配置格子数量，拆分拣货任务），添加抽核复核任务处理
     */
    @Deprecated
    public void processSowTask(List<OutStockOrderPO> lstOrders, WavesStrategyBO wavesStrategyDTO, PassageDTO passageDTO,
        String title, String operateUser, Integer cityId, WarehouseConfigDTO warehouseConfigDTO, BatchBO batchBO,
        List<WaveCreateDTO> lstCreateDTO, List<GoodsCollectionLocationBO> lstLocations, List<SowTaskPO> lstSowTaskPO,
        String locationName, List<SowOrderPO> lstSowOrders, String driverName, Boolean allocationFlag,
        boolean needRecheck, WaveCreateDTO oriCreateDTO) {
        // 多个出库批次时
        if (wavesStrategyDTO.getIsMultiOutBound()) {
            if (wavesStrategyDTO.getIsOpenSecondSort()
                && Objects.equals(passageDTO.getSowType(), SowTypeEnum.二次分拣.getType())) {
                LOG.info("[多出库批次创建波次]走酒饮二次分拣通道，通道：{}，出库批次号：{}，出库单号：{}", passageDTO.getPassageName(),
                    lstOrders.stream().map(OutStockOrderPO::getBoundNo).distinct().collect(Collectors.toList()),
                    lstOrders.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
                if (lstOrders.stream().flatMap(ord -> ord.getItems().stream())
                    .anyMatch(it -> it.getLocationId() == null)) {
                    LOG.info("[二次分拣]出库位为空，订单号：{}",
                        lstOrders.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
                    throw new BusinessException("二次分拣，出库位为空");
                }

                batchBO.getBatchPO().setSowType(passageDTO.getSowType());

                createSowTaskByOneOutBound(wavesStrategyDTO, passageDTO, title, operateUser, cityId, warehouseConfigDTO,
                    batchBO, lstLocations, lstSowTaskPO, locationName, lstSowOrders, driverName, allocationFlag,
                    needRecheck, lstOrders, lstCreateDTO, oriCreateDTO);

            } else { // 多个出库批次，但不走酒饮二次分拣通道
                LOG.info("[多出库批次创建波次]不走酒饮二次分拣通道，出库批次号：{}，出库单号：{}",
                    lstOrders.stream().map(OutStockOrderPO::getBoundNo).distinct().collect(Collectors.toList()),
                    lstOrders.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
                this.createSowTaskByMultiOutBound(wavesStrategyDTO, passageDTO, title, operateUser, cityId,
                    warehouseConfigDTO, batchBO, lstLocations, lstSowTaskPO, locationName, lstSowOrders, driverName,
                    allocationFlag, needRecheck, lstOrders, lstCreateDTO, oriCreateDTO);
            }
        } else { // 单个出库批次
            LOG.info("[单出库批次]创建播种");
            this.createSowTaskByOneOutBound(wavesStrategyDTO, passageDTO, title, operateUser, cityId,
                warehouseConfigDTO, batchBO, lstLocations, lstSowTaskPO, locationName, lstSowOrders, driverName,
                allocationFlag, needRecheck, lstOrders, lstCreateDTO, oriCreateDTO);
        }
    }

    /**
     * 处理播种信息,生成播种任务和播种任务明细，以及播种任务和订单关联关系
     * 
     * @param lstOrders
     * @param warehouseConfigDTO
     * @param batchPO
     * @param toLocation
     * @param lstSowTaskPO
     * @param lstSowOrders =
     */
    public void processSowTask(List<OutStockOrderPO> lstOrders, BatchPO batchPO, LocationReturnDTO toLocation,
        List<SowTaskPO> lstSowTaskPO, List<SowOrderPO> lstSowOrders) {
        if (CollectionUtils.isNotEmpty(lstOrders)) {
            Integer sowNum = lstSowTaskPO.size() + 1;
            String sowNo = String.format("%s-%s", batchPO.getBatchNo(), sowNum);
            Long sowId = UuidUtil.getUUidInt();
            SowTaskPO sowTaskPO = SowConverter.getSowTaskPO(sowNum, sowId, sowNo, lstOrders, batchPO,
                GoodsCollectionLocationBO.getDefault(toLocation));
            sowTaskPO.setState(SowTaskStateEnum.待播种.getType());
            sowTaskPO.setSowTaskType(SowTaskTypeEnum.二次分拣播种.getType());
            lstSowTaskPO.add(sowTaskPO);
            // 封装播种任务与订单关联信息
            List<SowOrderPO> sowOrderPOS = SowConverter.getSowOrderPOS(sowTaskPO, lstOrders, null);
            lstSowOrders.addAll(sowOrderPOS);
        }
    }

    /**
     * 处理播种信息（按配置格子数量，拆分拣货任务）
     */
    public void processSowTask(List<OutStockOrderPO> lstOrders, SowTaskPO sowTaskPO, List<SowTaskItemPO> lstSowTaskPO,
        List<SowOrderPO> lstSowOrders) {
        if (CollectionUtils.isNotEmpty(lstOrders)) {
            // 封装播种任务明细信息
            List<SowTaskItemPO> sowTaskItemPOS = SowConverter.getSowTaskItemPO(sowTaskPO, lstOrders);
            lstSowTaskPO.addAll(sowTaskItemPOS);
            // 封装播种任务与订单关联信息
            List<SowOrderPO> sowOrderPOS = SowConverter.getSowOrderPOS(sowTaskPO, lstOrders, null);
            lstSowOrders.addAll(sowOrderPOS);
        }
    }

    /**
     * 拆分播种任务，按仓库配置中播种分拣最大箱数进行拆分
     *
     * @param lstOrders
     * @return
     */
    private void splitSowTask(List<OutStockOrderPO> lstOrders, WarehouseConfigDTO warehouseConfigDTO,
        Map<Integer, List<OutStockOrderPO>> splitMap) {
        Integer totalGrids = warehouseConfigDTO.getTotalGrids();
        if (null == totalGrids || 0 == totalGrids) {
            throw new BusinessValidateException("当前仓库没有配置播种分拣最大箱数");
        }

        for (int i = 0; i < lstOrders.size(); i += totalGrids) {
            if (i + totalGrids > lstOrders.size()) {
                totalGrids = lstOrders.size() - i;
            }
            List<OutStockOrderPO> newList = lstOrders.subList(i, i + totalGrids);
            splitMap.put(splitMap.size(), newList);
        }
        LOG.info("播种任务拆分Map:{}", JSON.toJSONString(splitMap));
    }

    /**
     * 处理播种信息(自提点模式)
     */
    @Transactional(rollbackFor = Exception.class)
    @DistributeLock(conditions = "#batchPO.warehouseId", sleepMills = 3000, key = "processAddressSowTask")
    public void processAddressSowTask(WaveCreateDTO createDTO, List<OutStockOrderPO> lstOrders, BatchPO batchPO,
        BatchWorkSettingDTO batchWorkSettingDTO) {
        Integer orgId = batchPO.getOrgId();
        Integer warehouseId = batchPO.getWarehouseId();
        // 按自提点分配货位
        _this.buildOrderByAddress(orgId, warehouseId, lstOrders, batchWorkSettingDTO);

        // 获取集货区信息
        LocationInfoQueryDTO locationInfoQueryDTO = new LocationInfoQueryDTO();
        locationInfoQueryDTO.setCityId(orgId);
        locationInfoQueryDTO.setWarehouseId(warehouseId);
        locationInfoQueryDTO.setName(batchWorkSettingDTO.getSmallLocationName());
        locationInfoQueryDTO.setPageNum(1);
        locationInfoQueryDTO.setPageSize(1);
        PageList<LoactionDTO> locationDTOPageList = locationService.pageListLocation(locationInfoQueryDTO);
        if (locationDTOPageList == null || CollectionUtils.isEmpty(locationDTOPageList.getDataList())) {
            throw new BusinessValidateException("仓库未设置收货位，请联系技术支持设置后，再重试");
        }
        LoactionDTO sowLocation = locationDTOPageList.getDataList().get(0);

        List<SowTaskPO> lstSowTaskPOS = new ArrayList<>();
        List<SowOrderPO> lstSowOrders = new ArrayList<>();
        List<OutStockOrderPO> orders = lstOrders;
        // 开启通道的需按货位拆分
        if (createDTO.getWavesStrategyDTO().getPassPickType().equals(ConditionStateEnum.是.getType())) {
            PassageItemSO passageItemSO = new PassageItemSO();
            passageItemSO.setWarehouseId(warehouseId);
            passageItemSO.setPassageType(PassageTypeEnum.播种.getType());
            List<PassageDTO> passageDTOS = iPassageService.listPassageByRelate(passageItemSO);

            if (CollectionUtils.isNotEmpty(passageDTOS)) {
                List<Long> passageLocationIds = new ArrayList<>();
                passageDTOS.forEach(passage -> {
                    List<Long> locationIds = passage.getItemList().stream()
                        .map(item -> Long.valueOf(item.getRelateId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(locationIds)) {
                        passageLocationIds.addAll(locationIds);

                        List<OutStockOrderPO> passageOrders =
                            lstOrders.stream().filter(order -> locationIds.contains(order.getSowLocationId()))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(passageOrders)) {
                            // 按产品拆分生成播种任务
                            List<SowTaskPO> sowTaskPOS = SowConverter.getSowTaskPOByProduct(
                                SowTaskTypeEnum.按自提点播种.getType(), lstSowTaskPOS.size(), batchPO, sowLocation,
                                passage.getPassageName(), passageOrders);
                            lstSowTaskPOS.addAll(sowTaskPOS);

                            // 封装播种货位与订单关联信息
                            List<SowOrderPO> sowOrderPOS = SowConverter.getSowOrderPOSByOrders(orgId, warehouseId,
                                batchPO.getCreateUser(), passageOrders);
                            lstSowOrders.addAll(sowOrderPOS);
                        }
                    }
                });

                if (CollectionUtils.isNotEmpty(passageLocationIds)) {
                    orders = lstOrders.stream().filter(order -> !passageLocationIds.contains(order.getSowLocationId()))
                        .collect(Collectors.toList());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(orders)) {
            // 按产品拆分生成播种任务
            List<SowTaskPO> sowTaskPOS = SowConverter.getSowTaskPOByProduct(SowTaskTypeEnum.按自提点播种.getType(),
                lstSowTaskPOS.size(), batchPO, sowLocation, null, orders);
            lstSowTaskPOS.addAll(sowTaskPOS);

            // 封装播种货位与订单关联信息
            List<SowOrderPO> sowOrderPOS =
                SowConverter.getSowOrderPOSByOrders(orgId, warehouseId, batchPO.getCreateUser(), orders);
            lstSowOrders.addAll(sowOrderPOS);
        }

        if (CollectionUtils.isNotEmpty(lstSowTaskPOS)) {
            // 设置分仓属性
            warehouseAllocationTypeManageBL.setSowTaskWarehouseAllocationType(lstSowTaskPOS);

            sowTaskMapper.insertSowTaskList(lstSowTaskPOS);
            List<SowTaskItemPO> lstSowTaskItemPOS =
                lstSowTaskPOS.stream().flatMap(task -> task.getSowTaskItemPOS().stream()).collect(Collectors.toList());
            sowTaskItemMapper.batchInsert(lstSowTaskItemPOS);
            sowOrderMapper.insertSowOrderList(lstSowOrders);
            // 记录操作日志
            orderTraceBL.insertSowTaskList(lstSowTaskPOS);
        }
    }

    /**
     * 按自提点拆分订单
     */
    @DistributeLock(conditions = "#warehouseId", sleepMills = 3000, key = "buildOrderByAddress",
        lockType = DistributeLock.LockType.WAITLOCK)
    public void buildOrderByAddress(Integer orgId, Integer warehouseId, List<OutStockOrderPO> lstOrders,
        BatchWorkSettingDTO batchWorkSettingDTO) {
        if (CollectionUtils.isEmpty(lstOrders)) {
            return;
        }

        if (StringUtils.isEmpty(batchWorkSettingDTO.getSowLocationName())) {
            throw new BusinessValidateException("仓库未设置播种作业货区，请联系技术支持设置后，再重试");
        }
        if (StringUtils.isEmpty(batchWorkSettingDTO.getDeadline())) {
            throw new BusinessValidateException("仓库截单时间未设置，请联系技术支持设置后，再重试");
        }

        // 获取作业时间范围
        Map<Date, Date> workTimeMap = getWorkTime(batchWorkSettingDTO);

        String redisKey = RedisKeyConstant.WAREHOUSE_ADDRESS_COUNT + warehouseId;
        String warehouseAddressLocationIds = redisUtil.get(redisKey);
        LOG.info("作业时间范围获取到的当前已分配的最大格子数为：{}，过期时间：{}", JSON.toJSONString(warehouseAddressLocationIds),
            redisUtil.getExpire(redisKey));
        List<Long> locationIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(warehouseAddressLocationIds)) {
            locationIds = JSON.parseArray(warehouseAddressLocationIds, Long.class);
        }

        // 获取剩余播种货位
        Map<Integer, List<OutStockOrderPO>> pickupOrderMap =
            lstOrders.stream().collect(Collectors.groupingBy(OutStockOrderPO::getAddressId));
        List<Integer> addressIds = new ArrayList<>(pickupOrderMap.keySet());

        LocationInfoQueryDTO locationInfoQueryDTO = new LocationInfoQueryDTO();
        locationInfoQueryDTO.setCityId(orgId);
        locationInfoQueryDTO.setWarehouseId(warehouseId);
        locationInfoQueryDTO.setArea(batchWorkSettingDTO.getSowLocationName());
        locationInfoQueryDTO.setSubcategory(LocationEnum.集货位.getType().byteValue());
        if (CollectionUtils.isNotEmpty(locationIds)) {
            locationInfoQueryDTO.setExcludeLocationIds(locationIds);
        }
        locationInfoQueryDTO.setPageSize(addressIds.size());
        PageList<LoactionDTO> locationDTOPageList = locationService.pageListLocation(locationInfoQueryDTO);
        if (locationDTOPageList == null || CollectionUtils.isEmpty(locationDTOPageList.getDataList())
            || locationDTOPageList.getDataList().size() < addressIds.size()) {
            throw new BusinessValidateException("当前包含的自提点数量大于播种作业货区可用的货位数量，请增加货位后再重试");
        }
        List<LoactionDTO> locationDTOS = locationDTOPageList.getDataList();

        // 分配订单
        locationDTOS.sort((Comparator.comparing(LoactionDTO::getSequence)));
        for (int i = 0; i < addressIds.size(); i++) {
            LoactionDTO location = locationDTOS.get(i);
            List<OutStockOrderPO> outStockOrderPOS = pickupOrderMap.get(addressIds.get(i));
            outStockOrderPOS.forEach(order -> {
                order.setSowLocationId(location.getId());
                order.setSowLocationName(location.getName());
                order.setLocationSequence(location.getSequence());
            });
            locationIds.add(location.getId());
        }

        // 根据作业时间范围获取当前已分配的最大格子数
        long timeout = workTimeMap.values().stream().findFirst().get().getTime() - System.currentTimeMillis();
        redisUtil.set(redisKey, JSON.toJSONString(locationIds), timeout, TimeUnit.MILLISECONDS);
    }

    /**
     * @return key 开始时间，value 结束时间
     */
    private Map<Date, Date> getWorkTime(BatchWorkSettingDTO batchWorkSettingDTO) {
        Date currentDate = new Date();
        String workTime = batchWorkSettingDTO.getDeadline();
        if (StringUtils.isEmpty(workTime)) {
            throw new BusinessValidateException("仓库截单时间未设置，请联系技术支持设置后，再重试");
        }
        Map<Date, Date> dateMap = new HashMap<>();
        String dateFormat = DateUtils.getDateFormat(currentDate);
        Date currentWorkDate = DateUtils.getDateByDatetimeString(dateFormat + " " + workTime);

        if (currentDate.compareTo(currentWorkDate) > 0) {
            Date subDay = DateUtils.getSubDay(currentDate, -1);
            String subDayFormat = DateUtils.getDateFormat(subDay);
            Date subWorkDate = DateUtils.getDateByDatetimeString(subDayFormat + " " + workTime);
            dateMap.put(currentWorkDate, subWorkDate);
        } else {
            Date subDay = DateUtils.getSubDay(currentDate, 1);
            String subDayFormat = DateUtils.getDateFormat(subDay);
            Date subWorkDate = DateUtils.getDateByDatetimeString(subDayFormat + " " + workTime);
            dateMap.put(subWorkDate, currentWorkDate);
        }

        return dateMap;
    }

    // 返回集货位信息
    // 返回集货位信息
    public List<LocationReturnDTO> findSowLocation(BatchBO batchBO, List<PassageDTO> lstPassStrategyDTO,
        Long sowTaskId) {
        BatchPO batchPO = batchBO.getBatchPO();
        List<LocationReturnDTO> locationList = new ArrayList<>();
        if (sowTaskId != null || lstPassStrategyDTO.stream().anyMatch(p -> p.getSowType() == SowTypeEnum.播种墙播种.getType()
            || p.getSowType() == SowTypeEnum.分区分单不播种.getType() || p.getSowType() == SowTypeEnum.二次分拣.getType())) {
            // 查找集货区所有货位,快递直发的需要指定货位属性
            boolean isExpress = Objects.equals(batchPO.getRemark(), "快递直发");

            LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
            Byte express = ConditionStateEnum.否.getType();
            if (isExpress) {
                express = ConditionStateEnum.是.getType();
            }
            locationQueryDTO.setExpress(express);
            locationQueryDTO.setCityId(batchPO.getOrgId());
            locationQueryDTO.setWarehouseId(batchPO.getWarehouseId());
            locationQueryDTO.setSubcategory(LocationAreaEnum.集货区.getType().byteValue());
            locationQueryDTO.setAreaState(LocationStateEnum.启用.getType());
            locationList = iProductLocationService.findLocationList(locationQueryDTO);
            // 如果存在集货位，就不需要往集货区上存放
            // if (locationList.stream().anyMatch(p -> LocationEnum.集货位.getType() == p.getSubcategory().intValue())) {
            // locationList.removeIf(p -> LocationAreaEnum.集货区.getType().byteValue() == p.getSubcategory());
            // }
            locationList.removeIf(p -> LocationEnum.集货位.getType().byteValue() != p.getSubcategory());
            if (org.springframework.util.CollectionUtils.isEmpty(locationList)) {
                LOG.info(String.format("仓库：%s没有配置集货位！", batchPO.getWarehouseId()));
            } else {
                locationList.forEach(p -> {
                    if (p.getSequence() == null) {
                        p.setSequence(Integer.MAX_VALUE);
                    }
                    p.setLocationCapacity(0);
                });
                // 查找当前仓库未完成的播种任务存放在哪个区域
                List<SowTaskPO> sowTaskPOS = sowTaskMapper.listNoFinishSowTaskCount(batchPO.getWarehouseId());
                for (SowTaskPO sowTaskPO : sowTaskPOS) {
                    Optional<LocationReturnDTO> tmpLocation =
                        locationList.stream().filter(q -> q.getId().equals(sowTaskPO.getLocationId())).findAny();
                    if (sowTaskPO.getLocationId() != null && tmpLocation.isPresent()
                        && sowTaskPO.getOrderCount() != null) {
                        // 将当前未完成的集货区的数量（OrderCount），暂存到货位的Sequence中
                        tmpLocation.get().setLocationCapacity(sowTaskPO.getOrderCount());
                    }
                }
            }
        }
        return locationList;
    }

    // 返回集货位信息
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<GoodsCollectionLocationBO> findSowCollectLocation(BatchBO batchBO, List<PassageDTO> lstPassStrategyDTO,
        WavesStrategyBO wavesStrategyDTO) {
        BatchPO batchPO = batchBO.getBatchPO();
        List<GoodsCollectionLocationBO> boList = new ArrayList<>();
        if (lstPassStrategyDTO.stream().anyMatch(p -> p.getSowType() == SowTypeEnum.播种墙播种.getType()
            || p.getSowType() == SowTypeEnum.分区分单不播种.getType() || p.getSowType() == SowTypeEnum.二次分拣.getType())) {
            // 查找集货区所有货位,快递直发的需要指定货位属性
            boolean isExpress = Objects.equals(batchPO.getRemark(), "快递直发");

            LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
            Byte express = ConditionStateEnum.否.getType();
            if (isExpress) {
                express = ConditionStateEnum.是.getType();
            }
            locationQueryDTO.setExpress(express);
            locationQueryDTO.setCityId(batchPO.getOrgId());
            locationQueryDTO.setWarehouseId(batchPO.getWarehouseId());
            locationQueryDTO.setSubcategory(LocationAreaEnum.集货区.getType().byteValue());
            locationQueryDTO.setAreaState(LocationStateEnum.启用.getType());
            locationQueryDTO.setState(LocationStateEnum.启用.getType());
            List<LocationReturnDTO> locationList = iProductLocationService.findLocationList(locationQueryDTO);
            // 如果存在集货位，就不需要往集货区上存放
            // if (locationList.stream().anyMatch(p -> LocationEnum.集货位.getType() == p.getSubcategory().intValue())) {
            // locationList.removeIf(p -> LocationAreaEnum.集货区.getType().byteValue() == p.getSubcategory());
            // }
            locationList.removeIf(p -> LocationEnum.集货位.getType().byteValue() != p.getSubcategory());
            if (org.springframework.util.CollectionUtils.isEmpty(locationList)) {
                LOG.info(String.format("仓库：%s没有配置集货位！", batchPO.getWarehouseId()));
                return Collections.emptyList();
            }

            locationList.forEach(p -> {
                if (p.getSequence() == null) {
                    p.setSequence(Integer.MAX_VALUE);
                }
                p.setLocationCapacity(0);
            });

            boList.addAll(locationList.stream().map(location -> {
                GoodsCollectionLocationBO locationBO = new GoodsCollectionLocationBO();
                BeanUtils.copyProperties(location, locationBO);

                return locationBO;
            }).collect(Collectors.toList()));

            Map<Long, GoodsCollectionLocationBO> locationBOMap =
                boList.stream().collect(Collectors.toMap(GoodsCollectionLocationBO::getId, v -> v));

            // 查找当前仓库未完成的播种任务存放在哪个区域
            List<SowTaskPO> sowTaskPOS = sowTaskMapper.listNoFinishSowTask(batchPO.getWarehouseId());
            Map<Long, List<SowTaskPO>> sowTaskGroupMap =
                sowTaskPOS.stream().filter(sowTaskPO -> Objects.nonNull(sowTaskPO.getLocationId()))
                    .collect(Collectors.groupingBy(SowTaskPO::getLocationId));

            sowTaskGroupMap.forEach((key, value) -> {
                GoodsCollectionLocationBO locationBO = locationBOMap.get(key);
                if (Objects.nonNull(locationBO)) {
                    locationBO.setLocationCapacity(locationBO.getLocationCapacity() + value.size());
                    locationBO.getSowTaskPOList().addAll(value);
                }
            });

        }

        return boList;
    }

    /**
     * 查找播种任务要存放的集货位 needAccumulate:是否需要累加
     * 
     * @see CreateSowTaskByOutStockOrderBL#allocationLocation
     * @return
     */
    @Deprecated
    public LocationReturnDTO allocationLocation(List<LocationReturnDTO> locationList, boolean needAccumulate) {
        LocationReturnDTO toLocation = new LocationReturnDTO();
        List<LocationReturnDTO> locations = new ArrayList<>();

        // 去掉货位内都占满的货区
        if (org.springframework.util.CollectionUtils.isEmpty(locationList)) {
            return toLocation;
        } else {
            locationList = locationList.stream()
                .filter(location -> location.getArea_Id() != null && location.getLocationCapacity() != null)
                .collect(Collectors.toList());
            if (org.springframework.util.CollectionUtils.isEmpty(locationList)) {
                return toLocation;
            }
            locationList.stream().collect(Collectors.groupingBy(LocationReturnDTO::getArea_Id))
                .forEach((areaId, locationDTOS) -> {
                    List<LocationReturnDTO> redundancyLocations = locationDTOS.stream()
                        .filter(location -> location.getLocationCapacity() == 0).collect(Collectors.toList());
                    if (!org.springframework.util.CollectionUtils.isEmpty(redundancyLocations)) {
                        locations.addAll(locationDTOS);
                    }
                });
        }

        // 可以累加的，货区货位都满了的情况下则全部进入循环重新计算
        if (org.springframework.util.CollectionUtils.isEmpty(locations) && needAccumulate) {
            locations.addAll(locationList);
        } else if (org.springframework.util.CollectionUtils.isEmpty(locations) && !needAccumulate) {
            return toLocation;
        }

        // 默认分配一个最小的
        Optional<LocationReturnDTO> optionalLocationReturnDTO = locations.stream().min(
            Comparator.comparing(LocationReturnDTO::getLocationCapacity).thenComparing(LocationReturnDTO::getSequence));
        if (optionalLocationReturnDTO.isPresent()) {
            toLocation = optionalLocationReturnDTO.get();
        }
        Long defAreaId = toLocation.getArea_Id();

        // 获取最小货区总容量
        int areaOrderCount = locations.stream().filter(location -> Objects.equals(defAreaId, location.getArea_Id()))
            .mapToInt(LocationReturnDTO::getLocationCapacity).sum();
        // 按货区平均分配
        Map<Long, List<LocationReturnDTO>> locationMap =
            locations.stream().collect(Collectors.groupingBy(LocationReturnDTO::getArea_Id));
        for (Map.Entry<Long, List<LocationReturnDTO>> entry : locationMap.entrySet()) {
            List<LocationReturnDTO> areaLocations = entry.getValue();
            // 获取当前货区总容量
            int areaCapacity = areaLocations.stream().mapToInt(LocationReturnDTO::getLocationCapacity).sum();
            areaLocations.sort(Comparator.comparing(LocationReturnDTO::getLocationCapacity)
                .thenComparing(LocationReturnDTO::getSequence));
            LocationReturnDTO location = areaLocations.get(0);

            if (areaOrderCount > areaCapacity) {
                areaOrderCount = areaCapacity;
                toLocation = location;
            } else if (areaOrderCount == areaCapacity) {
                if (toLocation.getLocationCapacity() > location.getLocationCapacity()) {
                    toLocation = location;
                } else if (toLocation.getLocationCapacity().equals(location.getLocationCapacity())
                    && toLocation.getSequence() >= location.getSequence()) {
                    toLocation = location;
                }
            }
            // LOG.info("货区平均分配货位:{}", JSON.toJSONString(toLocation));
        }
        toLocation.setLocationCapacity(toLocation.getLocationCapacity() + 1);
        return toLocation;
    }

    /**
     * 根据多个出库批次的出库单list，创建多个播种任务
     */
    private void createSowTaskByMultiOutBound(WavesStrategyBO wavesStrategyDTO, PassageDTO passageDTO, String title,
        String operateUser, Integer cityId, WarehouseConfigDTO warehouseConfigDTO, BatchBO batchBO,
        List<GoodsCollectionLocationBO> lstLocations, List<SowTaskPO> lstSowTaskPO, String locationName,
        List<SowOrderPO> lstSowOrders, String driverName, Boolean allocationFlag, boolean needRecheck,
        List<OutStockOrderPO> multiOutBoundOrderList, List<WaveCreateDTO> lstCreateDTO, WaveCreateDTO oriCreateDTO) {

        Map<String, List<OutStockOrderPO>> outStockOrderGroup = multiOutBoundOrderList.stream()
            .collect(Collectors.groupingBy(ele -> String.format("%s", ele.getBoundNo())));
        outStockOrderGroup.forEach((boundNo, orderList) -> {
            if (Objects.equals(boundNo, "null")) {
                LOG.info("[多出库批次创建波次]出库批次号为空，订单号：{}",
                    orderList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
                throw new BusinessValidateException("多出库批次创建波次，出库批次号为空");
            }
            createSowTaskByOneOutBound(wavesStrategyDTO, passageDTO, title, operateUser, cityId, warehouseConfigDTO,
                batchBO, lstLocations, lstSowTaskPO, locationName, lstSowOrders, driverName, allocationFlag,
                needRecheck, orderList, lstCreateDTO, oriCreateDTO);
        });

    }

    /**
     * 根据一个出库批次的出库单list，创建多个播种任务
     */
    private void createSowTaskByOneOutBound(WavesStrategyBO wavesStrategyDTO, PassageDTO passageDTO, String title,
        String operateUser, Integer cityId, WarehouseConfigDTO warehouseConfigDTO, BatchBO batchBO,
        List<GoodsCollectionLocationBO> lstLocations, List<SowTaskPO> lstSowTaskPO, String locationName,
        List<SowOrderPO> lstSowOrders, String driverName, Boolean allocationFlag, boolean needRecheck,
        List<OutStockOrderPO> oneOutBoundOrderList, List<WaveCreateDTO> lstCreateDTO, WaveCreateDTO oriCreateDTO) {

        Map<Integer, List<OutStockOrderPO>> listMap = new HashMap<>();
        if (isOneBound(wavesStrategyDTO, passageDTO)) {
            listMap.put(1, oneOutBoundOrderList);
        } else {
            // 1、按配置格子数量，拆分拣货任务
            // 酒批仓库按订单组成波次，按订单拆分成播种任务
            splitSowTask(oneOutBoundOrderList, warehouseConfigDTO, listMap);
        }

        for (List<OutStockOrderPO> oneSplitOrderList : listMap.values()) {
            if (CollectionUtils.isEmpty(oneSplitOrderList)) {
                continue;
            }

            // 如果开了机器人拣货 和 整件拆零拣货的， 单独拆出来
            // WaveCreateDTO createDTO =
            // createSowTaskByOutStockOrder(wavesStrategyDTO, passageDTO, title, operateUser,
            // cityId,warehouseConfigDTO, batchPO, lstLocations,
            // lstSowTaskPO,locationName, lstSowOrders, driverName,
            // allocationFlag,needRecheck, oneSplitOrderList);
            List<WaveCreateDTO> waveCreateDTOList = createSowTaskContextBL.createSowTask(wavesStrategyDTO, passageDTO,
                title, operateUser, cityId, warehouseConfigDTO, batchBO, lstLocations, lstSowTaskPO, locationName,
                lstSowOrders, driverName, allocationFlag, needRecheck, oneSplitOrderList, oriCreateDTO);

            lstCreateDTO.addAll(waveCreateDTOList);
        }
    }

    private boolean isOneBound(WavesStrategyDTO wavesStrategyDTO, PassageDTO passageDTO) {
        if (passageDTO.getSowType() == SowTypeEnum.虚仓二次分拣播种.getType()) {
            return Boolean.TRUE;
        }
        if (BooleanUtils.isFalse(wavesStrategyDTO.getIsMultiOutBound())) {
            return Boolean.FALSE;
        }

        if (BooleanUtils.isFalse(wavesStrategyDTO.getIsOpenSecondSort())) {
            return Boolean.FALSE;
        }

        if (passageDTO.getSowType() == SowTypeEnum.二次分拣.getType()) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    /**
     * @see CreateSowTaskContextBL#createSowTask
     * @return
     */
    // @Deprecated
    // private List<WaveCreateDTO> createSowTask(WavesStrategyDTO wavesStrategyDTO, PassageDTO passageDTO, String title,
    // String operateUser, Integer cityId, WarehouseConfigDTO warehouseConfigDTO, BatchBO batchBO,
    // List<LocationReturnDTO> lstLocations, List<SowTaskPO> lstSowTaskPO, String locationName,
    // List<SowOrderPO> lstSowOrders, String driverName, Boolean allocationFlag, boolean needRecheck,
    // List<OutStockOrderPO> splitOrderList, WaveCreateDTO oriCreateDTO) {
    // // 没开机器人，也没开整件拆零
    // if (BooleanUtils.isFalse(RobotPickConstants.isPassageRobotPickOpen(wavesStrategyDTO, passageDTO))
    // && BooleanUtils
    // .isFalse(RobotPickConstants.isWarehouseOpenLargePick(wavesStrategyDTO, warehouseConfigDTO))) {
    // return Collections.singletonList(createSowTaskByOutStockOrder(wavesStrategyDTO, passageDTO, title,
    // operateUser, cityId, warehouseConfigDTO, batchBO, lstLocations, lstSowTaskPO, locationName,
    // lstSowOrders, driverName, allocationFlag, needRecheck, splitOrderList, oriCreateDTO));
    // }
    //
    // // 没开机器人，开了整件拆零
    // if (BooleanUtils.isFalse(RobotPickConstants.isPassageRobotPickOpen(wavesStrategyDTO, passageDTO))
    // && BooleanUtils.isTrue(RobotPickConstants.isWarehouseOpenLargePick(wavesStrategyDTO, warehouseConfigDTO))) {
    // return createOpenLargeNotOpenRobot(wavesStrategyDTO, passageDTO, title, operateUser, cityId,
    // warehouseConfigDTO, batchBO, lstLocations, lstSowTaskPO, locationName, lstSowOrders, driverName,
    // allocationFlag, needRecheck, splitOrderList, oriCreateDTO);
    // }
    //
    // // 开了机器人，开了整件拆零，开了 液晶不打包
    // if (BooleanUtils.isTrue(RobotPickConstants.isPassageRobotPickOpen(wavesStrategyDTO, passageDTO))
    // && BooleanUtils.isTrue(RobotPickConstants.isWarehouseOpenLargePick(wavesStrategyDTO, warehouseConfigDTO))
    // && BooleanUtils.isTrue(RobotPickConstants.openLiquidNotPackage(warehouseConfigDTO, wavesStrategyDTO))) {
    // return createOpenLargeAndOpenRobotAndLiquidNotPackage(wavesStrategyDTO, passageDTO, title, operateUser,
    // cityId, warehouseConfigDTO, batchBO, lstLocations, lstSowTaskPO, locationName, lstSowOrders, driverName,
    // allocationFlag, needRecheck, splitOrderList, oriCreateDTO);
    // }
    //
    // // 开了机器人，开了整件拆零，没开 液晶不打包
    //
    // // 存储位有两种，一种是单项拣货任务的（大件数>0），一种是多项拣货任务的（大件数=0，小件数>0）
    // // 单项整件有存储位 单项整件无存储位
    // List<OutStockOrderItemPO> packageStoreItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
    // .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick()))
    // .filter(SplitWaveOrderUtil::isStoreItem).collect(Collectors.toList());
    //
    // List<OutStockOrderItemPO> packageNotStoreItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
    // .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick()))
    // .filter(m -> BooleanUtils.isFalse(SplitWaveOrderUtil.isStoreItem(m))).collect(Collectors.toList());
    //
    // List<OutStockOrderItemPO> multiStorePackageItemList =
    // splitOrderList.stream().flatMap(m -> m.getItems().stream())
    // .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY.equals(m.getLargePick()))
    // .filter(SplitWaveOrderUtil::isStoreItem).collect(Collectors.toList());
    //
    // List<OutStockOrderItemPO> multiNotStorePackageItemList =
    // splitOrderList.stream().flatMap(m -> m.getItems().stream())
    // .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY.equals(m.getLargePick()))
    // .filter(m -> !SplitWaveOrderUtil.isStoreItem(m)).collect(Collectors.toList());
    //
    // List<OutStockOrderItemPO> unitItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
    // .filter(m -> !BatchTaskItemLargePickPatternConstants.isLargePick(m.getLargePick()))
    // .collect(Collectors.toList());
    //
    // // 通道开启了机器人，则生成机器人拣货任务，根据拣货货位判断； 对 otherItemList 还要拆
    // WaveCreateDTO waveCreateDTO = createSowTaskByOutStockOrder(wavesStrategyDTO, passageDTO, title, operateUser,
    // cityId, warehouseConfigDTO, batchBO, lstLocations, lstSowTaskPO, locationName, lstSowOrders, driverName,
    // allocationFlag, needRecheck, splitOrderList, oriCreateDTO);
    //
    // List<WaveCreateDTO> waveCreateList = new ArrayList<>();
    // if (CollectionUtils.isNotEmpty(packageStoreItemList)) {
    // WaveCreateDTO createDTO = createLiquidNotPackageWaveCreateDTO(warehouseConfigDTO, waveCreateDTO,
    // packageStoreItemList, splitOrderList);
    // createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE);
    // createDTO.setPassageDTO(null);
    // waveCreateList.add(createDTO);
    // }
    //
    // if (CollectionUtils.isNotEmpty(packageNotStoreItemList)) {
    // WaveCreateDTO createDTO =
    // SplitWaveOrderUtil.copyWaveCreateDTO(waveCreateDTO, splitOrderList, packageNotStoreItemList);
    // createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE);
    // waveCreateList.add(createDTO);
    // }
    //
    // if (CollectionUtils.isNotEmpty(multiStorePackageItemList)) {
    // WaveCreateDTO createDTO =
    // SplitWaveOrderUtil.copyWaveCreateDTO(waveCreateDTO, splitOrderList, multiStorePackageItemList);
    // createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY);
    // waveCreateList.add(createDTO);
    // }
    //
    // if (CollectionUtils.isNotEmpty(multiNotStorePackageItemList)) {
    // WaveCreateDTO createDTO =
    // SplitWaveOrderUtil.copyWaveCreateDTO(waveCreateDTO, splitOrderList, multiNotStorePackageItemList);
    // createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY);
    // waveCreateList.add(createDTO);
    // }
    //
    // if (CollectionUtils.isNotEmpty(unitItemList)) {
    // List<WaveCreateDTO> otherCreateDTOList =
    // waveSplitRobotBL.splitOtherWave(waveCreateDTO, splitOrderList, unitItemList);
    // waveCreateList.addAll(otherCreateDTOList);
    // }
    //
    // return waveCreateList;
    // }

    // 如果开了播种墙不打包，大件的订单不进 播种，排除；返回需要播种的订单
    private List<OutStockOrderPO> openLiquidNotPackageCreateInvolveSowOrderList(WarehouseConfigDTO warehouseConfigDTO,
        WavesStrategyDTO wavesStrategyDTO, List<OutStockOrderPO> orders,
        List<OutStockOrderItemPO> packageStoreItemList) {
        if (!RobotPickConstants.openLiquidNotPackage(warehouseConfigDTO, wavesStrategyDTO)) {
            return orders;
        }
        if (CollectionUtils.isEmpty(packageStoreItemList)) {
            return orders;
        }

        List<OutStockOrderItemPO> totalOutStockOrderItemList =
            orders.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList());

        List<OutStockOrderItemPO> lstOtherItems = totalOutStockOrderItemList.stream()
            .filter(p -> packageStoreItemList.stream()
                .noneMatch(q -> q.getId().equals(p.getId()) && Objects.equals(q.getLocationId(), p.getLocationId())
                    && Objects.equals(q.getLargePick(), p.getLargePick())))
            .collect(Collectors.toList());

        List<OutStockOrderPO> otherOrderList = OrderRebuildUtil.getOrdersByPassageItems(orders, lstOtherItems);

        return otherOrderList;
    }

    private WaveCreateDTO createLiquidNotPackageWaveCreateDTO(WarehouseConfigDTO warehouseConfigDTO,
        WaveCreateDTO createDTO, List<OutStockOrderItemPO> packageStoreItemList, List<OutStockOrderPO> orders) {
        // 开启了液晶屏的，先把整件的全部拿出来，设置货位为存储区，按订单生成拣货任务，并设置出库位为周转区
        WaveCreateDTO waveCreateDTO = waveSplitRobotBL.createLiquidNotPackageWaveCreateDTO(warehouseConfigDTO,
            packageStoreItemList, createDTO, orders);
        if (Objects.nonNull(waveCreateDTO)) {
            return waveCreateDTO;
        }
        return SplitWaveOrderUtil.copyWaveCreateDTO(createDTO, orders, packageStoreItemList);
    }

    private List<WaveCreateDTO> createOpenLargeAndOpenRobotAndLiquidNotPackage(WavesStrategyDTO wavesStrategyDTO,
        PassageDTO passageDTO, String title, String operateUser, Integer cityId, WarehouseConfigDTO warehouseConfigDTO,
        BatchBO batchBO, List<LocationReturnDTO> lstLocations, List<SowTaskPO> lstSowTaskPO, String locationName,
        List<SowOrderPO> lstSowOrders, String driverName, Boolean allocationFlag, boolean needRecheck,
        List<OutStockOrderPO> splitOrderList, WaveCreateDTO oriCreateDTO) {
        List<OutStockOrderItemPO> packageStoreItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick()))
            .filter(SplitWaveOrderUtil::isStoreItem).collect(Collectors.toList());

        List<OutStockOrderItemPO> packageNotStoreItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick()))
            .filter(m -> BooleanUtils.isFalse(SplitWaveOrderUtil.isStoreItem(m))).collect(Collectors.toList());

        List<OutStockOrderItemPO> packageItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(packageStoreItemList)) {
            packageItemList.addAll(packageStoreItemList);
        }
        if (CollectionUtils.isNotEmpty(packageNotStoreItemList)) {
            packageItemList.addAll(packageNotStoreItemList);
        }

        List<OutStockOrderItemPO> multiStorePackageItemList =
            splitOrderList.stream().flatMap(m -> m.getItems().stream())
                .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY.equals(m.getLargePick()))
                .filter(SplitWaveOrderUtil::isStoreItem).collect(Collectors.toList());

        List<OutStockOrderItemPO> multiNotStorePackageItemList =
            splitOrderList.stream().flatMap(m -> m.getItems().stream())
                .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY.equals(m.getLargePick()))
                .filter(m -> !SplitWaveOrderUtil.isStoreItem(m)).collect(Collectors.toList());

        List<OutStockOrderItemPO> unitItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> !BatchTaskItemLargePickPatternConstants.isLargePick(m.getLargePick()))
            .collect(Collectors.toList());

        // 开了液晶不打包的，整件 单独生成按订单拣货的拣货任务,不生成播种任务
        List<OutStockOrderPO> sowOutStockOrderList = openLiquidNotPackageCreateInvolveSowOrderList(warehouseConfigDTO,
            wavesStrategyDTO, splitOrderList, packageItemList);

        // 通道开启了机器人，则生成机器人拣货任务，根据拣货货位判断； 对 otherItemList 还要拆
        WaveCreateDTO waveCreateDTO = createSowTaskByOutStockOrder(wavesStrategyDTO, passageDTO, title, operateUser,
            cityId, warehouseConfigDTO, batchBO, lstLocations, lstSowTaskPO, locationName, lstSowOrders, driverName,
            allocationFlag, needRecheck, sowOutStockOrderList, oriCreateDTO);

        List<WaveCreateDTO> waveCreateList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(packageItemList)) {
            WaveCreateDTO createDTO =
                createLiquidNotPackageWaveCreateDTO(warehouseConfigDTO, waveCreateDTO, packageItemList, splitOrderList);
            createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE);
            createDTO.setPassageDTO(null);
            waveCreateList.add(createDTO);
        }

        if (CollectionUtils.isNotEmpty(multiStorePackageItemList)) {
            WaveCreateDTO createDTO =
                SplitWaveOrderUtil.copyWaveCreateDTO(waveCreateDTO, sowOutStockOrderList, multiStorePackageItemList);
            createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY);
            waveCreateList.add(createDTO);
        }

        if (CollectionUtils.isNotEmpty(multiNotStorePackageItemList)) {
            WaveCreateDTO createDTO =
                SplitWaveOrderUtil.copyWaveCreateDTO(waveCreateDTO, sowOutStockOrderList, multiNotStorePackageItemList);
            createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY);
            waveCreateList.add(createDTO);
        }

        if (CollectionUtils.isNotEmpty(unitItemList)) {
            List<WaveCreateDTO> otherCreateDTOList =
                waveSplitRobotBL.splitOtherWave(waveCreateDTO, sowOutStockOrderList, unitItemList);
            waveCreateList.addAll(otherCreateDTOList);
        }

        return waveCreateList;
    }

    /**
     * 创建开了整件拆零，没开机器人的播种和拣货任务。多项整件单独生成一个拣货任务,进播种；单项整件单独生成一个拣货任务不进播种；人工零拣任务 单独生成一个拣货任务，进播种。
     * 
     * @param wavesStrategyDTO
     * @param passageDTO
     * @param title
     * @param operateUser
     * @param cityId
     * @param warehouseConfigDTO
     * @param batchBO
     * @param lstLocations
     * @param lstSowTaskPO
     * @param locationName
     * @param lstSowOrders
     * @param driverName
     * @param allocationFlag
     * @param needRecheck
     * @param splitOrderList
     * @return
     */
    private List<WaveCreateDTO> createOpenLargeNotOpenRobot(WavesStrategyDTO wavesStrategyDTO, PassageDTO passageDTO,
        String title, String operateUser, Integer cityId, WarehouseConfigDTO warehouseConfigDTO, BatchBO batchBO,
        List<LocationReturnDTO> lstLocations, List<SowTaskPO> lstSowTaskPO, String locationName,
        List<SowOrderPO> lstSowOrders, String driverName, Boolean allocationFlag, boolean needRecheck,
        List<OutStockOrderPO> splitOrderList, WaveCreateDTO oriCreateDTO) {

        // 单项整件有存储位 单项整件无存储位
        List<OutStockOrderItemPO> packageStoreItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick()))
            .filter(SplitWaveOrderUtil::isStoreItem).collect(Collectors.toList());

        List<OutStockOrderItemPO> packageNotStoreItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick()))
            .filter(m -> BooleanUtils.isFalse(SplitWaveOrderUtil.isStoreItem(m))).collect(Collectors.toList());

        // 多项整件
        List<OutStockOrderItemPO> multiStorePackageItemList =
            splitOrderList.stream().flatMap(m -> m.getItems().stream())
                .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY.equals(m.getLargePick()))
                .filter(SplitWaveOrderUtil::isStoreItem).collect(Collectors.toList());

        List<OutStockOrderItemPO> multiNotStorePackageItemList =
            splitOrderList.stream().flatMap(m -> m.getItems().stream())
                .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY.equals(m.getLargePick()))
                .filter(m -> !SplitWaveOrderUtil.isStoreItem(m)).collect(Collectors.toList());

        List<OutStockOrderItemPO> unitItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> !BatchTaskItemLargePickPatternConstants.isLargePick(m.getLargePick()))
            .collect(Collectors.toList());

        WaveCreateDTO waveCreateDTO = createSowTaskByOutStockOrder(wavesStrategyDTO, passageDTO, title, operateUser,
            cityId, warehouseConfigDTO, batchBO, lstLocations, lstSowTaskPO, locationName, lstSowOrders, driverName,
            allocationFlag, needRecheck, splitOrderList, oriCreateDTO);

        List<WaveCreateDTO> waveCreateList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(packageStoreItemList)) {
            WaveCreateDTO createDTO =
                SplitWaveOrderUtil.copyWaveCreateDTO(waveCreateDTO, splitOrderList, packageStoreItemList);
            createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE);
            waveCreateList.add(createDTO);
        }

        if (CollectionUtils.isNotEmpty(packageNotStoreItemList)) {
            WaveCreateDTO createDTO =
                SplitWaveOrderUtil.copyWaveCreateDTO(waveCreateDTO, splitOrderList, packageNotStoreItemList);
            createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE);
            waveCreateList.add(createDTO);
        }
        if (CollectionUtils.isNotEmpty(multiStorePackageItemList)) {
            WaveCreateDTO createDTO =
                SplitWaveOrderUtil.copyWaveCreateDTO(waveCreateDTO, splitOrderList, multiStorePackageItemList);
            createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY);
            waveCreateList.add(createDTO);
        }

        if (CollectionUtils.isNotEmpty(multiNotStorePackageItemList)) {
            WaveCreateDTO createDTO =
                SplitWaveOrderUtil.copyWaveCreateDTO(waveCreateDTO, splitOrderList, multiNotStorePackageItemList);
            createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY);
            waveCreateList.add(createDTO);
        }

        if (CollectionUtils.isNotEmpty(unitItemList)) {
            List<WaveCreateDTO> createDTOList =
                waveSplitRobotBL.splitUnitAndNotPickPattern(waveCreateDTO, splitOrderList, unitItemList);
            waveCreateList.addAll(createDTOList);
        }

        return waveCreateList;
    }

    /**
     * 根据一个出库批次拆分后出库单list，创建一个播种任务
     * 
     * @see CreateSowTaskByOutStockOrderBL#createSowTaskByOutStockOrder
     */
    @Deprecated
    private WaveCreateDTO createSowTaskByOutStockOrder(WavesStrategyDTO wavesStrategyDTO, PassageDTO passageDTO,
        String title, String operateUser, Integer cityId, WarehouseConfigDTO warehouseConfigDTO, BatchBO batchBO,
        List<LocationReturnDTO> lstLocations, List<SowTaskPO> lstSowTaskPO, String locationName,
        List<SowOrderPO> lstSowOrders, String driverName, Boolean allocationFlag, boolean needRecheck,
        List<OutStockOrderPO> splitOrderList, WaveCreateDTO oriCreateDTO) {
        Byte sowType = passageDTO.getSowType();
        BatchPO batchPO = batchBO.getBatchPO();

        // 2、拆分过的订单，属于同一个播种任务
        Integer sowNum = batchBO.getSowTaskNum() + 1;
        batchBO.setSowTaskNum(sowNum);

        String sowNo = String.format("%s-%s", batchPO.getBatchNo(), sowNum);
        Long sowId = UuidUtil.getUUidInt();

        LocationReturnDTO toLocation = null;

        // 3、获取待保存的播种任务列表(开启分区分单的不设置集货位)
        Byte sowLocationAllocationType = warehouseConfigDTO.getSowLocationAllocationType();
        if ((sowType == SowTypeEnum.播种墙播种.getType() || sowType == SowTypeEnum.分区分单不播种.getType()
            || sowType == SowTypeEnum.二次分拣.getType())
            && !Objects.equals(sowLocationAllocationType, SowLocationAllocationTypeEnum.开始拣货时分配.getType())) {
            toLocation = allocationLocation(lstLocations, true);
        }

        SowTaskPO sowTaskPO = null;
        // 虚仓二次分拣时的特殊处理
        if (Objects.equals(sowType, SowTypeEnum.虚仓二次分拣播种.getType())) {
            sowTaskPO = SowConverter.getVirtualSecondPickSowTaskPO(sowId, sowNo, splitOrderList, batchPO);
        } else {
            sowTaskPO = SowConverter.getSowTaskPO(sowNum, sowId, sowNo, splitOrderList, batchPO,
                GoodsCollectionLocationBO.getDefault(toLocation));
        }

        if (sowType == SowTypeEnum.分区分单不播种.getType()) {
            // sowTaskPO.setState(SowTaskStateEnum.待集货.getType());
            sowTaskPO.setState(SowTaskStateEnum.已播种.getType());
        } else {
            sowTaskPO.setState(SowTaskStateEnum.待播种.getType());
        }

        if (sowType == SowTypeEnum.播种墙播种.getType()) {
            sowTaskPO.setSowTaskType(SowTaskTypeEnum.播种墙播种.getType());
        } else if (sowType == SowTypeEnum.分区分单播种.getType()) {
            sowTaskPO.setSowTaskType(SowTaskTypeEnum.打包复核.getType());
        } else if (sowType == SowTypeEnum.二次分拣.getType()) {
            sowTaskPO.setSowTaskType(SowTaskTypeEnum.二次分拣播种.getType());
        }
        /** 如果是抽核生成的复核任务，则类型为打包复核 */
        if (needRecheck) {
            sowTaskPO.setSowTaskType(SowTaskTypeEnum.打包复核.getType());
        }

        if (sowType != SowTypeEnum.分区分单拣货.getType()) {
            lstSowTaskPO.add(sowTaskPO);
        }

        List<Long> orderIdList = splitOrderList.stream().map(OutStockOrderPO::getId).collect(Collectors.toList());
        List<OutStockOrderPO> list = outStockOrderMapper.findByOrderId(orderIdList);

        // 封装播种任务与订单关联信息
        List<SowOrderPO> sowOrderPOS = new ArrayList<>();

        if (wavesStrategyDTO.getIsOpenSecondSort()
            && Objects.equals(passageDTO.getSowType(), SowTypeEnum.二次分拣.getType())) {
            sowOrderPOS.addAll(SowConverter.getSecondSowOrderPOS(sowTaskPO, splitOrderList, warehouseConfigDTO, list));
        } else {
            sowOrderPOS.addAll(SowConverter.getSowOrderPOS(sowTaskPO, splitOrderList, warehouseConfigDTO));
        }
        if (sowType != SowTypeEnum.分区分单拣货.getType()) {
            lstSowOrders.addAll(sowOrderPOS);
        }

        WaveCreateDTO createDTO =
            SplitWaveOrderUtil.getWaveCreateDTO(splitOrderList, wavesStrategyDTO, passageDTO.getPickingType(),
                passageDTO.getPickingGroupStrategy(), title, operateUser, cityId, locationName, driverName,
                passageDTO.getSowType(), passageDTO.getPassageType(), allocationFlag, oriCreateDTO.getOperateUserId());
        createDTO.setPassageDTO(passageDTO);
        if (sowType != SowTypeEnum.分区分单拣货.getType()) {
            createDTO.setSowId(sowId);
            createDTO.setSowNo(sowNo);
        }
        if (Objects.equals(sowTaskPO.getOperationMode(), OperationModeEnum.不拣货播种.getType())) {
            createDTO.setPickFlag(false);
        }

        return createDTO;
    }
}
