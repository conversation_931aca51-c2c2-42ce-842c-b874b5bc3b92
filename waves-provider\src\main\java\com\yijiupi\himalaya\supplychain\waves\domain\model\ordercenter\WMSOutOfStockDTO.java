package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class WMSOutOfStockDTO implements Serializable {

    private Long orderId;

    /**
     * @Fields outOfStockTime 缺货时间
     */
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outOfStockTime;

    private List<WMSOutOfStockItemDTO> items;

    private String optUserId;

    private Integer warehouseId;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Date getOutOfStockTime() {
        return outOfStockTime;
    }

    public void setOutOfStockTime(Date outOfStockTime) {
        this.outOfStockTime = outOfStockTime;
    }

    public List<WMSOutOfStockItemDTO> getItems() {
        return items;
    }

    public void setItems(List<WMSOutOfStockItemDTO> items) {
        this.items = items;
    }

    public String getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(String optUserId) {
        this.optUserId = optUserId;
    }

    /**
     * 获取
     *
     * @return warehouseId
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置
     *
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
