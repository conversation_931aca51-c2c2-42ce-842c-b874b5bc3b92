package com.yijiupi.himalaya.supplychain.waves.dto.agv;

import java.io.Serializable;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
public class AgvTaskMoveDTO implements Serializable {
    /**
     * 拣货任务id
     */
    private String batchTaskId;
    /**
     * 拣货任务明细id
     */
    private String batchTaskItemId;
    /**
     * 操作人id
     */
    private Integer optUserId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 组织机构id
     */
    private Integer orgId;

    /**
     * 获取 拣货任务id
     *
     * @return batchTaskId 拣货任务id
     */
    public String getBatchTaskId() {
        return this.batchTaskId;
    }

    /**
     * 设置 拣货任务id
     *
     * @param batchTaskId 拣货任务id
     */
    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    /**
     * 获取 拣货任务明细id
     *
     * @return batchTaskItemId 拣货任务明细id
     */
    public String getBatchTaskItemId() {
        return this.batchTaskItemId;
    }

    /**
     * 设置 拣货任务明细id
     *
     * @param batchTaskItemId 拣货任务明细id
     */
    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }
}
