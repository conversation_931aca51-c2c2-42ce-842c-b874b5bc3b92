package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: BatchTaskModDTO
 * @description:
 * @date 2022-09-30 09:20
 */
public class BatchTaskModDTO implements Serializable {

    private List<BatchTaskDTO> list;
    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 获取
     *
     * @return list
     */
    public List<BatchTaskDTO> getList() {
        return this.list;
    }

    /**
     * 设置
     *
     * @param list
     */
    public void setList(List<BatchTaskDTO> list) {
        this.list = list;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }
}
