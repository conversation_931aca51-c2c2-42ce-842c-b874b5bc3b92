package com.yijiupi.himalaya.supplychain.waves.util;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Nullable;

import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.config.UserWarehouseAllocation;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationQuery;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.news.BizUserService;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.news.dto.BizUserDetailDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.*;
import com.yijiupi.himalaya.supplychain.orgsettings.service.*;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.service.config.IWarehouseAllocationConfigService;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IWarehouse2DModelQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.OwnerService;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;

/**
 * 常用缓存类，一小时更新一次
 *
 * <AUTHOR>
 */
@Component
public class GlobalCache {

    private static final int MIN_SIZE = 100;
    private static final int MAX_SIZE = 1000;

    private static final int initialCapacity = 512;
    private static final long maximumSize = initialCapacity * 20;
    private static final long expireHours = 1;

    @Reference
    private IAdminUserService iAdminUserService;
    @Reference
    private IUserAddressService userAddressService;
    @Reference
    private ICarService iCarService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Reference
    private IOrgService iOrgService;
    @Reference
    private OwnerService ownerService;
    @Reference
    private IDriverService iDriverService;
    @Reference
    private IRoutingService iRoutingService;
    @Reference
    private IProductSkuQueryService iProductSkuQueryService;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private IVariableValueService iVariableValueService;
    @Reference
    private IWarehouseAllocationConfigService iWarehouseAllocationConfigService;
    @Reference
    private IWarehouse2DModelQueryService warehouse2DModelQueryService;

    private static Cache<Integer, String> adminCache = Caffeine.newBuilder().initialCapacity(MIN_SIZE)
        .maximumSize(MAX_SIZE).expireAfterWrite(1, TimeUnit.HOURS).build();
    private static Cache<Integer, String> routeCache = Caffeine.newBuilder().initialCapacity(MIN_SIZE)
        .maximumSize(MAX_SIZE).expireAfterWrite(1, TimeUnit.HOURS).build();
    private static Cache<Integer, String> adminMobileCache = Caffeine.newBuilder().initialCapacity(MIN_SIZE)
        .maximumSize(MAX_SIZE).expireAfterWrite(1, TimeUnit.HOURS).build();
    private static Cache<Integer, OrgDTO> cityOrgCache = Caffeine.newBuilder().initialCapacity(MIN_SIZE)
        .maximumSize(MAX_SIZE).expireAfterWrite(1, TimeUnit.HOURS).build();
    private static Cache<Integer, BizUserAddress> addressCache = Caffeine.newBuilder().initialCapacity(MIN_SIZE)
        .maximumSize(MAX_SIZE).expireAfterWrite(1, TimeUnit.HOURS).build();
    /**
     * 会员
     */
    private static Cache<Long, String> partnerUserNameCache = Caffeine.newBuilder().initialCapacity(MIN_SIZE)
        .maximumSize(MAX_SIZE).expireAfterWrite(1, TimeUnit.HOURS).build();
    private static Cache<Integer, Warehouse> warehouseCache = Caffeine.newBuilder().initialCapacity(MIN_SIZE)
        .maximumSize(MAX_SIZE).expireAfterWrite(1, TimeUnit.HOURS).build();
    /**
     * 车辆信息缓存（城市id，该城市下所有车辆集合）
     */
    private static Cache<Integer, List<CarDTO>> carListCache = Caffeine.newBuilder().initialCapacity(MIN_SIZE)
        .maximumSize(MAX_SIZE).expireAfterWrite(1, TimeUnit.HOURS).build();
    /**
     * 车辆信息缓存（车辆id，对应车辆信息）
     */
    private static Cache<Long, CarDTO> carCache = Caffeine.newBuilder().initialCapacity(MIN_SIZE).maximumSize(MAX_SIZE)
        .expireAfterWrite(1, TimeUnit.HOURS).build();
    /**
     * 司机信息缓存(userId,driverDto)
     */
    private static Cache<Integer, DriverDTO> driverCache = Caffeine.newBuilder().initialCapacity(MIN_SIZE)
        .maximumSize(MAX_SIZE).expireAfterWrite(1, TimeUnit.HOURS).build();
    /**
     * 司机列表缓存(cityId,list<driverDto)
     */
    private static Cache<Integer, List<DriverDTO>> driverListCache = Caffeine.newBuilder().initialCapacity(MIN_SIZE)
        .maximumSize(MAX_SIZE).expireAfterWrite(1, TimeUnit.HOURS).build();

    /**
     * ERP二级货主信息缓存（wms二级货主id:erp二级货主id）
     */
    private static Cache<Long, String> erpSecOwnerCache =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(1, TimeUnit.HOURS).build();

    private static Cache<Integer, AdminUser> adminUserCache = Caffeine.newBuilder().initialCapacity(MIN_SIZE)
        .maximumSize(MAX_SIZE).expireAfterWrite(30, TimeUnit.MINUTES).build();

    private static Cache<Integer, AdminUser> adminUserWithAuthCache = Caffeine.newBuilder().initialCapacity(MIN_SIZE)
        .maximumSize(MAX_SIZE).expireAfterWrite(30, TimeUnit.MINUTES).build();

    private static final Cache<Integer, BizUserUserEntity> BIZUSER_LOADING_CACHE = Caffeine.newBuilder()
        // cache的初始容量
        .initialCapacity(100)
        // cache最大缓存数
        .maximumSize(40000)
        // 设置写缓存后n秒钟过期
        .expireAfterWrite(1, TimeUnit.DAYS).build();

    /** 缓存skuId信息 */
    private static Cache<Long, ProductSkuDTO> productSkuCache = Caffeine.newBuilder().initialCapacity(MIN_SIZE)
        .maximumSize(MAX_SIZE).expireAfterWrite(1, TimeUnit.HOURS).build();

    /**
     * 仓库配置
     */
    private static Cache<Integer, WarehouseConfigDTO> warehouseConfigCache = Caffeine.newBuilder()
        .initialCapacity(MIN_SIZE).maximumSize(MAX_SIZE).expireAfterWrite(90, TimeUnit.SECONDS).build();

    private static Cache<Integer, Boolean> robotPickingCache =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(90, TimeUnit.SECONDS).build();

    private static Cache<Integer, Boolean> digitalTagCache =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(90, TimeUnit.SECONDS).build();

    private static Cache<Integer, Boolean> trayPositionLocation =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(90, TimeUnit.SECONDS).build();

    private static Cache<Integer, Boolean> warehouseRealTimePicking =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(90, TimeUnit.SECONDS).build();

    private static Cache<Integer, Boolean> openDigitalTagBatchTaskSortTypeCache =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(90, TimeUnit.SECONDS).build();

    private static Cache<Integer, Boolean> openPickByCustomerCache =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(90, TimeUnit.SECONDS).build();

    private static Cache<Integer, Boolean> palletForSortingMode =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(90, TimeUnit.SECONDS).build();

    private static Cache<Integer, Boolean> warehousePickUpOrdersIndependent =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(5, TimeUnit.MINUTES).build();

    private static Cache<String, Integer> userSplitWarehouseCache =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(5, TimeUnit.MINUTES).build();

    private static Cache<String, List<UserWarehouseAllocation>> userWarehouseAllocationCache = Caffeine.newBuilder()
        .initialCapacity(initialCapacity).maximumSize(maximumSize).expireAfterWrite(5, TimeUnit.MINUTES).build();

    private static Cache<Integer, Boolean> PickByCustomerCache =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(90, TimeUnit.SECONDS).build();

    private static Cache<Integer, Boolean> WarehouseOpenSecondSortCache =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(5, TimeUnit.MINUTES).build();

    // 货位拣货点
    private static final Cache<Integer, List<LocationPointInfoDTO>> locationPointInfoCache =
        Caffeine.newBuilder().initialCapacity(initialCapacity).maximumSize(maximumSize)
            .expireAfterWrite(expireHours, TimeUnit.HOURS).build();

    // 仓库门口起点
    private static final Cache<Integer, LocationPointInfoDTO> warehouseEntranceCache =
        Caffeine.newBuilder().initialCapacity(initialCapacity).maximumSize(maximumSize)
            .expireAfterWrite(expireHours, TimeUnit.HOURS).build();

    // 仓库点位图
    private static final Cache<Integer, List<WarehouseRoutePointDTO>> warehouseRoutePointCache =
        Caffeine.newBuilder().initialCapacity(initialCapacity).maximumSize(maximumSize)
            .expireAfterWrite(expireHours, TimeUnit.HOURS).build();

    // 仓库入口坐标
    private static final Cache<String, boolean[][]> warehouseGridCache =
        Caffeine.newBuilder().initialCapacity(initialCapacity).maximumSize(maximumSize)
            .expireAfterWrite(expireHours, TimeUnit.HOURS).build();

    // 上下楼梯坐标点
    private static final Cache<Integer, List<WarehouseStarirsDTO>> upAndDownStairsCoordinatePointsCache =
        Caffeine.newBuilder().initialCapacity(initialCapacity).maximumSize(maximumSize)
            .expireAfterWrite(expireHours, TimeUnit.HOURS).build();

    private static final String OPEN_TRAY_POSITION_LOCATION_KEY = "isNeedPalletForStock";
    private static final String WAREHOUSE_PICKING_BY_ORDER = "WarehouseRealTimePickingByOrder";
    private static final String WAREHOUSE_DIGITAL_PICKING = "WarehouseDigitalPickingSystem";
    private static final String DIGITAL_BATCH_TASK_OPTIMIZE_SORT = "OpenDigitalTagBatchTaskOptimizeSort";
    private static final String OPEN_PALLET_FOR_SORTING_MODE_KEY = "isNeedPalletForSortingMode";
    private static final String PICK_BY_CUSTOMER = "PickByCustomer";
    // 酒饮按订单分区拣货
    private static final String DRINK_PICK_BY_SORT_GROUP = "DrinkPickBySortGroup";

    public boolean[][] getGridIsPassableWithCache(Integer warehouseId, Integer floor) {
        AssertUtils.notNull(floor, "floor不能为空");
        String key = warehouseId + "_" + floor;
        boolean[][] grid = warehouseGridCache.getIfPresent(key);
        if (grid == null) {
            grid = getGridIsPassable(warehouseId, floor);
            warehouseGridCache.put(key, grid);
        }
        return grid;
    }

    private boolean[][] getGridIsPassable(Integer warehouseId, Integer floor) {
        AssertUtils.notNull(floor, "floor不能为空");
        List<WarehouseRoutePointDTO> warehouseGrid = warehouse2DModelQueryService.getWarehouseRoutePoints(warehouseId);

        warehouseGrid = warehouseGrid.stream().filter(point -> Objects.equals(point.getFloor(), floor))
            .collect(Collectors.toList());

        // 获取grid中X和Y的最大值
        int maxX = warehouseGrid.stream().mapToInt(WarehouseRoutePointDTO::getX).max().orElse(0);
        int maxY = warehouseGrid.stream().mapToInt(WarehouseRoutePointDTO::getY).max().orElse(0);

        // 构建boolean[][] grid
        boolean[][] grid = new boolean[maxX + 1][maxY + 1];

        // 读取仓库布局网格，并将其转换为boolean[][] grid
        for (WarehouseRoutePointDTO routePoint : warehouseGrid) {
            grid[routePoint.getX()][routePoint.getY()] = routePoint.getWalkable();
        }

        return grid;
    }

    /**
     * 仓库网格坐标点
     */
    public List<WarehouseRoutePointDTO> getWarehouseRoutePointWithCache(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        List<WarehouseRoutePointDTO> warehouseRoutePointList = warehouseRoutePointCache.getIfPresent(warehouseId);
        if (warehouseRoutePointList == null) {
            warehouseRoutePointList = warehouse2DModelQueryService.getWarehouseRoutePoints(warehouseId);
            AssertUtils.notEmpty(warehouseRoutePointList, "仓库路由点不存在。warehouseId=" + warehouseId);
            warehouseRoutePointCache.put(warehouseId, warehouseRoutePointList);
        }
        return warehouseRoutePointList;
    }

    /**
     * 拣货点坐标信息
     */
    public List<LocationPointInfoDTO> getLocationPointInfoWithCache(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        List<LocationPointInfoDTO> locationPointInfoList = locationPointInfoCache.getIfPresent(warehouseId);
        if (locationPointInfoList == null) {
            locationPointInfoList = warehouse2DModelQueryService.findWarehouseLocatinPoints(warehouseId);
            AssertUtils.notEmpty(locationPointInfoList, "货位拣货点信息不存在。warehouseId=" + warehouseId);
            locationPointInfoCache.put(warehouseId, locationPointInfoList);
        }
        return locationPointInfoList;
    }

    /**
     * 仓库门口坐标信息
     */
    public LocationPointInfoDTO getWarehouseEntranceWithCache(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        LocationPointInfoDTO locationPointInfo = warehouseEntranceCache.getIfPresent(warehouseId);
        if (locationPointInfo == null) {
            locationPointInfo = warehouse2DModelQueryService.findStarPoint(warehouseId);
            AssertUtils.notNull(locationPointInfo, "仓库门口起点信息不存在。warehouseId=" + warehouseId);
            warehouseEntranceCache.put(warehouseId, locationPointInfo);
        }
        return locationPointInfo;
    }

    /**
     * 上下楼坐标信息
     */
    public List<WarehouseStarirsDTO> findUpAndDownStairsCoordinatePointsWithCache(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        List<WarehouseStarirsDTO> locationPointInfoList =
            upAndDownStairsCoordinatePointsCache.getIfPresent(warehouseId);
        if (CollectionUtils.isEmpty(locationPointInfoList)) {
            locationPointInfoList = warehouse2DModelQueryService.findWarehouseStrairs(warehouseId);
            if (!CollectionUtils.isEmpty(locationPointInfoList)) {
                upAndDownStairsCoordinatePointsCache.put(warehouseId, locationPointInfoList);
            }
        }
        return locationPointInfoList;
    }

    /**
     * 根据cityId返回车辆对象集合
     */
    public List<CarDTO> getCarListCacheBycityId(Integer cityId) {
        if (cityId == null) {
            return null;
        }
        List<CarDTO> carList = carListCache.getIfPresent(cityId);
        if (carList == null || carList.isEmpty()) {
            CarDTO carDto = new CarDTO();
            carDto.setCityId(cityId);
            carList = iCarService.findByConditions(carDto);
            if (carList.isEmpty()) {
                return null;
            }
            carListCache.put(cityId, carList);
        }
        return carList;
    }

    /**
     * 根据carId返回车辆对象
     */
    public CarDTO getCarCacheByCarId(Long carId) {
        if (carId == null) {
            return null;
        }
        CarDTO car = carCache.getIfPresent(carId);
        if (car == null) {
            car = iCarService.findOne(carId);
            if (car == null) {
                return null;
            }
            carCache.put(carId, car);
        }
        return car;
    }

    /**
     * 根据userId返回司机对象
     */
    public DriverDTO getDriverCacheByUserId(Integer userId) {
        if (userId == null) {
            return null;
        }
        DriverDTO driver = driverCache.getIfPresent(userId);
        if (driver == null) {
            driver = iDriverService.findOne(userId);
            if (driver == null) {
                return null;
            }
            driverCache.put(userId, driver);
        }
        return driver;
    }

    /**
     * 获得商业用户公司名
     */
    public String getBizUserCompanyName(Integer userId) {
        if (Objects.isNull(userId)) {
            return Strings.EMPTY;
        }

        BizUserUserEntity entity = BIZUSER_LOADING_CACHE.get(userId, v -> {
            BizUserDetailDTO detail = BizUserService.getBizUserDetail(v);
            if (Objects.isNull(detail)) {
                return null;
            }
            return BizUserUserEntity.convert(detail);
        });
        if (Objects.isNull(entity)) {
            return Strings.EMPTY;
        }

        return entity.userCompanyName;
    }

    public String getPartnerUserName(Long userId) {
        if (userId == null) {
            return "";
        }
        String partnerUserName = partnerUserNameCache.getIfPresent(userId);
        if (partnerUserName == null || "".equals(partnerUserName)) {
            OwnerDTO model = ownerService.getOwnerById(userId);
            if (model == null) {
                return "";
            }
            partnerUserName = model.getOwnerName();
            partnerUserNameCache.put(userId, partnerUserName);
        }
        return partnerUserName;
    }

    public Map<Integer, BizUserUserEntity> batchGetBizUser(List<Integer> userIds) {
        Map<Integer, BizUserUserEntity> params = BIZUSER_LOADING_CACHE.getAllPresent(userIds);
        List<Integer> nonExistsUserIds =
            userIds.stream().filter(m -> !params.containsKey(m)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nonExistsUserIds)) {
            return params;
        }

        List<BizUserDetailDTO> existsList = BizUserService.findBizUserDetailList(nonExistsUserIds);
        if (CollectionUtils.isEmpty(existsList)) {
            return params;
        }
        Map<Integer, BizUserUserEntity> existsParams = existsList.stream().map(BizUserUserEntity::convert)
            .collect(Collectors.toMap(BizUserUserEntity::getUserId, v -> v));
        existsParams.putAll(params);

        return existsParams;
    }

    public String getBizUserName(Integer userId) {
        if (Objects.isNull(userId)) {
            return Strings.EMPTY;
        }

        BizUserUserEntity entity = BIZUSER_LOADING_CACHE.get(userId, v -> {
            BizUserDetailDTO detail = BizUserService.getBizUserDetail(v);
            if (Objects.isNull(detail)) {
                return null;
            }
            return BizUserUserEntity.convert(detail);
        });
        if (Objects.isNull(entity)) {
            return Strings.EMPTY;
        }

        return entity.userName;
    }

    /**
     * 获取admin用户的真实姓名(如果没有就获取nickname)
     */
    public String getAdminTrueName(Integer userId) {
        if (userId == null) {
            return "";
        }
        String trueName = adminCache.getIfPresent(userId);
        if (trueName == null || trueName.isEmpty()) {
            AdminUser model = iAdminUserService.getAdminUserWithoutAuthById(userId);
            if (model == null) {
                return "";
            }
            // 这个地方查不到 model 不会为 null
            trueName = model.getTrueName() == null ? model.getNickname() : model.getTrueName();
            if (StringUtils.hasText(model.getMobileNo()) && StringUtils.hasText(trueName)) {
                adminCache.put(userId, trueName);
                // 顺便把用户的手机号也查出来存着
                adminMobileCache.put(userId, model.getMobileNo());
            } else {
                trueName = "未知";
            }
        }
        return trueName;
    }

    /**
     * 通过用户 id 获取用户名, 当用户 id 为 Number 或者 内容为数字的 String 时将会正常返回用户名, 否则将会返回 null
     *
     * @param userId 用户 id
     * @return 用户 id 对应的用户名, 可能为 null
     */
    @Nullable
    public String getUserName(Object userId) {
        int intUserId;
        if (userId instanceof Number) {
            intUserId = ((Number)userId).intValue();
        } else if (userId instanceof String && (NumberUtils.isDigits(String.valueOf(userId)))) {
            intUserId = Integer.parseInt(userId.toString());
        } else {
            return null;
        }
        return getAdminTrueName(intUserId);
    }

    public AdminUser getAdminUser(Integer userId) {
        if (userId == null) {
            return null;
        }
        AdminUser user = adminUserCache.getIfPresent(userId);
        if (user == null) {
            user = iAdminUserService.getAdminUserWithoutAuthById(userId);
            if (user == null) {
                return null;
            }
            adminUserCache.put(userId, user);
        }
        return user;
    }

    public AdminUser getAdminUserWithAuth(Integer userId) {
        if (userId == null) {
            return null;
        }
        AdminUser user = adminUserWithAuthCache.getIfPresent(userId);
        if (user == null) {
            user = iAdminUserService.getAdminUserWithAuthById(userId);
            if (user == null) {
                return null;
            }
            adminUserWithAuthCache.put(userId, user);
        }
        return user;
    }

    /**
     * 根据addressid获取用户线路
     *
     * @param
     * @return
     */
    public String getUserRoute(Integer addressId, Integer warehouseId) {
        if (addressId == null) {
            return "";
        }
        String routeName = routeCache.getIfPresent(addressId);
        if (routeName == null || "".equals(routeName)) {
            ItemInRoutingDTO model = iRoutingService.findRoutingWithItemByAddressId(addressId, warehouseId);
            if (model == null) {
                return "";
            }
            routeName = model.getRouteName();
            routeCache.put(addressId, routeName);
        }
        return routeName;
    }

    /**
     * 获取admin用户的手机号
     *
     * @param userId
     * @return
     */
    public String getAdminMobile(Integer userId) {
        if (userId == null) {
            return "";
        }
        String mobile = adminMobileCache.getIfPresent(userId);
        if (mobile == null || "".equals(mobile)) {
            AdminUser model = iAdminUserService.getAdminUserWithoutAuthById(userId);
            if (model == null) {
                return "";
            }
            mobile = model.getMobileNo();
            adminMobileCache.put(userId, mobile);
        }
        return mobile;
    }

    public OrgDTO queryJiupiCityById(Integer cityId) {
        if (cityId == null || cityId <= 0) {
            return null;
        }
        OrgDTO cityOrg = cityOrgCache.getIfPresent(cityId);
        if (cityOrg == null || StringUtils.isEmpty(cityOrg.getOrgName())) {
            cityOrg = iOrgService.getOrg(cityId);
            if (cityOrg != null) {
                cityOrgCache.put(cityId, cityOrg);
            }
        }
        return cityOrg;
    }

    public Warehouse getWarehouse(Integer warehouseId) {
        if (warehouseId == null || warehouseId <= 0) {
            return null;
        }
        Warehouse warehouse = warehouseCache.getIfPresent(warehouseId);
        if (warehouse == null) {
            warehouse = iWarehouseQueryService.findWarehouseById(warehouseId);
            if (warehouse != null) {
                warehouseCache.put(warehouseId, warehouse);
            } else {
                return new Warehouse();
            }
        }
        return warehouse;
    }

    public BizUserAddress getAddress(int addressId) {
        BizUserAddress bizUserAddress = addressCache.getIfPresent(Integer.valueOf(addressId));
        if (bizUserAddress == null) {
            ArrayList<Integer> addressIds = new ArrayList<Integer>();
            addressIds.add(addressId);
            List<BizUserAddress> addresses = userAddressService.findBizUserIdByAdressId(addressIds);
            if (addresses != null && !addresses.isEmpty()) {
                bizUserAddress = addresses.get(0);
                addressCache.put(addressId, addresses.get(0));
            }
        }
        return bizUserAddress;
    }

    public static class BizUserUserEntity {
        Integer userId;

        String userName;

        String userCompanyName;

        public Integer getUserId() {
            return userId;
        }

        public String getUserName() {
            return userName;
        }

        public String getUserCompanyName() {
            return userCompanyName;
        }

        public static BizUserUserEntity convert(BizUserDetailDTO detail) {
            BizUserUserEntity entity = new BizUserUserEntity();
            entity.userId = detail.getId();
            entity.userName = detail.getUserName();
            entity.userCompanyName = detail.getUserCompanyName();
            return entity;
        }
    }

    public ProductSkuDTO getProductSkuBySku(Long productSkuId) {
        ProductSkuDTO productSkuDTO = productSkuCache.getIfPresent(productSkuId);
        if (null == productSkuDTO) {
            List<ProductSkuDTO> productSkuDTOList = iProductSkuQueryService.findBySku(Arrays.asList(productSkuId));
            if (!CollectionUtils.isEmpty(productSkuDTOList)) {
                productSkuDTO = productSkuDTOList.get(0);
            }
            productSkuCache.put(productSkuId, productSkuDTO);
        }
        return productSkuDTO;
    }

    public String getErpSecOwnerId(Long wmsSecOwnerId) {
        if (wmsSecOwnerId == null) {
            return null;
        }
        if (wmsSecOwnerId == 1) {
            return "1";
        }
        String erpSecOwner = erpSecOwnerCache.getIfPresent(wmsSecOwnerId);
        if (erpSecOwner == null) {
            OwnerDTO owner = ownerService.getOwnerById(wmsSecOwnerId);
            if (owner == null) {
                return null;
            }
            erpSecOwner = owner.getRefPartnerId();
            erpSecOwnerCache.put(owner.getId(), erpSecOwner);
        }
        return erpSecOwner;
    }

    /**
     * 根据仓库id获取仓库配置
     *
     * @param warehouseId
     * @return
     */
    public WarehouseConfigDTO getWarehouseConfigDTO(Integer warehouseId) {
        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigCache.getIfPresent(warehouseId);
        if (null == warehouseConfigDTO) {
            warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(warehouseId);
            warehouseConfigCache.put(warehouseId, warehouseConfigDTO);
        }
        return warehouseConfigDTO;
    }

    /**
     * 是否开启机器人配置
     *
     * @param warehouseId
     * @return
     */
    public Boolean checkWarehouseIsRobotPicking(Integer warehouseId) {
        Boolean openRobotPicking = robotPickingCache.getIfPresent(warehouseId);
        if (Objects.isNull(openRobotPicking)) {
            openRobotPicking = warehouseConfigService.checkWarehouseIsRobotPicking(warehouseId);
            robotPickingCache.put(warehouseId, openRobotPicking);
        }
        return openRobotPicking;
    }

    /**
     * 是否开启托盘位
     * 
     * @param warehouseId
     * @return
     */
    public Boolean getOpenTrayPositionLocation(Integer warehouseId) {
        Boolean openRobotPicking = trayPositionLocation.getIfPresent(warehouseId);
        if (Objects.isNull(openRobotPicking)) {
            openRobotPicking = getOpenTrayPositionLocationByWarehouseId(warehouseId);
            trayPositionLocation.put(warehouseId, openRobotPicking);
        }
        return openRobotPicking;
    }

    /**
     * 检查仓库是否是否开启托盘位
     */
    private boolean getOpenTrayPositionLocationByWarehouseId(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");

        VariableValueQueryDTO varQuery = new VariableValueQueryDTO();
        varQuery.setVariableKey(OPEN_TRAY_POSITION_LOCATION_KEY);
        varQuery.setWarehouseId(warehouseId);
        if (com.alibaba.dubbo.common.utils.StringUtils.isEmpty(varQuery.getVariableKey())
            && com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(varQuery.getVariableKeyList())) {
            throw new BusinessValidateException("参数key不能为空");
        }
        VariableDefAndValueDTO keyConfig = iVariableValueService.detailVariable(varQuery);
        if (keyConfig == null || com.alibaba.dubbo.common.utils.StringUtils.isEmpty(keyConfig.getVariableData())) {
            return false;
        }
        return "true".equals(keyConfig.getVariableData());
    }

    /**
     * 检查仓库是否开启按单分拣
     */
    public boolean getWarehouseRealTimePickingByOrder(Integer warehouseId) {
        Boolean openRobotPicking = warehouseRealTimePicking.getIfPresent(warehouseId);
        if (Objects.isNull(openRobotPicking)) {
            openRobotPicking = getWarehouseRealTimePickingByOrderByWarehouseId(warehouseId);
            warehouseRealTimePicking.put(warehouseId, openRobotPicking);
        }
        return openRobotPicking;
    }

    /**
     * 检查仓库是否开启按单分拣
     */
    private boolean getWarehouseRealTimePickingByOrderByWarehouseId(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");

        VariableValueQueryDTO varQuery = new VariableValueQueryDTO();
        varQuery.setVariableKey(WAREHOUSE_PICKING_BY_ORDER);
        varQuery.setWarehouseId(warehouseId);
        if (com.alibaba.dubbo.common.utils.StringUtils.isEmpty(varQuery.getVariableKey())
            && com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(varQuery.getVariableKeyList())) {
            throw new BusinessValidateException("参数key不能为空");
        }
        VariableDefAndValueDTO keyConfig = iVariableValueService.detailVariable(varQuery);
        if (keyConfig == null || com.alibaba.dubbo.common.utils.StringUtils.isEmpty(keyConfig.getVariableData())) {
            return false;
        }
        return "true".equals(keyConfig.getVariableData());
    }

    /**
     * 是否开启机器人配置
     *
     * @param warehouseId
     * @return
     */
    public boolean openDigitalTag(Integer warehouseId) {
        Boolean digitalTag = digitalTagCache.getIfPresent(warehouseId);
        if (Objects.isNull(digitalTag)) {
            digitalTag = openDigitalPickingSystem(warehouseId);
            digitalTagCache.put(warehouseId, digitalTag);
        }

        return digitalTag;
    }

    /**
     * 是否开启电子标签分拣
     *
     * @param warehouseId
     * @return
     */
    private boolean openDigitalPickingSystem(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");

        VariableValueQueryDTO varQuery = new VariableValueQueryDTO();
        varQuery.setVariableKey(WAREHOUSE_DIGITAL_PICKING);
        varQuery.setWarehouseId(warehouseId);
        if (com.alibaba.dubbo.common.utils.StringUtils.isEmpty(varQuery.getVariableKey())
            && com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(varQuery.getVariableKeyList())) {
            throw new BusinessValidateException("参数key不能为空");
        }
        VariableDefAndValueDTO keyConfig = iVariableValueService.detailVariable(varQuery);
        if (keyConfig == null || com.alibaba.dubbo.common.utils.StringUtils.isEmpty(keyConfig.getVariableData())) {
            return false;
        }
        return "true".equals(keyConfig.getVariableData());
    }

    private boolean openWarehousePickUpOrdersIndependent(UserWarehouseAllocation userWarehouseAllocation) {

        if (Objects.isNull(userWarehouseAllocation)) {
            return Boolean.FALSE;
        }

        if (Objects.isNull(userWarehouseAllocation.getOpenReceiveOrderBySelf())) {
            return Boolean.FALSE;
        }

        if (ConditionStateEnum.否.getType().equals(userWarehouseAllocation.getOpenReceiveOrderBySelf())) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    public List<UserWarehouseAllocation> findUserWarehouseAllocationsCache(Integer warehouseId, Integer userId) {
        // 使用userWarehouseAllocationCache
        String key = warehouseId + "_" + userId;
        List<UserWarehouseAllocation> userWarehouseAllocations = userWarehouseAllocationCache.getIfPresent(key);
        if (CollectionUtils.isEmpty(userWarehouseAllocations)) {
            WarehouseAllocationQuery query = WarehouseAllocationQuery.of(Collections.singleton(warehouseId), userId);
            userWarehouseAllocations = iWarehouseAllocationConfigService.queryConfigByUser(query);
            if (!CollectionUtils.isEmpty(userWarehouseAllocations)) {
                userWarehouseAllocationCache.put(key, userWarehouseAllocations);
            }
        }
        return userWarehouseAllocations;
    }

    /**
     * 是否开启电子标签分拣
     *
     * @param warehouseId
     * @return
     */
    public boolean isOpenDigitalTagBatchTaskOptimizeSort(Integer warehouseId) {
        Boolean batchTaskSortType = openDigitalTagBatchTaskSortTypeCache.getIfPresent(warehouseId);
        if (Objects.isNull(batchTaskSortType)) {
            batchTaskSortType = openDigitalTagBatchTaskOptimizeSort(warehouseId);
            openDigitalTagBatchTaskSortTypeCache.put(warehouseId, batchTaskSortType);
        }

        return batchTaskSortType;
    }

    /**
     * 是否开启电子标签分拣
     *
     * @param warehouseId
     * @return
     */
    private boolean openDigitalTagBatchTaskOptimizeSort(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");

        VariableValueQueryDTO varQuery = new VariableValueQueryDTO();
        varQuery.setVariableKey(DIGITAL_BATCH_TASK_OPTIMIZE_SORT);
        varQuery.setWarehouseId(warehouseId);
        if (com.alibaba.dubbo.common.utils.StringUtils.isEmpty(varQuery.getVariableKey())
            && com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(varQuery.getVariableKeyList())) {
            throw new BusinessValidateException("参数key不能为空");
        }
        VariableDefAndValueDTO keyConfig = iVariableValueService.detailVariable(varQuery);
        if (keyConfig == null || com.alibaba.dubbo.common.utils.StringUtils.isEmpty(keyConfig.getVariableData())) {
            return false;
        }
        return "true".equals(keyConfig.getVariableData());
    }

    /**
     * 是否开启接力分拣模式
     *
     * @param warehouseId
     * @return
     */
    public Boolean getOpenPalletForSortingMode(Integer warehouseId) {
        Boolean openPalletForSortingMode = palletForSortingMode.getIfPresent(warehouseId);
        if (Objects.isNull(openPalletForSortingMode)) {
            openPalletForSortingMode = getOpenPalletForSortingModeByWarehouseId(warehouseId);
            palletForSortingMode.put(warehouseId, openPalletForSortingMode);
        }
        return openPalletForSortingMode;
    }

    /**
     * 检查仓库是否开启接力分拣模式
     */
    private boolean getOpenPalletForSortingModeByWarehouseId(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");

        VariableValueQueryDTO varQuery = new VariableValueQueryDTO();
        varQuery.setVariableKey(OPEN_PALLET_FOR_SORTING_MODE_KEY);
        varQuery.setWarehouseId(warehouseId);
        if (com.alibaba.dubbo.common.utils.StringUtils.isEmpty(varQuery.getVariableKey())
            && com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(varQuery.getVariableKeyList())) {
            throw new BusinessValidateException("参数key不能为空");
        }
        VariableDefAndValueDTO keyConfig = iVariableValueService.detailVariable(varQuery);
        if (keyConfig == null || com.alibaba.dubbo.common.utils.StringUtils.isEmpty(keyConfig.getVariableData())) {
            return false;
        }
        return "true".equals(keyConfig.getVariableData());
    }

    /**
     * 是否开启分仓配置
     *
     * @param warehouseId
     * @return
     */
    public boolean openWarehouseSeparateAttributeConfig(Integer warehouseId) {
        Boolean config = iWarehouseAllocationConfigService.isEnabledWarehouseSplit(warehouseId);
        return config;
    }

    /**
     * 是否开启按客户拣货
     *
     * @param warehouseId
     * @return
     */
    public Boolean getPickByCustomerFromCache(Integer warehouseId) {
        Boolean pickByCustomer = PickByCustomerCache.getIfPresent(warehouseId);
        if (Objects.isNull(pickByCustomer)) {
            pickByCustomer = getPickByCustomer(warehouseId);
            PickByCustomerCache.put(warehouseId, pickByCustomer);
        }
        return pickByCustomer;
    }

    /**
     * 是否开启按客户拣货
     */
    private boolean getPickByCustomer(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");

        VariableValueQueryDTO varQuery = new VariableValueQueryDTO();
        varQuery.setVariableKey(PICK_BY_CUSTOMER);
        varQuery.setWarehouseId(warehouseId);
        if (com.alibaba.dubbo.common.utils.StringUtils.isEmpty(varQuery.getVariableKey())
            && com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(varQuery.getVariableKeyList())) {
            throw new BusinessValidateException("参数key不能为空");
        }
        VariableDefAndValueDTO keyConfig = iVariableValueService.detailVariable(varQuery);
        if (keyConfig == null || com.alibaba.dubbo.common.utils.StringUtils.isEmpty(keyConfig.getVariableData())) {
            return false;
        }
        return "true".equals(keyConfig.getVariableData());
    }

    /**
     * 是否开启酒批二次分拣
     */
    public Boolean isOpenSecondSortFromCache(Integer warehouseId) {
        Boolean config = WarehouseOpenSecondSortCache.getIfPresent(warehouseId);
        if (Objects.isNull(config)) {
            config = isOpenSecondSort(warehouseId);
            WarehouseOpenSecondSortCache.put(warehouseId, config);
        }

        return config;
    }

    /**
     * 是否开启酒批二次分拣
     */
    public Boolean isOpenSecondSort(Integer warehouseId) {
        VariableValueQueryDTO configQuery = new VariableValueQueryDTO();
        configQuery.setVariableKey("SECOND_SORT");
        configQuery.setWarehouseId(warehouseId);
        VariableDefAndValueDTO configValueDTO = iVariableValueService.detailVariable(configQuery);
        if (configValueDTO == null
            || com.alibaba.dubbo.common.utils.StringUtils.isEmpty(configValueDTO.getVariableData())) {
            return false;
        }
        return Boolean.valueOf(configValueDTO.getVariableData());
    }

    /**
     * 是否开启酒饮按订单分区拣货
     */
    public Boolean isDrinkPickBySortGroup(Integer warehouseId) {
        VariableValueQueryDTO configQuery = new VariableValueQueryDTO();
        configQuery.setVariableKey(DRINK_PICK_BY_SORT_GROUP);
        configQuery.setWarehouseId(warehouseId);
        VariableDefAndValueDTO configValueDTO = iVariableValueService.detailVariable(configQuery);
        if (configValueDTO == null
            || com.alibaba.dubbo.common.utils.StringUtils.isEmpty(configValueDTO.getVariableData())) {
            return false;
        }
        return Boolean.valueOf(configValueDTO.getVariableData());
    }

}
