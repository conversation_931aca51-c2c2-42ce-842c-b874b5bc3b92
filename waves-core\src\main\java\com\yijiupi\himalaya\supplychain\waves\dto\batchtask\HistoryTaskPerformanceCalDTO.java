package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/25
 */
public class HistoryTaskPerformanceCalDTO implements Serializable {
    /**
     * 开始时间
     */
    private String timeS;
    /**
     * 结束时间
     */
    private String timeE;
    /**
     * 仓库id列表
     */
    private List<Integer> warehouseIds;

    /**
     * 获取 开始时间
     *
     * @return timeS 开始时间
     */
    public String getTimeS() {
        return this.timeS;
    }

    /**
     * 设置 开始时间
     *
     * @param timeS 开始时间
     */
    public void setTimeS(String timeS) {
        this.timeS = timeS;
    }

    /**
     * 获取 结束时间
     *
     * @return timeE 结束时间
     */
    public String getTimeE() {
        return this.timeE;
    }

    /**
     * 设置 结束时间
     *
     * @param timeE 结束时间
     */
    public void setTimeE(String timeE) {
        this.timeE = timeE;
    }

    /**
     * 获取 仓库id列表
     *
     * @return warehouseIds 仓库id列表
     */
    public List<Integer> getWarehouseIds() {
        return this.warehouseIds;
    }

    /**
     * 设置 仓库id列表
     *
     * @param warehouseIds 仓库id列表
     */
    public void setWarehouseIds(List<Integer> warehouseIds) {
        this.warehouseIds = warehouseIds;
    }
}
