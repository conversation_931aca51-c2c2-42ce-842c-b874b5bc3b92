package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
public class ScanOrderPackageInfoResultDTO implements Serializable {

    /**
     * 订单序号
     */
    private Integer orderSequence;
    /**
     * 箱号
     */
    private String boxCodeNo;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 总箱数
     */
    private Integer totalBoxCount;
    /**
     * 订单号
     */
    private List<String> refOrderNoList;
    /**
     * 出库单id
     */
    private List<Long> orderIdList;
    /**
     * 拣货任务id列表
     */
    private List<String> batchTaskIds;
    /**
     * 出库位id
     */
    private Long locationId;
    /**
     * 出库位名称
     */
    private String locationName;
    /**
     * 托盘数量
     */
    private Integer palletCount;
    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 订单和拣货任务关系列表
     */
    private List<ScanOrderPackageInfoBatchTaskResultDTO> orderBatchTaskInfoList;

    /**
     * 获取 订单序号
     *
     * @return orderSequence 订单序号
     */
    public Integer getOrderSequence() {
        return this.orderSequence;
    }

    /**
     * 设置 订单序号
     *
     * @param orderSequence 订单序号
     */
    public void setOrderSequence(Integer orderSequence) {
        this.orderSequence = orderSequence;
    }

    /**
     * 获取 箱号
     *
     * @return boxCodeNo 箱号
     */
    public String getBoxCodeNo() {
        return this.boxCodeNo;
    }

    /**
     * 设置 箱号
     *
     * @param boxCodeNo 箱号
     */
    public void setBoxCodeNo(String boxCodeNo) {
        this.boxCodeNo = boxCodeNo;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 总箱数
     *
     * @return totalBoxCount 总箱数
     */
    public Integer getTotalBoxCount() {
        return this.totalBoxCount;
    }

    /**
     * 设置 总箱数
     *
     * @param totalBoxCount 总箱数
     */
    public void setTotalBoxCount(Integer totalBoxCount) {
        this.totalBoxCount = totalBoxCount;
    }

    /**
     * 获取 拣货任务id列表
     *
     * @return batchTaskIds 拣货任务id列表
     */
    public List<String> getBatchTaskIds() {
        return this.batchTaskIds;
    }

    /**
     * 设置 拣货任务id列表
     *
     * @param batchTaskIds 拣货任务id列表
     */
    public void setBatchTaskIds(List<String> batchTaskIds) {
        this.batchTaskIds = batchTaskIds;
    }

    /**
     * 获取 出库位id
     *
     * @return locationId 出库位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 出库位id
     *
     * @param locationId 出库位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 出库位名称
     *
     * @return locationName 出库位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 出库位名称
     *
     * @param locationName 出库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 订单号
     *
     * @return refOrderNoList 订单号
     */
    public List<String> getRefOrderNoList() {
        return this.refOrderNoList;
    }

    /**
     * 设置 订单号
     *
     * @param refOrderNoList 订单号
     */
    public void setRefOrderNoList(List<String> refOrderNoList) {
        this.refOrderNoList = refOrderNoList;
    }

    /**
     * 获取 出库单id
     *
     * @return orderIdList 出库单id
     */
    public List<Long> getOrderIdList() {
        return this.orderIdList;
    }

    /**
     * 设置 出库单id
     *
     * @param orderIdList 出库单id
     */
    public void setOrderIdList(List<Long> orderIdList) {
        this.orderIdList = orderIdList;
    }

    /**
     * 获取 订单和拣货任务关系列表
     *
     * @return orderBatchTaskInfoList 订单和拣货任务关系列表
     */
    public List<ScanOrderPackageInfoBatchTaskResultDTO> getOrderBatchTaskInfoList() {
        return this.orderBatchTaskInfoList;
    }

    /**
     * 设置 订单和拣货任务关系列表
     *
     * @param orderBatchTaskInfoList 订单和拣货任务关系列表
     */
    public void setOrderBatchTaskInfoList(List<ScanOrderPackageInfoBatchTaskResultDTO> orderBatchTaskInfoList) {
        this.orderBatchTaskInfoList = orderBatchTaskInfoList;
    }

    /**
     * 获取 托盘数量
     *
     * @return palletCount 托盘数量
     */
    public Integer getPalletCount() {
        return this.palletCount;
    }

    /**
     * 设置 托盘数量
     *
     * @param palletCount 托盘数量
     */
    public void setPalletCount(Integer palletCount) {
        this.palletCount = palletCount;
    }

    /**
     * 获取 店铺名称
     *
     * @return shopName 店铺名称
     */
    public String getShopName() {
        return this.shopName;
    }

    /**
     * 设置 店铺名称
     *
     * @param shopName 店铺名称
     */
    public void setShopName(String shopName) {
        this.shopName = shopName;
    }
}
