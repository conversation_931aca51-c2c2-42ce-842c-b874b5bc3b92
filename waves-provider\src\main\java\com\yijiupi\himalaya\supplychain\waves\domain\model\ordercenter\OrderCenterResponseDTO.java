package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/1
 */
public class OrderCenterResponseDTO<T> {
    /**
     * 响应码
     */
    private Integer code;
    /**
     * 消息
     */
    private String msg;
    /**
     * 响应体
     */
    private List<T> data;
    /**
     * 异常ID
     */
    private String exceptionId;
    /**
     * 轨迹栈
     */
    private String stacktrace;

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getExceptionId() {
        return exceptionId;
    }

    public void setExceptionId(String exceptionId) {
        this.exceptionId = exceptionId;
    }

    public String getStacktrace() {
        return stacktrace;
    }

    public void setStacktrace(String stacktrace) {
        this.stacktrace = stacktrace;
    }
}
