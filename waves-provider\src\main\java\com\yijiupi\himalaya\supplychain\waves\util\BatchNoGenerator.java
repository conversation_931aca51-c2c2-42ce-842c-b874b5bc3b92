package com.yijiupi.himalaya.supplychain.waves.util;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2018-03-24
 */
@Component
public class BatchNoGenerator {

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;
    private static final Logger LOG = LoggerFactory.getLogger(BatchNoGenerator.class);

    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyMMdd");

    /**
     * 获取唯一Id
     *
     * @param key     warehouseId
     * @param hashKey 单据类型+日期
     * @return
     * @throws BusinessException
     */
    private Long incrementHash(String key, String hashKey) throws BusinessException {
        try {
            // dela 增加量（不传采用1）
            // Long expire = redisTemplate.getExpire(key);
            // LOG.info(String.format("Id生成-Key:%s,HashKey:%s,value:%s,Expired:%s", key, hashKey, increment, expire));
            return redisTemplate.opsForHash().increment(key, hashKey, 1);
        } catch (Exception e) {
            // redis宕机时采用uuid的方式生成唯一id
            int first = new Random(10).nextInt(8) + 1;
            int randNo = UUID.randomUUID().toString().hashCode();
            if (randNo < 0) {
                randNo = -randNo;
            }
            return Long.valueOf(first + String.format("%16d", randNo).trim());
        }
    }
    //
    // public String generatorByType(String orderType) {
    // return generator(null, orderType);
    // }

    public String generator(Integer warehouseId, String orderType) {
        DateFormat dateFormat = new SimpleDateFormat("yyMMdd");
        Date now = new Date();
        String dateStr = dateFormat.format(now);
        Long tmpId = incrementHash(String.format("supf:stockorder:IdGenerator:%s", warehouseId),
                String.format("%s%s", orderType, dateStr));
        return String.format("%s%s%s%05d", StringUtils.isNotEmpty(orderType) ? orderType : "", warehouseId, dateStr,
                tmpId);
    }

    public String generatorId() {
        return generator(20, null);
    }

    /**
     * 波次名称：年+月+日+时+份+3位流水
     *
     * @return
     */
    public String generatorBatchName(Integer warehouseId) {
        DateFormat dateFormat = new SimpleDateFormat("yyMMdd");
        DateFormat timeFormat = new SimpleDateFormat("yyyyMMddHHmm");
        Date now = new Date();
        String dateStr = dateFormat.format(now);
        Long tmpId = incrementHash(String.format("supf:stockorder:IdGenerator:%s", warehouseId), dateStr);
        return String.format("%s%03d", timeFormat.format(now), tmpId);
    }

    /**
     * 获取自提点的周转箱编号，同时指定过期时间
     */
    public String getAddressContainerNo(Integer warehouseId, Date date, Integer addressId) {
        String containerNoKey = String.format("supc:batchtask:containerNo:%s", warehouseId);
        Long expireTime = redisTemplate.getExpire(containerNoKey);
        LOG.info("自提点的周转箱编号过期时间：{}s", expireTime);
        // 若自提点已经存在，直接返回
        if (redisTemplate.opsForHash().hasKey(containerNoKey, addressId)) {
            return String.valueOf(redisTemplate.opsForHash().get(containerNoKey, addressId));
        } else {
            // 不存在的话，获取当前最大编号
            String hashKey = "maxNo";
            Long containerNo = redisTemplate.opsForHash().increment(containerNoKey, hashKey, 1);
            if (containerNo == null) {
                throw new BusinessValidateException("获取自提点对应的周转箱编号失败！");
            }
            // 第一次新增要设置过期时间
            if (containerNo == 1) {
                Long milliseconds = date.getTime() - System.currentTimeMillis();
                // 当前时间超过今日截止时间时，截止时间往后加一天
                if (milliseconds < 0) {
                    date = LocalDateTimeUtil.getAfterDay(date);
                    LOG.info("当前时间超过今日截止时间时，截止时间往后加一天: {}", date);
                    milliseconds = date.getTime() - System.currentTimeMillis();
                }
                LOG.info("自提点的周转箱编号第一次新增时设置过期时间：{}ms", milliseconds);
                redisTemplate.expire(containerNoKey, milliseconds, TimeUnit.MILLISECONDS);
            }

            // 保存自提点对应的周转箱编号
            redisTemplate.opsForHash().putIfAbsent(containerNoKey, addressId, containerNo);
            return containerNo.toString();
        }
    }

    /**
     * 生成容器下标
     *
     * @param warehouseId
     * @param locationName
     * @return
     */
    public Long containerGenerator(Integer warehouseId, String locationName) {
        String today = LocalDate.now().format(FORMATTER);
        Long tmpId = incrementHash(String.format("supf:stockorder:IdGenerator:%s", warehouseId),
                String.format("%s%s", today, locationName));
        return tmpId;
    }
}
