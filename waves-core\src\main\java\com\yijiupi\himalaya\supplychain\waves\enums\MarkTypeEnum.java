package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 标记类型 1-缺货发货，10-全部配送，11-部分配送，12-配送失败
 *
 * <AUTHOR>
 * @Date 2022/5/9
 */
public enum MarkTypeEnum {
    /**
     * 缺货发货
     */
    缺货发货((byte)1),
    /**
     * 全部配送
     */
    全部配送((byte)10),
    /**
     * 部分配送
     */
    部分配送((byte)11),
    /**
     * 配送失败
     */
    配送失败((byte)12);

    /**
     * type
     */
    private Byte type;

    MarkTypeEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

}
