package com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.orgsettings.service.ICityAreaService;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IRoutingService;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.OutLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.RecommendOutLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationRuleEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationRuleManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.address.UserAddressTransferBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.BatchCreateUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.OutStockLocationRuleBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskKindOfPickingConstants;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
@Service
public class RecommendOutLocationBL {

    @Autowired
    private UserAddressTransferBL userAddressTransferBL;

    @Reference
    private ILocationRuleManageService iLocationRuleManageService;
    @Reference
    private IRoutingService iRoutingService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Reference
    private ICityAreaService iCityAreaService;

    private static final Logger LOG = LoggerFactory.getLogger(RecommendOutLocationBL.class);

    /**
     * 根据出库单获取推荐出库位 <br />
     * 抄以前代码，以前代码逻辑就有问题。既然有了addressId，为啥还搞什么key。。。 <br />
     * 当时需求应该做错了 <br />
     * FIXME 改addressId
     */
    @Deprecated
    public List<LocationRuleDTO> getRecommendOutLocation(Integer warehouseId, List<OutStockOrderPO> orders,
        Long deliveryCarId, Long logisticsCompanyId) {
        Map<Integer, Integer> addressMap = userAddressTransferBL.getTransferAddressIdByOrder(warehouseId, orders);
        return getRecommendOutLocation(warehouseId, orders, deliveryCarId, logisticsCompanyId, addressMap);
    }

    @Deprecated
    public List<LocationRuleDTO> getRecommendOutLocation(Integer warehouseId, List<OutStockOrderPO> orders,
        Long deliveryCarId, Long logisticsCompanyId, Map<Integer, Integer> addressMap) {
        try {
            // 2.推荐出库位查询条件
            List<RecommendOutLocationQueryDTO> locRuleRequest = new ArrayList<>();
            for (OutStockOrderPO order : orders) {
                RecommendOutLocationQueryDTO locRule = new RecommendOutLocationQueryDTO();
                // 快递物流公司、配送车辆
                if (logisticsCompanyId != null) {
                    locRule.setRuleId(String.valueOf(logisticsCompanyId));
                } else if (deliveryCarId != null) {
                    // 车辆ID
                    locRule.setRuleId(String.valueOf(deliveryCarId));
                }
                // 地址
                locRule.setAddressId(addressMap.getOrDefault(order.getAddressId(), order.getAddressId()).longValue());
                // 片区
                locRule.setAreaId(order.getAreaId());
                locRule.setRouteId(order.getRouteId());
                // 行政区域
                locRule.setProvince(order.getProvince());
                locRule.setCity(order.getCity());
                locRule.setDistrict(order.getCounty());
                locRule.setStreet(order.getStreet());
                locRule.setCarId(deliveryCarId);
                // 仓库
                locRule.setWarehouseId(order.getWarehouseId());
                locRuleRequest.add(locRule);
                // 记录查询条件
                order.setOutLocationQueryKey(OutStockLocationHelper.getOutLocationQueryKey(locRule));
            }
            LOG.info("查询推荐出库位，请求：{}", JSON.toJSONString(locRuleRequest));
            // 3.查询推荐出库位
            List<LocationRuleDTO> locationRuleDTOList = iLocationRuleManageService.getOutStockLocation(locRuleRequest);
            LOG.info("查询出推荐出库位，结果为:{}", JSON.toJSONString(locationRuleDTOList));
            return locationRuleDTOList;
        } catch (Exception ex) {
            LOG.info("查询推荐出库位异常", ex);
        }
        return new ArrayList<>();
    }

    /**
     * 获取波次对应线路或片区的出库位 直接copy老代码
     */
    public List<LocationInfoDTO> getLocationByAreaOrRoute(BatchPO batchPO) {
        if (null == batchPO) {
            return Collections.emptyList();
        }
        Integer warehouseId = batchPO.getWarehouseId();
        LOG.info("灰度获取出库位，波次信息为：{}", JSON.toJSONString(batchPO));
        return getGrayLocationInfo(batchPO);

        // List<LocationInfoDTO> locationInfoDTOList = new ArrayList<>();
        // // 按区域
        // if (isBatchHasArea(batchPO)) {
        // CityAreaDTO findOne = iCityAreaService.findOne(Long.valueOf(batchPO.getAreaId()));
        // LOG.info("获取Area详细信息：{}", JSON.toJSONString(findOne));
        // if (findOne != null && !CollectionUtils.isEmpty(findOne.getLocationList())) {
        // findOne.getLocationList().forEach(p -> {
        // LocationInfoDTO locationDTO = new LocationInfoDTO();
        // locationDTO.setLocationId(p.getId());
        // locationDTO.setLocationName(p.getName());
        // locationInfoDTOList.add(locationDTO);
        // });
        // }
        //
        // return locationInfoDTOList;
        // }
        // // 按线路
        // if (isBatchHasRoute(batchPO)) {
        // RoutingDTO findOne = iRoutingService.findOne(Long.valueOf(batchPO.getRouteId()));
        // LOG.info(String.format("获取Route详细信息：%s", JSON.toJSONString(findOne)));
        // if (findOne != null && !CollectionUtils.isEmpty(findOne.getLocationList())) {
        // findOne.getLocationList().forEach(p -> {
        // LocationInfoDTO locationDTO = new LocationInfoDTO();
        // locationDTO.setLocationId(p.getId());
        // locationDTO.setLocationName(p.getName());
        // locationInfoDTOList.add(locationDTO);
        // });
        // }
        // LOG.info("灰度按线路获取出库位，结果为：{} ，波次为：{}", JSON.toJSONString(locationInfoDTOList), JSON.toJSONString(batchPO));
        // return locationInfoDTOList;
        // }
        // return locationInfoDTOList;
    }

    private List<LocationInfoDTO> getGrayLocationInfo(BatchPO batchPO) {
        try {
            if (BatchCreateUtils.isBatchHasRoute(batchPO)) {
                return getGrayLocationInfo(Collections.singletonList(batchPO.getRouteId()), batchPO.getWarehouseId(),
                    LocationRuleEnum.LINE.getCode());
            }
            if (BatchCreateUtils.isBatchHasArea(batchPO)) {
                return getGrayLocationInfo(Collections.singletonList(batchPO.getAreaId()), batchPO.getWarehouseId(),
                    LocationRuleEnum.DISTRICT.getCode());
            }

            return Collections.emptyList();
        } catch (Exception e) {
            LOG.info("获取出库位失败" + JSON.toJSONString(batchPO), e);
            return Collections.emptyList();
        }
    }

    private List<LocationInfoDTO> getGrayLocationInfo(List<String> ruleIds, Integer warehouseId, Integer code) {
        OutLocationQueryDTO outLocationQueryDTO = new OutLocationQueryDTO();
        outLocationQueryDTO.setCode(code);
        outLocationQueryDTO.setWarehouseId(warehouseId);
        outLocationQueryDTO.setRuleIdList(ruleIds);
        List<LocationRuleDTO> locationRuleDTOList = iLocationRuleManageService.getOutLocation(outLocationQueryDTO);
        return convert(locationRuleDTOList);
    }

    public Map<String, OutStockLocationRuleBO> getGrayLocationInfoMap(List<String> ruleIds, Integer warehouseId,
        Integer code) {
        OutLocationQueryDTO outLocationQueryDTO = new OutLocationQueryDTO();
        outLocationQueryDTO.setCode(code);
        outLocationQueryDTO.setWarehouseId(warehouseId);
        outLocationQueryDTO.setRuleIdList(ruleIds);
        List<LocationRuleDTO> locationRuleDTOList = iLocationRuleManageService.getOutLocation(outLocationQueryDTO);
        List<OutStockLocationRuleBO> locationInfoDTOList = convertBO(locationRuleDTOList);
        if (CollectionUtils.isEmpty(locationInfoDTOList)) {
            return Collections.emptyMap();
        }

        return locationInfoDTOList.stream()
            .collect(Collectors.toMap(OutStockLocationRuleBO::getRuleId, v -> v, (v1, v2) -> {
                LOG.warn("线路的出库位重复，线路id:{}; 出库位1：{};出库位2: {}", v1.getRuleId(), v1.getLocationName(),
                    v2.getLocationName());
                return v1;
            }));
    }

    private List<OutStockLocationRuleBO> convertBO(List<LocationRuleDTO> locationRuleDTOList) {
        if (CollectionUtils.isEmpty(locationRuleDTOList)) {
            return Collections.emptyList();
        }

        return locationRuleDTOList.stream().map(ruleDTO -> {
            OutStockLocationRuleBO bo = new OutStockLocationRuleBO();
            bo.setLocationId(ruleDTO.getLocationId());
            bo.setLocationName(ruleDTO.getLocationName());
            bo.setRuleId(ruleDTO.getRuleId());
            return bo;
        }).collect(Collectors.toList());
    }

    private List<LocationInfoDTO> convert(List<LocationRuleDTO> locationRuleDTOList) {
        if (CollectionUtils.isEmpty(locationRuleDTOList)) {
            return Collections.emptyList();
        }

        return locationRuleDTOList.stream().map(ruleDTO -> {
            LocationInfoDTO locationInfoDTO = new LocationInfoDTO();
            locationInfoDTO.setLocationId(ruleDTO.getLocationId());
            locationInfoDTO.setLocationName(ruleDTO.getLocationName());
            return locationInfoDTO;
        }).collect(Collectors.toList());
    }

    public void setOutLocationByGroup(BatchTaskPO batchTaskPO, List<OutStockOrderPO> outStockOrderPOList,
        WavesStrategyBO wavesStrategyDTO, WaveCreateDTO createDTO) {
        try {
            if (createDTO.getSowNo() != null) {
                LOG.info("有播种任务的拣货任务不分配出库位：{}", createDTO.getSowNo());
                return;
            }

            boolean isAllHaveAddress = outStockOrderPOList.stream().anyMatch(m -> Objects.isNull(m.getAddressId()));
            if (BooleanUtils.isTrue(isAllHaveAddress)) {
                LOG.info("有地址信息为空！");
                return;
            }

            // if (!BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_USER
            // .equals(wavesStrategyDTO.getBatchAttrSettingWay())) {
            // return;
            // }

            List<LocationRuleDTO> locationRuleDTOList = getRecommendOutLocation(batchTaskPO.getWarehouseId(),
                outStockOrderPOList, wavesStrategyDTO.getDeliveryCarId(), wavesStrategyDTO.getLogisticsCompanyId());
            LOG.info("查询推荐出库位结果，{}", JSON.toJSONString(locationRuleDTOList));
            if (CollectionUtils.isEmpty(locationRuleDTOList)) {
                return;
            }
            LocationRuleDTO locationRuleDTO = locationRuleDTOList.get(0);
            batchTaskPO.setToLocationId(locationRuleDTO.getLocationId());
            batchTaskPO.setToLocationName(locationRuleDTO.getLocationName());
            batchTaskPO.setRemark(locationRuleDTO.getRuleName());
        } catch (Exception e) {
            LOG.warn("按订单拣货-订单分组出库位设置失败, 入参 " + JSON.toJSONString(batchTaskPO), e);
        }
    }

    public Map<String, OutStockLocationRuleBO> getLocationInfoMap(List<OutStockOrderPO> orderList,
        Integer warehouseId) {
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyMap();
        }

        List<OutStockOrderPO> filterOrderList =
            orderList.stream().filter(m -> Objects.nonNull(m.getRouteId()) || Objects.nonNull(m.getAreaId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterOrderList)) {
            return Collections.emptyMap();
        }

        Map<Long, List<OutStockOrderPO>> orderGroupMap =
            filterOrderList.stream().collect(Collectors.groupingBy(OutStockOrderPO::getId));

        List<RecommendOutLocationQueryDTO> queryList = new ArrayList<>();
        orderGroupMap.values().stream().forEach(orders -> {
            OutStockOrderPO order = orders.stream().findFirst().get();
            RecommendOutLocationQueryDTO queryDTO = new RecommendOutLocationQueryDTO();
            queryDTO.setWarehouseId(order.getWarehouseId());
            queryDTO.setAreaId(order.getAreaId());
            queryDTO.setRouteId(order.getRouteId());
            if (Objects.nonNull(order.getAddressId())) {
                queryDTO.setAddressId(order.getAddressId().longValue());
            }
            queryList.add(queryDTO);
        });

        if (CollectionUtils.isEmpty(queryList)) {
            return Collections.emptyMap();
        }

        List<LocationRuleDTO> locationRuleDTOList =
            iLocationRuleManageService.getOutStockLocationForNormalOrder(queryList);
        List<OutStockLocationRuleBO> locationInfoDTOList = convertBO(locationRuleDTOList);
        if (CollectionUtils.isEmpty(locationInfoDTOList)) {
            return Collections.emptyMap();
        }

        return locationInfoDTOList.stream()
            .collect(Collectors.toMap(OutStockLocationRuleBO::getRuleId, v -> v, (v1, v2) -> {
                LOG.warn("线路的出库位重复，线路id:{}; 出库位1：{};出库位2: {}", v1.getRuleId(), v1.getLocationName(),
                    v2.getLocationName());
                return v1;
            }));
    }

    /**
     * 边拣边播设置出库位
     *
     * @param batchTaskPOList
     * @param updateOutStockOrderPOS
     * @param orderItemTaskInfoPOList
     * @param warehouseId
     */
    public void handlePickAndSowTaskOutStockLocation(List<BatchTaskPO> batchTaskPOList,
        List<OutStockOrderPO> updateOutStockOrderPOS, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        Integer warehouseId) {
        handleBatchTaskToLocationIsNull(batchTaskPOList, updateOutStockOrderPOS, orderItemTaskInfoPOList, warehouseId);
        handleBatchTaskLocationIsNotNull(batchTaskPOList, updateOutStockOrderPOS, orderItemTaskInfoPOList, warehouseId);
    }

    private void handleBatchTaskToLocationIsNull(List<BatchTaskPO> batchTaskPOList,
        List<OutStockOrderPO> updateOutStockOrderPOS, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        Integer warehouseId) {
        List<BatchTaskPO> pickSowBatchTaskList = batchTaskPOList.stream()
            .filter(m -> BatchTaskKindOfPickingConstants.PICKING_SORTING.equals(m.getKindOfPicking()))
            .filter(m -> Objects.isNull(m.getToLocationId())).collect(Collectors.toList());

        if (org.springframework.util.CollectionUtils.isEmpty(pickSowBatchTaskList)) {
            return;
        }

        // key : 订单id； value：订单id; 找出属于边拣边播拣货任务的订单
        Map<Long, Long> orderIdMap = pickSowBatchTaskList.stream().map(batchTask -> {
            return orderItemTaskInfoPOList.stream().filter(m -> m.getBatchTaskId().equals(batchTask.getId()))
                .collect(Collectors.toList());
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(
            Collectors.toMap(OrderItemTaskInfoPO::getRefOrderId, OrderItemTaskInfoPO::getRefOrderId, (v1, v2) -> v1));

        Map<String, OutStockLocationRuleBO> toLocationMap = getLocationInfoMap(updateOutStockOrderPOS, warehouseId);
        if (org.springframework.util.CollectionUtils.isEmpty(toLocationMap)) {
            return;
        }

        List<OutStockOrderPO> filterOrderList = updateOutStockOrderPOS.stream()
            .filter(order -> Objects.nonNull(orderIdMap.get(order.getId()))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterOrderList)) {
            return;
        }

        for (OutStockOrderPO outStockOrderPO : filterOrderList) {
            if (org.springframework.util.CollectionUtils.isEmpty(outStockOrderPO.getItems())) {
                continue;
            }
            OutStockLocationRuleBO outStockLocationRuleBO = getOutStockLocationRuleBO(outStockOrderPO, toLocationMap);
            if (Objects.isNull(outStockLocationRuleBO)) {
                continue;
            }

            // 过滤没有出库位
            outStockOrderPO.getItems().stream().forEach(item -> {
                item.setLocationId(outStockLocationRuleBO.getLocationId());
                item.setLocationName(outStockLocationRuleBO.getLocationName());
            });
        }
    }

    private void handleBatchTaskLocationIsNotNull(List<BatchTaskPO> batchTaskPOList,
        List<OutStockOrderPO> updateOutStockOrderPOS, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        Integer warehouseId) {
        List<BatchTaskPO> pickSowBatchTaskList = batchTaskPOList.stream()
            .filter(m -> BatchTaskKindOfPickingConstants.PICKING_SORTING.equals(m.getKindOfPicking()))
            .filter(m -> Objects.nonNull(m.getToLocationId())).collect(Collectors.toList());

        if (org.springframework.util.CollectionUtils.isEmpty(pickSowBatchTaskList)) {
            return;
        }

        // key : 订单id； value：订单id; 找出属于边拣边播拣货任务的订单
        Map<Long, Long> orderIdMap = pickSowBatchTaskList.stream().map(batchTask -> {
            return orderItemTaskInfoPOList.stream().filter(m -> m.getBatchTaskId().equals(batchTask.getId()))
                .collect(Collectors.toList());
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(
            Collectors.toMap(OrderItemTaskInfoPO::getRefOrderId, OrderItemTaskInfoPO::getRefOrderId, (v1, v2) -> v1));

        List<OutStockOrderPO> filterOrderList = updateOutStockOrderPOS.stream()
            .filter(order -> Objects.nonNull(orderIdMap.get(order.getId()))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterOrderList)) {
            return;
        }

        Map<Long, List<OutStockOrderPO>> filterOrderGroupMap =
            filterOrderList.stream().collect(Collectors.groupingBy(k -> k.getId()));

        Map<String, BatchTaskPO> pickSowBatchTaskMap =
            pickSowBatchTaskList.stream().collect(Collectors.toMap(BatchTaskPO::getId, v -> v));

        // key：拣货任务id，value：出库单id
        Map<String, List<Long>> orderIdGroupMap = pickSowBatchTaskList.stream().map(batchTask -> {
            return orderItemTaskInfoPOList.stream().filter(m -> m.getBatchTaskId().equals(batchTask.getId()))
                .collect(Collectors.toList());
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
            .collect(Collectors.groupingBy(OrderItemTaskInfoPO::getBatchTaskId,
                Collectors.mapping(OrderItemTaskInfoPO::getRefOrderId, Collectors.toList())));

        for (Map.Entry<String, List<Long>> entry : orderIdGroupMap.entrySet()) {
            BatchTaskPO batchTaskPO = pickSowBatchTaskMap.get(entry.getKey());
            entry.getValue().stream().map(id -> filterOrderGroupMap.get(id)).filter(CollectionUtils::isNotEmpty)
                .flatMap(m -> m.stream()).flatMap(m -> m.getItems().stream()).forEach(item -> {
                    item.setLocationId(batchTaskPO.getToLocationId());
                    item.setLocationName(batchTaskPO.getToLocationName());
                });
        }

    }

    private OutStockLocationRuleBO getOutStockLocationRuleBO(OutStockOrderPO outStockOrderPO,
        Map<String, OutStockLocationRuleBO> toLocationMap) {
        OutStockLocationRuleBO locationRuleBO = null;
        if (Objects.nonNull(outStockOrderPO.getRouteId())) {
            locationRuleBO = toLocationMap.get(outStockOrderPO.getRouteId().toString());
        }
        if (Objects.isNull(locationRuleBO)) {
            if (Objects.nonNull(outStockOrderPO.getAreaId())) {
                locationRuleBO = toLocationMap.get(outStockOrderPO.getAreaId().toString());
            }
        }

        return locationRuleBO;
    }

    public List<LocationRuleDTO> getRecommendOutLocation(BatchPO batchPO, List<OutStockOrderPO> hasAddressOrderList,
        WavesStrategyBO wavesStrategyBO) {
        // key 是出库单上的地址id， value是转换后的地址id
        Map<Integer, Integer> addressMap =
            userAddressTransferBL.getTransferAddressIdByOrder(batchPO.getWarehouseId(), hasAddressOrderList);

        List<LocationRuleDTO> locRuleList = getRecommendOutLocation(batchPO.getWarehouseId(), hasAddressOrderList,
            wavesStrategyBO.getDeliveryCarId(), wavesStrategyBO.getLogisticsCompanyId(), addressMap);
        if (org.springframework.util.CollectionUtils.isEmpty(locRuleList)) {
            return Collections.emptyList();
        }

        locRuleList = locRuleList.stream().filter(m -> Objects.nonNull(m.getLocationId())).collect(Collectors.toList());

        Map<Integer, Integer> reverseAddressMap = new HashMap<>();
        addressMap.forEach((key, value) -> {
            reverseAddressMap.put(value, key);
        });

        locRuleList.forEach(rule -> {
            Integer addressId = reverseAddressMap.getOrDefault(rule.getAddressId(), rule.getAddressId());
            rule.setAddressId(addressId);
        });

        LOG.info("获取出库位为：{}", JSON.toJSONString(locRuleList));
        return locRuleList;
    }

}
