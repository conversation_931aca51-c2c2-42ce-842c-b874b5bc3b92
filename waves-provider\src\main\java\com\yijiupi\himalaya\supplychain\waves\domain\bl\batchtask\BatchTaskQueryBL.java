package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortGroupFlagEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ISortGroupService;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.OrderCommonDetailDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.OrderCommonDetailItemDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckParam;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckResultDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.CategoryPeriodConfigTypeEnum;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IInStockConfigService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.outstockorder.UnshippedQuantityQueryDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockOrderStatisticsService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductRelationGroupService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.ordercenter.OrderCenterQueryBL;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchTaskConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchTaskItemConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchTaskItemPeriodConfigResultConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.*;
import com.yijiupi.himalaya.supplychain.waves.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskItemStateEnum;

/**
 * <AUTHOR>
 * @since 2024-02-22 10:40
 **/
@Service
public class BatchTaskQueryBL {

    @Resource
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    @Resource
    private BatchTaskMapper batchTaskMapper;

    @Resource
    private BatchTaskItemMapper batchTaskItemMapper;

    @Resource
    private OutStockOrderMapper outStockOrderMapper;

    @Resource
    private OrderCenterQueryBL orderCenterQueryBL;

    @Reference
    private IProductRelationGroupService iProductRelationGroupService;
    @Reference
    private IOutStockOrderStatisticsService iOutStockOrderStatisticsService;
    @Reference
    private ISortGroupService iSortGroupService;
    @Reference
    private IInStockConfigService inStockConfigService;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    private static final Logger LOGGER = LoggerFactory.getLogger(BatchTaskQueryBL.class);

    @Transactional
    public List<BatchTaskDTO> findBatchTaskByOrder(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        Set<String> batchTaskIds = orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(orderIds).stream()
            .map(OrderItemTaskInfoPO::getBatchTaskId).collect(Collectors.toSet());
        if (batchTaskIds.isEmpty()) {
            return Collections.emptyList();
        }
        return BatchTaskConvertor.convertToBatchTaskDTO(batchTaskMapper.findTasksByBatchTaskId(batchTaskIds));
    }

    public List<LackOrderInfoDTO> findPartGroupOrderList(List<String> orderNos, Integer warehouseId) {
        List<Long> orderIds =
            outStockOrderMapper.findIdByOrderNos(orderNos, warehouseId).stream().map(OutStockOrderPO::getBusinessId)
                .filter(NumberUtils::isNumber).map(Long::parseLong).collect(Collectors.toList());
        Set<String> businessIds = orderCenterQueryBL.getByOrderIds(orderIds).stream().filter(this::filterCompProduct)
            .map(OrderCommonDetailDTO::getOrderId).map(String::valueOf).collect(Collectors.toSet());
        if (businessIds.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Long, OutStockOrderPO> orderMap = outStockOrderMapper.findByBusinessIds(businessIds, warehouseId).stream()
            .collect(Collectors.toMap(OutStockOrderPO::getId, Function.identity()));
        return orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(orderMap.keySet()).stream()
            .collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderId)).entrySet().stream()
            .filter(this::filterLackOrder).map(it -> LackOrderInfoDTO.of(orderMap.get(it.getKey()).getReforderno(), 1))
            .collect(Collectors.toList());
    }

    /**
     * 过滤出组合产品
     *
     * @param it 中台订单详情对象
     * @return 是否为组合产品
     */
    private boolean filterCompProduct(OrderCommonDetailDTO it) {
        return it.getOrderItems().stream().map(OrderCommonDetailItemDTO::getOrderItemBase)
            .anyMatch(item -> item.getCompositeId() != null);
    }

    /**
     * 过滤出有缺货的订单
     *
     * @param items 订单拣货信息
     * @return 订单是否有缺货
     */
    private boolean filterLackOrder(Map.Entry<Long, List<OrderItemTaskInfoPO>> items) {
        return items.getValue().stream()
            .anyMatch(it -> it.getOverSortCount().compareTo(it.getOriginalUnitTotalCount()) != 0);
    }

    public BatchTaskDTO getBatchTaskById(String batchTaskId) {
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);
        if (Objects.isNull(batchTaskPO)) {
            throw new BusinessValidateException("拣货任务不存在！");
        }
        List<BatchTaskDTO> batchTaskDTOS =
            BatchTaskConvertor.convertToBatchTaskDTO(Collections.singletonList(batchTaskPO));
        return batchTaskDTOS.stream().findAny().get();
    }

    public BatchTaskDTO getBatchTaskByNo(String batchTaskNo) {
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskByNo(batchTaskNo);
        if (Objects.isNull(batchTaskPO)) {
            throw new BusinessValidateException("拣货任务不存在！");
        }
        List<BatchTaskDTO> batchTaskDTOS =
            BatchTaskConvertor.convertToBatchTaskDTO(Collections.singletonList(batchTaskPO));
        return batchTaskDTOS.stream().findAny().get();
    }

    /**
     * 根据SKU查询2.5已拣货未出库的库存数量(排除已生内配单的订单)
     *
     * @param orgId
     * @param warehouseId
     * @param productSkuIds
     * @return
     */
    public Map<Long, BigDecimal> findPickedCountBySkuIdForSCM25(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds, boolean isForStoreCheck) {
        Map<Long, BigDecimal> result = new HashMap<>();
        List<Long> lstSkuIds = new ArrayList<>();
        Map<Long, List<Long>> configSkuMap = new HashMap<>();
        lstSkuIds.addAll(productSkuIds);
        if (isForStoreCheck) {
            // 如果是盘点，需要加上合并盘点产品
            configSkuMap = getConfigRelateSkuMap(orgId, warehouseId, productSkuIds);
            for (List<Long> value : configSkuMap.values()) {
                if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(value)) {
                    lstSkuIds.addAll(value);
                }
            }
        }
        lstSkuIds = lstSkuIds.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(lstSkuIds)) {
            List<PickedCountDTO> pickedCountBySkuIds =
                batchTaskMapper.findPickedCountBySkuIds(orgId, warehouseId, lstSkuIds);
            LOGGER.info(String.format("分拣占用数量结果：%s", JSON.toJSONString(pickedCountBySkuIds)));
            // LOGGER.info(String.format("分拣占用数量结果KeySet：%s",JSON.toJSONString(pickedCountBySkuIds.keySet())));
            for (Long skuId : productSkuIds) {
                List<Long> lstRelateSkuIds = new ArrayList<>();
                lstRelateSkuIds.add(skuId);
                if (isForStoreCheck && configSkuMap.containsKey(skuId)) {
                    List<Long> tmpSkuIds = configSkuMap.get(skuId);
                    if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(tmpSkuIds)) {
                        lstRelateSkuIds.addAll(tmpSkuIds);
                    }
                    lstRelateSkuIds =
                        lstRelateSkuIds.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
                }
                BigDecimal tmpCount = BigDecimal.ZERO;
                for (Long tmpSkuId : lstRelateSkuIds) {
                    List<PickedCountDTO> countDTOS = pickedCountBySkuIds.stream()
                        .filter(p -> p.getSkuId().equals(tmpSkuId)).collect(Collectors.toList());
                    for (PickedCountDTO countDTO : countDTOS) {
                        tmpCount =
                            tmpCount.add(ObjectUtils.defaultIfNull(countDTO.getUnitTotalCount(), BigDecimal.ZERO));
                    }
                }
                result.put(skuId, tmpCount);
            }
        }
        return result;
    }

    /**
     * 根据SKU查询2.5分拣占用详细信息（包含内配分拣占用）
     */
    public List<PickedDetailDTO> findPickedDetailBySkuIdForSCM25(Integer orgId, Integer warehouseId, Long productSkuId,
        boolean isForStoreCheck) {
        LOGGER.info("城市[{}]仓库[{}]开始查询产品[{}] 2.5分拣占用数量, 是否查询关联产品：{}", orgId, warehouseId, productSkuId, isForStoreCheck);
        List<Long> lstSkuIds = new ArrayList<>();
        Map<Long, List<Long>> configSkuMap = new HashMap<>();
        lstSkuIds.add(productSkuId);
        if (isForStoreCheck) {
            // 如果是盘点，需要加上合并盘点产品
            configSkuMap = getConfigRelateSkuMap(orgId, warehouseId, Collections.singletonList(productSkuId));
            for (List<Long> value : configSkuMap.values()) {
                if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(value)) {
                    lstSkuIds.addAll(value);
                }
            }
        }
        lstSkuIds = lstSkuIds.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(lstSkuIds)) {
            List<PickedDetailDTO> pickedDetailList =
                batchTaskMapper.findPickedDetailBySkuIdForSCM25(orgId, warehouseId, lstSkuIds);
            List<PickedDetailDTO> pickedDetailForInternalList =
                findPickedDetailBySkuIdsForCreateAllocation(orgId, warehouseId, lstSkuIds);
            List<PickedDetailDTO> result = new ArrayList<>();
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(pickedDetailList)) {
                // 大小单位转化
                pickedDetailList.stream().filter(Objects::nonNull).forEach(detail -> {
                    BigDecimal specQuantity = ObjectUtils.defaultIfNull(detail.getSpecQuantity(), BigDecimal.ONE);
                    BigDecimal unitTotalCount = ObjectUtils.defaultIfNull(detail.getUnitTotalCount(), BigDecimal.ZERO);
                    BigDecimal[] countArr = unitTotalCount.divideAndRemainder(specQuantity);
                    detail.setPackageCount(countArr[0]);
                    detail.setUnitCount(countArr[1]);
                });
                result.addAll(pickedDetailList);
            }
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(pickedDetailForInternalList)) {
                result.addAll(pickedDetailForInternalList);
            }
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(result)) {
                // 设置状态名称
                result.stream().filter(e -> e != null).forEach(e -> {
                    TaskItemStateEnum taskStateEnum = TaskItemStateEnum
                        .getEnum(e.getBatchTaskState() == null ? null : e.getBatchTaskState().intValue());
                    OutStockOrderStateEnum outStockStateEnum =
                        OutStockOrderStateEnum.getEnum(e.getOutStockOrderState());
                    e.setBatchTaskStateText(taskStateEnum == null ? null : taskStateEnum.name());
                    e.setOutStockOrderStateText(outStockStateEnum == null ? null : outStockStateEnum.name());
                });

                // 先按sku在按照isInternal排序
                Collections.sort(result,
                    Comparator.comparing(PickedDetailDTO::getSkuId, Comparator.nullsLast(Long::compareTo))
                        .thenComparing(PickedDetailDTO::getIsInternal, Comparator.nullsLast(Byte::compareTo)));
            }
            return result;
        }
        return Collections.emptyList();
    }

    /**
     * 根据SKU查询二级仓生成内配单的订单中已入库未出库数量
     *
     * @return
     */
    public Map<Long, BigDecimal> findPickedCountBySkuIdForCreateAllocation(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds, boolean isForStoreCheck) {
        // todo调oms接口查询二级仓已入库未出库的订单

        // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 -- HANDLED
        UnshippedQuantityQueryDTO quantityQueryDTO = new UnshippedQuantityQueryDTO();
        quantityQueryDTO.setSkuIds(productSkuIds);
        quantityQueryDTO.setOrgId(orgId);
        quantityQueryDTO.setWarehouseId(warehouseId);
        List<String> noStockOrderList = iOutStockOrderStatisticsService.getUnshippedOrderForSKU(quantityQueryDTO);
        LOGGER.info("调oms接口查询二级仓已入库未出库的订单：{}, orgId: {}, warehouseId: {}, productSkuIds: {}",
            JSON.toJSONString(noStockOrderList), orgId, warehouseId, JSON.toJSONString(productSkuIds));
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(noStockOrderList)) {
            return Collections.emptyMap();
        }

        Map<Long, BigDecimal> result = new HashMap<>();
        List<Long> lstSkuIds = new ArrayList<>();
        Map<Long, List<Long>> configSkuMap = new HashMap<>();
        lstSkuIds.addAll(productSkuIds);
        if (isForStoreCheck) {
            // 如果是盘点，需要加上合并盘点产品
            configSkuMap = getConfigRelateSkuMap(orgId, warehouseId, productSkuIds);
            for (List<Long> value : configSkuMap.values()) {
                if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(value)) {
                    lstSkuIds.addAll(value);
                }
            }
        }
        lstSkuIds = lstSkuIds.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(lstSkuIds)) {
            // 查询二级仓生成内配单的订单中分拣占用数量
            List<PickedCountDTO> pickedCountBySkuIds = outStockOrderMapper
                .findPickedCountBySkuIdsForCreateAllocation(orgId, warehouseId, productSkuIds, noStockOrderList);
            LOGGER.info(String.format("查询二级仓生成内配单的订单中分拣占用数量：%s", JSON.toJSONString(pickedCountBySkuIds)));
            for (Long skuId : productSkuIds) {
                List<Long> lstRelateSkuIds = new ArrayList<>();
                lstRelateSkuIds.add(skuId);
                if (isForStoreCheck && configSkuMap.containsKey(skuId)) {
                    List<Long> tmpSkuIds = configSkuMap.get(skuId);
                    if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(tmpSkuIds)) {
                        lstRelateSkuIds.addAll(tmpSkuIds);
                    }
                    lstRelateSkuIds =
                        lstRelateSkuIds.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
                }
                BigDecimal tmpCount = BigDecimal.ZERO;
                for (Long tmpSkuId : lstRelateSkuIds) {
                    List<PickedCountDTO> countDTOS = pickedCountBySkuIds.stream()
                        .filter(p -> p.getSkuId().equals(tmpSkuId)).collect(Collectors.toList());
                    for (PickedCountDTO countDTO : countDTOS) {
                        tmpCount =
                            tmpCount.add(ObjectUtils.defaultIfNull(countDTO.getUnitTotalCount(), BigDecimal.ZERO));
                    }
                }
                result.put(skuId, tmpCount);
            }
        }
        return result;
    }

    /**
     * 根据SKU查询2.5已拣货未出库的库存数量(排除已生内配单的订单) - 盘点计算差异
     */
    public Map<Long, BigDecimal> findSCM25PickedCountForStoreCheck(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds, Map<Long, List<Long>> relationGroupMap) {
        AssertUtils.notNull(orgId, "城市编号不能为空");
        AssertUtils.notNull(warehouseId, "仓库编号不能为空");
        AssertUtils.notEmpty(productSkuIds, "SkuId不能为空");
        if (relationGroupMap == null) {
            return findPickedCountBySkuIdForSCM25(orgId, warehouseId, productSkuIds, true);
        }
        productSkuIds = productSkuIds.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        List<Long> refSkuIdList = relationGroupMap.values().stream()
            .filter(e -> e != null && com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(e))
            .flatMap(e -> e.stream()).collect(Collectors.toList());
        List<Long> totalSkuIdList = new ArrayList<>(productSkuIds.size() * 2);
        totalSkuIdList.addAll(productSkuIds);
        totalSkuIdList.addAll(refSkuIdList);
        Map<Long, BigDecimal> totalPickedCountMap =
            findPickedCountBySkuIdForSCM25(orgId, warehouseId, productSkuIds, false);
        Map<Long, BigDecimal> result = new HashMap<>();
        for (Long skuId : productSkuIds) {
            BigDecimal count = BigDecimal.ZERO;
            List<Long> refSkuIds = relationGroupMap.get(skuId);
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(refSkuIds)) {
                // 增加关联产品数量
                for (Long refSkuId : refSkuIds) {
                    count = count.add(ObjectUtils.defaultIfNull(totalPickedCountMap.get(refSkuId), BigDecimal.ZERO));
                }
            }
            // 加上自己的数量
            count = count.add(ObjectUtils.defaultIfNull(totalPickedCountMap.get(skuId), BigDecimal.ZERO));
            // 记录结果
            result.put(skuId, count);
        }
        return result;
    }

    /**
     * 根据SKU查询二级仓生成内配单的订单中已入库未出库数量
     *
     * @return
     */
    private List<PickedDetailDTO> findPickedDetailBySkuIdsForCreateAllocation(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds) {
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(productSkuIds)) {
            return Collections.emptyList();
        }
        productSkuIds = productSkuIds.stream().distinct().collect(Collectors.toList());
        // todo调oms接口查询二级仓已入库未出库的订单
        // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 -- HANDLED
        UnshippedQuantityQueryDTO quantityQueryDTO = new UnshippedQuantityQueryDTO();
        quantityQueryDTO.setSkuIds(productSkuIds);
        quantityQueryDTO.setOrgId(orgId);
        quantityQueryDTO.setWarehouseId(warehouseId);
        List<String> noStockOrderList = iOutStockOrderStatisticsService.getUnshippedOrderForSKU(quantityQueryDTO);
        LOGGER.info("调oms接口查询二级仓已入库未出库的订单：{}, orgId: {}, warehouseId: {}, productSkuIds: {}",
            JSON.toJSONString(noStockOrderList), orgId, warehouseId, JSON.toJSONString(productSkuIds));
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(noStockOrderList)) {
            return Collections.emptyList();
        }
        return outStockOrderMapper.findPickedDetailBySkuIdsForCreateAllocation(orgId, warehouseId, productSkuIds,
            noStockOrderList);
    }

    private Map<Long, List<Long>> getConfigRelateSkuMap(Integer orgId, Integer warehouseId, List<Long> productSkuIds) {
        Map<Long, List<Long>> result = new HashMap<>();
        // 获取产品-关联产品SKU信息
        Map<Long, List<ProductSkuDTO>> refProductMap =
            iProductRelationGroupService.findSameGroupProductBySkuIds(warehouseId, productSkuIds);
        if (refProductMap == null || refProductMap.isEmpty()) {
            return result;
        }
        for (Map.Entry<Long, List<ProductSkuDTO>> entry : refProductMap.entrySet()) {
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            List<Long> skuIds = entry.getValue().stream().filter(e -> e != null && e.getProductSkuId() != null)
                .map(e -> e.getProductSkuId()).collect(Collectors.toList());
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(skuIds)) {
                result.put(entry.getKey(), skuIds);
            }
        }
        return result;
    }

    /**
     * 根据SKU查询2.5分拣占用详细信息（排除内配分拣占用）
     */
    public List<PickedDetailDTO> findPickedDetailDTOSForSCM25(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds, boolean isForStoreCheck) {
        LOGGER.info("分拣占用明细查询 城市[{}]仓库[{}]开始查询产品[{}] 2.5分拣占用数量, 是否查询关联产品：{}", orgId, warehouseId,
            JSON.toJSONString(productSkuIds), isForStoreCheck);
        List<Long> lstSkuIds = new ArrayList<>();
        Map<Long, List<Long>> configSkuMap = new HashMap<>();
        lstSkuIds.addAll(productSkuIds);
        if (isForStoreCheck) {
            // 如果是盘点，需要加上合并盘点产品
            configSkuMap = getConfigRelateSkuMap(orgId, warehouseId, productSkuIds);
            for (List<Long> value : configSkuMap.values()) {
                if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(value)) {
                    lstSkuIds.addAll(value);
                }
            }
        }
        lstSkuIds = lstSkuIds.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(lstSkuIds)) {
            return Collections.emptyList();
        }

        List<PickedDetailDTO> pickedDetailList =
            batchTaskMapper.findPickedDetailBySkuIdForSCM25(orgId, warehouseId, lstSkuIds);
        LOGGER.info("findPickedDetailDTOSForSCM25 分拣占用明细查询结果 ：{}", JSON.toJSONString(pickedDetailList));
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(pickedDetailList)) {
            return Collections.emptyList();
        }

        // 大小单位转化
        pickedDetailList.stream().filter(Objects::nonNull).forEach(detail -> {
            BigDecimal specQuantity = ObjectUtils.defaultIfNull(detail.getSpecQuantity(), BigDecimal.ONE);
            BigDecimal unitTotalCount = ObjectUtils.defaultIfNull(detail.getUnitTotalCount(), BigDecimal.ZERO);
            BigDecimal[] countArr = unitTotalCount.divideAndRemainder(specQuantity);
            detail.setPackageCount(countArr[0]);
            detail.setUnitCount(countArr[1]);
        });

        return pickedDetailList.stream().filter(
            p -> p != null && p.getUnitTotalCount() != null && p.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0)
            .collect(Collectors.toList());
    }

    /**
     * 获取在播种中数量
     */
    public Map<Long, BigDecimal> findSowPickedCountBySkuIdForSCM25(Integer orgId, Integer warehouseId,
        List<Long> productSkuId) {
        List<PickedCountDTO> pickedCountBySkuIds =
            batchTaskMapper.findSowPickedCountBySkuIdForSCM25(orgId, warehouseId, productSkuId);
        LOGGER.info("findPickedDetailDTOSForSCM25 分拣占用明细查询结果 ：{}", JSON.toJSONString(pickedCountBySkuIds));
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(pickedCountBySkuIds)) {
            return Collections.emptyMap();
        }

        Map<Long, BigDecimal> result = new HashMap<>();
        for (Long skuId : productSkuId) {
            List<Long> lstRelateSkuIds = new ArrayList<>();
            lstRelateSkuIds.add(skuId);
            BigDecimal tmpCount = BigDecimal.ZERO;
            for (Long tmpSkuId : lstRelateSkuIds) {
                List<PickedCountDTO> countDTOS = pickedCountBySkuIds.stream().filter(p -> p.getSkuId().equals(tmpSkuId))
                    .collect(Collectors.toList());
                for (PickedCountDTO countDTO : countDTOS) {
                    tmpCount = tmpCount.add(ObjectUtils.defaultIfNull(countDTO.getUnitTotalCount(), BigDecimal.ZERO));
                }
            }
            result.put(skuId, tmpCount);
        }
        return result;
    }

    public Map<Long, List<BatchTaskItemDTO>> findBatchTaskItemByOrderItemId(Collection<Long> orderItemIds) {
        Map<Long,
            List<String>> itemTaskMap = orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderItemIds(orderItemIds)
                .stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId,
                    Collectors.mapping(OrderItemTaskInfoPO::getBatchTaskItemId, Collectors.toList())));
        List<String> batchTaskItemIds =
            itemTaskMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        if (batchTaskItemIds.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, BatchTaskItemDTO> taskItemMap =
            batchTaskItemMapper.findBatchTaskItemByIds(batchTaskItemIds).stream().map(BatchTaskItemConverter::toDTO)
                .collect(Collectors.toMap(BatchTaskItemDTO::getId, Function.identity()));
        Set<String> taskIds =
            taskItemMap.values().stream().map(BatchTaskItemDTO::getBatchTaskId).collect(Collectors.toSet());
        Map<String, BatchTaskPO> taskMap = batchTaskMapper.findBatchTaskByIds(taskIds).stream()
            .collect(Collectors.toMap(BatchTaskPO::getId, Function.identity()));
        return itemTaskMap.entrySet().stream().map(it -> {
            List<BatchTaskItemDTO> items =
                it.getValue().stream().map(taskItemMap::get).filter(Objects::nonNull).collect(Collectors.toList());
            for (BatchTaskItemDTO item : items) {
                Optional.ofNullable(taskMap.get(item.getBatchTaskId())).map(BatchTaskPO::getToLocationName)
                    .ifPresent(item::setBatchTaskToLocationName);
            }
            return Pair.of(it.getKey(), items);
        }).collect(Collectors.toMap(Pair::getFirst, Pair::getSecond));
    }

    /**
     * 根据SKU查询分拣占用详细信息
     */
    public List<PickedDetailDTO> findPickedDetailByCondition(PickedDetailQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "城查询参数不能为空");
        AssertUtils.notNull(queryDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(queryDTO.getSkuIds(), "SkuId不能为空");

        LOGGER.info("根据SKU查询分拣占用详细信息 入参：{}", JSON.toJSONString(queryDTO));
        Integer orgId = queryDTO.getOrgId();
        Integer warehouseId = queryDTO.getWarehouseId();
        List<Long> productSkuIds = queryDTO.getSkuIds();
        boolean isForStoreCheck = queryDTO.getForStoreCheck();

        List<Long> lstSkuIds = new ArrayList<>();
        Map<Long, List<Long>> configSkuMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(productSkuIds)) {
            lstSkuIds.addAll(productSkuIds);
            if (isForStoreCheck) {
                // 如果是盘点，需要加上合并盘点产品
                configSkuMap = getConfigRelateSkuMap(orgId, warehouseId, productSkuIds);
                for (List<Long> value : configSkuMap.values()) {
                    if (!CollectionUtils.isEmpty(value)) {
                        lstSkuIds.addAll(value);
                    }
                }
            }
            lstSkuIds = lstSkuIds.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(lstSkuIds)) {
            queryDTO.setSkuIds(lstSkuIds);
        }

        List<PickedDetailDTO> pickedDetailList = batchTaskMapper.findPickedDetailByCondition(queryDTO);
        LOGGER.info("根据SKU查询分拣占用详细信息 结果 ：{}", JSON.toJSONString(pickedDetailList));
        if (CollectionUtils.isEmpty(pickedDetailList)) {
            return Collections.emptyList();
        }

        // 大小单位转化
        pickedDetailList.stream().filter(Objects::nonNull).forEach(detail -> {
            BigDecimal specQuantity = ObjectUtils.defaultIfNull(detail.getSpecQuantity(), BigDecimal.ONE);
            BigDecimal unitTotalCount = ObjectUtils.defaultIfNull(detail.getUnitTotalCount(), BigDecimal.ZERO);
            BigDecimal[] countArr = unitTotalCount.divideAndRemainder(specQuantity);
            detail.setPackageCount(countArr[0]);
            detail.setUnitCount(countArr[1]);
        });

        return pickedDetailList.stream().filter(
            p -> p != null && p.getUnitTotalCount() != null && p.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0)
            .collect(Collectors.toList());
    }

    /**
     * 通过货位信息查询分区信息，查询最近完成的拣货任务
     *
     * @param dto
     * @return
     */
    public BatchTaskDTO getSortGroupLatestBatchTask(SortGroupLatestBatchTaskGetDTO dto) {
        SortGroupDTO sortGroupDTO = getSortGroupDTO(dto);
        if (Objects.isNull(sortGroupDTO)) {
            throw new BusinessValidateException("未查询到分区信息！");
        }
        Warehouse warehouse = iWarehouseQueryService.findWarehouseById(dto.getWarehouseId());

        // 根据货位信息 查询 分组id信息
        BatchTaskDTO batchTaskDTO = new BatchTaskDTO();
        batchTaskDTO.setTaskState(TaskStateEnum.已完成.getType());
        batchTaskDTO.setSortGroupId(sortGroupDTO.getId());
        batchTaskDTO.setWarehouseId(dto.getWarehouseId());
        batchTaskDTO.setOrgId(warehouse.getCityId());
        BatchTaskDTO existBatchTaskDTO = batchTaskMapper.getSortGroupLatestBatchTask(batchTaskDTO);

        LOGGER.info("查询最近完成的拣货任务信息，入参：{}, 结果：{}", JSON.toJSONString(batchTaskDTO),
            JSON.toJSONString(existBatchTaskDTO));
        if (Objects.isNull(existBatchTaskDTO)) {
            return null;
        }

        String batchTaskId = existBatchTaskDTO.getId();

        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);
        List<BatchTaskItemPO> batchTaskItemPOList =
            batchTaskItemMapper.findBatchTaskItemDTOListByTaskIds(Collections.singletonList(batchTaskId));

        return BatchTaskConvertor.convertSortGroupLatestBatchTask(batchTaskPO, batchTaskItemPOList);
    }

    private SortGroupDTO getSortGroupDTO(SortGroupLatestBatchTaskGetDTO dto) {
        SortGroupSO so = new SortGroupSO();
        so.setWarehouseId(dto.getWarehouseId());
        so.setCallNum(dto.getCallNum());
        so.setFlag(SortGroupFlagEnum.分区拣货.getType());
        so.setState(ConditionStateEnum.是.getType());
        List<SortGroupDTO> sortGroupDTOList = iSortGroupService.findSortGroupTotalList(so);
        if (CollectionUtils.isEmpty(sortGroupDTOList)) {
            return null;
        }

        return sortGroupDTOList.stream().findFirst().get();
    }

    /**
     * 拣货任务项禁止销售配置查询
     */
    public List<BatchTaskItemPeriodConfigResultDTO>
        listBatchTaskItemPeriodConfig(BatchTaskItemPeriodConfigQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "询参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(queryDTO.getBatchTaskItemIdList(), "拣货任务项ids不能为空");

        LOGGER.info("拣货任务项禁止销售配置查询 入参：{}", JSON.toJSONString(queryDTO));
        List<BatchTaskItemPO> taskItemPOS =
            batchTaskItemMapper.findBatchTaskItemByIds(queryDTO.getBatchTaskItemIdList());
        if (CollectionUtils.isEmpty(taskItemPOS)) {
            return Collections.emptyList();
        }

        Map<String, List<BatchTaskItemPO>> itemMap = taskItemPOS.stream().filter(p -> p.getSkuId() != null)
            .collect(Collectors.groupingBy(p -> p.getSkuId() + "" + p.getLocationId()));
        if (itemMap.isEmpty()) {
            return Collections.emptyList();
        }

        // 仓库是否开启货位库存
        Boolean isOpenStock = warehouseConfigService.isOpenLocationStock(queryDTO.getWarehouseId());
        OutStockConfigCheckParam checkParam = new OutStockConfigCheckParam();
        checkParam.setWarehouseId(queryDTO.getWarehouseId());
        checkParam.setCategoryPeriodConfigType(CategoryPeriodConfigTypeEnum.FORBID_SALES_PERIOD.getValue());
        checkParam.setExcludeSubcategoryList(
            Arrays.asList(LocationEnum.残次品位.getType().byteValue(), LocationAreaEnum.残次品区.getType().byteValue()));
        List<OutStockConfigCheckDTO> checkProductDTOList = new ArrayList<>();
        itemMap.forEach((itemKey, list) -> {
            BatchTaskItemPO itemPO = list.stream().findFirst().get();
            OutStockConfigCheckDTO checkProductDTO = new OutStockConfigCheckDTO();
            checkProductDTO.setSkuId(itemPO.getSkuId());
            if (isOpenStock) {
                checkProductDTO.setLocationId(itemPO.getLocationId());
            }
            checkProductDTOList.add(checkProductDTO);
        });
        checkParam.setCheckProductDTOList(checkProductDTOList);
        checkParam.setCalStorageAttribute(false);

        List<OutStockConfigCheckResultDTO> checkResultDTOS =
            inStockConfigService.checkByCategoryPeriodConfigType(checkParam);
        LOGGER.info("拣货任务项禁止销售配置查询 结果：{}", JSON.toJSONString(checkResultDTOS));
        return BatchTaskItemPeriodConfigResultConvertor.convertTo(checkResultDTOS);
    }

    /**
     * 根据类目禁止销售期检查
     *
     * @param checkParam
     */
    public List<OutStockConfigCheckResultDTO> checkByForbidSalesDays(OutStockConfigCheckParam checkParam) {
        AssertUtils.notNull(checkParam, "请求参数不能为空");
        AssertUtils.notNull(checkParam.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(checkParam.getCheckProductDTOList(), "校验参数不能为空");
        checkParam.getCheckProductDTOList().forEach(elem -> {
            AssertUtils.notNull(elem.getSkuId(), "skuId不能为空");
        });

        checkParam.setCategoryPeriodConfigType(CategoryPeriodConfigTypeEnum.FORBID_SALES_PERIOD.getValue());
        checkParam.setExcludeSubcategoryList(
            Arrays.asList(LocationEnum.残次品位.getType().byteValue(), LocationAreaEnum.残次品区.getType().byteValue()));
        return inStockConfigService.checkByCategoryPeriodConfigType(checkParam);
    }
}
