package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.Pager;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.allot.service.IStockService;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.dto.FaceSheetManagerDTO;
import com.yijiupi.himalaya.supplychain.dto.FaceSheetSettingDTO;
import com.yijiupi.himalaya.supplychain.dto.OrderFaceSheetRsultDTO;
import com.yijiupi.himalaya.supplychain.dto.OrderPrintDTO;
import com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigType;
import com.yijiupi.himalaya.supplychain.ordercenter.service.OrderCenterConvertService;
import com.yijiupi.himalaya.supplychain.ordercenter.service.OrderCenterService;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.InStockOrderBusinessType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderBusinessType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.SourceType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockQueryService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IThirdPartyOutStockService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.thirdparty.StockOutApplyResultDTO;
import com.yijiupi.himalaya.supplychain.service.IFaceSheetManagerService;
import com.yijiupi.himalaya.supplychain.service.IOrderPrintService;
import com.yijiupi.himalaya.supplychain.traceablecode.dto.centerorder.OrderTraceableItemDTO;
import com.yijiupi.himalaya.supplychain.traceablecode.service.ordersync.ITransferOrderQueryService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderPalletInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IOrderLocationPalletService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductControlConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.MergePickOrderValidBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.LogisticsConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.PalletReviewInfoDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.ScanOrderForReviewDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.WaveOrderConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOrderInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.UpdateOrderItemCountDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.*;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.*;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.PickingTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderByProductSO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderItemProductSO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderSearchSO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderWaitDeliverySO;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.himalaya.supplychain.waves.util.PageHelperUtils;
import com.yijiupi.himalaya.supplychain.waves.util.RedisUtil;
import com.yijiupi.himalaya.supplychain.waves.utils.DateUtil;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

// import com.yijiupi.himalaya.framework.mqtt.core.MqttTemplate;

/**
 * <AUTHOR> 2018/3/16
 */
@Service
public class BatchOrderInfoBL {

    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private OutStockOrderItemChangeRecordMapper outStockOrderItemChangeRecordMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private PackageOrderItemMapper packageOrderItemMapper;
    @Autowired
    private RedisUtil<String> redisUtil;
    @Autowired
    private OrderFeatureBL orderFeatureBL;
    @Autowired
    private GlobalCache globalCache;
    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;

    @Reference(timeout = 30000)
    private IOrgService iOrgService;
    @Reference
    private IStockService iStockService;
    @Reference
    private IOutStockQueryService iOutStockQueryService;
    @Reference
    private IFaceSheetManagerService iFaceSheetManagerService;
    @Reference
    private IThirdPartyOutStockService iThirdPartyOutStockService;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchOrderProcessBL batchOrderProcessBL;
    @Autowired
    private OrderCenterBL orderCenterBL;
    @Reference
    private OrderCenterConvertService orderCenterConvertService;
    @Reference
    private OrderCenterService orderCenterService;
    @Reference
    private IOrderPrintService iOrderPrintService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Reference
    private ITransferOrderQueryService iTransferOrderQueryService;
    @Reference
    private IProductControlConfigService controlConfigService;
    @Reference
    private IProductSkuService productSkuService;
    @Reference
    private IOrderLocationPalletService iOrderLocationPalletService;
    @Reference
    private ILocationService iLocationService;

    private static final String redisKey = "orderPrintInfo";
    private static final Logger LOG = LoggerFactory.getLogger(BatchOrderInfoBL.class);

    public List<String> findNotCompleteBatchOrder(Integer warehouseId, List<String> lstOrderNo) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(lstOrderNo, "单号集合不能为空");
        List<OutStockOrderPO> lstOrders = outStockOrderMapper.findOrderByRefOrderNo(lstOrderNo, warehouseId);
        List<String> lstOrderNos = lstOrders.stream()
            .filter(p -> p.getState() == null
                || (!Objects.equals(p.getState().byteValue(), OutStockOrderStateEnum.已拣货.getType())
                    && !Objects.equals(p.getState().byteValue(), OutStockOrderStateEnum.已出库.getType())))
            .map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList());
        return lstOrderNos;
    }

    /**
     * 根据波次编号查询该波次关联的订单列表
     */
    public PageList<BatchOrderInfoDTO> findBatchOrderInfoListByBatchNo(String batchNo, Integer currentPage,
        Integer pageSize) {
        AssertUtils.notNull(batchNo, "波次编号不能为空");
        // 获取波次详情
        BatchOrderInfoDTO batchOrderInfoDTO = batchMapper.selectBatchInfoByBatchNo(batchNo);
        if (batchOrderInfoDTO == null) {
            throw new BusinessException("找不到波次！");
        }

        PageList<BatchOrderInfoDTO> pageList = new PageList<>();
        Pager pager = new Pager(currentPage, pageSize, 0);
        List<OutStockOrderDTO> outStockOrderList = new ArrayList<>();

        // 波次类型为微酒时
        if (Objects.equals(batchOrderInfoDTO.getBatchType(), BatchTypeEnum.微酒.getType())) {
            // 获取微酒订单
            List<String> orderIdList = batchMapper.listOrderIdByBatchNo(batchNo);
            if (CollectionUtils.isNotEmpty(orderIdList)) {
                List<com.yijiupi.himalaya.supplychain.allot.dto.stockout.StockOutApplyResultDTO> stockOutApplyList =
                    iStockService.findStockOutApply(orderIdList);
                // 组装微酒订单
                List<OutStockOrderDTO> outStockOrderByWineList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(stockOutApplyList)) {
                    stockOutApplyList.forEach(stockDTO -> {
                        OutStockOrderDTO orderDTO = new OutStockOrderDTO();
                        orderDTO.setId(Long.valueOf(stockDTO.getId()));
                        orderDTO.setRefOrderNo(stockDTO.getNoteNo());
                        try {
                            orderDTO.setOrderCreateTime(stockDTO.getBusinessTimeText() != null
                                ? DateUtil.parse(stockDTO.getBusinessTimeText(), DateUtil.YYYYMMDD_HHMMSS) : null);
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        orderDTO.setOrderType(OutStockOrderTypeEnum.第三方出库.getType());
                        orderDTO.setPackageAmount(new BigDecimal(stockDTO.getSpecificationscount()));
                        orderDTO.setUnitAmount(new BigDecimal(stockDTO.getUnitcount()));
                        orderDTO.setSkuCount(stockDTO.getProductCount());
                        outStockOrderByWineList.add(orderDTO);
                    });
                }
                outStockOrderList = outStockOrderByWineList;
                pager.setRecordCount(outStockOrderList.size());
            }

        } else if (BatchTypeEnum.isApplyOrderBatch(batchOrderInfoDTO.getBatchType())) {
            // 获取微酒订单
            List<String> orderIdList = batchMapper.listOrderIdByBatchNo(batchNo);
            if (CollectionUtils.isNotEmpty(orderIdList)) {
                List<Long> ids = orderIdList.stream().map(Long::valueOf).collect(Collectors.toList());
                List<StockOutApplyResultDTO> stockOutApplyList = iThirdPartyOutStockService.findStockOutApply(ids);
                // 组装微酒订单
                List<OutStockOrderDTO> outStockOrderByWineList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(stockOutApplyList)) {
                    stockOutApplyList.forEach(stockDTO -> {
                        OutStockOrderDTO orderDTO = new OutStockOrderDTO();
                        orderDTO.setId(Long.valueOf(stockDTO.getId()));
                        orderDTO.setRefOrderNo(stockDTO.getNoteNo());
                        try {
                            orderDTO.setOrderCreateTime(stockDTO.getBusinessTimeText() != null
                                ? DateUtil.parse(stockDTO.getBusinessTimeText(), DateUtil.YYYYMMDD_HHMMSS) : null);
                        } catch (ParseException e) {
                            LOG.warn("出现异常", e);
                        }
                        orderDTO.setOrderType(OutStockOrderTypeEnum.第三方出库.getType());
                        orderDTO.setPackageAmount(new BigDecimal(stockDTO.getSpecificationscount()));
                        orderDTO.setUnitAmount(new BigDecimal(stockDTO.getUnitcount()));
                        orderDTO.setSkuCount(stockDTO.getProductCount());
                        outStockOrderByWineList.add(orderDTO);
                    });
                }
                outStockOrderList = outStockOrderByWineList;
                pager.setRecordCount(outStockOrderList.size());
            }

        } else {
            // 查询该波次关联的订单列表
            PageResult<OutStockOrderDTO> pageResult =
                outStockOrderMapper.listOrderByBatchNo(batchNo, currentPage, pageSize);
            outStockOrderList = pageResult.toPageList().getDataList();
            pager = pageResult.getPager();
        }

        batchOrderInfoDTO.setItems(outStockOrderList);
        pageList.setPager(pager);
        pageList.setDataList(Arrays.asList(batchOrderInfoDTO));
        return pageList;
    }

    /**
     * 查询可用的订单
     *
     * @param dto
     * @return
     */
    public PageList<OutStockOrderDTO> findEnableOutStockOrderList(OutStockOrderSearchSO dto) {
        AssertUtils.notNull(dto.getWareHouseId(), "仓库id不能为空");
        PageList<OutStockOrderDTO> outStockOrderDTOPageList = new PageList<>();
        dto.setNotBusinessTypes(
            Arrays.asList(InStockOrderBusinessType.内配单.getType(), InStockOrderBusinessType.内配退货单.getType()));
        PageResult<String> outStockOrderPOPager =
            outStockOrderMapper.findOutStockOrderPOPageCount(dto, dto.getCurrentPage(), dto.getPageSize());
        Pager pager = outStockOrderPOPager.getPager();
        outStockOrderDTOPageList.setPager(pager);

        PageList<String> outStockOrderPOList = outStockOrderPOPager.toPageList();
        if (!CollectionUtils.isEmpty(outStockOrderPOList.getDataList())) {
            List<OutStockOrderPO> outStockOrderPOListResult = outStockOrderMapper.findByOrderId(
                outStockOrderPOList.getDataList().stream().map(Long::valueOf).collect(Collectors.toList()));
            List<OutStockOrderDTO> outStockOrderDTOS =
                WaveOrderConvertor.outStockOrderPOS2OutStockOrderDTOS(outStockOrderPOListResult);
            outStockOrderDTOPageList.setDataList(outStockOrderDTOS);
        }
        return outStockOrderDTOPageList;
    }

    /**
     * 查询出库单列表
     */
    public PageList<OutStockOrderDTO> findOutStockOrderList(OutStockOrderSearchSO dto) {
        AssertUtils.notNull(dto.getWareHouseId(), "仓库id不能为空");
        if (dto.getPageSize() <= 20) {
            LOG.info("分页数量少于20，入参:{}", JSON.toJSONString(dto));
            dto.setPageSize(20);
        }
        PageList<OutStockOrderDTO> outStockOrderDTOPageList = new PageList<>();
        if (dto.getBusinessTypes() == null
            || CollectionUtils.isEmpty(dto.getBusinessTypes()) && ObjectUtils.isEmpty(dto.getOutBoundType())) {
            dto.setNotBusinessTypes(
                Arrays.asList(OutStockOrderBusinessType.内配订单.getType(), OutStockOrderBusinessType.内配退订单.getType(),
                    OutStockOrderBusinessType.中转订单.getType(), OutStockOrderBusinessType.中转退订单.getType()));
        }

        // 扫描订单号/快递单号
        if (StringUtils.isNotEmpty(dto.getScanOrderNo())) {
            // if (isOpenOrderCenter) {
            // OrderQuery orderQuery = new OrderQuery();
            // OrderDeliveryQuery orderDelivery = new OrderDeliveryQuery();
            // orderDelivery.setTrackNumber(dto.getScanOrderNo());
            // orderQuery.setOrderDelivery(orderDelivery);
            // PageParam pageParam = new PageParam();
            // pageParam.setPageIndex(1);
            // pageParam.setPageSize(50);
            // List<OrderDocumentDTO> orderCenterlist =
            // orderCenterService.findOrderCenterByPage(orderQuery, pageParam);
            // LOG.info("findOrderCenterByPage-根据快递单号：{}，查询订单号：{}", dto.getScanOrderNo(),
            // JSON.toJSONString(orderCenterlist));
            // if (CollectionUtils.isEmpty(orderCenterlist)) {
            // outStockOrderDTOPageList.setDataList(new ArrayList<>());
            // outStockOrderDTOPageList.setPager(new Pager(1, 1, 0));
            // return outStockOrderDTOPageList;
            // }
            //
            // List<String> relOrderNoList =
            // orderCenterlist.stream().map(OrderDocumentDTO::getOrderBase).collect(Collectors.toList()).stream()
            // .map(OrderBaseDocumentDTO::getOrderNo).distinct().collect(Collectors.toList());
            // dto.setRefOrderNoList(relOrderNoList);
            // 获取订单打印信息
            List<OrderPrintDTO> printDTOS;
            try {
                printDTOS = iOrderPrintService.getOrderByCode(dto.getScanOrderNo());
            } catch (BusinessValidateException e) {
                LOG.info("重构根据快递单号报错：" + dto.getScanOrderNo(), e);
                throw e;
            } catch (Exception e) {
                LOG.info("重构根据快递单号报错：" + dto.getScanOrderNo(), e);
                outStockOrderDTOPageList.setDataList(new ArrayList<>());
                outStockOrderDTOPageList.setPager(new Pager(1, 1, 0));
                return outStockOrderDTOPageList;
            }
            LOG.info("重构根据快递单号：{}，查询打印数据：{}", dto.getScanOrderNo(), JSON.toJSONString(printDTOS));
            if (CollectionUtils.isEmpty(printDTOS)) {
                outStockOrderDTOPageList.setDataList(new ArrayList<>());
                outStockOrderDTOPageList.setPager(new Pager(1, 1, 0));
                return outStockOrderDTOPageList;
            }
            List<String> relOrderNoList = printDTOS.stream().filter(p -> StringUtils.isNotEmpty(p.getOrderNo()))
                .distinct().map(OrderPrintDTO::getOrderNo).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(relOrderNoList)) {
                outStockOrderDTOPageList.setDataList(new ArrayList<>());
                outStockOrderDTOPageList.setPager(new Pager(1, 1, 0));
                return outStockOrderDTOPageList;
            }
            dto.setRefOrderNoList(relOrderNoList);
            // }
            // else {
            // // List<String> refOrderNoList = new ArrayList<>();
            // // 1、优先根据快递单号查对应的订单号
            // List<String> relOrderNoList = iOrderQueryService.selectNoByLogisticsNo(dto.getScanOrderNo());
            // LOG.info("根据快递单号：{}，查询订单号：{}", dto.getScanOrderNo(), JSON.toJSONString(relOrderNoList));
            // if (CollectionUtils.isEmpty(relOrderNoList)) {
            // outStockOrderDTOPageList.setDataList(new ArrayList<>());
            // outStockOrderDTOPageList.setPager(new Pager(1, 1, 0));
            // return outStockOrderDTOPageList;
            // }
            // // // 2、当前扫描的可能是订单号
            // // refOrderNoList.add(dto.getScanOrderNo());
            // dto.setRefOrderNoList(relOrderNoList);
            // }
        }

        // 根据分仓配置类型设置订单特征查询条件
        setPackageAttributteByConfigType(dto);

        //设置了最大值没设置最小值的，默认最小值为0
        if (dto.getMaxCount() != null && dto.getMaxCount() > 0 && dto.getMinCount() == null) {
            dto.setMinCount(0);
        }

        PageResult<OutStockOrderPO> pageResult =
            outStockOrderMapper.listOutStockOrder(dto, dto.getCurrentPage(), dto.getPageSize());

        List<OutStockOrderDTO> outStockOrderDTOS = new ArrayList<>();
        if (pageResult.toPageList() != null && CollectionUtils.isNotEmpty(pageResult.toPageList().getDataList())) {
            outStockOrderDTOS =
                WaveOrderConvertor.outStockOrderPOS2OutStockOrderDTOS(pageResult.toPageList().getDataList());
        }
        // 订单特征
        List<Long> orderIds = new ArrayList<>();
        for (OutStockOrderDTO p : outStockOrderDTOS) {
            orderIds.add(p.getId());
            if (StringUtils.isInteger(p.getBusinessId())) {
                orderIds.add(Long.parseLong(p.getBusinessId()));
            }
        }
        // List<Long> orderIds = outStockOrderDTOS.stream().map(p->p.getId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderIds)) {
            orderIds = orderIds.stream().distinct().collect(Collectors.toList());
            Map<Long, List<Byte>> featureMap = iOutStockQueryService.getOrderFeatureMap(orderIds);
            // LOG.info("获取到的订单特征：{}", JSON.toJSONString(featureMap));
            if (featureMap != null && !featureMap.isEmpty()) {
                outStockOrderDTOS.forEach(p -> {
                    List<Byte> lstFeature = featureMap.containsKey(p.getId()) ? featureMap.get(p.getId())
                        : (StringUtils.isInteger(p.getBusinessId()) ? featureMap.get(Long.valueOf(p.getBusinessId()))
                            : new ArrayList<>());
                    p.setPackageAttribute(lstFeature);
                });

            }
        }
        // LOG.info("查询快递直发信息 pageResult={}", JSON.toJSONString(pageResult));
        // 根据快递单号查询快递单信息
        // if (isOpenOrderCenter) {
        List<Long> omsOrderIdList = outStockOrderDTOS.stream().map(OutStockOrderDTO::getBusinessId)
            .filter(StringUtils::isInteger).map(Long::valueOf).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(omsOrderIdList)) {
            OrderPrintDTO printDTO = new OrderPrintDTO();
            printDTO.setOrderList(omsOrderIdList);
            List<OrderPrintDTO> printDTOS = iOrderPrintService.list(printDTO);
            // LOG.info("findOutStockOrderList 根据订单id：{}，查询快递单信息：{}", JSON.toJSONString(omsOrderIdList),
            // JSON.toJSONString(printDTOS));
            if (CollectionUtils.isNotEmpty(printDTOS)) {
                Map<Long, List<OrderPrintDTO>> printDTOMap = printDTOS.stream().filter(p -> p.getOrderId() != null)
                    .collect(Collectors.groupingBy(OrderPrintDTO::getOrderId));
                outStockOrderDTOS.forEach(order -> {
                    if (!StringUtils.isNumeric(order.getBusinessId())) {
                        return;
                    }

                    List<OrderPrintDTO> orderPrintDTOS = printDTOMap.get(Long.valueOf(order.getBusinessId()));
                    if (CollectionUtils.isEmpty(orderPrintDTOS)) {
                        return;
                    }

                    order.setLogisticsList(LogisticsConverter.orderPrintDTOToLogisticsDTOS(orderPrintDTOS));
                });
            }
        }

        // 快递直发订单查询订单来源
        // if (Objects.equals(dto.getExpressFlag(), ConditionStateEnum.是.getType())) {
        // if (CollectionUtils.isNotEmpty(orderIds)) {
        // OrderExtensionsQueryDTO queryDTO = new OrderExtensionsQueryDTO();
        // queryDTO.setOrderIds(orderIds);
        // List<OrderExtensionsDTO> orderExtensionsDTOList =
        // iOrderExtensionsService.findOrderExtensionsByOrderIds(queryDTO);
        // if (CollectionUtils.isNotEmpty(orderExtensionsDTOList)) {
        // outStockOrderDTOS.forEach(orderDTO -> {
        // Optional<OrderExtensionsDTO> optional = orderExtensionsDTOList.stream()
        // .filter(p -> Objects.equals(p.getOrderId(), orderDTO.getId())).findFirst();
        // if (optional.isPresent() && optional.get().getOrderSouorce() != null) {
        // orderDTO.setOrderSource(optional.get().getOrderSouorce());
        // // 订单来源名称
        // AccessPlatformEnum accessPlatformEnum =
        // AccessPlatformEnum.getAccessPlatformEnum(optional.get().getOrderSouorce());
        // if (accessPlatformEnum != null) {
        // orderDTO.setOrderSourceText(accessPlatformEnum.getText());
        // }
        // }
        // });
        // }
        // }
        // }

        outStockOrderDTOPageList.setPager(pageResult.getPager());
        outStockOrderDTOPageList.setDataList(outStockOrderDTOS);
        // LOG.info("查询快递直发信息 outStockOrderDTOPageList={}", JSON.toJSONString(outStockOrderDTOPageList));
        return outStockOrderDTOPageList;
    }

    /**
     * 查询出库单列表
     *
     * @param dto
     * @param descending 是否根据下单时间降序
     * @return
     */
    public PageList<OutStockOrderDTO> findOutStockOrderList(OutStockOrderSearchSO dto, boolean descending) {
        PageList<OutStockOrderDTO> outStockOrderDTOPageList = new PageList<>();
        PageResult<OutStockOrderPO> pageResult =
            outStockOrderMapper.findOutStockOrderListNew(dto, dto.getCurrentPage(), dto.getPageSize(), descending);
        List<OutStockOrderDTO> outStockOrderDTOS = new ArrayList<>();
        if (pageResult.toPageList() != null && CollectionUtils.isNotEmpty(pageResult.toPageList().getDataList())) {
            outStockOrderDTOS =
                WaveOrderConvertor.outStockOrderPOS2OutStockOrderDTOS(pageResult.toPageList().getDataList());
            if (dto.isSearchItem()) { // 查询明细
                List<Long> orderIds = new ArrayList<>();
                Map<Long, List<OutStockOrderItemDTO>> outStockOrderItemDTOMap = new HashMap<>();
                outStockOrderDTOS.forEach(order -> {
                    orderIds.add(order.getId());
                    outStockOrderItemDTOMap.put(order.getId(), new ArrayList<OutStockOrderItemDTO>());
                });
                List<OutStockOrderItemPO> outStockOrderItemPOs =
                    outStockOrderItemMapper.findByOutstockorderIdList(orderIds);
                for (OutStockOrderItemPO outStockOrderItemPO : outStockOrderItemPOs) {
                    OutStockOrderItemDTO outStockOrderItemDTO =
                        WaveOrderConvertor.outStockOrderItemPO2OutStockOrderItemDTO(outStockOrderItemPO);
                    outStockOrderItemDTOMap.get(outStockOrderItemDTO.getOutstockorder_Id()).add(outStockOrderItemDTO);
                }
                outStockOrderDTOS.forEach(orderDTO -> {
                    orderDTO.setItemList(outStockOrderItemDTOMap.get(orderDTO.getId()));
                });
            }
        }
        outStockOrderDTOPageList.setPager(pageResult.getPager());
        outStockOrderDTOPageList.setDataList(outStockOrderDTOS);
        return outStockOrderDTOPageList;
    }

    /**
     * 分单拣货自动打印
     */
    public void autoPrintingByBatchTaskNo(OrderPrintInfoDTO orderPrintInfoDTO) {
        LOG.info("订单分拣打印参数:{}", orderPrintInfoDTO);

        // 根据拣货任务编号查询可打印订单
        List<BatchTaskItemPO> batchTaskItemPOS = batchTaskItemMapper.findBatchTaskItemByOrderNos(
            orderPrintInfoDTO.getOrderNos(), orderPrintInfoDTO.getOrgId(), orderPrintInfoDTO.getWarehouseId());
        if (CollectionUtils.isNotEmpty(batchTaskItemPOS)) {
            List<String> orderNos = new ArrayList<>();
            Map<String, List<BatchTaskItemPO>> BatchTaskItemMap =
                batchTaskItemPOS.stream().collect(Collectors.groupingBy(BatchTaskItemPO::getRefOrderNo));

            // 获取已拣货完成的订单号
            BatchTaskItemMap.forEach((orderNo, batchTaskItems) -> {
                if (batchTaskItems.stream().allMatch(item -> item.getTaskState() == 2)) {
                    orderNos.add(orderNo);
                }
            });

            if (CollectionUtils.isNotEmpty(orderNos)) {
                orderPrintInfoDTO.setOrderNos(orderNos);
                saveOrderPrintInfo(orderPrintInfoDTO);
            } else {
                LOG.info("订单拣货未完成，分拣打印参数:{}", orderPrintInfoDTO);
            }
        }
    }

    /**
     * 订单打印关联数据保存
     *
     * @param orderPrintInfoDTO
     */
    public void saveOrderPrintInfo(OrderPrintInfoDTO orderPrintInfoDTO) {
        // LOG.info("订单打印参数:{}", orderPrintInfoDTO);
        Integer orgId = orderPrintInfoDTO.getOrgId();
        Integer warehouseId = orderPrintInfoDTO.getWarehouseId();
        List<String> orderNos = orderPrintInfoDTO.getOrderNos();
        String printer = orderPrintInfoDTO.getPrinter();

        // 查找该key、filed下的数据
        String redisFiled = orgId.toString() + warehouseId.toString();
        String orderPrintInfo = redisUtil.getHash(redisKey, redisFiled);
        List<OrderPrintInfoDTO> orderPrintInfoDTOS = JSON.parseArray(orderPrintInfo, OrderPrintInfoDTO.class);

        // 添加传过来的数据，重新生成新的缓存
        if (CollectionUtils.isNotEmpty(orderPrintInfoDTOS)) {
            orderPrintInfoDTOS.forEach(info -> {
                if (info.getOrgId().equals(orgId) && info.getWarehouseId().equals(warehouseId)
                    && info.getPrinter().equals(printer)) {
                    orderNos.addAll(info.getOrderNos());
                    List<String> newOrderNos = orderNos.stream().distinct().collect(Collectors.toList());
                    info.setOrderNos(newOrderNos);
                }
            });
        } else {
            orderPrintInfoDTOS = new ArrayList<>();
            orderPrintInfoDTOS.add(orderPrintInfoDTO);
        }
        String redisValue = JSON.toJSONString(orderPrintInfoDTOS);
        // LOG.info("订单待打印数据:{}", orderPrintInfoDTOS);
        if (CollectionUtils.isNotEmpty(orderPrintInfoDTOS)) {
            redisUtil.setHash(redisKey, redisFiled, redisValue, 1, TimeUnit.DAYS);
        }
    }

    /**
     * 查询订单打印数据
     */
    public List<OrderPrintInfoDTO> findOrderPrintInfo(Integer orgId, Integer warehouseId) {
        String redisFiled = orgId.toString() + warehouseId.toString();
        String orderPrintInfo = redisUtil.getHash(redisKey, redisFiled);
        List<OrderPrintInfoDTO> orderPrintInfoDTOS = JSON.parseArray(orderPrintInfo, OrderPrintInfoDTO.class);
        if (CollectionUtils.isNotEmpty(orderPrintInfoDTOS)) {
            // LOG.info("订单分拣打印数据:{}", orderPrintInfoDTOS);
            redisUtil.delHash(redisKey, new String[] {redisFiled});
        }
        return orderPrintInfoDTOS;
    }

    /**
     * 推送订单打印关联数据
     */
    public void pushPrintingOrderMsg(OrderPrintInfoDTO orderPrintInfoDTO) {
        String orderMsg = JSON.toJSONString(orderPrintInfoDTO);
        LOG.info("推送待打印订单信息，订单Id为：{}", orderMsg);
        // mqttTemplate.sendString("SCM_Waves/Printer/"+orderPrintInfoDTO.getWarehouseId(), orderMsg);
    }

    /**
     * 查找出库单待出库的产品集合
     *
     * @return
     */
    public List<Long> listOutStockOrderProduct(OutStockOrderItemProductSO so) {
        return outStockOrderMapper.listOutStockOrderProduct(so);
    }

    /**
     * 查找待出库订单项列表
     *
     * @return
     */
    public PageList<OutStockOrderWaitDeliveryDTO> listOutStockOrderItemWaitDelivery(OutStockOrderWaitDeliverySO so) {
        PageResult<OutStockOrderWaitDeliveryDTO> pageResult = outStockOrderMapper.listOutStockOrderItemWaitDelivery(so);
        // 获取城市名称
        setCityName(pageResult);
        return pageResult.toPageList();
    }

    /**
     * 设置城市名称
     */
    private void setCityName(PageResult<OutStockOrderWaitDeliveryDTO> pageResult) {
        if (null == pageResult || null == pageResult.toPageList()) {
            return;
        }
        // 获取城市名称
        List<OutStockOrderWaitDeliveryDTO> orderList = pageResult.toPageList().getDataList();
        if (CollectionUtils.isNotEmpty(orderList)) {
            List<Integer> cityIds = orderList.stream().filter(p -> p.getCityId() != null).map(p -> p.getCityId())
                .distinct().collect(Collectors.toList());
            Map<Integer, OrgDTO> cityMap = iOrgService.findOrgByIds(cityIds);
            if (null != cityMap) {
                orderList.forEach(p -> {
                    OrgDTO orgDTO = cityMap.get(p.getCityId());
                    p.setCityName(orgDTO != null ? orgDTO.getOrgName() : null);
                });
            }
        }
    }

    /**
     * 根据波次id查询订单产品明细
     */
    public PageList<OutStockOrderByProductDTO> listOrderProductByBatchId(OutStockOrderByProductSO so) {
        PageResult<OutStockOrderByProductDTO> pageResult = outStockOrderMapper.listOrderProductByBatchId(so);
        return pageResult.toPageList();
    }

    /**
     * 根据波次id查询产品汇总
     */
    public PageList<OutStockOrderByProductDTO> listProductGroupByBatchId(OutStockOrderByProductSO so) {
        PageResult<OutStockOrderByProductDTO> pageResult = outStockOrderMapper.listProductGroupByBatchId(so);
        return pageResult.toPageList();
    }

    /**
     * 查询待出库的延迟配送订单
     */
    public List<OutStockOrderDTO> findDelayOrderByWarehouseId(Integer warehouseId) {
        List<OutStockOrderPO> poList = outStockOrderMapper.findDelayOrderByWarehouseId(warehouseId);
        if (CollectionUtils.isEmpty(poList)) {
            return null;
        }
        List<OutStockOrderDTO> orderList = new ArrayList<>();
        poList.forEach(p -> {
            // 订单
            OutStockOrderDTO order = WaveOrderConvertor.outStockOrderPO2OutStockOrderDTO(p);
            if (null != order) {
                // 订单项
                if (CollectionUtils.isNotEmpty(p.getItems())) {
                    List<OutStockOrderItemDTO> orderItemList = new ArrayList<>();
                    p.getItems().forEach(item -> {
                        OutStockOrderItemDTO orderItem =
                            WaveOrderConvertor.outStockOrderItemPO2OutStockOrderItemDTO(item);
                        if (null != orderItem) {
                            orderItemList.add(orderItem);
                        }
                    });
                    if (CollectionUtils.isNotEmpty(orderItemList)) {
                        order.setItemList(orderItemList);
                    }
                }
                orderList.add(order);
            }
        });
        return orderList;
    }

    /**
     * 获取产品分配波次的数量
     *
     * @return
     */
    public Map<Long, ProductAllotTypeDTO> getProductAllotCountMap(Integer cityId, Integer warehouseId,
        List<Long> productSkuIds) {
        AssertUtils.notNull(cityId, "城市ID不能为空");
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(productSkuIds, "产品skuId集合不能为空");

        Map<Long, ProductAllotTypeDTO> resultMap = new HashMap<>();

        List<ProductAllotStoreDTO> productAllotStoreDTOS =
            outStockOrderMapper.listProductAllotStore(cityId, warehouseId, productSkuIds);
        if (CollectionUtils.isNotEmpty(productAllotStoreDTOS)) {
            Map<Long, List<ProductAllotStoreDTO>> allotStoreMap =
                productAllotStoreDTOS.stream().collect(Collectors.groupingBy(p -> p.getProductSkuId()));
            allotStoreMap.forEach((skuId, list) -> {
                ProductAllotTypeDTO productAllotTypeDTO = new ProductAllotTypeDTO();

                // 未分配波次数量
                List<ProductAllotStoreDTO> notAllotList =
                    list.stream().filter(p -> Objects.equals(p.getAllotType(), ConditionStateEnum.否.getType()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(notAllotList)) {
                    productAllotTypeDTO.setNotAllotCount(notAllotList.get(0).getUnitTotalCount());
                } else {
                    productAllotTypeDTO.setNotAllotCount(BigDecimal.ZERO);
                }

                // 已分配波次数量
                List<ProductAllotStoreDTO> allotList =
                    list.stream().filter(p -> Objects.equals(p.getAllotType(), ConditionStateEnum.是.getType()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(allotList)) {
                    productAllotTypeDTO.setAllotCount(allotList.get(0).getUnitTotalCount());
                } else {
                    productAllotTypeDTO.setAllotCount(BigDecimal.ZERO);
                }

                resultMap.put(skuId, productAllotTypeDTO);
            });
        }

        return resultMap;
    }

    /**
     * 查询出库单（详情）列表
     */
    public PageList<OutStockOrderDTO> findOutStockOrderItemList(OutStockOrderSearchSO dto, boolean descending) {
        PageList<OutStockOrderDTO> outStockOrderDTOPageList = new PageList<>();
        PageResult<OutStockOrderPO> pageResult =
            outStockOrderMapper.findOutStockOrderItemList(dto, dto.getCurrentPage(), dto.getPageSize(), descending);
        List<OutStockOrderDTO> outStockOrderDTOS = new ArrayList<>();
        if (pageResult.toPageList() != null && CollectionUtils.isNotEmpty(pageResult.toPageList().getDataList())) {
            pageResult.toPageList().getDataList().forEach(data -> {
                OutStockOrderDTO outStockOrderDTO = WaveOrderConvertor.outStockOrderPO2OutStockOrderDTO(data);
                if (CollectionUtils.isNotEmpty(data.getItems())) {
                    List<OutStockOrderItemDTO> outStockOrderItemDTOs = new ArrayList<>();
                    data.getItems().forEach(item -> {
                        OutStockOrderItemDTO outStockOrderItemDTO =
                            WaveOrderConvertor.outStockOrderItemPO2OutStockOrderItemDTO(item);
                        outStockOrderItemDTOs.add(outStockOrderItemDTO);
                    });
                    outStockOrderDTO.setItemList(outStockOrderItemDTOs);
                }
                outStockOrderDTOS.add(outStockOrderDTO);
            });
        }
        outStockOrderDTOPageList.setPager(pageResult.getPager());
        outStockOrderDTOPageList.setDataList(outStockOrderDTOS);
        return outStockOrderDTOPageList;
    }

    /**
     * 查询出库单项变更记录
     *
     * @return
     */
    public PageList<OutStockOrderItemChangeRecordDTO>
        listOrderItemChangeRecord(OutStockOrderItemChangeRecordSO recordSO) {
        PageList<OutStockOrderItemChangeRecordDTO> pageList = new PageList<>();
        PageResult<OutStockOrderItemChangeRecordPO> pageResult =
            outStockOrderItemChangeRecordMapper.listOrderItemChangeRecord(recordSO);
        List<OutStockOrderItemChangeRecordDTO> dtoList = new ArrayList<>();
        if (pageResult.toPageList() != null && CollectionUtils.isNotEmpty(pageResult.toPageList().getDataList())) {
            pageResult.toPageList().getDataList().forEach(recordPO -> {
                OutStockOrderItemChangeRecordDTO recordDTO = new OutStockOrderItemChangeRecordDTO();
                BeanUtils.copyProperties(recordPO, recordDTO);
                dtoList.add(recordDTO);
            });
        }
        pageList.setPager(pageResult.getPager());
        pageList.setDataList(dtoList);
        return pageList;
    }

    /**
     * 根据订单ID获取订单明细
     *
     * @return
     */
    public List<OutStockOrderItemDTO> listOutStockOrderItemByOrderId(Long orderId) {
        AssertUtils.notNull(orderId, "订单id不能为空");
        List<OutStockOrderItemPO> outStockOrderItemPOS =
            outStockOrderItemMapper.findByOutstockorderIdList(Arrays.asList(orderId));
        if (CollectionUtils.isEmpty(outStockOrderItemPOS)) {
            return Collections.EMPTY_LIST;
        }
        List<OutStockOrderItemDTO> outStockOrderItemDTOS = outStockOrderItemPOS.stream()
            .map(p -> WaveOrderConvertor.outStockOrderItemPO2OutStockOrderItemDTO(p)).collect(Collectors.toList());
        // 检查数字分销订单检查是否控货
        checkRetailOrderControl(outStockOrderItemDTOS);
        return outStockOrderItemDTOS;
    }

    /**
     * 查询订单的快递面单信息
     */
    public String findExpressBillByOrderNo(Integer cityId, Integer warehouseId, String orderNo) {
        // 查询订单信息，获取oms订单id
        OutStockOrderPO outStockOrderPO = outStockOrderMapper.getByOrderNo(cityId, warehouseId, orderNo);
        OrgDTO org = iOrgService.getOrg(cityId);

        // 获取快递面单配置
        FaceSheetManagerDTO faceSheetManagerDTO = new FaceSheetManagerDTO();
        faceSheetManagerDTO.setCompanyId(org.getParentOrgId());
        faceSheetManagerDTO.setOrderNo(orderNo);
        faceSheetManagerDTO.setOrderId(outStockOrderPO.getBusinessId() == null ? outStockOrderPO.getId().toString()
            : outStockOrderPO.getBusinessId());
        List<FaceSheetSettingDTO> orderFaceSheet = iFaceSheetManagerService.getOrderFaceSheet(faceSheetManagerDTO);

        // 获取快递面单信息
        if (CollectionUtils.isEmpty(orderFaceSheet)) {
            throw new BusinessValidateException("仓库id:" + warehouseId + ",请设置快递配置");
        }
        faceSheetManagerDTO.setFaceSheetingId(orderFaceSheet.get(0).getId());
        OrderFaceSheetRsultDTO faceSheetPrint = iFaceSheetManagerService.getFaceSheetPrint(faceSheetManagerDTO);
        return faceSheetPrint.getPrintTemplate();
    }

    /**
     * 查询出库单的分拣顺序
     */
    public List<OrderPickSequenceDTO> listPickSequence(OrderPickSequenceQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "参数不能为空");
        AssertUtils.notNull(queryDTO.getOrgId(), "城市ID不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(queryDTO.getOrderNos(), "出库单号不能为空");
        BatchTaskQueryDTO batchTaskQueryDTO = new BatchTaskQueryDTO();
        batchTaskQueryDTO.setCityId(queryDTO.getOrgId());
        batchTaskQueryDTO.setWarehouseId(queryDTO.getWarehouseId());
        batchTaskQueryDTO.setRefOrderNoList(queryDTO.getOrderNos());
        batchTaskQueryDTO.setQueryItem(true);
        batchTaskQueryDTO.setQueryCondition(Arrays.asList(1));
        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.listBatchTask(batchTaskQueryDTO);
        if (CollectionUtils.isEmpty(batchTaskPOList)) {
            return Collections.emptyList();
        }

        List<OrderPrintSequenceDTO> orderPrintSequenceList = batchOrderProcessBL.getOrderPrintSequenceList(
            batchTaskPOList,
            batchTaskPOList.stream().flatMap(ele -> ele.getBatchTaskItemList().stream()).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(orderPrintSequenceList)) {
            return Collections.emptyList();
        }

        return orderPrintSequenceList.stream().map(elem -> {
            OrderPickSequenceDTO seq = new OrderPickSequenceDTO();
            seq.setOrderId(elem.getBusinessId());
            seq.setOrderItemId(elem.getBusinessItemId());
            seq.setPickSequence(elem.getSequnce());
            return seq;
        }).collect(Collectors.toList());
    }

    /**
     * 根据订单号或者面单号查询订单
     */
    public PageList<OutStockOrderPagesDTO> getOrderByCode(OrderQueryDTO queryDTO) {
        LOG.info("查询订单打印参数:{}", JSON.toJSONString(queryDTO));
        String code = queryDTO.getCode();
        Integer warehouseId = queryDTO.getWarehouseId();
        // 获取订单打印信息
        List<OrderPrintDTO> printDTOS = iOrderPrintService.getOrderByCode(code);
        if (!CollectionUtils.isEmpty(printDTOS)) {
            code = printDTOS.get(0).getOrderNo();
        }
        OutStockOrderSearchSO dto = new OutStockOrderSearchSO();
        dto.setRefOrderNo(code);
        dto.setWareHouseId(warehouseId);
        dto.setOrderStates(queryDTO.getStateList());
        dto.setOrderTypes(Arrays.asList(0, 1, 2, 3, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 25, 30, 50, 51, 52, 53, 54, 55,
            56, 57, 58, 59));
        dto.setOrderSourceTypeList(queryDTO.getOrderSourceTypeList());

        PageList<OutStockOrderDTO> outStockOrderList = new PageList<>();
        PageResult<OutStockOrderPO> pageResult =
            outStockOrderMapper.findOutStockOrderItemList(dto, dto.getCurrentPage(), dto.getPageSize(), true);
        List<OutStockOrderDTO> outStockOrderDTOS = new ArrayList<>();
        if (pageResult.toPageList() != null && CollectionUtils.isNotEmpty(pageResult.toPageList().getDataList())) {
            pageResult.toPageList().getDataList().forEach(data -> {
                OutStockOrderDTO outStockOrderDTO = WaveOrderConvertor.outStockOrderPO2OutStockOrderDTO(data);
                if (CollectionUtils.isNotEmpty(data.getItems())) {
                    List<OutStockOrderItemDTO> outStockOrderItemDTOs = new ArrayList<>();
                    data.getItems().forEach(item -> {
                        OutStockOrderItemDTO outStockOrderItemDTO =
                            WaveOrderConvertor.outStockOrderItemPO2OutStockOrderItemDTO(item);
                        outStockOrderItemDTOs.add(outStockOrderItemDTO);
                    });
                    outStockOrderDTO.setItemList(outStockOrderItemDTOs);
                }
                outStockOrderDTOS.add(outStockOrderDTO);
            });
        }
        outStockOrderList.setPager(pageResult.getPager());
        outStockOrderList.setDataList(outStockOrderDTOS);

        PageList<OutStockOrderPagesDTO> pageList = new PageList<>();
        pageList.setPager(outStockOrderList.getPager());
        if (outStockOrderList == null
            || org.springframework.util.CollectionUtils.isEmpty(outStockOrderList.getDataList())) {
            return pageList;
        }
        List<OutStockOrderPagesDTO> outStockOrderPagesDTOList = new ArrayList<>();
        outStockOrderList.getDataList().forEach(it -> {
            OutStockOrderPagesDTO orderPagesDTO = new OutStockOrderPagesDTO();
            BeanUtils.copyProperties(it, orderPagesDTO);
            if (!org.springframework.util.CollectionUtils.isEmpty(printDTOS)) {
                List<LogisticsDTO> logisticsList = new ArrayList<>();
                printDTOS.forEach(print -> {
                    LogisticsDTO logisticsDTO = new LogisticsDTO();
                    logisticsDTO.setNo(print.getLogisticCode());
                    logisticsDTO.setCompanyCode(print.getShipperCode());
                    logisticsDTO.setCompanyName(print.getShipperName());
                    logisticsList.add(logisticsDTO);
                });
                orderPagesDTO.setLogisticsList(logisticsList);
                orderPagesDTO.setLogisticCode(printDTOS.get(0).getLogisticCode());
                orderPagesDTO.setShipperCode(printDTOS.get(0).getShipperCode());
                orderPagesDTO.setShipperName(printDTOS.get(0).getShipperName());
            }
            outStockOrderPagesDTOList.add(orderPagesDTO);

        });
        pageList.setDataList(outStockOrderPagesDTOList);

        return pageList;
    }

    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    public void fixCount(UpdateOrderItemCountDTO dto) {
        if (!StringUtils.isBlank(dto.getBatchTaskItemId())) {
            BatchTaskItemPO batchTaskItemPO = new BatchTaskItemPO();
            batchTaskItemPO.setId(dto.getBatchTaskItemId());
            batchTaskItemPO.setUnitTotalCount(dto.getUnitTotalCount());
            batchTaskItemPO.setUnitCount(dto.getUnitTotalCount());
            batchTaskItemPO.setPackageCount(dto.getPackageCount());
            batchTaskItemMapper.updateBatchTaskItem(batchTaskItemPO);
        }
        if (Objects.nonNull(dto.getOrderItemTaskInfoId())) {
            List<OrderItemTaskInfoPO> recordPOList = new ArrayList<>();
            OrderItemTaskInfoPO update = new OrderItemTaskInfoPO();
            update.setId(dto.getOrderItemTaskInfoId());
            update.setUnitTotalCount(dto.getUnitTotalCount());
            recordPOList.add(update);
            orderItemTaskInfoMapper.updateBatch(recordPOList);
        }

    }

    /**
     * 数字分销订单检查是否控货
     *
     * @return
     */
    public void checkRetailOrderControl(List<OutStockOrderItemDTO> outStockOrderItemDTOS) {
        if (CollectionUtils.isEmpty(outStockOrderItemDTOS)) {
            return;
        }

        OutStockOrderPO outStockOrderPO =
            outStockOrderMapper.selectByRefOrderId(outStockOrderItemDTOS.get(0).getOutstockorder_Id());
        if (outStockOrderPO == null
            || !Objects.equals(outStockOrderPO.getOrderSourceType(), SourceType.RETAIL.getValue())) {
            return;
        }

        setControlConfigInfo(outStockOrderItemDTOS, outStockOrderPO.getOrgId());
    }

    /**
     * 订单查询是否控货
     *
     * @return
     */
    public void setControlConfigInfo(List<OutStockOrderItemDTO> orderItemList, Integer orgId) {
        // 查询控货策略
        // businessItemId -> 控货策略信息
        Map<Long, OrderTraceableItemDTO> orderItemConfigMap = null;
        // 控货策略ID -> 控货策略名称
        Map<Long, String> configNameMap = null;
        List<Long> businessItemIdList = orderItemList.stream().map(OutStockOrderItemDTO::getBusinessItemId)
            .filter(Objects::nonNull).distinct().map(Long::valueOf).collect(Collectors.toList());
        if (!org.springframework.util.CollectionUtils.isEmpty(businessItemIdList)) {
            List<OrderTraceableItemDTO> orderTraceableItemList =
                iTransferOrderQueryService.selectOrderTraceableItemInfoByOrderItemIds(businessItemIdList);
            LOG.info("查询控货策略 setControlConfigInfo businessItemIdList={}, 结果：={}", JSON.toJSONString(businessItemIdList),
                JSON.toJSONString(orderTraceableItemList));
            if (!org.springframework.util.CollectionUtils.isEmpty(orderTraceableItemList)) {
                orderItemConfigMap = orderTraceableItemList.stream()
                    .filter(elem -> elem.getOrderItemId() != null && elem.getProductControlId() != null).collect(
                        Collectors.toMap(OrderTraceableItemDTO::getOrderItemId, Function.identity(), (k1, k2) -> k1));
                List<Long> configIdList =
                    orderTraceableItemList.stream().map(OrderTraceableItemDTO::getProductControlId)
                        .filter(Objects::nonNull).collect(Collectors.toList());
                if (!org.springframework.util.CollectionUtils.isEmpty(configIdList)) {
                    configNameMap = controlConfigService.getConfigItemNameMap(configIdList);
                }
            }
        }

        for (OutStockOrderItemDTO orderItem : orderItemList) {
            // 设置控货策略名称
            if (orderItemConfigMap != null && orderItem.getBusinessItemId() != null) {
                OrderTraceableItemDTO traceableItem =
                    orderItemConfigMap.get(Long.valueOf(orderItem.getBusinessItemId()));
                if (traceableItem != null) {
                    orderItem.setControlConfigId(traceableItem.getProductControlId());
                    if (configNameMap != null) {
                        orderItem.setControlConfigName(configNameMap.get(orderItem.getControlConfigId()));
                    }
                }
            }

            // 设置溯源码个数
            if (orderItem.getControlConfigId() != null && orderItem.getUnitTotalCount() != null
                && orderItem.getSaleSpecQuantity() != null
                && orderItem.getSaleSpecQuantity().compareTo(BigDecimal.ZERO) != 0) {
                orderItem.setSourceCodeCount(
                    orderItem.getUnitTotalCount().divide(orderItem.getSaleSpecQuantity(), 2, RoundingMode.HALF_UP));
            }
        }
        LOG.info("查询控货策略 setControlConfigInfo 结果：={}", JSON.toJSONString(orderItemList));
    }

    /**
     * 根据分仓配置类型设置订单特征查询条件
     */
    public void setPackageAttributteByConfigType(OutStockOrderSearchSO dto) {
        LOG.info("根据分仓配置类型设置订单特征查询条件 入参：" + JSON.toJSONString(dto));
        if (Objects.isNull(dto) || CollectionUtils.isEmpty(dto.getAllocationConfigTypes())) {
            return;
        }

        // 目前分仓配置类型只有酒饮和休食，用户有两种分仓权限时，查询不限制订单特征
        List<Byte> packageAttributteList = new ArrayList<>();
        if (dto.getAllocationConfigTypes().stream()
            .allMatch(configType -> Objects.equals(configType, WarehouseAllocationConfigType.DRINKING.getValue()))) {
            packageAttributteList.add(OrderFeatureConstant.FEATURE_TYPE_DRINKING.byteValue());
        } else if (dto.getAllocationConfigTypes().stream()
            .allMatch(configType -> Objects.equals(configType, WarehouseAllocationConfigType.REST.getValue()))) {
            packageAttributteList.add(OrderFeatureConstant.FEATURE_TYPE_REST.byteValue());
        }

        if (CollectionUtils.isEmpty(packageAttributteList)) {
            return;
        }

        dto.setPackageAttribute(packageAttributteList);
        LOG.info("根据分仓配置类型设置订单特征查询条件 结果：" + JSON.toJSONString(dto));
    }

    /**
     * 扫单复核查询
     *
     * @param queryDTO
     * @return
     */
    public ScanOrderForReviewDTO queryScanOrderInfoFroReview(ScanOrderForReviewQueryDTO queryDTO) {
        OutStockOrderPO outStockOrderPO = getAndValidateOutStockOrder(queryDTO);

        List<Long> lstOrderId = Collections.singletonList(outStockOrderPO.getId());
        List<PackageOrderItemDTO> packageOrderItemDTOList = PageHelperUtils.splitPageQuery(lstOrderId, 500,
            it -> packageOrderItemMapper.listBoxesByOrderIds(it, queryDTO.getOrgId()));

        // 验证托盘号
        validatePalletNo(outStockOrderPO);

        ScanOrderForReviewOrderInfoDTO scanOrderForReviewOrderInfoDTO = new ScanOrderForReviewOrderInfoDTO();

        String batchNo = outStockOrderPO.getBatchno();

        SameUserOrderInBatchQueryPO queryPO = new SameUserOrderInBatchQueryPO();
        queryPO.setAddressId(outStockOrderPO.getAddressId());
        queryPO.setOrderFeatureType(OrderFeatureConstant.FEATURE_TYPE_DRINKING);
        queryPO.setOrderCreateTime(DateUtils.getMinusMonthFirstDay(3));
        queryPO.setStateList(Arrays.asList(OutStockOrderStateEnum.已拣货.getType(), OutStockOrderStateEnum.待拣货.getType(),
            OutStockOrderStateEnum.待调度.getType(), OutStockOrderStateEnum.拣货中.getType()));
        queryPO.setWarehouseId(outStockOrderPO.getWarehouseId());

        // 查询对应的酒饮订单
        List<OutStockOrderPO> wineOutStockOrderPOList = outStockOrderMapper.findSameUserOrderInBatch(queryPO);
        List<OutStockOrderItemPO> wineOutStockOrderItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(wineOutStockOrderPOList)) {
            List<Long> outStockOrderIds =
                wineOutStockOrderPOList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());

            wineOutStockOrderItemList = outStockOrderItemMapper.findByOutstockorderIdList(outStockOrderIds);
        }

        // 查询休食订单的拣货任务
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(lstOrderId);
        List<String> batchTaskIdList = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getBatchTaskId)
            .distinct().collect(Collectors.toList());
        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findBatchTaskByIds(batchTaskIdList);
        // 查询对应休食订单的托盘信息
        OrderLocationPalletQueryDTO restOrderLocationPalletQueryDTO = new OrderLocationPalletQueryDTO();
        restOrderLocationPalletQueryDTO.setOrderIdList(lstOrderId);
        restOrderLocationPalletQueryDTO.setWarehouseId(outStockOrderPO.getWarehouseId());
        List<OrderLocationPalletDTO> restPalletDTOList =
            iOrderLocationPalletService.findPalletByCondition(restOrderLocationPalletQueryDTO);

        // 没有酒饮订单时
        if (CollectionUtils.isEmpty(wineOutStockOrderPOList)) {
            return ScanOrderForReviewDTOConvertor.convertWithoutWine(outStockOrderPO, batchTaskPOList,
                packageOrderItemDTOList, restPalletDTOList);
        }

        OrderLocationPalletQueryDTO orderLocationPalletQueryDTO = new OrderLocationPalletQueryDTO();
        // 查询对应酒饮的托盘信息
        orderLocationPalletQueryDTO.setOrderIdList(
            wineOutStockOrderPOList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList()));
        orderLocationPalletQueryDTO.setWarehouseId(outStockOrderPO.getWarehouseId());
        List<OrderLocationPalletDTO> winePalletDTOList =
            iOrderLocationPalletService.findPalletByCondition(orderLocationPalletQueryDTO);

        // 有酒饮订单未绑定时
        if (CollectionUtils.isEmpty(winePalletDTOList)) {
            return ScanOrderForReviewDTOConvertor.convertWithWineNotBind(outStockOrderPO, batchTaskPOList,
                packageOrderItemDTOList, wineOutStockOrderItemList, wineOutStockOrderPOList, restPalletDTOList);
        }

        // 有酒饮订单绑定时
        ScanOrderForReviewDTO scanOrderForReviewDTO = ScanOrderForReviewDTOConvertor.convertWithWineBind(
            outStockOrderPO, batchTaskPOList, packageOrderItemDTOList, winePalletDTOList, wineOutStockOrderItemList,
            wineOutStockOrderPOList, restPalletDTOList);
        return scanOrderForReviewDTO;
    }

    private void validatePalletNo(OutStockOrderPO outStockOrderPO) {
        OrderLocationPalletQueryDTO orderLocationPalletQueryDTO = new OrderLocationPalletQueryDTO();
        orderLocationPalletQueryDTO.setOrderNo(outStockOrderPO.getReforderno());
        orderLocationPalletQueryDTO.setWarehouseId(outStockOrderPO.getWarehouseId());
        List<OrderLocationPalletDTO> palletDTOList =
            iOrderLocationPalletService.findPalletByCondition(orderLocationPalletQueryDTO);
        if (CollectionUtils.isEmpty(palletDTOList)) {
            return;
        }

        String locationName =
            palletDTOList.stream().map(OrderLocationPalletDTO::getLocationName).distinct().findAny().get();
        String palletNo =
            palletDTOList.stream().map(OrderLocationPalletDTO::getPalletNo).collect(Collectors.joining(","));
        throw new BusinessValidateException("订单已完成复核，出库位" + locationName + "，托盘号 " + palletNo);
    }

    private OutStockOrderPO getAndValidateOutStockOrder(ScanOrderForReviewQueryDTO queryDTO) {
        // 先查订单
        List<OutStockOrderPO> orderList = outStockOrderMapper
            .findIdByOrderNos(Collections.singletonList(queryDTO.getScanCode()), queryDTO.getWarehouseId());

        // 没查询到再根据箱码查订单
        if (CollectionUtils.isEmpty(orderList)) {
            List<String> orderNoList = packageOrderItemMapper.listOrderNoByBoxCodeNo(queryDTO.getScanCode());

            if (CollectionUtils.isEmpty(orderNoList)) {
                throw new BusinessValidateException("未找到对应的订单，请检查标签是否正确！");
            }

            orderList = outStockOrderMapper.findIdByOrderNos(orderNoList, queryDTO.getWarehouseId());
        }
        if (CollectionUtils.isEmpty(orderList)) {
            throw new BusinessValidateException("未找到对应的订单，请检查标签是否正确！");
        }

        OutStockOrderPO outStockOrderPO = orderList.get(0);

        if ((outStockOrderPO.getState().byteValue() == OutStockOrderStateEnum.已出库.getType())) {
            throw new BusinessValidateException("订单：" + outStockOrderPO.getReforderno() + "已出库，无需复核！");
        }

        if (outStockOrderPO.getState().byteValue() != OutStockOrderStateEnum.已拣货.getType()) {
            throw new BusinessValidateException("订单无需复核，请检查订单状态是否是已拣货未复核");
        }

        Map<Long, List<Byte>> featureMap =
            orderFeatureBL.getOrderFeatureMap(Collections.singletonList(outStockOrderPO.getId()));

        boolean isRestFeature = featureMap.getOrDefault(outStockOrderPO.getId(), Collections.emptyList()).stream()
            .anyMatch(OrderFeatureConstant.FEATURE_TYPE_REST::equals);

        if (BooleanUtils.isFalse(isRestFeature)) {
            throw new BusinessValidateException(outStockOrderPO.getReforderno() + " 不是休食订单！只有休食订单才可以复核！");
        }

        return outStockOrderPO;
    }

    /**
     * 扫码复核后设置出库位和托盘位
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void scanReviewToSetLocationPalletInfo(ScanReviewToSetLocationPalletInfoDTO dto) {
        LOG.info("扫码复核后设置出库位和托盘位 : {}", JSON.toJSONString(dto));
        List<OutStockOrderPO> orderList =
            outStockOrderMapper.findIdByOrderNos(Collections.singletonList(dto.getOrderNo()), dto.getWarehouseId());
        if (CollectionUtils.isEmpty(orderList)) {
            throw new BusinessValidateException("出库单不存在");
        }
        validatePalletNoLegal(dto.getLocationId(), dto.getPalletNoList());
        OutStockOrderPO outStockOrderPO = orderList.get(0);
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(Collections.singletonList(outStockOrderPO.getId()));
        List<String> batchTaskIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getBatchTaskId).distinct()
            .collect(Collectors.toList());

        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findBatchTaskByIds(batchTaskIds);

        boolean pickingByProduct =
            batchTaskPOList.stream().anyMatch(b -> b.getPickingType().equals(PickingTypeEnum.产品拣货.getType()));

        if (pickingByProduct) {
            throw new BusinessValidateException("按产品拣货不支持！");
        }

        // 先加托盘，后面更新出库位会通知tms
        batchTaskPOList.forEach(batchTaskPO -> {
            OrderLocationPalletDTO palletDTO = new OrderLocationPalletDTO();
            palletDTO.setOrgId(Integer.valueOf(batchTaskPO.getOrgId()));
            palletDTO.setWarehouseId(batchTaskPO.getWarehouseId());
            palletDTO.setLocationId(dto.getLocationId());
            palletDTO.setLocationName(dto.getLocationName());
            palletDTO.setBatchTaskId(batchTaskPO.getId());
            List<OrderPalletInfoDTO> palletInfoDTOS = dto.getPalletNoList().stream().map(p -> {
                OrderPalletInfoDTO orderPalletInfoDTO = new OrderPalletInfoDTO();
                orderPalletInfoDTO.setOrderNo(outStockOrderPO.getReforderno());
                orderPalletInfoDTO.setOrderId(outStockOrderPO.getId());
                orderPalletInfoDTO.setPalletNoList(Collections.singletonList(p));
                return orderPalletInfoDTO;
            }).collect(Collectors.toList());
            palletDTO.setOrderPalletInfoDTOS(palletInfoDTOS);
            palletDTO.setLastUpdateUser(globalCache.getAdminTrueName(dto.getOptUserId()));
            iOrderLocationPalletService.addPalletByOrderId(palletDTO);
        });

        BatchTaskUpdateLocationDTO batchTaskUpdateLocationDTO = new BatchTaskUpdateLocationDTO();
        batchTaskUpdateLocationDTO.setLocationId(dto.getLocationId());
        batchTaskUpdateLocationDTO.setBatchTaskIdList(batchTaskIds);
        batchTaskUpdateLocationDTO.setPalletNoList(dto.getPalletNoList());
        batchTaskUpdateLocationDTO.setOperateUser(dto.getOptUserId().toString());
        batchTaskUpdateLocationDTO.setLocationName(dto.getLocationName());
        batchOrderTaskBL.updateBatchTaskLocationByCk(batchTaskUpdateLocationDTO);
    }

    private void validatePalletNoLegal(Long locationId, List<String> palletNoList) {
        List<LoactionDTO> loactionDTOList = iLocationService.findLocationByIds(Collections.singletonList(locationId));
        if (CollectionUtils.isEmpty(loactionDTOList)) {
            throw new BusinessValidateException("出库位不存在，请确认！");
        }

        LoactionDTO loactionDTO = loactionDTOList.get(0);
        if (Objects.isNull(loactionDTO.getPalletCount())) {
            return;
        }
        int palletCount = loactionDTO.getPalletCount();

        palletNoList.forEach(palletNo -> {
            try {
                int palletNoInt = Integer.parseInt(palletNo);
                if (palletNoInt > palletCount) {
                    throw new BusinessValidateException("托盘号应该小于" + palletCount + "，传入托盘号" + palletNo + "非法！");
                }
            } catch (NumberFormatException e) {
                LOG.warn("转换托盘位信息失败！" + palletNo, e);
            }
        });

    }

    /**
     * 托盘复核查询
     */
    public PalletReviewInfoDTO queryPalletReviewInfo(PalletReviewQueryDTO queryDTO) {
        LOG.info("托盘复核查询入参：" + JSON.toJSONString(queryDTO));
        Warehouse warehouse = globalCache.getWarehouse(queryDTO.getWarehouseId());
        queryDTO.setOrgId(warehouse.getCityId());

        // 根据托盘号查订单托盘关系
        OrderLocationPalletQueryDTO orderLocationPalletQueryDTO = new OrderLocationPalletQueryDTO();
        orderLocationPalletQueryDTO.setWarehouseId(queryDTO.getWarehouseId());
        orderLocationPalletQueryDTO.setPalletNoList(Arrays.asList(queryDTO.getPalletNo()));
        List<OrderLocationPalletDTO> palletDTOList =
            iOrderLocationPalletService.findPalletByCondition(orderLocationPalletQueryDTO);
        LOG.info("托盘复核托盘查询数据：" + JSON.toJSONString(palletDTOList));
        if (CollectionUtils.isEmpty(palletDTOList)) {
            throw new BusinessValidateException("该托盘不存在，请确认后重新扫描");
        }

        // 过滤出已拣货状态出库单
        List<Long> palletOrderIds =
            palletDTOList.stream().filter(p -> p.getOrderId() != null && p.getLocationId() != null)
                .map(p -> p.getOrderId()).distinct().collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.selectByIds(palletOrderIds).stream()
            .filter(p -> p != null && Objects.equals(p.getState().byteValue(), OutStockOrderStateEnum.已拣货.getType()))
            .collect(Collectors.toList());
        LOG.info("outStockOrderPOS结果：" + JSON.toJSONString(outStockOrderPOS));
        if (CollectionUtils.isEmpty(outStockOrderPOS)) {
            throw new BusinessValidateException("该托盘订单未拣货或已出库，请确认后重新扫描");
        }

        List<Long> orderIds = outStockOrderPOS.stream().filter(p -> p.getId() != null).map(p -> p.getId()).distinct()
            .collect(Collectors.toList());
        // 如果托盘号有对应的多个出库位，则取第一个出库位。然后将相同出库位和托盘号的酒饮订单和休百订单数据聚合起来
        List<OrderLocationPalletDTO> firstPalletDTOList =
            palletDTOList.stream().filter(p -> orderIds.contains(p.getOrderId()))
                .sorted(Comparator.comparing(OrderLocationPalletDTO::getCreateTime))
                .collect(Collectors.groupingBy(p -> p.getLocationId())).values().stream().findFirst()
                .orElse(Collections.emptyList());
        List<Long> firstPalletOrderIds =
            firstPalletDTOList.stream().map(p -> p.getOrderId()).distinct().collect(Collectors.toList());
        List<OutStockOrderPO> firstOutStockOrderPOS =
            outStockOrderPOS.stream().filter(p -> firstPalletOrderIds.contains(p.getId())).collect(Collectors.toList());
        // 根据订单查询其它托盘号
        Map<Long, Set<String>> otherPalletMap = getOtherPalletMap(queryDTO, firstPalletDTOList);
        // 查询订单的拣货任务
        Map<Long, List<BatchTaskSorterDTO>> orderIdSorterMap = getOrderIdSorterMap(firstOutStockOrderPOS);
        // 订单特征
        Map<Long, List<Byte>> orderFeatureMap = orderFeatureBL.getOrderFeatureMap(firstPalletOrderIds);
        // 酒饮订单
        List<OutStockOrderPO> drinkOrderPOS = firstOutStockOrderPOS.stream().filter(m -> {
            List<Byte> orderFeatureList = orderFeatureMap.get(m.getId());
            return orderFeatureList.stream().anyMatch(t -> OrderFeatureConstant.FEATURE_TYPE_DRINKING.equals(t));
        }).collect(Collectors.toList());
        // 休食订单
        List<OutStockOrderPO> restOrderPOS = firstOutStockOrderPOS.stream().filter(m -> {
            List<Byte> orderFeatureList = orderFeatureMap.get(m.getId());
            return orderFeatureList.stream().anyMatch(t -> OrderFeatureConstant.FEATURE_TYPE_REST.equals(t));
        }).collect(Collectors.toList());
        // 酒饮订单查询产品条码箱码
        List<OutStockOrderItemPO> drinkItemPOS = getOrderItemPOS(drinkOrderPOS);
        Map<Long, ProductCodeDTO> packageAndUnitCode = getPackageAndUnitCode(drinkItemPOS);
        // 休食订单查询装箱信息
        Map<Long, Set<String>> orderIdBoxCodeNoMap = getBoxCodeNoMap(restOrderPOS);

        PalletReviewInfoDTO palletReviewInfoDTO = PalletReviewInfoDTOConvertor.convertPalletReviewLocationInfo(
            restOrderPOS, drinkItemPOS, firstOutStockOrderPOS, orderIdSorterMap, orderIdBoxCodeNoMap,
            packageAndUnitCode, firstPalletDTOList, otherPalletMap);
        LOG.info("托盘复核查询结果：" + JSON.toJSONString(palletReviewInfoDTO));
        return palletReviewInfoDTO;
    }

    private List<OutStockOrderItemPO> getOrderItemPOS(List<OutStockOrderPO> orderPOS) {
        if (CollectionUtils.isEmpty(orderPOS)) {
            return Collections.emptyList();
        }

        List<Long> orderIds = orderPOS.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());
        List<OutStockOrderItemPO> drinkItemPOS = outStockOrderItemMapper.findByOutstockorderIdList(orderIds);
        return drinkItemPOS;
    }

    private Map<Long, List<BatchTaskSorterDTO>> getOrderIdSorterMap(List<OutStockOrderPO> outStockOrderPOS) {
        if (CollectionUtils.isEmpty(outStockOrderPOS)) {
            return Collections.EMPTY_MAP;
        }

        List<String> orderNos = outStockOrderPOS.stream().filter(p -> StringUtils.isNotEmpty(p.getReforderno()))
            .map(p -> p.getReforderno()).distinct().collect(Collectors.toList());
        Map<Long, List<BatchTaskSorterDTO>> orderIdSorterMap =
            outStockOrderMapper.listSorterByOrderNos(orderNos, outStockOrderPOS.get(0).getWarehouseId()).stream()
                .filter(sorter -> sorter != null && sorter.getSorterId() != null
                    && StringUtils.isNotEmpty(sorter.getSorterName()))
                .collect(Collectors.groupingBy(BatchTaskSorterDTO::getOrderId));
        return orderIdSorterMap;
    }

    private Map<Long, ProductCodeDTO> getPackageAndUnitCode(List<OutStockOrderItemPO> itemPOS) {
        if (CollectionUtils.isEmpty(itemPOS)) {
            return Collections.EMPTY_MAP;
        }

        Set<Long> skuIds = itemPOS.stream().map(OutStockOrderItemPO::getSkuid).collect(Collectors.toSet());
        Map<Long, ProductCodeDTO> packageAndUnitCode =
            productSkuService.getPackageAndUnitCode(skuIds, itemPOS.get(0).getOrgId());
        return packageAndUnitCode;
    }

    private Map<Long, Set<String>> getBoxCodeNoMap(List<OutStockOrderPO> outStockOrderPOS) {
        if (CollectionUtils.isEmpty(outStockOrderPOS)) {
            return Collections.EMPTY_MAP;
        }

        OutStockOrderPO outStockOrderPO = outStockOrderPOS.stream().findFirst().get();
        List<Long> orderIds = outStockOrderPOS.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());
        List<PackageOrderItemDTO> lstPackageItem = PageHelperUtils.splitPageQuery(orderIds, 500,
            it -> packageOrderItemMapper.listBoxesByOrderIds(it, outStockOrderPO.getOrgId()));

        Map<Long,
            Set<String>> orderIdBoxCodeNoMap = lstPackageItem.stream()
                .filter(p -> p != null && p.getRefOrderId() != null && StringUtils.isNotEmpty(p.getBoxCodeNo()))
                .collect(Collectors.groupingBy(p -> p.getRefOrderId(),
                    Collectors.mapping(p -> p.getBoxCodeNo(), Collectors.toSet())));
        return orderIdBoxCodeNoMap;
    }

    private Map<Long, Set<String>> getOtherPalletMap(PalletReviewQueryDTO queryDTO,
        List<OrderLocationPalletDTO> palletDTOList) {
        if (CollectionUtils.isEmpty(palletDTOList)) {
            return Collections.EMPTY_MAP;
        }

        List<Long> orderIds = palletDTOList.stream().map(p -> p.getOrderId()).distinct().collect(Collectors.toList());
        // 根据订单查询其它托盘号
        OrderLocationPalletQueryDTO otherPalletQueryDTO = new OrderLocationPalletQueryDTO();
        otherPalletQueryDTO.setWarehouseId(queryDTO.getWarehouseId());
        otherPalletQueryDTO.setOrderIdList(orderIds);
        otherPalletQueryDTO.setLocationId(palletDTOList.get(0).getLocationId());
        Map<Long,
            Set<String>> otherPalletMap = iOrderLocationPalletService.findPalletByCondition(otherPalletQueryDTO)
                .stream().filter(p -> p != null && !p.getPalletNo().equals(queryDTO.getPalletNo())).collect(Collectors
                    .groupingBy(p -> p.getOrderId(), Collectors.mapping(p -> p.getPalletNo(), Collectors.toSet())));
        return otherPalletMap;
    }

    /**
     * 查询是否是合并拣货
     *
     * @param queryDTO
     * @return
     */
    public List<MergePickOrderInfoResultDTO> queryMergePickOrderInfo(MergePickOrderInfoQueryDTO queryDTO) {
        List<OutStockOrderPO> orderList = outStockOrderMapper.selectByIds(queryDTO.getOrderIdList());
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyList();
        }

        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(queryDTO.getOrderIdList());

        List<String> batchTaskIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getBatchTaskId).distinct()
            .collect(Collectors.toList());

        List<OrderItemTaskInfoPO> allTaskInfoPOList = orderItemTaskInfoMapper.listTaskInfoByBatchTaskIds(batchTaskIds);
        if (CollectionUtils.isEmpty(allTaskInfoPOList)) {
            return Collections.emptyList();
        }

        List<Long> otherOrderIdList = allTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderId)
            .filter(orderId -> !queryDTO.getOrderIdList().contains(orderId)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(otherOrderIdList)) {
            return Collections.emptyList();
        }

        List<OutStockOrderPO> otherOrderList = outStockOrderMapper.selectByIds(otherOrderIdList);

        List<MergePickOrderValidBO> queryOrderInfoList = MergePickOrderValidBO.convert(orderList, allTaskInfoPOList);
        List<MergePickOrderValidBO> otherOrderInfoList =
            MergePickOrderValidBO.convert(otherOrderList, allTaskInfoPOList);

        if (org.springframework.util.CollectionUtils.isEmpty(otherOrderInfoList)) {
            return Collections.emptyList();
        }

        Map<String, List<MergePickOrderValidBO>> orderGroupMap = MergePickOrderValidBO.convert(otherOrderInfoList);

        List<MergePickOrderInfoResultDTO> resultDTOS = new ArrayList<>();
        for (MergePickOrderValidBO mergePickOrderValidBO : queryOrderInfoList) {
            List<MergePickOrderValidBO> otherList = orderGroupMap.get(mergePickOrderValidBO.getKey());
            if (CollectionUtils.isEmpty(otherList)) {
                continue;
            }

            MergePickOrderInfoResultDTO mergePickOrderInfoResultDTO = new MergePickOrderInfoResultDTO();
            mergePickOrderInfoResultDTO.setIsMergePick(Boolean.TRUE);
            mergePickOrderInfoResultDTO.setOrderId(mergePickOrderValidBO.getOrderId());
            mergePickOrderInfoResultDTO.setOrderNo(mergePickOrderValidBO.getOrderNo());
            mergePickOrderInfoResultDTO.setOtherOrderList(otherList.stream().map(order -> {
                MergePickOrderInfoRelatedOrderResultDTO relatedOrder = new MergePickOrderInfoRelatedOrderResultDTO();
                relatedOrder.setOrderId(order.getOrderId());
                relatedOrder.setOrderNo(order.getOrderNo());
                return relatedOrder;
            }).collect(Collectors.toList()));
            mergePickOrderInfoResultDTO.setWarehouseId(queryDTO.getWarehouseId());
            resultDTOS.add(mergePickOrderInfoResultDTO);
        }

        LOG.info("查询结果为：{}", JSON.toJSONString(resultDTOS));
        return resultDTOS;
    }

}
