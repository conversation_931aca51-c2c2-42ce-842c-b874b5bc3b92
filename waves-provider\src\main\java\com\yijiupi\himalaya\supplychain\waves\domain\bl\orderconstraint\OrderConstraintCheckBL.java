package com.yijiupi.himalaya.supplychain.waves.domain.bl.orderconstraint;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.dto.variable.OrderConstraints;
import com.yijiupi.himalaya.supplychain.dto.variable.OrderVariableQueryDTO;
import com.yijiupi.himalaya.supplychain.outstock.service.IOrderQueryService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.outstockorder.SaleInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuManageService;
import com.yijiupi.himalaya.supplychain.service.IVariableDefinitionService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.OrderItemTaskInfoDetailLackHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.storecheck.PickLackStoreCheckBL;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.SaleInventoryQueryDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum.*;
import static com.yijiupi.himalaya.supplychain.waves.enums.ErrorCodeEnum.ORDER_CANNOT_LACK;
import static com.yijiupi.supplychain.serviceutils.constant.OrderConstant.ALLOT_TYPE_ALLOCATION;
import static java.util.stream.Collectors.*;

/**
 * <AUTHOR>
 * @since 2024-06-04 10:19
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class OrderConstraintCheckBL {

    @Resource
    private GlobalCache globalCache;

    @Resource
    private OutStockOrderMapper outStockOrderMapper;

    @Resource
    private BatchMapper batchMapper;

    @Resource
    private PickLackStoreCheckBL pickLackStoreCheckBL;

    @Reference
    private IVariableDefinitionService variableDefinitionService;

    @Reference
    private IOrderQueryService orderQueryService;

    @Reference
    private IProductSkuManageService productSkuManageService;

    private static final String ORDER_TYPE_INTER_DELIVERY = "内配单";

    private static final String ORDER_TYPE_NORMAL = "普通单";

    private static final String CHECK_SALE_INVENTORY = "校验销售库存";

    private static final String NP_LACK_TEMPLATE = "二级仓不允许对内配单 %s 标记缺货，如有缺货请联系中心仓补发，或者配送时标记部分配送";

    private static final Logger logger = LoggerFactory.getLogger(OrderConstraintCheckBL.class);

    /**
     * 校验内配订单能否缺货
     *
     * @param orders 订单信息
     */
    public void checkNPOrderLack(List<OutStockOrderPO> orders) {
        Set<String> npOrders = orders.stream().filter(this::isNPOrder)
                .map(OutStockOrderPO::getReforderno).collect(Collectors.toSet());
        if (npOrders.isEmpty()) {
            return;
        }
        throw new BusinessValidateException(String.format(NP_LACK_TEMPLATE, String.join(",", npOrders)));
    }

    /**
     * 校验订单能否标记缺货
     *
     * @param orders 订单信息
     */
    public void checkNormalOrderLack(List<OutStockOrderPO> orders) {
        checkNormalOrderLack(orders, OrderConstraints.MARK_LACK);
    }

    /**
     * 校验订单能否拣货缺货
     *
     * @param orders 订单信息
     */
    public void checkNormalOrderPickLack(List<OutStockOrderPO> orders) {
        checkNormalOrderLack(orders, OrderConstraints.PICK_LACK);
    }

    /**
     * 校验普通订单能否拣货缺货, 进这个方法就表示有缺货, 在尝试做缺货操作
     *
     * @param batchNos    波次编号
     * @param tasks       拣货任务
     * @param warehouseId 仓库 id
     */
    public void checkNormalOrderPickLack(List<String> batchNos, List<BatchTaskPO> tasks, Integer warehouseId) {
        // 存在缺货 判断是否为自提订单
        Integer cityId = globalCache.getWarehouse(warehouseId).getCityId();
        List<Integer> outBoundTypeList = outStockOrderMapper.queryOutBoundType(batchNos, cityId);
        logger.info("outBoundTypeList={},batchNos={}", JSON.toJSONString(outBoundTypeList), JSON.toJSONString(batchNos));
        if (outBoundTypeList.stream().anyMatch(SELF_PICKUP_SALE_ORDER::valueEquals)) {
            throw new BusinessValidateException("自提订单不允许缺货");
        }
        // 内配前置仓出库单不允许缺货
        List<String> batchTaskNoList = tasks.stream().map(BatchTaskPO::getBatchTaskNo).distinct().collect(Collectors.toList());
        // FIXME 这个接口换掉
        List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.findByBatchTaskNos(batchTaskNoList, warehouseId);
        if (outStockOrderPOS.stream().anyMatch(this::isNPFront)) {
            throw new BusinessException("二级仓不允许对内配单标记缺货，如有缺货请联系中心仓补发，或者配送时标记部分配送");
        }
        // 校验普通订单能否缺货
        checkNormalOrderPickLack(outStockOrderPOS);
        // 大客户订单不允许缺货
        List<BatchPO> bigCustomerBatch = batchMapper.findBatchByNos(batchNos, cityId).stream()
                .filter(it -> BatchTypeEnum.大客户.valueEquals(it.getBatchType())).collect(Collectors.toList());
        if (!bigCustomerBatch.isEmpty()) {
            throw new BusinessValidateException("大客户订单不允许缺货");
        }
        checkBigCustomerLackByBatchNo(batchNos, cityId);
    }

    /**
     * PDA 拣货校验普通订单和内配订单能否拣货缺货
     *
     * @param completeItem pda 提交拣货完成的项
     * @param orders       订单信息
     * @param itemInfo     拣货任务订单关联信息
     */
    public void checkNormalOrderPickLack(Integer warehouseId, List<BatchTaskItemCompleteDTO> completeItem, List<OutStockOrderPO> orders, List<OrderItemTaskInfoPO> itemInfo) {
        List<BatchTaskItemCompleteDTO> lackItems = completeItem.stream().filter(this::isPickLack).collect(toList());
        if (lackItems.isEmpty()) {
            return;
        }
        Set<String> lackTaskItemIds = lackItems.stream().map(BatchTaskItemCompleteDTO::getId).collect(toSet());
        Map<Long, OutStockOrderPO> orderMap = orders.stream().collect(toMap(OutStockOrderPO::getId, Function.identity()));
        List<OutStockOrderPO> lackOrders = itemInfo.stream().filter(k -> lackTaskItemIds.contains(k.getBatchTaskItemId()))
                .map(OrderItemTaskInfoPO::getRefOrderId).map(orderMap::get).filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 校验订单能否缺货
        checkNormalOrderPickLack(lackOrders);
        List<OutStockOrderPO> npOutStockOrderList = lackOrders.stream().filter(this::isAllotOrder).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(npOutStockOrderList)) {
            throw new BusinessValidateException("二级仓不允许对内配单标记缺货，如有缺货请联系中心仓补发，或者配送时标记部分配送");
        }
        // 校验大客户订单
        Map<String, Set<String>> batchTaskItemMap = itemInfo.stream()
                .collect(groupingBy(OrderItemTaskInfoPO::getBatchNo, mapping(OrderItemTaskInfoPO::getBatchTaskItemId, toSet())));
        Integer orgId = itemInfo.get(0).getOrgId();
        Set<String> bigCustomerTaskItemIds = batchMapper.findBatchByNos(batchTaskItemMap.keySet(), orgId).stream()
                .filter(it -> BatchTypeEnum.大客户.valueEquals(it.getBatchType()))
                .map(BatchPO::getBatchNo).map(batchTaskItemMap::get).filter(Objects::nonNull)
                .flatMap(Collection::stream).collect(toSet());
        Sets.SetView<String> intersection = Sets.intersection(lackTaskItemIds, bigCustomerTaskItemIds);
        if (!intersection.isEmpty()) {
            throw new BusinessValidateException("大客户订单不允许缺货");
        }
        checkBigCustomerLackByBatchNo(batchTaskItemMap.keySet(), orgId);

        List<OrderItemTaskInfoPO> lstLackItemInfoPO = itemInfo.stream().filter(p -> lackTaskItemIds.contains(p.getBatchTaskItemId())).collect(toList());
        // 校验产品是否允许缺货
        if (!CollectionUtils.isEmpty(lstLackItemInfoPO)) {
            checkSkuCanBeOutOfStock(warehouseId, lstLackItemInfoPO);
        }
    }

    /**
     * 校验产品是否允许缺货
     */
    private void checkSkuCanBeOutOfStock(Integer warehouseId, List<OrderItemTaskInfoPO> lstLackItem) {
        Set<Long> lackSkuIds = lstLackItem.stream()
                .map(OrderItemTaskInfoPO::getSkuId)
                .filter(Objects::nonNull)
                .collect(toSet());

        if (lackSkuIds.isEmpty()) {
            return;
        }

        Map<Long, Boolean> canOutOfStockMap = productSkuManageService.checkSkuCanBeOutOfStock(warehouseId, new ArrayList<>(lackSkuIds));
        List<Long> restrictedSkuIds = canOutOfStockMap.entrySet().stream()
                .filter(entry -> Boolean.FALSE.equals(entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        logger.info(String.format("校验缺货产品：%s，查询不能缺货结果：%s"
                , JSON.toJSONString(lstLackItem)
                , JSON.toJSONString(restrictedSkuIds)));

        if (!restrictedSkuIds.isEmpty()) {
            Set<String> restrictedProductNames = lstLackItem.stream()
                    .filter(item -> restrictedSkuIds.contains(item.getSkuId()))
                    .map(OrderItemTaskInfoPO::getProductName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            
            String productNamesStr = String.join("、", restrictedProductNames);
            String errorMessage = "以下产品未标记允许缺货，请联系仓管确认!";
            if (!productNamesStr.isEmpty()) {
                errorMessage += "\n" + productNamesStr;
            }
            
            throw new BusinessValidateException(errorMessage);
        }
    }

    /**
     * 校验普通订单能否缺货
     *
     * @param orders     订单信息
     * @param constraint 约束类型
     */
    public void checkNormalOrderLack(List<OutStockOrderPO> orders, String constraint) {
        String strErrorOrderNos = orders.stream()
                .filter(p -> Objects.equals(p.getState().byteValue(), OutStockOrderStateEnum.已出库.getType()))
                .map(OutStockOrderPO::getReforderno)
                .distinct().collect(Collectors.joining(","));
        if (!StringUtils.isEmpty(strErrorOrderNos)) {
            throw new BusinessValidateException("订单 " + strErrorOrderNos + " 已出库，不允许标记缺货！");
        }
        Map<Byte, Map<Integer, List<OutStockOrderPO>>> map = groupBySourceTypeAndWarehouse(orders, Function.identity());
        for (Map.Entry<Byte, Map<Integer, List<OutStockOrderPO>>> entry : map.entrySet()) {
            Byte sourceType = entry.getKey();
            for (Map.Entry<Integer, List<OutStockOrderPO>> warehouseOrder : entry.getValue().entrySet()) {
                Integer warehouseId = warehouseOrder.getKey();
                if (orderCanLack(sourceType, ORDER_TYPE_NORMAL, warehouseId, constraint)) {
                    continue;
                }
                String orderNos = warehouseOrder.getValue().stream().map(OutStockOrderPO::getReforderno)
                        .distinct().collect(Collectors.joining(","));
                throw new BusinessValidateException("订单 " + orderNos + " 不允许缺货");
            }
        }
    }

    /**
     * 获取可以缺货的订单信息
     *
     * @param orders 订单信息
     * @return 可以缺货的订单信息
     */
    public List<OutStockOrderPO> getCanLackOrders(List<OutStockOrderPO> orders) {
        return groupBySourceTypeAndWarehouse(orders, Function.identity()).entrySet().stream()
                .map(this::filterCanLackOrder).map(it -> it.map(Map.Entry::getValue))
                .flatMap(it -> it.flatMap(Collection::stream))
                .collect(Collectors.toList());
    }

    /**
     * 获取可以缺货的订单信息
     *
     * @param boList 订单信息
     * @return 可以缺货的订单信息
     */
    public List<OrderItemTaskInfoDetailLackHelperBO> getCanLackOrderInfo(List<OrderItemTaskInfoDetailLackHelperBO> boList) {
        return groupBySourceTypeAndWarehouse(boList, OrderItemTaskInfoDetailLackHelperBO::getOutStockOrderPO).entrySet()
                .stream().map(this::filterCanLackOrder).map(it -> it.map(Map.Entry::getValue))
                .flatMap(it -> it.flatMap(Collection::stream))
                .collect(Collectors.toList());
    }

    /**
     * 获取可以缺货的订单信息
     *
     * @param orders 订单信息
     * @return 可以缺货的订单信息
     */
    public List<OutStockOrderItemPO> getCanLackOrderItems(List<OutStockOrderItemPO> items, List<OutStockOrderPO> orders) {
        Map<Long, List<OutStockOrderItemPO>> orderItemMap = items.stream()
                .collect(groupingBy(OutStockOrderItemPO::getOutstockorderId));
        return getCanLackOrders(orders).stream().map(it -> orderItemMap.get(it.getId()))
                .filter(Objects::nonNull).flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * 获取不能缺货的订单信息
     *
     * @param orders 完整订单信息
     * @return 不能缺货的订单信息
     */
    public List<OutStockOrderPO> getCanNotLackOrders(List<OutStockOrderPO> orders) {
        return groupBySourceTypeAndWarehouse(orders, Function.identity()).entrySet().stream()
                .map(this::filterCanNotLackOrder).map(it -> it.map(Map.Entry::getValue))
                .flatMap(it -> it.flatMap(Collection::stream))
                .collect(Collectors.toList());
    }

    public void checkSaleInventory(List<OutStockOrderPO> orderList, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
//        List<OutStockOrderPO> meiTuanOrderList = getCanNotLackOrders(orderList);
//        if (CollectionUtils.isEmpty(meiTuanOrderList)) {
//            return;
//        }
        List<OutStockOrderPO> meiTuanOrderList = groupBySourceTypeAndWarehouse(orderList, Function.identity()).entrySet().stream()
                .map(this::filterCheckSaleInventoryOrder).map(it -> it.map(Map.Entry::getValue))
                .flatMap(it -> it.flatMap(Collection::stream))
                .collect(Collectors.toList());
        if (meiTuanOrderList.isEmpty()) {
            return;
        }
        OutStockOrderPO firstOrder = meiTuanOrderList.get(0);
        Integer warehouseId = firstOrder.getWarehouseId();
        Integer cityId = firstOrder.getOrgId();
        List<SaleInventoryQueryDTO> saleInventoryQueryDTOS = SaleInventoryQueryDTOConvertor
                .convertAndFilterList(meiTuanOrderList, orderItemTaskInfoPOList, warehouseId, cityId);
        // 这里返回的key是 998-9981-560993-null-1 这种结构
        Map<String, BigDecimal> saleInventoryMap = orderQueryService.getSaleInventoryMap(saleInventoryQueryDTOS);
        Map<String, String> specificationIdMap = new HashMap<>();
        saleInventoryMap.forEach((key, value) -> {
            if (value.compareTo(BigDecimal.ZERO) < 0) {
                specificationIdMap.put(key, key);
            }
        });
        if (CollectionUtils.isEmpty(specificationIdMap)) {
            return;
        }
        List<String> meiTuanOrderNoList = SaleInventoryQueryDTOConvertor.findMeiTuanOrder(specificationIdMap, meiTuanOrderList);
        if (CollectionUtils.isEmpty(meiTuanOrderNoList)) {
            return;
        }
        if (!CollectionUtils.isEmpty(meiTuanOrderList)) {
            throw ORDER_CANNOT_LACK.newValidateException("X", String.join(",", meiTuanOrderNoList));
        }
    }

    /**
     * 获取不可以缺货的订单信息
     *
     * @param boList 订单信息
     * @return 不可以缺货的订单信息
     */
    public List<OrderItemTaskInfoDetailLackHelperBO> getCanNotLackOrderInfo(List<OrderItemTaskInfoDetailLackHelperBO> boList) {
        return groupBySourceTypeAndWarehouse(boList, OrderItemTaskInfoDetailLackHelperBO::getOutStockOrderPO).entrySet()
                .stream().map(this::filterCanNotLackOrder).map(it -> it.map(Map.Entry::getValue))
                .flatMap(it -> it.flatMap(Collection::stream))
                .collect(Collectors.toList());
    }

    public <T> Map<Byte, Map<Integer, List<T>>> groupBySourceTypeAndWarehouse(List<T> list, Function<T, OutStockOrderPO> mapper) {
        return list.stream().collect(groupingBy(it -> mapper.apply(it).getOrderSourceType(),
                groupingBy(it -> mapper.apply(it).getWarehouseId())));
    }

    /**
     * 订单能否标记缺货
     */
    public boolean orderCanLack(Byte sourceType, String orderType, Integer warehouseId) {
        return checkOrderConstraint("WMS-" + sourceType, orderType, OrderConstraints.MARK_LACK, warehouseId);
    }

    /**
     * 订单能否缺货
     */
    public boolean orderCanLack(Byte sourceType, String orderType, Integer warehouseId, String constraint) {
        return checkOrderConstraint("WMS-" + sourceType, orderType, constraint, warehouseId);
    }

    public boolean orderCheckSaleInventory(Byte sourceType, String orderType, Integer warehouseId) {
        return checkOrderConstraint("WMS-" + sourceType, orderType, CHECK_SALE_INVENTORY, warehouseId);
    }

    /**
     * 校验订单约束
     *
     * @param channel     渠道
     * @param orderType   订单类型
     * @param constraint  约束
     * @param warehouseId 仓库 id
     * @return 结果
     */
    public boolean checkOrderConstraint(String channel, String orderType, String constraint, Integer warehouseId) {
        OrderVariableQueryDTO param = OrderVariableQueryDTO.of(channel, orderType, constraint);
        param.setWarehouseId(warehouseId);
        param.setOrgId(globalCache.getWarehouse(warehouseId).getCityId());
        return variableDefinitionService.checkOrderConstraint(param);
    }

    public boolean isNPOrder(OutStockOrderPO order) {
        return ALLOT_TYPE_ALLOCATION.equals(order.getAllotType()) && SALE_ORDER.valueEquals(order.getOutBoundType());
    }

    private <T> Stream<Map.Entry<Integer, List<T>>> filterCanLackOrder(Map.Entry<Byte, Map<Integer, List<T>>> it) {
        Byte sourceType = it.getKey();
        return it.getValue().entrySet().stream().filter(item -> orderCanLack(sourceType, ORDER_TYPE_NORMAL, item.getKey()));
    }

    private <T> Stream<Map.Entry<Integer, List<T>>> filterCanNotLackOrder(Map.Entry<Byte, Map<Integer, List<T>>> it) {
        Byte sourceType = it.getKey();
        return it.getValue().entrySet().stream().filter(item -> !orderCanLack(sourceType, ORDER_TYPE_NORMAL, item.getKey()));
    }

    private <T> Stream<Map.Entry<Integer, List<T>>> filterCheckSaleInventoryOrder(Map.Entry<Byte, Map<Integer, List<T>>> it) {
        Byte sourceType = it.getKey();
        return it.getValue().entrySet().stream().filter(item -> orderCheckSaleInventory(sourceType, ORDER_TYPE_NORMAL, item.getKey()));
    }

    private boolean isNPFront(OutStockOrderPO o) {
        if (o == null) {
            return false;
        }
        return Objects.equals(o.getAllotType(), OrderConstant.ALLOT_TYPE_ALLOCATION) &&
                o.getOutBoundType().intValue() == SALE_ORDER.getCode();
    }

    private boolean isPickLack(BatchTaskItemCompleteDTO m) {
        BigDecimal packageCount = Objects.nonNull(m.getLackPackageCount()) ? m.getLackPackageCount() : BigDecimal.ZERO;
        BigDecimal unitCount = Objects.nonNull(m.getLackUnitCount()) ? m.getLackUnitCount() : BigDecimal.ZERO;
        if (BigDecimal.ZERO.compareTo(packageCount) == 0 && BigDecimal.ZERO.compareTo(unitCount) == 0) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private boolean isAllotOrder(OutStockOrderPO outStockOrderPO) {
        if (!OrderConstant.ALLOT_TYPE_ALLOCATION.equals(outStockOrderPO.getAllotType())) {
            return Boolean.FALSE;
        }
        if (OutBoundTypeEnum.SALE_ORDER.getCode().byteValue() != outStockOrderPO.getOutBoundType()) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private void checkBigCustomerLackByBatchNo(Collection<String> batchNos, Integer cityId) {
        boolean hasBigCustomerOrder = outStockOrderMapper.findByBatchNos(batchNos, cityId).stream()
                .anyMatch(it -> FS_SALE_SELF_PICKUP_SALE_ORDER.valueEquals(it.getOutBoundType()));
        if (hasBigCustomerOrder) {
            throw new BusinessValidateException("大客户订单不允许缺货");
        }
    }

}
