package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 拣货任务提交时校验拣货数量是否是销售规格的倍数
 *
 * <AUTHOR>
 * @date 2019/2/13 15:56
 */
public class BatchTaskCheckSaleSpecDTO implements Serializable {

    /**
     * skuId（赠品SKUId可能为null）
     */
    private Long skuId;

    /**
     * 关联订单表id
     */
    private String refOrderId;

    /**
     * 销售规格系数
     */
    private BigDecimal saleSpecQuantity;

    /**
     * 已拣小数量总数
     */
    private BigDecimal overSortUnitTotolCount;

    /**
     * 拣货任务小数量总数
     */
    private BigDecimal unitTotolCount;

    public BigDecimal getUnitTotolCount() {
        return unitTotolCount;
    }

    public void setUnitTotolCount(BigDecimal unitTotolCount) {
        this.unitTotolCount = unitTotolCount;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getRefOrderId() {
        return refOrderId;
    }

    public void setRefOrderId(String refOrderId) {
        this.refOrderId = refOrderId;
    }

    public BigDecimal getSaleSpecQuantity() {
        return saleSpecQuantity;
    }

    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    public BigDecimal getOverSortUnitTotolCount() {
        return overSortUnitTotolCount;
    }

    public void setOverSortUnitTotolCount(BigDecimal overSortUnitTotolCount) {
        this.overSortUnitTotolCount = overSortUnitTotolCount;
    }
}
