package com.yijiupi.himalaya.supplychain.waves.util;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-11-08 09:23
 **/
public class StringTemplateUtils {

    /**
     * 字符串模板格式化工具
     *
     * @param template     字符串模板
     * @param varMap       参数 map
     * @param variableKeys 需要替换的 key
     * @return 格式化后的字符串
     */
    public static String replace(String template, Map<String, ?> varMap, String... variableKeys) {
        String result = template;
        for (String key : variableKeys) {
            result = result.replace(key, String.valueOf(varMap.get(key)));
        }
        return result;
    }

}
