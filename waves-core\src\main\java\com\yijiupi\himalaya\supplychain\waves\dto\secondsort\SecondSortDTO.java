package com.yijiupi.himalaya.supplychain.waves.dto.secondsort;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/6/8 17:10
 */
public class SecondSortDTO implements Serializable {
    private static final long serialVersionUID = -1917258511052409949L;
    /**
     * id
     */
    private Long id;
    /**
     * 城市id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 托盘编号 多个用英文逗号拼接
     */
    private String boxCode;
    /**
     * 拣货任务id
     */
    private String batchTaskId;
    /**
     * 拣货任务详情id
     */
    private String batchTaskItemId;

    private Long orderItemId;

    private Long userId;

    private String userName;

    private String orderNo;

    /**
     * 获取id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 设置id
     */
    public Long getId() {
        return id;
    }

    public String getBoxCode() {
        return boxCode;
    }

    public void setBoxCode(String boxCode) {
        this.boxCode = boxCode;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public String getBatchTaskItemId() {
        return batchTaskItemId;
    }

    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }
}
