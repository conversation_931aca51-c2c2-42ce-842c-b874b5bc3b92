package com.yijiupi.himalaya.supplychain.waves.domain.bl.variable;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024-08-29 16:43
 **/
@Service
public class VariableManager {

    @Reference
    private IVariableValueService variableValueService;

    /**
     * 查询 wms 网站上配置的参数
     *
     * @param key         参数 key
     * @param warehouseId 仓库 id
     * @return 查询结果, 将其值转换成 boolean 类型的, 若没配置则返回 false
     */
    public boolean checkBooleanKey(String key, Integer warehouseId) {
        return getVariableByKey(key, warehouseId)
                .map(VariableDefAndValueDTO::getVariableData)
                .filter(StringUtils::hasText).map(Boolean::parseBoolean)
                .orElse(false);
    }

    /**
     * 查询 wms 网站上配置的参数
     *
     * @param key 参数 key
     * @return 查询结果, 可能为空
     */
    public Optional<VariableDefAndValueDTO> getVariableByKey(String key) {
        return getVariableByKey(key, null);
    }

    /**
     * 查询 wms 网站上配置的参数
     *
     * @param key         参数 key
     * @param warehouseId 仓库 id
     * @return 查询结果, 可能为空
     */
    public Optional<VariableDefAndValueDTO> getVariableByKey(String key, Integer warehouseId) {
        return getVariableByKey(key, warehouseId, null);
    }

    /**
     * 查询 wms 网站上配置的参数
     *
     * @param key         参数 key
     * @param warehouseId 仓库 id
     * @param orgId       城市 id
     * @return 查询结果, 可能为空
     */
    @NotNull
    public Optional<VariableDefAndValueDTO> getVariableByKey(String key, Integer warehouseId, Integer orgId) {
        VariableValueQueryDTO queryDTO = new VariableValueQueryDTO();
        queryDTO.setVariableKey(key);
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setOrgId(orgId);
        return Optional.ofNullable(variableValueService.detailVariable(queryDTO));
    }

}
