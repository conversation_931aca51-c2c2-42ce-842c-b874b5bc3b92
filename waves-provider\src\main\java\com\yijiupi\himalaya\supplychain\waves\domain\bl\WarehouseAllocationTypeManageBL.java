package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuInfoSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/3/18 11:58
 * @Version 1.0
 */
@Component
public class WarehouseAllocationTypeManageBL {
	private static final Logger LOG = LoggerFactory.getLogger(WarehouseAllocationTypeManageBL.class);
	private static final int BATCH_QUERY_SIZE = 1000;

	@Reference
	private IProductSkuService productSkuService;
	@Autowired
	private OutStockOrderMapper outStockOrderMapper;

	// 返回每个skuId对应的分仓属性
	public Map<Long, Integer> getSkuWarehouseAllocationTypeMap(Integer warehouseId, List<Long> skuIds) {
		if (warehouseId == null || CollectionUtils.isEmpty(skuIds)) {
			LOG.warn("获取一组skuId分仓属性:参数为空:warehouseId={}:skuIds={}", warehouseId, skuIds);
			return null;
		}

		// skuIds 超过1000时，分批查询
		List<List<Long>> partitions = Lists.partition(skuIds, BATCH_QUERY_SIZE);
		Map<Long, String> skuConfigStorageAttribute = new HashMap<>(16);
		for (List<Long> partition : partitions) {
			ProductSkuInfoSO productSkuInfoSO = new ProductSkuInfoSO();
			productSkuInfoSO.setProductSkuIdList(partition);
			productSkuInfoSO.setWarehouseId(warehouseId);
			Map<Long, String> rlt = productSkuService.getSkuConfigStorageAttribute(productSkuInfoSO);
			skuConfigStorageAttribute.putAll(rlt);
		}

		// 补全数据
		Map<Long, Integer> skuWarehouseAllocationTypeMap = new HashMap<>(16);
		for (Long skuId : skuIds) {
			if (skuConfigStorageAttribute.containsKey(skuId)) {
				Integer warehouseAllocationType = null;
				String value = skuConfigStorageAttribute.get(skuId);
				// 0是wms初始化的默认值，无此枚举值
				if (StringUtils.isNotBlank(value) && !"0".equals(value)) {
					warehouseAllocationType = Integer.valueOf(value);
				}

				skuWarehouseAllocationTypeMap.put(skuId, warehouseAllocationType);
			} else {
				skuWarehouseAllocationTypeMap.put(skuId, null);
			}
		}
		return skuWarehouseAllocationTypeMap;
	}

	public Integer getSkuWarehouseAllocationType(Integer warehouseId, List<Long> skuIds) {
		Map<Long, Integer> skuWarehouseAllocationTypeMap = getSkuWarehouseAllocationTypeMap(warehouseId, skuIds);
		Set<Integer> collect = new HashSet<>(skuWarehouseAllocationTypeMap.values());
		Integer skuWarehouseAllocationType = null;
		if (collect.size() != 1) {
			LOG.warn("获取一组skuId分仓属性:skuId对应的分仓属性不一致:skuWarehouseAllocationConfigTypeMap={}",
					JSON.toJSONString(skuWarehouseAllocationTypeMap));
		} else {
			skuWarehouseAllocationType = collect.iterator().next();
		}

		return skuWarehouseAllocationType;
	}

	public void setBatchWarehouseAllocationType(BatchPO batchPO, Set<Long> OutStockOrderIds) {
		List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.selectByIds(OutStockOrderIds);
		if (CollectionUtils.isEmpty(outStockOrderPOS)) {
			return;
		}

		// warehouseAllocationType
		Set<Integer> warehouseAllocationTypeSet = outStockOrderPOS.stream()
				.map(OutStockOrderPO::getWarehouseAllocationType)
				.filter(Objects::nonNull)
				.collect(Collectors.toSet());

		if (warehouseAllocationTypeSet.size() == 1) {
			batchPO.setWarehouseAllocationType(warehouseAllocationTypeSet.iterator().next());
		}
		for (OutStockOrderPO outStockOrderPO : outStockOrderPOS) {
			if (outStockOrderPO.getWarehouseAllocationType() == null) {
				batchPO.setWarehouseAllocationType(null);
				break;
			}
		}
	}

	private void setBatchTaskWarehouseAllocationType(BatchTaskPO batchTaskPO, List<BatchTaskItemPO> itemPOList) {
		if (CollectionUtils.isEmpty(itemPOList)) {
			return;
		}

		List<Long> productSkuIdList = itemPOList.stream().map(BatchTaskItemPO::getSkuId).collect(Collectors.toList());

		if (batchTaskPO.getWarehouseId() == null) {
			LOG.error("仓库ID不能为空。outStockOrderPO={}", JSON.toJSONString(batchTaskPO));
			// AssertUtils.fail("仓库ID不能为空。");
			return;
		}

		Integer warehouseId = batchTaskPO.getWarehouseId();
		Integer warehouseAllocationType = getSkuWarehouseAllocationType(warehouseId, productSkuIdList);

		// 设置分仓属性
		batchTaskPO.setWarehouseAllocationType(warehouseAllocationType);
	}

	public void setBatchTaskWarehouseAllocationType(List<BatchTaskPO> lstBatchTask, List<BatchTaskItemPO> batchTaskItemPOList) {
		if (CollectionUtils.isEmpty(lstBatchTask)) {
			return;
		}

		if (CollectionUtils.isEmpty(batchTaskItemPOList)) {
			return;
		}

		// key = BatchTaskId, value = List<BatchTaskItemPO>
		// 分组
		Map<String, List<BatchTaskItemPO>> itemMap =
				batchTaskItemPOList.stream().collect(Collectors.groupingBy(BatchTaskItemPO::getBatchTaskId));

		for (BatchTaskPO batchTaskPO : lstBatchTask) {
			List<BatchTaskItemPO> itemPOList = itemMap.get(batchTaskPO.getId());
			if (CollectionUtils.isEmpty(itemPOList)) {
				continue;
			}

			setBatchTaskWarehouseAllocationType(batchTaskPO, itemPOList);
		}
	}

	public void setSowTaskWarehouseAllocationType(SowTaskPO sowTaskPO, List<SowTaskItemPO> sowTaskItemPOList) {
		if (sowTaskPO == null) {
			return;
		}

		if (CollectionUtils.isEmpty(sowTaskItemPOList)) {
			return;
		}
		List<Long> productSkuIdList = sowTaskItemPOList.stream().map(SowTaskItemPO::getProductSkuId).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(productSkuIdList)) {
			return;
		}
		if (sowTaskPO.getWarehouseId() == null) {
			LOG.error("仓库ID不能为空。outStockOrderPO={}", JSON.toJSONString(sowTaskPO));
			// AssertUtils.fail("仓库ID不能为空。");
			return;
		}

		Integer warehouseId = sowTaskPO.getWarehouseId();
		Integer warehouseAllocationType = getSkuWarehouseAllocationType(warehouseId, productSkuIdList);
		// 设置分仓属性
		sowTaskPO.setWarehouseAllocationType(warehouseAllocationType);
	}

	public void setSowTaskWarehouseAllocationType(List<SowTaskPO> lstSowTask) {
		if (CollectionUtils.isEmpty(lstSowTask)) {
			return;
		}

		for (SowTaskPO sowTaskPO : lstSowTask) {
			List<SowTaskItemPO> sowTaskItemPOS = sowTaskPO.getSowTaskItemPOS();
			setSowTaskWarehouseAllocationType(sowTaskPO, sowTaskItemPOS);
		}

	}
}
