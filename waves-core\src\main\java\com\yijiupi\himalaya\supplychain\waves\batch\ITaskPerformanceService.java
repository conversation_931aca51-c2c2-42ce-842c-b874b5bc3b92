package com.yijiupi.himalaya.supplychain.waves.batch;

import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.HistoryTaskPerformanceCalDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.performance.TaskPerformanceCalculateDTO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
public interface ITaskPerformanceService {

    /**
     * 计算历史绩效
     * 
     * @param dto
     */
    void calculateHistoryTaskPerformance(HistoryTaskPerformanceCalDTO dto);

    /**
     * 根据任务id计算任务绩效
     * 
     * @param dto
     */
    void calculateTaskPerformance(TaskPerformanceCalculateDTO dto);
}
