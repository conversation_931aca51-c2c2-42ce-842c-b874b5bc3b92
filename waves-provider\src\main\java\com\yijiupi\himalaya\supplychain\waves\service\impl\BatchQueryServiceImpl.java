package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemLackDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSorterDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;

/**
 * <AUTHOR> 2018/3/15
 */
@Service(timeout = 120000)
public class BatchQueryServiceImpl implements IBatchQueryService {

    @Autowired
    private BatchOrderBL batchOrderBL;

    /**
     * 查询波次列表
     *
     * @param batchQueryDTO
     * @return
     */
    @Override
    public PageList<BatchDTO> findBatchOrderList(BatchQueryDTO batchQueryDTO) {
        AssertUtils.notNull(batchQueryDTO.getWarehouseId(), "仓库id不能为空");
        PageList<BatchDTO> batchOrderList = batchOrderBL.findBatchOrderList(batchQueryDTO);
        return batchOrderList;
    }

    @Override
    public List<OutStockOrderLocationDTO> findBatchOrderLoctionList(List<Long> orderIds) {
        AssertUtils.notEmpty(orderIds, "订单id不能为空");
        return batchOrderBL.findBatchOrderLoctionList(orderIds);
    }

    /**
     * 根据波次编号，查找所有标记缺货的项
     *
     * @param batchNos
     * @return
     */
    @Override
    public List<BatchTaskItemLackDTO> listLackItemByBatchNos(List<String> batchNos) {
        AssertUtils.notEmpty(batchNos, "波次No不能为空");
        return batchOrderBL.listLackItemByBatchNos(batchNos);
    }

    @Override
    public List<BatchOrderDTO> listBatchOrderByWarehouse(Integer orgId, Integer warehouseId) {
        AssertUtils.notNull(orgId, "城市id不能为空");
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        return batchOrderBL.listBatchOrderByWarehouse(orgId, warehouseId);
    }

    /**
     * 查询波次打包列表
     *
     * @param queryParam
     * @return
     */
    @Override
    public List<BatchOrderDTO> listBatchOrder(BatchOrderQueryParam queryParam) {
        return listBatchOrderByWarehouse(queryParam.getOrgId(), queryParam.getWarehouseId());
    }

    /**
     * 获取仓库的波次作业设置
     * 
     * @return
     */
    @Override
    public BatchWorkSettingDTO getBatchWorkSetting(Integer warehouseId) {
        return batchOrderBL.getBatchWorkSetting(warehouseId);
    }

    @Override
    public Map<String, List<BatchTaskSorterDTO>> getSorterByOrderNos(List<String> orderNos, Integer warehouseId) {
        return batchOrderBL.listSorterByOrderNos(orderNos, warehouseId);
    }

    @Override
    public List<BatchOrderDTO> listBatchStatusByBatchNos(List<String> batchNos, Integer orgId, Integer warehouseId) {
        return batchOrderBL.listBatchStatusByBatchNos(batchNos, orgId, warehouseId);
    }

    @Override
    public PageList<BatchDTO> listSaaSBatchOrderList(BatchQueryDTO batchQueryDTO) {
        return batchOrderBL.listSaaSBatchOrderList(batchQueryDTO);
    }

    @Override
    public List<BatchDTO> findByBatchNos(List<String> batchNos, Integer orgId) {
        return batchOrderBL.findByBatchNos(batchNos, orgId);
    }

}
