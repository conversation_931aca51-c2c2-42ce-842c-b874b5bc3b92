package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import java.io.Serializable;

/**
 * 按订单拣货，订单中分了多个拣货任务
 *
 * <AUTHOR>
 * @date 2019/2/12 16:03
 */
public class BatchTaskSortOrderOtherDTO implements Serializable {

    private static final long serialVersionUID = 181570111615914793L;

    /**
     * 订单id
     */
    private String refOrderId;

    /**
     * 波次任务Id
     */
    private String batchTaskId;

    /**
     * 分拣员(姓名)
     */
    private String sorter;

    /**
     * 分拣员id
     */
    private Integer sorterId;

    /**
     * 大件数量
     */
    private Integer packageCount;

    /**
     * 小件数量
     */
    private Integer unitCount;

    public String getRefOrderId() {
        return refOrderId;
    }

    public void setRefOrderId(String refOrderId) {
        this.refOrderId = refOrderId;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public String getSorter() {
        return sorter;
    }

    public void setSorter(String sorter) {
        this.sorter = sorter;
    }

    public Integer getSorterId() {
        return sorterId;
    }

    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    public Integer getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(Integer packageCount) {
        this.packageCount = packageCount;
    }

    public Integer getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(Integer unitCount) {
        this.unitCount = unitCount;
    }
}
