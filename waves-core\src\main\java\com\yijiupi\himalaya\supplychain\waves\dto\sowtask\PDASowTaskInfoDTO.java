package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/1
 */
public class PDASowTaskInfoDTO implements Serializable {

    /**
     * 波次号
     */
    private String batchNo;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 集货位id
     */
    private Long sowLocationId;

    /**
     * 集货位名称
     */
    private String sowLocationName;

    /**
     * 仓库Id
     */
    private Integer warehouseId;
    /**
     * 拣货任务明细列表
     */
    private List<PDASowTaskRefBatchTaskItemDTO> batchTaskItemList;
    /**
     * 订单项明细列表
     */
    private List<PDASowTaskRefOutStockOrderItemDTO> outStockOrderItemList;

    /**
     * 获取 波次号
     *
     * @return batchNo 波次号
     */
    public String getBatchNo() {
        return this.batchNo;
    }

    /**
     * 设置 波次号
     *
     * @param batchNo 波次号
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * 获取 播种任务id
     *
     * @return sowTaskId 播种任务id
     */
    public Long getSowTaskId() {
        return this.sowTaskId;
    }

    /**
     * 设置 播种任务id
     *
     * @param sowTaskId 播种任务id
     */
    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    /**
     * 获取 播种任务编号
     *
     * @return sowTaskNo 播种任务编号
     */
    public String getSowTaskNo() {
        return this.sowTaskNo;
    }

    /**
     * 设置 播种任务编号
     *
     * @param sowTaskNo 播种任务编号
     */
    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    /**
     * 获取 集货位id
     *
     * @return sowLocationId 集货位id
     */
    public Long getSowLocationId() {
        return this.sowLocationId;
    }

    /**
     * 设置 集货位id
     *
     * @param sowLocationId 集货位id
     */
    public void setSowLocationId(Long sowLocationId) {
        this.sowLocationId = sowLocationId;
    }

    /**
     * 获取 集货位名称
     *
     * @return sowLocationName 集货位名称
     */
    public String getSowLocationName() {
        return this.sowLocationName;
    }

    /**
     * 设置 集货位名称
     *
     * @param sowLocationName 集货位名称
     */
    public void setSowLocationName(String sowLocationName) {
        this.sowLocationName = sowLocationName;
    }

    /**
     * 获取 仓库Id
     *
     * @return warehouseId 仓库Id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库Id
     *
     * @param warehouseId 仓库Id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 拣货任务明细列表
     *
     * @return batchTaskItemList 拣货任务明细列表
     */
    public List<PDASowTaskRefBatchTaskItemDTO> getBatchTaskItemList() {
        return this.batchTaskItemList;
    }

    /**
     * 设置 拣货任务明细列表
     *
     * @param batchTaskItemList 拣货任务明细列表
     */
    public void setBatchTaskItemList(List<PDASowTaskRefBatchTaskItemDTO> batchTaskItemList) {
        this.batchTaskItemList = batchTaskItemList;
    }

    /**
     * 获取 订单项明细列表
     *
     * @return outStockOrderItemList 订单项明细列表
     */
    public List<PDASowTaskRefOutStockOrderItemDTO> getOutStockOrderItemList() {
        return this.outStockOrderItemList;
    }

    /**
     * 设置 订单项明细列表
     *
     * @param outStockOrderItemList 订单项明细列表
     */
    public void setOutStockOrderItemList(List<PDASowTaskRefOutStockOrderItemDTO> outStockOrderItemList) {
        this.outStockOrderItemList = outStockOrderItemList;
    }
}
