﻿<?xml version="1.0"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yijiupi</groupId>
        <artifactId>himalaya-supplychain-microservice-waves</artifactId>
        <version>2.20.0</version>
    </parent>
    <artifactId>himalaya-supplychain-microservice-waves-provider</artifactId>

    <dependencies>
        <!-- starter -->
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-starter-dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>framework-rabbit</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-starter-logger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-starter-data-mysql</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.1</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-starter-config</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <!-- 二方CORE包 -->
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-waves-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-storecheck-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-orgsettings-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-warehouseproduct-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-inventory-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-batchinventory-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-outstockordersync-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-serviceutils</artifactId>
            <version>0.0.5-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>supplychain-lock-starter</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.yijiupi</groupId>-->
        <!--            <artifactId>himalaya-supplychain-microservice-wmsdubbop-core</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-dubbop-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-vrstms-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-warehouse-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-goodspursueoperate-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-user-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi.xxljob</groupId>
            <artifactId>basic-other-xxljob</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-sqlreport-core</artifactId>
        </dependency>
        <!-- 第三方包 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>24.0-jre</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-allot-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-starter-uuid</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-mqtt-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>ordercenter-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-wcs-core</artifactId>
            <version>2.20.0</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-junit-test</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>supplychain-microservice-omscore</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>tracer-core</artifactId>
            <version>2.4.7</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-assignment-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>ordercenter-unleash-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-algorithm-core</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.yijiupi</groupId>
                <artifactId>ordercenter-unleash-starter</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.8.1</version>
            </dependency>
            <dependency>
                <groupId>com.yijiupi</groupId>
                <artifactId>himalaya-supplychain-microservice-algorithm-core</artifactId>
                <version>2.20.2-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <resources>
            <resource>
                <directory>
                    src/main/resources
                </directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/test/java</directory>
                <includes>
                    <include>**/*.properties</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <descriptors>
                        <descriptor>src/main/assembly/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>