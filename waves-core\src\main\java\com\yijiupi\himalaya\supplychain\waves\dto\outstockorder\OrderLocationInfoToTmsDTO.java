package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/24
 */
public class OrderLocationInfoToTmsDTO implements Serializable {

    private List<String> orderNoList;

    private Integer warehouseId;

    private Integer orgId;

    private Integer optUserId;

    /**
     * 获取
     *
     * @return orderNoList
     */
    public List<String> getOrderNoList() {
        return this.orderNoList;
    }

    /**
     * 设置
     *
     * @param orderNoList
     */
    public void setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
    }

    /**
     * 获取
     *
     * @return warehouseId
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置
     *
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取
     *
     * @return orgId
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置
     *
     * @param orgId
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取
     *
     * @return optUserId
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置
     *
     * @param optUserId
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }
}
