package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;
import java.util.List;

public class SowTaskCancelDTO implements Serializable {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单变更类型 0：订单取消 1：订单修改 2：部分配送 3：配送失败
     */
    private Byte changeType;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 取消明细
     */
    private List<SowTaskCancelItemDTO> sowTaskCancelItemDTOS;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public List<SowTaskCancelItemDTO> getSowTaskCancelItemDTOS() {
        return sowTaskCancelItemDTOS;
    }

    public void setSowTaskCancelItemDTOS(List<SowTaskCancelItemDTO> sowTaskCancelItemDTOS) {
        this.sowTaskCancelItemDTOS = sowTaskCancelItemDTOS;
    }

    public Byte getChangeType() {
        return changeType;
    }

    public void setChangeType(Byte changeType) {
        this.changeType = changeType;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }
}
