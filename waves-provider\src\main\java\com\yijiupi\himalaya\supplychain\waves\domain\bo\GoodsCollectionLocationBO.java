package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 *
 * <AUTHOR>
 * @date 2024/11/4
 */
public class GoodsCollectionLocationBO {

    private Long id;
    private Integer warehouse_Id;
    private Integer city_Id;
    private String name;
    private String area;
    /**
     * 货位的容量，实际上是播种任务的数量
     */
    private Integer locationCapacity;

    private Long area_Id;

    private Integer sequence;
    /**
     * 集货位上的所有播种任务信息，新增的也会加到里面
     */
    private List<SowTaskPO> sowTaskPOList = new ArrayList<>();

    /**
     * 获取
     *
     * @return id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置
     *
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取
     *
     * @return warehouse_Id
     */
    public Integer getWarehouse_Id() {
        return this.warehouse_Id;
    }

    /**
     * 设置
     *
     * @param warehouse_Id
     */
    public void setWarehouse_Id(Integer warehouse_Id) {
        this.warehouse_Id = warehouse_Id;
    }

    /**
     * 获取
     *
     * @return city_Id
     */
    public Integer getCity_Id() {
        return this.city_Id;
    }

    /**
     * 设置
     *
     * @param city_Id
     */
    public void setCity_Id(Integer city_Id) {
        this.city_Id = city_Id;
    }

    /**
     * 获取
     *
     * @return name
     */
    public String getName() {
        return this.name;
    }

    /**
     * 设置
     *
     * @param name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取
     *
     * @return area
     */
    public String getArea() {
        return this.area;
    }

    /**
     * 设置
     *
     * @param area
     */
    public void setArea(String area) {
        this.area = area;
    }

    /**
     * 获取
     *
     * @return sequence
     */
    public Integer getSequence() {
        return this.sequence;
    }

    /**
     * 设置
     *
     * @param sequence
     */
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    /**
     * 获取
     *
     * @return area_Id
     */
    public Long getArea_Id() {
        return this.area_Id;
    }

    /**
     * 设置
     *
     * @param area_Id
     */
    public void setArea_Id(Long area_Id) {
        this.area_Id = area_Id;
    }

    /**
     * 获取 货位的容量，实际上是播种任务的数量
     *
     * @return locationCapacity 货位的容量，实际上是播种任务的数量
     */
    public Integer getLocationCapacity() {
        return this.locationCapacity;
    }

    /**
     * 设置 货位的容量，实际上是播种任务的数量
     *
     * @param locationCapacity 货位的容量，实际上是播种任务的数量
     */
    public void setLocationCapacity(Integer locationCapacity) {
        this.locationCapacity = locationCapacity;
    }

    /**
     * 获取 集货位上的所有播种任务信息，新增的也会加到里面
     *
     * @return sowTaskPOList 集货位上的所有播种任务信息，新增的也会加到里面
     */
    public List<SowTaskPO> getSowTaskPOList() {
        return this.sowTaskPOList;
    }

    /**
     * 设置 集货位上的所有播种任务信息，新增的也会加到里面
     *
     * @param sowTaskPOList 集货位上的所有播种任务信息，新增的也会加到里面
     */
    public void setSowTaskPOList(List<SowTaskPO> sowTaskPOList) {
        this.sowTaskPOList = sowTaskPOList;
    }

    public static GoodsCollectionLocationBO getDefault(LocationReturnDTO toLocation) {
        if (Objects.isNull(toLocation)) {
            return null;
        }
        GoodsCollectionLocationBO bo = new GoodsCollectionLocationBO();
        BeanUtils.copyProperties(toLocation, bo);

        return bo;
    }
}
