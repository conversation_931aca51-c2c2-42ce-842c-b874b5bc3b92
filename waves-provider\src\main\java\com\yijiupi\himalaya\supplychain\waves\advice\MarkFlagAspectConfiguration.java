package com.yijiupi.himalaya.supplychain.waves.advice;

import com.yijiupi.himalaya.distributedlock.constants.Constants;
import com.yijiupi.himalaya.distributedlock.utils.ListUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/5/24
 */
@Aspect
@Component
@Configuration
public class MarkFlagAspectConfiguration {

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;
    private static final String NULL = "null";
    private static final Integer EXPIRE_TIME = 30;
    private static final Logger LOGGER = LoggerFactory.getLogger(MarkFlagAspectConfiguration.class);


    @Pointcut("@annotation(com.yijiupi.himalaya.supplychain.waves.advice.MarkMethodInvokeFlag)")
    private void lockPoint() {

    }

    @Around("lockPoint()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        try {
            Method method = ((MethodSignature) pjp.getSignature()).getMethod();
            MarkMethodInvokeFlag flag = method.getAnnotation(MarkMethodInvokeFlag.class);
            String warehouseKey = parse(flag.warehouseId(), method, pjp.getArgs());
            String userKey = parse(flag.userId(), method, pjp.getArgs());

            String key = flag.key() + ":" + warehouseKey;
            BoundHashOperations<Object, Object, Object> boundHashOperations = redisTemplate.boundHashOps(key);
            boundHashOperations.expire(EXPIRE_TIME, TimeUnit.MINUTES);
            boundHashOperations.put(userKey, System.currentTimeMillis());
        } catch (Exception e) {
            LOGGER.warn("设置操作信息出错,", e);
        }

        return pjp.proceed();
    }


    private String[] getArgParams(Method method) {
        ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();
        return parameterNameDiscoverer.getParameterNames(method);
    }

    private String parse(String conditions, Method method, Object[] arguments) {
        if (conditions.equals(Constants.DEFAULT_VALUE)) {
            ListUtils.getDefaultList();
        }
        ExpressionParser parser = new SpelExpressionParser();
        Expression expression = parser.parseExpression(conditions);
        StandardEvaluationContext context = new StandardEvaluationContext();
        String[] parameterNames = getArgParams(method);
        for (int i = 0; i < parameterNames.length; i++) {
            context.setVariable(parameterNames[i], arguments[i]);
        }
        Object obj = expression.getValue(context);
        return String.valueOf(obj);
    }

}
