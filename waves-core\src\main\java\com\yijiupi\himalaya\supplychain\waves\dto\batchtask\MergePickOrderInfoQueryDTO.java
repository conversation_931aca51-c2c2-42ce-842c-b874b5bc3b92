package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/25
 */
public class MergePickOrderInfoQueryDTO implements Serializable {
    /**
     * 订单id
     */
    private List<Long> orderIdList;
    /**
     * 仓库id
     */
    private Integer warehouseId;


    /**
     * 获取 订单id
     *
     * @return orderIdList 订单id
     */
    public List<Long> getOrderIdList() {
        return this.orderIdList;
    }

    /**
     * 设置 订单id
     *
     * @param orderIdList 订单id
     */
    public void setOrderIdList(List<Long> orderIdList) {
        this.orderIdList = orderIdList;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
