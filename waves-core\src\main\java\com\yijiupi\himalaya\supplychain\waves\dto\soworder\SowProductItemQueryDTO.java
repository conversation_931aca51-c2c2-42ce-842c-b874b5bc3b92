package com.yijiupi.himalaya.supplychain.waves.dto.soworder;

import java.io.Serializable;

public class SowProductItemQueryDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 容器编号
     */
    private Integer containerNo;

    /**
     * 容器货位id
     */
    private Long locationId;

    /**
     * 容器货位名称
     */
    private String locationName;

    /**
     * 播种状态
     */
    private Byte state;

    /**
     * 装箱状态
     */
    private Byte packageState;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Integer getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(Integer containerNo) {
        this.containerNo = containerNo;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Byte getPackageState() {
        return packageState;
    }

    public void setPackageState(Byte packageState) {
        this.packageState = packageState;
    }
}
