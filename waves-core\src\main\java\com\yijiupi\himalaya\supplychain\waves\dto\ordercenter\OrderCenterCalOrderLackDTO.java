package com.yijiupi.himalaya.supplychain.waves.dto.ordercenter;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/8/15
 */
public class OrderCenterCalOrderLackDTO implements Serializable {

    /**
     * oms订单id
     */
    private Long orderId;
    /**
     * 标记类型 1-缺货发货，10-全部配送，11-部分配送，12-配送失败
     */
    private Integer markType;
    /**
     * 订单重算数据明细
     */
    private List<OrderCenterCalOrderLackItemDTO> items;

    /**
     * 获取 oms订单id
     *
     * @return orderId oms订单id
     */
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 oms订单id
     *
     * @param orderId oms订单id
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 标记类型 1-缺货发货，10-全部配送，11-部分配送，12-配送失败
     *
     * @return markType 标记类型 1-缺货发货，10-全部配送，11-部分配送，12-配送失败
     */
    public Integer getMarkType() {
        return this.markType;
    }

    /**
     * 设置 标记类型 1-缺货发货，10-全部配送，11-部分配送，12-配送失败
     *
     * @param markType 标记类型 1-缺货发货，10-全部配送，11-部分配送，12-配送失败
     */
    public void setMarkType(Integer markType) {
        this.markType = markType;
    }

    /**
     * 获取 订单重算数据明细
     *
     * @return items 订单重算数据明细
     */
    public List<OrderCenterCalOrderLackItemDTO> getItems() {
        return this.items;
    }

    /**
     * 设置 订单重算数据明细
     *
     * @param items 订单重算数据明细
     */
    public void setItems(List<OrderCenterCalOrderLackItemDTO> items) {
        this.items = items;
    }
}
