package com.yijiupi.himalaya.supplychain.waves.domain.model;

/**
 * 订单筛选策略
 * 
 * <AUTHOR>
 * @date: 2019年11月13日 上午9:45:03
 */
public enum SaaSOrderSelectionEnum {
    /**
     * 枚举
     */
    待拣货((byte)0), 拣货中((byte)1), 已完成((byte)2);

    /**
     * type
     */
    private Byte type;

    SaaSOrderSelectionEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    public static String getType(Byte type) {
        if (null == type) {
            return null;
        }
        for (SaaSOrderSelectionEnum orderSelectionEnum : SaaSOrderSelectionEnum.values()) {
            if (orderSelectionEnum.getType() == type) {
                return orderSelectionEnum.name();
            }
        }
        return null;
    }
}
