package com.yijiupi.himalaya.supplychain.waves.domain.model.tms;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.Pager;
import com.yijiupi.himalaya.base.search.PagerCondition;

/**
 * @since 2022/1/613:58
 */
public class DeliveryOrderInfoDTO implements Serializable {
    private static final long serialVersionUID = 4267029543348422350L;
    /**
     *
     */
    private Long id;
    /**
     * 配送中心id
     */
    private Integer deliveryCenterId;
    /**
     * 配送单号
     */
    private String deliveryNo;
    /**
     * 配送需求单id
     */
    private Long deliveryRequestId;
    /**
     * 配送地址id
     */
    private Long deliveryLocationId;
    /**
     * 车辆id
     */
    private Long deliveryCarId;
    /**
     * 司机id
     */
    private Integer deliveryUserId;
    /**
     * 车牌号
     */
    private String deliveryCarNumber;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String county;
    /**
     * 街道
     */
    private String street;
    /**
     * 地址
     */
    private String detailAddress;
    /**
     * 手机号
     */
    private String mobileNo;
    /**
     * 联系人
     */
    private String contact;
    /**
     * 状态
     */
    private Byte state;
    /**
     * 状态描述
     */
    private String stateDes;
    /**
     * 标记状态
     */
    private Byte deliverySignState;
    /**
     * 标记状态描述
     */
    private String deliverySignStateDes;
    /**
     * 发车时间
     */
    private Date dispatchTime;
    /**
     * 发车操作人
     */
    private Integer dispatchUserId;
    /**
     * 签收完成时间
     */
    private Date signCompleteTime;
    /**
     * 初始金额
     */
    private BigDecimal payableAmount;
    /**
     * 大数量
     */
    private BigDecimal maxCount;
    /**
     * 小数量
     */
    private BigDecimal minCount;
    /**
     * 配送模式
     */
    private Byte deliveryMode;

    /**
     * 配送模式描述
     */
    private String deliveryModeDes;

    /**
     * 备注
     */
    private String remark;

    /**
     * 配送批次id
     */
    private Long deliveryTaskId;

    /**
     * 调度计划id
     */
    private Long scheduleTaskId;

    /**
     * 单据中心id
     */
    private Long orderId;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 收货人
     */
    private String locationName;
    /**
     * 业务模型
     */
    private Byte modelType;
    /**
     * 1=订单2=退货单3=兑奖单
     */
    private Byte deliveryType;
    /**
     * 描述
     */
    private String deliveryTypeDes;
    /**
     * 金额
     */
    private BigDecimal orderAmount;
    /**
     * 优惠合计
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 是否为销售单
     */
    private Boolean saleOrder = false;

    /**
     * 是否为退货单
     */
    private Boolean returnOrder = false;

    /**
     * 是否为兑奖单
     */
    private Boolean awardOrder = false;

    /**
     * 是否调度状态
     */
    private Boolean scheduledState = false;

    /**
     * 是否延迟配送
     */
    private Boolean delayDelivery = false;

    /**
     * 配送失败原因
     */
    private String deliveryFailedReason;

    /**
     * tag，用于统计不能用于业务代码，调拨=1、直配=2、城际=3
     */
    private Byte businessTag;
    /**
     * 是否需要回单
     */
    private Boolean needReceipt;

    /**
     * 是否全部配送
     */
    private Boolean allDelivery = false;

    /**
     * 获取
     *
     * @return id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置
     *
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 配送中心id
     *
     * @return deliveryCenterId 配送中心id
     */
    public Integer getDeliveryCenterId() {
        return this.deliveryCenterId;
    }

    /**
     * 设置 配送中心id
     *
     * @param deliveryCenterId 配送中心id
     */
    public void setDeliveryCenterId(Integer deliveryCenterId) {
        this.deliveryCenterId = deliveryCenterId;
    }

    /**
     * 获取 配送单号
     *
     * @return deliveryNo 配送单号
     */
    public String getDeliveryNo() {
        return this.deliveryNo;
    }

    /**
     * 设置 配送单号
     *
     * @param deliveryNo 配送单号
     */
    public void setDeliveryNo(String deliveryNo) {
        this.deliveryNo = deliveryNo;
    }

    /**
     * 获取 配送需求单id
     *
     * @return deliveryRequestId 配送需求单id
     */
    public Long getDeliveryRequestId() {
        return this.deliveryRequestId;
    }

    /**
     * 设置 配送需求单id
     *
     * @param deliveryRequestId 配送需求单id
     */
    public void setDeliveryRequestId(Long deliveryRequestId) {
        this.deliveryRequestId = deliveryRequestId;
    }

    /**
     * 获取 配送地址id
     *
     * @return deliveryLocationId 配送地址id
     */
    public Long getDeliveryLocationId() {
        return this.deliveryLocationId;
    }

    /**
     * 设置 配送地址id
     *
     * @param deliveryLocationId 配送地址id
     */
    public void setDeliveryLocationId(Long deliveryLocationId) {
        this.deliveryLocationId = deliveryLocationId;
    }

    /**
     * 获取 车辆id
     *
     * @return deliveryCarId 车辆id
     */
    public Long getDeliveryCarId() {
        return this.deliveryCarId;
    }

    /**
     * 设置 车辆id
     *
     * @param deliveryCarId 车辆id
     */
    public void setDeliveryCarId(Long deliveryCarId) {
        this.deliveryCarId = deliveryCarId;
    }

    /**
     * 获取 司机id
     *
     * @return deliveryUserId 司机id
     */
    public Integer getDeliveryUserId() {
        return this.deliveryUserId;
    }

    /**
     * 设置 司机id
     *
     * @param deliveryUserId 司机id
     */
    public void setDeliveryUserId(Integer deliveryUserId) {
        this.deliveryUserId = deliveryUserId;
    }

    /**
     * 获取 车牌号
     *
     * @return deliveryCarNumber 车牌号
     */
    public String getDeliveryCarNumber() {
        return this.deliveryCarNumber;
    }

    /**
     * 设置 车牌号
     *
     * @param deliveryCarNumber 车牌号
     */
    public void setDeliveryCarNumber(String deliveryCarNumber) {
        this.deliveryCarNumber = deliveryCarNumber;
    }

    /**
     * 获取 省
     *
     * @return province 省
     */
    public String getProvince() {
        return this.province;
    }

    /**
     * 设置 省
     *
     * @param province 省
     */
    public void setProvince(String province) {
        this.province = province;
    }

    /**
     * 获取 市
     *
     * @return city 市
     */
    public String getCity() {
        return this.city;
    }

    /**
     * 设置 市
     *
     * @param city 市
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * 获取 区
     *
     * @return county 区
     */
    public String getCounty() {
        return this.county;
    }

    /**
     * 设置 区
     *
     * @param county 区
     */
    public void setCounty(String county) {
        this.county = county;
    }

    /**
     * 获取 街道
     *
     * @return street 街道
     */
    public String getStreet() {
        return this.street;
    }

    /**
     * 设置 街道
     *
     * @param street 街道
     */
    public void setStreet(String street) {
        this.street = street;
    }

    /**
     * 获取 地址
     *
     * @return detailAddress 地址
     */
    public String getDetailAddress() {
        return this.detailAddress;
    }

    /**
     * 设置 地址
     *
     * @param detailAddress 地址
     */
    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    /**
     * 获取 手机号
     *
     * @return mobileNo 手机号
     */
    public String getMobileNo() {
        return this.mobileNo;
    }

    /**
     * 设置 手机号
     *
     * @param mobileNo 手机号
     */
    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    /**
     * 获取 联系人
     *
     * @return contact 联系人
     */
    public String getContact() {
        return this.contact;
    }

    /**
     * 设置 联系人
     *
     * @param contact 联系人
     */
    public void setContact(String contact) {
        this.contact = contact;
    }

    /**
     * 获取 状态
     *
     * @return state 状态
     */
    public Byte getState() {
        return this.state;
    }

    /**
     * 设置 状态
     *
     * @param state 状态
     */
    public void setState(Byte state) {
        this.state = state;
    }

    /**
     * 获取 标记状态
     *
     * @return deliverySignState 标记状态
     */
    public Byte getDeliverySignState() {
        return this.deliverySignState;
    }

    /**
     * 设置 标记状态
     *
     * @param deliverySignState 标记状态
     */
    public void setDeliverySignState(Byte deliverySignState) {
        this.deliverySignState = deliverySignState;
    }

    /**
     * 获取 发车时间
     *
     * @return dispatchTime 发车时间
     */
    public Date getDispatchTime() {
        return this.dispatchTime;
    }

    /**
     * 设置 发车时间
     *
     * @param dispatchTime 发车时间
     */
    public void setDispatchTime(Date dispatchTime) {
        this.dispatchTime = dispatchTime;
    }

    /**
     * 获取 发车操作人
     *
     * @return dispatchUserId 发车操作人
     */
    public Integer getDispatchUserId() {
        return this.dispatchUserId;
    }

    /**
     * 设置 发车操作人
     *
     * @param dispatchUserId 发车操作人
     */
    public void setDispatchUserId(Integer dispatchUserId) {
        this.dispatchUserId = dispatchUserId;
    }

    /**
     * 获取 签收完成时间
     *
     * @return signCompleteTime 签收完成时间
     */
    public Date getSignCompleteTime() {
        return this.signCompleteTime;
    }

    /**
     * 设置 签收完成时间
     *
     * @param signCompleteTime 签收完成时间
     */
    public void setSignCompleteTime(Date signCompleteTime) {
        this.signCompleteTime = signCompleteTime;
    }

    /**
     * 获取 初始金额
     *
     * @return payableAmount 初始金额
     */
    public BigDecimal getPayableAmount() {
        return this.payableAmount;
    }

    /**
     * 设置 初始金额
     *
     * @param payableAmount 初始金额
     */
    public void setPayableAmount(BigDecimal payableAmount) {
        this.payableAmount = payableAmount;
    }

    /**
     * 获取 大数量
     *
     * @return maxCount 大数量
     */
    public BigDecimal getMaxCount() {
        return this.maxCount;
    }

    /**
     * 设置 大数量
     *
     * @param maxCount 大数量
     */
    public void setMaxCount(BigDecimal maxCount) {
        this.maxCount = maxCount;
    }

    /**
     * 获取 小数量
     *
     * @return minCount 小数量
     */
    public BigDecimal getMinCount() {
        return this.minCount;
    }

    /**
     * 设置 小数量
     *
     * @param minCount 小数量
     */
    public void setMinCount(BigDecimal minCount) {
        this.minCount = minCount;
    }

    /**
     * 获取 配送模式
     *
     * @return deliveryMode 配送模式
     */
    public Byte getDeliveryMode() {
        return this.deliveryMode;
    }

    /**
     * 设置 配送模式
     *
     * @param deliveryMode 配送模式
     */
    public void setDeliveryMode(Byte deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    /**
     * 获取 备注
     *
     * @return remark 备注
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * 设置 备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public static PageList<DeliveryOrderInfoDTO> getDefaultPageList(PagerCondition condition) {
        PageList<DeliveryOrderInfoDTO> pageList = new PageList<>();
        pageList.setPager(new Pager(condition, 0));
        pageList.setDataList(Collections.emptyList());

        return pageList;
    }

    /**
     * 获取 配送批次id
     *
     * @return deliveryTaskId 配送批次id
     */
    public Long getDeliveryTaskId() {
        return this.deliveryTaskId;
    }

    /**
     * 设置 配送批次id
     *
     * @param deliveryTaskId 配送批次id
     */
    public void setDeliveryTaskId(Long deliveryTaskId) {
        this.deliveryTaskId = deliveryTaskId;
    }

    /**
     * 获取 调度计划id
     *
     * @return scheduleTaskId 调度计划id
     */
    public Long getScheduleTaskId() {
        return this.scheduleTaskId;
    }

    /**
     * 设置 调度计划id
     *
     * @param scheduleTaskId 调度计划id
     */
    public void setScheduleTaskId(Long scheduleTaskId) {
        this.scheduleTaskId = scheduleTaskId;
    }

    /**
     * 获取 单据中心id
     *
     * @return orderId 单据中心id
     */
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 单据中心id
     *
     * @param orderId 单据中心id
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 创建时间
     *
     * @return createTime 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置 创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 状态描述
     *
     * @return stateDes 状态描述
     */
    public String getStateDes() {
        return this.stateDes;
    }

    /**
     * 设置 状态描述
     *
     * @param stateDes 状态描述
     */
    public void setStateDes(String stateDes) {
        this.stateDes = stateDes;
    }

    /**
     * 获取 标记状态描述
     *
     * @return deliverySignStateDes 标记状态描述
     */
    public String getDeliverySignStateDes() {
        return this.deliverySignStateDes;
    }

    /**
     * 设置 标记状态描述
     *
     * @param deliverySignStateDes 标记状态描述
     */
    public void setDeliverySignStateDes(String deliverySignStateDes) {
        this.deliverySignStateDes = deliverySignStateDes;
    }

    public static PageList<DeliveryOrderInfoDTO> getDefaultPage(PagerCondition pagerCondition) {
        PageList<DeliveryOrderInfoDTO> result = new PageList<>();
        result.setDataList(Collections.emptyList());
        result.setPager(new Pager(pagerCondition, 0));
        return result;
    }

    public static PageList<DeliveryOrderInfoDTO> getOnePage(PagerCondition pagerCondition,
        List<DeliveryOrderInfoDTO> deliveryOrderList) {
        PageList<DeliveryOrderInfoDTO> result = new PageList<>();
        result.setDataList(deliveryOrderList);
        result.setPager(new Pager(pagerCondition, 1));
        return result;
    }

    /**
     * 获取 收货人
     *
     * @return locationName 收货人
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 收货人
     *
     * @param locationName 收货人
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 业务模型
     *
     * @return modelType 业务模型
     */
    public Byte getModelType() {
        return this.modelType;
    }

    /**
     * 设置 业务模型
     *
     * @param modelType 业务模型
     */
    public void setModelType(Byte modelType) {
        this.modelType = modelType;
    }

    /**
     * 获取 1=订单2=退货单
     *
     * @return deliveryType 1=订单2=退货单
     */
    public Byte getDeliveryType() {
        return this.deliveryType;
    }

    /**
     * 设置 1=订单2=退货单
     *
     * @param deliveryType 1=订单2=退货单
     */
    public void setDeliveryType(Byte deliveryType) {
        this.deliveryType = deliveryType;
    }

    /**
     * 获取 金额
     *
     * @return orderAmount 金额
     */
    public BigDecimal getOrderAmount() {
        return this.orderAmount;
    }

    /**
     * 设置 金额
     *
     * @param orderAmount 金额
     */
    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    /**
     * 获取 优惠合计
     *
     * @return totalDiscountAmount 优惠合计
     */
    public BigDecimal getTotalDiscountAmount() {
        return this.totalDiscountAmount;
    }

    /**
     * 设置 优惠合计
     *
     * @param totalDiscountAmount 优惠合计
     */
    public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }

    /**
     * 获取 描述
     *
     * @return deliveryTypeDes 描述
     */
    public String getDeliveryTypeDes() {
        return this.deliveryTypeDes;
    }

    /**
     * 设置 描述
     *
     * @param deliveryTypeDes 描述
     */
    public void setDeliveryTypeDes(String deliveryTypeDes) {
        this.deliveryTypeDes = deliveryTypeDes;
    }

    public String getDeliveryModeDes() {
        return deliveryModeDes;
    }

    public void setDeliveryModeDes(String deliveryModeDes) {
        this.deliveryModeDes = deliveryModeDes;
    }

    public Boolean getSaleOrder() {
        return saleOrder;
    }

    public void setSaleOrder(Boolean saleOrder) {
        this.saleOrder = saleOrder;
    }

    public Boolean getReturnOrder() {
        return returnOrder;
    }

    public void setReturnOrder(Boolean returnOrder) {
        this.returnOrder = returnOrder;
    }

    public Boolean getAwardOrder() {
        return awardOrder;
    }

    public void setAwardOrder(Boolean awardOrder) {
        this.awardOrder = awardOrder;
    }

    public Boolean getScheduledState() {
        return scheduledState;
    }

    public void setScheduledState(Boolean scheduledState) {
        this.scheduledState = scheduledState;
    }

    public String getDeliveryFailedReason() {
        return deliveryFailedReason;
    }

    public void setDeliveryFailedReason(String deliveryFailedReason) {
        this.deliveryFailedReason = deliveryFailedReason;
    }

    public Byte getBusinessTag() {
        return businessTag;
    }

    public void setBusinessTag(Byte businessTag) {
        this.businessTag = businessTag;
    }

    public Boolean getNeedReceipt() {
        return needReceipt;
    }

    public void setNeedReceipt(Boolean needReceipt) {
        this.needReceipt = needReceipt;
    }

    public Boolean getDelayDelivery() {
        return delayDelivery;
    }

    public void setDelayDelivery(Boolean delayDelivery) {
        this.delayDelivery = delayDelivery;
    }

    public Boolean getAllDelivery() {
        return allDelivery;
    }

    public void setAllDelivery(Boolean allDelivery) {
        this.allDelivery = allDelivery;
    }
}
