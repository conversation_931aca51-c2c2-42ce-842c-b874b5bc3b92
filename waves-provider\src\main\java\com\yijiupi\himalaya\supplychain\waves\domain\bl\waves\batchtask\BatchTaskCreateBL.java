package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchtask;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.traceablecode.dto.centerorder.OrderTraceableItemDTO;
import com.yijiupi.himalaya.supplychain.traceablecode.service.ordersync.ITransferOrderQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortGroupFlagEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SowTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ISortGroupService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderProcessBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskHelper;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation.RecommendOutLocationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.ComputerStrategyConditionBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.CreateBatchChangeOrderSequenceBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.SplitBatchTaskByPassageBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.split.SplitBatchTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.split.SplitBatchTaskBySortGroupBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.split.WaveSplitRobotBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.CreateBatchTaskByOrderResultHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderGroupSortResultHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.util.RobotPickConstants;
import com.yijiupi.himalaya.supplychain.waves.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/19
 */
@Service
public class BatchTaskCreateBL {

    @Autowired
    private WaveSplitRobotBL waveSplitRobotBL;
    @Autowired
    private BatchTaskHelper batchTaskHelper;
    @Autowired
    private ComputerStrategyConditionBL computerStrategyConditionBL;
    @Autowired
    private RecommendOutLocationBL recommendOutLocationBL;
    @Autowired
    private CreateBatchChangeOrderSequenceBL createBatchChangeOrderSequenceBL;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private SplitBatchTaskByPassageBL splitBatchTaskByPassageBL;
    @Autowired
    private SplitBatchTaskBySortGroupBL splitBatchTaskBySortGroupBL;
    @Autowired
    private SplitBatchTaskBL splitBatchTaskBL;

    @Reference
    private ITransferOrderQueryService iTransferOrderQueryService;
    @Reference
    private ISortGroupService iSortGroupService;
    private static final Logger LOG = LoggerFactory.getLogger(BatchTaskCreateBL.class);

    /**
     * 按订单创建拣货任务
     *
     * @param batchPO
     * @param wavesStrategyBO
     * @param createDTO
     * @return
     */
    public CreateBatchTaskByOrderResultHelperBO createBatchTaskByOrder(BatchPO batchPO, WavesStrategyBO wavesStrategyBO,
        WaveCreateDTO createDTO) {
        // 按地址拆按订单拣货的拣货任务
        List<SplitBatchTaskByOrderResultBO> splitBatchTaskByOrderResultBOS =
            splitBatchTaskBL.splitBatchTaskByOrder(createDTO, batchPO);

        List<CreateBatchTaskByOrderResultHelperBO> createBatchTaskByOrderResultHelperBOList = new ArrayList<>();
        // 这里要再拆分一遍
        for (SplitBatchTaskByOrderResultBO resultBO : splitBatchTaskByOrderResultBOS) {
            // 酒饮的按分区拆分拣货任务；因为酒饮和休食的波次已经拆分了，这里可以安心处理;
            List<SplitBatchTaskByOrderGroupSortResultHelperBO> helperBOList = splitBatchTaskBySortGroupBL
                .splitBatchTaskBySortGroup(batchPO, wavesStrategyBO, resultBO.getOutStockOrderPOList());

            List<CreateBatchTaskByOrderResultHelperBO> list =
                createBatchTaskByOrder(resultBO, helperBOList, wavesStrategyBO, batchPO, createDTO);
            createBatchTaskByOrderResultHelperBOList.addAll(list);
        }

        CreateBatchTaskByOrderResultHelperBO finalResult =
            CreateBatchTaskByOrderResultHelperBO.getBOByBOList(createBatchTaskByOrderResultHelperBOList);

        return finalResult;
    }

    private List<CreateBatchTaskByOrderResultHelperBO> createBatchTaskByOrder(SplitBatchTaskByOrderResultBO resultBO,
        List<SplitBatchTaskByOrderGroupSortResultHelperBO> helperBOList, WavesStrategyBO wavesStrategyBO,
        BatchPO batchPO, WaveCreateDTO createDTO) {
        // 不存在，直接按订单创建拣货任务
        if (CollectionUtils.isEmpty(helperBOList)) {
            CreateBatchTaskByOrderResultHelperBO createBatchTaskByOrderResultHelperBO =
                processBatchTaskByOrder(batchPO, resultBO.getOutStockOrderPOList(), wavesStrategyBO, createDTO);
            return Collections.singletonList(createBatchTaskByOrderResultHelperBO);
        }

        List<CreateBatchTaskByOrderResultHelperBO> resultHelperBOS = new ArrayList<>();
        // 酒饮按订单分区拣货
        List<SplitBatchTaskByOrderGroupSortResultHelperBO> drinkPickByOrderResultHelperList =
            helperBOList.stream().filter(m -> Objects.nonNull(m.getSortGroupListDTO())).collect(Collectors.toList());

        // 没进分区的订单
        List<SplitBatchTaskByOrderGroupSortResultHelperBO> normalResultHelperList =
            helperBOList.stream().filter(m -> Objects.isNull(m.getSortGroupListDTO())).collect(Collectors.toList());

        List<CreateBatchTaskByOrderResultHelperBO> totalResultHelperBOS = new ArrayList<>();
        // 酒饮分区创建拣货任务
        if (!CollectionUtils.isEmpty(drinkPickByOrderResultHelperList)) {
            List<CreateBatchTaskByOrderResultHelperBO> createBatchTaskByOrderResultHelperBOList =
                drinkPickByOrderResultHelperList.stream().map(resultHelperBO -> {
                    List<OutStockOrderPO> tmpOutStockOrderPOList = resultHelperBO.getOutStockOrderPOList();
                    // 按订单创建拣货任务
                    CreateBatchTaskByOrderResultHelperBO tmpResultHelperList = processBatchTaskByOrder(batchPO,
                        resultHelperBO.getOutStockOrderPOList(), wavesStrategyBO, createDTO);
                    // 设置拣货任务 分区属性 和 任务类型
                    tmpResultHelperList.getBatchTaskPOList().forEach(batchTaskPO -> {
                        batchTaskHelper.handleSorterGroupBatchTask(resultHelperBO.getSortGroupListDTO(), batchTaskPO,
                            null, wavesStrategyBO);
                        batchTaskPO.setBatchTaskType(BatchTaskTypeEnum.酒饮按订单分区拣货.getType());
                        batchTaskHelper.setBatchTaskName(batchTaskPO, wavesStrategyBO, createDTO.getPassageName(),
                            createDTO, SplitWaveOrderUtil.hasAllotOrderByItem(tmpOutStockOrderPOList));
                    });
                    return tmpResultHelperList;
                }).collect(Collectors.toList());

            totalResultHelperBOS.addAll(createBatchTaskByOrderResultHelperBOList);
        }

        // 如果都在分区，则直接返回
        if (CollectionUtils.isEmpty(normalResultHelperList)) {
            return totalResultHelperBOS;
        }

        // 如果有订单有进分区的，还有没进分区的，则订单项未进分区的，把这些单独拆成一个拣货任务，也需要强制设置类型为酒饮按分区拣货
        Map<Long,
            Long> drinkPickByOrderMap = drinkPickByOrderResultHelperList.stream()
                .map(SplitBatchTaskByOrderGroupSortResultHelperBO::getOutStockOrderPOList).flatMap(Collection::stream)
                .map(OutStockOrderPO::getId).distinct().collect(Collectors.toMap(k -> k, v -> v));

        List<OutStockOrderPO> normalOrderList = normalResultHelperList.stream()
            .flatMap(m -> m.getOutStockOrderPOList().stream()).collect(Collectors.toList());

        // 有订单项进分区的订单
        List<OutStockOrderPO> hasInGroupOrderList = normalOrderList.stream()
            .filter(o -> drinkPickByOrderMap.containsKey(o.getId())).collect(Collectors.toList());

        // 没有订单项进分区的订单
        List<OutStockOrderPO> notHasInGroupOrderList = normalOrderList.stream()
            .filter(o -> drinkPickByOrderMap.containsKey(o.getId())).collect(Collectors.toList());

        // 有订单项进分区的订单创建拣货任务
        if (!CollectionUtils.isEmpty(hasInGroupOrderList)) {
            CreateBatchTaskByOrderResultHelperBO createBatchTaskByOrderResultHelperBO =
                // 按订单创建拣货任务
                processBatchTaskByOrder(batchPO, hasInGroupOrderList, wavesStrategyBO, createDTO);
            createBatchTaskByOrderResultHelperBO.getBatchTaskPOList().forEach(batchTaskPO -> {
                batchTaskPO.setBatchTaskType(BatchTaskTypeEnum.酒饮按订单分区拣货.getType());
            });

            totalResultHelperBOS.add(createBatchTaskByOrderResultHelperBO);
        }

        // 其他按订单拣货的拣货任务
        if (!CollectionUtils.isEmpty(notHasInGroupOrderList)) {
            CreateBatchTaskByOrderResultHelperBO createBatchTaskByOrderResultHelperBO =
                // 按订单创建拣货任务
                processBatchTaskByOrder(batchPO, notHasInGroupOrderList, wavesStrategyBO, createDTO);

            totalResultHelperBOS.add(createBatchTaskByOrderResultHelperBO);
        }

        return totalResultHelperBOS;
    }

    /**
     * 处理按订单拣货
     *
     * @param batchPO 波次对象
     * @param waveOrders 拣货任务关联的订单集合
     * @param wavesStrategyDTO 拆分波次的条件
     * @param createDTO 生成拣货任务需要的数据对象
     * @see BatchOrderProcessBL#processBatchTaskByOrder(BatchPO, List, List, List, WavesStrategyBO, List, WaveCreateDTO)
     */
    public CreateBatchTaskByOrderResultHelperBO processBatchTaskByOrder(BatchPO batchPO,
        List<OutStockOrderPO> waveOrders, WavesStrategyBO wavesStrategyDTO, WaveCreateDTO createDTO) {
        List<BatchTaskPO> lstBatchTask = new ArrayList<>();
        List<BatchTaskItemDTO> lstBatchTaskItem = new ArrayList<>();
        List<OrderItemTaskInfoPO> lstOrderItemTask = new ArrayList<>();

        PassageDTO passageDTO = createDTO.getPassageDTO();

        Long sowId = null;
        String sowNo = null;
        Optional<OutStockOrderItemPO> first = waveOrders.stream().flatMap(order -> order.getItems().stream())
            .filter(item -> item.getSowTaskNo() != null).findFirst();
        if (first.isPresent()) {
            sowId = first.get().getSowTaskId();
            sowNo = first.get().getSowTaskNo();
        }

        // 按订单拣货： 一个波次一个拣货任务
        // 按产品拣货：具体按产品类目、货位、货区来

        BatchTaskPO batchTaskPO = new BatchTaskPO();
        batchTaskPO
            .setBatchTaskNo(UuidUtil.generator(wavesStrategyDTO.getWarehouseId(), BatchOrderTypeConstans.BATCHTASK_NO));
        batchTaskPO.setId(UuidUtil.generatorId());
        BigDecimal packageCount = computerStrategyConditionBL.computerStrategyConditionByOrders(waveOrders,
            WavesStrategyLimitType.piecePackageNumberLimitType);
        batchTaskPO.setPackageAmount(packageCount);
        BigDecimal unitCount = computerStrategyConditionBL.computerStrategyConditionByOrders(waveOrders,
            WavesStrategyLimitType.pieceUnitNumberLimitType);
        batchTaskPO.setUnitAmount(unitCount);
        BigDecimal orderCount = computerStrategyConditionBL.computerStrategyConditionByOrders(waveOrders,
            WavesStrategyLimitType.orderCountLimitType);
        batchTaskPO.setOrderCount(orderCount.intValue());
        BigDecimal orderAmount = computerStrategyConditionBL.computerStrategyConditionByOrders(waveOrders,
            WavesStrategyLimitType.orderAmountLimitType);
        batchTaskPO.setOrderAmount(orderAmount);

        final List<Long> lstSkuIds = new ArrayList<>();
        waveOrders.forEach(p -> {
            p.getItems().forEach(q -> {
                if (!lstSkuIds.contains(q.getSkuid())) {
                    lstSkuIds.add(q.getSkuid());
                }
            });
        });
        batchTaskPO.setSkuCount(lstSkuIds.size());
        // 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
        batchTaskPO.setTaskState(TaskStateEnum.未分拣.getType());
        // batchTaskPO.setSorter(null);
        // batchTaskPO.setSorterId(null);
        batchTaskPO.setOrgId(String.valueOf(batchPO.getOrgId()));
        batchTaskPO.setBatchId(batchPO.getId());
        batchTaskPO.setBatchNo(batchPO.getBatchNo());
        boolean hasAllotOrder = SplitWaveOrderUtil.hasAllotOrderByItem(waveOrders);
        // 添加拣货属性和拣货方式
        String passageName = createDTO.getPassageName();
        batchTaskHelper.setBatchTaskName(batchTaskPO, wavesStrategyDTO, passageName, createDTO, hasAllotOrder);
        batchTaskPO.setSowTaskId(sowId);
        batchTaskPO.setSowTaskNo(sowNo);
        // 是否需要拣货
        batchTaskPO.setNeedPick(ConditionStateEnum.是.getType());

        // 设置拣货任务所属仓库
        batchTaskPO.setWarehouseId(batchPO.getWarehouseId());
        // 拣货任务类型
        batchTaskPO.setBatchTaskType(
            createDTO.getBatchTaskType() == null ? BatchTaskTypeEnum.默认.getType() : createDTO.getBatchTaskType());
        batchTaskPO.setPickPattern(RobotPickConstants.getBatchTaskRobotPickType(createDTO, passageDTO));
        batchTaskHelper.setTaskWarehouseFeatureType(batchTaskPO, wavesStrategyDTO);
        batchTaskHelper.handleSorterGroupBatchTask(null, batchTaskPO, sowId, wavesStrategyDTO);
        batchTaskPO.setTaskAppendSequence((byte)0);
        if (Objects.nonNull(createDTO.getAppendSequence())) {
            batchTaskPO.setTaskAppendSequence(createDTO.getAppendSequence().byteValue());
        }
        batchTaskPO.setKindOfPicking(
            Optional.ofNullable(createDTO.getKindOfPicking()).orElse(BatchTaskKindOfPickingConstants.DEFAULT));

        // 通道
        if (passageDTO != null) {
            batchTaskPO.setPassageId(passageDTO.getId());
            batchTaskPO.setPassageName(passageDTO.getPassageName());
        }
        lstBatchTask.add(batchTaskPO);

        // 接订单中台时，需通过TMS接口获取订单项的控货策略ID
        Map<Long, OrderTraceableItemDTO> orderTraceableItemMap = null;
        List<Long> businessItemIdList = waveOrders.stream().filter(ord -> !CollectionUtils.isEmpty(ord.getItems()))
            .flatMap(ord -> ord.getItems().stream()).map(OutStockOrderItemPO::getBusinessItemId)
            .filter(Objects::nonNull).map(Long::valueOf).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(businessItemIdList)) {
            orderTraceableItemMap =
                iTransferOrderQueryService.selectOrderTraceableItemInfoByOrderItemIds(businessItemIdList).stream()
                    .filter(elem -> elem.getOrderItemId() != null && elem.getProductControlId() != null).collect(
                        Collectors.toMap(OrderTraceableItemDTO::getOrderItemId, Function.identity(), (k1, k2) -> k1));
            LOG.info("[生成波次]接订单中台时，查TMS的控货策略：{}", JSON.toJSONString(orderTraceableItemMap));
        }

        if (BatchTaskPickPatternEnum.机器人拣货.getType().equals(batchTaskPO.getPickPattern())) {
            batchTaskPO.setSorter(RobotSorterConstants.ROBOT_NAME);
            batchTaskPO.setSorterId(RobotSorterConstants.ROBOT_ID);
        }

        // 设置按订单拣货-按订单分组的出库位
        recommendOutLocationBL.setOutLocationByGroup(batchTaskPO, waveOrders, wavesStrategyDTO, createDTO);

        for (OutStockOrderPO p : waveOrders) {
            if (CollectionUtils.isEmpty(p.getItems())) {
                continue;
            }
            for (OutStockOrderItemPO q : p.getItems()) {
                // 出库单详情关联拣货任务相关信息
                q.setBatchtaskId(batchTaskPO.getId());
                q.setBatchtaskno(batchTaskPO.getBatchTaskNo());
                // // 播种任务
                // q.setSowTaskId(sowId);
                // q.setSowTaskNo(sowNo);

                // 没开启按客户拣货 相同订单项+货位进行合并；开启了 按用户拣货，订单拣货时数量不合并
                if (lstBatchTaskItem.stream()
                    .anyMatch(batchTaskItemDTO -> Objects.equals(batchTaskItemDTO.getOrderItemId(), q.getId())
                        && Objects.equals(batchTaskItemDTO.getLocationId(), q.getLocationId()))
                    && BooleanUtils.isFalse(wavesStrategyDTO.isPickByCustomer())) {
                    BatchTaskItemDTO itemDTO = lstBatchTaskItem.stream()
                        .filter(batchTaskItemDTO -> Objects.equals(batchTaskItemDTO.getOrderItemId(), q.getId())
                            && Objects.equals(batchTaskItemDTO.getLocationId(), q.getLocationId()))
                        .findAny().get();
                    itemDTO.setUnitTotalCount(itemDTO.getUnitTotalCount().add(q.getUnittotalcount()));
                    itemDTO.setPackageCount(itemDTO.getPackageCount().add(q.getPackagecount()));
                    itemDTO.setUnitCount(itemDTO.getUnitCount().add(q.getUnitcount()));
                    // 出库单详情关联拣货任务项id
                    q.setBatchTaskItemId(itemDTO.getId());
                    // 订单项关联拣货任务项
                    addOrderItemTaskInfoPO(lstOrderItemTask, q, itemDTO, batchTaskPO);
                    continue;
                }
                BatchTaskItemDTO itemDTO = new BatchTaskItemDTO();
                itemDTO.setBatchTaskId(batchTaskPO.getId());
                itemDTO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
                itemDTO.setId(UuidUtil.generatorId());
                itemDTO.setLocationId(q.getLocationId());
                itemDTO.setLocationName(q.getLocationName());
                itemDTO.setLocationCategory(q.getLocationCategory());
                itemDTO.setOrgId(batchPO.getOrgId());
                itemDTO.setPackageCount(q.getPackagecount());
                itemDTO.setPackageName(q.getPackagename());
                itemDTO.setUnitCount(q.getUnitcount());
                itemDTO.setUnitName(q.getUnitname());
                itemDTO.setProductBrand(q.getProductbrand());
                itemDTO.setCategoryName(q.getCategoryname());
                itemDTO.setProductName(q.getProductname());
                itemDTO.setRefOrderId(String.valueOf(p.getId()));
                itemDTO.setSaleSpec(q.getSalespec());
                itemDTO.setRefOrderNo(p.getReforderno());
                itemDTO.setSaleSpecQuantity(q.getSalespecquantity());
                itemDTO.setSkuId(q.getSkuid());
                // 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
                itemDTO.setTaskState(TaskItemStateEnum.未分拣.getType());
                itemDTO.setSpecName(q.getSpecname());
                itemDTO.setOverSortCount(BigDecimal.ZERO);
                itemDTO.setLackUnitCount(BigDecimal.ZERO);
                itemDTO.setUnitTotalCount(q.getUnittotalcount());
                itemDTO.setOrderItemId(q.getId());
                itemDTO.setSpecQuantity(q.getSpecquantity());
                itemDTO.setSource(q.getSource());
                itemDTO.setChannel(q.getChannel());

                itemDTO.setOwnerId(q.getOwnerId());
                itemDTO.setSecOwnerId(q.getSecOwnerId());
                itemDTO.setProductSpecificationId(q.getProductSpecificationId());
                if (orderTraceableItemMap != null && StringUtils.isNotEmpty(q.getBusinessItemId())) {
                    OrderTraceableItemDTO traceableItemDTO =
                        orderTraceableItemMap.get(Long.valueOf(q.getBusinessItemId()));
                    if (traceableItemDTO != null) {
                        itemDTO.setControlConfigId(traceableItemDTO.getProductControlId());
                    }
                } else {
                    itemDTO.setControlConfigId(q.getControlConfigId());
                }
                // 生产日期和入库时间
                itemDTO.setBatchTime(q.getBatchTime());
                itemDTO.setProductionDate(q.getProductionDate());
                // 产品单价
                itemDTO.setUnitPrice(q.getUnitPrice());
                itemDTO.setCompleteUser(batchTaskPO.getSorter());
                itemDTO.setCompleteUserId(batchTaskPO.getSorterId());
                itemDTO.setLargePickPattern(createDTO.getLargePick());
                lstBatchTaskItem.add(itemDTO);
                // 出库单详情关联拣货任务相关信息
                q.setBatchtaskId(itemDTO.getBatchTaskId());
                q.setBatchtaskno(itemDTO.getBatchTaskNo());
                q.setBatchTaskItemId(itemDTO.getId());
                // // 播种任务
                // q.setSowTaskId(sowId);
                // q.setSowTaskNo(sowNo);

                // 订单项关联拣货任务项
                addOrderItemTaskInfoPO(lstOrderItemTask, q, itemDTO, batchTaskPO);
            }
        }

        createBatchChangeOrderSequenceBL.updateOrderSequenceIfSplitByUser(createDTO, waveOrders);

        CreateBatchTaskByOrderResultHelperBO resultHelperBO = new CreateBatchTaskByOrderResultHelperBO();
        resultHelperBO.setBatchTaskPOList(lstBatchTask);
        resultHelperBO.setBatchTaskItemDTOList(lstBatchTaskItem);
        resultHelperBO.setOrderItemTaskInfoPOList(lstOrderItemTask);

        return resultHelperBO;
    }

    /**
     * 处理按产品拣货（订单项维度）
     *
     * @param sortGroupListDTO 拣货分区id
     * @param batchPO 波次对象
     * @param waveOrderItems 拣货任务关联的订单项集合
     * @param wavesStrategyDTO 拆分波次的条件
     * @param createDTO 生成拣货任务需要的数据对象
     * @see BatchOrderProcessBL#processBatchTaskByOrderItem(SortGroupListDTO, BatchPO, List, List, List,
     *      WavesStrategyBO, WaveCreateDTO, List)
     */
    public CreateBatchTaskByOrderResultHelperBO processBatchTaskByOrderItem(SortGroupListDTO sortGroupListDTO,
        BatchPO batchPO, List<OutStockOrderItemPO> waveOrderItems, WavesStrategyBO wavesStrategyDTO,
        WaveCreateDTO createDTO) {

        List<BatchTaskPO> lstBatchTask = new ArrayList<>();
        List<BatchTaskItemDTO> lstBatchTaskItem = new ArrayList<>();
        List<OrderItemTaskInfoPO> lstOrderItemTask = new ArrayList<>();

        Long sowId = null;
        String sowNo = null;

        Optional<OutStockOrderItemPO> first =
            waveOrderItems.stream().filter(item -> item.getSowTaskId() != null).findFirst();
        if (first.isPresent()) {
            sowId = first.get().getSowTaskId();
            sowNo = first.get().getSowTaskNo();
        }
        PassageDTO passageDTO = createDTO.getPassageDTO();
        // 按订单拣货： 一个波次一个拣货任务
        // 按产品拣货：具体按产品类目、货位、货区来分
        BatchTaskPO batchTaskPO = new BatchTaskPO();
        batchTaskPO.setBatchTaskNo(UuidUtil.generator(batchPO.getWarehouseId(), BatchOrderTypeConstans.BATCHTASK_NO));
        batchTaskPO.setId(UuidUtil.generatorId());
        BigDecimal packageCount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.piecePackageNumberLimitType);
        batchTaskPO.setPackageAmount(packageCount);
        BigDecimal unitCount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.pieceUnitNumberLimitType);
        batchTaskPO.setUnitAmount(unitCount);
        BigDecimal orderCount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.orderCountLimitType);
        batchTaskPO.setOrderCount(orderCount.intValue());
        BigDecimal orderAmount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.orderAmountLimitType);
        batchTaskPO.setOrderAmount(orderAmount);
        BigDecimal skuCount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.skuCountLimitType);
        batchTaskPO.setSkuCount(skuCount.intValue());
        // 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
        batchTaskPO.setTaskState(TaskStateEnum.未分拣.getType());
        batchTaskPO.setOrgId(String.valueOf(batchPO.getOrgId()));
        batchTaskPO.setPickPattern(RobotPickConstants.getBatchTaskRobotPickType(createDTO, passageDTO));

        batchTaskHelper.handleSorterGroupBatchTask(sortGroupListDTO, batchTaskPO, sowId, wavesStrategyDTO);
        batchTaskHelper.setTaskWarehouseFeatureType(batchTaskPO, wavesStrategyDTO);
        batchTaskPO.setTaskAppendSequence((byte)0);

        if (Objects.nonNull(createDTO.getAppendSequence())) {
            batchTaskPO.setTaskAppendSequence(createDTO.getAppendSequence().byteValue());
        }

        if (BatchTaskPickPatternEnum.机器人拣货.getType().equals(batchTaskPO.getPickPattern())) {
            batchTaskPO.setSorter(RobotSorterConstants.ROBOT_NAME);
            batchTaskPO.setSorterId(RobotSorterConstants.ROBOT_ID);
        }

        batchTaskPO.setBatchId(batchPO.getId());
        batchTaskPO.setBatchNo(batchPO.getBatchNo());
        boolean hasAllotOrder = SplitWaveOrderUtil.hasAllotOrderByItem(createDTO.getOrders(), waveOrderItems);
        // 添加拣货属性和拣货方式
        String passageName = passageDTO != null ? passageDTO.getPassageName() : null;
        batchTaskHelper.setBatchTaskName(batchTaskPO, wavesStrategyDTO, passageName, createDTO, hasAllotOrder);
        batchTaskPO.setSowTaskId(sowId);
        batchTaskPO.setSowTaskNo(sowNo);
        // 是否需要拣货
        batchTaskPO.setNeedPick(ConditionStateEnum.是.getType());
        batchTaskPO.setKindOfPicking(
            Optional.ofNullable(createDTO.getKindOfPicking()).orElse(BatchTaskKindOfPickingConstants.DEFAULT));

        Byte pickingGroupStrategy = wavesStrategyDTO.getPickingGroupStrategy();
        if (pickingGroupStrategy.intValue() == WavesStrategyConstants.PICKINGGROUPSTRATEGY_CARGOAREA) {
            batchTaskPO.setLocationId(waveOrderItems.stream().findAny().get().getAreaId());
            batchTaskPO.setLocationName(waveOrderItems.stream().findAny().get().getAreaName());
        } else if (pickingGroupStrategy.intValue() == WavesStrategyConstants.PICKINGGROUPSTRATEGY_LOCATION) {
            batchTaskPO.setLocationId(waveOrderItems.stream().findAny().get().getLocationId());
            batchTaskPO.setLocationName(waveOrderItems.stream().findAny().get().getLocationName());
        } else if (pickingGroupStrategy.intValue() == WavesStrategyConstants.PICKINGGROUPSTRATEGY_CATEGORY) {
            batchTaskPO.setCategoryName(waveOrderItems.stream().findAny().get().getCategoryname());
        }

        // 处理二次分拣出库位逻辑
        setSecondSortBatchTaskLocation(batchTaskPO, wavesStrategyDTO, waveOrderItems);
        // 设置拣货任务所属仓库
        batchTaskPO.setWarehouseId(batchPO.getWarehouseId());
        // 拣货任务类型
        batchTaskPO.setBatchTaskType(
            createDTO.getBatchTaskType() == null ? BatchTaskTypeEnum.默认.getType() : createDTO.getBatchTaskType());
        batchTaskHelper.setTaskWarehouseFeatureType(batchTaskPO, wavesStrategyDTO);
        batchTaskPO.setTaskAppendSequence((byte)0);
        batchTaskHelper.handleSorterGroupBatchTask(sortGroupListDTO, batchTaskPO, sowId, wavesStrategyDTO);
        if (Objects.nonNull(createDTO.getAppendSequence())) {
            batchTaskPO.setTaskAppendSequence(createDTO.getAppendSequence().byteValue());
        }

        // 通道
        if (passageDTO != null) {
            batchTaskPO.setPassageId(passageDTO.getId());
            batchTaskPO.setPassageName(passageDTO.getPassageName());
        }
        lstBatchTask.add(batchTaskPO);

        // 接订单中台时，需通过TMS接口获取订单项的控货策略ID
        Map<Long, OrderTraceableItemDTO> orderTraceableItemMap = null;
        List<Long> businessItemIdList = waveOrderItems.stream().map(OutStockOrderItemPO::getBusinessItemId)
            .filter(Objects::nonNull).map(Long::valueOf).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(businessItemIdList)) {
            orderTraceableItemMap =
                iTransferOrderQueryService.selectOrderTraceableItemInfoByOrderItemIds(businessItemIdList).stream()
                    .filter(elem -> elem.getOrderItemId() != null && elem.getProductControlId() != null).collect(
                        Collectors.toMap(OrderTraceableItemDTO::getOrderItemId, Function.identity(), (k1, k2) -> k1));
            LOG.info("[生成波次]接订单中台时，查TMS的控货策略：{}", JSON.toJSONString(orderTraceableItemMap));
        }

        List<BatchTaskItemDTO> currBatchTaskItemList = new ArrayList<>();
        for (OutStockOrderItemPO q : waveOrderItems) {
            q.setBatchtaskId(batchTaskPO.getId());
            q.setBatchtaskno(batchTaskPO.getBatchTaskNo());
            // // 播种任务
            // q.setSowTaskId(sowId);
            // q.setSowTaskNo(sowNo);

            BatchTaskItemDTO itemDTO = new BatchTaskItemDTO();
            // 如果是分区分单则细分到订单
            if (passageDTO != null && (passageDTO.getSowType() == SowTypeEnum.分区分单播种.getType()
                || passageDTO.getSowType() == SowTypeEnum.分区分单不播种.getType()
                || passageDTO.getSowType() == SowTypeEnum.分区分单拣货.getType())) {
                itemDTO.setOrderItemId(q.getId());
                itemDTO.setRefOrderId(q.getOutstockorderId().toString());
                itemDTO.setRefOrderNo("");
            } else {
                if (lstBatchTaskItem.stream()
                    .anyMatch(p -> q.getProductIdentityKey().equals(p.getProductIdentityKey()))) {
                    itemDTO = lstBatchTaskItem.stream()
                        .filter(p -> q.getProductIdentityKey().equals(p.getProductIdentityKey())).findAny().get();

                    // 按产品拣货，不区分订单，将相同SKU货位及来源渠道的产品项合并
                    itemDTO.setUnitTotalCount(itemDTO.getUnitTotalCount().add(q.getUnittotalcount()));
                    itemDTO.setPackageCount(itemDTO.getPackageCount().add(q.getPackagecount()));
                    itemDTO.setUnitCount(itemDTO.getUnitCount().add(q.getUnitcount()));
                    // LOG.info(String.format("相同产品合并：%s", JSON.toJSONString(itemDTO)));
                    // 订单详情表中保存拣货任务项id
                    q.setBatchTaskItemId(itemDTO.getId());
                    // 订单项关联拣货任务项
                    addOrderItemTaskInfoPO(lstOrderItemTask, q, itemDTO, batchTaskPO);
                    continue;
                }
                // 按产品拣货，不需要将订单项与拣货任务关联。
                // 因为多个订单包含同一个产品，无法区分
                itemDTO.setRefOrderId("");
                itemDTO.setRefOrderNo("");
            }
            itemDTO.setBatchTaskId(batchTaskPO.getId());
            itemDTO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
            itemDTO.setId(UuidUtil.generatorId());
            itemDTO.setLocationId(q.getLocationId());
            itemDTO.setLocationName(q.getLocationName());
            itemDTO.setLocationCategory(q.getLocationCategory());
            itemDTO.setOrgId(batchPO.getOrgId());
            itemDTO.setPackageCount(q.getPackagecount());
            itemDTO.setPackageName(q.getPackagename());
            itemDTO.setUnitCount(q.getUnitcount());
            itemDTO.setUnitName(q.getUnitname());
            itemDTO.setProductBrand(q.getProductbrand());
            itemDTO.setCategoryName(q.getCategoryname());
            itemDTO.setProductName(q.getProductname());
            itemDTO.setSaleSpec(q.getSalespec());
            itemDTO.setSaleSpecQuantity(q.getSalespecquantity());
            itemDTO.setSkuId(q.getSkuid());
            // 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
            itemDTO.setTaskState(TaskItemStateEnum.未分拣.getType());
            itemDTO.setSpecName(q.getSpecname());
            itemDTO.setOverSortCount(BigDecimal.ZERO);
            itemDTO.setLackUnitCount(BigDecimal.ZERO);
            itemDTO.setUnitTotalCount(q.getUnittotalcount());
            // itemDTO.setOrderItemId(q.getId());
            itemDTO.setSpecQuantity(q.getSpecquantity());
            itemDTO.setSource(q.getSource());
            itemDTO.setChannel(q.getChannel());

            itemDTO.setOwnerId(q.getOwnerId());
            itemDTO.setSecOwnerId(q.getSecOwnerId());
            itemDTO.setProductSpecificationId(q.getProductSpecificationId());
            if (orderTraceableItemMap != null && StringUtils.isNotEmpty(q.getBusinessItemId())) {
                OrderTraceableItemDTO traceableItemDTO = orderTraceableItemMap.get(Long.valueOf(q.getBusinessItemId()));
                if (traceableItemDTO != null) {
                    itemDTO.setControlConfigId(traceableItemDTO.getProductControlId());
                }
            } else {
                itemDTO.setControlConfigId(q.getControlConfigId());
            }
            // 生产日期和入库时间
            itemDTO.setBatchTime(q.getBatchTime());
            itemDTO.setProductionDate(q.getProductionDate());
            // 产品单价
            itemDTO.setUnitPrice(q.getUnitPrice());

            itemDTO.setCompleteUser(batchTaskPO.getSorter());
            itemDTO.setCompleteUserId(batchTaskPO.getSorterId());
            itemDTO.setLargePickPattern(createDTO.getLargePick());
            itemDTO.setIsAdvent(q.getIsAdvent());
            lstBatchTaskItem.add(itemDTO);
            currBatchTaskItemList.add(itemDTO);

            // 订单详情表中保存拣货任务项id
            q.setBatchTaskItemId(itemDTO.getId());

            // 订单项关联拣货任务项
            addOrderItemTaskInfoPO(lstOrderItemTask, q, itemDTO, batchTaskPO);
        }

        waveSplitRobotBL.resetLargePickPattern(currBatchTaskItemList, passageDTO, createDTO, waveOrderItems);

        CreateBatchTaskByOrderResultHelperBO resultHelperBO = new CreateBatchTaskByOrderResultHelperBO();
        resultHelperBO.setBatchTaskPOList(lstBatchTask);
        resultHelperBO.setBatchTaskItemDTOList(lstBatchTaskItem);
        resultHelperBO.setOrderItemTaskInfoPOList(lstOrderItemTask);

        return resultHelperBO;
    }

    /**
     * 处理按产品拣货的拣货任务
     *
     * @param batchPO 波次对象
     * @param waveOrders 拣货任务关联的订单集合
     * @param wavesStrategyDTO 拆分波次的条件
     * @param scmVersionByWarehouseId 仓库版本
     * @param sowId 播种任务id
     * @param sowNo 播种任务编号
     * @param createDTO 生成拣货任务需要的数据对象
     * @return
     * @see BatchOrderProcessBL#processBatchTaskByProduct(BatchPO, List, List, List, List, WavesStrategyBO, Byte, Long,
     *      String, WaveCreateDTO, List)
     */
    public CreateBatchTaskByOrderResultHelperBO processBatchTaskByProduct(BatchPO batchPO,
        List<OutStockOrderPO> waveOrders, WavesStrategyBO wavesStrategyDTO, Byte scmVersionByWarehouseId, Long sowId,
        String sowNo, WaveCreateDTO createDTO) {

        List<CreateBatchTaskByOrderResultHelperBO> createBatchTaskByOrderResultHelperBOList = new ArrayList<>();

        List<OutStockOrderPO> lstNoLocationOrder = new ArrayList<>();
        if (PickingGroupStrategyEnum.货位.getType() == wavesStrategyDTO.getPickingGroupStrategy()
            || PickingGroupStrategyEnum.货区.getType() == wavesStrategyDTO.getPickingGroupStrategy()) {
            // if (WarehouseConfigConstants.SCM_VERSION_2_5.equals(scmVersionByWarehouseId)) {
            // //获取商品无货位的订单，waveOrders过滤无货位订单项
            // lstNoLocationOrder = getNoLocationOrder(waveOrders);
            // LOG.info("noLocatioOrderPOS:{}", JSON.toJSONString(lstNoLocationOrder));
            // }
            lstNoLocationOrder = SplitWaveOrderUtil.getNoLocationOrder(waveOrders);
            LOG.info("noLocatioOrderPOS:{}", JSON.toJSONString(lstNoLocationOrder));
        }

        List<OutStockOrderItemPO> lstOrderItems = new ArrayList<>();
        waveOrders.forEach(p -> {
            lstOrderItems.addAll(p.getItems());
        });

        // 按分区拆分拣货任务
        if (SplitWaveOrderUtil.couldSplitBySortGroup(wavesStrategyDTO, createDTO)) {
            SortGroupSO so = new SortGroupSO();
            so.setWarehouseId(batchPO.getWarehouseId());
            // 根据拣货分组策略 1货区 2货位 3类目
            so.setGroupType(wavesStrategyDTO.getPickingGroupStrategy());
            // 分区标识
            so.setFlag(SortGroupFlagEnum.分区拣货.getType());
            PageList<SortGroupListDTO> listGroup = iSortGroupService.listGroup(so);
            if (!listGroup.getDataList().isEmpty()) {
                // 如果按类目分组，获取一二级类目ID
                if (WavesStrategyConstants.PICKINGGROUPSTRATEGY_CATEGORY == wavesStrategyDTO.getPickingGroupStrategy()
                    .intValue()) {
                    splitBatchTaskByPassageBL.setCategoryId(lstOrderItems);
                }
                listGroup.getDataList().forEach(group -> {
                    Long groupId = group.getId();
                    SortGroupDTO groupDTO = iSortGroupService.getGroup(groupId);
                    List<String> lstIds = groupDTO.getGroupSettingList().stream().filter(p -> p.getSortId() != null)
                        .map(SortGroupSettingDTO::getSortId).distinct().collect(Collectors.toList());
                    List<OutStockOrderItemPO> lstTmpItems = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(lstIds)) {
                        // 根据拣货分组策略 1货区 2货位 3类目
                        switch (wavesStrategyDTO.getPickingGroupStrategy().intValue()) {
                            case WavesStrategyConstants.PICKINGGROUPSTRATEGY_CARGOAREA:
                                LOG.info(String.format("按货区拣货，分区：%s", JSON.toJSONString(groupDTO)));
                                lstTmpItems = lstOrderItems.stream()
                                    .filter(p -> p.getAreaId() != null && lstIds.contains(p.getAreaId().toString()))
                                    .collect(Collectors.toList());
                                break;
                            case WavesStrategyConstants.PICKINGGROUPSTRATEGY_LOCATION:
                                LOG.info(String.format("按货位拣货，分区：%s", JSON.toJSONString(groupDTO)));
                                lstTmpItems = lstOrderItems.stream()
                                    .filter(
                                        p -> p.getLocationId() != null && lstIds.contains(p.getLocationId().toString()))
                                    .collect(Collectors.toList());
                                break;
                            // TODO 感觉这里有bug；lstTmpItems 上面都是重新赋值，这里没有。多个类目会越来越多
                            case WavesStrategyConstants.PICKINGGROUPSTRATEGY_CATEGORY:
                                LOG.info(String.format("按类目拣货，分区：%s", JSON.toJSONString(groupDTO)));
                                List<OutStockOrderItemPO> lstSecCategoryItems = lstOrderItems.stream()
                                    .filter(p -> p.getSecCategoryId() != null
                                        && lstIds.contains(p.getSecCategoryId().toString()))
                                    .collect(Collectors.toList());
                                if (!CollectionUtils.isEmpty(lstSecCategoryItems)) {
                                    lstTmpItems.addAll(lstSecCategoryItems);
                                }
                                List<Long> itemIds =
                                    lstTmpItems.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());
                                List<OutStockOrderItemPO> lstFirstCategoryItems = lstOrderItems.stream()
                                    .filter(p -> p.getFirstCategoryId() != null
                                        && lstIds.contains(p.getFirstCategoryId().toString())
                                        && !itemIds.contains(p.getId()))
                                    .collect(Collectors.toList());
                                if (!CollectionUtils.isEmpty(lstFirstCategoryItems)) {
                                    lstTmpItems.addAll(lstFirstCategoryItems);
                                }
                                break;
                            default:
                                break;
                        }
                    }
                    if (!CollectionUtils.isEmpty(lstTmpItems)) {
                        // 移除匹配到的订单项
                        lstOrderItems.removeAll(lstTmpItems);
                        CreateBatchTaskByOrderResultHelperBO createBatchTaskByOrderResultHelperBO =
                            processBatchTaskByOrderItem(group, batchPO, lstTmpItems, wavesStrategyDTO, createDTO);
                        createBatchTaskByOrderResultHelperBOList.add(createBatchTaskByOrderResultHelperBO);
                    }
                });
            } else {
                LOG.info(String.format("仓库%s没有找到分区！", batchPO.getWarehouseId()));
            }
        }
        PassageDTO passageDTO = createDTO.getPassageDTO();
        if (!lstOrderItems.isEmpty()) {
            // 剩余订单项与之前获取的无货区订单匹配
            List<Long> lstOrderItemIds = lstOrderItems.stream().map(OutStockOrderItemPO::getOutstockorderId).distinct()
                .collect(Collectors.toList());
            List<Long> lstNoLocationOrderIds =
                lstNoLocationOrder.stream().map(OutStockOrderPO::getId).collect(Collectors.toList());
            // 交集
            List<Long> intersection =
                lstOrderItemIds.stream().filter(id -> lstNoLocationOrderIds.contains(id)).collect(Collectors.toList());
            // 提取相同订单id的订单项
            List<OutStockOrderItemPO> intersectionOrders = lstOrderItems.stream()
                .filter(item -> intersection.contains(item.getOutstockorderId())).collect(Collectors.toList());
            // 无货区订单添加同订单id的订单项
            lstNoLocationOrder.forEach(order -> {
                if (intersection.contains(order.getId())) {
                    order.getItems().addAll(intersectionOrders.stream()
                        .filter(item -> item.getOutstockorderId().equals(order.getId())).collect(Collectors.toList()));
                }
            });

            lstOrderItems.removeAll(intersectionOrders);
            // 没有匹配到分区的，单独生一个拣货任务
            // if (lstOrderItems.size() > 0 && passageDTO != null && (passageDTO.getSowType() ==
            // SowTypeEnum.分区分单播种.getType() || passageDTO.getSowType() == SowTypeEnum.分区分单不播种.getType() ||
            // passageDTO.getSowType() == SowTypeEnum.分区分单拣货.getType())) {
            // processBatchTaskByOrderItem(null, batchPO, lstOrderItems, lstBatchTask, lstBatchTaskItem,
            // wavesStrategyDTO, passageName, null, null, createDTO, lstOrderItemTask);
            // } else
            if (!lstOrderItems.isEmpty()) {
                CreateBatchTaskByOrderResultHelperBO createBatchTaskByOrderResultHelperBO =
                    processBatchTaskByOrderItem(null, batchPO, lstOrderItems, wavesStrategyDTO, createDTO);

                createBatchTaskByOrderResultHelperBOList.add(createBatchTaskByOrderResultHelperBO);
            }

        }
        // 单独按订单生成拣货任务
        if (!CollectionUtils.isEmpty(lstNoLocationOrder)) {
            WavesStrategyBO wavesStrategyDTONew = new WavesStrategyBO();
            BeanUtils.copyProperties(wavesStrategyDTO, wavesStrategyDTONew);
            wavesStrategyDTONew.setPickingType(PickingTypeEnum.订单拣货.getType());
            // if(passageDTO != null && (passageDTO.getSowType() == SowTypeEnum.分区分单播种.getType() ||
            // passageDTO.getSowType() == SowTypeEnum.分区分单不播种.getType() || passageDTO.getSowType() ==
            // SowTypeEnum.分区分单拣货.getType())) {
            // processBatchTaskByOrder(batchPO, lstNoLocationOrder, lstBatchTask, lstBatchTaskItem, wavesStrategyDTONew,
            // passageName, null, null, lstOrderItemTask);
            // } else {
            CreateBatchTaskByOrderResultHelperBO createBatchTaskByOrderResultHelperBO =
                processBatchTaskByOrder(batchPO, lstNoLocationOrder, wavesStrategyDTONew, createDTO);
            createBatchTaskByOrderResultHelperBO.setLstNoLocationOrder(lstNoLocationOrder);
            createBatchTaskByOrderResultHelperBOList.add(createBatchTaskByOrderResultHelperBO);
            // }
        }
        lstOrderItems.clear();
        CreateBatchTaskByOrderResultHelperBO finalResult =
            CreateBatchTaskByOrderResultHelperBO.getBOByBOList(createBatchTaskByOrderResultHelperBOList);

        return finalResult;
    }

    /**
     * 组装订单项关联拣货任务项
     *
     * @return
     */
    private void addOrderItemTaskInfoPO(List<OrderItemTaskInfoPO> taskInfoPOList, OutStockOrderItemPO orderItemPO,
        BatchTaskItemDTO itemDTO, BatchTaskPO batchTaskPO) {
        // 去重
        if (taskInfoPOList.stream()
            .anyMatch(p -> Objects.equals(String.format("%s_%s", p.getRefOrderItemId(), p.getBatchTaskItemId()),
                String.format("%s_%s", orderItemPO.getId(), itemDTO.getId())))) {
            // 存在则更新数量
            OrderItemTaskInfoPO oldPO = taskInfoPOList.stream()
                .filter(p -> Objects.equals(String.format("%s_%s", p.getRefOrderItemId(), p.getBatchTaskItemId()),
                    String.format("%s_%s", orderItemPO.getId(), itemDTO.getId())))
                .findFirst().get();
            oldPO.setUnitTotalCount(oldPO.getUnitTotalCount().add(orderItemPO.getUnittotalcount()));
            oldPO.setOriginalUnitTotalCount(oldPO.getOriginalUnitTotalCount().add(orderItemPO.getUnittotalcount()));
            oldPO.setOriginalUnitTotalCount(oldPO.getUnitTotalCount());

            // 明细去重
            List<OrderItemTaskInfoDetailPO> oldDetailList = oldPO.getDetailList();
            if (oldDetailList == null) {
                oldDetailList = new ArrayList<>();
                oldPO.setDetailList(oldDetailList);
            }
            Optional<OrderItemTaskInfoDetailPO> detailOptional = oldDetailList.stream()
                .filter(p -> Objects.equals(p.getSecOwnerId(), orderItemPO.getSecOwnerId())).findFirst();
            if (detailOptional.isPresent()) {
                detailOptional.get()
                    .setUnitTotalCount(detailOptional.get().getUnitTotalCount().add(orderItemPO.getUnittotalcount()));
                detailOptional.get().setOriginalUnitTotalCount(
                    detailOptional.get().getOriginalUnitTotalCount().add(orderItemPO.getUnittotalcount()));
            } else {
                OrderItemTaskInfoDetailPO detail = new OrderItemTaskInfoDetailPO();
                detail.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.ORDER_ITEM_TASK_INFO_DETAIL));
                detail.setOrgId(oldPO.getOrgId());
                detail.setTaskInfoId(oldPO.getId());
                detail.setSecOwnerId(orderItemPO.getSecOwnerId());
                detail.setOwnerId(orderItemPO.getOwnerId());
                detail.setProductSpecificationId(orderItemPO.getProductSpecificationId());
                detail.setUnitTotalCount(orderItemPO.getUnittotalcount());
                detail.setOriginalUnitTotalCount(orderItemPO.getUnittotalcount());
                oldDetailList.add(detail);
            }
            return;
        }
        // 新增关联拣货任务项
        OrderItemTaskInfoPO orderItemTaskPO = new OrderItemTaskInfoPO();
        orderItemTaskPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.ORDER_ITEM_TASK_INFO));
        orderItemTaskPO.setOrgId(Integer.valueOf(batchTaskPO.getOrgId()));
        orderItemTaskPO.setRefOrderItemId(orderItemPO.getId());
        orderItemTaskPO.setRefOrderId(orderItemPO.getOutstockorderId());
        orderItemTaskPO.setBatchTaskItemId(itemDTO.getId());
        orderItemTaskPO.setBatchTaskId(batchTaskPO.getId());
        orderItemTaskPO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
        orderItemTaskPO.setBatchId(batchTaskPO.getBatchId());
        orderItemTaskPO.setBatchNo(batchTaskPO.getBatchNo());
        orderItemTaskPO.setUnitTotalCount(orderItemPO.getUnittotalcount());
        orderItemTaskPO.setOriginalUnitTotalCount(orderItemPO.getUnittotalcount());

        // 新增明细
        List<OrderItemTaskInfoDetailPO> detailPOList = new ArrayList<>();
        OrderItemTaskInfoDetailPO detailPO = new OrderItemTaskInfoDetailPO();
        detailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.ORDER_ITEM_TASK_INFO_DETAIL));
        detailPO.setOrgId(orderItemTaskPO.getOrgId());
        detailPO.setTaskInfoId(orderItemTaskPO.getId());
        detailPO.setSecOwnerId(orderItemPO.getSecOwnerId());
        detailPO.setOwnerId(orderItemPO.getOwnerId());
        detailPO.setProductSpecificationId(orderItemPO.getProductSpecificationId());
        detailPO.setUnitTotalCount(orderItemTaskPO.getUnitTotalCount());
        detailPO.setOriginalUnitTotalCount(orderItemTaskPO.getUnitTotalCount());
        detailPOList.add(detailPO);

        orderItemTaskPO.setDetailList(detailPOList);
        taskInfoPOList.add(orderItemTaskPO);
    }

    private void setSecondSortBatchTaskLocation(BatchTaskPO batchTaskPO, WavesStrategyDTO wavesStrategyDTO,
        List<OutStockOrderItemPO> orderItemPOList) {
        if (Objects.nonNull(batchTaskPO.getToLocationId())) {
            return;
        }
        if (BooleanUtils.isFalse(wavesStrategyDTO.getIsOpenSecondSort())) {
            return;
        }
        if (BooleanUtils.isFalse(wavesStrategyDTO.getIsMultiOutBound())) {
            return;
        }

        List<Long> itemIds = orderItemPOList.stream().map(OutStockOrderItemPO::getId).collect(Collectors.toList());
        List<OutStockOrderItemPO> locationOrderList = Lists.partition(itemIds, 300).stream()
            .map(outStockOrderItemMapper::listByIds).filter(itemList -> !CollectionUtils.isEmpty(itemList))
            .flatMap(m -> m.stream()).collect(Collectors.toList());
        // LOG.info("设置总单二次分拣出库位查询出的订单项信息为：{}", JSON.toJSONString(locationOrderList));
        locationOrderList =
            locationOrderList.stream().filter(m -> Objects.nonNull(m.getLocationId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationOrderList)) {
            return;
        }
        OutStockOrderItemPO outStockOrderItemPO = locationOrderList.get(0);

        batchTaskPO.setToLocationId(outStockOrderItemPO.getLocationId());
        batchTaskPO.setToLocationName(outStockOrderItemPO.getLocationName());
    }

}
