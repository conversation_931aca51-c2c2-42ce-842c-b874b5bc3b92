<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper">
    <!--auto generated Code-->
    <sql id="Base_Column_List">
        id,
        Org_id,
        BatchtaskNo,
        Batchtask_id,
        RefOrderNo,
        RefOrder_id,
        ProductName,
        SkuId,
        ProductBrand,
        CategoryName,
        SpecName,
        SpecQuantity,
        SaleSpec,
        SaleSpecQuantity,
        PackageName,
        PackageCount,
        UnitName,
        UnitCount,
        UnitTotalCount,
        TaskState,
        LackUnitCount,
        IsLack,
        Remark,
        OverSortCount,
        `CreateUser`,
        CreateTime,
        LastUpdateUser,
        LastUpdateTime,
        LocationId,
        LocationName,
        LocationCategory,
        Channel,
        `Source`,
        OrderSquence,
        SownUnitTotalCount,
        ProductSpecification_Id,
        Owner_Id,
        SecOwner_Id,
        ControlConfig_Id,
        StartTime,
        CompleteTime,
        ProductionDate,
        BatchTime,
        RollbackUnitCount,
        UnitPrice,
        SowTaskItemId,
        completeUser,
        completeUserId,
        LargePickPattern
    </sql>

    <!--auto generated Code-->
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="Org_id" property="orgId" jdbcType="INTEGER"/>
        <result column="BatchtaskNo" property="batchTaskNo" jdbcType="VARCHAR"/>
        <result column="Batchtask_id" property="batchTaskId" jdbcType="VARCHAR"/>
        <result column="RefOrderNo" property="refOrderNo" jdbcType="VARCHAR"/>
        <result column="RefOrder_id" property="refOrderId" jdbcType="VARCHAR"/>
        <result column="ProductName" property="productName" jdbcType="VARCHAR"/>
        <result column="SkuId" property="skuId" jdbcType="VARCHAR"/>
        <result column="ProductBrand" property="productBrand" jdbcType="VARCHAR"/>
        <result column="CategoryName" property="categoryName" jdbcType="VARCHAR"/>
        <result column="SpecName" property="specName" jdbcType="VARCHAR"/>
        <result column="SpecQuantity" property="specQuantity" jdbcType="DECIMAL"/>
        <result column="SaleSpec" property="saleSpec" jdbcType="VARCHAR"/>
        <result column="SaleSpecQuantity" property="saleSpecQuantity" jdbcType="DECIMAL"/>
        <result column="PackageName" property="packageName" jdbcType="VARCHAR"/>
        <result column="PackageCount" property="packageCount" jdbcType="DECIMAL"/>
        <result column="UnitName" property="unitName" jdbcType="VARCHAR"/>
        <result column="UnitCount" property="unitCount" jdbcType="DECIMAL"/>
        <result column="UnitTotalCount" property="unitTotalCount" jdbcType="DECIMAL"/>
        <result column="TaskState" property="taskState" jdbcType="TINYINT"/>
        <result column="LackUnitCount" property="lackUnitCount" jdbcType="DECIMAL"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="OverSortCount" property="overSortCount" jdbcType="DECIMAL"/>
        <result column="LocationId" property="locationId" jdbcType="BIGINT"/>
        <result column="LocationName" property="locationName" jdbcType="VARCHAR"/>
        <result column="LocationCategory" property="locationCategory" jdbcType="TINYINT"/>
        <result column="OrderSquence" property="orderSquence" jdbcType="INTEGER"/>
        <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
        <result column="SowTaskItemId" property="sowTaskItemId" jdbcType="BIGINT"/>
        <result column="CompleteUserId" property="completeUserId" jdbcType="INTEGER"/>
        <result column="CompleteUser" property="completeUser" jdbcType="VARCHAR"/>
        <result column="LargePickPattern" property="largePickPattern" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="BaseResultMap1" type="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="Org_id" property="orgId" jdbcType="INTEGER"/>
        <result column="BatchtaskNo" property="batchTaskNo" jdbcType="VARCHAR"/>
        <result column="Batchtask_id" property="batchTaskId" jdbcType="VARCHAR"/>
        <result column="RefOrderNo" property="refOrderNo" jdbcType="VARCHAR"/>
        <result column="RefOrder_id" property="refOrderId" jdbcType="VARCHAR"/>
        <result column="ProductName" property="productName" jdbcType="VARCHAR"/>
        <result column="SkuId" property="skuId" jdbcType="BIGINT"/>
        <result column="ProductBrand" property="productBrand" jdbcType="VARCHAR"/>
        <result column="CategoryName" property="categoryName" jdbcType="VARCHAR"/>
        <result column="SpecName" property="specName" jdbcType="VARCHAR"/>
        <result column="SpecQuantity" property="specQuantity" jdbcType="DECIMAL"/>
        <result column="SaleSpec" property="saleSpec" jdbcType="VARCHAR"/>
        <result column="SaleSpecQuantity" property="saleSpecQuantity" jdbcType="DECIMAL"/>
        <result column="PackageName" property="packageName" jdbcType="VARCHAR"/>
        <result column="PackageCount" property="packageCount" jdbcType="DECIMAL"/>
        <result column="UnitName" property="unitName" jdbcType="VARCHAR"/>
        <result column="UnitCount" property="unitCount" jdbcType="DECIMAL"/>
        <result column="UnitTotalCount" property="unitTotalCount" jdbcType="DECIMAL"/>
        <result column="TaskState" property="taskState" jdbcType="TINYINT"/>
        <result column="LackUnitCount" property="lackUnitCount" jdbcType="DECIMAL"/>
        <result column="IsLack" property="isLack" jdbcType="TINYINT"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="OverSortCount" property="overSortCount" jdbcType="DECIMAL"/>
        <result column="LocationId" property="locationId" jdbcType="BIGINT"/>
        <result column="LocationName" property="locationName" jdbcType="VARCHAR"/>
        <result column="LocationCategory" property="locationCategory" jdbcType="TINYINT"/>
        <result column="PickingType" property="pickingType" jdbcType="TINYINT"/>
        <result column="Channel" property="channel" jdbcType="TINYINT"/>
        <result column="Source" property="source" jdbcType="TINYINT"/>
        <result column="PickingType" property="pickingType" jdbcType="TINYINT"/>
        <result column="OrderSquence" property="orderSquence" jdbcType="INTEGER"/>
        <result column="UserName" property="userName" jdbcType="VARCHAR"/>
        <result column="ShopName" property="shopName" jdbcType="VARCHAR"/>
        <result column="MobileNo" property="mobileNo" jdbcType="VARCHAR"/>
        <result column="Province" property="province" jdbcType="VARCHAR"/>
        <result column="City" property="city" jdbcType="VARCHAR"/>
        <result column="County" property="county" jdbcType="VARCHAR"/>
        <result column="Street" property="street" jdbcType="VARCHAR"/>
        <result column="DetailAddress" property="detailAddress" jdbcType="VARCHAR"/>
        <result column="OrderSquence" property="orderSquence" jdbcType="INTEGER"/>
        <result column="OrderSequence" property="orderSequence" jdbcType="INTEGER"/>
        <result column="SowTask_Id" property="sowTaskId" jdbcType="BIGINT"/>
        <result column="SowTaskNo" property="sowTaskNo" jdbcType="VARCHAR"/>
        <result column="ToLocation_Id" property="toLocationId" jdbcType="BIGINT"/>
        <result column="ToLocationName" property="toLocationName" jdbcType="VARCHAR"/>
        <result column="OrderType" property="orderType" jdbcType="TINYINT"/>
        <result column="BatchNo" property="batchNo" jdbcType="VARCHAR"/>
        <result column="Batch_id" property="batchId" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="Sorter_Id" property="sorterId" jdbcType="INTEGER"/>
        <result column="SorterName" property="sorter" jdbcType="VARCHAR"/>
        <result column="AreaName" property="areaName" jdbcType="VARCHAR"/>
        <result column="RouteName" property="routeName" jdbcType="VARCHAR"/>
        <result column="OrderSelection" property="orderSelection" jdbcType="TINYINT"/>
        <result column="ControlConfig_Id" property="controlConfigId" jdbcType="BIGINT"/>
        <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
        <result column="Owner_Id" property="ownerId" jdbcType="BIGINT"/>
        <result column="SecOwner_Id" property="secOwnerId" jdbcType="BIGINT"/>
        <result column="ProductionDate" property="productionDate" jdbcType="TIMESTAMP"/>
        <result column="BatchTime" property="batchTime" jdbcType="TIMESTAMP"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="PackageAttribute" property="packageAttribute" jdbcType="TINYINT"/>
        <result column="RollbackUnitCount" property="rollbackUnitCount" jdbcType="DECIMAL"/>
        <result column="DeliveryMode" property="deliveryMode" jdbcType="TINYINT"/>
        <result column="UnitPrice" property="unitPrice" jdbcType="DECIMAL"/>
        <result column="BusinessType" property="businessType" jdbcType="TINYINT"/>
        <result column="Business_Id" property="businessId" jdbcType="VARCHAR"/>
        <result column="CreateAllocation" property="createAllocation" jdbcType="VARCHAR"/>
        <result column="AllotType" property="allotType" jdbcType="TINYINT"/>
        <result column="SownUnitTotalCount" property="sownUnitTotalCount" jdbcType="DECIMAL"/>
        <result column="sowTaskItemState" property="sowTaskItemState" jdbcType="TINYINT"/>
        <result column="CompleteUserId" property="completeUserId" jdbcType="INTEGER"/>
        <result column="CompleteUser" property="completeUser" jdbcType="VARCHAR"/>
        <result column="LargePickPattern" property="largePickPattern" jdbcType="TINYINT"/>
        <result column="OutBoundType" property="outBoundType" jdbcType="TINYINT"/>
        <result column="AddressId" property="addressId" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="AllResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="Org_id" jdbcType="INTEGER" property="orgId"/>
        <result column="BatchtaskNo" jdbcType="VARCHAR" property="batchTaskNo"/>
        <result column="Batchtask_id" jdbcType="VARCHAR" property="batchTaskId"/>
        <result column="RefOrderNo" jdbcType="VARCHAR" property="refOrderNo"/>
        <result column="RefOrder_id" jdbcType="VARCHAR" property="refOrderId"/>
        <result column="ProductName" jdbcType="VARCHAR" property="productName"/>
        <result column="SkuId" jdbcType="VARCHAR" property="skuId"/>
        <result column="ProductBrand" jdbcType="VARCHAR" property="productBrand"/>
        <result column="CategoryName" jdbcType="VARCHAR" property="categoryName"/>
        <result column="SpecName" jdbcType="VARCHAR" property="specName"/>
        <result column="SpecQuantity" jdbcType="DECIMAL" property="specQuantity"/>
        <result column="SaleSpec" jdbcType="VARCHAR" property="saleSpec"/>
        <result column="SaleSpecQuantity" jdbcType="DECIMAL" property="saleSpecQuantity"/>
        <result column="PackageName" jdbcType="VARCHAR" property="packageName"/>
        <result column="PackageCount" jdbcType="DECIMAL" property="packageCount"/>
        <result column="UnitName" jdbcType="VARCHAR" property="unitName"/>
        <result column="UnitCount" jdbcType="DECIMAL" property="unitCount"/>
        <result column="UnitTotalCount" jdbcType="DECIMAL" property="unitTotalCount"/>
        <result column="TaskState" jdbcType="TINYINT" property="taskState"/>
        <result column="LackUnitCount" jdbcType="DECIMAL" property="lackUnitCount"/>
        <result column="IsLack" jdbcType="BIT" property="isLack"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="OverSortCount" jdbcType="DECIMAL" property="overSortCount"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LocationId" jdbcType="VARCHAR" property="locationId"/>
        <result column="LocationName" jdbcType="VARCHAR" property="locationName"/>
        <result column="LocationCategory" jdbcType="TINYINT" property="locationCategory"/>
        <result column="Channel" jdbcType="TINYINT" property="channel"/>
        <result column="Source" jdbcType="TINYINT" property="source"/>
        <result column="OrderSquence" jdbcType="INTEGER" property="orderSquence"/>
        <result column="SownUnitTotalCount" jdbcType="DECIMAL" property="sownUnitTotalCount"/>
        <result column="ProductSpecification_Id" jdbcType="BIGINT" property="productSpecificationId"/>
        <result column="Owner_Id" jdbcType="BIGINT" property="ownerId"/>
        <result column="SecOwner_Id" jdbcType="BIGINT" property="secOwnerId"/>
        <result column="ControlConfig_Id" jdbcType="BIGINT" property="controlConfigId"/>
        <result column="StartTime" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="CompleteTime" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="ProductionDate" jdbcType="TIMESTAMP" property="productionDate"/>
        <result column="BatchTime" jdbcType="TIMESTAMP" property="batchTime"/>
        <result column="RollbackUnitCount" jdbcType="DECIMAL" property="rollbackUnitCount"/>
        <result column="UnitPrice" jdbcType="DECIMAL" property="unitPrice"/>
        <result column="SowTaskItemId" jdbcType="BIGINT" property="sowTaskItemId"/>
        <result column="completeUser" jdbcType="VARCHAR" property="completeUser"/>
        <result column="completeUserId" jdbcType="INTEGER" property="completeUserId"/>
        <result column="LargePickPattern" jdbcType="TINYINT" property="largePickPattern"/>
    </resultMap>

    <select id="findBatchTaskItemDTOListByBatchTaskNo" resultMap="BaseResultMap1">
        select
        bti.id,
        bti.Org_id,
        bti.BatchtaskNo,
        bti.Batchtask_id,
        bti.RefOrderNo,
        bti.RefOrder_id,
        bti.ProductName,
        bti.SkuId,
        bti.ProductBrand,
        bti.CategoryName,
        bti.SpecName,
        bti.SpecQuantity,
        bti.SaleSpec,
        bti.SaleSpecQuantity,
        bti.PackageName,
        bti.PackageCount,
        bti.UnitName,
        bti.UnitCount,
        bti.UnitTotalCount,
        bti.UnitPrice,
        bti.TaskState,
        bti.LackUnitCount,
        bti.Remark,
        bti.OverSortCount,
        bti.LocationId,
        bti.LocationName,
        bti.LocationCategory,
        bti.completeUserId,
        bti.completeUser,
        bt.PickingType,
        bti.OrderSquence,
        bti.largePickPattern,
        bt.SowTask_Id,
        bt.SowTaskNo,
        oo.UserName,
        oo.ShopName,
        oo.MobileNo,
        oo.DetailAddress,
        oo.Province,
        oo.City,
        oo.County,
        oo.Street,
        oo.addressId,
        oo.outBoundType,
        bti.ControlConfig_Id,
        bt.Warehouse_Id
        from batchtaskitem bti
        INNER JOIN batchtask bt on bt.id = bti.Batchtask_id
        LEFT JOIN outstockorder oo on oo.id = bti.RefOrder_id
        where bti.BatchtaskNo = #{batchTaskNo,jdbcType=VARCHAR}
        <if test="cityId != null">
            and bti.Org_id = #{cityId,jdbcType=INTEGER}
        </if>
        order by bti.OrderSquence asc, bti.RefOrderNo asc, bti.id
    </select>

    <select id="findBatchTaskItemDTOList" resultMap="BaseResultMap1">
        select
        bti.id,
        bti.Org_id,
        bt.Warehouse_Id,
        bti.BatchtaskNo,
        bti.Batchtask_id,
        bti.RefOrderNo,
        bti.RefOrder_id,
        bti.ProductName,
        bti.SkuId,
        bti.ProductBrand,
        bti.CategoryName,
        bti.SpecName,
        bti.SpecQuantity,
        bti.SaleSpec,
        bti.SaleSpecQuantity,
        bti.PackageName,
        bti.PackageCount,
        bti.UnitName,
        bti.UnitCount,
        bti.UnitTotalCount,
        bti.TaskState,
        bti.LackUnitCount,
        bti.IsLack,
        bti.Remark,
        bti.OverSortCount,
        bti.LocationId,
        bti.LocationName,
        bti.LocationCategory,
        bti.completeUserId,
        bti.completeUser,
        bt.PickingType,
        bti.OrderSquence,
        bti.LargePickPattern,
        bt.SowTask_Id,
        bt.SowTaskNo,
        bt.BatchNo,
        bt.CreateTime,
        bt.Sorter_Id,
        bt.SorterName,
        b.AreaName,
        b.RouteName,
        b.OrderSelection,
        bti.ControlConfig_Id,
        bti.ProductionDate,
        bti.BatchTime,
        bti.UnitPrice,
        bt.ToLocation_Id,
        bt.ToLocationName
        from batchtaskitem bti
        INNER JOIN batchtask bt on bt.id = bti.Batchtask_id
        INNER JOIN batch b on bt.batch_id =b.id
        where bti.Org_id = #{so.cityId,jdbcType=INTEGER}
        <if test="so.warehouseId != null">
            and bt.Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="so.batchTaskNo != null and so.batchTaskNo != ''">
            and bti.BatchtaskNo = #{so.batchTaskNo,jdbcType=VARCHAR}
        </if>
        <if test="so.batchNo != null and so.batchNo != ''">
            and bt.BatchNo = #{so.batchNo,jdbcType=VARCHAR}
        </if>
        <if test="so.refOrderNo != null and so.refOrderNo != ''">
            and bti.RefOrderNo = #{so.refOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="so.productName != null and so.productName != ''">
            and bti.ProductName like concat('%', #{so.productName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="so.sorterId != null">
            and bt.Sorter_Id = #{so.sorterId,jdbcType=INTEGER}
        </if>
        <if test="so.sorter != null and so.sorter != ''">
            and bt.SorterName like concat('%', #{so.sorter,jdbcType=VARCHAR}, '%')
        </if>
        <if test="so.taskState != null">
            and bti.TaskState = #{so.taskState,jdbcType=TINYINT}
        </if>
        <if test="so.taskStateList != null">
            AND bti.TaskState in
            <foreach collection="so.taskStateList" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="so.startTime != null">
            AND bt.CreateTime <![CDATA[ >= ]]> #{so.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="so.endTime != null">
            AND bt.CreateTime <![CDATA[ <= ]]> #{so.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="so.batchTaskItemIdList != null and so.batchTaskItemIdList.size()>0">
            AND bti.id in
            <foreach collection="so.batchTaskItemIdList" item="itemId" separator="," open="(" close=")">
                #{itemId,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by bt.CreateTime desc, bt.BatchTaskNo asc, bti.RefOrderNo asc, bti.ProductName, bti.id
    </select>

    <select id="listNotSplitOrderByBatchTaskNo"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSplitOrderDTO">
        select RefOrderNo refOrderNo,
        sum(PackageCount) packageCount,
        sum(UnitCount) unitCount,
        sum(unitTotalCount) unitTotalCount,
        count(skuId) skuCount
        from batchtaskitem
        where BatchtaskNo = #{batchTaskNo,jdbcType=VARCHAR}
        group by RefOrderNo
    </select>

    <select id="listBatchTaskItemId" resultType="java.lang.String">
        select id from batchtaskitem
        <where>
            <if test="batchTaskNo!=null">
                BatchtaskNo= #{batchTaskNo,jdbcType=VARCHAR}
            </if>
            <if test="refOrderNo!=null">
                and RefOrderNo = #{refOrderNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="findBatchTaskItemDTOListByBatchTaskId" resultMap="BaseResultMap1">
        select bti.id,
        bti.Org_id,
        bti.BatchtaskNo,
        bti.Batchtask_id,
        bti.RefOrderNo,
        bti.RefOrder_id,
        bti.ProductName,
        bti.SkuId,
        bti.ProductBrand,
        bti.CategoryName,
        bti.SpecName,
        bti.SpecQuantity,
        bti.SaleSpec,
        bti.SaleSpecQuantity,
        bti.PackageName,
        bti.PackageCount,
        bti.UnitName,
        bti.UnitCount,
        bti.UnitTotalCount,
        bti.TaskState,
        bti.LackUnitCount,
        bti.Remark,
        bti.OverSortCount,
        bti.LocationId,
        bti.LocationName,
        bti.LocationCategory,
        bt.PickingType,
        bti.OrderSquence,
        bti.completeUserId,
        bti.completeUser,
        bti.LargePickPattern,
        bt.SowTask_Id,
        bt.SowTaskNo
        from batchtaskitem bti
        INNER JOIN batchtask bt on bt.id = bti.Batchtask_id
        where bti.Batchtask_id = #{batchTaskId,jdbcType=VARCHAR}
    </select>
    <select id="findBatchTaskItemDTOListByOrderId" resultMap="BaseResultMap1">
        select
        bi.id,
        bi.Org_id,
        bi.BatchtaskNo,
        bi.Batchtask_id,
        bi.RefOrder_id,
        bi.RefOrderNo,
        bi.ProductName,
        bi.SkuId,
        bi.ProductBrand,
        bi.CategoryName,
        bi.SpecName,
        bi.SpecQuantity,
        bi.SaleSpec,
        bi.SaleSpecQuantity,
        bi.PackageName,
        bi.PackageCount,
        bi.UnitName,
        bi.UnitCount,
        bi.UnitTotalCount,
        bi.TaskState,
        bi.LackUnitCount,
        bi.Remark,
        bi.OverSortCount,
        bi.LocationId,
        bi.LocationName,
        bi.LocationCategory,
        oo.RouteSequence,
        bt.PickingType,
        bi.OrderSquence,
        bi.LargePickPattern,
        oo.UserName,
        oo.ShopName,
        oo.MobileNo,
        oo.Province,
        oo.City,
        oo.County,
        oo.Street,
        oo.DetailAddress,
        oo.OrderSequence,
        oo.OrderType,
        oo.PackageAttribute,
        oo.DeliveryMode,
        oo.BusinessType,
        oo.Business_Id,
        oo.CreateAllocation,
        oo.AllotType,
        oo.AreaName,
        oo.RouteName,
        oo.OutBoundType,
        oo.AddressId,
        bt.SowTask_Id,
        bt.SowTaskNo,
        bi.Source,
        bi.Channel,
        bi.ProductSpecification_Id,
        bi.Owner_Id,
        bi.SecOwner_Id,
        bi.ControlConfig_Id,
        bi.ProductionDate,
        bi.BatchTime,
        bt.Warehouse_Id,
        bi.UnitPrice,
        bi.completeUserId,
        bi.completeUser
        from batchtaskitem bi
        INNER JOIN batchtask bt on bt.id = bi.Batchtask_id
        left JOIN outstockorder oo on oo.id = bi.RefOrder_id
        where bi.Batchtask_id = #{batchTaskId,jdbcType=VARCHAR}
        AND bi.RefOrder_id in
        <foreach collection="orderList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="finBatchOrderIdListById" resultType="string">
        SELECT RefOrder_id
        FROM batchtaskitem
        WHERE Batchtask_id = #{batchTaskId,jdbcType=VARCHAR}
        group by RefOrder_id
    </select>
    <select id="findPageBatchTaskItemDTOListById" resultMap="BaseResultMap1">
        select bi.id,
        bi.Org_id,
        bi.BatchtaskNo,
        bi.Batchtask_id,
        bi.RefOrder_id,
        bi.RefOrderNo,
        bi.ProductName,
        bi.SkuId,
        bi.ProductBrand,
        bi.CategoryName,
        bi.SpecName,
        bi.SpecQuantity,
        bi.SaleSpec,
        bi.SaleSpecQuantity,
        bi.PackageName,
        bi.PackageCount,
        bi.UnitName,
        bi.UnitCount,
        bi.UnitTotalCount,
        bi.TaskState,
        bi.LackUnitCount,
        bi.Remark,
        bi.OverSortCount,
        bi.LocationId,
        bi.LocationName,
        bi.LocationCategory,
        bt.PickingType,
        bi.OrderSquence,
        bt.SowTask_Id,
        bt.SowTaskNo,
        bi.Source,
        bi.Channel,
        bi.ProductSpecification_Id,
        bi.Owner_Id,
        bi.SecOwner_Id,
        bi.ControlConfig_Id,
        bi.ProductionDate,
        bi.BatchTime,
        bt.Warehouse_Id,
        bi.UnitPrice,
        bi.completeUserId,
        bi.completeUser,
        bi.LargePickPattern,
        bi.sownUnitTotalCount
        from batchtaskitem bi
        INNER JOIN batchtask bt on bt.id = bi.Batchtask_id
        WHERE bi.Batchtask_id = #{batchTaskId,jdbcType=VARCHAR}
    </select>
    <select id="findBatchTaskItemDTOListNoPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtaskitem
        WHERE Batchtask_id = #{batchTaskId,jdbcType=VARCHAR}
    </select>

    <select id="findBatchTaskItemDTOListByTaskIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtaskitem
        WHERE Batchtask_id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findBatchTaskItemDtoListById" resultMap="BaseResultMap1">
        select bi.id,
        bi.Org_id,
        bi.BatchtaskNo,
        bi.Batchtask_id,
        bi.RefOrder_id,
        bi.RefOrderNo,
        bi.ProductName,
        bi.SkuId,
        bi.ProductBrand,
        bi.CategoryName,
        bi.SpecName,
        bi.SpecQuantity,
        bi.SaleSpec,
        bi.SaleSpecQuantity,
        bi.PackageName,
        bi.PackageCount,
        bi.UnitName,
        bi.UnitCount,
        bi.UnitTotalCount,
        bi.TaskState,
        bi.LackUnitCount,
        bi.Remark,
        bi.OverSortCount,
        bi.LocationId,
        bi.LocationName,
        bi.LocationCategory,
        bi.Channel,
        bi.Source,
        bi.OrderSquence,
        bi.RollbackUnitCount,
        bi.completeUserId,
        bi.completeUser,
        bi.productSpecification_Id,
        b.PickingType,
        b.SowTask_Id,
        bi.LargePickPattern,
        b.sowTaskNo
        from batchtaskitem bi
        INNER JOIN batchtask b on b.id = bi.Batchtask_id
        WHERE bi.Batchtask_id = #{batchTaskId,jdbcType=VARCHAR}
    </select>

    <select id="findBatchTaskItemDtoListByNo" resultMap="BaseResultMap1">
        select
        bi.id,
        bi.Org_id,
        bi.BatchtaskNo,
        bi.Batchtask_id,
        bi.RefOrder_id,
        bi.RefOrderNo,
        bi.ProductName,
        bi.SkuId,
        bi.ProductBrand,
        bi.CategoryName,
        bi.SpecName,
        bi.SpecQuantity,
        bi.SaleSpec,
        bi.SaleSpecQuantity,
        bi.PackageName,
        bi.PackageCount,
        bi.UnitName,
        bi.UnitCount,
        bi.UnitTotalCount,
        bi.TaskState,
        bi.LackUnitCount,
        bi.Remark,
        bi.OverSortCount,
        bi.LocationId,
        bi.LocationName,
        bi.LocationCategory,
        bi.Channel,
        bi.Source,
        bi.OrderSquence,
        bi.completeUserId,
        bi.completeUser,
        bi.LargePickPattern,
        b.PickingType,
        b.SowTask_Id,
        b.sowTaskNo,
        b.Sorter_id,
        b.SorterName,
        b.Warehouse_Id
        from batchtaskitem bi
        INNER JOIN batchtask b on b.id = bi.Batchtask_id
        WHERE bi.BatchtaskNo IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="findBatchTaskItemDtoListBySowTaskNo" resultMap="BaseResultMap1">
        select bi.id,
        bi.Org_id,
        bi.BatchtaskNo,
        bi.Batchtask_id,
        bi.RefOrder_id,
        bi.RefOrderNo,
        bi.ProductName,
        bi.SkuId,
        bi.ProductBrand,
        bi.CategoryName,
        bi.SpecName,
        bi.SpecQuantity,
        bi.SaleSpec,
        bi.SaleSpecQuantity,
        bi.PackageName,
        bi.PackageCount,
        bi.UnitName,
        bi.UnitCount,
        bi.UnitTotalCount,
        bi.TaskState,
        bi.LackUnitCount,
        bi.Remark,
        bi.OverSortCount,
        bi.LocationId,
        bi.LocationName,
        bi.LocationCategory,
        bi.Channel,
        bi.Source,
        bi.OrderSquence,
        bi.completeUserId,
        bi.completeUser,
        bi.LargePickPattern,
        b.PickingType,
        b.SowTask_Id,
        b.sowTaskNo,
        b.ToLocation_Id,
        b.ToLocationName
        from batchtaskitem bi
        INNER JOIN batchtask b on b.id = bi.Batchtask_id
        WHERE b.SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR}
    </select>

    <select id="selectBatchTaskItemById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtaskitem
        WHERE Id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="selectBatchTaskItemByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtaskitem
        WHERE id in
        <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>


    <update id="updateBatchTaskItem" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO">
        UPDATE batchtaskitem
        <set>
            <if test="taskState != null">
                TaskState = #{taskState,jdbcType=TINYINT},
            </if>
            <if test="overSortCount != null">
                OverSortCount = #{overSortCount,jdbcType=DECIMAL},
            </if>
            <if test="unitTotalCount != null">
                UnitTotalCount = #{unitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="packageCount != null">
                PackageCount = #{packageCount,jdbcType=DECIMAL},
            </if>
            <if test="unitCount != null">
                UnitCount = #{unitCount,jdbcType=DECIMAL},
            </if>
            <if test="lackUnitCount != null">
                LackUnitCount = #{lackUnitCount,jdbcType=DECIMAL},
            </if>
            <if test="isLack != null">
                IsLack = #{isLack,jdbcType=BIT},
            </if>
            <if test="locationId != null">
                LocationId = #{locationId,jdbcType=VARCHAR},
            </if>
            <if test="locationName != null">
                LocationName = #{locationName,jdbcType=VARCHAR},
            </if>
            <if test="skuId != null">
                SkuId = #{skuId,jdbcType=BIGINT},
            </if>
            <if test="completeUser != null">
                CompleteUser = #{completeUser,jdbcType=VARCHAR},
            </if>
            <if test="completeUserId != null">
                CompleteUserId = #{completeUserId,jdbcType=INTEGER},
            </if>
            <if test="largePickPattern != null">
                LargePickPattern = #{largePickPattern,jdbcType=TINYINT},
            </if>
        </set>
        WHERE Id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateBatchTaskItemSorter"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO">
        UPDATE batchtaskitem
        set CompleteUser = #{completeUser,jdbcType=VARCHAR},
        CompleteUserId = #{completeUserId,jdbcType=INTEGER},
        lastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
        largePickPattern = #{largePickPattern,jdbcType=TINYINT}
        WHERE Id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateBatchTaskItemSorterBatch"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO">
        UPDATE batchtaskitem
        SET
        CompleteUser=
        CASE id
        <foreach collection="poList" index="index" item="item">
            WHEN #{item.id}
            THEN #{item.completeUser,jdbcType=VARCHAR}
        </foreach>
        END,
        CompleteUserId=
        CASE id
        <foreach collection="poList" index="index" item="item">
            WHEN #{item.id}
            THEN #{item.completeUserId,jdbcType=INTEGER}
        </foreach>
        END
        WHERE id IN
        <foreach collection="poList" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>


    <update id="updateBatchTaskByItem">
        UPDATE batchtaskitem SET
        TaskState=
        CASE id
        <foreach collection="poList" index="index" item="item">
            WHEN #{item.id}
            THEN #{item.taskState}
        </foreach>
        END,
        OverSortCount=
        CASE id
        <foreach collection="poList" index="index" item="item">
            when #{item.id}
            then #{item.overSortCount}
        </foreach>
        END,
        LackUnitCount=
        CASE id
        <foreach collection="poList" index="index" item="item">
            when #{item.id}
            then #{item.lackCount}
        </foreach>
        END,
        LastUpdateUser=
        CASE id
        <foreach collection="poList" index="index" item="item">
            when #{item.id}
            then #{item.lastUpdateUser}
        </foreach>
        END,
        IsLack=
        CASE id
        <foreach collection="poList" index="index" item="item">
            when #{item.id}
            then #{item.isLack}
        </foreach>
        END,
        UnitCount=
        CASE id
        <foreach collection="poList" index="index" item="item">
            WHEN #{item.id}
            THEN #{item.unitCount}
        </foreach>
        END,
        PackageCount=
        CASE id
        <foreach collection="poList" index="index" item="item">
            WHEN #{item.id}
            THEN #{item.packageCount}
        </foreach>
        END,
        UnitTotalCount=
        CASE id
        <foreach collection="poList" index="index" item="item">
            WHEN #{item.id}
            THEN #{item.unitTotalCount}
        </foreach>
        END
        WHERE id IN
        <foreach collection="poList" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateBatchTaskByItemIncludeLocation">
        UPDATE batchtaskitem
        <trim prefix="set" suffixOverrides=",">
            TaskState=
            CASE id
            <foreach collection="poList" index="index" item="item">
                WHEN #{item.id}
                THEN #{item.taskState}
            </foreach>
            END,
            OverSortCount=
            CASE id
            <foreach collection="poList" index="index" item="item">
                when #{item.id}
                then #{item.overSortCount}
            </foreach>
            END,
            LackUnitCount=
            CASE id
            <foreach collection="poList" index="index" item="item">
                when #{item.id}
                then #{item.lackCount}
            </foreach>
            END,
            LastUpdateUser=
            CASE id
            <foreach collection="poList" index="index" item="item">
                when #{item.id}
                then #{item.lastUpdateUser}
            </foreach>
            END,
            IsLack=
            CASE id
            <foreach collection="poList" index="index" item="item">
                when #{item.id}
                then #{item.isLack}
            </foreach>
            END,
            Remark=
            CASE id
            <foreach collection="poList" index="index" item="item">
                when #{item.id}
                then #{item.remark}
            </foreach>
            END,
            LocationId=
            CASE id
            <foreach collection="poList" index="index" item="item">
                when #{item.id}
                then #{item.fromLocationId}
            </foreach>
            END,
            LocationName=
            CASE id
            <foreach collection="poList" index="index" item="item">
                when #{item.id}
                then #{item.fromLocationName}
            </foreach>
            END,
            CompleteUserId=
            CASE id
            <foreach collection="poList" index="index" item="item">
                when #{item.id}
                then #{item.completeUserId}
            </foreach>
            END,
            completeUser=
            CASE id
            <foreach collection="poList" index="index" item="item">
                when #{item.id}
                then #{item.completeUser}
            </foreach>
            END,
            <trim prefix="StartTime = case" suffix="end,">
                <foreach collection="poList" item="item" index="index">
                    <if test="item.startTime != null">
                        when id=#{item.id} then #{item.startTime}
                    </if>
                    <if test="item.startTime == null ">
                        when id=#{item.id} then batchtaskitem.StartTime
                    </if>
                </foreach>
            </trim>
            <trim prefix="CompleteTime = case" suffix="end,">
                <foreach collection="poList" item="item" index="index">
                    <if test="item.completeTime != null">
                        when id=#{item.id} then #{item.completeTime}
                    </if>
                    <if test="item.completeTime == null ">
                        when id=#{item.id} then batchtaskitem.CompleteTime
                    </if>
                </foreach>
            </trim>
            <trim prefix="SownUnitTotalCount = case" suffix="end,">
                <foreach collection="poList" item="item" index="index">
                    <if test="item.sownUnitTotalCount != null">
                        when id=#{item.id} then #{item.sownUnitTotalCount}
                    </if>
                    <if test="item.sownUnitTotalCount == null ">
                        when id=#{item.id} then batchtaskitem.SownUnitTotalCount
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="poList" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
    </update>

    <update id="updateBatchTaskItemList">
        <if test="map!=null and map.size>0">
            UPDATE batchtaskitem
            SET TaskState=
            CASE id
            <foreach collection="map.keys" item="key">
                WHEN #{key}
                THEN #{map[${key}]}
            </foreach>
            END
            WHERE id IN
            <foreach collection="map.keys" index="index" item="key" open="(" separator="," close=")">
                #{key}
            </foreach>
        </if>
    </update>
    <!--auto generated Code-->
    <insert id="insertList">
        INSERT INTO batchtaskitem (
        id,
        Org_id,
        BatchtaskNo,
        Batchtask_id,
        RefOrderNo,
        RefOrder_id,
        ProductName,
        SkuId,
        ProductBrand,
        CategoryName,
        SpecName,
        SpecQuantity,
        SaleSpec,
        SaleSpecQuantity,
        PackageName,
        PackageCount,
        UnitName,
        UnitCount,
        UnitTotalCount,
        TaskState,
        LackUnitCount,
        Remark,
        OverSortCount,
        LocationId,
        LocationName,
        LocationCategory,
        Channel,
        Source,
        OrderSquence,
        ProductSpecification_Id,
        Owner_Id,
        SecOwner_Id,
        ControlConfig_Id,
        ProductionDate,
        BatchTime,
        UnitPrice,
        LargePickPattern,
        completeUserId,
        completeUser
        )VALUES
        <foreach collection="batchTaskItemPOs" item="batchTaskItemPO" index="index" separator=",">
            (
            #{batchTaskItemPO.id,jdbcType=VARCHAR},
            #{batchTaskItemPO.orgId,jdbcType=INTEGER},
            #{batchTaskItemPO.batchTaskNo,jdbcType=VARCHAR},
            #{batchTaskItemPO.batchTaskId,jdbcType=VARCHAR},
            #{batchTaskItemPO.refOrderNo,jdbcType=VARCHAR},
            #{batchTaskItemPO.refOrderId,jdbcType=VARCHAR},
            #{batchTaskItemPO.productName,jdbcType=VARCHAR},
            #{batchTaskItemPO.skuId,jdbcType=BIGINT},
            #{batchTaskItemPO.productBrand,jdbcType=VARCHAR},
            #{batchTaskItemPO.categoryName,jdbcType=VARCHAR},
            #{batchTaskItemPO.specName,jdbcType=VARCHAR},
            #{batchTaskItemPO.specQuantity,jdbcType=DECIMAL},
            #{batchTaskItemPO.saleSpec,jdbcType=VARCHAR},
            #{batchTaskItemPO.saleSpecQuantity,jdbcType=DECIMAL},
            #{batchTaskItemPO.packageName,jdbcType=VARCHAR},
            #{batchTaskItemPO.packageCount,jdbcType=DECIMAL},
            #{batchTaskItemPO.unitName,jdbcType=VARCHAR},
            #{batchTaskItemPO.unitCount,jdbcType=DECIMAL},
            #{batchTaskItemPO.unitTotalCount,jdbcType=DECIMAL},
            #{batchTaskItemPO.taskState,jdbcType=TINYINT},
            #{batchTaskItemPO.lackUnitCount,jdbcType=DECIMAL},
            #{batchTaskItemPO.remark,jdbcType=VARCHAR},
            #{batchTaskItemPO.overSortCount,jdbcType=DECIMAL},
            #{batchTaskItemPO.locationId,jdbcType=BIGINT},
            #{batchTaskItemPO.locationName,jdbcType=VARCHAR},
            #{batchTaskItemPO.locationCategory,jdbcType=TINYINT},
            #{batchTaskItemPO.channel,jdbcType=TINYINT},
            #{batchTaskItemPO.source,jdbcType=TINYINT},
            #{batchTaskItemPO.orderSquence,jdbcType=INTEGER},
            #{batchTaskItemPO.productSpecificationId,jdbcType=BIGINT},
            #{batchTaskItemPO.ownerId,jdbcType=BIGINT},
            #{batchTaskItemPO.secOwnerId,jdbcType=BIGINT},
            #{batchTaskItemPO.controlConfigId,jdbcType=BIGINT},
            #{batchTaskItemPO.productionDate,jdbcType=TIMESTAMP},
            #{batchTaskItemPO.batchTime,jdbcType=TIMESTAMP},
            #{batchTaskItemPO.unitPrice,jdbcType=DECIMAL},
            #{batchTaskItemPO.largePickPattern,jdbcType=TINYINT},
            #{batchTaskItemPO.completeUserId,jdbcType=INTEGER},
            #{batchTaskItemPO.completeUser,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective">
        UPDATE batchtaskitem
        <set>
            <if test="locationId!= null">
                LocationId = #{locationId,jdbcType=BIGINT},
            </if>
            <if test="locationName!= null">
                LocationName = #{locationName,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE Id = #{id,jdbcType=BIGINT}
    </update>

    <!--auto generated Code-->
    <update id="update">
        UPDATE batchtaskitem
        <set>
            <if test="batchTaskItemPO.id != null">id= #{batchTaskItemPO.id,jdbcType=VARCHAR},</if>
            <if test="batchTaskItemPO.orgId != null">Org_id= #{batchTaskItemPO.orgId,jdbcType=INTEGER},</if>
            <if test="batchTaskItemPO.batchTaskNo != null">BatchtaskNo=
                #{batchTaskItemPO.batchTaskNo,jdbcType=VARCHAR},
            </if>
            <if test="batchTaskItemPO.batchTaskId != null">Batchtask_id=
                #{batchTaskItemPO.batchTaskId,jdbcType=VARCHAR},
            </if>
            <if test="batchTaskItemPO.refOrderNo != null">RefOrderNo= #{batchTaskItemPO.refOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="batchTaskItemPO.refOrderId != null">RefOrder_id= #{batchTaskItemPO.refOrderId,jdbcType=VARCHAR},
            </if>
            <if test="batchTaskItemPO.productName != null">ProductName=
                #{batchTaskItemPO.productName,jdbcType=VARCHAR},
            </if>
            <if test="batchTaskItemPO.skuId != null">SkuId= #{batchTaskItemPO.skuId,jdbcType=VARCHAR},</if>
            <if test="batchTaskItemPO.productBrand != null">ProductBrand=
                #{batchTaskItemPO.productBrand,jdbcType=VARCHAR},
            </if>
            <if test="batchTaskItemPO.categoryName != null">CategoryName=
                #{batchTaskItemPO.categoryName,jdbcType=VARCHAR},
            </if>
            <if test="batchTaskItemPO.specName != null">SpecName= #{batchTaskItemPO.specName,jdbcType=VARCHAR},</if>
            <if test="batchTaskItemPO.specQuantity != null">SpecQuantity=
                #{batchTaskItemPO.specQuantity,jdbcType=DECIMAL},
            </if>
            <if test="batchTaskItemPO.saleSpec != null">SaleSpec= #{batchTaskItemPO.saleSpec,jdbcType=VARCHAR},</if>
            <if test="batchTaskItemPO.saleSpecQuantity != null">SaleSpecQuantity=
                #{batchTaskItemPO.saleSpecQuantity,jdbcType=DECIMAL},
            </if>
            <if test="batchTaskItemPO.packageName != null">PackageName=
                #{batchTaskItemPO.packageName,jdbcType=VARCHAR},
            </if>
            <if test="batchTaskItemPO.packageCount != null">PackageCount=
                #{batchTaskItemPO.packageCount,jdbcType=DECIMAL},
            </if>
            <if test="batchTaskItemPO.unitName != null">UnitName= #{batchTaskItemPO.unitName,jdbcType=VARCHAR},</if>
            <if test="batchTaskItemPO.unitCount != null">UnitCount= #{batchTaskItemPO.unitCount,jdbcType=DECIMAL},</if>
            <if test="batchTaskItemPO.unitTotalCount != null">UnitTotalCount=
                #{batchTaskItemPO.unitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="batchTaskItemPO.taskState != null">TaskState= #{batchTaskItemPO.taskState,jdbcType=TINYINT},</if>
            <if test="batchTaskItemPO.lackUnitCount != null">LackUnitCount=
                #{batchTaskItemPO.lackUnitCount,jdbcType=DECIMAL},
            </if>
            <if test="batchTaskItemPO.remark != null">Remark= #{batchTaskItemPO.remark,jdbcType=VARCHAR},</if>
            <if test="batchTaskItemPO.overSortCount != null">OverSortCount=
                #{batchTaskItemPO.overSortCount,jdbcType=DECIMAL},
            </if>
            <if test="batchTaskItemPO.locationId != null">LocationId= #{batchTaskItemPO.locationId,jdbcType=BIGINT},
            </if>
            <if test="batchTaskItemPO.locationName != null">LocationCategory=
                #{batchTaskItemPO.locationName,jdbcType=VARCHAR}
            </if>
            <if test="batchTaskItemPO.orderSquence != null">OrderSquence=
                #{batchTaskItemPO.orderSquence,jdbcType=INTEGER}
            </if>
            <if test="batchTaskItemPO.completeUserId != null">completeUserId=
                #{batchTaskItemPO.completeUserId,jdbcType=INTEGER}
            </if>
            <if test="batchTaskItemPO.completeUser != null">completeUser=
                #{batchTaskItemPO.completeUser,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE id = #{batchTaskItemPO.id,jdbcType=VARCHAR}
    </update>
    <update id="updateTaskState2Complete">
        update batchtaskitem set TaskState = 2,LastUpdateUser=#{operateUser,jdbcType=VARCHAR}
        where BatchtaskNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>
    <delete id="deleteByBatchTaskId">
        DELETE from batchtaskitem where Batchtask_id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteByBatchTaskNo">
        DELETE from batchtaskitem where BatchtaskNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="findAreaListByTaskNo" resultType="java.lang.String">
        select CONCAT_WS("",case tt.OrderSelection when 1 then 1 when 2 then 2 else 3 end
        ,','
        ,case tt.OrderSelection when 1 then tt.area_id when 2 then tt.route_id else null end)
        as area_id
        from batch tt
        INNER JOIN batchtask task on tt.id = task.batch_id
        WHERE tt.OrderSelection > 0
        and task.BatchTaskNo IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <update id="updateList">
        <if test="batchTaskItemPOs!=null and batchTaskItemPOs.size()>0">
            UPDATE batchtaskitem
            SET BatchtaskNo=
            CASE id
            <foreach collection="batchTaskItemPOs" item="batchTaskItemPO">
                WHEN #{batchTaskItemPO.id,jdbcType=VARCHAR}
                THEN #{batchTaskItemPO.batchTaskNo,jdbcType=VARCHAR}
            </foreach>
            END
            , Batchtask_id=
            CASE id
            <foreach collection="batchTaskItemPOs" item="batchTaskItemPO">
                WHEN #{batchTaskItemPO.id,jdbcType=VARCHAR}
                THEN #{batchTaskItemPO.batchTaskId,jdbcType=VARCHAR}
            </foreach>
            END
            WHERE id IN
            <foreach collection="batchTaskItemPOs" item="batchTaskItemPO" index="index" open="(" separator=","
                     close=")">
                #{batchTaskItemPO.id,jdbcType=VARCHAR}
            </foreach>
        </if>
    </update>

    <update id="updateListNew">
        <if test="batchTaskItemPOs!=null and batchTaskItemPOs.size()>0">
            UPDATE batchtaskitem
            SET BatchtaskNo=
            CASE id
            <foreach collection="batchTaskItemPOs" item="batchTaskItemPO">
                WHEN #{batchTaskItemPO.id,jdbcType=VARCHAR}
                THEN #{batchTaskItemPO.batchTaskNo,jdbcType=VARCHAR}
            </foreach>
            END
            , Batchtask_id=
            CASE id
            <foreach collection="batchTaskItemPOs" item="batchTaskItemPO">
                WHEN #{batchTaskItemPO.id,jdbcType=VARCHAR}
                THEN #{batchTaskItemPO.batchTaskId,jdbcType=VARCHAR}
            </foreach>
            END
            , TaskState=
            CASE id
            <foreach collection="batchTaskItemPOs" item="batchTaskItemPO">
                WHEN #{batchTaskItemPO.id,jdbcType=VARCHAR}
                THEN #{batchTaskItemPO.taskState,jdbcType=TINYINT}
            </foreach>
            END
            WHERE id IN
            <foreach collection="batchTaskItemPOs" item="batchTaskItemPO" index="index" open="(" separator=","
                     close=")">
                #{batchTaskItemPO.id,jdbcType=VARCHAR}
            </foreach>
        </if>
    </update>

    <select id="listBatchTaskItemByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtaskitem
        WHERE id IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="batchTaskNo != null">
            and BatchtaskNo = #{batchTaskNo,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="findBatchTaskItemByOrderNos" resultMap="BaseResultMap">
        select
        bti.id,
        bti.Org_id,
        bti.BatchtaskNo,
        bti.Batchtask_id,
        bti.RefOrder_id,
        bti.RefOrderNo,
        bti.ProductName,
        bti.SkuId,
        bti.ProductBrand,
        bti.CategoryName,
        bti.SpecName,
        bti.SpecQuantity,
        bti.SaleSpec,
        bti.SaleSpecQuantity,
        bti.PackageName,
        bti.PackageCount,
        bti.UnitName,
        bti.UnitCount,
        bti.UnitTotalCount,
        bti.TaskState,
        bti.LackUnitCount,
        bti.Remark,
        bti.OverSortCount,
        bti.LocationId,
        bti.LocationName,
        bti.LocationCategory,
        bti.Channel,
        bti.Source,
        bti.OrderSquence,
        bti.ProductSpecification_Id,
        bti.completeUserId,
        bti.completeUser
        from batchtaskitem bti
        inner join batchtask bt on bt.id = bti.Batchtask_id
        WHERE bti.Org_id = #{orgId,jdbcType=INTEGER}
        and bt.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and bti.RefOrderNo in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="findUndoneItemsByOrderNos" resultMap="BaseResultMap1">
        select
        bti.id,
        bti.Org_id,
        bti.BatchtaskNo,
        bti.Batchtask_id,
        bti.RefOrder_id,
        bti.RefOrderNo,
        bti.ProductName,
        bti.SkuId,
        bti.ProductBrand,
        bti.CategoryName,
        bti.SpecName,
        bti.SpecQuantity,
        bti.SaleSpec,
        bti.SaleSpecQuantity,
        bti.PackageName,
        bti.PackageCount,
        bti.UnitName,
        bti.UnitCount,
        bti.UnitTotalCount,
        bti.TaskState,
        bti.LackUnitCount,
        bti.Remark,
        bti.OverSortCount,
        bti.LocationId,
        bti.LocationName,
        bti.LocationCategory,
        bti.Channel,
        bti.Source,
        bti.OrderSquence,
        bti.ProductSpecification_Id,
        bti.largePickPattern,
        bti.completeUserId,
        bti.completeUser
        from batchtaskitem bti
        inner join batchtask bt on bt.id = bti.Batchtask_id
        WHERE bti.Org_id = #{orgId,jdbcType=INTEGER}
        and bt.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and bti.TaskState != 2
        and bti.RefOrderNo in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="listOtherBatchTaskByRefOrderId"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskSortOrderOtherDTO">
        select
        bi.RefOrder_id as refOrderId,
        bi.Batchtask_id as batchTaskId,
        bt.SorterName as sorter,
        bt.Sorter_Id as sorterId,
        sum(bi.PackageCount) as packageCount,
        sum(bi.UnitCount) as unitCount
        from batchtask bt inner join batchtaskitem bi on bt.id = bi.Batchtask_id
        where
        bi.RefOrder_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="orgId != null">
            and bt.Org_id = #{orgId,jdbcType=INTEGER}
            and bi.Org_id = #{orgId,jdbcType=INTEGER}
        </if>
        and bi.Batchtask_id != #{batchTaskId,jdbcType=VARCHAR}
        group by bi.RefOrder_id, bi.Batchtask_id, bt.SorterName, bt.Sorter_Id
    </select>

    <select id="listBatchTaskItemByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtaskitem
        WHERE 1=1
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
        and Batchtask_id in
        <foreach collection="bathTaskIds" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="listBatchTaskItemByTaskIdSku" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtaskitem
        WHERE Org_id = #{orgId,jdbcType=INTEGER}
        and Batchtask_id in
        <foreach collection="bathTaskIds" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="skuId != null">
            and SkuId = #{skuId, jdbcType=VARCHAR}
        </if>
    </select>


    <select id="findBatchTaskItemsBySowTaskNos" resultMap="BaseResultMap">
        select
        bti.id,
        bti.Org_id,
        bti.BatchtaskNo,
        bti.Batchtask_id,
        bti.RefOrder_id,
        bti.RefOrderNo,
        bti.ProductName,
        bti.SkuId,
        bti.ProductBrand,
        bti.CategoryName,
        bti.SpecName,
        bti.SpecQuantity,
        bti.SaleSpec,
        bti.SaleSpecQuantity,
        bti.PackageName,
        bti.PackageCount,
        bti.UnitName,
        bti.UnitCount,
        bti.UnitTotalCount,
        bti.TaskState,
        bti.LackUnitCount,
        bti.Remark,
        bti.OverSortCount,
        bti.LocationId,
        bti.LocationName,
        bti.LocationCategory,
        bti.Channel,
        bti.Source,
        bti.OrderSquence,
        bti.largePickPattern,
        bti.completeUserId,
        bti.completeUser
        from batchtask bt
        inner join batchtaskitem bti on bt.Id = bti.Batchtask_id
        where bt.Org_id = #{orgId,jdbcType=INTEGER}
        and bt.SowTaskNo in
        <foreach collection="sowTaskNos" item="sowTaskNo" open="(" separator="," close=")">
            #{sowTaskNo,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findBatchTaskItemsByBatchIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO">
        select
        bti.id as id,
        bti.Org_id as orgId,
        bti.BatchtaskNo as batchTaskNo,
        bti.Batchtask_id as batchTaskId,
        bti.RefOrder_id as refOrderId,
        bti.RefOrderNo as refOrderNo,
        bti.ProductName as productName,
        bti.SkuId as skuId,
        bti.ProductBrand as productBrand,
        bti.CategoryName as categoryName,
        bti.SpecName as specName,
        bti.SpecQuantity as specQuantity,
        bti.SaleSpec as saleSpec,
        bti.SaleSpecQuantity as saleSpecQuantity,
        bti.PackageName as packageName,
        bti.PackageCount as packageCount,
        bti.UnitName as unitName,
        bti.UnitCount as unitCount,
        bti.UnitTotalCount as unitTotalCount,
        bti.TaskState as taskState,
        bti.LackUnitCount as lackUnitCount,
        bti.Remark as remark,
        bti.OverSortCount as overSortCount,
        bti.LocationId as locationId,
        bti.LocationName as locationName,
        bti.LocationCategory as locationCategory,
        bti.Channel as channel,
        bti.Source as source,
        bti.OrderSquence as orderSquence,
        bti.completeUserId,
        bti.completeUser,
        bti.largePickPattern,
        b.id as batchId,
        b.BatchNo as batchNo,
        bt.Warehouse_Id as warehouseId,
        bt.ToLocation_Id as toLocationId,
        bt.ToLocationName as toLocationName,
        bt.SowTask_Id as sowTaskId,
        bt.SowTaskNo as sowTaskNo,
        bt.TaskState as batchTaskState,
        bt.pickingType
        from batchtaskitem bti
        inner join batchtask bt on bt.Id = bti.Batchtask_id
        inner join batch b on b.Id = bt.Batch_id
        where b.Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findPickBatchTaskItemsByBatchNos"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO">
        select
        bti.id as id,
        bti.Org_id as orgId,
        bti.BatchtaskNo as batchTaskNo,
        bti.Batchtask_id as batchTaskId,
        bti.RefOrder_id as refOrderId,
        bti.RefOrderNo as refOrderNo,
        bti.SkuId as skuId,
        bti.PackageCount as packageCount,
        bti.UnitCount as unitCount,
        bti.UnitTotalCount as unitTotalCount,
        bti.TaskState as taskState,
        bti.LackUnitCount as lackUnitCount,
        bti.OverSortCount as overSortCount,
        bti.LocationId as locationId,
        bti.LocationName as locationName,
        bti.Channel as channel,
        bti.Source as source,
        bti.completeUserId,
        bti.completeUser,
        bti.largePickPattern,
        b.id as batchId,
        b.BatchNo as batchNo,
        bt.ToLocation_Id as toLocationId,
        bt.ToLocationName as toLocationName,
        bt.SowTask_Id as sowTaskId,
        bt.SowTaskNo as sowTaskNo,
        bt.Warehouse_Id as warehouseId
        from batchtaskitem bti
        inner join batchtask bt on bt.Id = bti.Batchtask_id
        inner join batch b on b.Id = bt.Batch_id
        where b.BatchNo in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and bti.TaskState = 2
    </select>

    <delete id="deleteByBatchTaskItemId">
        DELETE from batchtaskitem where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <update id="updateStateByBatchTaskItemId">
        update batchtaskitem set TaskState = #{state,jdbcType=TINYINT}
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateBatchItemProductCount" parameterType="java.util.List">
        UPDATE batchtaskitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="PackageCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.packageCount != null ">
                        when id=#{item.id} then PackageCount + #{item.packageCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.packageCount == null ">
                        when id=#{item.id} then PackageCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="UnitCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.unitCount != null ">
                        when id=#{item.id} then UnitCount + #{item.unitCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.unitCount == null ">
                        when id=#{item.id} then UnitCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="UnitTotalCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.unitTotalCount != null ">
                        when id=#{item.id} then UnitTotalCount + #{item.unitTotalCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.unitTotalCount == null ">
                        when id=#{item.id} then UnitTotalCount
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateBatchItemProductCountDirect" parameterType="java.util.List">
        UPDATE batchtaskitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="PackageCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.packageCount != null ">
                        when id=#{item.id} then #{item.packageCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.packageCount == null ">
                        when id=#{item.id} then PackageCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="UnitCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.unitCount != null ">
                        when id=#{item.id} then #{item.unitCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.unitCount == null ">
                        when id=#{item.id} then UnitCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="UnitTotalCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.unitTotalCount != null ">
                        when id=#{item.id} then #{item.unitTotalCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.unitTotalCount == null ">
                        when id=#{item.id} then UnitTotalCount
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateBatchItemRollBackCount" parameterType="java.util.List">
        UPDATE batchtaskitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="RollbackUnitCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.rollbackUnitCount != null ">
                        when id=#{item.id} then RollbackUnitCount + #{item.rollbackUnitCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.rollbackUnitCount == null ">
                        when id=#{item.id} then RollbackUnitCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="Remark =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.remark != null ">
                        when id=#{item.id} then #{item.remark,jdbcType=VARCHAR}
                    </if>
                    <if test="item.remark == null ">
                        when id=#{item.id} then Remark
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="findBatchTaskItemDtoListBySowTaskId" resultMap="BaseResultMap1">
        select
        bi.id,
        bi.Org_id,
        bi.BatchtaskNo,
        bi.Batchtask_id,
        bi.RefOrder_id,
        bi.RefOrderNo,
        bi.ProductName,
        bi.SkuId,
        bi.ProductBrand,
        bi.CategoryName,
        bi.SpecName,
        bi.SpecQuantity,
        bi.SaleSpec,
        bi.SaleSpecQuantity,
        bi.PackageName,
        bi.PackageCount,
        bi.UnitName,
        bi.UnitCount,
        bi.UnitTotalCount,
        bi.TaskState,
        bi.LackUnitCount,
        bi.Remark,
        bi.OverSortCount,
        bi.LocationId,
        bi.LocationName,
        bi.LocationCategory,
        bi.Channel,
        bi.Source,
        bi.OrderSquence,
        bi.completeUserId,
        bi.completeUser,
        bi.largePickPattern,
        b.PickingType,
        b.SowTask_Id,
        b.sowTaskNo,
        b.ToLocation_Id,
        b.ToLocationName
        from batchtaskitem bi
        INNER JOIN batchtask b on b.id = bi.Batchtask_id
        WHERE b.SowTask_Id = #{sowTaskId,jdbcType=BIGINT}
        <if test="state != null">
            and bi.TaskState = #{state,jdbcType=TINYINT}
        </if>
    </select>

    <select id="findBatchTaskItemDtoListByBatchIds" resultMap="BaseResultMap1">
        select
        bi.id,
        bi.Org_id,
        bi.BatchtaskNo,
        bi.Batchtask_id,
        bi.ProductName,
        bi.SkuId,
        bi.SpecName,
        bi.SpecQuantity,
        bi.PackageName,
        bi.PackageCount,
        bi.UnitName,
        bi.UnitCount,
        bi.UnitTotalCount,
        bi.TaskState,
        bi.LackUnitCount,
        bi.OverSortCount,
        b.Batch_id
        from batchtaskitem bi
        INNER JOIN batchtask b on b.id = bi.Batchtask_id
        WHERE b.Org_id = #{orgId,jdbcType=INTEGER}
        and b.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and b.Batch_id in
        <foreach collection="batchIds" item="batchId" open="(" separator="," close=")">
            #{batchId,jdbcType=VARCHAR}
        </foreach>
    </select>


    <update id="updateBatchItemSowTaskId">
        update batchtaskitem
        set SowTaskItemId = #{sowTaskItemId,jdbcType=INTEGER}
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <select id="findLackCountWithinHourBySku" resultType="int">
        select count(1) lackCount
        from batchtaskitem ti
        inner join batchtask t on ti.Batchtask_id = t.id
        where t.Org_id = #{orgId,jdbcType=INTEGER}
        and t.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and ti.SkuId = #{skuId,jdbcType=VARCHAR}
        and ti.LastUpdateTime <![CDATA[ > ]]> #{timeLimit,jdbcType=TIMESTAMP}
        and ti.IsLack = 1
    </select>

    <select id="findLackSkuCountWithinHour" resultType="int">
        select
        count(distinct ti.SkuId) lackSkuCount
        from
        batchtaskitem ti inner join batchtask t on ti.Batchtask_id = t.id
        where
        t.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and ti.LastUpdateTime <![CDATA[ > ]]> #{timeLimit,jdbcType=TIMESTAMP}
        and ti.IsLack = 1
        <!-- and ti.SkuId != #{skuId,jdbcType=VARCHAR} -->
    </select>
    <select id="listToLocationName" resultMap="BaseResultMap">
        select o.RefOrderNo,max(b.ToLocationName) as ToLocationName
        from outstockorder o
        inner join orderitemtaskinfo i on i.RefOrder_Id = o.id
        inner join batchtask b on b.id = i.BatchTask_Id
        WHERE o.Org_id = #{orgId,jdbcType=INTEGER}
        and o.RefOrderNo in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        group by o.RefOrderNo
    </select>

    <update id="batchUpdateBatchTaskItem" parameterType="java.util.List">
        UPDATE batchtaskitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="SownUnitTotalCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.sownUnitTotalCount != null ">
                        when id=#{item.id} then #{item.sownUnitTotalCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.rollbackUnitCount == null ">
                        when id=#{item.id} then SownUnitTotalCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="Remark =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.remark != null ">
                        when id=#{item.id} then #{item.remark,jdbcType=VARCHAR}
                    </if>
                    <if test="item.remark == null ">
                        when id=#{item.id} then Remark
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.lastUpdateUser != null ">
                        when id=#{item.id} then #{item.lastUpdateUser,jdbcType=VARCHAR}
                    </if>
                    <if test="item.lastUpdateUser == null ">
                        when id=#{item.id} then LastUpdateUser
                    </if>
                </foreach>
            </trim>
            <trim prefix="LocationId =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.locationId != null ">
                        when id=#{item.id} then #{item.locationId,jdbcType=BIGINT}
                    </if>
                    <if test="item.locationId == null ">
                        when id=#{item.id} then LocationId
                    </if>
                </foreach>
            </trim>
            <trim prefix="LocationName =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.locationName != null ">
                        when id=#{item.id} then #{item.locationName,jdbcType=VARCHAR}
                    </if>
                    <if test="item.locationName == null ">
                        when id=#{item.id} then LocationName
                    </if>
                </foreach>
            </trim>
            Lastupdatetime = now()
        </trim>
        WHERE id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="listBatchTaskItem" resultMap="BaseResultMap1">
        select
        bti.id,
        bti.Org_id,
        bt.Warehouse_Id,
        bti.BatchtaskNo,
        bti.Batchtask_id,
        bti.RefOrderNo,
        bti.RefOrder_id,
        bti.ProductName,
        bti.SkuId,
        bti.ProductBrand,
        bti.CategoryName,
        bti.SpecName,
        bti.SpecQuantity,
        bti.SaleSpec,
        bti.SaleSpecQuantity,
        bti.PackageName,
        bti.PackageCount,
        bti.UnitName,
        bti.UnitCount,
        bti.UnitTotalCount,
        bti.TaskState,
        bti.LackUnitCount,
        bti.IsLack,
        bti.Remark,
        bti.OverSortCount,
        bti.LocationId,
        bti.LocationName,
        bti.LocationCategory,
        bt.PickingType,
        bti.OrderSquence,
        bt.SowTask_Id,
        bt.SowTaskNo,
        bt.CreateTime,
        bt.Sorter_Id,
        bt.SorterName,
        bti.ControlConfig_Id,
        bti.ProductionDate,
        bti.largePickPattern,
        bti.BatchTime,
        bti.UnitPrice,
        bt.ToLocation_Id,
        bt.ToLocationName,
        bti.SownUnitTotalCount,
        bti.completeUserId,
        bti.completeUser,
        bt.BatchNo
        from batchtaskitem bti
        INNER JOIN batchtask bt on bt.id = bti.Batchtask_id
        where bti.Org_id = #{cityId,jdbcType=INTEGER}
        <if test="warehouseId != null">
            and bt.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="batchTaskNo != null and batchTaskNo != ''">
            and bti.BatchtaskNo = #{batchTaskNo,jdbcType=VARCHAR}
        </if>
        <if test="batchNo != null and batchNo != ''">
            and bt.BatchNo = #{batchNo,jdbcType=VARCHAR}
        </if>
        <if test="refOrderNo != null and refOrderNo != ''">
            and bti.RefOrderNo = #{refOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="productName != null and productName != ''">
            and bti.ProductName like concat('%', #{productName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="sorterId != null">
            and bt.Sorter_Id = #{sorterId,jdbcType=INTEGER}
        </if>
        <if test="sorter != null and sorter != ''">
            and bt.SorterName like concat('%', #{sorter,jdbcType=VARCHAR}, '%')
        </if>
        <if test="taskState != null">
            and bti.TaskState = #{taskState,jdbcType=TINYINT}
        </if>
        <if test="taskStateList != null">
            AND bti.TaskState in
            <foreach collection="taskStateList" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="startTime != null">
            AND bt.CreateTime <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND bt.CreateTime <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="batchTaskItemIdList != null and batchTaskItemIdList.size()>0">
            AND bti.id in
            <foreach collection="batchTaskItemIdList" item="itemId" separator="," open="(" close=")">
                #{itemId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="batchTaskId != null and batchTaskId != ''">
            and bti.Batchtask_id = #{batchTaskId,jdbcType=VARCHAR}
        </if>
        <if test="batchTaskIdList != null and batchTaskIdList.size()>0">
            AND bti.Batchtask_id in
            <foreach collection="batchTaskIdList" item="taskId" separator="," open="(" close=")">
                #{taskId,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="listBatchTaskSkuId" resultType="java.lang.String">
        SELECT bti.SkuId
        FROM sowtask st
        LEFT JOIN batchtask bt ON st.Id = bt.SowTask_Id AND st.Org_id = bt.Org_id
        LEFT JOIN batchtaskitem bti ON bti.Batchtask_id = bt.id AND st.Org_id = bti.Org_id
        WHERE bt.SowTask_Id = #{id}
        and bt.Org_id = #{orgId}
    </select>

    <select id="findBatchTaskItemCountByTaskIds" resultType="java.lang.Integer">
        select
        count(1)
        from batchtaskitem
        WHERE Batchtask_id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findBatchTaskItemIdByTaskIds" resultType="java.lang.String">
        select
        Id
        from batchtaskitem
        WHERE Batchtask_id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findBatchTaskItemByIds" resultMap="AllResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtaskitem
        WHERE id IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findBatchTaskItemByLocationInfo" resultMap="BaseResultMap">
        select
        b.id ,
        b.Org_id,
        b.BatchtaskNo,
        b.Batchtask_id,
        b.RefOrder_id,
        b.RefOrderNo,
        b.ProductName,
        b.SkuId,
        b.ProductBrand,
        b.CategoryName,
        b.SpecName,
        b.SpecQuantity,
        b.SaleSpec,
        b.SaleSpecQuantity,
        b.PackageName,
        b.PackageCount,
        b.UnitName,
        b.UnitCount,
        b.UnitTotalCount,
        b.TaskState,
        b.LackUnitCount,
        b.Remark,
        b.OverSortCount,
        b.LocationId,
        b.LocationName,
        b.LocationCategory,
        b.Channel,
        b.Source,
        b.OrderSquence,
        b.LargePickPattern,
        b.ProductSpecification_Id,
        b.SowTaskItemId
        from batchtask a inner join batchtaskitem b
        on a.id = b.Batchtask_id
        where a.warehouse_id = #{query.warehouseId,jdbcType=INTEGER}
        and b.org_id = #{query.orgId,jdbcType=INTEGER}
        and b.TaskState in
        <foreach collection="query.taskStateList" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=TINYINT}
        </foreach>
        <if test="query.productSkuIds != null and query.productSkuIds.size() > 0">
            and b.skuid IN
            <foreach collection="query.productSkuIds" item="item" index="index" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
        <if test="query.locationIds != null and query.locationIds.size() > 0">
            and b.locationId IN
            <foreach collection="query.locationIds" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="findBatchTaskItemIdByLocationInfo" resultType="java.lang.String">
        select id from batchtaskitem
        where Org_id = #{query.orgId,jdbcType=INTEGER}
        and TaskState in
        <foreach collection="query.taskStateList" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=TINYINT}
        </foreach>
        <if test="query.locationIds != null and query.locationIds.size() > 0">
            and locationId IN
            <foreach collection="query.locationIds" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

</mapper>