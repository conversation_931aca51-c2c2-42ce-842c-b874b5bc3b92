package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/27
 */
public class OutStockOrderPagesDTO extends OutStockOrderDTO implements Serializable {

    private static final long serialVersionUID = 7335298806261497261L;
    /**
     * 快递公司编码
     */
    private String shipperCode;

    /**
     * 快递公司名称
     */
    private String shipperName;

    /**
     * 快递单号
     */
    private String logisticCode;

    public String getShipperCode() {
        return shipperCode;
    }

    public void setShipperCode(String shipperCode) {
        this.shipperCode = shipperCode;
    }

    public String getShipperName() {
        return shipperName;
    }

    public void setShipperName(String shipperName) {
        this.shipperName = shipperName;
    }

    public String getLogisticCode() {
        return logisticCode;
    }

    public void setLogisticCode(String logisticCode) {
        this.logisticCode = logisticCode;
    }
}
