package com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch;

import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/13
 */
public class SplitBatchTaskByOrderRequestBO {

    private WaveCreateDTO createDTO;

    private BatchPO batchPO;

    /**
     * 获取
     *
     * @return createDTO
     */
    public WaveCreateDTO getCreateDTO() {
        return this.createDTO;
    }

    /**
     * 设置
     *
     * @param createDTO
     */
    public void setCreateDTO(WaveCreateDTO createDTO) {
        this.createDTO = createDTO;
    }

    /**
     * 获取
     *
     * @return batchPO
     */
    public BatchPO getBatchPO() {
        return this.batchPO;
    }

    /**
     * 设置
     *
     * @param batchPO
     */
    public void setBatchPO(BatchPO batchPO) {
        this.batchPO = batchPO;
    }
}
