package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 波次任务分拣DTO
 *
 * <AUTHOR> 2018-03-26
 */
public class BatchTaskSortDTO implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1918524259530103503L;
    /**
     * id
     */
    private String id;
    /**
     * 波次任务号(拣货任务编号)
     */
    private String batchTaskNo;
    /**
     * 波次编号
     */
    private String batchNo;
    /**
     * 分拣员
     */
    private Integer sorterId;
    /**
     * 商品种类数量
     */
    private Integer skuCount;
    /**
     * 大件总数
     */
    private BigDecimal packageAmount;
    /**
     * 小件总数
     */
    private BigDecimal unitAmount;
    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte taskState;
    /**
     * 拣货方式 1 按订单拣货 2 按产品拣货',
     */
    private Byte pickingType;
    /**
     * 通道编码
     */
    private String passageCode;
    /**
     * 拣货分组值（产品拣货时 ）,
     */
    private String pickingValue;

    /**
     * 订单筛选 1 按区域 2 按线路
     */
    private Byte orderSelection;

    /**
     * 区域或线路名称
     */
    private String areaOrRouteName;

    /**
     * 拣货属性
     */
    private String batchTaskName;

    /**
     * 订单数量
     */
    private Integer orderCount;

    /**
     * 集货位id
     */
    private Long sowLocationId;

    /**
     * 集货位名称
     */
    private String sowLocationName;

    /**
     * 仓库Id
     */
    private Integer warehouseId;

    /**
     * 分区id
     */
    private Long sortGroupId;

    /**
     * 备货位id
     */
    private Long toLocationId;

    /**
     * 备货位名称
     */
    private String toLocationName;

    /**
     * 内配单标识, true: 内配单, false：非内配单
     */
    private Boolean allocationFlag = false;

    /**
     * 是否可以使用容器位, 1: 是, 0：否
     */
    private Integer containerFlag = 0;

    /**
     * 领取标识 0：不可领取 1：整单领取 2：拆分领取
     */
    private Byte receiveFlag;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种类型 1-总单播种 2-二次分拣 3-快速播种
     */
    private Integer sowType = 1;
    /**
     * 备注 (暂存容器数量)
     */
    private String remark;

    /**
     * 是否可以修改出库位
     */
    private Boolean updateToLocationFlag = true;

    /**
     * 托盘货位id
     */
    private Long palletLocationId;

    /**
     * 托盘名称
     */
    private String palletLocationName;

    /**
     * 托盘集合
     */
    private List<String> palletNoList;

    /**
     * 拣货方式:1、默认；2、电子标签拣货
     *
     * @see BatchTaskKindOfPickingConstants
     */
    private Byte kindOfPicking;

    /**
     * 是否存在电子标签
     */
    private boolean existSortGroupRfid;
    /**
     * 拣货任务方式
     * 
     * @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum
     */
    private Byte pickPattern;

    public Integer getSowType() {
        return sowType;
    }

    public void setSowType(Integer sowType) {
        this.sowType = sowType;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public Byte getReceiveFlag() {
        return receiveFlag;
    }

    public void setReceiveFlag(Byte receiveFlag) {
        this.receiveFlag = receiveFlag;
    }

    public Boolean getAllocationFlag() {
        return allocationFlag;
    }

    public void setAllocationFlag(Boolean allocationFlag) {
        this.allocationFlag = allocationFlag;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public Long getSortGroupId() {
        return sortGroupId;
    }

    public void setSortGroupId(Long sortGroupId) {
        this.sortGroupId = sortGroupId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    /**
     * 获取 id
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 波次任务号(拣货任务编号)
     */
    public String getBatchTaskNo() {
        return this.batchTaskNo;
    }

    /**
     * 设置 波次任务号(拣货任务编号)
     */
    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    /**
     * 获取 波次编号
     */
    public String getBatchNo() {
        return this.batchNo;
    }

    /**
     * 设置 波次编号
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * 获取 分拣员
     */
    public Integer getSorterId() {
        return this.sorterId;
    }

    /**
     * 设置 分拣员
     */
    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    /**
     * 获取 商品种类数量
     */
    public Integer getSkuCount() {
        return this.skuCount;
    }

    /**
     * 设置 商品种类数量
     */
    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    /**
     * 获取 大件总数
     */
    public BigDecimal getPackageAmount() {
        return this.packageAmount;
    }

    /**
     * 设置 大件总数
     */
    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    /**
     * 获取 小件总数
     */
    public BigDecimal getUnitAmount() {
        return this.unitAmount;
    }

    /**
     * 设置 小件总数
     */
    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    /**
     * 获取 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    public Byte getTaskState() {
        return this.taskState;
    }

    /**
     * 设置 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    /**
     * 获取 拣货方式 1 按订单拣货 2 按产品拣货',
     */
    public Byte getPickingType() {
        return this.pickingType;
    }

    /**
     * 设置 拣货方式 1 按订单拣货 2 按产品拣货',
     */
    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    /**
     * 获取 拣货分组值（产品拣货时 ）,
     */
    public String getPickingValue() {
        return this.pickingValue;
    }

    /**
     * 设置 拣货分组值（产品拣货时 ）,
     */
    public void setPickingValue(String pickingValue) {
        this.pickingValue = pickingValue;
    }

    public Byte getOrderSelection() {
        return orderSelection;
    }

    public void setOrderSelection(Byte orderSelection) {
        this.orderSelection = orderSelection;
    }

    public String getAreaOrRouteName() {
        return areaOrRouteName;
    }

    public void setAreaOrRouteName(String areaOrRouteName) {
        this.areaOrRouteName = areaOrRouteName;
    }

    public String getBatchTaskName() {
        return batchTaskName;
    }

    public void setBatchTaskName(String batchTaskName) {
        this.batchTaskName = batchTaskName;
    }

    public Long getSowLocationId() {
        return sowLocationId;
    }

    public void setSowLocationId(Long sowLocationId) {
        this.sowLocationId = sowLocationId;
    }

    public String getSowLocationName() {
        return sowLocationName;
    }

    public void setSowLocationName(String sowLocationName) {
        this.sowLocationName = sowLocationName;
    }

    /**
     * @return the containerFlag
     */
    public Integer getContainerFlag() {
        return containerFlag;
    }

    /**
     * @param containerFlag the containerFlag to set
     */
    public void setContainerFlag(Integer containerFlag) {
        this.containerFlag = containerFlag;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPassageCode() {
        return passageCode;
    }

    public void setPassageCode(String passageCode) {
        this.passageCode = passageCode;
    }

    public Boolean getUpdateToLocationFlag() {
        return updateToLocationFlag;
    }

    public void setUpdateToLocationFlag(Boolean updateToLocationFlag) {
        this.updateToLocationFlag = updateToLocationFlag;
    }

    public Long getPalletLocationId() {
        return palletLocationId;
    }

    public void setPalletLocationId(Long palletLocationId) {
        this.palletLocationId = palletLocationId;
    }

    public String getPalletLocationName() {
        return palletLocationName;
    }

    public void setPalletLocationName(String palletLocationName) {
        this.palletLocationName = palletLocationName;
    }

    public List<String> getPalletNoList() {
        return palletNoList;
    }

    public void setPalletNoList(List<String> palletNoList) {
        this.palletNoList = palletNoList;
    }

    public Byte getKindOfPicking() {
        return kindOfPicking;
    }

    public void setKindOfPicking(Byte kindOfPicking) {
        this.kindOfPicking = kindOfPicking;
    }

    public boolean isExistSortGroupRfid() {
        return existSortGroupRfid;
    }

    public void setExistSortGroupRfid(boolean existSortGroupRfid) {
        this.existSortGroupRfid = existSortGroupRfid;
    }

    /**
     * 默认
     */
    public static final Integer BATCHTASKSORT_SOWTYPE_DEFAULT = 1;
    /**
     * 二次分拣
     */
    public static final Integer BATCHTASKSORT_SECPICK_DEFAULT = 2;
    /**
     * 边拣边播
     */
    public static final Integer BATCHTASKSORT_PICKSORT_DEFAULT = 3;

    /**
     * 获取 拣货任务方式 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum
     *
     * @return pickPattern 拣货任务方式 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum
     */
    public Byte getPickPattern() {
        return this.pickPattern;
    }

    /**
     * 设置 拣货任务方式 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum
     *
     * @param pickPattern 拣货任务方式 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum
     */
    public void setPickPattern(Byte pickPattern) {
        this.pickPattern = pickPattern;
    }
}
