package com.yijiupi;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.pickup.IdleOrderPickerBL;
import org.junit.Test;
import org.junit.runner.RunWith;

import com.yijiupi.junit.runner.GeneralRunner;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @title: BatchOrderProcessPassageTest
 * @description:
 * @date 2022-12-29 15:53
 */
@RunWith(GeneralRunner.class)
public class IdleOrderPickerBLTest {

    @Autowired
    private IdleOrderPickerBL idleOrderPickerBL;

    @Test
    public void assignIdleOrderPicker() {
        idleOrderPickerBL.assignIdleOrderPickerTest();
    }

}
