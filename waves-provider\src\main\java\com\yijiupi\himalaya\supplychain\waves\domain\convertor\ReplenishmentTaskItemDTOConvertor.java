package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.ReplenishmentTaskItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;

/**
 * <AUTHOR>
 * @title: ReplenishmentTaskItemDTOConvertor
 * @description:
 * @date 2022-11-09 14:31
 */
public class ReplenishmentTaskItemDTOConvertor {

    public static final Byte TaskType_Create = 1;

    public static List<ReplenishmentTaskItemDTO> convert(List<BatchTaskItemPO> lstBatchTaskItem,
                                                         Map<Long, LocationReturnDTO> areaMap, Integer warehouseId) {
        return lstBatchTaskItem.stream().filter(item -> Objects.nonNull(areaMap.get(item.getSkuId()))).map(m -> {
            ReplenishmentTaskItemDTO itemDTO = new ReplenishmentTaskItemDTO();
            itemDTO.setOrgId(m.getOrgId());
            itemDTO.setWarehouseId(warehouseId);
            itemDTO.setProductSkuId(m.getSkuId());
            itemDTO.setProductName(m.getProductName());
            itemDTO.setProductBrand(m.getProductBrand());
            itemDTO.setSource(m.getSource());
            itemDTO.setOwnerId(m.getOwnerId());
            itemDTO.setChannel(m.getChannel());
            itemDTO.setProductSpecificationId(m.getProductSpecificationId());
            itemDTO.setSpecName(m.getSpecName());
            itemDTO.setSpecQuantity(m.getSpecQuantity());
            itemDTO.setPackageName(m.getPackageName());
            itemDTO.setPackageCount(m.getPackageCount());
            itemDTO.setUnitName(m.getUnitName());
            itemDTO.setUnitCount(m.getUnitCount());
            itemDTO.setUnitTotalCount(m.getUnitTotalCount());
            itemDTO.setTaskType(TaskType_Create);

            itemDTO.setToLocationId(m.getLocationId());
            itemDTO.setToLocationName(m.getLocationName());

            return itemDTO;
        }).collect(Collectors.toList());
    }

}
