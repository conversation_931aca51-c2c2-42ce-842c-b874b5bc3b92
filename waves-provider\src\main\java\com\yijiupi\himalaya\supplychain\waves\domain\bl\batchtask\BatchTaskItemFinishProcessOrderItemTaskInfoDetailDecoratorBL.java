package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskFinishHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishNeedOpBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoDetailMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.UUIDGeneratorUtil;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.<br />
 * 最低级，最后处理
 * 
 * <AUTHOR>
 * @date 2024/1/15
 */
@Service
public class BatchTaskItemFinishProcessOrderItemTaskInfoDetailDecoratorBL extends BatchTaskItemFinishDecoratorBL {

    @Reference
    private IBatchInventoryManageService iBatchInventoryManageService;

    @Autowired
    private OrderItemTaskInfoDetailMapper orderItemTaskInfoDetailMapper;
    @Autowired
    private BatchInventoryTransferBL batchInventoryTransferBL;

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }

    @Override
    protected void doCompleteBatchTaskItem(BatchTaskItemFinishNeedOpBO bo,
        BatchTaskFinishHelperBO batchTaskFinishHelperBO, List<BatchTaskItemDTO> needCompleteBatchTaskItemList) {

        List<PickUpDTO> pickUpDTOList = bo.getPickUpDTOList();
        boolean isCrossWareHouse =
            Objects.equals(batchTaskFinishHelperBO.getBatchPO().getCrossWareHouse(), ConditionStateEnum.是.getType());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            batchTaskFinishHelperBO.getUpdateBatchTaskItemRelateOrderItemTaskInfoPOList();
        List<OrderItemTaskInfoDetailPO> updateTaskInfoDetailList = bo.getUpdateOrderItemTaskInfoDetailPOList();
        Map<Long, BigDecimal> taskInfoDetailOverSortCountMap =
            batchTaskFinishHelperBO.getTaskInfoDetailOverSortCountMap();

        // 3、校验移库
        if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
            if (!isCrossWareHouse) {
                LOGGER.info("校验移库入参：{}", JSON.toJSONString(pickUpDTOList));
                pickUpDTOList = batchInventoryTransferBL.checkInventoryTransferByPickUp(pickUpDTOList);
            }
            // 根据实际移库数量更新出库单项关联明细表
            processOrderItemTaskInfoDetail(orderItemTaskInfoPOList, updateTaskInfoDetailList, pickUpDTOList,
                taskInfoDetailOverSortCountMap);
        }
    }

    /**
     * 根据实际移库数量更新出库单项关联明细表
     */
    private void processOrderItemTaskInfoDetail(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        List<OrderItemTaskInfoDetailPO> updateTaskInfoDetailList, List<PickUpDTO> realPickUpDTOList,
        Map<Long, BigDecimal> taskInfoDetailOverSortCountMap) {
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList) || CollectionUtils.isEmpty(realPickUpDTOList)) {
            return;
        }
        LOGGER.info("[拣货]实际移库数量：{}", JSON.toJSONString(realPickUpDTOList));
        // 新增明细
        List<OrderItemTaskInfoDetailPO> addTaskInfoDetailList = new ArrayList<>();
        // 实际移库数量按关联ID分组
        Map<String, List<PickUpDTO>> pickUpMap =
            realPickUpDTOList.stream().filter(p -> StringUtils.isNotEmpty(p.getBusinessId()))
                .collect(Collectors.groupingBy(p -> p.getBusinessId()));
        if (pickUpMap == null || pickUpMap.isEmpty()) {
            LOGGER.info("[拣货]兼容老数据，不更新出库单项关联明细表");
            return;
        }
        pickUpMap.forEach((taskInfoId, pickList) -> {
            if (CollectionUtils.isEmpty(pickList)) {
                return;
            }
            // 1、找出对应的出库单项的关联
            Optional<OrderItemTaskInfoPO> optionalInfo = orderItemTaskInfoPOList.stream()
                .filter(p -> Objects.equals(p.getId(), Long.valueOf(taskInfoId))).findFirst();
            if (!optionalInfo.isPresent()) {
                LOGGER.info("根据移库数据找不到出库单项关联表:{}", taskInfoId);
                return;
            }
            List<OrderItemTaskInfoDetailPO> taskInfoDetailPOS = optionalInfo.get().getDetailList();
            if (CollectionUtils.isEmpty(taskInfoDetailPOS)) {
                LOGGER.info("出库单项关联明细为空：{}" + taskInfoId);
                return;
            }
            // 是否第一次提交
            boolean isFirstSubmit = true;
            if (Objects.equals(optionalInfo.get().getBatchTaskItemState(), TaskStateEnum.已完成.getType())) {
                isFirstSubmit = false;
            }

            // 2、统计不同货主的移库数量
            Map<Long, BigDecimal> secOwnerIdMap = new HashMap<>();
            // 记录本次移库操作时不同货主的移库数量
            pickList.forEach(pick -> {
                addSecOwnerIdMap(secOwnerIdMap, pick.getSecOwnerId(), pick.getCount());
            });
            // 若不是第一次提交，则需要把上次的移库数量加上
            if (!isFirstSubmit && taskInfoDetailOverSortCountMap != null) {
                taskInfoDetailOverSortCountMap.forEach((detailId, overCount) -> {
                    Optional<OrderItemTaskInfoDetailPO> optionalDetail =
                        taskInfoDetailPOS.stream().filter(p -> Objects.equals(p.getId(), detailId)).findFirst();
                    optionalDetail.ifPresent(orderItemTaskInfoDetailPO -> addSecOwnerIdMap(secOwnerIdMap,
                        orderItemTaskInfoDetailPO.getSecOwnerId(), overCount));
                });
            }

            // 3、根据不同货主的移库数量更新关联明细的分配数量
            // （1）原来分配的二级货主没有进行实际移库，则把分配数量改为0
            taskInfoDetailPOS.stream().filter(p -> !secOwnerIdMap.containsKey(p.getSecOwnerId())).forEach(p -> {
                addUpdateTaskInfoDetailList(updateTaskInfoDetailList, p.getId(), BigDecimal.ZERO);
            });
            // （2）根据实际移库二级货主，若之前存在，则更新数量，不存在则新增
            secOwnerIdMap.forEach((secOwnerId, count) -> {
                Optional<OrderItemTaskInfoDetailPO> optionalDetail =
                    taskInfoDetailPOS.stream().filter(p -> Objects.equals(p.getSecOwnerId(), secOwnerId)).findFirst();
                if (optionalDetail.isPresent()) {
                    addUpdateTaskInfoDetailList(updateTaskInfoDetailList, optionalDetail.get().getId(), count);
                } else {
                    OrderItemTaskInfoDetailPO newDetailPO = new OrderItemTaskInfoDetailPO();
                    newDetailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.ORDER_ITEM_TASK_INFO_DETAIL));
                    newDetailPO.setOrgId(optionalInfo.get().getOrgId());
                    newDetailPO.setTaskInfoId(optionalInfo.get().getId());
                    newDetailPO.setSecOwnerId(secOwnerId);
                    newDetailPO.setUnitTotalCount(count);
                    newDetailPO.setOwnerId(taskInfoDetailPOS.get(0).getOwnerId());
                    newDetailPO.setProductSpecificationId(taskInfoDetailPOS.get(0).getProductSpecificationId());
                    addTaskInfoDetailList.add(newDetailPO);
                }
            });
        });

        if (CollectionUtils.isNotEmpty(addTaskInfoDetailList)) {
            Lists.partition(addTaskInfoDetailList, 100).forEach(p -> {
                orderItemTaskInfoDetailMapper.insertBatch(p);
            });
            LOGGER.info("[拣货]新增拣货任务项关联的订单项明细分配数量：{}", JSON.toJSONString(addTaskInfoDetailList));
        }
    }

    private void addSecOwnerIdMap(Map<Long, BigDecimal> secOwnerIdMap, Long secOwnerId, BigDecimal count) {
        if (secOwnerIdMap != null) {
            if (secOwnerIdMap.containsKey(secOwnerId)) {
                count = count.add(secOwnerIdMap.get(secOwnerId));
            }
            secOwnerIdMap.put(secOwnerId, count);
        }
    }

    /**
     * 记录需要更新的关联明细项
     */
    private void addUpdateTaskInfoDetailList(List<OrderItemTaskInfoDetailPO> updateTaskInfoDetailList,
        Long taskInfoDetailId, BigDecimal count) {
        if (Objects.isNull(taskInfoDetailId)) {
            return;
        }
        // 已存在，则更新数量
        if (updateTaskInfoDetailList.stream().anyMatch(p -> Objects.equals(p.getId(), taskInfoDetailId))) {
            OrderItemTaskInfoDetailPO oldDetailPO = updateTaskInfoDetailList.stream()
                .filter(p -> Objects.equals(p.getId(), taskInfoDetailId)).findFirst().get();
            oldDetailPO.setUnitTotalCount(count);
        } else {
            OrderItemTaskInfoDetailPO updateDetail = new OrderItemTaskInfoDetailPO();
            updateDetail.setId(taskInfoDetailId);
            updateDetail.setUnitTotalCount(count);
            updateTaskInfoDetailList.add(updateDetail);
        }
    }
}
