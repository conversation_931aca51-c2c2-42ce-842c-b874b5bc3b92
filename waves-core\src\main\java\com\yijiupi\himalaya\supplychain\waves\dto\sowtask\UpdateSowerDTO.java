package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;
import java.util.List;

public class UpdateSowerDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 播种任务编号
     */
    private List<String> sowTaskNos;

    /**
     * 播种人
     */
    private String sower;

    /**
     * 播种人id
     */
    private Integer sowerId;

    /**
     * 修改人
     */
    private String lastUpdateUser;

    /**
     * 状态
     */
    private Byte state;
    /**
     * 分拣员ID
     */
    private Integer sorterId;
    /**
     * 分拣员名称
     */
    private String sorterName;

    public Integer getSorterId() {
        return sorterId;
    }

    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    public String getSorterName() {
        return sorterName;
    }

    public void setSorterName(String sorterName) {
        this.sorterName = sorterName;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public List<String> getSowTaskNos() {
        return sowTaskNos;
    }

    public void setSowTaskNos(List<String> sowTaskNos) {
        this.sowTaskNos = sowTaskNos;
    }

    public String getSower() {
        return sower;
    }

    public void setSower(String sower) {
        this.sower = sower;
    }

    public Integer getSowerId() {
        return sowerId;
    }

    public void setSowerId(Integer sowerId) {
        this.sowerId = sowerId;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }
}
