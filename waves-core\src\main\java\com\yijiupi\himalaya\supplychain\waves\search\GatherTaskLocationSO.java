/*
 * @ClassName GatherTaskLocationDTO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-10-31 10:56:55
 */
package com.yijiupi.himalaya.supplychain.waves.search;

public class GatherTaskLocationSO {

    /**
     * @Fields id ID
     */
    private Long id;
    /**
     * @Fields orgId 城市id
     */
    private Integer orgId;
    /**
     * @Fields gatherTaskId 集货任务号ID
     */
    private Long gatherTaskId;
    /**
     * @Fields gatherTaskProductId 集货任务产品id
     */
    private Long gatherTaskProductId;

    /**
     * 获取 ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 城市id
     */
    public Integer getOrgId() {
        return orgId;
    }

    /**
     * 设置 城市id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 集货任务号ID
     */
    public Long getGatherTaskId() {
        return gatherTaskId;
    }

    /**
     * 设置 集货任务号ID
     */
    public void setGatherTaskId(Long gatherTaskId) {
        this.gatherTaskId = gatherTaskId;
    }

    /**
     * 获取 集货任务产品id
     */
    public Long getGatherTaskProductId() {
        return gatherTaskProductId;
    }

    /**
     * 设置 集货任务产品id
     */
    public void setGatherTaskProductId(Long gatherTaskProductId) {
        this.gatherTaskProductId = gatherTaskProductId;
    }

}