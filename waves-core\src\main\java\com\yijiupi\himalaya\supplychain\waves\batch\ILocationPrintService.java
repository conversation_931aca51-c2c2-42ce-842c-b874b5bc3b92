package com.yijiupi.himalaya.supplychain.waves.batch;

import com.yijiupi.himalaya.supplychain.waves.search.LocationContainerSO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/4/20
 */
public interface ILocationPrintService {
    /**
     * 创建货位容器编号
     *
     * @param locationContainerSO
     */
    void createLocationContainer(LocationContainerSO locationContainerSO);

    /**
     * 打印容器编号
     *
     * @return
     */
    String printLocationContainer(LocationContainerSO locationContainerSO);
}
