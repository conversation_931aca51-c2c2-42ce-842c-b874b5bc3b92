package com.yijiupi.himalaya.supplychain.waves.dto.soworder;

import java.io.Serializable;
import java.util.List;

public class SowOrderUpdateDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 订单编号
     */
    private List<Long> orderIds;

    /**
     * 出库位id
     */
    private Long locationId;

    /**
     * 出库位名称
     */
    private String locationName;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 播种任务明细id
     */
    private List<Long> sowTaskItemIds;

    private List<Long> orderItemIds;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public List<Long> getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(List<Long> orderIds) {
        this.orderIds = orderIds;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<Long> getSowTaskItemIds() {
        return sowTaskItemIds;
    }

    public void setSowTaskItemIds(List<Long> sowTaskItemIds) {
        this.sowTaskItemIds = sowTaskItemIds;
    }

    /**
     * 获取
     *
     * @return orderItemIds
     */
    public List<Long> getOrderItemIds() {
        return this.orderItemIds;
    }

    /**
     * 设置
     *
     * @param orderItemIds
     */
    public void setOrderItemIds(List<Long> orderItemIds) {
        this.orderItemIds = orderItemIds;
    }
}
