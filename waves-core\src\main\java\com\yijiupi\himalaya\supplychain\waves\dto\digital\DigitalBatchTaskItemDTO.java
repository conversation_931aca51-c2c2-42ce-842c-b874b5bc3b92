package com.yijiupi.himalaya.supplychain.waves.dto.digital;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/10/30
 */
public class DigitalBatchTaskItemDTO implements Serializable {

    /**
     * 拣货任务明细id
     */
    private String batchTaskItemId;
    /**
     * 拣货小数量
     */
    private BigDecimal unitTotalCount;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * 巷道id
     */
    private Long roadWayId;
    /**
     * 巷道名称
     */
    private String roadWayName;
    /**
     * 拣货任务明细状态
     */
    private Byte state;
    /**
     * 销售规格
     */
    private BigDecimal saleSpecQuantity;

    /**
     * 获取 拣货任务明细id
     *
     * @return batchTaskItemId 拣货任务明细id
     */
    public String getBatchTaskItemId() {
        return this.batchTaskItemId;
    }

    /**
     * 设置 拣货任务明细id
     *
     * @param batchTaskItemId 拣货任务明细id
     */
    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    /**
     * 获取 拣货小数量
     *
     * @return unitTotalCount 拣货小数量
     */
    public BigDecimal getUnitTotalCount() {
        return this.unitTotalCount;
    }

    /**
     * 设置 拣货小数量
     *
     * @param unitTotalCount 拣货小数量
     */
    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    /**
     * 获取 货位id
     *
     * @return locationId 货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位id
     *
     * @param locationId 货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 货位名称
     *
     * @return locationName 货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 货位名称
     *
     * @param locationName 货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 巷道id
     *
     * @return roadWayId 巷道id
     */
    public Long getRoadWayId() {
        return this.roadWayId;
    }

    /**
     * 设置 巷道id
     *
     * @param roadWayId 巷道id
     */
    public void setRoadWayId(Long roadWayId) {
        this.roadWayId = roadWayId;
    }

    /**
     * 获取 巷道名称
     *
     * @return roadWayName 巷道名称
     */
    public String getRoadWayName() {
        return this.roadWayName;
    }

    /**
     * 设置 巷道名称
     *
     * @param roadWayName 巷道名称
     */
    public void setRoadWayName(String roadWayName) {
        this.roadWayName = roadWayName;
    }

    /**
     * 获取 拣货任务明细状态
     *
     * @return state 拣货任务明细状态
     */
    public Byte getState() {
        return this.state;
    }

    /**
     * 设置 拣货任务明细状态
     *
     * @param state 拣货任务明细状态
     */
    public void setState(Byte state) {
        this.state = state;
    }

    /**
     * 获取 销售规格
     *
     * @return saleSpecQuantity 销售规格
     */
    public BigDecimal getSaleSpecQuantity() {
        return this.saleSpecQuantity;
    }

    /**
     * 设置 销售规格
     *
     * @param saleSpecQuantity 销售规格
     */
    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }
}
