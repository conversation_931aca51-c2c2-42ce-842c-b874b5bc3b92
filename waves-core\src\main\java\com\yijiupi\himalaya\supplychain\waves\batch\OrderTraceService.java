package com.yijiupi.himalaya.supplychain.waves.batch;

import com.yijiupi.himalaya.supplychain.waves.dto.trace.MessagePushParam;
import com.yijiupi.himalaya.supplychain.waves.dto.trace.OrderTraceDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.trace.OrderTraceParamDTO;

import java.util.Collection;
import java.util.List;

public interface OrderTraceService {

    /**
     * 单条插入
     */
    void insert(OrderTraceDTO record);

    /**
     * 批量插入
     */
    void insertUpdateBatch(List<OrderTraceDTO> record);

    /**
     * 根据业务Id或No查询所有日志
     */
    List<OrderTraceDTO> selectByBusinessIdOrNo(Integer orgId, Long businessId, String businessNo);

    /**
     * 根据业务Id或No查询所有日志
     */
    @Deprecated
    List<OrderTraceDTO> selectByBusinessIdOrNo(Long businessId, String businessNo);

    /**
     * 上架任务给PDA发消息提醒
     */
    void pushPutawayTaskMsg(Integer sortId);

    /**
     * 发送移库任务消息到PDA
     */
    void pushStoreTransferMsg(Integer sortId);

    /**
     * 发送消息到PDA
     */
    void pushMsg(Integer sortId, String content);

    /**
     * 群发消息到PDA
     */
    void batchPushMsg(List<Integer> sortIds, String content);

    /**
     * 根据业务Id或No查询所有日志
     */
    List<OrderTraceDTO> listOrderTrace(OrderTraceParamDTO orderTraceParamDTO);

    /**
     * 按角色推送消息
     *
     * @param param 推送入参
     */
    void pushMessageByRole(MessagePushParam param);

    /**
     * 群发消息到 PDA
     *
     * @param param 推送入参
     */
    void batchPushMsg(MessagePushParam param);

}
