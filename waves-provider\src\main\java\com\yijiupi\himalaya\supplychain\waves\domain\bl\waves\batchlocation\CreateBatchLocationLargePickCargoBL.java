package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.ProductLocationItemQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.*;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor.LocationDivideHelperBOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor.LocationDivideHelperResultConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.RobotPickConstants;
import com.yijiupi.supplychain.serviceutils.constant.DeliveryOrderConstant;

/**
 * <AUTHOR>
 * @title: CreateBatchLocationRobotCargoBL
 * @description: 开启机器人 和 货位库存的，走此方法分配货位
 * @date 2022-11-30 17:51
 */
@Component
public class CreateBatchLocationLargePickCargoBL extends CreateBatchLocationBaseBL {

    @Autowired
    private CreateBatchLocationOpenLocationGroupBL locationOpenLocationGroupBL;
    @Autowired
    private LocationLimitDivideBL locationLimitDivideBL;

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;
    @Reference
    private ILocationService iLocationService;

    /**
     * 整件货位
     */
    public static final List<Integer> largeLocationList = Collections.singletonList(LocationEnum.存储位.getType());
    /**
     * 拆零货位
     */
    public static final List<Integer> smallLocationList =
        Arrays.asList(LocationEnum.零拣位.getType(), LocationEnum.分拣位.getType(), LocationEnum.容器位.getType());

    @Override
    public boolean support(CreateBatchLocationBO bo) {
        if (bo.getWavesStrategyDTO().getIsOpenSecondSort()) {
            return Boolean.FALSE;
        }
        if (BooleanUtils.isFalse(isOpenRobotCargo(bo))) {
            return Boolean.FALSE;
        }

        if (!ConditionStateEnum.是.getType().equals(bo.getWarehouseConfigDTO().getRobotLargePick())) {
            return Boolean.FALSE;
        }

        return doSupport(bo);
    }

    @Override
    protected boolean doSupport(CreateBatchLocationBO bo) {
        return Boolean.TRUE;
    }

    @Override
    public List<OutStockOrderPO> doSetLocation(List<OutStockOrderPO> outStockOrderList, CreateBatchLocationBO bo) {

        outStockOrderList.stream().flatMap(m -> m.getItems().stream())
            .forEach(m -> m.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_NONE));

        List<OutStockOrderPO> copyOutStockOrderList = outStockOrderList.stream().map(m -> {
            OutStockOrderPO outStockOrderPO = new OutStockOrderPO();
            BeanUtils.copyProperties(m, outStockOrderPO);
            return outStockOrderPO;
        }).collect(Collectors.toList());

        LOG.info("处理整件拆零 原始订单为： {}", JSON.toJSONString(outStockOrderList));

        List<OutStockOrderPO> delayOrderList = copyOutStockOrderList.stream().filter(
            order -> Objects.equals(order.getDeliveryMarkState(), DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue()))
            .collect(Collectors.toList());
        List<OutStockOrderPO> normalOrderList =
            copyOutStockOrderList.stream().filter(order -> !Objects.equals(order.getDeliveryMarkState(),
                DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue())).collect(Collectors.toList());

        // 按产品聚合订单项
        Map<Long, List<OutStockOrderItemPO>> skuGroupMap = normalOrderList.stream().flatMap(m -> m.getItems().stream())
            .collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid));

        List<Long> skuIds = normalOrderList.stream().flatMap(m -> m.getItems().stream())
            .map(OutStockOrderItemPO::getSkuid).distinct().collect(Collectors.toList());
        Map<Long, Byte> skuFeatureMap = findSkuConfig(skuIds, bo.getWarehouseConfigDTO());

        // 有含有产品特征需要拆零sku的订单项
        List<LocationDivideHelperBO> splitHelperList =
            LocationDivideHelperBOConvertor.convert(skuGroupMap, skuFeatureMap);

        // 不需要拆零的订单项
        List<OutStockOrderItemPO> notSplitItemList =
                LocationDivideHelperBOConvertor.convertItem(skuGroupMap, skuFeatureMap);
        // 把需要拆分的 按大小件进行了拆分
        List<LocationDivideHelperResultBO> splitResultBOList =
            splitHelperList.stream().map(CreateBatchLocationLargePickCargoBL::divideCount).collect(Collectors.toList());

        // 小件的走下架策略
        List<OutStockOrderItemPO> unitItemList =
            splitResultBOList.stream().map(LocationDivideHelperResultBO::getUnitItemList)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        // 大件要拆出一个走通道
        List<OutStockOrderItemPO> packageItemList =
            splitResultBOList.stream().map(LocationDivideHelperResultBO::getPackageItemList)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());

        // 这里改成 全走下架策略，然后到后面把整件的再安排存储位，省的拆合的逻辑 TODO

        // 小件获取货位信息
        List<LocationSplitHelperResultBO> splitUnitResultBOList =
            handleUnitOrderList(unitItemList, normalOrderList, bo);

        // LOG.info("处理整件拆零 splitUnitResultBOList {}", JSON.toJSONString(splitUnitResultBOList));

        List<OutStockOrderItemPO> totalUnitItemList =
                splitUnitResultBOList.stream().filter(m -> CollectionUtils.isNotEmpty(m.getUnitItemList())).flatMap(m -> m.getUnitItemList().stream()).collect(Collectors.toList());

        // 设置货位
        packageItemList = handleSinglePackageLocation(packageItemList, normalOrderList, bo);

        // LOG.info("处理整件拆零 packageItemList {}", JSON.toJSONString(packageItemList));

        List<OutStockOrderItemPO> totalPackageItemList =
            getAllPackageItemList(normalOrderList, packageItemList, splitUnitResultBOList, bo);

        // LOG.info("处理整件拆零 totalPackageItemList {}", JSON.toJSONString(totalPackageItemList));

        List<OutStockOrderPO> totalUnitOrderList = buildNewOrderList(normalOrderList, totalUnitItemList);
        List<OutStockOrderPO> totalPackageOrderList = buildNewOrderList(normalOrderList, totalPackageItemList);

        // 小件的设置货位
        // totalUnitOrderList = handleDelayList(totalUnitOrderList, bo);
        // 大件的设置货位，这里统一先设置成 零拣位 和 分拣位，等到进通道的时候，再处理存储区的存储位，省的 拆分再合并了

        List<OutStockOrderPO> delayStrategyOrderList = handleDelayList(delayOrderList, bo);

        // 处理不用拆分的订单
        List<OutStockOrderPO> notSplitOrderList = buildNewOrderList(normalOrderList, notSplitItemList);
        List<OutStockOrderPO> notSplitOrderLocationList = handleDelayList(notSplitOrderList, bo);

        LOG.info(
            "整合后数量为：unitStrategyOrderList : {}; packageRecommendOrderList : {}; notSplitOrderLocationList : {}，delayStrategyOrderList : {}",
            JSON.toJSONString(totalUnitOrderList), JSON.toJSONString(totalPackageOrderList),
            JSON.toJSONString(notSplitOrderLocationList), JSON.toJSONString(delayStrategyOrderList));

        // packageOrderList 和 unitOrderList 数量比较校验，如果不对，还用老逻辑
        // if (BooleanUtils
        // .isFalse(validateCountShareIsLegal(packageRecommendOrderList, unitStrategyOrderList, splitHelperList))) {
        // LOG.warn(
        // "整件拆零分摊数量失败，走降级逻辑 setLocationByOutStockStrategyRule , 分摊数量为：packageOrderList : {}, unitOrderList : {}",
        // JSON.toJSONString(packageRecommendOrderList), JSON.toJSONString(unitStrategyOrderList));
        //
        // return setLocationByOutStockStrategyRule(outStockOrderList, delayOrderList, bo);
        // }

        return mergeOrderList(outStockOrderList, totalUnitOrderList, totalPackageOrderList, delayStrategyOrderList,
            notSplitOrderLocationList);
    }

    private List<OutStockOrderItemPO> handleSinglePackageLocation(List<OutStockOrderItemPO> packageItemList,
        List<OutStockOrderPO> normalOrderList, CreateBatchLocationBO bo) {
        if (CollectionUtils.isEmpty(packageItemList)) {
            return Collections.emptyList();
        }
        List<OutStockOrderPO> packageOrderList = buildNewOrderList(normalOrderList, packageItemList);
        if (CollectionUtils.isEmpty(packageOrderList)) {
            return Collections.emptyList();
        }
        List<OutStockOrderPO> outStockOrderPOList = locationOpenLocationGroupBL.doSetLocation(packageOrderList, bo);
        return outStockOrderPOList.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList());
    }

    private List<OutStockOrderItemPO> getTotalUnitItemList(List<LocationSplitHelperResultBO> splitUnitResultBOList,
        LocationPackageHelperBO packageHelperBO, LocationPackageHelperBO packageRecommendHelper) {
        List<OutStockOrderItemPO> totalUnitItemList = new ArrayList<>();
        // 已经有货位的,小件
        List<OutStockOrderItemPO> unitGroupItemList =
            splitUnitResultBOList.stream().flatMap(m -> m.getUnitItemList().stream()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unitGroupItemList)) {
            totalUnitItemList.addAll(unitGroupItemList);
        }
        List<OutStockOrderItemPO> packageUnitItemList = packageHelperBO.getUnitItemList();
        if (CollectionUtils.isNotEmpty(packageUnitItemList)) {
            totalUnitItemList.addAll(packageUnitItemList);
        }

        if (CollectionUtils.isNotEmpty(packageRecommendHelper.getUnitItemList())) {
            totalUnitItemList.addAll(packageRecommendHelper.getUnitItemList());
        }

        return totalUnitItemList;
    }

    private List<OutStockOrderItemPO> getAllPackageItemList(List<OutStockOrderPO> normalOrderList,
        List<OutStockOrderItemPO> packageItemList, List<LocationSplitHelperResultBO> splitUnitResultBOList,
        CreateBatchLocationBO bo) {
        List<OutStockOrderItemPO> totalPackageItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(packageItemList)) {
            // List<OutStockOrderPO> packageOrderList = buildNewOrderList(normalOrderList, packageItemList);
            // packageOrderList = handleDelayList(packageOrderList, bo);
            //
            // totalPackageItemList
            // .addAll(packageOrderList.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList()));
            totalPackageItemList.addAll(packageItemList);
        }

        List<OutStockOrderItemPO> unitGroupPackageItemList =
            splitUnitResultBOList.stream().flatMap(m -> m.getPackageItemList().stream()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unitGroupPackageItemList)) {
            // List<OutStockOrderPO> limitItemOrderList = buildNewOrderList(normalOrderList, unitGroupPackageItemList);
            // limitItemOrderList = handleDelayList(limitItemOrderList, bo);
            //
            // totalPackageItemList
            // .addAll(limitItemOrderList.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList()));
            totalPackageItemList.addAll(unitGroupPackageItemList);
        }

        return totalPackageItemList;
    }

    private List<OutStockOrderPO> handleDelayList(List<OutStockOrderPO> delayOrderList, CreateBatchLocationBO bo) {
        if (CollectionUtils.isEmpty(delayOrderList)) {
            return Collections.emptyList();
        }
        return locationOpenLocationGroupBL.doSetLocation(delayOrderList, bo);
    }

    /**
     * 获取货位信息，并按skuId聚合，去查看相应货位的库存上下限是否满足要求，并进行拆分
     *
     * @param unitItemList
     * @param normalOrderList
     * @param bo
     * @return
     */
    public List<LocationSplitHelperResultBO> handleUnitOrderList(List<OutStockOrderItemPO> unitItemList,
                                                                 List<OutStockOrderPO> normalOrderList, CreateBatchLocationBO bo) {
        if (CollectionUtils.isEmpty(unitItemList)) {
            return new ArrayList<>();
        }

        List<OutStockOrderPO> unitOrderList = buildNewOrderDetailList(normalOrderList, unitItemList);
        if (CollectionUtils.isEmpty(unitOrderList)) {
            return new ArrayList<>();
        }

        Map<Long, List<OutStockOrderItemPO>> unitGroupItemList = unitOrderList.stream()
            .flatMap(m -> m.getItems().stream()).collect(Collectors.groupingBy(OutStockOrderItemPO::getId));

        List<OutStockOrderItemPO> singleItemList = new ArrayList<>();
        List<OutStockOrderItemPO> multiItemList = new ArrayList<>();

        for (Map.Entry<Long, List<OutStockOrderItemPO>> entry : unitGroupItemList.entrySet()) {
            if (entry.getValue().size() == 1) {
                singleItemList.addAll(entry.getValue());
            } else {
                multiItemList.addAll(entry.getValue());
            }
        }

        List<OutStockOrderPO> singleUnitOrderList = buildNewOrderList(normalOrderList, singleItemList);
        List<OutStockOrderPO> multiUnitOrderList = multiItemList.stream()
            .flatMap(m -> buildNewOrderList(normalOrderList, Collections.singletonList(m)).stream())
            .collect(Collectors.toList());

        List<OutStockOrderPO> totalLocationOrderList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(singleUnitOrderList)) {
            List<OutStockOrderPO> locationOrderList =
                locationOpenLocationGroupBL.setSplitLocation(singleUnitOrderList, bo);
            totalLocationOrderList.addAll(locationOrderList);
        }
        // TODO 优化
        if (CollectionUtils.isNotEmpty(multiUnitOrderList)) {
            totalLocationOrderList.addAll(multiUnitOrderList.stream()
                .map(m -> locationOpenLocationGroupBL.setSplitLocation(Collections.singletonList(m), bo))
                .flatMap(Collection::stream).collect(Collectors.toList()));
        }

        List<OutStockOrderItemPO> locationOrderItemList =
            totalLocationOrderList.stream().flatMap(order -> order.getItems().stream()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationOrderItemList)) {
            return new ArrayList<>();
        }

        LOG.info("处理整件拆零 locationOrderItemList {}", JSON.toJSONString(locationOrderItemList));
        // 小件分配完货位后，将订单项产品数量汇总出来，与原始拣货货位的补货上限比较，超过上限则转换成大件规格，将转换后的整件数量汇总出来，生成多项整件拣货任务；
        return locationLimitDivideBL.divideByLimit(bo, locationOrderItemList);
    }

    private List<OutStockOrderPO> mergeOrderList(List<OutStockOrderPO> outStockOrderList,
        List<OutStockOrderPO> strategyOrderList, List<OutStockOrderPO> recommendOrderList,
        List<OutStockOrderPO> delayOrderList, List<OutStockOrderPO> notSplitOrderLocationList) {
        List<OutStockOrderPO> totalOrderList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(strategyOrderList)) {
            totalOrderList.addAll(strategyOrderList);
        }

        if (CollectionUtils.isNotEmpty(recommendOrderList)) {
            totalOrderList.addAll(recommendOrderList);
        }

        if (CollectionUtils.isNotEmpty(delayOrderList)) {
            totalOrderList.addAll(delayOrderList);
        }

        if (CollectionUtils.isNotEmpty(notSplitOrderLocationList)) {
            totalOrderList.addAll(notSplitOrderLocationList);
        }

        if (CollectionUtils.isEmpty(totalOrderList)) {
            return outStockOrderList;
        }

        List<OutStockOrderItemPO> itemList =
            totalOrderList.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList());
        Map<Long, List<OutStockOrderItemPO>> allResultLocationMap =
            itemList.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getId));
        for (int s = outStockOrderList.size() - 1; s >= 0; s--) {
            try {
                OutStockOrderPO outStockOrderPO = outStockOrderList.get(s);
                List<OutStockOrderItemPO> lstOrderItems = outStockOrderPO.getItems();
                List<OutStockOrderItemPO> newItemPo = Lists.newArrayList();
                for (OutStockOrderItemPO orderItemPo : lstOrderItems) {
                    try {
                        newItemPo.addAll(allResultLocationMap.get(orderItemPo.getId()));
                    } catch (Exception e) {
                        LOG.info("出现异常 订单为：" + JSON.toJSONString(outStockOrderList.get(s)) + "；订单项id 是："
                            + orderItemPo.getId(), e);
                        throw e;
                    }
                }
                outStockOrderPO.setItems(newItemPo);

            } catch (Exception e) {
                LOG.info("出现异常 订单为：{}" + JSON.toJSONString(outStockOrderList.get(s)), e);
                throw e;
            }
        }
        return outStockOrderList;
    }

    private List<OutStockOrderPO> buildNewOrderList(List<OutStockOrderPO> normalOrderList,
                                                    List<OutStockOrderItemPO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return Collections.emptyList();
        }
        Map<Long, List<OutStockOrderItemPO>> groupMap =
                itemList.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getOutstockorderId));
        Map<Long, OutStockOrderPO> orderMap =
                normalOrderList.stream().collect(Collectors.toMap(OutStockOrderPO::getId, v -> v));

        List<OutStockOrderPO> orderList = new ArrayList<>();
        for (Map.Entry<Long, List<OutStockOrderItemPO>> entry : groupMap.entrySet()) {
            OutStockOrderPO outStockOrderPO = orderMap.get(entry.getKey());
            OutStockOrderPO newOrder = new OutStockOrderPO();
            BeanUtils.copyProperties(outStockOrderPO, newOrder);

            newOrder.setItems(entry.getValue());
            orderList.add(newOrder);
        }

        return orderList;
    }

    /**
     * 小件的订单项重新组装成订单 这里直接把他按货主进行拆分
     *
     * @param normalOrderList
     * @param itemList
     * @return
     */
    private List<OutStockOrderPO> buildNewOrderDetailList(List<OutStockOrderPO> normalOrderList,
                                                          List<OutStockOrderItemPO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return Collections.emptyList();
        }
        Map<Long, List<OutStockOrderItemPO>> groupMap =
                itemList.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getOutstockorderId));
        Map<Long, OutStockOrderPO> orderMap =
                normalOrderList.stream().collect(Collectors.toMap(OutStockOrderPO::getId, v -> v));

        List<OutStockOrderPO> orderList = new ArrayList<>();
        for (Map.Entry<Long, List<OutStockOrderItemPO>> entry : groupMap.entrySet()) {
            OutStockOrderPO outStockOrderPO = orderMap.get(entry.getKey());
            OutStockOrderPO newOrder = new OutStockOrderPO();
            BeanUtils.copyProperties(outStockOrderPO, newOrder);

            newOrder.setItems(entry.getValue());
            orderList.add(newOrder);
        }
        // 按订单项明细的货主进行拆分
        setOutStockOrderItemSplitDetail(orderList);

        return orderList;
    }

    /**
     * 先分摊大件到各个订单项
     *
     * @param bo
     * @return
     */
    public static LocationDivideHelperResultBO divideCount(LocationDivideHelperBO bo) {
        // 把两种极端的排除
        if (bo.getUnitCount().compareTo(BigDecimal.ZERO) == 0) {
            return LocationDivideHelperResultConvertor.convertPackage(bo);
        }
        if (bo.getPackageCount().compareTo(BigDecimal.ZERO) == 0) {
            return LocationDivideHelperResultConvertor.convertUnit(bo);
        }

        // 有大件有小件的是需要分摊的
        // 1、规格6， 买了4和3的；2、规格6，买了6和1的
        return LocationDivideHelperResultConvertor.convertDivide(bo);
    }

    public static boolean validateCountShareIsLegal(List<OutStockOrderPO> packageOrderList,
                                                    List<OutStockOrderPO> unitOrderList, List<LocationDivideHelperBO> splitHelperList) {
        // 验证item的 和 详情的
        List<OutStockOrderItemPO> itemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(packageOrderList)) {
            itemList.addAll(packageOrderList.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(unitOrderList)) {
            itemList.addAll(unitOrderList.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList()));
        }

        if (CollectionUtils.isEmpty(itemList)) {
            LOG.warn("我是空的");
        }

        List<OutStockOrderItemPO> oriItemList =
                splitHelperList.stream().flatMap(m -> m.getItemList().stream()).collect(Collectors.toList());
        Map<Long, BigDecimal> rebuildItemMap =
                itemList.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid,
                        Collectors.reducing(BigDecimal.ZERO, OutStockOrderItemPO::getUnittotalcount, BigDecimal::add)));
        Map<Long, BigDecimal> oriItemMap =
                oriItemList.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid,
                        Collectors.reducing(BigDecimal.ZERO, OutStockOrderItemPO::getUnittotalcount, BigDecimal::add)));

        for (Map.Entry<Long, BigDecimal> entry : rebuildItemMap.entrySet()) {
            if (oriItemMap.get(entry.getKey()).compareTo(entry.getValue()) != 0) {
                LOG.warn("数量不一致 rebuildItemMap ：sku : {}, 原始数量： {} 分摊后数量：{}", entry.getKey(),
                        rebuildItemMap.get(entry.getKey()), entry.getValue());
                return Boolean.FALSE;
            }
        }

        // 暂时不验证明细数量。因为去走下架策略分配库存的时候，也会对订单项进行拆分。这个时候，这两个订单项，detailId，id，locationId 全部一致，
        // 无法对itemDetail再进行拆分成跟item数量一致

        // Map<Long, BigDecimal> rebuildDetailMap = itemList.stream().flatMap(m -> m.getItemDetails().stream())
        // .collect(Collectors.groupingBy(OutStockOrderItemDetailPO::getOutStockOrderItemId,
        // Collectors.reducing(BigDecimal.ZERO, OutStockOrderItemDetailPO::getUnitTotalCount, BigDecimal::add)));
        // Map<Long, BigDecimal> oriDetailMap = oriItemList.stream().flatMap(m -> m.getItemDetails().stream())
        // .collect(Collectors.groupingBy(OutStockOrderItemDetailPO::getOutStockOrderItemId,
        // Collectors.reducing(BigDecimal.ZERO, OutStockOrderItemDetailPO::getUnitTotalCount, BigDecimal::add)));
        //
        // for (Map.Entry<Long, BigDecimal> entry : rebuildDetailMap.entrySet()) {
        // if (oriDetailMap.get(entry.getKey()).compareTo(entry.getValue()) != 0) {
        // LOG.warn("数量不一致：sku : {}, 原始数量： {} 分摊后数量：{}", entry.getKey(), oriDetailMap.get(entry.getKey()),
        // entry.getValue());
        // return Boolean.FALSE;
        // }
        // }

        return Boolean.TRUE;
    }

    private Map<Long, Byte> findSkuConfig(List<Long> skuIds, WarehouseConfigDTO warehouseConfigDTO) {
        List<ProductSkuDTO> skuList =
                iProductSkuQueryService.findSkuInfoWithConfig(warehouseConfigDTO.getWarehouse_Id(), skuIds);
        if (org.springframework.util.CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyMap();
        }

        LOG.info("整件拆零，查询产品特征为：{}", JSON.toJSONString(skuList));

        return skuList.stream().filter(m -> Objects.nonNull(m.getProductFeature()))
                .collect(Collectors.toMap(ProductSkuDTO::getProductSkuId, ProductSkuDTO::getProductFeature));
    }

    private List<OutStockOrderItemPO> getHaveSetLargePickItemList(List<OutStockOrderItemPO> lstItems,
                                                                  WavesStrategyDTO wavesStrategyDTO, WarehouseConfigDTO warehouseConfigDTO) {
        if (BooleanUtils.isFalse(RobotPickConstants.isWarehouseOpenLargePick(wavesStrategyDTO, warehouseConfigDTO))) {
            return Collections.emptyList();
        }

        // 给没开启货位库存的产品设置最老的生产日期和批次时间
        // setProductDateAndBatchTime(packageOrderList);
        // 将订单项按详细拆分
        // setOutStockOrderItemSplitDetail(packageOrderList);

        List<OutStockOrderItemPO> packageOrderItemList =
                lstItems.stream().filter(m -> BatchTaskItemLargePickPatternConstants.isLargePick(m.getLargePick()))
                        .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(packageOrderItemList)) {
            return packageOrderItemList;
        }

        return Collections.emptyList();
    }

    /**
     * 设置分拣位和存储位，不看是否有货位库存了
     *
     * @param lstItems           在通道的订单项
     * @param totalItemList      所有的订单项（移除了已经进拣货任务的订单项），用来移除订单项的。
     * @param wavesStrategyDTO
     * @param warehouseConfigDTO
     * @return
     */
    public List<OutStockOrderItemPO> setPackageLocationByAssociateLocation(List<OutStockOrderItemPO> lstItems,
                                                                           List<OutStockOrderItemPO> totalItemList, WavesStrategyDTO wavesStrategyDTO,
                                                                           WarehouseConfigDTO warehouseConfigDTO) {
        List<OutStockOrderItemPO> packageOrderItemList =
                getHaveSetLargePickItemList(lstItems, wavesStrategyDTO, warehouseConfigDTO);
        if (CollectionUtils.isEmpty(packageOrderItemList)) {
            return lstItems;
        }

        Map<Long, ProductLoactionItemDTO> locationMap =
                getAssociateLocationMap(warehouseConfigDTO, packageOrderItemList);

        LOG.info("查询分拣位信息为：orderItemLocationList {}", JSON.toJSONString(packageOrderItemList));

        List<OutStockOrderItemPO> storeLocationExistItemList = packageOrderItemList.stream()
                .filter(m -> Objects.nonNull(locationMap.get(m.getSkuid()))).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(storeLocationExistItemList)) {
            storeLocationExistItemList.forEach(m -> {
                ProductLoactionItemDTO itemDTO = locationMap.get(m.getSkuid());
                if (Objects.nonNull(itemDTO.getLocationId())) {
                    m.setLocationId(itemDTO.getLocationId());
                    m.setLocationName(itemDTO.getLocationName());
                    m.setSubCategory(itemDTO.getSubcategory());
                    m.setAreaId(itemDTO.getAreaId());
                    m.setAreaName(itemDTO.getAreaName());
                }
            });
        }

        LOG.info("重新设置分拣位之后的订单项为：{}", JSON.toJSONString(packageOrderItemList));
        List<OutStockOrderItemPO> passageItemList = lstItems.stream().filter(m -> {
            if (!(BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick())
                    && largeLocationList.contains(m.getSubCategory().intValue()))) {
                return Boolean.TRUE;
            }

            return Boolean.FALSE;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(passageItemList)) {
            return lstItems;
        }

        passageItemList.forEach(item -> {
            totalItemList.removeIf(m -> m.getId().equals(item.getId()) && item.getLocationId().equals(m.getLocationId())
                    && m.getItemDetailId().equals(item.getItemDetailId()) && m.getLargePick().equals(item.getLargePick()));
            totalItemList.add(item);
        });

        return lstItems;
    }

    /**
     * 设置分拣位和存储位，不看是否有货位库存了
     *
     * @param totalItemList
     * @param wavesStrategyDTO
     * @param warehouseConfigDTO
     * @param warehouseId
     * @return
     */
    public List<OutStockOrderItemPO> setTotalItemPackageLocationByAssociateLocation(
            List<OutStockOrderItemPO> totalItemList, WavesStrategyDTO wavesStrategyDTO,
            WarehouseConfigDTO warehouseConfigDTO, Integer warehouseId) {
        List<OutStockOrderItemPO> packageOrderItemList =
                getHaveSetLargePickItemList(totalItemList, wavesStrategyDTO, warehouseConfigDTO);
        if (CollectionUtils.isEmpty(packageOrderItemList)) {
            return totalItemList;
        }

        Map<Long, ProductLoactionItemDTO> locationMap =
                getAssociateLocationMap(warehouseConfigDTO, packageOrderItemList);

        LOG.info("查询分拣位信息为：orderItemLocationList {}", JSON.toJSONString(packageOrderItemList));

        List<OutStockOrderItemPO> storeLocationExistItemList = packageOrderItemList.stream()
                .filter(m -> Objects.nonNull(locationMap.get(m.getSkuid()))).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(storeLocationExistItemList)) {
            storeLocationExistItemList.forEach(m -> {
                ProductLoactionItemDTO itemDTO = locationMap.get(m.getSkuid());
                if (Objects.nonNull(itemDTO.getLocationId())) {
                    m.setLocationId(itemDTO.getLocationId());
                    m.setLocationName(itemDTO.getLocationName());
                    m.setSubCategory(itemDTO.getSubcategory());
                    m.setAreaId(itemDTO.getAreaId());
                    m.setAreaName(itemDTO.getAreaName());
                }
            });
        }

        LOG.info("重新设置分拣位之后的订单项为：{}", JSON.toJSONString(packageOrderItemList));
        List<OutStockOrderItemPO> packageItemList = totalItemList.stream().filter(m -> {
            if (!(BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick())
                    && largeLocationList.contains(m.getSubCategory().intValue()))) {
                return Boolean.TRUE;
            }

            return Boolean.FALSE;
        }).collect(Collectors.toList());

        return packageItemList;
    }

    /**
     * 直接获取关联货位
     *
     * @param warehouseConfigDTO
     * @param packageOrderItemList
     * @return
     */
    private Map<Long, ProductLoactionItemDTO> getAssociateLocationMap(WarehouseConfigDTO warehouseConfigDTO,
                                                                      List<OutStockOrderItemPO> packageOrderItemList) {
        List<Long> productSkuIds =
                packageOrderItemList.stream().map(OutStockOrderItemPO::getSkuid).distinct().collect(Collectors.toList());

        List<ProductLoactionItemDTO> totalProductLocationList = new ArrayList<>();
        Lists.partition(productSkuIds, 250).forEach(skuIds -> {
            ProductLocationItemQueryDTO queryDTO = new ProductLocationItemQueryDTO();
            queryDTO.setSubcategorys(Arrays.asList(LocationAreaEnum.存储区.getType(), LocationAreaEnum.拣货区.getType(),
                    LocationAreaEnum.零拣区.getType()));
            queryDTO.setProductSkuIds(productSkuIds);
            queryDTO.setWarehouseId(warehouseConfigDTO.getWarehouse_Id());
            List<ProductLoactionItemDTO> productLocationList = iProductLocationService.findLocationItemByCon(queryDTO);

            if (CollectionUtils.isNotEmpty(productLocationList)) {
                totalProductLocationList.addAll(productLocationList);
            }
        });

        if (CollectionUtils.isEmpty(totalProductLocationList)) {
            return Collections.emptyMap();
        }

        return getLocationMap(totalProductLocationList);
    }

    private Map<Long, ProductLoactionItemDTO> getLocationMap(List<ProductLoactionItemDTO> productLocationList) {
        Map<Long, ProductLoactionItemDTO> locationMap = new HashMap<>();
        Map<Long, List<ProductLoactionItemDTO>> locationGroupMap =
                productLocationList.stream().collect(Collectors.groupingBy(ProductLoactionItemDTO::getProductSkuId));

        for (Map.Entry<Long, List<ProductLoactionItemDTO>> entry : locationGroupMap.entrySet()) {
            // entry.getKey();
            ProductLoactionItemDTO locationItemDTO = getMatchLocation(entry.getValue());
            if (Objects.nonNull(locationItemDTO)) {
                locationMap.put(entry.getKey(), locationItemDTO);
            }
        }

        return locationMap;
    }

    private ProductLoactionItemDTO getMatchLocation(List<ProductLoactionItemDTO> locationList) {
        Optional<ProductLoactionItemDTO> fenPickLocationOptional =
                locationList.stream().filter(m -> LocationEnum.分拣位.getType() == m.getSubcategory().intValue()).findAny();
        if (fenPickLocationOptional.isPresent()) {
            return fenPickLocationOptional.get();
        }

        Optional<ProductLoactionItemDTO> storePickLocationOptional =
                locationList.stream().filter(m -> LocationEnum.存储位.getType() == m.getSubcategory().intValue()).findAny();

        return storePickLocationOptional.orElse(null);
    }

}
