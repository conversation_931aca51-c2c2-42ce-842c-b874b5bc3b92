package com.yijiupi.himalaya.supplychain.waves.schedule.model;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-11-27 14:43
 **/
public class PickerEfficiencyBO {

    private Integer userId;

    private BigDecimal averageCount;

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public BigDecimal getAverageCount() {
        return averageCount;
    }

    public void setAverageCount(BigDecimal averageCount) {
        this.averageCount = averageCount;
    }
}
