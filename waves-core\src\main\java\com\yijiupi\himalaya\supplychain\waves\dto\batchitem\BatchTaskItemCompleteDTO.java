package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageCodeSortDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.<br />
 * copy BatchTaskItemUpdateDTO
 * 
 * <AUTHOR>
 * @date 2024/1/12
 */
public class BatchTaskItemCompleteDTO implements Serializable {
    /**
     * 波次任务详情id
     */
    private String id;
    /**
     * 已拣货大数量
     */
    private BigDecimal overSortPackageCount;
    /**
     * 已拣货小数量
     */
    private BigDecimal overSortUnitCount;
    /**
     * 缺省大数量
     */
    private BigDecimal lackPackageCount;

    /**
     * 缺省小数量
     */
    private BigDecimal lackUnitCount;

    /**
     * 箱号
     */
    private String boxCode;

    /**
     * 订单序号
     */
    private Integer orderSequence;

    /**
     * 来源货位id
     */
    private Long fromLocationId;
    /**
     * 来源货位名称
     */
    private String fromLocationName;

    /**
     * 多箱号
     */
    private List<PackageCodeSortDTO> boxCodeList;

    /**
     * 提交标识 0：初次提交 1：修改提交
     */
    private Byte submitFlag;

    /**
     * 开始拣货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 完成拣货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeTime;

    /**
     * 拣货容器位
     */
    private List<BatchTaskItemContainerDTO> containerList;

    public List<BatchTaskItemContainerDTO> getContainerList() {
        return containerList;
    }

    public void setContainerList(List<BatchTaskItemContainerDTO> containerList) {
        this.containerList = containerList;
    }

    /**
     * 批次入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date batchTime;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 备注
     */
    private String remark;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public Byte getSubmitFlag() {
        return submitFlag;
    }

    public void setSubmitFlag(Byte submitFlag) {
        this.submitFlag = submitFlag;
    }

    public List<PackageCodeSortDTO> getBoxCodeList() {
        return boxCodeList;
    }

    public void setBoxCodeList(List<PackageCodeSortDTO> boxCodeList) {
        this.boxCodeList = boxCodeList;
    }

    public Long getFromLocationId() {
        return fromLocationId;
    }

    public void setFromLocationId(Long fromLocationId) {
        this.fromLocationId = fromLocationId;
    }

    public String getFromLocationName() {
        return fromLocationName;
    }

    public void setFromLocationName(String fromLocationName) {
        this.fromLocationName = fromLocationName;
    }

    public Integer getOrderSequence() {
        return orderSequence;
    }

    public void setOrderSequence(Integer orderSequence) {
        this.orderSequence = orderSequence;
    }

    /**
     * 获取 波次任务详情id
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 波次任务详情id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 已拣货大数量
     */
    public BigDecimal getOverSortPackageCount() {
        return this.overSortPackageCount;
    }

    /**
     * 设置 已拣货大数量
     */
    public void setOverSortPackageCount(BigDecimal overSortPackageCount) {
        this.overSortPackageCount = overSortPackageCount;
    }

    /**
     * 获取 已拣货小数量
     */
    public BigDecimal getOverSortUnitCount() {
        return this.overSortUnitCount;
    }

    /**
     * 设置 已拣货小数量
     */
    public void setOverSortUnitCount(BigDecimal overSortUnitCount) {
        this.overSortUnitCount = overSortUnitCount;
    }

    /**
     * 获取 缺省小数量
     */
    public BigDecimal getLackUnitCount() {
        return this.lackUnitCount;
    }

    /**
     * 设置 缺省小数量
     */
    public void setLackUnitCount(BigDecimal lackUnitCount) {
        this.lackUnitCount = lackUnitCount;
    }

    /**
     * 获取 缺省大数量
     */
    public BigDecimal getLackPackageCount() {
        return this.lackPackageCount;
    }

    /**
     * 设置 缺省大数量
     */
    public void setLackPackageCount(BigDecimal lackPackageCount) {
        this.lackPackageCount = lackPackageCount;
    }

    public String getBoxCode() {
        return boxCode;
    }

    public void setBoxCode(String boxCode) {
        this.boxCode = boxCode;
    }

    public static final Byte SUBMIT_FLAG_FIRST = 1;

    public static final Byte SUBMIT_FLAG_SECOND = 2;

}
