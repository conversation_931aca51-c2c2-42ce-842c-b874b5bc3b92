package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 拣货分组策略 常量类
 *
 * <AUTHOR> 2018/3/13
 */
public enum TaskStateEnum {
    /**
     * 枚举
     */
    未分拣((byte)0), 分拣中((byte)1), 已完成((byte)2), 已作废((byte)3);

    /**
     * type
     */
    private final Byte type;

    TaskStateEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    public boolean valueEquals(Number number) {
        return number != null && this.type.equals(number.byteValue());
    }

    /**
     * 返回枚举
     */
    public static TaskStateEnum getEnum(Integer type) {
        TaskStateEnum e = null;

        if (type != null) {
            for (TaskStateEnum o : values()) {
                if (o.getType() == type) {
                    e = o;
                }
            }
        }
        return e;
    }

    public static String getType(Byte type) {
        if (type == null) {
            return null;
        }
        for (TaskStateEnum o : values()) {
            if (o.getType() == type) {
                return o.name();
            }
        }
        return null;
    }
}
