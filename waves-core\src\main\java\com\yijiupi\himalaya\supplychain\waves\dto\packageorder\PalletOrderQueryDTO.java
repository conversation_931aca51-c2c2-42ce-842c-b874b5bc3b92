package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yijiupi.himalaya.base.search.PageCondition;

public class PalletOrderQueryDTO extends PageCondition {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库Id
     */
    private Integer warehouseId;

    /**
     * 托盘号
     */
    private String boxCodeNo;

    /**
     * 复核状态
     */
    private Byte reviewState;

    /**
     * 包装类型
     */
    private Byte packageType;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getBoxCodeNo() {
        return boxCodeNo;
    }

    public void setBoxCodeNo(String boxCodeNo) {
        this.boxCodeNo = boxCodeNo;
    }

    public Byte getReviewState() {
        return reviewState;
    }

    public void setReviewState(Byte reviewState) {
        this.reviewState = reviewState;
    }

    public Byte getPackageType() {
        return packageType;
    }

    public void setPackageType(Byte packageType) {
        this.packageType = packageType;
    }
}
