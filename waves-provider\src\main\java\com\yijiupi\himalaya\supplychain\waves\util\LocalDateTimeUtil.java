package com.yijiupi.himalaya.supplychain.waves.util;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

import com.alibaba.dubbo.common.utils.StringUtils;

/**
 * 日期类
 *
 * <AUTHOR>
 * @date 2/7/21 5:59 PM
 */
public class LocalDateTimeUtil {

    /**
     * 获取指定日期的截止时间
     * 
     * @return
     */
    public static Date getTodayDeadline(Date date, String deadLine) {
        if (StringUtils.isEmpty(deadLine)) {
            return null;
        }
        // 默认获取当天日期
        LocalDateTime now = LocalDateTime.now();
        if (date != null) {
            now = Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
        }

        // 指定时分秒
        int hour = 0;
        int minute = 0;
        int second = 0;
        String[] arrry = deadLine.split(":");
        if (arrry.length > 0) {
            hour = Integer.valueOf(arrry[0]);
        }
        if (arrry.length > 1) {
            minute = Integer.valueOf(arrry[1]);
        }
        if (arrry.length > 2) {
            second = Integer.valueOf(arrry[2]);
        }
        LocalDateTime localDateTime =
            LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), hour, minute, second);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取指定日期的前一天
     * 
     * @return
     */
    public static Date getBeforeDay(Date date) {
        if (date == null) {
            return null;
        }
        LocalDateTime localDateTime =
            Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
        localDateTime = localDateTime.minusDays(1);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取指定日期的后一天
     * 
     * @return
     */
    public static Date getAfterDay(Date date) {
        if (date == null) {
            return null;
        }
        LocalDateTime localDateTime =
            Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
        localDateTime = localDateTime.plusDays(1);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

}
