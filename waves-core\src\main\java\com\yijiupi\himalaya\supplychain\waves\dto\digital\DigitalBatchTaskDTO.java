package com.yijiupi.himalaya.supplychain.waves.dto.digital;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/10/30
 */
public class DigitalBatchTaskDTO implements Serializable {
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 拣货任务号
     */
    private String batchTaskNo;
    /**
     * 拣货任务id
     */
    private String batchTaskId;
    /**
     * 出库位id
     */
    private Long toLocationId;
    /**
     * 出库位名称
     */
    private String toLocationName;

    /**
     * 拣货任务明细列表
     */
    private List<DigitalBatchTaskItemDTO> itemList;
    /**
     * 分区id
     */
    private Long sortGroupId;
    /**
     * 拣货任务状态
     */
    private Byte taskState;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 播种任务id
     */
    private Long sowTaskId;
    /**
     * 播种号
     */
    private String sowTaskNo;
    /**
     * 序号
     */
    private Integer sequence;
    /**
     * 拣货任务名称
     */
    private String batchTaskName;
    /**
     * 波次订单类型
     * 
     * @see com.yijiupi.himalaya.supplychain.waves.enums.BatchOrderTypeEnum
     */
    private Integer batchOrderType;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 拣货任务号
     *
     * @return batchTaskNo 拣货任务号
     */
    public String getBatchTaskNo() {
        return this.batchTaskNo;
    }

    /**
     * 设置 拣货任务号
     *
     * @param batchTaskNo 拣货任务号
     */
    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    /**
     * 获取 拣货任务id
     *
     * @return batchTaskId 拣货任务id
     */
    public String getBatchTaskId() {
        return this.batchTaskId;
    }

    /**
     * 设置 拣货任务id
     *
     * @param batchTaskId 拣货任务id
     */
    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    /**
     * 获取 拣货任务明细列表
     *
     * @return itemList 拣货任务明细列表
     */
    public List<DigitalBatchTaskItemDTO> getItemList() {
        return this.itemList;
    }

    /**
     * 设置 拣货任务明细列表
     *
     * @param itemList 拣货任务明细列表
     */
    public void setItemList(List<DigitalBatchTaskItemDTO> itemList) {
        this.itemList = itemList;
    }

    /**
     * 获取 出库位id
     *
     * @return toLocationId 出库位id
     */
    public Long getToLocationId() {
        return this.toLocationId;
    }

    /**
     * 设置 出库位id
     *
     * @param toLocationId 出库位id
     */
    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    /**
     * 获取 出库位名称
     *
     * @return toLocationName 出库位名称
     */
    public String getToLocationName() {
        return this.toLocationName;
    }

    /**
     * 设置 出库位名称
     *
     * @param toLocationName 出库位名称
     */
    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    /**
     * 获取 分区id
     *
     * @return sortGroupId 分区id
     */
    public Long getSortGroupId() {
        return this.sortGroupId;
    }

    /**
     * 设置 分区id
     *
     * @param sortGroupId 分区id
     */
    public void setSortGroupId(Long sortGroupId) {
        this.sortGroupId = sortGroupId;
    }

    /**
     * 获取 拣货任务状态
     *
     * @return taskState 拣货任务状态
     */
    public Byte getTaskState() {
        return this.taskState;
    }

    /**
     * 设置 拣货任务状态
     *
     * @param taskState 拣货任务状态
     */
    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    /**
     * 获取 创建时间
     *
     * @return createTime 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置 创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 播种任务id
     *
     * @return sowTaskId 播种任务id
     */
    public Long getSowTaskId() {
        return this.sowTaskId;
    }

    /**
     * 设置 播种任务id
     *
     * @param sowTaskId 播种任务id
     */
    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    /**
     * 获取 播种号
     *
     * @return sowTaskNo 播种号
     */
    public String getSowTaskNo() {
        return this.sowTaskNo;
    }

    /**
     * 设置 播种号
     *
     * @param sowTaskNo 播种号
     */
    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    /**
     * 获取 序号
     *
     * @return sequence 序号
     */
    public Integer getSequence() {
        return this.sequence;
    }

    /**
     * 设置 序号
     *
     * @param sequence 序号
     */
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    /**
     * 获取 拣货任务名称
     *
     * @return batchTaskName 拣货任务名称
     */
    public String getBatchTaskName() {
        return this.batchTaskName;
    }

    /**
     * 设置 拣货任务名称
     *
     * @param batchTaskName 拣货任务名称
     */
    public void setBatchTaskName(String batchTaskName) {
        this.batchTaskName = batchTaskName;
    }

    /**
     * 获取 波次订单类型 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchOrderTypeEnum
     *
     * @return batchOrderType 波次订单类型 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchOrderTypeEnum
     */
    public Integer getBatchOrderType() {
        return this.batchOrderType;
    }

    /**
     * 设置 波次订单类型 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchOrderTypeEnum
     *
     * @param batchOrderType 波次订单类型 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchOrderTypeEnum
     */
    public void setBatchOrderType(Integer batchOrderType) {
        this.batchOrderType = batchOrderType;
    }
}
