package com.yijiupi.himalaya.supplychain.waves.listener;

import java.util.Date;
import java.util.List;
import java.util.TreeMap;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.ScheduleJob;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyExecuteDTO;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyJob;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderProcessBL;
import com.yijiupi.himalaya.supplychain.waves.utils.DateUtil;

/**
 * <AUTHOR>
 * @Title: StoreHouseSyncListener.java
 * @Package
 * @date 2018/3/8 17:10
 */
@Service
public class ScheduleJobListener {
    private static final Logger LOG = LoggerFactory.getLogger(ScheduleJobListener.class);
    @Autowired
    private BatchOrderProcessBL batchOrderProcessBL;

    @RabbitListener(queues = "${mq.supplychain.scmschedule.triggerTask}")
    public void triggerTask(Message message) throws Exception {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOG.info("触发job>>>【mq.supplychain.scmschedule.triggerTask】:{}", json);
            if (StringUtils.isEmpty(json)) {
                throw new DataValidateException("job内容为空");
            }
            List<ScheduleJob> scheduleJobs = JSON.parseArray(json, ScheduleJob.class);
            ScheduleJob scheduleJob = scheduleJobs.get(0);
            WavesStrategyJob wavesStrategyJob =
                JSON.parseObject(JSON.toJSONString(scheduleJob.getObject()), WavesStrategyJob.class);
            List<WavesStrategyExecuteDTO> wavesStrategyExecuteDTOS =
                JSON.parseArray(wavesStrategyJob.getTimerRule(), WavesStrategyExecuteDTO.class);
            TreeMap<Long, WavesStrategyExecuteDTO> map = new TreeMap<>();
            String now = DateUtil.format(new Date(), DateUtil.HHMMSS);
            for (WavesStrategyExecuteDTO wavesStrategyExecuteDTO : wavesStrategyExecuteDTOS) {
                long timeDisparity = DateUtil.getTimeDisparity(now, wavesStrategyExecuteDTO.getExecuteTime());
                map.put(Long.valueOf(timeDisparity), wavesStrategyExecuteDTO);
            }
            WavesStrategyExecuteDTO wavesStrategyExecuteDTO = map.get(map.firstKey());
            String orderStartTime = null;
            String orderEndTime = null;

            if (wavesStrategyExecuteDTO.getOrderStartType() == WavesStrategyConstants.TODAY
                && !com.alibaba.dubbo.common.utils.StringUtils.isEmpty(wavesStrategyExecuteDTO.getOrderStartTime())) {
                orderStartTime = DateUtil.getTodayTime(wavesStrategyExecuteDTO.getOrderStartTime());
            } else if (wavesStrategyExecuteDTO.getOrderStartType() == WavesStrategyConstants.YESTERDAY
                && !com.alibaba.dubbo.common.utils.StringUtils.isEmpty(wavesStrategyExecuteDTO.getOrderStartTime())) {
                orderStartTime = DateUtil.getYesterdayTime(wavesStrategyExecuteDTO.getOrderStartTime());
            }

            if (wavesStrategyExecuteDTO.getOrderEndType() == WavesStrategyConstants.TODAY
                && !com.alibaba.dubbo.common.utils.StringUtils.isEmpty(wavesStrategyExecuteDTO.getOrderEndTime())) {
                orderEndTime = DateUtil.getTodayTime(wavesStrategyExecuteDTO.getOrderEndTime());
            } else if (wavesStrategyExecuteDTO.getOrderEndType() == WavesStrategyConstants.YESTERDAY
                && !com.alibaba.dubbo.common.utils.StringUtils.isEmpty(wavesStrategyExecuteDTO.getOrderEndTime())) {
                orderEndTime = DateUtil.getYesterdayTime(wavesStrategyExecuteDTO.getOrderEndTime());
            }
            LOG.info("策略 {} 开始执行 订单查询区间{}-{}", wavesStrategyJob.getId(), orderStartTime, orderEndTime);
            batchOrderProcessBL.processBatchOrderByStrateId(wavesStrategyJob.getId(), orderStartTime, orderEndTime);
        } catch (Exception ex) {
            LOG.info("手动执行波次策略失败", ex);
        }
    }
}
