package com.yijiupi.himalaya.supplychain.waves.dto.secondsort;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/7/7 14:44
 */
public class SowPalletDTO implements Serializable {

    private static final long serialVersionUID = -2114438138581209520L;
    /**
     * id
     */
    private Long palletId;

    /**
     * 托盘编号 多个用英文逗号拼接
     */
    private String boxCode;

    private String orderNo;

    private Long orderItemId;

    public Long getPalletId() {
        return palletId;
    }

    public void setPalletId(Long palletId) {
        this.palletId = palletId;
    }

    public String getBoxCode() {
        return boxCode;
    }

    public void setBoxCode(String boxCode) {
        this.boxCode = boxCode;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }
}
