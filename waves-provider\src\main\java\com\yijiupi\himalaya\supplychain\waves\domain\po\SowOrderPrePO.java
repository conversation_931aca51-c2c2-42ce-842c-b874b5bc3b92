package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.util.Date;

/**
 * 播种任务_订单关联表_预览
 */
public class SowOrderPrePO {
    /**
    * 主键id
    */
    private Long id;

    /**
     * 城市id
     */
    private Integer orgId;

    /**
    * 仓库id
    */
    private Integer warehouseId;

    /**
    * 播种Id
    */
    private Long sowTaskPreId;

    /**
    * 订单编号
    */
    private String refOrderNo;

    /**
    * 版本号
    */
    private Integer version;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date lastUpdateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSowTaskPreId() {
        return sowTaskPreId;
    }

    public void setSowTaskPreId(Long sowTaskPreId) {
        this.sowTaskPreId = sowTaskPreId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
}