package com.yijiupi.himalaya.supplychain.waves.domain.bl.stockup;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageHelper;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.common.dto.PageListUtil;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.instockorder.enums.YesOrNoEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.StockUpRecordItemPOMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.StockUpRecordPOMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.stockup.StockUpCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.stockup.StockUpRecordDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.stockup.StockUpRecordItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.stockup.StockUpRecordQueryParam;
import com.yijiupi.himalaya.supplychain.waves.enums.SubmitFlagEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.himalaya.supplychain.waves.utils.StreamUtils;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-10-11 14:37
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class DrinkingStockUpRecordBL {

    @Resource
    private StockUpRecordPOMapper stockUpRecordPOMapper;

    @Resource
    private StockUpRecordItemPOMapper stockUpRecordItemPOMapper;

    @Resource
    private BatchTaskMapper batchTaskMapper;

    @Resource
    private BatchTaskItemMapper batchTaskItemMapper;

    @Resource
    private GlobalCache globalCache;

    @Reference
    private IProductSkuService productSkuService;

    @Reference
    private IBatchTaskQueryService batchTaskQueryService;

    private static final BigDecimal HUNDRED = new BigDecimal(100);

    private static final Logger logger = LoggerFactory.getLogger(DrinkingStockUpRecordBL.class);

    /**
     * 完成扫码备货, 这个方法会做几件事<br/>
     * <ol>
     *     <li>记录备货记录表</li>
     *     <li>将指定拣货任务指派给扫码人</li>
     *     <li>完成当前扫码的 sku 对应的拣货任务</li>
     * </ol>
     *
     * @param param 完成参数
     */
    @Transactional(rollbackFor = Throwable.class)
    public void completeStockUp(StockUpCompleteDTO param) {
        Integer cityId = param.getCityId();
        Integer warehouseId = param.getWarehouseId();
        Integer userId = param.getUserId();
        String userName = globalCache.getAdminTrueName(userId);
        List<String> batchTaskItemIds = param.getBatchTaskItemIds();
        param.getRecords().forEach(it -> addOrUpdateStockUpRecord(it, userName));
        // 完成拣货任务, 只处理状态不为 已完成 的
        Map<String, List<BatchTaskItemPO>> taskItemMap = batchTaskItemMapper.findBatchTaskItemByIds(batchTaskItemIds).stream()
                .filter(it -> !TaskStateEnum.已完成.valueEquals(it.getTaskState()))
                .collect(Collectors.groupingBy(BatchTaskItemPO::getBatchTaskId));
        if (taskItemMap.isEmpty()) {
            logger.info("需要完成的拣货任务为空, 跳过后续操作");
            return;
        }
        // 指派拣货员
        List<BatchTaskDTO> tasks = batchTaskMapper.findBatchTaskByIds(taskItemMap.keySet()).stream()
                .map(it -> StreamUtils.copy(it, BatchTaskDTO::new))
                .peek(it -> it.setSorter(userName)).peek(it -> it.setSorterId(userId)).collect(Collectors.toList());
        Map<String, BatchTaskDTO> taskMap = tasks.stream().collect(Collectors.toMap(BatchTaskDTO::getId, Function.identity()));
        batchTaskQueryService.updateBatch(tasks, userName);
        for (Map.Entry<String, List<BatchTaskItemPO>> entry : taskItemMap.entrySet()) {
            String batchTaskId = entry.getKey();
            BatchTaskDTO task = taskMap.get(batchTaskId);
            if (task == null) {
                logger.error("找不到关联的拣货任务: {}", batchTaskId);
                continue;
            }
            List<BatchTaskItemCompleteDTO> completeDTOS = convertToCompleteDTO(entry.getValue());
            batchTaskQueryService.updateBatchTaskItem(
                    completeDTOS, batchTaskId, userName, warehouseId,
                    task.getToLocationId(), task.getToLocationName(),
                    cityId, userId, YesOrNoEnum.NO.getValue().byteValue()
            );
        }
    }

    /**
     * 新增或更新备货记录
     *
     * @param stockUpRecord 备货记录
     * @param userName      操作人名称
     */
    @Transactional(rollbackFor = Throwable.class)
    public void addOrUpdateStockUpRecord(StockUpRecordDTO stockUpRecord, String userName) {
        String taskNo = stockUpRecord.getTaskNo();
        Integer warehouseId = stockUpRecord.getWarehouseId();
        List<StockUpRecordItemDTO> items = stockUpRecord.getItems();
        StockUpRecordPO exists = stockUpRecordPOMapper.selectByTaskNO(taskNo, warehouseId);
        StockUpRecordPO newPO = StreamUtils.copy(stockUpRecord, StockUpRecordPO::new);
        if (exists == null) {
            exists = newPO;
            exists.setId(UUIDGenerator.getUUID("stockUpRecord"));
            if (!StringUtils.hasText(exists.getLicensePlate())) {
                exists.setLicensePlate("自带车");
            }
            stockUpRecordPOMapper.insertSelective(exists);
        } else {
            newPO.setId(exists.getId());
            // 分片字段不能更新
            newPO.setOrgId(null);
            stockUpRecordPOMapper.updateByPrimaryKeySelective(newPO);
        }
        // 先删后增
        Long stockUpRecordId = exists.getId();
        List<StockUpRecordItemPO> itemPos = items.stream().map(it -> StreamUtils.copy(it, StockUpRecordItemPO::new)).peek(it -> {
            it.setId(UUIDGenerator.getUUID("stockUpRecordItem"));
            it.setStockUpRecordId(stockUpRecordId);
            if (TaskStateEnum.已完成.valueEquals(it.getTaskState())) {
                it.setPickupUser(Optional.ofNullable(it.getPickupUser()).orElse(userName));
            } else {
                it.setPickupUser("无");
            }
            it.setCompleteTime(Optional.ofNullable(it.getCompleteTime()).orElseGet(Date::new));
        }).collect(Collectors.toList());
        stockUpRecordItemPOMapper.deleteByRecordId(stockUpRecordId);
        stockUpRecordItemPOMapper.batchInsert(itemPos);
    }

    /**
     * 查询备货记录
     *
     * @param param 备货记录查询参数
     * @return 查询结果
     */
    public PageList<StockUpRecordDTO> pageListStockUpRecord(StockUpRecordQueryParam param) {
        PageHelper.startPage(param.getCurrentPage(), param.getPageSize());
        PageResult<StockUpRecordPO> records = stockUpRecordPOMapper.pageListStockUpRecord(param);
        if (records.isEmpty()) {
            return PageListUtil.getDefault(param);
        }
        List<Long> recordIds = records.stream().map(StockUpRecordPO::getId).collect(Collectors.toList());
        Map<Long, List<StockUpRecordItemPO>> itemMap = stockUpRecordItemPOMapper.selectByRecordIds(recordIds).stream()
                .collect(Collectors.groupingBy(StockUpRecordItemPO::getStockUpRecordId));
        Set<Long> skuIds = itemMap.values().stream().flatMap(Collection::stream).map(StockUpRecordItemPO::getSkuId)
                .collect(Collectors.toSet());
        Map<Long, ProductCodeDTO> codeMap = productSkuService.getPackageAndUnitCode(skuIds, param.getOrgId());
        return records.toPageList(it -> it.stream().map(record -> {
            List<StockUpRecordItemDTO> items = itemMap.getOrDefault(record.getId(), Collections.emptyList()).stream()
                    .map(item -> StreamUtils.copy(item, StockUpRecordItemDTO::new))
                    .peek(item -> Optional.ofNullable(codeMap.get(item.getSkuId())).map(ProductCodeDTO::getPackageCode).ifPresent(item::setCode))
                    .collect(Collectors.toList());
            Map<Boolean, List<StockUpRecordItemDTO>> taskPart = items.stream()
                    .collect(Collectors.partitioningBy(item -> TaskStateEnum.已完成.valueEquals(item.getTaskState())));
            BigDecimal completeSize = BigDecimal.valueOf(taskPart.getOrDefault(true, Collections.emptyList()).size());
            BigDecimal totalSize = BigDecimal.valueOf(items.size());
            String progress = completeSize.divide(totalSize, 2, RoundingMode.HALF_UP).multiply(HUNDRED)
                    .stripTrailingZeros().toPlainString();
            StockUpRecordDTO result = StreamUtils.copy(record, StockUpRecordDTO::new);
            // 不传 recordId 不给 item
            if (param.getRecordId() != null) {
                result.setItems(items);
            }
            result.setProgress(progress);
            return result;
        }).collect(Collectors.toList()));
    }

    private List<BatchTaskItemCompleteDTO> convertToCompleteDTO(List<BatchTaskItemPO> taskItems) {
        return taskItems.stream().map(it -> {
            BatchTaskItemCompleteDTO complete = new BatchTaskItemCompleteDTO();
            complete.setContainerList(Collections.emptyList());
            complete.setRemark("扫码备货自动完成拣货任务");
            complete.setStartTime(new Date());
            complete.setCompleteTime(new Date());
            complete.setSubmitFlag(SubmitFlagEnum.初次提交.getType());
            complete.setFromLocationId(it.getLocationId());
            complete.setFromLocationName(it.getLocationName());
            complete.setId(it.getId());
            complete.setOverSortPackageCount(it.getPackageCount());
            complete.setOverSortUnitCount(it.getUnitCount());
            complete.setLackUnitCount(BigDecimal.ZERO);
            complete.setLackPackageCount(BigDecimal.ZERO);
            complete.setBatchTime(null);
            complete.setProductionDate(null);
            complete.setBoxCodeList(null);
            complete.setOrderSequence(null);
            complete.setBoxCode(null);
            return complete;
        }).collect(Collectors.toList());
    }

}
