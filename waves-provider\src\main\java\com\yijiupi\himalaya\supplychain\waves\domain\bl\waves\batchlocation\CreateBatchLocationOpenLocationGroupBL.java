package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.order.OrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CreateBatchLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchTaskConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.supplychain.serviceutils.constant.DeliveryOrderConstant;

/**
 * <AUTHOR>
 * @title: CreateBatchLocationOpenLocationGroupBL
 * @description:
 * @date 2022-09-27 09:29
 */
@Component
public class CreateBatchLocationOpenLocationGroupBL extends CreateBatchLocationBaseBL {
    @Override
    public boolean doSupport(CreateBatchLocationBO bo) {
        boolean isOpenStock = bo.getIsOpenStock();
        boolean isOpenLocationGroup = bo.getIsOpenLocationGroup();

        if (isOpenStock && !isOpenLocationGroup) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public List<OutStockOrderPO> doSetLocation(List<OutStockOrderPO> outStockOrderPOList, CreateBatchLocationBO bo) {

        List<OutStockOrderItemPO> totalOrderItemPOList = Lists.newArrayList();

        outStockOrderPOList.forEach(e -> {
            totalOrderItemPOList.addAll(e.getItems());
        });

        List<OrderItemDTO> orderItemDTOS = getOrderItemDTOList(outStockOrderPOList, bo);
        if (CollectionUtils.isEmpty(orderItemDTOS)) {
            return outStockOrderPOList;
        }
        // 组装新的订单项
        Map<String, List<OutStockOrderItemPO>> resultLocation =
            getOutStockOrderItemPOListByInventoryBak(orderItemDTOS, totalOrderItemPOList);

        return rebuildOrderList(resultLocation, outStockOrderPOList);
    }

    public List<OutStockOrderPO> setSplitLocation(List<OutStockOrderPO> outStockOrderPOList, CreateBatchLocationBO bo) {
        List<OutStockOrderItemPO> totalOrderItemPOList = Lists.newArrayList();

        outStockOrderPOList.forEach(e -> {
            totalOrderItemPOList.addAll(e.getItems());
        });

        List<OrderItemDTO> orderItemDTOS = getOrderItemDTOList(outStockOrderPOList, bo);
        if (CollectionUtils.isEmpty(orderItemDTOS)) {
            return outStockOrderPOList;
        }
        // 组装新的订单项
        Map<String, List<OutStockOrderItemPO>> resultLocation =
            getOutStockOrderItemPOListByInventoryNormal(orderItemDTOS, totalOrderItemPOList);

        return rebuildOrderList(resultLocation, outStockOrderPOList);
    }

    public List<OrderItemDTO> getOrderItemDTOList(List<OutStockOrderPO> outStockOrderPOList, CreateBatchLocationBO bo) {
        List<OutStockOrderItemPO> outStockOrderItemPOList = getNormalOrderItemList(outStockOrderPOList);
        List<OutStockOrderItemPO> outStockDelayOrderItemPOList = getDelayOrderItemList(outStockOrderPOList);

        if (CollectionUtils.isEmpty(outStockOrderItemPOList) && CollectionUtils.isEmpty(outStockDelayOrderItemPOList)) {
            return Collections.emptyList();
        }

        List<OrderItemDTO> orderItemDTOS = new ArrayList<>();
        if (outStockOrderItemPOList.size() > 0) {
            List<OrderItemDTO> orderItemDTO =
                BatchTaskConvertor.getOrderItemDTO(outStockOrderItemPOList, bo.getWarehouseId());
            LOG.info("获取货位货区参数1{}", JSON.toJSONString(orderItemDTO));
            List<OrderItemDTO> normalOrderItemDTOS =
                batchInventoryQueryService.findOutStockStrategyRule(orderItemDTO, "1");
            LOG.info("获取货位货区结果1{}", JSON.toJSONString(normalOrderItemDTOS));
            orderItemDTOS.addAll(normalOrderItemDTOS);
        }

        // 延迟配送，单独从退货暂存区取货
        if (outStockDelayOrderItemPOList.size() > 0) {
            List<OrderItemDTO> delayOrderItemDTO =
                BatchTaskConvertor.getOrderItemDTO(outStockDelayOrderItemPOList, bo.getWarehouseId());
            LOG.info("获取货位货区参数2{}", JSON.toJSONString(delayOrderItemDTO));
            List<OrderItemDTO> delayOrderItemDTOS =
                batchInventoryQueryService.findDelayOutStockStrategyRule(delayOrderItemDTO, "1");
            LOG.info("获取货位货区结果2{}", JSON.toJSONString(delayOrderItemDTOS));
            orderItemDTOS.addAll(delayOrderItemDTOS);
        }

        return orderItemDTOS;
    }

    private List<OutStockOrderItemPO> getNormalOrderItemList(List<OutStockOrderPO> outStockOrderPOList) {
        return outStockOrderPOList.stream()
            .filter(
                o -> !Objects.equals(o.getDeliveryMarkState(), DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue()))
            .flatMap(o -> o.getItems().stream()).collect(Collectors.toList());
    }

    private List<OutStockOrderItemPO> getDelayOrderItemList(List<OutStockOrderPO> outStockOrderPOList) {
        return outStockOrderPOList.stream()
            .filter(o -> Objects.equals(o.getDeliveryMarkState(), DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue()))
            .flatMap(o -> o.getItems().stream()).collect(Collectors.toList());

    }

    private List<OutStockOrderItemPO> getPromotionOrderItemList(List<OutStockOrderPO> outStockOrderPOList) {
        return outStockOrderPOList.stream()
            .filter(
                o -> !Objects.equals(o.getDeliveryMarkState(), DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue()))
            .flatMap(o -> o.getItems().stream()).filter(item -> isAdvent(item)).collect(Collectors.toList());
    }

    private boolean isAdvent(OutStockOrderItemPO outStockOrderItemPO) {
        if (Objects.isNull(outStockOrderItemPO.getIsAdvent())) {
            return Boolean.FALSE;
        }
        if (ConditionStateEnum.是.getType().equals(outStockOrderItemPO.getIsAdvent())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    private boolean isNotAdvent(OutStockOrderItemPO outStockOrderItemPO) {
        if (Objects.isNull(outStockOrderItemPO.getIsAdvent())) {
            return Boolean.TRUE;
        }
        if (ConditionStateEnum.是.getType().equals(outStockOrderItemPO.getIsAdvent())) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    private List<OutStockOrderPO> rebuildOrderList(Map<String, List<OutStockOrderItemPO>> resultLocation,
        List<OutStockOrderPO> outStockOrderPOList) {
        String msg = "";
        List<OutStockOrderItemPO> haveLocation = resultLocation.get("haveLocation");
        LOG.info("haveLocationcollect{}", JSON.toJSONString(haveLocation));
        List<OutStockOrderItemPO> noLocation = resultLocation.get("noLocation");
        LOG.info("noLocationcollect{}", JSON.toJSONString(noLocation));
        Map<Long, List<OutStockOrderItemPO>> noLocationcollect = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(noLocation)) {
            noLocationcollect = noLocation.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getId));
        }

        List<OutStockOrderItemPO> allResultLocation = new ArrayList<>();
        allResultLocation.addAll(noLocation);
        allResultLocation.addAll(haveLocation);
        Map<Long, List<OutStockOrderItemPO>> allResultLocationMap =
            allResultLocation.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getId));

        for (int s = outStockOrderPOList.size() - 1; s >= 0; s--) {
            OutStockOrderPO outStockOrderPO = outStockOrderPOList.get(s);
            List<OutStockOrderItemPO> lstOrderItems = outStockOrderPO.getItems();
            List<OutStockOrderItemPO> newItemPo = Lists.newArrayList();
            for (OutStockOrderItemPO orderItemPo : lstOrderItems) {
                // 记录无货位商品
                if (noLocationcollect.containsKey(orderItemPo.getId())) {
                    msg = msg + orderItemPo.getProductname() + ",";
                }
                newItemPo.addAll(allResultLocationMap.get(orderItemPo.getId()));
            }
            outStockOrderPO.setItems(newItemPo);
        }
        LOG.info("以下商品未找到合适货位 {}", msg);

        return outStockOrderPOList;
    }

    /**
     * 组装新的订单项
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2018/4/17 15:10
     */
    private Map<String, List<OutStockOrderItemPO>> getOutStockOrderItemPOListByInventoryBak(
        List<OrderItemDTO> orderItemDTOS, List<OutStockOrderItemPO> lstOrderItems) {
        LOG.info("组装新的订单项：{}", JSON.toJSONString(lstOrderItems));

        Map<String, List<OutStockOrderItemPO>> resultMap = Maps.newHashMap();
        Map<Long, OutStockOrderItemPO> itemPOImmutableMap =
            lstOrderItems.stream().collect(Collectors.toMap(OutStockOrderItemPO::getId, (p) -> p));

        List<OutStockOrderItemPO> haveLocation = new ArrayList<>();
        List<OutStockOrderItemPO> noLocation = new ArrayList<>();

        for (OrderItemDTO orderItemDTO : orderItemDTOS) {
            OutStockOrderItemPO outStockOrderItemPO = itemPOImmutableMap.get(orderItemDTO.getId());

            OutStockOrderItemPO stockOrderItemPO = new OutStockOrderItemPO();
            BeanUtils.copyProperties(outStockOrderItemPO, stockOrderItemPO);
            stockOrderItemPO.setAreaId(orderItemDTO.getAreaId());
            stockOrderItemPO.setAreaName(orderItemDTO.getAreaName());
            stockOrderItemPO.setUnittotalcount(orderItemDTO.getPickUpCount() != null ? orderItemDTO.getPickUpCount()
                : orderItemDTO.getUnitTotalCount());
            BigDecimal[] pickUpCountRemainder =
                stockOrderItemPO.getUnittotalcount().divideAndRemainder(stockOrderItemPO.getSpecquantity());
            stockOrderItemPO.setPackagecount(pickUpCountRemainder[0]);
            stockOrderItemPO.setUnitcount(pickUpCountRemainder[1]);
            stockOrderItemPO.setCategoryname(outStockOrderItemPO.getCategoryname());
            stockOrderItemPO.setSubCategory(orderItemDTO.getSubCategory());
            stockOrderItemPO.setBatchTime(orderItemDTO.getBatchTime());
            stockOrderItemPO.setProductionDate(orderItemDTO.getProductionDate());
            stockOrderItemPO.setItemDetailId(orderItemDTO.getItemDetailId());
            stockOrderItemPO.setSecOwnerId(orderItemDTO.getSecOwnerId());

            if (orderItemDTO.getLocationId() != null) {
                stockOrderItemPO.setLocationId(orderItemDTO.getLocationId());
                stockOrderItemPO.setLocationName(orderItemDTO.getLocationName());
                haveLocation.add(stockOrderItemPO);
            } else {
                // 没有货位库存的产品设置小单位总数量
                if (null == stockOrderItemPO.getUnittotalcount()) {
                    stockOrderItemPO.setUnittotalcount(stockOrderItemPO.getPackagecount()
                        .multiply(stockOrderItemPO.getSpecquantity()).add(stockOrderItemPO.getUnitcount()));
                }
                stockOrderItemPO.setLocationId(null);
                stockOrderItemPO.setLocationName(null);
                noLocation.add(stockOrderItemPO);
            }
        }
        resultMap.put("haveLocation", haveLocation);
        resultMap.put("noLocation", noLocation);
        return resultMap;
    }

    private Map<String, List<OutStockOrderItemPO>> getOutStockOrderItemPOListByInventoryNormal(
        List<OrderItemDTO> orderItemDTOS, List<OutStockOrderItemPO> lstOrderItems) {
        LOG.info("组装新的订单项：{}", JSON.toJSONString(lstOrderItems));

        Map<String, List<OutStockOrderItemPO>> resultMap = Maps.newHashMap();
        Map<Long, OutStockOrderItemPO> itemPOImmutableMap =
            lstOrderItems.stream().collect(Collectors.toMap(OutStockOrderItemPO::getId, (p) -> p));

        List<OutStockOrderItemPO> haveLocation = new ArrayList<>();
        List<OutStockOrderItemPO> noLocation = new ArrayList<>();

        for (OrderItemDTO orderItemDTO : orderItemDTOS) {
            OutStockOrderItemPO outStockOrderItemPO = itemPOImmutableMap.get(orderItemDTO.getId());

            OutStockOrderItemPO stockOrderItemPO = new OutStockOrderItemPO();
            BeanUtils.copyProperties(outStockOrderItemPO, stockOrderItemPO);
            stockOrderItemPO.setAreaId(orderItemDTO.getAreaId());
            stockOrderItemPO.setAreaName(orderItemDTO.getAreaName());
            stockOrderItemPO.setUnittotalcount(orderItemDTO.getPickUpCount() != null ? orderItemDTO.getPickUpCount()
                : orderItemDTO.getUnitTotalCount());
            BigDecimal[] pickUpCountRemainder =
                stockOrderItemPO.getUnittotalcount().divideAndRemainder(stockOrderItemPO.getSpecquantity());
            stockOrderItemPO.setPackagecount(pickUpCountRemainder[0]);
            stockOrderItemPO.setUnitcount(pickUpCountRemainder[1]);
            stockOrderItemPO.setCategoryname(outStockOrderItemPO.getCategoryname());
            stockOrderItemPO.setSubCategory(orderItemDTO.getSubCategory());
            stockOrderItemPO.setBatchTime(orderItemDTO.getBatchTime());
            stockOrderItemPO.setProductionDate(orderItemDTO.getProductionDate());
            stockOrderItemPO.setItemDetailId(orderItemDTO.getItemDetailId());
            stockOrderItemPO.setSecOwnerId(orderItemDTO.getSecOwnerId());

            if (orderItemDTO.getLocationId() != null) {
                stockOrderItemPO.setLocationId(orderItemDTO.getLocationId());
                stockOrderItemPO.setLocationName(orderItemDTO.getLocationName());
                haveLocation.add(stockOrderItemPO);
            } else {
                // 没有货位库存的产品设置小单位总数量
                if (null == stockOrderItemPO.getUnittotalcount()) {
                    stockOrderItemPO.setUnittotalcount(stockOrderItemPO.getPackagecount()
                        .multiply(stockOrderItemPO.getSpecquantity()).add(stockOrderItemPO.getUnitcount()));
                }
                stockOrderItemPO.setLocationId(null);
                stockOrderItemPO.setLocationName(null);
                noLocation.add(stockOrderItemPO);
            }
        }
        resultMap.put("haveLocation", haveLocation);
        resultMap.put("noLocation", noLocation);
        return resultMap;
    }

}
