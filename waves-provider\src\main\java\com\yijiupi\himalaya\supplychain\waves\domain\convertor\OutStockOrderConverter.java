package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.Collections;
import java.util.Optional;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;

/**
 * <AUTHOR>
 * @since 2024-08-21 17:33
 **/
public class OutStockOrderConverter {

    public static OutStockOrderDTO toDTO(OutStockOrderPO it) {
        OutStockOrderDTO result = new OutStockOrderDTO();
        result.setProvince(it.getProvince());
        result.setCity(it.getCity());
        result.setCounty(it.getCounty());
        result.setStreet(it.getStreet());
        result.setExpectedOutStockTime(it.getExpectedOutStockTime());
        result.setDeliveryMode(it.getDeliveryMode());
        result.setPicktime(it.getPicktime());
        result.setOutstocktime(it.getOutstocktime());
        result.setOutstockuser(it.getOutstockuser());
        result.setRemark(it.getRemark());
        result.setCreateuser(it.getCreateuser());
        result.setCreatetime(it.getCreatetime());
        result.setLastupdateuser(it.getLastupdateuser());
        result.setLastupdatetime(it.getLastupdatetime());
        result.setOrderSource(it.getOrderSource());
        result.setOrderSourceText(null);
        result.setLogisticsOrderType(null);
        result.setBusinessId(it.getBusinessId());
        result.setBusinessNo(it.getBusinessNo());
        result.setBatchno(it.getBatchno());
        result.setBatchId(it.getBatchId());
        result.setState(it.getState());
        result.setPackageAttribute(Collections.singletonList(it.getPackageAttribute()));
        result.setUsername(it.getUsername());
        result.setShopname(it.getShopname());
        result.setMobileno(it.getMobileno());
        result.setAreaId(it.getAreaId());
        result.setAreaName(it.getAreaName());
        result.setRouteId(it.getRouteId());
        result.setRouteName(it.getRouteName());
        result.setId(it.getId());
        result.setRefOrderNo(it.getReforderno());
        result.setOrderType(Optional.ofNullable(it.getOrdertype()).map(Number::byteValue).orElse(null));
        result.setOrderAmount(it.getOrderamount());
        result.setSkuCount(it.getSkucount());
        result.setPackageAmount(it.getPackageamount());
        result.setUnitAmount(it.getUnitamount());
        result.setShopName(it.getShopname());
        result.setDetailAddress(it.getDetailaddress());
        result.setOrderCreateTime(it.getOrdercreatetime());
        result.setWarehouseId(it.getWarehouseId());
        result.setWarehouseName(null);
        result.setItemList(null);
        result.setStateText(null);
        result.setOrderTypeText(null);
        result.setPackageAttributeText(null);
        result.setLogisticsList(null);
        result.setOrderSourceType(it.getOrderSourceType());
        result.setAddressId(it.getAddressId());
        return result;
    }

}
