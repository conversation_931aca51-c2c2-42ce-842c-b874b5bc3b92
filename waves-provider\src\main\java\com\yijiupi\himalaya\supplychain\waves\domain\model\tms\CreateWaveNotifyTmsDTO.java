package com.yijiupi.himalaya.supplychain.waves.domain.model.tms;

import java.io.Serializable;
import java.util.Date;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/7/24
 */
public class CreateWaveNotifyTmsDTO implements Serializable {
    /**
     * 中台订单id
     */
    private Long orderId;
    /**
     * 波次号
     */
    private String waveNo;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 创建时间
     */
    private Date createWaveTime;
    /**
     * 操作人
     */
    private String optUserId;


    /**
     * 获取 中台订单id
     *
     * @return orderId 中台订单id
     */
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 中台订单id
     *
     * @param orderId 中台订单id
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 波次号
     *
     * @return waveNo 波次号
     */
    public String getWaveNo() {
        return this.waveNo;
    }

    /**
     * 设置 波次号
     *
     * @param waveNo 波次号
     */
    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 创建时间
     *
     * @return createWaveTime 创建时间
     */
    public Date getCreateWaveTime() {
        return this.createWaveTime;
    }

    /**
     * 设置 创建时间
     *
     * @param createWaveTime 创建时间
     */
    public void setCreateWaveTime(Date createWaveTime) {
        this.createWaveTime = createWaveTime;
    }

    /**
     * 获取 操作人
     *
     * @return optUserId 操作人
     */
    public String getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人
     *
     * @param optUserId 操作人
     */
    public void setOptUserId(String optUserId) {
        this.optUserId = optUserId;
    }
}
