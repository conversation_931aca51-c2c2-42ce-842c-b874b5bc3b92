package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;

import com.yijiupi.himalaya.base.search.PageCondition;

/**
 * 出库单项变更记录查询
 *
 * <AUTHOR>
 * @date 2020-06-09 15:21
 */
public class OutStockOrderItemChangeRecordSO extends PageCondition implements Serializable {

    private static final long serialVersionUID = -5495864342725406285L;

    /**
     * 城市ID
     */
    private Integer orgId;

    /**
     * 拣货任务id
     */
    private String batchTaskId;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 移库单id
     */
    private Long transferOrderId;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public Long getTransferOrderId() {
        return transferOrderId;
    }

    public void setTransferOrderId(Long transferOrderId) {
        this.transferOrderId = transferOrderId;
    }
}
