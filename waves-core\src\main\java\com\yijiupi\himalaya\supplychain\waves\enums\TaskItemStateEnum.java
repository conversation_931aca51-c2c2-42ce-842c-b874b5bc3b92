package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 拣货分组策略 常量类
 *
 * <AUTHOR> 2018/3/13
 */
public enum TaskItemStateEnum {
    /**
     * 枚举
     */
    未分拣((byte)0), 分拣中((byte)1), 已完成((byte)2);

    /**
     * type
     */
    private final Byte type;

    TaskItemStateEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 返回枚举
     */
    public static TaskItemStateEnum getEnum(Integer type) {
        TaskItemStateEnum e = null;

        if (type != null) {
            for (TaskItemStateEnum o : values()) {
                if (o.getType() == type) {
                    e = o;
                }
            }
        }
        return e;
    }

    public boolean valueEquals(Number number) {
        return number != null && this.type.equals(number.byteValue());
    }

}
