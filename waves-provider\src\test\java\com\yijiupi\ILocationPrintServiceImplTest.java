package com.yijiupi;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.waves.batch.ILocationPrintService;
import com.yijiupi.himalaya.supplychain.waves.search.LocationContainerSO;
import com.yijiupi.junit.runner.GeneralRunner;
import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/4/20
 */
@RunWith(GeneralRunner.class)
public class ILocationPrintServiceImplTest {

    @Reference
    private ILocationPrintService locationPrintService;

    @Test
    public void createLocationContainerTest() {
        LocationContainerSO locationContainerSO = new LocationContainerSO();
        locationContainerSO.setWarehouseId(9981);
        locationContainerSO.setOrgId(998);
        locationContainerSO.setRefOrderNo("998307600003");
        locationContainerSO.setLocationId(169368317437191407L);
        locationContainerSO.setLocationName("CK01-01");
        locationPrintService.createLocationContainer(locationContainerSO);
    }

    @Test
    public void printLocationContainerTest() {
        LocationContainerSO locationContainerSO = new LocationContainerSO();
        locationContainerSO.setWarehouseId(9981);
        locationContainerSO.setOrgId(998);
        locationContainerSO.setRefOrderNo("998307600003");
        locationContainerSO.setLocationId(169368317437191407L);
        locationContainerSO.setLocationName("CK01-01");
        locationPrintService.printLocationContainer(locationContainerSO);
    }
}
