package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.enums.PassagePickTypeEnum;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IPassageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.OutStockOrderLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.SowingOrderBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.SowingSkuBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowOrderPrePOMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskPrePOMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPrePO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPrePO;
import com.yijiupi.himalaya.supplychain.waves.util.UUIDGeneratorUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 仓库播种算法研究，提高播种产品订单的重复率【现有基础上提高10%】
 * <AUTHOR>
 * @Date 2025/3/10 15:48
 * @Version 1.0
 */
@Component
public class SeedingAlgorithmManageBL {
	/*
	 * 解决方案一：
	 * 1、统计所有订单中每个 SKU 的总出现次数。
	 * 2、对每个订单，计算其包含的 SKU 在所有订单中的总出现次数之和。
	 * 3、根据这个总和对订单进行排序，降序排列。
	 * 例如，假设有订单 1 包含 SKU A 和 B，订单 2 包含 SKU A 和 C，订单 3 包含 SKU B 和 C。
	 * 统计每个 SKU 的总出现次数：A 出现 2 次，B 出现 2 次，C 出现 2 次。
	 * 然后每个订单的总重复次数是订单中每个 SKU 的总出现次数之和。
	 * 订单 1 的总重复次数是 2（A）+2（B）=4，订单 2 是 2（A）+2（C）=4，订单 3 是 2（B）+2（C）=4。
	 * 这种情况下所有订单排序相同，但如果有订单包含更多高重复 SKU，总和会更高。
	 *
	 * 实现过程:
	 * 1、遍历所有订单，统计每个 SKU 的总出现次数（每个 SKU 在所有订单中的出现次数，不管是否在同一订单中）。
	 * 2、对于每个订单，收集其包含的唯一 SKU 列表。
	 * 3、计算每个订单的总重复次数：将订单中的每个唯一 SKU 的总出现次数相加。
	 * 4、按总重复次数对订单进行降序排序。
	 *
	 * 注意事项:
	 * 1、需要检查是否有其他因素需要考虑，比如订单的大小（包含的 SKU 数量）是否会影响结果。
	 * 例如，一个订单包含多个低重复 SKU，可能总和比一个包含少量高重复 SKU 的订单低，这可能符合用户需求，因为高重复 SKU 更值得优先处理。
	 * 2、考虑sku通道，因为通道影响到拣货任务
	 * 3、sku存放的位置的距离
	 *
	 * 什么是通道？
	 * 所有待拣货订单
	 * -> 过滤出需要播种的订单
	 * -> 先生成播种任务 (最大箱数, 100个单，箱数是20，拆成5个播种任务， 如果没有其他拆分条件，也会拆成5个拣货任务)
	 * -> 根据通道、播种任务 里的 箱数 或者 大小件 生成拣按产品货任务，
	 * 没在通道的订单项，会生成按订单拣货的拣货任务
	 *
	 * 评估播种重复率：
	 * 相同的波次下，记录使用算法分配的播种单据 sowtaskpre(播种任务表_预览)、soworderpre(播种任务_订单关联表_预览)
	 * 对比未使用算法的播种单据
	 *
	 *# 评估播种重复率 - 线上
	 *select sum(t.cnt), sum(t.mergeCnt), round(sum(t.mergeCnt) * 100 / sum(t.cnt), 2) mergeRate
	 * from (select s.SowTaskNo, s.SowTaskName, o2.ProductSpecification_Id, count(*) cnt, count(*) - 1 mergeCnt
	 *       from sc_stockorder_1.sowtask s
	 *                inner join sc_stockorder_1.soworder s2 on s2.SowTask_Id = s.Id
	 *                inner join sc_stockorder_1.outstockorder o
	 *                           on o.RefOrderNo = s2.RefOrderNo and o.Warehouse_Id = s2.Warehouse_Id
	 *                inner join sc_stockorder_1.outstockorderitem o2 on o2.Outstockorder_Id = o.id
	 *       where s.Warehouse_Id = 1023
	 *         and s.CompleteTime BETWEEN DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND CURDATE()
	 *         AND o2.UnitTotalCount > 0
	 *       group by s.id, o2.ProductSpecification_Id) t
	 * ;
	 *
	 * # 评估播种重复率 - 重放预览
	 * select sum(t.cnt), sum(t.mergeCnt), round(sum(t.mergeCnt) * 100 / sum(t.cnt), 2) mergeRate
	 * from (select s.SowTaskNo, s.SowTaskName, o2.ProductSpecification_Id, count(*) cnt, count(*) - 1 mergeCnt
	 *       from sc_stockorder_1.sowtaskpre s
	 *                inner join sc_stockorder_1.soworderpre s2 on s2.SowTaskPreId = s.Id
	 *                inner join sc_stockorder_1.outstockorder o
	 *                           on o.RefOrderNo = s2.RefOrderNo and o.Warehouse_Id = s2.WarehouseId
	 *                inner join sc_stockorder_1.outstockorderitem o2 on o2.Outstockorder_Id = o.id
	 *       where s.Warehouse_Id = 1023
	 *         and s.CompleteTime BETWEEN DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND CURDATE()
	 *         AND o2.UnitTotalCount > 0
	 *       group by s.id, o2.ProductSpecification_Id) t
	 * ;
	 *
	 * create table sc_stockorder_3.sowtaskpre
	 * (
	 *     Id             bigint unsigned                    not null comment 'id'
	 *         primary key,
	 *     OrgId          int                                not null comment '城市id',
	 *     WarehouseId    int                                not null comment '仓库id',
	 *     BatchId        varchar(50)                        not null comment '波次表id',
	 *     BatchNo        varchar(50)                        not null comment '波次编号',
	 *     SowTaskPreNo   varchar(50)                        not null comment '播种任务编号',
	 *     Version        int                                null comment '版本号',
	 *     CreateTime     datetime default CURRENT_TIMESTAMP not null comment '创建时间 ',
	 *     LastUpdateTime datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
	 *     key idx_BatchId (BatchId),
	 *     key idx_BatchNo20 (BatchNo(20))
	 * )
	 *     comment '播种任务表_预览';
	 *
	 * create table sc_stockorder_3.soworderpre
	 * (
	 *     Id             bigint unsigned                    not null comment '主键id'
	 *         primary key,
	 *     OrgId          int                                not null comment '城市id',
	 *     WarehouseId    int                                not null comment '仓库id',
	 *     SowTaskPreId   bigint unsigned                    not null comment '播种Id',
	 *     RefOrderNo     varchar(50)                        null comment '订单编号',
	 *     Version        int                                null comment '版本号',
	 *     CreateTime     datetime default CURRENT_TIMESTAMP not null comment '创建时间',
	 *     LastUpdateTime datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
	 *     key idx_SowTaskPreId (SowTaskPreId),
	 *     key idx_RefOrderNo20 (RefOrderNo(20))
	 * )
	 *     comment '播种任务_订单关联表_预览';
	 */
	private static final Logger LOGGER = LoggerFactory.getLogger(PackageOrderItemBL.class);
	@Autowired
	private OutStockOrderMapper outStockOrderMapper;
	@Autowired
	private BatchMapper batchMapper;
	@Autowired
	private SowTaskPrePOMapper sowTaskPrePOMapper;
	@Autowired
	private SowOrderPrePOMapper sowOrderPrePOMapper;
	@Reference
	private IPassageService iPassageService;
	@Reference
	private WarehouseConfigService warehouseConfigService;
	@Autowired
	private TransactionTemplate transactionTemplate;

	// 限流器，每秒允许执行次数
	private final RateLimiter rateLimiter = RateLimiter.create(7);
	private static final int BATCH_QUERY_SIZE = 1000;

	/**
	 * 按 SKU 重复率对订单列表进行排序
	 */
	public List<SowingOrderBO> sortSowingOrdersBySKURepeatRate(List<SowingOrderBO> allSowingOrders) {
		// 统计全局SKU频次
		Map<Long, Integer> skuCounts = new HashMap<>();
		for (SowingOrderBO sowingOrder : allSowingOrders) {
			// 排除单个订单内重复
			Set<Long> uniqueSkuIds = sowingOrder.getSowingSkuBOList().stream()
					.map(SowingSkuBO::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
			for (Long skuId : uniqueSkuIds) {
				// skuId只出现过一次的置0
				skuCounts.put(skuId, skuCounts.getOrDefault(skuId, -1) + 1);
			}
		}

		// 存储订单及其重复率得分的列表
		List<Map.Entry<SowingOrderBO, Integer>> orderScores = new ArrayList<>();
		for (SowingOrderBO sowingOrder : allSowingOrders) {
			Set<Long> uniqueSkuIds = sowingOrder.getSowingSkuBOList().stream()
					.map(SowingSkuBO::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
			int score = 0;
			for (Long skuId : uniqueSkuIds) {
				score += skuCounts.getOrDefault(skuId, 0);
			}

			// 重复skuId得分
			sowingOrder.setScore(score);
			orderScores.add(new AbstractMap.SimpleEntry<>(sowingOrder, score));
		}

		// 按得分降序排序
		orderScores.sort((entry1, entry2) -> entry2.getValue().compareTo(entry1.getValue()));

		// 提取排序后的订单列表
		List<SowingOrderBO> sortedSowingOrders = new ArrayList<>();
		for (Map.Entry<SowingOrderBO, Integer> entry : orderScores) {
			sortedSowingOrders.add(entry.getKey());
		}

		return sortedSowingOrders;
	}

	@Async
	public void clearSowingTaskReplayData(List<String> batchNos, Integer orgId, Integer version) {
		AssertUtils.notEmpty(batchNos, "batchNos不能为空");

		// 查询in不超过1000个波次
		List<List<String>> partitions = Lists.partition(batchNos, BATCH_QUERY_SIZE);
		for (List<String> partition : partitions) {
			List<SowTaskPrePO> sowTaskPreByBatchNos = sowTaskPrePOMapper.findSowTaskPreByBatchNos(partition, version, orgId);

			for (SowTaskPrePO sowTaskPreByBatchNo : sowTaskPreByBatchNos) {
				sowTaskPrePOMapper.deleteByPrimaryKey(sowTaskPreByBatchNo.getId());

				List<SowOrderPrePO> sowOrderPreSowTaskPreId =
						sowOrderPrePOMapper.findSowOrderPreSowTaskPreId(sowTaskPreByBatchNo.getId(), version, orgId);

				for (SowOrderPrePO sowOrderPrePO : sowOrderPreSowTaskPreId) {
					sowOrderPrePOMapper.deleteByPrimaryKey(sowOrderPrePO.getId());
				}
			}
		}

		LOGGER.info("重放播种任务清理完成。orgId={}:处理波次数量={}", orgId, batchNos.size());
	}

	@Async
	public void replaySowingTaskByBatchNos(List<String> batchNos, Integer orgId, Integer version) {
		AssertUtils.notEmpty(batchNos, "batchNos不能为空");

		LOGGER.info("开始重放播种任务。orgId={}:batchNosSize={}", orgId, batchNos.size());

		// 查询in不超过1000个波次
		List<List<String>> partitions = Lists.partition(batchNos, BATCH_QUERY_SIZE);

		List<BatchPO> batchPOList = Collections.emptyList();
		for (List<String> partition : partitions) {
			if (orgId == null) {
				batchPOList = batchMapper.listByBatchNos(partition);
			} else {
				batchPOList = batchMapper.findBatchByNos(partition, orgId);
			}
		}

		if (CollectionUtils.isEmpty(batchPOList)) {
			LOGGER.warn("未找到波次。batchNos={}", batchNos);
			return;
		}

		for (BatchPO batchPO : batchPOList) {
			// 获取许可，如果没有可用许可，会阻塞直到有可用许可
			double waitTime = rateLimiter.acquire();
			/// LOGGER.info("获取许可，等待时间：{}ms", waitTime);
			replaySowingTaskByBatch(batchPO,version);
		}

		LOGGER.info("重放播种任务完成。orgId={}:处理波次数量={}", orgId, batchPOList.size());
	}

	/**
	 * 按波次重放播种任务
	 */
	private void replaySowingTaskByBatch(BatchPO batchPO, Integer version) {
		String batchNo = batchPO.getBatchNo();
		Integer warehouseId = batchPO.getWarehouseId();
		Integer orgId = batchPO.getOrgId();

		List<OutStockOrderLocationBO> outStockOrderLocationBOList
				= outStockOrderMapper.findOutStockOrderLocationByBatchNo(batchNo, orgId);

		if (CollectionUtils.isEmpty(outStockOrderLocationBOList)) {
			LOGGER.warn("未找到波次关联的订单。batchNo" + batchNo);
			return;
		}

		// 仓库开启拣货通道类型
		Byte pickType = PassagePickTypeEnum.按货位通道.getType();
		// 根据类目或货位查询满足条件的通道配置
		List<PassageDTO> lstPassStrategyDTO = findPassageList(outStockOrderLocationBOList, warehouseId, pickType);
		if (CollectionUtils.isEmpty(lstPassStrategyDTO)) {
			LOGGER.warn("未找到符合条件的通道配置。batchNo={}:warehouseId={}:pickType={}", batchNo, warehouseId, pickType);
			return;
		}

		// 符合通道的订单项
		List<OutStockOrderLocationBO> filterValidOrders = new ArrayList<>();
		for (PassageDTO passageDTO : lstPassStrategyDTO) {
			// 根据通道策咯查找所有符合条件的订单详情
			List<OutStockOrderLocationBO> lstItems = findOutStockOrderItemByPassage(outStockOrderLocationBOList, pickType, filterValidOrders, passageDTO);
			// 记录符合通道的订单项
			filterValidOrders.addAll(lstItems);
		}

		if (CollectionUtils.isEmpty(filterValidOrders)) {
			LOGGER.warn("未找到符合播种条件的订单。batchNo={}:warehouseId={}:pickType={}", batchNo, warehouseId, pickType);
			return;
		}

		transactionTemplate.execute(new TransactionCallbackWithoutResult() {
			@Override
			protected void doInTransactionWithoutResult(TransactionStatus status) {
				try {
					generatePreview(filterValidOrders, batchPO, version);
				} catch (Exception e) {
					status.setRollbackOnly();
					throw e;
				}
			}
		});
	}

	private void generatePreview(List<OutStockOrderLocationBO> filterValidOrders, BatchPO batchPO, Integer version) {
		// result分组，key=refOrderNo, value=OutStockOrderLocationBO
		Map<String, List<OutStockOrderLocationBO>> validOrderMap =
				filterValidOrders.stream().collect(Collectors.groupingBy(OutStockOrderLocationBO::getRefOrderNo));

		Integer warehouseId = batchPO.getWarehouseId();
		Integer orgId = batchPO.getOrgId();

		// 检查当前版本是否已存在播种任务
		List<SowTaskPrePO> sowTaskPrePOList =
				sowTaskPrePOMapper.findSowTaskPreByBatchId(batchPO.getId(), version, orgId);
		if (!CollectionUtils.isEmpty(sowTaskPrePOList)) {
			LOGGER.warn("当前版本已存在播种任务。batchNo={}:warehouseId={}:orgId={}", batchPO.getBatchNo(), warehouseId, orgId);
			return;
		}

		List<SowingOrderBO> sowingOrderBOList = new ArrayList<>();
		// 根据validOrderMap 构建sowingOrderBOList
		validOrderMap.forEach((k, v) -> {
			SowingOrderBO sowingOrderBO = new SowingOrderBO();
			sowingOrderBO.setRefOrderNo(k);
			sowingOrderBO.setSowingSkuBOList(new ArrayList<>());
			v.forEach(dto -> {
				SowingSkuBO sowingSkuBO = new SowingSkuBO();
				sowingSkuBO.setSkuId(dto.getSkuId());
				sowingOrderBO.getSowingSkuBOList().add(sowingSkuBO);
			});

			sowingOrderBOList.add(sowingOrderBO);
		});

		// 按SKU重复率对订单列表进行排序
		List<SowingOrderBO> sowingOrderResultList = sortSowingOrdersBySKURepeatRate(sowingOrderBOList);

		// 播种格子总数
		WarehouseConfigDTO configByWareHouseId = warehouseConfigService.getConfigByWareHouseId(warehouseId);
		Integer totalGrids = configByWareHouseId.getTotalGrids();

		LOGGER.warn("按SKU重复率排序后订单={}:波次号={}:参与播种的订单数={}:播种格子总数={}", JSON.toJSONString(sowingOrderResultList),
				batchPO.getBatchNo(), sowingOrderResultList.size(), totalGrids);

		// 根据格子数，将sowingOrderResultList分组
		List<List<SowingOrderBO>> sowingOrderBOGroupList = splitList(sowingOrderResultList, totalGrids);

		for (int i = 0; i < sowingOrderBOGroupList.size(); i++) {
			// 生成sowtaskpre数据
			SowTaskPrePO sowTaskPrePO = new SowTaskPrePO();
			sowTaskPrePO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.SOW_TASK_PRE));
			sowTaskPrePO.setOrgId(orgId);
			sowTaskPrePO.setWarehouseId(warehouseId);
			sowTaskPrePO.setBatchId(batchPO.getId());
			sowTaskPrePO.setBatchNo(batchPO.getBatchNo());
			sowTaskPrePO.setSowTaskPreNo(String.format("%s-%s", batchPO.getBatchNo(), i+1));
			sowTaskPrePO.setVersion(version);
			sowTaskPrePOMapper.insertSelective(sowTaskPrePO);

			// 生成soworderpre数据
			for (SowingOrderBO sowingOrderBO : sowingOrderResultList) {
				SowOrderPrePO sowOrderPrePO = new SowOrderPrePO();
				sowOrderPrePO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.SOW_ORDER_PRE));
				sowOrderPrePO.setOrgId(orgId);
				sowOrderPrePO.setWarehouseId(warehouseId);
				sowOrderPrePO.setSowTaskPreId(sowTaskPrePO.getId());
				sowOrderPrePO.setRefOrderNo(sowingOrderBO.getRefOrderNo());
				sowOrderPrePO.setVersion(version);
				sowOrderPrePOMapper.insertSelective(sowOrderPrePO);
			}
		}
	}

	public List<PassageDTO> findPassageList(List<OutStockOrderLocationBO> lstOrderItems, Integer warehouseId,
											Byte pickType) {
		List<String> lstRelateList = new ArrayList<>();
		if (PassagePickTypeEnum.按货位通道.getType() == pickType) {
			// 获取所有货位Id集合
			lstRelateList = lstOrderItems.stream().filter(p -> p.getLocationId() != null)
					.map(p -> p.getLocationId().toString()).distinct().collect(Collectors.toList());
		}

		// 根据类目或货位查询满足条件的通道配置
		PassageItemSO passageItemSO = new PassageItemSO();
		passageItemSO.setWarehouseId(warehouseId);
		passageItemSO.setRelateType(pickType);
		passageItemSO.setRelateIdList(lstRelateList);
		return iPassageService.listPassageByRelate(passageItemSO);
	}

	@SuppressWarnings("checkstyle:LineLength")
	private List<OutStockOrderLocationBO> findOutStockOrderItemByPassage(List<OutStockOrderLocationBO> lstOrderItems,
																		 Byte pickType,
																		 List<OutStockOrderLocationBO> lstAdded,
																		 PassageDTO passageDTO) {
		List<OutStockOrderLocationBO> lstItems = new ArrayList<>();
		if (PassagePickTypeEnum.按货位通道.getType() == pickType) {
			// 根据货位Id查找所有符合条件的通道策略
			lstItems = lstOrderItems.stream()
					.filter(p -> lstAdded.stream()
							.noneMatch(q -> q.getOrderItemId().equals(p.getOrderItemId()) && Objects.equals(q.getLocationId(), p.getLocationId()))
							&& p.getLocationId() != null
							&& passageDTO.getItemList().stream()
							.anyMatch(q -> q.getRelateId().equals(p.getLocationId().toString())))
					.collect(Collectors.toList());
		}

		return lstItems;
	}

	public static <T> List<List<T>> splitList(List<T> list, int chunkSize) {
		chunkSize = Math.max(chunkSize, 1); // 防止chunkSize为0

		List<List<T>> result = new ArrayList<>();
		for (int i = 0; i < list.size(); i += chunkSize) {
			System.out.println("i:" + i);
			result.add(list.subList(i, Math.min(i + chunkSize, list.size())));
		}
		return result;
	}
}
