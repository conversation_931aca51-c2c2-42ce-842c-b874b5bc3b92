package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderItemDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.StoreTransferEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo.OutStockOrderAdminCancelHandlePickBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo.OutStockOrderAdminCancelHandlePickResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo.OutStockOrderAdminCancelHandlePickTransferResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.OrderChangeTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.util.UUIDGeneratorUtil;

/**
 * 已经拣货的生成移库单
 * 
 * <AUTHOR>
 * @since 2023-05-25 09:06
 */
@Service
@SuppressWarnings("NonAsciiCharacters")
public class OutStockOrderHasPickedCancelBL extends OutStockOrderAdminCancelBaseBL {

    private static final Logger logger = LoggerFactory.getLogger(OutStockOrderHasPickedCancelBL.class);

    @Override
    public boolean support(OutStockOrderAdminCancelHandlePickBO bo) {
        if (OutStockOrderStateEnum.已拣货.getType() == bo.getOutStockOrderPO().getState()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public boolean baseSupport(OutStockOrderAdminCancelHandlePickBO bo) {
        if (Objects.isNull(bo.getBatchPO())) {
            return Boolean.FALSE;
        }
        byte batchState = Byte.parseByte(bo.getBatchPO().getState().toString());
        if (BatchStateEnum.PICKINGEND.getType().byteValue() == batchState) {
            return Boolean.TRUE;
        }
        if (OutStockOrderStateEnum.已拣货.getType() == bo.getOutStockOrderPO().getState()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    protected OutStockOrderAdminCancelHandlePickResultBO doCancel(OutStockOrderAdminCancelHandlePickBO bo) {
        logger.info("已拣货订单取消: {}", JSON.toJSONString(bo, SerializerFeature.WriteMapNullValue));
        List<OrderItemTaskInfoPO> orderItemTaskInfoList = bo.getOrderItemTaskInfoList();
        OutStockOrderPO outStockOrder = bo.getOutStockOrderPO();
        if (CollectionUtils.isEmpty(orderItemTaskInfoList)) {
            return null;
        }
        List<String> batchTaskItemIdList = orderItemTaskInfoList.stream().map(OrderItemTaskInfoPO::getBatchTaskItemId).filter(Objects ::nonNull)
            .distinct().collect(Collectors.toList());
        List<BatchTaskItemPO> batchTaskItemList = batchTaskItemMapper.selectBatchTaskItemByIds(batchTaskItemIdList);
        List<String> batchTaskIds =
            batchTaskItemList.stream().map(BatchTaskItemPO::getBatchTaskId).distinct().collect(Collectors.toList());

        List<BatchTaskPO> batchTaskList = batchTaskMapper.findBatchTaskByIds(batchTaskIds);
        Map<String, BatchTaskPO> batchTaskMap =
            batchTaskList.stream().collect(Collectors.toMap(BatchTaskPO::getId, v -> v));
        Map<String, BatchTaskItemPO> batchTaskItemMap =
            batchTaskItemList.stream().collect(Collectors.toMap(BatchTaskItemPO::getId, v -> v));
        Map<Long, OutStockOrderItemPO> orderItemMap =
            outStockOrder.getItems().stream().collect(Collectors.toMap(OutStockOrderItemPO::getId, v -> v));
        Map<Long, SowTaskPO> orderItemSowTaskMap = getSowTaskList(outStockOrder);
        logger.info("组装数据, batchTaskItemMap: {}",
            JSON.toJSONString(batchTaskItemMap, SerializerFeature.WriteMapNullValue));
        logger.info("组装数据, orderItemSowTaskMap: {}",
            JSON.toJSONString(orderItemSowTaskMap, SerializerFeature.WriteMapNullValue));
        logger.info("组装数据, orderItemMap: {}", JSON.toJSONString(orderItemMap, SerializerFeature.WriteMapNullValue));
        logger.info("组装数据, batchTaskMap: {}", JSON.toJSONString(batchTaskMap, SerializerFeature.WriteMapNullValue));
        logger.info("组装数据, outStockOrder: {}", JSON.toJSONString(outStockOrder, SerializerFeature.WriteMapNullValue));
        List<OutStockOrderItemChangeRecordPO> recordList = orderItemTaskInfoList.stream().map(m -> {
            BatchTaskItemPO batchTaskItemPO = batchTaskItemMap.get(m.getBatchTaskItemId());
            SowTaskPO sowTaskPO = orderItemSowTaskMap.get(m.getRefOrderItemId());
            OutStockOrderItemPO outStockOrderItemPO = orderItemMap.get(m.getRefOrderItemId());
            BatchTaskPO batchTaskPO = batchTaskMap.get(m.getBatchTaskId());
            return createOutStockOrderItemChangeRecordList(batchTaskItemPO, sowTaskPO, outStockOrder,
                outStockOrderItemPO, m, batchTaskPO);
        }).collect(Collectors.toList());
        List<StoreTransferOrderItemDTO> storeTransferOrderItemList = orderItemTaskInfoList.stream().map(m -> {
            BatchTaskItemPO batchTaskItemPO = batchTaskItemMap.get(m.getBatchTaskItemId());
            SowTaskPO sowTaskPO = orderItemSowTaskMap.get(m.getRefOrderItemId());
            OutStockOrderItemPO outStockOrderItemPO = orderItemMap.get(m.getRefOrderItemId());
            BatchTaskPO batchTaskPO = batchTaskMap.get(m.getBatchTaskId());
            return createStoreTransferOrderItemDTOList(batchTaskItemPO, sowTaskPO, outStockOrder, outStockOrderItemPO,
                m, batchTaskPO);
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        List<OrderItemTaskInfoPO> updateOrderItemTaskInfoList = orderItemTaskInfoList.stream().map(m -> {
            OrderItemTaskInfoPO orderItemTaskInfoPO = new OrderItemTaskInfoPO();
            orderItemTaskInfoPO.setId(m.getId());
            orderItemTaskInfoPO.setMoveCount(m.getUnitTotalCount().negate());
            return orderItemTaskInfoPO;
        }).collect(Collectors.toList());
        OutStockOrderAdminCancelHandlePickTransferResultBO resultBO =
            new OutStockOrderAdminCancelHandlePickTransferResultBO();
        resultBO.setRecordList(recordList);
        resultBO.setUpdateOrderItemTaskInfoList(updateOrderItemTaskInfoList);
        if (CollectionUtils.isNotEmpty(storeTransferOrderItemList)) {
            StoreTransferOrderDTO storeTransferOrderDTO = new StoreTransferOrderDTO();
            storeTransferOrderDTO.setStoreTransferOrderItemDTOS(storeTransferOrderItemList);
            storeTransferOrderDTO.setOrg_id(outStockOrder.getOrgId());
            storeTransferOrderDTO.setTransferType(StoreTransferEnum.单据修改.getType());
            storeTransferOrderDTO.setStartTime(new Date());
            storeTransferOrderDTO.setFinishTime(storeTransferOrderDTO.getStartTime());
            storeTransferOrderDTO.setSorterName(bo.getOpUser());
            storeTransferOrderDTO.setCreateUser(bo.getOpUser());
            storeTransferOrderDTO.setRemark(OrderChangeTypeEnum.getEnumByValue(OrderChangeTypeEnum.订单取消.getType())
                + ": " + outStockOrder.getReforderno());
            storeTransferOrderDTO.setIgnoreHasNotEnoughStore(true);
            storeTransferOrderDTO.setIgnoreProductionDate(true);
            storeTransferOrderDTO.setWarehouse_Id(outStockOrder.getWarehouseId());
            Map<Long, StoreTransferOrderDTO> outStockOrderMap = new HashMap<>();
            outStockOrderMap.put(outStockOrder.getId(), storeTransferOrderDTO);
            resultBO.setStoreTransferOrderDTO(outStockOrderMap);
        }
        // storeTransferOrderDTO.setWarehouseName();
        return resultBO;
    }

    private List<StoreTransferOrderItemDTO> createStoreTransferOrderItemDTOList(BatchTaskItemPO batchTaskItemPO,
        SowTaskPO sowTaskPO, OutStockOrderPO order, OutStockOrderItemPO orderItemPO,
        OrderItemTaskInfoPO orderItemTaskInfoPO, BatchTaskPO batchTaskPO) {
        List<OrderItemTaskInfoDetailPO> orderItemTaskInfoDetailList = orderItemTaskInfoPO.getDetailList();
        // 记录回退货位
        // changeLocationPO.setLocationId(toLocationId);
        // changeLocationPO.setLocationName(toLocationName);
        return orderItemTaskInfoDetailList.stream()
            .map(m -> createStoreTransferOrderItemDTO(batchTaskItemPO, sowTaskPO, orderItemPO, m, batchTaskPO))
            .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private OutStockOrderItemChangeRecordPO createOutStockOrderItemChangeRecordList(BatchTaskItemPO batchTaskItemPO,
        SowTaskPO sowTaskPO, OutStockOrderPO order, OutStockOrderItemPO orderItemPO,
        OrderItemTaskInfoPO orderItemTaskInfoPO, BatchTaskPO batchTaskPO) {
        LocationInfo locationInfo = getLocationInfo(batchTaskItemPO, sowTaskPO, orderItemPO, batchTaskPO);
        OutStockOrderItemChangeRecordPO changeRecordPO = new OutStockOrderItemChangeRecordPO();
        changeRecordPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.ORDER_ITEM_CHANGE_RECORD));
        changeRecordPO.setOrgId(batchTaskItemPO.getOrgId());
        changeRecordPO.setWarehouseId(batchTaskItemPO.getWarehouseId());
        changeRecordPO.setRefOrderId(order.getId());
        changeRecordPO.setRefOrderNo(order.getReforderno());
        changeRecordPO.setRefOrderItemId(orderItemPO.getId());
        changeRecordPO.setProductSkuId(orderItemPO.getSkuid());
        changeRecordPO.setProductName(orderItemPO.getProductname());
        changeRecordPO.setSpecName(orderItemPO.getSpecname());
        changeRecordPO.setSpecQuantity(orderItemPO.getSpecquantity());
        changeRecordPO.setPackageName(orderItemPO.getPackagename());
        changeRecordPO.setUnitName(orderItemPO.getUnitname());
        changeRecordPO.setUnitTotalCount(orderItemTaskInfoPO.getUnitTotalCount());
        BigDecimal[] count = orderItemTaskInfoPO.getUnitTotalCount().divideAndRemainder(orderItemPO.getSpecquantity());
        changeRecordPO.setPackageCount(count[0]);
        changeRecordPO.setUnitCount(count[1]);
        changeRecordPO.setBatchId(batchTaskItemPO.getBatchId());
        changeRecordPO.setBatchNo(batchTaskItemPO.getBatchNo());
        changeRecordPO.setBatchTaskId(batchTaskItemPO.getBatchTaskId());
        changeRecordPO.setBatchTaskNo(batchTaskItemPO.getBatchTaskNo());
        if (Objects.nonNull(sowTaskPO)) {
            changeRecordPO.setSowTaskId(sowTaskPO.getId());
            changeRecordPO.setSowTaskNo(sowTaskPO.getSowTaskNo());
        }
        changeRecordPO.setChangeType(OrderChangeTypeEnum.订单取消.getType());
        // changeRecordPO.setCreateUser(changeDTO.getOperatorUser());
        changeRecordPO.setMoveFlag(Boolean.TRUE);
        changeRecordPO.setLocationId(locationInfo.toLocationId);
        changeRecordPO.setLocationName(locationInfo.toLocationName);

        return changeRecordPO;
    }

    private StoreTransferOrderItemDTO createStoreTransferOrderItemDTO(BatchTaskItemPO batchTaskItemPO,
        SowTaskPO sowTaskPO, OutStockOrderItemPO orderItemPO, OrderItemTaskInfoDetailPO detailPO,
        BatchTaskPO batchTaskPO) {
        // 出库位
        // 1、优先取集货位
        LocationInfo locationInfo = getLocationInfo(batchTaskItemPO, sowTaskPO, orderItemPO, batchTaskPO);
        BigDecimal changeCount = detailPO.getUnitTotalCount();
        BigDecimal[] count = changeCount.divideAndRemainder(orderItemPO.getSpecquantity());
        if (locationInfo.fromLocationId == null || locationInfo.toLocationId == null) {
            LOG.info("订单修改移库时找不到货位：{}", JSON.toJSONString(batchTaskItemPO));
            return null;
        }
        StoreTransferOrderItemDTO itemDTO = new StoreTransferOrderItemDTO();
        itemDTO.setOrg_id(batchTaskItemPO.getOrgId());
        itemDTO.setSkuId(batchTaskItemPO.getSkuId());
        itemDTO.setFromChannel(String.valueOf(batchTaskItemPO.getChannel()));
        itemDTO.setToChannel(String.valueOf(batchTaskItemPO.getChannel()));
        itemDTO.setFromLocation_id(locationInfo.fromLocationId);
        itemDTO.setFromLocationName(locationInfo.fromLocationName);
        itemDTO.setToLocation_id(locationInfo.toLocationId);
        itemDTO.setToLocationName(locationInfo.toLocationName);
        itemDTO.setOverMovePackageCount(count[0]);
        itemDTO.setOverMoveUnitCount(count[1]);
        itemDTO.setSecOwnerId(detailPO.getSecOwnerId());
        return itemDTO;
    }

    /**
     * @return key是OrderItemId， value 是 sowTask
     */
    private Map<Long, SowTaskPO> getSowTaskList(OutStockOrderPO outStockOrder) {
        List<OutStockOrderItemPO> sowItems = outStockOrder.getItems().stream().filter(it -> it.getSowTaskId() != null)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sowItems)) {
            return Collections.emptyMap();
        }
        Set<Long> sowTaskIds = sowItems.stream().map(OutStockOrderItemPO::getSowTaskId).collect(Collectors.toSet());
        Map<Long, SowTaskPO> sowTaskMap = sowTaskMapper.findAllSowTaskByIds(outStockOrder.getOrgId(), sowTaskIds).stream()
                .collect(Collectors.toMap(SowTaskPO::getId, v -> v));
        if (sowTaskMap.isEmpty()) {
            return Collections.emptyMap();
        }
        return sowItems.stream().collect(Collectors.toMap(OutStockOrderItemPO::getId, v -> sowTaskMap.get(v.getSowTaskId())));
    }

    private LocationInfo getLocationInfo(BatchTaskItemPO batchTaskItemPO, SowTaskPO sowTaskPO,
        OutStockOrderItemPO orderItemPO, BatchTaskPO batchTaskPO) {
        Long fromLocationId = null;
        String fromLocationName = null;
        if (Objects.nonNull(sowTaskPO)) {
            fromLocationId = sowTaskPO.getLocationId();
            fromLocationName = sowTaskPO.getLocationName();
        }
        // 2、其次取拣货任务的出库位
        if (fromLocationId == null) {
            if (Objects.nonNull(batchTaskPO)) {
                fromLocationId = batchTaskPO.getToLocationId();
                fromLocationName = batchTaskPO.getToLocationName();
            }
        }
        // 3、最后取订单项的出库位
        if (fromLocationId == null) {
            fromLocationId = orderItemPO.getLocationId();
            fromLocationName = orderItemPO.getLocationName();
        }
        // 拣货位
        Long toLocationId;
        String toLocationName;
        if (batchTaskItemPO != null) {
            toLocationId = batchTaskItemPO.getLocationId();
            toLocationName = batchTaskItemPO.getLocationName();
        } else {
            toLocationId = null;
            toLocationName = null;
        }
        // 变更数量大于0，表示订单项数量增加，需要从拣货位移到集货位/出库位
        // if (unitTotalCount.compareTo(BigDecimal.ZERO) > 0) {
        // toLocationId = fromLocationId;
        // toLocationName = fromLocationName;
        // fromLocationId = batchTaskItemPO.getLocationId();
        // fromLocationName = batchTaskItemPO.getLocationName();
        // }
        LocationInfo locationInfo = new LocationInfo();
        locationInfo.fromLocationId = fromLocationId;
        locationInfo.fromLocationName = fromLocationName;
        locationInfo.toLocationId = toLocationId;
        locationInfo.toLocationName = toLocationName;
        return locationInfo;
    }

    private static class LocationInfo {
        Long fromLocationId;
        String fromLocationName;
        Long toLocationId;
        String toLocationName;
    }

}
