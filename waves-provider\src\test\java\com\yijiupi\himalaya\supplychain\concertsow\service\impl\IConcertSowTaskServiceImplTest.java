package com.yijiupi.himalaya.supplychain.concertsow.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.supplychain.waves.concertsow.service.IConcertSowTaskService;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskReceiveDTO;
import com.yijiupi.junit.runner.GeneralRunner;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.Collections;

/**
 * <AUTHOR>
 * @since 2023-08-30 15:29
 **/
@RunWith(GeneralRunner.class)
public class IConcertSowTaskServiceImplTest {

    @Reference
    private IConcertSowTaskService concertSowTaskService;

    @Test
    public void queryWaitSowTask() {
        SowTaskReceiveDTO dto = new SowTaskReceiveDTO();
        dto.setLocationName("ZT1-1");
        dto.setWarehouseId(9981);
        dto.setOrgId(998);
        dto.setSkuIds(Collections.singletonList(5038459890122859460L));
        SowTaskDTO sowTaskDTO = concertSowTaskService.queryWaitSowTask(dto);
        Assert.assertNotNull(sowTaskDTO);
        System.out.println(JSON.toJSONString(sowTaskDTO, SerializerFeature.PrettyFormat));
    }
}