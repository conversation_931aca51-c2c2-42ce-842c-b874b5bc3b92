package com.yijiupi.himalaya.supplychain.waves.utils;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

public class StreamUtils {

    private static final Pattern NUMBER_PATTERN = Pattern.compile("^[-\\+]?[\\d]*$");

    /**
     * 根据key进行去重
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    public static boolean isNum(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return NUMBER_PATTERN.matcher(str).matches();
    }

    /**
     * 使用 BeanUtils copy 属性
     *
     * @param src      原始对象
     * @param supplier 新对象的 supplier
     * @param <T>      原始对象类型
     * @param <R>      新对象类型
     * @return 拷贝属性后的新对象
     */
    public static <T, R> R copy(T src, Supplier<R> supplier) {
        R result = supplier.get();
        BeanUtils.copyProperties(src, result);
        return result;
    }

}
