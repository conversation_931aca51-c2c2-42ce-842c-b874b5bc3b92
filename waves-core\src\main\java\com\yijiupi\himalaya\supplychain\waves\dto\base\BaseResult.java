package com.yijiupi.himalaya.supplychain.waves.dto.base;

import java.io.ByteArrayOutputStream;

import com.yijiupi.himalaya.supplychain.waves.constant.WebConstants;

/*********************************************
 * ClassName: BaseResult Description: 返回数据基类
 * 
 * <AUTHOR> Date 2016年3月04日
 *********************************************/
public class BaseResult {
    // region 属性定义
    private String message;
    private String result;
    private String detailMessage;
    private Integer totalCount;
    private Integer code = WebConstants.CODE_SUCCESS;
    private Boolean success;
    private Integer currentLoginCount;
    private String pdaVersion;
    private String stackMessage;

    public String getStackMessage() {
        return stackMessage;
    }

    public void setStackMessage(String stackMessage) {
        this.stackMessage = stackMessage;
    }

    // endregion

    public Integer getCurrentLoginCount() {
        return currentLoginCount;
    }

    public String getPdaVersion() {
        return pdaVersion;
    }

    public void setPdaVersion(String pdaVersion) {
        this.pdaVersion = pdaVersion;
    }

    public void setCurrentLoginCount(Integer currentLoginCount) {
        this.currentLoginCount = currentLoginCount;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    // region 构造方法
    public BaseResult() {}

    public BaseResult(String message, String result) {
        this.message = message;
        this.result = result;
        this.success = Boolean.TRUE;
    }

    public BaseResult(String message, String result, Boolean success) {
        this.message = message;
        this.result = result;
        this.success = success;
    }

    public BaseResult(Exception exception) {
        this.result = WebConstants.RESULT_FAILED;
        // if (exception.getCause() != null) {
        // this.message = exception.getCause().getMessage();
        // } else {
        this.message = exception.getMessage();
        // }
        this.detailMessage = exception.getMessage();
        this.code = WebConstants.CODE_FAILED;
    }

    public BaseResult(String message, Exception exception) {
        this(exception);
        this.message = message;
        this.code = WebConstants.CODE_FAILED;
    }
    // endregion

    // region get&set方法
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getDetailMessage() {
        return detailMessage;
    }

    public void setDetailMessage(String detailMessage) {
        this.detailMessage = detailMessage;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }
    // endregion

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    // region 静态方法
    public static BaseResult getFailedResult(String message) {
        return new BaseResult(message, WebConstants.RESULT_FAILED);
    }

    public static BaseResult getErrorResult(String message) {
        return new BaseResult(message, WebConstants.RESULT_ERROR);
    }

    // 获取异常详细信息
    public static BaseResult getErrorResult(Exception e) {
        ByteArrayOutputStream buf = new ByteArrayOutputStream();
        e.printStackTrace(new java.io.PrintWriter(buf, true));
        String message = buf.toString();
        return new BaseResult(message, WebConstants.RESULT_ERROR);
    }

    public static BaseResult getSuccessResult() {
        return new BaseResult("success", WebConstants.RESULT_SUCCESS, true);
    }
    // endregion
}
