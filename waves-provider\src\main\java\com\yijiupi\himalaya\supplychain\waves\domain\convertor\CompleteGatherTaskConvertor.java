package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskLocationPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskProductPO;
import com.yijiupi.himalaya.supplychain.waves.search.CompleteGatherTaskLocationSO;
import com.yijiupi.himalaya.supplychain.waves.search.CompleteGatherTaskProductSO;
import com.yijiupi.himalaya.supplychain.waves.search.CompleteGatherTaskSO;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;

/**
 * <AUTHOR>
 * @Title: himalaya-supplychain-microservice-waves
 * @Package com.yijiupi.himalaya.supplychain.waves.domain.convertor
 * @Description:
 * @date 2018/3/26 14:14
 */
public class CompleteGatherTaskConvertor {

    public static List<GatherTaskProductPO> convertToCompleteGatherTaskProductPOList(
        List<CompleteGatherTaskProductSO> completeGatherTaskProductSOs, Long gatherTaskId, Long userId, Integer orgId) {
        List<GatherTaskProductPO> productPOList = completeGatherTaskProductSOs.stream().map(input -> {
            GatherTaskProductPO po = new GatherTaskProductPO();
            po.setId(UuidUtil.getUUidInt());
            po.setBatchTaskItemId((long)0); // 默认没有拣货任务
            po.setGatherTaskId(gatherTaskId);
            po.setOrgId(orgId);
            po.setProductSkuId(input.getProductSkuId());
            po.setProductName(input.getProductName());
            po.setSpecificationId(input.getSpecificationId());
            po.setProductSpecName(input.getProductSpecName());
            po.setSpecQuantity(input.getSpecQuantity());
            po.setTotalCount(input.getTotalCount());
            po.setTakeCount(input.getTotalCount());
            po.setCreateUser(userId);
            po.setLastUpdateUser(userId);
            return po;
        }).collect(Collectors.toList());

        return productPOList;
    }

    /**
     * @param userId
     * @param po
     * @param goodsDTOList
     * @param productPOMAP
     * @return
     */
    public static List<GatherTaskLocationPO> convertToCompleteGatherTaskLocationPOList(Long userId, GatherTaskPO po,
        List<CompleteGatherTaskLocationSO> locationSOs, Map<Long, GatherTaskProductPO> productPOMAP) {

        List<GatherTaskLocationPO> locationDTOList = locationSOs.stream().map(input -> {
            GatherTaskLocationPO locationPO = new GatherTaskLocationPO();
            locationPO.setId(UuidUtil.getUUidInt());
            locationPO.setOrgId(po.getOrgId());
            locationPO.setBusinessId(input.getOrderId());
            locationPO.setGatherTaskId(po.getId());

            GatherTaskProductPO gatherTaskProductPO = productPOMAP.get(input.getProductSkuId());
            locationPO.setGatherTaskProductId(gatherTaskProductPO.getId());
            locationPO.setLocationId(input.getToLocationId());
            locationPO.setLocationName(input.getToLocationName());
            locationPO.setSpecQuantity(gatherTaskProductPO.getSpecQuantity());
            locationPO.setTotalCount(input.getTotal()); // 需集货
            locationPO.setGatherCount(input.getTotal()); // 已集货
            locationPO.setCreateUser(userId);
            locationPO.setLastUpdateUser(userId);
            return locationPO;
        }).collect(Collectors.toList());
        return locationDTOList;
    }

    public static Map<GatherTaskLocationPO, CompleteGatherTaskLocationSO> convertToCompleteGatherTaskLocationMap(
        Long userId, GatherTaskPO po, List<CompleteGatherTaskLocationSO> locationSOs,
        Map<Long, GatherTaskProductPO> productPOMAP) {
        Map<GatherTaskLocationPO, CompleteGatherTaskLocationSO> completeGatherTaskLocationMap = new HashMap<>();
        locationSOs.forEach(input -> {
            GatherTaskLocationPO locationPO = new GatherTaskLocationPO();
            locationPO.setId(UuidUtil.getUUidInt());
            locationPO.setOrgId(po.getOrgId());
            locationPO.setBusinessId(input.getOrderId());
            locationPO.setGatherTaskId(po.getId());

            GatherTaskProductPO gatherTaskProductPO = productPOMAP.get(input.getProductSkuId());
            locationPO.setGatherTaskProductId(gatherTaskProductPO.getId());
            locationPO.setLocationId(input.getToLocationId());
            locationPO.setLocationName(input.getToLocationName());
            locationPO.setSpecQuantity(gatherTaskProductPO.getSpecQuantity());
            locationPO.setTotalCount(input.getTotal()); // 需集货
            locationPO.setGatherCount(input.getTotal()); // 已集货
            locationPO.setCreateUser(userId);
            locationPO.setLastUpdateUser(userId);
            completeGatherTaskLocationMap.put(locationPO, input);
        });
        return completeGatherTaskLocationMap;
    }

    /**
     * Boolean转化成对应的Byte
     * 
     * @param bool
     * @return
     */
    public static Boolean convertToBoolean(Byte b) {
        if (null == b) {
            return null;
        }
        return b == 1 ? true : false;
    }

    public static GatherTaskPO convertToCompleteGatherTaskPO(CompleteGatherTaskSO completeGatherTaskSO, Long userId) {
        GatherTaskPO po = new GatherTaskPO();
        po.setBatchTaskId("-1");
        po.setBatchtaskNo("-1");
        po.setOrgId(completeGatherTaskSO.getOrgId());
        po.setTaskName("完成集货");
        po.setWarehouseId(completeGatherTaskSO.getWarehouseId());
        po.setCreateUser(userId);
        po.setLastUpdateUser(userId);
        po.setId(UuidUtil.getUUidInt());
        return po;
    }
}
