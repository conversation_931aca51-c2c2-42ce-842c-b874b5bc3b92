package com.yijiupi.himalaya.supplychain.waves.schedule;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Sets;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationConfigItemDTO;
import com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigType;
import com.yijiupi.himalaya.supplychain.instockorder.enums.YesOrNoEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.query.WarehouseForWMSQueryDTO;
import com.yijiupi.himalaya.supplychain.service.IWarehouseQueryForWMSService;
import com.yijiupi.himalaya.supplychain.service.config.IWarehouseAllocationConfigService;
import com.yijiupi.himalaya.supplychain.user.dto.sign.AdminUserSignDTO;
import com.yijiupi.himalaya.supplychain.user.dto.sign.AdminUserSignDetailDTO;
import com.yijiupi.himalaya.supplychain.user.service.sign.IAdminUserSignService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.enums.EnableState;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.PickerShortageWarningMapper;
import com.yijiupi.himalaya.supplychain.waves.schedule.model.PickerEfficiencyBO;
import com.yijiupi.himalaya.supplychain.waves.schedule.push.PickerShortageNotifyPusher;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigRelateType.WAREHOUSE_WORKER;
import static com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigType.DRINKING;
import static com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant.FEATURE_TYPE_DRINKING;
import static com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant.FEATURE_TYPE_REST;
import static java.util.stream.Collectors.toSet;

/**
 * 分拣不足预警
 *
 * <AUTHOR>
 * @since 2024-11-07 10:57
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class PickerShortageWarningService {

    @Resource
    private PickerShortageWarningMapper pickerShortageWarningMapper;

    @Resource
    private PickerShortageNotifyPusher pickerShortageNotifyPusher;

    @Resource
    @Qualifier("waveTaskExecutor")
    private Executor pool;

    @Reference
    private IWarehouseQueryForWMSService warehouseQueryForWMSService;

    @Reference
    private IWarehouseAllocationConfigService warehouseAllocationConfigService;

    @Reference
    private IAdminUserSignService adminUserSignService;

    private static final Set<Byte> COUNT_STATE = Sets.newHashSet(
            OutStockOrderStateEnum.待调度.getType(), OutStockOrderStateEnum.待拣货.getType()
    );

    private static final Logger logger = LoggerFactory.getLogger(PickerShortageWarningService.class);

    @XxlJob("scheduleNotify")
    public void scheduleNotify() {
        Set<Integer> warehouseIds = queryWarehouseIds();
        if (warehouseIds.isEmpty()) {
            logger.info("仓库 id 为空, 跳过后续处理");
            return;
        }
        Map<Integer, List<WarehouseAllocationConfigDTO>> configMap = warehouseAllocationConfigService.listConfigs(warehouseIds);
        for (Integer warehouseId : warehouseIds) {
            logger.info("开始调度仓库 {}", warehouseId);
            // 仓库分仓配置
            List<WarehouseAllocationConfigDTO> configs = configMap.get(warehouseId);
            if (CollectionUtils.isEmpty(configs)) {
                logger.info("仓库分仓配置为空, 跳过后续处理");
                continue;
            }
            configs.forEach(it -> pool.execute(() -> scheduleBySplitConfig(it)));
            logger.info("仓库 {} 调度结束", warehouseId);
        }
    }

    /**
     * 根据分仓配置调度
     *
     * @param config 分仓配置
     */
    private void scheduleBySplitConfig(WarehouseAllocationConfigDTO config) {
        Integer warehouseId = config.getWarehouseId();
        Set<Integer> onlineUserIds = queryOnlineWorkers(config);
        if (onlineUserIds.isEmpty()) {
            logger.info("仓库 {}, 分仓 {} 没有在线分拣工, 跳过后续处理", warehouseId, config.getName());
            return;
        }
        logger.info("开始调度仓库 {}, 分仓 {} 的分拣预警", warehouseId, config.getName());
        int avgOrderCount = pickerShortageWarningMapper.countAvgOrderByFeatureAndTime(getFeature(config), 30, config);
        int leftHours = getLeftHours(config);
        // 现在到截单时间为止的时长, 这里一定是个整数
        BigDecimal needHours = calcAboutNeedHours(avgOrderCount, new BigDecimal(leftHours), config);
        // 预计完成工作时间
        LocalDateTime aboutFinishTime = LocalDateTime.now().plusHours(needHours.intValue()).withMinute(0).withSecond(0);
        // 最晚下班时间
        LocalDateTime lastShiftTime = getLastShiftTime(config);
        logger.info("预计完成工作时间: {}, 最晚下班时间: {}", aboutFinishTime, lastShiftTime);
        long minutes = Duration.between(aboutFinishTime, lastShiftTime).toMinutes();
        if (minutes < 0) {
            logger.info("预计还需要加班 {} 分钟才能完成工作", Math.abs(minutes));
            int orderCount = calcOrderCount(config);
            pickerShortageNotifyPusher.builder()
                    .setWarehouseId(warehouseId)
                    // 预计完成所有工作所需时间
                    .setAboutFinishHours(needHours)
                    // 预计到截单时间后, 当前待分拣单量 + 新增单量
                    .setAboutNewOrderCount(orderCount + (avgOrderCount * leftHours))
                    // 分仓下 待调度, 待拣货的订单数量
                    .setOrderCount(orderCount)
                    // 当前在岗分拣员数量
                    .setOnlineWorkerCount(onlineUserIds.size())
                    // 分仓截单时间
                    .setOrderCutoffTime(config.getTodayOrderCutOffTime())
                    .buildAndPush();
        } else {
            logger.info("预计 {} 分钟后就能完成工作", minutes);
        }
        logger.info("仓库 {}, 分仓 {} 调度结束", warehouseId, config.getName());
    }

    /**
     * 获取现在离截单时间还剩下多少小时
     *
     * @param config 分仓配置
     * @return 现在离截单时间还剩下多少小时
     */
    private int getLeftHours(WarehouseAllocationConfigDTO config) {
        LocalDateTime todayOrderCutOffTime = config.getTodayOrderCutOffTime();
        logger.info("仓库 {}, 分仓 {} 今天的截单时间是: {}", config.getWarehouseId(), config.getName(), todayOrderCutOffTime);
        long leftHours = Duration.between(LocalDateTime.now(), todayOrderCutOffTime).toHours();
        // 如果当前时间已经超过截单时间, 那就将 leftHours 设置成 0
        return leftHours <= 0 ? 0 : (int) leftHours;
    }

    /**
     * 分仓下 待调度, 待拣货的订单数量
     *
     * @param config 分仓配置
     * @return 分仓下 待调度, 待拣货的订单数量
     */
    private int calcOrderCount(WarehouseAllocationConfigDTO config) {
        return pickerShortageWarningMapper.countOrderByFeature(getFeature(config), COUNT_STATE, config);
    }

    /**
     * 计算指定仓库完成分拣还需多少小时<br/>
     * 每小时统计一次当前仓库以及分仓下的本地订单待拣货产品数量（酒饮按件数，休百按行数）,<br/>
     * 加上预计下单量（前30天平均每小时下单量*平均每单件数/行数*当前到截单时间剩余小时数）<br/>
     * 根据当前在岗人数的合计每小时人效，计算完成预计待分拣数量需要的小时数。
     *
     * @param avgOrderCount 前 30 天平均每小时下单数量
     * @param leftHours     到截单时间剩余小时数
     * @param config        分仓配置
     * @return 该仓库完成分拣还需时间 (小时)
     */
    private BigDecimal calcAboutNeedHours(int avgOrderCount, BigDecimal leftHours, WarehouseAllocationConfigDTO config) {
        Integer warehouseId = config.getWarehouseId();
        String name = config.getName();
        int feature = getFeature(config);
        // 当前在岗分拣员 id
        Map<Integer, AdminUserSignDTO> workerInfos = queryOnlineWorkInfos(config);
        // 产品数量
        BigDecimal productCount = BigDecimal.ZERO;
        // 前 30 天平均每单产品数量
        BigDecimal prevAvgProductCount = BigDecimal.ZERO;
        List<PickerEfficiencyBO> efficiency = Collections.emptyList();
        if (feature == FEATURE_TYPE_DRINKING) {
            productCount = pickerShortageWarningMapper.countOrderProductByPackage(config);
            prevAvgProductCount = pickerShortageWarningMapper.countPrevAvgProductCountByPackage(config);
            efficiency = pickerShortageWarningMapper.countDrinkingEfficiency(config);
        } else if (feature == FEATURE_TYPE_REST) {
            productCount = pickerShortageWarningMapper.countOrderProductByLine(config);
            prevAvgProductCount = pickerShortageWarningMapper.countPrevAvgProductCountByLine(config);
            efficiency = pickerShortageWarningMapper.countRestEfficiency(config);
        }
        // 在岗分拣员总人效
        BigDecimal totalEfficiency = efficiency.stream().filter(it -> workerInfos.containsKey(it.getUserId()))
                .map(it -> calcEfficiency(it, workerInfos)).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 预计新下单产品数量
        BigDecimal aboutNewProductCount = new BigDecimal(avgOrderCount).multiply(leftHours).multiply(prevAvgProductCount)
                .setScale(6, RoundingMode.HALF_UP);
        logger.info("仓库 {} 分仓 {}, 现有待拣货产品数量: {}, 前 30 天平均每单产品数量 {}, 在岗分仓分拣工总人效: {}, 预计新下单产品数量: {}",
                warehouseId, name, productCount, prevAvgProductCount, totalEfficiency, aboutNewProductCount
        );
        // 当前分拣任务还需要 x 小时完成, 如果暂无人效, 则算 0
        if (totalEfficiency.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        // 现有产品数量 + 新下单产品数量 / 人效
        return productCount.add(aboutNewProductCount).divide(totalEfficiency, 0, RoundingMode.CEILING);
    }

    /**
     * 计算分拣工人效
     *
     * @param bo          分拣工拣货信息
     * @param workerInfos 分拣工打卡上班信息
     * @return 分拣工人效
     */
    private BigDecimal calcEfficiency(PickerEfficiencyBO bo, Map<Integer, AdminUserSignDTO> workerInfos) {
        int workHours = Optional.ofNullable(workerInfos.get(bo.getUserId())).map(AdminUserSignDTO::getDetails)
                .orElseGet(Collections::emptyList).stream()
                .map(AdminUserSignDetailDTO::getWorkHours).reduce(0, Math::addExact);
        if (workHours == 0) {
            return BigDecimal.ZERO;
        }
        return bo.getAverageCount().divide(new BigDecimal(workHours), 6, RoundingMode.HALF_UP);
    }

    /**
     * 获取分仓最晚下班时间
     *
     * @param config 分仓配置
     * @return 分仓员工最晚下班时间
     */
    private LocalDateTime getLastShiftTime(WarehouseAllocationConfigDTO config) {
        LocalDateTime now = LocalDateTime.now();
        return getSplitWarehouseWorkerInfo(config).values().stream()
                .map(AdminUserSignDTO::getWorkEndTime).filter(Objects::nonNull).max(Comparator.naturalOrder())
                .map(DateUtils::getLocalDateTimeByDate).map(it -> it.withYear(now.getYear()))
                .map(it -> it.withMonth(now.getMonthValue()).withDayOfMonth(now.getDayOfMonth()))
                .orElseGet(() -> now.withHour(0).withMinute(0).withSecond(0));
    }

    /**
     * 获取分仓拣货员的打卡信息
     *
     * @param config 分仓配置
     * @return 分仓拣货员的打卡信息
     */
    private Map<Integer, AdminUserSignDTO> getSplitWarehouseWorkerInfo(WarehouseAllocationConfigDTO config) {
        return adminUserSignService.listUserSignInfo(getConfigUsers(config));
    }

    /**
     * 获取仓库 id, 如果参数指定了仓库 id 则优先使用参数的
     *
     * @return 要生成任务的仓库 id, 只会返回启用分仓配置的仓库 id
     */
    private Set<Integer> queryWarehouseIds() {
        String param = XxlJobContext.getXxlJobContext().getJobParam();
        if (StringUtils.hasText(param)) {
            logger.info("指定灰度仓库调度: {}", param);
            List<Integer> array = JSONArray.parseArray(param, Integer.class);
            if (CollectionUtils.isEmpty(array)) {
                return Collections.emptySet();
            }
            return new HashSet<>(array);
        }
        WarehouseForWMSQueryDTO queryDTO = new WarehouseForWMSQueryDTO();
        queryDTO.setStatus(EnableState.启用.value);
        List<Warehouse> warehouseList = warehouseQueryForWMSService.listForWms(queryDTO).getDataList();
        if (CollectionUtils.isEmpty(warehouseList)) {
            return Collections.emptySet();
        }
        Set<Integer> warehouseIds = warehouseList.stream().map(Warehouse::getId).collect(toSet());
        return warehouseAllocationConfigService.listWarehouseSplitConfig(warehouseIds).entrySet().stream()
                .filter(it -> Boolean.TRUE.equals(it.getValue()))
                .map(Map.Entry::getKey).collect(Collectors.toSet());
    }

    /**
     * 查询分仓下的在线员工 id
     *
     * @param config 分仓配置
     * @return 分仓下的在线员工 id
     */
    private Set<Integer> queryOnlineWorkers(WarehouseAllocationConfigDTO config) {
        return queryOnlineWorkInfos(config).values().stream().map(AdminUserSignDTO::getUserId).collect(Collectors.toSet());
    }

    /**
     * 查询分仓下的在线员工
     *
     * @param config 分仓配置
     * @return 分仓下的在线员工
     */
    private Map<Integer, AdminUserSignDTO> queryOnlineWorkInfos(WarehouseAllocationConfigDTO config) {
        return getSplitWarehouseWorkerInfo(config).values().stream()
                .filter(it -> YesOrNoEnum.YES.valueEquals(it.getIsOnline()))
                .collect(Collectors.toMap(AdminUserSignDTO::getUserId, Function.identity()));
    }

    /**
     * 从分仓配置中取出用户数据
     *
     * @param config 分仓配置
     * @return 用户数据
     */
    private Set<Integer> getConfigUsers(WarehouseAllocationConfigDTO config) {
        return config.getItems().stream()
                .filter(it -> WAREHOUSE_WORKER.valueEquals(it.getRelateType()))
                .map(WarehouseAllocationConfigItemDTO::getRelateId).filter(NumberUtils::isDigits)
                .map(Integer::parseInt).collect(Collectors.toSet());
    }

    private int getFeature(WarehouseAllocationConfigDTO config) {
        WarehouseAllocationConfigType type = WarehouseAllocationConfigType.getEnum(config.getConfigType())
                .orElseThrow(() -> new IllegalArgumentException("不支持的类型: " + config.getConfigType()));
        return (int) (DRINKING.equals(type) ? FEATURE_TYPE_DRINKING : FEATURE_TYPE_REST);
    }

}
