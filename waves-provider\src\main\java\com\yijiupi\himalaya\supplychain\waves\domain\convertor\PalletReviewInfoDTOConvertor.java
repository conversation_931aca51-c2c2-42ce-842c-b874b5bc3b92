package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSorterDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.PalletReviewInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.PalletReviewLocationInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.PalletReviewOrderInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.PalletReviewProductInfoDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
public class PalletReviewInfoDTOConvertor {

    public static PalletReviewInfoDTO convertPalletReviewLocationInfo(List<OutStockOrderPO> restOrderPOS,
        List<OutStockOrderItemPO> drinkItemPOS, List<OutStockOrderPO> outStockOrderPOS,
        Map<Long, List<BatchTaskSorterDTO>> orderIdSorterMap, Map<Long, Set<String>> orderIdBoxCodeNoMap,
        Map<Long, ProductCodeDTO> packageAndUnitCode, List<OrderLocationPalletDTO> palletDTOList,
        Map<Long, Set<String>> otherPalletMap) {

        PalletReviewLocationInfoDTO locationInfoDTO = new PalletReviewLocationInfoDTO();
        locationInfoDTO.setRouteAreaName(getRouteAreaName(outStockOrderPOS));
        locationInfoDTO.setLocationName(palletDTOList.get(0).getLocationName());
        locationInfoDTO.setOtherPalletNos(getOtherPalletNos(otherPalletMap));

        // 酒饮订单产品数据
        List<PalletReviewProductInfoDTO> productInfoDTOS =
            getPalletReviewProductInfoList(drinkItemPOS, packageAndUnitCode, orderIdSorterMap);
        // 休食订单
        List<PalletReviewOrderInfoDTO> orderInfoDTOS =
            getPalletReviewOrderInfoList(restOrderPOS, orderIdBoxCodeNoMap, orderIdSorterMap);
        PalletReviewInfoDTO infoDTO = new PalletReviewInfoDTO();
        infoDTO.setLocationInfo(locationInfoDTO);
        infoDTO.setProductInfos(productInfoDTOS);
        infoDTO.setOrderInfos(orderInfoDTOS);
        return infoDTO;
    }

    private static List<String> getOtherPalletNos(Map<Long, Set<String>> otherPalletMap) {
        if (otherPalletMap == null || otherPalletMap.size() <= 0) {
            return Collections.emptyList();
        }

        return otherPalletMap.values().stream().findFirst().orElse(Collections.emptySet()).stream()
            .collect(Collectors.toList());
    }

    private static String getRouteAreaName(List<OutStockOrderPO> outStockOrderPOS) {
        OutStockOrderPO outStockOrderPO = outStockOrderPOS.stream()
            .filter(p -> !StringUtils.isEmpty(p.getRouteName()) || !StringUtils.isEmpty(p.getAreaName())).findFirst()
            .orElse(new OutStockOrderPO());
        if (StringUtils.isEmpty(outStockOrderPO.getRouteName())) {
            return outStockOrderPO.getAreaName();
        }
        if (StringUtils.isEmpty(outStockOrderPO.getAreaName())) {
            return outStockOrderPO.getRouteName();
        }

        return outStockOrderPO.getRouteName().concat("/").concat(outStockOrderPO.getAreaName());
    }

    private static List<PalletReviewProductInfoDTO> getPalletReviewProductInfoList(List<OutStockOrderItemPO> itemPOS,
        Map<Long, ProductCodeDTO> packageAndUnitCode, Map<Long, List<BatchTaskSorterDTO>> orderIdSorterMap) {
        if (CollectionUtils.isEmpty(itemPOS)) {
            return Collections.emptyList();
        }

        List<PalletReviewProductInfoDTO> productInfoDTOS = new ArrayList<>();
        Map<Long, List<OutStockOrderItemPO>> orderItemSkuMap =
            itemPOS.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid));
        for (Map.Entry<Long, List<OutStockOrderItemPO>> entry : orderItemSkuMap.entrySet()) {
            OutStockOrderItemPO itemPO = entry.getValue().get(0);
            BigDecimal totalCount = entry.getValue().stream().map(OutStockOrderItemPO::getUnittotalcount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal[] count = totalCount.divideAndRemainder(entry.getValue().get(0).getSpecquantity());
            PalletReviewProductInfoDTO productInfoDTO = new PalletReviewProductInfoDTO();
            productInfoDTO.setCountDesc(count[0].stripTrailingZeros().toPlainString() + "大件"
                + count[1].stripTrailingZeros().toPlainString() + "小件");
            productInfoDTO.setSkuId(entry.getKey());
            productInfoDTO.setProductName(itemPO.getProductname());
            productInfoDTO.setSpecName(itemPO.getSpecname());
            ProductCodeDTO productCodeDTO = packageAndUnitCode.get(entry.getKey());
            if (Objects.nonNull(productCodeDTO)) {
                productInfoDTO.setUnitCode(productCodeDTO.getUnitCode());
                productInfoDTO.setPackageCode(productCodeDTO.getPackageCode());
            }
            List<BatchTaskSorterDTO> sorterDTOS = orderIdSorterMap.get(itemPO.getOutstockorderId());
            if (CollectionUtils.isNotEmpty(sorterDTOS)) {
                productInfoDTO.setSorterName(sorterDTOS.get(0).getSorterName());
            }
            productInfoDTOS.add(productInfoDTO);
        }

        return productInfoDTOS;
    }

    private static List<PalletReviewOrderInfoDTO> getPalletReviewOrderInfoList(List<OutStockOrderPO> restOrderPOS,
        Map<Long, Set<String>> orderIdBoxCodeNoMap, Map<Long, List<BatchTaskSorterDTO>> orderIdSorterMap) {
        if (CollectionUtils.isEmpty(restOrderPOS)) {
            return Collections.emptyList();
        }

        List<PalletReviewOrderInfoDTO> orderInfoDTOS = new ArrayList<>();
        restOrderPOS.stream().forEach(order -> {
            PalletReviewOrderInfoDTO orderInfoDTO = new PalletReviewOrderInfoDTO();
            orderInfoDTO.setOrderId(order.getId());
            orderInfoDTO.setOrderNo(order.getReforderno());
            orderInfoDTO.setOrderSequence(order.getOrderSequence());
            Set<String> boxCodeNos = orderIdBoxCodeNoMap.get(order.getId());
            if (CollectionUtils.isNotEmpty(boxCodeNos)) {
                orderInfoDTO.setPackageCount(boxCodeNos.size());
                orderInfoDTO.setBoxCodeNoList(boxCodeNos.stream().collect(Collectors.toList()));
            }
            List<BatchTaskSorterDTO> sorterDTOS = orderIdSorterMap.get(order.getId());
            if (CollectionUtils.isNotEmpty(sorterDTOS)) {
                orderInfoDTO.setSorterName(sorterDTOS.get(0).getSorterName());
            }
            orderInfoDTOS.add(orderInfoDTO);
        });

        return orderInfoDTOS;
    }

}
