/*
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.ordercenter.OrderCenterQueryBL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.OrderCommonDetailDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.OrderPick;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.RecommendOutLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationRuleManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation.bo.OutStockOrderBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;

/**
 * 灰度仓库获取出库位策略
 *
 * <AUTHOR>
 * @since 2023/11/22
 */
@Service
public class OutStockLocationGrayBL extends OutStockLocationBaseBL {

    @Resource
    private OutStockOrderMapper outStockOrderMapper;

    @Resource
    private OrderCenterQueryBL orderCenterQueryBL;

    @Reference
    private ILocationRuleManageService locationRuleManageService;

    private static final Logger logger = LoggerFactory.getLogger(OutStockLocationGrayBL.class);

    @Override
    public boolean support(Integer warehouseId) {
        return true;
    }

    @Override
    public List<OutStockOrderLocationDTO> getOutLocation(List<OutStockOrderLocationDTO> locations) {
        // 这里可能会重
        Map<Long, OutStockOrderLocationDTO> orderLocationMap = locations.stream()
            .collect(Collectors.toMap(OutStockOrderLocationDTO::getOmsOrderId, Function.identity(), (a, b) -> a));
        List<String> businessIds = orderLocationMap.keySet().stream().map(String::valueOf).collect(Collectors.toList());
        Integer warehouseId = locations.get(0).getWarehouseId();
        // 出库单
        List<OutStockOrderPO> outStockOrders =
            outStockOrderMapper.selectByBusinessIdAndWarehouseId(businessIds, warehouseId);
        Map<Long, OrderCommonDetailDTO> centerOrderMap = findOrderCenterByPage(businessIds);
        List<OutStockOrderBO> orders = outStockOrders.stream().map(it -> convertToOutStockOrderBO(centerOrderMap, it))
            .collect(Collectors.toList());
        // 填充前置仓信息
        fillDefaultLocationInfo(orders, locations);
        List<LocationRuleDTO> locRuleList = getLocation(orders);
        if (locRuleList.isEmpty()) {
            // 填充目标仓库信息
            OutStockLocationHelper.fillWarehouseInfo(locations, super::queryWarehouseByIds);
            return locations;
        }
        // 清空ruleId
        locRuleList.forEach(m -> m.setRuleId(null));
        // 查询条件->推荐出库位
        Map<String, List<LocationRuleDTO>> locRuleMap =
            locRuleList.stream().collect(Collectors.groupingBy(OutStockLocationHelper::getOutLocationQueryKey));
        // 4.出库单分组，ruleName为分组条件, 一个分组对应一个波次
        Map<String,
            List<OutStockOrderPO>> orderSelectionMap = outStockOrders.stream()
                .collect(Collectors.groupingBy(it -> Optional.ofNullable(locRuleMap.get(it.getOutLocationQueryKey()))
                    .map(item -> item.get(0)).map(LocationRuleDTO::getRuleName).orElse("null")));
        logger.info("出库位 map: {}", JSON.toJSONString(locRuleMap, SerializerFeature.WriteMapNullValue));
        for (List<OutStockOrderPO> orderList : orderSelectionMap.values()) {
            String key = orderList.get(0).getOutLocationQueryKey();
            List<LocationRuleDTO> locationRule = locRuleMap.get(key);
            if (locationRule == null) {
                logger.warn("订单: {}, 没有查到推荐出库位, key: {}",
                    JSON.toJSONString(orderList, SerializerFeature.WriteMapNullValue), key);
                continue;
            }
            for (OutStockOrderPO orderPO : orderList) {
                String businessId = orderPO.getBusinessId();
                Optional.ofNullable(orderLocationMap.get(Long.valueOf(businessId))).ifPresent(it -> {
                    for (LocationRuleDTO location : locationRule) {
                        if (it.getWarehouseId().equals(location.getWarehouseId())) {
                            it.setLocationId(location.getLocationId());
                            it.setLocationName(location.getLocationName());
                        } else {
                            it.setToWarehouseId(location.getWarehouseId());
                            it.setToLocationId(location.getLocationId());
                            it.setToLocationName(location.getLocationName());
                        }
                    }
                });
            }
        }
        // 填充目标仓库信息
        OutStockLocationHelper.fillWarehouseInfo(locations, super::queryWarehouseByIds);
        return locations;
    }

    private List<LocationRuleDTO> getLocation(List<OutStockOrderBO> orders) {
        try {
            // 2.推荐出库位查询条件
            List<RecommendOutLocationQueryDTO> normalRequest = new ArrayList<>();
            List<RecommendOutLocationQueryDTO> allotRequest = new ArrayList<>();
            for (OutStockOrderBO outStockOrderBO : orders) {
                OutStockOrderPO order = outStockOrderBO.getOrder();
                RecommendOutLocationQueryDTO locRule = new RecommendOutLocationQueryDTO();
                // 地址
                locRule.setAddressId(order.getAddressId().longValue());
                // 片区
                locRule.setAreaId(order.getAreaId());
                locRule.setRouteId(order.getRouteId());
                // 行政区域
                locRule.setProvince(order.getProvince());
                locRule.setCity(order.getCity());
                locRule.setDistrict(order.getCounty());
                locRule.setStreet(order.getStreet());
                // 前置仓仓库
                locRule.setWarehouseId(outStockOrderBO.getToWarehouseId());
                // 中心仓仓库
                locRule.setFromWarehouseId(order.getWarehouseId());
                if (outStockOrderBO.isAllot()) {
                    allotRequest.add(locRule);
                } else {
                    normalRequest.add(locRule);
                }
                // 记录查询条件, 用以替代老的 omsOrderId 做分组
                order.setOutLocationQueryKey(OutStockLocationHelper.getOutLocationQueryKey(locRule));
            }
            logger.info("查询推荐出库位,普通订单: {}, 内配订单: {}", JSON.toJSONString(normalRequest),
                JSON.toJSONString(allotRequest));
            // 3.查询推荐出库位
            List<LocationRuleDTO> normalLocation =
                locationRuleManageService.getOutStockLocationForNormalOrder(normalRequest);
            List<LocationRuleDTO> allotLocation = queryLocationForAllot(allotRequest);
            return Stream.concat(normalLocation.stream(), allotLocation.stream()).collect(Collectors.toList());
        } catch (Exception ex) {
            logger.info("查询推荐出库位异常", ex);
        }
        return Collections.emptyList();
    }

    public Map<Long, OrderCommonDetailDTO> findOrderCenterByPage(List<String> businessIds) {
        List<Long> businessIdStr = businessIds.stream().map(Long::valueOf).collect(Collectors.toList());
        return orderCenterQueryBL.getByOrderIds(businessIdStr).stream()
            .collect(Collectors.toMap(OrderCommonDetailDTO::getOrderId, Function.identity()));
    }

    public OutStockOrderBO convertToOutStockOrderBO(Map<Long, OrderCommonDetailDTO> centerOrderMap,
        OutStockOrderPO order) {
        OrderCommonDetailDTO orderDetail = centerOrderMap.get(Long.parseLong(order.getBusinessId()));
        try {
            return convertToOutStockOrderBO(orderDetail, order);
        } catch (RuntimeException e) {
            logger.error("获取订单仓库信息失败, 入参: {}, {}", JSON.toJSONString(orderDetail), JSON.toJSONString(order));
            throw e;
        }
    }

    @SuppressWarnings("NonAsciiCharacters")
    private OutStockOrderBO convertToOutStockOrderBO(OrderCommonDetailDTO orderDetail, OutStockOrderPO order) {
        boolean isAllot = OrderConstant.ALLOT_TYPE_ALLOCATION.equals(order.getAllotType());
        boolean isInternalDelivery = OutBoundTypeEnum.INTERNAL_DELIVERY_ORDER.valueEquals(order.getOutBoundType());
        boolean isTransfer = OutBoundTypeEnum.ALLOT_ORDER.valueEquals(order.getOutBoundType());
        boolean isAwardOrder = OutStockOrderTypeEnum.兑奖配送单.valueEquals(order.getOrdertype());
        // allotType = 8 或 outboundType = 8 的都是内配
        boolean isNp = isAllot || isInternalDelivery;
        boolean isNPT = isNp && order.getReforderno().startsWith("NPT");
        Integer toWarehouseId = Optional.ofNullable(orderDetail).map(orderInfo -> {
            OrderPick pick = orderInfo.getOrderPick();
            // 调拨单的目的仓是 OrderPick.WarehouseId, 兑奖单没有内配, 仓库就是 warehouseId
            if ((isTransfer && !isNPT) || isAwardOrder) {
                return pick.getWarehouseId().intValue();
            } else {
                int warehouseId = (isNPT ? pick.getFromWarehouseId() : pick.getWarehouseId()).intValue();
                Long checkoutWarehouseId = orderInfo.getOrderSale().getCheckoutWarehouseId();
                // 内配单的前置仓仓库是 checkoutWarehouseId
                if (isNp && checkoutWarehouseId != null && warehouseId != checkoutWarehouseId) {
                    warehouseId = checkoutWarehouseId.intValue();
                }
                return warehouseId;
            }
        }).orElseThrow(() -> new BusinessValidateException("订单 " + order.getBusinessId() + " 在中台不存在"));
        return OutStockOrderBO.of(order, isNp, toWarehouseId);
    }

    /**
     * 查询内配单推荐出库位
     * <ol>
     * <li>按 集货方式 是线路/片区, 去查询前置仓相应的出库位</li>
     * <li>先按 省市区 匹配 中心仓 货位, 都匹配不到再按 集货方式 是 线路/片区 去匹配中心仓货位</li>
     * </ol>
     *
     * @param allotRequest 内配单推荐出库位查询入参
     * @return 查询结果
     */
    private List<LocationRuleDTO> queryLocationForAllot(List<RecommendOutLocationQueryDTO> allotRequest) {
        // 查前置仓出库位 (线路、片区)
        List<LocationRuleDTO> normalLocation =
            locationRuleManageService.getOutStockLocationForNormalOrder(allotRequest);
        // 查中心仓出库位 (省市区)
        List<LocationRuleDTO> allotLocation =
            queryAllotLocations(allotRequest, locationRuleManageService::getOutStockLocationForAllotOrder);
        // 查询条件->推荐出库位
        Map<String, List<LocationRuleDTO>> locRuleMap = Stream.concat(normalLocation.stream(), allotLocation.stream())
            .collect(Collectors.groupingBy(OutStockLocationHelper::getOutLocationQueryKey));
        // 如果没匹配到, 再取中心仓按线路/片区的出库位
        List<RecommendOutLocationQueryDTO> notFoundRequest = allotRequest.stream()
            .filter(it -> !locRuleMap.containsKey(OutStockLocationHelper.getOutLocationQueryKey(it)))
            .collect(Collectors.toList());
        List<LocationRuleDTO> foundRule = allotRequest.stream()
            .filter(it -> locRuleMap.containsKey(OutStockLocationHelper.getOutLocationQueryKey(it)))
            .map(it -> locRuleMap.get(OutStockLocationHelper.getOutLocationQueryKey(it))).flatMap(Collection::stream)
            .collect(Collectors.toList());
        List<LocationRuleDTO> extraRule =
            queryAllotLocations(notFoundRequest, locationRuleManageService::getOutStockLocationForNormalOrder);
        return Stream.concat(foundRule.stream(), extraRule.stream()).collect(Collectors.toList());
    }

    /**
     * 填充默认货位信息
     *
     * @param orders 订单信息
     * @param locations 货位信息
     */
    private void fillDefaultLocationInfo(List<OutStockOrderBO> orders, List<OutStockOrderLocationDTO> locations) {
        Map<String, OutStockOrderBO> orderBOMap =
            orders.stream().collect(Collectors.toMap(it -> it.getOrder().getBusinessId(), Function.identity()));
        for (OutStockOrderLocationDTO location : locations) {
            OutStockOrderBO outStockOrderBO = orderBOMap.get(String.valueOf(location.getOmsOrderId()));
            if (outStockOrderBO == null) {
                continue;
            }
            Integer toWarehouseId = outStockOrderBO.getToWarehouseId();
            Integer warehouseId = outStockOrderBO.getOrder().getWarehouseId();
            if (!Objects.equals(toWarehouseId, warehouseId)) {
                location.setToWarehouseId(toWarehouseId);
            }
        }
    }

    /**
     * 调转 fromWarehouseId 和 warehouseId 查询推荐出库位
     *
     * @param allotRequest 查询请求
     * @param queryFunction 查询方法
     * @return 查询结果
     */
    private List<LocationRuleDTO> queryAllotLocations(List<RecommendOutLocationQueryDTO> allotRequest,
        Function<List<RecommendOutLocationQueryDTO>, List<LocationRuleDTO>> queryFunction) {
        if (CollectionUtils.isEmpty(allotRequest)) {
            return Collections.emptyList();
        }
        List<RecommendOutLocationQueryDTO> deepCopyRequest =
            JSON.parseArray(JSON.toJSONString(allotRequest), RecommendOutLocationQueryDTO.class);
        Map<Integer, List<RecommendOutLocationQueryDTO>> allotRequestMap =
            deepCopyRequest.stream().collect(Collectors.groupingBy(RecommendOutLocationQueryDTO::getFromWarehouseId));
        return allotRequestMap.entrySet().stream().map(it -> {
            // 中心仓仓库 id, 为了查询中心仓的省市区、线路片区 出库位, 这里需要交换一下仓库 id
            Integer fromWarehouseId = it.getKey();
            List<RecommendOutLocationQueryDTO> queryList = it.getValue();
            for (RecommendOutLocationQueryDTO query : queryList) {
                query.setWarehouseId(fromWarehouseId);
            }
            return queryFunction.apply(queryList);
        }).filter(it -> !CollectionUtils.isEmpty(it)).flatMap(Collection::stream).collect(Collectors.toList());
    }

}
