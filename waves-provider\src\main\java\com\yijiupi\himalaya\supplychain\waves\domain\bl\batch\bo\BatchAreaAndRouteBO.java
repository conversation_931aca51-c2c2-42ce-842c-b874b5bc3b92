package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/8/28
 */
public class BatchAreaAndRouteBO {

    private String areaId;

    private String areaName;

    private String routeId;

    private String routeName;

    private Integer routeSequence;

    public BatchAreaAndRouteBO() {
    }

    public BatchAreaAndRouteBO(String areaName, String routeName) {
        this.areaName = areaName;
        this.routeName = routeName;
    }

    /**
     * 获取
     *
     * @return areaId
     */
    public String getAreaId() {
        return this.areaId;
    }

    /**
     * 设置
     *
     * @param areaId
     */
    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    /**
     * 获取
     *
     * @return areaName
     */
    public String getAreaName() {
        return this.areaName;
    }

    /**
     * 设置
     *
     * @param areaName
     */
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    /**
     * 获取
     *
     * @return routeId
     */
    public String getRouteId() {
        return this.routeId;
    }

    /**
     * 设置
     *
     * @param routeId
     */
    public void setRouteId(String routeId) {
        this.routeId = routeId;
    }

    /**
     * 获取
     *
     * @return routeName
     */
    public String getRouteName() {
        return this.routeName;
    }

    /**
     * 设置
     *
     * @param routeName
     */
    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    /**
     * 获取
     *
     * @return routeSequence
     */
    public Integer getRouteSequence() {
        return this.routeSequence;
    }

    /**
     * 设置
     *
     * @param routeSequence
     */
    public void setRouteSequence(Integer routeSequence) {
        this.routeSequence = routeSequence;
    }
}
