package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Sets;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderTraceBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.RestockRequestParam;
import com.yijiupi.himalaya.supplychain.waves.dto.trace.MessagePushParam;
import com.yijiupi.himalaya.supplychain.waves.enums.PushMessageConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yijiupi.supplychain.serviceutils.constant.AdminUserAuthRoleConstant.*;

/**
 * <AUTHOR>
 * @since 2024-09-04 16:46
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class RestockRequestBL {

    @Resource
    private OrderTraceBL orderTraceBL;

    @Reference
    private IProductLocationService productLocationService;

    @Reference
    private IProductSkuQueryService productSkuQueryService;

    private static final String TITLE = "分拣呼叫补货通知";

    private static final String TEMPLATE = "%s (货位: %s, 存储货位: %s) 需要补货, 请及时处理。";

    private static final Set<String> ROLE_CODES = Sets.newHashSet(
            ROLE_CODE_仓库管理员新流程, ROLE_CODE_补货人
    );

    private static final Logger logger = LoggerFactory.getLogger(RestockRequestBL.class);

    /**
     * 分拣呼叫补货<br/> 给仓管发送一条补货通知
     *
     * @param param 通知参数
     */
    public void sendRequestMessage(RestockRequestParam param) {
        if (param.getItems().stream().anyMatch(it -> it.getSkuId() == null)) {
            logger.info("存在 skuId 为 null 的数据, 不发送通知消息");
            return;
        }
        Map<Long, List<RestockRequestParam.RequestItems>> itemMap = param.getItems().stream()
                .collect(Collectors.groupingBy(RestockRequestParam.RequestItems::getSkuId));
        List<Long> skuIds = new ArrayList<>(itemMap.keySet());
        Integer warehouseId = param.getWarehouseId();
        Map<Long, List<LoactionDTO>> skuLocationMap = productLocationService.findLocationDTOBySkuId(warehouseId, skuIds);
        Map<Long, ProductSkuDTO> skuMap = productSkuQueryService.findBySku(skuIds).stream()
                .collect(Collectors.toMap(ProductSkuDTO::getProductSkuId, Function.identity(), (a, b) -> a));
        for (Map.Entry<Long, List<RestockRequestParam.RequestItems>> entry : itemMap.entrySet()) {
            Long skuId = entry.getKey();
            String storeLocation = skuLocationMap.getOrDefault(skuId, Collections.emptyList()).stream()
                    .filter(it -> Objects.equals(LocationEnum.存储位.getType().byteValue(), it.getSubcategory()))
                    .map(LoactionDTO::getName).collect(Collectors.joining(","));
            ProductSkuDTO sku = skuMap.get(skuId);
            if (sku == null) {
                continue;
            }
            for (RestockRequestParam.RequestItems item : entry.getValue()) {
                String locationName = item.getLocationName();
                String productName = String.format("%s (%s)", sku.getName(), sku.getSpecificationName());
                pushMessage(warehouseId, productName, locationName, storeLocation);
            }
        }
    }

    private void pushMessage(Integer warehouseId, String productName, String location, String storeLocation) {
        String content = String.format(TEMPLATE, productName, location, storeLocation);
        MessagePushParam pushParam = MessagePushParam.of(ROLE_CODES, warehouseId, content);
        pushParam.setTitle(TITLE);
        pushParam.setAccountPushType(PushMessageConstants.ACCOUNT_TYPE_TO_ALL_DEVICE);
        pushParam.setExtraMap(PushMessageConstants.cacheMessageExtra());
        orderTraceBL.pushMessageByRole(pushParam);
    }

}
