package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;

import org.assertj.core.api.Assertions;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.BatchOrderChangeBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemPackageReviewDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemPickCompleteDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemPickCompleteDetailQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemUpdateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.CancelOutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemProcessChangeDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderProcessChangeDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.OrderChangeTypeEnum;

/**
 * Created by 余明 on 2018-05-15.
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class BatchOrderTaskBLTest {

    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;

    @Test
    public void updateBatch() {
        String json =
            "[{\"id\":\"2018051500015\",\"batchNo\":\"BC999118051500003\",\"batchTaskNo\":\"BT999118051500003\",\"createTime\":\"2018-05-15 14:11:40\",\"sorter\":\"张庆\",\"sorterId\":126,\"orderAmount\":10800.00,\"orderCount\":1,\"skuCount\":1,\"packageAmount\":3,\"unitAmount\":0,\"taskState\":2,\"taskStateName\":\"已完成\",\"goodsCount\":\"3大件0小件\",\"printTimes\":0,\"isPrinted\":false,\"pickingType\":1,\"pickingTypeName\":\"按订单拣货\",\"items\":null}]";
        List<BatchTaskDTO> msgs = JSON.parseArray(json, BatchTaskDTO.class);
        batchOrderTaskBL.updateBatch(msgs, null);
    }

    @Test
    public void lockTest() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        Thread[] threads = new Thread[10];
        for (int i = 0; i < threads.length; i++) {
            threads[i] = new Thread() {
                @Override
                public void run() {
                    try {
                        latch.await();
                    } catch (Exception e) {
                        System.out.println(e.getMessage());
                    }
                    System.err.println(Thread.currentThread() + "开始执行");
                    batchOrderTaskBL.updateBatchTaskItem(null, "123456", null, null, null, null, null, null, null);
                    // driverMarkBL.lock();
                    System.err.println(Thread.currentThread() + "执行==");
                }
            };
        }
        for (int i = 0; i < threads.length; i++) {
            threads[i].start();
        }
        latch.countDown();
        CountDownLatch latch2 = new CountDownLatch(1);
        latch2.await();
    }

    @Autowired
    private BatchTaskMapper batchTaskMapper;

    @Test
    public void listBatchTaskTest() {
        BatchTaskQueryDTO btQueryDTO = new BatchTaskQueryDTO();
        btQueryDTO.setWarehouseId(9981);
        btQueryDTO.setRefOrderNoList(Collections.singletonList("CK9982204061105562"));
        btQueryDTO.setQueryItem(false);
        List<BatchTaskDTO> batchTaskDTOS = batchOrderTaskBL.listBatchTask(btQueryDTO);
        System.out.println(batchTaskDTOS);
    }

    @Test
    public void a() {
        List<BatchTaskPO> bt998122041500004 =
            batchTaskMapper.findTasksByBatchTaskNo(Arrays.asList("BT998122041500004"));
        System.out.println(bt998122041500004);
    }

    @Test
    public void batchTaskCompleteTest() {
        List<BatchTaskItemUpdateDTO> batchTaskItemList = new ArrayList<>();

        BatchTaskItemUpdateDTO batchTaskItemDTO = new BatchTaskItemUpdateDTO();
        batchTaskItemDTO.setId("2022110300032");
        // 可为空，不为空有逻辑
        // batchTaskItemDTO.setBoxCodeList();
        batchTaskItemDTO.setOverSortPackageCount(BigDecimal.ONE);
        batchTaskItemDTO.setOverSortUnitCount(BigDecimal.ZERO);
        batchTaskItemDTO.setLackPackageCount(BigDecimal.ONE);
        batchTaskItemDTO.setLackUnitCount(BigDecimal.ZERO);
        batchTaskItemDTO.setStartTime(new Date());
        batchTaskItemDTO.setCompleteTime(new Date());

        batchTaskItemList.add(batchTaskItemDTO);

        String batchTaskId = "2022110300031";
        String userName = "王新军";
        Integer warehouseId = 9981;
        Long locationId = null;
        String locationName = "";
        Integer cityId = 998;
        Integer userId = 175;
        Byte containerFlag = 0;

        // Map<String, List<BatchTaskDTO>> map = batchOrderTaskBL.updateBatchTaskItem(batchTaskItemList, batchTaskId,
        // userName, warehouseId, locationId, locationName, cityId, userId, containerFlag);
        // Assertions.assertThat(map).isNotNull();

        Integer sorterId = 1111111;
        boolean result = "1111111".equals(sorterId);
        System.err.println(result);
    }

    @Test
    public void findCompleteBatchTaskItemTest() {
        BatchTaskItemPickCompleteDetailQueryDTO queryDTO = new BatchTaskItemPickCompleteDetailQueryDTO();
        // 2022121400135 2022121400140
        queryDTO.setBatchTaskId("2022121400140");
        BatchTaskItemPickCompleteDetailDTO detailDTO = batchOrderTaskBL.findCompleteBatchTaskItem(queryDTO);

        Assertions.assertThat(detailDTO).isNotNull();
    }

    @Test
    public void findPackageReviewBatchItemTest() {
        BatchTaskItemPickCompleteDetailQueryDTO queryDTO = new BatchTaskItemPickCompleteDetailQueryDTO();
        queryDTO.setBatchTaskId("2022122900536");
        List<BatchTaskItemPackageReviewDetailDTO> detailDTOS = batchOrderTaskBL.findPackageReviewBatchItem(queryDTO);

        Assert.assertNotNull(detailDTOS);
    }

    @Autowired
    private BatchOrderBL batchOrderBL;
    @Autowired
    private BatchOrderChangeBL batchOrderChangeBL;

    // 待入库的
    @Test
    public void waitProcessOrderChangeWithBatchTest() {
        List<OutStockOrderProcessChangeDTO> processChangeDTOS = new ArrayList<>();
        OutStockOrderProcessChangeDTO outStockOrderProcessChangeDTO = new OutStockOrderProcessChangeDTO();
        outStockOrderProcessChangeDTO.setRefOrderNo("************");
        outStockOrderProcessChangeDTO.setId(5183045471431405824L);
        outStockOrderProcessChangeDTO.setCityId(998);
        outStockOrderProcessChangeDTO.setWarehouseId(9981);

        List<OutStockOrderItemProcessChangeDTO> itemList = new ArrayList<>();
        OutStockOrderItemProcessChangeDTO itemChangeDTO = new OutStockOrderItemProcessChangeDTO();
        itemChangeDTO.setId(5183045471666653453L);
        itemChangeDTO.setSkuId(5038459890122859460L);
        itemChangeDTO.setUnitTotalCount(BigDecimal.valueOf(2));
        itemChangeDTO.setDiffCount(BigDecimal.valueOf(2));

        itemList.add(itemChangeDTO);

        outStockOrderProcessChangeDTO.setItemList(itemList);

        processChangeDTOS.add(outStockOrderProcessChangeDTO);
        String operatorUser = "125";
        Byte changeType = OrderChangeTypeEnum.订单取消.getType();

        CancelOutStockOrderDTO cancelOutStockOrderDTO = new CancelOutStockOrderDTO();
        cancelOutStockOrderDTO.setProcessChangeDTOS(processChangeDTOS);
        cancelOutStockOrderDTO.setOptUserId("125");
        // batchOrderChangeBL.cancelOutStockOrder(cancelOutStockOrderDTO);
        // batchOrderBL.waitProcessOrderChangeWithBatch(processChangeDTOS, operatorUser, changeType);
    }

    // 已拣货的
    @Test
    public void waitProcessOrderChangeWithBatchHasPickedTest() {
        List<OutStockOrderProcessChangeDTO> processChangeDTOS = new ArrayList<>();
        OutStockOrderProcessChangeDTO outStockOrderProcessChangeDTO = new OutStockOrderProcessChangeDTO();
        outStockOrderProcessChangeDTO.setRefOrderNo("164315700012");
        outStockOrderProcessChangeDTO.setId(5197899986800980759L);
        outStockOrderProcessChangeDTO.setCityId(164);
        outStockOrderProcessChangeDTO.setWarehouseId(1641);

        List<OutStockOrderItemProcessChangeDTO> itemList = new ArrayList<>();
        OutStockOrderItemProcessChangeDTO itemChangeDTO = new OutStockOrderItemProcessChangeDTO();
        itemChangeDTO.setId(5197899986961568409L);
        itemChangeDTO.setSkuId(5153340891621115080L);
        itemChangeDTO.setUnitTotalCount(BigDecimal.valueOf(48));
        itemChangeDTO.setDiffCount(BigDecimal.valueOf(48));

        itemList.add(itemChangeDTO);

        outStockOrderProcessChangeDTO.setItemList(itemList);

        processChangeDTOS.add(outStockOrderProcessChangeDTO);
        String operatorUser = "125";
        Byte changeType = OrderChangeTypeEnum.订单取消.getType();

        CancelOutStockOrderDTO cancelOutStockOrderDTO = new CancelOutStockOrderDTO();
        cancelOutStockOrderDTO.setProcessChangeDTOS(processChangeDTOS);
        cancelOutStockOrderDTO.setOptUserId("125");
        // batchOrderChangeBL.cancelOutStockOrder(cancelOutStockOrderDTO);
        // batchOrderBL.waitProcessOrderChangeWithBatch(processChangeDTOS, operatorUser, changeType);
    }

}