package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 团购订单作业进度对象明细
 *
 * <AUTHOR>
 * @date 2/5/21 5:12 PM
 */
public class GroupBuyWorkScheduleItemDTO implements Serializable {

    private static final long serialVersionUID = -1178329940483092839L;

    /**
     * 波次id
     */
    private String batchId;

    /**
     * 任务类型 0：摘果 1：播种
     */
    private Byte taskType;

    /**
     * skuId
     */
    private Long productSkuId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 总数量
     */
    private BigDecimal totalCount;

    /**
     * 已播数量
     */
    private BigDecimal overSortCount;

    /**
     * 缺货数量
     */
    private BigDecimal lackCount;

    /**
     * 瓶码集合
     */
    private List<String> unitCode;

    /**
     * 箱码集合
     */
    private List<String> packageCode;

    public List<String> getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(List<String> unitCode) {
        this.unitCode = unitCode;
    }

    public List<String> getPackageCode() {
        return packageCode;
    }

    public void setPackageCode(List<String> packageCode) {
        this.packageCode = packageCode;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public Byte getTaskType() {
        return taskType;
    }

    public void setTaskType(Byte taskType) {
        this.taskType = taskType;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getOverSortCount() {
        return overSortCount;
    }

    public void setOverSortCount(BigDecimal overSortCount) {
        this.overSortCount = overSortCount;
    }

    public BigDecimal getLackCount() {
        return lackCount;
    }

    public void setLackCount(BigDecimal lackCount) {
        this.lackCount = lackCount;
    }
}
