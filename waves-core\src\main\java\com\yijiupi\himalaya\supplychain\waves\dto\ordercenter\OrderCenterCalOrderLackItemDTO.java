package com.yijiupi.himalaya.supplychain.waves.dto.ordercenter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/8/15
 */
public class OrderCenterCalOrderLackItemDTO implements Serializable {
    /**
     * 订单明细id
     */
    private Long orderItemId;
    /**
     * 小单位数量
     */
    private BigDecimal unitCount;

    /**
     * 获取 订单明细id
     *
     * @return orderItemId 订单明细id
     */
    public Long getOrderItemId() {
        return this.orderItemId;
    }

    /**
     * 设置 订单明细id
     *
     * @param orderItemId 订单明细id
     */
    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    /**
     * 获取 小单位数量
     *
     * @return unitCount 小单位数量
     */
    public BigDecimal getUnitCount() {
        return this.unitCount;
    }

    /**
     * 设置 小单位数量
     *
     * @param unitCount 小单位数量
     */
    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }
}
