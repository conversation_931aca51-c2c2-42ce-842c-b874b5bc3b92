package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.BeanUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO;

public class OutStockOrderItemConverter {

    public static List<OutStockOrderItemDTO> poList2DTOList(List<OutStockOrderItemPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return null;
        }
        List<OutStockOrderItemDTO> dtoList = new ArrayList<OutStockOrderItemDTO>(poList.size());
        poList.stream().filter(Objects::nonNull).forEach(po -> {
            OutStockOrderItemDTO dto = new OutStockOrderItemDTO();
            BeanUtils.copyProperties(po, dto);
            dto.setOrg_id(po.getOrgId());
            dto.setOutstockorder_Id(po.getOutstockorderId());
            dto.setUnitTotalCount(po.getUnittotalcount());
            dto.setSkuId(po.getSkuid());
            dto.setSaleSpecQuantity(po.getSalespecquantity());
            dto.setSpecQuantity(po.getSpecquantity());
            dto.setSpecName(po.getSpecname());
            // 剩余字段还需注意是否需要设置 TODO
            // dto.setOutStockOrderItemDetailDTOS(
            // OutStockOrderItemDetailConverter.toOutStockOrderItemDetailDTOList(po.getOutStockOrderItemDetailPOS()));
            dtoList.add(dto);
        });
        return dtoList;
    }

}
