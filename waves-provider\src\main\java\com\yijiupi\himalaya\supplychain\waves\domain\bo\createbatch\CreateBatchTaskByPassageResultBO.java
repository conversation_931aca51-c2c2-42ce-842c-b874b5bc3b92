package com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskFinalResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 通过通道创建拣货任务的结果BO
 * 
 * <AUTHOR>
 * @date 2024/11/24
 */
public class CreateBatchTaskByPassageResultBO {
    /**
     * 创建拣货任务信息
     */
    private List<WaveCreateDTO> waveCreateDTOList;
    /**
     * 播种任务列表 <br/>
     * 走createBatchTaskByPassage方法的时候，已经插入了<br />
     * FIXME
     */
    private List<SowTaskPO> sowTaskPOList;
    /**
     * 播种订单列表 <br />
     * 走createBatchTaskByPassage方法的时候，已经插入了
     */
    private List<SowOrderPO> sowOrderPOList;
    /**
     * 处理已存在的播种任务
     */
    private List<CreateSowTaskResultBO> existSowTaskList;

    /**
     * 获取 创建拣货任务信息
     *
     * @return waveCreateDTOList 创建拣货任务信息
     */
    public List<WaveCreateDTO> getWaveCreateDTOList() {
        return this.waveCreateDTOList;
    }

    /**
     * 设置 创建拣货任务信息
     *
     * @param waveCreateDTOList 创建拣货任务信息
     */
    public void setWaveCreateDTOList(List<WaveCreateDTO> waveCreateDTOList) {
        this.waveCreateDTOList = waveCreateDTOList;
    }

    /**
     * 获取 播种任务列表
     *
     * @return sowTaskPOList 播种任务列表
     */
    public List<SowTaskPO> getSowTaskPOList() {
        return this.sowTaskPOList;
    }

    /**
     * 设置 播种任务列表
     *
     * @param sowTaskPOList 播种任务列表
     */
    public void setSowTaskPOList(List<SowTaskPO> sowTaskPOList) {
        this.sowTaskPOList = sowTaskPOList;
    }

    /**
     * 获取 播种订单列表
     *
     * @return sowOrderPOList 播种订单列表
     */
    public List<SowOrderPO> getSowOrderPOList() {
        return this.sowOrderPOList;
    }

    /**
     * 设置 播种订单列表
     *
     * @param sowOrderPOList 播种订单列表
     */
    public void setSowOrderPOList(List<SowOrderPO> sowOrderPOList) {
        this.sowOrderPOList = sowOrderPOList;
    }

    /**
     * 获取 处理已存在的播种任务
     *
     * @return existSowTaskList 处理已存在的播种任务
     */
    public List<CreateSowTaskResultBO> getExistSowTaskList() {
        return this.existSowTaskList;
    }

    /**
     * 设置 处理已存在的播种任务
     *
     * @param existSowTaskList 处理已存在的播种任务
     */
    public void setExistSowTaskList(List<CreateSowTaskResultBO> existSowTaskList) {
        this.existSowTaskList = existSowTaskList;
    }

    public void fillExistSowTaskList(List<CreateSowTaskFinalResultBO> finalResultBOList) {
        if (CollectionUtils.isEmpty(finalResultBOList)) {
            return;
        }
        List<CreateSowTaskResultBO> existSowTaskList =
            finalResultBOList.stream().map(CreateSowTaskFinalResultBO::getExistSowTaskList)
                .filter(com.alibaba.dubbo.common.utils.CollectionUtils::isNotEmpty).flatMap(Collection::stream)
                .collect(Collectors.toList());
        setExistSowTaskList(existSowTaskList);
    }

    public static CreateBatchTaskByPassageResultBO getDefault() {
        CreateBatchTaskByPassageResultBO result = new CreateBatchTaskByPassageResultBO();
        result.setWaveCreateDTOList(new ArrayList<>(0));
        result.setSowTaskPOList(new ArrayList<>(0));
        result.setSowOrderPOList(new ArrayList<>(0));
        result.setExistSowTaskList(new ArrayList<>(0));

        return result;
    }

    public static CreateBatchTaskByPassageResultBO
        buildCreateBatchTaskByPassageResultBO(List<CreateSowTaskFinalResultBO> finalResultBOList) {

        if (CollectionUtils.isEmpty(finalResultBOList)) {
            return CreateBatchTaskByPassageResultBO.getDefault();
        }

        List<CreateSowTaskResultBO> normalSowTaskList =
            finalResultBOList.stream().filter(m -> !CollectionUtils.isEmpty(m.getNormalSowTaskList()))
                .flatMap(m -> m.getNormalSowTaskList().stream()).collect(Collectors.toList());

        List<WaveCreateDTO> notSowWaveCreateList =
            finalResultBOList.stream().filter(m -> !CollectionUtils.isEmpty(m.getNotSowWaveCreateList()))
                .flatMap(m -> m.getNotSowWaveCreateList().stream()).collect(Collectors.toList());

        List<SowTaskPO> sowTaskPOList =
            normalSowTaskList.stream().flatMap(m -> m.getSowTaskPOList().stream()).collect(Collectors.toList());
        List<SowOrderPO> sowOrderPOList =
            normalSowTaskList.stream().flatMap(m -> m.getSowOrderPOList().stream()).collect(Collectors.toList());
        List<WaveCreateDTO> waveCreateDTOList =
            normalSowTaskList.stream().flatMap(m -> m.getWaveCreateDTOList().stream()).collect(Collectors.toList());

        List<WaveCreateDTO> totalWaveCreateDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(waveCreateDTOList)) {
            totalWaveCreateDTOList.addAll(waveCreateDTOList);
        }
        if (!CollectionUtils.isEmpty(notSowWaveCreateList)) {
            totalWaveCreateDTOList.addAll(notSowWaveCreateList);
        }

        CreateBatchTaskByPassageResultBO createBatchTaskByPassageBO = new CreateBatchTaskByPassageResultBO();
        createBatchTaskByPassageBO.setSowOrderPOList(sowOrderPOList);
        createBatchTaskByPassageBO.setSowTaskPOList(sowTaskPOList);
        createBatchTaskByPassageBO.setWaveCreateDTOList(totalWaveCreateDTOList);
        createBatchTaskByPassageBO.fillExistSowTaskList(finalResultBOList);

        return createBatchTaskByPassageBO;
    }
}
