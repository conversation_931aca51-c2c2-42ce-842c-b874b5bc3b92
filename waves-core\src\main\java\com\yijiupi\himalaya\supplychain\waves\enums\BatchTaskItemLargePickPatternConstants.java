package com.yijiupi.himalaya.supplychain.waves.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @title: BatchTaskItemLargePickPatternConstants
 * @description:
 * @date 2023-02-17 10:14
 */
public class BatchTaskItemLargePickPatternConstants {

    /**
     * 无
     */
    public static Byte LARGE_PICK_NONE = 0;
    /**
     * 单项
     */
    public static Byte LARGE_PICK_WHOLE = 1;
    /**
     * 多项
     */
    public static Byte LARGE_PICK_MANY = 2;
    /**
     * 小件--人
     */
    public static Byte LARGE_PICK_SMALL_HUMAN = 3;
    /**
     * 小件--机器
     */
    public static Byte LARGE_PICK_SMALL_ROBOT = 4;

    public static boolean isLargePick(Byte largePick) {
        if (Objects.isNull(largePick)) {
            return Boolean.FALSE;
        }

        if (LARGE_PICK_WHOLE.equals(largePick)) {
            return Boolean.TRUE;
        }
        if (LARGE_PICK_MANY.equals(largePick)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

}
