package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import static com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum.分拣中;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskFinishHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishNeedOpBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/15
 */
@Service
public class BatchTaskItemFinishBatchTaskDecoratorBL extends BatchTaskItemFinishDecoratorBL {

    @Override
    public int getOrder() {
        return MIDDLE_PRECEDENCE - 10;
    }

    @Override
    protected void doCompleteBatchTaskItem(BatchTaskItemFinishNeedOpBO bo,
        BatchTaskFinishHelperBO batchTaskFinishHelperBO, List<BatchTaskItemDTO> needCompleteBatchTaskItemList) {
        BatchTaskPO updateBatchTaskPO = new BatchTaskPO();
        BatchTaskPO batchTaskPO = batchTaskFinishHelperBO.getBatchTaskPO();

        List<BatchTaskItemDTO> totalBatchTaskItemList = batchTaskFinishHelperBO.getTotalBatchTaskItemList();
        Map<String, BatchTaskItemCompleteDTO> map = batchTaskFinishHelperBO.getBatchTaskItemCompleteDTOMap();
        updateBatchTaskPO.setId(batchTaskPO.getId());
        updateBatchTaskPO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
        updateBatchTaskPO.setOrgId(batchTaskPO.getOrgId());
        updateBatchTaskPO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
        updateBatchTaskPO.setTaskState(getTaskState(totalBatchTaskItemList, map));

        Long sowLocationId = batchTaskFinishHelperBO.getSowLocationId();
        BatchTaskItemFinishBO batchTaskItemFinishBO = batchTaskFinishHelperBO.getBatchTaskItemFinishBO();
        Long locationId = batchTaskItemFinishBO.getLocationId();
        String locationName = batchTaskItemFinishBO.getLocationName();

        // 存在集货位时，出库位设置为空
        if (sowLocationId != null) {
            locationId = null;
            locationName = null;
        }
        updateBatchTaskPO.setLocationId(locationId);
        updateBatchTaskPO.setLocationName(locationName);

        bo.setCompleteBatchTaskPO(updateBatchTaskPO);
    }

    private byte getTaskState(List<BatchTaskItemDTO> totalBatchTaskItemList,
        Map<String, BatchTaskItemCompleteDTO> map) {
        boolean anyNotFinish = totalBatchTaskItemList.stream()
            // 找出没在提交的拣货任务明细里的
            .filter(item -> Objects.isNull(map.get(item.getId())))
            .anyMatch(item -> TaskStateEnum.已完成.getType() != item.getTaskState());
        if (anyNotFinish) {
            return 分拣中.getType();
        }

        return TaskStateEnum.已完成.getType();
    }
}
