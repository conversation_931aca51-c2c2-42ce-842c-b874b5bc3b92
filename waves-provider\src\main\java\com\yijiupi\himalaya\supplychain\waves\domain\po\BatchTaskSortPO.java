package com.yijiupi.himalaya.supplychain.waves.domain.po;

import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 波次任务
 *
 * <AUTHOR> 2018/3/16
 */
public class BatchTaskSortPO implements Serializable {
    /**
     * id
     */
    private String id;
    /**
     * 波次任务号
     */
    private String batchTaskNo;
    /**
     * 波次编号
     */
    private String batchNo;
    /**
     * 分拣员id
     */
    private Integer sorterId;
    /**
     * 商品种类数量
     */
    private Integer skuCount;
    /**
     * 大件总数
     */
    private BigDecimal packageAmount;
    /**
     * 小件总数
     */
    private BigDecimal unitAmount;
    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte taskState;
    /**
     * 拣货方式 1 按订单拣货 2 按产品拣货',
     */
    private Byte pickingType;
    /**
     * 拣货分组策略 1货区 2货位 3类目',
     */
    private Byte pickingGroupStrategy;
    /**
     * 类目名称
     */
    private String categoryName;
    /**
     * 货位或者货区名称
     */
    private String locationName;

    /**
     * 波次任务生产时间
     */
    private Date createTime;

    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 线路名称
     */
    private String routeName;
    /**
     * 订单筛选 1 按区域 2 按线路
     */
    private Byte orderSelection;

    /**
     * 拣货属性
     */
    private String batchTaskName;

    /**
     * 订单数量
     */
    private Integer orderCount;

    /**
     * 分区id
     */
    private Long sortGroupId;

    /**
     * 备货位id
     */
    private Long toLocationId;

    /**
     * 备货位名称
     */
    private String toLocationName;

    /**
     * 领取标识 0：不可领取 1：整单领取 2：拆分领取
     */
    private Byte receiveFlag;
    /**
     * 关联播种任务Id
     */
    private Long sowTaskId;

    /**
     * 播种类型 1-总单播种 2-二次分拣 3-快速播种
     */
    private Integer sowType = 1;
    /**
     * 通道ID
     */
    private Long passageId;
    /**
     * 备注(暂存容器数量)
     */
    private String remark;
    /**
     * 波次名称
     */
    private String batchName;

    /**
     * 拣货方式:1、默认；2、电子标签拣货
     *
     * @see BatchTaskKindOfPickingConstants
     */
    private Byte kindOfPicking;
    /**
     * @see BatchTaskPickPatternEnum
     */
    private Byte pickPattern;

    public Long getPassageId() {
        return passageId;
    }

    public void setPassageId(Long passageId) {
        this.passageId = passageId;
    }

    public Integer getSowType() {
        return sowType;
    }

    public void setSowType(Integer sowType) {
        this.sowType = sowType;
    }

    public Byte getReceiveFlag() {
        return receiveFlag;
    }

    public void setReceiveFlag(Byte receiveFlag) {
        this.receiveFlag = receiveFlag;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public Long getSortGroupId() {
        return sortGroupId;
    }

    public void setSortGroupId(Long sortGroupId) {
        this.sortGroupId = sortGroupId;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    /**
     * 获取 id
     *
     * @return id id
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 id
     *
     * @param id id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 波次任务号
     *
     * @return batchTaskNo 波次任务号
     */
    public String getBatchTaskNo() {
        return this.batchTaskNo;
    }

    /**
     * 设置 波次任务号
     *
     * @param batchTaskNo 波次任务号
     */
    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    /**
     * 获取 商品种类数量
     *
     * @return skuCount 商品种类数量
     */
    public Integer getSkuCount() {
        return this.skuCount;
    }

    /**
     * 设置 商品种类数量
     *
     * @param skuCount 商品种类数量
     */
    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    /**
     * 获取 大件总数
     *
     * @return packageAmount 大件总数
     */
    public BigDecimal getPackageAmount() {
        return this.packageAmount;
    }

    /**
     * 设置 大件总数
     *
     * @param packageAmount 大件总数
     */
    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    /**
     * 获取 小件总数
     *
     * @return unitAmount 小件总数
     */
    public BigDecimal getUnitAmount() {
        return this.unitAmount;
    }

    /**
     * 设置 小件总数
     *
     * @param unitAmount 小件总数
     */
    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    /**
     * 获取 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     *
     * @return taskState 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     */
    public Byte getTaskState() {
        return this.taskState;
    }

    /**
     * 设置 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     *
     * @param taskState 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     */
    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    /**
     * 获取 分拣员id
     *
     * @return sorterId 分拣员id
     */
    public Integer getSorterId() {
        return this.sorterId;
    }

    /**
     * 设置 分拣员id
     *
     * @param sorterId 分拣员id
     */
    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    /**
     * 获取 波次编号
     *
     * @return batchNo 波次编号
     */
    public String getBatchNo() {
        return this.batchNo;
    }

    /**
     * 设置 波次编号
     *
     * @param batchNo 波次编号
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * 获取 拣货方式 1 按订单拣货 2 按产品拣货',
     */
    public Byte getPickingType() {
        return this.pickingType;
    }

    /**
     * 设置 拣货方式 1 按订单拣货 2 按产品拣货',
     */
    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    /**
     * 获取 拣货分组策略 1货区 2货位 3类目',
     */
    public Byte getPickingGroupStrategy() {
        return this.pickingGroupStrategy;
    }

    /**
     * 设置 拣货分组策略 1货区 2货位 3类目',
     */
    public void setPickingGroupStrategy(Byte pickingGroupStrategy) {
        this.pickingGroupStrategy = pickingGroupStrategy;
    }

    /**
     * 获取 货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 类目名称
     */
    public String getCategoryName() {
        return this.categoryName;
    }

    /**
     * 设置 类目名称
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public Byte getOrderSelection() {
        return orderSelection;
    }

    public void setOrderSelection(Byte orderSelection) {
        this.orderSelection = orderSelection;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getBatchTaskName() {
        return batchTaskName;
    }

    public void setBatchTaskName(String batchTaskName) {
        this.batchTaskName = batchTaskName;
    }

    /**
     * @return the sowTaskId
     */
    public Long getSowTaskId() {
        return sowTaskId;
    }

    /**
     * @param sowTaskId the sowTaskId to set
     */
    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取 波次名称
     *
     * @return batchName 波次名称
     */
    public String getBatchName() {
        return this.batchName;
    }

    /**
     * 设置 波次名称
     *
     * @param batchName 波次名称
     */
    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public Byte getKindOfPicking() {
        return kindOfPicking;
    }

    public void setKindOfPicking(Byte kindOfPicking) {
        this.kindOfPicking = kindOfPicking;
    }

    /**
     * 获取 @see BatchTaskPickPatternEnum
     *
     * @return pickPattern @see BatchTaskPickPatternEnum
     */
    public Byte getPickPattern() {
        return this.pickPattern;
    }

    /**
     * 设置 @see BatchTaskPickPatternEnum
     *
     * @param pickPattern @see BatchTaskPickPatternEnum
     */
    public void setPickPattern(Byte pickPattern) {
        this.pickPattern = pickPattern;
    }
}
