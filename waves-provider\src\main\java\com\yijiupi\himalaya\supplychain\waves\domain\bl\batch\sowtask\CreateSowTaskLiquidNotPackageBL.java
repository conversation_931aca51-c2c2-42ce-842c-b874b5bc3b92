package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.sowtask;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskResultBO;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskByOutStockOrderResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;
import com.yijiupi.himalaya.supplychain.waves.util.RobotPickConstants;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 开了整件拆零，开了液晶不打包，没开机器人
 * 
 * <AUTHOR>
 * @date 2024/8/18
 */
@Service
public class CreateSowTaskLiquidNotPackageBL extends CreateSowTaskBaseBL {

    @Override
    public boolean doSupport(CreateSowTaskBO bo) {
        if (BooleanUtils
            .isTrue(RobotPickConstants.isPassageRobotPickOpen(bo.getWavesStrategyDTO(), bo.getPassageDTO()))) {
            return Boolean.FALSE;
        }

        return BooleanUtils
            .isTrue(RobotPickConstants.isWarehouseOpenLargePick(bo.getWavesStrategyDTO(), bo.getWarehouseConfigDTO()))
            && BooleanUtils
                .isTrue(RobotPickConstants.openLiquidNotPackage(bo.getWarehouseConfigDTO(), bo.getWavesStrategyDTO()));
    }

    @Override
    public CreateSowTaskResultBO doCreateSowTask(CreateSowTaskBO bo) {
        List<OutStockOrderPO> splitOrderList = bo.getSplitOrderList();

        List<OutStockOrderItemPO> packageStoreItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick()))
            .filter(SplitWaveOrderUtil::isStoreItem).collect(Collectors.toList());

        List<OutStockOrderItemPO> packageNotStoreItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick()))
            .filter(m -> BooleanUtils.isFalse(SplitWaveOrderUtil.isStoreItem(m))).collect(Collectors.toList());

        List<OutStockOrderItemPO> packageItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(packageStoreItemList)) {
            packageItemList.addAll(packageStoreItemList);
        }
        if (CollectionUtils.isNotEmpty(packageNotStoreItemList)) {
            packageItemList.addAll(packageNotStoreItemList);
        }

        List<OutStockOrderItemPO> multiStorePackageItemList =
            splitOrderList.stream().flatMap(m -> m.getItems().stream())
                .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY.equals(m.getLargePick()))
                .filter(SplitWaveOrderUtil::isStoreItem).collect(Collectors.toList());

        List<OutStockOrderItemPO> multiNotStorePackageItemList =
            splitOrderList.stream().flatMap(m -> m.getItems().stream())
                .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY.equals(m.getLargePick()))
                .filter(m -> !SplitWaveOrderUtil.isStoreItem(m)).collect(Collectors.toList());

        List<OutStockOrderItemPO> unitItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> !BatchTaskItemLargePickPatternConstants.isLargePick(m.getLargePick()))
            .collect(Collectors.toList());

        List<OutStockOrderItemPO> totalUnitItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(multiStorePackageItemList)) {
            totalUnitItemList.addAll(multiStorePackageItemList);
        }

        if (CollectionUtils.isNotEmpty(multiNotStorePackageItemList)) {
            totalUnitItemList.addAll(multiNotStorePackageItemList);
        }

        if (CollectionUtils.isNotEmpty(unitItemList)) {
            totalUnitItemList.addAll(unitItemList);
        }
        // 开了液晶不打包的，整件 单独生成按订单拣货的拣货任务,也要生成播种任务
        // List<OutStockOrderPO> sowOutStockOrderList = openLiquidNotPackageCreateInvolveSowOrderList(
        // bo.getWarehouseConfigDTO(), bo.getWavesStrategyDTO(), splitOrderList, packageItemList);

        // 通道开启了机器人，则生成机器人拣货任务，根据拣货货位判断； 对 otherItemList 还要拆
        CreateSowTaskByOutStockOrderResultBO orderResultBO =
            createSowTaskByOutStockOrderBL.createSowTaskByOutStockOrder(bo);
        WaveCreateDTO waveCreateDTO = orderResultBO.getWaveCreateDTO();

        List<WaveCreateDTO> waveCreateList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(packageItemList)) {
            WaveCreateDTO createDTO =
                SplitWaveOrderUtil.copyWaveCreateDTO(waveCreateDTO, splitOrderList, packageItemList);
            createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE);
            // createDTO.setPassageDTO(null);
            waveCreateList.add(createDTO);
        }

        if (CollectionUtils.isNotEmpty(totalUnitItemList)) {
            List<WaveCreateDTO> otherCreateDTOList =
                waveSplitRobotBL.splitOtherWave(waveCreateDTO, splitOrderList, totalUnitItemList);
            waveCreateList.addAll(otherCreateDTOList);
        }

        CreateSowTaskResultBO createSowTaskResultBO = new CreateSowTaskResultBO();
        createSowTaskResultBO.setSowTaskPOList(orderResultBO.getSowTaskPOList());
        createSowTaskResultBO.setSowOrderPOList(orderResultBO.getSowOrdersList());
        createSowTaskResultBO.setWaveCreateDTOList(waveCreateList);
        return createSowTaskResultBO;
    }

}
