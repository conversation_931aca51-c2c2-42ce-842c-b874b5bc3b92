package com.yijiupi.himalaya.supplychain.waves.dto.secondsort;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/25
 */
public class SecondPickSowQueryDTO implements Serializable {
    private static final long serialVersionUID = -3640136675046898148L;
    /**
     * 城市id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 拣货任务详情id
     */
    private String batchTaskItemId;
    /**
     * 拣货任务详情id
     */
    private List<String> batchTaskItemIds;
    /**
     * 拣货任务ID
     */
    private String batchTaskId;
    /**
     * 是否忽略配送人
     */
    private Boolean isIgnoreDeliveryUser = true;

    public Boolean getIgnoreDeliveryUser() {
        return isIgnoreDeliveryUser;
    }

    public void setIgnoreDeliveryUser(Boolean ignoreDeliveryUser) {
        isIgnoreDeliveryUser = ignoreDeliveryUser;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getBatchTaskItemId() {
        return batchTaskItemId;
    }

    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    public List<String> getBatchTaskItemIds() {
        return batchTaskItemIds;
    }

    public void setBatchTaskItemIds(List<String> batchTaskItemIds) {
        this.batchTaskItemIds = batchTaskItemIds;
    }
}
