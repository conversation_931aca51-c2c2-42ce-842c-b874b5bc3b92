package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2018/5/16 20:11
 */
public class OutStockOrderLocationDTO implements Serializable {

    /**
     * 订单id
     */
    private Long outStockOrderId;

    /**
     * oms订单id
     */
    private Long omsOrderId;

    /**
     * 订单号
     */
    private String orderNo;

    private Long locationId;

    private String locationName;

    /**
     * 二级仓货位id
     */
    private Long toLocationId;

    /**
     * 二级仓货位名称
     */
    private String toLocationName;

    /**
     * 二级仓仓库名称
     */
    private String toWarehouseName;

    /**
     * 二级仓城市名称
     */
    private String toCity;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 目标仓库id
     */
    private Integer toWarehouseId;

    public Long getOmsOrderId() {
        return omsOrderId;
    }

    public void setOmsOrderId(Long omsOrderId) {
        this.omsOrderId = omsOrderId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public String getToWarehouseName() {
        return toWarehouseName;
    }

    public void setToWarehouseName(String toWarehouseName) {
        this.toWarehouseName = toWarehouseName;
    }

    public String getToCity() {
        return toCity;
    }

    public void setToCity(String toCity) {
        this.toCity = toCity;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getToWarehouseId() {
        return toWarehouseId;
    }

    public void setToWarehouseId(Integer toWarehouseId) {
        this.toWarehouseId = toWarehouseId;
    }

    public Long getOutStockOrderId() {
        return outStockOrderId;
    }

    public void setOutStockOrderId(Long outStockOrderId) {
        this.outStockOrderId = outStockOrderId;
    }
}
