package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.supplychain.enums.PassagePickTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PassageRelateTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IPassageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderProcessBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderProcessTransferBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.SowCalculationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByRefOrderNoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.DeliveryTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.PickingTypeEnum;

/**
 * Created by 余明 on 2018-07-30.
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class BatchOrderProcessServiceImplTest {

    @Autowired
    private BatchOrderProcessServiceImpl batchOrderProcessServiceImp;
    @Autowired
    private BatchOrderProcessBL batchOrderProcessBL;
    @Autowired
    private BatchOrderProcessTransferBL batchOrderProcessTransferBL;
    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    // {"orderIdList":["998000221202109841"],"cityId":998,"warehouseId":9981,"pickingType":1,"pickingGroupStrategy":3,
    // "operateUser":"临时外包-张裔譞阿里","passPickType":1,"groupType":1}

    // 两个订单：规格6， 一个 89 一个 92，89的写了两个货主 "998000230113110868", "998000230113150870"
    // 一个订单 ，规格6，一个 89 ， 两个货主 ：998000230113110868
    // 一个订单 边界值的 数据
    // 两个订单 边界值的 数据
    // 两个订单，8和7 "998000230113150875", "998000230113150876"
    // 一个订单 80 一个货主 998000230113150873
    // 一个订单 80 两个货主，一个30， 一个50 998000230113150874
    // 两个订单 一个80 两个货主，一个30， 一个50 998000230113150874；一个80 一个货主 998000230113150873

    // 规格7 ， 买个两个订单， 一个订单的订单项买了 10 ，分两个货主 一个7 ， 一个3
    // 一个订单的订单项买了 8， 分两个货主 一个6， 一个 2
    //
    @Test
    public void createBatchTest() {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setCityId(998);
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setPickingType((byte)1);
        batchCreateDTO.setPickingGroupStrategy((byte)3);
        batchCreateDTO.setPassPickType((byte)1);
        batchCreateDTO.setGroupType(1);
        batchCreateDTO.setOperateUser("临时外包-张裔譞阿里");
        // batchCreateDTO.setOrderIdList(Arrays.asList("998000230224161454", "998000230224161455"));
        batchCreateDTO.setOrderIdList(Arrays.asList("998000230303171689"));

        batchOrderProcessTransferBL.createBatch(batchCreateDTO);

        Assertions.assertThat(batchCreateDTO).isNotNull();
    }

    @Test
    public void createBatchTest5() {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setCityId(998);
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setPickingType((byte)1);
        batchCreateDTO.setPickingGroupStrategy((byte)3);
        batchCreateDTO.setPassPickType((byte)1);
        batchCreateDTO.setGroupType(1);
        batchCreateDTO.setOperateUser("临时外包-张裔譞阿里");
        batchCreateDTO.setOrderIdList(Arrays.asList("998000230131101003"));

        batchOrderProcessTransferBL.createBatch(batchCreateDTO);

        Assertions.assertThat(batchCreateDTO).isNotNull();
    }

    // 一个订单 80 一个货主 998000230113150873
    @Test
    public void createBatchTest4() {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setCityId(998);
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setPickingType((byte)1);
        batchCreateDTO.setPickingGroupStrategy((byte)3);
        batchCreateDTO.setPassPickType((byte)1);
        batchCreateDTO.setGroupType(1);
        batchCreateDTO.setOperateUser("临时外包-张裔譞阿里");
        batchCreateDTO.setOrderIdList(Arrays.asList("998000230113150873"));

        batchOrderProcessTransferBL.createBatch(batchCreateDTO);

        Assertions.assertThat(batchCreateDTO).isNotNull();
    }

    // 一个订单 ，规格6，一个 89 ， 两个货主 ：998000230113110868
    @Test
    public void createBatchTest3() {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setCityId(998);
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setPickingType((byte)1);
        batchCreateDTO.setPickingGroupStrategy((byte)3);
        batchCreateDTO.setPassPickType((byte)1);
        batchCreateDTO.setGroupType(1);
        batchCreateDTO.setOperateUser("临时外包-张裔譞阿里");
        batchCreateDTO.setOrderIdList(Arrays.asList("998000230113110868"));

        batchOrderProcessTransferBL.createBatch(batchCreateDTO);

        Assertions.assertThat(batchCreateDTO).isNotNull();
    }

    // 两个订单：规格6， 一个 89 一个 92，89的写了两个货主
    @Test
    public void createBatchTest2() {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setCityId(998);
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setPickingType((byte)1);
        batchCreateDTO.setPickingGroupStrategy((byte)3);
        batchCreateDTO.setPassPickType((byte)1);
        batchCreateDTO.setGroupType(1);
        batchCreateDTO.setOperateUser("临时外包-张裔譞阿里");
        batchCreateDTO.setOrderIdList(Arrays.asList("998000230113110868", "998000230113150870"));

        batchOrderProcessTransferBL.createBatch(batchCreateDTO);

        Assertions.assertThat(batchCreateDTO).isNotNull();
    }

    @Test
    public void createBatchByRefOrderNo() {
        String json =
            "{\"allocationFlag\":false,\"deliveryCarId\":1,\"deliveryTaskList\":[{\"deliveryCarId\":1,\"deliveryCarName\":\"测试自带车\",\"deliveryTaskNo\":\"DD16420230914002217\",\"deliveryUserId\":68187058,\"deliveryUserName\":\"张朵朵\",\"taskId\":\"5234168836253515863\"}],\"driverName\":\"张朵朵\",\"groupType\":1,\"operateUser\":\"测试李\",\"operateUserId\":67795309,\"orderPickFlag\":3,\"passPickType\":1,\"pickingGroupStrategy\":3,\"pickingType\":2,\"refOrderNos\":[\"164325700039\"],\"title\":\"测试自带车-张朵朵\",\"warehouseId\":1641}";
        BatchCreateByRefOrderNoDTO dto = JSON.parseObject(json, BatchCreateByRefOrderNoDTO.class);
        batchOrderProcessServiceImp.createBatchByRefOrderNo(dto);
    }

    @Test
    public void createSowTask() {

        BatchCreateByRefOrderNoDTO dto = new BatchCreateByRefOrderNoDTO();
        dto.setPickingType(PickingTypeEnum.订单拣货.getType());
        dto.setPassPickType(PassagePickTypeEnum.按类目通道.getType());
        dto.setTitle("测试总单拣货生成播种任务");
        dto.setWarehouseId(9981);
        dto.setRefOrderNos(Arrays.asList("998228600065", "998228700036"));
        dto.setOperateUser("临时外包-张裔譞阿里");

        DeliveryTaskDTO deliveryTaskDTO1 = new DeliveryTaskDTO();
        deliveryTaskDTO1.setTaskId("998005221019113687");
        deliveryTaskDTO1.setDeliveryTaskNo("DD202210190004");
        deliveryTaskDTO1.setDeliveryCarId(1L);
        deliveryTaskDTO1.setDeliveryCarName("自带车");
        deliveryTaskDTO1.setDeliveryUserId(176);
        deliveryTaskDTO1.setDeliveryUserName("成师傅");
        deliveryTaskDTO1.setLogistics(Boolean.FALSE);
        deliveryTaskDTO1.setOrderNoList(Collections.singletonList("998228600065"));

        DeliveryTaskDTO deliveryTaskDTO2 = new DeliveryTaskDTO();
        deliveryTaskDTO2.setTaskId("998005221019113683");
        deliveryTaskDTO2.setDeliveryTaskNo("DD202210190003");
        deliveryTaskDTO2.setDeliveryCarId(1L);
        deliveryTaskDTO2.setDeliveryCarName("自带车");
        deliveryTaskDTO2.setDeliveryUserId(176);
        deliveryTaskDTO2.setDeliveryUserName("成师傅");
        deliveryTaskDTO2.setLogistics(Boolean.FALSE);
        deliveryTaskDTO2.setOrderNoList(Collections.singletonList("998228700036"));

        dto.setDeliveryTaskList(Arrays.asList(deliveryTaskDTO1, deliveryTaskDTO2));
        batchOrderProcessServiceImp.createBatchByRefOrderNo(dto);
    }

    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;

    @Test
    public void mapperTest() {
        // List<String> batchTaskIds = batchTaskMapper.findIdByBatchIds(Collections.singletonList("2022111700214"));

        // List<String> batchIds =
        // outStockOrderMapper.findByBatchIdByIds(Collections.singletonList("5125084371358425618"), 9982);
        //
        // Assertions.assertThat(batchIds).isNotNull();
    }

    @Autowired
    private SowCalculationBL sowCalculationBL;
    @Reference
    private IPassageService iPassageService;

    @Test
    public void collectLocationTest() {
        BatchPO batchPO = new BatchPO();
        batchPO.setOrgId(998);
        batchPO.setWarehouseId(9981);

        List<PassageDTO> lstPassStrategyDTO = new ArrayList<>();
        PassageDTO passageDTO = iPassageService.getPassage(5084419197907009374L);
        // passageDTO.setSowType(SowTypeEnum.播种墙播种.getType());
        lstPassStrategyDTO.add(passageDTO);

        List<LocationReturnDTO> locationList = sowCalculationBL.findSowLocation(null, lstPassStrategyDTO, null);

        List<LocationReturnDTO> result = new ArrayList<>();

        PassageItemSO passageItemSO = new PassageItemSO();
        passageItemSO.setPassageId(5084419197907009374L);
        passageItemSO.setPassageType(PassageRelateTypeEnum.集货位.getType());
        passageItemSO.setWarehouseId(9981);
        List<PassageItemDTO> collectLocationList = iPassageService.findPassageItem(passageItemSO);
        if (CollectionUtils.isEmpty(collectLocationList)) {
            result.addAll(locationList);
        }

        Map<String, String> collectLocationMap = collectLocationList.stream()
            .collect(Collectors.toMap(PassageItemDTO::getRelateId, PassageItemDTO::getRelateId));
        List<LocationReturnDTO> filterList = locationList.stream()
            .filter(m -> Objects.nonNull(collectLocationMap.get(m.getId().toString()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            result.addAll(locationList);
        } else {
            result.addAll(filterList);
        }

        Assertions.assertThat(result).isNotNull();
    }

    private List<LocationReturnDTO> getPassageCollectLocationList(List<LocationReturnDTO> locationList,
        PassageDTO passageDTO) {
        PassageItemSO passageItemSO = new PassageItemSO();
        passageItemSO.setPassageId(passageItemSO.getPassageId());
        passageItemSO.setPassageType(PassageRelateTypeEnum.集货位.getType());
        passageItemSO.setWarehouseId(passageItemSO.getWarehouseId());
        List<PassageItemDTO> collectLocationList = iPassageService.findPassageItem(passageItemSO);
        if (CollectionUtils.isEmpty(collectLocationList)) {
            return locationList;
        }

        Map<String, String> collectLocationMap = collectLocationList.stream()
            .collect(Collectors.toMap(PassageItemDTO::getRelateId, PassageItemDTO::getRelateId));
        List<LocationReturnDTO> filterList = locationList.stream()
            .filter(m -> Objects.nonNull(collectLocationMap.get(m.getId().toString()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            return locationList;
        }

        return filterList;
    }

    // ===================================

    /**
     * 规格6，一个买10，一个买8 ；小件整好聚合成大件
     */
    @Test
    public void createBatchTest10() {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setCityId(998);
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setPickingType((byte)2);
        batchCreateDTO.setPickingGroupStrategy((byte)3);
        batchCreateDTO.setPassPickType((byte)1);
        batchCreateDTO.setGroupType(1);
        batchCreateDTO.setOperateUser("临时外包-张裔譞阿里");
        batchCreateDTO.setOrderIdList(Arrays.asList("998000230209101217", "998000230209101218"));

        batchOrderProcessTransferBL.createBatch(batchCreateDTO);

        Assertions.assertThat(batchCreateDTO).isNotNull();
    }

    /**
     * 规格6，一个买14，一个买15 ；
     */
    @Test
    public void createBatchTest11() {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setCityId(998);
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setPickingType((byte)2);
        batchCreateDTO.setPickingGroupStrategy((byte)3);
        batchCreateDTO.setPassPickType((byte)1);
        batchCreateDTO.setGroupType(1);
        batchCreateDTO.setOperateUser("临时外包-张裔譞阿里");
        batchCreateDTO.setOrderIdList(Arrays.asList("998000230209101220", "998000230209101221"));

        batchOrderProcessTransferBL.createBatch(batchCreateDTO);

        Assertions.assertThat(batchCreateDTO).isNotNull();
    }

    /**
     * 规格6，一个买16，一个买15 ；
     */
    @Test
    public void createBatchTest12() {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setCityId(998);
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setPickingType((byte)2);
        batchCreateDTO.setPickingGroupStrategy((byte)3);
        batchCreateDTO.setPassPickType((byte)1);
        batchCreateDTO.setGroupType(1);
        batchCreateDTO.setOperateUser("临时外包-张裔譞阿里");
        batchCreateDTO.setOrderIdList(Arrays.asList("998000230209101221", "998000230209101222"));

        batchOrderProcessTransferBL.createBatch(batchCreateDTO);

        Assertions.assertThat(batchCreateDTO).isNotNull();
    }

}