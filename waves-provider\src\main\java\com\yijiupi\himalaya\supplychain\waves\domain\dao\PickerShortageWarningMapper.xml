<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.PickerShortageWarningMapper">

    <select id="countOrderByFeature" resultType="int">
        select count(distinct o.id)
        from outstockorder o inner join orderfeature oe on o.id = oe.Order_Id
        <where>
            oe.FeatureType = #{param1}
            and o.Org_Id = #{param3.orgId,jdbcType=INTEGER}
            and o.Warehouse_Id = #{param3.warehouseId,jdbcType=INTEGER}
            and o.State in
            <foreach collection="param2" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=TINYINT}
            </foreach>
        </where>
    </select>

    <select id="countAvgOrderByFeatureAndTime" resultType="int">
        select ceil(count(distinct a.id) / 30 / 24) as averageCount
        from outstockorder a
        inner join orderfeature b on a.id = b.Order_Id
        <where>
            a.Warehouse_Id = #{param3.warehouseId,jdbcType=INTEGER}
            and a.Org_Id = #{param3.orgId,jdbcType=INTEGER}
            and b.FeatureType = #{param1}
            and a.createtime >= current_date() - interval #{param2} day
        </where>
    </select>

    <select id="countOrderProductByPackage" resultType="java.math.BigDecimal">
        select sum(packageCount) as totalCount
        from batchtaskitem a
        inner join (select distinct a.id
        from batchtaskitem a
        inner join batchtask c on a.BatchtaskNo = c.BatchTaskNo
        inner join outstockorder d on c.BatchNo = d.BatchNo and d.outBoundType = 2 and d.AllotType != 8
        inner join orderfeature b on b.Order_Id = d.id
        where a.TaskState = 0
        and b.FeatureType = 4
        and d.Org_Id = #{orgId,jdbcType=INTEGER}
        and d.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}) b on a.id = b.id
        where org_id = #{orgId,jdbcType=INTEGER}
    </select>

    <select id="countOrderProductByLine" resultType="java.math.BigDecimal">
        select count(distinct e.RefOrderItem_Id) as totalCount
        from batchtaskitem a
        inner join batchtask c on a.BatchtaskNo = c.BatchTaskNo
        inner join outstockorder d on c.BatchNo = d.BatchNo and d.outBoundType = 2 and d.AllotType != 8
        inner join orderfeature b on b.Order_Id = d.id
        inner join orderitemtaskinfo e on a.id = e.BatchTaskItem_Id
        where a.TaskState = 0
        and b.FeatureType = 5
        and d.Org_Id = #{orgId,jdbcType=INTEGER}
        and d.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </select>

    <select id="countPrevAvgProductCountByPackage" resultType="java.math.BigDecimal">
        select round(sum(PackageAmount) / count(id), 6) as averageCount
        from outstockorder
        where org_id = #{orgId,jdbcType=INTEGER} and id in(select a.id
        from outstockorder a
        inner join outstockorderitem b on a.id = b.Outstockorder_Id
        inner join orderfeature c on a.id = c.Order_Id
        where a.createtime >= current_date() - interval 30 day
        and c.FeatureType = 4
        and a.Org_Id = #{orgId,jdbcType=INTEGER}
        and a.Warehouse_Id = #{warehouseId,jdbcType=INTEGER})
    </select>

    <select id="countPrevAvgProductCountByLine" resultType="java.math.BigDecimal">
        select round(count(distinct b.id) / count(distinct a.id), 6) as averageCount
        from outstockorder a
                 inner join outstockorderitem b on a.id = b.Outstockorder_Id
                 inner join orderfeature c on a.id = c.Order_Id
        where a.createtime >= current_date() - interval 30 day
          and c.FeatureType = 5
        and a.Org_Id =#{orgId,jdbcType=INTEGER}
        and a.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </select>

    <select id="countRestEfficiency"
            resultType="com.yijiupi.himalaya.supplychain.waves.schedule.model.PickerEfficiencyBO">
        select count(distinct c.reforderitem_id) as averageCount, a.completeUserId as userId
        from batchtaskitem a
        inner join orderitemtaskinfo c on a.id = c.batchtaskitem_id
        inner join outstockorder e on e.BatchNo = c.BatchNo
        inner join orderfeature b on b.Order_Id = e.id
        where a.org_id = #{orgId,jdbcType=INTEGER}
        and a.taskState = 2
        and e.warehouse_id = #{warehouseId,jdbcType=INTEGER}
        and b.FeatureType = 5
        and a.CompleteTime >= current_date() - interval 30 day
        group by a.completeUserId
    </select>

    <select id="countDrinkingEfficiency"
            resultType="com.yijiupi.himalaya.supplychain.waves.schedule.model.PickerEfficiencyBO">
        select sum(packagecount) as averageCount, completeuserid as userId
        from batchtaskitem
        where org_id = #{orgId,jdbcType=INTEGER} and id in (select a.id as id
        from batchtaskitem a
        inner join orderitemtaskinfo c on a.id = c.batchtaskitem_id
        inner join outstockorder e on e.BatchNo = c.BatchNo
        inner join orderfeature b on b.Order_Id = e.id
        where a.org_id = #{orgId,jdbcType=INTEGER}
        and a.taskState = 2
        and e.warehouse_id = #{warehouseId,jdbcType=INTEGER}
        and b.FeatureType = 4
        and a.CompleteTime >= current_date() - interval 30 day)
        group by completeUserId
    </select>

</mapper>