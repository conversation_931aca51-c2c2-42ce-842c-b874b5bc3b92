package com.yijiupi.himalaya.supplychain.waves.dto.ordercenter;

import java.io.Serializable;
import java.util.Map;

public class PartSendWsmDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long businessId;
    private Map<Long, Integer> itemShipMap;
    private Map<Long, Integer> itemMap;
    private String orderNo;
    private Integer userId;
    private Map<Object, Object> extendMap;

    public PartSendWsmDTO() {}

    public Map<Object, Object> getExtendMap() {
        return this.extendMap;
    }

    public void setExtendMap(Map<Object, Object> extendMap) {
        this.extendMap = extendMap;
    }

    public Integer getUserId() {
        return this.userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Long getBusinessId() {
        return this.businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Map<Long, Integer> getItemShipMap() {
        return this.itemShipMap;
    }

    public void setItemShipMap(Map<Long, Integer> itemShipMap) {
        this.itemShipMap = itemShipMap;
    }

    public Map<Long, Integer> getItemMap() {
        return this.itemMap;
    }

    public void setItemMap(Map<Long, Integer> itemMap) {
        this.itemMap = itemMap;
    }

    public String getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
