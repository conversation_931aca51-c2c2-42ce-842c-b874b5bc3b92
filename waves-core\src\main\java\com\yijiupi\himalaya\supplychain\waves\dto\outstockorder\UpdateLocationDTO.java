package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
public class UpdateLocationDTO implements Serializable {

    private List<String> refOrderNos;

    private List<String> boundNos;

    private Integer warehouseId;

    /**
     * 获取
     *
     * @return refOrderNos
     */
    public List<String> getRefOrderNos() {
        return this.refOrderNos;
    }

    /**
     * 设置
     *
     * @param refOrderNos
     */
    public void setRefOrderNos(List<String> refOrderNos) {
        this.refOrderNos = refOrderNos;
    }

    /**
     * 获取
     *
     * @return boundNos
     */
    public List<String> getBoundNos() {
        return this.boundNos;
    }

    /**
     * 设置
     *
     * @param boundNos
     */
    public void setBoundNos(List<String> boundNos) {
        this.boundNos = boundNos;
    }

    /**
     * 获取
     *
     * @return warehouseId
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置
     *
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
