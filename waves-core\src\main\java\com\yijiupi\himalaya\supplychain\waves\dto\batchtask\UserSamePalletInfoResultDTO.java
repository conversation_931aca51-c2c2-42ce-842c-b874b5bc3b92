package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/20
 */
public class UserSamePalletInfoResultDTO implements Serializable {
    /**
     * 出库位id
     */
    private Long locationId;
    /**
     * 出库位名称
     */
    private String locationName;
    /**
     * 托盘列表
     */
    private List<String> palletNoList;

    public UserSamePalletInfoResultDTO() {}

    public UserSamePalletInfoResultDTO(Long locationId, String locationName) {
        this.locationId = locationId;
        this.locationName = locationName;
    }

    /**
     * 获取 出库位id
     *
     * @return locationId 出库位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 出库位id
     *
     * @param locationId 出库位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 出库位名称
     *
     * @return locationName 出库位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 出库位名称
     *
     * @param locationName 出库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 托盘列表
     *
     * @return palletNoList 托盘列表
     */
    public List<String> getPalletNoList() {
        return this.palletNoList;
    }

    /**
     * 设置 托盘列表
     *
     * @param palletNoList 托盘列表
     */
    public void setPalletNoList(List<String> palletNoList) {
        this.palletNoList = palletNoList;
    }

    public static UserSamePalletInfoResultDTO getDefault() {
        return new UserSamePalletInfoResultDTO();
    }

    public static UserSamePalletInfoResultDTO getDefaultLocation(Long locationId, String locationName) {
        return new UserSamePalletInfoResultDTO(locationId, locationName);
    }
}
