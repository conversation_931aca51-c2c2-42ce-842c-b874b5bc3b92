package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/25
 */
public class MergePickOrderValidBO {

    private Long orderId;

    private String batchTaskId;

    private Integer orderSequence;

    private String orderNo;

    public MergePickOrderValidBO() {}

    public MergePickOrderValidBO(Long orderId, String batchTaskId, Integer orderSequence, String orderNo) {
        this.orderId = orderId;
        this.batchTaskId = batchTaskId;
        this.orderSequence = orderSequence;
        this.orderNo = orderNo;
    }

    /**
     * 获取
     *
     * @return orderId
     */
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * 设置
     *
     * @param orderId
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取
     *
     * @return batchTaskId
     */
    public String getBatchTaskId() {
        return this.batchTaskId;
    }

    /**
     * 设置
     *
     * @param batchTaskId
     */
    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    /**
     * 获取
     *
     * @return orderSequence
     */
    public Integer getOrderSequence() {
        return this.orderSequence;
    }

    /**
     * 设置
     *
     * @param orderSequence
     */
    public void setOrderSequence(Integer orderSequence) {
        this.orderSequence = orderSequence;
    }

    public static List<MergePickOrderValidBO> convert(List<OutStockOrderPO> orderList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        Map<Long, String> orderBatchTaskMap = orderItemTaskInfoPOList.stream().collect(
            Collectors.toMap(OrderItemTaskInfoPO::getRefOrderId, OrderItemTaskInfoPO::getBatchTaskId, (v1, v2) -> v1));

        return orderList.stream().filter(m -> Objects.nonNull(orderBatchTaskMap.get(m.getId()))).map(order -> {
            String batchTaskId = orderBatchTaskMap.get(order.getId());
            return new MergePickOrderValidBO(order.getId(), batchTaskId, order.getOrderSequence(),
                order.getReforderno());
        }).collect(Collectors.toList());

    }

    public static Map<String, List<MergePickOrderValidBO>> convert(List<MergePickOrderValidBO> boList) {

        return boList.stream().collect(Collectors.groupingBy(k -> k.getKey()));
    }

    public String getKey() {
        return this.getBatchTaskId() + "-" + this.getOrderSequence();
    }

    /**
     * 获取
     *
     * @return orderNo
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置
     *
     * @param orderNo
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
