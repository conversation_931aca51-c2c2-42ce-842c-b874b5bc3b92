package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location;

import java.util.ArrayList;
import java.util.List;

import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchWorkSettingDTO;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @title: CreateBatchLocationBO
 * @description:
 * @date 2022-09-26 14:36
 */
public class CreateBatchLocationBO {
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 仓库是2.5还是3.0
     */
    private Boolean isOpenStock;
    /**
     * 是否开启了货位组
     */
    private Boolean isOpenLocationGroup;
    /**
     * 网格仓的作业设置（如果为null, 表示非网格仓）
     */
    private BatchWorkSettingDTO workSetting;
    /**
     * 仓库配置
     */
    private WarehouseConfigDTO warehouseConfigDTO;
    /**
     * 拣货策略配置
     */
    private WavesStrategyBO wavesStrategyDTO;
    /**
     * 是否开启前置仓内配单按产品拣货
     */
    private boolean openFrontWarehouseOpenNPProductPick = Boolean.FALSE;

    private List<OutStockOrderPO> normalOrderList;
    /**
     * 内配按产品拣货的出库单列表
     */
    private List<OutStockOrderPO> npProductOrderList;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 仓库是2.5还是3.0
     *
     * @return isOpenStock 仓库是2.5还是3.0
     */
    public Boolean getIsOpenStock() {
        return this.isOpenStock;
    }

    /**
     * 设置 仓库是2.5还是3.0
     *
     * @param isOpenStock 仓库是2.5还是3.0
     */
    public void setIsOpenStock(Boolean isOpenStock) {
        this.isOpenStock = isOpenStock;
    }

    /**
     * 获取 是否开启了货位组
     *
     * @return isOpenLocationGroup 是否开启了货位组
     */
    public Boolean getIsOpenLocationGroup() {
        return this.isOpenLocationGroup;
    }

    /**
     * 设置 是否开启了货位组
     *
     * @param isOpenLocationGroup 是否开启了货位组
     */
    public void setIsOpenLocationGroup(Boolean isOpenLocationGroup) {
        this.isOpenLocationGroup = isOpenLocationGroup;
    }

    /**
     * 获取 网格仓的作业设置（如果为null 表示非网格仓）
     *
     * @return workSetting 网格仓的作业设置（如果为null 表示非网格仓）
     */
    public BatchWorkSettingDTO getWorkSetting() {
        return this.workSetting;
    }

    /**
     * 设置 网格仓的作业设置（如果为null 表示非网格仓）
     *
     * @param workSetting 网格仓的作业设置（如果为null 表示非网格仓）
     */
    public void setWorkSetting(BatchWorkSettingDTO workSetting) {
        this.workSetting = workSetting;
    }

    /**
     * 获取 仓库配置
     *
     * @return warehouseConfigDTO 仓库配置
     */
    public WarehouseConfigDTO getWarehouseConfigDTO() {
        return this.warehouseConfigDTO;
    }

    /**
     * 设置 仓库配置
     *
     * @param warehouseConfigDTO 仓库配置
     */
    public void setWarehouseConfigDTO(WarehouseConfigDTO warehouseConfigDTO) {
        this.warehouseConfigDTO = warehouseConfigDTO;
    }

    /**
     * 获取 拣货策略配置
     *
     * @return wavesStrategyDTO 拣货策略配置
     */
    public WavesStrategyBO getWavesStrategyDTO() {
        return this.wavesStrategyDTO;
    }

    /**
     * 设置 拣货策略配置
     *
     * @param wavesStrategyDTO 拣货策略配置
     */
    public void setWavesStrategyDTO(WavesStrategyBO wavesStrategyDTO) {
        this.wavesStrategyDTO = wavesStrategyDTO;
    }

    /**
     * 获取 是否开启前置仓内配单按产品拣货
     *
     * @return openFrontWarehouseOpenNPProductPick 是否开启前置仓内配单按产品拣货
     */
    public boolean getOpenFrontWarehouseOpenNPProductPick() {
        return this.openFrontWarehouseOpenNPProductPick;
    }

    /**
     * 设置 是否开启前置仓内配单按产品拣货
     *
     * @param openFrontWarehouseOpenNPProductPick 是否开启前置仓内配单按产品拣货
     */
    public void setOpenFrontWarehouseOpenNPProductPick(boolean openFrontWarehouseOpenNPProductPick) {
        this.openFrontWarehouseOpenNPProductPick = openFrontWarehouseOpenNPProductPick;
    }

    /**
     * 获取
     *
     * @return normalOrderList
     */
    public List<OutStockOrderPO> getNormalOrderList() {
        return this.normalOrderList;
    }

    /**
     * 设置
     *
     * @param normalOrderList
     */
    public void setNormalOrderList(List<OutStockOrderPO> normalOrderList) {
        this.normalOrderList = normalOrderList;
    }

    /**
     * 获取 内配按产品拣货的出库单列表
     *
     * @return npProductOrderList 内配按产品拣货的出库单列表
     */
    public List<OutStockOrderPO> getNpProductOrderList() {
        return this.npProductOrderList;
    }

    /**
     * 设置 内配按产品拣货的出库单列表
     *
     * @param npProductOrderList 内配按产品拣货的出库单列表
     */
    public void setNpProductOrderList(List<OutStockOrderPO> npProductOrderList) {
        this.npProductOrderList = npProductOrderList;
    }

    public List<OutStockOrderPO> getTotalOrderList() {
        List<OutStockOrderPO> totalOrderList = new ArrayList<OutStockOrderPO>();
        if (!CollectionUtils.isEmpty(normalOrderList)) {
            totalOrderList.addAll(normalOrderList);
        }
        if (!CollectionUtils.isEmpty(npProductOrderList)) {
            totalOrderList.addAll(npProductOrderList);
        }

        return totalOrderList;
    }

}
