package com.yijiupi.himalaya.supplychain.waves.event;

import org.springframework.context.ApplicationEvent;

import com.yijiupi.himalaya.supplychain.waves.domain.bo.NotifyOrderLackBO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
public class BatchFinishedEvent extends ApplicationEvent {
    /**
     * 波次号
     */
    private NotifyOrderLackBO notifyOrderLackBO;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public BatchFinishedEvent(Object source) {
        super(source);
    }

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public BatchFinishedEvent(Object source, NotifyOrderLackBO notifyOrderLackBO) {
        super(source);
        this.notifyOrderLackBO = notifyOrderLackBO;
    }

    /**
     * 获取 波次号
     *
     * @return notifyOrderLackBO 波次号
     */
    public NotifyOrderLackBO getNotifyOrderLackBO() {
        return this.notifyOrderLackBO;
    }

    /**
     * 设置 波次号
     *
     * @param notifyOrderLackBO 波次号
     */
    public void setNotifyOrderLackBO(NotifyOrderLackBO notifyOrderLackBO) {
        this.notifyOrderLackBO = notifyOrderLackBO;
    }
}
