package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/12
 */
public class BatchTaskItemFinishNeedOpBO {

    /**
     * 需要更新的拣货任务明细
     */
    private List<BatchTaskItemUpdatePO> poList;

    /**
     * 拣货装箱信息
     */
    private List<PackageOrderItemDTO> packageOrderItemDTOS;
    /**
     * 移库数据
     */
    private List<PickUpDTO> pickUpDTOList;

    private List<OrderItemTaskInfoPO> updateOrderItemTaskInfoList = new ArrayList<>();

    private List<OrderItemTaskInfoDetailPO> updateOrderItemTaskInfoDetailPOList = new ArrayList<>();

    private List<BatchTaskItemContainerPO> addContainerList = new ArrayList<>();

    private List<BatchTaskItemContainerPO> updateContainerList = new ArrayList<>();
    /**
     * 待完成的拣货任务
     */
    private BatchTaskPO completeBatchTaskPO;

    /**
     * 获取 需要更新的拣货任务明细
     *
     * @return poList 需要更新的拣货任务明细
     */
    public List<BatchTaskItemUpdatePO> getPoList() {
        return this.poList;
    }

    /**
     * 设置 需要更新的拣货任务明细
     *
     * @param poList 需要更新的拣货任务明细
     */
    public void setPoList(List<BatchTaskItemUpdatePO> poList) {
        this.poList = poList;
    }

    /**
     * 获取 拣货装箱信息
     *
     * @return packageOrderItemDTOS 拣货装箱信息
     */
    public List<PackageOrderItemDTO> getPackageOrderItemDTOS() {
        return this.packageOrderItemDTOS;
    }

    /**
     * 设置 拣货装箱信息
     *
     * @param packageOrderItemDTOS 拣货装箱信息
     */
    public void setPackageOrderItemDTOS(List<PackageOrderItemDTO> packageOrderItemDTOS) {
        this.packageOrderItemDTOS = packageOrderItemDTOS;
    }

    /**
     * 获取 移库数据
     *
     * @return pickUpDTOList 移库数据
     */
    public List<PickUpDTO> getPickUpDTOList() {
        return this.pickUpDTOList;
    }

    /**
     * 设置 移库数据
     *
     * @param pickUpDTOList 移库数据
     */
    public void setPickUpDTOList(List<PickUpDTO> pickUpDTOList) {
        this.pickUpDTOList = pickUpDTOList;
    }

    /**
     * 获取
     *
     * @return updateOrderItemTaskInfoList
     */
    public List<OrderItemTaskInfoPO> getUpdateOrderItemTaskInfoList() {
        return this.updateOrderItemTaskInfoList;
    }

    /**
     * 设置
     *
     * @param updateOrderItemTaskInfoList
     */
    public void setUpdateOrderItemTaskInfoList(List<OrderItemTaskInfoPO> updateOrderItemTaskInfoList) {
        this.updateOrderItemTaskInfoList = updateOrderItemTaskInfoList;
    }

    /**
     * 获取
     *
     * @return updateOrderItemTaskInfoDetailPOList
     */
    public List<OrderItemTaskInfoDetailPO> getUpdateOrderItemTaskInfoDetailPOList() {
        return this.updateOrderItemTaskInfoDetailPOList;
    }

    /**
     * 设置
     *
     * @param updateOrderItemTaskInfoDetailPOList
     */
    public void
        setUpdateOrderItemTaskInfoDetailPOList(List<OrderItemTaskInfoDetailPO> updateOrderItemTaskInfoDetailPOList) {
        this.updateOrderItemTaskInfoDetailPOList = updateOrderItemTaskInfoDetailPOList;
    }

    /**
     * 获取
     *
     * @return addContainerList
     */
    public List<BatchTaskItemContainerPO> getAddContainerList() {
        return this.addContainerList;
    }

    /**
     * 设置
     *
     * @param addContainerList
     */
    public void setAddContainerList(List<BatchTaskItemContainerPO> addContainerList) {
        this.addContainerList = addContainerList;
    }

    /**
     * 获取
     *
     * @return updateContainerList
     */
    public List<BatchTaskItemContainerPO> getUpdateContainerList() {
        return this.updateContainerList;
    }

    /**
     * 设置
     *
     * @param updateContainerList
     */
    public void setUpdateContainerList(List<BatchTaskItemContainerPO> updateContainerList) {
        this.updateContainerList = updateContainerList;
    }

    public void intPickUpDTOList(List<PickUpDTO> pickUpDTOList) {
        if (CollectionUtils.isEmpty(this.pickUpDTOList)) {
            this.pickUpDTOList = new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(pickUpDTOList)) {
            return;
        }

        this.pickUpDTOList.addAll(pickUpDTOList);
    }

    /**
     * 获取 待完成的拣货任务
     *
     * @return completeBatchTaskPO 待完成的拣货任务
     */
    public BatchTaskPO getCompleteBatchTaskPO() {
        return this.completeBatchTaskPO;
    }

    /**
     * 设置 待完成的拣货任务
     *
     * @param completeBatchTaskPO 待完成的拣货任务
     */
    public void setCompleteBatchTaskPO(BatchTaskPO completeBatchTaskPO) {
        this.completeBatchTaskPO = completeBatchTaskPO;
    }
}
