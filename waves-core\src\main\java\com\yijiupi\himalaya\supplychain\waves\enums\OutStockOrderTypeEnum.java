package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * <AUTHOR>
 * @Title: himalaya-supplychain-microservice-goodspursueoperate
 * @Package com.yijiupi.himalaya.supplychain.goodspursueoperate.enums
 * @Description:
 * @date 2018/5/5 16:01
 */
public enum OutStockOrderTypeEnum {
    /**
     * 枚举
     */
    销售订单((byte)0), 招商订单((byte)1), 经销商直配订单((byte)2), 大货批发订单((byte)3), 经济人撮合订单((byte)4), 临期产品订单((byte)6), 换货订单((byte)7),
    团购订单((byte)8), 独家包销订单((byte)9), 大商转配送订单((byte)10), 轻加盟订单((byte)11), 经销商订单((byte)12), 易经销订单((byte)13),
    兑奖配送单((byte)25), 销售出库((byte)51), 调拨出库((byte)52), 破损出库((byte)53), 其他出库((byte)54), 采购退货((byte)55), 盘亏出库((byte)56),
    第三方出库((byte)57), 同城调拨出库((byte)58), 处理品转入((byte)59);

    /**
     * type
     */
    private Byte type;

    OutStockOrderTypeEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 返回枚举
     *
     * @param type
     * @return
     */
    public static OutStockOrderTypeEnum getEnum(Byte type) {
        OutStockOrderTypeEnum e = null;

        if (type != null) {
            for (OutStockOrderTypeEnum o : values()) {
                if (o.getType() == type) {
                    e = o;
                }
            }
        }
        return e;
    }

    public static String getType(Byte type) {
        if (null == type) {
            return null;
        }
        if (type != null) {
            for (OutStockOrderTypeEnum o : values()) {
                if (o.getType() == type) {
                    return o.name();
                }
            }
        }
        return null;
    }
}
