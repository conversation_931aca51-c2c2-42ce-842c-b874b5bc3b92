package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.Map;

public class OutStockLackDTO implements Serializable {

    private static final long serialVersionUID = 6504576179660850306L;

    private Long businessId;
    /**
     * 订单项发货数量Map<订单项ID,上次缺货数量>
     */
    private Map<Long, Integer> itemShipMap;
    /**
     * 数量明细Map<订单项ID,缺货或部分配送实际数据-编辑后数量>
     */
    private Map<Long, Integer> itemMap;
    private String orderNo;
    private Integer userId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    private Map<Object, Object> extendMap;

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Map<Long, Integer> getItemShipMap() {
        return itemShipMap;
    }

    public void setItemShipMap(Map<Long, Integer> itemShipMap) {
        this.itemShipMap = itemShipMap;
    }

    public Map<Long, Integer> getItemMap() {
        return itemMap;
    }

    public void setItemMap(Map<Long, Integer> itemMap) {
        this.itemMap = itemMap;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Map<Object, Object> getExtendMap() {
        return extendMap;
    }

    public void setExtendMap(Map<Object, Object> extendMap) {
        this.extendMap = extendMap;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
