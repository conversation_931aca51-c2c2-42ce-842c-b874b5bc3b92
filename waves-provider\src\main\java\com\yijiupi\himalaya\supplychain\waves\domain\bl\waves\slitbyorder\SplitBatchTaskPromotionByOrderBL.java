package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.slitbyorder;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderRequestBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/23
 */
@Service
public class SplitBatchTaskPromotionByOrderBL extends SplitBatchTaskByOrderBL {

    @Override
    List<OutStockOrderPO> supportOrderList(SplitBatchTaskByOrderRequestBO bo) {
        List<OutStockOrderPO> promotionOrderList =
            bo.getCreateDTO().getOrders().stream().filter(m -> !abnormalSourceTypes.contains(m.getOrderSourceType()))
                .filter(m -> SplitWaveOrderUtil.orderIsPromotion(m)).collect(Collectors.toList());

        return promotionOrderList;
    }

    @Override
    protected List<SplitBatchTaskByOrderResultBO> doSplitBatchTaskByOrder(SplitBatchTaskByOrderRequestBO bo,
        List<OutStockOrderPO> promotionOrderList) {
        WaveCreateDTO createDTO = bo.getCreateDTO();
        WavesStrategyBO wavesStrategyDTO = createDTO.getWavesStrategyDTO();

        if (CollectionUtils.isEmpty(promotionOrderList)) {
            return Collections.emptyList();
        }

        return splitByOrderFeatureAndAddress(promotionOrderList);
    }
}
