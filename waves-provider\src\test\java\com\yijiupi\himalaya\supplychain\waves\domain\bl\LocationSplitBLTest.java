package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CreateBatchLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.LocationSplitHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.LocationSplitHelperResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor.LocationSplitHelperResultBOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * <AUTHOR>
 * @title: LocationSplitBL
 * @description:
 * @date 2022-12-01 16:44
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class LocationSplitBLTest {

    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private BatchOrderProcessBL batchOrderProcessBL;

    @Test
    public void splitAndMergeTest() {
        CreateBatchLocationBO bo = new CreateBatchLocationBO();
        // bo.setWarehouseId(warehouseId);
        // bo.setIsOpenStock(isOpenStock);
        // bo.setIsOpenLocationGroup(isOpenLocationGroup);
        // bo.setWorkSetting(workSettingDTO);
        // bo.setWarehouseConfigDTO(warehouseConfigDTO);
        // bo.setWavesStrategyDTO((WavesStrategyBO) wavesStrategyDTO);

        List<Long> orderIds = Arrays.asList(998000221124159800L, 998000221124149798L, 998000221124149797L,
            998000221123169781L, 998000221123109734L, 998000221123109721L);
        List<OutStockOrderPO> outStockOrderList = outStockOrderMapper
            .findByOrderIdWithoutCon(orderIds.stream().map(String::valueOf).collect(Collectors.toList()));
        List<OutStockOrderItemPO> totalItemList =
            outStockOrderList.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList());

        // List<OutStockOrderPO> locationOrderList = createBatchLocationLargePickCargoBL.setLocation(outStockOrderList,
        // bo);
        List<OutStockOrderItemPO> passageItemList = new ArrayList<>();

        // passageItemList =
        // MergeSplitItemConvertor.mergerItemList(passageItemList, totalItemList, Collections.emptyList(), null, null);
    }

    @Test
    public void splitTest() {
        // 正好是0的
        // List<Long> orderIds = Arrays.asList(998000221124159800L, 998000221124149798L, 998000221124149797L,
        // 998000221123169781L);
        List<Long> orderIds = Arrays.asList(998000221124159800L, 998000221124149798L, 998000221124149797L,
            998000221123169781L, 998000221123109734L, 998000221123109721L);
        List<OutStockOrderPO> outStockOrderList = outStockOrderMapper
            .findByOrderIdWithoutCon(orderIds.stream().map(String::valueOf).collect(Collectors.toList()));

        List<OutStockOrderItemPO> itemList =
            outStockOrderList.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList());
        itemList =
            itemList.stream().filter(m -> m.getSkuid().equals(4983004920933682706L)).collect(Collectors.toList());

        // 按产品聚合订单项
        Map<Long, List<OutStockOrderItemPO>> skuGroupMap =
            itemList.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid));

        // List<LocationSplitHelperBO> splitHelperList = LocationSplitHelperBOConvertor.convert(skuGroupMap);
        //
        // List<LocationSplitHelperResultBO> splitResultBOList =
        // splitHelperList.stream().map(this::divideCount).collect(Collectors.toList());
        //
        // Assertions.assertThat(splitResultBOList).isNotNull();

    }

    // 先分摊大件到各个订单项
    private LocationSplitHelperResultBO divideCount(LocationSplitHelperBO bo) {

        // 把两种极端的排除
        if (bo.getUnitCount().compareTo(BigDecimal.ZERO) == 0) {
            return LocationSplitHelperResultBOConvertor.convertPackage(bo);
        }
        if (bo.getPackageCount().compareTo(BigDecimal.ZERO) == 0) {
            return LocationSplitHelperResultBOConvertor.convertUnit(bo);
        }

        // 有大件有小件的是需要分摊的
        // 1、规格6， 买了4和3的；2、规格6，买了6和1的
        return LocationSplitHelperResultBOConvertor.convertSplit(bo);
    }

    @Test
    public void minusTest() {
        List<OutStockOrderItemPO> itemList = outStockOrderItemMapper.findByOutstockorderIdList(
            Arrays.asList(998000221124159800L, 998000221124149798L, 998000221124149797L, 998000221123169781L));
        itemList =
            itemList.stream().filter(m -> m.getSkuid().equals(4983004920933682706L)).collect(Collectors.toList());
        BigDecimal totalCount =
            itemList.stream().map(OutStockOrderItemPO::getUnittotalcount).reduce(BigDecimal.ZERO, BigDecimal::add);
        itemList.sort((o1, o2) -> o2.getUnittotalcount().compareTo(o1.getUnittotalcount()));

        OutStockOrderItemPO itemPO = itemList.stream().findFirst().get();
        BigDecimal[] count = totalCount.divideAndRemainder(itemPO.getSpecquantity());

        OutStockOrderItemPO result = getMinusItem(count[0].multiply(itemPO.getSpecquantity()), itemList, 0);

        Assertions.assertThat(result).isNotNull();
    }

    private static OutStockOrderItemPO getMinusItem(BigDecimal packageUnitTotalCount,
        List<OutStockOrderItemPO> itemList, int i) {
        if (packageUnitTotalCount.subtract(itemList.get(i).getUnittotalcount()).compareTo(BigDecimal.ZERO) <= 0) {
            return itemList.get(i);
        }

        BigDecimal tmpCount = packageUnitTotalCount.subtract(itemList.get(i).getUnittotalcount());
        return getMinusItem(tmpCount, itemList, i + 1);
    }

}
