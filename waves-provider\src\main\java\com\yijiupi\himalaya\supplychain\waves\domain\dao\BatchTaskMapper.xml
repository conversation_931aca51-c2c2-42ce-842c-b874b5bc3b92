<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper">
    <!--auto generated Code-->
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="Org_id" property="orgId" jdbcType="INTEGER"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="BatchTaskNo" property="batchTaskNo" jdbcType="VARCHAR"/>
        <result column="BatchTaskName" property="batchTaskName" jdbcType="VARCHAR"/>
        <result column="BatchNo" property="batchNo" jdbcType="VARCHAR"/>
        <result column="Batch_id" property="batchId" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="SorterName" property="sorter" jdbcType="VARCHAR"/>
        <result column="Sorter_id" property="sorterId" jdbcType="INTEGER"/>
        <result column="OrderAmount" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="OrderCount" property="orderCount" jdbcType="INTEGER"/>
        <result column="SkuCount" property="skuCount" jdbcType="INTEGER"/>
        <result column="PackageAmount" property="packageAmount" jdbcType="DECIMAL"/>
        <result column="UnitAmount" property="unitAmount" jdbcType="DECIMAL"/>
        <result column="TaskState" property="taskState" jdbcType="TINYINT"/>
        <result column="PickingType" property="pickingType" jdbcType="TINYINT"/>
        <result column="PickingGroupStrategy" property="pickingGroupStrategy" jdbcType="TINYINT"/>
        <result column="location_id" property="locationId" jdbcType="BIGINT"/>
        <result column="LocationName" property="locationName" jdbcType="VARCHAR"/>
        <result column="CategoryName" property="categoryName" jdbcType="VARCHAR"/>
        <result column="LocationCategory" property="locationCategory" jdbcType="TINYINT"/>
        <result column="PrintTimes" property="printTimes" jdbcType="INTEGER"/>
        <result column="IsPrinted" property="isPrinted" jdbcType="BIT"/>
        <result column="ToLocationName" property="toLocationName" jdbcType="VARCHAR"/>
        <result column="ToLocation_id" property="toLocationId" jdbcType="BIGINT"/>
        <result column="OrderSelection" property="orderSelection" jdbcType="TINYINT"/>
        <result column="SowTask_Id" property="sowTaskId" jdbcType="BIGINT"/>
        <result column="SowTaskNo" property="sowTaskNo" jdbcType="VARCHAR"/>
        <result column="sowLocationId" property="sowLocationId" jdbcType="BIGINT"/>
        <result column="sowLocationName" property="sowLocationName" jdbcType="VARCHAR"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="SortGroupId" property="sortGroupId" jdbcType="BIGINT"/>
        <result column="CompleteTime" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="IsNeedPick" property="needPick" jdbcType="TINYINT"/>
        <result column="StartTime" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="CompleteUser" property="completeUser" jdbcType="VARCHAR"/>
        <result column="CompleteUserId" property="completeUserId" jdbcType="BIGINT"/>
        <result column="BatchName" property="batchName" jdbcType="VARCHAR"/>
        <result column="BatchTaskType" property="batchTaskType" jdbcType="TINYINT"/>
        <result column="RefOrderNo" property="refOrderNo" jdbcType="VARCHAR"/>
        <result column="Passage_Id" property="passageId" jdbcType="BIGINT"/>
        <result column="PassageName" property="passageName" jdbcType="VARCHAR"/>
        <result column="PickPattern" property="pickPattern" jdbcType="TINYINT"/>
        <result column="toPalletNo" property="toPalletNo" jdbcType="VARCHAR"/>
        <result column="KindOfPicking" property="kindOfPicking" jdbcType="TINYINT"/>
        <result column="taskAppendSequence" property="taskAppendSequence" jdbcType="TINYINT"/>
        <result column="taskWarehouseFeatureType" property="taskWarehouseFeatureType" jdbcType="TINYINT"/>
        <result column="shiftOfPerformance" jdbcType="DATE" property="shiftOfPerformance"/>
    </resultMap>

    <resultMap id="ItemBaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="orgId" property="orgId" jdbcType="INTEGER"/>
        <result column="WarehouseId" property="warehouseId" jdbcType="INTEGER"/>
        <result column="BatchTaskNo" property="batchTaskNo" jdbcType="VARCHAR"/>
        <result column="BatchTaskName" property="batchTaskName" jdbcType="VARCHAR"/>
        <result column="BatchNo" property="batchNo" jdbcType="VARCHAR"/>
        <result column="batchId" property="batchId" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="sorter" property="sorter" jdbcType="VARCHAR"/>
        <result column="sorterId" property="sorterId" jdbcType="INTEGER"/>
        <result column="OrderAmount" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="OrderCount" property="orderCount" jdbcType="INTEGER"/>
        <result column="SkuCount" property="skuCount" jdbcType="INTEGER"/>
        <result column="PackageAmount" property="packageAmount" jdbcType="DECIMAL"/>
        <result column="UnitAmount" property="unitAmount" jdbcType="DECIMAL"/>
        <result column="TaskState" property="taskState" jdbcType="TINYINT"/>
        <result column="PickingType" property="pickingType" jdbcType="TINYINT"/>
        <result column="PickingGroupStrategy" property="pickingGroupStrategy" jdbcType="TINYINT"/>
        <result column="locationId" property="locationId" jdbcType="BIGINT"/>
        <result column="LocationName" property="locationName" jdbcType="VARCHAR"/>
        <result column="CategoryName" property="categoryName" jdbcType="VARCHAR"/>
        <result column="LocationCategory" property="locationCategory" jdbcType="TINYINT"/>
        <result column="PrintTimes" property="printTimes" jdbcType="INTEGER"/>
        <result column="IsPrinted" property="isPrinted" jdbcType="BIT"/>
        <result column="ToLocationName" property="toLocationName" jdbcType="VARCHAR"/>
        <result column="toLocationId" property="toLocationId" jdbcType="BIGINT"/>
        <result column="OrderSelection" property="orderSelection" jdbcType="TINYINT"/>
        <result column="sowTaskId" property="sowTaskId" jdbcType="BIGINT"/>
        <result column="SowTaskNo" property="sowTaskNo" jdbcType="VARCHAR"/>
        <result column="sowLocationId" property="sowLocationId" jdbcType="BIGINT"/>
        <result column="sowLocationName" property="sowLocationName" jdbcType="VARCHAR"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="SortGroupId" property="sortGroupId" jdbcType="BIGINT"/>
        <result column="CompleteTime" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="IsNeedPick" property="needPick" jdbcType="TINYINT"/>
        <result column="StartTime" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="CompleteUser" property="completeUser" jdbcType="VARCHAR"/>
        <result column="CompleteUserId" property="completeUserId" jdbcType="BIGINT"/>
        <result column="BatchName" property="batchName" jdbcType="VARCHAR"/>
        <result column="BatchTaskType" property="batchTaskType" jdbcType="TINYINT"/>
        <result column="RefOrderNo" property="refOrderNo" jdbcType="VARCHAR"/>
        <result column="PassageName" property="passageName" jdbcType="VARCHAR"/>
        <result column="PickPattern" property="pickPattern" jdbcType="TINYINT"/>
        <result column="KindOfPicking" property="kindOfPicking" jdbcType="TINYINT"/>
        <result column="taskAppendSequence" property="taskAppendSequence" jdbcType="TINYINT"/>
        <result column="taskWarehouseFeatureType" property="taskWarehouseFeatureType" jdbcType="TINYINT"/>
        <result column="shiftOfPerformance" property="shiftOfPerformance" jdbcType="VARCHAR"/>
        <collection property="batchTaskItemList"
                    ofType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO">
            <result column="item_id" property="id" jdbcType="VARCHAR"/>
            <result column="item_Org_id" property="orgId" jdbcType="INTEGER"/>
            <result column="item_BatchtaskNo" property="batchTaskNo" jdbcType="VARCHAR"/>
            <result column="item_Batchtask_id" property="batchTaskId" jdbcType="VARCHAR"/>
            <result column="item_RefOrderNo" property="refOrderNo" jdbcType="VARCHAR"/>
            <result column="item_RefOrder_id" property="refOrderId" jdbcType="VARCHAR"/>
            <result column="item_ProductName" property="productName" jdbcType="VARCHAR"/>
            <result column="item_SkuId" property="skuId" jdbcType="VARCHAR"/>
            <result column="item_ProductBrand" property="productBrand" jdbcType="VARCHAR"/>
            <result column="item_CategoryName" property="categoryName" jdbcType="VARCHAR"/>
            <result column="item_SpecName" property="specName" jdbcType="VARCHAR"/>
            <result column="item_SpecQuantity" property="specQuantity" jdbcType="DECIMAL"/>
            <result column="item_SaleSpec" property="saleSpec" jdbcType="VARCHAR"/>
            <result column="item_SaleSpecQuantity" property="saleSpecQuantity" jdbcType="DECIMAL"/>
            <result column="item_PackageName" property="packageName" jdbcType="VARCHAR"/>
            <result column="item_PackageCount" property="packageCount" jdbcType="DECIMAL"/>
            <result column="item_UnitName" property="unitName" jdbcType="VARCHAR"/>
            <result column="item_UnitCount" property="unitCount" jdbcType="DECIMAL"/>
            <result column="item_UnitTotalCount" property="unitTotalCount" jdbcType="DECIMAL"/>
            <result column="item_TaskState" property="taskState" jdbcType="TINYINT"/>
            <result column="item_LackUnitCount" property="lackUnitCount" jdbcType="DECIMAL"/>
            <result column="item_Remark" property="remark" jdbcType="VARCHAR"/>
            <result column="item_OverSortCount" property="overSortCount" jdbcType="DECIMAL"/>
            <result column="item_LocationId" property="locationId" jdbcType="BIGINT"/>
            <result column="item_LocationName" property="locationName" jdbcType="VARCHAR"/>
            <result column="item_LocationCategory" property="locationCategory" jdbcType="TINYINT"/>
            <result column="item_OrderSquence" property="orderSquence" jdbcType="INTEGER"/>
            <result column="item_ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
            <result column="item_SowTaskItemId" property="sowTaskItemId" jdbcType="BIGINT"/>
            <result column="item_orderItemId" property="orderItemId" jdbcType="BIGINT"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        bt
        .
        id
        ,
        bt.Org_id as orgId,
        bt.BatchTaskNo,
        bt.BatchTaskName,
        bt.BatchNo,
        bt.CreateTime,
        bt.Sorter_id as sorterId,
        bt.SorterName as sorter,
        bt.OrderAmount,
        bt.OrderCount,
        bt.SkuCount,
        bt.PackageAmount,
        bt.UnitAmount,
        bt.TaskState,
        bt.PickingType,
        bt.PickingGroupStrategy,
        bt.location_id as locationId,
        bt.LocationName,
        bt.CategoryName,
        bt.LocationCategory,
        bt.PrintTimes,
        bt.IsPrinted,
        bt.Batch_id as batchId,
        bt.ToLocation_Id as toLocationId,
        bt.ToLocationName,
        bt.SowTask_id as sowTaskId,
        bt.SowTaskNo,
        bt.Warehouse_Id as WarehouseId,
        bt.Remark,
        bt.SortGroupId,
        bt.CompleteTime,
        bt.IsNeedPick,
        bt.StartTime,
        bt.CompleteUser,
        bt.CompleteUserId,
        bt.BatchTaskType,
        bt.Passage_Id,
        bt.Passage_Id as PassageId,
        bt.PassageName,
        bt.PickPattern,
        bt.toPalletNo,
        bt.KindOfPicking,
        bt.taskAppendSequence,
        bt.taskWarehouseFeatureType,
        bt.Warehouse_Id as warehouseId,
        bt.shiftOfPerformance
    </sql>

    <sql id="Base_Column_List_1">
        bt
        .
        id
        ,
        bt.Org_id as orgId,
        bt.BatchTaskNo,
        bt.BatchTaskName,
        bt.BatchNo,
        bt.CreateTime,
        bt.Sorter_id as sorterId,
        bt.SorterName as sorter,
        bt.OrderAmount,
        bt.OrderCount,
        bt.SkuCount,
        bt.PackageAmount,
        bt.UnitAmount,
        bt.TaskState,
        bt.PickingType,
        bt.PickingGroupStrategy,
        bt.location_id as locationId,
        bt.LocationName,
        bt.CategoryName,
        bt.LocationCategory,
        bt.PrintTimes,
        bt.IsPrinted,
        bt.Batch_id as batchId,
        bt.ToLocation_Id,
        bt.ToLocationName,
        bt.SowTask_id as sowTaskId,
        bt.SowTaskNo,
        bt.Warehouse_Id as WarehouseId,
        bt.Remark,
        bt.SortGroupId,
        bt.CompleteTime,
        bt.IsNeedPick,
        bt.StartTime,
        bt.CompleteUser,
        bt.CompleteUserId,
        bt.BatchTaskType,
        bt.Passage_Id,
        bt.PassageName,
        bt.PickPattern,
        bt.KindOfPicking,
        bt.taskAppendSequence,
        bt.taskWarehouseFeatureType,
        bt.shiftOfPerformance
    </sql>

    <sql id="Item_Alias_Column_List">
        bti
        .
        id
        as item_id,
        bti.Org_id as item_Org_id,
        bti.BatchtaskNo as item_BatchtaskNo,
        bti.Batchtask_id as item_Batchtask_id,
        bti.RefOrder_id as item_RefOrder_id,
        bti.RefOrderNo as item_RefOrderNo,
        bti.ProductName as item_ProductName,
        bti.SkuId as item_SkuId,
        bti.ProductBrand as item_ProductBrand,
        bti.CategoryName as item_CategoryName,
        bti.SpecName as item_SpecName,
        bti.SpecQuantity as item_SpecQuantity,
        bti.SaleSpec as item_SaleSpec,
        bti.SaleSpecQuantity as item_SaleSpecQuantity,
        bti.PackageName as item_PackageName,
        bti.PackageCount as item_PackageCount,
        bti.UnitName as item_UnitName,
        bti.UnitCount as item_UnitCount,
        bti.UnitTotalCount as item_UnitTotalCount,
        bti.TaskState as item_TaskState,
        bti.LackUnitCount as item_LackUnitCount,
        bti.Remark as item_Remark,
        bti.OverSortCount as item_OverSortCount,
        bti.LocationId as item_LocationId,
        bti.LocationName as item_LocationName,
        bti.LocationCategory as item_LocationCategory,
        bti.Channel as item_Channel,
        bti.Source as item_Source,
        bti.OrderSquence as item_OrderSquence,
        bti.ProductSpecification_Id as item_ProductSpecification_Id,
        bti.SowTaskItemId as item_SowTaskItemId
    </sql>

    <sql id="taskStateSql">and bt.TaskState in(0, 1, 2)</sql>

    <!--2018-03-16 09:39:51-->
    <select id="findList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,
        b.areaName,b.routeName,b.orderSelection,b.BatchName,
        st.Location_Id as sowLocationId, st.LocationName as sowLocationName
        from
        batchtask bt
        INNER JOIN batch b on bt.batch_id =b.id
        LEFT JOIN sowtask st on bt.SowTask_Id = st.id
        where bt.Warehouse_Id = #{dto.warehouseId}
        <if test="dto.cityId != null">
            and bt.Org_id = #{dto.cityId,jdbcType=INTEGER}
        </if>
        <if test="dto.batchTaskNo != null and dto.batchTaskNo!=''">and bt.BatchTaskNo =
            #{dto.batchTaskNo,jdbcType=VARCHAR}
        </if>
        <if test="dto.batchNo != null and dto.batchNo!=''">AND bt.BatchNo= #{dto.batchNo,jdbcType=VARCHAR}</if>
        <if test="dto.sorterId != null ">AND bt.Sorter_id= #{dto.sorterId,jdbcType=VARCHAR}</if>
        <if test="dto.printed != null ">AND bt.IsPrinted= #{dto.printed,jdbcType=BIT}</if>
        <if test="dto.startTime != null">AND bt.CreateTime <![CDATA[ >= ]]> #{dto.startTime}</if>
        <if test="dto.endTime != null">AND bt.CreateTime <![CDATA[ <= ]]> #{dto.endTime}</if>
        <if test="dto.taskState != null">AND bt.TaskState = #{dto.taskState,jdbcType=TINYINT}</if>
        <if test="dto.taskStateList != null">
            AND bt.TaskState in
            <foreach collection="dto.taskStateList" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="dto.sorter != null and dto.sorter!=''">
            AND bt.SorterName like concat('%',#{dto.sorter,jdbcType=VARCHAR},'%')
        </if>
        <if test="dto.remark != null and dto.remark !=''">
            AND b.batchName like concat('%',#{dto.remark,jdbcType=VARCHAR},'%')
        </if>
        <if test="dto.property != null and dto.property !=''">
            AND bt.batchTaskName like concat('%',#{dto.property,jdbcType=VARCHAR},'%')
        </if>
        <if test="dto.taskWarehouseFeatureTypes != null and dto.taskWarehouseFeatureTypes.size() > 0">
            AND bt.taskWarehouseFeatureType in
            <foreach collection="dto.taskWarehouseFeatureTypes" index="index" item="item" separator="," open="("
                     close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="dto.pickPattern != null">
            AND bt.PickPattern=#{dto.pickPattern,jdbcType=TINYINT}
        </if>
        <if test="dto.shiftOfPerformance != null">
            AND bt.shiftOfPerformance=#{dto.shiftOfPerformance}
        </if>
        order by bt.CreateTime desc, bt.Id
    </select>
    <select id="findBatchsByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtask bt
        INNER JOIN batchtask old on bt.batch_id = old.batch_id
        where old.id = #{taskId}
        <include refid="taskStateSql"/>
    </select>
    <select id="findBatchsByTaskNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtask bt
        INNER JOIN batchtask old on bt.batch_id = old.batch_id
        where old.BatchTaskNo = #{taskNo}
        <include refid="taskStateSql"/>
    </select>
    <select id="findBatchTaskById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtask bt
        where bt.id = #{taskId}
    </select>
    <select id="findBatchTaskByNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtask bt
        where bt.BatchTaskNo = #{taskNo}
    </select>
    <insert id="insertList">
        INSERT INTO batchtask (
        id,
        org_id,
        Warehouse_Id,
        BatchTaskNo,
        BatchTaskName,
        batch_id,
        BatchNo,
        SorterName,
        Sorter_id,
        OrderAmount,
        OrderCount,
        SkuCount,
        PackageAmount,
        UnitAmount,
        TaskState,
        PickingType,
        PickingGroupStrategy,
        CategoryName,
        location_id,
        LocationName,
        LocationCategory,
        toLocation_id,
        toLocationName,
        SowTask_Id,
        SowTaskNo,
        CreateTime,
        Remark,
        SortGroupId,
        IsNeedPick,
        BatchTaskType,
        Passage_Id,
        PassageName,
        PickPattern,
        KindOfPicking,
        taskAppendSequence,
        taskWarehouseFeatureType,
        WarehouseAllocationType,
        shiftOfPerformance
        )VALUES
        <foreach collection="batchTaskPOs" item="batchTaskPO" index="index" separator=",">
            (
            #{batchTaskPO.id,jdbcType=VARCHAR},
            #{batchTaskPO.orgId,jdbcType=INTEGER},
            #{batchTaskPO.warehouseId,jdbcType=INTEGER},
            #{batchTaskPO.batchTaskNo,jdbcType=VARCHAR},
            #{batchTaskPO.batchTaskName,jdbcType=VARCHAR},
            #{batchTaskPO.batchId,jdbcType=VARCHAR},
            #{batchTaskPO.batchNo,jdbcType=VARCHAR},
            #{batchTaskPO.sorter,jdbcType=VARCHAR},
            #{batchTaskPO.sorterId,jdbcType=INTEGER},
            #{batchTaskPO.orderAmount,jdbcType=DECIMAL},
            #{batchTaskPO.orderCount,jdbcType=INTEGER},
            #{batchTaskPO.skuCount,jdbcType=INTEGER},
            #{batchTaskPO.packageAmount,jdbcType=DECIMAL},
            #{batchTaskPO.unitAmount,jdbcType=DECIMAL},
            #{batchTaskPO.taskState,jdbcType=TINYINT},
            #{batchTaskPO.pickingType,jdbcType=TINYINT},
            #{batchTaskPO.pickingGroupStrategy,jdbcType=TINYINT},
            #{batchTaskPO.categoryName,jdbcType=VARCHAR},
            #{batchTaskPO.locationId,jdbcType=BIGINT},
            #{batchTaskPO.locationName,jdbcType=VARCHAR},
            #{batchTaskPO.locationCategory,jdbcType=TINYINT},
            #{batchTaskPO.toLocationId,jdbcType=BIGINT},
            #{batchTaskPO.toLocationName,jdbcType=VARCHAR},
            #{batchTaskPO.sowTaskId,jdbcType=BIGINT},
            #{batchTaskPO.sowTaskNo,jdbcType=VARCHAR},
            #{batchTaskPO.createTime,jdbcType=TIMESTAMP},
            #{batchTaskPO.remark,jdbcType=VARCHAR},
            #{batchTaskPO.sortGroupId,jdbcType=BIGINT},
            #{batchTaskPO.needPick,jdbcType=TINYINT},
            #{batchTaskPO.BatchTaskType,jdbcType=TINYINT},
            #{batchTaskPO.passageId,jdbcType=BIGINT},
            #{batchTaskPO.passageName,jdbcType=VARCHAR},
            #{batchTaskPO.pickPattern,jdbcType=TINYINT},
            #{batchTaskPO.kindOfPicking,jdbcType=TINYINT},
            #{batchTaskPO.taskAppendSequence,jdbcType=TINYINT},
            #{batchTaskPO.taskWarehouseFeatureType,jdbcType=TINYINT},
            #{batchTaskPO.warehouseAllocationType,jdbcType=INTEGER},
            #{batchTaskPO.shiftOfPerformance,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="findBatchTaskSortList" resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskSortPO">
        select bt.id as id,
        bt.BatchTaskNo as batchTaskNo,
        bt.BatchTaskName as batchTaskName,
        bt.BatchNo as batchNo,
        bt.Sorter_id as sorterId,
        bt.SkuCount as skuCount,
        bt.PackageAmount as packageAmount,
        bt.UnitAmount as unitAmount,
        bt.TaskState as taskState,
        bt.PickingType as pickingType,
        bt.PickingGroupStrategy as pickingGroupStrategy,
        bt.LocationName as locationName,
        bt.ToLocation_Id as toLocationId,
        bt.ToLocationName as toLocationName,
        bt.CategoryName as categoryName,
        bt.CreateTime as createTime,
        b.AreaName as areaName,
        b.RouteName as routeName,
        b.OrderSelection as orderSelection,
        bt.OrderCount as orderCount,
        b.CreateTime as batchCreateTime,
        b.batchName as batchName,
        bt.SowTask_Id as sowTaskId,
        b.SowType as sowType,
        bt.Passage_Id as passageId,
        bt.PickPattern as pickPattern,
        bt.Remark as remark,
        bt.KindOfPicking as kindOfPicking,
        bt.taskAppendSequence,
        bt.taskWarehouseFeatureType
        from batchtask bt
        inner join batch b on bt.Batch_id = b.id
        WHERE bt.Org_id = #{cityId,jdbcType=INTEGER}
        and bt.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and bt.Sorter_id = #{userId,jdbcType=INTEGER}
        and bt.TaskState = 2
        <if test="startTime != null">
            and bt.CreateTime <![CDATA[ >= ]]> #{startTime,jdbcType=DATE}
        </if>
        <if test="endTime != null">
            and bt.CreateTime <![CDATA[ <= ]]> #{endTime,jdbcType=DATE}
        </if>
        <if test="splitWarehouseAttrList != null and splitWarehouseAttrList.size() > 0">
            and bt.taskWarehouseFeatureType in
            <foreach collection="splitWarehouseAttrList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        order by batchCreateTime desc, createTime desc, id
    </select>

    <sql id="findBatchTaskSortSql">
        bt
        .
        id
        as id,
        bt.BatchTaskNo as batchTaskNo,
        bt.BatchTaskName as batchTaskName,
        bt.BatchNo as batchNo,
        bt.Sorter_id as sorterId,
        bt.SkuCount as skuCount,
        bt.PackageAmount as packageAmount,
        bt.UnitAmount as unitAmount,
        bt.PickingType as pickingType,
        bt.PickingGroupStrategy as pickingGroupStrategy,
        bt.LocationName as locationName,
        bt.ToLocation_Id as toLocationId,
        bt.ToLocationName as toLocationName,
        bt.CategoryName as categoryName,
        bt.CreateTime as createTime,
        b.AreaName as areaName,
        b.RouteName as routeName,
        b.OrderSelection as orderSelection,
        b.CreateTime as batchCreateTime,
        b.batchName as batchName,
        b.SowType as sowType,
        bt.OrderCount as orderCount,
        bt.SortGroupId as sortGroupId,
        bt.SowTask_Id as sowTaskId,
        bt.Passage_Id as passageId,
        bt.PickPattern as pickPattern,
        bt.Remark as remark,
        bt.KindOfPicking as kindOfPicking,
        bt.taskAppendSequence,
        bt.taskWarehouseFeatureType,
        bt.shiftOfPerformance
    </sql>

    <update id="updateBatchTaskById">
        UPDATE batchtask
        <trim prefix="set" suffixOverrides=",">
            PickSource=1,
            <if test="taskState != null">TaskState=#{taskState,jdbcType=VARCHAR},</if>
            <if test="pickUser != null">CompleteUser=#{pickUser,jdbcType=VARCHAR},</if>
            <if test="pickUserId != null">CompleteUserId=#{pickUserId,jdbcType=VARCHAR},</if>
            <if test="locationId != null">ToLocation_Id=#{locationId,jdbcType=BIGINT},</if>
            <if test="locationName != null">ToLocationName = #{locationName,jdbcType=VARCHAR},</if>
            <if test="startPickFlag != null and startPickFlag == true">StartTime = now(),</if>
            <if test="taskState != null and taskState == 2">CompleteTime = now(),</if>
            <if test="toPalletNo != null and toPalletNo != ''">toPalletNo = #{toPalletNo,jdbcType=VARCHAR}</if>
        </trim>
        WHERE id=#{id}
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
    </update>

    <update id="updateBatchTaskStateCompleteByIds">
        UPDATE batchtask set PickSource=1, TaskState=2, CompleteTime = now()
        WHERE id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateBatchTask" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO">
        UPDATE batchtask
        SET OrderCount = #{orderCount,jdbcType=INTEGER},
        SkuCount = #{skuCount,jdbcType=INTEGER},
        PackageAmount = #{packageAmount,jdbcType=DECIMAL},
        UnitAmount = #{unitAmount,jdbcType=DECIMAL}
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateBatchTaskByNo">
        UPDATE batchtask
        SET TaskState=#{taskState}
        WHERE BatchTaskNo = #{taskNo}
    </update>

    <select id="findBatchTaskSortListByReceive"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskSortPO">
        select * from
        (
        <!--（1）分配给拣货员，待拣货和拣货中的拣货任务-->
        select
        <include refid="findBatchTaskSortSql"/>,
        bt.TaskState as taskState,
        0 as receiveFlag,
        0 as groupLevel
        from batchtask bt inner join batch b on bt.Batch_id = b.id
        where bt.Org_id = #{cityId,jdbcType=INTEGER}
        and bt.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and bt.Sorter_id = #{userId,jdbcType=INTEGER}
        and bt.TaskState in (0, 1)
        <if test="splitWarehouseAttrList != null and splitWarehouseAttrList.size() > 0">
            and bt.taskWarehouseFeatureType in
            <foreach collection="splitWarehouseAttrList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        union all
        <!--（2）未分配给拣货员，待领取的拣货任务 -->
        select
        <include refid="findBatchTaskSortSql"/>,
        -1 as taskState,

        <!-- 区分是否是无货位的拣货任务（拆分领取）-->
        case
        when (select count(*) from batchtaskitem bti where bti.Batchtask_id = bt.id and bti.LocationId is not null) = 0
        then 2 else 1
        end receiveFlag,

        <!-- 分区优先级标标识： 1：自己分区 > 2：非自己分区 > 3：没有分区 -->
        case
        <if test="sortGroupId != null and sortGroupId.size() > 0">
            when bt.SortGroupId in
            <foreach collection="sortGroupId" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
            then 1
        </if>
        when bt.SortGroupId is null then 3
        when bt.SortGroupId = 0 then 3
        else 2
        end groupLevel
        from batchtask bt inner join batch b on bt.Batch_id = b.id
        where bt.Org_id = #{cityId,jdbcType=INTEGER}
        and bt.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and (bt.Sorter_id is null or bt.Sorter_id = 0)
        and bt.TaskState = 0
        <if test="passageIds != null and passageIds.size > 0">
            and (bt.passage_Id in
            <foreach collection="passageIds" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
            or bt.passage_Id is null)
        </if>
        <if test="passageIds == null or passageIds.size == 0">
            and bt.passage_Id is null
        </if>
        <if test="splitWarehouseAttrList != null and splitWarehouseAttrList.size() > 0">
            and bt.taskWarehouseFeatureType in
            <foreach collection="splitWarehouseAttrList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        ) h
        where 1 = 1
        <!-- 非抢单模式下-->
        <if test="batchTaskType == null or batchTaskType == 0">
            and (
            <!-- 指派给自己的 -->
            h.groupLevel = 0
            or
            <!-- 只有自己分区且按订单拣货的才可被领取 -->
            (h.groupLevel = 1 and h.pickingType = 1)
            or
            <!-- 无货位的可拆分领取 -->
            h.receiveFlag = 2
            )
        </if>
        order by h.taskState desc, h.receiveFlag,
        <!-- 分区优先 -->
        <if test="orderByType != null and orderByType == 1">
            h.groupLevel, h.batchNo,
        </if>
        <!-- 波次优先 -->
        <if test="orderByType != null and orderByType == 2">
            h.batchNo, h.groupLevel,
        </if>

        <!-- 大件拣货员（按产品拣货优先） -->
        <if test="sorterType != null and sorterType == 1">
            h.pickingType desc,
        </if>
        <!-- 小件拣货员（按订单拣货优先） -->
        <if test="sorterType != null and sorterType == 2">
            h.pickingType asc,
        </if>
        h.batchCreateTime asc, h.createTime asc, h.id asc
    </select>

    <!--    获取团购订单摘果任务列表-->
    <select id="listBatchTaskByGroupBuy"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.GroupBuyBatchTaskDTO">
        select
        bt.id as id,
        bt.BatchTaskNo as batchTaskNo,
        bt.BatchNo as batchNo,
        bt.Sorter_id as sorterId,
        bt.SorterName as sorterName,
        bt.SkuCount as skuCount,
        bt.PackageAmount as packageAmount,
        bt.UnitAmount as unitAmount,
        bt.TaskState as taskState,
        bt.PickingType as pickingType,
        bt.ToLocation_Id as toLocationId,
        bt.ToLocationName as toLocationName,
        bt.BatchTaskType as batchTaskType,
        bt.Passage_Id as passageId,
        bt.PassageName as passageName,
        b.AreaName as areaName,
        b.RouteName as routeName,
        b.OrderSelection as orderSelection
        from batchtask bt inner join batch b on bt.Batch_id = b.id
        where bt.Org_id = #{cityId,jdbcType=INTEGER}
        and bt.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and bt.TaskState in (0, 1)
        and (bt.Sorter_id = #{userId,jdbcType=INTEGER} or bt.Sorter_id is null or bt.Sorter_id = 0)
        <if test="batchTaskType != null">
            and bt.BatchTaskType = #{batchTaskType,jdbcType=TINYINT}
        </if>
        <if test="toLocationName != null and toLocationName != ''">
            and bt.ToLocationName = #{toLocationName,jdbcType=VARCHAR}
        </if>
        <if test="passage != null and passage != ''">
            and bt.PassageName = #{passage,jdbcType=VARCHAR}
        </if>
        <if test="startTime != null">
            AND bt.CreateTime <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            AND bt.CreateTime <![CDATA[ <= ]]> #{endTime}
        </if>
        order by bt.TaskState desc, bt.CreateTime desc, bt.id
    </select>

    <update id="updateBatchTaskByNoList">
        UPDATE batchtask
        SET TaskState=#{taskState}
        WHERE BatchTaskNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <!--auto generated Code-->
    <update id="updateBatch">
        UPDATE batchtask
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="SorterName =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sorter,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="Sorter_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sorterId,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
        <if test="cityId != null">
            and Org_id = #{cityId,jdbcType=INTEGER}
        </if>
    </update>
    <update id="updatePrintCount">
        update batchtask set IsPrinted = 1,PrintTimes=PrintTimes+1,LastUpdateUser=#{operateUser,jdbcType=VARCHAR}
        where BatchTaskNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateTaskState2Complete">
        update batchtask
        <trim prefix="set" suffixOverrides=",">
            TaskState = 2,PickSource=2,CompleteTime = now(),
            <if test="operateUser != null">LastUpdateUser=#{operateUser,jdbcType=VARCHAR},</if>
            <if test="operateUser != null">CompleteUser=#{operateUser,jdbcType=VARCHAR},</if>
            <if test="operateUserId != null">CompleteUserId=#{operateUserId,jdbcType=VARCHAR},</if>
            <if test="locationId != null">ToLocation_Id=#{locationId,jdbcType=BIGINT},</if>
            <if test="locationName != null">ToLocationName = #{locationName,jdbcType=VARCHAR},</if>
            <if test="sorterId != null">Sorter_Id=#{sorterId,jdbcType=INTEGER},</if>
            <if test="sorterName != null">SorterName=#{sorterName,jdbcType=VARCHAR},</if>
        </trim>
        where BatchTaskNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
    </update>

    <update id="updateTaskLocation">
        update batchtask
        <trim prefix="set" suffixOverrides=",">
            <if test="operateUser != null">LastUpdateUser=#{operateUser,jdbcType=VARCHAR},</if>
            <if test="locationId != null">ToLocation_Id=#{locationId,jdbcType=BIGINT},</if>
            <if test="locationName != null">ToLocationName = #{locationName,jdbcType=VARCHAR},</if>
        </trim>
        where SowTaskNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateTaskLocationByTaskId">
        update batchtask
        <trim prefix="set" suffixOverrides=",">
            <if test="operateUser != null">LastUpdateUser=#{operateUser,jdbcType=VARCHAR},</if>
            <if test="locationId != null">ToLocation_Id=#{locationId,jdbcType=BIGINT},</if>
            <if test="locationName != null">ToLocationName = #{locationName,jdbcType=VARCHAR},</if>
            <if test="toPalletNo != null">toPalletNo=#{toPalletNo,jdbcType=VARCHAR},</if>
        </trim>
        where id in
        <foreach collection="batchTaskIds" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>

    <delete id="deleteByBatchNo">
        DELETE from batchtask where BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="findIdByBatchNo" resultType="java.lang.String">
        SELECT id from batchtask bt where BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <include refid="taskStateSql"/>
    </select>

    <select id="findIdByBatchIds" resultType="java.lang.String">
        SELECT id from batchtask bt where Batch_Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findTaskByBatchNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from batchtask bt where BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <include refid="taskStateSql"/>
    </select>

    <select id="findTasksByBatchTaskNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from batchtask bt where bt.BatchTaskNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findTasksByBatchTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from batchtask bt where bt.id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <resultMap id="BaseResultMap2" type="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskCompletePO">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="BatchNo" property="batchNo" jdbcType="VARCHAR"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="State" property="state" jdbcType="TINYINT"/>
        <collection property="items" ofType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO">
            <result column="btid" property="id" jdbcType="VARCHAR"/>
            <result column="BatchTaskNo" property="batchTaskNo" jdbcType="VARCHAR"/>
            <result column="TaskState" property="taskState" jdbcType="TINYINT"/>
        </collection>
    </resultMap>

    <select id="findByBatchTaskNo" resultMap="BaseResultMap2">
        SELECT b.id,
        b.BatchNo,
        b.Warehouse_Id,
        b.State,
        bt.id as btid,
        bt.BatchTaskNo,
        bt.TaskState
        FROM batch b
        INNER JOIN batchtask bt ON b.BatchNo = bt.BatchNo
        WHERE bt.BatchTaskNo = #{batchTaskNo}
    </select>
    <select id="findNoFinishTaskCountByTaskNo" resultType="java.lang.Integer">
        select count(0)
        from batchtask bt1
        inner join batchtask bt2 on bt1.batchno = bt2.batchno
        where bt2.BatchTaskNo = #{batchTaskNo}
        and bt1.taskstate in (0, 1, 2)
    </select>

    <select id="listBatchTaskBySowNo" resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO">
        SELECT
        <include refid="Base_Column_List"/>,
        st.Location_Id as sowLocationId, st.LocationName as sowLocationName
        FROM
        batchtask bt
        inner join sowtask st on bt.SowTask_Id =st.id and bt.Org_Id = st.Org_Id
        WHERE bt.SowTaskNo = #{sowTaskNo} AND bt.Org_Id = #{orgId} AND bt.TaskState in (0,1,2)
    </select>

    <select id="findBySowTaskNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtask bt
        where bt.Org_Id = #{orgId,jdbcType=INTEGER} and bt.SowTaskNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="states!=null and states.size>0">
            and bt.TaskState in
            <foreach collection="states" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
    </select>
    <select id="findBatchTaskInfoById" resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO">
        SELECT
        <include refid="Base_Column_List"/>,
        st.Location_Id as sowLocationId, st.LocationName as sowLocationName,
        so.RefOrderNo, so.SowOrderSequence
        FROM
        batchtask bt
        inner join sowtask st on bt.SowTask_Id =st.id and bt.Org_Id = st.Org_Id
        inner join soworder so on bt.SowTask_Id =so.SowTask_Id and bt.Org_Id = so.Org_Id
        where bt.id = #{batchTaskId,jdbcType=VARCHAR}
    </select>
    <select id="findNoFinishTaskBySowTaskNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtask bt
        where bt.Org_id = #{orgId,jdbcType=INTEGER} and bt.SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR}
        and bt.TaskState in (0,1)
    </select>

    <select id="findPickedCountBySkuIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickedCountDTO">
        SELECT oi.skuId, sum(oii.OverSortCount + oii.MoveCount) as unitTotalCount
        FROM
        outstockorderitem oi
        INNER JOIN outstockorder oo on oo.id = oi.outstockorder_id
        INNER JOIN orderitemtaskinfo oii on oii.RefOrderItem_Id = oi.id
        INNER JOIN batchtaskitem bi on bi.id = oii.BatchTaskItem_Id
        WHERE
        oo.Org_id = #{orgId,jdbcType=INTEGER}
        AND oo.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND oo.State in (1,2,3)
        and oi.Batchtask_Id is not null and oi.Batch_Id is not null
        and bi.TaskState = 2
        and oi.SkuId IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            '${item}'
        </foreach>
        and (oo.CreateAllocation = 0 or oo.CreateAllocation is null)
        group by oi.SkuId
    </select>

    <select id="findPickedDetailBySkuIdForSCM25"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickedDetailDTO">
        SELECT oo.Org_id as orgId, oo.Warehouse_Id as warehouseId,
        bi.skuId as skuId, bi.ProductName as productName, oo.RefOrderNo as refOrderNo, oo.id as outStockOrderId,
        bi.BatchtaskNo as batchTaskNo, oo.BatchNo as batchNo, bi.TaskState as batchTaskState, 0 as isInternal,
        oo.State as outStockOrderState , oi.SpecName as specName, oi.UnitName as unitName, oi.PackageName as
        packageName,
        oi.SpecQuantity as specQuantity, (oii.OverSortCount + oii.MoveCount) as unitTotalCount
        FROM
        outstockorderitem oi
        INNER JOIN outstockorder oo on oo.id = oi.outstockorder_id
        INNER JOIN orderitemtaskinfo oii on oii.RefOrderItem_Id = oi.id
        INNER JOIN batchtaskitem bi on bi.id = oii.BatchTaskItem_Id
        WHERE
        oo.Org_id = #{orgId,jdbcType=INTEGER}
        AND oo.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND oo.State in (1,2,3)
        and oi.Batchtask_Id is not null and oi.Batch_Id is not null
        and bi.TaskState = 2
        and (oo.CreateAllocation = 0 or oo.CreateAllocation is null)
        and oi.SkuId IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            '${item}'
        </foreach>
    </select>

    <select id="findbatchTaskIncludeAllocationById" resultType="java.lang.String">
        select distinct bt.id
        from batchtask bt
        inner join batchtaskitem bti on bt.id = bti.Batchtask_id
        inner join outstockorder oo on bti.RefOrder_id = oo.id
        where oo.CreateAllocation = 1
        <if test="orgId != null">
            and bt.Org_id = #{orgId,jdbcType=INTEGER}
        </if>
        and bt.id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="finTransferSkuId" resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchitem.TransferSkuIdDTO">
        select
        DISTINCT bi.SkuId as skuId,
        oi.SkuId as oldSkuId
        from batchtaskitem bi
        inner JOIN outstockorderitem oi
        on oi.Org_id = bi.Org_id
        and oi.ProductSpecification_Id = bi.ProductSpecification_Id
        and ((oi.Owner_Id is null and bi.Owner_Id is null) or oi.Owner_Id = bi.Owner_Id)
        and ((oi.SecOwner_Id is null and bi.SecOwner_Id is null) or oi.SecOwner_Id = bi.SecOwner_Id)
        and oi.SaleSpecQuantity = bi.SaleSpecQuantity
        INNER JOIN outstockorder os on os.Org_id = bi.Org_id and os.id = oi.Outstockorder_Id
        where
        bi.id in
        <foreach collection="itemIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and os.RefOrderNo in
        <foreach collection="orderNos" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="orgId != null">
            and bi.Org_id = #{orgId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and os.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
    </select>
    <select id="findFinishTaskBySowTaskNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtask bt
        where bt.Org_id = #{orgId,jdbcType=INTEGER} and bt.SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR}
        and bt.TaskState = 2
    </select>

    <update id="updateTaskLocationByIds">
        update batchtask set
        ToLocation_Id = #{toLocationId,jdbcType=BIGINT},
        ToLocationName = #{toLocationName,jdbcType=VARCHAR},
        LastUpdateUser = #{operator,jdbcType=VARCHAR}
        where Org_id = #{orgId,jdbcType=INTEGER}
        and id in
        <foreach collection="batchTaskIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="getToLocationByOrderId" resultType="java.lang.String">
        select ToLocationName
        from batchtask
        where id in (select oi.Batchtask_Id
        from outstockorderitem oi
        where oi.Org_id = #{orgId,jdbcType=INTEGER}
        and oi.Outstockorder_Id = #{orderId,jdbcType=BIGINT}) limit 1
    </select>

    <select id="listBatchTaskRelatedBySowNos"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO">
        SELECT
        <include refid="Base_Column_List"/>,
        st.Location_Id as sowLocationId, st.LocationName as sowLocationName,
        st.State as sowState
        FROM
        batchtask bt
        inner join sowtask st on bt.SowTask_Id =st.id and bt.Org_Id = st.Org_Id
        WHERE bt.Org_Id = #{orgId,jdbcType=INTEGER} AND bt.TaskState in (0,1,2)
        AND bt.SowTaskNo in
        <foreach collection="sowTaskNos" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <update id="deleteSowTaskBySowTaskIds">
        update batchtask set
        SowTask_Id = null,
        SowTaskNo = null
        where SowTask_Id in
        <foreach collection="sowTaskIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectBatchTaskBySowTaskIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtask bt
        where SowTask_Id in
        <foreach collection="sowTaskIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listBatchTaskLocationUseCount"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskLocationUseCountDTO">
        select h.SkuId as productSkuId,
        h.LocationId as locationId,
        h.ProductionDate as productionDate,
        h.BatchTime as batchTime,
        sum(h.sortCount) as useCount
        from (SELECT bti.id,
        bti.SkuId,
        (oii.OverSortCount + oii.MoveCount) as sortCount,
        IFNULL(bt.ToLocation_Id, oi.LocationId) as LocationId,
        bti.ProductionDate,
        bti.BatchTime,
        oi.id as orderItemId
        FROM outstockorder o
        inner join outstockorderitem oi on o.id = oi.OutStockOrder_Id
        inner join orderitemtaskinfo oii on oii.RefOrderItem_Id = oi.id
        inner join batchtaskitem bti on oii.BatchTaskItem_Id = bti.id
        inner join batchtask bt on bti.Batchtask_id = bt.id
        WHERE o.Org_id = #{cityId,jdbcType=INTEGER}
        and o.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and o.State in (1, 2, 3)
        and bti.TaskState = 2) h
        GROUP by h.SkuId, h.LocationId, h.ProductionDate, h.BatchTime
    </select>

    <select id="findOrderRelatedBatchTaskLocation"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO">
        select distinct bt.id as id,
        bt.BatchTaskNo as batchTaskNo,
        bt.ToLocation_Id as toLocationId,
        bt.ToLocationName as toLocationName
        from batchtask bt
        inner join batchtaskitem bti on bt.id = bti.Batchtask_id
        and bt.org_id = bti.org_id
        where bti.RefOrderNo = #{orderNo,jdbcType=VARCHAR}
        and bt.PickingType = 1
        and bt.TaskState = 2
        and bt.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and bt.org_id = #{orgId,jdbcType=INTEGER}
    </select>

    <delete id="deleteByBatchTaskId">
        DELETE from batchtask where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <update id="updateBatchTaskStatistics" parameterType="java.util.List">
        UPDATE batchtask
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="OrderCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.orderCount != null ">
                        when id=#{item.id} then #{item.orderCount,jdbcType=INTEGER}
                    </if>
                    <if test="item.orderCount == null ">
                        when id=#{item.id} then OrderCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="SkuCount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.skuCount != null ">
                        when id=#{item.id} then #{item.skuCount,jdbcType=INTEGER}
                    </if>
                    <if test="item.skuCount == null ">
                        when id=#{item.id} then SkuCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="PackageAmount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.packageAmount != null ">
                        when id=#{item.id} then #{item.packageAmount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.packageAmount == null ">
                        when id=#{item.id} then PackageAmount
                    </if>
                </foreach>
            </trim>
            <trim prefix="UnitAmount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.unitAmount != null ">
                        when id=#{item.id} then #{item.unitAmount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.unitAmount == null ">
                        when id=#{item.id} then UnitAmount
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="getBatchTaskStateCount"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskStateCountDTO">
        SELECT TaskState as taskState, count(*) as count
        FROM batchtask
        where Org_Id = #{cityId,jdbcType=INTEGER}
        and warehouse_id = #{warehouseId,jdbcType=INTEGER}
        and TaskState in (0
        , 1)
        and Sorter_Id = #{userId,jdbcType=INTEGER}
        group by TaskState
    </select>

    <select id="getSortingBatchTaskCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM batchtask
        where Org_Id = #{cityId,jdbcType=INTEGER}
        and warehouse_id = #{warehouseId,jdbcType=INTEGER}
        and TaskState = 1
        and Sorter_Id = #{userId,jdbcType=INTEGER}
    </select>

    <select id="getSortingBatchTaskMaxDays" resultType="java.lang.Integer">
        SELECT datediff(now(), StartTime)
        FROM batchtask
        where Org_Id = #{cityId,jdbcType=INTEGER}
        and warehouse_id = #{warehouseId,jdbcType=INTEGER}
        and TaskState = 1
        and Sorter_Id = #{userId,jdbcType=INTEGER}
        and StartTime is not null
        order by datediff(now(), StartTime) desc limit 1
    </select>


    <update id="updateBatchTaskSowTaskById">
        UPDATE batchtask
        set SowTask_Id = #{sowTaskId,jdbcType=BIGINT},
        SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR}
        WHERE id in
        <foreach collection="batchTaskIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>


    <select id="findBatchTaskSecondSortList"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskSortPO">
        SELECT
        bt.id as id,
        bt.BatchTaskNo as batchTaskNo,
        bt.BatchTaskName as batchTaskName,
        bt.BatchNo as batchNo,
        bt.Sorter_id as sorterId,
        bt.SkuCount as skuCount,
        bt.PackageAmount as packageAmount,
        bt.UnitAmount as unitAmount,
        bt.TaskState as taskState,
        bt.PickingType as pickingType,
        bt.PickingGroupStrategy as pickingGroupStrategy,
        bt.LocationName as locationName,
        bt.ToLocation_Id as toLocationId,
        bt.ToLocationName as toLocationName,
        bt.CategoryName as categoryName,
        bt.CreateTime as createTime,
        b.AreaName as areaName,
        b.RouteName as routeName,
        b.OrderSelection as orderSelection,
        bt.OrderCount as orderCount,
        b.CreateTime as batchCreateTime,
        bt.SowTask_Id as sowTaskId,
        b.SowType as sowType
        FROM
        batch b
        INNER JOIN batchtask bt ON bt.Batch_id = b.id
        AND bt.TaskState = 2
        WHERE
        b.SowType = 2
        <if test="cityId != null">
            and bt.Org_id = #{cityId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and bt.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="userId != null">
            and bt.Sorter_id = #{userId,jdbcType=INTEGER}
        </if>
        order by batchCreateTime desc, createTime desc, id asc
    </select>
    <select id="listToLocationNameById"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO">
        select ToLocationName as toLocationName,id from batchtask
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and Org_id=#{orgId}
    </select>

    <select id="listBatchTask" resultMap="ItemBaseResultMap">
        select
        <include refid="Base_Column_List"/>
        <if test="queryCondition != null and queryCondition.contains(1)">
            ,oo.RefOrderNo
        </if>
        <!--  查询分拣任务项   -->
        <if test="isQueryItem != null and isQueryItem == true">
            ,<include refid="Item_Alias_Column_List"/>,oi.id as item_orderItemId
        </if>
        from batchtaskitem bti
        INNER JOIN batchtask bt on bt.id = bti.Batchtask_id
        INNER JOIN batch b on bt.batch_id =b.id
        <if test="queryCondition != null and queryCondition.contains(1)">
            inner JOIN outstockorderitem oi on oi.BatchTaskItem_Id = bti.id
            inner JOIN outstockorder oo on oo.id = oi.Outstockorder_Id
        </if>
        where
        bt.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="cityId != null">
            and bti.Org_id = #{cityId,jdbcType=INTEGER}
        </if>
        <if test="batchTaskNo != null and batchTaskNo != ''">
            and bti.BatchtaskNo = #{batchTaskNo,jdbcType=VARCHAR}
        </if>
        <if test="batchNo != null and batchNo != ''">
            and bt.BatchNo = #{batchNo,jdbcType=VARCHAR}
        </if>
        <if test="queryCondition != null and queryCondition.contains(1)">
            <if test="refOrderNoList != null and refOrderNoList.size() > 0">
                and oo.RefOrderNo in
                <foreach collection="refOrderNoList" item="refOrderNo" open="(" separator="," close=")">
                    #{refOrderNo,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="refOrderIdList != null and refOrderIdList.size() > 0">
                and oo.id in
                <foreach collection="refOrderIdList" item="refOrderId" open="(" separator="," close=")">
                    #{refOrderId,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>
        <if test="batchTaskId != null and batchTaskId != ''">
            and bt.id = #{batchTaskId,jdbcType=VARCHAR}
        </if>
    </select>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO">
        update batchtask
        <set>
            <if test="batchId != null">
                Batch_id = #{batchId,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null">
                BatchNo = #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="batchTaskNo != null">
                BatchTaskNo = #{batchTaskNo,jdbcType=VARCHAR},
            </if>
            <if test="sorter != null">
                SorterName = #{sorter,jdbcType=VARCHAR},
            </if>
            <if test="sorterId != null">
                Sorter_Id = #{sorterId,jdbcType=INTEGER},
            </if>
            <if test="orderAmount != null">
                OrderAmount = #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="orderCount != null">
                OrderCount = #{orderCount,jdbcType=INTEGER},
            </if>
            <if test="skuCount != null">
                SkuCount = #{skuCount,jdbcType=INTEGER},
            </if>
            <if test="packageAmount != null">
                PackageAmount = #{packageAmount,jdbcType=DECIMAL},
            </if>
            <if test="unitAmount != null">
                UnitAmount = #{unitAmount,jdbcType=DECIMAL},
            </if>
            <if test="taskState != null">
                TaskState = #{taskState,jdbcType=TINYINT},
            </if>
            <if test="locationId != null">
                Location_Id = #{locationId,jdbcType=BIGINT},
            </if>
            <if test="locationName != null">
                LocationName = #{locationName,jdbcType=VARCHAR},
            </if>
            <if test="locationCategory != null">
                LocationCategory = #{locationCategory,jdbcType=TINYINT},
            </if>
            <if test="categoryName != null">
                CategoryName = #{categoryName,jdbcType=VARCHAR},
            </if>
            <if test="pickingType != null">
                PickingType = #{pickingType,jdbcType=TINYINT},
            </if>
            <if test="pickingGroupStrategy != null">
                PickingGroupStrategy = #{pickingGroupStrategy,jdbcType=TINYINT},
            </if>
            <if test="isPrinted != null">
                IsPrinted = #{isPrinted,jdbcType=BIT},
            </if>
            <if test="printTimes != null">
                PrintTimes = #{printTimes,jdbcType=INTEGER},
            </if>
            <if test="toLocationId != null">
                ToLocation_Id = #{toLocationId,jdbcType=BIGINT},
            </if>
            <if test="toLocationName != null">
                ToLocationName = #{toLocationName,jdbcType=VARCHAR},
            </if>
            <if test="batchTaskName != null">
                BatchTaskName = #{batchTaskName,jdbcType=VARCHAR},
            </if>
            <if test="completeUser != null">
                CompleteUser = #{completeUser,jdbcType=VARCHAR},
            </if>
            <if test="completeUserId != null">
                CompleteUserId = #{completeUserId,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="sowTaskId != null">
                SowTask_Id = #{sowTaskId,jdbcType=BIGINT},
            </if>
            <if test="sowTaskNo != null">
                SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR},
            </if>
            <if test="sortGroupId != null">
                SortGroupId = #{sortGroupId,jdbcType=BIGINT},
            </if>
            <if test="completeTime != null">
                CompleteTime = #{completeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="needPick != null">
                IsNeedPick = #{needPick,jdbcType=TINYINT},
            </if>
            <if test="startTime != null">
                StartTime = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="batchTaskType != null">
                BatchTaskType = #{batchTaskType,jdbcType=TINYINT},
            </if>
            <if test="passageId != null">
                Passage_Id = #{passageId,jdbcType=BIGINT},
            </if>
            <if test="passageName != null">
                PassageName = #{passageName,jdbcType=VARCHAR},
            </if>
            <if test="pickPattern != null">
                pickPattern = #{pickPattern,jdbcType=TINYINT},
            </if>
            <if test="kindOfPicking != null">
                KindOfPicking = #{kindOfPicking,jdbcType=TINYINT},
            </if>
            <if test="shiftOfPerformance != null">
                shiftOfPerformance = #{shiftOfPerformance,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateBatchTaskContainer">
        UPDATE batchtask
        SET Remark = #{remark,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>
    <select id="findBatchTaskByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_1"/>
        from batchtask bt
        where bt.id in
        <foreach collection="taskIds" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="findAssignOrderSorter"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.bo.QueryAssignOrderSorterResultBO">
        select t1.BatchTask_Id as taskID,o1.id as orderId,max(bt.Sorter_Id) as sorterId
        from outstockorder o1
        inner join orderitemtaskinfo t1 on o1.id = t1.RefOrder_Id
        inner join outstockorder o on o.warehouse_id = o1.warehouse_id and o1.AddressId = o.AddressId and o.id != o1.id
        inner join orderitemtaskinfo t on o.id = t.RefOrder_Id
        inner join batchtask bt on bt.id = t.BatchTask_Id
        where o1.id in
        <foreach collection="lstOrderId" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and bt.TaskState in (0,1)
        and bt.Sorter_Id is not null
        group by taskID,orderId
    </select>
    <select id="findNotFinishedBatchTaskBySorterAndWarehouseId" resultType="java.lang.Integer">
        select distinct Sorter_Id
        from batchtask b
        where b.warehouse_id = #{warehouseId,jdbcType=INTEGER}
        and b.TaskState in (0,1) and b.Sorter_Id is not null and b.Sorter_Id in
        <foreach collection="lstSorterId" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        <if test="lstTaskNo != null and lstTaskNo.size() > 0">
            and b.BatchTaskNo not in
            <foreach collection="lstTaskNo" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="findNoSorterBatchTaskByWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_1"/>
        from batchtask bt
        where bt.warehouse_id = #{warehouseId,jdbcType=INTEGER}
        and bt.TaskState = 0
        and bt.taskWarehouseFeatureType = 2
        and bt.Sorter_Id is null
    </select>
    <select id="listBatchTaskItem" resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO">
        SELECT
        <include refid="Base_Column_List"/>,
        st.Location_Id as sowLocationId, st.LocationName as sowLocationName,
        st.State as sowState
        FROM
        batchtask bt
        inner join sowtask st on bt.SowTask_Id =st.id and bt.Org_Id = st.Org_Id
        inner join batch b on b.BatchNo = bt.BatchNo
        <if test="query.productInfo != null and query.productInfo != ''">
            inner join sowTaskItem sti on st.Id = sti.sowTask_Id
        </if>
        WHERE bt.Org_Id = #{query.orgId,jdbcType=INTEGER} and bt.Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
        <if test="sowTaskState != null and sowTaskState.size() > 0">
            and st.State in
            <foreach collection="sowTaskState" item="item" open="(" separator="," close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="batchState != null and batchState.size() > 0">
            and b.State in
            <foreach collection="batchState" item="item" open="(" separator="," close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="query.sowTaskNo != null and query.sowTaskNo != '' ">
            and bt.SowTaskNo = #{query.sowTaskNo,jdbcType=VARCHAR}
        </if>
        <if test="query.sowTaskName != null and query.sowTaskName != '' ">
            and b.BatchName = #{query.sowTaskName,jdbcType=VARCHAR}
        </if>
        <if test="query.productName != null and query.productName != ''">
            and sti.ProductName like concat(#{query.productName,jdbcType=VARCHAR},'%')
        </if>
        <if test="query.skuIds != null and query.skuIds.size() > 0">
            and sti.ProductSku_Id in
            <foreach collection="query.skuIds" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        order by st.createtime desc
    </select>

    <update id="updateToClearRobotInfo"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO">
        update
        set Sorter_Id = #{sorterId,jdbcType=INTEGER},
        SorterName = #{sorter,jdbcType=VARCHAR},
        pickPattern = #{pickPattern,jdbcType=TINYINT}
        where id = #{id,jdbcType=VARCHAR}
    </update>


    <select id="findSowPickedCountBySkuIdForSCM25"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickedCountDTO">
        SELECT oo.Org_id as orgId, oo.Warehouse_Id as warehouseId,
        bi.skuId as skuId, bi.ProductName as productName, oo.RefOrderNo as refOrderNo, oo.id as outStockOrderId,
        bi.BatchtaskNo as batchTaskNo, oo.BatchNo as batchNo, bi.TaskState as batchTaskState, 0 as isInternal,
        oo.State as outStockOrderState , oi.SpecName as specName, oi.UnitName as unitName, oi.PackageName as
        packageName,
        oi.SpecQuantity as specQuantity, (oii.OverSortCount + oii.MoveCount) as unitTotalCount
        FROM
        outstockorderitem oi
        INNER JOIN outstockorder oo on oo.id = oi.outstockorder_id
        INNER JOIN orderitemtaskinfo oii on oii.RefOrderItem_Id = oi.id
        INNER JOIN batchtaskitem bi on bi.id = oii.BatchTaskItem_Id
        inner join batchtask b on b.id = bi.Batchtask_id
        inner join sowtask s on b.SowTask_Id = s.Id
        WHERE
        oo.Org_id = #{orgId}
        AND oo.Warehouse_Id = #{warehouseId}
        AND oo.State in (1,2,3)
        and oi.Batchtask_Id is not null and oi.Batch_Id is not null
        and bi.TaskState = 2
        and s.State != 2
    </select>

    <select id="findTaskByBatchIdsAndState" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from batchtask bt where BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="states !=null and states.size>0">
            and TaskState in
            <foreach collection="states" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
    </select>

    <update id="updatePalletById">
        update batchtask
        set toPalletNo = #{toPalletNo,jdbcType=VARCHAR} where id = #{batchTaskId,jdbcType=VARCHAR}
    </update>

    <update id="updateBatchTaskByIdList">
        UPDATE batchtask
        <set>
            <if test="taskState != null">
                TaskState = #{taskState},
            </if>
            <if test="startTime != null">
                StartTime = #{startTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateKindOfPickingByIdList">
        UPDATE batchtask
        SET KindOfPicking = #{kindOfPicking}
        WHERE id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectMaxTaskSequenceByBatchNo" resultType="java.lang.Integer">
        select MAX(TaskAppendSequence)
        from batchTask
        where batchno = #{batchNo}
        and org_Id = #{orgId}
    </select>

    <select id="countRobotTaskInBatch" resultType="java.lang.Integer">
        SELECT
        count(1)
        from batchtask bt where BatchNo = #{batchNo,jdbcType=VARCHAR}
        and PickPattern != 2
        and org_Id = #{orgId, jdbcType=INTEGER}
    </select>


    <select id="findBatchTaskOneUserOrderIds" resultType="java.lang.Long">
        select distinct c.Id
        from batchtask a
        inner join orderitemtaskinfo b
        on a.id = b.BatchTask_Id
        inner join outstockorder c
        on b.reforder_id = c.Id
        where a.Id = #{batchTaskId, jdbcType=VARCHAR}
        and c.AddressId = #{addressId, jdbcType=INTEGER}
        and a.Org_Id = #{orgId, jdbcType=INTEGER}
    </select>

    <select id="findPickedDetailByCondition"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickedDetailDTO">
        SELECT oo.Org_id as orgId, oo.Warehouse_Id as warehouseId,
        bi.skuId as skuId, bi.ProductName as productName, oo.RefOrderNo as refOrderNo, oo.id as outStockOrderId,
        bi.BatchtaskNo as batchTaskNo, oo.BatchNo as batchNo, bi.TaskState as batchTaskState, oo.CreateAllocation as
        isInternal,
        oo.State as outStockOrderState , oi.SpecName as specName, oi.UnitName as unitName,
        oi.PackageName aspackageName,oi.SpecQuantity as specQuantity, (oii.OverSortCount + oii.MoveCount) as
        unitTotalCount,
        IFNULL(
        CASE
        WHEN s.Id IS NULL THEN
        IFNULL(b.ToLocation_Id, oi.LocationId)
        WHEN s.State = 2 THEN
        oi.LocationId
        ELSE
        s.Location_Id
        END,
        oi.LocationId
        ) as locationId,
        IFNULL(
        CASE
        WHEN s.Id IS NULL THEN
        IFNULL(b.ToLocationName, oi.LocationName)
        WHEN s.State = 2 THEN
        oi.LocationName
        ELSE
        s.LocationName
        END,
        oi.LocationName
        ) as locationName
        FROM outstockorderitem oi
        INNER JOIN outstockorder oo on oo.id = oi.outstockorder_id
        INNER JOIN orderitemtaskinfo oii on oii.RefOrderItem_Id = oi.id
        INNER JOIN batchtaskitem bi on bi.id = oii.BatchTaskItem_Id
        INNER JOIN batchtask b on b.id = bi.Batchtask_id
        LEFT JOIN sowtask s on b.SowTask_Id = s.Id
        WHERE
        oo.Org_id = #{orgId,jdbcType=INTEGER}
        AND oo.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND oo.State in (1,2,3)
        and oi.Batchtask_Id is not null and oi.Batch_Id is not null
        and bi.TaskState = 2
        <if test="queryAllOrder == null or queryAllOrder == false">
            and (oo.CreateAllocation = 0 or oo.CreateAllocation is null)
        </if>
        <if test="skuIds != null and skuIds.size > 0">
            and oi.SkuId IN
            <foreach collection="skuIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getSortGroupLatestBatchTask"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO">
        select id, completeTime from batchtask
        where
        Org_id = #{orgId,jdbcType=INTEGER}
        and warehouse_id = #{warehouseId,jdbcType=INTEGER}
        and taskstate = #{taskState, jdbcType=TINYINT}
        and sortGroupId = #{sortGroupId,jdbcType=BIGINT}
        order by completeTime desc limit 0,1
    </select>

    <select id="findPickingEndingLocationInfoByBatchTaskNos"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.bo.pickingEndingLocationInfoBO">
        select bt.Warehouse_Id warehouseId,
        bt.BatchTaskNo batchTaskNo,
        IFNULL(bt.ToLocation_Id, s.Location_Id) as locationId,
        IFNULL(bt.ToLocationName, s.LocationName) as locationName
        from batchtask bt
        left join sowtask s on s.SowTaskNo = bt.SowTaskNo
        where bt.BatchTaskNo in
        <foreach collection="batchTaskNos" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findBatchTaskListByCon" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchtask bt
        where
        bt.Warehouse_Id in
        <foreach collection="queryPO.warehouseIds" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        <if test="queryPO.taskStateList != null">
            AND bt.TaskState in
            <foreach collection="queryPO.taskStateList" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="queryPO.startTime!= null and queryPO.endTime!= ''">
            AND bt.CompleteTime between #{queryPO.startTime} and #{queryPO.endTime}
        </if>
    </select>
</mapper>

