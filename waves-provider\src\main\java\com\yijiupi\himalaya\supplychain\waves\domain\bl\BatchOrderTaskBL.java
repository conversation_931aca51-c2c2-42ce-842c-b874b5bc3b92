package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import static com.yijiupi.himalaya.supplychain.waves.enums.BatchStateEnum.*;
import static com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum.分拣中;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.Pager;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.annotation.BatchDistributeLock;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.distributedlock.exceptions.HasLockedException;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.algorithm.dto.ResultWithMetrics;
import com.yijiupi.himalaya.supplychain.algorithm.dto.pickingtaskitemsort.PickingTaskItemDTO;
import com.yijiupi.himalaya.supplychain.algorithm.enums.AlgorithmExecutionStateEnum;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryManageService;
import com.yijiupi.himalaya.supplychain.constants.WarehouseConfigConstants;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.*;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.ProductOwnerInfoDTO;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.SaleInventoryInfoDTO;
import com.yijiupi.himalaya.supplychain.enums.DefaultTypeEnum;
import com.yijiupi.himalaya.supplychain.enums.InventoryChangeTypeEnum;
import com.yijiupi.himalaya.supplychain.enums.ProcessOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.enums.ProcessRuleOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryQueryService;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckResultDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.OutStockAlarmEnum;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IInStockConfigService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderBusinessType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.SourceType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockOrderStatisticsService;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSpecificationDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductCodeInfoQueryService;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductInfoSpecificationSerivce;
import com.yijiupi.himalaya.supplychain.service.IDefaultLocationConfigService;
import com.yijiupi.himalaya.supplychain.service.IOrderProcessRuleConfigService;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserService;
import com.yijiupi.himalaya.supplychain.util.SecOwnerIdComparator;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductImageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoSearchDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductFeatureEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortGroupFlagEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortGroupRfidTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SowTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.ProductRelevanceConfigQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.*;
import com.yijiupi.himalaya.supplychain.waves.batch.ISowQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchFinishedBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.*;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.OrderItemTaskInfoDetailLackHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderconstraint.OrderConstraintCheckBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderpallet.OrderLocationPalletBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation.RecommendOutLocationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder.OutStockOrderStateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.perform.TaskPerformanceCalculateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.wcs.PassageBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.TaskPerformanceCalculateBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.*;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.model.OutStockOrderItemQuerySO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.SaaSOrderSelectionEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.mq.OrderToLocationSyncMQ;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.WaveNoItemModel;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.WaveNoModel;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.*;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.*;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderSaveDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.*;
import com.yijiupi.himalaya.supplychain.waves.dto.trace.OrderTraceDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.manager.OmsSaleInventoryManager;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderSearchSO;
import com.yijiupi.himalaya.supplychain.waves.util.*;
import com.yijiupi.himalaya.supplychain.waves.utils.DateUtil;
import com.yijiupi.himalaya.supplychain.waves.utils.StreamUtils;
import com.yijiupi.himalaya.supplychain.wcs.service.task.IDPSTaskService;
import com.yijiupi.himalaya.utils.QuantityShareUtils;
import com.yijiupi.supplychain.serviceutils.constant.AdminUserAuthRoleConstant;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;
import com.yijiupi.supplychain.serviceutils.constant.redis.RedisConstant;

/**
 * 波次任务
 *
 * <AUTHOR> 2018/3/16
 */
@Service
public class BatchOrderTaskBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(BatchOrderTaskBL.class);
    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Reference
    private IDefaultLocationConfigService iDefaultLocationConfigService;
    @Autowired
    private OutStockOrderBL outStockOrderBL;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private OutStockOrderItemDetailCommMapper outStockOrderItemDetailCommMapper;
    @Autowired
    private OrderTraceBL orderTraceBL;
    @Reference
    private LocationAreaService locationAreaService;
    @Reference
    private IBatchInventoryManageService iBatchInventoryManageService;
    @Autowired
    private PackageOrderItemBL packageOrderItemBL;
    @Autowired
    private SowTaskMapper sowTaskMapper;
    @Autowired
    private SowOrderMapper sowOrderMapper;
    @Autowired
    private SowManagerBL sowManagerBL;
    @Autowired
    private BatchTaskQueryBL batchTaskQueryBL;
    @Autowired
    private BatchInventoryTransferBL batchInventoryTransferBL;
    @Autowired
    private BatchTaskSortConvertor batchTaskSortConvertor;
    @Autowired
    private TaskPerformanceCalculateBL taskPerformanceCalculateBL;

    @Reference
    private ISowQueryService iSowQueryService;
    @Reference
    private IProductSkuService iProductSkuService;
    @Reference
    private ISortGroupService iSortGroupService;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Reference
    private IWarehouseInventoryQueryService iWarehouseInventoryQueryService;
    @Reference
    private IDPSTaskService idpsTaskService;
    @Reference
    private IOutStockOrderStatisticsService iOutStockOrderStatisticsService;
    @Reference
    private IAdminUserService iAdminUserService;

    @Autowired
    private BatchOrderBL batchOrderBL;

    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Reference
    private IProductRelationGroupService iProductRelationGroupService;

    @Reference
    private IPassageService iPassageService;

    @Reference
    private IProductControlConfigService iProductControlConfigService;
    @Autowired
    private BatchTaskItemContainerMapper batchTaskItemContainerMapper;
    @Autowired
    private OrderToLocationSyncMQ orderToLocationSyncMQ;
    @Autowired
    private PackageOrderItemMapper packageOrderItemMapper;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private SowTaskItemMapper sowTaskItemMapper;
    @Autowired
    private OrderItemTaskInfoDetailMapper orderItemTaskInfoDetailMapper;
    @Autowired
    private RedisUtil<Integer> redisUtil;
    @Autowired
    private BatchTaskItemPickCompleteDetailDTOConvertor batchTaskItemPickCompleteDetailDTOConvertor;
    @Autowired
    private BatchTaskItemPackageReviewDetailDTOConvertor batchTaskItemPackageReviewDetailDTOConvertor;
    @Autowired
    private RecommendOutLocationBL recommendOutLocationBL;
    @Autowired
    private BatchFinishedBL batchFinishedBL;
    @Autowired
    private BatchTaskFinishValidateBL batchTaskFinishValidateBL;
    @Resource
    private OrderFeatureBL orderFeatureBL;
    @Resource
    private PassageBL passageBL;
    @Resource
    private OutStockOrderStateBL outStockOrderStateBL;
    @Resource
    private OmsSaleInventoryManager omsSaleInventoryManager;
    @Resource
    private BatchTaskHelper batchTaskHelper;
    @Autowired
    private GlobalCache globalCache;
    @Resource
    private OrderConstraintCheckBL orderConstraintCheckBL;
    @Autowired
    private OrderCenterBL orderCenterBL;
    @Autowired
    private CompletePickNotifyDTOConvertor completePickNotifyDTOConvertor;
    @Autowired
    private BatchTaskItemFinishValidateBL batchTaskItemFinishValidateBL;
    @Autowired
    private BatchTaskSortItemDTOConvertor batchTaskSortItemDTOConvertor;
    @Autowired
    private BatchTaskItemCompleteConvertor batchTaskItemCompleteConvertor;
    @Autowired
    private OrderLocationPalletBL orderLocationPalletBL;

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;
    @Reference
    private IOrderProcessRuleConfigService iOrderProcessRuleConfigService;
    @Reference
    private ILocationService iLocationService;
    @Reference
    private IProductInfoSpecificationSerivce iProductInfoSpecificationSerivce;
    @Reference
    private IAdminUserQueryService iAdminUserQueryService;
    @Reference
    private IVariableValueService iVariableValueService;
    @Reference
    private IInStockConfigService inStockConfigService;
    @Reference
    private IProductCodeInfoQueryService iProductCodeInfoQueryService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Reference
    private OwnerService ownerService;
    @Reference
    private IStockAgeStrategyService iStockAgeStrategyService;
    @Reference
    private IProductLocationService iProductLocationService;
    @Autowired
    private WavesStrategyBOConvertor wavesStrategyBOConvertor;
    @Reference
    private ISortGroupRfidService iSortGroupRfidService;
    @Resource
    private BatchTaskAssignHelper batchTaskAssignHelper;
    @Autowired
    private WarehouseAllocationTypeManageBL warehouseAllocationTypeManageBL;

    /**
     * 拣货任务按分区排序配置
     */
    private static final String BATCHTASK_GROUP_SORT = "batchTaskGroupSort";
    private static final String PICK_SAMETIME = "pickAndSowSameTime:";
    public static final String ROBOT_NAME = "机器人";
    @Autowired
    private BatchTaskChangeNotifyBL batchTaskChangeNotifyBL;
    @Autowired
    private PickingTaskItemSortingBL pickingTaskItemSortingBL;

    /**
     * 查出波次任务列表
     */
    public PageList<BatchTaskDTO> findBatchOrderTaskList(BatchTaskQueryDTO batchTaskQueryDTO) {
        // // 兼容wms客户端查询, 转换为按分仓属性WarehouseAllocationType查询
        // List<Byte> taskWarehouseFeatureTypes = batchTaskQueryDTO.getTaskWarehouseFeatureTypes();
        // if (CollectionUtils.isEmpty(taskWarehouseFeatureTypes)) {
        // batchTaskQueryDTO.setWarehouseAllocationType(null);
        // } else {
        // batchTaskQueryDTO.setWarehouseAllocationType(Integer.valueOf(taskWarehouseFeatureTypes.get(0)));
        // }

        PageResult<BatchTaskPO> batchTaskPOPageResult = batchTaskMapper.findList(batchTaskQueryDTO,
            batchTaskQueryDTO.getCurrentPage(), batchTaskQueryDTO.getPageSize());
        PageList<BatchTaskPO> batchOrderTaskPOPageList = batchTaskPOPageResult.toPageList();
        List<BatchTaskDTO> batchTaskDTOS =
            WaveOrderTaskConvertor.batchOrderTaskPOS2BatchOrderTaskDTOS(batchOrderTaskPOPageList.getDataList());
        // 根据分区id查询分区标签(仅限分区拣货，即电子标签拣货)
        fillSortGroupRfidInfo(batchTaskDTOS);
        PageList<BatchTaskDTO> batchOrderDTOPageList = new PageList<>();
        batchOrderDTOPageList.setDataList(batchTaskDTOS);
        batchOrderDTOPageList.setPager(batchOrderTaskPOPageList.getPager());
        return batchOrderDTOPageList;
    }

    /**
     * 查询波次任务详情
     */
    public PageList<BatchTaskItemDTO> findBatchTaskItemList(BatchTaskItemQueryDTO batchTaskItemQueryDTO) {
        LOGGER.info("查询波次任务详情: {}", JSON.toJSONString(batchTaskItemQueryDTO, SerializerFeature.WriteMapNullValue));
        PageResult<BatchTaskItemDTO> lstItemDTO = batchTaskItemMapper.findBatchTaskItemDTOList(batchTaskItemQueryDTO,
            batchTaskItemQueryDTO.getCurrentPage(), batchTaskItemQueryDTO.getPageSize());
        PageList<BatchTaskItemDTO> dtoPageList = lstItemDTO.toPageList();
        // 关联出库单的设置出库单的信息
        initBatchTaskItemOrderInfo(dtoPageList.getDataList());
        // 是否需要排序
        if (CollectionUtils.isNotEmpty(dtoPageList.getDataList())
            && Objects.equals(batchTaskItemQueryDTO.getSortFlag(), true)) {
            List<BatchTaskItemDTO> lstItemOrderByIndex = processTaskItemIndex(lstItemDTO);
            // 获取控货策略名称
            List<Long> configIds = lstItemOrderByIndex.stream().map(BatchTaskItemDTO::getControlConfigId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, String> configNameMap = getControlConfigNameMap(configIds);
            if (configNameMap != null) {
                lstItemOrderByIndex.forEach(item -> {
                    if (item.getControlConfigId() != null) {
                        item.setControlConfigName(configNameMap.get(item.getControlConfigId()));
                    }
                });
            }
            Integer orgId = lstItemOrderByIndex.get(0).getOrgId();
            Integer warehouseId = lstItemOrderByIndex.get(0).getWarehouseId();
            Set<Long> skuList =
                lstItemOrderByIndex.stream().map(BatchTaskItemDTO::getSkuId).collect(Collectors.toSet());
            Map<Long, ProductCodeDTO> packageAndUnitCode = iProductSkuService.getPackageAndUnitCode(skuList, orgId);
            List<ProductSkuDTO> productCharacteristic =
                iProductSkuService.getProductCharacteristic(skuList, warehouseId);
            if (!org.springframework.util.ObjectUtils.isEmpty(productCharacteristic)) {
                List<ProductSkuDTO> codeNotSkuList = new ArrayList<>();
                for (BatchTaskItemDTO itemOrderByIndex : lstItemOrderByIndex) {
                    // 判断sku 特征为大件小件
                    ProductSkuDTO productSkuDTO = productCharacteristic.stream()
                        .filter(p -> p.getProductSkuId().compareTo(itemOrderByIndex.getSkuId()) == 0).findFirst()
                        .orElse(new ProductSkuDTO());
                    if (org.springframework.util.ObjectUtils.isEmpty(productSkuDTO.getProductSkuId())) {
                        continue;
                    }
                    ProductCodeDTO productCodeDTO =
                        Optional.ofNullable(packageAndUnitCode.get(productSkuDTO.getProductSkuId()))
                            .orElse(new ProductCodeDTO());
                    if (Objects.equals(productSkuDTO.getProductFeature(), ProductFeatureEnum.小件.getType())) {
                        if (CollectionUtils.isNotEmpty(productCodeDTO.getUnitCode())) {
                            itemOrderByIndex.setBarCode(CollectionUtils.join(productCodeDTO.getUnitCode(), ","));
                        } else {
                            ProductSkuDTO codeNotDto = new ProductSkuDTO();
                            codeNotDto.setProductSkuId(productSkuDTO.getProductSkuId());
                            codeNotDto.setCityId(orgId);
                            codeNotSkuList.add(codeNotDto);
                        }
                    } else {
                        if (CollectionUtils.isNotEmpty(productCodeDTO.getPackageCode())) {
                            itemOrderByIndex.setBarCode(CollectionUtils.join(productCodeDTO.getPackageCode(), ","));
                        } else {
                            ProductSkuDTO codeNotDto = new ProductSkuDTO();
                            codeNotDto.setProductSkuId(productSkuDTO.getProductSkuId());
                            codeNotDto.setCityId(orgId);
                            codeNotSkuList.add(codeNotDto);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(codeNotSkuList)) {
                    // 查询sku条码信息
                    List<ProductSkuDTO> productSkuDTOList = iProductSkuService.listSkuCode(codeNotSkuList);
                    for (ProductSkuDTO productSkuDTO : productSkuDTOList) {
                        BatchTaskItemDTO batchTaskItemDTO = lstItemOrderByIndex.stream()
                            .filter(l -> l.getSkuId().compareTo(productSkuDTO.getProductSkuId()) == 0).findFirst()
                            .orElse(new BatchTaskItemDTO());
                        batchTaskItemDTO.setBarCode(productSkuDTO.getBarCode());
                    }
                }
            }
            dtoPageList.setDataList(lstItemOrderByIndex);
        }
        return dtoPageList;
    }

    /**
     * 关联出库单的设置出库单的信息
     */
    private void initBatchTaskItemOrderInfo(List<BatchTaskItemDTO> batchTaskItemDTOList) {
        List<Long> outStockOrderIds = batchTaskItemDTOList.stream().map(BatchTaskItemDTO::getRefOrderId)
            .filter(org.apache.commons.lang3.StringUtils::isNotBlank).map(Long::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outStockOrderIds)) {
            return;
        }
        List<OutStockOrderPO> outStockOrderPOList =
            outStockOrderMapper.findIdByIds(outStockOrderIds, batchTaskItemDTOList.get(0).getWarehouseId());
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }
        Map<Long, OutStockOrderPO> outStockOrderMap =
            outStockOrderPOList.stream().collect(Collectors.toMap(OutStockOrderPO::getId, v -> v));
        batchTaskItemDTOList.stream().filter(item -> StringUtils.isNotEmpty(item.getRefOrderId()))
            .filter(item -> Objects.nonNull(outStockOrderMap.get(Long.valueOf(item.getRefOrderId())))).forEach(item -> {
                OutStockOrderPO outStockOrderPO = outStockOrderMap.get(Long.valueOf(item.getRefOrderId()));
                // oo.UserName,oo.ShopName,oo.MobileNo,oo.DetailAddress,oo.Province,oo.City,oo.County,oo.Street
                item.setUserName(outStockOrderPO.getUsername());
                item.setShopName(outStockOrderPO.getShopname());
                item.setMobileNo(outStockOrderPO.getMobileno());
                item.setDetailAddress(outStockOrderPO.getDetailaddress());
                item.setProvince(outStockOrderPO.getProvince());
                item.setCity(outStockOrderPO.getCity());
                item.setCounty(outStockOrderPO.getCounty());
                item.setStreet(outStockOrderPO.getStreet());
            });
    }

    public List<BatchTaskItemDTO> processTaskItemIndex(List<BatchTaskItemDTO> lstItemDTO) {
        if (CollectionUtils.isEmpty(lstItemDTO)) {
            // 默认排序
            return processTaskItemOriginalSort(lstItemDTO);
        }

        // 不影响主流程
        try {
            // 切换到排序算法
            Integer warehouseId = lstItemDTO.get(0).getWarehouseId();

            String batchTaskNo = lstItemDTO.get(0).getBatchTaskNo();
            if (pickingTaskItemSortingBL.triggerPickingTaskSortingSwitch(warehouseId)) {
                BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskByNo(batchTaskNo);
                if (pickingTaskItemSortingBL.usePickingTaskSortingSwitch(warehouseId, batchTaskPO)) {
                    // 新版拣货排序算法
                    LOGGER.info("准备使用新版拣货排序算法:warehouseId={}:SorterId={}", warehouseId, batchTaskPO.getSorterId());
                    ResultWithMetrics<List<PickingTaskItemDTO>> resultWithMetrics =
                        pickingTaskItemSortingBL.getAlgoResultWithMetrics(lstItemDTO);
                    AlgorithmExecutionStateEnum stateEnum =
                        AlgorithmExecutionStateEnum.getEnum(resultWithMetrics.getState());
                    if (AlgorithmExecutionStateEnum.FINISHED == stateEnum) {
                        LOGGER.info("排序算法执行完成，开始处理结果:pickingTaskItemSortingResultId={}",
                            resultWithMetrics.getResultId());
                        return pickingTaskItemSortingBL.sortByAlgorithmResult(lstItemDTO,
                            resultWithMetrics.getResult());
                    } else {
                        LOGGER.info("排序算法执行未完成，使用原排序方法:pickingTaskItemSortingResultId={}:stateEnum={}",
                            resultWithMetrics.getResultId(), stateEnum);
                        // 默认排序
                        return processTaskItemOriginalSort(lstItemDTO);
                    }
                }
            }

            // 默认排序
            return processTaskItemOriginalSort(lstItemDTO);
        } catch (Exception e) {
            LOGGER.error("排序算法执行异常，使用原排序方法:lstItemDTO={}", JSON.toJSONString(lstItemDTO), e);
            // 默认排序
            return processTaskItemOriginalSort(lstItemDTO);
        }
    }

    public List<BatchTaskItemDTO> processTaskItemOriginalSort(List<BatchTaskItemDTO> lstItemDTO) {
        List<BatchTaskItemDTO> lstResult = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(lstItemDTO)) {
            // 按货位顺序排序
            List<String> locationIdSet = lstItemDTO.stream().filter(p -> p.getLocationId() != null)
                .map(p -> p.getLocationId().toString()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(locationIdSet)) {
                List<LocationReturnDTO> locationListById = locationAreaService.findLocationListById(locationIdSet);
                locationListById.forEach(q -> {
                    List<BatchTaskItemDTO> lstItem = lstItemDTO.stream()
                        .filter(p -> p.getLocationId() != null && p.getLocationId().equals(q.getId()))
                        .collect(Collectors.toList());
                    lstItem.forEach(item -> {
                        item.setSequence(q.getSequence());
                        item.setLocationCategory(q.getSubcategory() == null ? 0 : q.getSubcategory().intValue());
                    });
                });
            }
            if (WavesStrategyConstants.PICKINGTYPE_ORDER == lstItemDTO.get(0).getPickingType().intValue()) {
                // 按货位顺序排序
                List<BatchTaskItemDTO> lstTmp = new ArrayList<>();
                lstItemDTO.forEach(p -> {
                    if (p.getAddressId() == null) {
                        p.setAddressId(-1);
                    }
                    if (p.getOrderSquence() == null) {
                        p.setOrderSquence(Integer.MAX_VALUE);
                    }
                });
                // 按订单拣货，需要按波次生成时的订单顺序
                // 2025-02-08 调整为同客户的订单排到一块
                lstItemDTO.sort(Comparator.comparing(BatchTaskItemDTO::getRefOrderNo)
                    .thenComparing(BatchTaskItemDTO::getOrderSquence));
                for (BatchTaskItemDTO itemDTO : lstItemDTO) {
                    if (lstTmp.stream().anyMatch(p -> p.getRefOrderId().equals(itemDTO.getRefOrderId()))) {
                        continue;
                    }
                    // 查找同一客户的所有订单，无效地址只处理当前订单
                    List<String> lstOrderIdByCustom = itemDTO.getAddressId() > 0
                        ? lstItemDTO.stream().filter(p -> Objects.equals(p.getAddressId(), itemDTO.getAddressId()))
                            .map(BatchTaskItemDTO::getRefOrderId).distinct().collect(Collectors.toList())
                        : Collections.singletonList(itemDTO.getRefOrderId());
                    // 得到该客户的所有订单，混合按产品排序
                    List<BatchTaskItemDTO> taskItemDTOS = lstItemDTO.stream()
                        .filter(q -> lstOrderIdByCustom.contains(q.getRefOrderId())).collect(Collectors.toList());
                    // 按货位顺序排序
                    taskItemDTOS = processProductByLocation(taskItemDTOS, false);
                    lstTmp.addAll(taskItemDTOS);
                }
                lstResult = lstTmp;
            } else {
                lstResult = processProductByLocation(lstItemDTO, true);
            }
        }
        for (int i = 0; i < lstResult.size(); i++) {
            lstResult.get(i).setPickSequence(i);
        }
        return lstResult;
    }

    /**
     * 获取拣货排序优先级 1：分区优先（分区分单不播种） 2：波次优先（分区分单播种）
     */
    public Byte getOrderByType(Integer warehouseId) {
        if (warehouseId == null) {
            return null;
        }
        Byte orderType = null;
        PassageSO passageSO = new PassageSO();
        passageSO.setWarehouseId(warehouseId);
        PageList<PassageDTO> pageList = iPassageService.listPassage(passageSO);
        if (pageList != null && CollectionUtils.isNotEmpty(pageList.getDataList())) {
            for (PassageDTO passageDTO : pageList.getDataList()) {
                if (Objects.equals(passageDTO.getSowType(), SowTypeEnum.分区分单播种.getType())) {
                    orderType = (byte)2;
                } else if (Objects.equals(passageDTO.getSowType(), SowTypeEnum.分区分单不播种.getType())
                    || Objects.equals(passageDTO.getSowType(), SowTypeEnum.分区分单拣货.getType())) {
                    orderType = (byte)1;
                }
            }
        }
        // 读取内容配置，判断当前仓库是否按分区排序
        VariableValueQueryDTO configQuery = new VariableValueQueryDTO();
        configQuery.setVariableKey(BATCHTASK_GROUP_SORT);
        configQuery.setWarehouseId(warehouseId);
        VariableDefAndValueDTO configValueDTO = iVariableValueService.detailVariable(configQuery);
        if (configValueDTO != null && StringUtils.isNotEmpty(configValueDTO.getVariableData())) {
            if (Boolean.valueOf(configValueDTO.getVariableData())) {
                orderType = (byte)1;
            }
        }

        return orderType;
    }

    /**
     * 根据查找波次任务列表（PDA）
     */
    public PageList<BatchTaskSortDTO> findBatchTaskSortList(BatchTaskSortQueryDTO batchTaskSortQueryDTO) {
        LOGGER.info("波次任务列表参数：{}", JSON.toJSONString(batchTaskSortQueryDTO));
        // 获取当前仓库的拣货任务模式
        Integer warehouseId = batchTaskSortQueryDTO.getWarehouseId();
        Integer cityId = batchTaskSortQueryDTO.getCityId();
        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(warehouseId);
        PageResult<BatchTaskSortPO> poPageResult;
        resetSplitWarehouseAttrList(batchTaskSortQueryDTO);
        // 已完成，酒饮二次分拣时，查询分拣任务项
        Map<String, List<BatchTaskItemDTO>> secondPickBatchTaskItemGroup = null;
        if (Objects.equals(batchTaskSortQueryDTO.getType(), BatchTaskListTypeEnum.待拣货列表.getType())) {
            // 获取当前用户的分区ID
            List<Long> groupIds = iSortGroupService.listGroupIdByUserId(batchTaskSortQueryDTO.getUserId(),
                SortGroupFlagEnum.分区拣货.getType());
            batchTaskSortQueryDTO.setSortGroupId(groupIds);
            if (warehouseConfigDTO == null) {
                LOGGER.info("获取仓库的拣货任务模式失败！");
            }
            Byte batchTaskType = warehouseConfigDTO != null ? warehouseConfigDTO.getBatchTaskType() : null;
            batchTaskSortQueryDTO.setBatchTaskType(batchTaskType);
            // 拣货排序优先级
            batchTaskSortQueryDTO.setOrderByType(getOrderByType(warehouseId));
            AdminUser adminUser = globalCache.getAdminUserWithAuth(batchTaskSortQueryDTO.getUserId());
            List<Long> packagePassageIds = passageBL.getPackagePassageId(warehouseId, adminUser);
            if (CollectionUtils.isNotEmpty(packagePassageIds)) {
                batchTaskSortQueryDTO.setPassageIds(packagePassageIds);
            }
            poPageResult = batchTaskMapper.findBatchTaskSortListByReceive(batchTaskSortQueryDTO);
        } else {
            // 已拣货列表
            poPageResult = batchTaskMapper.findBatchTaskSortList(batchTaskSortQueryDTO);
            List<String> secondPickBatchTaskIdLit =
                poPageResult.stream().filter(BatchTaskKindOfPickingConvertor::isSecPickOrSortPicking)
                    .map(BatchTaskSortPO::getId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(secondPickBatchTaskIdLit)) {
                BatchTaskItemQueryDTO taskItemQueryDTO = new BatchTaskItemQueryDTO();
                taskItemQueryDTO.setCityId(cityId);
                taskItemQueryDTO.setWarehouseId(warehouseId);
                taskItemQueryDTO.setBatchTaskIdList(secondPickBatchTaskIdLit);
                taskItemQueryDTO.setPageSize(Integer.MAX_VALUE);
                List<BatchTaskItemDTO> batchTaskItemList =
                    batchTaskItemMapper.listBatchTaskItem(taskItemQueryDTO).getResult();
                if (CollectionUtils.isNotEmpty(batchTaskItemList)) {
                    secondPickBatchTaskItemGroup =
                        batchTaskItemList.stream().collect(Collectors.groupingBy(BatchTaskItemDTO::getBatchTaskId));
                }
            }
        }
        PageList<BatchTaskSortDTO> resultPageList = new PageList<>();
        resultPageList.setPager(poPageResult.getPager());
        if (CollectionUtils.isEmpty(poPageResult)) {
            resultPageList.setDataList(Collections.emptyList());
            return resultPageList;
        }
        // 酒饮二次分拣时，查询通道
        Map<Long, PassageDTO> secondPickPassageMap = passageBL.getSecondPickPassageMap(warehouseId, poPageResult);
        // 根据拣货任务查询播种任务关联信息
        List<String> batchTaskIds = poPageResult.stream().map(BatchTaskSortPO::getId).collect(Collectors.toList());
        List<SowTaskInfoPO> sowTaskInfoPOS = sowTaskMapper.findSowTaskInfoByBatchTaskIds(batchTaskIds, cityId);
        // 查询包含内配单的拣货任务id
        List<String> allocationBatchTaskIds = batchTaskMapper.findbatchTaskIncludeAllocationById(cityId, batchTaskIds);
        Map<Long, PassageDTO> passageMap = passageBL.listPassageByIds(warehouseId, poPageResult);
        Map<Set<Long>, PassageDTO> locationPassageMap = passageBL.listLocationPassages(warehouseId, poPageResult);
        // 随便取一个 locationId 就行
        Map<String,
            Long> batchTaskLocationMap = batchTaskItemMapper.findBatchTaskItemDTOListByTaskIds(batchTaskIds).stream()
                .filter(it -> it.getLocationId() != null).collect(
                    Collectors.toMap(BatchTaskItemPO::getBatchTaskId, BatchTaskItemPO::getLocationId, (a, b) -> b));

        // 同一波次下拣货任务必须是相同出库位，目前同一波次是同一用户
        boolean isNeedPalletForStock = wavesStrategyBOConvertor.isOpenTrayAndPickingRealTime(warehouseId);
        List<String> batchNos = poPageResult.stream().filter(p -> !StringUtils.isEmpty(p.getBatchNo()))
            .map(BatchTaskSortPO::getBatchNo).collect(Collectors.toList());
        Map<String, List<OrderLocationPalletDTO>> batchNoPalletMap =
            getBatchNoPalletMap(warehouseId, batchNos, isNeedPalletForStock);

        // 根据分区id查询分区标签(仅限分区拣货，即电子标签拣货)
        List<Long> sortGroupIds = poPageResult.stream()
            .filter(p -> p.getSortGroupId() != null
                && Objects.equals(p.getPickPattern(), BatchTaskPickPatternEnum.电子标签.getType()))
            .map(p -> p.getSortGroupId()).distinct().collect(Collectors.toList());
        Map<Long, SortGroupRfidDTO> sortGroupRfidMap =
            listSortGroupRfidInfo(warehouseId, SortGroupRfidTypeEnum.分区.getType(), sortGroupIds);

        List<BatchTaskSortDTO> resultDataList = new ArrayList<>();
        for (BatchTaskSortPO po : poPageResult) {
            BatchTaskSortDTO dto = new BatchTaskSortDTO();
            Byte kindOfPicking = po.getKindOfPicking();
            Integer sowType = po.getSowType();
            Long passageId = po.getPassageId();
            if (BatchTaskKindOfPickingConvertor.isSecPickOrSortPicking(po)) {
                if (secondPickBatchTaskItemGroup != null) {
                    List<BatchTaskItemDTO> curTaskItemList = secondPickBatchTaskItemGroup.get(po.getId());
                    // 未播种完成的
                    if (CollectionUtils.isNotEmpty(curTaskItemList) && hasUnCompletedBatchTask(curTaskItemList)) {
                        continue;
                    }
                }
                if (secondPickPassageMap != null) {
                    PassageDTO curPassage = secondPickPassageMap.get(passageId);
                    if (curPassage != null) {
                        if (!Objects.equals(curPassage.getSowType(), SowTypeEnum.二次分拣.getType())) {
                            sowType = null;
                        }
                    } else {
                        sowType = null;
                    }
                }
            }
            BeanUtils.copyProperties(po, dto);
            dto.setSowType(BatchTaskKindOfPickingConvertor.getBatchTaskSowType(sowType, po.getKindOfPicking()));
            // 拣货分组
            if (PickingTypeEnum.产品拣货.getType() == dto.getPickingType()) {
                if (PickingGroupStrategyEnum.货位.getType() == po.getPickingGroupStrategy()
                    || PickingGroupStrategyEnum.货区.getType() == po.getPickingGroupStrategy()) {
                    dto.setPickingValue(po.getLocationName());
                } else if (PickingGroupStrategyEnum.类目.getType() == po.getPickingGroupStrategy()) {
                    dto.setPickingValue(po.getCategoryName());
                }
            }
            dto.setBatchTaskName(getBatchTaskName(dto));
            // 订单筛选策略
            dto.setAreaOrRouteName(BatchTaskSortDTOConvertor.getAreaOrRouteName(po));
            // 获取集货位
            if (CollectionUtils.isNotEmpty(sowTaskInfoPOS)) {
                Optional<SowTaskInfoPO> sowTaskInfoPO = sowTaskInfoPOS.stream()
                    .filter(item -> dto.getBatchTaskNo().equals(item.getBatchTaskNo())).findAny();
                if (sowTaskInfoPO.isPresent()) {
                    dto.setSowLocationId(sowTaskInfoPO.get().getSowLocationId());
                    dto.setSowLocationName(sowTaskInfoPO.get().getSowLocationName());
                    dto.setSowTaskId(sowTaskInfoPO.get().getSowTaskId());
                    dto.setSowTaskNo(sowTaskInfoPO.get().getSowTaskNo());
                }
            }
            // 是否包含内配单
            if (CollectionUtils.isNotEmpty(allocationBatchTaskIds) && allocationBatchTaskIds.contains(dto.getId())) {
                dto.setAllocationFlag(true);
            }
            // LOGGER.info("仓库配置信息：{}",JSON.toJSONString(warehouseConfigDTO));
            // LOGGER.info("拣货任务信息：{}",JSON.toJSONString(po));
            // 仓库是否开通容器位
            if (Objects.nonNull(warehouseConfigDTO)) {
                if (warehouseConfigDTO.getOpenContainerLocation() != null
                    && warehouseConfigDTO.getOpenContainerLocation() == 1) {
                    // 出库位为空、按产品拣货、没有关联播种任务
                    if (po.getToLocationId() == null && po.getPickingType() == 2 && po.getSowTaskId() == null) {
                        dto.setContainerFlag(1);
                    }
                }
            }
            Integer count = redisUtil.getHash(RedisConstant.SUP_C.concat(PICK_SAMETIME).concat(dto.getBatchTaskNo()),
                dto.getBatchTaskNo());
            dto.setRemark(Objects.nonNull(count) ? count.toString() : BigDecimal.ZERO.toString());
            // 没有通道 id 就尝试用货位查询其关联的通道
            if (passageId == null) {
                for (Map.Entry<Set<Long>, PassageDTO> entry : locationPassageMap.entrySet()) {
                    if (entry.getKey().contains(batchTaskLocationMap.get(po.getId()))) {
                        dto.setPassageCode(entry.getValue().getPassageCode());
                    }
                }
            } else {
                Optional.ofNullable(passageMap.get(passageId)).map(PassageDTO::getPassageCode)
                    .ifPresent(dto::setPassageCode);
            }
            // 设置是否允许修改出库位
            batchTaskSortConvertor.setBachPalletInfo(dto, batchNoPalletMap);
            // 设置是否存在分区标签
            batchTaskSortConvertor.setSortGroupRfid(dto, sortGroupRfidMap);
            resultDataList.add(dto);
        }
        resultPageList.setDataList(resultDataList);
        return resultPageList;
    }

    private void resetSplitWarehouseAttrList(BatchTaskSortQueryDTO queryDTO) {
        if (Objects.isNull(queryDTO.getSplitWarehouseAttr())) {
            return;
        }

        if (TaskWarehouseFeatureTypeConstants.TASK_WAREHOUSE_FEATURE_WINE.equals(queryDTO.getSplitWarehouseAttr())) {
            queryDTO.setSplitWarehouseAttrList(
                Collections.singletonList(TaskWarehouseFeatureTypeConstants.TASK_WAREHOUSE_FEATURE_WINE));
            return;
        }

        queryDTO
            .setSplitWarehouseAttrList(Arrays.asList(TaskWarehouseFeatureTypeConstants.TASK_WAREHOUSE_FEATURE_DEFAULT,
                TaskWarehouseFeatureTypeConstants.TASK_WAREHOUSE_FEATURE_REST));
    }

    private String getBatchTaskName(BatchTaskSortDTO dto) {
        if (StringUtils.isBlank(dto.getRemark())) {
            return dto.getBatchTaskName();
        }

        if (dto.getBatchTaskName().contains(dto.getRemark())) {
            return dto.getBatchTaskName();
        }

        return dto.getBatchTaskName() + "-" + dto.getRemark();
    }

    /**
     * 根据id查找 根据订单拣货的波次任务详情
     */
    public PageList<BatchTaskSortOrderDTO> findBatchTaskSortItemByOrder(String batchTaskId, Integer currentPage,
        Integer pageSize) {
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);
        if (batchTaskPO == null) {
            throw new BusinessException("找不到该拣货任务，请联系管理员！");
        }
        // 以订单为维度，查找订单
        PageList<BatchTaskSortOrderDTO> pageList = new PageList<>();
        PageResult<String> orderPageList =
            batchTaskItemMapper.finBatchOrderIdListById(batchTaskId, currentPage, pageSize);
        if (orderPageList.isEmpty()) {
            pageList.setDataList(new ArrayList<>());
            pageList.setPager(new Pager(currentPage, pageSize, 0));
            return pageList;
        }
        List<BatchTaskItemDTO> batchTaskItemDTOList =
            batchTaskItemMapper.findBatchTaskItemDTOListByOrderId(batchTaskId, orderPageList);

        batchTaskItemDTOList = processTaskItemIndex(batchTaskItemDTOList);
        // Map<String, List<BatchTaskItemDTO>> map =
        // batchTaskItemDTOList.stream().collect(Collectors.groupingBy(BatchTaskItemDTO::getRefOrderId));

        Integer orgId =
            CollectionUtils.isNotEmpty(batchTaskItemDTOList) ? batchTaskItemDTOList.get(0).getOrgId() : null;
        Integer warehouseId =
            CollectionUtils.isNotEmpty(batchTaskItemDTOList) ? batchTaskItemDTOList.get(0).getWarehouseId() : null;
        Set<Long> skuIdSet = batchTaskItemDTOList.stream().map(BatchTaskItemDTO::getSkuId).collect(Collectors.toSet());
        // 拿到瓶码箱码信息
        Map<Long, ProductCodeDTO> codeMap = iProductSkuService.getPackageAndUnitCode(skuIdSet, orgId);
        // 货区货区类型
        List<String> locationIds = batchTaskItemDTOList.stream().filter(p -> p.getLocationId() != null)
            .map(p -> p.getLocationId().toString()).distinct().collect(Collectors.toList());
        LOGGER.info(String.format("货区类型locationIds：%s", JSON.toJSONString(locationIds)));
        // 查找拣货任务的集货位信息
        List<SowOrderDTO> sowOrderDTOS = sowOrderMapper.findByOrderIds(orderPageList);
        List<LocationReturnDTO> locationDTOList = null;
        if (CollectionUtils.isNotEmpty(locationIds)) {
            locationDTOList = locationAreaService.findLocationListById(locationIds);
        }
        // 订单在其他拣货任务的拣货数量
        List<BatchTaskSortOrderOtherDTO> otherBatchTaskDTOList =
            batchTaskItemMapper.listOtherBatchTaskByRefOrderId(batchTaskId, orderPageList, orgId);
        // 获取拣货任务详情
        List<String> batchTaskItemIds =
            batchTaskItemDTOList.stream().map(BatchTaskItemDTO::getId).collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByBatchTaskItemIds(batchTaskItemIds);
        List<Long> orderItemIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderItemId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderItemPO> outStockOrderItemPOList = outStockOrderItemMapper.listByIds(orderItemIds);

        // 获取控货策略名称
        List<Long> configIds = batchTaskItemDTOList.stream().map(BatchTaskItemDTO::getControlConfigId)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, String> configNameMap = getControlConfigNameMap(configIds);
        // 获取关联产品
        List<Long> skuIds =
            batchTaskItemDTOList.stream().map(BatchTaskItemDTO::getSkuId).distinct().collect(Collectors.toList());
        Map<Long, List<Long>> relationMap = getRelationProductSkuId(orgId, batchTaskPO.getWarehouseId(), skuIds);
        // 获取货主名称
        List<Long> ownerIds = batchTaskItemDTOList.stream().map(BatchTaskItemDTO::getOwnerId).filter(Objects::nonNull)
            .distinct().collect(Collectors.toList());
        Map<Long, String> ownerNameMap = getOwnerNameMap(ownerIds);
        // 获取内配单对应二级仓原单的出库位
        List<OutStockOrderLocationDTO> outStockOrderLocationDTOS = batchTaskItemDTOList.stream()
            .filter(p -> (Objects.equals(p.getBusinessType(), OutStockOrderBusinessType.内配订单.getType())
                || Objects.equals(p.getBusinessType(), OutStockOrderBusinessType.中转订单.getType())
                || Objects.equals(p.getOutBoundType(), OutBoundTypeEnum.INTERNAL_DELIVERY_ORDER.getCode().byteValue()))
                && !Objects.equals(p.getCreateAllocation(), ConditionStateEnum.是.getType()))
            .map(p -> {
                OutStockOrderLocationDTO orderLocationDTO = new OutStockOrderLocationDTO();
                orderLocationDTO.setOmsOrderId(StreamUtils.isNum(p.getBusinessId()) ? Long.valueOf(p.getBusinessId())
                    : Long.valueOf(p.getRefOrderId()));
                orderLocationDTO.setWarehouseId(p.getWarehouseId());
                return orderLocationDTO;
            }).collect(Collectors.toList());
        List<OutStockOrderLocationDTO> orderLocationDTOS =
            outStockOrderBL.findOutStockOrderLocation(outStockOrderLocationDTOS);
        // 获取内配单收货暂存位
        DefaultLocationConfigDTO defaultLocationConfigDTO = getDefaultNPLocaiton(batchTaskPO.getWarehouseId());
        // 获取产品是否是库龄管控产品
        Map<Long, Boolean> stockAgeMap =
            iStockAgeStrategyService.isProductStockAgeControls(orgId, warehouseId, new ArrayList<>(skuIdSet), false);
        // 获取产品图片
        Map<Long, ProductImageDTO> productImageDTOMap = iProductSkuQueryService.getProductImageUrl(skuIds);
        // 订单货位托盘关系数据
        Map<Long,
            List<String>> orderPalletMap = outStockOrderBL.getOrderPalletNoListMap(warehouseId,
                orderPageList.stream().filter(p -> !StringUtils.isEmpty(p) && StringUtils.isNumeric(p))
                    .map(Long::valueOf).distinct().collect(Collectors.toList()));
        // 封装信息
        // LOGGER.info(String.format("findBatchTaskSortItemByOrder 入参batchTaskItemDTOList：%s",
        // JSON.toJSONString(batchTaskItemDTOList)));
        // LOGGER.info(String.format("findBatchTaskSortItemByOrder 入参otherBatchTaskDTOList：%s",
        // JSON.toJSONString(otherBatchTaskDTOList)));
        // LOGGER.info(String.format("findBatchTaskSortItemByOrder 入参outStockOrderItemPOList：%s",
        // JSON.toJSONString(outStockOrderItemPOList)));
        List<BatchTaskSortOrderDTO> list = WaveOrderTaskConvertor.batchTakItemMapTODTO(batchTaskItemDTOList, codeMap,
            locationDTOList, sowOrderDTOS, otherBatchTaskDTOList, orderItemTaskInfoPOList, configNameMap, relationMap,
            ownerNameMap, orderLocationDTOS, defaultLocationConfigDTO, stockAgeMap, productImageDTOMap, orderPalletMap,
            outStockOrderItemPOList);
        // LOGGER.info(String.format("findBatchTaskSortItemByOrder 结果batchTaskItemDTOList：%s",
        // JSON.toJSONString(batchTaskItemDTOList)));
        // LOGGER.info(String.format("findBatchTaskSortItemByOrder 结果otherBatchTaskDTOList：%s",
        // JSON.toJSONString(otherBatchTaskDTOList)));
        // LOGGER.info(String.format("findBatchTaskSortItemByOrder 结果outStockOrderItemPOList：%s",
        // JSON.toJSONString(outStockOrderItemPOList)));
        // 封装对应的箱号
        List<String> refOrderNoList =
            list.stream().map(BatchTaskSortOrderDTO::getOrderNoteno).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(refOrderNoList)) {
            // 根据单号查询所有包装信息
            List<PackageOrderItemDTO> packageOrderItemDTOList =
                packageOrderItemMapper.listPackageBatchTaskItemByOrderNos(refOrderNoList,
                    Integer.valueOf(batchTaskPO.getOrgId()), batchTaskPO.getWarehouseId());
            if (CollectionUtils.isNotEmpty(packageOrderItemDTOList)) {
                list.forEach(batchTaskSortOrderDTO -> {
                    // 根据单号查询所有包装信息
                    List<PackageOrderItemDTO> newPackageOrderItemDTOList = packageOrderItemDTOList.stream()
                        .filter(p -> Objects.equals(p.getRefOrderNo(), batchTaskSortOrderDTO.getOrderNoteno()))
                        .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(newPackageOrderItemDTOList)) {
                        Map<String, List<PackageOrderItemDTO>> packageMap =
                            newPackageOrderItemDTOList.stream().filter(item -> item.getBatchTaskItemId() != null)
                                .collect(Collectors.groupingBy(PackageOrderItemDTO::getBatchTaskItemId));
                        Map<Long, List<PackageOrderItemDTO>> packageSkuMap = newPackageOrderItemDTOList.stream()
                            .collect(Collectors.groupingBy(PackageOrderItemDTO::getSkuId));
                        batchTaskSortOrderDTO.getBatchTaskItemDTOList().forEach(item -> {
                            List<PackageOrderItemDTO> packageList = packageMap.get(item.getId());
                            List<PackageOrderItemDTO> packageSkuList = packageSkuMap.get(item.getSkuId());
                            if (CollectionUtils.isNotEmpty(packageList)) {
                                item.setBoxCodeList(packageList.stream().map(PackageOrderItemDTO::getBoxCode)
                                    .collect(Collectors.toList()));
                                // 兼容旧版-单箱号
                                if (packageList.size() == 1) {
                                    item.setBoxCode(packageList.get(0).getBoxCode());
                                }
                                item.setPackageType(packageList.get(0).getPackageType());
                            } else if (CollectionUtils.isNotEmpty(packageSkuList)) {
                                item.setBoxCodeList(packageSkuList.stream().map(PackageOrderItemDTO::getBoxCode)
                                    .collect(Collectors.toList()));
                                // 兼容旧版-单箱号
                                if (packageSkuList.size() == 1) {
                                    item.setBoxCode(packageSkuList.get(0).getBoxCode());
                                }
                                item.setPackageType(packageSkuList.get(0).getPackageType());
                            }
                        });
                    }
                });
            }
        }
        List<BatchTaskSortItemDTO> sortItemDTOList =
            list.stream().filter(m -> CollectionUtils.isNotEmpty(m.getBatchTaskItemDTOList()))
                .flatMap(m -> m.getBatchTaskItemDTOList().stream()).collect(Collectors.toList());

        batchTaskSortItemDTOConvertor.fillPromotionInfo(sortItemDTOList, batchTaskItemDTOList, orgId);
        orderFeatureBL.fillOrderFeature(list);
        pageList.setDataList(list);
        pageList.setPager(orderPageList.getPager());
        // LOGGER.info(String.format("按订单顺序排序-3：%s", JSON.toJSONString(list)));
        return pageList;
    }

    /**
     * 整件打包拣货完成任务明细
     */
    public BatchTaskItemPickCompleteDetailDTO
        findCompleteBatchTaskItem(BatchTaskItemPickCompleteDetailQueryDTO queryDTO) {
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(queryDTO.getBatchTaskId());
        if (Objects.isNull(batchTaskPO)) {
            throw new BusinessValidateException("拣货任务不存在！");
        }
        WarehouseConfigDTO warehouseConfigDTO =
            warehouseConfigService.getConfigByWareHouseId(batchTaskPO.getWarehouseId());
        if (Objects.isNull(warehouseConfigDTO)
            || !WavesStrategyConstants.PACKAGEREVIEWTYPE_OPEN.equals(warehouseConfigDTO.getPackageReviewType())) {
            return null;
        }

        if (Objects.isNull(batchTaskPO.getSowTaskId())) {
            return null;
        }

        List<OrderItemTaskInfoPO> orderItemTaskInfoList =
            orderItemTaskInfoMapper.listTaskInfoByBatchTaskIds(Collections.singletonList(queryDTO.getBatchTaskId()));

        List<OutStockOrderItemPO> outStockItemList = outStockOrderItemMapper.findByOutstockorderIdList(
            orderItemTaskInfoList.stream().map(OrderItemTaskInfoPO::getRefOrderId).collect(Collectors.toList()));

        return batchTaskItemPickCompleteDetailDTOConvertor.convert(batchTaskPO, orderItemTaskInfoList, outStockItemList,
            queryDTO.getSkuId());
    }

    /**
     * 整件打包拣货任务明细
     *
     * @param queryDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<BatchTaskItemPackageReviewDetailDTO>
        findPackageReviewBatchItem(BatchTaskItemPickCompleteDetailQueryDTO queryDTO) {
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(queryDTO.getBatchTaskId());
        if (Objects.isNull(batchTaskPO)) {
            throw new BusinessValidateException("拣货任务不存在！");
        }
        WarehouseConfigDTO warehouseConfigDTO =
            warehouseConfigService.getConfigByWareHouseId(batchTaskPO.getWarehouseId());
        if (Objects.isNull(warehouseConfigDTO)
            || !WavesStrategyConstants.PACKAGEREVIEWTYPE_OPEN.equals(warehouseConfigDTO.getPackageReviewType())) {
            return null;
        }

        if (Objects.isNull(batchTaskPO.getSowTaskId())) {
            return null;
        }

        List<OrderItemTaskInfoPO> orderItemTaskInfoList =
            orderItemTaskInfoMapper.listTaskInfoByBatchTaskIds(Collections.singletonList(queryDTO.getBatchTaskId()));

        List<OutStockOrderItemPO> outStockItemList = outStockOrderItemMapper.findByOutstockorderIdList(
            orderItemTaskInfoList.stream().map(OrderItemTaskInfoPO::getRefOrderId).collect(Collectors.toList()));

        List<BatchTaskItemPO> batchTaskItemList =
            batchTaskItemMapper.findBatchTaskItemDTOListByTaskIds(Collections.singletonList(batchTaskPO.getId()));

        return batchTaskItemPackageReviewDetailDTOConvertor.convert(batchTaskPO, orderItemTaskInfoList,
            outStockItemList, batchTaskItemList);
    }

    /**
     * 获取内配单收货暂存位
     *
     * @return
     */
    private DefaultLocationConfigDTO getDefaultNPLocaiton(Integer warehouseId) {
        DefaultLocationQueryDTO defaultLocationQueryDTO = new DefaultLocationQueryDTO();
        defaultLocationQueryDTO.setWarehouseId(warehouseId);
        defaultLocationQueryDTO.setTemporaryLocationType(DefaultTypeEnum.内配单收货暂存位.getType());
        List<DefaultLocationConfigDTO> defaultLocationList =
            iDefaultLocationConfigService.findDefaultLocationConfig(defaultLocationQueryDTO);
        if (CollectionUtils.isEmpty(defaultLocationList)) {
            return null;
        }
        return defaultLocationList.get(0);
    }

    /**
     * 根据控货策略ID查询控货策略名称
     *
     * @return
     */
    private Map<Long, String> getControlConfigNameMap(List<Long> configIds) {
        if (CollectionUtils.isEmpty(configIds)) {
            return null;
        }
        return iProductControlConfigService.getConfigItemNameMap(configIds);
    }

    /**
     * 获取货主名称
     *
     * @return
     */
    private Map<Long, String> getOwnerNameMap(List<Long> ownerIds) {
        Map<Long, String> ownerNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ownerIds)) {
            List<OwnerDTO> ownerDTOS = ownerService.listOwnerByIds(ownerIds);
            if (CollectionUtils.isNotEmpty(ownerDTOS)) {
                ownerDTOS.forEach(owner -> {
                    ownerNameMap.put(owner.getId(), owner.getOwnerName());
                });
            }
        }
        return ownerNameMap;
    }

    /**
     * 查询关联产品
     *
     * @return
     */
    private Map<Long, List<Long>> getRelationProductSkuId(Integer cityId, Integer warehouseId, List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return null;
        }
        ProductRelevanceConfigQueryDTO configQueryDTO = new ProductRelevanceConfigQueryDTO();
        configQueryDTO.setCityId(cityId);
        configQueryDTO.setWarehouseId(warehouseId);
        configQueryDTO.setSkuIds(skuIds);
        Map<Long, List<Long>> relationMap = iProductRelationGroupService.findGroupProductDirect(warehouseId, skuIds);
        if (relationMap == null || relationMap.isEmpty()) {
            return null;
        }
        return relationMap;
    }

    /**
     * 根据id查找 根据产品拣货的波次任务详情
     *
     * @param batchTaskId
     * @param currentPage
     * @param pageSize
     * @return
     */
    public PageList<BatchTaskSortItemDTO> findBatchTaskSortItemByProduct(String batchTaskId, Integer currentPage,
        Integer pageSize) {
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);
        if (batchTaskPO == null) {
            throw new BusinessException("找不到该拣货任务，请联系管理员！");
        }
        PageList<BatchTaskSortItemDTO> pageList = new PageList<>();
        PageResult<BatchTaskItemDTO> pageResult =
            batchTaskItemMapper.findPageBatchTaskItemDTOListById(batchTaskId, currentPage, pageSize);

        // 按货位顺序排序
        List<BatchTaskItemDTO> lstResult = processTaskItemIndex(pageResult);

        if (lstResult.size() == 0) {
            pageList.setDataList(new ArrayList<>());
            pageList.setPager(new Pager(currentPage, pageSize, 0));
            return pageList;
        }

        Integer orgId =
            batchTaskPO.getOrgId() != null ? Integer.valueOf(batchTaskPO.getOrgId()) : lstResult.get(0).getOrgId();
        Integer warehouseId = batchTaskPO.getWarehouseId();
        List<BatchTaskSortItemDTO> list = setBarcodes(lstResult, orgId, warehouseId);
        // 查询包装箱信息
        setBoxCodes(list, orgId, warehouseId);
        // 设置拣货任务明细项容器位集合
        setContainerDTOList(list);
        // 按产品拣货任务项填充调拨和内配退批次类型
        fillAllotOutBoundTypeByProduct(list);
        pageList.setPager(pageResult.getPager());
        pageList.setDataList(list);
        return pageList;
    }

    /**
     * 设置箱号信息
     *
     * @param batchTaskSortItemDTOS
     * @param orgId
     * @param warehouseId
     * @return
     */
    private void setBoxCodes(List<BatchTaskSortItemDTO> batchTaskSortItemDTOS, Integer orgId, Integer warehouseId) {
        if (CollectionUtils.isNotEmpty(batchTaskSortItemDTOS)) {
            List<String> batchItemIds =
                batchTaskSortItemDTOS.stream().map(BatchTaskSortItemDTO::getId).collect(Collectors.toList());
            List<PackageOrderItemPO> packageOrderItemPOS =
                packageOrderItemMapper.findPackageOrderItemsByBatchItemIds(batchItemIds, orgId, warehouseId);
            if (CollectionUtils.isNotEmpty(packageOrderItemPOS)) {
                packageOrderItemPOS.stream().filter(item -> item.getBatchTaskItemId() != null)
                    .collect(Collectors.groupingBy(PackageOrderItemPO::getBatchTaskItemId))
                    .forEach((batchTaskItemId, list) -> {
                        List<String> boxCodes =
                            list.stream().map(PackageOrderItemPO::getBoxCode).collect(Collectors.toList());
                        batchTaskSortItemDTOS.stream().filter(item -> item.getId().equals(batchTaskItemId))
                            .forEach(item -> {
                                item.setBoxCodeList(boxCodes);
                                item.setPackageType(list.get(0).getPackageType());
                            });
                    });
            }
        }
    }

    /**
     * 设置拣货任务明细项容器位集合
     *
     * @param list
     */
    private void setContainerDTOList(List<BatchTaskSortItemDTO> list) {
        List<Long> batchTaskItemIdList = list.stream().map(p -> Long.valueOf(p.getId())).collect(Collectors.toList());
        List<BatchTaskItemContainerPO> containerPOList =
            batchTaskItemContainerMapper.selectByBatchtaskitemIdList(batchTaskItemIdList);
        List<BatchTaskItemContainerDTO> containerDTOList = containerPOList.stream().map(po -> {
            BatchTaskItemContainerDTO dto = new BatchTaskItemContainerDTO();
            dto.setBatchTaskItemId(po.getBatchtaskitemId());
            dto.setLocationId(po.getLocationId());
            dto.setLocationName(po.getLocationName());
            return dto;
        }).collect(Collectors.toList());
        Map<Long, List<BatchTaskItemContainerDTO>> containerDTOMap =
            containerDTOList.stream().collect(Collectors.groupingBy(x -> x.getBatchTaskItemId()));
        list.forEach(x -> {
            x.setContainerDTOList(containerDTOMap.get(Long.valueOf(x.getId())));
        });
    }

    /**
     * 查询打印拣货单(供应链客户端)
     *
     * @param batchTaskNoS
     * @return
     */
    public Map<String, List<BatchTaskItemDTO>> findBatchTaskItemMap(List<String> batchTaskNoS) {
        HashMap<String, List<BatchTaskItemDTO>> map = new HashMap<>();
        for (String batchTaskNo : batchTaskNoS) {
            PageResult<BatchTaskItemDTO> pageResult =
                batchTaskItemMapper.findBatchTaskItemDTOListByBatchTaskNo(null, batchTaskNo, null, null);
            List<BatchTaskItemDTO> lstItems = pageResult.toPageList().getDataList();
            if (CollectionUtils.isNotEmpty(lstItems)) {
                lstItems = processTaskItemIndex(lstItems);
            }
            map.put(batchTaskNo, lstItems);
        }
        return map;
    }

    private List<BatchTaskItemDTO> processProductByLocation(List<BatchTaskItemDTO> lstItems, boolean isWineFirst) {
        lstItems.forEach(p -> {
            if (p.getSequence() == null) {
                p.setSequence(Integer.MAX_VALUE);
            }
            if (StringUtils.isEmpty(p.getCategoryName())) {
                p.setCategoryName("");
            }
            if (StringUtils.isEmpty(p.getProductBrand())) {
                p.setProductBrand("");
            }
            if (StringUtils.isEmpty(p.getProductName())) {
                p.setProductName("");
            }
        });
        List<BatchTaskItemDTO> lstResult = new ArrayList<>();
        if (isWineFirst) {
            lstResult = lstItems.stream()
                .sorted(Comparator.comparing(BatchTaskItemDTO::getSequence)
                    .thenComparing(BatchTaskItemDTO::getCategoryName).thenComparing(BatchTaskItemDTO::getProductBrand)
                    .thenComparing(BatchTaskItemDTO::getProductName))
                .collect(Collectors.toList());
        } else {
            lstResult = lstItems.stream()
                .sorted(Comparator.comparing(BatchTaskItemDTO::getSequence)
                    .thenComparing(BatchTaskItemDTO::getCategoryName).thenComparing(BatchTaskItemDTO::getProductBrand)
                    .thenComparing(BatchTaskItemDTO::getProductName))
                .collect(Collectors.toList());
        }
        return lstResult;
    }

    private List<BatchTaskSortItemDTO> setBarcodes(List<BatchTaskItemDTO> lstItems, Integer cityId,
        Integer warehouseId) {
        Set<Long> skuIdSet = lstItems.stream().map(p -> p.getSkuId()).collect(Collectors.toSet());
        Map<Long, ProductCodeDTO> codeMap = iProductSkuService.getPackageAndUnitCode(skuIdSet,
            CollectionUtils.isNotEmpty(lstItems) ? lstItems.get(0).getOrgId() : null);
        // 获取货区
        List<String> locationIds = lstItems.stream().filter(p -> p.getLocationId() != null)
            .map(p -> p.getLocationId().toString()).distinct().collect(Collectors.toList());
        LOGGER.info(String.format("货区类型locationIds：%s", JSON.toJSONString(locationIds)));
        List<LocationReturnDTO> locationDTOList = null;
        if (CollectionUtils.isNotEmpty(locationIds)) {
            locationDTOList = locationAreaService.findLocationListById(locationIds);
        }
        // 获取控货策略名称
        List<Long> configIds = lstItems.stream().filter(p -> p.getControlConfigId() != null)
            .map(p -> p.getControlConfigId()).distinct().collect(Collectors.toList());
        Map<Long, String> configNameMap = getControlConfigNameMap(configIds);
        // 获取关联产品
        List<Long> skuIds = lstItems.stream().map(p -> p.getSkuId()).distinct().collect(Collectors.toList());
        Map<Long, List<Long>> relationMap = getRelationProductSkuId(cityId, warehouseId, skuIds);
        // 获取货主名称
        List<Long> ownerIds = lstItems.stream().filter(p -> p.getOwnerId() != null).map(p -> p.getOwnerId()).distinct()
            .collect(Collectors.toList());
        Map<Long, String> ownerNameMap = getOwnerNameMap(ownerIds);
        // 获取产品是否是库龄管控产品
        Map<Long, Boolean> stockAgeMap =
            iStockAgeStrategyService.isProductStockAgeControls(cityId, warehouseId, new ArrayList<>(skuIdSet), false);
        // 获取产品图片
        Map<Long, ProductImageDTO> productImageDTOMap = iProductSkuQueryService.getProductImageUrl(skuIds);

        List<BatchTaskSortItemDTO> list =
            WaveOrderTaskConvertor.getBatchTaskSortItemDTOList(codeMap, lstItems, locationDTOList, null, configNameMap,
                relationMap, ownerNameMap, stockAgeMap, productImageDTOMap, Collections.emptyList());
        batchTaskSortItemDTOConvertor.fillPromotionInfo(list, lstItems, cityId);
        return list;
    }

    /**
     * 波次任务开始
     *
     * @param batchTaskId
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public BatchTaskSowDTO beginBatchTask(String batchTaskId, String operateUser, Integer userId) {
        List<BatchTaskPO> lstBatchTasks =
            batchTaskMapper.findTasksByBatchTaskId(Collections.singletonList(batchTaskId));
        if (CollectionUtils.isEmpty(lstBatchTasks)) {
            throw new BusinessException("拣货任务不存在，请刷新重试！");
        }
        if (!lstBatchTasks.stream().anyMatch(p -> TaskStateEnum.未分拣.getType() == p.getTaskState())) {
            throw new BusinessValidateException("单据状态已变更，请刷新后重试！");
        }
        Integer cityId =
            lstBatchTasks.get(0).getOrgId() != null ? Integer.valueOf(lstBatchTasks.get(0).getOrgId()) : null;
        // 修改波次状态
        batchTaskMapper.updateBatchTaskById(batchTaskId, 分拣中.getType(), null, null, userId, operateUser, true, cityId,
            null);
        // -添加操作记录（任务开始拣货）
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);
        if (Objects.isNull(batchTaskPO)) {
            throw new BusinessValidateException("拣货任务不存在，请刷新重试！");
        }
        orderTraceBL.updateBatchTaskTrace(batchTaskPO, OrderTraceDescriptionEnum.开始拣货.name(), operateUser);

        // 播种任务下的拣货任务第一次拣货时，需要给没有集货位的播种任务设置集货位(通道开启分区分单)
        sowManagerBL.updateSowTaskLocation(batchTaskPO);

        List<String> lstBatchIds = new ArrayList<>();
        lstBatchTasks.forEach(p -> {
            LOGGER.info(JSON.toJSONString(p));
            if (StringUtils.isNotEmpty(p.getBatchNo()) && !lstBatchIds.contains(p.getBatchNo())) {
                lstBatchIds.add(p.getBatchNo());
            }
        });
        if (lstBatchIds.size() > 0) {
            // 更新波次状态
            batchMapper.updateBatchStateByNos(lstBatchIds, PICKING.getType(), cityId);
            // -添加操作记录（波次开始拣货）
            List<BatchPO> batchPOList = batchMapper.listByBatchNos(lstBatchIds);
            orderTraceBL.updateBatchTrace(batchPOList, OrderTraceDescriptionEnum.波次开始拣货.name(), operateUser);
        }
        // List<BatchTaskItemPO> batchTaskItemDTOList = batchTaskItemMapper.findBatchTaskItemDTOListNoPage(batchTaskId);
        // Map<String, Byte> map = new HashMap<>(batchTaskItemDTOList.size());
        // batchTaskItemDTOList.forEach(p -> {
        // if (p.getUnitTotalCount() > (p.getOverSortCount() + p.getLackUnitCount())) {
        // map.put(p.getId(), TaskItemStateEnum.分拣中.getType());
        // }
        // });
        // AssertUtils.notEmpty(map, "该波次任务已经分拣完成");
        // //批量修改波次任务详情状态
        // batchTaskItemMapper.updateBatchTaskItemList(map);
        BatchTaskSowDTO batchTaskSowDTO;
        if (batchTaskPO.getSowTaskId() != null) {
            List<BatchTaskPO> batchTasks = batchTaskMapper.findBatchTaskInfoById(batchTaskId);
            batchTaskSowDTO = BatchTaskConvertor.convertToBatchTaskSowDTO(batchTasks);
        } else {
            batchTaskSowDTO = BatchTaskConvertor.convertToBatchTaskSowDTO(Collections.singletonList(batchTaskPO));
        }
        return batchTaskSowDTO;
    }

    /**
     * 批量修改波次任务拣货人
     *
     * <AUTHOR>
     * @since 2018/4/2 9:40
     */
    public int updateBatch(List<BatchTaskDTO> batchTaskDTOS, String operateUser) {
        List<String> lstTaskNos = batchTaskDTOS.stream().map(BatchTaskDTO::getBatchTaskNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lstTaskNos)) {
            throw new DataValidateException("拣货任务编号不能为空");
        }
        List<BatchTaskPO> lstBatchTasks = batchTaskMapper.findTasksByBatchTaskNo(lstTaskNos);
        if (CollectionUtils.isEmpty(lstBatchTasks)) {
            throw new BusinessValidateException("拣货任务已取消，请刷新");
        }
        if (lstBatchTasks.stream().anyMatch(p -> TaskStateEnum.已完成.getType() == p.getTaskState())) {
            throw new BusinessValidateException("已完成的拣货任务，不能指派分拣员！");
        }
        Integer successItemCount = batchTaskAssignHelper.handleRobot(lstBatchTasks, batchTaskDTOS, operateUser);
        Integer failItemCount = batchTaskAssignHelper.handleNormal(lstBatchTasks, batchTaskDTOS, operateUser);
        // -添加操作记录（指派拣货员）
        String sorter = "（" + batchTaskDTOS.get(0).getSorter() + "）";
        orderTraceBL.updateBatchTaskTraceBatch(lstBatchTasks, OrderTraceDescriptionEnum.指派拣货员.name() + sorter,
            operateUser);
        List<Integer> lstSorter = batchTaskDTOS.stream().map(BatchTaskDTO::getSorterId).filter(Objects::nonNull)
            .distinct().collect(Collectors.toList());
        // 发送消息到PDA
        lstSorter.forEach(p -> {
            // 消息推送
            orderTraceBL.pushTaskMsg(p);
            // orderTraceBL.sendPushMsg(p, 1);
        });
        return successItemCount + failItemCount;
    }

    /**
     * 批量更新波次任务
     *
     * @param modDTO
     * @return
     */
    public int updateBatchTaskBatch(BatchTaskModDTO modDTO) {
        String trueName = "";
        if (Objects.nonNull(modDTO.getOptUserId())) {
            AdminUser adminUser = iAdminUserService.getAdminUserWithoutAuthById(modDTO.getOptUserId());
            trueName = Objects.isNull(adminUser) ? "" : adminUser.getTrueName();
        }
        return updateBatch(modDTO.getList(), trueName);
    }

    /**
     * 领取拣货任务
     */
    @DistributeLock(conditions = "#batchTaskDTO.id", sleepMills = 3000, key = "receiveBatchTask")
    @Transactional(rollbackFor = RuntimeException.class)
    public void receiveBatchTask(BatchTaskDTO batchTaskDTO) {
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskDTO.getId());
        if (batchTaskPO == null) {
            throw new BusinessException("拣货单不存在！");
        }
        if (!Objects.equals(batchTaskPO.getTaskState(), TaskStateEnum.未分拣.getType())) {
            throw new BusinessValidateException("只有待拣货状态的拣货任务才能领取！");
        }
        if (batchTaskPO.getSorterId() != null) {
            throw new BusinessValidateException("该拣货任务已经被领取，请刷新！");
        }
        BatchTaskPO updateBatchTaskPO = new BatchTaskPO();
        BeanUtils.copyProperties(batchTaskDTO, updateBatchTaskPO);
        LOGGER.info("领取拣货任务:{}", JSON.toJSONString(updateBatchTaskPO));
        Integer cityId = batchTaskPO.getOrgId() != null ? Integer.valueOf(batchTaskPO.getOrgId()) : null;
        batchTaskMapper.updateBatch(Arrays.asList(updateBatchTaskPO), cityId);

        // -添加操作记录（领取拣货任务）
        orderTraceBL.updateBatchTaskTrace(batchTaskPO, OrderTraceDescriptionEnum.领取拣货任务成功.name(),
            batchTaskDTO.getSorter());

        // 消息推送
        orderTraceBL.pushTaskMsg(updateBatchTaskPO.getSorterId());
    }

    /**
     * 记录打印
     *
     * @param taskNoList 拣货单编号
     * <AUTHOR>
     */
    public void recordPrint(List<String> taskNoList, String operateUser) {
        batchTaskMapper.updatePrintCount(taskNoList, operateUser);
        // -添加操作记录（打印拣货单）
        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findTasksByBatchTaskNo(taskNoList);
        orderTraceBL.printBatchTaskTrace(batchTaskPOList, operateUser);
    }

    /**
     * 转换skuId（内配单会出现skuId不一致的情况）
     */
    public void transferSkuId(List<String> refOrderNos, List<BatchTaskItemLackDTO> filterBatchTaskItemLackDTOS) {
        List<String> itemIds =
            filterBatchTaskItemLackDTOS.stream().map(p -> p.getBatchTaskItemId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refOrderNos) || CollectionUtils.isEmpty(itemIds)) {
            return;
        }
        Integer cityId = filterBatchTaskItemLackDTOS.get(0).getCityId();
        Integer warehouseId = filterBatchTaskItemLackDTOS.get(0).getWarehouseId();
        List<TransferSkuIdDTO> transferSkuIdDTOS =
            batchTaskMapper.finTransferSkuId(cityId, warehouseId, itemIds, refOrderNos);
        LOGGER.info("转换skuId：{}", JSON.toJSONString(transferSkuIdDTOS));
        filterBatchTaskItemLackDTOS.forEach(lackDTO -> {
            List<TransferSkuIdDTO> tempList = transferSkuIdDTOS.stream()
                .filter(p -> p.getSkuId().equals(lackDTO.getSkuId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(tempList)) {
                lackDTO.setSkuId(tempList.get(0).getOldSkuId());
            }
        });
    }

    /**
     * 拣货任务确认完成
     *
     * <AUTHOR>
     */
    @DistributeLock(conditions = "#batchTaskConfirmDTO.batchTaskIds[0]", sleepMills = 3000, expireMills = 60000,
        key = BatchTaskConstants.BATCH_TASK_COMPLETE_KEY)
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public BatchTaskCompleteResultDTO batchTaskComplete(BatchTaskConfirmDTO batchTaskConfirmDTO) {
        LOGGER.info("拣货任务完成，入参: {}", JSON.toJSONString(batchTaskConfirmDTO));
        BatchTaskCompleteResultDTO confirmResultDTO = new BatchTaskCompleteResultDTO();
        List<String> batchTaskNoS = batchTaskConfirmDTO.getBatchTaskNo();
        List<BatchTaskItemDTO> batchTaskItemList = batchTaskConfirmDTO.getBatchTaskItemList();
        Integer orgId = batchTaskConfirmDTO.getOrgId();
        Integer warehouseId = batchTaskConfirmDTO.getWarehouseId();
        String operateUser = batchTaskConfirmDTO.getOperateUser();
        Integer operateUserId = batchTaskConfirmDTO.getOperateUserId();
        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findTasksByBatchTaskNo(batchTaskNoS);
        if (CollectionUtils.isEmpty(batchTaskPOList)) {
            throw new BusinessException("获取拣货任务为空");
        }
        batchTaskPOList.removeIf(it -> TaskStateEnum.已作废.valueEquals(it.getTaskState()));
        if (batchTaskPOList.isEmpty()) {
            LOGGER.info("拣货任务全部作废: {}", JSON.toJSONString(batchTaskConfirmDTO, SerializerFeature.WriteMapNullValue));
            return confirmResultDTO;
        }
        List<String> batchNos =
            batchTaskPOList.stream().map(BatchTaskPO::getBatchNo).distinct().collect(Collectors.toList());
        List<BatchPO> batchPOS = batchMapper.findBatchByNos(batchNos, orgId);
        if (CollectionUtils.isEmpty(batchPOS)) {
            throw new BusinessException("获取波次任务为空");
        }

        filterAndValidateSecPick(batchPOS, batchTaskPOList, warehouseId);

        // 判断订单类型是否为自提订单 如果是自提订单 则不能缺货
        List<BatchTaskItemDTO> lackTaskItems = batchTaskItemList.stream().filter(s -> s.getLackUnitCount() != null)
            .filter(s -> s.getLackUnitCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(lackTaskItems)) {
            // 校验订单能否缺货
            orderConstraintCheckBL.checkNormalOrderPickLack(batchNos, batchTaskPOList, warehouseId);
        }
        List<BatchTaskItemDTO> batchTaskItemDTOList = batchTaskItemMapper.findBatchTaskItemDtoListByNo(batchTaskNoS);
        if (CollectionUtils.isEmpty(batchTaskItemDTOList)) {
            throw new BusinessException("获取拣货任务详情为空");
        }
        batchTaskPOList.forEach(p -> {
            if (p.getTaskState() == TaskStateEnum.已完成.getType()) {
                throw new BusinessValidateException("拣货已完成，请勿重复操作！编号：" + p.getBatchTaskNo());
            }
        });

        // 拣货任务项的出库配置校验
        if (isBanOutStockByOutStockConfig(batchTaskItemDTOList, batchTaskConfirmDTO.getBatchTaskItemList(), warehouseId,
            confirmResultDTO)) {
            return confirmResultDTO;
        }

        // 获取拣货任务项关联的订单项
        List<OrderItemTaskInfoDetailPO> updateTaskInfoDetailList = new ArrayList<>();
        List<String> batchTaskItemIdList =
            batchTaskItemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByBatchTaskItemIds(batchTaskItemIdList);
        Map<Long, BigDecimal> taskInfoDetailOverSortCountMap = new HashMap<>();
        Map<Long, BigDecimal> lastTaskInfoLackCountMap = new HashMap<>();
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            LOGGER.info("[确认拣货]获取拣货任务项关联的订单项为空：{}", JSON.toJSONString(batchTaskItemIdList));
        } else {
            // 记录上次已拣数量
            orderItemTaskInfoPOList.forEach(p -> {
                if (CollectionUtils.isNotEmpty(p.getDetailList())) {
                    p.getDetailList().forEach(detail -> {
                        taskInfoDetailOverSortCountMap.put(detail.getId(), detail.getUnitTotalCount());
                    });
                }
                // 记录上次缺货数量
                lastTaskInfoLackCountMap.put(p.getId(), p.getLackUnitCount());
            });
        }

        List<Long> lstOutStockOrders = new ArrayList<>();
        List<PickUpDTO> pickUpDTOList = new ArrayList<>();
        List<BatchTaskItemUpdatePO> poList = new ArrayList<>();
        processTaskCompleteItems(operateUser, batchTaskConfirmDTO, batchTaskItemDTOList, lstOutStockOrders,
            pickUpDTOList, poList, orderItemTaskInfoPOList, updateTaskInfoDetailList, taskInfoDetailOverSortCountMap);

        batchTaskFinishValidateBL.validateIsMeiTuan(orderItemTaskInfoPOList, batchTaskPOList);
        // 校验拣货任务是否标缺货
        batchPOS.forEach(batchPO -> checkIsLack(poList, batchPO.getBatchType()));

        // 1、更新拣货任务及拣货任务项状态
        batchTaskMapper.updateTaskState2Complete(batchTaskNoS, operateUser, batchTaskConfirmDTO.getOperateUserId(),
            batchTaskConfirmDTO.getLocationId(), batchTaskConfirmDTO.getLocationName(),
            batchTaskConfirmDTO.getSorterName(), batchTaskConfirmDTO.getSorterId(), batchTaskConfirmDTO.getOrgId());
        batchTaskItemMapper.updateBatchTaskByItemIncludeLocation(poList, batchTaskConfirmDTO.getOrgId());
        // -添加操作记录（任务拣货完成）
        orderTraceBL.updateBatchTaskTraceBatch(batchTaskPOList, OrderTraceDescriptionEnum.拣货完成.name(), operateUser);

        // 更新拣货任务项关联的订单项的拣货数量和缺货数量
        if (CollectionUtils.isNotEmpty(orderItemTaskInfoPOList)) {
            // 修改播种明细数量
            // List<String> batchItemIds = batchTaskItemDTOList.stream().filter(item -> item.getSowTaskId() !=
            // null).map(BatchTaskItemDTO::getId).collect(Collectors.toList());
            // List<OrderItemTaskInfoPO> sowOrderItemTaskInfoPOS = orderItemTaskInfoPOList.stream().filter(item ->
            // batchItemIds.contains(item.getBatchTaskItemId())).collect(Collectors.toList());
            // updateSowTaskItemCount(sowOrderItemTaskInfoPOS);

            Lists.partition(orderItemTaskInfoPOList, 50).forEach(p -> {
                orderItemTaskInfoMapper.updateBatch(p);
            });
            LOGGER.info("[确认拣货]更新【orderItemTaskInfo】拣货数量和缺货数量：{}", JSON.toJSONString(orderItemTaskInfoPOList));
        }

        // 是否跨库
        boolean isCrossWareHouse = Objects.equals(batchPOS.get(0).getCrossWareHouse(), ConditionStateEnum.是.getType());

        // 2、校验移库
        if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
            if (!isCrossWareHouse) {
                LOGGER.info("校验移库入参：{}", JSON.toJSONString(pickUpDTOList));
                pickUpDTOList = iBatchInventoryManageService.checkInventoryTransferByPickUp(pickUpDTOList);
            }
            // 根据实际移库数量更新出库单项关联明细表
            processOrderItemTaskInfoDetail(orderItemTaskInfoPOList, updateTaskInfoDetailList, pickUpDTOList,
                taskInfoDetailOverSortCountMap);
        }
        // 更新出库单项关联明细表
        if (CollectionUtils.isNotEmpty(updateTaskInfoDetailList)) {
            orderItemTaskInfoDetailMapper.updateBatch(updateTaskInfoDetailList);
            LOGGER.info("[确认拣货]更新【orderItemTaskInfoDetail】分配数量：{}", JSON.toJSONString(updateTaskInfoDetailList));
        }

        List<String> batchTaskIds =
            batchTaskPOList.stream().map(BatchTaskPO::getId).distinct().collect(Collectors.toList());
        List<BatchTaskItemPO> batchTaskItemPOList = batchTaskItemMapper.findBatchTaskItemDTOListByTaskIds(batchTaskIds);
        BatchTaskPO batchTaskPO = batchTaskPOList.stream().findFirst().get();
        batchTaskItemFinishValidateBL.validatePromotionTransferLegal(pickUpDTOList, batchTaskItemPOList, batchTaskPO,
            orderItemTaskInfoPOList);

        // 3、有播种任务的拣货任务项实时标记缺货 FIXME 现在这里的逻辑实际上应该是没走到的
        List<String> batchTaskItemIds = poList.stream().map(p -> p.getId()).collect(Collectors.toList());
        outStockOrderBL.markLackBySowTask(batchTaskItemIds, lastTaskInfoLackCountMap, operateUserId);

        // 4、更新波次和出库单状态
        // 如果task全部已拣货,将batch表的状态修改为已拣货.同时将出库单状态更新为已拣货
        batchPOS.forEach(batchPO -> {
            try {
                batchFinishedBL.completeWave(batchPO.getBatchNo(), operateUserId, batchPO.getOrgId());
                // batchOrderBL.updateBatchStateByBatchNo(batchPO.getBatchNo(), operateUser, operateUserId);
            } catch (HasLockedException e) {
                throw new BusinessValidateException("正在处理，请稍后再试！");
            }
        });

        // 5、按订单拣货时，一个订单拣完时更新出库单状态
        if (!lstOutStockOrders.isEmpty()) {
            batchPOS.forEach(batchPO -> outStockOrderBL.updateStateByOrderIds(lstOutStockOrders, batchPO));
        }

        batchTaskPOList.forEach(updateBatchTaskPO -> {
            updateBatchTaskPO.setSorterId(batchTaskConfirmDTO.getSorterId());
            updateBatchTaskPO.setSorter(batchTaskConfirmDTO.getSorterName());
            updateBatchTaskPO.setCompleteTime(new Date());
        });

        taskPerformanceCalculateBL
            .calculateAndModTaskPerformance(TaskPerformanceCalculateBO.getBatchTaskInstance(batchTaskIds,
                batchTaskConfirmDTO.getSorterId(), batchTaskConfirmDTO.getWarehouseId(), batchTaskConfirmDTO.getOrgId(),
                updateBatchTaskPO -> batchTaskMapper.updateByPrimaryKeySelective(updateBatchTaskPO)));

        // 6、更新批次库存
        // 如果包含播种任务，先挪到集货区
        // 如果不包含播种任务，直接挪到备货区
        // 拣货到周转区
        if (CollectionUtils.isNotEmpty(pickUpDTOList) && !isCrossWareHouse) {
            LOGGER.info("拣货移库入参：{}，batchTaskNoS：{}", JSON.toJSONString(pickUpDTOList), JSON.toJSONString(batchTaskNoS));
            PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
            String orderNo = "";
            if (batchTaskNoS.size() == 1) {
                pickUpChangeRecordDTO.setOrderNo(batchTaskNoS.get(0));
            } else {
                orderNo = "(" + Joiner.on(",").join(batchTaskNoS) + ")";
            }
            pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.供应链确认拣货.name() + orderNo);
            pickUpChangeRecordDTO.setCreateUser(operateUser);
            iBatchInventoryManageService.pickupCompleteBySku(pickUpDTOList, pickUpChangeRecordDTO);
        }
        LOGGER.info("拣货完成");
        return confirmResultDTO;
    }

    /**
     * 根据波次任务编号查找默认片区
     *
     * @param batchTaskNoS
     * @return
     */
    public List<String> findAreaListByTaskNo(List<String> batchTaskNoS) {
        List<String> lstResult = new ArrayList<>();
        if (!CollectionUtils.isEmpty(batchTaskNoS)) {
            lstResult = batchTaskItemMapper.findAreaListByTaskNo(batchTaskNoS);
        }
        return lstResult;
    }

    /**
     * 修改波次任务详情 <br />
     * 现在拆成两个接口：完成拣货任务明细 和 完成拣货任务
     */
    @Deprecated
    @DistributeLock(conditions = "#batchTaskId", sleepMills = 3000, expireMills = 60000,
        key = BatchTaskConstants.BATCH_TASK_COMPLETE_KEY)
    @Transactional(rollbackFor = RuntimeException.class)
    public Map<String, List<BatchTaskDTO>> updateBatchTaskItem(
        List<BatchTaskItemCompleteDTO> batchTaskItemUpdateDTOList, String batchTaskId, String userName,
        Integer warehouseId, Long locationId, String locationName, Integer cityId, Integer userId, Byte containerFlag) {
        LOGGER.info("提交拣货：{}, batchTaskId:{}", JSON.toJSONString(batchTaskItemUpdateDTOList), batchTaskId);
        List<String> updateBatchTaskItemIds =
            batchTaskItemUpdateDTOList.stream().map(BatchTaskItemCompleteDTO::getId).collect(Collectors.toList());
        List<BatchTaskItemPO> oldBatchTaskItemPOS =
            batchTaskItemMapper.listBatchTaskItemByIds(updateBatchTaskItemIds, null);
        if (CollectionUtils.isEmpty(oldBatchTaskItemPOS)) {
            throw new BusinessValidateException("拣货详情不存在，请刷新重试！");
        }
        if (oldBatchTaskItemPOS.size() != updateBatchTaskItemIds.size()) {
            throw new BusinessValidateException("部分拣货详情不存在，请刷新重试！");
        }
        BatchTaskPO batchTaskOld = batchTaskMapper.findBatchTaskById(batchTaskId);
        AssertUtils.notNull(batchTaskOld, "拣货任务不存在！Id：" + batchTaskId);
        if (batchTaskOld.getTaskState() == TaskStateEnum.已完成.getType()) {
            throw new BusinessValidateException("拣货已完成，请勿重复操作！Id：" + batchTaskId);
        }
        if (TaskStateEnum.已作废.valueEquals(batchTaskOld.getTaskState())) {
            LOGGER.info("拣货任务已作废: {}", JSON.toJSONString(batchTaskOld, SerializerFeature.WriteMapNullValue));
            return Collections.emptyMap();
        }
        // 1111111为机器人拣货
        if (!RobotSorterConstants.ROBOT_ID.equals(batchTaskOld.getSorterId())) {
            if (!Objects.equals(userId, batchTaskOld.getSorterId())) {
                throw new BusinessValidateException("提交失败，此拣货任务已经指派给拣货员：" + batchTaskOld.getSorter());
            }
        }
        BatchPO batchPO = batchMapper.selectBatchByBatchNo(
            StringUtils.isEmpty(batchTaskOld.getOrgId()) ? null : Integer.valueOf(batchTaskOld.getOrgId()),
            batchTaskOld.getBatchNo());
        if (batchPO == null) {
            throw new BusinessException("拣货任务对应的波次任务不存在！Id：" + batchTaskId);
        }
        // 前端传过来的出库位跟拣货任务出库位不一样时，提示报错
        if (batchTaskOld.getToLocationId() != null && locationId != null) {
            if (!Objects.equals(batchTaskOld.getToLocationId(), locationId)) {
                LOGGER.info("拣货任务的出库位已变更, ToLocationId: {}, ToLocationName: {}, locationId: {}, locationName: {}",
                    batchTaskOld.getToLocationId(), batchTaskOld.getToLocationName(), locationId, locationName);
                throw new BusinessValidateException("拣货任务的出库位已变更，请回退到待拣货列表重试！");
            }
        }

        batchTaskItemUpdateDTOList =
            batchTaskItemCompleteConvertor.resetBatchTaskItemCompleteDTO(batchTaskItemUpdateDTOList, batchTaskOld);
        LOGGER.info("提交拣货，重组后参数为：{}", JSON.toJSONString(batchTaskItemUpdateDTOList));
        updateBatchTaskItemIds =
            batchTaskItemUpdateDTOList.stream().map(BatchTaskItemCompleteDTO::getId).collect(Collectors.toList());
        // 拣货任务完成检查是否需要托盘信息
        checkIsNeedPalletForStock(batchTaskOld);

        // 获取拣货任务项关联的订单项
        List<OrderItemTaskInfoDetailPO> updateTaskInfoDetailList = new ArrayList<>();
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByBatchTaskItemIds(updateBatchTaskItemIds);
        List<Long> outStockOrderIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findIdByIds(outStockOrderIds, warehouseId);
        // 校验订单能否缺货
        orderConstraintCheckBL.checkNormalOrderPickLack(warehouseId, batchTaskItemUpdateDTOList, outStockOrderPOList,
            orderItemTaskInfoPOList);
        Map<Long, BigDecimal> taskInfoDetailOverSortCountMap = new HashMap<>();
        Map<Long, BigDecimal> lastTaskInfoLackCountMap = new HashMap<>();
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            LOGGER.info("[PDA拣货]获取拣货任务项关联的订单项为空：{}", JSON.toJSONString(updateBatchTaskItemIds));
        } else {
            // 记录上次已拣数量
            orderItemTaskInfoPOList.forEach(p -> {
                if (CollectionUtils.isNotEmpty(p.getDetailList())) {
                    p.getDetailList().forEach(detail -> {
                        taskInfoDetailOverSortCountMap.put(detail.getId(), detail.getUnitTotalCount());
                    });
                }
                // 记录上次缺货数量
                lastTaskInfoLackCountMap.put(p.getId(), p.getLackUnitCount());
            });
        }

        // 查找该波次任务所有商品详情
        List<BatchTaskItemDTO> batchTaskItemDTOList = batchTaskItemMapper.findBatchTaskItemDtoListById(batchTaskId);
        Map<String, BatchTaskItemCompleteDTO> map = new HashMap<>();
        batchTaskItemUpdateDTOList.forEach(p -> {
            map.put(p.getId(), p);
        });
        // 波次任务的状态.默认分拣完成
        byte taskState = TaskStateEnum.已完成.getType();
        List<BatchTaskItemUpdatePO> poList = new ArrayList<>();
        List<Long> lstOutStockOrders = new ArrayList<>();
        List<PackageOrderItemDTO> packageOrderItemDTOList = new ArrayList<>();
        List<PickUpDTO> pickUpDTOList = new ArrayList<>();
        List<BatchTaskCheckSaleSpecDTO> checkSaleSpecDTOList = new ArrayList<>();
        // 拣货任务明细项容器位集合
        List<BatchTaskItemContainerPO> containerPOList = new ArrayList<BatchTaskItemContainerPO>();

        // 获取集货位信息
        List<SowTaskDTO> sowTasks = getSowTasks(batchTaskItemDTOList);

        // 获取集货区
        Long sowLocationId = null;
        if (batchTaskOld.getSowTaskId() != null) {
            Optional<SowTaskDTO> sowTaskDTO =
                sowTasks.stream().filter(p -> p.getId().equals(batchTaskOld.getSowTaskId())).findAny();
            if (sowTaskDTO.isPresent() && sowTaskDTO.get().getLocationId() != null) {
                sowLocationId = sowTaskDTO.get().getLocationId();
            }
        }

        // 分单拣货的合单通知集合
        List<String> orderNos = new ArrayList<>();
        Map<String, List<BatchTaskDTO>> msgMap = new HashMap<>();

        // 查找该波次任务所有出库单详情列表和转换sku列表,后续装箱需用到
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOS = new ArrayList<>();
        // Map<Integer, Map<Long, Long>> actualDeliverySkuIdMap = new HashMap<>();
        List<BatchTaskItemCompleteDTO> packageUpdateList = batchTaskItemUpdateDTOList.stream()
            .filter(dto -> CollectionUtils.isNotEmpty(dto.getBoxCodeList())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(packageUpdateList)) {
            // 查询订单拣货任务关联表
            orderItemTaskInfoPOS = getCurBatchTaskItem(batchTaskItemDTOList, orderItemTaskInfoPOList);
            // LOGGER.info("拣货订单数据:{}", JSON.toJSONString(orderItemTaskInfoPOS));
        }

        for (BatchTaskItemDTO batchTaskItemDTO : batchTaskItemDTOList) {
            // 已完成的项，不需要再处理
            // if (TaskStateEnum.已完成.getType() == batchTaskItemDTO.getTaskState()) {
            // continue;
            // }
            BatchTaskItemCompleteDTO dto = map.get(batchTaskItemDTO.getId());
            // 当存在波次任务中的一个商品详情用户没有对其做改变时,分析该商品
            if (dto == null) {
                // 判断在数据库中的状态是否为已完成
                if (TaskStateEnum.已完成.getType() != batchTaskItemDTO.getTaskState()) {
                    taskState = 分拣中.getType();
                }
                continue;
            }
            // 初次提交时，如果拣货任务项状态不是“未分拣”，则报异常
            if (Objects.equals(dto.getSubmitFlag(), SubmitFlagEnum.初次提交)
                && !Objects.equals(batchTaskItemDTO.getTaskState(), TaskStateEnum.未分拣.getType())) {
                throw new BusinessValidateException("不能重复提交，请刷新重试或者联系技术支持！");
            }
            BigDecimal specQuantity = batchTaskItemDTO.getSpecQuantity();
            // 数据库中拣货数量+用户拣货数量
            BigDecimal overSortCount =
                dto.getOverSortPackageCount().multiply(specQuantity).add(dto.getOverSortUnitCount());
            // 数据库中缺省数量+用户缺省数量
            BigDecimal lackCount = dto.getLackPackageCount().multiply(specQuantity).add(dto.getLackUnitCount());
            if (overSortCount.add(lackCount).compareTo(batchTaskItemDTO.getUnitTotalCount()) != 0) {
                LOGGER.info("已拣货数量 加 缺货数量 不等于 待拣货数量: {}", JSON.toJSONString(batchTaskItemDTO));
                throw new BusinessValidateException("拣货数量有误，请返回拣货任务列表刷新重试！");
            }

            if (StringUtils.isNotEmpty(batchTaskItemDTO.getRefOrderId())
                && PickingTypeEnum.订单拣货.getType() == batchTaskOld.getPickingType()) {
                // 按订单拣货，一次至少完成一个订单
                lstOutStockOrders.add(Long.valueOf(batchTaskItemDTO.getRefOrderId()));
            }
            if (StringUtils.isNotEmpty(batchTaskItemDTO.getRefOrderNo())
                && PickingTypeEnum.订单拣货.getType() == batchTaskOld.getPickingType()) {
                orderNos.add(batchTaskItemDTO.getRefOrderNo());
            }

            // 对用户做改变的商品构建PO
            BatchTaskItemUpdatePO po = new BatchTaskItemUpdatePO();
            po.setId(dto.getId());
            po.setOverSortCount(overSortCount);
            po.setLackCount(lackCount);
            po.setTaskState(TaskStateEnum.已完成.getType());
            po.setCompleteUser(userName);
            po.setCompleteUserId(userId);
            po.setLastUpdateUser(userName);
            if (lackCount.compareTo(BigDecimal.ZERO) > 0) {
                // 缺货
                po.setIsLack((byte)1);
            } else {
                po.setIsLack((byte)0);
            }
            // 来源货位id（如果客户端传过来为空，则取拣货任务详情的货位id）
            Long fromLocationId = dto.getFromLocationId();
            String fromLocationName = dto.getFromLocationName();
            if (Objects.isNull(fromLocationId)) {
                fromLocationId = batchTaskItemDTO.getLocationId();
            }
            if (StringUtils.isBlank(fromLocationName)) {
                fromLocationName = batchTaskItemDTO.getLocationName();
            }
            po.setFromLocationId(fromLocationId);
            po.setFromLocationName(fromLocationName);
            po.setStartTime(dto.getStartTime());
            po.setCompleteTime(dto.getCompleteTime());
            po.setRemark(dto.getRemark());
            if (Objects.equals(batchPO.getSowType(), BatchSowTypeEnum.二次分拣.getType())) {
                po.setSownUnitTotalCount(new BigDecimal(SowTaskStateEnum.待播种.getType()));
            }
            poList.add(po);

            // 处理拣货任务项关联的订单项的已拣货数量和缺货数量
            List<OrderItemTaskInfoPO> nowBatchTaskItemInfoList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
                nowBatchTaskItemInfoList = orderItemTaskInfoPOList.stream()
                    .filter(p -> Objects.equals(p.getBatchTaskItemId(), dto.getId())).collect(Collectors.toList());
                processOrderItemTaskInfoPOList(nowBatchTaskItemInfoList, lackCount, updateTaskInfoDetailList,
                    batchTaskItemDTO, outStockOrderPOList);
            }

            // 移库
            // 开启容器位拣货
            if (Objects.equals(containerFlag, ConditionStateEnum.是.getType())) {
                if (CollectionUtils.isEmpty(dto.getContainerList())) {
                    throw new BusinessException("容器位不能为空");
                }
                List<BatchTaskItemContainerPO> containerList =
                    batchTaskItemContainerMapper.selectByBatchtaskitemId(batchTaskItemDTO.getId());
                /*if (TaskStateEnum.已完成.getType() == batchTaskItemDTO.getTaskState()) {
                	containerMap = batchTaskItemContainerMapper.selectByBatchtaskitemId(batchTaskItemDTO.getId());
                }*/
                for (BatchTaskItemContainerDTO containerDTO : dto.getContainerList()) {
                    BigDecimal pickUnitTotalCount =
                        containerDTO.getPickPackageCount().multiply(specQuantity).add(containerDTO.getPickUnitCount());
                    BatchTaskItemContainerPO containerPO = new BatchTaskItemContainerPO();
                    containerPO.setOrgId(cityId);
                    containerPO.setWarehouseId(warehouseId);
                    containerPO.setBatchtaskitemId(Long.valueOf(batchTaskItemDTO.getId()));
                    containerPO.setLocationId(containerDTO.getLocationId());
                    containerPO.setLocationName(containerDTO.getLocationName());
                    containerPO.setPickUnitTotalCount(pickUnitTotalCount);
                    containerPO.setCreateUserId(userId);
                    containerPO.setLastUpdateUserId(userId);
                    containerPO.setRemark("PDA");

                    // 本次移库小单位数量
                    overSortCount = pickUnitTotalCount;
                    // 重复拣货时，移库数量 = 本次拣货数量 - 上次拣货数量
                    if (containerList != null
                        && containerList.size() > 0/*containerMap.get(containerDTO.getLocationId()) != null*/) {
                        BatchTaskItemContainerPO oldContainerPO = containerList.get(0);
                        fromLocationId = oldContainerPO.getLocationId();
                        containerPO.setId(oldContainerPO.getId());
                        // overSortCount = overSortCount.subtract(oldContainerPO.getPickUnitTotalCount());
                        batchTaskItemContainerMapper.updateByPrimaryKeySelective(containerPO);
                    } else {
                        containerPOList.add(containerPO);
                    }
                    PickUpDTO pickUpDTO = getContainerPickUpDTO(warehouseId, containerDTO.getLocationId(),
                        batchTaskItemDTO, overSortCount, fromLocationId);
                    if (null != pickUpDTO) {
                        pickUpDTOList.add(pickUpDTO);
                    }
                }
            } else {
                if (Objects.equals(batchPO.getSowType(), BatchSowTypeEnum.二次分拣.getType())) {
                    getSecondSortPickUpDTO(pickUpDTOList, warehouseId, batchTaskItemDTO, fromLocationId, dto,
                        sowLocationId, nowBatchTaskItemInfoList, taskInfoDetailOverSortCountMap, sowTasks);
                } else {
                    getPickUpDTO(pickUpDTOList, warehouseId, locationId, batchTaskItemDTO, overSortCount,
                        fromLocationId, dto, sowLocationId, nowBatchTaskItemInfoList, taskInfoDetailOverSortCountMap);
                }
            }

            // 封装装箱详情
            // if (null != dto.getBoxCode()) {
            // // 兼容旧版 - 单箱
            // PackageOrderItemDTO packageOrderItemDTO = getPackageOrderItemDTO(warehouseId, cityId, userId,
            // outStockOrderItemPOList, batchTaskItemDTO, dto.getBoxCode(), dto.getOverSortPackageCount(),
            // dto.getOverSortUnitCount());
            // if (packageOrderItemDTO != null) {
            // packageOrderItemDTOList.add(packageOrderItemDTO);
            // }
            // } else if (CollectionUtils.isNotEmpty(dto.getBoxCodeList())) {
            // // 支持分多箱
            // for (PackageCodeSortDTO boxCodeDTO : dto.getBoxCodeList()) {
            // PackageOrderItemDTO packageOrderItemDTO = getPackageOrderItemDTO(warehouseId, cityId, userId,
            // outStockOrderItemPOList, batchTaskItemDTO, boxCodeDTO.getBoxCode(), boxCodeDTO.getPackageCount(),
            // boxCodeDTO.getUnitCount());
            // if (packageOrderItemDTO != null) {
            // // 合并一个产品存在多个相同箱号
            // List<PackageOrderItemDTO> filterList = packageOrderItemDTOList.stream().filter(q ->
            // String.format("%s-%s-%s-%s", q.getRefOrderId(), q.getRefOrderItemId(), q.getSkuId(), q.getBoxCode())
            // .equals(String.format("%s-%s-%s-%s", packageOrderItemDTO.getRefOrderId(),
            // packageOrderItemDTO.getRefOrderItemId(), packageOrderItemDTO.getSkuId(),
            // packageOrderItemDTO.getBoxCode()))).collect(Collectors.toList());
            // if (CollectionUtils.isEmpty(filterList)) {
            // packageOrderItemDTOList.add(packageOrderItemDTO);
            // } else {
            // filterList.get(0).setPackageCount(filterList.get(0).getPackageCount().add(packageOrderItemDTO.getPackageCount()));
            // filterList.get(0).setUnitCount(filterList.get(0).getUnitCount().add(packageOrderItemDTO.getUnitCount()));
            // filterList.get(0).setUnitTotalCount(filterList.get(0).getUnitTotalCount().add(packageOrderItemDTO.getUnitTotalCount()));
            // }
            // }
            // }
            // }

            // 拣货装箱
            packageOrderItemBL.pickingAndPacking(warehouseId, cityId, userId, userName, orderItemTaskInfoPOS,
                batchTaskItemDTO, dto.getBoxCodeList(), packageOrderItemDTOList);

            // 校验销售规格数量参数
            BatchTaskCheckSaleSpecDTO batchTaskCheckSaleSpecDTO = getCheckSaleSpec(batchTaskItemDTO, overSortCount);
            checkSaleSpecDTOList.add(batchTaskCheckSaleSpecDTO);
        }

        // 校验销售规格
        checkSaleSpec(checkSaleSpecDTOList);

        // 1、修改波次任务详情状态
        if (!CollectionUtils.isEmpty(poList)) {
            // 校验是否标缺货
            checkIsLack(poList, batchPO.getBatchType());

            batchTaskItemMapper.updateBatchTaskByItemIncludeLocation(poList, cityId);
        }
        // 新增拣货明细项容器位
        if (!containerPOList.isEmpty()) {
            containerPOList.forEach(p -> p.setId(UUIDGeneratorUtil.getUUID(BatchTaskItemContainerPO.class.getName())));
            batchTaskItemContainerMapper.insertList(containerPOList);
        }

        // 2、修改波次任务状态
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);
        // 存在集货位时，出库位设置为空
        if (sowLocationId != null) {
            locationId = null;
            locationName = null;
        }
        batchTaskMapper.updateBatchTaskById(batchTaskPO.getId(), taskState, locationId, locationName, null, null, null,
            cityId, null);
        if (taskState == TaskStateEnum.已完成.getType()) {
            // -添加操作记录（完成拣货）
            orderTraceBL.updateBatchTaskTrace(batchTaskPO, OrderTraceDescriptionEnum.拣货完成.name(), userName);
        }
        // 更新拣货任务项关联的订单项的拣货数量和缺货数量
        if (CollectionUtils.isNotEmpty(orderItemTaskInfoPOList)) {
            // 修改播种明细数量
            // List<String> batchItemIds = batchTaskItemDTOList.stream().filter(item -> item.getSowTaskId() !=
            // null).map(BatchTaskItemDTO::getId).collect(Collectors.toList());
            // List<OrderItemTaskInfoPO> sowOrderItemTaskInfoPOS = orderItemTaskInfoPOList.stream().filter(item ->
            // batchItemIds.contains(item.getBatchTaskItemId())).collect(Collectors.toList());
            // updateSowTaskItemCount(sowOrderItemTaskInfoPOS);

            Lists.partition(orderItemTaskInfoPOList, 50).forEach(p -> {
                orderItemTaskInfoMapper.updateBatch(p);
            });
            LOGGER.info("[PDA拣货]更新【orderItemTaskInfo】拣货数量和缺货数量：{}", JSON.toJSONString(orderItemTaskInfoPOList));
        }

        // 是否跨库
        boolean isCrossWareHouse =
            Objects.equals(batchPO.getCrossWareHouse(), ConditionStateEnum.是.getType()) ? true : false;

        // 3、校验移库
        if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
            if (!isCrossWareHouse) {
                LOGGER.info("校验移库入参：{}", JSON.toJSONString(pickUpDTOList));
                pickUpDTOList = iBatchInventoryManageService.checkInventoryTransferByPickUp(pickUpDTOList);
            }
            // 根据实际移库数量更新出库单项关联明细表
            processOrderItemTaskInfoDetail(orderItemTaskInfoPOList, updateTaskInfoDetailList, pickUpDTOList,
                taskInfoDetailOverSortCountMap);
        }
        // 更新出库单项关联明细表
        if (CollectionUtils.isNotEmpty(updateTaskInfoDetailList)) {
            orderItemTaskInfoDetailMapper.updateBatch(updateTaskInfoDetailList);
            LOGGER.info("[PDA拣货]更新【orderItemTaskInfoDetail】分配数量：{}", JSON.toJSONString(updateTaskInfoDetailList));
        }

        // 验证移库数量
        batchTaskItemFinishValidateBL.validatePromotionTransfer(pickUpDTOList, oldBatchTaskItemPOS, batchTaskOld,
            outStockOrderPOList);

        // 4、有播种任务的拣货任务项实时标记缺货
        List<String> batchTaskItemIds = poList.stream().map(p -> p.getId()).collect(Collectors.toList());

        /** 二次分拣，不实时同步oms */
        if (!Objects.equals(batchPO.getSowType(), BatchSowTypeEnum.二次分拣.getType())) {
            outStockOrderBL.markLackBySowTask(batchTaskItemIds, lastTaskInfoLackCountMap, userId);
        }

        // 5、保存装箱信息
        if (CollectionUtils.isNotEmpty(packageOrderItemDTOList)) {
            packageOrderItemBL.savePackageBatch(packageOrderItemDTOList, true);
        }
        if (CollectionUtils.isEmpty(packageUpdateList)) {
            handleAllPackageRemove(orderItemTaskInfoPOList, batchTaskItemDTOList, map);
        }

        // 6、修改波次状态
        try {
            batchFinishedBL.completeWave(batchTaskOld.getBatchNo(), userId, Integer.valueOf(batchTaskOld.getOrgId()));
            // batchOrderBL.updateBatchStateByBatchNo(batchTaskOld.getBatchNo(), userName, userId);
        } catch (HasLockedException e) {
            throw new BusinessValidateException("正在处理，请稍后再试！");
        }

        // 7、按订单拣货时，一个订单拣完时更新出库单状态
        if (lstOutStockOrders.size() > 0) {
            outStockOrderBL.updateStateByOrderIds(lstOutStockOrders, batchPO);
        }

        // 8、分单拣货(整单完成需保存打印数据，整单未完成通知PDA将拆分出去的单合过来)
        if (batchTaskPO.getPickingType() == PickingTypeEnum.订单拣货.getType() && CollectionUtils.isNotEmpty(orderNos)) {
            orderNos = orderNos.stream().distinct().collect(Collectors.toList());
            msgMap =
                batchOrderBL.separateOrderPicking(batchTaskId, map.keySet(), orderNos, userName, warehouseId, cityId);
        }
        batchTaskFinishValidateBL.validateIsMeiTuan(orderItemTaskInfoPOList, Collections.singletonList(batchTaskPO));
        // 9、移库操作
        if (CollectionUtils.isNotEmpty(pickUpDTOList) && !isCrossWareHouse) {
            LOGGER.info("拣货移库入参：{}，batchTaskNO：{}", new Gson().toJson(pickUpDTOList), batchTaskOld.getBatchTaskNo());
            PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
            pickUpChangeRecordDTO.setCityId(cityId);
            pickUpChangeRecordDTO.setOrderId(batchTaskOld.getId());
            pickUpChangeRecordDTO.setOrderNo(batchTaskOld.getBatchTaskNo());
            pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.PDA提交拣货.name());
            pickUpChangeRecordDTO.setCreateUser(userName);
            iBatchInventoryManageService.pickupCompleteBySku(pickUpDTOList, pickUpChangeRecordDTO);
        }
        // 二次分拣拣货任务，则缺货更新出库单
        if (Objects.equals(batchPO.getSowType(), BatchSowTypeEnum.二次分拣.getType())) {
            updateSecondSow(poList, orderItemTaskInfoPOList, batchPO, batchTaskOld, taskState);
        }
        return msgMap;
    }

    public List<OrderItemTaskInfoPO> getCurBatchTaskItem(List<BatchTaskItemDTO> batchTaskItemDTOList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOS;
        List<String> batchTaskItemIds =
            batchTaskItemDTOList.stream().map(BatchTaskItemDTO::getId).collect(Collectors.toList());
        // 查询订单拣货任务关联表
        orderItemTaskInfoPOS = orderItemTaskInfoPOList.stream()
            .filter(item -> batchTaskItemIds.contains(item.getBatchTaskItemId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOS)) {
            // 兼容旧数据
            orderItemTaskInfoPOS = outStockOrderItemMapper.listOrderItemTaskInfoByBatchTaskItemIds(batchTaskItemIds);
        }

        return orderItemTaskInfoPOS;
    }

    public void handleAllPackageRemove(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        List<BatchTaskItemDTO> batchTaskItemList, Map<String, BatchTaskItemCompleteDTO> map) {
        List<OrderItemTaskInfoPO> currOrderItemTaskInfoPOS =
            getCurBatchTaskItem(batchTaskItemList, orderItemTaskInfoPOList);
        List<BatchTaskItemDTO> allLackBatchTaskItemList = filterAllLackBatchTaskItemList(batchTaskItemList, map);
        if (CollectionUtils.isEmpty(allLackBatchTaskItemList)) {
            return;
        }
        packageOrderItemBL.handleAllPackageRemove(currOrderItemTaskInfoPOS, allLackBatchTaskItemList);
    }

    private List<BatchTaskItemDTO> filterAllLackBatchTaskItemList(List<BatchTaskItemDTO> batchTaskItemList,
        Map<String, BatchTaskItemCompleteDTO> map) {
        return batchTaskItemList.stream().filter(item -> Objects.nonNull(map.get(item.getId()))).filter(item -> {
            BatchTaskItemCompleteDTO updateItem = map.get(item.getId());
            return BigDecimal.ZERO.compareTo(updateItem.getOverSortPackageCount()) == 0
                && BigDecimal.ZERO.compareTo(updateItem.getOverSortUnitCount()) == 0;
        }).collect(Collectors.toList());
    }

    /**
     * 二次分拣拣货任务，把拣货缺货的数量分摊到出库单项上
     */
    @Deprecated
    public void updateSecondSow(List<BatchTaskItemUpdatePO> batchItemUpdateList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, BatchPO batchPO, BatchTaskPO batchTaskPO, byte taskState) {
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList) || CollectionUtils.isEmpty(batchItemUpdateList)
            || batchPO == null) {
            return;
        }

        // 根据出库单明细项，找到对应的托盘项，更新已完成小单位总数量，取出库单实际出库数量
        updateSecondSortByOutOrderItem(orderItemTaskInfoPOList, batchPO);
        if (taskState == TaskStateEnum.已完成.getType()) {
            List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper
                .listOutStockOrderByBatchTaskId(batchTaskPO.getId(), batchPO.getOrgId(), batchTaskPO.getWarehouseId());
            List<Long> lstOutStockOrders =
                outStockOrderPOS.stream().filter(it -> !it.getState().equals((int)OutStockOrderStateEnum.已拣货.getType()))
                    .map(OutStockOrderPO::getId).collect(Collectors.toList());
            outStockOrderBL.updateStateByOrderIds(lstOutStockOrders, batchPO);
        }
    }

    /**
     * 根据播种任务找到出库单项，在根据关联关系找到缺货数量，更新播种项缺货数量
     *
     * @param orgId
     * @param sowTaskItemPO
     */
    private void updateSecondSowTaskItemBySowTaskNo(Integer orgId, SowTaskItemPO sowTaskItemPO) {
        /** 根据播种任务找到出库单项 */
        List<OutStockOrderItemDTO> outStockOrderItems =
            outStockOrderItemMapper.findBySowTaskNo(sowTaskItemPO.getSowTaskNo(), orgId);
        List<Long> outStockItemIds = outStockOrderItems.stream()
            .filter(it -> it.getSowTaskItemId() != null && it.getSowTaskItemId().equals(sowTaskItemPO.getId()))
            .map(OutStockOrderItemDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outStockItemIds)) {
            return;
        }
        /** 在根据关联关系找到缺货数量，更新播种项缺货数量 */
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOS =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderItemIds(outStockItemIds);
        BigDecimal lackUnitCount = orderItemTaskInfoPOS.stream().filter(it -> it.getLackUnitCount() != null)
            .map(OrderItemTaskInfoPO::getLackUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        sowTaskItemPO.setLackUnitTotalCount(lackUnitCount);
        sowTaskItemPO
            .setOverUnitTotalCount(sowTaskItemPO.getUnitTotalCount().subtract(sowTaskItemPO.getLackUnitTotalCount()));
        sowTaskItemMapper.updateByPrimaryKeySelective(sowTaskItemPO);
    }

    /**
     * 根据出库单明细项，找到对应的托盘项，更新已完成小单位总数量，取出库单项实际出库数量
     *
     * @param orderItemTaskInfoPOList
     * @param batchPO
     */
    private void updateSecondSortByOutOrderItem(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, BatchPO batchPO) {
        List<BatchPO> batchPOList = batchMapper.listByBatchNos(Arrays.asList(batchPO.getBatchNo()));
        if (CollectionUtils.isEmpty(batchPOList)) {
            return;
        }
        BatchPO newBatch = batchPOList.get(0);
        // 已拣货和已出库的不用重复更新
        if (Objects.equals(BatchStateEnum.PICKINGEND.getType().byteValue(), batchPO.getState())
            || Objects.equals(BatchStateEnum.ALREADYOUTOFSTORE.getType().byteValue(), batchPO.getState())) {
            return;
        }
        Integer orgId = batchPO.getOrgId();
        String batchTaskNo = orderItemTaskInfoPOList.get(0).getBatchTaskNo();

        // 根据出库单明细项，找到对应的托盘项，更新已完成小单位总数量，取出库单实际出库数量
        List<Long> pickOutItemIds = orderItemTaskInfoPOList.stream().filter(it -> it.getRefOrderItemId() != null)
            .map(it -> it.getRefOrderItemId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickOutItemIds)) {
            LOGGER.info("[二次分拣]关联拣货信息无出库单项信息，拣货任务号：{}", batchTaskNo);
            return;
        }

        List<Long> pickOrderIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderId)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickOrderIds)) {
            LOGGER.info("[二次分拣]关联拣货信息无出库单信息，拣货任务号：{}", batchTaskNo);
            return;
        }
        // 正在分拣的出库单项
        OutStockOrderItemQuerySO itemQuerySO = new OutStockOrderItemQuerySO();
        itemQuerySO.setOrgId(orgId);
        itemQuerySO.setIds(pickOutItemIds);
        itemQuerySO.setPageSize(Integer.MAX_VALUE);
        List<OutStockOrderItemDTO> pickOutItemList = outStockOrderItemMapper.findItemAndDetailByIds(itemQuerySO);
        if (CollectionUtils.isEmpty(pickOutItemList)) {
            LOGGER.info("[二次分拣]无出库单项信息，拣货任务号：{}", batchTaskNo);
            return;
        }

        // 查询所有出库单信息
        OutStockOrderSearchSO outQuerySO = new OutStockOrderSearchSO();
        outQuerySO.setOrgId(orgId);
        outQuerySO.setOrderIds(pickOrderIds);
        List<OutStockOrderPO> outOrderList = outStockOrderMapper.listAllInfo(outQuerySO);
        if (CollectionUtils.isEmpty(outOrderList)) {
            LOGGER.info("[二次分拣]无出库单信息，拣货任务号：{}", batchTaskNo);
            return;
        }

        // 获取每一个出库单项的缺货数量
        Map<Long, OrderItemTaskInfoPO> outStockMap = orderItemTaskInfoPOList.stream()
            .collect(Collectors.toMap(OrderItemTaskInfoPO::getRefOrderItemId, it -> it, (v1, v2) -> v1));
        Set<Long> existsOutItemIdList = outStockMap.keySet();
        Map<Long, BigDecimal> outItemLackMap = new HashMap<>();
        pickOutItemList.stream().filter(it -> existsOutItemIdList.contains(it.getId())).forEach(it -> {
            OrderItemTaskInfoPO orderItemTaskInfoPO = outStockMap.get(it.getId());
            if (orderItemTaskInfoPO == null) {
                return;
            }
            outItemLackMap.put(it.getId(), orderItemTaskInfoPO.getLackUnitCount());
        });

        if (outItemLackMap.size() == 0) {
            LOGGER.info("[二次分拣]无拣货缺货，拣货任务号：{}", batchTaskNo);
            return;
        }
        LOGGER.info("[二次分拣]拣货缺货数量：{}", outItemLackMap);
        // 分配缺货数量到出库单
        Map<Long, List<OutStockOrderItemDTO>> pickItemByOrderGroup =
            pickOutItemList.stream().collect(Collectors.groupingBy(OutStockOrderItemDTO::getOutstockorder_Id));

        // 拣货数量是否有变化
        boolean isCountChange = false;
        List<OutStockOrderPO> updateOutOrdList = new ArrayList<>();
        List<OutStockOrderItemPO> updateOutItemList = new ArrayList<>();
        List<OutStockOrderItemDetailPO> addOutDetailList = new ArrayList<>();
        for (OutStockOrderPO ord : outOrderList) {
            OutStockOrderPO updateOrd = new OutStockOrderPO();
            updateOrd.setId(ord.getId());
            BigDecimal packageAmount = BigDecimal.ZERO;
            BigDecimal unitAmount = BigDecimal.ZERO;

            List<OutStockOrderItemDTO> curPickItemList = pickItemByOrderGroup.get(ord.getId());
            if (CollectionUtils.isEmpty(curPickItemList)) {
                continue;
            }
            // 正在分拣的
            for (OutStockOrderItemDTO curItem : curPickItemList) {
                BigDecimal curItemLackUnitCount = outItemLackMap.get(curItem.getId());
                BigDecimal unitTotalCount = curItem.getOriginalUnitTotalCount()
                    .subtract(curItemLackUnitCount == null ? BigDecimal.ZERO : curItemLackUnitCount);

                BigDecimal[] counts = unitTotalCount.divideAndRemainder(curItem.getSpecQuantity());

                OutStockOrderItemPO updatePO = new OutStockOrderItemPO();
                updatePO.setId(curItem.getId());
                updatePO.setUnittotalcount(unitTotalCount);
                updatePO.setPackagecount(counts[0]);
                updatePO.setUnitcount(counts[1]);
                updatePO.setRemark(String.valueOf(SowTaskStateEnum.待播种.getType()));
                updatePO.setSowTaskItemId(curItem.getSowTaskItemId());
                updatePO.setCurItemLackUnitCount(curItemLackUnitCount);
                updatePO.setOutstockorderId(ord.getId());
                updateOutItemList.add(updatePO);

                curItem.setUnitTotalCount(updatePO.getUnittotalcount());
                curItem.setPackageCount(updatePO.getPackagecount());
                curItem.setUnitCount(updatePO.getUnitcount());

                // 出库单项detail
                if (CollectionUtils.isNotEmpty(curItem.getOutStockOrderItemDetailDTOS())) {
                    List<OutStockOrderItemDetailPO> newDetailList =
                        outStockOrderBL.getOutItemDetailByOrderItem(curItem, curItem.getOrg_id(), null);
                    if (CollectionUtils.isNotEmpty(newDetailList)) {
                        addOutDetailList.addAll(newDetailList);
                    }
                }

                packageAmount = packageAmount.add(curItem.getPackageCount());
                unitAmount = unitAmount.add(curItem.getUnitCount());
            }

            // 非正在分拣的
            for (OutStockOrderItemPO oldItem : ord.getItems().stream()
                .filter(ite -> !pickOutItemIds.contains(ite.getId())).collect(Collectors.toList())) {
                packageAmount = packageAmount.add(oldItem.getPackagecount());
                unitAmount = unitAmount.add(oldItem.getUnitcount());
            }

            updateOrd.setPackageamount(packageAmount);
            updateOrd.setUnitamount(unitAmount);

            updateOutOrdList.add(updateOrd);

            if (packageAmount.compareTo(ord.getPackageamount()) != 0
                || unitAmount.compareTo(ord.getUnitamount()) != 0) {
                isCountChange = true;
            }
        }
        if (isCountChange) {
            // 更新出库单相关信息
            // if (CollectionUtils.isNotEmpty(addOutDetailList)) {
            // List<Long> delDetailIdList =
            // addOutDetailList.stream().map(OutStockOrderItemDetailPO::getId).collect(Collectors.toList());
            // LOGGER.info("[二次分拣]删除的出库单项detail：{}", JSON.toJSONString(delDetailIdList));
            // Lists.partition(delDetailIdList, 100)
            // .forEach(itemPart -> outStockOrderItemDetailCommMapper.deleteByItemDetailIds(itemPart, orgId));
            //
            // LOGGER.info("[二次分拣]更新的出库单项detail：{}", JSON.toJSONString(addOutDetailList));
            // Lists.partition(addOutDetailList, 100)
            // .forEach(itemPart -> outStockOrderItemDetailCommMapper.insertBatch(itemPart));
            // }

            if (CollectionUtils.isNotEmpty(updateOutItemList)) {
                LOGGER.info("[二次分拣]更新的出库单项：{}", JSON.toJSONString(updateOutItemList));
                Lists.partition(updateOutItemList, 50)
                    .forEach(itemPart -> outStockOrderItemMapper.updateBatchByPOList(itemPart));
                updateSowTaskItem(orgId, updateOutItemList);
            }

            LOGGER.info("[二次分拣]更新的出库单：{}", JSON.toJSONString(updateOutOrdList));
            outStockOrderStateBL.updateBatchByPOList(updateOutOrdList);
        }
    }

    private void updateSowTaskItem(Integer orgId, List<OutStockOrderItemPO> updateOutItemList) {
        updateOutItemList =
            updateOutItemList.stream().filter(m -> Objects.nonNull(m.getSowTaskItemId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateOutItemList)) {
            return;
        }
        // 只要修改出库单想 就需要修改播种项
        Map<Long, List<OutStockOrderItemPO>> collect =
            updateOutItemList.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getSowTaskItemId));
        // 获取所有的播种任务项
        List<SowTaskItemPO> sowTaskItemPOList = sowTaskItemMapper.findByItemIds(orgId, updateOutItemList.stream()
            .map(OutStockOrderItemPO::getSowTaskItemId).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(sowTaskItemPOList)) {
            return;
        }
        // 减去播种项数量
        for (SowTaskItemPO sowTaskItemPO : sowTaskItemPOList) {
            if (Objects.isNull(sowTaskItemPO.getOverUnitTotalCount())) {
                Map<Long, BigDecimal> updateSowItemMap = new HashMap<>();
                Set<Long> keySet = collect.keySet();
                for (Long key : keySet) {
                    // TODO 这里会报空指针
                    List<OutStockOrderItemPO> outStockOrderItemPOS = collect.get(key);
                    updateSowItemMap.put(key, outStockOrderItemPOS.stream().map(OutStockOrderItemPO::getUnittotalcount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                BigDecimal bigDecimal = updateSowItemMap.get(sowTaskItemPO.getId());
                if (BigDecimal.ZERO.compareTo(bigDecimal) == 0) {
                    // 没有开始播种 但是修改订单数量跟播种数量已经全缺
                    sowTaskItemPO.setUnitTotalCount(BigDecimal.ZERO);
                    sowTaskItemPO.setState(SowTaskStateEnum.已播种.getType());
                    LOGGER.info("全缺修改播种任务 id={}", sowTaskItemPO.getId());
                    sowTaskItemMapper.updateByPrimaryKeySelective(sowTaskItemPO);
                } else {
                    // 没有开始播种 但是修改订单数量跟播种数量已经全缺
                    sowTaskItemPO.setUnitTotalCount(bigDecimal);
                    LOGGER.info("部分缺修改播种任务 id={}", sowTaskItemPO.getId());
                    sowTaskItemMapper.updateByPrimaryKeySelective(sowTaskItemPO);
                }
            } else {
                Map<Long, BigDecimal> updateSowItemMap = new HashMap<>();
                Set<Long> keySet = collect.keySet();
                for (Long key : keySet) {
                    List<OutStockOrderItemPO> outStockOrderItemPOS = collect.get(key);
                    updateSowItemMap.put(key, outStockOrderItemPOS.stream()
                        .map(OutStockOrderItemPO::getCurItemLackUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                BigDecimal bigDecimal = updateSowItemMap.get(sowTaskItemPO.getId());
                BigDecimal add = bigDecimal.add(sowTaskItemPO.getOverUnitTotalCount());
                if (add.compareTo(sowTaskItemPO.getUnitTotalCount()) == 0) {
                    sowTaskItemPO.setUnitTotalCount(sowTaskItemPO.getOverUnitTotalCount());
                    sowTaskItemPO.setState(SowTaskStateEnum.已播种.getType());
                    LOGGER.info("部分缺修改播种任务 id={}", sowTaskItemPO.getId());
                    sowTaskItemMapper.updateByPrimaryKeySelective(sowTaskItemPO);
                    continue;
                }
                sowTaskItemPO.setUnitTotalCount(
                    sowTaskItemPO.getUnitTotalCount().subtract(sowTaskItemPO.getOverUnitTotalCount()));
                // 正常缺货修改数据
                sowTaskItemMapper.updateByPrimaryKeySelective(sowTaskItemPO);
            }
        }
        // 获取所有的播种任务项
        List<SowTaskItemPO> sowTaskItemPOListNew =
            sowTaskItemMapper.findBySowTaskNo(orgId, sowTaskItemPOList.get(0).getSowTaskId());
        if (sowTaskItemPOListNew.stream().allMatch(s -> s.getState().compareTo(SowTaskStateEnum.已播种.getType()) == 0)) {
            // 修改播种任务为已完成
            sowTaskMapper.updateSowTaskState(sowTaskItemPOListNew.get(0).getSowTaskNo(), SowTaskStateEnum.已播种.getType(),
                orgId);
        }
    }

    /**
     * 根据实际移库数量更新出库单项关联明细表
     */
    @Deprecated
    public void processOrderItemTaskInfoDetail(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        List<OrderItemTaskInfoDetailPO> updateTaskInfoDetailList, List<PickUpDTO> realPickUpDTOList,
        Map<Long, BigDecimal> taskInfoDetailOverSortCountMap) {
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList) || CollectionUtils.isEmpty(realPickUpDTOList)) {
            return;
        }
        LOGGER.info("[拣货]实际移库数量：{}", JSON.toJSONString(realPickUpDTOList));
        // 新增明细
        List<OrderItemTaskInfoDetailPO> addTaskInfoDetailList = new ArrayList<>();
        // 实际移库数量按关联ID分组
        Map<String, List<PickUpDTO>> pickUpMap =
            realPickUpDTOList.stream().filter(p -> StringUtils.isNotEmpty(p.getBusinessId()))
                .collect(Collectors.groupingBy(p -> p.getBusinessId()));
        if (pickUpMap == null || pickUpMap.isEmpty()) {
            LOGGER.info("[拣货]兼容老数据，不更新出库单项关联明细表");
            return;
        }
        pickUpMap.forEach((taskInfoId, pickList) -> {
            if (CollectionUtils.isEmpty(pickList)) {
                return;
            }
            // 1、找出对应的出库单项的关联
            Optional<OrderItemTaskInfoPO> optionalInfo = orderItemTaskInfoPOList.stream()
                .filter(p -> Objects.equals(p.getId(), Long.valueOf(taskInfoId))).findFirst();
            if (!optionalInfo.isPresent()) {
                LOGGER.info("根据移库数据找不到出库单项关联表:{}", taskInfoId);
                return;
            }
            List<OrderItemTaskInfoDetailPO> taskInfoDetailPOS = optionalInfo.get().getDetailList();
            if (CollectionUtils.isEmpty(taskInfoDetailPOS)) {
                LOGGER.info("出库单项关联明细为空：{}" + taskInfoId);
                return;
            }
            // 是否第一次提交
            boolean isFirstSubmit = true;
            if (Objects.equals(optionalInfo.get().getBatchTaskItemState(), TaskStateEnum.已完成.getType())) {
                isFirstSubmit = false;
            }

            // 2、统计不同货主的移库数量
            Map<Long, BigDecimal> secOwnerIdMap = new HashMap<>();
            // 记录本次移库操作时不同货主的移库数量
            pickList.forEach(pick -> {
                addSecOwnerIdMap(secOwnerIdMap, pick.getSecOwnerId(), pick.getCount());
            });
            // 若不是第一次提交，则需要把上次的移库数量加上
            if (!isFirstSubmit && taskInfoDetailOverSortCountMap != null) {
                taskInfoDetailOverSortCountMap.forEach((detailId, overCount) -> {
                    Optional<OrderItemTaskInfoDetailPO> optionalDetail =
                        taskInfoDetailPOS.stream().filter(p -> Objects.equals(p.getId(), detailId)).findFirst();
                    if (optionalDetail.isPresent()) {
                        LOGGER.warn("非第一次拣货，加了上一次的数量: {} ; 二级货主id : {} ； secOwnerIdMap : {}",
                            JSON.toJSONString(secOwnerIdMap), optionalDetail.get().getSecOwnerId(), overCount);
                        addSecOwnerIdMap(secOwnerIdMap, optionalDetail.get().getSecOwnerId(), overCount);
                    }
                });
            }

            // 3、根据不同货主的移库数量更新关联明细的分配数量
            // （1）原来分配的二级货主没有进行实际移库，则把分配数量改为0
            taskInfoDetailPOS.stream().filter(p -> !secOwnerIdMap.containsKey(p.getSecOwnerId())).forEach(p -> {
                addUpdateTaskInfoDetailList(updateTaskInfoDetailList, p.getId(), BigDecimal.ZERO);
            });
            // （2）根据实际移库二级货主，若之前存在，则更新数量，不存在则新增
            secOwnerIdMap.forEach((secOwnerId, count) -> {
                Optional<OrderItemTaskInfoDetailPO> optionalDetail =
                    taskInfoDetailPOS.stream().filter(p -> Objects.equals(p.getSecOwnerId(), secOwnerId)).findFirst();
                if (optionalDetail.isPresent()) {
                    addUpdateTaskInfoDetailList(updateTaskInfoDetailList, optionalDetail.get().getId(), count);
                } else {
                    OrderItemTaskInfoDetailPO newDetailPO = new OrderItemTaskInfoDetailPO();
                    newDetailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.ORDER_ITEM_TASK_INFO_DETAIL));
                    newDetailPO.setOrgId(optionalInfo.get().getOrgId());
                    newDetailPO.setTaskInfoId(optionalInfo.get().getId());
                    newDetailPO.setSecOwnerId(secOwnerId);
                    newDetailPO.setUnitTotalCount(count);
                    newDetailPO.setOwnerId(taskInfoDetailPOS.get(0).getOwnerId());
                    newDetailPO.setProductSpecificationId(taskInfoDetailPOS.get(0).getProductSpecificationId());
                    addTaskInfoDetailList.add(newDetailPO);
                }
            });
        });

        if (CollectionUtils.isNotEmpty(addTaskInfoDetailList)) {
            Lists.partition(addTaskInfoDetailList, 100).forEach(p -> {
                orderItemTaskInfoDetailMapper.insertBatch(p);
            });
            LOGGER.info("[拣货]新增拣货任务项关联的订单项明细分配数量：{}", JSON.toJSONString(addTaskInfoDetailList));
        }
    }

    private void addSecOwnerIdMap(Map<Long, BigDecimal> secOwnerIdMap, Long secOwnerId, BigDecimal count) {
        if (secOwnerIdMap != null) {
            if (secOwnerIdMap.containsKey(secOwnerId)) {
                count = count.add(secOwnerIdMap.get(secOwnerId));
            }
            secOwnerIdMap.put(secOwnerId, count);
        }
    }

    /**
     * 记录需要更新的关联明细项
     */
    private void addUpdateTaskInfoDetailList(List<OrderItemTaskInfoDetailPO> updateTaskInfoDetailList,
        Long taskInfoDetailId, BigDecimal count) {
        if (taskInfoDetailId == null) {
            return;
        }
        // 已存在，则更新数量
        if (updateTaskInfoDetailList.stream().anyMatch(p -> Objects.equals(p.getId(), taskInfoDetailId))) {
            OrderItemTaskInfoDetailPO oldDetailPO = updateTaskInfoDetailList.stream()
                .filter(p -> Objects.equals(p.getId(), taskInfoDetailId)).findFirst().get();
            oldDetailPO.setUnitTotalCount(count);
        } else {
            OrderItemTaskInfoDetailPO updateDetail = new OrderItemTaskInfoDetailPO();
            updateDetail.setId(taskInfoDetailId);
            updateDetail.setUnitTotalCount(count);
            updateTaskInfoDetailList.add(updateDetail);
        }
    }

    /**
     * 修改播种任务明细数据
     *
     * @param sowOrderItemTaskInfoPOS
     */
    private void updateSowTaskItemCount(List<OrderItemTaskInfoPO> sowOrderItemTaskInfoPOS) {
        if (CollectionUtils.isEmpty(sowOrderItemTaskInfoPOS)) {
            return;
        }
        LOGGER.info("修改播种任务明细参数:{}", JSON.toJSONString(sowOrderItemTaskInfoPOS));
        List<Long> orderItemIds = sowOrderItemTaskInfoPOS.stream().map(OrderItemTaskInfoPO::getRefOrderItemId)
            .distinct().collect(Collectors.toList());
        List<SowTaskItemPO> sowTaskItemPOS = sowTaskItemMapper.findByOrderItemIds(orderItemIds);
        LOGGER.info("修改播种任务明细,查询明细:{}", JSON.toJSONString(sowTaskItemPOS));
        if (CollectionUtils.isNotEmpty(sowTaskItemPOS)) {
            List<SowTaskItemUpdateDTO> updateSowTaskItems = new ArrayList<>();
            List<Long> sowTaskIds = new ArrayList<>();
            sowTaskItemPOS.stream().collect(Collectors.groupingBy(SowTaskItemPO::getId))
                .forEach((sowTaskItemId, list) -> {
                    SowTaskItemUpdateDTO update = new SowTaskItemUpdateDTO();
                    update.setSowTaskItemId(sowTaskItemId);
                    List<Long> itemIds =
                        list.stream().map(SowTaskItemPO::getOrderItemId).distinct().collect(Collectors.toList());
                    BigDecimal lackUnitCount =
                        sowOrderItemTaskInfoPOS.stream().filter(item -> itemIds.contains(item.getRefOrderItemId()))
                            .map(OrderItemTaskInfoPO::getLackUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal unitTotalCount = list.get(0).getUnitTotalCount().subtract(lackUnitCount);
                    update.setUnitTotalCount(unitTotalCount);
                    BigDecimal[] divideAndRemainder = unitTotalCount.divideAndRemainder(list.get(0).getSpecQuantity());
                    update.setPackageCount(divideAndRemainder[0]);
                    update.setUnitCount(divideAndRemainder[1]);
                    update.setState(list.get(0).getState());
                    if (unitTotalCount.compareTo(BigDecimal.ZERO) == 0) {
                        update.setState(SowTaskStateEnum.已播种.getType());
                        sowTaskIds.add(list.get(0).getSowTaskId());
                    }

                    updateSowTaskItems.add(update);
                });
            LOGGER.info("修改播种任务明细:{}", JSON.toJSONString(updateSowTaskItems));
            sowTaskItemMapper.batchUpdateItem(updateSowTaskItems);

            if (CollectionUtils.isNotEmpty(sowTaskIds)) {
                List<Long> completeSowTaskIds = new ArrayList<>();
                List<SowTaskItemPO> sowTaskItems =
                    sowTaskItemMapper.findBySowTaskIds(sowTaskIds.stream().distinct().collect(Collectors.toList()));
                sowTaskItems.stream().collect(Collectors.groupingBy(SowTaskItemPO::getSowTaskId))
                    .forEach((sowTaskId, list) -> {
                        List<SowTaskItemPO> taskItemPOS =
                            list.stream().filter(item -> item.getState() != SowTaskStateEnum.已播种.getType())
                                .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(taskItemPOS)) {
                            completeSowTaskIds.add(sowTaskId);
                        }
                    });

                if (CollectionUtils.isNotEmpty(completeSowTaskIds)) {
                    sowTaskMapper.completeByIds(completeSowTaskIds);
                }
            }
        }
    }

    /**
     * 处理拣货任务项关联的订单项的已拣货数量和缺货数量
     *
     * @return
     */
    public void processOrderItemTaskInfoPOList(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, BigDecimal lackCount,
        List<OrderItemTaskInfoDetailPO> updateTaskInfoDetailList, BatchTaskItemDTO batchTaskItemDTO,
        List<OutStockOrderPO> outStockOrderPOList) {
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return;
        }
        // 拣货任务项关联的订单项明细
        List<OrderItemTaskInfoDetailPO> taskInfoDetailPOList = new ArrayList<>();
        for (OrderItemTaskInfoPO taskInfoPO : orderItemTaskInfoPOList) {
            if (CollectionUtils.isNotEmpty(taskInfoPO.getDetailList())) {
                taskInfoDetailPOList.addAll(taskInfoPO.getDetailList());
            }
        }

        List<OrderItemTaskInfoDetailLackHelperBO> boList = OrderItemTaskInfoDetailLackHelperBOConvertor
            .convert(orderItemTaskInfoPOList, outStockOrderPOList, batchTaskItemDTO);

        if (CollectionUtils.isNotEmpty(taskInfoDetailPOList)) {

            // 重复拣货时，缺货移库数量 = 本次缺货数量 - 上次缺货数量
            if (TaskStateEnum.已完成.getType() == batchTaskItemDTO.getTaskState()) {
                lackCount = lackCount.subtract(batchTaskItemDTO.getLackUnitCount());
            }

            // 增加移库数量（为了后面把移库数量清0）
            BigDecimal moveCount =
                orderItemTaskInfoPOList.stream().map(p -> p.getMoveCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            lackCount = lackCount.add(moveCount);

            // 分配缺货数量
            if (lackCount.compareTo(BigDecimal.ZERO) != 0) {
                LOGGER.info("[拣货缺货前]：{}", JSON.toJSONString(taskInfoDetailPOList));
                // 缺货优先级排序
                Integer cityId = orderItemTaskInfoPOList.get(0).getOrgId();
                taskInfoDetailPOList = sortOrderItemTaskInfoDetail(taskInfoDetailPOList, boList, cityId);

                // 分配缺货数量
                for (OrderItemTaskInfoDetailPO detail : taskInfoDetailPOList) {
                    if (lackCount.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    // 当前明细项的实际缺货数量
                    BigDecimal nowLackCount = lackCount;
                    // 当前明细项可分配的数量
                    BigDecimal allotCount = detail.getUnitTotalCount();
                    // 缺货数量小于0，说明重复拣货，缺货数量变少了，需要增加明细项的分配数量
                    if (lackCount.compareTo(BigDecimal.ZERO) < 0) {
                        Optional<OrderItemTaskInfoPO> optional = orderItemTaskInfoPOList.stream()
                            .filter(p -> Objects.equals(p.getId(), detail.getTaskInfoId())).findFirst();
                        if (optional.isPresent()) {
                            // 限制最多增加当前订单项的缺货数量
                            allotCount = optional.get().getLackUnitCount();
                        }
                    }
                    // 若缺货数量大于可分配的数量，本次最多处理当前可分配的数量
                    if (lackCount.abs().compareTo(allotCount) > 0) {
                        nowLackCount = allotCount;
                        if (lackCount.compareTo(BigDecimal.ZERO) < 0) {
                            nowLackCount = nowLackCount.negate();
                        }
                    }

                    // 扣减当前订单项的缺货数量
                    lackCount = lackCount.subtract(nowLackCount);
                    // 保存缺货后数量
                    detail.setUnitTotalCount(detail.getUnitTotalCount().subtract(nowLackCount));
                    // 记录需要更新的关联明细项
                    addUpdateTaskInfoDetailList(updateTaskInfoDetailList, detail.getId(), detail.getUnitTotalCount());
                }
                LOGGER.info("[拣货缺货后]：{}", JSON.toJSONString(taskInfoDetailPOList));
                // 如果缺货数量分配还有剩余，说明当前拣货数量可能已经变更，提示刷新重新拣货
                if (lackCount.compareTo(BigDecimal.ZERO) != 0) {
                    throw new BusinessValidateException("拣货任务项数量可能存在变更，请刷新重新拣货！");
                }

            }

            // 根据关联明细项的分配数量，计算已拣货数量和缺货数量
            for (OrderItemTaskInfoPO p : orderItemTaskInfoPOList) {
                if (CollectionUtils.isNotEmpty(p.getDetailList())) {
                    BigDecimal overSortCount = p.getDetailList().stream().map(detail -> detail.getUnitTotalCount())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 已拣货数量（明细项的分配数量）
                    p.setOverSortCount(overSortCount);
                    // 缺货数量
                    p.setLackUnitCount(p.getUnitTotalCount().subtract(p.getOverSortCount()));
                    // 移库数量清0
                    p.setMoveCount(BigDecimal.ZERO);
                }
            }

            // 兼容老数据
        } else {
            processOrderItemTaskInfoPOListOld(orderItemTaskInfoPOList, lackCount);
        }
    }

    /**
     * 缺货优先级排序
     */
    private List<OrderItemTaskInfoDetailPO> sortOrderItemTaskInfoDetail(
        List<OrderItemTaskInfoDetailPO> taskInfoDetailPOList, List<OrderItemTaskInfoDetailLackHelperBO> boList,
        Integer cityId) {
        List<OrderItemTaskInfoDetailLackHelperBO> meituanBoList =
            boList.stream().filter(m -> SourceType.isMeiTuanLikeOrder(m.getOutStockOrderPO().getOrderSourceType()))
                .collect(Collectors.toList());
        List<OrderItemTaskInfoDetailLackHelperBO> normalBoList =
            boList.stream().filter(m -> !SourceType.isMeiTuanLikeOrder(m.getOutStockOrderPO().getOrderSourceType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(normalBoList)) {
            return OrderItemTaskInfoDetailLackHelperBOConvertor.convertOrderItemTaskInfo(boList, taskInfoDetailPOList);
        }

        if (CollectionUtils.isEmpty(meituanBoList)) {
            return OrderItemTaskInfoDetailLackHelperBOConvertor.convertOrderItemTaskInfo(sortItemList(boList, cityId),
                taskInfoDetailPOList);
        }

        normalBoList = sortItemList(normalBoList, cityId);
        normalBoList.addAll(meituanBoList);
        return OrderItemTaskInfoDetailLackHelperBOConvertor.convertOrderItemTaskInfo(normalBoList,
            taskInfoDetailPOList);
    }

    private List<OrderItemTaskInfoDetailLackHelperBO> sortItemList(List<OrderItemTaskInfoDetailLackHelperBO> boList,
        Integer cityId) {
        OrderProcessRuleConfigDTO orderProcessRuleConfigDTO = iOrderProcessRuleConfigService
            .findByOrgIdAndOrderType(cityId, ProcessRuleOrderTypeEnum.拣货缺货.getType(), true);
        if (orderProcessRuleConfigDTO == null) {
            LOGGER.info("[拣货缺货]优先级为空！");
            return boList;
        }
        ProcessOrderTypeEnum orderTypeEnum = ProcessOrderTypeEnum.getEnum(orderProcessRuleConfigDTO.getProcessType());
        LOGGER.info("[拣货缺货]优先级：{}", orderTypeEnum);
        boList = boList.stream()
            .sorted(Comparator.nullsFirst(Comparator
                .comparing(OrderItemTaskInfoDetailLackHelperBO::getSecOwnerId, new SecOwnerIdComparator(orderTypeEnum))
                .thenComparing(OrderItemTaskInfoDetailLackHelperBO::getUnitTotalCount, Comparator.reverseOrder())))
            .collect(Collectors.toList());

        boList = OrderItemTaskInfoDetailLackHelperBOConvertor.reSortLackHelperBOList(boList);
        return boList;
    }

    /**
     * 处理拣货任务项关联的订单项的已拣货数量和缺货数量(兼容老数据)
     *
     * @return
     */
    private void processOrderItemTaskInfoPOListOld(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        BigDecimal lackCount) {
        LOGGER.info("兼容老数据，是否有调用，入参:{}", JSON.toJSONString(orderItemTaskInfoPOList));
        // 缺货时，按分配数量由多到小排序，依次分配缺货数量
        if (lackCount.compareTo(BigDecimal.ZERO) > 0) {
            orderItemTaskInfoPOList = orderItemTaskInfoPOList.stream()
                .sorted(Comparator.comparing(OrderItemTaskInfoPO::getUnitTotalCount).reversed())
                .collect(Collectors.toList());
        }
        // 设置已拣货数量和缺货数量
        for (OrderItemTaskInfoPO p : orderItemTaskInfoPOList) {
            p.setLackUnitCount(BigDecimal.ZERO);
            if (lackCount.compareTo(BigDecimal.ZERO) > 0) {
                // 记录当前订单项的缺货数量
                BigDecimal nowLackCount = lackCount;
                if (lackCount.compareTo(p.getUnitTotalCount()) > 0) {
                    nowLackCount = p.getUnitTotalCount();
                }
                // 缺货数量
                p.setLackUnitCount(nowLackCount);
                // 扣减当前订单项的缺货数量
                lackCount = lackCount.subtract(nowLackCount);
            }
            // 已拣货数量
            p.setOverSortCount(p.getUnitTotalCount().subtract(p.getLackUnitCount()));
        }

        // 如果缺货数量分配还有剩余，说明当前拣货数量可能已经变更，提示刷新重新拣货
        if (lackCount.compareTo(BigDecimal.ZERO) > 0) {
            throw new BusinessValidateException("拣货任务项数量可能存在变更，请刷新重新拣货！");
        }
    }

    /**
     * 校验拣货任务是否标缺货
     */
    public void checkIsLack(List<BatchTaskItemUpdatePO> poList, Byte batchType) {
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        if (batchType != null && !Objects.equals(batchType, BatchTypeEnum.酒批.getType())) {
            if (poList.stream().anyMatch(p -> Objects.equals(p.getIsLack(), ConditionStateEnum.是.getType()))) {
                throw new BusinessValidateException("当前订单不能做缺货拣货，无货时请通知客户取消订单！");
            }
        }
    }

    /**
     * 校验已拣货数量是不是销售规格倍数
     */
    public void checkSaleSpec(List<BatchTaskCheckSaleSpecDTO> checkSaleSpecDTOList) {
        Map<String, List<BatchTaskCheckSaleSpecDTO>> checkSaleSpecMap = checkSaleSpecDTOList.stream().collect(
            Collectors.groupingBy(p -> p.getSkuId() + "_" + p.getRefOrderId() + "_" + p.getSaleSpecQuantity()));
        if (null == checkSaleSpecMap) {
            return;
        }
        checkSaleSpecMap.forEach((k, list) -> {
            BigDecimal overSortUnitTotolCount =
                list.stream().map(p -> p.getOverSortUnitTotolCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal unitTotolCount =
                list.stream().map(p -> p.getUnitTotolCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal saleSpecQuantity = list.get(0).getSaleSpecQuantity();
            /*
             * 同时满足以下条件才需要检验：
             *  (1)拣货任务数量 > 销售规格
             *  (2)拣货任务数量 != 已拣数量
             *  (3)已拣数量不是销售规格倍数
             */
            if (unitTotolCount.compareTo(saleSpecQuantity) >= 0 && unitTotolCount.compareTo(overSortUnitTotolCount) != 0
                && overSortUnitTotolCount.divideAndRemainder(saleSpecQuantity)[1].compareTo(BigDecimal.ZERO) != 0) {
                throw new BusinessValidateException("已拣货数量不是销售规格的倍数！");
            }
        });
    }

    public BatchTaskCheckSaleSpecDTO getCheckSaleSpec(BatchTaskItemDTO batchTaskItemDTO, BigDecimal overSortCount) {
        BatchTaskCheckSaleSpecDTO batchTaskCheckSaleSpecDTO = new BatchTaskCheckSaleSpecDTO();
        batchTaskCheckSaleSpecDTO.setSkuId(batchTaskItemDTO.getSkuId());
        batchTaskCheckSaleSpecDTO.setRefOrderId(batchTaskItemDTO.getRefOrderId());
        batchTaskCheckSaleSpecDTO.setSaleSpecQuantity(batchTaskItemDTO.getSaleSpecQuantity());
        batchTaskCheckSaleSpecDTO.setOverSortUnitTotolCount(overSortCount);
        batchTaskCheckSaleSpecDTO.setUnitTotolCount(batchTaskItemDTO.getUnitTotalCount());
        return batchTaskCheckSaleSpecDTO;
    }

    /**
     * 获取拣货移库入参
     *
     * @return
     */
    public PickUpDTO getContainerPickUpDTO(Integer warehouseId, Long locationId, BatchTaskItemDTO batchTaskItemDTO,
        BigDecimal overSortCount, Long fromLocationId) {
        PickUpDTO pickUpDTO = new PickUpDTO();
        BigDecimal pickCount = overSortCount;
        if (pickCount.compareTo(BigDecimal.ZERO) == 0) {
            LOGGER.info("拣货数量为0，不需要移库");
            return null;
        }
        pickUpDTO.setFromLocationId(fromLocationId);
        pickUpDTO.setLocationId(locationId);
        pickUpDTO.setProductSkuId(batchTaskItemDTO.getSkuId());
        pickUpDTO.setWarehouseId(warehouseId);
        pickUpDTO.setFromChannel(batchTaskItemDTO.getChannel().intValue());
        pickUpDTO.setFromSource(batchTaskItemDTO.getSource().intValue());
        pickUpDTO.setToChannel(batchTaskItemDTO.getChannel().intValue());
        pickUpDTO.setToSource(batchTaskItemDTO.getSource().intValue());
        pickUpDTO.setCount(pickCount);
        return pickUpDTO;
    }

    /**
     * 获取拣货容器位移库入参
     *
     * @return
     */
    public void getPickUpDTO(List<PickUpDTO> pickUpDTOList, Integer warehouseId, Long locationId,
        BatchTaskItemDTO batchTaskItemDTO, BigDecimal overSortCount, Long fromLocationId,
        BatchTaskItemCompleteDTO updateDTO, Long sowLocationId, List<OrderItemTaskInfoPO> nowBatchTaskItemInfoList,
        Map<Long, BigDecimal> taskInfoDetailOverSortCountMap) {
        List<PickUpDTO> nowPickUpDTOList = new ArrayList<>();
        // 根据关联移库
        if (CollectionUtils.isNotEmpty(nowBatchTaskItemInfoList)) {
            nowBatchTaskItemInfoList.forEach(info -> {
                // 关联明细
                if (CollectionUtils.isNotEmpty(info.getDetailList())) {
                    info.getDetailList().forEach(detail -> {
                        PickUpDTO pickUpDTO = new PickUpDTO();
                        BigDecimal pickCount = detail.getUnitTotalCount();
                        // 重复拣货时，移库数量 = 本次拣货数量 - 上次拣货数量
                        if (TaskStateEnum.已完成.getType() == batchTaskItemDTO.getTaskState()) {
                            // 上次拣货数量
                            BigDecimal prevPickCount = BigDecimal.ZERO;
                            if (taskInfoDetailOverSortCountMap != null
                                && taskInfoDetailOverSortCountMap.get(detail.getId()) != null) {
                                prevPickCount = taskInfoDetailOverSortCountMap.get(detail.getId());
                            }
                            pickCount = pickCount.subtract(prevPickCount);
                        }
                        if (pickCount.compareTo(BigDecimal.ZERO) == 0) {
                            LOGGER.info("拣货数量为0，不需要移库");
                            return;
                        }
                        pickUpDTO.setFromLocationId(fromLocationId);
                        setPickupToLocation(locationId, sowLocationId, pickUpDTO);
                        pickUpDTO.setProductSkuId(batchTaskItemDTO.getSkuId());
                        pickUpDTO.setWarehouseId(warehouseId);
                        pickUpDTO.setFromChannel(batchTaskItemDTO.getChannel().intValue());
                        pickUpDTO.setFromSource(batchTaskItemDTO.getSource().intValue());
                        pickUpDTO.setToChannel(batchTaskItemDTO.getChannel().intValue());
                        pickUpDTO.setToSource(batchTaskItemDTO.getSource().intValue());
                        pickUpDTO.setCount(pickCount);
                        // 生产日期和批次时间
                        pickUpDTO.setProductionDate(updateDTO != null ? updateDTO.getProductionDate() : null);
                        pickUpDTO.setBatchTime(updateDTO != null ? updateDTO.getBatchTime() : null);
                        // 自动分配库存
                        pickUpDTO.setAutoAllotFlag(true);
                        pickUpDTO.setSecOwnerId(detail.getSecOwnerId());
                        pickUpDTO.setBusinessId(info.getId().toString());
                        // 排除负库存
                        pickUpDTO.setExcludeNegativeFlag(true);
                        nowPickUpDTOList.add(pickUpDTO);
                    });
                }
            });
        }

        // 兼容老数据
        if (CollectionUtils.isEmpty(nowPickUpDTOList)) {
            PickUpDTO pickUpDTO = new PickUpDTO();
            BigDecimal pickCount = overSortCount;
            // 重复拣货时，移库数量 = 本次拣货数量 - 上次拣货数量
            if (TaskStateEnum.已完成.getType() == batchTaskItemDTO.getTaskState()) {
                pickCount = overSortCount.subtract(batchTaskItemDTO.getOverSortCount());
            }
            if (pickCount.compareTo(BigDecimal.ZERO) == 0) {
                LOGGER.info("拣货数量为0，不需要移库");
                return;
            }
            pickUpDTO.setFromLocationId(fromLocationId);
            setPickupToLocation(locationId, sowLocationId, pickUpDTO);
            pickUpDTO.setProductSkuId(batchTaskItemDTO.getSkuId());
            pickUpDTO.setWarehouseId(warehouseId);
            pickUpDTO.setFromChannel(batchTaskItemDTO.getChannel().intValue());
            pickUpDTO.setFromSource(batchTaskItemDTO.getSource().intValue());
            pickUpDTO.setToChannel(batchTaskItemDTO.getChannel().intValue());
            pickUpDTO.setToSource(batchTaskItemDTO.getSource().intValue());
            pickUpDTO.setCount(pickCount);
            // 生产日期和批次时间
            pickUpDTO.setProductionDate(updateDTO != null ? updateDTO.getProductionDate() : null);
            pickUpDTO.setBatchTime(updateDTO != null ? updateDTO.getBatchTime() : null);
            // 自动分配库存
            pickUpDTO.setAutoAllotFlag(true);
            // 排除负库存
            pickUpDTO.setExcludeNegativeFlag(true);
            nowPickUpDTOList.add(pickUpDTO);
        }

        // 移库数据
        pickUpDTOList.addAll(nowPickUpDTOList);
    }

    /**
     * sku转换
     *
     * @param fromCityId
     * @param orgId
     * @param skuId
     * @return
     */
    private Long transferSku(Integer fromCityId, Integer orgId, Long skuId) {
        Long skuNew = null;
        if (fromCityId != null && !Objects.equals(fromCityId, orgId)) {
            // 将招商或者长株潭城市SKU转换为实际发货的SKU
            Map<Long, Long> actualDeliverySkuIdMap =
                iWarehouseInventoryQueryService.getActualDeliverySkuIdMap(Arrays.asList(skuId), 0, fromCityId);
            if (actualDeliverySkuIdMap.size() > 0) {
                skuNew = actualDeliverySkuIdMap.get(skuId);
            }
        }
        return skuNew;
    }

    private PackageOrderItemDTO getPackageOrderItemDTO(Integer warehouseId, Integer cityId, Integer userId,
        List<OutStockOrderItemPO> outStockOrderItemPOList, BatchTaskItemDTO batchTaskItemDTO, String boxCode,
        BigDecimal overSortPackageCount, BigDecimal overSortUnitCount) {
        PackageOrderItemDTO packageOrderItemDTO = new PackageOrderItemDTO();
        packageOrderItemDTO.setOrgId(cityId);
        packageOrderItemDTO.setWarehouseId(warehouseId);
        packageOrderItemDTO.setRefOrderNo(batchTaskItemDTO.getRefOrderNo());
        packageOrderItemDTO.setRefOrderId(Long.valueOf(batchTaskItemDTO.getRefOrderId()));
        // 查找出库单项
        List<OutStockOrderItemPO> filterList = outStockOrderItemPOList.stream()
            .filter(p -> Objects.equals(p.getBatchTaskItemId(), batchTaskItemDTO.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            LOGGER.info("根据skuId去查找出库单项");
            filterList = outStockOrderItemPOList.stream()
                .filter(outStockOrderItemPO -> batchTaskItemDTO.getRefOrderId()
                    .equals(outStockOrderItemPO.getOutstockorderId().toString())
                    && batchTaskItemDTO.getSkuId().equals(outStockOrderItemPO.getSkuid()))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterList)) {
                OutStockOrderPO outStockOrderPO =
                    outStockOrderMapper.selectByRefOrderId(Long.valueOf(batchTaskItemDTO.getRefOrderId()));
                if (null != outStockOrderPO) {
                    Long newSkuId = transferSku(outStockOrderPO.getFromCityId(), outStockOrderPO.getOrgId(),
                        batchTaskItemDTO.getSkuId());
                    if (null != newSkuId) {
                        filterList = outStockOrderItemPOList.stream()
                            .filter(outStockOrderItemPO -> batchTaskItemDTO.getRefOrderId()
                                .equals(outStockOrderItemPO.getOutstockorderId().toString())
                                && newSkuId.equals(outStockOrderItemPO.getSkuid()))
                            .collect(Collectors.toList());
                    }
                    LOGGER.info("转换skuId, outStockOrderPO: {}, newSkuId: {}", JSON.toJSONString(outStockOrderPO),
                        newSkuId);
                }
            }
        }
        if (CollectionUtils.isEmpty(filterList)) {
            LOGGER.info(String.format("订单可能已取消，不需要装箱！%s", JSON.toJSONString(batchTaskItemDTO)));
            return null;
        }
        packageOrderItemDTO.setRefOrderItemId(filterList.get(0).getId());
        packageOrderItemDTO.setBoxCode(boxCode);
        packageOrderItemDTO.setProductName(batchTaskItemDTO.getProductName());
        packageOrderItemDTO.setSkuId(batchTaskItemDTO.getSkuId());
        packageOrderItemDTO.setSpecName(batchTaskItemDTO.getSpecName());
        packageOrderItemDTO.setSpecQuantity(batchTaskItemDTO.getSpecQuantity());
        packageOrderItemDTO.setPackageName(batchTaskItemDTO.getPackageName());
        packageOrderItemDTO.setPackageCount(overSortPackageCount);
        packageOrderItemDTO.setUnitName(batchTaskItemDTO.getUnitName());
        packageOrderItemDTO.setUnitCount(overSortUnitCount);
        packageOrderItemDTO.setUnitTotalCount(
            overSortPackageCount.multiply(packageOrderItemDTO.getSpecQuantity()).add(overSortUnitCount));
        packageOrderItemDTO.setRemark(null);
        packageOrderItemDTO.setCreateUser(String.valueOf(userId));
        return packageOrderItemDTO;
    }

    @Deprecated
    private void setPickupToLocation(Long locationId, Long sowLocationId, PickUpDTO pickUpDTO) {
        // 如果有播种任务，拣货完成，先放到集货区
        if (sowLocationId != null) {
            // 拣货完成，先放到集货位
            pickUpDTO.setLocationId(sowLocationId);
            LOGGER.info(String.format("播种任务拣货完成，备货区货位：%s,先放到集货区：%s", locationId, JSON.toJSONString(pickUpDTO)));
        }
        if (pickUpDTO.getLocationId() == null) {
            pickUpDTO.setLocationId(locationId);
        }
    }

    /**
     * 获取集货位信息
     *
     * @return
     */
    public List<SowTaskDTO> getSowTasks(List<BatchTaskItemDTO> batchTaskItemDTOList) {
        List<SowTaskDTO> sowTasks = new ArrayList<>();
        if (batchTaskItemDTOList.stream().anyMatch(p -> StringUtils.isNotEmpty(p.getSowTaskNo()))) {
            // 获取集货位信息
            Integer orgId = batchTaskItemDTOList.get(0).getOrgId();
            List<String> lstSowTaskNos =
                batchTaskItemDTOList.stream().filter(p -> StringUtils.isNotEmpty(p.getSowTaskNo()))
                    .map(p -> p.getSowTaskNo()).distinct().collect(Collectors.toList());
            sowTasks = iSowQueryService.findSowTaskByTaskNos(orgId, lstSowTaskNos);
        }
        return sowTasks;
    }

    private void processTaskCompleteItems(String operateUser, BatchTaskConfirmDTO batchTaskConfirmDTO,
        List<BatchTaskItemDTO> batchTaskItemDTOList, List<Long> lstOutStockOrders, List<PickUpDTO> pickUpDTOList,
        List<BatchTaskItemUpdatePO> poList, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        List<OrderItemTaskInfoDetailPO> updateTaskInfoDetailList,
        Map<Long, BigDecimal> taskInfoDetailOverSortCountMap) {
        List<BatchTaskItemDTO> batchTaskItemList = batchTaskConfirmDTO.getBatchTaskItemList();
        Integer warehouseId = batchTaskConfirmDTO.getWarehouseId();
        Long locationId = batchTaskConfirmDTO.getLocationId();
        // 获取拣货任务详情对应的缺货数量
        Map<String, BatchTaskItemDTO> lackUnitCountMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(batchTaskItemList)) {
            batchTaskItemList.forEach(p -> {
                lackUnitCountMap.put(p.getId(), p);
            });
        }

        List<Long> outStockOrderIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findIdByIds(outStockOrderIds, warehouseId);

        // 获取集货位信息
        List<SowTaskDTO> sowTasks = getSowTasks(batchTaskItemDTOList);
        Date currentTime = new Date();
        for (BatchTaskItemDTO batchTaskItemDTO : batchTaskItemDTOList) {
            // 获取集货位
            Long sowLocationId = null;
            if (batchTaskItemDTO.getSowTaskId() != null) {
                Optional<SowTaskDTO> sowTaskDTO =
                    sowTasks.stream().filter(p -> p.getId().equals(batchTaskItemDTO.getSowTaskId())).findAny();
                if (sowTaskDTO.isPresent() && sowTaskDTO.get().getLocationId() != null) {
                    sowLocationId = sowTaskDTO.get().getLocationId();
                    // 出库位设置为空
                    batchTaskConfirmDTO.setLocationId(null);
                    batchTaskConfirmDTO.setLocationName(null);
                }
            }

            // 实际拣货数量
            BigDecimal realUnitTotalCount = batchTaskItemDTO.getUnitTotalCount();
            // 判断客户端是否填写了缺货数量，如果填写了则实际拣货数量=拣货数量-缺货数量
            BigDecimal lackUnitCount =
                (batchTaskItemDTO.getLackUnitCount() != null) ? batchTaskItemDTO.getLackUnitCount() : new BigDecimal(0);
            if (null != lackUnitCountMap && null != lackUnitCountMap.get(batchTaskItemDTO.getId())) {
                lackUnitCount = lackUnitCountMap.get(batchTaskItemDTO.getId()).getLackUnitCount();
                if (lackUnitCount.compareTo(BigDecimal.ZERO) < 0
                    || lackUnitCount.compareTo(batchTaskItemDTO.getUnitTotalCount()) > 0) {
                    throw new BusinessValidateException("缺货数量有误！");
                }
                realUnitTotalCount = batchTaskItemDTO.getUnitTotalCount().subtract(lackUnitCount);
            }

            // 来源货位id（如果拣货任务详情的货位id为空时，由客户端传过来）
            Long fromLocationId = batchTaskItemDTO.getLocationId();
            String fromLocationName = batchTaskItemDTO.getLocationName();
            if (fromLocationId == null && null != lackUnitCountMap.get(batchTaskItemDTO.getId())) {
                fromLocationId = lackUnitCountMap.get(batchTaskItemDTO.getId()).getFromLocationId();
                fromLocationName = lackUnitCountMap.get(batchTaskItemDTO.getId()).getFromLocationName();
            }

            // 更新拣货任务详情
            BatchTaskItemUpdatePO po = new BatchTaskItemUpdatePO();
            if (lackUnitCount.compareTo(BigDecimal.ZERO) > 0) {
                po.setIsLack(Byte.valueOf("1"));
            }
            po.setId(batchTaskItemDTO.getId());
            po.setOverSortCount(realUnitTotalCount);
            po.setLackCount(lackUnitCount);
            po.setTaskState(TaskStateEnum.已完成.getType());
            po.setLastUpdateUser(operateUser);
            po.setFromLocationId(fromLocationId);
            po.setFromLocationName(fromLocationName);
            po.setCompleteUser(batchTaskConfirmDTO.getOperateUser());
            po.setCompleteUserId(batchTaskConfirmDTO.getOperateUserId());
            po.setCompleteTime(currentTime);
            poList.add(po);

            // 处理拣货任务项关联的订单项的已拣货数量和缺货数量
            List<OrderItemTaskInfoPO> nowBatchTaskItemInfoList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
                nowBatchTaskItemInfoList = orderItemTaskInfoPOList.stream()
                    .filter(p -> Objects.equals(p.getBatchTaskItemId(), batchTaskItemDTO.getId()))
                    .collect(Collectors.toList());
                processOrderItemTaskInfoPOList(nowBatchTaskItemInfoList, lackUnitCount, updateTaskInfoDetailList,
                    batchTaskItemDTO, outStockOrderPOList);
            }

            // 移库数据
            getPickUpDTO(pickUpDTOList, warehouseId, locationId, batchTaskItemDTO, realUnitTotalCount, fromLocationId,
                null, sowLocationId, nowBatchTaskItemInfoList, taskInfoDetailOverSortCountMap);

            if (StringUtils.isNotEmpty(batchTaskItemDTO.getRefOrderId())
                && batchTaskItemDTO.getPickingType() == PickingTypeEnum.订单拣货.getType()) {
                lstOutStockOrders.add(Long.valueOf(batchTaskItemDTO.getRefOrderId()));
            }
        }
    }

    /**
     * 根据波次任务编号查询待拆分订单（按订单接货）
     *
     * @param batchTaskNo
     * @param currentPage
     * @param pageSize
     * @return
     */
    public PageList<BatchTaskSplitOrderDTO> listNotSplitOrderByBatchTaskNo(String batchTaskNo, Integer currentPage,
        Integer pageSize) {
        PageResult<BatchTaskSplitOrderDTO> batchTaskSplitOrderDTOS =
            batchTaskItemMapper.listNotSplitOrderByBatchTaskNo(batchTaskNo, currentPage, pageSize);
        PageList<BatchTaskSplitOrderDTO> pageList = batchTaskSplitOrderDTOS.toPageList();
        // pageList.getDataList().forEach(batchTaskSplitOrderDTO -> {
        // // 获取商品种类
        // batchTaskSplitOrderDTO.setSkuCount(batchTaskItemMapper.countSkuCount(batchTaskNo,
        // batchTaskSplitOrderDTO.getRefOrderNo()));
        // });
        LOGGER.info("待拆分订单列表：{}", JSON.toJSONString(pageList));
        return pageList;
    }

    /**
     * 拣货任务确认拆分
     *
     * @param batchTaskSplitDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchTaskConfirmSplit(BatchTaskSplitDTO batchTaskSplitDTO) {
        // 1、校验参数
        BatchTaskPO oldBatchTaskPO = validateSplit(batchTaskSplitDTO);
        // 2、处理拣货任务拆分结果
        List<BatchTaskPO> batchTaskList = new ArrayList<>();
        List<BatchTaskItemPO> batchTaskItemList = new ArrayList<>();
        List<BatchUpdateOutStockOrderItemPO> outStockOrderItemList = new ArrayList<>();
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList = new ArrayList<>();
        processSplitResult(batchTaskSplitDTO, oldBatchTaskPO, batchTaskList, batchTaskItemList, outStockOrderItemList,
            orderItemTaskInfoPOList);
        // 3、更新拣货任务
        updateBatchTaskResult(Arrays.asList(batchTaskSplitDTO.getBatchTaskNo()), batchTaskList, batchTaskItemList,
            outStockOrderItemList, orderItemTaskInfoPOList);
        // 4、记录操作日志
        recordBatchTaskLogSplit(batchTaskList, oldBatchTaskPO, batchTaskSplitDTO.getOperateUser());
        // 5、推送消息到PDA
        batchTaskList.forEach(p -> {
            orderTraceBL.pushTaskMsg(p.getSorterId());
            // orderTraceBL.sendPushMsg(p.getSorterId(), 1);
        });

        batchTaskChangeNotifyBL.notifyBatchTaskSplit(oldBatchTaskPO, batchTaskList);
    }

    /**
     * 校验拆分拣货任务参数
     *
     * @return
     */
    private BatchTaskPO validateSplit(BatchTaskSplitDTO batchTaskSplitDTO) {
        AssertUtils.notNull(batchTaskSplitDTO, "确认拆分拣货任务参数不能为空");
        AssertUtils.notNull(batchTaskSplitDTO.getPickingType(), "拣货方式不能为空");
        AssertUtils.notNull(batchTaskSplitDTO.getBatchTaskNo(), "拣货任务编号不能为空");
        AssertUtils.notNull(batchTaskSplitDTO.getOperateUser(), "操作人不能为空");
        AssertUtils.notEmpty(batchTaskSplitDTO.getBatchTaskList(), "拆分的子任务列表不能为空");

        // 验证拣货任务状态是否为“待拣货”
        BatchTaskPO oldBatchTaskPO = batchTaskMapper.findBatchTaskByNo(batchTaskSplitDTO.getBatchTaskNo());
        if (null == oldBatchTaskPO || TaskStateEnum.未分拣.getType() != oldBatchTaskPO.getTaskState()) {
            throw new BusinessValidateException("只有待拣货状态的拣货任务才可拆分");
        }
        LOGGER.info("确认拆分参数:{}", JSON.toJSONString(batchTaskSplitDTO));
        return oldBatchTaskPO;
    }

    /**
     * 处理拣货任务拆分结果
     *
     * @param batchTaskSplitDTO 拆分需要的参数
     * @param oldBatchTaskPO 原待拆分的拣货任务
     * @param batchTaskList 拆分后的拣货任务列表
     * @param batchTaskItemList 拆分后需要处理的拣货任务详情列表
     * @param outStockOrderItemList 拆分后需要处理的出库单详情列表
     */
    private void processSplitResult(BatchTaskSplitDTO batchTaskSplitDTO, BatchTaskPO oldBatchTaskPO,
        List<BatchTaskPO> batchTaskList, List<BatchTaskItemPO> batchTaskItemList,
        List<BatchUpdateOutStockOrderItemPO> outStockOrderItemList, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        batchTaskSplitDTO.getBatchTaskList().forEach(batchTaskDTO -> {
            // 1、封装拣货任务PO类
            BatchTaskPO batchTaskPO = new BatchTaskPO();
            BeanUtils.copyProperties(batchTaskDTO, batchTaskPO);
            String batchTaskId = UuidUtil.generatorId();
            batchTaskPO.setId(batchTaskId);
            batchTaskPO.setOrgId(oldBatchTaskPO.getOrgId());
            batchTaskPO.setWarehouseId(oldBatchTaskPO.getWarehouseId());
            batchTaskPO.setBatchId(oldBatchTaskPO.getBatchId());
            batchTaskPO.setBatchNo(oldBatchTaskPO.getBatchNo());
            // batchTaskPO.setOrderAmount(null); 订单总金额
            // todo 按产品拆分无法确定订单数
            if (batchTaskPO.getOrderCount() == null) {
                batchTaskPO.setOrderCount(1);
            }
            batchTaskPO.setTaskState(TaskStateEnum.未分拣.getType());
            batchTaskPO.setLocationId(oldBatchTaskPO.getLocationId());
            batchTaskPO.setLocationName(oldBatchTaskPO.getLocationName());
            batchTaskPO.setLocationCategory(oldBatchTaskPO.getLocationCategory());
            batchTaskPO.setCategoryName(oldBatchTaskPO.getCategoryName());
            batchTaskPO.setPickingType(oldBatchTaskPO.getPickingType());
            batchTaskPO.setPickingGroupStrategy(oldBatchTaskPO.getPickingGroupStrategy());
            batchTaskPO.setToLocationId(oldBatchTaskPO.getToLocationId());
            batchTaskPO.setToLocationName(oldBatchTaskPO.getToLocationName());
            batchTaskPO.setBatchTaskName(oldBatchTaskPO.getBatchTaskName());
            batchTaskPO.setSowTaskId(oldBatchTaskPO.getSowTaskId());
            batchTaskPO.setSowTaskNo(oldBatchTaskPO.getSowTaskNo());
            batchTaskPO.setCreateTime(new Date());
            // 是否需要拣货
            batchTaskPO.setNeedPick(ConditionStateEnum.是.getType());
            batchTaskPO.setBatchTaskType(oldBatchTaskPO.getBatchTaskType());
            batchTaskPO.setPassageId(oldBatchTaskPO.getPassageId());
            batchTaskPO.setPassageName(oldBatchTaskPO.getPassageName());
            batchTaskPO.setTaskAppendSequence(oldBatchTaskPO.getTaskAppendSequence());
            batchTaskPO.setTaskWarehouseFeatureType(oldBatchTaskPO.getTaskWarehouseFeatureType());
            batchTaskPO.setKindOfPicking(oldBatchTaskPO.getKindOfPicking());
            batchTaskPO.setPickPattern(oldBatchTaskPO.getPickPattern());
            batchTaskList.add(batchTaskPO);

            /// 2、封装拣货任务详情PO类
            batchTaskDTO.getBatchTaskItemList().forEach(batchTaskItemDTO -> {
                // 按订单拆分
                if (PickingTypeEnum.订单拣货.getType() == batchTaskSplitDTO.getPickingType()) {
                    // 根据订单号和拣货任务编号获取所有拣货任务详情id
                    List<String> batchTaskItemIdList = batchTaskItemMapper
                        .listBatchTaskItemId(oldBatchTaskPO.getBatchTaskNo(), batchTaskItemDTO.getRefOrderNo());
                    batchTaskItemIdList.forEach(id -> {
                        BatchTaskItemPO batchTaskItemPO = new BatchTaskItemPO();
                        batchTaskItemPO.setId(id);
                        batchTaskItemPO.setBatchTaskId(batchTaskId);
                        batchTaskItemPO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
                        batchTaskItemList.add(batchTaskItemPO);
                    });
                    // 按产品拆分
                } else {
                    // 根据客户端传过来的任务详情id进行封装
                    BatchTaskItemPO batchTaskItemPO = new BatchTaskItemPO();
                    batchTaskItemPO.setId(batchTaskItemDTO.getId());
                    batchTaskItemPO.setBatchTaskId(batchTaskId);
                    batchTaskItemPO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
                    batchTaskItemList.add(batchTaskItemPO);
                }
            });
        });

        // 3、封装出库单详情PO类
        List<String> idList = batchTaskItemList.stream().map(BatchTaskItemPO::getId).collect(Collectors.toList());
        // 获取需要处理的拣货任务详情列表
        List<BatchTaskItemPO> batchTaskItemPOList =
            batchTaskItemMapper.listBatchTaskItemByIds(idList, batchTaskSplitDTO.getBatchTaskNo());
        if (CollectionUtils.isEmpty(batchTaskItemPOList)) {
            throw new BusinessValidateException("该拣货任务已经被拆分");
        }
        batchTaskItemPOList.forEach(po -> {
            Optional<BatchTaskItemPO> optional =
                batchTaskItemList.stream().filter(p -> Objects.equals(p.getId(), po.getId())).findAny();
            if (optional.isPresent()) {
                BatchTaskItemPO newPO = optional.get();
                po.setBatchTaskId(newPO.getBatchTaskId());
                po.setBatchTaskNo(newPO.getBatchTaskNo());
            }
        });
        addOutStockOrderItemList(oldBatchTaskPO.getBatchTaskNo(), oldBatchTaskPO.getPickingType(), batchTaskItemPOList,
            outStockOrderItemList, orderItemTaskInfoPOList);

        if (CollectionUtils.isEmpty(batchTaskList) || CollectionUtils.isEmpty(batchTaskItemList)) {
            throw new BusinessValidateException("处理拣货任务拆分结果异常");
        }
        LOGGER.info(
            "处理拣货任务拆分结果： batchTaskList：{}，batchTaskItemList：{}，outStockOrderItemList：{}，orderItemTaskInfoPOList：{}",
            JSON.toJSONString(batchTaskList), JSON.toJSONString(batchTaskItemList),
            JSON.toJSONString(outStockOrderItemList), JSON.toJSONString(orderItemTaskInfoPOList));
    }

    /**
     * 封装待更新的出库单详情
     */
    private void addOutStockOrderItemList(String batchTaskNo, Byte pickingType,
        List<BatchTaskItemPO> batchTaskItemPOList, List<BatchUpdateOutStockOrderItemPO> outStockOrderItemList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        // 根据待拆分的拣货任务详情ID查询关联
        List<String> batchTaskItemIdList =
            batchTaskItemPOList.stream().map(BatchTaskItemPO::getId).collect(Collectors.toList());
        List<OrderItemTaskInfoPO> oldOrderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByBatchTaskItemIds(batchTaskItemIdList);

        if (CollectionUtils.isNotEmpty(oldOrderItemTaskInfoPOList)) {
            // 1、封装待更新的出库单与拣货任务项关联
            oldOrderItemTaskInfoPOList.forEach(taskInfo -> {
                List<BatchTaskItemPO> filterList = batchTaskItemPOList.stream()
                    .filter(batchTaskItemPO -> Objects.equals(taskInfo.getBatchTaskItemId(), batchTaskItemPO.getId()))
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filterList)) {
                    taskInfo.setBatchTaskId(filterList.get(0).getBatchTaskId());
                    taskInfo.setBatchTaskNo(filterList.get(0).getBatchTaskNo());
                    orderItemTaskInfoPOList.add(taskInfo);
                }
            });

            // 2、封装待更新的出库单详情
            Map<Long, List<OrderItemTaskInfoPO>> outStockOrderItemMap =
                orderItemTaskInfoPOList.stream().collect(Collectors.groupingBy(p -> p.getRefOrderItemId()));
            outStockOrderItemMap.forEach((orderItemId, list) -> {
                if (CollectionUtils.isNotEmpty(list)) {
                    BatchUpdateOutStockOrderItemPO batchUpdateOutStockOrderItemPO =
                        new BatchUpdateOutStockOrderItemPO();
                    batchUpdateOutStockOrderItemPO.setId(orderItemId);
                    batchUpdateOutStockOrderItemPO.setBatchtaskId(list.get(0).getBatchTaskId());
                    batchUpdateOutStockOrderItemPO.setBatchtaskno(list.get(0).getBatchTaskNo());
                    outStockOrderItemList.add(batchUpdateOutStockOrderItemPO);
                }
            });

            // 兼容老数据
        } else {
            // 获取与待拆分的拣货任务关联的所有出库单详情列表
            List<OutStockOrderItemPO> outStockOrderItemPOList = outStockOrderItemMapper.listByBatchTaskNo(batchTaskNo);
            outStockOrderItemPOList.forEach(outStockOrderItemPO -> {
                List<BatchTaskItemPO> filterList = null;
                if (PickingTypeEnum.订单拣货.getType() == pickingType) {
                    // 按订单号筛选出拣货任务详情中存在指定出库单id的列表
                    filterList = batchTaskItemPOList.stream().filter(batchTaskItemPO -> Objects
                        .equals(outStockOrderItemPO.getOutstockorderId().toString(), batchTaskItemPO.getRefOrderId()))
                        .collect(Collectors.toList());
                } else {
                    // 按拣货任务详情id筛选
                    filterList = batchTaskItemPOList.stream().filter(batchTaskItemPO -> Objects
                        .equals(outStockOrderItemPO.getBatchTaskItemId(), batchTaskItemPO.getId()))
                        .collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(filterList)) {
                    BatchUpdateOutStockOrderItemPO batchUpdateOutStockOrderItemPO =
                        new BatchUpdateOutStockOrderItemPO();
                    batchUpdateOutStockOrderItemPO.setId(outStockOrderItemPO.getId());
                    batchUpdateOutStockOrderItemPO.setBatchtaskId(filterList.get(0).getBatchTaskId());
                    batchUpdateOutStockOrderItemPO.setBatchtaskno(filterList.get(0).getBatchTaskNo());
                    outStockOrderItemList.add(batchUpdateOutStockOrderItemPO);
                }
            });
        }
    }

    /**
     * 更新拣货任务结果
     */
    private void updateBatchTaskResult(List<String> batchTaskNoList, List<BatchTaskPO> batchTaskList,
        List<BatchTaskItemPO> batchTaskItemList, List<BatchUpdateOutStockOrderItemPO> outStockOrderItemList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        // 设置分仓属性
        warehouseAllocationTypeManageBL.setBatchTaskWarehouseAllocationType(batchTaskList, batchTaskItemList);

        // 1、保存拣货子任务
        batchTaskMapper.insertList(batchTaskList);
        // 2、更新拣货任务详情对应的拣货任务编号和id
        Lists.partition(batchTaskItemList, 50).forEach(p -> {
            batchTaskItemMapper.updateList(p);
        });

        // 触发拣货任务项排序任务
        pickingTaskItemSortingBL.triggerPickingItemSorting(batchTaskList, batchTaskItemList);

        // 3、更新订单项详情信息 batchTaskNo batchTaskId
        if (CollectionUtils.isNotEmpty(outStockOrderItemList)) {
            outStockOrderItemMapper.batchUpdateOutStockOrderItem(outStockOrderItemList);
        }
        // 4、更新订单项详情关联拣货任务详情信息 batchTaskNo batchTaskId
        if (CollectionUtils.isNotEmpty(orderItemTaskInfoPOList)) {
            Lists.partition(orderItemTaskInfoPOList, 50).forEach(p -> {
                orderItemTaskInfoMapper.updateBatchByBatchTask(p);
            });
        }
        // 5、修改原拣货任务的状态为“已作废”
        batchTaskMapper.updateBatchTaskByNoList(batchTaskNoList, TaskStateEnum.已作废.getType());
    }

    /**
     * 记录拆分操作日志
     *
     * @param batchTaskList
     */
    private void recordBatchTaskLogSplit(List<BatchTaskPO> batchTaskList, BatchTaskPO oldBatchTaskPO,
        String operateUser) {
        StringBuilder splitBatchTaskNo = new StringBuilder();
        // 记录拆分的拣货任务
        List<OrderTraceDTO> orderTraceDTOList = batchTaskList.stream().map(batchTaskPO -> {
            splitBatchTaskNo.append(batchTaskPO.getBatchTaskNo() + "、");
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.拣货任务.getType());
            dto.setBusinessId(Long.valueOf(batchTaskPO.getId()));
            dto.setBusinessno(batchTaskPO.getBatchTaskNo());
            dto.setEventtype(OrderTraceEventTypeEnum.新增.getType());
            dto.setDescription(
                OrderTraceDescriptionEnum.拆分的拣货任务新建成功.name() + "（被拆分的拣货编号：" + oldBatchTaskPO.getBatchTaskNo() + "）");
            dto.setCreateuser(null != operateUser ? operateUser : "1");
            dto.setOrgId(
                StringUtils.isNotEmpty(batchTaskPO.getOrgId()) ? Integer.valueOf(batchTaskPO.getOrgId()) : null);
            return dto;
        }).collect(Collectors.toList());
        orderTraceBL.insertUpdateBatch(orderTraceDTOList);

        if (splitBatchTaskNo.length() > 0) {
            String description = ConstantUtil.splitStringBySymbol(splitBatchTaskNo.toString(), "、", 1000);
            // 记录被拆分的拣货任务
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.拣货任务.getType());
            dto.setBusinessId(Long.valueOf(oldBatchTaskPO.getId()));
            dto.setBusinessno(oldBatchTaskPO.getBatchTaskNo());
            dto.setEventtype(OrderTraceEventTypeEnum.修改.getType());
            dto.setDescription(OrderTraceDescriptionEnum.拆分拣货任务成功.name() + "（拆分后的拣货编号：" + description + "）");
            dto.setCreateuser(null != operateUser ? operateUser : "1");
            dto.setOrgId(
                StringUtils.isNotEmpty(oldBatchTaskPO.getOrgId()) ? Integer.valueOf(oldBatchTaskPO.getOrgId()) : null);
            orderTraceBL.insert(dto);
        }
    }

    /**
     * 拣货任务确认合并
     *
     * @param batchTaskMergeDTO
     */
    public void batchTaskConfirmMerge(BatchTaskMergeDTO batchTaskMergeDTO) {
        // 1、验证合并的参数
        List<BatchTaskPO> batchTaskPOList = validateMerge(batchTaskMergeDTO);
        // 2、处理拣货任务合并结果
        List<BatchTaskPO> batchTaskList = new ArrayList<>();
        List<BatchTaskItemPO> batchTaskItemList = new ArrayList<>();
        List<BatchUpdateOutStockOrderItemPO> outStockOrderItemList = new ArrayList<>();
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList = new ArrayList<>();
        processMergeResult(batchTaskPOList, batchTaskMergeDTO, batchTaskList, batchTaskItemList, outStockOrderItemList,
            orderItemTaskInfoPOList);
        Integer warehouseId = getSplitWarehouseId(batchTaskList);

        handleSplitWarehouseBatchTaskInfo(batchTaskList, orderItemTaskInfoPOList, warehouseId);
        // 3、更新拣货任务
        updateBatchTaskResult(batchTaskMergeDTO.getBatchTaskNoList(), batchTaskList, batchTaskItemList,
            outStockOrderItemList, orderItemTaskInfoPOList);
        // 4、记录操作日志
        recordBatchTaskLogMerge(batchTaskList.get(0), batchTaskPOList, batchTaskMergeDTO.getOperateUser());
        // 5、推送消息到PDA
        orderTraceBL.pushTaskMsg(batchTaskMergeDTO.getSorterId());
        // orderTraceBL.sendPushMsg(batchTaskMergeDTO.getSorterId(), 1);
    }

    private Integer getSplitWarehouseId(List<BatchTaskPO> batchTaskPOList) {
        Optional<Integer> warehouseOptional =
            batchTaskPOList.stream().map(BatchTaskPO::getWarehouseId).filter(Objects::nonNull).distinct().findFirst();

        if (warehouseOptional.isPresent()) {
            return warehouseOptional.get();
        }

        throw new BusinessValidateException("仓库信息不能为空！");
    }

    private void handleSplitWarehouseBatchTaskInfo(List<BatchTaskPO> batchTaskList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, Integer warehouseId) {
        if (CollectionUtils.isEmpty(batchTaskList)) {
            return;
        }
        if (!globalCache.openWarehouseSeparateAttributeConfig(warehouseId)) {
            return;
        }
        List<BatchTaskPO> featureNullbatchTaskPOList = batchTaskList.stream()
            .filter(m -> Objects.isNull(m.getTaskWarehouseFeatureType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(featureNullbatchTaskPOList)) {
            return;
        }
        Long outStockOrderId =
            orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderId).distinct().findFirst().get();

        Map<Long, List<Byte>> orderFeatureMap =
            orderFeatureBL.getOrderFeatureMap(Collections.singletonList(outStockOrderId));
        List<Byte> featureList = orderFeatureMap.get(outStockOrderId);
        if (CollectionUtils.isEmpty(featureList)) {
            return;
        }
        Byte featureType = new Supplier<Byte>() {
            @Override
            public Byte get() {
                if (org.springframework.util.CollectionUtils.isEmpty(featureList)) {
                    return (byte)0;
                }
                if (featureList.stream().anyMatch(OrderFeatureConstant.FEATURE_TYPE_DRINKING::equals)) {
                    return OrderFeatureConstant.FEATURE_TYPE_DRINKING;
                }
                if (featureList.stream().anyMatch(OrderFeatureConstant.FEATURE_TYPE_REST::equals)) {
                    return OrderFeatureConstant.FEATURE_TYPE_REST;
                }
                return (byte)0;
            }
        }.get();

        featureNullbatchTaskPOList.forEach(batchTaskPO -> {
            batchTaskPO.setTaskWarehouseFeatureType(featureType);
        });
    }

    /**
     * 验证合并参数
     *
     * @param batchTaskMergeDTO
     * @return
     */
    private List<BatchTaskPO> validateMerge(BatchTaskMergeDTO batchTaskMergeDTO) {
        AssertUtils.notNull(batchTaskMergeDTO, "确认合并拣货任务参数不能为空");
        AssertUtils.notEmpty(batchTaskMergeDTO.getBatchTaskNoList(), "待合并的拣货编号列表不能为空");
        AssertUtils.notNull(batchTaskMergeDTO.getSorter(), "拣货员不能为空");
        AssertUtils.notNull(batchTaskMergeDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(batchTaskMergeDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(batchTaskMergeDTO.getOperateUser(), "操作人不能为空");
        // 检验合并拣货任务数量
        if (batchTaskMergeDTO.getBatchTaskNoList().size() < 2) {
            throw new BusinessValidateException("至少需要两个及以上的拣货任务才允许合并");
        }
        // 根据任务编号查询波次任务列表
        List<BatchTaskPO> batchTaskPOList =
            batchTaskMapper.findTasksByBatchTaskNo(batchTaskMergeDTO.getBatchTaskNoList());
        if (batchTaskPOList.stream().collect(Collectors.groupingBy(BatchTaskPO::getBatchNo)).size() != 1) {
            throw new BusinessValidateException("波次相同的拣货任务才允许合并");
        }
        if (batchTaskPOList.stream().map(p -> StringUtils.isEmpty(p.getSowTaskNo()) ? "" : p.getSowTaskNo())
            .collect(Collectors.groupingBy(p -> p, Collectors.counting())).size() != 1) {
            throw new BusinessValidateException("播种任务相同的拣货任务才允许合并");
        }
        if (batchTaskPOList.stream().collect(Collectors.groupingBy(BatchTaskPO::getBatchTaskName)).size() != 1) {
            throw new BusinessValidateException("任务属性相同的拣货任务才允许合并");
        }
        if (batchTaskPOList.stream().filter(batchTask -> batchTask.getTaskState() != TaskStateEnum.未分拣.getType())
            .collect(Collectors.toList()).size() > 0) {
            throw new BusinessValidateException("待拣货状态的拣货任务才允许合并");
        }
        if (CollectionUtils.isEmpty(batchTaskPOList)) {
            throw new BusinessValidateException("波次任务列表查询为空");
        }
        if (batchTaskPOList.stream().collect(Collectors.groupingBy(BatchTaskPO::getPickPattern)).size() != 1) {
            throw new BusinessValidateException("不同分拣模式的拣货任务，不能合并！");
        }
        if (batchTaskPOList.stream().collect(Collectors.groupingBy(BatchTaskPO::getTaskWarehouseFeatureType))
            .size() != 1) {
            throw new BusinessValidateException("不同分仓的拣货任务，不能合并！");
        }
        LOGGER.info("确认合并参数:{}", JSON.toJSONString(batchTaskMergeDTO));
        return batchTaskPOList;
    }

    /**
     * 处理合并结果
     *
     * @param batchTaskPOList 待合并的拣货任务列表
     * @param batchTaskMergeDTO 合并需要的相关参数
     * @param batchTaskItemList 合并后需要处理的拣货任务详情列表
     * @param outStockOrderItemList 合并后需要处理的出库单详情列表
     */
    private void processMergeResult(List<BatchTaskPO> batchTaskPOList, BatchTaskMergeDTO batchTaskMergeDTO,
        List<BatchTaskPO> batchTaskList, List<BatchTaskItemPO> batchTaskItemList,
        List<BatchUpdateOutStockOrderItemPO> outStockOrderItemList, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        List<BatchTaskItemDTO> batchTaskItemDTOList =
            batchTaskItemMapper.findBatchTaskItemDtoListByNo(batchTaskMergeDTO.getBatchTaskNoList());
        if (CollectionUtils.isEmpty(batchTaskItemDTOList)) {
            throw new BusinessValidateException("该拣货任务已经被合并");
        }
        Optional<BatchTaskPO> oldBatchTaskOptional = batchTaskPOList.stream().findFirst();
        if (BooleanUtils.isFalse(oldBatchTaskOptional.isPresent())) {
            throw new BusinessValidateException("拣货任务不存在！");
        }
        BatchTaskPO oldbatchTaskPO = oldBatchTaskOptional.get();
        // 1、封装拣货任务PO类（创建新的拣货任务）
        BatchTaskPO batchTaskPO = new BatchTaskPO();
        String batchTaskId = UuidUtil.generatorId();
        batchTaskPO.setId(batchTaskId);
        batchTaskPO.setWarehouseId(batchTaskMergeDTO.getWarehouseId());
        batchTaskPO.setOrgId(batchTaskMergeDTO.getOrgId());
        batchTaskPO.setBatchId(oldbatchTaskPO.getBatchId());
        batchTaskPO.setBatchNo(oldbatchTaskPO.getBatchNo());
        String batchTaskNo =
            UuidUtil.generator(batchTaskMergeDTO.getWarehouseId(), BatchOrderTypeConstans.BATCHTASK_NO);
        batchTaskPO.setBatchTaskNo(batchTaskNo);
        batchTaskPO.setSorterId(batchTaskMergeDTO.getSorterId());
        batchTaskPO.setSorter(batchTaskMergeDTO.getSorter());
        // batchTaskPO.setOrderAmount(null);
        batchTaskPO.setOrderCount(
            batchTaskItemDTOList.stream().collect(Collectors.groupingBy(BatchTaskItemDTO::getRefOrderId)).size());
        batchTaskPO.setSkuCount(
            batchTaskItemDTOList.stream().collect(Collectors.groupingBy(BatchTaskItemDTO::getSkuId)).size());
        batchTaskPO.setPackageAmount(
            batchTaskPOList.stream().map(BatchTaskPO::getPackageAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        batchTaskPO.setUnitAmount(
            batchTaskPOList.stream().map(BatchTaskPO::getUnitAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        batchTaskPO.setTaskState(TaskStateEnum.未分拣.getType());
        batchTaskPO.setPickingType(oldbatchTaskPO.getPickingType());
        batchTaskPO.setPickingGroupStrategy(oldbatchTaskPO.getPickingGroupStrategy());
        batchTaskPO.setBatchTaskName(oldbatchTaskPO.getBatchTaskName());
        batchTaskPO.setSowTaskId(oldbatchTaskPO.getSowTaskId());
        batchTaskPO.setSowTaskNo(oldbatchTaskPO.getSowTaskNo());
        batchTaskPO.setCreateTime(new Date());
        // 是否需要拣货
        batchTaskPO.setNeedPick(ConditionStateEnum.是.getType());
        batchTaskPO.setBatchTaskType(oldbatchTaskPO.getBatchTaskType());
        batchTaskPO.setPassageId(oldbatchTaskPO.getPassageId());
        batchTaskPO.setPassageName(oldbatchTaskPO.getPassageName());
        batchTaskPO.setPickPattern(oldbatchTaskPO.getPickPattern());
        batchTaskPO.setTaskWarehouseFeatureType(oldbatchTaskPO.getTaskWarehouseFeatureType());
        Optional<Byte> minAppendSequence =
            batchTaskPOList.stream().map(BatchTaskPO::getTaskAppendSequence).min(Comparator.naturalOrder());

        batchTaskPO.setTaskAppendSequence(minAppendSequence.orElse((byte)0));
        batchTaskPO.setKindOfPicking(oldbatchTaskPO.getKindOfPicking());
        batchTaskList.add(batchTaskPO);

        // 2、封装拣货任务详情PO类（更新batchTaskId、batchTaskNo）
        List<String> bathTaskIds = batchTaskPOList.stream().map(p -> p.getId()).collect(Collectors.toList());
        List<BatchTaskItemPO> oldBatchTaskItemList =
            batchTaskItemMapper.listBatchTaskItemByTaskId(bathTaskIds, Integer.valueOf(batchTaskMergeDTO.getOrgId()));
        oldBatchTaskItemList.forEach(p -> {
            BatchTaskItemPO batchTaskItemPO = new BatchTaskItemPO();
            batchTaskItemPO.setId(p.getId());
            batchTaskItemPO.setBatchTaskId(batchTaskId);
            batchTaskItemPO.setBatchTaskNo(batchTaskNo);
            batchTaskItemList.add(batchTaskItemPO);
        });

        // 3、封装出库单详情PO类（更新batchTaskId、batchTaskNo）
        List<Long> outStockOrderIdList = outStockOrderItemMapper.listIdByBatchTaskIs(bathTaskIds);
        outStockOrderIdList.forEach(id -> {
            BatchUpdateOutStockOrderItemPO batchUpdateOutStockOrderItemPO = new BatchUpdateOutStockOrderItemPO();
            batchUpdateOutStockOrderItemPO.setId(id);
            batchUpdateOutStockOrderItemPO.setBatchtaskId(batchTaskId);
            batchUpdateOutStockOrderItemPO.setBatchtaskno(batchTaskNo);
            outStockOrderItemList.add(batchUpdateOutStockOrderItemPO);
        });

        // 4、封装出库单详情关联拣货任务项PO类
        List<OrderItemTaskInfoPO> oldOrderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByBatchTaskIds(bathTaskIds);
        oldOrderItemTaskInfoPOList.forEach(p -> {
            OrderItemTaskInfoPO taskInfoPO = new OrderItemTaskInfoPO();
            taskInfoPO.setId(p.getId());
            taskInfoPO.setBatchTaskId(batchTaskId);
            taskInfoPO.setBatchTaskNo(batchTaskNo);
            orderItemTaskInfoPOList.add(taskInfoPO);
        });

        if (CollectionUtils.isEmpty(batchTaskList) || CollectionUtils.isEmpty(batchTaskItemList)) {
            throw new BusinessValidateException("处理拣货任务合并结果异常");
        }
        LOGGER.info(
            "处理拣货任务合并结果： batchTaskList：{}，batchTaskItemList：{}，outStockOrderItemList：{}，orderItemTaskInfoPOList：{}",
            JSON.toJSONString(batchTaskList), JSON.toJSONString(batchTaskItemList),
            JSON.toJSONString(outStockOrderItemList), JSON.toJSONString(orderItemTaskInfoPOList));
    }

    /**
     * 记录合并操作日志
     */
    private void recordBatchTaskLogMerge(BatchTaskPO batchTask, List<BatchTaskPO> oldbatchTaskPOList,
        String operateUser) {
        StringBuilder mergeBatchTaskNo = new StringBuilder();
        // 记录待合并的拣货任务
        List<OrderTraceDTO> oldOrderTraceDTOList = oldbatchTaskPOList.stream().map(batchTaskPO -> {
            mergeBatchTaskNo.append(batchTaskPO.getBatchTaskNo() + "、");
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.拣货任务.getType());
            dto.setBusinessId(Long.valueOf(batchTaskPO.getId()));
            dto.setBusinessno(batchTaskPO.getBatchTaskNo());
            dto.setEventtype(OrderTraceEventTypeEnum.修改.getType());
            dto.setDescription(
                OrderTraceDescriptionEnum.合并拣货任务成功.name() + "（合并后的拣货编号：" + batchTask.getBatchTaskNo() + "）");
            dto.setCreateuser(null != operateUser ? operateUser : "1");
            dto.setOrgId(
                StringUtils.isNotEmpty(batchTaskPO.getOrgId()) ? Integer.valueOf(batchTaskPO.getOrgId()) : null);
            return dto;
        }).collect(Collectors.toList());
        orderTraceBL.insertUpdateBatch(oldOrderTraceDTOList);

        if (mergeBatchTaskNo.length() > 0) {
            // 记录合并后的拣货任务
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.拣货任务.getType());
            dto.setBusinessId(Long.valueOf(batchTask.getId()));
            dto.setBusinessno(batchTask.getBatchTaskNo());
            dto.setEventtype(OrderTraceEventTypeEnum.新增.getType());
            dto.setDescription(OrderTraceDescriptionEnum.合并的拣货任务新建成功.name() + "（被合并的拣货编号："
                + mergeBatchTaskNo.substring(0, mergeBatchTaskNo.length() - 1) + "）");
            dto.setCreateuser(null != operateUser ? operateUser : "1");
            dto.setOrgId(StringUtils.isNotEmpty(batchTask.getOrgId()) ? Integer.valueOf(batchTask.getOrgId()) : null);
            orderTraceBL.insert(dto);
        }
    }

    /**
     * 拣货任务确认拆分(PDA)
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchTaskConfirmSplitByPDA(BatchTaskSplitByPdaDTO batchTaskSplitByPdaDTO) {
        // 1、校验pda拆分参数
        BatchTaskPO oldBatchTaskPO = validateSplitByPDA(batchTaskSplitByPdaDTO);
        // 2、获取拆分后的原拣货任务
        BatchTaskPO updateBatchTaskPO = getUpdateBatchTaskPO(oldBatchTaskPO.getId(), oldBatchTaskPO.getTaskState(),
            batchTaskSplitByPdaDTO.getBatchTaskItemIds());
        // 3、处理拣货任务拆分结果
        List<BatchTaskPO> batchTaskList = new ArrayList<>();
        List<BatchTaskItemPO> batchTaskItemList = new ArrayList<>();
        List<BatchUpdateOutStockOrderItemPO> outStockOrderItemList = new ArrayList<>();
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList = new ArrayList<>();
        processSplitResultByPDA(batchTaskSplitByPdaDTO, oldBatchTaskPO, batchTaskList, batchTaskItemList,
            outStockOrderItemList, orderItemTaskInfoPOList);
        // 4、更新拣货任务
        updateBatchTaskResultByPDA(batchTaskSplitByPdaDTO, oldBatchTaskPO, updateBatchTaskPO, batchTaskList,
            batchTaskItemList, outStockOrderItemList, orderItemTaskInfoPOList);
        // 5、记录操作日志
        recordBatchTaskLogSplit(batchTaskList, oldBatchTaskPO, batchTaskSplitByPdaDTO.getOperateUser());
        // 6、推送消息到PDA
        orderTraceBL.pushTaskMsg(batchTaskSplitByPdaDTO.getSorterId());
        // orderTraceBL.sendPushMsg(p.getSorterId(), 1);
    }

    /**
     * 校验pda拆分拣货任务参数
     *
     * @return
     */
    private BatchTaskPO validateSplitByPDA(BatchTaskSplitByPdaDTO batchTaskSplitByPdaDTO) {
        AssertUtils.notNull(batchTaskSplitByPdaDTO, "拣货任务确认拆分(PDA)参数不能为空");
        AssertUtils.notNull(batchTaskSplitByPdaDTO.getBatchTaskNo(), "拣货任务编号不能为空");
        AssertUtils.notNull(batchTaskSplitByPdaDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(batchTaskSplitByPdaDTO.getSorterId(), "拣货员id不能为空");
        AssertUtils.notNull(batchTaskSplitByPdaDTO.getSorter(), "拣货员不能为空");
        AssertUtils.notNull(batchTaskSplitByPdaDTO.getOperateUser(), "操作人不能为空");
        AssertUtils.notEmpty(batchTaskSplitByPdaDTO.getBatchTaskItemIds(), "拣货任务详情id集合不能为空");

        // 验证拣货任务状态是否为“已完成”
        BatchTaskPO oldBatchTaskPO = batchTaskMapper.findBatchTaskByNo(batchTaskSplitByPdaDTO.getBatchTaskNo());
        if (null == oldBatchTaskPO || TaskStateEnum.已完成.getType() == oldBatchTaskPO.getTaskState()) {
            throw new BusinessValidateException("已完成的拣货任务不可拆分");
        }
        LOGGER.info("确认拆分(PDA)参数:{}", JSON.toJSONString(batchTaskSplitByPdaDTO));
        return oldBatchTaskPO;
    }

    /**
     * 获取拆分后的原拣货任务
     *
     * @param taskId
     * @param batchTaskItemIds
     * @return
     */
    private BatchTaskPO getUpdateBatchTaskPO(String taskId, Byte taskState, List<String> batchTaskItemIds) {
        // 原拣货任务详情
        List<BatchTaskItemPO> oldBatchTaskPOList = batchTaskItemMapper.findBatchTaskItemDTOListNoPage(taskId);
        // 过滤掉拆分的任务详情
        List<BatchTaskItemPO> filterList =
            oldBatchTaskPOList.stream().filter(p -> !batchTaskItemIds.contains(p.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filterList)) {
            BatchTaskPO batchTaskPO = new BatchTaskPO();
            batchTaskPO.setId(taskId);
            // 订单数
            batchTaskPO.setOrderCount(filterList.stream()
                .collect(Collectors.groupingBy(p -> StringUtils.isEmpty(p.getRefOrderId()) ? "" : p.getRefOrderId()))
                .size());
            // 商品种类数量
            batchTaskPO
                .setSkuCount(filterList.stream().collect(Collectors.groupingBy(BatchTaskItemPO::getSkuId)).size());
            // 大件总数
            batchTaskPO.setPackageAmount(
                filterList.stream().map(BatchTaskItemPO::getPackageCount).reduce(BigDecimal.ZERO, BigDecimal::add));
            // 小件总数
            batchTaskPO.setUnitAmount(
                filterList.stream().map(BatchTaskItemPO::getUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add));
            // 如果拆分后的任务详情状态全部“已完成”，则拣货任务状态也修改为“已完成”
            long count = filterList.stream().filter(n -> TaskStateEnum.未分拣.getType() == n.getTaskState().byteValue()
                || 分拣中.getType() == n.getTaskState().byteValue()).count();
            if (count == 0) {
                batchTaskPO.setTaskState(TaskStateEnum.已完成.getType());
            } else {
                batchTaskPO.setTaskState(taskState);
            }
            return batchTaskPO;
        }
        return null;
    }

    /**
     * 处理拣货任务拆分结果（PDA）
     *
     * @param batchTaskSplitByPdaDTO 拆分需要的参数
     * @param oldBatchTaskPO 原待拆分的拣货任务
     * @param batchTaskList 拆分后的拣货任务列表
     * @param batchTaskItemList 拆分后需要处理的拣货任务详情列表
     * @param outStockOrderItemList 拆分后需要处理的出库单详情列表
     */
    private void processSplitResultByPDA(BatchTaskSplitByPdaDTO batchTaskSplitByPdaDTO, BatchTaskPO oldBatchTaskPO,
        List<BatchTaskPO> batchTaskList, List<BatchTaskItemPO> batchTaskItemList,
        List<BatchUpdateOutStockOrderItemPO> outStockOrderItemList, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        // 获取拣货任务详情列表
        List<BatchTaskItemPO> batchTaskItemPOList = batchTaskItemMapper.listBatchTaskItemByIds(
            batchTaskSplitByPdaDTO.getBatchTaskItemIds(), batchTaskSplitByPdaDTO.getBatchTaskNo());
        if (CollectionUtils.isEmpty(batchTaskItemPOList)) {
            throw new BusinessValidateException("该拣货任务已经被领取，请刷新页面");
        }

        // 1、封装拣货任务PO类
        BatchTaskPO batchTaskPO = new BatchTaskPO();
        BeanUtils.copyProperties(oldBatchTaskPO, batchTaskPO);
        batchTaskPO.setId(UuidUtil.generatorId());
        batchTaskPO.setBatchTaskNo(
            UuidUtil.generator(batchTaskSplitByPdaDTO.getWarehouseId(), BatchOrderTypeConstans.BATCHTASK_NO));
        // 分拣员
        batchTaskPO.setSorterId(batchTaskSplitByPdaDTO.getSorterId());
        batchTaskPO.setSorter(batchTaskSplitByPdaDTO.getSorter());
        // 订单数
        batchTaskPO.setOrderCount(batchTaskItemPOList.stream()
            .collect(Collectors.groupingBy(p -> StringUtils.isEmpty(p.getRefOrderId()) ? "" : p.getRefOrderId()))
            .size());
        // 商品种类数量
        batchTaskPO
            .setSkuCount(batchTaskItemPOList.stream().collect(Collectors.groupingBy(BatchTaskItemPO::getSkuId)).size());
        // 大件总数
        batchTaskPO.setPackageAmount(batchTaskItemPOList.stream().map(BatchTaskItemPO::getPackageCount)
            .reduce(BigDecimal.ZERO, BigDecimal::add));
        // 小件总数
        batchTaskPO.setUnitAmount(
            batchTaskItemPOList.stream().map(BatchTaskItemPO::getUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 分拣任务状态
        batchTaskPO.setTaskState(TaskStateEnum.未分拣.getType());
        // 播种任务
        batchTaskPO.setSowTaskId(oldBatchTaskPO.getSowTaskId());
        batchTaskPO.setSowTaskNo(oldBatchTaskPO.getSowTaskNo());
        batchTaskPO.setCreateTime(new Date());
        // 是否需要拣货
        batchTaskPO.setNeedPick(ConditionStateEnum.是.getType());
        batchTaskPO.setBatchTaskType(oldBatchTaskPO.getBatchTaskType());
        batchTaskPO.setPassageId(oldBatchTaskPO.getPassageId());
        batchTaskPO.setPassageName(oldBatchTaskPO.getPassageName());
        batchTaskPO.setTaskAppendSequence(oldBatchTaskPO.getTaskAppendSequence());
        batchTaskPO.setTaskWarehouseFeatureType(oldBatchTaskPO.getTaskWarehouseFeatureType());
        batchTaskPO.setKindOfPicking(oldBatchTaskPO.getKindOfPicking());
        batchTaskPO.setPickPattern(oldBatchTaskPO.getPickPattern());

        batchTaskList.add(batchTaskPO);

        /// 2、封装拣货任务详情PO类
        batchTaskItemPOList.forEach(batchTaskItemPO -> {
            batchTaskItemPO.setBatchTaskId(batchTaskPO.getId());
            batchTaskItemPO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
            batchTaskItemPO.setTaskState(TaskStateEnum.未分拣.getType());
            batchTaskItemList.add(batchTaskItemPO);
        });

        // 3、封装出库单详情PO类
        addOutStockOrderItemList(oldBatchTaskPO.getBatchTaskNo(), oldBatchTaskPO.getPickingType(), batchTaskItemList,
            outStockOrderItemList, orderItemTaskInfoPOList);

        if (CollectionUtils.isEmpty(batchTaskList) || CollectionUtils.isEmpty(batchTaskItemList)) {
            throw new BusinessValidateException("处理拣货任务拆分结果异常");
        }
        LOGGER.info(
            "处理拣货任务拆分结果（PDA）： batchTaskList：{}，batchTaskItemList：{}，outStockOrderItemList：{}，orderItemTaskInfoPOList：{}",
            JSON.toJSONString(batchTaskList), JSON.toJSONString(batchTaskItemList),
            JSON.toJSONString(outStockOrderItemList), JSON.toJSONString(orderItemTaskInfoPOList));
    }

    /**
     * 更新拣货任务结果（PDA）
     */
    private void updateBatchTaskResultByPDA(BatchTaskSplitByPdaDTO batchTaskSplitByPdaDTO, BatchTaskPO oldBatchTaskPO,
        BatchTaskPO updateBatchTaskPO, List<BatchTaskPO> batchTaskList, List<BatchTaskItemPO> batchTaskItemList,
        List<BatchUpdateOutStockOrderItemPO> outStockOrderItemList, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        // 设置分仓属性
        warehouseAllocationTypeManageBL.setBatchTaskWarehouseAllocationType(batchTaskList, batchTaskItemList);

        // 1、保存拆分的拣货子任务
        batchTaskMapper.insertList(batchTaskList);
        // 2、更新拣货任务详情对应的拣货任务编号和id
        Lists.partition(batchTaskItemList, 50).forEach(p -> {
            batchTaskItemMapper.updateListNew(p);
        });

        // 触发拣货任务项排序任务
        pickingTaskItemSortingBL.triggerPickingItemSorting(batchTaskList, batchTaskItemList);

        // 3、更新订单项详情信息 batchTaskNo batchTaskId
        if (CollectionUtils.isNotEmpty(outStockOrderItemList)) {
            outStockOrderItemMapper.batchUpdateOutStockOrderItem(outStockOrderItemList);
        }
        // 4、更新订单项详情关联拣货任务详情信息 batchTaskNo batchTaskId
        if (CollectionUtils.isNotEmpty(orderItemTaskInfoPOList)) {
            Lists.partition(orderItemTaskInfoPOList, 50).forEach(p -> {
                orderItemTaskInfoMapper.updateBatchByBatchTask(p);
            });
        }
        // 5、如果原拣货任务详情全部拆分，则修改原拣货任务的状态为“已作废”，否则更新订单数
        if (null == updateBatchTaskPO) {
            batchTaskMapper.updateBatchTaskByNo(batchTaskSplitByPdaDTO.getBatchTaskNo(), TaskStateEnum.已作废.getType());
        } else {
            // 更新订单数
            batchTaskMapper.updateBatchTask(updateBatchTaskPO);
            // 如果原拣货任务未拆分的产品全部已完成，则修改原拣货任务的状态为“已完成”
            if (TaskStateEnum.已完成.getType() == updateBatchTaskPO.getTaskState()) {
                batchTaskMapper.updateBatchTaskByNo(batchTaskSplitByPdaDTO.getBatchTaskNo(),
                    TaskStateEnum.已完成.getType());
                // -添加操作记录（完成拣货）
                orderTraceBL.updateBatchTaskTrace(oldBatchTaskPO, OrderTraceDescriptionEnum.拣货完成.name(),
                    batchTaskSplitByPdaDTO.getOperateUser());
            }
        }
    }

    /**
     * 根据播种任务和波次任务状态获取波次任务
     *
     * @see BatchTaskSowQueryBL#findBatchTaskListBySowTaskNos(List, List, Integer)
     */
    @Deprecated
    public List<BatchTaskDTO> listBatchTaskBySowTaskNos(List<String> sowTaskNos, List<Integer> taskStates,
        Integer cityId) {
        List<BatchTaskDTO> batchTaskDTOS = new ArrayList<>();
        List<BatchTaskItemDTO> batchTaskItemList = new ArrayList<>();
        // 通过播种任务找到拣货任务
        List<BatchTaskPO> batchTaskPOS = batchTaskMapper.listBatchTaskRelatedBySowNos(sowTaskNos, cityId);
        if (CollectionUtils.isEmpty(batchTaskPOS)) {
            return null;
        }
        // 通过拣货任务编号找到拣货任务详情信息
        List<BatchTaskItemDTO> batchTaskItemDTOS = batchTaskItemMapper.findBatchTaskItemDtoListByNo(
            batchTaskPOS.stream().map(BatchTaskPO::getBatchTaskNo).collect(Collectors.toList()));
        // 排序
        batchTaskItemDTOS = processTaskItemIndex(batchTaskItemDTOS);

        if (CollectionUtils.isNotEmpty(taskStates)) {
            // 查出符合状态的拣货任务详情
            List<Byte> taskStateList =
                taskStates.stream().map(integer -> Byte.valueOf(integer.toString())).collect(Collectors.toList());
            batchTaskItemList.addAll(batchTaskItemDTOS.stream()
                .filter(batchTaskItemDTO -> taskStateList.contains(batchTaskItemDTO.getTaskState()))
                .collect(Collectors.toList()));
            LOGGER.info("状态符合{}的拣货任务详情是: {}", JSON.toJSONString(taskStateList), JSON.toJSONString(batchTaskItemList));
        } else {
            batchTaskItemList.addAll(batchTaskItemDTOS);
        }

        // 为波次任务详情添加产品条码信息
        if (CollectionUtils.isNotEmpty(batchTaskItemList)) {
            // 获取符合状态的拣货任务
            List<String> matchStateBatchNos =
                batchTaskItemList.stream().map(BatchTaskItemDTO::getBatchTaskNo).collect(Collectors.toList());
            batchTaskPOS =
                batchTaskPOS.stream().filter(batchTaskPO -> matchStateBatchNos.contains(batchTaskPO.getBatchTaskNo()))
                    .collect(Collectors.toList());

            // 通过播种任务编号查找出库单信息
            List<OutStockOrderItemDTO> outStockOrderItemDTOS =
                outStockOrderItemMapper.findBySowTaskNos(sowTaskNos, cityId);
            // 查找拣货任务详情中商品的条码集合
            Set<Long> skuIds =
                outStockOrderItemDTOS.stream().map(OutStockOrderItemDTO::getSkuId).collect(Collectors.toSet());
            Map<Long, ProductCodeDTO> packageAndUnitCode = iProductSkuService.getPackageAndUnitCode(skuIds, cityId);
            Map<Long, ProductImageDTO> productImageUrl =
                iProductSkuQueryService.getProductImageUrl(new ArrayList<>(skuIds));
            List<ProductSkuDTO> productCharacteristic =
                iProductSkuService.getProductCharacteristic(skuIds, outStockOrderItemDTOS.get(0).getWarehouseId());
            Map<Long, ProductSpecificationDTO> productSpecificationMap = iProductInfoSpecificationSerivce
                .findByProductSkuIds(new ArrayList<>(skuIds), outStockOrderItemDTOS.get(0).getWarehouseId());

            // 查询已拣货没货位的产品关联货位信息
            List<Long> noLocationSkuIds = batchTaskItemList.stream()
                .filter(item -> item.getTaskState() == TaskStateEnum.已完成.getType() && item.getLocationId() == null)
                .map(BatchTaskItemDTO::getSkuId).distinct().collect(Collectors.toList());
            Map<Long, List<LoactionDTO>> skuLocationMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(noLocationSkuIds)) {
                skuLocationMap = iProductLocationService
                    .findLocationDTOBySkuId(batchTaskItemList.get(0).getWarehouseId(), noLocationSkuIds);
            }

            // 查询订单默认出库位
            List<OutStockOrderLocationDTO> outStockOrderLocationDTOS = new ArrayList<>();
            outStockOrderItemDTOS.stream().collect(Collectors.groupingBy(OutStockOrderItemDTO::getOutstockorder_Id))
                .forEach((orderId, items) -> {
                    OutStockOrderLocationDTO outStockOrderLocationDTO = new OutStockOrderLocationDTO();
                    outStockOrderLocationDTO.setWarehouseId(items.get(0).getWarehouseId());
                    String businessId = items.get(0).getBusinessId();
                    outStockOrderLocationDTO
                        .setOmsOrderId(StreamUtils.isNum(businessId) ? Long.valueOf(businessId) : orderId);

                    outStockOrderLocationDTOS.add(outStockOrderLocationDTO);
                });
            Map<Long, OutStockOrderLocationDTO> outStockOrderLocationMap =
                outStockOrderBL.findOutStockOrderLocation(outStockOrderLocationDTOS).stream().collect(Collectors
                    .toMap(OutStockOrderLocationDTO::getOmsOrderId, Function.identity(), (key1, key2) -> key2));

            Map<Long, List<LoactionDTO>> finalSkuLocationMap = skuLocationMap;
            batchTaskItemList.forEach(item -> {
                ProductCodeDTO productCodeDTO = packageAndUnitCode.get(item.getSkuId());
                ProductImageDTO productImageDTO = productImageUrl.get(item.getSkuId());
                if (null != productCodeDTO) {
                    item.setPackageCode(productCodeDTO.getPackageCode());
                    item.setUnitCode(productCodeDTO.getUnitCode());
                }
                if (item.getLocationId() == null
                    && CollectionUtils.isNotEmpty(finalSkuLocationMap.get(item.getSkuId()))) {
                    LoactionDTO loactionDTO = finalSkuLocationMap.get(item.getSkuId()).get(0);
                    item.setLocationId(loactionDTO.getId());
                    item.setLocationName(loactionDTO.getName());
                }
                if (productImageDTO != null) {
                    item.setDefaultImageFile(productImageDTO.getDefaultImageFile());
                    item.setImageFiles(productImageDTO.getImageFiles());
                }
            });

            outStockOrderItemDTOS.forEach(item -> {
                ProductCodeDTO productCodeDTO = packageAndUnitCode.get(item.getSkuId());
                OutStockOrderLocationDTO outStockOrderLocationDTO =
                    outStockOrderLocationMap.get(StreamUtils.isNum(item.getBusinessId())
                        ? Long.valueOf(item.getBusinessId()) : item.getOutstockorder_Id());
                ProductImageDTO productImageDTO = productImageUrl.get(item.getSkuId());
                ProductSpecificationDTO productSpecificationDTO = productSpecificationMap.get(item.getSkuId());
                if (null != productCodeDTO) {
                    item.setPackageCode(productCodeDTO.getPackageCode());
                    item.setUnitCode(productCodeDTO.getUnitCode());
                }
                if (null != outStockOrderLocationDTO) {
                    if (null == item.getLocationId()) {
                        item.setLocationId(outStockOrderLocationDTO.getLocationId());
                        item.setLocationName(outStockOrderLocationDTO.getLocationName());
                    }
                    if (null == item.getToLocationId()) {
                        item.setToCity(outStockOrderLocationDTO.getToCity());
                        item.setToWarehouseName(outStockOrderLocationDTO.getToWarehouseName());
                        item.setToLocationId(outStockOrderLocationDTO.getToLocationId());
                        item.setToLocationName(outStockOrderLocationDTO.getToLocationName());
                    }
                }
                if (productImageDTO != null) {
                    item.setDefaultImageFile(productImageDTO.getDefaultImageFile());
                    item.setImageFiles(productImageDTO.getImageFiles());
                }
                if (productSpecificationDTO != null) {
                    item.setLength(productSpecificationDTO.getLength());
                    item.setHeight(productSpecificationDTO.getHeight());
                    item.setWidth(productSpecificationDTO.getWidth());
                    item.setProductFeature(productSpecificationDTO.getProductFeature());
                }
            });

            // 根据ToLocationId查询货位信息赋值货位顺序
            List<String> locationIds = outStockOrderItemDTOS.stream().filter(p -> p.getToLocationId() != null)
                .map(p -> p.getToLocationId().toString()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(locationIds)) {
                List<LocationReturnDTO> locationList = locationAreaService.findLocationListById(locationIds);
                if (CollectionUtils.isNotEmpty(locationList)) {
                    locationList.forEach(l -> {
                        List<OutStockOrderItemDTO> outStockOrderItem = outStockOrderItemDTOS.stream()
                            .filter(p -> p.getToLocationId() != null && p.getToLocationId().compareTo(l.getId()) == 0)
                            .collect(Collectors.toList());
                        outStockOrderItem.forEach(item -> {
                            item.setLocationSequence(l.getSequence());
                        });
                    });
                }
            }

            // 封装拣货任务dto，加入拣货任务详情信息
            batchTaskPOS.forEach(batchTaskPO -> {
                BatchTaskDTO batchTaskDTO = new BatchTaskDTO();
                BeanUtils.copyProperties(batchTaskPO, batchTaskDTO);
                String batchTaskNo = batchTaskDTO.getBatchTaskNo();
                Byte taskState = batchTaskDTO.getTaskState();
                List<BatchTaskItemDTO> batchTaskItems = batchTaskItemList.stream()
                    .filter(item -> item.getBatchTaskNo().equals(batchTaskNo)).collect(Collectors.toList());
                List<OutStockOrderItemDTO> orderItems = outStockOrderItemDTOS.stream().filter(item -> {
                    if (item.getBatchTaskNo().equals(batchTaskNo)) {
                        item.setSorter(batchTaskPO.getCompleteUser());
                        item.setSorterId(batchTaskPO.getCompleteUserId());
                        item.setTaskState(taskState);
                        return true;
                    } else {
                        return false;
                    }
                }).sorted(Comparator.comparing(OutStockOrderItemDTO::getCreatetime)).collect(Collectors.toList());
                LOGGER.info("productCharacteristic={}", JSON.toJSONString(productCharacteristic));
                batchTaskItems.forEach(taskItem -> {
                    OutStockOrderItemDTO firstOrdItem = orderItems.stream()
                        .filter(ordItem -> Objects.equals(ordItem.getBatchTaskItemId(), taskItem.getId())).findFirst()
                        .orElse(null);
                    ProductSkuDTO productSkuDTO = productCharacteristic.stream()
                        .filter(s -> s.getProductSkuId().compareTo(taskItem.getSkuId()) == 0).findFirst()
                        .orElse(new ProductSkuDTO());
                    taskItem.setSkuProperty(String.valueOf(productSkuDTO.getProductFeature()));
                    if (firstOrdItem != null) {
                        taskItem.setSowTaskItemState(firstOrdItem.getSowTaskItemState());
                    }
                    if (Objects.isNull(taskItem.getCreateAllocation())) {
                        taskItem.setCreateAllocation(ConditionStateEnum.否.getType());
                    }
                });
                if (CollectionUtils.isNotEmpty(orderItems)) {
                    orderItems.forEach(item -> {
                        if (Objects.isNull(item.getCreateAllocation())) {
                            item.setCreateAllocation(ConditionStateEnum.否.getType());
                        }
                    });
                }
                batchTaskDTO.setBatchTaskItemList(batchTaskItems);
                batchTaskDTO.setOutStockOrderItemList(orderItems);
                batchTaskDTO.setCreateTime(DateUtil.format(batchTaskPO.getCreateTime(), DateUtil.YYYYMMDD_HHMMSS));
                batchTaskDTOS.add(batchTaskDTO);
            });
        } else {
            LOGGER.info("未找到状态符合{}的拣货任务详情,播种任务编号:{}", JSON.toJSONString(taskStates), JSON.toJSONString(sowTaskNos));
        }
        // 填充通道编码
        passageBL.fillPassageCode(batchTaskDTOS);
        Integer warehouseId = batchTaskPOS.get(0).getWarehouseId();

        // 开启了按客户拣货，相同地址合并拣货
        if (globalCache.getPickByCustomerFromCache(warehouseId)) {
            PDASowTaskInfoDTOConvertor.pickByCustomerSetValue(batchTaskDTOS);
        }
        // SCM-13939 【WMS】查询播种数据，如果订单项有生成整件任务，去除整件数量
        // return batchTaskHelper.removeBatchTaskCount(batchTaskDTOS);
        return batchTaskDTOS;
    }

    /**
     * 获取波次默认备货区
     */
    public LoactionDTO findDefaultLocationByBatchNo(Integer orgId, String batchNo) {
        BatchPO batchPO = batchMapper.selectBatchByBatchNo(orgId, batchNo);
        return getLoactionDTOByBatch(batchPO);
    }

    /**
     * 获取拣货任务默认备货区
     */
    public LoactionDTO findDefaultLocationByBatchTaskNo(Integer orgId, String batchTaskNo) {
        BatchTaskPO batchTaskByNo = batchTaskMapper.findBatchTaskByNo(batchTaskNo);
        AssertUtils.notNull(batchTaskByNo, "拣货任务不存在！编号：" + batchTaskNo);
        LoactionDTO loactionDTO = getLoactionDTOByBatchTask(batchTaskByNo);
        if (loactionDTO == null) {
            BatchPO batchPO = batchMapper.selectBatchByTaskNo(orgId, batchTaskNo);
            LOGGER.info(String.format("获取波次详情：%s", JSON.toJSONString(batchPO)));
            loactionDTO = getLoactionDTOByBatch(batchPO);
        }
        return loactionDTO;
    }

    private LoactionDTO getLoactionDTOByBatchTask(BatchTaskPO batchTaskByNo) {
        LoactionDTO loc = null;
        if (batchTaskByNo.getToLocationId() != null && StringUtils.isNotEmpty(batchTaskByNo.getToLocationName())) {
            loc = new LoactionDTO();
            loc.setId(batchTaskByNo.getToLocationId());
            loc.setName(batchTaskByNo.getToLocationName());
        }
        return loc;
    }

    private LoactionDTO getLoactionDTOByBatch(BatchPO batchPO) {
        LoactionDTO loc = new LoactionDTO();
        // 获取波次对应线路或片区的出库位
        List<LocationInfoDTO> locationInfoDTOS = recommendOutLocationBL.getLocationByAreaOrRoute(batchPO);
        if (CollectionUtils.isEmpty(locationInfoDTOS)) {
            return loc;
        }
        // 过滤出当前仓库实际存在的出库位
        List<Long> locationIdList = locationInfoDTOS.stream().filter(p -> p.getLocationId() != null)
            .map(p -> p.getLocationId()).distinct().collect(Collectors.toList());
        List<LoactionDTO> loactionDTOList = getRealToLocation(locationIdList, batchPO.getWarehouseId());
        if (CollectionUtils.isEmpty(loactionDTOList)) {
            return loc;
        }
        return loactionDTOList.get(0);
    }

    /**
     * 获取当前仓库实际存在的出库位
     *
     * @return
     */
    public List<LoactionDTO> getRealToLocation(List<Long> locationIds, Integer warehouseId) {
        if (CollectionUtils.isEmpty(locationIds)) {
            return null;
        }
        // 查询货位信息
        List<LoactionDTO> loactionDTOS = iLocationService.findLocationByIds(locationIds);
        if (CollectionUtils.isEmpty(loactionDTOS)) {
            LOGGER.info("出库位不存在：{}", JSON.toJSONString(locationIds));
            return null;
        }
        // 存在货位，且是当前仓库的
        List<LoactionDTO> filterList = loactionDTOS.stream()
            .filter(p -> Objects.equals(p.getWarehouseId(), warehouseId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            LOGGER.info("出库位在仓库：{}, 不存在：{}", warehouseId, JSON.toJSONString(loactionDTOS));
            return null;
        }
        return filterList;
    }

    /**
     * 修改拣货任务的周转区
     *
     * @param updateBatchTaskLocaitonDTO
     */
    public void updateBatchTaskLocation(UpdateBatchTaskLocaitonDTO updateBatchTaskLocaitonDTO) {
        LOGGER.info("修改拣货任务的周转区参数：{}", JSON.toJSONString(updateBatchTaskLocaitonDTO));
        // 根据播种任务编号查询播种任务列表
        List<SowTaskPO> sowTaskPOList = sowTaskMapper.findSowTaskByTaskNos(updateBatchTaskLocaitonDTO.getOrgId(),
            updateBatchTaskLocaitonDTO.getSowTaskNo());
        long sowTaskCount =
            sowTaskPOList.stream().filter(n -> SowTaskStateEnum.已播种.getType() == n.getState().byteValue()).count();
        if (sowTaskCount > 0) {
            throw new BusinessValidateException("操作失败，存在已播种的播种任务！");
        }
        batchTaskMapper.updateTaskLocation(updateBatchTaskLocaitonDTO.getSowTaskNo(),
            updateBatchTaskLocaitonDTO.getOperateUser(), updateBatchTaskLocaitonDTO.getLocationId(),
            updateBatchTaskLocaitonDTO.getLocationName());
    }

    /**
     * 通过仓库id获取待播种的拣货任务信息(分区分单)
     *
     * @param orgId
     * @param warehouseId
     * @return
     */
    public List<BatchTaskDTO> listBatchTaskByWarehouse(Integer orgId, Integer warehouseId) {
        // 通过仓库信息获取集货位中的播种任务
        // List<SowTaskPO> sowOrderPOS = sowTaskMapper.findSowTaskByWarehouseId(orgId, warehouseId);
        // 通过仓库信息获取拣货中的波次关联播种任务
        List<SowTaskPO> sowOrderPOS = sowTaskMapper.findPickingSowTaskByWarehouseId(orgId, warehouseId);
        List<String> sowTaskNos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sowOrderPOS)) {
            // 通过播种任务编号获取拣货任务信息
            Map<String, List<SowTaskPO>> SowTaskMap =
                sowOrderPOS.stream().collect(Collectors.groupingBy(SowTaskPO::getBatchNo));
            SowTaskMap.forEach((batchNo, sowTask) -> {
                List<SowTaskPO> finishedSowTask = sowTask.stream()
                    .filter(s -> s.getState() != SowTaskStateEnum.已播种.getType()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(finishedSowTask)) {
                    sowTaskNos.addAll(sowTask.stream().map(SowTaskPO::getSowTaskNo).collect(Collectors.toList()));
                }
            });

        }

        if (CollectionUtils.isNotEmpty(sowTaskNos)) {
            return listBatchTaskBySowTaskNos(sowTaskNos, null, orgId);
        } else {
            return null;
        }
    }

    /**
     * 根据SKU查询二级仓生成内配单的订单中已入库未出库数量 - 盘点计算差异
     *
     * @return
     */
    public Map<Long, BigDecimal> findCreateAllocationPickedCountForStoreCheck(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds, Map<Long, List<Long>> relationGroupMap) {
        AssertUtils.notNull(orgId, "城市编号不能为空");
        AssertUtils.notNull(warehouseId, "仓库编号不能为空");
        AssertUtils.notEmpty(productSkuIds, "SkuId不能为空");
        if (relationGroupMap == null) {
            return batchTaskQueryBL.findPickedCountBySkuIdForCreateAllocation(orgId, warehouseId, productSkuIds, true);
        }
        productSkuIds = productSkuIds.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        List<Long> refSkuIdList =
            relationGroupMap.values().stream().filter(e -> e != null && CollectionUtils.isNotEmpty(e))
                .flatMap(e -> e.stream()).collect(Collectors.toList());
        List<Long> totalSkuIdList = new ArrayList<>(productSkuIds.size() * 2);
        totalSkuIdList.addAll(productSkuIds);
        totalSkuIdList.addAll(refSkuIdList);
        Map<Long, BigDecimal> totalPickedCountMap =
            batchTaskQueryBL.findPickedCountBySkuIdForCreateAllocation(orgId, warehouseId, productSkuIds, false);
        Map<Long, BigDecimal> result = new HashMap<>();
        for (Long skuId : productSkuIds) {
            BigDecimal count = BigDecimal.ZERO;
            List<Long> refSkuIds = relationGroupMap.get(skuId);
            if (CollectionUtils.isNotEmpty(refSkuIds)) {
                // 增加关联产品数量
                for (Long refSkuId : refSkuIds) {
                    count = count.add(ObjectUtils.defaultIfNull(totalPickedCountMap.get(refSkuId), BigDecimal.ZERO));
                }
            }
            // 加上自己的数量
            count = count.add(ObjectUtils.defaultIfNull(totalPickedCountMap.get(skuId), BigDecimal.ZERO));
            // 记录结果
            result.put(skuId, count);
        }
        return result;
    }

    /**
     * 集货任务完成
     *
     * @param completeReceiveTaskDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void completeReceiveTask(CompleteReceiveTaskDTO completeReceiveTaskDTO) {
        Integer orgId = completeReceiveTaskDTO.getOrgId();
        String operator = completeReceiveTaskDTO.getOperator();
        Integer operatorUserId = completeReceiveTaskDTO.getOperatorUserId();
        // 根据订单id查询订单项
        List<OutStockOrderItemPO> outStockOrderItemPOS = outStockOrderItemMapper
            .findByOutstockorderIdList(Arrays.asList(completeReceiveTaskDTO.getOutStockOrderId()));

        if (CollectionUtils.isEmpty(outStockOrderItemPOS)) {
            throw new BusinessValidateException("找不到对应订单");
        }

        // 根据拣货任务id修改出库位
        List<String> batchTaskIds =
            outStockOrderItemPOS.stream().map(OutStockOrderItemPO::getBatchtaskId).collect(Collectors.toList());
        batchTaskMapper.updateTaskLocationByIds(orgId, batchTaskIds, completeReceiveTaskDTO.getToLocationId(),
            completeReceiveTaskDTO.getToLocationName(), operator);

        // 批量完成播种任务
        List<String> sowTaskNos = outStockOrderItemPOS.stream().filter(item -> item.getSowTaskNo() != null)
            .map(OutStockOrderItemPO::getSowTaskNo).distinct().collect(Collectors.toList());

        sowTaskNos.forEach(sowTaskNo -> {
            SowOrderSaveDTO sowOrderSaveDTO = new SowOrderSaveDTO();
            sowOrderSaveDTO.setOrgId(orgId);
            sowOrderSaveDTO.setWarehouseId(completeReceiveTaskDTO.getWarehouseId());
            sowOrderSaveDTO.setSowTaskNo(sowTaskNo);
            sowOrderSaveDTO.setOperatorUserName(operator);
            sowOrderSaveDTO.setOperatorUserId(operatorUserId);
            sowManagerBL.saveSowOrder(sowOrderSaveDTO);
        });
    }

    /**
     * 拣货任务修改出库位
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @DistributeLock(conditions = "#batchTaskUpdateLocationDTO.batchTaskIdList[0]", sleepMills = 3000,
        key = "updateBatchTaskLocationByCk", lockType = DistributeLock.LockType.WAITLOCK)
    public void updateBatchTaskLocationByCk(BatchTaskUpdateLocationDTO batchTaskUpdateLocationDTO) {
        LOGGER.info("拣货任务修改出库位参数：{}", JSON.toJSONString(batchTaskUpdateLocationDTO));

        List<BatchTaskPO> batchTaskPOList =
            batchTaskMapper.findTasksByBatchTaskId(batchTaskUpdateLocationDTO.getBatchTaskIdList());
        if (CollectionUtils.isEmpty(batchTaskPOList)
            || batchTaskPOList.size() != batchTaskUpdateLocationDTO.getBatchTaskIdList().size()) {
            throw new BusinessValidateException(
                "拣货任务不存在：" + JSON.toJSONString(batchTaskUpdateLocationDTO.getBatchTaskIdList()));
        }

        batchTaskPOList = getNeedUpdateBatchTaskList(batchTaskUpdateLocationDTO, batchTaskPOList);

        LOGGER.info("查询出拣货任务为：{}", JSON.toJSONString(batchTaskPOList));

        // 1、已完成的拣货任务项需要移库
        batchTaskPOList.forEach(batchTaskPO -> {
            if (batchTaskPO.getSowTaskId() != null) {
                throw new BusinessValidateException("存在播种任务的拣货任务不允许修改出库位：" + batchTaskPO.getId());
            }
        });

        Map<String,
            String> batchTaskPalletMap = batchTaskPOList.stream()
                .filter(m -> Objects.nonNull(getCkChangedPalletNo(m, batchTaskUpdateLocationDTO))).collect(
                    Collectors.toMap(BatchTaskPO::getId, m -> getCkChangedPalletNo(m, batchTaskUpdateLocationDTO)));

        List<String> batchTaskIds =
            batchTaskPOList.stream().map(BatchTaskPO::getId).distinct().collect(Collectors.toList());
        List<String> batchNoList =
            batchTaskPOList.stream().map(BatchTaskPO::getBatchNo).distinct().collect(Collectors.toList());

        Integer orgId = Integer.parseInt(batchTaskPOList.get(0).getOrgId());

        List<BatchPO> batchPOList = batchMapper.findBatchByNos(batchNoList, orgId);
        Map<String, BatchPO> batchMap = batchPOList.stream().collect(Collectors.toMap(BatchPO::getId, v -> v));

        List<BatchTaskPO> updateBatchTask = batchTaskPOList.stream().filter(batchTaskPO -> {
            return !Objects.equals(batchTaskPO.getToLocationId(), batchTaskUpdateLocationDTO.getLocationId());
        }).map(batchTaskPO -> {
            // (1)更新拣货任务的出库位信息
            List<String> updateBatchTaskIds = new ArrayList<>();
            updateBatchTaskIds.add(batchTaskPO.getId());
            String palletNo = batchTaskPalletMap.get(batchTaskPO.getId());
            batchTaskMapper.updateTaskLocationByTaskId(updateBatchTaskIds, batchTaskUpdateLocationDTO.getOperateUser(),
                batchTaskUpdateLocationDTO.getLocationId(), batchTaskUpdateLocationDTO.getLocationName(), palletNo);
            LOGGER.info("拣货任务：{}，修改出库位：{}[{}]；托盘号 : {}", JSON.toJSONString(updateBatchTaskIds),
                batchTaskUpdateLocationDTO.getLocationId(), batchTaskUpdateLocationDTO.getLocationName(),
                JSON.toJSONString(palletNo));
            return batchTaskPO;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(updateBatchTask)) {
            if (!org.springframework.util.CollectionUtils.isEmpty(batchTaskPalletMap)) {
                notifyTmsOutStockLocation(batchTaskPOList, batchMap);
            }
            return;
        }

        // 修改托盘信息的出库位
        orderLocationPalletBL.modBatchTaskLocation(batchTaskPOList, batchTaskUpdateLocationDTO);
        transferLocationByCkChanged(batchTaskPOList, batchTaskUpdateLocationDTO);

        // 3、通知TMS更新出库位
        notifyTmsOutStockLocation(updateBatchTask, batchMap);
    }

    private List<BatchTaskPO> getNeedUpdateBatchTaskList(BatchTaskUpdateLocationDTO batchTaskUpdateLocationDTO,
        List<BatchTaskPO> inputBatchTaskPOList) {
        List<BatchTaskPO> needPalletForStockBatchTaskList =
            getNeedPalletForStockBatchTask(batchTaskUpdateLocationDTO, inputBatchTaskPOList);

        List<BatchTaskPO> pickingByOrderList =
            checkAndGetUpdateBatchTaskPOList(batchTaskUpdateLocationDTO, inputBatchTaskPOList);

        List<BatchTaskPO> totalOrderList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(needPalletForStockBatchTaskList)) {
            totalOrderList.addAll(needPalletForStockBatchTaskList);
        }
        if (CollectionUtils.isNotEmpty(pickingByOrderList)) {
            totalOrderList.addAll(pickingByOrderList);
        }

        return totalOrderList.stream().collect(Collectors.toMap(BatchTaskPO::getId, v -> v, (v1, v2) -> v1)).values()
            .stream().collect(Collectors.toList());
    }

    private List<BatchTaskPO> getNeedPalletForStockBatchTask(BatchTaskUpdateLocationDTO batchTaskUpdateLocationDTO,
        List<BatchTaskPO> inputBatchTaskPOList) {
        BatchTaskPO batchTaskPO = inputBatchTaskPOList.stream().findFirst().get();
        Integer warehouseId = batchTaskPO.getWarehouseId();
        Integer orgId = Integer.valueOf(batchTaskPO.getOrgId());
        boolean openTrayPositionLocation = globalCache.getOpenTrayPositionLocation(warehouseId);
        if (BooleanUtils.isFalse(openTrayPositionLocation)) {
            return inputBatchTaskPOList;
        }

        List<BatchTaskPO> drinkSortBatchTaskList = inputBatchTaskPOList.stream()
            .filter(m -> BatchTaskTypeEnum.酒饮按订单分区拣货.getType().equals(m.getBatchTaskType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(drinkSortBatchTaskList)) {
            return inputBatchTaskPOList;
        }

        List<String> batchTaskIds =
            drinkSortBatchTaskList.stream().map(BatchTaskPO::getId).distinct().collect(Collectors.toList());

        List<OrderItemTaskInfoPO> batchTaskOrderItemList =
            orderItemTaskInfoMapper.listTaskInfoByBatchTaskIds(batchTaskIds);

        List<Long> refOrderIds = batchTaskOrderItemList.stream().map(OrderItemTaskInfoPO::getRefOrderId).distinct()
            .collect(Collectors.toList());

        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(refOrderIds);

        List<String> totalBatchTaskIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getBatchTaskId)
            .distinct().collect(Collectors.toList());

        List<BatchTaskPO> totalBatchTaskPOList = batchTaskMapper.findBatchTaskByIds(totalBatchTaskIds);

        List<BatchTaskPO> finishBatchTaskList = totalBatchTaskPOList.stream()
            .filter(m -> m.getTaskState().equals(TaskStateEnum.已完成.getType())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(finishBatchTaskList)) {
            BatchTaskPO tmpBatchTaskPO = finishBatchTaskList.stream().findFirst().get();
            throw new BusinessValidateException("拣货任务" + tmpBatchTaskPO.getBatchTaskNo() + "已完成，已指定出库位"
                + tmpBatchTaskPO.getToLocationName() + "，不允许修改出库位。");
        }

        return totalBatchTaskPOList;
    }

    // 如果开启实时拣货，且没开启边拣边播， 且是按订单拣货，则需更新 拣货任务对应 用户下所有 订单对应的拣货任务的出库位
    private List<BatchTaskPO> checkAndGetUpdateBatchTaskPOList(BatchTaskUpdateLocationDTO batchTaskUpdateLocationDTO,
        List<BatchTaskPO> inputBatchTaskPOList) {
        BatchTaskPO batchTaskPO = inputBatchTaskPOList.stream().findFirst().get();
        Integer warehouseId = batchTaskPO.getWarehouseId();
        Integer orgId = Integer.valueOf(batchTaskPO.getOrgId());

        // 是否开启按订单实时拣货
        boolean openWarehouseRealTimePickingByOrder = globalCache.getWarehouseRealTimePickingByOrder(warehouseId);
        // 是否开启边拣边播
        boolean openPalletForSortingMode = globalCache.getOpenTrayPositionLocation(warehouseId);

        if (BooleanUtils.isFalse(openWarehouseRealTimePickingByOrder)) {
            return inputBatchTaskPOList;
        }
        if (BooleanUtils.isTrue(openPalletForSortingMode)) {
            return inputBatchTaskPOList;
        }

        List<BatchTaskPO> pickByOrderBatchTaskList = inputBatchTaskPOList.stream()
            .filter(m -> PickingTypeEnum.订单拣货.getType() == m.getPickingType()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(pickByOrderBatchTaskList)) {
            return inputBatchTaskPOList;
        }

        List<String> batchTaskIds =
            pickByOrderBatchTaskList.stream().map(BatchTaskPO::getId).distinct().collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoByBatchTaskIds(batchTaskIds);

        List<Long> outStockOrderIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderPOList =
            outStockOrderMapper.findSimpleInfoByIds(outStockOrderIds, warehouseId);
        List<Integer> addressIds =
            outStockOrderPOList.stream().map(OutStockOrderPO::getAddressId).distinct().collect(Collectors.toList());

        SameUserOrderInBatchQueryPO queryPO = new SameUserOrderInBatchQueryPO();
        queryPO.setAddressIdList(addressIds);
        queryPO.setOrderCreateTime(DateUtils.getMinusMonthFirstDay(1));
        queryPO.setStateList(Arrays.asList(OutStockOrderStateEnum.已拣货.getType(), OutStockOrderStateEnum.待拣货.getType(),
            OutStockOrderStateEnum.待调度.getType(), OutStockOrderStateEnum.拣货中.getType()));
        queryPO.setWarehouseId(warehouseId);
        List<OutStockOrderPO> sameUserOrderInBatch = outStockOrderMapper.findSameUserOrderInBatch(queryPO);
        if (CollectionUtils.isEmpty(sameUserOrderInBatch)) {
            return inputBatchTaskPOList;
        }

        List<Long> sameUserOrderIdsInBatch =
            sameUserOrderInBatch.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());

        List<OrderItemTaskInfoPO> sameUserOrderItemTaskInfoList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(sameUserOrderIdsInBatch);
        if (CollectionUtils.isEmpty(sameUserOrderItemTaskInfoList)) {
            return inputBatchTaskPOList;
        }

        List<String> sameUserBatchTaskIdList = sameUserOrderItemTaskInfoList.stream()
            .map(OrderItemTaskInfoPO::getBatchTaskId).distinct().collect(Collectors.toList());

        List<BatchTaskPO> sameUserBatchTaskList = batchTaskMapper.findBatchTaskByIds(sameUserBatchTaskIdList);

        Optional<BatchTaskPO> finishBatchTaskOptional =
            sameUserBatchTaskList.stream().filter(m -> TaskStateEnum.已完成.getType() == m.getTaskState()).findAny();
        if (finishBatchTaskOptional.isPresent()) {
            BatchTaskPO tmpBatchTaskPO = finishBatchTaskOptional.get();
            throw new BusinessValidateException("拣货任务" + tmpBatchTaskPO.getBatchTaskNo() + "已设置出库位"
                + tmpBatchTaskPO.getToLocationName() + ", 不允许重新设置！");
        }

        List<Long> refOrderIds = sameUserOrderItemTaskInfoList.stream().map(OrderItemTaskInfoPO::getRefOrderId)
            .distinct().collect(Collectors.toList());

        // 验证订单是否已经绑定托盘和出库位
        OrderLocationPalletQueryDTO orderLocationPalletQueryDTO = new OrderLocationPalletQueryDTO();
        orderLocationPalletQueryDTO.setWarehouseId(warehouseId);
        orderLocationPalletQueryDTO.setOrderIdList(refOrderIds);
        List<OrderLocationPalletDTO> orderLocationPalletDTOList =
            orderLocationPalletBL.findPalletByCondition(orderLocationPalletQueryDTO);

        if (!CollectionUtils.isEmpty(orderLocationPalletDTOList)) {
            throwOrderBindPalletException(orderLocationPalletDTOList, orgId, warehouseId);
        }

        Map<String, BatchTaskPO> batchTaskPOMap =
            inputBatchTaskPOList.stream().collect(Collectors.toMap(BatchTaskPO::getId, v -> v));

        Map<String, BatchTaskPO> sameUserBatchTaskMap =
            sameUserBatchTaskList.stream().collect(Collectors.toMap(BatchTaskPO::getId, v -> v));

        batchTaskPOMap.putAll(sameUserBatchTaskMap);

        return batchTaskPOMap.values().stream().collect(Collectors.toList());
    }

    private void throwOrderBindPalletException(List<OrderLocationPalletDTO> orderLocationPalletDTOList, Integer orgId,
        Integer warehouseId) {
        List<Long> orderIds = orderLocationPalletDTOList.stream().map(OrderLocationPalletDTO::getOrderId).distinct()
            .collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findSimpleByIds(orderIds, orgId, warehouseId);
        Map<Long, String> orderNoIdMap = outStockOrderPOList.stream()
            .collect(Collectors.toMap(OutStockOrderPO::getId, OutStockOrderPO::getReforderno));
        StringBuilder stringBuilder = new StringBuilder();
        for (OrderLocationPalletDTO orderLocationPalletDTO : orderLocationPalletDTOList) {
            String orderNo = orderNoIdMap.get(orderLocationPalletDTO.getOrderId());
            stringBuilder.append("订单 ");
            stringBuilder.append(orderNo);
            stringBuilder.append(" 已绑定托盘");
            stringBuilder.append(orderLocationPalletDTO.getLocationName());
            stringBuilder.append("；");
        }
        stringBuilder.append("请勿重复绑定！");

        throw new BusinessValidateException(stringBuilder.toString());
    }

    private void notifyTmsOutStockLocation(List<BatchTaskPO> updateBatchTask, Map<String, BatchPO> batchMap) {
        updateBatchTask.stream().collect(Collectors.groupingBy(p -> p.getBatchId()))
            .forEach((batchId, batchTaskList) -> {
                if (CollectionUtils.isNotEmpty(batchTaskList)) {
                    BatchPO batchPO = batchMap.get(batchId);
                    if (batchPO != null
                        && Objects.equals(batchPO.getState(), BatchStateEnum.PICKINGEND.getType().byteValue())) {
                        List<Long> lstOrderIds =
                            outStockOrderMapper.findOutStockOrderIdsByBatchId(batchTaskList.get(0).getBatchId());
                        List<BatchTaskPO> batchTaskPOS =
                            batchTaskMapper.findTaskByBatchNo(Arrays.asList(batchTaskList.get(0).getBatchNo()));
                        outStockOrderBL.pureSyncOrderToLocation(batchTaskList.get(0).getBatchNo(), batchTaskPOS,
                            lstOrderIds);
                    }
                }
            });
    }

    private String getCkChangedPalletNo(BatchTaskPO batchTaskPO,
        BatchTaskUpdateLocationDTO batchTaskUpdateLocationDTO) {
        if (CollectionUtils.isEmpty(batchTaskUpdateLocationDTO.getPalletNoList())) {
            return null;
        }
        String palletNoList =
            "#".concat(batchTaskUpdateLocationDTO.getPalletNoList().stream().collect(Collectors.joining("#")));
        if (StringUtils.isBlank(batchTaskPO.getToPalletNo())) {
            return palletNoList.trim();
        }

        return batchTaskPO.getToPalletNo().concat(palletNoList);
    }

    // 老代码，直接复制，先抽出来
    private void transferLocationByCkChanged(List<BatchTaskPO> batchTaskPOList,
        BatchTaskUpdateLocationDTO batchTaskUpdateLocationDTO) {
        if (CollectionUtils.isEmpty(batchTaskPOList)) {
            return;
        }
        WarehouseConfigDTO warehouseConfigDTO =
            warehouseConfigService.getConfigByWareHouseId(batchTaskPOList.get(0).getWarehouseId());

        Map<BatchTaskPO, List<PickUpDTO>> pickUpMap = new HashMap<>();
        batchTaskUpdateLocationDTO.getBatchTaskIdList().forEach(batchTaskId -> {
            Optional<BatchTaskPO> optional =
                batchTaskPOList.stream().filter(p -> Objects.equals(p.getId(), batchTaskId)).findAny();
            if (!optional.isPresent()) {
                throw new BusinessValidateException("找不到该拣货任务：" + batchTaskId);
            }
            BatchTaskPO batchTaskPO = optional.get();
            if (Objects.equals(batchTaskPO.getToLocationId(), batchTaskUpdateLocationDTO.getLocationId())) {
                LOGGER.info("该拣货任务的出库位已经是当前要修改的出库位，跳过修改：{}", batchTaskId);
                return;
            }

            // (2)如果拣货任务已经拣过货，则需要把原出库位产品的库存转移到现出库位中
            if (batchTaskPO.getToLocationId() == null) {
                LOGGER.info("该拣货任务找不到原出库位，跳过移库：{}", batchTaskId);
                return;
            }
            List<BatchTaskItemDTO> batchTaskItemDTOList = batchTaskItemMapper.findBatchTaskItemDtoListById(batchTaskId);
            if (CollectionUtils.isEmpty(batchTaskItemDTOList)) {
                return;
            }
            // 找出已拣货的拣货任务项
            batchTaskItemDTOList = batchTaskItemDTOList.stream()
                .filter(p -> Objects.equals(p.getTaskState(), TaskStateEnum.已完成.getType())
                    && p.getOverSortCount().subtract(p.getRollbackUnitCount()).compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(batchTaskItemDTOList)) {
                return;
            }
            List<PickUpDTO> pickUpDTOList = new ArrayList<>();
            // 查找拣货任务项关联项
            List<String> batchTaskItemIds =
                batchTaskItemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList());
            List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
                orderItemTaskInfoMapper.listTaskInfoAndDetailByBatchTaskItemIds(batchTaskItemIds);

            batchTaskItemDTOList.forEach(batchTaskItemDTO -> {
                List<PickUpDTO> tempPickUpDTOList = new ArrayList<>();
                // 当前拣货任务项关联项
                List<OrderItemTaskInfoPO> nowBatchTaskItemInfoList = orderItemTaskInfoPOList.stream()
                    .filter(p -> Objects.equals(p.getBatchTaskItemId(), batchTaskItemDTO.getId()))
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(nowBatchTaskItemInfoList)) {
                    nowBatchTaskItemInfoList.forEach(info -> {
                        // 关联明细
                        if (CollectionUtils.isNotEmpty(info.getDetailList())) {
                            info.getDetailList().forEach(detail -> {
                                if (detail.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0) {
                                    PickUpDTO pickUpDTO = getPickUpDTOByUpdateBatchTaskLocationByCk(batchTaskPO,
                                        batchTaskUpdateLocationDTO.getLocationId(), batchTaskItemDTO,
                                        detail.getUnitTotalCount());
                                    pickUpDTO.setSecOwnerId(detail.getSecOwnerId());
                                    tempPickUpDTOList.add(pickUpDTO);
                                }
                            });
                        }
                    });
                }
                // 兼容老数据
                if (CollectionUtils.isEmpty(tempPickUpDTOList)) {
                    BigDecimal moveCount =
                        batchTaskItemDTO.getOverSortCount().subtract(batchTaskItemDTO.getRollbackUnitCount() != null
                            ? batchTaskItemDTO.getRollbackUnitCount() : BigDecimal.ZERO);
                    PickUpDTO pickUpDTO = getPickUpDTOByUpdateBatchTaskLocationByCk(batchTaskPO,
                        batchTaskUpdateLocationDTO.getLocationId(), batchTaskItemDTO, moveCount);
                    tempPickUpDTOList.add(pickUpDTO);
                }
                // 移库数据
                if (CollectionUtils.isNotEmpty(tempPickUpDTOList)) {
                    pickUpDTOList.addAll(tempPickUpDTOList);
                }
            });

            // 移库数据
            if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
                pickUpMap.put(batchTaskPO, pickUpDTOList);
            }
        });

        // 2、移库
        if (pickUpMap != null && pickUpMap.size() > 0) {
            pickUpMap.forEach((batchTaskPO, pickUpDTOList) -> {
                PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
                pickUpChangeRecordDTO.setCityId(Integer.valueOf(batchTaskPO.getOrgId()));
                pickUpChangeRecordDTO.setOrderId(batchTaskPO.getId());
                pickUpChangeRecordDTO.setOrderNo(batchTaskPO.getBatchTaskNo());
                pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.修改出库位.name());
                pickUpChangeRecordDTO.setCreateUser(batchTaskUpdateLocationDTO.getOperateUser());
                LOGGER.info("拣货任务修改出库位移库：{}, pickUpDTOList：{}", JSON.toJSONString(pickUpChangeRecordDTO),
                    JSON.toJSONString(pickUpDTOList));
                iBatchInventoryManageService.updateLocationByCk(pickUpDTOList, pickUpChangeRecordDTO);
            });
        }
    }

    private PickUpDTO getPickUpDTOByUpdateBatchTaskLocationByCk(BatchTaskPO batchTaskPO, Long toLocationId,
        BatchTaskItemDTO batchTaskItemDTO, BigDecimal moveCount) {
        PickUpDTO pickUpDTO = new PickUpDTO();
        pickUpDTO.setFromLocationId(batchTaskPO.getToLocationId());
        pickUpDTO.setLocationId(toLocationId);
        pickUpDTO.setProductSkuId(batchTaskItemDTO.getSkuId());
        pickUpDTO.setWarehouseId(batchTaskPO.getWarehouseId());
        pickUpDTO.setFromChannel(batchTaskItemDTO.getChannel().intValue());
        pickUpDTO.setFromSource(batchTaskItemDTO.getSource().intValue());
        pickUpDTO.setToChannel(batchTaskItemDTO.getChannel().intValue());
        pickUpDTO.setToSource(batchTaskItemDTO.getSource().intValue());
        pickUpDTO.setCount(moveCount);
        return pickUpDTO;
    }

    /**
     * 订单修改出库位
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateOrderLocationByCk(OutStockOrderUpdateLocationDTO orderUpdateLocationDTO) {
        AssertUtils.notNull(orderUpdateLocationDTO, "订单修改出库位参数不能为空");
        AssertUtils.notNull(orderUpdateLocationDTO.getOrderId(), "订单ID不能为空");
        AssertUtils.notNull(orderUpdateLocationDTO.getOrderNo(), "订单编号不能为空");
        AssertUtils.notNull(orderUpdateLocationDTO.getOldLocationId(), "原出库位ID不能为空");
        AssertUtils.notNull(orderUpdateLocationDTO.getOldLocationName(), "原出库位名称不能为空");
        AssertUtils.notNull(orderUpdateLocationDTO.getLocationId(), "出库位ID不能为空");
        AssertUtils.notNull(orderUpdateLocationDTO.getLocationName(), "出库位名称不能为空");
        AssertUtils.notNull(orderUpdateLocationDTO.getOperateUser(), "操作人不能为空");
        LOGGER.info("订单修改出库位参数：{}", JSON.toJSONString(orderUpdateLocationDTO));

        // 根据订单ID查询订单
        OutStockOrderPO outStockOrderPO = outStockOrderMapper.selectByRefOrderId(orderUpdateLocationDTO.getOrderId());
        if (outStockOrderPO == null) {
            throw new BusinessValidateException("找不到该订单！");
        }
        // 根据订单ID查询订单项
        List<OutStockOrderItemPO> orderItemPOS =
            outStockOrderItemMapper.findByOutstockorderIdList(Arrays.asList(orderUpdateLocationDTO.getOrderId()));
        if (CollectionUtils.isEmpty(orderItemPOS)) {
            throw new BusinessValidateException("找不到订单项！");
        }
        if (Objects.equals(orderUpdateLocationDTO.getOldLocationId(), orderUpdateLocationDTO.getLocationId())) {
            throw new BusinessValidateException("修改的出库位与原出库位相同，请选择其他出库位！");
        }
        // 1、更新订单项出库位
        List<Long> orderItemIds = orderItemPOS.stream().map(p -> p.getId()).collect(Collectors.toList());
        outStockOrderItemMapper.updateLocationBatch(orderUpdateLocationDTO.getLocationId(),
            orderUpdateLocationDTO.getLocationName(), orderItemIds);
        LOGGER.info("订单：{}，修改出库位：{}[{}]", orderUpdateLocationDTO.getOrderNo(), orderUpdateLocationDTO.getLocationId(),
            orderUpdateLocationDTO.getLocationName());

        // 2、移库
        List<OutStockOrderItemPO> pickOrderItemPOS = orderItemPOS.stream()
            .filter(p -> p.getUnittotalcount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(pickOrderItemPOS)) {
            List<PickUpDTO> pickUpDTOList = new ArrayList<>();
            // 查询订单项detail
            List<Long> pickOrderItemIds = pickOrderItemPOS.stream().map(p -> p.getId()).collect(Collectors.toList());
            List<OutStockOrderItemDetailPO> orderItemDetailPOList =
                outStockOrderItemDetailCommMapper.findByItemIds(outStockOrderPO.getOrgId(), pickOrderItemIds);

            pickOrderItemPOS.forEach(orderItemPO -> {
                List<PickUpDTO> tempPickUpDTOList = new ArrayList<>();
                // 当前订单项detail
                List<OutStockOrderItemDetailPO> nowOrderItemDetailPOList = orderItemDetailPOList.stream()
                    .filter(p -> Objects.equals(p.getOutStockOrderItemId(), orderItemPO.getId()))
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(nowOrderItemDetailPOList)) {
                    nowOrderItemDetailPOList.forEach(detail -> {
                        if (detail.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0) {
                            PickUpDTO pickUpDTO = getPickUpDTOByupdateOrderLocationByCk(orderUpdateLocationDTO,
                                orderItemPO, outStockOrderPO.getWarehouseId(), detail.getUnitTotalCount());
                            pickUpDTO.setSecOwnerId(detail.getSecOwnerId());
                            tempPickUpDTOList.add(pickUpDTO);
                        }
                    });
                }
                // 兼容老数据
                if (CollectionUtils.isEmpty(tempPickUpDTOList)) {
                    PickUpDTO pickUpDTO = getPickUpDTOByupdateOrderLocationByCk(orderUpdateLocationDTO, orderItemPO,
                        outStockOrderPO.getWarehouseId(), orderItemPO.getUnittotalcount());
                    tempPickUpDTOList.add(pickUpDTO);
                }
                // 移库数据
                if (CollectionUtils.isNotEmpty(tempPickUpDTOList)) {
                    pickUpDTOList.addAll(tempPickUpDTOList);
                }
            });

            // 移库操作
            if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
                PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
                pickUpChangeRecordDTO.setCityId(outStockOrderPO.getOrgId());
                pickUpChangeRecordDTO.setOrderId(orderUpdateLocationDTO.getOrderId().toString());
                pickUpChangeRecordDTO.setOrderNo(orderUpdateLocationDTO.getOrderNo());
                pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.修改出库位.name());
                pickUpChangeRecordDTO.setCreateUser(orderUpdateLocationDTO.getOperateUser());
                iBatchInventoryManageService.updateLocationByCk(pickUpDTOList, pickUpChangeRecordDTO);
            }
        }

        // 3、订单出库位变更同步给TMS
        WaveNoModel waveNoModel = new WaveNoModel();
        waveNoModel.setWaveNo(outStockOrderPO.getBatchno());
        waveNoModel.setWarehouseId(outStockOrderPO.getWarehouseId());
        List<WaveNoItemModel> itemList = new ArrayList<>();

        WaveNoItemModel itemModel = new WaveNoItemModel();
        itemModel.setBusinessNo(orderUpdateLocationDTO.getOrderNo());
        itemModel.setLocationId(orderUpdateLocationDTO.getLocationId());
        itemModel.setLocationName(orderUpdateLocationDTO.getLocationName()
            .concat(getPalletNo(outStockOrderPO.getWarehouseId(), orderUpdateLocationDTO.getOrderNo())));
        itemList.add(itemModel);
        waveNoModel.setOrderItems(itemList);
        orderToLocationSyncMQ.send(waveNoModel);

        orderCenterBL
            .completePickNotify(completePickNotifyDTOConvertor.convert(outStockOrderPO, orderUpdateLocationDTO));
    }

    /**
     * 同步托盘信息给tms
     *
     * @param dto
     */
    public void sendOrderLocationInfoToTms(final OrderLocationInfoToTmsDTO dto) {
        List<OutStockOrderPO> outStockOrderPOList =
            outStockOrderMapper.listByRefOrderNo(dto.getOrderNoList(), dto.getOrgId(), dto.getWarehouseId());

        Map<String, List<OutStockOrderPO>> orderBatchMap =
            outStockOrderPOList.stream().collect(Collectors.groupingBy(OutStockOrderPO::getBatchno));
        for (Map.Entry<String, List<OutStockOrderPO>> entry : orderBatchMap.entrySet()) {
            entry.getValue().forEach(outStockOrderPO -> {
                orderCenterBL.completePickNotify(completePickNotifyDTOConvertor.convert(dto, outStockOrderPO));
            });
        }

    }

    private String getPalletNo(Integer warehouseId, String orderNo) {
        OrderLocationPalletQueryDTO queryDTO = new OrderLocationPalletQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setOrderNo(orderNo);
        List<OrderLocationPalletDTO> palletList = orderLocationPalletBL.findPalletByCondition(queryDTO);
        if (CollectionUtils.isEmpty(palletList)) {
            return "";
        }

        return "#" + palletList.stream().map(OrderLocationPalletDTO::getPalletNo).collect(Collectors.joining("#"));
    }

    private PickUpDTO getPickUpDTOByupdateOrderLocationByCk(OutStockOrderUpdateLocationDTO orderUpdateLocationDTO,
        OutStockOrderItemPO orderItemPO, Integer warehouseId, BigDecimal moveCount) {
        PickUpDTO pickUpDTO = new PickUpDTO();
        pickUpDTO.setFromLocationId(orderUpdateLocationDTO.getOldLocationId());
        pickUpDTO.setLocationId(orderUpdateLocationDTO.getLocationId());
        pickUpDTO.setProductSkuId(orderItemPO.getSkuid());
        pickUpDTO.setWarehouseId(warehouseId);
        pickUpDTO.setFromChannel(orderItemPO.getChannel().intValue());
        pickUpDTO.setFromSource(orderItemPO.getSource().intValue());
        pickUpDTO.setToChannel(orderItemPO.getChannel().intValue());
        pickUpDTO.setToSource(orderItemPO.getSource().intValue());
        pickUpDTO.setCount(moveCount);
        return pickUpDTO;
    }

    /**
     * 根据订单ID获取出库位
     *
     * @param orderId
     * @param orgId
     * @return
     */
    public String getToLocationByOrderId(Long orderId, Integer orgId) {
        return batchTaskMapper.getToLocationByOrderId(orderId, orgId);
    }

    /**
     * 根据订单号获取播种任务
     *
     * @param orderNos
     * @return
     */
    public PageList<SowTaskDTO> findSowByOrderNos(List<String> orderNos, Integer orgId, Integer pageNum,
        Integer pageSize) {
        PageResult<SowTaskDTO> sowTaskDTOS = sowOrderMapper.findByOrderNos(orderNos, orgId, pageNum, pageSize);
        PageList<SowTaskDTO> sowTaskDTOPageList = sowTaskDTOS.toPageList();
        return sowTaskDTOPageList;
    }

    /**
     * 根据ids获取订单信息
     *
     * @param orgId
     * @param refOrderIdList
     * @return
     */
    public List<OutStockOrderDTO> findSimpleOutstockByIds(Integer orgId, List<Long> refOrderIdList) {
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findSimpleByIds(refOrderIdList, orgId, null);
        return WaveOrderConvertor.outStockOrderPOS2OutStockOrderDTOS(outStockOrderPOList);

    }

    /**
     * 修改拣货任务明细（op后台工具）
     */
    public void updateBatchTaskItem(BatchTaskItemDTO batchTaskItemDTO) {
        AssertUtils.notNull(batchTaskItemDTO, "参数不能为空");
        AssertUtils.notNull(batchTaskItemDTO.getId(), "拣货任务项id不能为空");

        BatchTaskItemPO itemPO = batchTaskItemMapper.selectBatchTaskItemById(batchTaskItemDTO.getId());
        if (itemPO == null) {
            throw new BusinessValidateException("拣货任务项不存在！");
        }

        // 1、修改拣货任务项
        BatchTaskItemPO batchTaskItemPO = new BatchTaskItemPO();
        BeanUtils.copyProperties(batchTaskItemDTO, batchTaskItemPO);
        batchTaskItemMapper.updateBatchTaskItem(batchTaskItemPO);
        LOGGER.info("【op后台】修改拣货任务明细：{}", JSON.toJSONString(batchTaskItemPO));

        // 2、更新拣货任务状态
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(itemPO.getBatchTaskId());
        // 当拣货任务未完成时
        if (batchTaskPO != null && !Objects.equals(batchTaskPO.getTaskState(), TaskStateEnum.已完成.getType())) {
            // 若拣货任务项全部完成，则拣货任务状态也更新为已完成
            List<BatchTaskItemDTO> batchTaskItemDTOList =
                batchTaskItemMapper.findBatchTaskItemDtoListById(itemPO.getBatchTaskId());
            if (batchTaskItemDTOList.stream()
                .allMatch(p -> Objects.equals(p.getTaskState(), TaskStateEnum.已完成.getType()))) {
                batchTaskMapper.updateBatchTaskByNo(batchTaskPO.getBatchTaskNo(), TaskStateEnum.已完成.getType());
                LOGGER.info("【op后台】更新拣货任务状态为已完成：{}", batchTaskPO.getBatchTaskNo());
            }
        }
    }

    /**
     * 查询已拣货未出库的拣货任务占用的周转区库存数量
     *
     * @return
     */
    public List<BatchTaskLocationUseCountDTO> listBatchTaskLocationUseCount(Integer cityId, Integer warehouseId) {
        AssertUtils.notNull(cityId, "城市ID不能为空");
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        List<BatchTaskLocationUseCountDTO> resultList =
            batchTaskMapper.listBatchTaskLocationUseCount(cityId, warehouseId);
        LOGGER.info("查询已拣货未出库的拣货任务占用的周转区库存数量, cityId: {}, warehouseId: {}, result: {}", cityId, warehouseId,
            JSON.toJSONString(resultList));
        return resultList;
    }

    /**
     * 查询订单关联拣货任务货位信息
     */
    public List<BatchTaskDTO> findOrderRelatedBatchTaskLocation(Integer orgId, Integer warehouseId, String orderNo) {
        AssertUtils.notNull(orderNo, "订单号不能为空");
        return batchTaskMapper.findOrderRelatedBatchTaskLocation(orgId, warehouseId, orderNo);
    }

    /**
     * 根据拣货任务项id获取实际分配数量
     *
     * @return
     */
    public Map<String, List<OrderItemDetailAllotDTO>> getBatchTaskItemAllotMap(List<String> batchTaskItemIds) {
        AssertUtils.notEmpty(batchTaskItemIds, "拣货任务项项id不能为空");
        // 根据拣货任务项ID查询关联的拣货任务项
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOS =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByBatchTaskItemIds(batchTaskItemIds);
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOS)) {
            return Collections.EMPTY_MAP;
        }
        // 查询二级货主名称
        Map<Long, String> secOwnerMap = new HashMap<>();
        List<Long> secOwnerIds = orderItemTaskInfoPOS.stream().flatMap(p -> p.getDetailList().stream())
            .filter(p -> p.getSecOwnerId() != null).map(p -> p.getSecOwnerId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(secOwnerIds)) {
            List<OwnerDTO> ownerDTOList = ownerService.listOwnerByIds(secOwnerIds);
            if (CollectionUtils.isNotEmpty(ownerDTOList)) {
                ownerDTOList.forEach(p -> {
                    secOwnerMap.put(p.getId(), p.getOwnerName());
                });
            }
        }

        Map<String, List<OrderItemDetailAllotDTO>> resultMap = new HashMap<>();
        // 按拣货任务项ID分组
        Map<String, List<OrderItemTaskInfoPO>> orderItemTaskInfoMap =
            orderItemTaskInfoPOS.stream().collect(Collectors.groupingBy(p -> p.getBatchTaskItemId()));
        orderItemTaskInfoMap.forEach((batchTaskItemId, list) -> {
            List<OrderItemDetailAllotDTO> allotDTOList = new ArrayList<>();
            // 1、二级货主为空单独处理
            List<OrderItemTaskInfoDetailPO> detailPOList = list.stream().flatMap(p -> p.getDetailList().stream())
                .filter(p -> p.getSecOwnerId() == null).collect(Collectors.toList());
            addAllotDTOList(allotDTOList, detailPOList, null);

            // 2、二级货主不为空的，按二级货主分组
            Map<Long, List<OrderItemTaskInfoDetailPO>> detailMap =
                list.stream().flatMap(p -> p.getDetailList().stream()).filter(p -> p.getSecOwnerId() != null)
                    .collect(Collectors.groupingBy(p -> p.getSecOwnerId()));
            if (detailMap != null && detailMap.size() > 0) {
                detailMap.forEach((secOwnerId, detailList) -> {
                    addAllotDTOList(allotDTOList, detailList, secOwnerMap);
                });
            }
            if (CollectionUtils.isNotEmpty(allotDTOList)) {
                resultMap.put(batchTaskItemId, allotDTOList);
            }
        });
        LOGGER.info("根据拣货任务项id获取实际分配数量参数：{}, 结果：{}", JSON.toJSONString(batchTaskItemIds), JSON.toJSONString(resultMap));
        return resultMap;
    }

    /**
     * 根据订单项id获取实际分配数量
     *
     * @return
     */
    public Map<Long, List<OrderItemDetailAllotDTO>> getOrderItemDetailAllotMap(List<Long> orderItemIds,
        Integer warehouseId, boolean isPDAOpreate) {
        AssertUtils.notEmpty(orderItemIds, "订单项id不能为空");
        // 根据订单项ID查询关联的拣货任务项
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOS =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(orderItemIds);
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOS)) {
            return Collections.EMPTY_MAP;
        }

        // 2025-02-20 PDA播种不区分大小件，客户端播种区分
        // 客户端播种，过滤整件项
        if (!isPDAOpreate) {
            WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(warehouseId);
            if (WarehouseConfigConstants.SOW_CONTROL_TYPE_LIQUID_NOT_PACK.equals(warehouseConfigDTO.getSowControlType())
                && ConditionStateEnum.是.getType().equals(warehouseConfigDTO.getRobotLargePick())) {
                return handleLiquidNotPackAllotInfo(orderItemTaskInfoPOS, orderItemIds);
            }
        }

        return handleNormalAllotInfo(orderItemTaskInfoPOS, orderItemIds);
    }

    public Map<Long, List<OrderItemDetailAllotDTO>>
        handleLiquidNotPackAllotInfo(List<OrderItemTaskInfoPO> orderItemTaskInfoPOS, List<Long> orderItemIds) {
        List<String> batchTaskItemIds = orderItemTaskInfoPOS.stream().map(OrderItemTaskInfoPO::getBatchTaskItemId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderItemPO> outStockOrderItemPOList = outStockOrderItemMapper.listByIds(orderItemIds);

        // List<BatchTaskItemPO> batchTaskItemPOList = batchTaskItemMapper.findBatchTaskItemByIds(batchTaskItemIds);
        // 过滤出整件的拣货任务项
        List<OutStockOrderItemPO> packageBatchTaskItemPOList = outStockOrderItemPOList.stream()
            .filter(
                m -> m.getUnittotalcount().divideAndRemainder(m.getSpecquantity())[0].compareTo(BigDecimal.ZERO) != 0)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(packageBatchTaskItemPOList)) {
            return handleNormalAllotInfo(orderItemTaskInfoPOS, orderItemIds);
        }

        Map<Long, OutStockOrderItemPO> packageOrderItemMap =
            packageBatchTaskItemPOList.stream().collect(Collectors.toMap(OutStockOrderItemPO::getId, v -> v));
        Map<Long, List<OrderItemTaskInfoPO>> orderItemTaskInfoBatchTaskItemMap =
            orderItemTaskInfoPOS.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId));

        final List<OrderItemTaskInfoPO> totalOrderItemTaskInfoList = new ArrayList<>();

        for (Map.Entry<Long, List<OrderItemTaskInfoPO>> entry : orderItemTaskInfoBatchTaskItemMap.entrySet()) {
            Long orderItemId = entry.getKey();
            OutStockOrderItemPO orderItemPO = packageOrderItemMap.get(orderItemId);
            // 如果没有，说明没有整件拣货任务项，直接添加全部数据
            if (Objects.isNull(orderItemPO)) {
                totalOrderItemTaskInfoList.addAll(entry.getValue());
                continue;
            }

            BigDecimal[] count = orderItemPO.getUnittotalcount().divideAndRemainder(orderItemPO.getSpecquantity());
            if (count[0].compareTo(BigDecimal.ZERO) == 0) {
                totalOrderItemTaskInfoList.addAll(entry.getValue());
                continue;
            }

            List<OrderItemTaskInfoPO> orderItemTaskInfoPOList = entry.getValue();
            // 排序
            orderItemTaskInfoPOList
                .sort((o1, o2) -> o2.getUnitTotalCount().subtract(o1.getUnitTotalCount()).intValue());
            List<QuantityShareUtils.CountHelper> helperList = orderItemTaskInfoPOList.stream()
                .map(m -> new QuantityShareUtils.CountHelper(m.getId().toString(), m.getUnitTotalCount()))
                .collect(Collectors.toList());

            QuantityShareUtils.CountShareResultHelper countShareResultHelper =
                QuantityShareUtils.shareCount(helperList, count[0].multiply(orderItemPO.getSpecquantity()));

            Map<String, OrderItemTaskInfoPO> orderItemTaskInfoPOMap =
                orderItemTaskInfoPOList.stream().collect(Collectors.toMap(k -> k.getId().toString(), v -> v));

            countShareResultHelper.getShareHelperList().forEach(share -> {
                OrderItemTaskInfoPO orderItemTaskInfoPO = orderItemTaskInfoPOMap.get(share.getId());
                orderItemTaskInfoPO.setUnitTotalCount(share.getCount());
                totalOrderItemTaskInfoList.add(orderItemTaskInfoPO);
            });
        }
        List<OrderItemTaskInfoPO> filterOrderItemTaskInfoList = totalOrderItemTaskInfoList.stream()
            .filter(m -> m.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterOrderItemTaskInfoList)) {
            return Collections.emptyMap();
        }

        return handleNormalAllotInfo(filterOrderItemTaskInfoList, orderItemIds);
    }

    private Map<Long, List<OrderItemDetailAllotDTO>>
        handleNormalAllotInfo(List<OrderItemTaskInfoPO> orderItemTaskInfoPOS, List<Long> orderItemIds) {
        LOGGER.info("orderItemTaskInfoPOS : {}", JSON.toJSONString(orderItemTaskInfoPOS));
        Map<Long, List<OrderItemDetailAllotDTO>> resultMap = new HashMap<>();
        // 按订单项ID分组
        Map<Long, List<OrderItemTaskInfoPO>> orderItemTaskInfoMap =
            orderItemTaskInfoPOS.stream().collect(Collectors.groupingBy(p -> p.getRefOrderItemId()));
        orderItemTaskInfoMap.forEach((orderItemid, list) -> {
            List<OrderItemDetailAllotDTO> allotDTOList = new ArrayList<>();
            // 1、二级货主为空单独处理
            List<OrderItemTaskInfoDetailPO> detailPOList = list.stream().flatMap(p -> p.getDetailList().stream())
                .filter(p -> p.getSecOwnerId() == null).collect(Collectors.toList());
            addAllotDTOList(allotDTOList, detailPOList, null);

            // 2、二级货主不为空的，按二级货主分组
            Map<Long, List<OrderItemTaskInfoDetailPO>> detailMap =
                list.stream().flatMap(p -> p.getDetailList().stream()).filter(p -> p.getSecOwnerId() != null)
                    .collect(Collectors.groupingBy(p -> p.getSecOwnerId()));
            if (detailMap != null && detailMap.size() > 0) {
                detailMap.forEach((secOwnerId, detailList) -> {
                    addAllotDTOList(allotDTOList, detailList, null);
                });
            }
            if (CollectionUtils.isNotEmpty(allotDTOList)) {
                resultMap.put(orderItemid, allotDTOList);
            }
        });
        LOGGER.info("根据订单项id获取实际分配数量参数：{}, 结果：{}", JSON.toJSONString(orderItemIds), JSON.toJSONString(resultMap));
        return resultMap;
    }

    private void addAllotDTOList(List<OrderItemDetailAllotDTO> allotDTOList, List<OrderItemTaskInfoDetailPO> detailList,
        Map<Long, String> secOwnerMap) {
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        OrderItemDetailAllotDTO allotDTO = new OrderItemDetailAllotDTO();
        // 分配数量
        BigDecimal allotCount =
            detailList.stream().map(p -> p.getUnitTotalCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        allotDTO.setSecOwnerId(detailList.get(0).getSecOwnerId());
        // 二级货主名称
        if (secOwnerMap != null && allotDTO.getSecOwnerId() != null) {
            allotDTO.setSecOwnerName(secOwnerMap.get(allotDTO.getSecOwnerId()));
        }
        allotDTO.setOwnerId(detailList.get(0).getOwnerId());
        allotDTO.setProductSpecificationId(detailList.get(0).getProductSpecificationId());
        allotDTO.setOwnerId(detailList.get(0).getOwnerId());
        allotDTO.setUnitTotalCount(allotCount);
        allotDTOList.add(allotDTO);
    }

    /**
     * 根据实际移库数量重新生成OrderItemTaskInfoDetail
     *
     * @param realPickUpDTOList
     */
    public void rebuildOrderItemTaskInfoDetail(List<PickUpDTO> realPickUpDTOList) {
        if (CollectionUtils.isEmpty(realPickUpDTOList)) {
            return;
        }

        List<Long> outStockOrderItemIds =
            realPickUpDTOList.stream().filter(pickUp -> pickUp.getBusinessId() != null).map(pickUp -> {
                return Long.valueOf(pickUp.getBusinessId());
            }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(outStockOrderItemIds)) {
            return;
        }

        List<OrderItemTaskInfoDetailPO> orderItemTaskInfoDetailPOS = new ArrayList<>();
        // 根据订单项id查找关联信息
        List<OrderItemTaskInfoPO> itemTaskInfoPOS =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(outStockOrderItemIds);
        // 重新调整已拣货数量
        itemTaskInfoPOS.forEach(info -> {
            if (CollectionUtils.isNotEmpty(info.getDetailList())) {
                BigDecimal overSortCount = info.getDetailList().stream()
                    .map(OrderItemTaskInfoDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                info.setOverSortCount(overSortCount);
            }
        });
        Map<Long, List<OrderItemTaskInfoPO>> itemTaskInfoMap =
            itemTaskInfoPOS.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId));
        realPickUpDTOList.stream().filter(pickUp -> pickUp.getBusinessId() != null)
            .collect(Collectors.groupingBy(PickUpDTO::getBusinessId)).forEach((orderItemId, list) -> {
                List<OrderItemTaskInfoPO> itemTaskInfos = itemTaskInfoMap.get(Long.valueOf(orderItemId));
                itemTaskInfos.sort(Comparator.comparing(OrderItemTaskInfoPO::getUnitTotalCount));

                for (PickUpDTO pickUp : list) {
                    BigDecimal count = pickUp.getCount();
                    for (OrderItemTaskInfoPO itemTaskInfo : itemTaskInfos) {
                        BigDecimal overSortCount = itemTaskInfo.getOverSortCount();
                        if (overSortCount.compareTo(BigDecimal.ZERO) <= 0 || count.compareTo(BigDecimal.ZERO) <= 0
                            || CollectionUtils.isEmpty(itemTaskInfo.getDetailList())) {
                            continue;
                        }
                        OrderItemTaskInfoDetailPO infoDetailPO = new OrderItemTaskInfoDetailPO();
                        BeanUtils.copyProperties(itemTaskInfo.getDetailList().get(0), infoDetailPO);
                        infoDetailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.ORDER_ITEM_TASK_INFO_DETAIL));
                        infoDetailPO.setSecOwnerId(pickUp.getSecOwnerId());
                        infoDetailPO.setOrgId(itemTaskInfo.getOrgId());
                        BigDecimal resultCount = overSortCount.subtract(count);
                        if (resultCount.compareTo(BigDecimal.ZERO) >= 0) {
                            itemTaskInfo.setOverSortCount(resultCount);
                            infoDetailPO.setUnitTotalCount(count);

                            orderItemTaskInfoDetailPOS.add(infoDetailPO);
                            break;
                        } else {
                            itemTaskInfo.setOverSortCount(BigDecimal.ZERO);
                            infoDetailPO.setUnitTotalCount(overSortCount);

                            orderItemTaskInfoDetailPOS.add(infoDetailPO);
                            count = resultCount.negate();
                        }
                    }
                }
            });

        // 先删除后新增
        if (CollectionUtils.isNotEmpty(orderItemTaskInfoDetailPOS)) {
            List<Long> taskInfoIds = orderItemTaskInfoDetailPOS.stream().map(OrderItemTaskInfoDetailPO::getTaskInfoId)
                .collect(Collectors.toList());
            List<Long> delInfoDetailIds = itemTaskInfoPOS.stream()
                .filter(itemTaskInfo -> taskInfoIds.contains(itemTaskInfo.getId())
                    && CollectionUtils.isNotEmpty(itemTaskInfo.getDetailList()))
                .flatMap(itemTaskInfo -> itemTaskInfo.getDetailList().stream()).map(OrderItemTaskInfoDetailPO::getId)
                .collect(Collectors.toList());
            orderItemTaskInfoDetailMapper.deleteByIds(delInfoDetailIds);
            LOGGER.info("新增orderItemTaskInfoDetail:{}", JSON.toJSONString(orderItemTaskInfoDetailPOS));
            Lists.partition(orderItemTaskInfoDetailPOS, 100).forEach(p -> {
                orderItemTaskInfoDetailMapper.insertBatch(p);
            });
        }
    }

    /**
     * 获取拣货任务不同状态的数量
     *
     * @return
     */
    public List<BatchTaskStateCountDTO> getBatchTaskStateCount(Integer cityId, Integer warehouseId, Integer userId) {
        AssertUtils.notNull(cityId, "城市id不能为空");
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        AssertUtils.notNull(userId, "用户id不能为空");
        return batchTaskMapper.getBatchTaskStateCount(cityId, warehouseId, userId);
    }

    /**
     * 获取拣货任务分拣中数量
     *
     * @return
     */
    public Integer getSortingBatchTaskCount(Integer cityId, Integer warehouseId, Integer userId) {
        AssertUtils.notNull(cityId, "城市id不能为空");
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        AssertUtils.notNull(userId, "用户id不能为空");
        return batchTaskMapper.getSortingBatchTaskCount(cityId, warehouseId, userId);
    }

    /**
     * 获取拣货任务最长已进行时间（天数）
     *
     * @return
     */
    public Integer getSortingBatchTaskMaxDays(Integer cityId, Integer warehouseId, Integer userId) {
        AssertUtils.notNull(cityId, "城市id不能为空");
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        AssertUtils.notNull(userId, "用户id不能为空");
        return batchTaskMapper.getSortingBatchTaskMaxDays(cityId, warehouseId, userId);
    }

    /**
     * 根据产品名称查询2.5分拣占用详细信息（包含内配分拣占用）
     */
    public List<PickedProductDTO> findPickedDetailByProductNameForSCM25(Integer orgId, Integer warehouseId,
        String productName) {
        AssertUtils.notNull(orgId, "城市ID不能为空");
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(productName), "产品名称不能为空");
        ProductSkuInfoSearchDTO searchDTO = new ProductSkuInfoSearchDTO();
        searchDTO.setCityId(orgId);
        searchDTO.setPrecise(0); // 模糊查询
        searchDTO.setProductName(org.apache.commons.lang3.StringUtils.trimToNull(productName));
        searchDTO.setPageNum(1);
        searchDTO.setPageSize(10); // 根据产品名称模糊查询限制数据条数
        PageList<ProductSkuInfoReturnDTO> productList = iProductSkuService.getProductSkuInfo(searchDTO);
        List<ProductSkuInfoReturnDTO> productDataList = productList.getDataList();
        if (CollectionUtils.isEmpty(productDataList)) {
            return Collections.emptyList();
        }
        List<PickedProductDTO> pickedList = new ArrayList<>(productDataList.size());
        productDataList.stream().filter(p -> p != null && p.getSkuId() != null).forEach(p -> {
            List<PickedDetailDTO> pickedDetails =
                batchTaskQueryBL.findPickedDetailBySkuIdForSCM25(orgId, warehouseId, p.getSkuId(), true);
            if (CollectionUtils.isNotEmpty(pickedDetails)) {
                PickedProductDTO pickedProduct = new PickedProductDTO();
                pickedProduct.setSkuId(p.getSkuId());
                pickedProduct.setProductName(p.getProductName());
                pickedProduct.setDetailDTOList(pickedDetails);
                pickedList.add(pickedProduct);
            }
        });
        return pickedList;
    }

    /**
     * 查询缺货产品
     *
     * @return
     */
    public List<Long> getLackProductSkuId(Integer cityId, Integer warehouseId, Date startTime, Date endTime) {
        AssertUtils.notNull(cityId, "城市ID不能为空");
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notNull(startTime, "开始时间不能为空");
        AssertUtils.notNull(endTime, "结束时间不能为空");
        return orderItemTaskInfoMapper.getLackProductSkuId(cityId, warehouseId, startTime, endTime);
    }

    /**
     * 获取团购订单摘果任务列表
     *
     * @return
     */
    public PageList<GroupBuyBatchTaskDTO> listBatchTaskByGroupBuy(GroupBuyBatchTaskSO so) {
        AssertUtils.notNull(so, "参数不能为空");
        AssertUtils.notNull(so.getCityId(), "城市id不能为空");
        AssertUtils.notNull(so.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(so.getUserId(), "用户id不能为空");
        AssertUtils.notNull(so.getBatchTaskType(), "拣货任务类型不能为空");
        PageResult<GroupBuyBatchTaskDTO> pageResult = batchTaskMapper.listBatchTaskByGroupBuy(so);
        return pageResult.toPageList();
    }

    /**
     * 工作状态 待作业：0 作业中：1 已作业：2
     */
    private Byte getWorkState(Byte batchState) {
        if (Objects.equals(BatchStateEnum.PENDINGPICKING.getType().byteValue(), batchState)) {
            return 0;
        } else if (Objects.equals(PICKING.getType().byteValue(), batchState)
            || Objects.equals(BatchStateEnum.SOWN.getType().byteValue(), batchState)
            || Objects.equals(BatchStateEnum.REVIEWING.getType().byteValue(), batchState)) {
            return 1;
        } else {
            return 2;
        }
    }

    /**
     * 获取波次对应的自提点数量
     */
    private Map<String, Integer> getAddressCountMap(Integer cityId, Integer warehouseId, List<String> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return null;
        }
        // 获取波次对应的自提点数量
        List<OutStockOrderPO> orderList =
            outStockOrderMapper.listOutStockOrderByBatchIds(batchIds, cityId, warehouseId);
        if (CollectionUtils.isEmpty(orderList)) {
            return null;
        }
        Map<String, Integer> addressCountMap = new HashMap<>();
        Map<String, List<OutStockOrderPO>> orderMap =
            orderList.stream().collect(Collectors.groupingBy(p -> p.getBatchId()));
        orderMap.forEach((batchId, orders) -> {
            addressCountMap.put(batchId,
                orders.stream().map(p -> p.getAddressId()).distinct().collect(Collectors.toList()).size());
        });
        return addressCountMap;
    }

    /**
     * 获取波次对应的播种数据
     *
     * @return
     */
    private Map<String, List<SowTaskItemPO>> getSowTaskMap(Integer cityId, Integer warehouseId, List<String> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return null;
        }
        // 获取播种数据
        List<SowTaskItemPO> sowTaskItemList = sowTaskItemMapper.findByBatchIds(cityId, warehouseId, batchIds);
        if (CollectionUtils.isEmpty(sowTaskItemList)) {
            return null;
        }
        return sowTaskItemList.stream().collect(Collectors.groupingBy(p -> p.getBatchId()));
    }

    /**
     * 获取波次对应的拣货数据
     *
     * @return
     */
    private Map<String, List<BatchTaskItemDTO>> getBatchTaskMap(Integer cityId, Integer warehouseId,
        List<String> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return null;
        }
        // 获取拣货数据
        List<BatchTaskItemDTO> batchTaskItemList =
            batchTaskItemMapper.findBatchTaskItemDtoListByBatchIds(cityId, warehouseId, batchIds);
        if (CollectionUtils.isEmpty(batchTaskItemList)) {
            return null;
        }
        return batchTaskItemList.stream().collect(Collectors.groupingBy(p -> p.getBatchId()));
    }

    /**
     * 获取产品明细
     *
     * @return
     */
    private List<GroupBuyWorkScheduleItemDTO> getWorkScheduleItemList(Integer cityId, String batchId,
        Map<String, List<SowTaskItemPO>> sowTaskMap, Map<String, List<BatchTaskItemDTO>> batchTaskMap) {
        List<GroupBuyWorkScheduleItemDTO> itemDTOList = new ArrayList<>();
        // 播种数据
        if (sowTaskMap != null && CollectionUtils.isNotEmpty(sowTaskMap.get(batchId))) {
            List<GroupBuyWorkScheduleItemDTO> sowTaskItemList = sowTaskMap.get(batchId).stream().map(p -> {
                GroupBuyWorkScheduleItemDTO itemDTO = new GroupBuyWorkScheduleItemDTO();
                itemDTO.setBatchId(batchId);
                itemDTO.setTaskType((byte)1);
                itemDTO.setProductSkuId(p.getProductSkuId());
                itemDTO.setProductName(p.getProductName());
                itemDTO.setTotalCount(p.getUnitTotalCount().divideAndRemainder(p.getSpecQuantity())[0]);
                itemDTO
                    .setOverSortCount((p.getOverUnitTotalCount() == null ? BigDecimal.ZERO : p.getOverUnitTotalCount())
                        .divideAndRemainder(p.getSpecQuantity())[0]);
                itemDTO.setLackCount((p.getLackUnitTotalCount() == null ? BigDecimal.ZERO : p.getLackUnitTotalCount())
                    .divideAndRemainder(p.getSpecQuantity())[0]);
                return itemDTO;
            }).collect(Collectors.toList());
            itemDTOList.addAll(sowTaskItemList);
        }
        // 拣货数据
        if (batchTaskMap != null && CollectionUtils.isNotEmpty(batchTaskMap.get(batchId))) {
            List<GroupBuyWorkScheduleItemDTO> batchTaskItemList = batchTaskMap.get(batchId).stream().map(p -> {
                GroupBuyWorkScheduleItemDTO itemDTO = new GroupBuyWorkScheduleItemDTO();
                itemDTO.setBatchId(batchId);
                itemDTO.setTaskType((byte)0);
                itemDTO.setProductSkuId(p.getSkuId());
                itemDTO.setProductName(p.getProductName());
                itemDTO.setTotalCount(p.getUnitTotalCount().divideAndRemainder(p.getSpecQuantity())[0]);
                itemDTO.setOverSortCount(p.getOverSortCount().divideAndRemainder(p.getSpecQuantity())[0]);
                itemDTO.setLackCount(p.getLackUnitCount().divideAndRemainder(p.getSpecQuantity())[0]);
                return itemDTO;
            }).collect(Collectors.toList());
            itemDTOList.addAll(batchTaskItemList);
        }
        // 获取商品的瓶码和箱码
        if (CollectionUtils.isNotEmpty(itemDTOList)) {
            Set<Long> skuIdSet = itemDTOList.stream().map(p -> p.getProductSkuId()).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(skuIdSet)) {
                Map<Long, ProductCodeDTO> codeMap = iProductSkuService.getPackageAndUnitCode(skuIdSet, cityId);
                if (codeMap != null) {
                    codeMap.forEach((skuId, code) -> {
                        itemDTOList.stream().filter(p -> Objects.equals(p.getProductSkuId(), skuId)).forEach(p -> {
                            p.setPackageCode(code.getPackageCode());
                            p.setUnitCode(code.getUnitCode());
                        });
                    });
                }
            }
        }
        return itemDTOList;
    }

    /**
     * 拣货任务移库
     */
    public void updateBatchTaskPickCount(String batchTaskNo) {
        LOGGER.info("拣货任务移库（op后台工具）：{}", batchTaskNo);

        List<String> batchTaskNoS = new ArrayList<>();
        batchTaskNoS.add(batchTaskNo);
        // 获取拣货任务
        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findTasksByBatchTaskNo(batchTaskNoS);
        if (CollectionUtils.isEmpty(batchTaskPOList)) {
            throw new BusinessValidateException("获取拣货任务为空");
        }

        // 获取拣货任务详情
        List<BatchTaskItemDTO> batchTaskItemDTOList = batchTaskItemMapper.findBatchTaskItemDtoListByNo(batchTaskNoS);
        if (CollectionUtils.isEmpty(batchTaskItemDTOList)) {
            throw new BusinessValidateException("获取波次任务详情为空");
        }

        // 获取拣货任务关联
        List<String> batchTaskItemIdList =
            batchTaskItemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByBatchTaskItemIds(batchTaskItemIdList);

        // 获取集货位信息
        List<SowTaskDTO> sowTasks = getSowTasks(batchTaskItemDTOList);

        List<PickUpDTO> pickUpDTOList = new ArrayList<>();
        for (BatchTaskItemDTO batchTaskItemDTO : batchTaskItemDTOList) {

            // 获取集货位
            Long sowLocationId = null;
            if (batchTaskItemDTO.getSowTaskId() != null) {
                Optional<SowTaskDTO> sowTaskDTO =
                    sowTasks.stream().filter(p -> p.getId().equals(batchTaskItemDTO.getSowTaskId())).findAny();
                if (sowTaskDTO.isPresent() && sowTaskDTO.get().getLocationId() != null) {
                    sowLocationId = sowTaskDTO.get().getLocationId();
                }
            }

            List<OrderItemTaskInfoPO> nowBatchTaskItemInfoList = orderItemTaskInfoPOList.stream()
                .filter(p -> Objects.equals(p.getBatchTaskItemId(), batchTaskItemDTO.getId()))
                .collect(Collectors.toList());
            // 根据关联移库
            if (CollectionUtils.isNotEmpty(nowBatchTaskItemInfoList)) {
                for (OrderItemTaskInfoPO info : nowBatchTaskItemInfoList) {
                    // 关联明细
                    if (CollectionUtils.isNotEmpty(info.getDetailList())) {
                        for (OrderItemTaskInfoDetailPO detail : info.getDetailList()) {
                            PickUpDTO pickUpDTO = new PickUpDTO();
                            pickUpDTO.setFromLocationId(batchTaskItemDTO.getLocationId());
                            setPickupToLocation(batchTaskPOList.get(0).getToLocationId(), sowLocationId, pickUpDTO);
                            pickUpDTO.setProductSkuId(batchTaskItemDTO.getSkuId());
                            pickUpDTO.setWarehouseId(batchTaskPOList.get(0).getWarehouseId());
                            pickUpDTO.setFromChannel(batchTaskItemDTO.getChannel().intValue());
                            pickUpDTO.setFromSource(batchTaskItemDTO.getSource().intValue());
                            pickUpDTO.setToChannel(batchTaskItemDTO.getChannel().intValue());
                            pickUpDTO.setToSource(batchTaskItemDTO.getSource().intValue());
                            pickUpDTO.setCount(detail.getUnitTotalCount());
                            // 生产日期和批次时间
                            pickUpDTO.setProductionDate(null);
                            pickUpDTO.setBatchTime(null);
                            // 自动分配库存
                            pickUpDTO.setAutoAllotFlag(true);
                            pickUpDTO.setSecOwnerId(detail.getSecOwnerId());
                            pickUpDTO.setBusinessId(info.getId().toString());
                            // 排除负库存
                            pickUpDTO.setExcludeNegativeFlag(true);
                            pickUpDTOList.add(pickUpDTO);
                        }
                    }
                }
            }
        }

        // 移库操作
        if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
            LOGGER.info("【工具】拣货移库入参：{}，batckNO：{}", new Gson().toJson(pickUpDTOList),
                batchTaskPOList.get(0).getBatchTaskNo());
            PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
            pickUpChangeRecordDTO.setCityId(Integer.valueOf(batchTaskPOList.get(0).getOrgId()));
            pickUpChangeRecordDTO.setOrderId(batchTaskPOList.get(0).getId());
            pickUpChangeRecordDTO.setOrderNo(batchTaskPOList.get(0).getBatchTaskNo());
            pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.PDA提交拣货.name());
            pickUpChangeRecordDTO.setCreateUser("");
            iBatchInventoryManageService.pickupCompleteBySku(pickUpDTOList, pickUpChangeRecordDTO);
        }

    }

    /**
     * 获取标记缺货的校验类型
     *
     * @return 0=不校验，1=二维码校验，2=人脸识别校验
     */
    public LackCheckMethodResultDTO getMarkLackCheckMethod(Integer orgId, Integer warehouseId, String skuId) {
        LackCheckMethodResultDTO lackCheckMethodDTO = new LackCheckMethodResultDTO();
        Integer saleStoreNum = getSaleStoreBySku(orgId, warehouseId, skuId);
        lackCheckMethodDTO.setSaleStoreNum(saleStoreNum);

        // 销售库存等于0时，则不校验
        if (saleStoreNum <= 0) {
            lackCheckMethodDTO.setCheckMethod(LackCheckMethodEnum.不校验.getType());
            return lackCheckMethodDTO;
        }

        Integer noCheckHour =
            Integer.valueOf(getValueByWarehouseKey(LackCheckConfigKeyEnum.LACK_NO_CHECK_HOUR_LIMIT, warehouseId));

        // 查询n小时内，sku已经标记缺货的分拣任务项数
        Integer lackCount =
            batchTaskItemMapper.findLackCountWithinHourBySku(orgId, warehouseId, skuId, nowMinusHour(noCheckHour));
        if (lackCount > 0) {
            lackCheckMethodDTO.setCheckMethod(LackCheckMethodEnum.不校验.getType());
            return lackCheckMethodDTO;
        }
        // 进行二维码校验的小时时限
        Integer qrCheckHour =
            Integer.valueOf(getValueByWarehouseKey(LackCheckConfigKeyEnum.LACK_QR_CHECK_HOUR_LIMIT, warehouseId));
        // 进行二维码校验的sku种类数限制
        Integer qrCheckSkuCount =
            Integer.valueOf(getValueByWarehouseKey(LackCheckConfigKeyEnum.LACK_QR_CHECK_SKUCOUNT_LIMIT, warehouseId));

        // 查询n小时内，仓库的标记缺货sku的数量
        Integer lackSkuCount =
            batchTaskItemMapper.findLackSkuCountWithinHour(orgId, warehouseId, skuId, nowMinusHour(qrCheckHour));

        if (lackSkuCount > qrCheckSkuCount) {
            List<WarehouseKeeper> warehouseKeepers = getWarehouseKeepersByWarehouseId(warehouseId);
            lackCheckMethodDTO.setWarehouseKeepers(warehouseKeepers);
            lackCheckMethodDTO.setCheckMethod(LackCheckMethodEnum.人脸识别校验.getType());
            return lackCheckMethodDTO;
        } else {
            lackCheckMethodDTO.setCheckMethod(LackCheckMethodEnum.二维码校验.getType());
            return lackCheckMethodDTO;
        }
    }

    /**
     * 获取仓库所有的仓管信息
     */
    public List<WarehouseKeeper> getWarehouseKeepersByWarehouseId(Integer warehouseId) {
        List<AdminUser> adminUsers = iAdminUserQueryService
            .listAdminUser(Arrays.asList(AdminUserAuthRoleConstant.ROLE_CODE_仓库管理员新流程), warehouseId);

        List<WarehouseKeeper> warehouseKeepers = adminUsers.stream()
            .filter(keeper -> keeper.getId() != null && (keeper.getUserName() != null || keeper.getTrueName() != null))
            .map(elem -> {
                WarehouseKeeper keeper = new WarehouseKeeper();
                keeper.setId(elem.getId());
                keeper.setBusinessId(elem.getBusinessId());
                keeper.setName(elem.getTrueName() != null ? elem.getTrueName() : elem.getUserName());
                return keeper;
            }).collect(Collectors.toList());
        return warehouseKeepers;
    }

    /**
     * 根据仓库id、skuId获取销售库存
     *
     * @param warehouseId 仓库id
     * @param skuId
     * @return sku的销售库存
     */
    public Integer getSaleStoreBySku(Integer cityId, Integer warehouseId, String skuId) {
        ProductSkuInfoReturnDTO skuInfo = iProductSkuService.findProductSkuInfoBySkuId(Long.valueOf(skuId));
        if (skuInfo == null) {
            throw new BusinessValidateException("获取产品的销售库存失败，因为查不到sku信息，仓库id:" + warehouseId + "，skuId:" + skuId);
        }
        // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 -- 暂不换接口
        ProductOwnerInfoDTO productOwner =
            ProductOwnerInfoDTO.of(skuInfo.getCompanyId(), skuInfo.getProductSpecificationId());
        List<SaleInventoryInfoDTO> saleInventoryDTOS = omsSaleInventoryManager.findInventoryByProductOwners(cityId,
            warehouseId, Collections.singletonList(productOwner));
        if (CollectionUtils.isEmpty(saleInventoryDTOS)) {
            throw new BusinessValidateException("获取产品的销售库存失败，仓库id:" + warehouseId + "，skuId:" + skuId);
        }
        BigDecimal saleStore = saleInventoryDTOS.stream().map(SaleInventoryInfoDTO::getSaleInventoryCount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        return saleStore.intValue();
    }

    /**
     * 根据key查找出仓库的配置值
     */
    private String getValueByWarehouseKey(String key, Integer warehouseId) {
        VariableValueQueryDTO variableQuery = new VariableValueQueryDTO();
        variableQuery.setVariableKey(key);
        variableQuery.setWarehouseId(warehouseId);
        VariableDefAndValueDTO keyConfig = iVariableValueService.detailVariable(variableQuery);

        if (keyConfig == null || StringUtils.isEmpty(keyConfig.getVariableData())) {
            LOGGER.info("该仓库的缺货校验类型的配置项获取不到，仓库id：{}", warehouseId);
            throw new BusinessValidateException("该仓库的缺货校验类型的配置项获取不到");
        }

        return keyConfig.getVariableData();
    }

    /**
     * 获取当前时间n小时之前的时间
     */
    private Date nowMinusHour(Integer period) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.HOUR, -period);
        return cal.getTime();
    }

    /**
     * 获取新的缺货随机码
     */
    public Integer newLackRandomCode(Integer warehouseId) {
        // 生成新的随机码
        Integer newCode = RandomUtils.nextInt(1000, 10000);

        // 存储随机码到redis
        // key = supc:waves:lackRandomCode value = warehouseId -> code
        redisUtil.setHash(RedisKeyConstant.LACK_RANDOM_CODE, String.valueOf(warehouseId), newCode, 7, TimeUnit.DAYS);

        return newCode;
    }

    /**
     * 获取旧的缺货随机码
     */
    public Integer oldLackRandomCode(Integer warehouseId) {
        Integer code = redisUtil.getHash(RedisKeyConstant.LACK_RANDOM_CODE, String.valueOf(warehouseId));
        if (code == null) {
            code = newLackRandomCode(warehouseId);
        }
        return code;
    }

    /**
     * 确认拣货时，是否禁止拣货出库 - 拣货任务项的出库配置校验
     *
     * @param firstBatchTaskItems 分拣任务项（优先使用的location）
     * @param secondBatchTaskItems 分拣任务项（firstBatchTaskItems的location为空时，则使用secondBatchTaskItems）
     * @param warehouseId 仓库Id
     * @param taskCompleteDTO 分拣任务出库完成信息
     * @return true=禁止拣货出库，false=允许拣货出库
     */
    public boolean isBanOutStockByOutStockConfig(List<BatchTaskItemDTO> firstBatchTaskItems,
        List<BatchTaskItemDTO> secondBatchTaskItems, Integer warehouseId, BatchTaskCompleteResultDTO taskCompleteDTO) {
        List<OutStockConfigCheckDTO> checkDTOList = new ArrayList<>();
        boolean isOpenStock = warehouseConfigService.isOpenLocationStock(warehouseId);
        firstBatchTaskItems.forEach(firstItemElem -> {
            OutStockConfigCheckDTO checkElem = new OutStockConfigCheckDTO();
            checkElem.setSkuId(firstItemElem.getSkuId());
            // 获取第一优先fromLocationId，若第一优先fromLocationId为null，则获取第二优先fromLocationId
            if (isOpenStock) {
                Long fromLocationId = firstItemElem.getLocationId() != null ? firstItemElem.getLocationId()
                    : secondBatchTaskItems.stream()
                        .filter(secondTaskItem -> Objects.equals(firstItemElem.getId(), secondTaskItem.getId()))
                        .filter(elem -> elem.getFromLocationId() != null).map(BatchTaskItemDTO::getFromLocationId)
                        .findFirst().orElse(null);
                AssertUtils.notNull(fromLocationId, "4.0仓库拣货，来源货位不能为空");
                checkElem.setLocationId(fromLocationId);
            } else {
                checkElem.setLocationId(null);
            }
            checkElem.setChannel(firstItemElem.getChannel() == null ? 0 : Integer.valueOf(firstItemElem.getChannel()));
            checkElem.setSource(firstItemElem.getSource() == null ? 0 : Integer.valueOf(firstItemElem.getSource()));
            checkDTOList.add(checkElem);
        });
        List<OutStockConfigCheckResultDTO> checkResultDTOS =
            inStockConfigService.checkByOutStockConfig(checkDTOList, warehouseId);

        if (CollectionUtils.isEmpty(checkResultDTOS)) {
            return false;
        }

        List<String> banProductNames =
            checkResultDTOS.stream().filter(elem -> Objects.equals(elem.getAlarm(), OutStockAlarmEnum.出库禁止.getType()))
                .map(OutStockConfigCheckResultDTO::getProductName).collect(Collectors.toList());
        List<String> remindProductNames =
            checkResultDTOS.stream().filter(elem -> Objects.equals(elem.getAlarm(), OutStockAlarmEnum.出库提醒.getType()))
                .map(OutStockConfigCheckResultDTO::getProductName).collect(Collectors.toList());

        boolean isBan = false;
        if (CollectionUtils.isNotEmpty(banProductNames)) {
            taskCompleteDTO.setBanProductNames(banProductNames);
            isBan = true;
        }

        if (CollectionUtils.isNotEmpty(remindProductNames)) {
            taskCompleteDTO.setRemindProductNames(remindProductNames);
        }

        return isBan;
    }

    /**
     * 对PDA客户端波次任务修改请求进行校验
     */
    public BatchTaskCompleteResultDTO
        checkOutStockByOutStockConfig(List<BatchTaskItemCompleteDTO> batchTaskItemUpdateDTOList, Integer warehouseId) {
        AssertUtils.notEmpty(batchTaskItemUpdateDTOList, "分拣任务项更新列表不能为空");

        List<BatchTaskItemPO> oldBatchTaskItemPOS = batchTaskItemMapper.listBatchTaskItemByIds(
            batchTaskItemUpdateDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()), null);
        if (CollectionUtils.isEmpty(oldBatchTaskItemPOS)) {
            throw new DataValidateException("拣货详情不存在，请刷新重试！");
        }
        // 查找该波次任务所有商品详情
        List<BatchTaskItemDTO> batchTaskItemDTOList =
            batchTaskItemMapper.findBatchTaskItemDtoListById(oldBatchTaskItemPOS.get(0).getBatchTaskId());

        // 进行出库配置校验
        List<BatchTaskItemDTO> checkItemDTOList = new ArrayList<>();
        oldBatchTaskItemPOS.forEach(oldElem -> {
            BatchTaskItemCompleteDTO reqItem = batchTaskItemUpdateDTOList.stream()
                .filter(updateElem -> Objects.equals(updateElem.getId(), oldElem.getId())).findFirst().orElse(null);
            if (reqItem == null) {
                return;
            }
            BatchTaskItemDTO checkElem = new BatchTaskItemDTO();
            checkElem.setSkuId(oldElem.getSkuId());
            checkElem.setLocationId(reqItem.getFromLocationId());
            checkElem.setChannel(oldElem.getChannel());
            checkElem.setSource(oldElem.getSource());
            checkItemDTOList.add(checkElem);
        });

        BatchTaskCompleteResultDTO taskCompleteResultDTO = new BatchTaskCompleteResultDTO();
        isBanOutStockByOutStockConfig(checkItemDTOList, batchTaskItemDTOList, warehouseId, taskCompleteResultDTO);
        return taskCompleteResultDTO;
    }

    /**
     * 查出波次任务列表
     */
    public List<BatchTaskDTO> listBatchTask(BatchTaskQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "请求不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库不能为空");
        queryDTO.setQueryCondition(Arrays.asList(1));
        return WaveOrderTaskConvertor.batchOrderTaskPOS2BatchOrderTaskDTOS(batchTaskMapper.listBatchTask(queryDTO));
    }

    public List<BatchTaskDTO> listToLocationNameById(List<Long> idList, Integer orgId) {
        List<BatchTaskPO> batchTaskPOList =
            Optional.of(batchTaskMapper.listToLocationNameById(idList, orgId)).orElse(new ArrayList<>());
        List<BatchTaskDTO> batchTaskDTOList = new ArrayList<>(batchTaskPOList.size());
        batchTaskPOList.forEach(b -> {
            BatchTaskDTO batchTaskDTO = new BatchTaskDTO();
            BeanUtils.copyProperties(b, batchTaskDTO);
            batchTaskDTOList.add(batchTaskDTO);
        });
        return batchTaskDTOList;
    }

    public List<BatchTaskItemDTO> listToLocationNameByOrderNo(List<String> orderNos, Integer orgId) {
        List<BatchTaskItemDTO> batchTaskItemDTOList = new ArrayList<>();
        List<BatchTaskItemPO> batchTaskItemPOS =
            Optional.of(batchTaskItemMapper.listToLocationName(orderNos, orgId)).orElse(new ArrayList<>());
        batchTaskItemPOS.forEach(b -> {
            if (!org.springframework.util.ObjectUtils.isEmpty(b)) {
                BatchTaskItemDTO batchTaskItemDTO = new BatchTaskItemDTO();
                batchTaskItemDTO.setRefOrderNo(b.getRefOrderNo());
                batchTaskItemDTO.setToLocationName(b.getToLocationName());
                batchTaskItemDTOList.add(batchTaskItemDTO);
            }
        });
        orderNos.forEach(p -> {
            if (batchTaskItemPOS.stream().noneMatch(q -> Objects.equals(p, q.getRefOrderNo()))) {
                BatchTaskItemDTO batchTaskItemDTO = new BatchTaskItemDTO();
                batchTaskItemDTO.setRefOrderNo(p);
                batchTaskItemDTO.setToLocationName("");
                batchTaskItemDTOList.add(batchTaskItemDTO);
            }
        });
        return batchTaskItemDTOList;
    }

    /**
     * 根据id查找 根据产品拣货的波次任务详情
     */
    public PageList<BatchTaskSowItemDTO> findBatchTaskSowItemByProduct(String batchTaskId, Integer currentPage,
        Integer pageSize) {
        PageList<BatchTaskSortItemDTO> batchTaskSortItemList =
            this.findBatchTaskSortItemByProduct(batchTaskId, currentPage, pageSize);
        return toBatchTaskSowItemDTO(batchTaskSortItemList, batchTaskId);
    }

    private PageList<BatchTaskSowItemDTO> toBatchTaskSowItemDTO(PageList<BatchTaskSortItemDTO> dtoPageList,
        String batchTaskId) {
        PageList<BatchTaskSowItemDTO> resultPageList = new PageList<>();
        resultPageList.setPager(dtoPageList.getPager());
        if (CollectionUtils.isEmpty(dtoPageList.getDataList())) {
            return resultPageList;
        }
        List<BatchTaskSortItemDTO> sourceList = dtoPageList.getDataList();
        List<BatchTaskSowItemDTO> resultList = new ArrayList<>();

        for (BatchTaskSortItemDTO source : sourceList) {
            BatchTaskSowItemDTO result = new BatchTaskSowItemDTO();
            BeanUtils.copyProperties(source, result);
            result.setSowState(source.getSownUnitTotalCount() != null
                && source.getSownUnitTotalCount().byteValue() == SowTaskStateEnum.已播种.getType()
                    ? SowTaskStateEnum.已播种.getType() : SowTaskStateEnum.待播种.getType());
            resultList.add(result);
        }
        resultPageList.setDataList(resetPackageCount(resultList, batchTaskId));
        return resultPageList;
    }

    private List<BatchTaskSowItemDTO> resetPackageCount(List<BatchTaskSowItemDTO> resultList, String batchTaskId) {
        if (CollectionUtils.isEmpty(resultList)) {
            return resultList;
        }
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);
        if (Objects.isNull(batchTaskPO)) {
            return resultList;
        }
        WarehouseConfigDTO warehouseConfigDTO =
            warehouseConfigService.getConfigByWareHouseId(batchTaskPO.getWarehouseId());
        if (Objects.isNull(warehouseConfigDTO)
            || !WavesStrategyConstants.PACKAGEREVIEWTYPE_OPEN.equals(warehouseConfigDTO.getPackageReviewType())) {
            return resultList;
        }

        resultList.forEach(m -> {
            BigDecimal[] remainder = m.getUnitTotalCount().divideAndRemainder(m.getSpecQuantity());

            m.setPackageCount(remainder[0]);
            m.setUnitCount(remainder[1]);
        });

        return resultList;
    }

    /**
     * 获取二次分拣的移库list
     */
    @Deprecated
    public void getSecondSortPickUpDTO(List<PickUpDTO> pickUpDTOList, Integer warehouseId,
        BatchTaskItemDTO batchTaskItemDTO, Long fromLocationId, BatchTaskItemCompleteDTO updateDTO, Long sowLocationId,
        List<OrderItemTaskInfoPO> nowBatchTaskItemInfoList, Map<Long, BigDecimal> taskInfoDetailOverSortCountMap,
        List<SowTaskDTO> sowTasks) {
        if (CollectionUtils.isEmpty(nowBatchTaskItemInfoList)) {
            return;
        }
        OutStockOrderItemQuerySO querySO = new OutStockOrderItemQuerySO();
        querySO.setOrgId(batchTaskItemDTO.getOrgId());
        querySO.setWarehouseId(warehouseId);
        querySO.setIds(
            nowBatchTaskItemInfoList.stream().map(OrderItemTaskInfoPO::getRefOrderItemId).collect(Collectors.toList()));
        PageResult<OutStockOrderItemPO> outStockOrderItemList = outStockOrderItemMapper.listOrderItem(querySO);
        if (CollectionUtils.isEmpty(outStockOrderItemList)) {
            LOGGER.info("[酒饮二次分拣]出库单项为空，查询条件：{}", JSON.toJSONString(querySO));
            return;
        }

        Map<Long, Long> orderItemToLocationMap = initOrderItemToLocationMap(outStockOrderItemList, sowTasks);
        LOGGER.info("[酒饮二次分拣]出库单项的出库位：{}", orderItemToLocationMap);

        List<PickUpDTO> nowPickUpDTOList = new ArrayList<>();

        nowBatchTaskItemInfoList.forEach(info -> {
            // 关联明细
            if (CollectionUtils.isEmpty(info.getDetailList())) {
                return;
            }
            info.getDetailList().forEach(detail -> {
                PickUpDTO pickUpDTO = new PickUpDTO();
                BigDecimal pickCount = detail.getUnitTotalCount();
                // 重复拣货时，移库数量 = 本次拣货数量 - 上次拣货数量
                if (TaskStateEnum.已完成.getType() == batchTaskItemDTO.getTaskState()) {
                    // 上次拣货数量
                    BigDecimal prevPickCount = BigDecimal.ZERO;
                    if (taskInfoDetailOverSortCountMap != null
                        && taskInfoDetailOverSortCountMap.get(detail.getId()) != null) {
                        prevPickCount = taskInfoDetailOverSortCountMap.get(detail.getId());
                    }
                    pickCount = pickCount.subtract(prevPickCount);
                }
                if (pickCount.compareTo(BigDecimal.ZERO) == 0) {
                    LOGGER.info("拣货数量为0，不需要移库");
                    return;
                }
                Long toLocationId = orderItemToLocationMap.get(info.getRefOrderItemId());
                if (toLocationId == null) {
                    LOGGER.info("出库位为空，不能移库，出库单项ID：{}，移库数量：{}", info.getRefOrderItemId(), pickCount);
                    throw new BusinessValidateException("二次分拣，出库位不能为空");
                }
                pickUpDTO.setFromLocationId(fromLocationId);
                setPickupToLocation(toLocationId, sowLocationId, pickUpDTO);
                pickUpDTO.setProductSkuId(batchTaskItemDTO.getSkuId());
                pickUpDTO.setWarehouseId(warehouseId);
                pickUpDTO.setFromChannel(batchTaskItemDTO.getChannel().intValue());
                pickUpDTO.setFromSource(batchTaskItemDTO.getSource().intValue());
                pickUpDTO.setToChannel(batchTaskItemDTO.getChannel().intValue());
                pickUpDTO.setToSource(batchTaskItemDTO.getSource().intValue());
                pickUpDTO.setCount(pickCount);
                // 生产日期和批次时间
                pickUpDTO.setProductionDate(updateDTO != null ? updateDTO.getProductionDate() : null);
                pickUpDTO.setBatchTime(updateDTO != null ? updateDTO.getBatchTime() : null);
                // 自动分配库存
                pickUpDTO.setAutoAllotFlag(true);
                pickUpDTO.setSecOwnerId(detail.getSecOwnerId());
                pickUpDTO.setBusinessId(info.getId().toString());
                // 排除负库存
                pickUpDTO.setExcludeNegativeFlag(true);
                nowPickUpDTOList.add(pickUpDTO);
            });
        });
        LOGGER.info("[酒批二次分拣]移库请求：{}", JSON.toJSONString(nowPickUpDTOList));
        pickUpDTOList.addAll(nowPickUpDTOList);
    }

    private Map<Long, Long> initOrderItemToLocationMap(PageResult<OutStockOrderItemPO> outStockOrderItemList,
        List<SowTaskDTO> sowTasks) {
        Map<Long, Long> orderItemToLocationMap =
            outStockOrderItemList.stream().filter(ele -> ele.getLocationId() != null).collect(
                Collectors.toMap(OutStockOrderItemPO::getId, OutStockOrderItemPO::getLocationId, (k1, k2) -> k1));
        LOGGER.info("[酒饮二次分拣]出库单项的出库位：{}", orderItemToLocationMap);
        if (CollectionUtils.isEmpty(sowTasks)) {
            return orderItemToLocationMap;
        }

        Map<Long, SowTaskDTO> sowTaskDTOMap =
            sowTasks.stream().collect(Collectors.toMap(SowTaskDTO::getId, v -> v, (v1, v2) -> v1));

        Map<Long, Long> orderItemToLocationSowMap =
            outStockOrderItemList.stream().filter(ele -> Objects.nonNull(sowTaskDTOMap.get(ele.getSowTaskId())))
                .collect(Collectors.toMap(OutStockOrderItemPO::getId, v -> {
                    SowTaskDTO sowTaskDTO = sowTaskDTOMap.get(v.getSowTaskId());
                    return sowTaskDTO.getLocationId();
                }, (k1, k2) -> k1));

        for (Map.Entry<Long, Long> entry : orderItemToLocationSowMap.entrySet()) {
            Long locationId = orderItemToLocationMap.get(entry.getKey());
            if (Objects.isNull(locationId)) {
                orderItemToLocationMap.put(entry.getKey(), entry.getValue());
            }
        }

        return orderItemToLocationMap;
    }

    /**
     * 查找分拣任务项
     */
    public PageList<BatchTaskItemDTO> listBatchTaskItem(BatchTaskItemQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO.getCityId(), "城市ID不能为空");
        return batchTaskItemMapper.listBatchTaskItem(queryDTO).toPageList();
    }

    private void filterAndValidateSecPick(List<BatchPO> batchPOS, List<BatchTaskPO> batchTaskPOList,
        Integer warehouseId) {
        Map<String, String> batchMap =
            batchPOS.stream().filter(ele -> Objects.equals(ele.getSowType(), BatchSowTypeEnum.二次分拣.getType()))
                .collect(Collectors.toMap(BatchPO::getBatchNo, BatchPO::getBatchNo));

        List<BatchTaskPO> filterBatchTaskPOS = batchTaskPOList.stream().filter(m -> {
            if (BatchTaskKindOfPickingConstants.PICKING_SORTING.equals(m)) {
                return Boolean.TRUE;
            }

            return StringUtils.isNotEmpty(batchMap.get(m.getBatchNo()));
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterBatchTaskPOS)) {
            return;
        }

        this.secondPickValidate(batchPOS, filterBatchTaskPOS, warehouseId);
    }

    /**
     * 二次分拣校验
     */
    private void secondPickValidate(List<BatchPO> batchPOS, List<BatchTaskPO> batchTaskPOList, Integer warehouseId) {
        List<String> batchIdList =
            batchPOS.stream().filter(ele -> Objects.equals(ele.getSowType(), BatchSowTypeEnum.二次分拣.getType()))
                .map(BatchPO::getId).distinct().collect(Collectors.toList());
        // 酒饮二次分拣时，查询通道
        List<Long> secondPickPassageIdLit =
            batchTaskPOList.stream().filter(ele -> batchIdList.contains(ele.getBatchId()))
                .map(BatchTaskPO::getPassageId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(secondPickPassageIdLit)) {
            PassageSO passageSO = new PassageSO();
            passageSO.setWarehouseId(warehouseId);
            passageSO.setPassageIdList(secondPickPassageIdLit);
            List<PassageDTO> secondPickPassageList = iPassageService.listPassage(passageSO).getDataList();
            if (CollectionUtils.isNotEmpty(secondPickPassageList)) {
                List<Long> secondPickPassageIdList = secondPickPassageList.stream()
                    .filter(ele -> Objects.equals(ele.getSowType(), SowTypeEnum.二次分拣.getType())).map(PassageDTO::getId)
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(secondPickPassageIdList)
                    && batchTaskPOList.stream().anyMatch(ele -> secondPickPassageIdList.contains(ele.getPassageId()))) {
                    throw new BusinessValidateException("二次分拣的拣货任务请在PDA拣货，分拣任务："
                        + batchTaskPOList.stream().filter(ele -> secondPickPassageIdList.contains(ele.getPassageId()))
                            .map(BatchTaskPO::getBatchTaskNo).distinct().limit(5).collect(Collectors.joining(",")));
                }
            }
        }
    }

    /**
     * 重置拣货任务明细的拣货人
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void wcsAssignSorter(WCSAssignItemSorterDTO dto) {
        LOGGER.info("重置拣货任务明细的拣货人 ： {}", JSON.toJSONString(dto));
        dto.resetBatchTaskItemIdsIfEmpty();
        List<BatchTaskItemPO> batchTaskItemPOList =
            batchTaskItemMapper.selectBatchTaskItemByIds(dto.getBatchTaskItemIds());
        if (CollectionUtils.isEmpty(batchTaskItemPOList)) {
            throw new BusinessValidateException("拣货任务明细不存在！");
        }

        dto.getBatchTaskItemIds().forEach(itemId -> {
            BatchTaskItemPO updateItem = new BatchTaskItemPO();
            updateItem.setId(itemId);
            updateItem.setCompleteUser(null);
            updateItem.setCompleteUserId(null);
            updateItem.setLastUpdateUser(dto.getOptUserName());
            updateItem.setLargePickPattern(BatchTaskItemLargePickPatternConstants.LARGE_PICK_SMALL_HUMAN);
            batchTaskItemMapper.updateBatchTaskItemSorter(updateItem);
        });

        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskItemPOList.get(0).getBatchTaskId());
        BatchTaskPO updateBatchTaskPO = new BatchTaskPO();
        updateBatchTaskPO.setId(batchTaskPO.getId());
        updateBatchTaskPO.setPickPattern(BatchTaskPickPatternEnum.人机混拣.getType());
        // updateBatchTaskPO.setSorter(null);
        // updateBatchTaskPO.setSorterId(null);
        // batchTaskMapper.updateToClearRobotInfo(updateBatchTaskPO);
        batchTaskMapper.updateByPrimaryKeySelective(updateBatchTaskPO);
        // orderTraceBL.insert();
    }

    public List<BatchTaskDTO> listBatchTask(BatchTaskPramDTO batchTaskPramDTO) {
        List<BatchTaskDTO> batchTaskDTOs = listBatchTaskBySowTaskNos(Arrays.asList(batchTaskPramDTO.getSowTaskNo()),
            batchTaskPramDTO.getTaskStates(), batchTaskPramDTO.getCityId());
        batchTaskDTOs.forEach(batchTaskDTO -> {
            setOutStockOrderItemDTOProductCode(batchTaskDTO.getOutStockOrderItemList());
            setBatchTaskItemDTOProductCode(batchTaskDTO.getBatchTaskItemList());
        });
        return batchTaskDTOs;
    }

    private void setBatchTaskItemDTOProductCode(List<BatchTaskItemDTO> batchTaskItemList) {
        if (CollectionUtils.isNotEmpty(batchTaskItemList)) {
            List<Long> skuIds = batchTaskItemList.stream().map(BatchTaskItemDTO::getSkuId).collect(Collectors.toList());
            Map<Long, List<ProductCodeInfoDTO>> productCodeMap =
                iProductCodeInfoQueryService.findProductCodeBySkuIds(skuIds);
            batchTaskItemList.forEach(item -> {
                List<ProductCodeInfoDTO> productCodeInfos = productCodeMap.get(item.getSkuId());
                if (CollectionUtils.isNotEmpty(productCodeInfos)) {
                    List<String> unitCodes = productCodeInfos.stream()
                        .filter(code -> ProductCodeTypeEnum.BAR_CODE.getType().equals(code.getCodeType()))
                        .map(ProductCodeInfoDTO::getCode).collect(Collectors.toList());
                    List<String> packageCodes = productCodeInfos.stream()
                        .filter(code -> ProductCodeTypeEnum.BOX_CODE.getType().equals(code.getCodeType()))
                        .map(ProductCodeInfoDTO::getCode).collect(Collectors.toList());
                    item.setUnitCode(unitCodes == null ? new ArrayList<String>() : unitCodes);
                    item.setPackageCode(packageCodes == null ? new ArrayList<String>() : packageCodes);
                }
            });
        }
    }

    private void setOutStockOrderItemDTOProductCode(List<OutStockOrderItemDTO> outStockOrderItemDTOS) {
        if (CollectionUtils.isNotEmpty(outStockOrderItemDTOS)) {
            List<Long> skuIds =
                outStockOrderItemDTOS.stream().map(OutStockOrderItemDTO::getSkuId).collect(Collectors.toList());
            Map<Long, List<ProductCodeInfoDTO>> productCodeMap =
                iProductCodeInfoQueryService.findProductCodeBySkuIds(skuIds);
            outStockOrderItemDTOS.forEach(item -> {
                List<ProductCodeInfoDTO> productCodeInfos = productCodeMap.get(item.getSkuId());
                if (CollectionUtils.isNotEmpty(productCodeInfos)) {
                    List<String> unitCodes = productCodeInfos.stream()
                        .filter(code -> ProductCodeTypeEnum.BAR_CODE.getType().equals(code.getCodeType()))
                        .map(ProductCodeInfoDTO::getCode).collect(Collectors.toList());
                    List<String> packageCodes = productCodeInfos.stream()
                        .filter(code -> ProductCodeTypeEnum.BOX_CODE.getType().equals(code.getCodeType()))
                        .map(ProductCodeInfoDTO::getCode).collect(Collectors.toList());

                    item.setUnitCode(unitCodes == null ? new ArrayList<String>() : unitCodes);
                    item.setPackageCode(packageCodes == null ? new ArrayList<String>() : packageCodes);
                }
            });
        }

    }

    public PageList<BatchTaskDTO> saasBatchTaskList(BatchTaskQueryDTO batchTaskQueryDTO) {
        PageList<BatchTaskDTO> batchTaskList = findBatchOrderTaskList(batchTaskQueryDTO);
        batchTaskList.getDataList().forEach(it -> {
            it.setTaskStateText(TaskStateEnum.getType(it.getTaskState()));
            it.setPickingTypeText(PickingTypeEnum.getEnumByValue(it.getPickingType()));
            it.setOrderSelectionText(SaaSOrderSelectionEnum.getType(it.getOrderSelection()));
        });
        return batchTaskList;
    }

    public List<BatchTaskDTO> saasGetBatchSowTask(BatchTaskQueryDTO batchTaskQueryDTO) {
        List<BatchTaskDTO> batchTaskDTOS = listBatchTaskBySowTaskNos(Arrays.asList(batchTaskQueryDTO.getSowTaskNo()),
            null, batchTaskQueryDTO.getCityId());
        if (CollectionUtils.isEmpty(batchTaskDTOS)) {
            new ArrayList<>();
        }
        for (BatchTaskDTO batchTaskDTO : batchTaskDTOS) {
            setOutStockOrderItemDTOProductCode(batchTaskDTO.getOutStockOrderItemList());
            setBatchTaskItemDTOProductCode(batchTaskDTO.getBatchTaskItemList());
        }
        return batchTaskDTOS;
    }

    public void saveBatchTaskContainerCount(BatchTaskContainersDTO batchTaskContainersDTO) {
        BatchTaskPO batchTaskByNo = batchTaskMapper.findBatchTaskByNo(batchTaskContainersDTO.getBatchTaskNo());
        AssertUtils.notNull(batchTaskByNo, "未找到对应拣货任务!");

        if (Objects.isNull(batchTaskContainersDTO.getContainerCount())) {
            return;
        }

        redisUtil.setHash(RedisConstant.SUP_C.concat(PICK_SAMETIME).concat(batchTaskContainersDTO.getBatchTaskNo()),
            batchTaskContainersDTO.getBatchTaskNo(), batchTaskContainersDTO.getContainerCount(), 7, TimeUnit.DAYS);
    }

    public PageList<BatchTaskDTO> listBatchTaskInfo(SowTaskQueryDTO sowTaskQueryDTO) {
        // 默认全状态
        List<Integer> batchStateList =
            Arrays.asList(PENDINGSCHEDULE.getType(), PENDINGPICKING.getType(), PICKING.getType(), PICKINGEND.getType());
        // List<Integer> batchTaskItemList = Arrays.asList(未分拣.getType(), 分拣中.getType(),
        // 已完成.getType()).stream().map(Integer::valueOf).collect(Collectors.toList());
        // 播种状态
        List<Integer> sowTaskStateList = sowTaskQueryDTO.getStates() != null
            ? sowTaskQueryDTO.getStates().stream().map(Byte::intValue).collect(Collectors.toList()) : new ArrayList<>();

        if (Objects.equals(sowTaskQueryDTO.getSowTaskName(), "总单二次分拣")) {
            // 总单二次分拣状态
            batchStateList = Arrays.asList(PICKING.getType(), PICKINGEND.getType());
            // batchTaskItemList = Arrays.asList(分拣中.getType(),
            // 已完成.getType()).stream().map(Integer::valueOf).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(sowTaskQueryDTO.getSkuIds())
            || StringUtils.isNotEmpty(sowTaskQueryDTO.getProductName())) {
            sowTaskQueryDTO.setProductInfo(sowTaskQueryDTO.getClass().getName());
        }
        PageResult<BatchTaskPO> result = batchTaskMapper.listBatchTaskItem(sowTaskStateList, batchStateList,
            sowTaskQueryDTO, sowTaskQueryDTO.getPageNum(), sowTaskQueryDTO.getPageSize());
        PageList<BatchTaskPO> batchTaskPOPageList = result.toPageList();
        List<BatchTaskPO> batchTaskPOS = batchTaskPOPageList.getDataList();
        if (CollectionUtils.isEmpty(batchTaskPOS)) {
            return new PageList<>();
        }
        List<String> batchTaskNos =
            batchTaskPOS.stream().filter(batchTaskPO -> !Objects.isNull(batchTaskPO.getBatchTaskNo()))
                .map(BatchTaskPO::getBatchTaskNo).collect(Collectors.toList());
        List<BatchTaskItemDTO> batchTaskItemDtoListByNo =
            batchTaskItemMapper.findBatchTaskItemDtoListByNo(batchTaskNos);
        if (CollectionUtils.isNotEmpty(batchTaskItemDtoListByNo)) {
            Map<String, List<BatchTaskItemDTO>> batchTaskMap =
                batchTaskItemDtoListByNo.stream().collect(Collectors.groupingBy(BatchTaskItemDTO::getBatchTaskNo));
            batchTaskPOS.forEach(batchTaskPO -> {
                List<BatchTaskItemDTO> batchTaskItemDTOS = batchTaskMap.get(batchTaskPO.getBatchTaskNo());
                if (CollectionUtils.isEmpty(batchTaskItemDTOS)) {
                    return;
                }
                BigDecimal unitTotalCount = batchTaskItemDTOS.stream()
                    .map(batchTaskItemDTO -> batchTaskItemDTO.getUnitTotalCount()
                        .divideAndRemainder(batchTaskItemDTO.getSpecQuantity())[1])
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal packageCount = batchTaskItemDTOS.stream()
                    .map(batchTaskItemDTO -> batchTaskItemDTO.getUnitTotalCount()
                        .divideAndRemainder(batchTaskItemDTO.getSpecQuantity())[0])
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                batchTaskPO.setUnitAmount(unitTotalCount);
                batchTaskPO.setPackageAmount(packageCount);
            });
        }

        PageList<BatchTaskDTO> batchTaskDTOPageList = new PageList<>();
        List<BatchTaskDTO> list = new ArrayList<>();
        batchTaskPOS.forEach(batchTaskPO -> {
            BatchTaskDTO batchTaskDTO = new BatchTaskDTO();
            BeanUtils.copyProperties(batchTaskPO, batchTaskDTO);
            list.add(batchTaskDTO);
        });
        batchTaskDTOPageList.setDataList(list);
        batchTaskDTOPageList.setPager(result.toPageList().getPager());
        return batchTaskDTOPageList;
    }

    /**
     * 是否存在未完成的播种任务
     *
     * @param curTaskItemList 播种任务列表
     */
    private static boolean hasUnCompletedBatchTask(List<BatchTaskItemDTO> curTaskItemList) {
        return curTaskItemList.stream().anyMatch(ele -> {
            boolean waitSow = ele.getSownUnitTotalCount() == null
                || ele.getSownUnitTotalCount().byteValue() == SowTaskStateEnum.待播种.getType();
            return waitSow && ele.getUnitTotalCount().compareTo(ele.getLackUnitCount()) != 0;
        });
    }

    /**
     * 获取波次下所有托盘信息
     *
     * @param warehouseId
     * @param batchNoList
     * @param isNeedPalletForStock
     * @return
     */
    private Map<String, List<OrderLocationPalletDTO>> getBatchNoPalletMap(Integer warehouseId, List<String> batchNoList,
        boolean isNeedPalletForStock) {
        if (!isNeedPalletForStock) {
            return Collections.EMPTY_MAP;
        }

        List<BatchTaskPO> completeBatchTaskPOS =
            batchTaskMapper.findTaskByBatchIdsAndState(batchNoList, Arrays.asList(TaskStateEnum.已完成.getType()));
        if (CollectionUtils.isEmpty(completeBatchTaskPOS)) {
            return Collections.EMPTY_MAP;
        }

        Map<String,
            String> taskIdBatchNoMap = completeBatchTaskPOS.stream()
                .filter(p -> !StringUtils.isEmpty(p.getId()) && !StringUtils.isEmpty(p.getBatchNo()))
                .collect(Collectors.toMap(p -> p.getId(), p -> p.getBatchNo(), (v1, v2) -> v1));
        List<String> batchTaskIdList = completeBatchTaskPOS.stream()
            .filter(p -> !StringUtils.isEmpty(p.getId()) && !StringUtils.isEmpty(p.getBatchNo()))
            .map(BatchTaskPO::getId).distinct().collect(Collectors.toList());
        OrderLocationPalletQueryDTO palletQueryDTO = new OrderLocationPalletQueryDTO();
        palletQueryDTO.setWarehouseId(warehouseId);
        palletQueryDTO.setBatchTaskIdList(batchTaskIdList);
        List<OrderLocationPalletDTO> palletDTOS = orderLocationPalletBL.findPalletByCondition(palletQueryDTO);
        if (CollectionUtils.isEmpty(palletDTOS)) {
            return Collections.EMPTY_MAP;
        }

        Map<String, List<OrderLocationPalletDTO>> batchNoPalletMap =
            palletDTOS.stream().filter(p -> !StringUtils.isEmpty(p.getBatchTaskId()))
                .collect(Collectors.groupingBy(p -> taskIdBatchNoMap.get(p.getBatchTaskId())));
        LOGGER.info("获取波次下所有托盘信息 ：{}", JSON.toJSONString(batchNoPalletMap));
        return batchNoPalletMap;
    }

    /**
     * 拣货任务完成检查是否需要托盘信息
     *
     * @param batchTaskPO
     */
    public void checkIsNeedPalletForStock(BatchTaskPO batchTaskPO) {
        boolean isNeedPalletForStock =
            wavesStrategyBOConvertor.getOpenTrayPositionLocation(batchTaskPO.getWarehouseId());
        boolean isNeedPalletForSortingMode =
            wavesStrategyBOConvertor.getOpenPalletForSortingMode(batchTaskPO.getWarehouseId());
        if (!isNeedPalletForStock && !isNeedPalletForSortingMode) {
            return;
        }
        // 只处理按单拣货
        if (!Objects.equals(batchTaskPO.getPickingType(), PickingTypeEnum.订单拣货.getType())) {
            return;
        }

        throw new BusinessValidateException("当前版本较低，请先升级后再完成拣货!");
    }

    /**
     * 拍灯领取拣货任务
     *
     * @param dto
     */
    public void getDigitalBatchTask(DigitalGetBatchTaskDTO dto) {
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(dto.getBatchTaskId());
        if (Objects.isNull(batchTaskPO)) {
            throw new BusinessValidateException("拣货任务不存在！");
        }

        if (TaskStateEnum.已完成.getType() == batchTaskPO.getTaskState()) {
            throw new BusinessValidateException("拣货任务已完成！");
        }

        if (TaskStateEnum.未分拣.getType() != batchTaskPO.getTaskState()) {
            throw new BusinessValidateException("拣货任务已被领取！");
        }

        String userName = globalCache.getAdminTrueName(dto.getOptUserId());
        BatchTaskPO updateBatchTaskPO = new BatchTaskPO();
        updateBatchTaskPO.setId(batchTaskPO.getId());
        updateBatchTaskPO.setTaskState(分拣中.getType());
        // updateBatchTaskPO.setSorter(userName);
        // updateBatchTaskPO.setSorterId(dto.getOptUserId());
        updateBatchTaskPO.setStartTime(new Date());

        batchTaskMapper.updateByPrimaryKeySelective(updateBatchTaskPO);

    }

    /**
     * 分区拣货（即电子标签拣货），根据分区id获取绑定标签数据
     *
     * @param warehouseId 仓库 id
     * @param sortGroupIds 分区id列表
     * @return 分区数据, key 是 分区id, value 是分区对象
     */
    private Map<Long, SortGroupRfidDTO> listSortGroupRfidInfo(Integer warehouseId, Byte rfidType,
        List<Long> sortGroupIds) {
        if (CollectionUtils.isEmpty(sortGroupIds)) {
            return Collections.EMPTY_MAP;
        }

        SortGroupRfidSO rfidSO = new SortGroupRfidSO();
        rfidSO.setWarehouseId(warehouseId);
        rfidSO.setRfidType(rfidType);
        rfidSO.setSortIdList(sortGroupIds);
        List<SortGroupRfidDTO> rfidDTOS = iSortGroupRfidService.listSortGroupRfidInfo(rfidSO);
        LOGGER.info("根据分区id查询绑定标签结果:", JSON.toJSONString(rfidDTOS));
        if (CollectionUtils.isEmpty(rfidDTOS)) {
            return Collections.EMPTY_MAP;
        }

        return rfidDTOS.stream()
            .collect(Collectors.toMap(SortGroupRfidDTO::getSortId, Function.identity(), (k1, k2) -> k1));
    }

    /**
     * 分区拣货（即电子标签拣货），根据分区id填充分区标签数据
     */
    private void fillSortGroupRfidInfo(List<BatchTaskDTO> batchTaskDTOS) {
        if (CollectionUtils.isEmpty(batchTaskDTOS)) {
            return;
        }

        List<BatchTaskDTO> rfidBatchTaskDTOS = batchTaskDTOS.stream()
            .filter(p -> p.getSortGroupId() != null
                && Objects.equals(p.getPickPattern(), BatchTaskPickPatternEnum.电子标签.getType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rfidBatchTaskDTOS)) {
            return;
        }

        Integer warehouseId = batchTaskDTOS.get(0).getWarehouseId();
        List<Long> sortGroupIds =
            rfidBatchTaskDTOS.stream().map(p -> p.getSortGroupId()).distinct().collect(Collectors.toList());
        Map<Long, SortGroupRfidDTO> sortGroupRfidMap =
            listSortGroupRfidInfo(warehouseId, SortGroupRfidTypeEnum.分区.getType(), sortGroupIds);
        if (sortGroupRfidMap == null || sortGroupRfidMap.size() <= 0) {
            return;
        }

        rfidBatchTaskDTOS.forEach(dto -> {
            SortGroupRfidDTO sortGroupRfidDTO = sortGroupRfidMap.get(dto.getSortGroupId());
            if (sortGroupRfidDTO == null) {
                return;
            }

            dto.setExistSortGroupRfid(true);
        });
    }

    /**
     * 同步拣货任务到电子标签
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncBatchTaskToRfid(BatchTaskDTO dto) {
        LOGGER.info("同步拣货任务到电子标签参数 ：{}", JSON.toJSONString(dto));

        List<BatchTaskPO> batchTaskList = batchTaskMapper.findTasksByBatchTaskId(dto.getBatchTaskIds());
        if (CollectionUtils.isEmpty(batchTaskList)) {
            throw new BusinessException("拣货任务不存在，请刷新重试！");
        }

        batchTaskList = batchTaskList.stream()
            .filter(p -> Objects.equals(p.getTaskState(), TaskStateEnum.未分拣.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchTaskList)) {
            throw new BusinessValidateException("拣货任务已在分拣中或已完成！");
        }

        batchTaskList = batchTaskList.stream().filter(p -> p.getSorterId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchTaskList)) {
            throw new BusinessValidateException("拣货任务未指定拣货人！");
        }

        batchTaskList = batchTaskList.stream()
            .filter(p -> p.getSortGroupId() != null
                && Objects.equals(p.getPickPattern(), BatchTaskPickPatternEnum.电子标签.getType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchTaskList)) {
            throw new BusinessValidateException("拣货任务不是分区拣货，或未设置分区标签！");
        }

        List<String> batchTaskIds = batchTaskList.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());
        // 检查分区id是否存在其他分拣中的拣货任务
        List<Long> sortGroupIds =
            batchTaskList.stream().map(p -> p.getSortGroupId()).distinct().collect(Collectors.toList());
        DigitalBatchTaskQueryDTO queryDTO = new DigitalBatchTaskQueryDTO();
        queryDTO.setWarehouseId(dto.getWarehouseId());
        queryDTO.setStateList(Arrays.asList(TaskStateEnum.分拣中.getType()));
        queryDTO.setPickPattern(BatchTaskPickPatternEnum.电子标签.getType());
        queryDTO.setSortGroupIds(sortGroupIds);
        queryDTO.setExcludeBatchTaskIds(batchTaskIds);
        List<DigitalBatchTaskDTO> existSortGroupDTOS = batchTaskMapper.findDigitalBatchTaskInfo(queryDTO);
        if (!CollectionUtils.isEmpty(existSortGroupDTOS)) {
            LOGGER.info("existSortGroupDTOS结果：{}", JSON.toJSONString(existSortGroupDTOS));
            throw new BusinessValidateException("同步失败，电子标签已有进行中的任务，请等待电子标签空闲或继续使用pda拣货！");
        }

        // 更新状态为拣货中
        batchTaskMapper.updateBatchTaskByIdList(batchTaskIds, TaskStateEnum.分拣中.getType(), new Date());
        LOGGER.info("同步拣货任务到电子标签 更新状态拣货任务ids：{}", JSON.toJSONString(batchTaskIds));
        // 同步任务
        batchTaskChangeNotifyBL.notifyBatchTaskGet(batchTaskList);
        LOGGER.info("同步拣货任务到电子标签 同步数据 ：{}", JSON.toJSONString(batchTaskList));
    }

    /**
     * 清理电子标签拣货任务
     *
     * @param dto
     */
    public void cleanUpDigitalTask(CleanUpDigitalTaskDTO dto) {
        List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findBatchTaskByIds(dto.getBatchTaskIds());
        AssertUtils.notEmpty(batchTaskPOS, "拣货任务不能为空！");

        batchTaskPOS = batchTaskPOS.stream().filter(m -> BatchTaskPickPatternEnum.电子标签.getType() == m.getPickPattern())
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchTaskPOS)) {
            return;
        }

        batchTaskChangeNotifyBL.notifyBatchDelete(batchTaskPOS);
        String userName = globalCache.getAdminTrueName(dto.getOptUserId());

        orderTraceBL.notifyDigitalBatchTaskTrace(userName, batchTaskPOS);
    }

    @BatchDistributeLock(conditions = "#orderItemTaskInfoDTOS", property = "batchTaskId", expireMills = 60000,
        sleepMills = 3000, key = BatchTaskConstants.BATCH_TASK_COMPLETE_KEY,
        lockType = BatchDistributeLock.LockType.WAITLOCK)
    public void waitProcessOrderChangeWithBatch(List<OutStockOrderProcessChangeDTO> processChangeDTOS,
        String operatorUser, Byte changeType, String batchNo, List<OrderItemTaskInfoDTO> orderItemTaskInfoDTOS) {
        batchOrderBL.waitProcessOrderChangeWithBatch(processChangeDTOS, operatorUser, changeType, batchNo);
    }

    /**
     * 更新拣货任务拣货方式
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchTaskKindOfPicking(BatchTaskDTO dto) {
        LOGGER.info("更新拣货任务拣货方式参数 ：{}", JSON.toJSONString(dto));

        List<BatchTaskPO> batchTaskList = batchTaskMapper.findTasksByBatchTaskId(dto.getBatchTaskIds());
        if (CollectionUtils.isEmpty(batchTaskList)) {
            throw new BusinessException("拣货任务不存在，请刷新重试！");
        }

        batchTaskList = batchTaskList.stream().filter(p -> Objects.equals(p.getTaskState(), TaskStateEnum.未分拣.getType())
            || Objects.equals(p.getTaskState(), TaskStateEnum.分拣中.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchTaskList)) {
            throw new BusinessValidateException("拣货任务已完成！");
        }

        Byte pickPattern = BatchTaskKindOfPickingConstants.convertToPickPattern(dto);

        batchTaskList = batchTaskList.stream().filter(p -> !Objects.equals(p.getKindOfPicking(), pickPattern))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchTaskList)) {
            return;
        }

        List<String> batchTaskIds = batchTaskList.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());

        BatchTaskPO updateBatchTaskPO = new BatchTaskPO();
        updateBatchTaskPO.setPickPattern(pickPattern);
        // 更新拣货方式
        batchTaskIds.forEach(id -> {
            updateBatchTaskPO.setId(id);
            batchTaskMapper.updateByPrimaryKeySelective(updateBatchTaskPO);
        });

        LOGGER.info("更新拣货任务拣货方式完成，拣货任务ids：{}", JSON.toJSONString(batchTaskIds));
        // 电子标签拣货改成默认时，通知客户端清理拣货任务
        if (Objects.equals(pickPattern, BatchTaskPickPatternEnum.人工拣货.getType())) {
            batchTaskChangeNotifyBL.notifyBatchDelete(batchTaskList);
            LOGGER.info("通知拣货任务删除 同步数据 ：{}", JSON.toJSONString(batchTaskList));
        }
    }

    /**
     * 更新拣货模式
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void changeKindOfPickingToPickPattern(CleanUpDigitalTaskDTO dto) {
        BatchTaskPO batchTaskPO = new BatchTaskPO();
        batchTaskPO.setPickPattern(BatchTaskPickPatternEnum.电子标签.getType());
        batchTaskPO.setKindOfPicking(BatchTaskKindOfPickingConstants.DEFAULT);
        dto.getBatchTaskIds().forEach(m -> {
            batchTaskPO.setId(m);
            batchTaskMapper.updateByPrimaryKeySelective(batchTaskPO);
        });
    }

    /**
     * SCM-22614 按客户拣货，相同地址合并拣货 1、开启了【仓库按订单实时分拣】，未开启【是否开启接力分拣模式】时，休百订单或酒饮订单分拣完成，都要选择绑定托盘位，同时同客户其他订单同步出库位和托盘位 补充： 1）前提条件是
     * 开启了 isNeedPalletForStock 按订单拣货装托 2）已完成的订单，同步到当前分拣的出库位和托盘位 不允许修改 3）开启了【仓库按订单实时分拣】未开启【是否开启接力分拣模式】，未开启
     * isNeedPalletForStock 按订单拣货装托，实时同步出库位信息
     *
     * @param dto
     * @return
     */
    public UserSamePalletInfoResultDTO findSameUserPalletInfo(UserSamePalletInfoQueryDTO dto) {
        OutStockOrderPO outStockOrderPO =
            outStockOrderMapper.getByOrderNo(dto.getOrgId(), dto.getWarehouseId(), dto.getOrderNo());

        SameUserOrderInBatchQueryPO queryPO = new SameUserOrderInBatchQueryPO();
        queryPO.setAddressId(outStockOrderPO.getAddressId());
        queryPO.setOrderCreateTime(DateUtils.getMinusMonthFirstDay(1));
        queryPO.setStateList(Arrays.asList(OutStockOrderStateEnum.已拣货.getType(), OutStockOrderStateEnum.待拣货.getType(),
            OutStockOrderStateEnum.待调度.getType(), OutStockOrderStateEnum.拣货中.getType()));
        queryPO.setWarehouseId(outStockOrderPO.getWarehouseId());
        // queryPO.setOrderSequence(outStockOrderPO.getOrderSequence());
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findSameUserOrderInBatch(queryPO);

        List<Long> orderIds =
            outStockOrderPOList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());

        boolean openTrayPositionLocation = globalCache.getOpenTrayPositionLocation(dto.getWarehouseId());

        if (BooleanUtils.isFalse(openTrayPositionLocation)) {
            List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
                orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(orderIds);
            List<String> batchTaskIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getBatchTaskId)
                .distinct().collect(Collectors.toList());
            List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findBatchTaskByIds(batchTaskIds);
            Optional<BatchTaskPO> locationBatchTaskOptional =
                batchTaskPOList.stream().filter(m -> Objects.nonNull(m.getToLocationId())).findFirst();
            if (locationBatchTaskOptional.isPresent()) {
                BatchTaskPO batchTaskPO = locationBatchTaskOptional.get();
                return UserSamePalletInfoResultDTO.getDefaultLocation(batchTaskPO.getToLocationId(),
                    batchTaskPO.getToLocationName());
            }

            return UserSamePalletInfoResultDTO.getDefault();
        }

        OrderLocationPalletQueryDTO restOrderLocationPalletQueryDTO = new OrderLocationPalletQueryDTO();
        restOrderLocationPalletQueryDTO.setOrderIdList(orderIds);
        restOrderLocationPalletQueryDTO.setWarehouseId(outStockOrderPO.getWarehouseId());
        List<OrderLocationPalletDTO> palletDTOList =
            orderLocationPalletBL.findPalletByCondition(restOrderLocationPalletQueryDTO);

        if (CollectionUtils.isEmpty(palletDTOList)) {
            return UserSamePalletInfoResultDTO.getDefault();
        }

        List<String> palletNoList =
            palletDTOList.stream().map(OrderLocationPalletDTO::getPalletNo).distinct().collect(Collectors.toList());

        OrderLocationPalletDTO palletDTO = palletDTOList.stream().findFirst().get();

        UserSamePalletInfoResultDTO resultDTO = new UserSamePalletInfoResultDTO();
        resultDTO.setLocationId(palletDTO.getLocationId());
        resultDTO.setLocationName(palletDTO.getLocationName());
        resultDTO.setPalletNoList(palletNoList);

        return resultDTO;
    }

    /**
     * 按产品拣货任务项填充调拨和内配退批次类型
     *
     * @param itemDTOS
     */
    private void fillAllotOutBoundTypeByProduct(List<BatchTaskSortItemDTO> itemDTOS) {
        if (CollectionUtils.isEmpty(itemDTOS)) {
            return;
        }

        Integer orgId = itemDTOS.stream().findFirst().get().getOrgId();
        List<String> batchTaskItemIds = itemDTOS.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderList = outStockOrderMapper.listByBatchTaskItemIds(batchTaskItemIds, orgId);

        // 获取调拨、内配退订单skuid
        List<Long> allotSkuIds = outStockOrderList.stream()
            .filter(p -> Objects.equals(p.getOutBoundType(), OutBoundTypeEnum.ALLOT_ORDER.getCode().byteValue())
                && CollectionUtils.isNotEmpty(p.getItems()))
            .flatMap(p -> p.getItems().stream()).map(OutStockOrderItemPO::getSkuid).distinct()
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allotSkuIds)) {
            return;
        }

        itemDTOS.forEach(item -> {
            if (allotSkuIds.contains(item.getSkuId())) {
                item.setOutBoundType(OutBoundTypeEnum.ALLOT_ORDER.getCode().byteValue());
            }
        });
    }
}
