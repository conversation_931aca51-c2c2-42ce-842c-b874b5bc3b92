package com.yijiupi.himalaya.supplychain.waves.enums;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 波次类型
 *
 * <AUTHOR>
 * @since 2019-09-24 14:51
 */
@SuppressWarnings("NonAsciiCharacters")
public enum BatchTypeEnum {
    /**
     * 酒批
     */
    酒批((byte)0),
    /**
     * 微酒
     */
    微酒((byte)1),
    /**
     * 外部平台
     */
    外部平台((byte)2),
    /**
     * 第三方
     */
    第三方((byte) 3),
    /**
     * 大客户
     */
    大客户((byte) 4),
    ;

    /**
     * type
     */
    private final Byte type;

    /**
     * 出库申请单类型波次订单
     */
    private static final Set<BatchTypeEnum> APPLY_ORDER_BATCH = new HashSet<>(Arrays.asList(第三方, 大客户));

    BatchTypeEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

    public boolean valueEquals(Number number) {
        return number != null && this.type.equals(number.byteValue());
    }

    /**
     * 是否为出库申请单创建波次
     *
     * @param number 波次类型
     */
    public static boolean isApplyOrderBatch(Number number) {
        return APPLY_ORDER_BATCH.stream().anyMatch(it -> it.valueEquals(number));
    }

}
