package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.DeleteBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchStateEnum;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-08-12 10:06
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class BatchDeleteVerifyBL {

    @Resource
    private OutStockOrderMapper outStockOrderMapper;

    @Resource
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    public void verifyCanDelete(List<BatchPO> batches, DeleteBatchDTO deleteBatchDTO) {
        List<String> batchNos = deleteBatchDTO.getBatchNoList();
        if (CollectionUtils.isEmpty(batches)) {
            throw new BusinessException("波次不存在：" + JSON.toJSONString(batchNos));
        }
        // 波次状态已出库不能删除
        if (batches.stream().map(BatchPO::getState).anyMatch(BatchStateEnum.ALREADYOUTOFSTORE::valueEquals)) {
            String batchNoList =
                    batches.stream().filter(m -> BatchStateEnum.ALREADYOUTOFSTORE.valueEquals(m.getState()))
                            .map(BatchPO::getBatchNo).collect(Collectors.joining(","));

            throw new BusinessValidateException(batchNoList + " 是已出库的波次不能删除！");
        }
        // 是否需要校验波次状态
        if (!Objects.equals(deleteBatchDTO.getIgnoreBatchState(), true)) {
            if (batches.stream().anyMatch(p -> !BatchStateEnum.PENDINGPICKING.valueEquals(p.getState()))) {
                throw new BusinessValidateException("只能删除待拣货状态的波次！");
            }
        }
        if (deleteBatchDTO.isCheckOrderState()) {
            // 出库单状态 已出库 不能删除
            boolean anyOutStock = outStockOrderMapper.findByBatchNos(batchNos, batches.get(0).getOrgId()).stream()
                    .anyMatch(it -> OutStockOrderStateEnum.已出库.valueEquals(it.getState()));
            if (anyOutStock) {
                throw new BusinessValidateException("已出库的波次不能删除！");
            }
        }
        // 久批自提/分销自提订单不允许自己删除
        int ziTiOrderCount = orderItemTaskInfoMapper.checkBatchCanDeleteByBatchNoAndOutBoundType(batchNos, OutBoundTypeEnum.SELF_PICKUP_SALE_ORDER.getCode());
        if (ziTiOrderCount > 0) {
            throw new BusinessValidateException("自提订单/分销订单已付款已打印，不允许删除拣货任务！");
        }
        // if (BooleanUtils.isFalse(deleteBatchDTO.getIsIsOpCancel())) {
        // // 检验订单状态，虚仓实配波次不允许删除，33，35
        // int xuCangShiPeiOrderCount = orderItemTaskInfoMapper.checkBatchCanDeleteByBatchNos(batchNos);
        // Integer warehouseId = batches.get(0).getWarehouseId();
        // // if (xuCangShiPeiOrderCount > 0 && !iWarehouseQueryService.isOpenOrderCenter(warehouseId)) {
        // // throw new BusinessValidateException("虚仓实配波次不能删除！");
        // // }
        // }
    }

}
