package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupRfidDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/1/3
 */
@Component
public class BatchTaskSortConvertor {

    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    public void setBachPalletInfo(BatchTaskSortDTO dto, Map<String, List<OrderLocationPalletDTO>> batchNoPalletMap) {
        if (batchNoPalletMap == null || batchNoPalletMap.size() <= 0) {
            return;
        }

        List<OrderLocationPalletDTO> palletDTOS = batchNoPalletMap.get(dto.getBatchNo());
        if (CollectionUtils.isEmpty(palletDTOS)) {
            return;
        }

        List<String> palletNoList = palletDTOS.stream().filter(p -> !StringUtils.isEmpty(p.getPalletNo()))
            .map(OrderLocationPalletDTO::getPalletNo).distinct().collect(Collectors.toList());
        dto.setPalletNoList(palletNoList);
        dto.setPalletLocationId(palletDTOS.get(0).getLocationId());
        dto.setPalletLocationName(palletDTOS.get(0).getLocationName());
        dto.setUpdateToLocationFlag(false);
    }

    /**
     * 设置分区标签数据
     *
     */
    public void setSortGroupRfid(BatchTaskSortDTO dto, Map<Long, SortGroupRfidDTO> sortGroupRfidMap) {
        if (dto.getSortGroupId() == null || sortGroupRfidMap == null || sortGroupRfidMap.size() <= 0) {
            return;
        }

        SortGroupRfidDTO sortGroupRfidDTO = sortGroupRfidMap.get(dto.getSortGroupId());
        if (Objects.isNull(sortGroupRfidDTO)) {
            return;
        }

        dto.setExistSortGroupRfid(true);
    }
}
