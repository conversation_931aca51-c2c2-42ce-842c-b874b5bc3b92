package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutBoundBatchDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.outboundbatch.BatchQueryOutBoundBatchDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutBoundBatchQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderProcessBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.SecondSortBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.tms.NotifyTmsBatchCreateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchBaseBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.CreateBatchBaseBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.lock.LockCreateBatchBL;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByRefOrderNoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.DeliveryTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.himalaya.supplychain.waves.util.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 * 
 * <AUTHOR>
 * @date 2024/11/25
 */
@Service
public class BatchCreateDTOConvertor {

    @Autowired
    private RedisUtil<String> redisUtil;
    @Autowired
    protected SecondSortBL secondSortBl;
    @Autowired
    protected BatchTitleConvertor batchTitleConvertor;
    @Autowired
    private WavesStrategyBOConvertor wavesStrategyBOConvertor;
    @Autowired
    private GlobalCache globalCache;

    @Reference
    private IOutBoundBatchQueryService iOutBoundBatchQueryService;

    protected static final Logger LOG = LoggerFactory.getLogger(BatchCreateDTOConvertor.class);

    /**
     * 对追加的订单，单独生成波次
     * 
     * @param batchNo
     * @param appendOutStockOrderList
     * @return
     */
    public CreateBatchBaseBO getBatchCreateParams(BatchPO batchPO, List<OutStockOrderPO> appendOutStockOrderList) {
        String createBatchInfoStr = redisUtil.get(LockCreateBatchBL.BATCH_PARAM_CACHE_KEY + batchPO.getBatchNo());

        CreateBatchBaseBO createBatchBaseBO = null;
        try {
            createBatchBaseBO =
                JSON.parseObject(createBatchInfoStr, new TypeReference<CreateBatchBaseBO>() {}.getType());
        } catch (Exception e) {
            throw new BusinessValidateException("参数异常，此播种任务不支持追加！");
        }
        if (Objects.isNull(createBatchBaseBO)) {
            throw new BusinessValidateException("参数异常，此播种任务不支持追加！");
        }

        if (Objects.isNull(createBatchBaseBO.getBatchCreateDTO())
            && Objects.isNull(createBatchBaseBO.getBatchCreateByRefOrderNoDTO())) {
            throw new BusinessValidateException("参数异常，此播种任务不支持追加！");
        }

        BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO = createBatchBaseBO.getBatchCreateByRefOrderNoDTO();
        BatchCreateDTO batchCreateDTO = createBatchBaseBO.getBatchCreateDTO();

        if (Objects.nonNull(batchCreateDTO)) {
            batchCreateDTO.setOrderIdList(appendOutStockOrderList.stream().map(OutStockOrderPO::getId)
                .map(String::valueOf).collect(Collectors.toList()));

            return createBatchBaseBO;
        }

        if (Objects.nonNull(batchCreateByRefOrderNoDTO)) {
            if (CollectionUtils.isNotEmpty(batchCreateByRefOrderNoDTO.getDeliveryTaskList())) {
                batchCreateByRefOrderNoDTO.setRefOrderNos(
                    appendOutStockOrderList.stream().map(OutStockOrderPO::getReforderno).collect(Collectors.toList()));
                batchCreateByRefOrderNoDTO
                    .setDeliveryTaskList(getBatchCreateDeliveryTask(appendOutStockOrderList, batchPO.getWarehouseId()));
                // TODO 处理总单二次分拣
                // throw new BusinessValidateException("还未处理");
                return createBatchBaseBO;
            }
            if (CollectionUtils.isNotEmpty(batchCreateByRefOrderNoDTO.getRefOrderNos())) {
                batchCreateByRefOrderNoDTO.setRefOrderNos(
                    appendOutStockOrderList.stream().map(OutStockOrderPO::getReforderno).collect(Collectors.toList()));
                return createBatchBaseBO;
            }

        }

        return createBatchBaseBO;
    }

    private List<DeliveryTaskDTO> getBatchCreateDeliveryTask(List<OutStockOrderPO> appendOutStockOrderList,
        Integer warehouseId) {

        boolean notHaveBoundNo = appendOutStockOrderList.stream().anyMatch(o -> StringUtils.isBlank(o.getBoundNo()));
        if (notHaveBoundNo) {
            String orderNoList = appendOutStockOrderList.stream().filter(o -> StringUtils.isBlank(o.getBoundNo()))
                .map(o -> o.getReforderno()).collect(Collectors.joining(","));

            throw new BusinessValidateException("订单" + orderNoList + "未分车，无法追加到总单二次分拣波次！");
        }

        BatchQueryOutBoundBatchDTO queryOutBoundBatchDTO = new BatchQueryOutBoundBatchDTO();
        queryOutBoundBatchDTO.setWarehouseId(warehouseId);
        List<String> boundNoList =
            appendOutStockOrderList.stream().map(OutStockOrderPO::getBoundNo).distinct().collect(Collectors.toList());
        queryOutBoundBatchDTO.setOutBoundBatchNos(boundNoList);

        List<OutBoundBatchDTO> outBoundBatchDTOList =
            iOutBoundBatchQueryService.findOutBoundBatchList(queryOutBoundBatchDTO);

        Map<String, List<OutStockOrderPO>> orderGroupByBoundList =
            appendOutStockOrderList.stream().collect(Collectors.groupingBy(OutStockOrderPO::getBoundNo));

        Map<String, OutBoundBatchDTO> outBoundBatchDTOMap =
            outBoundBatchDTOList.stream().collect(Collectors.toMap(OutBoundBatchDTO::getBoundBatchNo, v -> v));

        List<DeliveryTaskDTO> deliveryTaskDTOS = new ArrayList<>();
        for (Map.Entry<String, OutBoundBatchDTO> entry : outBoundBatchDTOMap.entrySet()) {
            OutBoundBatchDTO outBoundBatchDTO = entry.getValue();
            List<OutStockOrderPO> outStockOrderPOList = orderGroupByBoundList.get(entry.getKey());
            DeliveryTaskDTO deliveryTaskDTO = new DeliveryTaskDTO();
            deliveryTaskDTO.setDeliveryTaskNo(outBoundBatchDTO.getRelatedBatchNo());
            deliveryTaskDTO.setDeliveryCarName(outBoundBatchDTO.getCarInfo());
            // deliveryTaskDTO.setDeliveryUserId(outBoundBatchDTO.getuser);
            // deliveryTaskDTO.setDeliveryCarId(outBoundBatchDTO.getca);
            deliveryTaskDTO.setDeliveryUserName(outBoundBatchDTO.getPickUpUserName());
            deliveryTaskDTO.setTaskId(outBoundBatchDTO.getRelatedBatchId().toString());
            deliveryTaskDTO.setOrderNoList(outStockOrderPOList.stream().map(OutStockOrderPO::getReforderno).distinct()
                .collect(Collectors.toList()));
            deliveryTaskDTOS.add(deliveryTaskDTO);
        }

        return deliveryTaskDTOS;
    }

    /**
     * 合并追加订单到原波次参数，走创建逻辑
     * 
     * @param batchNo
     * @param outStockOrderList
     * @param appendOutStockOrderList
     * @return
     */
    public CreateBatchBaseBO getAppendBatchCreateParams(String batchNo, List<OutStockOrderPO> outStockOrderList,
        List<OutStockOrderPO> appendOutStockOrderList) {
        String createBatchInfoStr = redisUtil.get(LockCreateBatchBL.BATCH_PARAM_CACHE_KEY + batchNo);

        CreateBatchBaseBO createBatchBaseBO = null;
        try {
            createBatchBaseBO =
                JSON.parseObject(createBatchInfoStr, new TypeReference<CreateBatchBaseBO>() {}.getType());
        } catch (Exception e) {
            throw new BusinessValidateException("参数异常，此播种任务不支持追加！");
        }
        if (Objects.isNull(createBatchBaseBO)) {
            throw new BusinessValidateException("参数异常，此播种任务不支持追加！");
        }

        if (Objects.isNull(createBatchBaseBO.getBatchCreateDTO())
            && Objects.isNull(createBatchBaseBO.getBatchCreateByRefOrderNoDTO())) {
            throw new BusinessValidateException("参数异常，此播种任务不支持追加！");
        }

        BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO = createBatchBaseBO.getBatchCreateByRefOrderNoDTO();
        BatchCreateDTO batchCreateDTO = createBatchBaseBO.getBatchCreateDTO();
        handleBatchCreateDTO(batchCreateDTO, outStockOrderList, appendOutStockOrderList);
        handleBatchCreateByRefOrderNoDTO(batchCreateByRefOrderNoDTO, outStockOrderList, appendOutStockOrderList);
        return createBatchBaseBO;
    }

    public CreateBatchBaseBO getBatchAppendParams(String batchNo, List<OutStockOrderPO> appendOutStockOrderList) {
        String createBatchInfoStr = redisUtil.get(LockCreateBatchBL.BATCH_PARAM_CACHE_KEY + batchNo);

        CreateBatchBaseBO createBatchBaseBO = null;
        try {
            createBatchBaseBO =
                JSON.parseObject(createBatchInfoStr, new TypeReference<CreateBatchBaseBO>() {}.getType());
        } catch (Exception e) {
            throw new BusinessValidateException("参数异常，此播种任务不支持追加！");
        }
        if (Objects.isNull(createBatchBaseBO)) {
            throw new BusinessValidateException("参数异常，此播种任务不支持追加！");
        }

        if (Objects.isNull(createBatchBaseBO.getBatchCreateDTO())
            && Objects.isNull(createBatchBaseBO.getBatchCreateByRefOrderNoDTO())) {
            throw new BusinessValidateException("参数异常，此播种任务不支持追加！");
        }

        BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO = createBatchBaseBO.getBatchCreateByRefOrderNoDTO();
        BatchCreateDTO batchCreateDTO = createBatchBaseBO.getBatchCreateDTO();
        handleBatchCreateDTO(batchCreateDTO, Collections.emptyList(), appendOutStockOrderList);
        handleBatchCreateByRefOrderNoDTO(batchCreateByRefOrderNoDTO, Collections.emptyList(), appendOutStockOrderList);
        return createBatchBaseBO;
    }

    private void handleBatchCreateDTO(BatchCreateDTO batchCreateDTO, List<OutStockOrderPO> outStockOrderList,
        List<OutStockOrderPO> appendOutStockOrderList) {
        if (Objects.isNull(batchCreateDTO)) {
            return;
        }
        List<Long> appendOrderIds =
            appendOutStockOrderList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outStockOrderList)) {
            List<Long> orderIds =
                outStockOrderList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
            appendOrderIds.addAll(orderIds);
        }

        batchCreateDTO.setOrderIdList(appendOrderIds.stream().map(String::valueOf).collect(Collectors.toList()));
    }

    private void handleBatchCreateByRefOrderNoDTO(BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO,
        List<OutStockOrderPO> outStockOrderList, List<OutStockOrderPO> appendOutStockOrderList) {
        if (Objects.isNull(batchCreateByRefOrderNoDTO)) {
            return;
        }
        List<String> refOrderNos = appendOutStockOrderList.stream().map(OutStockOrderPO::getReforderno).distinct()
            .collect(Collectors.toList());
        List<Long> appendOrderIds =
            outStockOrderList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());

        // 处理总单二次分拣
        if (CollectionUtils.isNotEmpty(batchCreateByRefOrderNoDTO.getDeliveryTaskList())) {
            batchCreateByRefOrderNoDTO.getRefOrderNos().addAll(refOrderNos);
            resetAppendBatchCreateDeliveryTask(batchCreateByRefOrderNoDTO, appendOutStockOrderList,
                batchCreateByRefOrderNoDTO.getWarehouseId());
            return;
        }
        if (CollectionUtils.isNotEmpty(batchCreateByRefOrderNoDTO.getRefOrderNos())) {
            batchCreateByRefOrderNoDTO.getRefOrderNos().addAll(refOrderNos);
            return;
        }

    }

    private List<DeliveryTaskDTO> resetAppendBatchCreateDeliveryTask(
        BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO, List<OutStockOrderPO> appendOutStockOrderList,
        Integer warehouseId) {

        boolean notHaveBoundNo = appendOutStockOrderList.stream().anyMatch(o -> StringUtils.isBlank(o.getBoundNo()));
        if (notHaveBoundNo) {
            String orderNoList = appendOutStockOrderList.stream().filter(o -> StringUtils.isBlank(o.getBoundNo()))
                .map(o -> o.getReforderno()).collect(Collectors.joining(","));

            throw new BusinessValidateException("订单" + orderNoList + "未分车，无法追加到总单二次分拣波次！请先分车后再追加！");
        }

        Map<String, DeliveryTaskDTO> deliveryTaskDTOMap = batchCreateByRefOrderNoDTO.getDeliveryTaskList().stream()
            .collect(Collectors.toMap(DeliveryTaskDTO::getTaskId, v -> v));

        BatchQueryOutBoundBatchDTO queryOutBoundBatchDTO = new BatchQueryOutBoundBatchDTO();
        queryOutBoundBatchDTO.setWarehouseId(warehouseId);
        List<String> boundNoList =
            appendOutStockOrderList.stream().map(OutStockOrderPO::getBoundNo).distinct().collect(Collectors.toList());
        queryOutBoundBatchDTO.setOutBoundBatchNos(boundNoList);

        List<OutBoundBatchDTO> outBoundBatchDTOList =
            iOutBoundBatchQueryService.findOutBoundBatchList(queryOutBoundBatchDTO);

        Map<String, List<OutStockOrderPO>> orderGroupByBoundList =
            appendOutStockOrderList.stream().collect(Collectors.groupingBy(OutStockOrderPO::getBoundNo));

        Map<String, OutBoundBatchDTO> outBoundBatchDTOMap =
            outBoundBatchDTOList.stream().collect(Collectors.toMap(OutBoundBatchDTO::getBoundBatchNo, v -> v));

        Map<String, String> taskBoundNoMap = outBoundBatchDTOList.stream()
            .collect(Collectors.toMap(k -> k.getRelatedBatchId().toString(), OutBoundBatchDTO::getBoundBatchNo));

        List<DeliveryTaskDTO> appendTaskDTOList = new ArrayList<>();
        for (Map.Entry<String, OutBoundBatchDTO> entry : outBoundBatchDTOMap.entrySet()) {
            OutBoundBatchDTO outBoundBatchDTO = entry.getValue();
            List<OutStockOrderPO> outStockOrderPOList = orderGroupByBoundList.get(entry.getKey());
            DeliveryTaskDTO deliveryTaskDTO = new DeliveryTaskDTO();
            deliveryTaskDTO.setDeliveryTaskNo(outBoundBatchDTO.getRelatedBatchNo());
            deliveryTaskDTO.setDeliveryCarName(outBoundBatchDTO.getCarInfo());
            // deliveryTaskDTO.setDeliveryUserId(outBoundBatchDTO.getuser);
            // deliveryTaskDTO.setDeliveryCarId(outBoundBatchDTO.getca);
            deliveryTaskDTO.setDeliveryUserName(outBoundBatchDTO.getPickUpUserName());
            deliveryTaskDTO.setTaskId(outBoundBatchDTO.getRelatedBatchId().toString());
            deliveryTaskDTO.setOrderNoList(outStockOrderPOList.stream().map(OutStockOrderPO::getReforderno).distinct()
                .collect(Collectors.toList()));

            appendTaskDTOList.add(deliveryTaskDTO);
        }

        for (DeliveryTaskDTO deliveryTaskDTO : appendTaskDTOList) {
            DeliveryTaskDTO existTaskDTO = deliveryTaskDTOMap.get(deliveryTaskDTO.getTaskId());

            if (Objects.isNull(existTaskDTO)) {
                batchCreateByRefOrderNoDTO.getDeliveryTaskList().add(deliveryTaskDTO);
            } else {
                existTaskDTO.getOrderNoList().addAll(deliveryTaskDTO.getOrderNoList());
            }
        }

        return batchCreateByRefOrderNoDTO.getDeliveryTaskList();
    }

    public ProcessBatchDTO
        convertProcessBatchDTOByBatchCreateByRefOrderNoDTO(BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO) {
        ProcessBatchDTO processBatchDTO = new ProcessBatchDTO();
        processBatchDTO.setBatchName(batchCreateByRefOrderNoDTO.getTitle());
        processBatchDTO.setOperateUser(batchCreateByRefOrderNoDTO.getOperateUser());
        processBatchDTO.setLocationName(batchCreateByRefOrderNoDTO.getLocationName());
        processBatchDTO.setDriverName(batchCreateByRefOrderNoDTO.getDriverName());
        processBatchDTO.setAllocationFlag(batchCreateByRefOrderNoDTO.getAllocationFlag());
        processBatchDTO.setToLocationId(batchCreateByRefOrderNoDTO.getToLocationId());
        processBatchDTO.setToLocationName(batchCreateByRefOrderNoDTO.getToLocationName());
        processBatchDTO.setNotThrowException(ConditionStateEnum.是.getType());
        processBatchDTO.setOrderList(batchCreateByRefOrderNoDTO.getOrderList());
        processBatchDTO.setOrderPickFlag(batchCreateByRefOrderNoDTO.getOrderPickFlag());
        processBatchDTO.setOperateUserId(batchCreateByRefOrderNoDTO.getOperateUserId());

        return processBatchDTO;
    }

    public void handleDeliveryTask(BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO,
        WavesStrategyBO wavesStrategyDTO, ProcessBatchDTO processBatchDTO) {
        List<DeliveryTaskDTO> deliveryTaskList = batchCreateByRefOrderNoDTO.getDeliveryTaskList();

        if (org.springframework.util.CollectionUtils.isEmpty(deliveryTaskList)) {
            return;
        }
        // if (org.springframework.util.CollectionUtils.isEmpty(outOrderList)) {
        // LOG.info("[通过波次创建出库批次]出库单列表为空");
        // return;
        // }
        Integer orgId = wavesStrategyDTO.getOrgId();
        Integer warehouseId = wavesStrategyDTO.getWarehouseId();

        // 未开启二次分拣时，注意：这里决定不了已经开启酒批二次分拣，因为通道也要为二次分拣，但能决定没有开启二次分拣
        if (deliveryTaskList.size() == 1 || !globalCache.isOpenSecondSortFromCache(warehouseId)) {
            if (processBatchDTO.getBatchName() == null) {
                processBatchDTO.setBatchName(batchTitleConvertor.getBatchTitleByDeliveryTask(deliveryTaskList.get(0)));
                LOG.info("title生成结果：{}", processBatchDTO.getBatchName());
            }

            LOG.info("仓库未开启二次分拣，仓库：{}，车次数量：{}", warehouseId, deliveryTaskList.size());
            return;
        }

        wavesStrategyDTO.setIsOpenSecondSort(Boolean.TRUE);
        // 目前，只有酒饮二次分拣，才支持多车次创建波次
        wavesStrategyDTO.setIsMultiOutBound(Boolean.TRUE);
    }

    public ProcessBatchDTO convertProcessBatchDTOByBatchCreateDTO(BatchCreateDTO batchCreateDTO) {
        ProcessBatchDTO baseProcessBatchDTO = new ProcessBatchDTO();
        baseProcessBatchDTO.setBatchName(batchCreateDTO.getBatchName());
        baseProcessBatchDTO.setOperateUser(batchCreateDTO.getOperateUser());
        baseProcessBatchDTO.setExpressFlag(batchCreateDTO.getExpressFlag());
        baseProcessBatchDTO.setBatchType(batchCreateDTO.getBatchType());
        baseProcessBatchDTO.setOrderPickFlag(batchCreateDTO.getOrderPickFlag());
        // 默认按线路或片区拆分
        baseProcessBatchDTO.setGroupFlag(ConditionStateEnum.是.getType());
        baseProcessBatchDTO.setOperateUserId(batchCreateDTO.getOperateUserId());

        return baseProcessBatchDTO;
    }

    public WavesStrategyBO getByBatchCreateDTO(BatchCreateDTO batchCreateDTO) {
        // 组装波次参数
        WavesStrategyBO wavesStrategyDTO = wavesStrategyBOConvertor.getWavesStrategyDTOByString(
            batchCreateDTO.getWarehouseId(), batchCreateDTO.getPassPickType(), batchCreateDTO.getPickingType(),
            batchCreateDTO.getPickingGroupStrategy(), batchCreateDTO.getGroupType(), batchCreateDTO.getOrderPickFlag());
        wavesStrategyDTO.setDeliveryCarId(batchCreateDTO.getDeliveryCarId());
        wavesStrategyDTO.setLogisticsCompanyId(batchCreateDTO.getLogisticsCompanyId());

        return wavesStrategyDTO;
    }

    public WavesStrategyBO getByBatchCreateByRefOrderNoDTO(BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO) {
        WavesStrategyBO.WavesStrategyBOBuilder builder = WavesStrategyBO.WavesStrategyBOBuilder
            .buildWavesStrategyBuilder().withWarehouseId(batchCreateByRefOrderNoDTO.getWarehouseId())
            .withPassPickType(batchCreateByRefOrderNoDTO.getPassPickType())
            .withPickingType(batchCreateByRefOrderNoDTO.getPickingType())
            .withGroupType(batchCreateByRefOrderNoDTO.getGroupType())
            .withPickingGroupStrategy(batchCreateByRefOrderNoDTO.getPickingGroupStrategy())
            .withDeliveryCarId(batchCreateByRefOrderNoDTO.getDeliveryCarId())
            .withLogisticsCompanyId(batchCreateByRefOrderNoDTO.getLogisticsCompanyId())
            .withOrderPickFlag(batchCreateByRefOrderNoDTO.getOrderPickFlag());
        WavesStrategyBO wavesStrategyDTO = wavesStrategyBOConvertor.createByBuilder(builder);
        wavesStrategyDTO.setDeliveryCarId(batchCreateByRefOrderNoDTO.getDeliveryCarId());
        wavesStrategyDTO.setLogisticsCompanyId(batchCreateByRefOrderNoDTO.getLogisticsCompanyId());

        return wavesStrategyDTO;
    }

    public WavesStrategyBO getAppendWavesStrategyBO(CreateBatchBaseBO createBatchBaseBO) {
        if (Objects.nonNull(createBatchBaseBO.getBatchCreateDTO())) {
            return getByBatchCreateDTO(createBatchBaseBO.getBatchCreateDTO());
        }
        if (Objects.nonNull(createBatchBaseBO.getBatchCreateByRefOrderNoDTO())) {
            return getByBatchCreateByRefOrderNoDTO(createBatchBaseBO.getBatchCreateByRefOrderNoDTO());
        }

        return null;
    }

    public ProcessBatchDTO getAppendProcessBatchDTOAndHandleDeliveryTask(CreateBatchBaseBO createBatchBaseBO,
        WavesStrategyBO wavesStrategyBO) {
        if (Objects.nonNull(createBatchBaseBO.getBatchCreateDTO())) {
            return convertProcessBatchDTOByBatchCreateDTO(createBatchBaseBO.getBatchCreateDTO());
        }
        if (Objects.nonNull(createBatchBaseBO.getBatchCreateByRefOrderNoDTO())) {
            BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO = createBatchBaseBO.getBatchCreateByRefOrderNoDTO();
            ProcessBatchDTO processBatchDTO =
                convertProcessBatchDTOByBatchCreateByRefOrderNoDTO(batchCreateByRefOrderNoDTO);

            handleDeliveryTask(batchCreateByRefOrderNoDTO, wavesStrategyBO, processBatchDTO);

            return processBatchDTO;
        }

        return null;
    }

}
