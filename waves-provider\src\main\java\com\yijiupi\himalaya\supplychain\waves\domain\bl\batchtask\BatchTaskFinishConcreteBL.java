package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.exceptions.HasLockedException;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryManageService;
import com.yijiupi.himalaya.supplychain.enums.InventoryChangeTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.*;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchFinishedBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskFinishHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishNeedOpBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.util.UUIDGeneratorUtil;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
@Service
public class BatchTaskFinishConcreteBL {

    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private PackageOrderItemBL packageOrderItemBL;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private BatchTaskItemContainerMapper batchTaskItemContainerMapper;
    @Autowired
    private SowQueryBL sowQueryBL;
    @Autowired
    private BatchFinishedBL batchFinishedBL;
    @Autowired
    private OutStockOrderBL outStockOrderBL;
    @Autowired
    private OrderTraceBL orderTraceBL;
    @Autowired
    private BatchOrderBL batchOrderBL;
    @Autowired
    private BatchTaskFinishHandleSecondSortBL batchTaskFinishHandleSecondSortBL;
    @Autowired
    private BatchTaskFinishValidateBL batchTaskFinishValidateBL;
    @Autowired
    private List<BatchTaskItemFinishDecoratorBL> decoratorList;
    @Autowired
    private OrderItemTaskInfoDetailMapper orderItemTaskInfoDetailMapper;

    @Reference
    private IBatchInventoryManageService iBatchInventoryManageService;
    @Reference
    private com.yijiupi.himalaya.supplychain.outstock.service.IOrderQueryService orderQueryService;

    protected static final Logger LOGGER = LoggerFactory.getLogger(BatchTaskFinishConcreteBL.class);

    /**
     * @see BatchOrderTaskBL#updateBatchTaskItem ；BatchOrderTaskBL#batchTaskComplete
     *
     * @param batchTaskItemFinishBO
     * @return
     */
    public Map<String, List<BatchTaskDTO>> finishBatchTaskItem(BatchTaskItemFinishBO batchTaskItemFinishBO) {
        LOGGER.info("提交拣货：{}", JSON.toJSONString(batchTaskItemFinishBO));
        List<BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOS = batchTaskItemFinishBO.getBatchTaskItemCompleteDTOS();
        String batchTaskId = batchTaskItemFinishBO.getBatchTaskId();

        // 初始化数据
        BatchTaskFinishHelperBO batchTaskFinishHelperBO = initBatchTaskFinishHelper(batchTaskItemFinishBO);
        // 校验
        batchTaskFinishValidateBL.validate(batchTaskFinishHelperBO);

        Map<String, List<BatchTaskDTO>> msg = doFinishBatchTaskItem(batchTaskFinishHelperBO);
        completeBatch(batchTaskFinishHelperBO);

        return msg;
    }

    private Map<String, List<BatchTaskDTO>> doFinishBatchTaskItem(BatchTaskFinishHelperBO batchTaskFinishHelperBO) {
        BatchTaskItemFinishNeedOpBO bo = new BatchTaskItemFinishNeedOpBO();
        decoratorList.forEach(decorator -> {
            decorator.completeBatchTaskItem(bo, batchTaskFinishHelperBO);
        });

        // 验证是否是美团订单缺货
        List<OrderItemTaskInfoPO> items = batchTaskFinishHelperBO.getUpdateBatchTaskItemRelateOrderItemTaskInfoPOList();
        List<BatchTaskPO> batch = Collections.singletonList(batchTaskFinishHelperBO.getBatchTaskPO());
        batchTaskFinishValidateBL.validateIsMeiTuan(items, batch);

        handleBatchTaskItemFinishNeedOpBO(batchTaskFinishHelperBO, bo);

        // 处理先装箱，然后全缺，需要清空箱号的情况
        handleAllPackageRemove(batchTaskFinishHelperBO);
        // 完成拣货任务
        completeBatchTask(batchTaskFinishHelperBO, bo);

        // 处理按订单拣货场景
        return handlePickByOrder(batchTaskFinishHelperBO);
    }

    private void handleBatchTaskItemFinishNeedOpBO(BatchTaskFinishHelperBO batchTaskFinishHelperBO,
        BatchTaskItemFinishNeedOpBO bo) {
        BatchTaskItemFinishBO batchTaskItemFinishBO = batchTaskFinishHelperBO.getBatchTaskItemFinishBO();
        List<BatchTaskItemUpdatePO> poList = bo.getPoList();
        if (CollectionUtils.isNotEmpty(bo.getPoList())) {
            batchTaskItemMapper.updateBatchTaskByItemIncludeLocation(poList, batchTaskItemFinishBO.getCityId());
        }

        List<BatchTaskItemContainerPO> addContainerPOList = bo.getAddContainerList();
        if (CollectionUtils.isNotEmpty(addContainerPOList)) {
            addContainerPOList
                .forEach(p -> p.setId(UUIDGeneratorUtil.getUUID(BatchTaskItemContainerPO.class.getName())));
            batchTaskItemContainerMapper.insertList(addContainerPOList);
        }

        List<BatchTaskItemContainerPO> updateContainerPOList = bo.getUpdateContainerList();
        if (CollectionUtils.isNotEmpty(updateContainerPOList)) {
            updateContainerPOList.forEach(containerPO -> {
                batchTaskItemContainerMapper.updateByPrimaryKeySelective(containerPO);
            });
        }

        List<PackageOrderItemDTO> packageOrderItemDTOS = bo.getPackageOrderItemDTOS();
        if (CollectionUtils.isNotEmpty(packageOrderItemDTOS)) {
            packageOrderItemBL.savePackageBatch(packageOrderItemDTOS, true);
        }

        if (CollectionUtils.isNotEmpty(bo.getUpdateOrderItemTaskInfoList())) {
            Lists.partition(bo.getUpdateOrderItemTaskInfoList(), 100).forEach(p -> {
                orderItemTaskInfoMapper.updateBatch(p);
            });
            LOGGER.info("[PDA拣货]更新【orderItemTaskInfo】拣货数量和缺货数量：{}",
                JSON.toJSONString(bo.getUpdateOrderItemTaskInfoList()));
        }

        if (CollectionUtils.isNotEmpty(bo.getUpdateOrderItemTaskInfoDetailPOList())) {
            Lists.partition(bo.getUpdateOrderItemTaskInfoDetailPOList(), 100).forEach(p -> {
                orderItemTaskInfoDetailMapper.updateBatch(p);
            });
            LOGGER.info("[PDA拣货]更新【orderItemTaskInfoDetail】分配数量：{}",
                JSON.toJSONString(bo.getUpdateOrderItemTaskInfoDetailPOList()));
        }

        List<PickUpDTO> pickUpDTOList = bo.getPickUpDTOList();
        completePickUp(batchTaskFinishHelperBO, pickUpDTOList);
        processHaveSowTask(batchTaskFinishHelperBO, bo);
        processSecondSort(batchTaskFinishHelperBO, bo);
    }

    // 处理按订单拣货场景
    private Map<String, List<BatchTaskDTO>> handlePickByOrder(BatchTaskFinishHelperBO batchTaskFinishHelperBO) {
        List<BatchTaskItemDTO> batchTaskItemDTOList =
            BatchTaskItemFinishDecoratorBL.getNeedCompleteBatchTaskItemList(batchTaskFinishHelperBO);

        if (CollectionUtils.isEmpty(batchTaskItemDTOList)) {
            return Collections.emptyMap();
        }

        BatchPO batchPO = batchTaskFinishHelperBO.getBatchPO();
        BatchTaskPO batchTaskPO = batchTaskFinishHelperBO.getBatchTaskPO();
        List<Long> outStockOrderIds = new ArrayList<>();
        List<String> orderNos = new ArrayList<>();

        for (BatchTaskItemDTO batchTaskItemDTO : batchTaskItemDTOList) {
            if (StringUtils.isNotEmpty(batchTaskItemDTO.getRefOrderId())
                && PickingTypeEnum.订单拣货.getType() == batchTaskPO.getPickingType()) {
                // 按订单拣货，一次至少完成一个订单
                outStockOrderIds.add(Long.valueOf(batchTaskItemDTO.getRefOrderId()));
            }
            if (StringUtils.isNotEmpty(batchTaskItemDTO.getRefOrderNo())
                && PickingTypeEnum.订单拣货.getType() == batchTaskPO.getPickingType()) {
                orderNos.add(batchTaskItemDTO.getRefOrderNo());
            }
        }

        // 7、按订单拣货时，一个订单拣完时更新出库单状态
        if (outStockOrderIds.size() > 0) {
            outStockOrderBL.updateStateByOrderIds(outStockOrderIds, batchPO);
        }

        // 8、分单拣货(整单完成需保存打印数据，整单未完成通知PDA将拆分出去的单合过来)
        if (batchTaskPO.getPickingType() == PickingTypeEnum.订单拣货.getType() && CollectionUtils.isNotEmpty(orderNos)) {
            orderNos = orderNos.stream().distinct().collect(Collectors.toList());
            BatchTaskItemFinishBO batchTaskItemFinishBO = batchTaskFinishHelperBO.getBatchTaskItemFinishBO();
            Integer warehouseId = batchTaskItemFinishBO.getWarehouseId();
            String userName = batchTaskItemFinishBO.getUserName();
            Integer cityId = batchPO.getOrgId();
            Set<String> updateBatchTaskIds = batchTaskItemFinishBO.getBatchTaskItemCompleteDTOS().stream()
                .map(BatchTaskItemCompleteDTO::getId).collect(Collectors.toSet());
            return batchOrderBL.separateOrderPicking(batchTaskPO.getId(), updateBatchTaskIds, orderNos, userName,
                warehouseId, cityId);
        }

        return Collections.emptyMap();
    }

    private void completeBatchTask(BatchTaskFinishHelperBO batchTaskFinishHelperBO, BatchTaskItemFinishNeedOpBO bo) {
        BatchTaskPO batchTaskPO = bo.getCompleteBatchTaskPO();
        if (Objects.isNull(batchTaskPO)) {
            return;
        }

        String userName = batchTaskFinishHelperBO.getBatchTaskItemFinishBO().getUserName();
        batchTaskMapper.updateBatchTaskById(batchTaskPO.getId(), batchTaskPO.getTaskState(),
            batchTaskPO.getLocationId(), batchTaskPO.getLocationName(), null, null, null,
            Integer.valueOf(batchTaskPO.getOrgId()), null);

        if (batchTaskPO.getTaskState() == TaskStateEnum.已完成.getType()) {
            // -添加操作记录（完成拣货）
            orderTraceBL.updateBatchTaskTrace(batchTaskPO, OrderTraceDescriptionEnum.拣货完成.name(), userName);
        }
    }

    /**
     * 有播种任务的拣货任务项实时标记缺货
     *
     * @param batchTaskFinishHelperBO
     * @param bo
     */
    private void processHaveSowTask(BatchTaskFinishHelperBO batchTaskFinishHelperBO, BatchTaskItemFinishNeedOpBO bo) {
        BatchPO batchPO = batchTaskFinishHelperBO.getBatchPO();
        List<BatchTaskItemUpdatePO> poList = bo.getPoList();
        // 4、有播种任务的拣货任务项实时标记缺货
        List<String> batchTaskItemIds = poList.stream().map(BatchTaskItemUpdatePO::getId).collect(Collectors.toList());
        Integer userId = batchTaskFinishHelperBO.getBatchTaskItemFinishBO().getUserId();

        Map<Long, BigDecimal> lastTaskInfoLackCountMap = batchTaskFinishHelperBO.getLastTaskInfoLackCountMap();

        /** 二次分拣，不实时同步oms */
        if (!Objects.equals(batchPO.getSowType(), BatchSowTypeEnum.二次分拣.getType())) {
            outStockOrderBL.markLackBySowTask(batchTaskItemIds, lastTaskInfoLackCountMap, userId);
        }
    }

    private void processSecondSort(BatchTaskFinishHelperBO batchTaskFinishHelperBO, BatchTaskItemFinishNeedOpBO bo) {
        BatchPO batchPO = batchTaskFinishHelperBO.getBatchPO();
        BatchTaskPO updateBatchTaskPO = bo.getCompleteBatchTaskPO();
        List<BatchTaskItemUpdatePO> poList = bo.getPoList();

        // 二次分拣拣货任务，则缺货更新出库单
        if (Objects.equals(batchPO.getSowType(), BatchSowTypeEnum.二次分拣.getType())) {
            // TODO 几个参数
            batchTaskFinishHandleSecondSortBL.updateSecondSow(poList, bo.getUpdateOrderItemTaskInfoList(), batchPO,
                updateBatchTaskPO, updateBatchTaskPO.getTaskState());
        }
    }

    private void completePickUp(BatchTaskFinishHelperBO batchTaskFinishHelperBO, List<PickUpDTO> pickUpDTOList) {
        BatchPO batchPO = batchTaskFinishHelperBO.getBatchPO();
        BatchTaskPO batchTaskPO = batchTaskFinishHelperBO.getBatchTaskPO();
        BatchTaskItemFinishBO batchTaskItemFinishBO = batchTaskFinishHelperBO.getBatchTaskItemFinishBO();
        // 是否跨库
        boolean isCrossWareHouse = Objects.equals(batchPO.getCrossWareHouse(), ConditionStateEnum.是.getType());
        // 9、移库操作
        if (CollectionUtils.isNotEmpty(pickUpDTOList) && !isCrossWareHouse) {
            LOGGER.info("拣货移库入参：{}，batchTaskNO：{}", new Gson().toJson(pickUpDTOList), batchTaskPO.getBatchTaskNo());
            PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
            pickUpChangeRecordDTO.setCityId(batchTaskItemFinishBO.getCityId());
            pickUpChangeRecordDTO.setOrderId(batchTaskPO.getId());
            pickUpChangeRecordDTO.setOrderNo(batchTaskPO.getBatchTaskNo());
            pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.PDA提交拣货.name());
            pickUpChangeRecordDTO.setCreateUser(batchTaskItemFinishBO.getUserName());
            iBatchInventoryManageService.pickupCompleteBySku(pickUpDTOList, pickUpChangeRecordDTO);
        }
    }

    private void completeBatch(BatchTaskFinishHelperBO batchTaskFinishHelperBO) {
        BatchTaskPO batchTaskPO = batchTaskFinishHelperBO.getBatchTaskPO();
        Integer userId = batchTaskFinishHelperBO.getBatchTaskItemFinishBO().getUserId();
        Integer cityId = batchTaskFinishHelperBO.getBatchTaskItemFinishBO().getCityId();
        // 修改波次状态
        try {
            batchFinishedBL.completeWave(batchTaskPO.getBatchNo(), userId, cityId);
            // batchOrderBL.updateBatchStateByBatchNo(batchTaskOld.getBatchNo(), userName, userId);
        } catch (HasLockedException e) {
            throw new BusinessValidateException("正在处理，请稍后再试！");
        }
    }

    private void handleAllPackageRemove(BatchTaskFinishHelperBO batchTaskFinishHelperBO) {
        List<BatchTaskItemCompleteDTO> packageUpdateList =
            batchTaskFinishHelperBO.getBatchTaskItemFinishBO().getBatchTaskItemCompleteDTOS().stream()
                .filter(dto -> CollectionUtils.isNotEmpty(dto.getBoxCodeList())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(packageUpdateList)) {
            return;
        }
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            batchTaskFinishHelperBO.getUpdateBatchTaskItemRelateOrderItemTaskInfoPOList();
        List<BatchTaskItemDTO> batchTaskItemList = batchTaskFinishHelperBO.getTotalBatchTaskItemList();
        List<OrderItemTaskInfoPO> currOrderItemTaskInfoPOS =
            getCurBatchTaskItem(batchTaskItemList, orderItemTaskInfoPOList);
        Map<String, BatchTaskItemCompleteDTO> map = batchTaskFinishHelperBO.getBatchTaskItemCompleteDTOMap();

        List<BatchTaskItemDTO> allLackBatchTaskItemList = filterAllLackBatchTaskItemList(batchTaskItemList, map);
        if (CollectionUtils.isEmpty(allLackBatchTaskItemList)) {
            return;
        }
        packageOrderItemBL.handleAllPackageRemove(currOrderItemTaskInfoPOS, allLackBatchTaskItemList);
    }

    private List<BatchTaskItemDTO> filterAllLackBatchTaskItemList(List<BatchTaskItemDTO> batchTaskItemList,
        Map<String, BatchTaskItemCompleteDTO> map) {
        return batchTaskItemList.stream().filter(item -> Objects.nonNull(map.get(item.getId()))).filter(item -> {
            BatchTaskItemCompleteDTO updateItem = map.get(item.getId());
            return BigDecimal.ZERO.compareTo(updateItem.getOverSortPackageCount()) == 0
                && BigDecimal.ZERO.compareTo(updateItem.getOverSortUnitCount()) == 0;
        }).collect(Collectors.toList());
    }

    private BatchTaskFinishHelperBO initBatchTaskFinishHelper(BatchTaskItemFinishBO batchTaskItemFinishBO) {
        List<BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOS = batchTaskItemFinishBO.getBatchTaskItemCompleteDTOS();
        String batchTaskId = batchTaskItemFinishBO.getBatchTaskId();
        String userName = batchTaskItemFinishBO.getUserName();
        Integer warehouseId = batchTaskItemFinishBO.getWarehouseId();

        BatchTaskFinishHelperBO batchTaskFinishHelperBO = new BatchTaskFinishHelperBO();

        List<String> updateBatchTaskItemIds =
            batchTaskItemCompleteDTOS.stream().map(BatchTaskItemCompleteDTO::getId).collect(Collectors.toList());
        List<BatchTaskItemPO> oldBatchTaskItemPOS =
            batchTaskItemMapper.listBatchTaskItemByIds(updateBatchTaskItemIds, null);
        AssertUtils.notEmpty(oldBatchTaskItemPOS, "拣货详情不存在，请刷新重试！");

        BatchTaskPO batchTaskOld = batchTaskMapper.findBatchTaskById(batchTaskId);
        AssertUtils.notNull(batchTaskOld, "拣货任务不存在！Id：" + batchTaskId);

        List<BatchTaskItemPO> notInBatchTaskItems = oldBatchTaskItemPOS.stream()
            .filter(m -> !m.getBatchTaskId().equals(batchTaskId)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(notInBatchTaskItems)) {
            LOGGER.warn("拣货任务明细不属于该拣货任务, 明细为:{}, 拣货任务id为：{}", JSON.toJSONString(notInBatchTaskItems), batchTaskId);
            String productNameList = notInBatchTaskItems.stream().map(BatchTaskItemPO::getProductName).distinct()
                .collect(Collectors.joining(","));
            throw new BusinessValidateException("拣货任务明细" + productNameList + "不属于该拣货任务，请刷新重试！");
        }

        Integer orgId = Integer.valueOf(batchTaskOld.getOrgId());
        BatchPO batchPO = batchMapper.selectBatchByBatchNo(orgId, batchTaskOld.getBatchNo());

        // 查找该波次任务所有商品详情
        List<BatchTaskItemDTO> batchTaskItemDTOList = batchTaskItemMapper.findBatchTaskItemDtoListById(batchTaskId);
        List<SowTaskDTO> sowTaskDTOS = getSowTasks(batchTaskItemDTOList, orgId);
        Long sowLocationId = getSowLocationId(batchTaskOld, sowTaskDTOS);

        Map<String, BatchTaskItemCompleteDTO> map =
            batchTaskItemCompleteDTOS.stream().collect(Collectors.toMap(BatchTaskItemCompleteDTO::getId, v -> v));

        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByBatchTaskItemIds(updateBatchTaskItemIds);

        List<OrderItemTaskInfoPO> needPackageOrderItemTaskInfo =
            getNeedPackageOrderItemTaskInfo(batchTaskItemCompleteDTOS, batchTaskItemDTOList, orderItemTaskInfoPOList);

        List<Long> outStockOrderIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderId)
            .distinct().collect(Collectors.toList());

        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findIdByIds(outStockOrderIds, warehouseId);

        Map<Long, BigDecimal> taskInfoDetailOverSortCountMap =
            BatchTaskFinishHelperBO.initTaskInfoDetailOverSortCountMap(orderItemTaskInfoPOList);

        Map<Long, BigDecimal> lastTaskInfoLackCountMap =
            BatchTaskFinishHelperBO.initLastTaskInfoLackCountMap(orderItemTaskInfoPOList);

        batchTaskFinishHelperBO.setBatchPO(batchPO);
        batchTaskFinishHelperBO.setBatchTaskPO(batchTaskOld);
        batchTaskFinishHelperBO.setPickedBatchTaskItemList(oldBatchTaskItemPOS);
        batchTaskFinishHelperBO.setBatchTaskItemFinishBO(batchTaskItemFinishBO);
        batchTaskFinishHelperBO.setSowTasks(sowTaskDTOS);
        batchTaskFinishHelperBO.setSowLocationId(sowLocationId);
        batchTaskFinishHelperBO.setBatchTaskItemCompleteDTOMap(map);
        batchTaskFinishHelperBO.setUpdateBatchTaskItemRelateOrderItemTaskInfoPOList(orderItemTaskInfoPOList);
        batchTaskFinishHelperBO.setNeedPackageOrderItemTaskInfo(needPackageOrderItemTaskInfo);
        // 先用，看能改回 PO 不
        batchTaskFinishHelperBO.setTotalBatchTaskItemList(batchTaskItemDTOList);
        batchTaskFinishHelperBO.setRelatedOutStockOrderList(outStockOrderPOList);
        batchTaskFinishHelperBO.setTaskInfoDetailOverSortCountMap(taskInfoDetailOverSortCountMap);
        batchTaskFinishHelperBO.setLastTaskInfoLackCountMap(lastTaskInfoLackCountMap);

        return batchTaskFinishHelperBO;
    }

    private List<OrderItemTaskInfoPO> getNeedPackageOrderItemTaskInfo(
        List<BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOS, List<BatchTaskItemDTO> batchTaskItemDTOList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {

        List<BatchTaskItemCompleteDTO> packageUpdateList = batchTaskItemCompleteDTOS.stream()
            .filter(dto -> CollectionUtils.isNotEmpty(dto.getBoxCodeList())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(packageUpdateList)) {
            return Collections.emptyList();
        }
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOS =
            getCurBatchTaskItem(batchTaskItemDTOList, orderItemTaskInfoPOList);

        return orderItemTaskInfoPOS;
    }

    // 这里第一个参数应该传错了，应该是需要更新的 batchTaskItem，而不是整个的
    private List<OrderItemTaskInfoPO> getCurBatchTaskItem(List<BatchTaskItemDTO> batchTaskItemDTOList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOS;
        List<String> batchTaskItemIds =
            batchTaskItemDTOList.stream().map(BatchTaskItemDTO::getId).collect(Collectors.toList());
        // 查询订单拣货任务关联表
        orderItemTaskInfoPOS = orderItemTaskInfoPOList.stream()
            .filter(item -> batchTaskItemIds.contains(item.getBatchTaskItemId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOS)) {
            // 兼容旧数据
            orderItemTaskInfoPOS = outStockOrderItemMapper.listOrderItemTaskInfoByBatchTaskItemIds(batchTaskItemIds);
        }

        return orderItemTaskInfoPOS;
    }

    private Long getSowLocationId(BatchTaskPO batchTaskOld, List<SowTaskDTO> sowTaskDTOS) {
        if (CollectionUtils.isEmpty(sowTaskDTOS)) {
            return null;
        }
        if (Objects.isNull(batchTaskOld.getSowTaskId())) {
            return null;
        }

        Optional<SowTaskDTO> sowTaskDTOOptional =
            sowTaskDTOS.stream().filter(p -> p.getId().equals(batchTaskOld.getSowTaskId())).findAny();
        if (!sowTaskDTOOptional.isPresent()) {
            return null;
        }
        SowTaskDTO sowTaskDTO = sowTaskDTOOptional.get();
        if (Objects.isNull(sowTaskDTO.getLocationId())) {
            return null;
        }

        return sowTaskDTO.getLocationId();
    }

    private List<SowTaskDTO> getSowTasks(List<BatchTaskItemDTO> batchTaskItemDTOList, Integer orgId) {
        List<String> sowTaskNoList = batchTaskItemDTOList.stream().filter(p -> StringUtils.isNotEmpty(p.getSowTaskNo()))
            .map(BatchTaskItemDTO::getSowTaskNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sowTaskNoList)) {
            return Collections.emptyList();
        }
        return sowQueryBL.findSowTaskByTaskNos(orgId, sowTaskNoList);
    }

}
