// package com.yijiupi.himalaya.supplychain.waves.domain.dao;
//
// import com.yijiupi.himalaya.supplychain.algorithm.dto.replenishstock.BatchTaskItemSummaryDTO;
// import com.yijiupi.himalaya.supplychain.algorithm.service.replenishstock.InventoryReplenishmentSystem;
// import com.yijiupi.himalaya.supplychain.algorithm.service.replenishstock.ReplenishmentRecommendation;
// import com.yijiupi.himalaya.supplychain.waves.dto.algorithm.DynamicReplenishmentQuery;
// import com.yijiupi.junit.runner.GeneralRunner;
// import org.apache.ibatis.annotations.Mapper;
// import org.junit.Test;
// import org.junit.runner.RunWith;
//
// import java.time.LocalDate;
// import java.time.LocalDateTime;
// import java.time.temporal.ChronoUnit;
// import java.util.*;
// import java.util.stream.Collectors;
//
//
/// **
// * <AUTHOR> 2018/3/24
// */
// @RunWith(GeneralRunner.class)
// public class BatchTaskMapperTest {
// // 历史数据保留天数（用于训练预测模型）
// private static final int DAYS_HISTORY = 30;
// // 每天的小时数（用于定义时间序列的粒度）
// private static final int HOURS_PER_DAY = 24;
// @Mapper
// private BatchTaskItemMapper batchTaskItemMapper;
//
// public Map<String, List<Double>> bbb(String dateStr) {
// // 查询 7041 2025-06-07 每小时销售量
// //String dateStr = "2025-06-07";
// DynamicReplenishmentQuery query = new DynamicReplenishmentQuery();
// query.setOrgId(704);
// query.setWarehouseId(7041);
// query.setDateStr(dateStr);
// query.setSkuIds(Collections.singletonList(5364872495651689573L));
// query.setLimitSku(100);
// List<BatchTaskItemBO> batchTaskItemOneDay = batchTaskItemMapper.findBatchTaskItemOneDay(query);
// // 过滤掉赠品
//
// Map<String, List<BatchTaskItemSummaryDTO>> aabbcc = aabbcc(dateStr, 1, batchTaskItemOneDay);
//
// Map<String, List<Double>> skuActualValueMap = new HashMap<>();
//
// aabbcc.forEach((skuId, batchTaskItemSummaryDTOList) -> {
// skuActualValueMap.computeIfAbsent(skuId, k -> new ArrayList<>());
// List<Double> actualValueList = skuActualValueMap.get(skuId);
//
// while (actualValueList.size() < HOURS_PER_DAY) {
// // 初始填充0值
// actualValueList.add(0.0);
// }
//
// for (BatchTaskItemSummaryDTO batchTaskItemSummaryDTO : batchTaskItemSummaryDTOList) {
// actualValueList.set(batchTaskItemSummaryDTO.getHourIndex(),
// batchTaskItemSummaryDTO.getUnitTotalCount().doubleValue());
// }
// });
//
// return skuActualValueMap;
// }
//
// private Map<String, List<BatchTaskItemSummaryDTO>> aabbcc(String dateStr, Integer historyDays, List<BatchTaskItemBO>
// batchTaskItemBOList) {
// // 预测 7041 2025-06-07 每小时销售量
//
// /// String dateStr = "2025-06-07";
// LocalDate localDate = LocalDate.parse(dateStr);
// LocalDateTime localDateTime = localDate.atStartOfDay();
//
// // key = skuId_index
// Map<String, BatchTaskItemSummaryDTO> batchTaskItemSummaryBOMap = new HashMap<>();
// // 创建时间在同一个小时内的数量进行合并
// for (BatchTaskItemBO batchTaskItemBO : batchTaskItemBOList) {
// LocalDateTime taskItemCreateTime = batchTaskItemBO.getCreateTime().toInstant()
// .atZone(java.time.ZoneId.systemDefault())
// .toLocalDateTime().withMinute(0).withSecond(0).withNano(0);
// // 以"当前时间-30天"为起点，计算目标时间与起点的小时差，取模得到循环索引
// /// LocalDateTime now = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
// long hoursBetween = ChronoUnit.HOURS.between(localDateTime.minusDays(historyDays), taskItemCreateTime);
// int hourIndex = (int) (hoursBetween >= 0 ? hoursBetween % (historyDays * HOURS_PER_DAY) : 0);
//
// String key = batchTaskItemBO.getSkuId() + "_" + hourIndex;
// BatchTaskItemSummaryDTO batchTaskItemSummaryDTO = batchTaskItemSummaryBOMap.get(key);
// if (batchTaskItemSummaryDTO == null) {
// batchTaskItemSummaryDTO = new BatchTaskItemSummaryDTO();
// batchTaskItemSummaryDTO.setHourIndex(hourIndex);
// batchTaskItemSummaryDTO.setCreateTimeStr(batchTaskItemBO.getCreateTimeStr());
// batchTaskItemSummaryDTO.setWarehouseId(batchTaskItemBO.getWarehouseId());
// batchTaskItemSummaryDTO.setSkuId(batchTaskItemBO.getSkuId());
// batchTaskItemSummaryDTO.setProductName(batchTaskItemBO.getProductName());
// batchTaskItemSummaryDTO.setSpecName(batchTaskItemBO.getSpecName());
// batchTaskItemSummaryDTO.setSpecQuantity(batchTaskItemBO.getSpecQuantity());
//
// // 大单位数量
// batchTaskItemSummaryDTO.setPackageName(batchTaskItemBO.getPackageName());
// batchTaskItemSummaryDTO.setPackageCount(batchTaskItemBO.getPackageCount());
//
// // unitCount是除了大单位数量以外，剩余的小数量
// batchTaskItemSummaryDTO.setUnitName(batchTaskItemBO.getUnitName());
// batchTaskItemSummaryDTO.setUnitCount(batchTaskItemBO.getUnitCount());
//
// // unitTotalCount是将大单位也换算成小数量
// batchTaskItemSummaryDTO.setUnitTotalCount(batchTaskItemBO.getUnitTotalCount());
//
// } else {
// batchTaskItemSummaryDTO.setPackageCount(batchTaskItemSummaryDTO.getPackageCount().add(batchTaskItemBO.getPackageCount()));
// batchTaskItemSummaryDTO.setUnitCount(batchTaskItemSummaryDTO.getUnitCount().add(batchTaskItemBO.getUnitCount()));
// batchTaskItemSummaryDTO.setUnitTotalCount(batchTaskItemSummaryDTO.getUnitTotalCount().add(batchTaskItemBO.getUnitTotalCount()));
// }
//
//
// batchTaskItemSummaryBOMap.put(key, batchTaskItemSummaryDTO);
// }
//
//
// List<BatchTaskItemSummaryDTO> batchTaskItemSummaryBOList = new ArrayList<>();
// // 将batchTaskItemSummaryBOMap转换为batchTaskItemSummaryBOList
// batchTaskItemSummaryBOList.addAll(batchTaskItemSummaryBOMap.values());
//
// // key= skuId
// Map<String, List<BatchTaskItemSummaryDTO>> abc = new HashMap<>();
// // batchTaskItemSummaryBOList按照skuId分组, 里面按index排序
// abc = batchTaskItemSummaryBOList.stream().sorted((o1, o2) -> o1.getHourIndex() - o2.getHourIndex()).collect(
// Collectors.groupingBy(BatchTaskItemSummaryDTO::getSkuId));
//
// return abc;
// }
//
// @Test
// public void aaa() {
// String dateStr = "2025-06-07";
// // 实际值
// Map<String, List<Double>> getOneDayData = bbb(dateStr);
//
// List<Long> skuIds = getOneDayData.keySet().stream().map(Long::parseLong).collect(Collectors.toList());
//
// // 预测 7041 2025-06-07 每小时销售量
// /// 测试查询 warehouseId = 7041 数据
//
// DynamicReplenishmentQuery query = new DynamicReplenishmentQuery();
// query.setOrgId(704);
// query.setWarehouseId(7041);
// query.setDateStr(dateStr);
// query.setSkuIds(skuIds);
// List<BatchTaskItemBO> batchTaskItem30Days = batchTaskItemMapper.findBatchTaskItemThirtyDays(query);
//
// Map<String, List<BatchTaskItemSummaryDTO>> aabbcc = aabbcc(dateStr, 30, batchTaskItem30Days);
//
// InventoryReplenishmentSystem inventoryReplenishmentSystem = new InventoryReplenishmentSystem();
// inventoryReplenishmentSystem.addHistoricalData2(aabbcc);
//
// Map<String, List<Double>> skuHistory = inventoryReplenishmentSystem.getSkuHistory();
//
// // 生成未来24小时的补货建议（调用核心预测与计算逻辑）
// Map<String, ReplenishmentRecommendation> recommendations =
// inventoryReplenishmentSystem.generateReplenishmentRecommendations(24, null, null, null, null);
//
//
//
// recommendations.forEach((skuId, replenishmentRecommendation) -> {
// List<Double> actualValueList = getOneDayData.get(skuId);
//
// double[] data = actualValueList.stream().mapToDouble(Double::doubleValue).toArray();
//
// double totalActualDemand = 0;
// for (double demand : data) {
// totalActualDemand += Math.max(0, demand);
// }
//
// replenishmentRecommendation.setHourlyActual(data);
// replenishmentRecommendation.setActualDemand(totalActualDemand);
//
// // 与实际需求相差的百分比，精确到小数点后两位
// double differencePercentage = (replenishmentRecommendation.getActualDemand() -
// replenishmentRecommendation.getForecastDemand()) / totalActualDemand * 100;
// differencePercentage = Math.round(differencePercentage * 100) / 100.0;
// replenishmentRecommendation.setDifferencePercentage(differencePercentage + "%");
// });
//
//
// System.out.println(1);
// }
// }