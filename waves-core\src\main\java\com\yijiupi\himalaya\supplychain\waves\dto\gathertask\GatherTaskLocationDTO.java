/*
 * @ClassName GatherTaskLocationDTO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-10-31 10:56:55
 */
package com.yijiupi.himalaya.supplychain.waves.dto.gathertask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class GatherTaskLocationDTO implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = -2239606462328154287L;
    /**
     * @Fields id ID
     */
    private Long id;
    /**
     * @Fields orgId 城市id
     */
    private Integer orgId;
    /**
     * @Fields gatherTaskId 集货任务号ID
     */
    private Long gatherTaskId;
    /**
     * @Fields gatherTaskProductId 集货任务产品id
     */
    private Long gatherTaskProductId;
    /**
     * @Fields taskNumber 车次编号
     */
    private String taskNumber;
    /**
     * @Fields carId 车辆id
     */
    private Long carId;
    /**
     * @Fields carName 车辆名称
     */
    private String carName;
    /**
     * @Fields licensePlate 车牌号
     */
    private String licensePlate;
    /**
     * @Fields locationId 出库位Id
     */
    private Long locationId;
    /**
     * @Fields locationName 出库位名称
     */
    private String locationName;
    /**
     * @Fields gatherType 集货类型（0=按线路集货，1=按片区集货）
     */
    private Byte gatherType;
    /**
     * @Fields gatherTypeId 按线路集货存线路id，按片区集货存片区id
     */
    private Long gatherTypeId;
    /**
     * @Fields gatherTypeName 按线路集货存线路名，按片区集货存片区名
     */
    private String gatherTypeName;
    /**
     * @Fields driverName 司机名
     */
    private String driverName;
    /**
     * @Fields specQuantity 规格转换系数
     */
    private BigDecimal specQuantity;
    /**
     * @Fields totalCount 需集货数量
     */
    private BigDecimal totalCount;
    /**
     * @Fields gatherCount 已集货数量
     */
    private BigDecimal gatherCount;
    /**
     * @Fields status 状态 0=待集货 1=已集货
     */
    private Byte status;
    /**
     * @Fields createUser 创建人
     */
    private Long createUser;
    /**
     * @Fields createTime 注册日期
     */
    private Date createTime;
    /**
     * @Fields lastUpdateUser 最后更新人
     */
    private Long lastUpdateUser;
    /**
     * @Fields lastUpdateTime 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 获取 ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 城市id
     */
    public Integer getOrgId() {
        return orgId;
    }

    /**
     * 设置 城市id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 集货任务号ID
     */
    public Long getGatherTaskId() {
        return gatherTaskId;
    }

    /**
     * 设置 集货任务号ID
     */
    public void setGatherTaskId(Long gatherTaskId) {
        this.gatherTaskId = gatherTaskId;
    }

    /**
     * 获取 集货任务产品id
     */
    public Long getGatherTaskProductId() {
        return gatherTaskProductId;
    }

    /**
     * 设置 集货任务产品id
     */
    public void setGatherTaskProductId(Long gatherTaskProductId) {
        this.gatherTaskProductId = gatherTaskProductId;
    }

    /**
     * 获取 车次编号
     */
    public String getTaskNumber() {
        return taskNumber;
    }

    /**
     * 设置 车次编号
     */
    public void setTaskNumber(String taskNumber) {
        this.taskNumber = taskNumber == null ? null : taskNumber.trim();
    }

    /**
     * 获取 车辆id
     */
    public Long getCarId() {
        return carId;
    }

    /**
     * 设置 车辆id
     */
    public void setCarId(Long carId) {
        this.carId = carId;
    }

    /**
     * 获取 车辆名称
     */
    public String getCarName() {
        return carName;
    }

    /**
     * 设置 车辆名称
     */
    public void setCarName(String carName) {
        this.carName = carName == null ? null : carName.trim();
    }

    /**
     * 获取 车牌号
     */
    public String getLicensePlate() {
        return licensePlate;
    }

    /**
     * 设置 车牌号
     */
    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate == null ? null : licensePlate.trim();
    }

    /**
     * 获取 出库位Id
     */
    public Long getLocationId() {
        return locationId;
    }

    /**
     * 设置 出库位Id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 出库位名称
     */
    public String getLocationName() {
        return locationName;
    }

    /**
     * 设置 出库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName == null ? null : locationName.trim();
    }

    /**
     * 获取 集货类型（0=按线路集货，1=按片区集货）
     */
    public Byte getGatherType() {
        return gatherType;
    }

    /**
     * 设置 集货类型（0=按线路集货，1=按片区集货）
     */
    public void setGatherType(Byte gatherType) {
        this.gatherType = gatherType;
    }

    /**
     * 获取 按线路集货存线路id，按片区集货存片区id
     */
    public Long getGatherTypeId() {
        return gatherTypeId;
    }

    /**
     * 设置 按线路集货存线路id，按片区集货存片区id
     */
    public void setGatherTypeId(Long gatherTypeId) {
        this.gatherTypeId = gatherTypeId;
    }

    /**
     * 获取 按线路集货存线路名，按片区集货存片区名
     */
    public String getGatherTypeName() {
        return gatherTypeName;
    }

    /**
     * 设置 按线路集货存线路名，按片区集货存片区名
     */
    public void setGatherTypeName(String gatherTypeName) {
        this.gatherTypeName = gatherTypeName == null ? null : gatherTypeName.trim();
    }

    /**
     * 获取 司机名
     */
    public String getDriverName() {
        return driverName;
    }

    /**
     * 设置 司机名
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    /**
     * 获取 规格转换系数
     */
    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    /**
     * 设置 规格转换系数
     */
    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    /**
     * 获取 需集货数量
     */
    public BigDecimal getTotalCount() {
        return totalCount;
    }

    /**
     * 设置 需集货数量
     */
    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 获取 已集货数量
     */
    public BigDecimal getGatherCount() {
        return gatherCount;
    }

    /**
     * 设置 已集货数量
     */
    public void setGatherCount(BigDecimal gatherCount) {
        this.gatherCount = gatherCount;
    }

    /**
     * 获取 状态 0=待集货 1=已集货
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置 状态 0=待集货 1=已集货
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取 创建人
     */
    public Long getCreateUser() {
        return createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 注册日期
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 注册日期
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后更新人
     */
    public Long getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置 最后更新人
     */
    public void setLastUpdateUser(Long lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    /**
     * 获取 最后更新时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置 最后更新时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
}