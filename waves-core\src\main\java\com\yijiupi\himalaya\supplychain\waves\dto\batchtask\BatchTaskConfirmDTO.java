package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * 确认拣货
 *
 * <AUTHOR>
 * @date 2020-03-12 17:05
 */
public class BatchTaskConfirmDTO implements Serializable {

    private static final long serialVersionUID = 3844823716009947468L;

    /**
     * 城市ID
     */
    private Integer orgId;

    /**
     * 仓库Id
     */
    private Integer warehouseId;

    /**
     * 波次任务编号
     */
    private List<String> batchTaskNo;

    /**
     * 拣货任务详情列表
     */
    private List<BatchTaskItemDTO> batchTaskItemList;

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 操作人id
     */
    private Integer operateUserId;

    /**
     * 存放货位Id
     */
    private Long locationId;

    /**
     * 存放货位名称
     */
    private String locationName;

    /**
     * 分拣员id
     */
    private Integer sorterId;

    /**
     * 分拣员名称
     */
    private String sorterName;
    /**
     * 拣货任务id列表
     */
    private List<String> batchTaskIds;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<String> getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(List<String> batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    public List<BatchTaskItemDTO> getBatchTaskItemList() {
        return batchTaskItemList;
    }

    public void setBatchTaskItemList(List<BatchTaskItemDTO> batchTaskItemList) {
        this.batchTaskItemList = batchTaskItemList;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public Integer getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(Integer operateUserId) {
        this.operateUserId = operateUserId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Integer getSorterId() {
        return sorterId;
    }

    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    public String getSorterName() {
        return sorterName;
    }

    public void setSorterName(String sorterName) {
        this.sorterName = sorterName;
    }

    /**
     * 获取 拣货任务id列表
     *
     * @return batchTaskIds 拣货任务id列表
     */
    public List<String> getBatchTaskIds() {
        return this.batchTaskIds;
    }

    /**
     * 设置 拣货任务id列表
     *
     * @param batchTaskIds 拣货任务id列表
     */
    public void setBatchTaskIds(List<String> batchTaskIds) {
        this.batchTaskIds = batchTaskIds;
    }
}
