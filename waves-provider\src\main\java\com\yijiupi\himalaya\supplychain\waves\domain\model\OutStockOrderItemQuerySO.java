package com.yijiupi.himalaya.supplychain.waves.domain.model;

import java.util.List;

import com.yijiupi.himalaya.base.search.PageCondition;

public class OutStockOrderItemQuerySO extends PageCondition {
    /**
     * 城市id
     */
    private Integer orgId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 订单项ids
     */
    private List<Long> ids;
    /**
     * 订单项ids
     */
    private List<String> businessItemIds;
    /**
     * 订单号列表
     */
    private List<String> refOrderNoList;
    /**
     * 波次创建的状态 0=未创建
     */
    private Byte batchCreateState;

    public Byte getBatchCreateState() {
        return batchCreateState;
    }

    public void setBatchCreateState(Byte batchCreateState) {
        this.batchCreateState = batchCreateState;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<String> getRefOrderNoList() {
        return refOrderNoList;
    }

    public void setRefOrderNoList(List<String> refOrderNoList) {
        this.refOrderNoList = refOrderNoList;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public List<String> getBusinessItemIds() {
        return businessItemIds;
    }

    public void setBusinessItemIds(List<String> businessItemIds) {
        this.businessItemIds = businessItemIds;
    }
}
