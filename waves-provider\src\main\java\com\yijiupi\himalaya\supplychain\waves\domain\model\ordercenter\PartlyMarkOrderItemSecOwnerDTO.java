package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单重算数据明细
 *
 * <AUTHOR>
 * @Date 2023/8/8
 */
public class PartlyMarkOrderItemSecOwnerDTO implements Serializable {

    private static final long serialVersionUID = 8975411113800284768L;

    /**
     * 订单明细Id
     */
    private Long orderItemId;

    /**
     * 标记实际数量（小单位）
     */
    private BigDecimal unitCount;

    /**
     * 二级货主id
     */
    private Long SecOwnerId;

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public Long getSecOwnerId() {
        return SecOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        SecOwnerId = secOwnerId;
    }
}
