package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/3
 */
public class DeleteBatchNoDTO implements Serializable {
    /**
     * 波次号
     */
    private String batchNo;

    public DeleteBatchNoDTO() {}

    public DeleteBatchNoDTO(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * 获取 波次号
     *
     * @return batchNo 波次号
     */
    public String getBatchNo() {
        return this.batchNo;
    }

    /**
     * 设置 波次号
     *
     * @param batchNo 波次号
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }
}
