package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

public class OutStockOrderItemDTO implements Serializable {
    /**
     * 订单项id
     */
    private Long id;

    /**
     * 城市ID，分库用（第三方订单使用一个新的自定义CityId）
     */
    private Integer org_id;

    /**
     * 订单编号
     */
    private String refOrderNo;

    /**
     * 波次任务编号
     */
    private String batchTaskNo;

    /**
     * 波次任务Id
     */
    private String batchtask_Id;

    /**
     * 关联出库单表id
     */
    private Long outstockorder_Id;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 产品SKUid
     */
    private Long skuId;

    /**
     * 品牌
     */
    private String productBrand;

    /**
     * 类目
     */
    private String categoryName;

    /**
     * 包装规格
     */
    private String specName;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal specQuantity;

    /**
     * 销售规格名称
     */
    private String saleSpec;

    /**
     * 销售规格系数
     */
    private BigDecimal saleSpecQuantity;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 大单位数量
     */
    private BigDecimal packageCount;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 小单位数量
     */
    private BigDecimal unitCount;

    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 销售模式 代营(0),自营(1),合作(2),寄售(3),大商转自营(4),大商转配送(5),入驻(6),总部寄售(7),独家包销(8)
     */
    private Byte saleModel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 销售单位
     */
    private String sellUnit;

    /**
     * 销售数量
     */
    private BigDecimal saleCount;

    /**
     * 产品总金额
     */
    private BigDecimal totalAmount;

    /**
     * 应付金额
     */
    private BigDecimal payAmount;

    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Byte source;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private String sourceText;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Byte channel;

    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private String channelText;

    /**
     * 创建人
     */
    private String createuser;

    /**
     * 创建时间
     */
    private Date createtime;

    /**
     * 更新人
     */
    private String lastupdateuser;

    /**
     * 更新时间
     */
    private Date lastupdatetime;

    /**
     * 货位或货区Id
     */
    private Long locationId;

    /**
     * 货位或货区名称
     */
    private String locationName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date batchTime;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 收货人姓名
     */
    private String userName;

    /**
     * 订单序号
     */
    private Integer orderSequence;

    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte taskState;
    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private String taskStateText;
    /**
     * 所属人(货主)id
     */
    private Long ownerId;

    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    /**
     * 产品规格ID
     */
    private Long productSpecificationId;

    /**
     * 播种订单序号
     */
    private Integer sowOrderSequence;

    /**
     * 播种订单id
     */
    private Long sowOrderId;

    /**
     * 波次编号
     */
    private String batchno;

    /**
     * 波次Id
     */
    private String batchId;

    /**
     * 拣货任务详情id
     */
    private String batchTaskItemId;

    /**
     * 容器货位id
     */
    private Long containerLocationId;

    /**
     * 容器货位名称
     */
    private String containerLocationName;

    /**
     * 箱码集合
     *
     * @return
     */
    private List<String> packageCode;

    /**
     * 瓶码集合
     *
     * @return
     */
    private List<String> unitCode;

    /**
     * 出库位Id
     */
    private Long toLocationId;

    /**
     * 出库位名称
     */
    private String toLocationName;

    /**
     * 分拣员(姓名)
     */
    private String sorter;

    /**
     * 分拣员id
     */
    private Integer sorterId;

    /**
     * 下单地址区域
     */
    private String county;
    /**
     * 下单地址城市
     */
    private String city;

    /**
     * 播种任务明细id
     */
    private Long sowTaskItemId;

    /**
     * 播种任务明细状态
     */
    private Byte sowTaskItemState;

    /**
     * 目标仓库名称
     */
    private String toWarehouseName;

    /**
     * 目标城市名称
     */
    private String toCity;

    /**
     * 产品默认图片
     */
    private String defaultImageFile;

    /**
     * 产品图片
     */
    private List<String> imageFiles;

    /**
     * 业务项ID
     */
    private String businessItemId;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 内配类型: 8.内配，9.内配退，11.中转 12.中转退
     */
    private Byte allotType;

    /**
     * 是否需要生成内配: 0.否 1.是
     */
    private Byte createAllocation;

    private Byte outBoundType;
    /**
     * 是否是促销产品
     *
     * @see com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum
     */
    private Byte IsAdvent;

    public Byte getAllotType() {
        return allotType;
    }

    public void setAllotType(Byte allotType) {
        this.allotType = allotType;
    }

    /**
     * 订单明细项
     */
    private List<OutStockOrderItemDetailDTO> outStockOrderItemDetailDTOS;

    /**
     * 配送方式: 7.快递
     */
    private Byte deliveryMode;

    /**
     * 长
     */
    private Double length;

    /**
     * 宽
     */
    private Double width;

    /**
     * 高
     */
    private Double height;

    /**
     * 产品特征：1:大件,2:小件
     */
    private Byte productFeature;

    /**
     * 出库单类型
     */
    private Byte orderType;
    /**
     * 原始小单位数量
     */
    private BigDecimal originalUnitTotalCount;

    /**
     * 货位顺序
     */
    private Integer locationSequence;

    /**
     * 播种领取员
     */
    private Integer sowTaskItemSorterId;

    /**
     * 领取状态：0:待领,1:已领,2:已播种,3:不可领
     */
    private Byte receiveState;
    /**
     * 线路名称
     */
    private String routeName;
    /**
     * 线路id
     */
    private Long routeId;

    /**
     * 片区名称
     */
    private String areaName;
    /**
     * 片区id
     */
    private Long areaId;

    /**
     * 出库单来源 {@link com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.SourceType}
     */
    private Byte outStockOrderSource;

    /**
     * 销售库存
     */
    private BigDecimal saleInventory;

    /**
     * 控货策略ID
     */
    private Long controlConfigId;
    /**
     * 控货策略名称
     */
    private String controlConfigName;
    /**
     * 溯源码个数
     */
    private BigDecimal sourceCodeCount;

    /**
     * 多箱号
     */
    private List<String> boxCodeList;

    /**
     * 包装箱类型
     */
    private Byte packageType;

    /**
     * 二级仓出库位Id
     */
    private Long secondLocationId;

    /**
     * 二级仓出库位名称
     */
    private String secondLocationName;

    /**
     * 二级仓库名称
     */
    private String secondWarehouseName;

    /**
     * 业务类型
     */
    private Byte businessType;
    /**
     * 地址id
     */
    private Integer addressId;

    public BigDecimal getOriginalUnitTotalCount() {
        return originalUnitTotalCount;
    }

    public void setOriginalUnitTotalCount(BigDecimal originalUnitTotalCount) {
        this.originalUnitTotalCount = originalUnitTotalCount;
    }

    public String getBatchTaskItemId() {
        return batchTaskItemId;
    }

    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg_id() {
        return org_id;
    }

    public void setOrg_id(Integer org_id) {
        this.org_id = org_id;
    }

    public String getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo == null ? null : batchTaskNo.trim();
    }

    public String getBatchtask_Id() {
        return batchtask_Id;
    }

    public void setBatchtask_Id(String batchtask_Id) {
        this.batchtask_Id = batchtask_Id == null ? null : batchtask_Id.trim();
    }

    public Long getOutstockorder_Id() {
        return outstockorder_Id;
    }

    public void setOutstockorder_Id(Long outstockorder_Id) {
        this.outstockorder_Id = outstockorder_Id;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand == null ? null : productBrand.trim();
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName == null ? null : categoryName.trim();
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName == null ? null : specName.trim();
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public String getSaleSpec() {
        return saleSpec;
    }

    public void setSaleSpec(String saleSpec) {
        this.saleSpec = saleSpec == null ? null : saleSpec.trim();
    }

    public BigDecimal getSaleSpecQuantity() {
        return saleSpecQuantity;
    }

    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName == null ? null : packageName.trim();
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Byte getSaleModel() {
        return saleModel;
    }

    public void setSaleModel(Byte saleModel) {
        this.saleModel = saleModel;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getSellUnit() {
        return sellUnit;
    }

    public void setSellUnit(String sellUnit) {
        this.sellUnit = sellUnit == null ? null : sellUnit.trim();
    }

    public BigDecimal getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(BigDecimal saleCount) {
        this.saleCount = saleCount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public Byte getChannel() {
        return channel;
    }

    public void setChannel(Byte channel) {
        this.channel = channel;
    }

    public String getCreateuser() {
        return createuser;
    }

    public void setCreateuser(String createuser) {
        this.createuser = createuser == null ? null : createuser.trim();
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getLastupdateuser() {
        return lastupdateuser;
    }

    public void setLastupdateuser(String lastupdateuser) {
        this.lastupdateuser = lastupdateuser == null ? null : lastupdateuser.trim();
    }

    public Date getLastupdatetime() {
        return lastupdatetime;
    }

    public void setLastupdatetime(Date lastupdatetime) {
        this.lastupdatetime = lastupdatetime;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getOrderSequence() {
        return orderSequence;
    }

    public void setOrderSequence(Integer orderSequence) {
        this.orderSequence = orderSequence;
    }

    public Byte getTaskState() {
        return taskState;
    }

    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Integer getSowOrderSequence() {
        return sowOrderSequence;
    }

    public void setSowOrderSequence(Integer sowOrderSequence) {
        this.sowOrderSequence = sowOrderSequence;
    }

    public Long getSowOrderId() {
        return sowOrderId;
    }

    public void setSowOrderId(Long sowOrderId) {
        this.sowOrderId = sowOrderId;
    }

    public Long getContainerLocationId() {
        return containerLocationId;
    }

    public void setContainerLocationId(Long containerLocationId) {
        this.containerLocationId = containerLocationId;
    }

    public String getContainerLocationName() {
        return containerLocationName;
    }

    public void setContainerLocationName(String containerLocationName) {
        this.containerLocationName = containerLocationName;
    }

    public List<String> getPackageCode() {
        return packageCode;
    }

    public void setPackageCode(List<String> packageCode) {
        this.packageCode = packageCode;
    }

    public List<String> getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(List<String> unitCode) {
        this.unitCode = unitCode;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public String getSorter() {
        return sorter;
    }

    public void setSorter(String sorter) {
        this.sorter = sorter;
    }

    public Integer getSorterId() {
        return sorterId;
    }

    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    /**
     * @return the channelText
     */
    public String getChannelText() {
        return channelText;
    }

    /**
     * @param channelText the channelText to set
     */
    public void setChannelText(String channelText) {
        this.channelText = channelText;
    }

    /**
     * @return the sourceText
     */
    public String getSourceText() {
        return sourceText;
    }

    /**
     * @param sourceText the sourceText to set
     */
    public void setSourceText(String sourceText) {
        this.sourceText = sourceText;
    }

    /**
     * @return the taskStateText
     */
    public String getTaskStateText() {
        return taskStateText;
    }

    /**
     * @param taskStateText the taskStateText to set
     */
    public void setTaskStateText(String taskStateText) {
        this.taskStateText = taskStateText;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public Long getSowTaskItemId() {
        return sowTaskItemId;
    }

    public void setSowTaskItemId(Long sowTaskItemId) {
        this.sowTaskItemId = sowTaskItemId;
    }

    public Byte getSowTaskItemState() {
        return sowTaskItemState;
    }

    public void setSowTaskItemState(Byte sowTaskItemState) {
        this.sowTaskItemState = sowTaskItemState;
    }

    public String getToWarehouseName() {
        return toWarehouseName;
    }

    public void setToWarehouseName(String toWarehouseName) {
        this.toWarehouseName = toWarehouseName;
    }

    public String getToCity() {
        return toCity;
    }

    public void setToCity(String toCity) {
        this.toCity = toCity;
    }

    public String getDefaultImageFile() {
        return defaultImageFile;
    }

    public void setDefaultImageFile(String defaultImageFile) {
        this.defaultImageFile = defaultImageFile;
    }

    public List<String> getImageFiles() {
        return imageFiles;
    }

    public void setImageFiles(List<String> imageFiles) {
        this.imageFiles = imageFiles;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getBusinessItemId() {
        return businessItemId;
    }

    public void setBusinessItemId(String businessItemId) {
        this.businessItemId = businessItemId;
    }

    public List<OutStockOrderItemDetailDTO> getOutStockOrderItemDetailDTOS() {
        return outStockOrderItemDetailDTOS;
    }

    public void setOutStockOrderItemDetailDTOS(List<OutStockOrderItemDetailDTO> outStockOrderItemDetailDTOS) {
        this.outStockOrderItemDetailDTOS = outStockOrderItemDetailDTOS;
    }

    public Byte getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(Byte deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    public Byte getCreateAllocation() {
        return createAllocation;
    }

    public void setCreateAllocation(Byte createAllocation) {
        this.createAllocation = createAllocation;
    }

    public Double getLength() {
        return length;
    }

    public void setLength(Double length) {
        this.length = length;
    }

    public Double getWidth() {
        return width;
    }

    public void setWidth(Double width) {
        this.width = width;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public Byte getProductFeature() {
        return productFeature;
    }

    public void setProductFeature(Byte productFeature) {
        this.productFeature = productFeature;
    }

    public Byte getOrderType() {
        return orderType;
    }

    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }

    public Integer getLocationSequence() {
        return locationSequence;
    }

    public void setLocationSequence(Integer locationSequence) {
        this.locationSequence = locationSequence;
    }

    public Integer getSowTaskItemSorterId() {
        return sowTaskItemSorterId;
    }

    public void setSowTaskItemSorterId(Integer sowTaskItemSorterId) {
        this.sowTaskItemSorterId = sowTaskItemSorterId;
    }

    public Byte getReceiveState() {
        return receiveState;
    }

    public void setReceiveState(Byte receiveState) {
        this.receiveState = receiveState;
    }

    /**
     * 获取 线路名称
     *
     * @return routeName 线路名称
     */
    public String getRouteName() {
        return this.routeName;
    }

    /**
     * 设置 线路名称
     *
     * @param routeName 线路名称
     */
    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    /**
     * 获取 线路id
     *
     * @return routeId 线路id
     */
    public Long getRouteId() {
        return this.routeId;
    }

    /**
     * 设置 线路id
     *
     * @param routeId 线路id
     */
    public void setRouteId(Long routeId) {
        this.routeId = routeId;
    }

    /**
     * 获取
     *
     * @return outBoundType
     */
    public Byte getOutBoundType() {
        return this.outBoundType;
    }

    /**
     * 设置
     *
     * @param outBoundType
     */
    public void setOutBoundType(Byte outBoundType) {
        this.outBoundType = outBoundType;
    }

    /**
     * 获取 下单地址城市
     *
     * @return city 下单地址城市
     */
    public String getCity() {
        return this.city;
    }

    /**
     * 设置 下单地址城市
     *
     * @param city 下单地址城市
     */
    public void setCity(String city) {
        this.city = city;
    }

    public Byte getOutStockOrderSource() {
        return outStockOrderSource;
    }

    public void setOutStockOrderSource(Byte outStockOrderSource) {
        this.outStockOrderSource = outStockOrderSource;
    }

    public BigDecimal getSaleInventory() {
        return saleInventory;
    }

    public void setSaleInventory(BigDecimal saleInventory) {
        this.saleInventory = saleInventory;
    }

    public Long getControlConfigId() {
        return controlConfigId;
    }

    public void setControlConfigId(Long controlConfigId) {
        this.controlConfigId = controlConfigId;
    }

    public String getControlConfigName() {
        return controlConfigName;
    }

    public void setControlConfigName(String controlConfigName) {
        this.controlConfigName = controlConfigName;
    }

    public BigDecimal getSourceCodeCount() {
        return sourceCodeCount;
    }

    public void setSourceCodeCount(BigDecimal sourceCodeCount) {
        this.sourceCodeCount = sourceCodeCount;
    }

    public List<String> getBoxCodeList() {
        return boxCodeList;
    }

    public void setBoxCodeList(List<String> boxCodeList) {
        this.boxCodeList = boxCodeList;
    }

    public Byte getPackageType() {
        return packageType;
    }

    public void setPackageType(Byte packageType) {
        this.packageType = packageType;
    }

    public Long getSecondLocationId() {
        return secondLocationId;
    }

    public void setSecondLocationId(Long secondLocationId) {
        this.secondLocationId = secondLocationId;
    }

    public String getSecondLocationName() {
        return secondLocationName;
    }

    public void setSecondLocationName(String secondLocationName) {
        this.secondLocationName = secondLocationName;
    }

    public String getSecondWarehouseName() {
        return secondWarehouseName;
    }

    public void setSecondWarehouseName(String secondWarehouseName) {
        this.secondWarehouseName = secondWarehouseName;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    /**
     * 获取 片区名称
     *
     * @return areaName 片区名称
     */
    public String getAreaName() {
        return this.areaName;
    }

    /**
     * 设置 片区名称
     *
     * @param areaName 片区名称
     */
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    /**
     * 获取 片区id
     *
     * @return areaId 片区id
     */
    public Long getAreaId() {
        return this.areaId;
    }

    /**
     * 设置 片区id
     *
     * @param areaId 片区id
     */
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    /**
     * 获取 地址id
     *
     * @return addressId 地址id
     */
    public Integer getAddressId() {
        return this.addressId;
    }

    /**
     * 设置 地址id
     *
     * @param addressId 地址id
     */
    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }


    /**
     * 获取 是否是促销产品           @see com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum
     *
     * @return IsAdvent 是否是促销产品           @see com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum
     */
    public Byte getIsAdvent() {
        return this.IsAdvent;
    }

    /**
     * 设置 是否是促销产品           @see com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum
     *
     * @param IsAdvent 是否是促销产品           @see com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum
     */
    public void setIsAdvent(Byte IsAdvent) {
        this.IsAdvent = IsAdvent;
    }
}