package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;
import java.math.BigDecimal;

public class SowTaskUpdateDTO implements Serializable {

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 订单数量
     */
    private Integer orderCount;

    /**
     * 商品种类
     */
    private Integer skuCount;

    /**
     * 大件总数
     */
    private BigDecimal packageAmount;

    /**
     * 小件总数
     */
    private BigDecimal unitAmount;

    /**
     * 播种任务状态
     */
    private Byte state;

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public BigDecimal getPackageAmount() {
        return packageAmount;
    }

    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    public BigDecimal getUnitAmount() {
        return unitAmount;
    }

    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }
}
