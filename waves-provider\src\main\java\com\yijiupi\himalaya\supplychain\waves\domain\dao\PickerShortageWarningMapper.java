package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationConfigDTO;
import com.yijiupi.himalaya.supplychain.waves.schedule.model.PickerEfficiencyBO;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-11-27 14:18
 **/
@Mapper
public interface PickerShortageWarningMapper {

    /**
     * 按特征、状态统计出库单数量
     *
     * @param feature    特征
     * @param countState 状态
     * @param config     分仓配置
     * @return 出库单数量
     */
    int countOrderByFeature(int feature, Set<Byte> countState, WarehouseAllocationConfigDTO config);

    /**
     * 根据特征统计最近 days 天每小时平均下单量
     *
     * @param feature 特征
     * @param days    天数
     * @param config  分仓配置
     * @return 每小时平均下单量
     */
    int countAvgOrderByFeatureAndTime(int feature, int days, WarehouseAllocationConfigDTO config);

    /**
     * 按件数统计酒饮待拣货的产品
     *
     * @param config 分仓配置
     * @return 酒饮待拣货件数
     */
    BigDecimal countOrderProductByPackage(WarehouseAllocationConfigDTO config);

    /**
     * 按行数统计休食待拣货的产品
     *
     * @param config 分仓配置
     * @return 休食待拣货行数
     */
    BigDecimal countOrderProductByLine(WarehouseAllocationConfigDTO config);

    /**
     * 前 30 天平均每单件数
     *
     * @param config 分仓配置
     * @return 酒饮件数
     */
    BigDecimal countPrevAvgProductCountByPackage(WarehouseAllocationConfigDTO config);

    /**
     * 前 30 天平均每单行数
     *
     * @param config 分仓配置
     * @return 休食行数
     */
    BigDecimal countPrevAvgProductCountByLine(WarehouseAllocationConfigDTO config);

    /**
     * 统计指定仓库的休食人效, 还需要再除以上班时长
     *
     * @param config 分仓配置
     * @return 休食人效
     */
    List<PickerEfficiencyBO> countRestEfficiency(WarehouseAllocationConfigDTO config);

    /**
     * 统计指定仓库的酒饮人效, 还需要再除以上班时长
     *
     * @param config 分仓配置
     * @return 酒饮人效
     */
    List<PickerEfficiencyBO> countDrinkingEfficiency(WarehouseAllocationConfigDTO config);
}
