package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/3/18
 */
public class OrderItemTaskInfoDTO implements Serializable {
    private static final long serialVersionUID = -5000047553518368033L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 城市ID
     */
    private Integer orgId;

    /**
     * 订单项id
     */
    private Long refOrderItemId;

    /**
     * 订单id
     */
    private Long refOrderId;

    /**
     * 拣货任务项id
     */
    private String batchTaskItemId;

    /**
     * 拣货任务id
     */
    private String batchTaskId;

    /**
     * 拣货任务编号
     */
    private String batchTaskNo;

    /**
     * 波次id
     */
    private String batchId;

    /**
     * 波次编号
     */
    private String batchNo;

    /**
     * 分配变更小单位总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 已拣货数量
     */
    private BigDecimal overSortCount;

    /**
     * 缺货数量
     */
    private BigDecimal lackUnitCount;

    /**
     * 移库数量
     */
    private BigDecimal moveCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 最后修改人
     */
    private String lastUpdateUser;

    /**
     * 拣货任务项状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte batchTaskItemState;

    /**
     * 订单号
     */
    private String refOrderNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getRefOrderItemId() {
        return refOrderItemId;
    }

    public void setRefOrderItemId(Long refOrderItemId) {
        this.refOrderItemId = refOrderItemId;
    }

    public Long getRefOrderId() {
        return refOrderId;
    }

    public void setRefOrderId(Long refOrderId) {
        this.refOrderId = refOrderId;
    }

    public String getBatchTaskItemId() {
        return batchTaskItemId;
    }

    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public String getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public BigDecimal getOverSortCount() {
        return overSortCount;
    }

    public void setOverSortCount(BigDecimal overSortCount) {
        this.overSortCount = overSortCount;
    }

    public BigDecimal getLackUnitCount() {
        return lackUnitCount;
    }

    public void setLackUnitCount(BigDecimal lackUnitCount) {
        this.lackUnitCount = lackUnitCount;
    }

    public BigDecimal getMoveCount() {
        return moveCount;
    }

    public void setMoveCount(BigDecimal moveCount) {
        this.moveCount = moveCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Byte getBatchTaskItemState() {
        return batchTaskItemState;
    }

    public void setBatchTaskItemState(Byte batchTaskItemState) {
        this.batchTaskItemState = batchTaskItemState;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }
}
