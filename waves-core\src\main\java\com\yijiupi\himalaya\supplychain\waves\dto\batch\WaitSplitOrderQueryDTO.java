package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import com.yijiupi.himalaya.base.search.PagerCondition;

/**
 * <AUTHOR>
 * @title: WaitSplitOrderQueryDTO
 * @description:
 * @date 2022-09-30 11:56
 */
public class WaitSplitOrderQueryDTO extends PagerCondition {
    /**
     * 波次任务编号
     */
    private String batchTaskNo;

    /**
     * 获取 波次任务编号
     *
     * @return batchTaskNo 波次任务编号
     */
    public String getBatchTaskNo() {
        return this.batchTaskNo;
    }

    /**
     * 设置 波次任务编号
     *
     * @param batchTaskNo 波次任务编号
     */
    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }
}
