package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.order.OrderItemDTO;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSowDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.OrderItemTaskInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.util.ConstantUtil;

/**
 * <AUTHOR>
 * @Title: himalaya-supplychain-microservice-waves
 * @Package com.yijiupi.himalaya.supplychain.waves.domain.convertor
 * @Description:
 * @date 2018/3/26 14:14
 */
public class BatchTaskConvertor {

    public static List<BatchTaskItemPO> convertToListBatchTaskItemPO(List<OutStockOrderPO> lstOrders,
                                                                     List<BatchTaskPO> lstBatchTask, List<BatchTaskItemDTO> batchTaskItemDTOS) {
        List<BatchTaskItemPO> batchTaskItemPOList = batchTaskItemDTOS.stream().map(input -> {
            BatchTaskItemPO batchTaskItemPO = new BatchTaskItemPO();
            BeanUtils.copyProperties(input, batchTaskItemPO);
            batchTaskItemPO.setOrderItemId(input.getOrderItemId());
            if (!StringUtils.isEmpty(batchTaskItemPO.getRefOrderNo())) {
                // 如果是按订单拣货，给排序赋值
                if (lstBatchTask.stream().anyMatch(p -> p.getId().equals(batchTaskItemPO.getBatchTaskId())
                        && WavesStrategyConstants.PICKINGTYPE_ORDER == p.getPickingType().intValue())) {
                    if (lstOrders.stream().anyMatch(q -> q.getReforderno().equals(batchTaskItemPO.getRefOrderNo()))) {
                        OutStockOrderPO order = lstOrders.stream()
                                .filter(q -> q.getReforderno().equals(batchTaskItemPO.getRefOrderNo())).findAny().get();
                        Integer index = lstOrders.indexOf(order);
                        batchTaskItemPO.setOrderSquence(index);
                    }
                }
            }
            return batchTaskItemPO;
        }).collect(Collectors.toList());
        return batchTaskItemPOList;
    }

    public static List<BatchTaskPO> convertToListBatchTaskPO(List<BatchTaskDTO> BatchTaskDTOS) {
        List<BatchTaskPO> batchTaskPOS = BatchTaskDTOS.stream().map(input -> {
            BatchTaskPO batchTaskItemPO = new BatchTaskPO();
            BeanUtils.copyProperties(input, batchTaskItemPO);
            batchTaskItemPO.setSorter(ConstantUtil.getMaxLen(batchTaskItemPO.getSorter(), 50));
            return batchTaskItemPO;
        }).collect(Collectors.toList());
        return batchTaskPOS;
    }

    /**
     * @param
     * @return
     * @Description: 根据订单项生成查询货区货位的查询条件
     * <AUTHOR>
     * @date 2018/4/2 16:48
     */
    public static List<BatchLocationInfoQueryDTO> getBatchLocationInfoDTO(List<OutStockOrderItemPO> lstOrderItems,
                                                                          Integer warehouseId) {

        List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOList = lstOrderItems.stream().map(input -> {
            BatchLocationInfoQueryDTO batchInventoryQueryDTO = new BatchLocationInfoQueryDTO();
            batchInventoryQueryDTO.setProductSkuId(input.getSkuid());
            batchInventoryQueryDTO.setWarehouseId(warehouseId);
            batchInventoryQueryDTO.setChannel(0);
            batchInventoryQueryDTO.setSource(0);
            return batchInventoryQueryDTO;
        }).collect(Collectors.toList());
        return batchLocationInfoQueryDTOList;
    }

    public static List<OrderItemDTO> getOrderItemDTO(List<OutStockOrderItemPO> lstOrderItems, Integer warehouseId) {
        List<OrderItemDTO> orderItemDTOS = new ArrayList<>();
        lstOrderItems.forEach(input -> {
            // 按订单项详细去分配货位
            if (CollectionUtils.isNotEmpty(input.getItemDetails())) {
                input.getItemDetails().forEach(detail -> {
                    OrderItemDTO orderItemDTO = new OrderItemDTO();
                    orderItemDTO.setId(input.getId());
                    orderItemDTO.setSpecQuantity(input.getSpecquantity());
                    orderItemDTO.setProductSkuId(input.getSkuid());
                    orderItemDTO.setWarehouseId(warehouseId);
                    orderItemDTO.setChannel(Integer.valueOf(input.getChannel()));
                    orderItemDTO.setSource(Integer.valueOf(input.getSource()));
                    orderItemDTO.setUnitTotalCount(detail.getUnitTotalCount());
                    orderItemDTO.setOwnerId(detail.getOwnerId());
                    orderItemDTO.setSecOwnerId(detail.getSecOwnerId());
                    orderItemDTO.setItemDetailId(detail.getId());
                    orderItemDTOS.add(orderItemDTO);
                });
            } else {
                OrderItemDTO orderItemDTO = new OrderItemDTO();
                orderItemDTO.setId(input.getId());
                orderItemDTO.setSpecQuantity(input.getSpecquantity());
                orderItemDTO.setProductSkuId(input.getSkuid());
                orderItemDTO.setWarehouseId(warehouseId);
                orderItemDTO.setChannel(Integer.valueOf(input.getChannel()));
                orderItemDTO.setSource(Integer.valueOf(input.getSource()));
                orderItemDTO.setUnitTotalCount(input.getUnittotalcount());
                orderItemDTO.setOwnerId(input.getOwnerId());
                orderItemDTO.setSecOwnerId(input.getSecOwnerId());
                orderItemDTOS.add(orderItemDTO);
            }
        });
        return orderItemDTOS;
    }

    public static BatchTaskSowDTO convertToBatchTaskSowDTO(List<BatchTaskPO> batchTasks) {
        BatchTaskSowDTO batchTaskSowDTO = new BatchTaskSowDTO();
        if (CollectionUtils.isNotEmpty(batchTasks)) {
            BatchTaskPO batchTaskPO = batchTasks.get(0);
            batchTaskSowDTO.setId(batchTaskPO.getId());
            batchTaskSowDTO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
            batchTaskSowDTO.setBatchTaskName(batchTaskPO.getBatchTaskName());
            batchTaskSowDTO.setSowLocationId(batchTaskPO.getSowLocationId());
            batchTaskSowDTO.setSowLocationName(batchTaskPO.getSowLocationName());

            HashMap<String, Integer> orderSequenceMap = new HashMap<>();
            batchTasks.forEach(batchTask -> {
                Integer sowOrderSequence = batchTask.getSowOrderSequence();
                String refOrderNo = batchTask.getRefOrderNo();
                if (sowOrderSequence != null && refOrderNo != null) {
                    orderSequenceMap.put(refOrderNo, sowOrderSequence);
                }
            });
            batchTaskSowDTO.setOrderSequenceMap(orderSequenceMap);
        }
        return batchTaskSowDTO;
    }

    public static List<BatchTaskDTO> convertToBatchTaskDTO(List<BatchTaskPO> batchTasks) {
        List<BatchTaskDTO> batchTaskDTOS = new ArrayList<>();
        batchTasks.forEach(batchTaskPO -> {
            BatchTaskDTO batchTaskDTO = new BatchTaskDTO();
            BeanUtils.copyProperties(batchTaskPO, batchTaskDTO);
            batchTaskDTOS.add(batchTaskDTO);
        });
        return batchTaskDTOS;
    }

    /**
     * Boolean转化成对应的Byte
     *
     * @param bool
     * @return
     */
    public static Boolean convertToBoolean(Byte b) {
        if (null == b) {
            return null;
        }
        return b == 1 ? true : false;
    }

    public static List<OrderItemTaskInfoDTO> orderItemTaskInfoPOToDTO(List<OrderItemTaskInfoPO> poList) {
        List<OrderItemTaskInfoDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(poList)) {
            return dtoList;
        }

        poList.forEach(po -> {
            OrderItemTaskInfoDTO dto = new OrderItemTaskInfoDTO();
            BeanUtils.copyProperties(po, dto);
            dtoList.add(dto);
        });
        return dtoList;

    }

    public static BatchTaskDTO convertSortGroupLatestBatchTask(BatchTaskPO batchTaskPO, List<BatchTaskItemPO> itemList) {
        BatchTaskDTO batchTaskDTO = new BatchTaskDTO();
        batchTaskDTO.setId(batchTaskPO.getId());
        batchTaskDTO.setWarehouseId(batchTaskPO.getWarehouseId());
        batchTaskDTO.setOrgId(Integer.valueOf(batchTaskPO.getOrgId()));
        batchTaskDTO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
        List<BatchTaskItemDTO> itemDTOS = itemList.stream().map(item -> {
            BatchTaskItemDTO batchTaskItemDTO = new BatchTaskItemDTO();
            batchTaskItemDTO.setId(item.getId());
            return batchTaskItemDTO;
        }).collect(Collectors.toList());
        batchTaskDTO.setBatchTaskItemList(itemDTOS);

        return batchTaskDTO;
    }
}
