package com.yijiupi.himalaya.supplychain.waves.domain.bl.wcs;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUserAuth;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PassageConstants;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IPassageService;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskSortPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchSowTypeEnum;
import com.yijiupi.supplychain.serviceutils.constant.AdminUserAuthRoleConstant;

/**
 * <AUTHOR>
 * @since 2023-09-12 14:12
 **/
@Component
public class PassageBL {

    @Reference
    private IPassageService passageService;

    @Resource
    private BatchTaskItemMapper batchTaskItemMapper;

    private static final Logger logger = LoggerFactory.getLogger(PassageBL.class);

    /**
     * 查询所有拣货任务的通道
     *
     * @param warehouseId 仓库 id
     * @param taskList 拣货任务
     * @return 通道数据, key 是通道 id, value 是通道对象
     */
    public Map<Long, PassageDTO> listPassageByIds(Integer warehouseId, List<BatchTaskSortPO> taskList) {
        return listPassageByIds(warehouseId, taskList, BatchTaskSortPO::getPassageId);
    }

    /**
     * 查询所有拣货任务的通道
     *
     * @param mapper 获取通道 id 的 mapper
     * @see #listPassageByIds(Integer, List)
     */
    public <T> Map<Long, PassageDTO> listPassageByIds(Integer warehouseId, List<T> taskList, Function<T, Long> mapper) {
        List<Long> passageIds =
            taskList.stream().map(mapper).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        PassageSO so = new PassageSO();
        so.setWarehouseId(warehouseId);
        so.setPassageIdList(passageIds);
        return passageService.listPassage(so).getDataList().stream()
            .collect(Collectors.toMap(PassageDTO::getId, Function.identity(), (k1, k2) -> k1));
    }

    /**
     * 查询所有拣货任务的通道
     *
     * @param warehouseId 仓库 id
     * @param taskList 拣货任务
     * @return 通道数据, key 是 通道关联的货位 id Set, value 是通道对象
     */
    public Map<Set<Long>, PassageDTO> listLocationPassages(Integer warehouseId, List<BatchTaskSortPO> taskList) {
        // 用货位查通道, 兼容逻辑, 以后的新数据肯定有 passageId
        List<String> locationIds =
            taskList.stream().filter(it -> it.getPassageId() == null).map(BatchTaskSortPO::getToLocationId)
                .filter(Objects::nonNull).distinct().map(String::valueOf).collect(Collectors.toList());
        PassageItemSO itemSo = new PassageItemSO();
        itemSo.setWarehouseId(warehouseId);
        itemSo.setRelateIdList(locationIds);
        List<PassageDTO> passages = passageService.listPassageByRelate(itemSo);
        if (CollectionUtils.isEmpty(passages)) {
            return Collections.emptyMap();
        }
        return passages.stream().collect(Collectors.toMap(it -> it.getItemList().stream()
            .map(PassageItemDTO::getRelateId).map(Long::valueOf).collect(Collectors.toSet()), it -> it, (a, b) -> a));
    }

    /**
     * 查询二次分拣通道
     *
     * @param warehouseId 仓库 id
     * @param taskList 拣货任务
     * @return 二次分拣通道, key 是通道 id, value 是通道对象
     */
    @SuppressWarnings("NonAsciiCharacters")
    public Map<Long, PassageDTO> getSecondPickPassageMap(Integer warehouseId, List<BatchTaskSortPO> taskList) {
        List<Long> secondPickPassageIdLit =
            taskList.stream().filter(ele -> Objects.equals(ele.getSowType(), (int)BatchSowTypeEnum.二次分拣.getType()))
                .map(BatchTaskSortPO::getPassageId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(secondPickPassageIdLit)) {
            PassageSO passageSO = new PassageSO();
            passageSO.setWarehouseId(warehouseId);
            passageSO.setPassageIdList(secondPickPassageIdLit);
            List<PassageDTO> secondPickPassageList = passageService.listPassage(passageSO).getDataList();
            if (!CollectionUtils.isEmpty(secondPickPassageList)) {
                return secondPickPassageList.stream()
                    .collect(Collectors.toMap(PassageDTO::getId, Function.identity(), (k1, k2) -> k1));
            }
        }
        return null;
    }

    /**
     * 为拣货任务填充通道 编码
     *
     * @param batchTasks 拣货任务
     */
    public void fillPassageCode(List<BatchTaskDTO> batchTasks) {
        if (CollectionUtils.isEmpty(batchTasks)) {
            logger.info("拣货任务为空, 跳过后续处理");
            return;
        }
        Integer warehouseId = batchTasks.get(0).getWarehouseId();
        Map<Long, PassageDTO> passageMap = listPassageByIds(warehouseId, batchTasks, BatchTaskDTO::getPassageId);
        Map<Set<Long>, PassageDTO> locationPassageMap = listLocationPassagesByBatchTask(warehouseId, batchTasks);
        List<String> historyData = batchTasks.stream().filter(it -> it.getPassageId() == null).map(BatchTaskDTO::getId)
            .collect(Collectors.toList());
        Map<String, Long> batchTaskLocationMap = Collections.emptyMap();
        if (!historyData.isEmpty()) {
            // 随便取一个 locationId 就行
            batchTaskLocationMap = batchTaskItemMapper.findBatchTaskItemDTOListByTaskIds(historyData).stream()
                .filter(it -> it.getLocationId() != null).collect(
                    Collectors.toMap(BatchTaskItemPO::getBatchTaskId, BatchTaskItemPO::getLocationId, (a, b) -> b));
        }
        for (BatchTaskDTO batchTask : batchTasks) {
            Long passageId = batchTask.getPassageId();
            // 没有通道 id 就尝试用货位查询其关联的通道
            if (passageId == null) {
                for (Map.Entry<Set<Long>, PassageDTO> entry : locationPassageMap.entrySet()) {
                    if (entry.getKey().contains(batchTaskLocationMap.get(batchTask.getId()))) {
                        batchTask.setPassageCode(entry.getValue().getPassageCode());
                    }
                }
            } else {
                Optional.ofNullable(passageMap.get(passageId)).map(PassageDTO::getPassageCode)
                    .ifPresent(batchTask::setPassageCode);
            }
        }
    }

    private Map<Set<Long>, PassageDTO> listLocationPassagesByBatchTask(Integer warehouseId,
        List<BatchTaskDTO> batchTasks) {
        List<BatchTaskSortPO> adapter = batchTasks.stream().map(it -> {
            BatchTaskSortPO dto = new BatchTaskSortPO();
            dto.setPassageId(it.getPassageId());
            dto.setToLocationId(it.getToLocationId());
            return dto;
        }).collect(Collectors.toList());
        return listLocationPassages(warehouseId, adapter);
    }

    /**
     * 人身上设置的小件，则能查 passageId 是 小件 和 passageId为null的 <br />
     * 人身上设置的大件，则能查到 passageId 是大件 和 passageId 为 null 的 <br />
     * 人身上没设置，则能查到所有的 <br />
     * 
     * @param warehouseId
     * @param adminUser
     * @return
     */
    public List<Long> getPackagePassageId(Integer warehouseId, AdminUser adminUser) {
        PassageSO passageSO = new PassageSO();
        passageSO.setWarehouseId(warehouseId);

        passageSO.setPageSize(100);

        PageList<PassageDTO> pageList = passageService.listPassage(passageSO);
        if (Objects.isNull(pageList)
            || com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(pageList.getDataList())) {
            return Collections.emptyList();
        }

        if (CollectionUtils.isEmpty(adminUser.getAuthList())) {
            return Collections.emptyList();
        }

        List<AdminUserAuth> adminUserAuths = adminUser.getAuthList().stream()
            .filter(m -> Objects.nonNull(m.getOrg_Id())).filter(m -> m.getOrg_Id().equals(warehouseId))
            .filter(m -> AdminUserAuthRoleConstant.ROLE_CODE_拣货员.equals(m.getUserRole())).collect(Collectors.toList());

        Byte pickingAttrType = getPickingAttr(adminUserAuths);
        if (getAllPassage(pickingAttrType)) {
            return pageList.getDataList().stream().map(PassageDTO::getId).distinct().collect(Collectors.toList());
        }

        return pageList.getDataList().stream()
            .filter(passageDTO -> (Objects.isNull(passageDTO.getPackageType()) || PassageConstants.DEFAULT.equals(passageDTO.getPackageType())
                    || pickingAttrType.equals(passageDTO.getPackageType())))
            .map(PassageDTO::getId).distinct().collect(Collectors.toList());
    }

    private boolean getAllPassage(Byte pickingAttrType) {
        if (Objects.isNull(pickingAttrType)) {
            return Boolean.TRUE;
        }
        if (PassageConstants.PACKAGE.equals(pickingAttrType)) {
            return Boolean.FALSE;
        }
        if (PassageConstants.UNIT.equals(pickingAttrType)) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    private Byte getPickingAttr(List<AdminUserAuth> adminUserAuths) {
        if (CollectionUtils.isEmpty(adminUserAuths)) {
            return null;
        }
        AdminUserAuth adminUserAuth = adminUserAuths.stream().findFirst().get();
        if (PassageConstants.DEFAULT.equals(adminUserAuth.getPickingAttr())) {
            return null;
        }

        return adminUserAuth.getPickingAttr();
    }

}
