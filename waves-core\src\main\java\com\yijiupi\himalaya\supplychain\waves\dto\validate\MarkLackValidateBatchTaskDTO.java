package com.yijiupi.himalaya.supplychain.waves.dto.validate;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/8/8
 */
public class MarkLackValidateBatchTaskDTO implements Serializable {

    /**
     * businessId列表
     */
    private List<String> businessIds;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 操作人id
     */
    private String optUserId;

    /**
     * 获取 businessId列表
     *
     * @return businessIds businessId列表
     */
    public List<String> getBusinessIds() {
        return this.businessIds;
    }

    /**
     * 设置 businessId列表
     *
     * @param businessIds businessId列表
     */
    public void setBusinessIds(List<String> businessIds) {
        this.businessIds = businessIds;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public String getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(String optUserId) {
        this.optUserId = optUserId;
    }
}
