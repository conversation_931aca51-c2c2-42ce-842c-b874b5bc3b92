package com.yijiupi.himalaya.supplychain.waves.domain.po;

import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 内配酒饮扫码备货记录明细
 *
 * <AUTHOR>
 * @since 2024-10-11 16:11
 **/
public class StockUpRecordItemPO implements Serializable {
    /**
     * 主键 id
     */
    private Long id;

    /**
     * 城市 id
     */
    private Integer orgId;

    /**
     * 主表 id
     */
    private Long stockUpRecordId;

    /**
     * 产品 SkuId
     */
    private Long skuId;

    /**
     * 任务状态
     *
     * @see TaskStateEnum
     */
    private Byte taskState;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 拣货员名称
     */
    private String pickupUser;

    /**
     * 拣货完成时间
     */
    private Date completeTime;

    /**
     * 大件数量
     */
    private BigDecimal packageCount;

    /**
     * 小件数量
     */
    private BigDecimal unitCount;

    /**
     * 创建时间
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getStockUpRecordId() {
        return stockUpRecordId;
    }

    public void setStockUpRecordId(Long stockUpRecordId) {
        this.stockUpRecordId = stockUpRecordId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Byte getTaskState() {
        return taskState;
    }

    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getPickupUser() {
        return pickupUser;
    }

    public void setPickupUser(String pickupUser) {
        this.pickupUser = pickupUser == null ? null : pickupUser.trim();
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() +
               " [" +
               "Hash = " + hashCode() +
               ", id=" + id +
               ", orgId=" + orgId +
               ", stockUpRecordId=" + stockUpRecordId +
               ", skuId=" + skuId +
               ", taskState=" + taskState +
               ", productName=" + productName +
               ", pickupUser=" + pickupUser +
               ", completeTime=" + completeTime +
               ", packageCount=" + packageCount +
               ", unitCount=" + unitCount +
               ", createTime=" + createTime +
               "]";
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StockUpRecordItemPO other = (StockUpRecordItemPO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
               && (this.getOrgId() == null ? other.getOrgId() == null : this.getOrgId().equals(other.getOrgId()))
               && (this.getStockUpRecordId() == null ? other.getStockUpRecordId() == null : this.getStockUpRecordId().equals(other.getStockUpRecordId()))
               && (this.getSkuId() == null ? other.getSkuId() == null : this.getSkuId().equals(other.getSkuId()))
               && (this.getTaskState() == null ? other.getTaskState() == null : this.getTaskState().equals(other.getTaskState()))
               && (this.getProductName() == null ? other.getProductName() == null : this.getProductName().equals(other.getProductName()))
               && (this.getPickupUser() == null ? other.getPickupUser() == null : this.getPickupUser().equals(other.getPickupUser()))
               && (this.getCompleteTime() == null ? other.getCompleteTime() == null : this.getCompleteTime().equals(other.getCompleteTime()))
               && (this.getPackageCount() == null ? other.getPackageCount() == null : this.getPackageCount().equals(other.getPackageCount()))
               && (this.getUnitCount() == null ? other.getUnitCount() == null : this.getUnitCount().equals(other.getUnitCount()))
               && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrgId() == null) ? 0 : getOrgId().hashCode());
        result = prime * result + ((getStockUpRecordId() == null) ? 0 : getStockUpRecordId().hashCode());
        result = prime * result + ((getSkuId() == null) ? 0 : getSkuId().hashCode());
        result = prime * result + ((getTaskState() == null) ? 0 : getTaskState().hashCode());
        result = prime * result + ((getProductName() == null) ? 0 : getProductName().hashCode());
        result = prime * result + ((getPickupUser() == null) ? 0 : getPickupUser().hashCode());
        result = prime * result + ((getCompleteTime() == null) ? 0 : getCompleteTime().hashCode());
        result = prime * result + ((getPackageCount() == null) ? 0 : getPackageCount().hashCode());
        result = prime * result + ((getUnitCount() == null) ? 0 : getUnitCount().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }
}