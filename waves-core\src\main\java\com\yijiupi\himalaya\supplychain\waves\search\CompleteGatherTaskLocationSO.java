/*
 * @ClassName GatherTaskDTO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-10-30 17:42:36
 */
package com.yijiupi.himalaya.supplychain.waves.search;

import java.io.Serializable;
import java.math.BigDecimal;

public class CompleteGatherTaskLocationSO implements Serializable {

    private static final long serialVersionUID = 5378595921671899520L;

    private Long orderId;
    private Long productSkuId;
    /** 集货数量 */
    private BigDecimal total;
    /** 源货位id */
    private Long fromLocationId;
    /** 源货位名 */
    private String fromLocationName;
    /** 目标货位id */
    private Long toLocationId;
    /** 目标货位名 */
    private String toLocationName;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getFromLocationId() {
        return fromLocationId;
    }

    public void setFromLocationId(Long fromLocationId) {
        this.fromLocationId = fromLocationId;
    }

    public String getFromLocationName() {
        return fromLocationName;
    }

    public void setFromLocationName(String fromLocationName) {
        this.fromLocationName = fromLocationName;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }
}