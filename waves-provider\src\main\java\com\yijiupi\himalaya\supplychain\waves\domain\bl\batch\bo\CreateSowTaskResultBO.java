package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo;

import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;

import java.util.Collections;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 创建播种任务的结果BO
 *
 * <AUTHOR>
 * @date 2024/8/18
 */
public class CreateSowTaskResultBO {

    private List<WaveCreateDTO> waveCreateDTOList;

    private List<SowTaskPO> sowTaskPOList;

    private List<SowOrderPO> sowOrderPOList;

    public CreateSowTaskResultBO() {}

    public CreateSowTaskResultBO(List<WaveCreateDTO> waveCreateDTOList) {
        this.waveCreateDTOList = waveCreateDTOList;
    }

    /**
     * 获取
     *
     * @return waveCreateDTOList
     */
    public List<WaveCreateDTO> getWaveCreateDTOList() {
        return this.waveCreateDTOList;
    }

    /**
     * 设置
     *
     * @param waveCreateDTOList
     */
    public void setWaveCreateDTOList(List<WaveCreateDTO> waveCreateDTOList) {
        this.waveCreateDTOList = waveCreateDTOList;
    }

    /**
     * 获取
     *
     * @return sowTaskPOList
     */
    public List<SowTaskPO> getSowTaskPOList() {
        return this.sowTaskPOList;
    }

    /**
     * 设置
     *
     * @param sowTaskPOList
     */
    public void setSowTaskPOList(List<SowTaskPO> sowTaskPOList) {
        this.sowTaskPOList = sowTaskPOList;
    }

    /**
     * 获取
     *
     * @return sowOrderPOList
     */
    public List<SowOrderPO> getSowOrderPOList() {
        return this.sowOrderPOList;
    }

    /**
     * 设置
     *
     * @param sowOrderPOList
     */
    public void setSowOrderPOList(List<SowOrderPO> sowOrderPOList) {
        this.sowOrderPOList = sowOrderPOList;
    }

    public static CreateSowTaskResultBO getDefault() {
        return new CreateSowTaskResultBO(Collections.EMPTY_LIST);
    }
}
