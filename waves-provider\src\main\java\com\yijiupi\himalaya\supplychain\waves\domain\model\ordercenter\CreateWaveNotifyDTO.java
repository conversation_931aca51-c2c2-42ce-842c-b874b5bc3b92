package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.io.Serializable;
import java.util.Date;

/**
 * 创建波次时通知订单中台DTO
 * 
 * <AUTHOR>
 * @Date 2022/1/20
 */
public class CreateWaveNotifyDTO implements Serializable {
    private static final long serialVersionUID = -7175877647052408809L;
    /**
     * 订单Id
     */
    private Long orderId;
    /**
     * 波次号
     */
    private String waveNo;
    /**
     * 仓库Id
     */
    private Integer warehouseId;
    /**
     * 创建波次时间
     */
    private Date createWaveTime;
    /**
     * 操作人Id
     */
    private String optUserId;
    /**
     * 操作人名称
     */
    private String optUserName;

    public String getOptUserName() {
        return optUserName;
    }

    public void setOptUserName(String optUserName) {
        this.optUserName = optUserName;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Date getCreateWaveTime() {
        return createWaveTime;
    }

    public void setCreateWaveTime(Date createWaveTime) {
        this.createWaveTime = createWaveTime;
    }

    public String getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(String optUserId) {
        this.optUserId = optUserId;
    }
}
