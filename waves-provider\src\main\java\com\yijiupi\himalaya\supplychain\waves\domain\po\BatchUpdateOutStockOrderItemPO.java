package com.yijiupi.himalaya.supplychain.waves.domain.po;

/**
 * <AUTHOR>
 * @Title: himalaya-supplychain-microservice-waves
 * @Package com.yijiupi.himalaya.supplychain.waves.domain.po
 * @Description:
 * @date 2018/3/26 15:10
 */
public class BatchUpdateOutStockOrderItemPO {
    /**
     * 任务id
     */
    private Long id;
    /** 波次任务编号 */
    private String batchtaskno;

    /** 波次任务Id */
    private String batchtaskId;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 波次编号
     */
    private String batchno;

    /**
     * 波次Id
     */
    private String batchId;

    /**
     * 播种订单id
     */
    private Long sowOrderId;

    /**
     * 拣货任务详情id
     */
    private String batchTaskItemId;

    /**
     * 播种任务明细id
     */
    private Long sowTaskItemId;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;

    public String getBatchTaskItemId() {
        return batchTaskItemId;
    }

    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBatchtaskno() {
        return batchtaskno;
    }

    public void setBatchtaskno(String batchtaskno) {
        this.batchtaskno = batchtaskno;
    }

    public String getBatchtaskId() {
        return batchtaskId;
    }

    public void setBatchtaskId(String batchtaskId) {
        this.batchtaskId = batchtaskId;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public Long getSowOrderId() {
        return sowOrderId;
    }

    public void setSowOrderId(Long sowOrderId) {
        this.sowOrderId = sowOrderId;
    }

    public Long getSowTaskItemId() {
        return sowTaskItemId;
    }

    public void setSowTaskItemId(Long sowTaskItemId) {
        this.sowTaskItemId = sowTaskItemId;
    }

    /**
     * 获取 货位id
     *
     * @return locationId 货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位id
     *
     * @param locationId 货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 货位名称
     *
     * @return locationName 货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 货位名称
     *
     * @param locationName 货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
