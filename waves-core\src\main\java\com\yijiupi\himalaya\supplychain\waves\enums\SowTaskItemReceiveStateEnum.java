package com.yijiupi.himalaya.supplychain.waves.enums;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/**
 * 播种任务项领取状态枚举
 *
 * <AUTHOR> 2022/12/5
 */
public enum SowTaskItemReceiveStateEnum {
    /**
     * 枚举
     */
    待领((byte)0), 已领((byte)1), 已播种((byte)2), 不可领((byte)3);

    /**
     * type
     */
    private byte type;

    SowTaskItemReceiveStateEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     * 
     * @param value
     * @return
     */
    public static String getEnumByValue(Byte value) {
        if (ObjectUtils.isEmpty(value)) {
            return StringUtils.EMPTY;
        }
        for (SowTaskItemReceiveStateEnum taskStateEnum : SowTaskItemReceiveStateEnum.values()) {
            if (value.compareTo(taskStateEnum.type) == 0) {
                return taskStateEnum.name();
            }
        }
        return StringUtils.EMPTY;
    }
}
