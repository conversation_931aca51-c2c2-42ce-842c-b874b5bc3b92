package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.core.Ordered;
import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskFinishHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishNeedOpBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemUpdatePO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchSowTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/12
 */
@Service
public class BatchTaskItemFinishTaskItemDecoratorBL extends BatchTaskItemFinishDecoratorBL {

    @Override
    protected void doCompleteBatchTaskItem(BatchTaskItemFinishNeedOpBO bo,
        BatchTaskFinishHelperBO batchTaskFinishHelperBO, List<BatchTaskItemDTO> needCompleteBatchTaskItemList) {

        Map<String, BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOMap =
            batchTaskFinishHelperBO.getBatchTaskItemCompleteDTOMap();
        BatchPO batchPO = batchTaskFinishHelperBO.getBatchPO();
        String userName = batchTaskFinishHelperBO.getBatchTaskItemFinishBO().getUserName();

        List<BatchTaskItemUpdatePO> batchTaskItemUpdatePOList = new ArrayList<>();
        for (BatchTaskItemDTO batchTaskItemDTO : needCompleteBatchTaskItemList) {
            BatchTaskItemCompleteDTO dto = batchTaskItemCompleteDTOMap.get(batchTaskItemDTO.getId());
            BatchTaskItemUpdatePO batchTaskItemUpdatePO =
                getBatchTaskItemUpdateDTO(dto, batchTaskItemDTO, batchPO, userName);
            batchTaskItemUpdatePOList.add(batchTaskItemUpdatePO);
        }

        bo.setPoList(batchTaskItemUpdatePOList);
    }

    private BatchTaskItemUpdatePO getBatchTaskItemUpdateDTO(BatchTaskItemCompleteDTO dto,
        BatchTaskItemDTO batchTaskItemDTO, BatchPO batchPO, String userName) {
        BigDecimal specQuantity = batchTaskItemDTO.getSpecQuantity();
        // 数据库中拣货数量+用户拣货数量
        BigDecimal overSortCount = dto.getOverSortPackageCount().multiply(specQuantity).add(dto.getOverSortUnitCount());
        // 数据库中缺省数量+用户缺省数量
        BigDecimal lackCount = dto.getLackPackageCount().multiply(specQuantity).add(dto.getLackUnitCount());

        // 对用户做改变的商品构建PO
        BatchTaskItemUpdatePO po = new BatchTaskItemUpdatePO();
        po.setId(dto.getId());
        po.setOverSortCount(overSortCount);
        po.setLackCount(lackCount);
        po.setTaskState(TaskStateEnum.已完成.getType());
        po.setLastUpdateUser(userName);
        if (lackCount.compareTo(BigDecimal.ZERO) > 0) {
            // 缺货
            po.setIsLack((byte)1);
        } else {
            po.setIsLack((byte)0);
        }
        // 来源货位id（如果客户端传过来为空，则取拣货任务详情的货位id）
        Long fromLocationId = BatchTaskFinishHelperBO.getFromLocationId(dto, batchTaskItemDTO);
        String fromLocationName = BatchTaskFinishHelperBO.getFromLocationName(dto, batchTaskItemDTO);
        po.setFromLocationId(fromLocationId);
        po.setFromLocationName(fromLocationName);
        po.setStartTime(dto.getStartTime());
        po.setCompleteTime(dto.getCompleteTime());
        po.setRemark(dto.getRemark());
        // ......
        if (Objects.equals(batchPO.getSowType(), BatchSowTypeEnum.二次分拣.getType())) {
            po.setSownUnitTotalCount(new BigDecimal(SowTaskStateEnum.待播种.getType()));
        }

        return po;
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 100;
    }
}
