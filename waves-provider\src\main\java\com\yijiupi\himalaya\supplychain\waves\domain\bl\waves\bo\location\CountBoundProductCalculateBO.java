package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @title: CountBoundProductCalculateBO
 * @description: 按产品维度聚合的对象，里面都是订单项或者订单明细项
 * @date 2022-12-02 10:24
 */
public class CountBoundProductCalculateBO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;
    /**
     * 大件数
     */
    private BigDecimal packageCount;
    /**
     * 小件数
     */
    private BigDecimal unitCount;
    /**
     * 包装规格系数
     */
    private BigDecimal specQuantity;

    private List<CountBoundCalculateBO> itemList;

    /**
     * 获取 小单位总数量
     *
     * @return unitTotalCount 小单位总数量
     */
    public BigDecimal getUnitTotalCount() {
        return this.unitTotalCount;
    }

    /**
     * 设置 小单位总数量
     *
     * @param unitTotalCount 小单位总数量
     */
    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    /**
     * 获取 主键
     *
     * @return id 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 主键
     *
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 大件数
     *
     * @return packageCount 大件数
     */
    public BigDecimal getPackageCount() {
        return this.packageCount;
    }

    /**
     * 设置 大件数
     *
     * @param packageCount 大件数
     */
    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    /**
     * 获取 小件数
     *
     * @return unitCount 小件数
     */
    public BigDecimal getUnitCount() {
        return this.unitCount;
    }

    /**
     * 设置 小件数
     *
     * @param unitCount 小件数
     */
    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    /**
     * 获取 包装规格系数
     *
     * @return specQuantity 包装规格系数
     */
    public BigDecimal getSpecQuantity() {
        return this.specQuantity;
    }

    /**
     * 设置 包装规格系数
     *
     * @param specQuantity 包装规格系数
     */
    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    /**
     * 获取
     *
     * @return itemList
     */
    public List<CountBoundCalculateBO> getItemList() {
        return this.itemList;
    }

    /**
     * 设置
     *
     * @param itemList
     */
    public void setItemList(List<CountBoundCalculateBO> itemList) {
        this.itemList = itemList;
    }
}
