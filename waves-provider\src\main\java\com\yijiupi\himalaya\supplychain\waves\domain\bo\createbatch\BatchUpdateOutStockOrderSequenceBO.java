package com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/24
 */
public class BatchUpdateOutStockOrderSequenceBO {
    /**
     * 出库单id列表
     */
    private List<Long> outStockOrderIds;
    /**
     * 订单序号
     */
    private Integer orderSequence;

    /**
     * 获取 出库单id列表
     *
     * @return outStockOrderIds 出库单id列表
     */
    public List<Long> getOutStockOrderIds() {
        return this.outStockOrderIds;
    }

    /**
     * 设置 出库单id列表
     *
     * @param outStockOrderIds 出库单id列表
     */
    public void setOutStockOrderIds(List<Long> outStockOrderIds) {
        this.outStockOrderIds = outStockOrderIds;
    }

    /**
     * 获取 订单序号
     *
     * @return orderSequence 订单序号
     */
    public Integer getOrderSequence() {
        return this.orderSequence;
    }

    /**
     * 设置 订单序号
     *
     * @param orderSequence 订单序号
     */
    public void setOrderSequence(Integer orderSequence) {
        this.orderSequence = orderSequence;
    }
}
