package com.yijiupi.himalaya.supplychain.waves.domain.model.tms;

import java.io.Serializable;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/13
 */
public class SyncTmsOutLocationOrderDTO implements Serializable {
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 默认货位名称
     */
    private String defaultLocationName;
    /**
     * 默认货位id
     */
    private Long defaultLocationId;

    /**
     * 获取 订单id
     *
     * @return orderId 订单id
     */
    public String getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 订单id
     *
     * @param orderId 订单id
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 默认货位名称
     *
     * @return defaultLocationName 默认货位名称
     */
    public String getDefaultLocationName() {
        return this.defaultLocationName;
    }

    /**
     * 设置 默认货位名称
     *
     * @param defaultLocationName 默认货位名称
     */
    public void setDefaultLocationName(String defaultLocationName) {
        this.defaultLocationName = defaultLocationName;
    }

    /**
     * 获取 默认货位id
     *
     * @return defaultLocationId 默认货位id
     */
    public Long getDefaultLocationId() {
        return this.defaultLocationId;
    }

    /**
     * 设置 默认货位id
     *
     * @param defaultLocationId 默认货位id
     */
    public void setDefaultLocationId(Long defaultLocationId) {
        this.defaultLocationId = defaultLocationId;
    }
}
