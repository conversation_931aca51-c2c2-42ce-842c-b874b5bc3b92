package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.util.Collections;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.perform.HistoryTaskPerformanceCalculateBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.HistoryTaskPerformanceCalDTO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/7/8
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class TaskPerformanceCalculateBLTest {

    @Autowired
    private HistoryTaskPerformanceCalculateBL historyTaskPerformanceCalculateBL;

    @Test
    public void calculateTest() {
        HistoryTaskPerformanceCalDTO dto = new HistoryTaskPerformanceCalDTO();
        dto.setWarehouseIds(Collections.singletonList(9981));
        dto.setTimeE("2025-08-01 00:00:00");
        dto.setTimeS("2025-06-01 00:00:00");
        historyTaskPerformanceCalculateBL.calculateHistoryTaskPerformance(dto);
    }

}
