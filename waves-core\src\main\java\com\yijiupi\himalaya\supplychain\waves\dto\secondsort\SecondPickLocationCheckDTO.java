package com.yijiupi.himalaya.supplychain.waves.dto.secondsort;

import java.io.Serializable;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.dto.batch.DeliveryTaskDTO;

/**
 * <AUTHOR>
 * @Date 2022/8/18
 */
public class SecondPickLocationCheckDTO implements Serializable {
    private static final long serialVersionUID = -6657140921965985172L;
    private Integer orgId;
    private Integer warehouseId;
    /**
     * 开启拣货通道 0 不开启,1 开启
     */
    private Byte passPickType;
    /**
     * 配送车次列表
     */
    private List<DeliveryTaskDTO> deliveryTaskList;

    public Byte getPassPickType() {
        return passPickType;
    }

    public void setPassPickType(Byte passPickType) {
        this.passPickType = passPickType;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<DeliveryTaskDTO> getDeliveryTaskList() {
        return deliveryTaskList;
    }

    public void setDeliveryTaskList(List<DeliveryTaskDTO> deliveryTaskList) {
        this.deliveryTaskList = deliveryTaskList;
    }
}
