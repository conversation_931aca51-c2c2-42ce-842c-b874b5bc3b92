package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.io.Serializable;

/**
 * 取消波次时通知订单中台DTO
 * 
 * <AUTHOR>
 * @Date 2022/1/20
 */
public class CancelWaveNotifyDTO implements Serializable {
    private static final long serialVersionUID = -779447106832617852L;
    /**
     * 订单Id
     */
    private Long orderId;
    /**
     * 仓库Id
     */
    private Integer warehouseId;
    /**
     * 操作人Id
     */
    private String optUserId;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(String optUserId) {
        this.optUserId = optUserId;
    }
}
