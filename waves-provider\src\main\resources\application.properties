#dubbo
dubbo.scanpackage=com.yijiupi.himalaya
dubbo.zookeeper.address=Zookeeper01.yjp.com:2181,Zookeeper02.yjp.com:2181,Zookeeper03.yjp.com:2181
#dubbo.zookeeper.address=localhost:2181
dubbo.provider=supplychain-waves
dubbo.port=${random.int[3000,4000]}
#dubbo.loadbalance=localfirst
#Redis cluster
spring.redis.cluster.nodes=Redis01.yjp.com:6301,Redis01.yjp.com:6302,Redis02.yjp.com:6301,Redis02.yjp.com:6302,Redis03.yjp.com:6301,Redis03.yjp.com:6302
spring.cache.type=redis
#datasource & pool
#spring.datasource.url=****************************************************************************************
#spring.datasource.url=****************************************************************************************
spring.datasource.url=*****************************************************************************************************
spring.datasource.username=root
spring.datasource.password=mycat
config.sources=sc_StockOrderDB
spring.datasource.maxActive=100
# MyBatis
mybatis.typealiasespackage=com.yijiupi.himalaya.supplychain.waves.domain.po
mybatis.mapperLocations=com/yijiupi/himalaya/supplychain/waves/domain/dao/*.xml
mybatis.configLocation=classpath:/mybatis-config.xml
#MQ
spring.rabbitmq.addresses=RabbitMQ01.yjp.com:6000,RabbitMQ02.yjp.com:6000,RabbitMQ03.yjp.com:6000
spring.rabbitmq.username=yjp
spring.rabbitmq.password=yjp
spring.rabbitmq.listener.retry.enabled=false
spring.rabbitmq.listener.retry.max-attempts=0
spring.rabbitmq.listener.acknowledge-mode=auto
spring.rabbitmq.recover.enable=true
spring.rabbitmq.virtual-host=/
# \u4E8C\u6B21\u5305\u88C5\u4FE1\u606F\u540C\u6B65
ex.supplychain.orderpackage.packageSync=ex.supplychain.orderpackage.packageSync
ex.supplychain.orderpackage.packageDelete=ex.supplychain.orderpackage.packageDelete
# \u04E1\u02F3\u036C
ex.supplychain.orderprint.printSequence=ex.supplychain.orderprint.printSequence
#\u4F9B\u5E94\u94FE3.0\u8C03\u5EA6-\u89E6\u53D1\u4EFB\u52A1
mq.supplychain.scmschedule.triggerTask=mq.supplychain.scmschedule.triggerTask
#APP\u6D88\u606F\u63A8\u9001\u961F\u5217
ex.baseservice.message.yjp_Message_Push=ex.baseservice.message.yjp_Message_Push
# \u5B8C\u6210\u62E3\u8D27\u65F6\u901A\u77E5TMS\uFF0C\u6CE2\u6B21\u4E0B\u6240\u6709\u8BA2\u5355\u5BF9\u5E94\u7684\u51FA\u5E93\u4F4D
ex.supplychain.batchTask.orderToLocation=ex.supplychain.batchTask.orderToLocation
##MQTT\u6D88\u606F\u63A8\u9001
#mqtt.client-name=mqtt-waves-server
#mqtt.user-name=admin
#mqtt.password=admin
#mqtt.server-urls=tcp://mqtt.yjp.com:11883
#mqtt.time-to-wait=3000
#Seata\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0534\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
service.max.commit.retry.timeout=5
service.max.rollback.retry.timeout=5
# \u68C0\u67E5\u8BA2\u5355\u662F\u5426\u91CD\u590D\u751F\u6CE2\u6B21
ex.supplychain.createBatch.checkOrder=ex.supplychain.createBatch.checkOrder
mq.supplychain.createBatch.checkOrder=mq.supplychain.createBatch.checkOrder
# \u65B0\u589E\u6CE2\u6B21
mq.supplychain.waves.createBatch=mq.supplychain.waves.createBatch
# \u66F4\u65B0\u51FA\u5E93\u6279\u6B21\u7684\u51FA\u5E93\u4F4D
mq.supplychain.outBoundBatch.updateStorageLocation=mq.supplychain.outBoundBatch.updateStorageLocation
ex.supplychain.outBoundBatch.updateStorageLocation=ex.supplychain.outBoundBatch.updateStorageLocation
#\u8BA2\u5355\u4E2D\u53F0\u914D\u7F6E
ordercenter.sdk.host=http://in-ordercenter.yjp.com
ordercenter.sdk.timeout=30000
ordercenter.sdk.app-key=6d01b033-7e2c-435a-b0a6-6cfd21e17a80
ordercenter.sdk.app-secret=2e18dc6ff2e5c9ceb2a310137521872b
ex.supplychain.replenishment.add=ex.supplychain.replenishment.add
xxl.job.executor.appname=scm-app-waves
xxl.job.executor.name=scm-app-waves


# Unleash Feature Toggles
unleash.api-key=wms:production.23d36a798e7903c833e2ed964415804a5f8ecc5b05456bb6c1d9677b