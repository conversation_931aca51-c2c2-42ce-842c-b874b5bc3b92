<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper">

    <select id="findDigitalBatchTaskInfo"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskDTO">
        select a.Id as batchTaskId, a.batchTaskNo, a.Warehouse_Id as warehouseId, a.toLocation_Id as locationId,
        a.toLocationName,a.sortGroupId, a.taskState, a.createTime, a.SowTaskNo, a.SowTask_Id as sowTaskId,
        a.BatchTaskName,b.BatchOrderType
        from batchtask a
        inner join batch b on a.BatchNo = b.BatchNo
        where a.warehouse_id = #{warehouseId, jdbcType=INTEGER}
        <if test="orgId != null">
            and a.Org_id = #{orgId, jdbcType=INTEGER}
        </if>
        <if test="sortGroupId != null">
            and a.SortGroupId = #{sortGroupId, jdbcType=BIGINT}
        </if>
        <if test="stateList != null and stateList.size() > 0">
            AND a.taskstate in
            <foreach collection="stateList" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="pickPattern != null">
            and (a.PickPattern = #{pickPattern} or a.KindOfPicking = 2)
        </if>
        <if test="batchTaskNoList != null and batchTaskNoList.size() > 0">
            and a.BatchTaskNo in
            <foreach collection="batchTaskNoList" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="batchTaskIds != null and batchTaskIds.size() > 0">
            and a.Id in
            <foreach collection="batchTaskIds" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="sortGroupIds != null and sortGroupIds.size() > 0">
            and a.SortGroupId in
            <foreach collection="sortGroupIds" index="index" item="item" separator="," open="(" close=")">
                #{item, jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="excludeBatchTaskIds != null and excludeBatchTaskIds.size() > 0">
            and a.Id not in
            <foreach collection="excludeBatchTaskIds" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="timeS != null and timeE != null">
            and a.LastUpdateTime between #{timeS} and #{timeE}
        </if>
    </select>

</mapper>

