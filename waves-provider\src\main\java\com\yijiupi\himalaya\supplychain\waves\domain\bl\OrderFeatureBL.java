package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockQueryService;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskSortOrderDTO;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-09-05 13:49
 **/
@Component
public class OrderFeatureBL {

    @Reference
    @SuppressWarnings("deprecation")
    private IOutStockQueryService outStockQueryService;

    /**
     * 填充 orderFeature 信息
     *
     * @param list 目标 list
     */
    public void fillOrderFeature(List<BatchTaskSortOrderDTO> list) {
        List<Long> orderIds = list.stream().map(BatchTaskSortOrderDTO::getOrderId)
                .map(Long::valueOf).collect(Collectors.toList());
        Map<Long, List<Byte>> orderFeatureMap = getOrderFeatureMap(orderIds);
        for (BatchTaskSortOrderDTO batchTaskSortOrder : list) {
            String orderId = batchTaskSortOrder.getOrderId();
            batchTaskSortOrder.setFeatureType(orderFeatureMap.get(Long.valueOf(orderId)));
        }
    }

    public Map<Long, List<Byte>> getOrderFeatureMap(List<Long> outStockOrderIds) {
        if (CollectionUtils.isEmpty(outStockOrderIds)) {
            return Collections.emptyMap();
        }
        // 这里可能返回的 value 并不是 Byte 类型, 所以需要重新造型
        return outStockQueryService.getOrderFeatureMap(outStockOrderIds).entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, it -> reCastToByte(it.getValue())));
    }

    private List<Byte> reCastToByte(Collection<? extends Number> numbers) {
        return numbers.stream().map(Number::byteValue).collect(Collectors.toList());
    }

}
