package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.ArrayList;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderLocationPalletPO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderPalletInfoDTO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/11
 */
public class OrderPalletInfoDTOConvertor {

    public static List<OrderLocationPalletPO> convert(List<OrderPalletInfoDTO> orderPalletInfoDTOS,
        BatchTaskPO batchTaskPO) {
        List<OrderLocationPalletPO> orderLocationPalletPOList = new ArrayList<>();
        orderPalletInfoDTOS.forEach(orderPalletInfoDTO -> {
            List<String> palletNoList = orderPalletInfoDTO.getPalletNoList();
            palletNoList.forEach(palletNo -> {
                OrderLocationPalletPO orderLocationPalletPO = new OrderLocationPalletPO();
                orderLocationPalletPO.setBatchTaskId(batchTaskPO.getId());
                orderLocationPalletPO.setPalletNo(palletNo);
                orderLocationPalletPO.setLocationId(batchTaskPO.getToLocationId());
                orderLocationPalletPO.setOrderId(orderPalletInfoDTO.getOrderId());
                orderLocationPalletPOList.add(orderLocationPalletPO);
            });
        });

        return orderLocationPalletPOList;
    }

}
