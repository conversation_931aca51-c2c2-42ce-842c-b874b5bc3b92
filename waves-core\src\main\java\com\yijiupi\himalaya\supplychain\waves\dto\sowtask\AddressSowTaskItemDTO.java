package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;
import java.math.BigDecimal;

public class AddressSowTaskItemDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 播种任务明细id
     */
    private Long sowTaskItemId;

    /**
     * skuId
     */
    private Long productSkuId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 规格系数
     */
    private BigDecimal specQuantity;

    /**
     * 待播种小单位总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 已播种小单位总数量
     */
    private BigDecimal overUnitTotalCount;

    /**
     * 播种缺货小单位总数量
     */
    private BigDecimal lackUnitTotalCount;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 播种任务名称
     */
    private String sowTaskName;

    /**
     * 自提点数量
     */
    private Integer addressCount;

    /**
     * 集货位id
     */
    private Long locationId;

    /**
     * 集货位名称
     */
    private String locationName;

    /**
     * 播种货位id
     */
    private Long sowLocationId;

    /**
     * 播种货位名称
     */
    private String sowLocationName;

    /**
     * 播种位数量
     */
    private BigDecimal sowUnitTotalCount;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 操作人id
     */
    private Integer operatorId;

    /**
     * 播种明细状态
     */
    private Byte state;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSowTaskItemId() {
        return sowTaskItemId;
    }

    public void setSowTaskItemId(Long sowTaskItemId) {
        this.sowTaskItemId = sowTaskItemId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public BigDecimal getOverUnitTotalCount() {
        return overUnitTotalCount;
    }

    public void setOverUnitTotalCount(BigDecimal overUnitTotalCount) {
        this.overUnitTotalCount = overUnitTotalCount;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public String getSowTaskName() {
        return sowTaskName;
    }

    public void setSowTaskName(String sowTaskName) {
        this.sowTaskName = sowTaskName;
    }

    public Integer getAddressCount() {
        return addressCount;
    }

    public void setAddressCount(Integer addressCount) {
        this.addressCount = addressCount;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Long getSowLocationId() {
        return sowLocationId;
    }

    public void setSowLocationId(Long sowLocationId) {
        this.sowLocationId = sowLocationId;
    }

    public String getSowLocationName() {
        return sowLocationName;
    }

    public void setSowLocationName(String sowLocationName) {
        this.sowLocationName = sowLocationName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getSowUnitTotalCount() {
        return sowUnitTotalCount;
    }

    public void setSowUnitTotalCount(BigDecimal sowUnitTotalCount) {
        this.sowUnitTotalCount = sowUnitTotalCount;
    }

    public BigDecimal getLackUnitTotalCount() {
        return lackUnitTotalCount;
    }

    public void setLackUnitTotalCount(BigDecimal lackUnitTotalCount) {
        this.lackUnitTotalCount = lackUnitTotalCount;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }
}
