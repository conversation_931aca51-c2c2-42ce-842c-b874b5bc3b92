package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @Date 2021/7/8 14:03
 */
public class LackCheckMethodResultDTO implements Serializable {

    private static final long serialVersionUID = -7876068435155976482L;

    /**
     * 标记缺货的校验方式 0=不校验，1=二维码校验，2=人脸识别校验
     */
    private Byte checkMethod;

    /**
     * 销售库存数量
     */
    private Integer saleStoreNum;

    /**
     * 仓库管理员列表
     */
    private List<WarehouseKeeper> warehouseKeepers;

    public List<WarehouseKeeper> getWarehouseKeepers() {
        return warehouseKeepers;
    }

    public void setWarehouseKeepers(List<WarehouseKeeper> warehouseKeepers) {
        this.warehouseKeepers = warehouseKeepers;
    }

    public Byte getCheckMethod() {
        return checkMethod;
    }

    public void setCheckMethod(Byte checkMethod) {
        this.checkMethod = checkMethod;
    }

    public Integer getSaleStoreNum() {
        return saleStoreNum;
    }

    public void setSaleStoreNum(Integer saleStoreNum) {
        this.saleStoreNum = saleStoreNum;
    }
}
