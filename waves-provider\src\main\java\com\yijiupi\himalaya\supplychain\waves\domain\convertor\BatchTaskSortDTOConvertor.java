package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskSortPO;

/**
 * <AUTHOR>
 * @title: BatchTaskSortDTOConvertor
 * @description:
 * @date 2023-06-20 14:16
 */
public class BatchTaskSortDTOConvertor {

    // TODO 魔术换成枚举
    public static String getAreaOrRouteNameByBatchTask(BatchTaskSortPO po) {
        // 订单筛选策略
        if (po.getOrderSelection().equals(Byte.valueOf("0"))) {
            // 手动新增波次，优先显示区域，区域为空则显示线路
            if (StringUtils.isNotEmpty(po.getAreaName())) {
                return po.getAreaName();
            } else if (StringUtils.isNotEmpty(po.getRouteName())) {
                return po.getRouteName();
            }
        } else if (po.getOrderSelection().equals(Byte.valueOf("1"))) {
            // 按区域
            return po.getAreaName();
        } else if (po.getOrderSelection().equals(Byte.valueOf("2"))) {
            // 按线路
            return po.getRouteName();
        }

        return "";
    }

    public static String getAreaOrRouteName(BatchTaskSortPO po) {
        String areaOrRouteName = getAreaOrRouteNameByBatchTask(po);

        if (StringUtils.isBlank(areaOrRouteName)) {
            return po.getBatchName();
        }

        return areaOrRouteName.concat("-").concat(po.getBatchName());
    }

}
