package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo;

import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;

import java.util.List;

/**
 * <AUTHOR>
 * @title: SowCreateResultBO
 * @description:
 * @date 2023-02-10 10:17
 */
public class SowCreateResultBO {
    /**
     * 新建的播种任务
     */
    List<SowTaskPO> lstSowTaskPO;
    /**
     * 播种单
     */
    List<SowOrderPO> lstSowOrders;
    /**
     * 新建的拣货任务
     */
    List<WaveCreateDTO> lstCreateDTO;

    /**
     * 获取 新建的播种任务
     *
     * @return lstSowTaskPO 新建的播种任务
     */
    public List<SowTaskPO> getLstSowTaskPO() {
        return this.lstSowTaskPO;
    }

    /**
     * 设置 新建的播种任务
     *
     * @param lstSowTaskPO 新建的播种任务
     */
    public void setLstSowTaskPO(List<SowTaskPO> lstSowTaskPO) {
        this.lstSowTaskPO = lstSowTaskPO;
    }

    /**
     * 获取 播种单
     *
     * @return lstSowOrders 播种单
     */
    public List<SowOrderPO> getLstSowOrders() {
        return this.lstSowOrders;
    }

    /**
     * 设置 播种单
     *
     * @param lstSowOrders 播种单
     */
    public void setLstSowOrders(List<SowOrderPO> lstSowOrders) {
        this.lstSowOrders = lstSowOrders;
    }

    /**
     * 获取 新建的拣货任务
     *
     * @return lstCreateDTO 新建的拣货任务
     */
    public List<WaveCreateDTO> getLstCreateDTO() {
        return this.lstCreateDTO;
    }

    /**
     * 设置 新建的拣货任务
     *
     * @param lstCreateDTO 新建的拣货任务
     */
    public void setLstCreateDTO(List<WaveCreateDTO> lstCreateDTO) {
        this.lstCreateDTO = lstCreateDTO;
    }
}
