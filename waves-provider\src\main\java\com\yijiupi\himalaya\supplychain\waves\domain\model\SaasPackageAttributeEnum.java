package com.yijiupi.himalaya.supplychain.waves.domain.model;

/**
 * <AUTHOR>
 * @date 2019/11/25 10:49
 */
public enum SaasPackageAttributeEnum {
    /**
     * 枚举
     */
    大件订单((byte)1), 小件订单((byte)2), 快递直发((byte)3), 统采优先((byte)4);

    /**
     * type
     */
    private Byte type;

    SaasPackageAttributeEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    public static String getType(Byte type) {
        if (null == type) {
            return null;
        }
        for (SaasPackageAttributeEnum attributeEnum : SaasPackageAttributeEnum.values()) {
            if (attributeEnum.getType() == type) {
                return attributeEnum.toString();
            }
        }
        return null;
    }
}
