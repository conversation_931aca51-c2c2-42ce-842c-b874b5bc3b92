package com.yijiupi.himalaya.supplychain.waves.search;

import java.io.Serializable;

/**
 * 出库单产品筛选条件
 *
 * <AUTHOR>
 * @date 2019/4/15 16:27
 */
public class OutStockOrderItemProductSO implements Serializable {

    private static final long serialVersionUID = -8401468425305507271L;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

}
