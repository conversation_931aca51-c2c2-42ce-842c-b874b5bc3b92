package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @title: LocationDivideHelperBO
 * @description: 按订单项处理，把订单项中有大件的
 * @date 2023-02-08 09:47
 */
public class LocationDivideHelperBO {
    /**
     * 产品skuId
     */
    private Long skuId;
    /**
     * 小件数
     */
    private BigDecimal unitTotalCount;
    /**
     * 包装规格数
     */
    private BigDecimal specQuantity;
    /**
     * 销售规格数
     */
    private BigDecimal saleSpecQuantity;
    /**
     * 大件数
     * item中packageCount 聚合 的 数量
     */
    private BigDecimal packageCount;
    /**
     * 小件数
     * item中unitCount 聚合 的 数量
     */
    private BigDecimal unitCount;
    /**
     * 订单项列表
     */
    private List<OutStockOrderItemPO> itemList;


    /**
     * 获取 产品skuId
     *
     * @return skuId 产品skuId
     */
    public Long getSkuId() {
        return this.skuId;
    }

    /**
     * 设置 产品skuId
     *
     * @param skuId 产品skuId
     */
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    /**
     * 获取 小件数
     *
     * @return unitTotalCount 小件数
     */
    public BigDecimal getUnitTotalCount() {
        return this.unitTotalCount;
    }

    /**
     * 设置 小件数
     *
     * @param unitTotalCount 小件数
     */
    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    /**
     * 获取 包装规格数
     *
     * @return specQuantity 包装规格数
     */
    public BigDecimal getSpecQuantity() {
        return this.specQuantity;
    }

    /**
     * 设置 包装规格数
     *
     * @param specQuantity 包装规格数
     */
    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    /**
     * 获取 销售规格数
     *
     * @return saleSpecQuantity 销售规格数
     */
    public BigDecimal getSaleSpecQuantity() {
        return this.saleSpecQuantity;
    }

    /**
     * 设置 销售规格数
     *
     * @param saleSpecQuantity 销售规格数
     */
    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    /**
     * 获取 订单项列表
     *
     * @return itemList 订单项列表
     */
    public List<OutStockOrderItemPO> getItemList() {
        return this.itemList;
    }

    /**
     * 设置 订单项列表
     *
     * @param itemList 订单项列表
     */
    public void setItemList(List<OutStockOrderItemPO> itemList) {
        this.itemList = itemList;
    }

    /**
     * 获取 大件数      item中packageCount 聚合 的 数量
     *
     * @return packageCount 大件数      item中packageCount 聚合 的 数量
     */
    public BigDecimal getPackageCount() {
        return this.packageCount;
    }

    /**
     * 设置 大件数      item中packageCount 聚合 的 数量
     *
     * @param packageCount 大件数      item中packageCount 聚合 的 数量
     */
    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    /**
     * 获取 小件数      item中unitCount 聚合 的 数量
     *
     * @return unitCount 小件数      item中unitCount 聚合 的 数量
     */
    public BigDecimal getUnitCount() {
        return this.unitCount;
    }

    /**
     * 设置 小件数      item中unitCount 聚合 的 数量
     *
     * @param unitCount 小件数      item中unitCount 聚合 的 数量
     */
    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }
}
