<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderTracePOMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.dto.trace.OrderTraceDTO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="BusinessType" jdbcType="TINYINT" property="businesstype"/>
        <result column="Business_Id" jdbcType="BIGINT" property="businessId"/>
        <result column="BusinessNo" jdbcType="VARCHAR" property="businessno"/>
        <result column="EventType" jdbcType="TINYINT" property="eventtype"/>
        <result column="Description" jdbcType="VARCHAR" property="description"/>
        <result column="DiagnoseInfo" jdbcType="VARCHAR" property="diagnoseinfo"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createuser"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createtime"/>
        <result column="Org_id" jdbcType="INTEGER" property="orgId"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id, BusinessType, Business_Id, BusinessNo, EventType, Description, DiagnoseInfo,
        Remark, CreateUser, CreateTime, Org_id
    </sql>
    <select id="selectByBusinessIdOrNo" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stockordertrace
        where 1=1
        <if test="businessId != null and businessId != ''">-- 经销商名称
            AND Business_Id =#{businessId}
        </if>
        <if test="businessNo != null and businessNo != ''">-- 经销商名称
            AND BusinessNo = #{businessNo}
        </if>
        <if test="orgId != null and orgId != ''">-- 城市id
            AND Org_id = #{orgId}
        </if>
        order by CreateTime desc
    </select>
    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.waves.dto.trace.OrderTraceDTO">
        insert into stockordertrace (
        Id,
        BusinessType,
        Business_Id,
        BusinessNo,
        EventType,
        Description,
        DiagnoseInfo,
        Remark,
        CreateUser,
        CreateTime,
        Org_id
        )values(
        #{id,jdbcType=BIGINT},
        #{businesstype,jdbcType=TINYINT},
        #{businessId,jdbcType=BIGINT},
        #{businessno,jdbcType=VARCHAR},
        #{eventtype,jdbcType=TINYINT},
        #{description,jdbcType=VARCHAR},
        #{diagnoseinfo,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{createuser,jdbcType=VARCHAR},
        SYSDATE(),
        #{orgId,jdbcType=INTEGER}
        )
    </insert>

    <insert id="insertUpdateBatch" parameterType="java.util.List">
        insert into stockordertrace (
        Id,
        BusinessType,
        Business_Id,
        BusinessNo,
        EventType,
        Description,
        DiagnoseInfo,
        Remark,
        CreateUser,
        CreateTime,
        Org_id
        )values
        <foreach collection="list" item="trace" separator=",">
            (
            #{trace.id,jdbcType=BIGINT},
            #{trace.businesstype,jdbcType=TINYINT},
            #{trace.businessId,jdbcType=BIGINT},
            #{trace.businessno,jdbcType=VARCHAR},
            #{trace.eventtype,jdbcType=TINYINT},
            #{trace.description,jdbcType=VARCHAR},
            #{trace.diagnoseinfo,jdbcType=VARCHAR},
            #{trace.remark,jdbcType=VARCHAR},
            #{trace.createuser,jdbcType=VARCHAR},
            now(),
            #{trace.orgId,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

</mapper>