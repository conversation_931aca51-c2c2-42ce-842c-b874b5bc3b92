package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.split;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IPromotionStoreBatchService;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IRoutingService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationRuleEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderFeatureBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation.RecommendOutLocationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CreateBatchLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor.WaveCreateDTOSplitConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.ComputerStrategyConditionBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.decoratesplitbatchtask.SplitBatchTaskByOrderTypeBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.slitbyorder.SplitBatchTaskByOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.OutStockLocationRuleBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderRequestBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderTypeBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveOrderInfo;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.OrderPickFlagEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.OutStockOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.WavesStrategyLimitType;
import com.yijiupi.supplychain.serviceutils.constant.DeliveryOrderConstant;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/2
 */
@Service
public class SplitBatchTaskBL {

    @Autowired
    private RecommendOutLocationBL recommendOutLocationBL;
    @Autowired
    private ComputerStrategyConditionBL computerStrategyConditionBL;
    @Autowired
    private OrderFeatureBL orderFeatureBL;
    @Autowired
    private SplitBatchTaskByOrderTypeBL splitBatchTaskByOrderTypeBL;
    @Autowired
    private List<SplitBatchTaskByOrderBL> splitBatchTaskByOrderBLList;

    @Reference
    private IRoutingService routingService;
    @Reference
    private IPromotionStoreBatchService iPromotionStoreBatchService;
    private static final Logger LOG = LoggerFactory.getLogger(SplitBatchTaskBL.class);

    public List<SplitBatchTaskByOrderResultBO> splitBatchTaskByOrder(WaveCreateDTO createDTO, BatchPO batchPO) {
        // 2025-02-08 按订单-不分组分单、 按客户拣货 都不拆单，组合成一个大的拣货任务
        SplitBatchTaskByOrderRequestBO bo = new SplitBatchTaskByOrderRequestBO();
        bo.setCreateDTO(createDTO);
        bo.setBatchPO(batchPO);

        List<SplitBatchTaskByOrderResultBO> totalResultList = splitBatchTaskByOrderBLList.stream()
            .map(m -> m.splitBatchTaskByOrder(bo)).filter(com.alibaba.dubbo.common.utils.CollectionUtils::isNotEmpty)
            .flatMap(m -> m.stream()).collect(Collectors.toList());

        return totalResultList;
    }

    @Deprecated
    public Map<WaveOrderInfo, List<OutStockOrderPO>> splitBatch(List<OutStockOrderPO> outStockOrderPOList,
        WavesStrategyBO wavesStrategyDTO, ProcessBatchDTO processBatchDTO) {
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return Maps.newHashMap();
        }
        Map<WaveOrderInfo, List<OutStockOrderPO>> waveOrders = Maps.newHashMap();
        if (outStockOrderPOList.stream().anyMatch(
            p -> Objects.equals(DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue(), p.getDeliveryMarkState()))) {
            List<OutStockOrderPO> orderPOList = outStockOrderPOList.stream()
                .filter(p -> DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue() == p.getDeliveryMarkState())
                .collect(Collectors.toList());
            waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1), orderPOList);
            outStockOrderPOList
                .removeIf(p -> DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue() == p.getDeliveryMarkState());
        }
        if (outStockOrderPOList.stream()
            .anyMatch(p -> Objects.equals(p.getOrdertype().byteValue(), OutStockOrderTypeEnum.大货批发订单.getType()))) {
            List<OutStockOrderPO> orderPOList = outStockOrderPOList.stream()
                .filter(p -> p.getOrdertype().byteValue() == OutStockOrderTypeEnum.大货批发订单.getType())
                .collect(Collectors.toList());
            waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1), orderPOList);
            outStockOrderPOList.removeIf(p -> p.getOrdertype().byteValue() == OutStockOrderTypeEnum.大货批发订单.getType());
        }
        // 5、是否拆分波次
        if (outStockOrderPOList.size() > 0) {
            if (Objects.equals(processBatchDTO.getOrderPickFlag(), OrderPickFlagEnum.按订单拆分.getType())) {
                LOG.info("按订单拆分波次。。。");
                outStockOrderPOList.forEach(p -> {
                    List<OutStockOrderPO> poList = new ArrayList<>();
                    poList.add(p);
                    waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1), poList);
                });

            } else if (Objects.equals(processBatchDTO.getOrderPickFlag(), OrderPickFlagEnum.按用户拆分.getType())) {
                LOG.info("按用户拆分波次。。。");
                outStockOrderPOList.stream()
                    .collect(Collectors.groupingBy(p -> p.getAddressId() == null ? 0L : p.getAddressId()))
                    .forEach((addressId, orders) -> {
                        waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1), orders);
                    });

                // 这个在调度的时候，车次变更通知wms生成波次，有用
            } else if (Objects.equals(processBatchDTO.getOrderPickFlag(), OrderPickFlagEnum.按集货方式拆分.getType())) {
                LOG.info("按集货方式拆分波次。。。");
                Map<WaveOrderInfo, List<OutStockOrderPO>> orderGroupMap =
                    getWaveShippingMap(outStockOrderPOList, wavesStrategyDTO, processBatchDTO);
                orderGroupMap.forEach((waveOrderInfo, outStockOrderPOS) -> {
                    waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1, waveOrderInfo.getToLocationId()),
                        outStockOrderPOS);
                });
            } else if (Objects.equals(processBatchDTO.getGroupFlag(), ConditionStateEnum.是.getType())) {
                LOG.info("按线路、或片区拆分波次。。。");
                // Map<String, List<OutStockOrderPO>> orderSelectionMap =
                // getOutStockOrderListMap(outStockOrderPOList, wavesStrategyDTO, true);
                // orderSelectionMap.forEach((k, outStockOrderPOS) -> {
                // waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1), outStockOrderPOS);
                // });
                waveOrders.putAll(getWaveOrderMap(outStockOrderPOList, wavesStrategyDTO, true));
            } else {
                waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1), outStockOrderPOList);
            }
        }

        return waveOrders;
    }

    public Map<WaveOrderInfo, List<OutStockOrderPO>> splitBatchInfo(CreateBatchLocationBO createBatchLocationBO,
        WavesStrategyBO wavesStrategyDTO, ProcessBatchDTO processBatchDTO) {
        List<OutStockOrderPO> normalOrderList = createBatchLocationBO.getNormalOrderList();
        List<OutStockOrderPO> npProductOrderList = createBatchLocationBO.getNpProductOrderList();
        List<OutStockOrderPO> totalOrderList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(normalOrderList)) {
            totalOrderList.addAll(normalOrderList);
        }
        if (!CollectionUtils.isEmpty(npProductOrderList)) {
            totalOrderList.addAll(npProductOrderList);
        }

        // TODO
        Map<WaveOrderInfo, List<OutStockOrderPO>> wavesMap =
            splitBatchInfo(totalOrderList, wavesStrategyDTO, processBatchDTO);

        return wavesMap;
    }

    /**
     * 拆分波次
     *
     * @param outStockOrderPOList
     * @param wavesStrategyDTO
     * @param processBatchDTO
     * @return
     */
    public Map<WaveOrderInfo, List<OutStockOrderPO>> splitBatchInfo(List<OutStockOrderPO> outStockOrderPOList,
        WavesStrategyBO wavesStrategyDTO, ProcessBatchDTO processBatchDTO) {
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return Maps.newHashMap();
        }
        Map<WaveOrderInfo, List<OutStockOrderPO>> waveOrders = Maps.newHashMap();
        if (outStockOrderPOList.stream().anyMatch(
            p -> Objects.equals(DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue(), p.getDeliveryMarkState()))) {
            List<OutStockOrderPO> orderPOList = outStockOrderPOList.stream()
                .filter(p -> DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue() == p.getDeliveryMarkState())
                .collect(Collectors.toList());
            waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1), orderPOList);
            outStockOrderPOList
                .removeIf(p -> DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue() == p.getDeliveryMarkState());
        }
        if (outStockOrderPOList.stream()
            .anyMatch(p -> Objects.equals(p.getOrdertype().byteValue(), OutStockOrderTypeEnum.大货批发订单.getType()))) {
            List<OutStockOrderPO> orderPOList = outStockOrderPOList.stream()
                .filter(p -> p.getOrdertype().byteValue() == OutStockOrderTypeEnum.大货批发订单.getType())
                .collect(Collectors.toList());
            waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1), orderPOList);
            outStockOrderPOList.removeIf(p -> p.getOrdertype().byteValue() == OutStockOrderTypeEnum.大货批发订单.getType());
        }
        // 5、是否拆分波次
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return waveOrders;
        }

        if (wavesStrategyDTO.getOrderSelection().intValue() == WavesStrategyConstants.ORDERSELECTION_ORDER) {
            LOG.info("按订单拆分波次。。。");
            outStockOrderPOList.forEach(p -> {
                List<OutStockOrderPO> poList = new ArrayList<>();
                poList.add(p);
                waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1), poList);
            });

        } else if (wavesStrategyDTO.getOrderSelection().intValue() == WavesStrategyConstants.ORDERSELECTION_USER) {
            LOG.info("按用户拆分波次。。。");
            outStockOrderPOList.stream()
                .collect(Collectors.groupingBy(p -> p.getAddressId() == null ? 0L : p.getAddressId()))
                .forEach((addressId, orders) -> {
                    waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1), orders);
                });

            // 这个在调度的时候，车次变更通知wms生成波次，有用
        } else if (wavesStrategyDTO.getOrderSelection().intValue() == WavesStrategyConstants.ORDERSELECTION_SHIPPING) {
            LOG.info("按集货方式拆分波次。。。");
            Map<WaveOrderInfo, List<OutStockOrderPO>> orderGroupMap =
                getWaveShippingMap(outStockOrderPOList, wavesStrategyDTO, processBatchDTO);
            orderGroupMap.forEach((waveOrderInfo, outStockOrderPOS) -> {
                waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1, waveOrderInfo.getToLocationId()),
                    outStockOrderPOS);
            });
        } else if (Objects.equals(processBatchDTO.getGroupFlag(), ConditionStateEnum.是.getType())) {
            LOG.info("按线路、或片区拆分波次。。。");
            // Map<String, List<OutStockOrderPO>> orderSelectionMap =
            // getOutStockOrderListMap(outStockOrderPOList, wavesStrategyDTO, true);
            // orderSelectionMap.forEach((k, outStockOrderPOS) -> {
            // waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1), outStockOrderPOS);
            // });
            waveOrders.putAll(getWaveOrderMap(outStockOrderPOList, wavesStrategyDTO, true));
        } else {
            waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1), outStockOrderPOList);
        }
        return waveOrders;
    }

    /**
     * 过滤出可以追加到波次的订单
     *
     * @param appendOrderList
     * @param batchPO
     * @param batchOutStockOrderList
     * @param wavesStrategyDTO
     * @param processBatchDTO
     * @return
     */
    public List<OutStockOrderPO> filterAppendOutStockOrderInBatch(List<OutStockOrderPO> appendOrderList,
        BatchPO batchPO, WavesStrategyBO wavesStrategyDTO, ProcessBatchDTO processBatchDTO,
        List<OutStockOrderPO> batchOutStockOrderList) {
        if (CollectionUtils.isEmpty(appendOrderList)) {
            return Collections.emptyList();
        }
        if (wavesStrategyDTO.getOrderSelection().intValue() == WavesStrategyConstants.ORDERSELECTION_ORDER) {
            LOG.info("【追加波次】按订单拆分波次。。。");
            return Collections.emptyList();

        } else if (wavesStrategyDTO.getOrderSelection().intValue() == WavesStrategyConstants.ORDERSELECTION_USER) {
            LOG.info("【追加波次】按用户拆分波次。。。");
            Integer existAddressId = batchOutStockOrderList.stream().filter(o -> Objects.nonNull(o.getAddressId()))
                .findFirst().get().getAddressId();

            return appendOrderList.stream().filter(o -> Objects.equals(o.getAddressId(), existAddressId))
                .collect(Collectors.toList());

            // 这个在调度的时候，车次变更通知wms生成波次，有用
        } else if (wavesStrategyDTO.getOrderSelection().intValue() == WavesStrategyConstants.ORDERSELECTION_SHIPPING) {
            LOG.info("【追加波次】按集货方式拆分波次。。。");
            String boundNo = batchOutStockOrderList.stream().filter(o -> StringUtils.isNotBlank(o.getBoundNo()))
                .findFirst().get().getBoundNo();

            List<OutStockOrderPO> filterOrderList = appendOrderList.stream()
                .filter(o -> StringUtils.isBlank(o.getBoundNo()) || !boundNo.equals(o.getBoundNo()))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterOrderList)) {
                return appendOrderList;
            }

            String orderNos = filterOrderList.stream().map(OutStockOrderPO::getReforderno).distinct()
                .collect(Collectors.joining(","));
            throw new BusinessValidateException(
                "订单：" + orderNos + "不在出库批次 " + boundNo + "中，无法追加播种任务！请先将运单追加到车次后再追加波次！");
        } else if (Objects.equals(processBatchDTO.getGroupFlag(), ConditionStateEnum.是.getType())) {
            LOG.info("【追加波次】按线路、或片区拆分波次。。。");
            if (Objects.isNull(wavesStrategyDTO.getOrderSelection())
                || WavesStrategyConstants.ORDERSELECTION_NO == wavesStrategyDTO.getOrderSelection().intValue()) {
                return appendOrderList;
            } else if (WavesStrategyConstants.ORDERSELECTION_AREA == wavesStrategyDTO.getOrderSelection().intValue()) {
                Long areaId = batchOutStockOrderList.stream().filter(o -> Objects.nonNull(o.getAreaId())).findFirst()
                    .get().getAreaId();

                return appendOrderList.stream().filter(o -> Objects.equals(o.getAreaId(), areaId))
                    .collect(Collectors.toList());
            } else if (WavesStrategyConstants.ORDERSELECTION_WAY == wavesStrategyDTO.getOrderSelection().intValue()) {
                Long routeId = batchOutStockOrderList.stream().filter(o -> Objects.nonNull(o.getRouteId())).findFirst()
                    .get().getRouteId();
                return appendOrderList.stream().filter(o -> Objects.equals(o.getRouteId(), routeId))
                    .collect(Collectors.toList());
            }
        }

        LOG.info("【追加波次】走默认拆分波次。。。");
        return appendOrderList;
    }

    private Map<WaveOrderInfo, List<OutStockOrderPO>> getWaveShippingMap(List<OutStockOrderPO> outStockOrderPOList,
        WavesStrategyDTO wavesStrategyDTO, ProcessBatchDTO processBatchDTO) {
        // if (BatchGroupTypeEnum.按用户.getType().equals(wavesStrategyDTO.getGroupType())
        // && Objects.nonNull(processBatchDTO.getToLocationId())) {
        // Map<WaveOrderInfo, List<OutStockOrderPO>> orderGroupMap = new HashMap<>();
        // orderGroupMap.put(new WaveOrderInfo(1, processBatchDTO.getToLocationId()), outStockOrderPOList);
        // return orderGroupMap;
        // }

        Map<WaveOrderInfo, List<OutStockOrderPO>> orderGroupMap =
            this.groupOutStockOrderByShippingWay(outStockOrderPOList, wavesStrategyDTO);
        return orderGroupMap;
    }

    public Map<WaveOrderInfo, List<OutStockOrderPO>> getWaveOrderMap(List<OutStockOrderPO> outStockOrderPOList,
        WavesStrategyDTO wavesStrategyDTO, Boolean isCheck) {
        Map<WaveOrderInfo, List<OutStockOrderPO>> waveOrders = Maps.newHashMap();
        Map<String, OutStockLocationRuleBO> outStockLocationInfoMap = new HashMap<>();
        try {
            Map<String, List<OutStockOrderPO>> orderSelectionMap = Maps.newHashMap();
            LOG.info("波次订单筛选类型{}", wavesStrategyDTO.getOrderSelection());
            if (wavesStrategyDTO.getOrderSelection() == null
                || WavesStrategyConstants.ORDERSELECTION_NO == wavesStrategyDTO.getOrderSelection().intValue()) {
                orderSelectionMap = Maps.newHashMap(ImmutableMap.of("1", outStockOrderPOList));
            } else {

                if (WavesStrategyConstants.ORDERSELECTION_AREA == wavesStrategyDTO.getOrderSelection().intValue()) {
                    // 校验区域是否为空
                    if (isCheck) {
                        if (outStockOrderPOList.stream().anyMatch(p -> p.getAreaId() == null)) {
                            List<String> orderNos = outStockOrderPOList.stream().filter(p -> p.getAreaId() == null)
                                .map(p -> p.getReforderno()).distinct().collect(Collectors.toList());
                            throw new BusinessValidateException(
                                "生成波次失败，以下订单没有对应的区域：" + Arrays.toString(orderNos.toArray()));
                        }
                    }
                    List<OutStockOrderPO> outStockOrderPOS =
                        outStockOrderPOList.stream().filter(p -> p.getAreaId() != null).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(outStockOrderPOS)) {
                        orderSelectionMap = outStockOrderPOS.stream()
                            .collect(Collectors.groupingBy(elem -> String.valueOf(elem.getAreaId())));
                        List<String> areaIdList =
                            orderSelectionMap.keySet().stream().map(String::valueOf).collect(Collectors.toList());
                        outStockLocationInfoMap.putAll(recommendOutLocationBL.getGrayLocationInfoMap(areaIdList,
                            wavesStrategyDTO.getWarehouseId(), LocationRuleEnum.DISTRICT.getCode()));
                        // 按片区查询出库位

                    } else {
                        LOG.info("订单无区域id>>>>{}", JSON.toJSONString(outStockOrderPOList));
                    }

                } else if (WavesStrategyConstants.ORDERSELECTION_WAY == wavesStrategyDTO.getOrderSelection()
                    .intValue()) {
                    // 校验线路是否为空
                    if (isCheck) {
                        if (outStockOrderPOList.stream().anyMatch(p -> p.getRouteId() == null)) {
                            List<String> orderNos = outStockOrderPOList.stream().filter(p -> p.getRouteId() == null)
                                .map(p -> p.getReforderno()).distinct().collect(Collectors.toList());
                            throw new BusinessValidateException(
                                "生成波次失败，以下订单没有对应的线路：" + Arrays.toString(orderNos.toArray()));
                        }
                    }
                    List<OutStockOrderPO> outStockOrderPOS =
                        outStockOrderPOList.stream().filter(p -> p.getRouteId() != null).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(outStockOrderPOS)) {
                        orderSelectionMap = outStockOrderPOS.stream()
                            .collect(Collectors.groupingBy(elem -> String.valueOf(elem.getRouteId())));
                        List<String> areaIdList =
                            orderSelectionMap.keySet().stream().map(String::valueOf).collect(Collectors.toList());
                        outStockLocationInfoMap.putAll(recommendOutLocationBL.getGrayLocationInfoMap(areaIdList,
                            wavesStrategyDTO.getWarehouseId(), LocationRuleEnum.LINE.getCode()));
                    } else {
                        LOG.info("订单无线路id>>>{}", JSON.toJSONString(outStockOrderPOList));
                    }
                }

                // 是否按车容量生成波次
                if (null != wavesStrategyDTO.getIsByCarCapacity()
                    && WavesStrategyConstants.BYCARCAPACITY_YES == wavesStrategyDTO.getIsByCarCapacity().intValue()) {
                    orderSelectionMap = processCarByCarCapacity(orderSelectionMap, wavesStrategyDTO);
                }
                // LOG.info("orderSelectionMap:{}", JSON.toJSONString(orderSelectionMap));
            }

            orderSelectionMap.forEach((k, outStockOrderPOS) -> {
                OutStockLocationRuleBO locationRuleBO = outStockLocationInfoMap.get(k);
                if (Objects.nonNull(locationRuleBO) && Objects.nonNull(locationRuleBO.getLocationId())) {
                    waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1, locationRuleBO.getLocationId()),
                        outStockOrderPOS);
                } else {
                    waveOrders.put(new WaveOrderInfo(waveOrders.size() + 1), outStockOrderPOS);
                }
            });
            return waveOrders;
        } catch (Exception ex) {
            LOG.info("订单分组异常>>>>>>", ex);
            throw new BusinessException(ex);
        }
    }

    /**
     * 按照车次容量进行拆分
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2018/4/18 16:03
     */
    private Map<String, List<OutStockOrderPO>>
        processCarByCarCapacity(Map<String, List<OutStockOrderPO>> OutStockListMap, WavesStrategyDTO wavesStrategyDTO) {
        Map<String, List<OutStockOrderPO>> orderSelectionMap = Maps.newHashMap();

        String routeID = null;
        String areaID = null;

        for (String key : OutStockListMap.keySet()) {
            if (wavesStrategyDTO.getOrderSelection() != null) {
                if (WavesStrategyConstants.ORDERSELECTION_AREA == wavesStrategyDTO.getOrderSelection().intValue()) {
                    routeID = null;
                    areaID = key;
                } else if (WavesStrategyConstants.ORDERSELECTION_WAY == wavesStrategyDTO.getOrderSelection()
                    .intValue()) {
                    routeID = key;
                    areaID = null;
                }
            }
            // 车量最大总件数
            BigDecimal carCapacityByAreaIdOrRouteId =
                routingService.findCarCapacityByAreaIdOrRouteId(wavesStrategyDTO.getWarehouseId(),
                    routeID == null ? null : Long.valueOf(routeID), areaID == null ? null : Long.valueOf(areaID));
            LOG.info("routeID:{} areaID:{} carCapacityByAreaIdOrRouteId:{}", routeID, areaID,
                carCapacityByAreaIdOrRouteId);
            // 订单总件数
            List<OutStockOrderPO> outStockOrderPOS = OutStockListMap.get(key);
            while (outStockOrderPOS.size() > 0) {
                BigDecimal packageCount = computerStrategyConditionBL.computerStrategyConditionByOrders(
                    outStockOrderPOS, WavesStrategyLimitType.piecePackageNumberLimitType);
                LOG.info("packageCount:{}", packageCount);
                List<OutStockOrderPO> lstItemOrders = new ArrayList<>(outStockOrderPOS);
                outStockOrderPOS.clear();
                // 订单明细行上限
                if (carCapacityByAreaIdOrRouteId != null && carCapacityByAreaIdOrRouteId.intValue() > 0
                    && packageCount.compareTo(carCapacityByAreaIdOrRouteId) > 0) {
                    LOG.info("订单总件数上限大于车次要求,自动调整…");
                    // 移除多余的订单，直到满足订单项总数上限
                    while (lstItemOrders.size() > 0 && packageCount.compareTo(carCapacityByAreaIdOrRouteId) > 0) {
                        OutStockOrderPO outStockOrderPO = lstItemOrders.get(lstItemOrders.size() - 1);
                        BigDecimal bigDecimal = computerStrategyConditionBL.computerStrategyConditionByOrder(
                            outStockOrderPO, WavesStrategyLimitType.piecePackageNumberLimitType);
                        lstItemOrders.remove(outStockOrderPO);
                        LOG.info("bigDecimal：{}", bigDecimal);
                        if (bigDecimal != null && bigDecimal.compareTo(carCapacityByAreaIdOrRouteId) > 0) {
                            LOG.info("单个订单：{}大于车次限制的上限进行排除：{}", outStockOrderPO.getId(), carCapacityByAreaIdOrRouteId);
                            continue;
                        }
                        outStockOrderPOS.add(outStockOrderPO);
                        packageCount = computerStrategyConditionBL.computerStrategyConditionByOrders(lstItemOrders,
                            WavesStrategyLimitType.piecePackageNumberLimitType);
                    }
                }
                // LOG.info("lstItemOrders:{}", JSON.toJSONString(lstItemOrders));
                if (lstItemOrders.size() > 0) {
                    orderSelectionMap.put(String.valueOf(orderSelectionMap.size() + 1), lstItemOrders);
                }
            }

        }

        return orderSelectionMap;
    }

    /**
     * 对出库单按集货方式进行分组，并设置推荐出库位，一个分组对应一个波次
     */
    private Map<WaveOrderInfo, List<OutStockOrderPO>>
        groupOutStockOrderByShippingWay(List<OutStockOrderPO> outStockOrderPOList, WavesStrategyDTO wavesStrategyDTO) {
        LOG.info("查询推荐出库位，波次策略：{}", JSON.toJSONString(wavesStrategyDTO));
        // if (BatchGroupTypeEnum.按用户.getType().equals(wavesStrategyDTO.getGroupType())) {
        //
        // }

        Map<WaveOrderInfo, List<OutStockOrderPO>> orderGroupMap = new HashMap<>();
        // 1.查询推荐出库位
        List<LocationRuleDTO> locRuleList =
            recommendOutLocationBL.getRecommendOutLocation(wavesStrategyDTO.getWarehouseId(), outStockOrderPOList,
                wavesStrategyDTO.getDeliveryCarId(), wavesStrategyDTO.getLogisticsCompanyId());
        if (CollectionUtils.isEmpty(locRuleList)) {
            LOG.info("获取不到推荐出库位");
            orderGroupMap.put(new WaveOrderInfo(1), outStockOrderPOList);
            return orderGroupMap;
        }
        // LOG.info("查询推荐出库位响应：{}", JSON.toJSONString(locRuleList));
        long count = locRuleList.stream().map(LocationRuleDTO::getLocationId).distinct().count();
        // 这里只有按车次调度后通知wms才会进这里的逻辑，而且下面会把车次订单放到一个所以一个车次如果有多个出库位，就是有问题，直接返回
        if (count != 1) {
            orderGroupMap.put(new WaveOrderInfo(1), outStockOrderPOList);
            return orderGroupMap;
        }

        // 4.出库单分组，ruleName为分组条件，一个分组对应一个波次
        // 这里之前有个bug，查询出库位的时候，ruleName根本没赋值，一直是空，所以这里的分组一直是所有订单一个分组，现在ruleName设置的bug修复了，
        // 但是已经上生产很久，这里意义不能改，所以直接key设置成null
        // Map<String, List<OutStockOrderPO>> orderSelectionMap =
        // outStockOrderPOList.stream().collect(Collectors.groupingBy(ord -> {
        // LocationRuleDTO locRule = locRuleMap.get(ord.getOutLocationQueryKey());
        // return String.format("%s", locRule == null ? null : locRule.getRuleName());
        // }));
        Map<String, List<OutStockOrderPO>> orderSelectionMap = new HashMap<>();

        // 【促销订单】单独生成波次
        Map<Byte, List<OutStockOrderPO>> promotionGroupMap = splitOutStockOrderByPromotion(outStockOrderPOList);
        int i = 1;
        for (Map.Entry<Byte, List<OutStockOrderPO>> entry : promotionGroupMap.entrySet()) {
            orderGroupMap.put(new WaveOrderInfo(i++, locRuleList.get(0).getLocationId()), entry.getValue());
        }
        // 5.设置推荐出库位，locationId为推荐出库位
        // orderSelectionMap.forEach((ruleName, orderPOList) -> {
        // LocationRuleDTO locRuleDTO = locRuleList.get(0);
        // orderGroupMap.put(
        // new WaveOrderInfo(orderGroupMap.size() + 1, locRuleDTO == null ? null : locRuleDTO.getLocationId()),
        // orderPOList);
        // });
        // LOG.info("推荐出库单，出库单分组结果：{}", JSON.toJSONString(orderSelectionMap));
        return orderGroupMap;
    }

    public List<WaveCreateDTO> splitAllotOrderBatchTaskList(List<OutStockOrderPO> createAllocationOrders,
        WavesStrategyDTO wavesStrategyDTO, ProcessBatchDTO processBatchDTO) {
        if (CollectionUtils.isEmpty(createAllocationOrders)) {
            return Collections.emptyList();
        }
        String title = processBatchDTO.getBatchName();
        Integer cityId = processBatchDTO.getCityId();
        String operateUser = processBatchDTO.getOperateUser();
        String locationName = processBatchDTO.getLocationName();
        String driverName = processBatchDTO.getDriverName();

        if (BooleanUtils.isFalse(processBatchDTO.getOpenFrontWarehouseOpenNPProductPick())) {
            LOG.info("内配单原单单独生成一个按订单拣货的拣货任务。。。");
            WaveCreateDTO allocationCreateDTO =
                SplitWaveOrderUtil.getWaveCreateDTO(createAllocationOrders, wavesStrategyDTO,
                    (byte)WavesStrategyConstants.PICKINGTYPE_ORDER, wavesStrategyDTO.getPickingGroupStrategy(), title,
                    operateUser, cityId, locationName, driverName, null, null, false, processBatchDTO);
            allocationCreateDTO.setToLocationId(processBatchDTO.getToLocationId());
            allocationCreateDTO.setToLocationName(processBatchDTO.getToLocationName());

            // LOG.info("生成波次信息为1:{}", JSON.toJSONString(allocationCreateDTO));
            return Collections.singletonList(allocationCreateDTO);
        }

        List<WaveCreateDTO> waveCreateDTOS = new ArrayList<>();

        List<Long> ids =
            createAllocationOrders.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
        Map<Long, List<Byte>> orderFeatureMap = orderFeatureBL.getOrderFeatureMap(ids);

        // LOG.info("查找到的特征为:{}", JSON.toJSONString(orderFeatureMap));

        List<OutStockOrderPO> npProductOrderList = createAllocationOrders.stream()
            .filter(m -> com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(orderFeatureMap.get(m.getId())))
            .filter(m -> orderFeatureMap.get(m.getId()).stream()
                .anyMatch(feature -> feature.equals(OrderFeatureConstant.FEATURE_TYPE_DRINKING)))
            .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(npProductOrderList)) {
            WaveCreateDTO allocationCreateDTO =
                SplitWaveOrderUtil.getWaveCreateDTO(npProductOrderList, wavesStrategyDTO,
                    (byte)WavesStrategyConstants.PICKINGTYPE_PRODUCT, wavesStrategyDTO.getPickingGroupStrategy(), title,
                    operateUser, cityId, locationName, driverName, null, null, false, processBatchDTO);
            allocationCreateDTO.setToLocationId(processBatchDTO.getToLocationId());
            allocationCreateDTO.setToLocationName(processBatchDTO.getToLocationName());
            allocationCreateDTO
                .setOpenFrontWarehouseOpenNPProductPick(processBatchDTO.getOpenFrontWarehouseOpenNPProductPick());
            waveCreateDTOS.add(allocationCreateDTO);

        }

        Map<Long, Long> npProductOrderMap =
            npProductOrderList.stream().map(OutStockOrderPO::getId).collect(Collectors.toMap(k -> k, v -> v));
        List<OutStockOrderPO> npOrderOrderList = createAllocationOrders.stream()
            .filter(m -> Objects.isNull(npProductOrderMap.get(m.getId()))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(npOrderOrderList)) {
            // LOG.info("生成波次信息为2:{}", JSON.toJSONString(waveCreateDTOS));
            return waveCreateDTOS;
        }

        WaveCreateDTO allocationCreateDTO = SplitWaveOrderUtil.getWaveCreateDTO(npOrderOrderList, wavesStrategyDTO,
            (byte)WavesStrategyConstants.PICKINGTYPE_ORDER, wavesStrategyDTO.getPickingGroupStrategy(), title,
            operateUser, cityId, locationName, driverName, null, null, false, processBatchDTO);
        allocationCreateDTO.setToLocationId(processBatchDTO.getToLocationId());
        allocationCreateDTO.setToLocationName(processBatchDTO.getToLocationName());
        allocationCreateDTO
            .setOpenFrontWarehouseOpenNPProductPick(processBatchDTO.getOpenFrontWarehouseOpenNPProductPick());
        waveCreateDTOS.add(allocationCreateDTO);

        // LOG.info("生成波次信息为3:{}", JSON.toJSONString(waveCreateDTOS));
        return waveCreateDTOS;
    }

    /**
     * 根据订单类型去拆分拣货任务
     *
     * @param wavesStrategyDTO
     * @param orders
     * @param processBatchDTO
     * @return
     */
    public SplitBatchTaskByOrderTypeBO splitBatchTaskByOrderType(WavesStrategyBO wavesStrategyDTO,
        List<OutStockOrderPO> orders, ProcessBatchDTO processBatchDTO) {
        SplitBatchTaskByOrderTypeBO splitBatchTaskByOrderTypeBO = new SplitBatchTaskByOrderTypeBO();

        // 1、内配单原单(单独生成一个按订单拣货的拣货任务)
        List<OutStockOrderPO> createAllocationOrders = orders.stream()
            .filter(p -> Objects.nonNull(p.getOutBoundType())
                && OutBoundTypeEnum.SALE_ORDER.getCode().byteValue() == p.getOutBoundType()
                && OrderConstant.ALLOT_TYPE_ALLOCATION.equals(p.getAllotType()))
            .collect(Collectors.toList());
        List<WaveCreateDTO> allotWaveCreateDTO =
            splitAllotOrderBatchTaskList(createAllocationOrders, wavesStrategyDTO, processBatchDTO);
        if (!CollectionUtils.isEmpty(allotWaveCreateDTO)) {
            splitBatchTaskByOrderTypeBO.setAllotWaveCreateDTO(allotWaveCreateDTO);
        }

        // 2、普通订单（排除内配单原单）
        List<OutStockOrderPO> normalOrders = orders.stream()
            .filter(p -> Objects.isNull(p.getOutBoundType())
                || OutBoundTypeEnum.SALE_ORDER.getCode().byteValue() != p.getOutBoundType()
                || !OrderConstant.ALLOT_TYPE_ALLOCATION.equals(p.getAllotType()))
            .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(normalOrders)) {
            // 按目的仓库拆分拣货任务
            Map<Integer, List<OutStockOrderPO>> orderMap = normalOrders.stream()
                .collect(Collectors.groupingBy(p -> p.getToWarehouseId() == null ? 0 : p.getToWarehouseId()));
            LOG.info("按目的仓库拆分拣货任务：{}", JSON.toJSONString(orderMap));
            List<WaveCreateDTO> waveCreateDTOList =
                WaveCreateDTOSplitConvertor.splitWaveByWarehouse(normalOrders, wavesStrategyDTO, processBatchDTO);

            waveCreateDTOList = WaveCreateDTOSplitConvertor.splitSupplyAgainList(waveCreateDTOList);
            // 如果启用实时分拣， 而且选了 按用户拆分波次， 则按订单特征和用户 进行拣货任务的拆分
            waveCreateDTOList = WaveCreateDTOSplitConvertor.splitWaveByTrayLocation(wavesStrategyDTO, processBatchDTO,
                waveCreateDTOList, getOrderFeatureMap());
            splitBatchTaskByOrderTypeBO.setNormalWaveCreateDTO(waveCreateDTOList);
        }

        return splitBatchTaskByOrderTypeBO;
    }

    public Function<List<Long>, Map<Long, List<Byte>>> getOrderFeatureMap() {
        return orderIds -> orderFeatureBL.getOrderFeatureMap(orderIds);
    }

    /**
     * 按订单特征拆分拣货任务 <br />
     * 像第三方出库单，特征不存在，需要兼容，这里返回-1
     *
     * @param outStockOrderPOList
     * @return
     */
    public Map<Byte, List<OutStockOrderPO>> splitOrderByFeature(List<OutStockOrderPO> outStockOrderPOList) {
        List<Long> outStockOrderIds =
            outStockOrderPOList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());

        Map<Long, List<Byte>> featureMap = orderFeatureBL.getOrderFeatureMap(outStockOrderIds);

        Map<Byte, List<OutStockOrderPO>> orderGroupMap =
            outStockOrderPOList.stream().collect(Collectors.groupingBy(order -> {
                List<Byte> featureList = featureMap.get(order.getId());
                if (CollectionUtils.isEmpty(featureList)) {
                    return (byte)-1;
                }
                if (featureList.stream().anyMatch(OrderFeatureConstant.FEATURE_TYPE_DRINKING::equals)) {
                    return OrderFeatureConstant.FEATURE_TYPE_DRINKING;
                }
                if (featureList.stream().anyMatch(OrderFeatureConstant.FEATURE_TYPE_REST::equals)) {
                    return OrderFeatureConstant.FEATURE_TYPE_REST;
                }
                return (byte)-1;
            }));

        return orderGroupMap;
    }

    private Map<Byte, List<OutStockOrderPO>> splitOutStockOrderByPromotion(List<OutStockOrderPO> outStockOrderPOList) {
        return outStockOrderPOList.stream().collect(Collectors.groupingBy(order -> {
            boolean isPromotion = SplitWaveOrderUtil.orderIsPromotion(order);
            if (isPromotion) {
                return ConditionStateEnum.是.getType();
            }

            return ConditionStateEnum.否.getType();
        }));
    }

}
