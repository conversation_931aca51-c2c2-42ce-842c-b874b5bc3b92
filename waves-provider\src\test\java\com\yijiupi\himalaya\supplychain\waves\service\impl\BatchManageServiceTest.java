package com.yijiupi.himalaya.supplychain.waves.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.SowConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.ConfirmReturnDTO;
import com.yijiupi.junit.runner.GeneralRunner;
import org.assertj.core.api.Assertions;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023-09-11 10:08
 **/
@RunWith(GeneralRunner.class)
public class BatchManageServiceTest {

    @Reference
    private IBatchManageService batchManageService;

    @Test
    public void orderItemDetail() {
        List<ConfirmReturnDTO> confirmReturnDTOS = batchManageService.orderItemDetail("7040002309081600689", 7041);
        Assert.assertNotNull(confirmReturnDTOS);
        System.out.println(JSON.toJSONString(confirmReturnDTOS, SerializerFeature.PrettyFormat));
    }
}