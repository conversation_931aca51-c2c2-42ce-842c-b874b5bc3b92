package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.pickup.IdleOrderPickerBL;
import com.yijiupi.himalaya.supplychain.waves.dto.trace.MessagePushParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dubbop.constant.PushAPPType;
import com.yijiupi.himalaya.supplychain.dubbop.constant.PushType;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderTracePOMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.PushMessageNewDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.mq.PushMsgMQ;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.billreview.BillReviewDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.trace.OrderTraceDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;

import static com.yijiupi.himalaya.supplychain.waves.enums.TaskWarehouseFeatureTypeConstants.TASK_WAREHOUSE_FEATURE_REST;

/**
 * 操作记录
 */
@Service
public class OrderTraceBL {

    private static final Logger LOG = LoggerFactory.getLogger(OrderTraceBL.class);

    private static final int MAX_LENGTH = 1000;

    @Autowired
    private OrderTracePOMapper orderTracePOMapper;

    @Autowired
    private PushMsgMQ pushMsgMQ;

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;

    @Reference
    private IAdminUserQueryService adminUserQueryService;

    @Reference
    private IOrgService orgService;

    @Autowired
    private IdleOrderPickerBL idleOrderPickerBL;

    /**
     * 单条插入
     *
     * @param record
     * @return
     */
    public void insert(OrderTraceDTO record) {
        if (null == record) {
            LOG.info("操作记录参数为null");
            return;
        }
        record.setId(Long.valueOf(UuidUtil.generatorId()));
        if (StringUtils.isNotEmpty(record.getDescription()) && record.getDescription().length() > MAX_LENGTH) {
            record.setDescription(record.getDescription().substring(0, MAX_LENGTH));
        }
        LOG.info("操作记录（{}）参数：{}", record.getDescription(), JSON.toJSONString(record));
        orderTracePOMapper.insert(record);
    }

    /**
     * 批量插入
     *
     * @param record
     * @return
     */
    public void insertUpdateBatch(List<OrderTraceDTO> record) {
        if (null == record) {
            LOG.info("操作记录参数为null");
            return;
        }
        record.stream().map(dto -> {
            dto.setId(Long.valueOf(UuidUtil.generatorId()));
            if (StringUtils.isNotEmpty(dto.getDescription()) && dto.getDescription().length() > MAX_LENGTH) {
                dto.setDescription(dto.getDescription().substring(0, MAX_LENGTH));
            }
            LOG.info("操作记录（{}）参数：{}", dto.getDescription(), JSON.toJSONString(dto));
            return dto;
        }).collect(Collectors.toList());
        orderTracePOMapper.insertUpdateBatch(record);
    }

    /**
     * 根据业务Id或No查询所有日志
     *
     * @param businessId
     * @param businessNo
     * @return
     */
    public List<OrderTraceDTO> selectByBusinessIdOrNo(Integer orgId, Long businessId, String businessNo) {
        AssertUtils.notNull(businessId == null ? businessNo : businessId, "业务ID和编号不能同时为空！");
        return orderTracePOMapper.selectByBusinessIdOrNo(orgId, businessId, businessNo);
    }

    /**
     * 操作记录 - 组建波次成功
     *
     * @param batchPO
     */
    public void insertBatchTrace(BatchPO batchPO, Boolean flag) {
        String description = OrderTraceDescriptionEnum.手动组建波次成功.name();
        if (flag) {
            description = OrderTraceDescriptionEnum.自动组建波次成功.name();
        }
        if (null == batchPO) {
            LOG.info("操作记录（{}）参数为null", description);
            return;
        }
        OrderTraceDTO dto = new OrderTraceDTO();
        dto.setId(Long.valueOf(UuidUtil.generatorId()));
        dto.setBusinesstype(OrderTraceBusinessTypeEnum.波次.getType());
        dto.setBusinessId(Long.valueOf(batchPO.getId()));
        dto.setBusinessno(batchPO.getBatchNo());
        dto.setEventtype(OrderTraceEventTypeEnum.新增.getType());
        dto.setDescription(description);
        dto.setCreateuser(null != batchPO.getCreateUser() ? batchPO.getCreateUser() : "1");
        dto.setOrgId(batchPO.getOrgId());
        LOG.info("操作记录（{}）参数：{}", description, JSON.toJSONString(dto));
        orderTracePOMapper.insert(dto);
    }

    /**
     * 操作记录 - 新建拣货任务成功
     */
    public void insertBatchTaskTrace(String createUser, List<BatchTaskPO> lstBatchTask) {
        if (null == lstBatchTask) {
            LOG.info("操作记录（{}）参数为null", OrderTraceDescriptionEnum.新建拣货任务成功.name());
            return;
        }
        List<OrderTraceDTO> orderTraceDTOList = lstBatchTask.stream().map(batchTask -> {
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setId(Long.valueOf(UuidUtil.generatorId()));
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.拣货任务.getType());
            dto.setBusinessId(Long.valueOf(batchTask.getId()));
            dto.setBusinessno(batchTask.getBatchTaskNo());
            dto.setEventtype(OrderTraceEventTypeEnum.新增.getType());
            dto.setDescription(OrderTraceDescriptionEnum.新建拣货任务成功.name());
            dto.setCreateuser(StringUtils.isEmpty(createUser) ? "1" : createUser);
            dto.setOrgId(StringUtils.isNotEmpty(batchTask.getOrgId()) ? Integer.valueOf(batchTask.getOrgId()) : null);
            return dto;
        }).collect(Collectors.toList());
        orderTracePOMapper.insertUpdateBatch(orderTraceDTOList);

    }

    public void insertBatchTaskTrace(List<OrderTraceDTO> orderTraceDTOList) {
        AssertUtils.notEmpty(orderTraceDTOList, "记录信息不能为空！");
        orderTraceDTOList.forEach(orderTraceDTO -> {
            AssertUtils.notNull(orderTraceDTO.getBusinessno(), "单据不能为空！");
            AssertUtils.notNull(orderTraceDTO.getBusinessno(), "单据不能为空！");
        });

        orderTracePOMapper.insertUpdateBatch(orderTraceDTOList);
    }

    public void notifyDigitalBatchTaskTrace(String createUser, List<BatchTaskPO> lstBatchTask) {
        if (null == lstBatchTask) {
            LOG.info("操作记录（{}）参数为null", OrderTraceDescriptionEnum.新建拣货任务成功.name());
            return;
        }
        List<OrderTraceDTO> orderTraceDTOList = lstBatchTask.stream().map(batchTask -> {
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setId(Long.valueOf(UuidUtil.generatorId()));
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.拣货任务.getType());
            dto.setBusinessId(Long.valueOf(batchTask.getId()));
            dto.setBusinessno(batchTask.getBatchTaskNo());
            dto.setEventtype(OrderTraceEventTypeEnum.新增.getType());
            dto.setDescription(OrderTraceDescriptionEnum.通知电子标签成功.name());
            dto.setCreateuser(StringUtils.isEmpty(createUser) ? "1" : createUser);
            dto.setOrgId(StringUtils.isNotEmpty(batchTask.getOrgId()) ? Integer.valueOf(batchTask.getOrgId()) : null);
            return dto;
        }).collect(Collectors.toList());
        orderTracePOMapper.insertUpdateBatch(orderTraceDTOList);

    }

    /**
     * 操作记录 - 波次删除
     *
     * @param batchPOList
     */
    public void deleteBatchTrace(List<BatchPO> batchPOList, String operateUser) {
        if (null == batchPOList) {
            LOG.info("操作记录（{}）参数为null", OrderTraceDescriptionEnum.波次删除.name());
            return;
        }
        List<OrderTraceDTO> orderTraceDTOList = batchPOList.stream().map(batchPO -> {
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setId(Long.valueOf(UuidUtil.generatorId()));
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.波次.getType());
            dto.setBusinessId(Long.valueOf(batchPO.getId()));
            dto.setBusinessno(batchPO.getBatchNo());
            dto.setEventtype(OrderTraceEventTypeEnum.删除.getType());
            dto.setDescription(OrderTraceDescriptionEnum.波次删除.name());
            dto.setCreateuser(null != operateUser ? operateUser : "1");
            dto.setOrgId(batchPO.getOrgId());
            LOG.info("操作记录（{}）参数：{}", OrderTraceDescriptionEnum.波次删除.name(), JSON.toJSONString(dto));
            return dto;
        }).collect(Collectors.toList());
        orderTracePOMapper.insertUpdateBatch(orderTraceDTOList);
    }

    /**
     * 操作记录 - 删除拣货任务
     *
     * @param batchTaskPOList
     */
    public void deleteBatchTaskTrace(List<BatchTaskPO> batchTaskPOList, String operateUser) {
        if (null == batchTaskPOList) {
            LOG.info("操作记录（{}）参数为null", OrderTraceDescriptionEnum.删除拣货任务.name());
            return;
        }
        List<OrderTraceDTO> orderTraceDTOList = batchTaskPOList.stream().map(batchTaskPO -> {
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setId(Long.valueOf(UuidUtil.generatorId()));
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.拣货任务.getType());
            dto.setBusinessId(Long.valueOf(batchTaskPO.getId()));
            dto.setBusinessno(batchTaskPO.getBatchTaskNo());
            dto.setEventtype(OrderTraceEventTypeEnum.删除.getType());
            dto.setDescription(OrderTraceDescriptionEnum.删除拣货任务.name());
            dto.setCreateuser(null != operateUser ? operateUser : "1");
            dto.setOrgId(
                StringUtils.isNotEmpty(batchTaskPO.getOrgId()) ? Integer.valueOf(batchTaskPO.getOrgId()) : null);
            LOG.info("操作记录（{}）参数：{}", OrderTraceDescriptionEnum.删除拣货任务.name(), JSON.toJSONString(dto));
            return dto;
        }).collect(Collectors.toList());
        orderTracePOMapper.insertUpdateBatch(orderTraceDTOList);
    }

    /**
     * 操作记录 - 修改波次
     *
     * @param batchPOList * @param type 波次拣货描述（波次开始拣货、波次完成拣货）
     */
    public void updateBatchTrace(List<BatchPO> batchPOList, String type, String operateUser) {
        if (null == batchPOList) {
            LOG.info("操作记录（{}）参数为null", type);
            return;
        }
        List<OrderTraceDTO> orderTraceDTOList = batchPOList.stream().map(batchPO -> {
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setId(Long.valueOf(UuidUtil.generatorId()));
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.波次.getType());
            dto.setBusinessId(Long.valueOf(batchPO.getId()));
            dto.setBusinessno(batchPO.getBatchNo());
            dto.setEventtype(OrderTraceEventTypeEnum.修改.getType());
            dto.setDescription(type);
            dto.setCreateuser(null != operateUser ? operateUser : "1");
            dto.setOrgId(batchPO.getOrgId());
            LOG.info("操作记录（{}）参数：{}", type, JSON.toJSONString(dto));
            return dto;
        }).collect(Collectors.toList());
        orderTracePOMapper.insertUpdateBatch(orderTraceDTOList);
    }

    /**
     * 操作记录 - 修改拣货任务
     *
     * @param batchTaskPO
     * @param type 拣货描述（指派拣货员、开始拣货、提交拣货、拣货完成）
     * @param operateUser 操作人
     */
    public void updateBatchTaskTrace(BatchTaskPO batchTaskPO, String type, String operateUser) {
        if (null == batchTaskPO || null == type) {
            LOG.info("操作记录（{}）参数为null", type);
            return;
        }
        OrderTraceDTO dto = new OrderTraceDTO();
        dto.setId(Long.valueOf(UuidUtil.generatorId()));
        dto.setBusinesstype(OrderTraceBusinessTypeEnum.拣货任务.getType());
        dto.setBusinessId(Long.valueOf(batchTaskPO.getId()));
        dto.setBusinessno(batchTaskPO.getBatchTaskNo());
        dto.setEventtype(OrderTraceEventTypeEnum.修改.getType());
        dto.setDescription(type);
        dto.setCreateuser(null != operateUser ? operateUser : "1");
        dto.setOrgId(StringUtils.isNotEmpty(batchTaskPO.getOrgId()) ? Integer.valueOf(batchTaskPO.getOrgId()) : null);
        LOG.info("操作记录（{}）参数：{}", type, JSON.toJSONString(dto));
        orderTracePOMapper.insert(dto);
        // 休食拣货完成后自动分配新的拣货任务
        if (Objects.equals(type, OrderTraceDescriptionEnum.拣货完成.name())
            && TASK_WAREHOUSE_FEATURE_REST.equals(batchTaskPO.getTaskWarehouseFeatureType())) {
            // 自动分配新的拣货任务
            idleOrderPickerBL.assignIdleOrderPickerByWarehouse(Collections.singletonList(batchTaskPO.getSorterId()),
                batchTaskPO.getWarehouseId(), Collections.singletonList(batchTaskPO.getBatchTaskNo()));
        }
    }

    /**
     * 操作记录 - 批量修改拣货任务
     *
     * @param batchTaskPOList
     * @param type 拣货描述（指派拣货员、开始拣货、提交拣货、拣货完成）
     * @param operateUser 操作人
     */
    public void updateBatchTaskTraceBatch(List<BatchTaskPO> batchTaskPOList, String type, String operateUser) {
        if (null == batchTaskPOList || null == type) {
            LOG.info("操作记录（{}）参数为null", type);
            return;
        }
        List<OrderTraceDTO> orderTraceDTOList = batchTaskPOList.stream().map(batchTaskPO -> {
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setId(Long.valueOf(UuidUtil.generatorId()));
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.拣货任务.getType());
            dto.setBusinessId(Long.valueOf(batchTaskPO.getId()));
            dto.setBusinessno(batchTaskPO.getBatchTaskNo());
            dto.setEventtype(OrderTraceEventTypeEnum.修改.getType());
            dto.setDescription(type);
            dto.setCreateuser(null != operateUser ? operateUser : "1");
            dto.setOrgId(
                StringUtils.isNotEmpty(batchTaskPO.getOrgId()) ? Integer.valueOf(batchTaskPO.getOrgId()) : null);
            LOG.info("操作记录（{}）参数：{}", type, JSON.toJSONString(dto));
            return dto;
        }).collect(Collectors.toList());
        orderTracePOMapper.insertUpdateBatch(orderTraceDTOList);
        if (Objects.equals(type, OrderTraceDescriptionEnum.拣货完成.name())) {
            List<Integer> lstUserId = batchTaskPOList.stream()
                .filter(
                    p -> p.getSorterId() != null && TASK_WAREHOUSE_FEATURE_REST.equals(p.getTaskWarehouseFeatureType()))
                .map(p -> p.getSorterId()).collect(Collectors.toList());
            idleOrderPickerBL.assignIdleOrderPickerByWarehouse(lstUserId, batchTaskPOList.get(0).getWarehouseId(),
                batchTaskPOList.stream().map(BatchTaskPO::getBatchTaskNo).collect(Collectors.toList()));
        }
    }

    /**
     * 操作记录 - 打印拣货单
     *
     * @param batchTaskPOList
     * @param operateUser 操作人
     */
    public void printBatchTaskTrace(List<BatchTaskPO> batchTaskPOList, String operateUser) {
        if (null == batchTaskPOList) {
            LOG.info("操作记录（{}）参数为null", OrderTraceDescriptionEnum.打印拣货单.name());
            return;
        }
        List<OrderTraceDTO> orderTraceDTOList = batchTaskPOList.stream().map(batchTaskPO -> {
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setId(Long.valueOf(UuidUtil.generatorId()));
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.拣货任务.getType());
            dto.setBusinessId(Long.valueOf(batchTaskPO.getId()));
            dto.setBusinessno(batchTaskPO.getBatchTaskNo());
            dto.setEventtype(OrderTraceEventTypeEnum.其他.getType());
            dto.setDescription(OrderTraceDescriptionEnum.打印拣货单.name());
            dto.setCreateuser(null != operateUser ? operateUser : "1");
            dto.setOrgId(
                StringUtils.isNotEmpty(batchTaskPO.getOrgId()) ? Integer.valueOf(batchTaskPO.getOrgId()) : null);
            LOG.info("操作记录（{}）参数：{}", OrderTraceDescriptionEnum.打印拣货单.name(), JSON.toJSONString(dto));
            return dto;
        }).collect(Collectors.toList());
        orderTracePOMapper.insertUpdateBatch(orderTraceDTOList);
    }

    /**
     * 发送消息到PDA
     *
     * @param sortId
     */
    public void pushTaskMsg(Integer sortId) {
        pushMsg(sortId, "您有新的拣货任务，请及时处理！");
    }

    /**
     * 发送上架任务消息到PDA
     *
     * @param sortId
     */
    public void pushPutawayTaskMsg(Integer sortId) {
        pushMsg(sortId, "您有新的上架任务，请及时处理！");
    }

    /**
     * 发送移库任务消息到PDA
     *
     * @param sortId
     */
    public void pushStoreTransferMsg(Integer sortId) {
        pushMsg(sortId, "您有新的移库任务，请及时处理！");
    }

    /**
     * 批量添加播种任务生成操作记录
     *
     */
    public void insertSowTaskList(List<SowTaskPO> lstSowTasks) {
        if (CollectionUtils.isEmpty(lstSowTasks)) {
            LOG.info("操作记录（{}）参数为null", OrderTraceDescriptionEnum.新建播种任务成功.name());
            return;
        }

        List<OrderTraceDTO> OrderTraceDTOS = lstSowTasks.stream().map(sowTaskPO -> {
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setId(Long.valueOf(UuidUtil.generatorId()));
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.播种任务.getType());
            dto.setBusinessId(sowTaskPO.getId());
            dto.setBusinessno(sowTaskPO.getSowTaskNo());
            dto.setEventtype(OrderTraceEventTypeEnum.新增.getType());
            dto.setDescription(OrderTraceDescriptionEnum.新建播种任务成功.name());
            dto.setCreateuser(StringUtils.isEmpty(sowTaskPO.getCreateUser()) ? "1" : sowTaskPO.getCreateUser());
            dto.setOrgId(sowTaskPO.getOrgId());
            LOG.info("操作记录（{}）参数：{}", OrderTraceDescriptionEnum.新建播种任务成功.name(), JSON.toJSONString(dto));
            return dto;
        }).collect(Collectors.toList());
        orderTracePOMapper.insertUpdateBatch(OrderTraceDTOS);
    }

    /**
     * 修改播种任务状态
     */
    public void updateSowTaskState(SowTaskDTO sowTaskDTO, String description) {
        if (sowTaskDTO.getNeedLog()) {
            if (sowTaskDTO.getState() != null && sowTaskDTO.getState() == SowTaskStateEnum.播种中.getType()) {
                description = "开始播种，集货位:" + sowTaskDTO.getLocationName();
            }
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setId(Long.valueOf(UuidUtil.generatorId()));
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.播种任务.getType());
            dto.setBusinessId(sowTaskDTO.getId());
            dto.setBusinessno(sowTaskDTO.getSowTaskNo());
            dto.setEventtype(OrderTraceEventTypeEnum.修改.getType());
            dto.setDescription(description);
            dto.setCreateuser(
                StringUtils.isEmpty(sowTaskDTO.getLastUpdateUser()) ? "1" : sowTaskDTO.getLastUpdateUser());
            dto.setOrgId(sowTaskDTO.getOrgId());
            LOG.info("操作记录（{}）参数：{}", description, JSON.toJSONString(dto));

            orderTracePOMapper.insert(dto);

            // 根据播种任务id查找订单详情
            // List<OutStockOrderPO> outStockOrderPOS =
            // outStockOrderMapper.listSimpleBySowTaskNo(sowTaskDTO.getSowTaskNo(), sowTaskDTO.getOrgId());
            // if (CollectionUtils.isNotEmpty(outStockOrderPOS)) {
            // List<Long> omsOrderIds = outStockOrderPOS.stream().map(order -> {
            // if (OrderConstant.ALLOT_TYPE_DEFAULT.equals(order.getAllotType())) {
            // return order.getId();
            // } else {
            // return Long.valueOf(order.getBusinessId());
            // }
            // }).distinct().collect(Collectors.toList());
            // iorderTraceService.addWithOptUserId(omsOrderIds, description, sowTaskDTO.getOperatorId());
            // }
        }
    }

    /**
     * 操作日志 -批量删除播种任务
     *
     * @param sowTaskPOS
     * @param operateUser
     */
    public void deleteSowTaskTrace(List<SowTaskPO> sowTaskPOS, String operateUser) {
        if (null == sowTaskPOS) {
            LOG.info("操作记录（{}）参数为null", OrderTraceDescriptionEnum.播种任务删除.name());
            return;
        }
        List<OrderTraceDTO> orderTraceDTOList = sowTaskPOS.stream().map(sowTaskPO -> {
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setId(Long.valueOf(UuidUtil.generatorId()));
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.播种任务.getType());
            dto.setBusinessId(sowTaskPO.getId());
            dto.setBusinessno(sowTaskPO.getSowTaskNo());
            dto.setEventtype(OrderTraceEventTypeEnum.删除.getType());
            dto.setDescription(OrderTraceDescriptionEnum.播种任务删除.name());
            dto.setCreateuser(null != operateUser ? operateUser : "1");
            dto.setOrgId(sowTaskPO.getOrgId());
            LOG.info("操作记录（{}）参数：{}", OrderTraceDescriptionEnum.播种任务删除.name(), JSON.toJSONString(dto));
            return dto;
        }).collect(Collectors.toList());
        orderTracePOMapper.insertUpdateBatch(orderTraceDTOList);
    }

    public void pushMsg(Integer sortId, String content) {
        try {
            if (sortId == null) {
                return;
            }
            PushMessageNewDTO pushMessageDTO = new PushMessageNewDTO();
            pushMessageDTO.setPushAppTypeValue(PushAPPType.E_JIU_PI_PDA.getValue());
            pushMessageDTO.setPushType(PushType.用户推送);
            List<String> tagOrIdList = new ArrayList<String>();
            tagOrIdList.add("PDA" + sortId); // APP约定注册
            pushMessageDTO.setContent(content);
            pushMessageDTO.setTagOrIdList(tagOrIdList);

            Map<String, String> extras = new HashMap<>();
            extras.put("type", "1");
            extras.put("userId", sortId.toString());
            extras.put("alert", pushMessageDTO.getContent());
            extras.put("content-available", "1");
            pushMessageDTO.setExtras(extras);

            pushMsgMQ.pushAppMessage(pushMessageDTO);

            LOG.info("推送成功：" + JSON.toJSONString(pushMessageDTO));
        } catch (Exception e) {
            LOG.info("推送失败：" + e.getMessage());
        }
    }

    public void batchPushMsg(List<Integer> sortIds, String content) {
        batchPushMsg(sortIds, content, null, null);
    }

    public void batchPushMsg(MessagePushParam param) {
        Optional.ofNullable(param.getUserIds()).map(ArrayList::new).filter(CollectionUtils::isNotEmpty)
            .ifPresent(it -> batchPushMsg(it, param.getContent(), param.getExtraMap(), param.getTitle()));
    }

    public void batchPushMsg(List<Integer> sortIds, String content, Map<String, String> extraMap, String title) {
        batchPushMsg(sortIds, content, extraMap, title, 0);
    }

    public void batchPushMsg(List<Integer> sortIds, String content, Map<String, String> extraMap, String title,
        Integer accountPushType) {
        try {
            if (CollectionUtils.isEmpty(sortIds)) {
                return;
            }
            PushMessageNewDTO pushMessageDTO = new PushMessageNewDTO();
            pushMessageDTO.setPushAppTypeValue(PushAPPType.E_JIU_PI_PDA.getValue());
            pushMessageDTO.setPushType(PushType.用户推送);
            pushMessageDTO.setContent(content);
            pushMessageDTO.setAccountPushType(Optional.ofNullable(accountPushType).orElse(0));
            pushMessageDTO.setTagOrIdList(sortIds.stream().map(it -> "PDA" + it).collect(Collectors.toList()));
            Optional.ofNullable(title).ifPresent(pushMessageDTO::setTitle);
            Map<String, String> extras = new HashMap<>();
            extras.put("type", "1");
            extras.put("userId", sortIds.get(0).toString());
            extras.put("alert", content);
            extras.put("content-available", "1");
            extras.put("x-createTime", DateUtils.getLocalDateTimeNow());
            if (extraMap != null) {
                extras.putAll(extraMap);
            }
            pushMessageDTO.setExtras(extras);
            pushMsgMQ.pushAppMessage(pushMessageDTO);
            LOG.info("批量消息推送成功：{}", JSON.toJSONString(pushMessageDTO));
        } catch (Exception e) {
            LOG.info("批量消息推送失败：{}", e.getMessage());
        }
    }

    public void updateBillReviewState(BillReviewDTO billReviewDTO) {
        OrderTraceDTO dto = new OrderTraceDTO();
        dto.setId(Long.valueOf(UuidUtil.generatorId()));
        dto.setBusinesstype(OrderTraceBusinessTypeEnum.打包复核.getType());
        dto.setBusinessId(billReviewDTO.getBusinessId() == null ? null : Long.valueOf(billReviewDTO.getBusinessId()));
        dto.setBusinessno(billReviewDTO.getBusinessNo());
        String description = null;
        if (billReviewDTO.getState() == BillReviewStateEnum.复核中.getType()) {
            dto.setEventtype(OrderTraceEventTypeEnum.修改.getType());
            description = OrderTraceDescriptionEnum.开始打包复核.name();
        } else if (billReviewDTO.getState() == BillReviewStateEnum.已复核.getType()) {
            dto.setEventtype(OrderTraceEventTypeEnum.修改.getType());
            description = OrderTraceDescriptionEnum.打包复核完成.name();
        }
        dto.setDescription(description);
        dto.setCreateuser(StringUtils.isEmpty(billReviewDTO.getReviewer()) ? "1" : billReviewDTO.getReviewer());
        dto.setOrgId(billReviewDTO.getOrgId());
        LOG.info("操作记录（{}）参数：{}", description, JSON.toJSONString(dto));

        orderTracePOMapper.insert(dto);

    }

    /**
     * 修改播种任务状态
     */
    public void batchUpdateSowTaskState(List<SowTaskPO> sowTaskDTOS, String description, Integer operatorId,
        String operatorName) {
        List<OrderTraceDTO> orderTraceDTOS = new ArrayList<>();
        sowTaskDTOS.forEach(sowTaskPO -> {
            OrderTraceDTO dto = new OrderTraceDTO();
            dto.setId(Long.valueOf(UuidUtil.generatorId()));
            dto.setBusinesstype(OrderTraceBusinessTypeEnum.播种任务.getType());
            dto.setBusinessId(sowTaskPO.getId());
            dto.setBusinessno(sowTaskPO.getSowTaskNo());
            dto.setEventtype(OrderTraceEventTypeEnum.修改.getType());
            dto.setDescription(description);
            dto.setCreateuser(operatorName);
            dto.setOrgId(sowTaskPO.getOrgId());
            LOG.info("操作记录（{}）参数：{}", description, JSON.toJSONString(dto));

            orderTraceDTOS.add(dto);
        });

        orderTracePOMapper.insertUpdateBatch(orderTraceDTOS);

        // 根据播种任务id查找订单详情
        List<String> sowTaskNos =
            orderTraceDTOS.stream().map(OrderTraceDTO::getBusinessno).collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderPOS =
            outStockOrderMapper.listSimpleBySowTaskNos(sowTaskNos, sowTaskDTOS.get(0).getOrgId());
    }

    /**
     * 记录播种任务轨迹
     */
    public void addSowTaskUpdateTrace(SowTaskDTO sowTaskDTO, String description) {
        OrderTraceDTO dto = new OrderTraceDTO();
        dto.setId(Long.valueOf(UuidUtil.generatorId()));
        dto.setBusinesstype(OrderTraceBusinessTypeEnum.播种任务.getType());
        dto.setBusinessId(sowTaskDTO.getId());
        dto.setBusinessno(sowTaskDTO.getSowTaskNo());
        dto.setEventtype(OrderTraceEventTypeEnum.修改.getType());
        dto.setDescription(description);
        dto.setCreateuser(StringUtils.isEmpty(sowTaskDTO.getLastUpdateUser()) ? "1" : sowTaskDTO.getLastUpdateUser());
        dto.setOrgId(sowTaskDTO.getOrgId());
        LOG.info("操作记录（{}）参数：{}", description, JSON.toJSONString(dto));

        orderTracePOMapper.insert(dto);

        // 根据播种任务id查找订单详情
        List<OutStockOrderPO> outStockOrderPOS =
            outStockOrderMapper.listSimpleBySowTaskNo(sowTaskDTO.getSowTaskNo(), sowTaskDTO.getOrgId());
    }

    public void pushMessageByRole(MessagePushParam param) {
        Collection<String> roleCodes = param.getRoleCodes();
        Integer warehouseId = param.getWarehouseId();
        List<AdminUser> users = adminUserQueryService.listAdminUser(new ArrayList<>(roleCodes), warehouseId);
        if (users.isEmpty()) {
            LOG.info("推送消息失败, 找不到负责人, 入参 {}, {}", roleCodes, warehouseId);
            return;
        }
        List<Integer> userIds = users.stream().map(AdminUser::getId).distinct().collect(Collectors.toList());
        batchPushMsg(userIds, param.getContent(), param.getExtraMap(), param.getTitle(), param.getAccountPushType());
    }

}
