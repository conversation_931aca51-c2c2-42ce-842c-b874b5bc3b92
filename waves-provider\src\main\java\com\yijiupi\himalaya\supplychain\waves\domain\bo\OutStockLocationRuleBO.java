package com.yijiupi.himalaya.supplychain.waves.domain.bo;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/6
 */
public class OutStockLocationRuleBO {

    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * 线路/片区id
     */
    private String ruleId;

    /**
     * 获取 货位id
     *
     * @return locationId 货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位id
     *
     * @param locationId 货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 货位名称
     *
     * @return locationName 货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 货位名称
     *
     * @param locationName 货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 线路片区id
     *
     * @return ruleId 线路片区id
     */
    public String getRuleId() {
        return this.ruleId;
    }

    /**
     * 设置 线路片区id
     *
     * @param ruleId 线路片区id
     */
    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }
}
