package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo;

import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;

import java.util.List;

/**
 * <AUTHOR>
 * @title: SowCreateBO
 * @description:
 * @date 2023-02-10 10:14
 */
public class SowCreateBO {

    /**
     * 波次策略
     */
    private WavesStrategyDTO wavesStrategyDTO;
    /**
     * 通道
     */
    private PassageDTO passageDTO;
    /**
     * 波次名称
     */
    private String title;
    /**
     * 操作人
     */
    private String operateUser;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 仓库配置
     */
    private WarehouseConfigDTO warehouseConfigDTO;
    /**
     * 波次
     */
    private BatchPO batchPO;
    /**
     *
     */
    private String locationName;
    /**
     * 司机名称
     */
    private String driverName;
    /**
     * 确认是否抽检
     */
    private Boolean needRecheck;
    /**
     * 内配单标识：内配单波次时传true, 否则传false
     */
    private Boolean allocationFlag;
    /**
     * 生成播种任务的订单
     */
    private List<OutStockOrderPO> outStockOrderList;
    /**
     * 集货位
     */
    private List<LocationReturnDTO> lstLocations;

    /**
     * 获取 波次策略
     *
     * @return wavesStrategyDTO 波次策略
     */
    public WavesStrategyDTO getWavesStrategyDTO() {
        return this.wavesStrategyDTO;
    }

    /**
     * 设置 波次策略
     *
     * @param wavesStrategyDTO 波次策略
     */
    public void setWavesStrategyDTO(WavesStrategyDTO wavesStrategyDTO) {
        this.wavesStrategyDTO = wavesStrategyDTO;
    }

    /**
     * 获取 通道
     *
     * @return passageDTO 通道
     */
    public PassageDTO getPassageDTO() {
        return this.passageDTO;
    }

    /**
     * 设置 通道
     *
     * @param passageDTO 通道
     */
    public void setPassageDTO(PassageDTO passageDTO) {
        this.passageDTO = passageDTO;
    }

    /**
     * 获取 波次名称
     *
     * @return title 波次名称
     */
    public String getTitle() {
        return this.title;
    }

    /**
     * 设置 波次名称
     *
     * @param title 波次名称
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 获取 操作人
     *
     * @return operateUser 操作人
     */
    public String getOperateUser() {
        return this.operateUser;
    }

    /**
     * 设置 操作人
     *
     * @param operateUser 操作人
     */
    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    /**
     * 获取 城市id
     *
     * @return cityId 城市id
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 城市id
     *
     * @param cityId 城市id
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库配置
     *
     * @return warehouseConfigDTO 仓库配置
     */
    public WarehouseConfigDTO getWarehouseConfigDTO() {
        return this.warehouseConfigDTO;
    }

    /**
     * 设置 仓库配置
     *
     * @param warehouseConfigDTO 仓库配置
     */
    public void setWarehouseConfigDTO(WarehouseConfigDTO warehouseConfigDTO) {
        this.warehouseConfigDTO = warehouseConfigDTO;
    }

    /**
     * 获取 波次
     *
     * @return batchPO 波次
     */
    public BatchPO getBatchPO() {
        return this.batchPO;
    }

    /**
     * 设置 波次
     *
     * @param batchPO 波次
     */
    public void setBatchPO(BatchPO batchPO) {
        this.batchPO = batchPO;
    }

    /**
     * 获取
     *
     * @return locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置
     *
     * @param locationName
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 司机名称
     *
     * @return driverName 司机名称
     */
    public String getDriverName() {
        return this.driverName;
    }

    /**
     * 设置 司机名称
     *
     * @param driverName 司机名称
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    /**
     * 获取 确认是否抽检
     *
     * @return needRecheck 确认是否抽检
     */
    public Boolean getNeedRecheck() {
        return this.needRecheck;
    }

    /**
     * 设置 确认是否抽检
     *
     * @param needRecheck 确认是否抽检
     */
    public void setNeedRecheck(Boolean needRecheck) {
        this.needRecheck = needRecheck;
    }

    /**
     * 获取 内配单标识：内配单波次时传true 否则传false
     *
     * @return allocationFlag 内配单标识：内配单波次时传true 否则传false
     */
    public Boolean getAllocationFlag() {
        return this.allocationFlag;
    }

    /**
     * 设置 内配单标识：内配单波次时传true 否则传false
     *
     * @param allocationFlag 内配单标识：内配单波次时传true 否则传false
     */
    public void setAllocationFlag(Boolean allocationFlag) {
        this.allocationFlag = allocationFlag;
    }

    /**
     * 获取 生成播种任务的订单
     *
     * @return outStockOrderList 生成播种任务的订单
     */
    public List<OutStockOrderPO> getOutStockOrderList() {
        return this.outStockOrderList;
    }

    /**
     * 设置 生成播种任务的订单
     *
     * @param outStockOrderList 生成播种任务的订单
     */
    public void setOutStockOrderList(List<OutStockOrderPO> outStockOrderList) {
        this.outStockOrderList = outStockOrderList;
    }

    /**
     * 获取 集货位
     *
     * @return lstLocations 集货位
     */
    public List<LocationReturnDTO> getLstLocations() {
        return this.lstLocations;
    }

    /**
     * 设置 集货位
     *
     * @param lstLocations 集货位
     */
    public void setLstLocations(List<LocationReturnDTO> lstLocations) {
        this.lstLocations = lstLocations;
    }
}
