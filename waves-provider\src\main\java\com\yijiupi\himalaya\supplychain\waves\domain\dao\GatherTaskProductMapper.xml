<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.GatherTaskProductMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskProductPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Org_Id" property="orgId" jdbcType="INTEGER"/>
        <result column="BatchTaskItem_Id" property="batchTaskItemId" jdbcType="BIGINT"/>
        <result column="GatherTask_Id" property="gatherTaskId" jdbcType="BIGINT"/>
        <result column="ProductSku_Id" property="productSkuId" jdbcType="BIGINT"/>
        <result column="ProductName" property="productName" jdbcType="VARCHAR"/>
        <result column="Specification_Id" property="specificationId" jdbcType="BIGINT"/>
        <result column="ProductSpecName" property="productSpecName" jdbcType="VARCHAR"/>
        <result column="SpecQuantity" property="specQuantity" jdbcType="DECIMAL"/>
        <result column="TotalCount" property="totalCount" jdbcType="DECIMAL"/>
        <result column="TakeCount" property="takeCount" jdbcType="DECIMAL"/>
        <result column="CreateUser" property="createUser" jdbcType="BIGINT"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="BIGINT"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Org_Id,BatchTaskItem_Id, GatherTask_Id, ProductSku_Id, ProductName, Specification_Id, ProductSpecName,
        SpecQuantity, TotalCount, TakeCount, CreateUser, CreateTime, LastUpdateUser, LastUpdateTime
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from gathertaskproduct
        where Id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectProduct"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskProductScaleDTO">
        SELECT
        p.Id id,
        p.ProductName productName,
        p.Specification_Id specificationId,
        p.ProductSpecName productSpecName,
        p.TotalCount totalCount,
        p.SpecQuantity specQuantity,
        CONCAT_WS('/',sum(l.`Status`),count(1)) statusScale
        FROM
        gathertaskproduct p
        LEFT JOIN gathertasklocation l ON p.Id = l.GatherTaskProduct_Id
        LEFT JOIN gathertask g ON p.GatherTask_Id = g.Id
        WHERE
        g.BatchTask_Id = #{so.batchTaskId,jdbcType=VARCHAR}
        <if test="so.orgId!=null">
            and p.Org_Id=#{so.orgId,jdbcType=INTEGER}
        </if>
        <if test="so.gatherTaskProductId!=null">
            and p.Id=#{so.gatherTaskProductId,jdbcType=BIGINT}
        </if>
        <if test="so.productName!=null">
            and p.productName like concat('%',#{so.productName,jdbcType=VARCHAR},'%')
        </if>
        GROUP BY
        p.Id,
        p.ProductName,
        p.Specification_Id,
        p.ProductSpecName,
        p.TotalCount,
        p.SpecQuantity
    </select>
    <select id="selectProductDetail"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskLocationScaleDTO">
        SELECT
        l.Id id,
        l.LocationName locationName,
        l.TaskNumber taskNumber,
        l.LicensePlate licensePlate,
        l.DriverName driverName,
        l.SpecQuantity specQuantity,
        l.TotalCount totalCount,
        l.Status status
        FROM
        gathertasklocation l
        WHERE l.GatherTaskProduct_Id = #{gatherTaskProductId,jdbcType=BIGINT}
        <if test="orgId!=null">
            and l.Org_Id=#{orgId,jdbcType=INTEGER}
        </if>
        order by status,locationName
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from gathertaskproduct
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskProductPO">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into gathertaskproduct
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">
                Org_Id,
            </if>
            <if test="batchTaskItemId != null">
                BatchTaskItem_Id,
            </if>
            <if test="gatherTaskId != null">
                GatherTask_Id,
            </if>
            <if test="productSkuId != null">
                ProductSku_Id,
            </if>
            <if test="productName != null">
                ProductName,
            </if>
            <if test="specificationId != null">
                Specification_Id,
            </if>
            <if test="productSpecName != null">
                ProductSpecName,
            </if>
            <if test="specQuantity != null">
                SpecQuantity,
            </if>
            <if test="totalCount != null">
                TotalCount,
            </if>
            <if test="takeCount != null">
                TakeCount,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="batchTaskItemId != null">
                #{batchTaskItemId,jdbcType=BIGINT},
            </if>
            <if test="gatherTaskId != null">
                #{gatherTaskId,jdbcType=BIGINT},
            </if>
            <if test="productSkuId != null">
                #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="specificationId != null">
                #{specificationId,jdbcType=BIGINT},
            </if>
            <if test="productSpecName != null">
                #{productSpecName,jdbcType=VARCHAR},
            </if>
            <if test="specQuantity != null">
                #{specQuantity,jdbcType=DECIMAL},
            </if>
            <if test="totalCount != null">
                #{totalCount,jdbcType=DECIMAL},
            </if>
            <if test="takeCount != null">
                #{takeCount,jdbcType=DECIMAL},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                now(),
            </if>
            <if test="lastUpdateUser != null">
                #{createUser,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateTime != null">
                now(),
            </if>
        </trim>
    </insert>
    <insert id="insertList">
        insert into gathertaskproduct (
        Id,
        Org_Id,
        BatchTaskItem_Id,
        GatherTask_Id,
        ProductSku_Id,
        ProductName,
        Specification_Id,
        ProductSpecName,
        SpecQuantity,
        TotalCount,
        TakeCount,
        CreateUser,
        CreateTime,
        LastUpdateUser,
        LastUpdateTime
        )VALUES
        <foreach collection="pos" item="po" index="index" separator=",">
            (
            #{po.id,jdbcType=BIGINT},
            #{po.orgId,jdbcType=INTEGER},
            #{po.batchTaskItemId,jdbcType=BIGINT},
            #{po.gatherTaskId,jdbcType=BIGINT},
            #{po.productSkuId,jdbcType=BIGINT},
            #{po.productName,jdbcType=VARCHAR},
            #{po.specificationId,jdbcType=BIGINT},
            #{po.productSpecName,jdbcType=VARCHAR},
            #{po.specQuantity,jdbcType=DECIMAL},
            #{po.totalCount,jdbcType=DECIMAL},
            #{po.takeCount,jdbcType=DECIMAL},
            #{po.createUser,jdbcType=BIGINT},
            now(),
            #{po.createUser,jdbcType=BIGINT},
            now()
            )
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.GatherTaskProductPO">
        update gathertaskproduct
        <set>
            <if test="batchTaskItemId != null">
                BatchTaskItem_Id = #{batchTaskItemId,jdbcType=BIGINT},
            </if>
            <if test="gatherTaskId != null">
                GatherTask_Id = #{gatherTaskId,jdbcType=BIGINT},
            </if>
            <if test="productSkuId != null">
                ProductSku_Id = #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                ProductName = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="specificationId != null">
                Specification_Id = #{specificationId,jdbcType=BIGINT},
            </if>
            <if test="productSpecName != null">
                ProductSpecName = #{productSpecName,jdbcType=VARCHAR},
            </if>
            <if test="specQuantity != null">
                SpecQuantity = #{specQuantity,jdbcType=DECIMAL},
            </if>
            <if test="totalCount != null">
                TotalCount = #{totalCount,jdbcType=DECIMAL},
            </if>
            <if test="takeCount != null">
                TakeCount = #{takeCount,jdbcType=DECIMAL},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = now(),
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
</mapper>