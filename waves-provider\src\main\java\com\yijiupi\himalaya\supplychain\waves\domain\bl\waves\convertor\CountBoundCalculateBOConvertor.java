package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CountBoundCalculateBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;

/**
 * <AUTHOR>
 * @title: CountBoundCalculateBOConvertor
 * @description:
 * @date 2022-12-01 13:59
 */
public class CountBoundCalculateBOConvertor {
    private static final Logger LOG = LoggerFactory.getLogger(CountBoundCalculateBOConvertor.class);

    public static List<CountBoundCalculateBO> convertItemList(List<OutStockOrderItemPO> itemList) {
        LOG.info("CountBoundCalculateBOConvertor  convertItemList");
        return itemList.stream().map(CountBoundCalculateBOConvertor::convertItem)
            .sorted((o1, o2) -> o2.getUnitTotalCount().compareTo(o1.getUnitTotalCount())).collect(Collectors.toList());
    }

    public static CountBoundCalculateBO convertItem(OutStockOrderItemPO item) {
        CountBoundCalculateBO bo = new CountBoundCalculateBO();
        bo.setUnitTotalCount(item.getUnittotalcount());
        bo.setId(item.getId());
        return bo;
    }

    public static List<CountBoundCalculateBO> convertItemDetailList(List<OutStockOrderItemDetailPO> detailList) {
        LOG.info("CountBoundCalculateBOConvertor  convertItemDetailList");
        return detailList.stream().map(CountBoundCalculateBOConvertor::convertItemDetail)
            .sorted((o1, o2) -> o2.getUnitTotalCount().compareTo(o1.getUnitTotalCount())).collect(Collectors.toList());
    }

    public static CountBoundCalculateBO convertItemDetail(OutStockOrderItemDetailPO detail) {
        CountBoundCalculateBO bo = new CountBoundCalculateBO();
        bo.setUnitTotalCount(detail.getUnitTotalCount());
        bo.setId(detail.getId());

        return bo;
    }

    public static CountBoundCalculateBO getUpItem(CountBoundCalculateBO oriItem, BigDecimal leftUnitTotalCount) {
        CountBoundCalculateBO bo = new CountBoundCalculateBO();
        bo.setId(oriItem.getId());
        bo.setUnitTotalCount(leftUnitTotalCount);

        return bo;
    }

    public static CountBoundCalculateBO getDownItem(CountBoundCalculateBO oriItem, BigDecimal leftUnitTotalCount) {
        CountBoundCalculateBO bo = new CountBoundCalculateBO();
        bo.setId(oriItem.getId());
        bo.setUnitTotalCount(oriItem.getUnitTotalCount().subtract(leftUnitTotalCount));

        return bo;
    }

    public static OutStockOrderItemDetailPO getItemDetail(OutStockOrderItemDetailPO oriItem, CountBoundCalculateBO bo) {
        OutStockOrderItemDetailPO copyItem = new OutStockOrderItemDetailPO();
        BeanUtils.copyProperties(oriItem, copyItem);
        copyItem.setUnitTotalCount(bo.getUnitTotalCount());

        return copyItem;
    }

    public static List<CountBoundCalculateBO> convertPackageToSlitList(List<PackageOrderItemDTO> packageOrderItemDTOS) {
        return packageOrderItemDTOS.stream().map(dto -> {
            CountBoundCalculateBO bo = new CountBoundCalculateBO();
            bo.setId(dto.getId());
            bo.setUnitTotalCount(dto.getUnitTotalCount());
            return bo;
        }).collect(Collectors.toList());
    }

    public static List<PackageOrderItemDTO> convertToPackageOrderList(
        List<CountBoundCalculateBO> countBoundCalculateBOList, List<PackageOrderItemDTO> notZeroPackageItemList) {
        Map<Long, PackageOrderItemDTO> packageOrderItemDTOMap =
            notZeroPackageItemList.stream().collect(Collectors.toMap(PackageOrderItemDTO::getId, v -> v));
        return countBoundCalculateBOList.stream().filter(bo -> {
            PackageOrderItemDTO packageOrderItemDTO = packageOrderItemDTOMap.get(bo.getId());
            return needUpdatePackageOrderItem(packageOrderItemDTO, bo);
        }).map(bo -> {
            PackageOrderItemDTO packageOrderItemDTO = packageOrderItemDTOMap.get(bo.getId());
            packageOrderItemDTO.setUnitTotalCount(bo.getUnitTotalCount());
            return packageOrderItemDTO;
        }).collect(Collectors.toList());
    }

    private static boolean needUpdatePackageOrderItem(PackageOrderItemDTO packageOrderItemDTO,
        CountBoundCalculateBO bo) {
        if (packageOrderItemDTO.getUnitTotalCount().compareTo(bo.getUnitTotalCount()) != 0) {
            return Boolean.TRUE;
        }
        BigDecimal[] count = bo.getUnitTotalCount().divideAndRemainder(packageOrderItemDTO.getSpecQuantity());

        return count[0].compareTo(packageOrderItemDTO.getPackageCount()) == 0
            && count[1].compareTo(packageOrderItemDTO.getUnitCount()) == 0;
    }

}
