<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskItemMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="VARCHAR" property="orgId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="SowTaskNo" jdbcType="VARCHAR" property="sowTaskNo"/>
        <result column="SowTask_Id" jdbcType="BIGINT" property="sowTaskId"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productSkuId"/>
        <result column="ProductSpecification_Id" jdbcType="BIGINT" property="productSpecificationId"/>
        <result column="Owner_Id" jdbcType="BIGINT" property="ownerId"/>
        <result column="SecOwner_Id" jdbcType="BIGINT" property="secOwnerId"/>
        <result column="ProductName" jdbcType="VARCHAR" property="productName"/>
        <result column="SpecQuantity" jdbcType="DECIMAL" property="specQuantity"/>
        <result column="SpecName" jdbcType="VARCHAR" property="specName"/>
        <result column="PackageCount" jdbcType="DECIMAL" property="packageCount"/>
        <result column="PackageName" jdbcType="VARCHAR" property="packageName"/>
        <result column="UnitCount" jdbcType="DECIMAL" property="unitCount"/>
        <result column="UnitName" jdbcType="VARCHAR" property="unitName"/>
        <result column="UnitTotalCount" jdbcType="DECIMAL" property="unitTotalCount"/>
        <result column="OverUnitTotalCount" jdbcType="DECIMAL" property="overUnitTotalCount"/>
        <result column="StartTime" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="CompleteTime" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LackUnitTotalCount" jdbcType="DECIMAL" property="lackUnitTotalCount"/>
        <result column="Location_Id" jdbcType="BIGINT" property="locationId"/>
        <result column="LocationName" jdbcType="VARCHAR" property="locationName"/>
        <result column="SaleSpecQuantity" jdbcType="DECIMAL" property="saleSpecQuantity"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id
        , Org_Id, Warehouse_Id, SowTaskNo, SowTask_Id, State, ProductSku_Id, ProductSpecification_Id, Owner_Id,
        SecOwner_Id, ProductName, SpecQuantity, SpecName, PackageCount, PackageName,
        UnitCount, UnitName, UnitTotalCount, OverUnitTotalCount, StartTime, CompleteTime,
        Remark, CreateUser, CreateTime, LastUpdateUser, LastUpdateTime, LackUnitTotalCount
    </sql>

    <sql id="Alias_Column_List">
        item
        .
        Id
        , item.Org_Id, item.Warehouse_Id, item.SowTaskNo, item.SowTask_Id, item.State, item.ProductSku_Id,
        item.ProductSpecification_Id, item.Owner_Id,
        item.SecOwner_Id, item.ProductName, item.SpecQuantity, item.SpecName, item.PackageCount, item.PackageName,
        item.UnitCount, item.UnitName, item.UnitTotalCount, item.OverUnitTotalCount, item.StartTime, item.CompleteTime,
        item.Remark, item.CreateUser, item.CreateTime, item.LastUpdateUser, item.LastUpdateTime,
        item.LackUnitTotalCount, item.SaleSpecQuantity
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtaskitem
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO">
        insert into sowtaskitem (Id, Org_Id, Warehouse_Id,
        SowTaskNo, SowTask_Id, State,
        ProductSku_Id, ProductSpecification_Id, Owner_Id,
        SecOwner_Id, ProductName, SpecQuantity,
        SpecName, PackageCount, PackageName,
        UnitCount, UnitName, UnitTotalCount,
        OverUnitTotalCount, StartTime, CompleteTime,
        Remark, CreateUser, CreateTime,
        LastUpdateUser, LastUpdateTime, LackUnitTotalCount)
        values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER},
        #{sowTaskNo,jdbcType=VARCHAR}, #{sowTaskId,jdbcType=BIGINT}, #{state,jdbcType=TINYINT},
        #{productSkuId,jdbcType=BIGINT}, #{productSpecificationId,jdbcType=BIGINT}, #{ownerId,jdbcType=BIGINT},
        #{secOwnerId,jdbcType=BIGINT}, #{productName,jdbcType=VARCHAR}, #{specQuantity,jdbcType=DECIMAL},
        #{specName,jdbcType=VARCHAR}, #{packageCount,jdbcType=DECIMAL}, #{packageName,jdbcType=VARCHAR},
        #{unitCount,jdbcType=DECIMAL}, #{unitName,jdbcType=VARCHAR}, #{unitTotalCount,jdbcType=DECIMAL},
        #{overUnitTotalCount,jdbcType=DECIMAL},
        #{startTime,jdbcType=TIMESTAMP} #{completeTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, now(),
        #{lastUpdateUser,jdbcType=VARCHAR}, now(), #{lackUnitTotalCount,jdbcType=DECIMAL})
    </insert>

    <insert id="insertSelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO">
        insert into sowtaskitem
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="orgId != null">
                Org_id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="sowTaskNo != null">
                SowTaskNo,
            </if>
            <if test="sowTaskId != null">
                SowTask_Id,
            </if>
            <if test="state != null">
                State,
            </if>
            <if test="productSkuId != null">
                ProductSku_Id,
            </if>
            <if test="productSpecificationId != null">
                ProductSpecification_Id,
            </if>
            <if test="ownerId != null">
                Owner_Id,
            </if>
            <if test="secOwnerId != null">
                SecOwner_Id,
            </if>
            <if test="productName != null">
                ProductName,
            </if>
            <if test="specQuantity != null">
                SpecQuantity,
            </if>
            <if test="specName != null">
                SpecName,
            </if>
            <if test="packageCount != null">
                PackageCount,
            </if>
            <if test="packageName != null">
                PackageName,
            </if>
            <if test="unitCount != null">
                UnitCount,
            </if>
            <if test="unitName != null">
                UnitName,
            </if>
            <if test="unitTotalCount != null">
                UnitTotalCount,
            </if>
            <if test="overUnitTotalCount != null">
                OverUnitTotalCount,
            </if>
            <if test="startTime != null">
                StartTime,
            </if>
            <if test="completeTime != null">
                CompleteTime,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lackUnitTotalCount != null">
                LackUnitTotalCount,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="sowTaskNo != null">
                #{sowTaskNo,jdbcType=VARCHAR},
            </if>
            <if test="sowTaskId != null">
                #{sowTaskId,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="productSkuId != null">
                #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="productSpecificationId != null">
                #{productSpecificationId,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="secOwnerId != null">
                #{secOwnerId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="specQuantity != null">
                #{specQuantity,jdbcType=DECIMAL},
            </if>
            <if test="specName != null">
                #{specName,jdbcType=VARCHAR},
            </if>
            <if test="packageCount != null">
                #{packageCount,jdbcType=DECIMAL},
            </if>
            <if test="packageName != null">
                #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="unitCount != null">
                #{unitCount,jdbcType=DECIMAL},
            </if>
            <if test="unitName != null">
                #{unitName,jdbcType=VARCHAR},
            </if>
            <if test="unitTotalCount != null">
                #{unitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="overUnitTotalCount != null">
                #{overUnitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="completeTime != null">
                #{completeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lackUnitTotalCount != null">
                #{lackUnitTotalCount,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO">
        update sowtaskitem
        <set>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="sowTaskNo != null">
                SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR},
            </if>
            <if test="sowTaskId != null">
                SowTask_Id = #{sowTaskId,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="productSkuId != null">
                ProductSku_Id = #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="productSpecificationId != null">
                ProductSpecification_Id = #{productSpecificationId,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                Owner_Id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="secOwnerId != null">
                SecOwner_Id = #{secOwnerId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                ProductName = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="specQuantity != null">
                SpecQuantity = #{specQuantity,jdbcType=DECIMAL},
            </if>
            <if test="specName != null">
                SpecName = #{specName,jdbcType=VARCHAR},
            </if>
            <if test="packageCount != null">
                PackageCount = #{packageCount,jdbcType=DECIMAL},
            </if>
            <if test="packageName != null">
                PackageName = #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="unitCount != null">
                UnitCount = #{unitCount,jdbcType=DECIMAL},
            </if>
            <if test="unitName != null">
                UnitName = #{unitName,jdbcType=VARCHAR},
            </if>
            <if test="unitTotalCount != null">
                UnitTotalCount = #{unitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="overUnitTotalCount != null">
                OverUnitTotalCount = #{overUnitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="startTime != null">
                StartTime = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="completeTime != null">
                CompleteTime = #{completeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lackUnitTotalCount != null">
                LackUnitTotalCount = #{lackUnitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="sorterId != null">
                sorterId = #{sorterId,jdbcType=INTEGER},
            </if>
            <if test="sorterName != null">
                sorterName = #{sorterName,jdbcType=VARCHAR},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <select id="findBySowTaskNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtaskitem
        where SowTask_Id = #{sowTaskId,jdbcType=BIGINT}
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="findBySowTaskNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtaskitem
        where SowTaskNo in
        <foreach collection="sowTaskNos" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="orgId != null">
            and Org_id = #{orgId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="findBySowTaskIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtaskitem
        where SowTask_Id in
        <foreach collection="sowTaskIds" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <update id="completeSowTaskItems">
        update sowtaskitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="State = case" suffix="end,">
                <foreach collection="sowTaskItemPOS" item="item" index="index">
                    when id=#{item.id} then #{item.state,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="OverUnitTotalCount = case" suffix="end,">
                <foreach collection="sowTaskItemPOS" item="item" index="index">
                    when id=#{item.id} then #{item.overUnitTotalCount,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="StartTime = case" suffix="end,">
                <foreach collection="sowTaskItemPOS" item="item" index="index">
                    when id=#{item.id} then #{item.startTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="CompleteTime = case" suffix="end,">
                <foreach collection="sowTaskItemPOS" item="item" index="index">
                    when id=#{item.id} then #{item.completeTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser = case" suffix="end,">
                <foreach collection="sowTaskItemPOS" item="item" index="index">
                    when id=#{item.id} then #{item.lastUpdateUser,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="LackUnitTotalCount = case" suffix="end,">
                <foreach collection="sowTaskItemPOS" item="item" index="index">
                    <if test="item.lackUnitTotalCount != null ">
                        when id=#{item.id} then #{item.lackUnitTotalCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.lackUnitTotalCount == null ">
                        when id=#{item.id} then sowtaskitem.LackUnitTotalCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="sorterId = case" suffix="end,">
                <foreach collection="sowTaskItemPOS" item="item" index="index">
                    <if test="item.sorterId != null ">
                        when id=#{item.id} then #{item.sorterId,jdbcType=INTEGER}
                    </if>
                    <if test="item.sorterId == null ">
                        when id=#{item.id} then sowtaskitem.sorterId
                    </if>
                </foreach>
            </trim>
            <trim prefix="sorterName = case" suffix="end,">
                <foreach collection="sowTaskItemPOS" item="item" index="index">
                    <if test="item.sorterName != null ">
                        when id=#{item.id} then #{item.sorterName,jdbcType=VARCHAR}
                    </if>
                    <if test="item.sorterName == null ">
                        when id=#{item.id} then sowtaskitem.sorterName
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="sowTaskItemPOS" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <delete id="deleteByIds">
        delete from
        sowtaskitem
        where id in
        <foreach collection="sowTaskItemIds" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
        and Org_id = #{orgId,jdbcType=INTEGER}
    </delete>

    <update id="batchUpdateItem">
        update sowtaskitem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="PackageCount =case" suffix="end,">
                <foreach collection="sowTaskItemUpdateDTOS" item="item" index="index">
                    <if test="item.packageCount != null">
                        when id=#{item.sowTaskItemId,jdbcType=BIGINT} then #{item.packageCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.packageCount == null">
                        when id=#{item.sowTaskItemId,jdbcType=BIGINT} then PackageCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="UnitCount =case" suffix="end,">
                <foreach collection="sowTaskItemUpdateDTOS" item="item" index="index">
                    <if test="item.unitCount != null">
                        when id=#{item.sowTaskItemId,jdbcType=BIGINT} then #{item.unitCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.unitCount == null">
                        when id=#{item.sowTaskItemId,jdbcType=BIGINT} then UnitCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="UnitTotalCount =case" suffix="end,">
                <foreach collection="sowTaskItemUpdateDTOS" item="item" index="index">
                    <if test="item.unitTotalCount != null">
                        when id=#{item.sowTaskItemId,jdbcType=BIGINT} then #{item.unitTotalCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.unitTotalCount == null">
                        when id=#{item.sowTaskItemId,jdbcType=BIGINT} then UnitTotalCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="OverUnitTotalCount =case" suffix="end,">
                <foreach collection="sowTaskItemUpdateDTOS" item="item" index="index">
                    <if test="item.overUnitTotalCount != null">
                        when id=#{item.sowTaskItemId,jdbcType=BIGINT} then #{item.overUnitTotalCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.overUnitTotalCount == null">
                        when id=#{item.sowTaskItemId,jdbcType=BIGINT} then OverUnitTotalCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="LackUnitTotalCount =case" suffix="end,">
                <foreach collection="sowTaskItemUpdateDTOS" item="item" index="index">
                    <if test="item.lackUnitTotalCount != null">
                        when id=#{item.sowTaskItemId,jdbcType=BIGINT} then #{item.lackUnitTotalCount,jdbcType=DECIMAL}
                    </if>
                    <if test="item.lackUnitTotalCount == null">
                        when id=#{item.sowTaskItemId,jdbcType=BIGINT} then LackUnitTotalCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="State =case" suffix="end,">
                <foreach collection="sowTaskItemUpdateDTOS" item="item" index="index">
                    <if test="item.state != null">
                        when id=#{item.sowTaskItemId,jdbcType=BIGINT} then #{item.state,jdbcType=TINYINT}
                    </if>
                    <if test="item.state == null">
                        when id=#{item.sowTaskItemId,jdbcType=BIGINT} then `State`
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser = case" suffix="end,">
                <foreach collection="sowTaskItemUpdateDTOS" item="item" index="index">
                    <if test="item.lastUpdateUser != null">
                        when id=#{item.sowTaskItemId,jdbcType=BIGINT} then #{item.lastUpdateUser,jdbcType=VARCHAR}
                    </if>
                    <if test="item.lastUpdateUser == null">
                        when id=#{item.sowTaskItemId,jdbcType=BIGINT} then LastUpdateUser
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="sowTaskItemUpdateDTOS" item="item" separator="," open="(" close=")">
            #{item.sowTaskItemId,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="pageListSowOrderItems"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderItemDTO">
        select
        so.RefOrderNo as orderNo, sti.ProductName as productName, so.SowOrderSequence as sowOrderSequence,
        poi.BoxcodeNo as boxCodeNo, sti.CompleteTime as CompleteTime, osi.PackageCount as sownPackageCount,
        osi.PackageName as packageName, osi.UnitCount as sownUnitCount, osi.UnitName as unitName,
        st.OperatorName as operatorName, osi.LocationName as locationName
        from sowtaskitem sti
        inner join outstockorderitem osi on osi.SowTaskItem_Id = sti.Id and osi.Org_id = sti.Org_Id
        inner join soworder so on so.Outstockorder_Id = osi.Outstockorder_Id and so.Org_Id = osi.Org_Id
        left join packageorderitem poi on poi.RefOrder_ItemId = osi.Id and poi.Org_id = osi.Org_id
        inner join sowtask st on st.Id = sti.SowTask_Id and st.Org_id = sti.Org_Id
        where sti.Org_Id = #{query.orgId,jdbcType=INTEGER}
        and sti.SowTask_Id = #{query.sowTaskId,jdbcType=BIGINT}
        <if test="query.orderNo != null and query.orderNo != ''">
            and so.RefOrderNo like concat('%',#{query.orderNo,jdbcType=VARCHAR},'%')
        </if>
        order by so.SowOrderSequence
    </select>

    <insert id="batchInsert">
        insert into sowtaskitem (
        Id, Org_Id, Warehouse_Id,
        SowTaskNo, SowTask_Id, State,
        ProductSku_Id, ProductSpecification_Id, Owner_Id,
        SecOwner_Id, ProductName, SpecQuantity,
        SpecName, PackageCount, PackageName,
        UnitCount, UnitName, UnitTotalCount,
        OverUnitTotalCount,
        Remark, CreateUser, CreateTime,
        LastUpdateUser, LastUpdateTime, LackUnitTotalCount, SaleSpecQuantity
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT}, #{item.orgId,jdbcType=INTEGER}, #{item.warehouseId,jdbcType=INTEGER},
            #{item.sowTaskNo,jdbcType=VARCHAR}, #{item.sowTaskId,jdbcType=BIGINT}, #{item.state,jdbcType=TINYINT},
            #{item.productSkuId,jdbcType=BIGINT}, #{item.productSpecificationId,jdbcType=BIGINT},
            #{item.ownerId,jdbcType=BIGINT},
            #{item.secOwnerId,jdbcType=BIGINT}, #{item.productName,jdbcType=VARCHAR},
            #{item.specQuantity,jdbcType=DECIMAL},
            #{item.specName,jdbcType=VARCHAR}, #{item.packageCount,jdbcType=DECIMAL},
            #{item.packageName,jdbcType=VARCHAR},
            #{item.unitCount,jdbcType=DECIMAL}, #{item.unitName,jdbcType=VARCHAR},
            #{item.unitTotalCount,jdbcType=DECIMAL},
            #{item.overUnitTotalCount},
            #{item.remark,jdbcType=VARCHAR}, #{item.createUser,jdbcType=VARCHAR}, now(),
            #{item.lastUpdateUser,jdbcType=VARCHAR}, now(),
            #{item.lackUnitTotalCount,jdbcType=DECIMAL},#{item.saleSpecQuantity,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

    <select id="findByOrderItemIds" resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO">
        select
        sti.Id, sti.PackageCount, sti.UnitCount,
        sti.UnitTotalCount, sti.SpecQuantity, osi.Id as orderItemId,
        sti.State, sti.SowTask_Id as sowTaskId, sti.LackUnitTotalCount, sti.ProductSku_Id as productSkuId,
        osi.SaleSpecQuantity as saleSpecQuantity
        from sowtaskitem sti
        inner join outstockorderitem osi on osi.SowTaskItem_Id = sti.Id
        where osi.Id in
        <foreach collection="orderItemIds" item="itemId" separator="," open="(" close=")">
            #{itemId,jdbcType=BIGINT}
        </foreach>
    </select>

    <delete id="deleteBySowTaskIds">
        delete from sowtaskitem
        where SowTask_Id in
        <foreach collection="sowTaskIds" index="index" item="id" separator="," open="(" close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
        and Org_Id = #{orgId,jdbcType=INTEGER}
    </delete>

    <select id="pageListAddressSowTaskItems"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.sowtask.AddressSowTaskItemDTO">
        select
        distinct sti.Id as sowTaskItemId,
        sti.SowTaskNo, sti.Org_Id as orgId, sti.Warehouse_Id as warehouseId,
        sti.ProductSku_Id as productSkuId, sti.ProductName,
        sti.SpecQuantity, sti.SpecName, sti.PackageName, sti.UnitName,
        sti.UnitTotalCount, sti.OverUnitTotalCount, sti.LackUnitTotalCount,
        st.SowTaskName, st.AddressCount,
        st.Location_Id as locationId, st.LocationName,
        st.Operator_Id as operatorId, st.OperatorName, st.State
        from sowtaskitem sti
        inner join sowtask st on sti.SowTaskNo = st.SowTaskNo and sti.Org_Id = st.Org_Id
        inner join outstockorderitem osi on sti.id = osi.SowTaskItem_Id and sti.Org_Id = osi.Org_id
        inner join soworder so on osi.Outstockorder_Id = so.OutStockOrder_Id and osi.Org_id = so.Org_Id
        where sti.Org_Id = #{query.orgId,jdbcType=INTEGER}
        and sti.Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
        <if test="query.addressLocationIds != null and query.addressLocationIds.size() > 0">
            and so.LocationId in
            <foreach collection="query.addressLocationIds" item="addressLocationId" open="(" separator="," close=")">
                #{addressLocationId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="query.productName != null and query.productName != '' ">
            and sti.ProductName like concat('%',#{query.productName,jdbcType=VARCHAR},'%')
        </if>
        <if test="query.states != null and query.states.size() > 0">
            and st.State in
            <foreach collection="query.states" item="state" open="(" separator="," close=")">
                #{state,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="query.operatorId != null">
            and (st.Operator_Id = #{query.operatorId,jdbcType=INTEGER} or st.Operator_Id is null)
        </if>
        <if test="query.skuIds != null and query.skuIds.size() > 0">
            and sti.ProductSku_Id in
            <foreach collection="query.skuIds" item="skuId" open="(" separator="," close=")">
                #{skuId,jdbcType=BIGINT}
            </foreach>
        </if>
        order by st.State desc , sti.Id
    </select>

    <select id="pageListAddressSowTaskItemDetails"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.sowtask.AddressSowTaskItemDTO">
        select distinct so.LocationId as sowLocationId,
        sti.SowTaskNo, sti.Org_Id as orgId, sti.Warehouse_Id as warehouseId,
        sti.Id as sowTaskItemId, sti.ProductSku_Id as productSkuId, sti.ProductName,
        sti.SpecQuantity, sti.SpecName, sti.PackageName, sti.UnitName,
        sti.UnitTotalCount, sti.OverUnitTotalCount, sti.LackUnitTotalCount,
        so.LocationName as sowLocationName, result.sowUnitTotalCount
        from sowtaskitem sti
        inner join (
        select
        so.LocationId as sowLocationId, sum(osi.UnitTotalCount) as sowUnitTotalCount, so.SowOrderSequence,
        osi.sowTaskItem_Id
        from sowtaskitem sti
        inner join outstockorderitem osi on sti.id = osi.SowTaskItem_Id and sti.Org_Id = osi.Org_id
        inner join soworder so on osi.Outstockorder_Id = so.OutStockOrder_Id and osi.Org_id = so.Org_Id
        where sti.Org_Id = #{query.orgId,jdbcType=INTEGER}
        and sti.Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
        and sti.id in
        <foreach collection="query.sowTaskItemIds" item="sowTaskItemId" open="(" close=")" separator=",">
            #{sowTaskItemId,jdbcType=BIGINT}
        </foreach>
        <if test="query.addressLocationIds != null and query.addressLocationIds.size() > 0">
            and so.LocationId in
            <foreach collection="query.addressLocationIds" item="addressLocationId" open="(" separator="," close=")">
                #{addressLocationId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="query.overLocationIds != null and query.overLocationIds.size() > 0">
            and concat(osi.sowTaskItem_Id,'-',so.LocationId) not in
            <foreach collection="query.overLocationIds" item="overLocationId" open="(" separator="," close=")">
                #{overLocationId,jdbcType=BIGINT}
            </foreach>
        </if>
        group by so.LocationId, osi.sowTaskItem_Id, so.SowOrderSequence
        order by so.SowOrderSequence
        ) result on result.sowTaskItem_Id = sti.Id
        inner join outstockorderitem osi on sti.id = osi.SowTaskItem_Id and sti.Org_Id = osi.Org_id
        inner join soworder so on so.LocationId = result.sowLocationId and osi.Outstockorder_Id = so.OutStockOrder_Id
        and so.Org_Id = sti.Org_Id
    </select>

    <select id="findByItemIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sowtaskitem
        where Org_Id = #{orgId,jdbcType=INTEGER}
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findByBatchIds" resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO">
        select
        i.Id as id,
        i.Org_Id as orgId,
        i.Warehouse_Id as warehouseId,
        i.SowTaskNo as sowTaskNo,
        i.SowTask_Id as sowTaskId,
        i.State as state,
        i.ProductSku_Id as productSkuId,
        i.ProductName as productName,
        i.SpecQuantity as specQuantity,
        i.SpecName as specName,
        i.PackageCount as packageCount,
        i.PackageName as packageName,
        i.UnitCount as unitCount,
        i.UnitName as unitName,
        i.UnitTotalCount as unitTotalCount,
        i.OverUnitTotalCount as overUnitTotalCount,
        i.LackUnitTotalCount as lackUnitTotalCount,
        s.Batch_id as batchId
        from sowtaskitem i
        inner join sowtask s on s.id = i.SowTask_Id
        where i.Org_Id = #{orgId,jdbcType=INTEGER}
        and i.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and s.Batch_id in
        <foreach collection="batchIds" item="batchId" open="(" separator="," close=")">
            #{batchId,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="listSowOTaskItems" resultMap="BaseResultMap">
        select
        <include refid="Alias_Column_List"/>,sow.Location_Id,sow.LocationName
        from sowtaskitem item
        inner join sowtask sow on sow.id = item.SowTask_Id
        where item.Org_Id = #{orgId,jdbcType=INTEGER}
        and item.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="sowTaskItemIds != null and sowTaskItemIds.size()>0">
            and item.id in
            <foreach collection="sowTaskItemIds" item="itemId" open="(" separator="," close=")">
                #{itemId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="findSowPackageItems" resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowPackageItemPO">
        select so.RefOrderNo as orderNo,
        so.SowOrderSequence as sowOrderSequence,
        osi.LocationName as toLocationName,
        osi.LocationId as toLocationId,
        poi.BoxcodeNo as boxCodeNo,
        poi.OperatingTime as packageTime,
        poi.PackageCount,
        poi.PackageName,
        poi.UnitCount,
        poi.UnitName,
        poi.OperatorName,
        poi.operatorId,
        poi.SpecQuantity
        from sowtaskitem sti
        inner join outstockorderitem osi on osi.SowTaskItem_Id = sti.Id and osi.Org_id = sti.Org_Id
        inner join soworder so on so.Outstockorder_Id = osi.Outstockorder_Id and so.Org_Id = osi.Org_Id
        inner join packageorderitem poi on poi.RefOrder_ItemId = osi.Id and poi.Org_id = osi.Org_id
        inner join sowtask st on st.Id = sti.SowTask_Id and st.Org_id = sti.Org_Id
        where st.Org_Id = #{query.orgId,jdbcType=INTEGER}
        and st.Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
        and st.SowTaskNo = #{query.sowTaskNo,jdbcType=BIGINT}
        order by so.SowOrderSequence
    </select>
    <select id="querySortingTaskListDetails"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO">
        select sowtaskitem.id,
        sowtaskitem.ProductName,
        sowtaskitem.ProductSku_Id as productSkuId,
        sowtaskitem.UnitTotalCount,
        sowtaskitem.SpecName,
        sowtaskitem.SpecQuantity,
        sowtaskitem.OverUnitTotalCount,
        sowtaskitem.State,
        sowtaskitem.ProductName as productName,
        sowtaskitem.UnitName as unitName,
        sowtaskitem.SowTask_Id as sowTaskId,
        sowtaskitem.SowTaskNo as sowTaskNo,
        sowtaskitem.UnitTotalCount,
        sowtaskitem.PackageName,
        sowtaskitem.SaleSpecQuantity
        from sowtaskitem
        left join sowtask on sowtaskitem.SowTask_Id = sowtask.Id
        where sowtaskitem.Org_Id = #{cityId}
        and sowtask.Location_Id = #{locationId}
        and (sowtaskitem.State = 0
        or (sowtaskitem.SorterId = #{userId} and sowtaskitem.State = 1))
    </select>
    <select id="queryUserSortingTaskListDetails"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO">
        select sowtaskitem.ProductSku_Id as productSkuId,
        sowtaskitem.ProductName as productName,
        sowtaskitem.OverUnitTotalCount as overUnitTotalCount,
        sowtaskitem.UnitTotalCount as unitTotalCount,
        sowtaskitem.LackUnitTotalCount as lackUnitTotalCount,
        sowtaskitem.SpecName as specName,
        sowtaskitem.SpecQuantity as specQuantity,
        sowtaskitem.id as id,
        sowtaskitem.SowTaskNo as sowTaskNo,
        sowtaskitem.SowTask_Id as sowTaskId,
        sowtaskitem.State,
        sowtaskitem.ProductSku_Id as productSkuId,
        sowtaskitem.SaleSpecQuantity
        from sowtaskitem
        left join sowtask on sowtaskitem.SowTask_Id = sowtask.Id
        where sowtaskitem.SorterId = #{userId}
        and sowtaskitem.State
        IN (0, 1)
        and sowtaskitem.Org_id = #{cityId}
        and sowtaskitem.Warehouse_Id = #{warehouseId}
    </select>
    <select id="listSowTaskItemBySowTaskIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO">
        select ProductSku_Id as productSkuId,SaleSpecQuantity,
        ProductName as productName,
        UnitTotalCount as unitTotalCount,
        OverUnitTotalCount as overUnitTotalCount,
        SpecQuantity as specQuantity,
        SpecName as specName,
        SorterName as sortingUserName,
        LackUnitTotalCount as lackUnitTotalCount,
        PackageCount,UnitCount,id,SowTaskNo as sowTaskNo,SowTask_Id as sowTaskId
        from sowtaskitem
        where Org_Id = #{orgId}
        and id in
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>
    <select id="querySowTaskNoByItemId" resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO">
        select id,SowTaskNo,State,SorterName as sortingUserName
        from sowtaskitem where Id in
        <foreach collection="taskIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        and Org_Id =#{cityId};
    </select>
    <select id="queryNotSkuCount" resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO">
        select SowTask_Id as sowTaskId,ProductSku_Id as productSkuId,PackageCount,UnitCount
        from sowtaskitem where SowTask_Id in
        <foreach collection="collect" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        and Org_Id=#{cityId} and State!=2;
    </select>
    <update id="getSortingTask">
        update sowtaskitem
        set LastUpdateTime = now(),LastUpdateUser=#{userId},SorterId=#{userId},SorterName=#{userName},State=1 where
        Org_id=#{cityId} and id
        in
        <foreach collection="taskIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>
    <select id="findSowTaskNoByItemId" resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO">
        select SowTaskNo,State,SorterName as sortingUserName
        from sowtaskitem where Id in
        <foreach collection="taskIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        and Org_Id =#{cityId};
    </select>

    <select id="findBySowTaskItemSkuCount" resultType="java.lang.Integer">
        select count(distinct productsku_id)
        from sowtaskitem
        where sowtask_id = #{sowTaskId}
    </select>


</mapper>