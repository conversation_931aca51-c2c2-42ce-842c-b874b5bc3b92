package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 条件状态
 *
 * <AUTHOR>
 * @date 2019/04/16 17:30
 */
public enum ConditionStateEnum {
    /**
     * 否
     */
    否((byte)0),
    /**
     * 是
     */
    是((byte)1);

    /**
     * type
     */
    private Byte type;

    ConditionStateEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

}
