package com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/6/24
 */
@Service
public class OutStockOrderItemQueryBL {

    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;

    private static final int MAX_SIZE = 250;
    private static final int SUPER_MAX_SIZE = 800;
    protected static final Logger LOG = LoggerFactory.getLogger(OutStockOrderItemQueryBL.class);

    public List<OutStockOrderItemPO> findOutStockOrderItemList(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        int size = ids.size();

        if (size >= SUPER_MAX_SIZE) {
            LOG.warn("超长了， {}", size);
        }
        return Lists.partition(ids, MAX_SIZE).stream()
            .map(orderItemIds -> outStockOrderItemMapper.listByIds(orderItemIds)).filter(CollectionUtils::isNotEmpty)
            .flatMap(Collection::stream).collect(Collectors.toList());
    }

}
