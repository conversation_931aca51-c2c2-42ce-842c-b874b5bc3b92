package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtaskquery;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.dto.config.UserWarehouseAllocation;
import com.yijiupi.himalaya.supplychain.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.service.config.IWarehouseAllocationConfigService;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchOrderInfoQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderInfoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskPickInfoForPDADTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderSearchSO;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/19
 */
@Service
public class BatchTaskSortQueryBL {

    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private GlobalCache globalCache;
    @Autowired
    private BatchOrderInfoBL batchOrderInfoBL;

    @Reference
    private IBatchOrderInfoQueryService iBatchOrderInfoQueryService;
    @Reference
    private IWarehouseAllocationConfigService iWarehouseAllocationConfigService;
    private static final Logger LOG = LoggerFactory.getLogger(BatchTaskSortQueryBL.class);

    private boolean openWarehousePickUpOrdersIndependent(UserWarehouseAllocation userWarehouseAllocation) {

        if (Objects.isNull(userWarehouseAllocation)) {
            return Boolean.FALSE;
        }

        if (Objects.isNull(userWarehouseAllocation.getOpenReceiveOrderBySelf())) {
            return Boolean.FALSE;
        }

        if (ConditionStateEnum.否.getType().equals(userWarehouseAllocation.getOpenReceiveOrderBySelf())) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * 查找拣货任务列表（POA）
     *
     * @param batchTaskSortQueryDTO
     * @return
     */
    public BatchTaskPickInfoForPDADTO findPickBatchTaskList(BatchTaskSortQueryDTO batchTaskSortQueryDTO) {
        AssertUtils.notNull(batchTaskSortQueryDTO.getWarehouseId(), "仓库信息不能为空！");

        List<UserWarehouseAllocation> userWarehouseAllocationsCache =
                globalCache.findUserWarehouseAllocationsCache(batchTaskSortQueryDTO.getWarehouseId(), batchTaskSortQueryDTO.getUserId());

        // 获取当前人员是否开启自主拣货
        boolean isOpenWarehousePickUpOrdersIndependent = false;
        Integer splitWarehouseConfigType = null;
        if (!CollectionUtils.isEmpty(userWarehouseAllocationsCache)) {
            // 获取当前人员是否开启自主拣货
            for (UserWarehouseAllocation userWarehouseAllocation : userWarehouseAllocationsCache) {
                if (openWarehousePickUpOrdersIndependent(userWarehouseAllocation)) {
                    isOpenWarehousePickUpOrdersIndependent = true;
                    break;
                }
            }

            // 属于单分仓属性时
            if (userWarehouseAllocationsCache.size() == 1) {
                splitWarehouseConfigType = userWarehouseAllocationsCache.get(0).getConfigType();
                batchTaskSortQueryDTO.setSplitWarehouseAttr(splitWarehouseConfigType.byteValue());
            }
        }
        LOG.info("当前人员是否开启自主拣货: {}, 单分仓属性: {}", isOpenWarehousePickUpOrdersIndependent, splitWarehouseConfigType);

        PageList<BatchTaskSortDTO> batchTaskSortDTOList = batchOrderTaskBL.findBatchTaskSortList(batchTaskSortQueryDTO);
        if (BooleanUtils.isFalse(isOpenWarehousePickUpOrdersIndependent)) {
            return BatchTaskPickInfoForPDADTO.defaultInstance(batchTaskSortDTOList);
        }

        if (batchTaskSortDTOList.getPager().getRecordCount() != 0) {
            return BatchTaskPickInfoForPDADTO.defaultInstance(batchTaskSortDTOList);
        }

        // :{"wareHouseId":9981,"timeS":"2024-11-18 00:00:00","timeE":"2024-11-18
        // 23:59:59","refOrderNo":"","orderStates":[0],"orderByCreateTime":1,"outBoundType":2,"currentPage":1,"pageSize":100}
        OutStockOrderSearchSO orderSearchSO = new OutStockOrderSearchSO();
        orderSearchSO.setOrderStates(Collections.singletonList(OutStockOrderStateEnum.待调度.getType()));
        orderSearchSO.setOrderByCreateTime((byte)1);
        orderSearchSO.setCurrentPage(batchTaskSortQueryDTO.getPageNum());
        orderSearchSO.setPageSize(batchTaskSortQueryDTO.getPageSize());
        orderSearchSO.setWareHouseId(batchTaskSortQueryDTO.getWarehouseId());
        orderSearchSO.setOutBoundType(OutBoundTypeEnum.SALE_ORDER.getCode());
        orderSearchSO.setTimeS(DateUtils.getMinusMonthFirstDay(3));
        orderSearchSO.setTimeE(DateUtils.getCurrentDateTimeE(1));
        orderSearchSO.setPackageAttribute(getPackageAttribute(batchTaskSortQueryDTO, splitWarehouseConfigType));
        LOG.info("查询参数是: {}", JSON.toJSONString(orderSearchSO));
        PageList<OutStockOrderDTO> outStockOrderList = batchOrderInfoBL.findOutStockOrderList(orderSearchSO);

        return BatchTaskPickInfoForPDADTO.defaultOrderInstance(outStockOrderList);
    }

    private List<Byte> getPackageAttribute(BatchTaskSortQueryDTO batchTaskSortQueryDTO,
        Integer splitWarehouseConfigType) {
        if (Objects.isNull(splitWarehouseConfigType)) {
            return null;
        }

        if (WarehouseAllocationConfigType.REST.getValue().equals(splitWarehouseConfigType)) {
            return Collections.singletonList(OrderFeatureConstant.FEATURE_TYPE_REST);
        }
        if (WarehouseAllocationConfigType.DRINKING.getValue().equals(splitWarehouseConfigType)) {
            return Collections.singletonList(OrderFeatureConstant.FEATURE_TYPE_DANPING);
        }

        return null;
    }

}
