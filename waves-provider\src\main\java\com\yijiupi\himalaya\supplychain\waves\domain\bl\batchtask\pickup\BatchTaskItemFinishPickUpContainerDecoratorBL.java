package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.pickup;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskFinishHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishNeedOpBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemContainerMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemContainerPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemContainerDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/12
 */
@Service
public class BatchTaskItemFinishPickUpContainerDecoratorBL extends BatchTaskItemFinishPickUpBaseDecoratorBL {

    @Autowired
    private BatchTaskItemContainerMapper batchTaskItemContainerMapper;

    @Override
    boolean support(BatchTaskFinishHelperBO batchTaskFinishHelperBO) {
        Byte containerFlag = batchTaskFinishHelperBO.getBatchTaskItemFinishBO().getContainerFlag();
        if (Objects.equals(containerFlag, ConditionStateEnum.是.getType())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    protected void doCompleteBatchTaskItemPickUp(BatchTaskItemFinishNeedOpBO bo,
        BatchTaskFinishHelperBO batchTaskFinishHelperBO, List<BatchTaskItemDTO> needCompleteBatchTaskItemList) {
        Map<String, BatchTaskItemCompleteDTO> map = batchTaskFinishHelperBO.getBatchTaskItemCompleteDTOMap();
        BatchTaskItemFinishBO batchTaskItemFinishBO = batchTaskFinishHelperBO.getBatchTaskItemFinishBO();
        Integer warehouseId = batchTaskFinishHelperBO.getBatchTaskPO().getWarehouseId();
        Integer cityId = batchTaskFinishHelperBO.getBatchPO().getOrgId();
        Integer userId = batchTaskItemFinishBO.getUserId();

        List<BatchTaskItemContainerPO> addContainerPOList = new ArrayList<>();
        List<BatchTaskItemContainerPO> updateContainerPOList = new ArrayList<>();
        List<PickUpDTO> totalPickUpDTO = new ArrayList<>();

        for (BatchTaskItemDTO batchTaskItemDTO : needCompleteBatchTaskItemList) {
            BatchTaskItemCompleteDTO completeDTO = map.get(batchTaskItemDTO.getId());
            if (CollectionUtils.isEmpty(completeDTO.getContainerList())) {
                throw new BusinessException("容器位不能为空");
            }
            List<BatchTaskItemContainerPO> containerList =
                batchTaskItemContainerMapper.selectByBatchtaskitemId(batchTaskItemDTO.getId());
            /*if (TaskStateEnum.已完成.getType() == batchTaskItemDTO.getTaskState()) {
            	containerMap = batchTaskItemContainerMapper.selectByBatchtaskitemId(batchTaskItemDTO.getId());
            }*/
            BigDecimal specQuantity = batchTaskItemDTO.getSpecQuantity();
            Long fromLocationId = BatchTaskFinishHelperBO.getFromLocationId(completeDTO, batchTaskItemDTO);

            for (BatchTaskItemContainerDTO containerDTO : completeDTO.getContainerList()) {
                BigDecimal pickUnitTotalCount =
                    containerDTO.getPickPackageCount().multiply(specQuantity).add(containerDTO.getPickUnitCount());
                BatchTaskItemContainerPO containerPO = new BatchTaskItemContainerPO();
                containerPO.setOrgId(cityId);
                containerPO.setWarehouseId(warehouseId);
                containerPO.setBatchtaskitemId(Long.valueOf(batchTaskItemDTO.getId()));
                containerPO.setLocationId(containerDTO.getLocationId());
                containerPO.setLocationName(containerDTO.getLocationName());
                containerPO.setPickUnitTotalCount(pickUnitTotalCount);
                containerPO.setCreateUserId(userId);
                containerPO.setLastUpdateUserId(userId);
                containerPO.setRemark("PDA");

                // 本次移库小单位数量
                BigDecimal overSortCount = pickUnitTotalCount;
                // 重复拣货时，移库数量 = 本次拣货数量 - 上次拣货数量
                if (containerList != null
                    && containerList.size() > 0/*containerMap.get(containerDTO.getLocationId()) != null*/) {
                    BatchTaskItemContainerPO oldContainerPO = containerList.get(0);
                    fromLocationId = oldContainerPO.getLocationId();
                    containerPO.setId(oldContainerPO.getId());
                    // overSortCount = overSortCount.subtract(oldContainerPO.getPickUnitTotalCount());
                    updateContainerPOList.add(containerPO);
                } else {
                    addContainerPOList.add(containerPO);
                }
                PickUpDTO pickUpDTO = getContainerPickUpDTO(warehouseId, containerDTO.getLocationId(), batchTaskItemDTO,
                    overSortCount, fromLocationId);
                if (null != pickUpDTO) {
                    totalPickUpDTO.add(pickUpDTO);
                }
            }
        }

        bo.setUpdateContainerList(updateContainerPOList);
        bo.setAddContainerList(addContainerPOList);
        bo.intPickUpDTOList(totalPickUpDTO);
    }

    /**
     * 获取拣货移库入参
     *
     * @return
     */
    private PickUpDTO getContainerPickUpDTO(Integer warehouseId, Long locationId, BatchTaskItemDTO batchTaskItemDTO,
        BigDecimal overSortCount, Long fromLocationId) {
        PickUpDTO pickUpDTO = new PickUpDTO();
        BigDecimal pickCount = overSortCount;
        if (pickCount.compareTo(BigDecimal.ZERO) == 0) {
            LOGGER.info("拣货数量为0，不需要移库");
            return null;
        }
        pickUpDTO.setFromLocationId(fromLocationId);
        pickUpDTO.setLocationId(locationId);
        pickUpDTO.setProductSkuId(batchTaskItemDTO.getSkuId());
        pickUpDTO.setWarehouseId(warehouseId);
        pickUpDTO.setFromChannel(batchTaskItemDTO.getChannel().intValue());
        pickUpDTO.setFromSource(batchTaskItemDTO.getSource().intValue());
        pickUpDTO.setToChannel(batchTaskItemDTO.getChannel().intValue());
        pickUpDTO.setToSource(batchTaskItemDTO.getSource().intValue());
        pickUpDTO.setCount(pickCount);
        return pickUpDTO;
    }
}
