package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderFeatureBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.PickingTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/4/7
 */
@Component
public class BatchTaskItemCompleteConvertor {

    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private OrderFeatureBL orderFeatureBL;

    /**
     * SCM-21647 二级仓内配单拣货，同客户订单一起完成拣货
     *
     * @param batchTaskItemCompleteDTOList
     * @param batchTaskPO
     * @return
     */
    public List<BatchTaskItemCompleteDTO> resetBatchTaskItemCompleteDTO(
            List<BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOList, BatchTaskPO batchTaskPO) {
        if (PickingTypeEnum.订单拣货.getType() != batchTaskPO.getPickingType()) {
            return batchTaskItemCompleteDTOList;
        }

        if (batchTaskPO.getOrderCount() <= 1) {
            return batchTaskItemCompleteDTOList;
        }

        List<String> updateBatchTaskItemIds =
                batchTaskItemCompleteDTOList.stream().map(BatchTaskItemCompleteDTO::getId).collect(Collectors.toList());

        Integer orgId = Integer.parseInt(batchTaskPO.getOrgId());

        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
                orderItemTaskInfoMapper.listTaskInfoAndDetailByBatchTaskItemIds(updateBatchTaskItemIds);
        Long outStockOrderId =
                orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderId).distinct().findFirst().get();

        OutStockOrderPO outStockOrderPO = getOutStockOrderPO(outStockOrderId);

        if (BooleanUtils.isFalse(validateOrder(outStockOrderPO))) {
            return batchTaskItemCompleteDTOList;
        }

        Integer addressId = outStockOrderPO.getAddressId();

        List<Long> orderIds =
                batchTaskMapper.findBatchTaskOneUserOrderIds(orgId, outStockOrderPO.getAddressId(), batchTaskPO.getId());
        if (orderIds.size() == 1) {
            return batchTaskItemCompleteDTOList;
        }

        List<OutStockOrderPO> totalOutStockOrderPOList = outStockOrderMapper.selectByIds(orderIds);

        List<OutStockOrderPO> frontOrderList = getNpFrontWarehouseOrderList(orderIds);
        if (CollectionUtils.isEmpty(frontOrderList)) {
            return batchTaskItemCompleteDTOList;
        }

        if (frontOrderList.size() == 1) {
            return batchTaskItemCompleteDTOList;
        }

        List<OutStockOrderPO> frontRestOrderList = getRestOrderList(frontOrderList);
        if (CollectionUtils.isEmpty(frontRestOrderList)) {
            return batchTaskItemCompleteDTOList;
        }

        orderIds = frontRestOrderList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());

        List<Long> outStockOrderItemIds =
                outStockOrderPO.getItems().stream().map(OutStockOrderItemPO::getId).distinct().collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOS =
                orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderItemIds(outStockOrderItemIds);
        List<String> batchTaskItemIds = orderItemTaskInfoPOS.stream().map(OrderItemTaskInfoPO::getBatchTaskItemId)
                .distinct().collect(Collectors.toList());

        boolean isAllItemInOrderHasFinished =
                isAllItemInOrderHasFinished(batchTaskItemIds, batchTaskPO, batchTaskItemCompleteDTOList);
        if (BooleanUtils.isFalse(isAllItemInOrderHasFinished)) {
            return batchTaskItemCompleteDTOList;
        }

        orderIds.removeIf(m -> m.equals(outStockOrderPO.getId()));

        List<OrderItemTaskInfoPO> sameAddressOrderItemTaskInfoList =
                orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(orderIds);
        sameAddressOrderItemTaskInfoList = sameAddressOrderItemTaskInfoList.stream()
                .filter(m -> m.getBatchTaskId().equals(batchTaskPO.getId())).collect(Collectors.toList());

        List<String> sameAddressBatchTaskItemIds = sameAddressOrderItemTaskInfoList.stream()
                .filter(m -> m.getBatchTaskId().equals(batchTaskPO.getId())).map(OrderItemTaskInfoPO::getBatchTaskItemId)
                .filter(itemId -> !batchTaskItemIds.contains(itemId)).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(sameAddressBatchTaskItemIds)) {
            return batchTaskItemCompleteDTOList;
        }

        BatchTaskItemCompleteDTO firstBatchTaskItemCompleteDTO =
                batchTaskItemCompleteDTOList.stream().findFirst().get();

        List<BatchTaskItemPO> sameAddressBatchTaskItemList =
                batchTaskItemMapper.findBatchTaskItemByIds(sameAddressBatchTaskItemIds);

        sameAddressBatchTaskItemList = sameAddressBatchTaskItemList.stream()
                .filter(m -> TaskStateEnum.已完成.getType() != m.getTaskState()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sameAddressBatchTaskItemList)) {
            return batchTaskItemCompleteDTOList;
        }

        List<BatchTaskItemCompleteDTO> sameAddressBatchTaskCompleteDTOList =
                sameAddressBatchTaskItemList.stream().map(batchTaskItem -> {
                    BatchTaskItemCompleteDTO batchTaskItemCompleteDTO = new BatchTaskItemCompleteDTO();
                    batchTaskItemCompleteDTO.setFromLocationId(firstBatchTaskItemCompleteDTO.getFromLocationId());
                    batchTaskItemCompleteDTO.setFromLocationName(firstBatchTaskItemCompleteDTO.getFromLocationName());
                    batchTaskItemCompleteDTO.setId(batchTaskItem.getId());
                    batchTaskItemCompleteDTO.setLackPackageCount(BigDecimal.ZERO);
                    batchTaskItemCompleteDTO.setLackUnitCount(BigDecimal.ZERO);
                    batchTaskItemCompleteDTO.setOrderSequence(null);
                    batchTaskItemCompleteDTO.setOverSortPackageCount(batchTaskItem.getPackageCount());
                    batchTaskItemCompleteDTO.setOverSortUnitCount(batchTaskItem.getUnitCount());
                    batchTaskItemCompleteDTO.setSubmitFlag((byte) 0);

                    return batchTaskItemCompleteDTO;
                }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(sameAddressBatchTaskCompleteDTOList)) {
            batchTaskItemCompleteDTOList.addAll(sameAddressBatchTaskCompleteDTOList);
        }

        return batchTaskItemCompleteDTOList;
    }

    /**
     * 是否是休食订单 和 是否是 前置仓内配单
     *
     * @param outStockOrderPO
     * @return
     */
    private boolean validateOrder(OutStockOrderPO outStockOrderPO) {
        if (BooleanUtils.isFalse(isFrontWarehouseNpOrder(outStockOrderPO))) {
            return Boolean.FALSE;
        }

        List<OutStockOrderPO> restOrder = getRestOrderList(Collections.singletonList(outStockOrderPO));

        if (CollectionUtils.isEmpty(restOrder)) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    private List<OutStockOrderPO> getNpFrontWarehouseOrderList(List<Long> orderIds) {
        List<OutStockOrderPO> totalOutStockOrderPOList = outStockOrderMapper.selectByIds(orderIds);
        if (CollectionUtils.isEmpty(totalOutStockOrderPOList)) {
            return Collections.emptyList();
        }

        // 前置仓内配单
        return totalOutStockOrderPOList.stream().filter(m -> isFrontWarehouseNpOrder(m)).collect(Collectors.toList());
    }

    private List<OutStockOrderPO> getRestOrderList(List<OutStockOrderPO> outStockOrderPOList) {
        List<Long> orderIds =
                outStockOrderPOList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
        Map<Long, List<Byte>> orderFeatureMap = orderFeatureBL.getOrderFeatureMap(orderIds);

        return outStockOrderPOList.stream().filter(m -> {
            List<Byte> featureList = orderFeatureMap.get(m.getId());
            if (CollectionUtils.isEmpty(featureList)) {
                return Boolean.FALSE;
            }

            return featureList.contains(OrderFeatureConstant.FEATURE_TYPE_REST);
        }).collect(Collectors.toList());
    }

    /**
     * 是否是前置仓内配单
     *
     * @param outStockOrderPO
     * @return
     */
    private boolean isFrontWarehouseNpOrder(OutStockOrderPO outStockOrderPO) {
        if (!OrderConstant.ALLOT_TYPE_ALLOCATION.equals(outStockOrderPO.getAllotType())) {
            return Boolean.FALSE;
        }

        if (OutBoundTypeEnum.SALE_ORDER.getCode() != outStockOrderPO.getOutBoundType().intValue()) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * @param batchTaskItemIds
     * @param batchTaskPO
     * @param batchTaskItemCompleteDTOList
     * @return
     */
    private boolean isAllItemInOrderHasFinished(List<String> batchTaskItemIds, BatchTaskPO batchTaskPO,
                                                List<BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOList) {
        List<BatchTaskItemPO> batchTaskItemPOS =
                batchTaskItemMapper.listBatchTaskItemByIds(batchTaskItemIds, batchTaskPO.getId());

        List<String> updateBatchTaskItemIds =
                batchTaskItemCompleteDTOList.stream().map(BatchTaskItemCompleteDTO::getId).collect(Collectors.toList());

        boolean allMatch = batchTaskItemPOS.stream().filter(item -> !updateBatchTaskItemIds.contains(item.getId()))
                .allMatch(m -> TaskStateEnum.已完成.getType() == m.getTaskState());

        if (allMatch) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    private OutStockOrderPO getOutStockOrderPO(Long outStockOrderId) {
        List<OutStockOrderPO> outStockOrderPOList =
                outStockOrderMapper.findByOrderIdWithoutCon(Collections.singletonList(outStockOrderId.toString()));
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            throw new BusinessValidateException("出库单不存在！");
        }

        return outStockOrderPOList.stream().findFirst().get();
    }
}