package com.yijiupi.himalaya.supplychain.waves.enums;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/**
 * 播种任务状态枚举
 *
 * <AUTHOR>
 * @since 2018/3/13
 */
public enum SowTaskStateEnum {
    /**
     * 枚举
     */
    待播种((byte)0), 播种中((byte)1), 已播种((byte)2), 待集货((byte)3), 已取消((byte)4);

    /**
     * type
     */
    private final byte type;

    SowTaskStateEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     */
    public static String getEnumByValue(Byte value) {
        if (ObjectUtils.isEmpty(value)) {
            return StringUtils.EMPTY;
        }
        for (SowTaskStateEnum taskStateEnum : SowTaskStateEnum.values()) {
            if (value.compareTo(taskStateEnum.type) == 0) {
                return taskStateEnum.name();
            }
        }
        return StringUtils.EMPTY;
    }
}
