package com.yijiupi.himalaya.supplychain.waves.util;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.WavesApp;

/**
 * Created by 余明 on 2018-04-11.
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class UuidUtilTest {

    @Test
    public void beforeInit() {}

    @Test
    public void generatorByCityId() {}

    @Test
    public void generatorByType() {}

    @Test
    public void generator() {}

    @Test
    public void generatorNew() {
        String strId1 = UuidUtil.generator(1001, "AA");
        String strId2 = UuidUtil.generator(1002, "AA");
        String strId3 = UuidUtil.generator(1001, "AA");
        String strId4 = UuidUtil.generator(1002, "AA");
        String strId5 = UuidUtil.generator(1001, "BB");
        String strId6 = UuidUtil.generator(1002, "BB");
        String strId7 = UuidUtil.generator(1001, "CC");
        String strId8 = UuidUtil.generator(1002, "CC");
    }

    @Test
    public void generatorId() {}
}