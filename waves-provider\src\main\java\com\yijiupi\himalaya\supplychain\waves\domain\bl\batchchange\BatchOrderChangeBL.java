package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrder;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskChangeNotifyBL;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.OutStockOrderConverter;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchUpdateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.IStoreTransferOrderService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderState;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo.OutStockOrderAdminCancelHandlePickBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo.OutStockOrderAdminCancelHandlePickResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo.OutStockOrderAdminCancelHandlePickTransferResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.ConfirmReturnDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskItemUpdateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskUpdateDTO;

/**
 * <AUTHOR>
 * @since 2023-05-24 11:42
 */
@Service
@SuppressWarnings("NonAsciiCharacters")
public class BatchOrderChangeBL {

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private SowTaskMapper sowTaskMapper;
    @Autowired
    private SowTaskItemMapper sowTaskItemMapper;
    @Autowired
    private SowOrderMapper sowOrderMapper;
    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private OutStockOrderItemChangeRecordMapper outStockOrderItemChangeRecordMapper;
    @Autowired
    private OrderItemTaskInfoDetailMapper orderItemTaskInfoDetailMapper;
    @Autowired
    private List<OutStockOrderAdminCancelBaseBL> cancelList;
    @Autowired
    private OutStockOrderHasPickedCancelBL outStockOrderHasPickedCancelBL;
    @Autowired
    private OutStockOrderPickingCancelBL outStockOrderPickingCancelBL;
    @Autowired
    private OutStockOrderWaitPickCancelBL outStockOrderWaitPickCancelBL;
    @Autowired
    private BatchTaskChangeNotifyBL batchTaskChangeNotifyBL;

    @Resource
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Reference
    private IStoreTransferOrderService iStoreTransferOrderService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    private static final Logger LOGGER = LoggerFactory.getLogger(BatchOrderChangeBL.class);

    /**
     * 取消出库单处理波次信息
     *
     * @param cancelOutStockOrderDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void cancelOutStockOrderHandleBatchInfo(CancelOutStockOrderDTO cancelOutStockOrderDTO) {
        Function<OutStockOrderAdminCancelHandlePickBO, OutStockOrderAdminCancelHandlePickResultBO> function = bo -> {
            if (outStockOrderWaitPickCancelBL.support(bo)) {
                return outStockOrderWaitPickCancelBL.cancel(bo);
            }
            return null;
        };
        cancelOutStockOrder(cancelOutStockOrderDTO, function);
    }

    /**
     * 处理异常单处理波次信息
     *
     * @param cancelOutStockOrderDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void exceptionOrderHandleBatchInfo(CancelOutStockOrderDTO cancelOutStockOrderDTO) {
        Function<OutStockOrderAdminCancelHandlePickBO, OutStockOrderAdminCancelHandlePickResultBO> function = bo -> {
            if (outStockOrderHasPickedCancelBL.support(bo)) {
                return outStockOrderHasPickedCancelBL.cancel(bo);
            }
            return null;
        };
        cancelOutStockOrder(cancelOutStockOrderDTO, function);
    }

    private void cancelOutStockOrder(CancelOutStockOrderDTO cancelOutStockOrderDTO,
        Function<OutStockOrderAdminCancelHandlePickBO, OutStockOrderAdminCancelHandlePickResultBO> function) {
        AssertUtils.notEmpty(cancelOutStockOrderDTO.getProcessChangeDTOS(), "出库单信息不能为空！");
        AssertUtils.notNull(function, "处理方式不能为空！");
        LOGGER.info("取消出库单生成的波次和拣货任务信息,入参:{}", JSON.toJSONString(cancelOutStockOrderDTO));
        List<OutStockOrderProcessChangeDTO> processChangeDTOS = cancelOutStockOrderDTO.getProcessChangeDTOS();
        Map<Integer, List<OutStockOrderProcessChangeDTO>> orgIdGroupList =
            processChangeDTOS.stream().collect(Collectors.groupingBy(OutStockOrderProcessChangeDTO::getCityId));
        for (Map.Entry<Integer, List<OutStockOrderProcessChangeDTO>> entry : orgIdGroupList.entrySet()) {
            // 获取当前城市下订单
            List<OutStockOrderProcessChangeDTO> outStockOrderProcessChangeDTOS = orgIdGroupList.get(entry.getKey());
            if (CollectionUtils.isEmpty(outStockOrderProcessChangeDTOS)) {
                LOGGER.warn("订单信息为空");
                continue;
            }
            List<Long> outStockOrderIds = outStockOrderProcessChangeDTOS.stream()
                .map(OutStockOrderProcessChangeDTO::getId).collect(Collectors.toList());
            // 根据城市 + refOrderNo查询波次信息
            List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findByOrderIds(outStockOrderIds);
            if (CollectionUtils.isEmpty(outStockOrderPOList)) {
                LOGGER.warn("查询出库单信息为空 {}", JSON.toJSONString(processChangeDTOS));
                continue;
            }
            // outStockOrderPOList = outStockOrderPOList.stream().filter(m -> OutStockOrderStateEnum.待拣货.getType() ==
            // m.getState()).collect(Collectors.toList());
            List<Long> orderItemIds = processChangeDTOS.stream().flatMap(p -> p.getItemList().stream())
                .map(OutStockOrderItemProcessChangeDTO::getId).distinct().collect(Collectors.toList());
            List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
                orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(orderItemIds);
            Map<Long, List<OrderItemTaskInfoPO>> infoMap =
                orderItemTaskInfoPOList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderId));
            List<String> batchNoList = outStockOrderPOList.stream().map(OutStockOrderPO::getBatchno)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            Map<String, List<BatchPO>> batchMap;
            if (CollectionUtils.isNotEmpty(batchNoList)) {
                batchMap = batchMapper.listByBatchNos(batchNoList).stream()
                    .collect(Collectors.groupingBy(BatchPO::getBatchNo));
            } else {
                batchMap = new HashMap<>();
            }

            List<BatchTaskPO> oriBatchTaskPOList = findOriBatchTaskList(orderItemTaskInfoPOList);

            List<OutStockOrderAdminCancelHandlePickResultBO> resultList = outStockOrderPOList.stream()
                .map(order -> new OutStockOrderAdminCancelHandlePickBO(order, infoMap, batchMap)).map(function)
                .filter(Objects::nonNull).collect(Collectors.toList());
            handleData(resultList, entry.getKey());

            notifyOrderCanceled(oriBatchTaskPOList, cancelOutStockOrderDTO);
        }
    }

    private OutStockOrderAdminCancelHandlePickResultBO doCancel(OutStockOrderAdminCancelHandlePickBO handlePickBO) {
        OutStockOrderAdminCancelHandlePickResultBO result = cancelList.stream().filter(m -> m.baseSupport(handlePickBO))
            .findAny().map(it -> it.cancel(handlePickBO)).orElse(null);
        if (result == null) {
            LOGGER.warn("没有支持的策略: {}", JSON.toJSONString(handlePickBO));
        }
        return result;
    }

    private void handleData(List<OutStockOrderAdminCancelHandlePickResultBO> resultList, Integer orgId) {
        List<BatchTaskPO> updateBatchTaskList =
            resultList.stream().map(OutStockOrderAdminCancelHandlePickResultBO::getUpdateBatchTaskList)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        List<String> deleteBatchTaskIds =
            resultList.stream().map(OutStockOrderAdminCancelHandlePickResultBO::getDeleteBatchTaskIds)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        List<BatchTaskItemUpdatePO> updateBatchTaskItemList =
            resultList.stream().map(OutStockOrderAdminCancelHandlePickResultBO::getUpdateBatchTaskItemList)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        List<String> deleteBatchTaskItemIds =
            resultList.stream().map(OutStockOrderAdminCancelHandlePickResultBO::getDeleteBatchTaskItemIds)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        List<SowTaskItemUpdateDTO> sowTaskItemUpdateDTOS =
            resultList.stream().map(OutStockOrderAdminCancelHandlePickResultBO::getSowTaskItemUpdateDTOS)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        List<Long> deleteSowTaskItemIds =
            resultList.stream().map(OutStockOrderAdminCancelHandlePickResultBO::getDeleteSowTaskItemIds)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        List<SowTaskUpdateDTO> updateSowTaskList =
            resultList.stream().map(OutStockOrderAdminCancelHandlePickResultBO::getUpdateSowTaskList)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        List<Long> deleteSowTaskIds =
            resultList.stream().map(OutStockOrderAdminCancelHandlePickResultBO::getDeleteSowTaskIds)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        List<Long> deleteSowOrderIds =
            resultList.stream().map(OutStockOrderAdminCancelHandlePickResultBO::getDeleteSowOrderIds)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        List<BatchPO> updateBatchList =
            resultList.stream().map(OutStockOrderAdminCancelHandlePickResultBO::getUpdateBatchList)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        List<String> deleteBatchIds =
            resultList.stream().map(OutStockOrderAdminCancelHandlePickResultBO::getDeleteBatchIds)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        List<Long> deleteOrderItemTaskInfoIds =
            resultList.stream().map(OutStockOrderAdminCancelHandlePickResultBO::getDeleteOrderItemTaskInfoIds)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        List<Long> deleteOrderItemTaskInfoDetailIds =
            resultList.stream().map(OutStockOrderAdminCancelHandlePickResultBO::getDeleteOrderItemTaskInfoDetailIds)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateBatchTaskItemList)) {
            batchTaskItemMapper.updateBatchTaskByItem(updateBatchTaskItemList);
        }
        if (CollectionUtils.isNotEmpty(deleteBatchTaskItemIds)) {
            batchTaskItemMapper.deleteByBatchTaskItemId(deleteBatchTaskItemIds);
        }
        if (CollectionUtils.isNotEmpty(updateBatchTaskList)) {
            // TODO 改成批量
            updateBatchTaskList.forEach(batchTask -> batchTaskMapper.updateByPrimaryKeySelective(batchTask));
        }
        if (CollectionUtils.isNotEmpty(deleteBatchTaskIds)) {
            batchTaskMapper.deleteByBatchTaskId(deleteBatchTaskIds);
        }
        if (CollectionUtils.isNotEmpty(sowTaskItemUpdateDTOS)) {
            sowTaskItemMapper.batchUpdateItem(sowTaskItemUpdateDTOS);
        }
        if (CollectionUtils.isNotEmpty(deleteSowTaskItemIds)) {
            sowTaskItemMapper.deleteByIds(deleteSowTaskItemIds, orgId);
        }
        if (CollectionUtils.isNotEmpty(updateSowTaskList)) {
            sowTaskMapper.batchUpdateCount(updateSowTaskList);
        }
        if (CollectionUtils.isNotEmpty(deleteSowTaskIds)) {
            sowTaskMapper.deleteBySowTaskIds(deleteSowTaskIds, orgId);
        }
        if (CollectionUtils.isNotEmpty(deleteSowOrderIds)) {
            sowOrderMapper.deleteByIds(deleteSowOrderIds, orgId);
        }
        if (CollectionUtils.isNotEmpty(updateBatchList)) {
            updateBatchList.forEach(m -> batchMapper.updateBatchInfo(m));
        }
        if (CollectionUtils.isNotEmpty(deleteBatchIds)) {
            batchMapper.deleteByBatchIds(deleteBatchIds);
        }
        if (CollectionUtils.isNotEmpty(deleteOrderItemTaskInfoIds)) {
            orderItemTaskInfoMapper.deleteByIds(deleteOrderItemTaskInfoIds);
        }
        if (CollectionUtils.isNotEmpty(deleteOrderItemTaskInfoDetailIds)) {
            orderItemTaskInfoDetailMapper.deleteByIds(deleteOrderItemTaskInfoDetailIds);
        }
        List<OutStockOrderAdminCancelHandlePickTransferResultBO> transferResultList =
            resultList.stream().filter(m -> m instanceof OutStockOrderAdminCancelHandlePickTransferResultBO)
                .map(m -> (OutStockOrderAdminCancelHandlePickTransferResultBO)m).collect(Collectors.toList());
        handleTransferOrder(transferResultList);
        // TODO 推送消息给分拣员
    }

    private void handleTransferOrder(List<OutStockOrderAdminCancelHandlePickTransferResultBO> transferResultList) {
        if (CollectionUtils.isEmpty(transferResultList)) {
            return;
        }
        List<OutStockOrderAdminCancelHandlePickTransferResultBO> transferList = transferResultList.stream()
            .filter(m -> !org.springframework.util.CollectionUtils.isEmpty(m.getStoreTransferOrderDTO()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(transferList)) {
            return;
        }
        List<Integer> warehouseIds =
            transferList.stream().map(m -> m.getStoreTransferOrderDTO().values()).flatMap(Collection::stream)
                .map(StoreTransferOrderDTO::getWarehouse_Id).distinct().collect(Collectors.toList());
        List<Warehouse> warehouseList = iWarehouseQueryService.listWarehouseByIds(warehouseIds);
        Map<Integer, Warehouse> warehouseMap =
            warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
        for (OutStockOrderAdminCancelHandlePickTransferResultBO resultBO : transferList) {
            Map<Long, StoreTransferOrderDTO> storeTransferNoMap = new HashMap<>();
            try {
                resultBO.getStoreTransferOrderDTO().forEach((orderId, storeTransferOrderDTO) -> {
                    Warehouse warehouse = warehouseMap.get(storeTransferOrderDTO.getWarehouse_Id());
                    storeTransferOrderDTO.setWarehouseName(warehouse != null ? warehouse.getName() : null);
                    LOGGER.info("[订单变更]移库单：{}", JSON.toJSONString(storeTransferOrderDTO));
                    StoreTransferOrderDTO returnTransferOrderDTO =
                        iStoreTransferOrderService.updateStoreTranferNoOrder(storeTransferOrderDTO);
                    // 记录订单对应的移库单号
                    storeTransferNoMap.put(orderId, returnTransferOrderDTO);
                });
            } catch (Exception e) {
                LOGGER.error("[订单变更]移库异常", e);
            }
            // 7、保存订单项变更记录
            if (CollectionUtils.isNotEmpty(resultBO.getRecordList())) {
                // 需要移库的记录移库单ID和编号
                resultBO.getRecordList().forEach(changeDTO -> {
                    if (changeDTO.getMoveFlag()) {
                        StoreTransferOrderDTO transferOrderDTO = storeTransferNoMap.get(changeDTO.getRefOrderId());
                        if (transferOrderDTO != null) {
                            changeDTO.setTransferOrderId(transferOrderDTO.getId());
                            changeDTO.setTransferOrderNo(transferOrderDTO.getStoreTransferNo());
                        }
                    }
                });
                LOGGER.info("[订单变更]保存订单项变更记录：{}", JSON.toJSONString(resultBO.getRecordList()));
                outStockOrderItemChangeRecordMapper.insertBatch(resultBO.getRecordList());
            }
        }
    }

    /**
     * 已拣货的取消订单生成移库单
     */
    public void cancelOrderCreateTransferOrder(CancelOrderCreateTransferOrderDTO dto) {
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findByOrderIds(dto.getOutStockOrderIds());
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            throw new BusinessValidateException("出库单不存在！");
        }
        outStockOrderPOList = outStockOrderPOList.stream()
            .filter(m -> OutStockOrderStateEnum.已出库.getType() != m.getState()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }
        List<Long> orderItemIds = outStockOrderPOList.stream().flatMap(m -> m.getItems().stream())
            .map(OutStockOrderItemPO::getId).distinct().collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(orderItemIds);
        Map<Long, List<OrderItemTaskInfoPO>> orderItemTaskInfoGroupMap =
            orderItemTaskInfoPOList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderId));
        List<OutStockOrderAdminCancelHandlePickTransferResultBO> resultBOList = outStockOrderPOList.stream()
            .map(m -> new OutStockOrderAdminCancelHandlePickBO(m, orderItemTaskInfoGroupMap.get(m.getId())))
            .map(outStockOrderHasPickedCancelBL::doCancel).filter(Objects::nonNull)
            .map(m -> (OutStockOrderAdminCancelHandlePickTransferResultBO)m).collect(Collectors.toList());
        handleTransferOrder(resultBOList);
    }

    public List<ConfirmReturnDTO> orderItemDetail(String businessId, Integer warehouseId) {
        List<OutStockOrderPO> orders =
            outStockOrderMapper.findByBusinessIds(Collections.singletonList(businessId), warehouseId);
        // 过滤掉待拣货的出库单
        List<Long> outStockOrderIds =
            orders.stream().filter(it -> !(OutStockOrderState.待拣货 == it.getState().byteValue()))
                .map(OutStockOrderPO::getId).collect(Collectors.toList());
        if (outStockOrderIds.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Long, OutStockOrderItemPO> orderItemMap =
            outStockOrderItemMapper.findByOutstockorderIdList(outStockOrderIds).stream()
                .collect(Collectors.toMap(OutStockOrderItemPO::getId, Function.identity()));
        List<Long> orderItemIds = new ArrayList<>(orderItemMap.keySet());
        List<OrderItemTaskInfoPO> taskInfos = orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderItemIds(orderItemIds);
        if (CollectionUtils.isEmpty(taskInfos)) {
            return Collections.emptyList();
        }
        List<String> batchTaskIds =
            taskInfos.stream().map(OrderItemTaskInfoPO::getBatchTaskId).collect(Collectors.toList());
        List<String> batchTaskItemIds =
            taskInfos.stream().map(OrderItemTaskInfoPO::getBatchTaskItemId).collect(Collectors.toList());
        Map<String, BatchTaskPO> batchTaskMap = batchTaskMapper.findBatchTaskByIds(batchTaskIds).stream()
            .collect(Collectors.toMap(BatchTaskPO::getId, Function.identity()));
        Map<String, BatchTaskItemPO> batchTaskItemMap = batchTaskItemMapper.findBatchTaskItemByIds(batchTaskItemIds)
            .stream().collect(Collectors.toMap(BatchTaskItemPO::getId, Function.identity()));
        return taskInfos.stream().map(it -> {
            BatchTaskPO batchTask = batchTaskMap.get(it.getBatchTaskId());
            BatchTaskItemPO taskItem = batchTaskItemMap.get(it.getBatchTaskItemId());
            OutStockOrderItemPO orderItem = orderItemMap.get(it.getRefOrderItemId());
            ConfirmReturnDTO dto = new ConfirmReturnDTO();
            dto.setPackageCount(orderItem.getPackagecount());
            dto.setPackageName(orderItem.getPackagename());
            dto.setUnitCount(orderItem.getUnitcount());
            dto.setUnitName(orderItem.getUnitname());
            dto.setProductName(orderItem.getProductname());
            dto.setSpecName(orderItem.getSpecname());
            dto.setFromLocationName(taskItem.getLocationName());
            Long sowTaskId = batchTask.getSowTaskId();
            if (sowTaskId != null) {
                SowTaskPO sowTask = sowTaskMapper.getSowTaskById(sowTaskId, Integer.valueOf(batchTask.getOrgId()));
                if (Objects.nonNull(sowTask)) {
                    dto.setToLocationName(sowTask.getLocationName());
                }
            } else {
                dto.setToLocationName(batchTask.getToLocationName());
            }
            return dto;
        }).collect(Collectors.toList());
    }

    public void updateBatchTaskItemSkuId(List<BatchTaskItemDTO> batchTaskItemDTOList) {
        if (CollectionUtils.isEmpty(batchTaskItemDTOList)) {
            return;
        }
        batchTaskItemDTOList.forEach(m -> {
            BatchTaskItemPO batchTaskItemPO = new BatchTaskItemPO();
            batchTaskItemPO.setId(m.getId());
            batchTaskItemPO.setSkuId(m.getSkuId());
            batchTaskItemMapper.updateBatchTaskItem(batchTaskItemPO);
        });
    }

    public void checkBatchOrderState(String batchNo, Integer orgId, Integer b) {
        boolean allMatch = outStockOrderMapper.findByBatchNos(Collections.singletonList(batchNo), orgId).stream()
            .allMatch(it -> b.equals(it.getState()));
        AssertUtils.isTrue(allMatch);
    }

    public List<OutStockOrderDTO> selectByBatchTaskId(String batchTaskId, Integer orgId) {
        Set<Long> orderIds = orderItemTaskInfoMapper.listTaskInfoByBatchTaskIds(Collections.singletonList(batchTaskId))
            .stream().map(OrderItemTaskInfoPO::getRefOrderId).collect(Collectors.toSet());
        return outStockOrderMapper.findByOrderIds(orderIds).stream().map(OutStockOrderConverter::toDTO)
            .collect(Collectors.toList());
    }

    public void updateBatchTaskPallet(String batchTaskId, String toPalletNo) {
        batchTaskMapper.updatePalletById(batchTaskId, toPalletNo);
    }

    /**
     * 更新波次状态
     *
     * @param batchUpdateDTO
     */
    public void updateBatchInfo(BatchUpdateDTO batchUpdateDTO) {
        List<BatchPO> batchList = batchMapper.findBatchByNos(batchUpdateDTO.getOrderNos(), batchUpdateDTO.getOrgId());
        if (CollectionUtils.isEmpty(batchList)) {
            return;
        }
        batchMapper.updateBatchState(batchList.stream().map(BatchPO::getId).collect(Collectors.toList()),
            batchUpdateDTO.getBatchState(), batchUpdateDTO.getOrgId());
    }

    private void notifyOrderCanceled(List<BatchTaskPO> oriBatchTaskPOList,
        CancelOutStockOrderDTO cancelOutStockOrderDTO) {
        if (CollectionUtils.isEmpty(oriBatchTaskPOList)) {
            return;
        }

        if (CollectionUtils.isEmpty(cancelOutStockOrderDTO.getProcessChangeDTOS())) {
            return;
        }
        batchTaskChangeNotifyBL.notifyOrderCanceled(oriBatchTaskPOList,
            cancelOutStockOrderDTO.getProcessChangeDTOS().get(0).getWarehouseId());
    }

    private List<BatchTaskPO> findOriBatchTaskList(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return Collections.emptyList();
        }
        List<String> batchTaskNoList = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getBatchTaskNo)
            .distinct().collect(Collectors.toList());

        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findTasksByBatchTaskNo(batchTaskNoList);

        return batchTaskPOList;

    }
}
