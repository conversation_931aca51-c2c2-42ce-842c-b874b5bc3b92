package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.waves.domain.model.OutStockOrderItemQuerySO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;

@Mapper
public interface OutStockOrderItemMapper {

    int insertList(@Param("outStockOrderItemPOs") List<OutStockOrderItemNewPO> outStockOrderItemPOs);

    int update(@Param("outStockOrderItemPO") OutStockOrderItemPO outStockOrderItemPO);

    /**
     * 更新库位
     * 
     * @param outStockOrderItemPO
     * @return
     */
    int updateLocation(@Param("outStockOrderItemPO") OutStockOrderItemPO outStockOrderItemPO);

    /**
     * 更新出库位
     * 
     * @return
     */
    void updateLocationBatch(@Param("locationId") Long locationId, @Param("locationName") String locationName,
        @Param("list") List<Long> itemIds);

    void batchUpdateOutStockOrderItem(
        @Param("list") List<BatchUpdateOutStockOrderItemPO> batchUpdateOutStockOrderItemPOS);

    /**
     * 如果有波次号的时候，不会更新
     * 
     * @param batchUpdateOutStockOrderItemPOS
     * @return
     */
    int batchUpdateOutStockOrderItemOfSowTask(
        @Param("list") List<BatchUpdateOutStockOrderItemPO> batchUpdateOutStockOrderItemPOS);

    int forceBatchUpdateOutStockOrderItemOfSowTask(
        @Param("list") List<BatchUpdateOutStockOrderItemPO> batchUpdateOutStockOrderItemPOS);

    int batchUpdateOutStockOrderItemLocation(
        @Param("list") List<BatchUpdateOutStockOrderItemPO> batchUpdateOutStockOrderItemPOS);

    int batchUpdateOutStockOrderItemOfBatch(@Param("list") List<OutStockOrderItemPO> outStockOrderItemPOS);

    /**
     * 更新订单项的波次信息
     */
    int batchUpdateOutStockOrderItemSowId(@Param("list") List<OutStockOrderItemPO> outStockOrderItemPOS);

    OutStockOrderItemPO findById(Long id);

    List<OutStockOrderItemPO> findBySkuid(@Param("skuids") List<Long> skuids);

    List<OutStockOrderItemPO>
        findByOutstockorderIdList(@Param("outstockorderIdList") Collection<Long> outstockorderIdList);

    /**
     * @param batchTaskId
     */
    void cleanBatchTask(List<String> batchTaskId);

    /**
     * 清除订单项波次相关信息
     */
    void cleanBatchTaskByBatchNo(List<String> batchNos);

    /**
     * 根据拣货任务id获取出库单详情id列表
     * 
     * @return
     */
    List<Long> listIdByBatchTaskIs(@Param("list") List<String> batchTaskIds);

    /**
     * 根据拣货任务详情id获取出库单详情列表
     * 
     * @param batchTaskNo
     * @return
     */
    List<OutStockOrderItemPO> listByBatchTaskItemId(@Param("list") List<String> batchTaskItemIds,
        @Param("orgId") Integer orgId);

    /**
     * 根据拣货任务编号获取出库单详情列表
     * 
     * @param batchTaskNo
     * @return
     */
    List<OutStockOrderItemPO> listByBatchTaskNo(@Param("batchTaskNo") String batchTaskNo);

    /**
     * 根据拣货任务id获取出库单详情列表
     * 
     * @param batchTaskId
     * @return
     */
    List<OutStockOrderItemPO> listByBatchTaskId(@Param("batchTaskId") String batchTaskId);

    /**
     * 根据播种任务获取出库单详情列表
     * 
     * @param sowTaskNo
     * @param cityId
     * @return
     */
    List<OutStockOrderItemDTO> findBySowTaskNo(@Param("sowTaskNo") String sowTaskNo, @Param("orgId") Integer cityId);

    List<OutStockOrderItemPO> listItemBySowTaskNo(@Param("sowTaskNo") String sowTaskNo, @Param("orgId") Integer cityId);

    /**
     * 根据波次编号获取出库单id
     */
    List<Long> listOrderIdByBatchNo(@Param("list") Collection<String> batchNos);

    /**
     * 查找出没有完成拣货的出库单id
     * 
     * @returnlistBatchTaskBySowTaskNo
     */
    List<Long> listOrderIdNoPick(@Param("list") List<Long> orderIds);

    /**
     * 查找出没有生成拣货任务的出库单id
     * 
     * @return
     */
    List<Long> listOrderIdNoBatchTask(@Param("list") List<Long> orderIds);

    /**
     * 根据id查询明细
     */
    List<OutStockOrderItemPO> listByIds(@Param("ids") List<Long> ids);

    /**
     * 根据波次id查询订单明细
     * 
     * @return
     */
    List<OutStockOrderItemPO> listByBatchIds(@Param("list") List<String> batchIds);

    /**
     * 根据出库单项id查询波次id
     */
    List<String> listBatchIdByIds(@Param("ids") List<Long> ids);

    /**
     * 根据订单id查询订单打印数据
     * 
     * @param orgId
     * @param outStockOrderIds
     * @return
     */
    List<OutStockOrderItemPrintPO> listOutStockOrderPrintByOrderIds(@Param("orgId") Integer orgId,
        @Param("outStockOrderIds") List<Long> outStockOrderIds);

    /**
     * 根据订单id查询订单打印数据
     */
    List<OutStockOrderItemPrintPO> listOutStockOrderPrintByOrderId(@Param("orgId") Integer orgId,
        @Param("orderId") Long orderId);

    /**
     * 根据播种任务获取出库单详情列表
     */
    List<OutStockOrderItemDTO> findBySowTaskNos(@Param("sowTaskNos") List<String> sowTaskNos,
        @Param("orgId") Integer cityId);

    /**
     * 根据播种任务获取出库单详情列表
     */
    PageResult<OutStockOrderItemDTO> findListBySowTaskNos(@Param("sowTaskNos") List<String> sowTaskNos,
        @Param("orgId") Integer cityId, @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据订单号查询出库位
     */
    List<OutStockOrderLocationDTO> findLocationByOrderNo(@Param("orderNos") List<String> npOrderNos,
        @Param("orgId") Integer cityId, @Param("warehouseId") Integer warehouseId);

    /**
     * 根据出库单项ID获取出库单详情
     *
     */
    List<OutStockOrderItemDTO> findByItemIds(@Param("list") Collection<Long> orderItemIds,
        @Param("isOrderBy") Integer isOrderBy);

    List<OutStockOrderItemDTO> findSowOrderItemsByOrderNos(@Param("orgId") Integer orgId,
        @Param("orderIds") List<Long> orderIds);

    List<OutStockOrderItemDTO> findBySowTaskItemIds(@Param("orgId") Integer orgId,
        @Param("sowTaskItemIds") List<Long> sowTaskItemIds);

    void updateOutStockOrderItemLocationByIds(@Param("orgId") Integer orgID, @Param("locationId") Long locationId,
        @Param("locationName") String locationName, @Param("itemIds") List<Long> itemIds);

    List<OrderItemTaskInfoPO> listOrderItemTaskInfoByBatchTaskItemIds(@Param("list") List<String> batchTaskItemIds);

    List<Long> findOrderItemToLocationIdBySpecId(@Param("orderId") Long orderId, @Param("specId") Long specId,
        @Param("ownerId") Long ownerId);

    List<Long> findToLocationIdByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据出库单id获取播种任务编号
     * 
     * @return
     */
    List<String> listSowTaskNoByOrderIds(@Param("list") List<Long> orderIds);

    /**
     * 根据出库单id和播种任务编号查找其它出库单
     * 
     * @return
     */
    List<Long> listOtherOrderIdBySowTaskNos(@Param("sowTaskNoList") List<String> sowTaskNos,
        @Param("orderIdList") List<Long> orderIds);

    void updateSowTaskById(OutStockOrderItemDTO dto);

    /**
     * 查询出库位信息
     * 
     * @param cityId
     * @param taskId
     * @return
     */
    OutStockOrderItemPO queryToLocationByTaskId(@Param("cityId") Integer cityId, @Param("taskId") Long taskId);

    /**
     * 批量查询出库位信息
     * 
     * @param cityId
     * @param taskIds
     * @return
     */
    List<OutStockOrderItemPO> listToLocationByTaskIds(@Param("cityId") Integer cityId,
        @Param("taskIds") List<Long> taskIds);

    /**
     * 查询出库单id
     * 
     * @param sowNo
     * @param cityId
     * @return
     */
    List<OutStockOrderItemPO> listOutStockOrderIdBySowNo(@Param("sowNo") List<String> sowNo,
        @Param("cityId") Integer cityId);

    /**
     * 更新出库单项数量
     * 
     * @param poList
     *
     * @return
     */
    int updateBatchByPOList(@Param("list") List<OutStockOrderItemPO> poList);

    /**
     * 根据出库单项ID获取出库单详情
     * 
     * @return
     */
    List<OutStockOrderItemDTO> findItemAndDetailByIds(OutStockOrderItemQuerySO querySO);

    /**
     * 根据出库单项ID获取出库单详情
     * 
     * @return
     */
    PageResult<OutStockOrderItemPO> listOrderItem(OutStockOrderItemQuerySO querySO);

    void clearBatchInfo(@Param("idList") List<Long> idList);

    /**
     * 根据波次编号获取缺货的出库单项id
     */
    List<Long> findLackItemIdByBatchNo(@Param("list") List<String> batchNos);

    /**
     * 查询指定仓库所有作业中订单
     *
     * @param warehouseId 仓库 id
     * @return 该仓库所有作业中订单
     */
    List<OutStockOrderItemPO> findAllWorkingOrders(Integer warehouseId);
}
