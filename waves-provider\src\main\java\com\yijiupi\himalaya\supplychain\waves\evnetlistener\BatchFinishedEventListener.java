package com.yijiupi.himalaya.supplychain.waves.evnetlistener;

import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchFinishedBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchFinishedLackOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.NotifyOrderLackBO;
import com.yijiupi.himalaya.supplychain.waves.event.BatchFinishedEvent;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
@Component
public class BatchFinishedEventListener {

    @Autowired
    private BatchFinishedLackOrderBL batchFinishedLackOrderBL;
    private static final Logger LOG = LoggerFactory.getLogger(BatchFinishedBL.class);

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleOrderCreatedEvent(BatchFinishedEvent event) {
        try {
            NotifyOrderLackBO notifyOrderLackBO = event.getNotifyOrderLackBO();
            LOG.warn("handleOrderCreatedEvent 处理缺货信息:{}；事务是否执行中：{}, 事务名称:{}",
                JSON.toJSONString(event.getNotifyOrderLackBO()),
                TransactionSynchronizationManager.isSynchronizationActive(),
                TransactionSynchronizationManager.getCurrentTransactionName());
            if (Objects.isNull(notifyOrderLackBO)) {
                return;
            }
            batchFinishedLackOrderBL.processOrderLack(notifyOrderLackBO, notifyOrderLackBO.getOptUserId());
        } catch (Exception e) {
            LOG.error("调用缺货接口失败，入参:{}", JSON.toJSONString(event.getNotifyOrderLackBO()), e);
        }
    }

}
