package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/12
 */
public class OrderItemTaskInfoDetailLackHelperBO {

    private Long orderItemTaskInfoId;

    private Long orderItemTaskInfoDetailId;

    private BigDecimal lackCount = BigDecimal.ZERO;

    private BigDecimal moveCount;

    private BigDecimal unitTotalCount;
    @JsonIgnore
    private OutStockOrderPO outStockOrderPO;

    private OrderItemTaskInfoPO orderItemTaskInfoPO;
    @JsonIgnore
    private OrderItemTaskInfoDetailPO orderItemTaskInfoDetailPO;

    /**
     * 二级货主ID
     */
    private Long secOwnerId;
    /**
     * 销售规格
     */
    private BigDecimal saleSpecQuantity;
    /**
     * 缺货顺序排序号
     */
    private Integer sequence;

    /**
     * 获取
     *
     * @return orderItemTaskInfoId
     */
    public Long getOrderItemTaskInfoId() {
        return this.orderItemTaskInfoId;
    }

    /**
     * 设置
     *
     * @param orderItemTaskInfoId
     */
    public void setOrderItemTaskInfoId(Long orderItemTaskInfoId) {
        this.orderItemTaskInfoId = orderItemTaskInfoId;
    }

    /**
     * 获取
     *
     * @return orderItemTaskInfoDetailId
     */
    public Long getOrderItemTaskInfoDetailId() {
        return this.orderItemTaskInfoDetailId;
    }

    /**
     * 设置
     *
     * @param orderItemTaskInfoDetailId
     */
    public void setOrderItemTaskInfoDetailId(Long orderItemTaskInfoDetailId) {
        this.orderItemTaskInfoDetailId = orderItemTaskInfoDetailId;
    }

    /**
     * 获取
     *
     * @return lackCount
     */
    public BigDecimal getLackCount() {
        return this.lackCount;
    }

    /**
     * 设置
     *
     * @param lackCount
     */
    public void setLackCount(BigDecimal lackCount) {
        this.lackCount = lackCount;
    }

    /**
     * 获取
     *
     * @return moveCount
     */
    public BigDecimal getMoveCount() {
        return this.moveCount;
    }

    /**
     * 设置
     *
     * @param moveCount
     */
    public void setMoveCount(BigDecimal moveCount) {
        this.moveCount = moveCount;
    }

    /**
     * 获取
     *
     * @return unitTotalCount
     */
    public BigDecimal getUnitTotalCount() {
        return this.unitTotalCount;
    }

    /**
     * 设置
     *
     * @param unitTotalCount
     */
    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    /**
     * 获取
     *
     * @return outStockOrderPO
     */
    public OutStockOrderPO getOutStockOrderPO() {
        return this.outStockOrderPO;
    }

    /**
     * 设置
     *
     * @param outStockOrderPO
     */
    public void setOutStockOrderPO(OutStockOrderPO outStockOrderPO) {
        this.outStockOrderPO = outStockOrderPO;
    }

    /**
     * 获取 二级货主ID
     *
     * @return secOwnerId 二级货主ID
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主ID
     *
     * @param secOwnerId 二级货主ID
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    /**
     * 获取
     *
     * @return orderItemTaskInfoPO
     */
    public OrderItemTaskInfoPO getOrderItemTaskInfoPO() {
        return this.orderItemTaskInfoPO;
    }

    /**
     * 设置
     *
     * @param orderItemTaskInfoPO
     */
    public void setOrderItemTaskInfoPO(OrderItemTaskInfoPO orderItemTaskInfoPO) {
        this.orderItemTaskInfoPO = orderItemTaskInfoPO;
    }

    /**
     * 获取
     *
     * @return orderItemTaskInfoDetailPO
     */
    public OrderItemTaskInfoDetailPO getOrderItemTaskInfoDetailPO() {
        return this.orderItemTaskInfoDetailPO;
    }

    /**
     * 设置
     *
     * @param orderItemTaskInfoDetailPO
     */
    public void setOrderItemTaskInfoDetailPO(OrderItemTaskInfoDetailPO orderItemTaskInfoDetailPO) {
        this.orderItemTaskInfoDetailPO = orderItemTaskInfoDetailPO;
    }

    /**
     * 获取 销售规格
     *
     * @return saleSpecQuantity 销售规格
     */
    public BigDecimal getSaleSpecQuantity() {
        return this.saleSpecQuantity;
    }

    /**
     * 设置 销售规格
     *
     * @param saleSpecQuantity 销售规格
     */
    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    /**
     * 获取 缺货顺序排序号
     *
     * @return sequence 缺货顺序排序号
     */
    public Integer getSequence() {
        return this.sequence;
    }

    /**
     * 设置 缺货顺序排序号
     *
     * @param sequence 缺货顺序排序号
     */
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }
}
