package com.yijiupi.himalaya.supplychain.waves.batch;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.PickedDetailQueryParam;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.*;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderItemDetailAllotDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;

/**
 * 波次任务查询
 *
 * <AUTHOR> 2018/3/16
 */
public interface IBatchTaskQueryService {

    /**
     * 查找波次任务列表
     *
     * <AUTHOR>
     */
    PageList<BatchTaskDTO> findBatchTaskList(BatchTaskQueryDTO batchTaskQueryDTO);

    /**
     * 查找波次任务列表（PDA）
     *
     * @return
     */
    PageList<BatchTaskSortDTO> findBatchTaskSortList(BatchTaskSortQueryDTO batchTaskSortQueryDTO);

    /**
     * 查找拣货任务列表（POA）
     *
     * @param batchTaskSortQueryDTO
     * @return
     */
    BatchTaskPickInfoForPDADTO findPickBatchTaskList(BatchTaskSortQueryDTO batchTaskSortQueryDTO);

    /**
     * 查找 根据订单拣货的波次任务详情
     *
     * @param batchTaskId 波次任务id
     */
    PageList<BatchTaskSortOrderDTO> findBatchTaskSortItemByOrder(String batchTaskId, Integer currentPage,
        Integer pageSize);

    /**
     * 整件打包拣货完成任务明细
     *
     * @param queryDTO
     * @return
     */
    @Deprecated
    BatchTaskItemPickCompleteDetailDTO findCompleteBatchTaskItem(BatchTaskItemPickCompleteDetailQueryDTO queryDTO);

    /**
     * 整件打包拣货任务明细
     *
     * @param queryDTO
     * @return
     */
    List<BatchTaskItemPackageReviewDetailDTO>
        findPackageReviewBatchItem(BatchTaskItemPickCompleteDetailQueryDTO queryDTO);

    /**
     * 查找 根据产品拣货的波次任务详情
     *
     * @param batchTaskId 波次任务id
     */
    PageList<BatchTaskSortItemDTO> findBatchTaskSortItemByProduct(String batchTaskId, Integer currentPage,
        Integer pageSize);

    /**
     * 开始波次任务
     */
    BatchTaskSowDTO beginBatchTask(String batchTaskId, String operateUser, Integer userId);

    /**
     * 修改波次任务详情
     */
    Map<String, List<BatchTaskDTO>> updateBatchTaskItem(List<BatchTaskItemCompleteDTO> batchTaskItemUpdateDTOList,
        String batchTaskId, String userName, Integer warehouseId, Long locationId, String locationName, Integer cityId,
        Integer userId, Byte containerFlag);

    /**
     * 查询波次任务详情(分页)
     *
     * @return
     */
    PageList<BatchTaskItemDTO> findBatchTaskItemList(BatchTaskItemQueryDTO batchTaskItemQueryDTO);

    /**
     * 根据波次任务编号查询波次任务详情(查询打印拣货单)
     *
     * @param batchTaskNo
     * @return
     * <AUTHOR>
     */
    Map<String, List<BatchTaskItemDTO>> findBatchTaskItemMap(List<String> batchTaskNo);

    /**
     * 指派拣货员
     *
     * <AUTHOR>
     * @since 2018/4/2 15:06
     */
    int updateBatch(List<BatchTaskDTO> batchTaskDTOList, String operateUser);

    /**
     * 批量更新波次任务
     *
     * @param modDTO
     * @return
     */
    int updateBatchTaskBatch(BatchTaskModDTO modDTO);

    /**
     * 领取拣货任务
     */
    void receiveBatchTask(BatchTaskDTO batchTaskDTO);

    /**
     * 记录打印
     *
     * @param taskNoList 拣货单编号
     * <AUTHOR>
     */
    void recordPrint(List<String> taskNoList, String operateUser);

    /**
     * 根据播种任务查询波次任务列表
     */
    List<BatchTaskDTO> listBatchTaskBySowTaskNo(String sowTaskNo, List<Integer> taskStates, Integer cityId);

    /**
     * 根据播种任务查询波次任务列表
     *
     * @return
     */
    List<BatchTaskDTO> findBatchTaskBySowTaskNo(BatchTaskBySowTaskQueryParam queryParam);

    /**
     * 查询仓库所有集货位任务信息
     */
    List<BatchTaskDTO> listBatchTaskByWarehouse(Integer orgId, Integer WarehouseId);

    /**
     * 根据SKU查询二级仓生成内配单的订单中已入库未出库数量 - 盘点计算差异
     *
     * @return
     */
    Map<Long, BigDecimal> findCreateAllocationPickedCountForStoreCheck(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds, Map<Long, List<Long>> relationGroupMap);

    /**
     * 根据SKU查询二级仓生成内配单的订单中已入库未出库数量
     *
     * @return
     */
    Map<Long, BigDecimal> findPickedCountBySkuIdForCreateAllocation(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds, boolean isForStoreCheck);

    /**
     * 根据SKU查询2.5已拣货未出库的库存数量(排除已生内配单的订单) - 盘点计算差异
     */
    Map<Long, BigDecimal> findSCM25PickedCountForStoreCheck(Integer orgId, Integer warehouseId,
        List<Long> productSkuIds, Map<Long, List<Long>> relationGroupMap);

    /**
     * 根据SKU查询2.5已拣货未出库的库存数量
     *
     * @param orgId
     * @param warehouseId
     * @param productSkuId
     * @return
     */
    Map<Long, BigDecimal> findPickedCountBySkuIdForSCM25(Integer orgId, Integer warehouseId, List<Long> productSkuId,
        boolean isForStoreCheck);

    /**
     * 根据订单ID获取出库位
     *
     * @param orderId
     * @param orgId
     * @return
     */
    String getToLocationByOrderId(Long orderId, Integer orgId);

    /**
     * 根据播种任务查询波次任务列表
     */
    List<BatchTaskDTO> listBatchTaskBySowTaskNos(List<String> sowTaskNos, List<Integer> taskStates, Integer cityId);

    /**
     * 根据订单号获取播种任务
     */
    PageList<SowTaskDTO> findSowByOrderNos(List<String> orderNos, Integer orgId, Integer pageNum, Integer pageSize);

    /**
     * 根据ids获取订单信息
     *
     * @param orgId
     * @param refOrderIdList
     * @return
     */
    List<OutStockOrderDTO> findSimpleOutstockByIds(Integer orgId, List<Long> refOrderIdList);

    /**
     * 查询已拣货未出库的拣货任务占用的周转区库存数量
     *
     * @return
     */
    List<BatchTaskLocationUseCountDTO> listBatchTaskLocationUseCount(Integer cityId, Integer warehouseId);

    /**
     * 查询出库单是否生成了内配单
     *
     * @return KEY：出库单id VALUE：true-生成了内配单 false-没有生成
     */
    Map<String, Boolean> findCreateAllocationByOrderId(List<String> orderIds);

    /**
     * 查询订单关联已完成的按单拣货任务的货位信息
     */
    List<BatchTaskDTO> findOrderRelatedBatchTaskLocation(Integer orgId, Integer warehouseId, String orderNo);

    /**
     * 根据SKU查询2.5分拣占用详细信息（包含内配分拣占用）
     */
    List<PickedDetailDTO> findPickedDetailBySkuIdForSCM25(Integer orgId, Integer warehouseId, Long productSkuId,
        boolean isForStoreCheck);

    /**
     * 根据订单ID，规格信息查询子项出库位
     */
    List<Long> findOrderItemToLocationIdBySpecId(Long orderId, Long specId, Long ownerId);

    /**
     * 根据订单ID查询子项出库位
     */
    List<Long> findToLocationIdByOrderId(Long orderId);

    /**
     * 根据拣货任务项id获取实际分配数量
     *
     * @return
     */
    Map<String, List<OrderItemDetailAllotDTO>> getBatchTaskItemAllotMap(List<String> batchTaskItemIds);

    /**
     * 获取拣货任务不同状态的数量
     *
     * @return
     */
    List<BatchTaskStateCountDTO> getBatchTaskStateCount(Integer cityId, Integer warehouseId, Integer userId);

    /**
     * 获取拣货任务分拣中数量
     *
     * @return
     */
    Integer getSortingBatchTaskCount(Integer cityId, Integer warehouseId, Integer userId);

    /**
     * 获取拣货任务最长已进行时间（天数）
     *
     * @return
     */
    Integer getSortingBatchTaskMaxDays(Integer cityId, Integer warehouseId, Integer userId);

    /**
     * 获取团购订单摘果任务列表
     *
     * @return
     */
    PageList<GroupBuyBatchTaskDTO> listBatchTaskByGroupBuy(GroupBuyBatchTaskSO so);

    /**
     * 获取团购订单作业进度列表
     *
     * @return
     */
    PageList<GroupBuyWorkScheduleDTO> listWorkScheduleByGroupBuy(GroupBuyWorkScheduleSO so);

    /**
     * 根据产品名称查询2.5分拣占用详细信息（包含内配分拣占用）
     */
    List<PickedProductDTO> findPickedDetailByProductNameForSCM25(Integer orgId, Integer warehouseId,
        String productName);

    /**
     * 查询2.5分拣占用详细信息（包含内配分拣占用）
     */
    List<PickedProductDTO> findPickedDetailForSCM25(PickedDetailQueryParam param);

    /**
     * 查询缺货产品
     *
     * @return
     */
    List<Long> getLackProductSkuId(Integer cityId, Integer warehouseId, Date startTime, Date endTime);

    /**
     * 获取标记缺货的校验类型
     */
    LackCheckMethodResultDTO getMarkLackCheckMethod(LackCheckMethodQueryDTO methodQuery);

    /**
     * 获取新的缺货随机码
     */
    Integer newLackRandomCode(Integer warehouseId);

    /**
     * 获取旧的缺货随机码
     */
    Integer oldLackRandomCode(Integer warehouseId);

    /**
     * 查询分拣任务
     */
    List<BatchTaskDTO> listBatchTask(BatchTaskQueryDTO queryDTO);

    /**
     * 根据batchtaskId 查询出库位置
     *
     * @param idList
     * @param orgId
     * @return
     */
    List<BatchTaskDTO> listToLocationNameById(List<Long> idList, Integer orgId);

    /**
     * 根据订单号批量获取出库位信息
     *
     * @return
     */
    List<BatchTaskItemDTO> listToLocationNameByOrderNo(List<String> orderNos, Integer orgId);

    /**
     * 查找 根据产品拣货的波次任务详情
     *
     * @param batchTaskId 波次任务id
     */
    PageList<BatchTaskSowItemDTO> findBatchTaskSowItemByProduct(String batchTaskId, Integer currentPage,
        Integer pageSize);

    /**
     * 查找分拣任务项
     */
    PageList<BatchTaskItemDTO> listBatchTaskItem(BatchTaskItemQueryDTO queryDTO);

    /**
     * 根据播种任务查询波次任务列表
     */
    List<BatchTaskDTO> saaSListBatchTask(BatchTaskPramDTO batchTaskPramDTO);

    /**
     * 查找波次任务列表saas
     *
     * @param batchTaskQueryDTO
     * @return
     * <AUTHOR>
     */
    PageList<BatchTaskDTO> saasBatchTaskList(BatchTaskQueryDTO batchTaskQueryDTO);

    /**
     * 根据播种任务查询波次任务列表
     */
    List<BatchTaskDTO> saasListBatchTaskBySowTaskNo(BatchTaskQueryDTO batchTaskQueryDTO);

    /**
     * 根据播种任务查询波次任务列表
     */
    List<BatchTaskDTO> saasGetBatchSowTask(BatchTaskQueryDTO batchTaskQueryDTO);

    /**
     * 保存容器数量
     *
     * @param batchTaskContainersDTO
     */
    void saveBatchTaskContainerCount(BatchTaskContainersDTO batchTaskContainersDTO);

    /**
     * 通过出库单 id 查找对应拣货任务
     *
     * @param outStockOrderIds 出库单 id
     * @return 找到的拣货任务, 找不到返回空 list
     */
    List<BatchTaskDTO> findBatchTaskByOrder(List<Long> outStockOrderIds);

    /**
     * 根据产品名称查询2.5分拣占用详细信息（排除内配分拣占用）
     */
    List<PickedDetailDTO> findPickedDetailDTOSForSCM25(Integer orgId, Integer warehouseId, List<Long> productSkuIds,
        boolean isForStoreCheck);

    /**
     * 根据订单号查询有组合产品缺货的订单号列表
     *
     * @param orderNos 订单号
     * @param warehouseId 仓库 id
     * <AUTHOR>
     * @since 2019年3月12日 下午4:22:31
     */
    List<LackOrderInfoDTO> findPartGroupOrderList(List<String> orderNos, Integer warehouseId);

    /**
     * 获取在播种中数量
     */
    Map<Long, BigDecimal> findSowPickedCountBySkuIdForSCM25(Integer orgId, Integer warehouseId,
        List<Long> productSkuId);

    /**
     * 通过订单项 id 查询拣货任务项
     *
     * @param orderItemIds 订单项 id
     * @return 查询结果
     */
    Map<Long, List<BatchTaskItemDTO>> findBatchTaskItemByOrderItemId(Collection<Long> orderItemIds);

    /**
     * 根据SKU查询分拣占用详细信息
     */
    List<PickedDetailDTO> findPickedDetailByCondition(PickedDetailQueryDTO queryDTO);

    /**
     * 拣货任务项禁止销售配置查询
     */
    List<BatchTaskItemPeriodConfigResultDTO> listBatchTaskItemPeriodConfig(BatchTaskItemPeriodConfigQueryDTO queryDTO);

    /**
     * 通过货位信息查询分区信息，查询最近完成的拣货任务
     *
     * @param dto
     * @return
     */
    BatchTaskDTO getSortGroupLatestBatchTask(SortGroupLatestBatchTaskGetDTO dto);
}
