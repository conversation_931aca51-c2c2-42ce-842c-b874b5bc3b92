package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPrePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SowTaskPrePOMapper {
    int deleteByPrimaryKey(Long id);

    int insertSelective(SowTaskPrePO record);

    SowTaskPrePO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SowTaskPrePO record);

    List<SowTaskPrePO> findSowTaskPreByBatchId(@Param("batchId") String batchId, @Param("version") Integer version, @Param("orgId") Integer orgId);

    List<SowTaskPrePO> findSowTaskPreByBatchNos(@Param("batchNos") List<String> batchNos, @Param("version") Integer version, @Param("orgId") Integer orgId);
}