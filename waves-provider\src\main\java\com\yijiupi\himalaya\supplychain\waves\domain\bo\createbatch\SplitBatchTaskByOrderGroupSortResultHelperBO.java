package com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupListDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/16
 */
public class SplitBatchTaskByOrderGroupSortResultHelperBO {
    /**
     *
     */
    private Byte pickingType;

    private SortGroupDTO groupDTO;

    private SortGroupListDTO sortGroupListDTO;

    private List<OutStockOrderItemPO> outStockOrderItemPOList;

    private List<OutStockOrderPO> outStockOrderPOList;

    /**
     * 获取
     *
     * @return groupDTO
     */
    public SortGroupDTO getGroupDTO() {
        return this.groupDTO;
    }

    /**
     * 设置
     *
     * @param groupDTO
     */
    public void setGroupDTO(SortGroupDTO groupDTO) {
        this.groupDTO = groupDTO;
    }

    /**
     * 获取
     *
     * @return outStockOrderItemPOList
     */
    public List<OutStockOrderItemPO> getOutStockOrderItemPOList() {
        return this.outStockOrderItemPOList;
    }

    /**
     * 设置
     *
     * @param outStockOrderItemPOList
     */
    public void setOutStockOrderItemPOList(List<OutStockOrderItemPO> outStockOrderItemPOList) {
        this.outStockOrderItemPOList = outStockOrderItemPOList;
    }

    /**
     * 获取
     *
     * @return pickingType
     */
    public Byte getPickingType() {
        return this.pickingType;
    }

    /**
     * 设置
     *
     * @param pickingType
     */
    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    /**
     * 获取
     *
     * @return outStockOrderPOList
     */
    public List<OutStockOrderPO> getOutStockOrderPOList() {
        return this.outStockOrderPOList;
    }

    /**
     * 设置
     *
     * @param outStockOrderPOList
     */
    public void setOutStockOrderPOList(List<OutStockOrderPO> outStockOrderPOList) {
        this.outStockOrderPOList = outStockOrderPOList;
    }

    /**
     * 获取
     *
     * @return sortGroupListDTO
     */
    public SortGroupListDTO getSortGroupListDTO() {
        return this.sortGroupListDTO;
    }

    /**
     * 设置
     *
     * @param sortGroupListDTO
     */
    public void setSortGroupListDTO(SortGroupListDTO sortGroupListDTO) {
        this.sortGroupListDTO = sortGroupListDTO;
    }

}
