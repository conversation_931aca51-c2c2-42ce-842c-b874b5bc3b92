package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderWaitDeliverySO;

/**
 * 按产品+按订单组合生成波次
 *
 * <AUTHOR>
 * @date 2019/4/16 16:50
 */
public class BatchCreateByProductAndOrderDTO implements Serializable {

    private static final long serialVersionUID = 7851127557355674124L;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 选择产品集合
     */
    private List<BatchCreateChooseProductDTO> productList;

    /**
     * 选择订单项Id集合
     */
    private List<Long> orderItemIdList;

    /**
     * 分组方式 1:按线路，2：按片区
     */
    private Integer groupType;

    /**
     * 是否拆分（按线路或片区拆分波次） 0:否 1:是
     */
    private Byte groupFlag;

    /**
     * 是否一键全选 0:否 1:是
     */
    private Byte checkAll;

    /**
     * 订单查询条件
     */
    private OutStockOrderWaitDeliverySO orderSO;

    /**
     * 波次优先级
     */
    private BatchAllotPriorityDTO allotPriority;

    /**
     * 是否生成车次 0:否 1:是
     */
    private Byte trainFlag;

    /**
     * 车次拆分方式 0：不拆分，1:按线路，2：按片区
     */
    private Integer trainType;

    /**
     * 操作人
     */
    private Integer operateUserId;
    /**
     * 配送车辆ID
     */
    private Long deliveryCarId;
    /**
     * 物流公司ID
     */
    private Long logisticsCompanyId;

    public Long getDeliveryCarId() {
        return deliveryCarId;
    }

    public void setDeliveryCarId(Long deliveryCarId) {
        this.deliveryCarId = deliveryCarId;
    }

    public Long getLogisticsCompanyId() {
        return logisticsCompanyId;
    }

    public void setLogisticsCompanyId(Long logisticsCompanyId) {
        this.logisticsCompanyId = logisticsCompanyId;
    }

    public BatchAllotPriorityDTO getAllotPriority() {
        return allotPriority;
    }

    public void setAllotPriority(BatchAllotPriorityDTO allotPriority) {
        this.allotPriority = allotPriority;
    }

    public Byte getTrainFlag() {
        return trainFlag;
    }

    public void setTrainFlag(Byte trainFlag) {
        this.trainFlag = trainFlag;
    }

    public Integer getTrainType() {
        return trainType;
    }

    public void setTrainType(Integer trainType) {
        this.trainType = trainType;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Byte getCheckAll() {
        return checkAll;
    }

    public void setCheckAll(Byte checkAll) {
        this.checkAll = checkAll;
    }

    public OutStockOrderWaitDeliverySO getOrderSO() {
        return orderSO;
    }

    public void setOrderSO(OutStockOrderWaitDeliverySO orderSO) {
        this.orderSO = orderSO;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public List<BatchCreateChooseProductDTO> getProductList() {
        return productList;
    }

    public void setProductList(List<BatchCreateChooseProductDTO> productList) {
        this.productList = productList;
    }

    public List<Long> getOrderItemIdList() {
        return orderItemIdList;
    }

    public void setOrderItemIdList(List<Long> orderItemIdList) {
        this.orderItemIdList = orderItemIdList;
    }

    public Integer getGroupType() {
        return groupType;
    }

    public void setGroupType(Integer groupType) {
        this.groupType = groupType;
    }

    public Byte getGroupFlag() {
        return groupFlag;
    }

    public void setGroupFlag(Byte groupFlag) {
        this.groupFlag = groupFlag;
    }

    public Integer getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(Integer operateUserId) {
        this.operateUserId = operateUserId;
    }
}
