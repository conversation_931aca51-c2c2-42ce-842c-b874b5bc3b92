package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemPackageReviewDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemPickCompleteCollectDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemPickCompletePackageDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;

/**
 * <AUTHOR>
 * @title: BatchTaskItemPackageReviewDetailDTOConvertor
 * @description:
 * @date 2023-01-05 10:38
 */
@Component
public class BatchTaskItemPackageReviewDetailDTOConvertor {

    @Autowired
    private SowTaskMapper sowTaskMapper;

    /**
     * ①BT001拣货产品项为：
     *
     * 乐事薯片黄瓜味，12袋/件；其中，A订单：1件2袋，B订单：2件5袋 乐事薯片青柠味，12袋/件；其中，A订单：2袋，B订单：2件1袋
     * 
     * 当BT001拣货完成后，
     *
     * 提示放置到集货区数量显示为：
     *
     * 乐事薯片黄瓜味，12袋/件 7袋 乐事薯片青柠味，12袋/件 3袋
     *
     * 提示放置到打包区数量显示为：
     *
     * 乐事薯片黄瓜味，12袋/件 3件 乐事薯片青柠味，12袋/件 2件
     *
     * ②BT002拣货产品项为：
     *
     * 卫龙辣条，20袋/件；其中，A订单：1件1袋，B订单：5件1袋 麻辣王子，15袋/件；其中，B订单：3件2袋，B订单：1件5袋
     *
     * 当BT002拣货完成后，提示放置到集货区数量显示为：
     *
     * 卫龙辣条，20袋/件； 2袋 麻辣王子，15袋/件； 7袋
     *
     * 当BT002拣货完成后，提示放置到打包区数量显示为：
     *
     * 卫龙辣条，20袋/件； 6件 麻辣王子，15袋/件； 4件
     * 
     * @param batchTaskPO
     * @param orderItemTaskInfoList
     * @param outStockItemList
     * @return
     *
     *         1、按订单聚合 否 2、按订单项聚合 3、按订单项产品聚合 4、按拣货任务明细产品聚合 否
     *
     *         拣货任务产品项+订单合计数
     *
     *         两个订单： A产品 规格6 1、一个订单A产品 1件2瓶 2。一个订单A产品 4瓶
     *
     *         先按skuId聚合，聚合后，再分别把订单项中的大件和小件汇总
     */
    public List<BatchTaskItemPackageReviewDetailDTO> convert(BatchTaskPO batchTaskPO,
        List<OrderItemTaskInfoPO> orderItemTaskInfoList, List<OutStockOrderItemPO> outStockItemList,
        List<BatchTaskItemPO> batchTaskItemList) {
        SowTaskPO sowTaskPO =
            sowTaskMapper.getSowTaskById(batchTaskPO.getSowTaskId(), Integer.valueOf(batchTaskPO.getOrgId()));

        Map<Long, List<OutStockOrderItemPO>> itemGroupMap =
            outStockItemList.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid));
        Map<Long, List<OrderItemTaskInfoPO>> itemTaskInfoMap =
            orderItemTaskInfoList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId));
        Map<String, BatchTaskItemPO> batchTaskItemMap =
            batchTaskItemList.stream().collect(Collectors.toMap(BatchTaskItemPO::getId, v -> v));

        List<BatchTaskItemPackageReviewDetailDTO> detailList = new ArrayList<>();

        for (Map.Entry<Long, List<OutStockOrderItemPO>> entry : itemGroupMap.entrySet()) {
            BatchTaskItemPackageReviewDetailDTO detailDTO = new BatchTaskItemPackageReviewDetailDTO();
            detailDTO.setSowLocationId(sowTaskPO.getLocationId());
            detailDTO.setSowLocationName(sowTaskPO.getLocationName());
            detailDTO.setWarehouseId(sowTaskPO.getWarehouseId());
            detailDTO.setSkuId(entry.getKey());
            BatchTaskItemPickCompleteCollectDetailDTO collectDetail =
                convertCollect(entry.getValue(), itemTaskInfoMap, batchTaskItemMap);
            BatchTaskItemPickCompletePackageDetailDTO packageDetailDTO =
                convertPackage(entry.getValue(), itemTaskInfoMap, batchTaskItemMap);

            detailDTO.setCollectDetail(collectDetail);
            detailDTO.setPackageDetail(packageDetailDTO);

            detailList.add(detailDTO);
        }

        return detailList;
    }

    // 小件进集货区
    private BigDecimal getUnitTotalCount(BatchTaskItemPO taskItem, OrderItemTaskInfoPO orderItemPO) {
        if (TaskStateEnum.已完成.getType() == taskItem.getTaskState()) {
            return orderItemPO.getOverSortCount();
        }

        return orderItemPO.getUnitTotalCount();
    }

    // 小件进集货区
    private BatchTaskItemPickCompleteCollectDetailDTO convertCollect(List<OutStockOrderItemPO> outStockItemList,
        Map<Long, List<OrderItemTaskInfoPO>> itemTaskInfoMap, Map<String, BatchTaskItemPO> batchTaskItemMap) {
        List<OrderItemTaskInfoPO> filterItemTaskInfoList =
            outStockItemList.stream().filter(m -> CollectionUtils.isNotEmpty(itemTaskInfoMap.get(m.getId())))
                .map(m -> itemTaskInfoMap.get(m.getId())).flatMap(list -> list.stream()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterItemTaskInfoList)) {
            return null;
        }

        Map<Long, List<OrderItemTaskInfoPO>> orderItemGroupByOrder =
            filterItemTaskInfoList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderId));
        OutStockOrderItemPO outStockOrderItemPO = outStockItemList.stream().findAny().get();

        BigDecimal unitCount = orderItemGroupByOrder.values().stream().map(itemList -> {
            BigDecimal unitTotalCount =
                itemList.stream().map(m -> getUnitTotalCount(batchTaskItemMap.get(m.getBatchTaskItemId()), m))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal[] count = unitTotalCount.divideAndRemainder(outStockOrderItemPO.getSpecquantity());

            return count[1];
        }).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (BigDecimal.ZERO.compareTo(unitCount) == 0) {
            return null;
        }

        BatchTaskItemPickCompleteCollectDetailDTO detailDTO = new BatchTaskItemPickCompleteCollectDetailDTO();
        detailDTO.setProductName(outStockOrderItemPO.getProductname());
        detailDTO.setSkuId(outStockOrderItemPO.getSkuid());
        detailDTO.setPackageCount(BigDecimal.ZERO);
        detailDTO.setUnitCount(unitCount);
        detailDTO.setUnitTotalCount(unitCount);
        detailDTO.setSpecName(outStockOrderItemPO.getSpecname());
        detailDTO.setSaleSpec(outStockOrderItemPO.getSalespec());
        detailDTO.setSpecQuantity(outStockOrderItemPO.getSpecquantity());
        detailDTO.setSaleSpecQuantity(outStockOrderItemPO.getSalespecquantity());
        detailDTO.setPackageName(outStockOrderItemPO.getPackagename());
        detailDTO.setUnitName(outStockOrderItemPO.getUnitname());

        return detailDTO;
    }

    // 整件进打包区
    private BatchTaskItemPickCompletePackageDetailDTO convertPackage(List<OutStockOrderItemPO> outStockItemList,
        Map<Long, List<OrderItemTaskInfoPO>> itemTaskInfoMap, Map<String, BatchTaskItemPO> batchTaskItemMap) {
        List<OrderItemTaskInfoPO> filterItemTaskInfoList =
            outStockItemList.stream().filter(m -> CollectionUtils.isNotEmpty(itemTaskInfoMap.get(m.getId())))
                .map(m -> itemTaskInfoMap.get(m.getId())).flatMap(list -> list.stream()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterItemTaskInfoList)) {
            return null;
        }

        Map<Long, List<OrderItemTaskInfoPO>> orderItemGroupByOrder =
            filterItemTaskInfoList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderId));
        OutStockOrderItemPO outStockOrderItemPO = outStockItemList.stream().findAny().get();

        BigDecimal packageCount = orderItemGroupByOrder.values().stream().map(itemList -> {
            BigDecimal unitTotalCount =
                itemList.stream().map(m -> getUnitTotalCount(batchTaskItemMap.get(m.getBatchTaskItemId()), m))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal[] count = unitTotalCount.divideAndRemainder(outStockOrderItemPO.getSpecquantity());

            return count[0];
        }).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (BigDecimal.ZERO.compareTo(packageCount) == 0) {
            return null;
        }

        BatchTaskItemPickCompletePackageDetailDTO detailDTO = new BatchTaskItemPickCompletePackageDetailDTO();
        detailDTO.setProductName(outStockOrderItemPO.getProductname());
        detailDTO.setSkuId(outStockOrderItemPO.getSkuid());
        detailDTO.setPackageCount(packageCount);
        detailDTO.setUnitCount(BigDecimal.ZERO);
        detailDTO.setUnitTotalCount(packageCount.multiply(outStockOrderItemPO.getSpecquantity()));
        detailDTO.setSpecName(outStockOrderItemPO.getSpecname());
        detailDTO.setSaleSpec(outStockOrderItemPO.getSalespec());
        detailDTO.setSpecQuantity(outStockOrderItemPO.getSpecquantity());
        detailDTO.setSaleSpecQuantity(outStockOrderItemPO.getSalespecquantity());
        detailDTO.setPackageName(outStockOrderItemPO.getPackagename());
        detailDTO.setUnitName(outStockOrderItemPO.getUnitname());

        return detailDTO;
    }

}
