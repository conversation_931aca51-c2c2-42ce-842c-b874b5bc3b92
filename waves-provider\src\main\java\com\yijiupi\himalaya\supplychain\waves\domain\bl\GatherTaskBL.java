package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.CompleteGatherTaskConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.GatherTaskConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskLocationScaleDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskProductScaleDTO;
import com.yijiupi.himalaya.supplychain.waves.search.CompleteGatherTaskLocationSO;
import com.yijiupi.himalaya.supplychain.waves.search.CompleteGatherTaskSO;
import com.yijiupi.himalaya.supplychain.waves.search.GatherTaskSO;

/**
 * 集货任务任务
 *
 * <AUTHOR> 2019/10/31
 */
@Service
public class GatherTaskBL {

    private static Logger LOGGER = LoggerFactory.getLogger(GatherTaskBL.class);

    @Autowired
    private GatherTaskMapper gatherTaskMapper;

    @Autowired
    private GatherTaskProductMapper gatherTaskProductMapper;

    @Autowired
    private GatherTaskLocationMapper gatherTaskLocationMapper;

    @Autowired
    private BatchTaskMapper batchTaskMapper;

    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;

    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;

    @Reference
    private IBatchInventoryManageService iBatchInventoryManageService;

    @Autowired
    BatchTaskItemContainerMapper batchTaskItemContainerMapper;

    /**
     * 生成集货任务
     * 
     * @param batchTaskId
     * @param userId
     */
    @Deprecated
    @Transactional(rollbackFor = RuntimeException.class)
    public void createGatherTask(String batchTaskId, Long userId) {
        LOGGER.info("生成集货任务：batchTaskId={},userId={}", batchTaskId, userId);
        Integer count = gatherTaskMapper.selectByBatchTaskId(batchTaskId);
        // 判断是否已经创建集货任务
        if (count == 0) {
            BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);
            AssertUtils.notNull(batchTaskPO, "拣货任务不存在！");
            // 保存集货任务
            GatherTaskPO po = GatherTaskConvertor.convertToGatherTaskPO(batchTaskPO, userId);
            gatherTaskMapper.insertSelective(po);

            // 保存集货任务产品明细
            List<BatchTaskItemPO> batchTaskItemPOList = batchTaskItemMapper.findBatchTaskItemDTOListNoPage(batchTaskId);
            AssertUtils.isTrue(batchTaskItemPOList != null && batchTaskItemPOList.size() > 0, "拣货任务明细项为空！");
            List<GatherTaskProductPO> productPOList =
                GatherTaskConvertor.convertToGatherTaskProductPOList(batchTaskItemPOList, po.getId(), userId);
            gatherTaskProductMapper.insertList(productPOList);
            /*
             * 保存集货任务出库位
             */
            // 根据拣货任务id查询订单明细项
            List<OutStockOrderItemPO> orderItemList = outStockOrderItemMapper.listByBatchTaskId(batchTaskId);
            if (CollectionUtils.isEmpty(orderItemList)) {
                throw new BusinessException("拣货任务未关联出库单明细项！");
            }
            List<String> orderIdList = orderItemList.stream().map(p -> String.valueOf(p.getOutstockorderId()))
                .distinct().collect(Collectors.toList());
            LOGGER.info("参数单据ID集合：{}", JSON.toJSONString(orderIdList));
            // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 - HANDLED
            throw new BusinessException("NOT SUPPORT!");
        }
    }

    /**
     * 查询集货任务所有出库位 必传batchTaskId 过滤locationName
     * 
     * @param so
     * @return
     */
    public List<GatherTaskLocationScaleDTO> findGatherTaskLocation(GatherTaskSO so) {
        LOGGER.info("查询集货任务所有出库位：{}", JSON.toJSONString(so));
        return gatherTaskLocationMapper.selectLocaltion(so);
    }

    /**
     * 查询集货任务出库位明细 必传batchTaskId、locationId
     * 
     * @param so
     * @return
     */
    public GatherTaskLocationScaleDTO findGatherTaskLocationDetail(GatherTaskSO so) {
        LOGGER.info("查询集货任务出库位明细：{}", JSON.toJSONString(so));
        List<GatherTaskLocationScaleDTO> locationScaleDTOList = gatherTaskLocationMapper.selectLocaltion(so);
        GatherTaskLocationScaleDTO locationScaleDTO = null;
        if (locationScaleDTOList != null && locationScaleDTOList.size() > 0) {
            locationScaleDTO = locationScaleDTOList.get(0);
            String[] gatherTaskProductIds = locationScaleDTO.getGatherTaskProductIds().split("_");
            locationScaleDTO.setProductScaleList(gatherTaskLocationMapper
                .selectLocaltionDetail(locationScaleDTO.getLocationId(), so.getOrgId(), gatherTaskProductIds));
        }
        return locationScaleDTO;
    }

    /**
     * 查询集货任务所有产品 必传 batchTaskId 过滤productName
     * 
     * @param so
     * @return
     */
    public List<GatherTaskProductScaleDTO> findGatherTaskProduct(GatherTaskSO so) {
        LOGGER.info("查询集货任务所有产品：{}", JSON.toJSONString(so));
        return gatherTaskProductMapper.selectProduct(so);
    }

    /**
     * 根据集货产品出库位明细 必传 batchTaskId、gatherTaskProductId
     * 
     * @param so
     * @return
     */
    public GatherTaskProductScaleDTO findGatherTaskProductDetail(GatherTaskSO so) {
        LOGGER.info("根据集货产品出库位明细：{}", JSON.toJSONString(so));
        List<GatherTaskProductScaleDTO> productScaleDTOList = gatherTaskProductMapper.selectProduct(so);
        GatherTaskProductScaleDTO productScaleDTO = null;
        if (productScaleDTOList != null && productScaleDTOList.size() > 0) {
            productScaleDTO = productScaleDTOList.get(0);
            productScaleDTO.setLocationScaleList(
                gatherTaskProductMapper.selectProductDetail(productScaleDTO.getId(), so.getOrgId()));
        }
        return productScaleDTO;
    }

    /**
     * 修改集货任务
     * 
     * @param po
     */
    public void updateGatherTaskStatus(GatherTaskPO po) {
        LOGGER.info("修改集货任务状态：{}", JSON.toJSONString(po));
        gatherTaskMapper.updateGatherTaskStatus(po);
    }

    /**
     * 修改集货任务出库位集货状态 只传id、status(0=待集货，1=已集货)、lastUpdateUser
     * 
     * @param dto
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateGatherTaskLocationStatus(GatherTaskLocationDTO dto) {
        LOGGER.info("修改出库位集货状态：{}", JSON.toJSONString(dto));
        GatherTaskLocationPO po = new GatherTaskLocationPO();
        BeanUtils.copyProperties(dto, po);
        gatherTaskLocationMapper.updateGatherTaskLocationStatus(po);
        po = gatherTaskLocationMapper.selectByPrimaryKey(po.getId());
        int count = gatherTaskLocationMapper.selectStatusZeroCount(po.getGatherTaskId(), po.getOrgId());
        // 如果所有的出库位产品集货完成 就修改集货任务产品明细表状态为 已集货
        if (count == 0) {
            GatherTaskPO gatherTaskPO = new GatherTaskPO();
            gatherTaskPO.setId(po.getGatherTaskId());
            gatherTaskPO.setOrgId(dto.getOrgId());
            gatherTaskPO.setLastUpdateUser(dto.getLastUpdateUser());
            updateGatherTaskStatus(gatherTaskPO);
        }
        // 集货完成需要做移库
        if (po.getStatus() == 1) {
            pickupCompleteBySku(po);
        }
    }

    /**
     * 移库操作
     * 
     * @param locationPO
     */
    public void pickupCompleteBySku(GatherTaskLocationPO locationPO) {
        LOGGER.info("移库操作：{}", JSON.toJSONString(locationPO));
        // 查询集货任务产品明细
        GatherTaskProductPO gatherTaskProductPO =
            gatherTaskProductMapper.selectByPrimaryKey(locationPO.getGatherTaskProductId());
        // 查询集货任务
        GatherTaskPO gatherTaskPO = gatherTaskMapper.selectByPrimaryKey(locationPO.getGatherTaskId());
        // 初始化移库实体
        PickUpDTO pickUpDTO = new PickUpDTO();

        // 根据拣货任务明细项id查询容器位和数量
        List<BatchTaskItemContainerPO> containerList = batchTaskItemContainerMapper
            .selectByBatchtaskitemId(String.valueOf(gatherTaskProductPO.getBatchTaskItemId()));
        if (CollectionUtils.isEmpty(containerList)) {
            throw new BusinessException("该产品未拣到容器位！");
        }
        containerList.forEach(containerPO -> {
            // 已拣货数量
            BigDecimal pickUnitTotalCount = containerPO.getPickUnitTotalCount();
            // 已移库数量
            BigDecimal moveUnitTotalCount =
                containerPO.getMoveUnitTotalCount() == null ? BigDecimal.ZERO : containerPO.getMoveUnitTotalCount();
            if (pickUnitTotalCount.compareTo(moveUnitTotalCount) > 0) {
                pickUpDTO.setFromLocationId(containerPO.getLocationId());// 设置来源库位
                BigDecimal subCount = pickUnitTotalCount.subtract(moveUnitTotalCount);
                // 已集货数量
                BigDecimal gatherCount = locationPO.getGatherCount();
                if (gatherCount.compareTo(subCount) > 0) {
                    locationPO.setGatherCount(gatherCount.subtract(subCount));
                    containerPO.setMoveUnitTotalCount(pickUnitTotalCount);
                    batchTaskItemContainerMapper.updateByPrimaryKeySelective(containerPO);
                } else {
                    containerPO.setMoveUnitTotalCount(moveUnitTotalCount.add(gatherCount));
                    batchTaskItemContainerMapper.updateByPrimaryKeySelective(containerPO);
                    return;
                }
            }
        });

        // 移库
        List<PickUpDTO> pickUpDTOList = new ArrayList<PickUpDTO>();
        pickUpDTO.setLocationId(locationPO.getLocationId());
        pickUpDTO.setProductSkuId(gatherTaskProductPO.getProductSkuId());
        pickUpDTO.setWarehouseId(gatherTaskPO.getWarehouseId());
        pickUpDTO.setFromChannel(0);
        pickUpDTO.setFromSource(0);
        pickUpDTO.setToChannel(0);
        pickUpDTO.setToSource(0);
        pickUpDTO.setCount(locationPO.getTotalCount());
        pickUpDTOList.add(pickUpDTO);
        PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
        pickUpChangeRecordDTO.setCityId(locationPO.getOrgId());
        pickUpChangeRecordDTO.setOrderId(String.valueOf(locationPO.getBusinessId()));
        pickUpChangeRecordDTO.setOrderNo("");
        pickUpChangeRecordDTO.setDescription("PDA集货完成");
        pickUpChangeRecordDTO.setCreateUser(String.valueOf(locationPO.getLastUpdateUser()));
        iBatchInventoryManageService.pickupCompleteBySku(pickUpDTOList, pickUpChangeRecordDTO);

        // 移库完成后更新出库单实际出库位
        List<OutStockOrderItemPO> orderItemList =
            outStockOrderItemMapper.listByBatchTaskId(gatherTaskPO.getBatchTaskId());
        orderItemList.forEach(x -> {
            if (Objects.equals(x.getOutstockorderId(), locationPO.getBusinessId())
                && Objects.equals(x.getSkuid(), gatherTaskProductPO.getProductSkuId())
                && Objects.equals(x.getProductSpecificationId(), gatherTaskProductPO.getSpecificationId())) {
                LOGGER.info("修改订单明细项库位：{}", JSON.toJSONString(x));
                x.setLocationId(locationPO.getLocationId());
                x.setLocationName(locationPO.getLocationName());
                outStockOrderItemMapper.updateLocation(x);
            }
        });
    }

    private void pickupAndTransferCompleteBySku(GatherTaskLocationPO locationPO,
        CompleteGatherTaskLocationSO locationSO, Integer warehouseId) {
        LOGGER.info("移库操作：{}", JSON.toJSONString(locationPO));
        // 初始化移库实体
        PickUpDTO pickUpDTO = new PickUpDTO();
        pickUpDTO.setFromLocationId(locationSO.getFromLocationId());
        // 移库
        List<PickUpDTO> pickUpDTOList = new ArrayList<PickUpDTO>();
        pickUpDTO.setLocationId(locationSO.getToLocationId());
        pickUpDTO.setProductSkuId(locationSO.getProductSkuId());
        pickUpDTO.setWarehouseId(warehouseId);
        pickUpDTO.setFromChannel(0);
        pickUpDTO.setFromSource(0);
        pickUpDTO.setToChannel(0);
        pickUpDTO.setToSource(0);
        pickUpDTO.setCount(locationPO.getTotalCount());
        pickUpDTOList.add(pickUpDTO);
        PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
        pickUpChangeRecordDTO.setCityId(locationPO.getOrgId());
        pickUpChangeRecordDTO.setOrderId(String.valueOf(locationPO.getBusinessId()));
        pickUpChangeRecordDTO.setOrderNo("");
        pickUpChangeRecordDTO.setDescription("PDA集货完成");
        pickUpChangeRecordDTO.setCreateUser(String.valueOf(locationPO.getLastUpdateUser()));
        iBatchInventoryManageService.pickupCompleteBySku(pickUpDTOList, pickUpChangeRecordDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void completeGatherTask(CompleteGatherTaskSO completeGatherTaskSO) {
        GatherTaskPO gatherTaskPO = CompleteGatherTaskConvertor.convertToCompleteGatherTaskPO(completeGatherTaskSO,
            completeGatherTaskSO.getCreateUser());
        // 生成集货任务并完成
        gatherTaskPO.setStatus((byte)1);
        gatherTaskMapper.insertSelective(gatherTaskPO);

        // 插入集货产品信息
        List<GatherTaskProductPO> gatherTaskProductPOs = CompleteGatherTaskConvertor
            .convertToCompleteGatherTaskProductPOList(completeGatherTaskSO.getGathertaskproducts(),
                gatherTaskPO.getId(), completeGatherTaskSO.getCreateUser(), completeGatherTaskSO.getOrgId());
        gatherTaskProductMapper.insertList(gatherTaskProductPOs);
        Map<Long, GatherTaskProductPO> productPOMAP = gatherTaskProductPOs.stream().collect(
            Collectors.toMap(GatherTaskProductPO::getProductSkuId, GatherTaskProductPO -> GatherTaskProductPO));

        Map<GatherTaskLocationPO, CompleteGatherTaskLocationSO> completeGatherTaskLocationMap =
            CompleteGatherTaskConvertor.convertToCompleteGatherTaskLocationMap(completeGatherTaskSO.getCreateUser(),
                gatherTaskPO, completeGatherTaskSO.getGathertasklocations(), productPOMAP);
        List<GatherTaskLocationPO> gatherTaskLocationPOs = new ArrayList<>(completeGatherTaskLocationMap.keySet());
        // 插入集货货位信息
        gatherTaskLocationMapper.insertList(gatherTaskLocationPOs);
        List<CompleteGatherTaskLocationSO> completeGatherTaskLocationSOs =
            completeGatherTaskSO.getGathertasklocations();

        // 移库
        gatherTaskLocationPOs.forEach(locationPO -> {
            CompleteGatherTaskLocationSO locationSO = completeGatherTaskLocationMap.get(locationPO);
            pickupAndTransferCompleteBySku(locationPO, locationSO, gatherTaskPO.getWarehouseId());
        });
    }
}
