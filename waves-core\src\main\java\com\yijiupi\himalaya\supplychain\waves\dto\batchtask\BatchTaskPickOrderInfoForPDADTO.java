package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/19
 */
public class BatchTaskPickOrderInfoForPDADTO implements Serializable {
    /**
     * 订单号
     */
    private String refOrderNo;
    /**
     * 出库单id
     */
    private Long outStockOrderId;
    /**
     * 线路名称
     */
    private String route;
    /**
     * 片区名称
     */
    private String area;
    /**
     * sku数量
     */
    private Integer skuCount;
    /**
     * 大件数
     */
    private BigDecimal packageCount;
    /**
     * 小件数
     */
    private BigDecimal unitCount;

    /**
     * 获取 订单号
     *
     * @return refOrderNo 订单号
     */
    public String getRefOrderNo() {
        return this.refOrderNo;
    }

    /**
     * 设置 订单号
     *
     * @param refOrderNo 订单号
     */
    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    /**
     * 获取 出库单id
     *
     * @return outStockOrderId 出库单id
     */
    public Long getOutStockOrderId() {
        return this.outStockOrderId;
    }

    /**
     * 设置 出库单id
     *
     * @param outStockOrderId 出库单id
     */
    public void setOutStockOrderId(Long outStockOrderId) {
        this.outStockOrderId = outStockOrderId;
    }

    /**
     * 获取 线路名称
     *
     * @return route 线路名称
     */
    public String getRoute() {
        return this.route;
    }

    /**
     * 设置 线路名称
     *
     * @param route 线路名称
     */
    public void setRoute(String route) {
        this.route = route;
    }

    /**
     * 获取 片区名称
     *
     * @return area 片区名称
     */
    public String getArea() {
        return this.area;
    }

    /**
     * 设置 片区名称
     *
     * @param area 片区名称
     */
    public void setArea(String area) {
        this.area = area;
    }

    /**
     * 获取 sku数量
     *
     * @return skuCount sku数量
     */
    public Integer getSkuCount() {
        return this.skuCount;
    }

    /**
     * 设置 sku数量
     *
     * @param skuCount sku数量
     */
    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    /**
     * 获取 大件数
     *
     * @return packageCount 大件数
     */
    public BigDecimal getPackageCount() {
        return this.packageCount;
    }

    /**
     * 设置 大件数
     *
     * @param packageCount 大件数
     */
    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    /**
     * 获取 小件数
     *
     * @return unitCount 小件数
     */
    public BigDecimal getUnitCount() {
        return this.unitCount;
    }

    /**
     * 设置 小件数
     *
     * @param unitCount 小件数
     */
    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }
}
