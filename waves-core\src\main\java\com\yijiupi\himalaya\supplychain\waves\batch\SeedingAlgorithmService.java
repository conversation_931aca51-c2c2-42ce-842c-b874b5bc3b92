package com.yijiupi.himalaya.supplychain.waves.batch;

import java.util.List;

public interface SeedingAlgorithmService {

	/**
	 * 按波次任务重放
	 */
	void replaySowingTaskByBatchNos(List<String> batchNos, Integer orgId, Integer version);

	void clearSowingTaskReplayData(List<String> batchNos, Integer orgId, Integer version);

	/**
	 * 历史拣货排序对比
	 */
	void compareHistoricalPickingSorting(List<String> batchTaskNos);

	void printWarehouseGrid(Integer warehouseId, Integer floor);

	void clearAlgoExecutionResult(List<Long> resultIds);
}
