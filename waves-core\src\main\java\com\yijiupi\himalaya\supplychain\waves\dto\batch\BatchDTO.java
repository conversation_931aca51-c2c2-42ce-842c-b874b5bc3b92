package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 波次列表查询返回
 *
 * <AUTHOR> 2018/3/15
 */
public class BatchDTO implements Serializable {
    /**
     * id
     */
    private String id;
    /**
     * 城市id
     */
    private Integer orgId;
    /**
     * 波次编号
     */
    private String batchNo;
    /**
     * 波次名称
     */
    private String batchName;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 波次状态
     */
    private Byte state;

    /**
     * 波次状态
     */
    private String stateText;
    /**
     * 拣货方式 1 按订单拣货 2 按产品拣货
     */
    private Byte pickingType;
    /**
     * 拣货方式 1 按订单拣货 2 按产品拣货
     */
    private String pickingTypeText;
    /**
     * 拣货分组策略 1货区 2货位 3类目
     */
    private Byte pickingGroupStrategy;
    /**
     * 拣货分组策略 1货区 2货位 3类目
     */
    private String pickingGroupStrategyText;
    /**
     * 关联的订单应付总金额
     */
    private BigDecimal orderAmount;
    /**
     * 订单数量
     */
    private Integer orderCount;
    /**
     * 商品种类
     */
    private Integer skuCount;
    /**
     * 大件总数
     */
    private BigDecimal packageAmount;
    /**
     * 小件总数
     */
    private BigDecimal unitAmount;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 区域id
     */
    private Long areaId;
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 线路id
     */
    private Long routeId;
    /**
     * 线路名称
     */
    private String routeName;
    /**
     * 排序号
     */
    private Integer routeSequence;
    /**
     * 订单删选 1 按区域 2 按线路
     */
    private Byte orderSelection;

    /**
     * 订单删选 1 按区域 2 按线路
     */
    private String orderSelectionText;

    /**
     * 是否包含酒批订单1=包含，0=不包含
     */
    private Byte includeJpOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 波次类别 0：酒批(默认) 1：微酒
     */
    private Byte batchType;

    /**
     * 开始拣货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 完成拣货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeTime;

    public Byte getBatchType() {
        return batchType;
    }

    public void setBatchType(Byte batchType) {
        this.batchType = batchType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取 id
     *
     * @return id id
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 id
     *
     * @param id id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 波次编号
     *
     * @return batchNo 波次编号
     */
    public String getBatchNo() {
        return this.batchNo;
    }

    /**
     * 设置 波次编号
     *
     * @param batchNo 波次编号
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * 获取 波次名称
     *
     * @return batchName 波次名称
     */
    public String getBatchName() {
        return this.batchName;
    }

    /**
     * 设置 波次名称
     *
     * @param batchName 波次名称
     */
    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    /**
     * 获取 波次状态
     *
     * @return state 波次状态
     */
    public Byte getState() {
        return this.state;
    }

    /**
     * 设置 波次状态
     *
     * @param state 波次状态
     */
    public void setState(Byte state) {
        this.state = state;
    }

    /**
     * 获取 商品种类
     *
     * @return skuCount 商品种类
     */
    public Integer getSkuCount() {
        return this.skuCount;
    }

    /**
     * 设置 商品种类
     *
     * @param skuCount 商品种类
     */
    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    /**
     * 获取 大件总数
     *
     * @return packageAmount 大件总数
     */
    public BigDecimal getPackageAmount() {
        return this.packageAmount;
    }

    /**
     * 设置 大件总数
     *
     * @param packageAmount 大件总数
     */
    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    /**
     * 获取 小件总数
     *
     * @return unitAmount 小件总数
     */
    public BigDecimal getUnitAmount() {
        return this.unitAmount;
    }

    /**
     * 设置 小件总数
     *
     * @param unitAmount 小件总数
     */
    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    /**
     * 获取 拣货方式 1 按订单拣货 2 按产品拣货
     *
     * @return pickingType 拣货方式 1 按订单拣货 2 按产品拣货
     */
    public Byte getPickingType() {
        return this.pickingType;
    }

    /**
     * 设置 拣货方式 1 按订单拣货 2 按产品拣货
     *
     * @param pickingType 拣货方式 1 按订单拣货 2 按产品拣货
     */
    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    /**
     * 获取 关联的订单应付总金额
     *
     * @return orderAmount 关联的订单应付总金额
     */
    public BigDecimal getOrderAmount() {
        return this.orderAmount;
    }

    /**
     * 设置 关联的订单应付总金额
     *
     * @param orderAmount 关联的订单应付总金额
     */
    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    /**
     * 获取 订单数量
     *
     * @return orderCount 订单数量
     */
    public Integer getOrderCount() {
        return this.orderCount;
    }

    /**
     * 设置 订单数量
     *
     * @param orderCount 订单数量
     */
    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Byte getPickingGroupStrategy() {
        return pickingGroupStrategy;
    }

    public void setPickingGroupStrategy(Byte pickingGroupStrategy) {
        this.pickingGroupStrategy = pickingGroupStrategy;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Long getRouteId() {
        return routeId;
    }

    public void setRouteId(Long routeId) {
        this.routeId = routeId;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public Integer getRouteSequence() {
        return routeSequence;
    }

    public void setRouteSequence(Integer routeSequence) {
        this.routeSequence = routeSequence;
    }

    public Byte getOrderSelection() {
        return orderSelection;
    }

    public void setOrderSelection(Byte orderSelection) {
        this.orderSelection = orderSelection;
    }

    /**
     * 是否包含酒批订单
     */
    public Byte getIncludeJpOrder() {
        return includeJpOrder;
    }

    /**
     * 是否包含酒批订单
     */
    public void setIncludeJpOrder(Byte includeJpOrder) {
        this.includeJpOrder = includeJpOrder;
    }

    /**
     * @return the stateText
     */
    public String getStateText() {
        return stateText;
    }

    /**
     * @param stateText the stateText to set
     */
    public void setStateText(String stateText) {
        this.stateText = stateText;
    }

    /**
     * @return the orderSelectionText
     */
    public String getOrderSelectionText() {
        return orderSelectionText;
    }

    /**
     * @param orderSelectionText the orderSelectionText to set
     */
    public void setOrderSelectionText(String orderSelectionText) {
        this.orderSelectionText = orderSelectionText;
    }

    /**
     * @return the pickingTypeText
     */
    public String getPickingTypeText() {
        return pickingTypeText;
    }

    /**
     * @param pickingTypeText the pickingTypeText to set
     */
    public void setPickingTypeText(String pickingTypeText) {
        this.pickingTypeText = pickingTypeText;
    }

    /**
     * @return the pickingGroupStrategyText
     */
    public String getPickingGroupStrategyText() {
        return pickingGroupStrategyText;
    }

    /**
     * @param pickingGroupStrategyText the pickingGroupStrategyText to set
     */
    public void setPickingGroupStrategyText(String pickingGroupStrategyText) {
        this.pickingGroupStrategyText = pickingGroupStrategyText;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }
}
