package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.PackageOrderItemBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
@Component
public class BatchConvertor {

    @Autowired
    private SowTaskMapper sowTaskMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private PackageOrderItemBL packageOrderItemBL;
    private static final Logger LOG = LoggerFactory.getLogger(BatchConvertor.class);

    /**
     * 获取波次状态
     *
     * @return
     */
    public Integer getBatchState(List<BatchTaskPO> batchTaskPOS, String batchId, Integer orgId) {
        Integer batchState = BatchStateEnum.PICKING.getType();
        long count = 0;
        if (CollectionUtils.isNotEmpty(batchTaskPOS)) {
            // 如果波次任务中不包含，拣货中，待拣货，待调度，说明本波次中已经全部拣货完毕
            count = batchTaskPOS.stream().filter(n -> TaskStateEnum.未分拣.getType() == n.getTaskState().byteValue()
                || TaskStateEnum.分拣中.getType() == n.getTaskState().byteValue()).count();
        }
        if (count != 0) {
            List<String> batchTaskNoList = batchTaskPOS.stream()
                .filter(n -> TaskStateEnum.未分拣.getType() == n.getTaskState().byteValue()
                    || TaskStateEnum.分拣中.getType() == n.getTaskState().byteValue())
                .map(BatchTaskPO::getBatchTaskNo).distinct().collect(Collectors.toList());
            LOG.info("拣货任务未完成:{}", JSON.toJSONString(batchTaskNoList));
        }
        if (count == 0) {
            // 如果波次存在播种任务，且播种任务中包含未播种、播种中状态的，则波次状态为“播种中”
            batchState = getBatchStateEnumToSown(batchId, orgId);
        }

        if (batchState.equals(BatchStateEnum.PICKINGEND.getType())) {
            // 判断托盘复核是否全部完成
            List<Byte> reviewStates = new ArrayList<>();
            reviewStates.add(PackageReviewStateEnum.待复核.getType());
            reviewStates.add(PackageReviewStateEnum.复核中.getType());
            List<PackageOrderItemDTO> packageOrderItemDTOS = packageOrderItemBL.findPackageOrderItemsByBatchId(batchId,
                orgId, PackageTypeEnum.托盘.getType(), reviewStates);
            if (CollectionUtils.isNotEmpty(packageOrderItemDTOS)) {
                String boxCodeNos = packageOrderItemDTOS.stream().map(PackageOrderItemDTO::getBoxCodeNo)
                    .collect(Collectors.joining(","));
                LOG.info("{}波次完成拣货,托盘复核任务未完成:{}", batchId, boxCodeNos);
                batchState = BatchStateEnum.REVIEWING.getType();
            }
        }
        return batchState;
    }

    /**
     * 设置波次状态为“播种中”
     */
    private Integer getBatchStateEnumToSown(String batchId, Integer orgId) {
        Integer batchState = BatchStateEnum.PICKINGEND.getType();
        // 根据波次id查询所有播种任务
        List<SowTaskPO> sowTaskPOList = sowTaskMapper.listSowTaskByBatchId(batchId, orgId);
        LOG.info("播种任务列表：{}, batchId: {}", JSON.toJSONString(sowTaskPOList), batchId);
        // 拣货任务全部完成时，如果波次存在播种任务，且播种任务中包含未播种、播种中状态的，则波次状态为“播种中”
        if (CollectionUtils.isNotEmpty(sowTaskPOList)) {
            long sowTaskCount = sowTaskPOList.stream()
                .filter(n -> SowTaskStateEnum.待播种.getType() == n.getState().byteValue()
                    || SowTaskStateEnum.播种中.getType() == n.getState().byteValue()
                    || SowTaskStateEnum.待集货.getType() == n.getState().byteValue())
                .count();
            if (sowTaskCount > 0) {
                batchState = BatchStateEnum.SOWN.getType();
            }
        }
        return batchState;
    }

    public List<Long> filterNeedFinishPickOrderIds(List<Long> orderIds, BatchPO batchPO) {
        if (batchPO == null) {
            throw new BusinessException("波次不能为空");
        }
        if (org.springframework.util.CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }

        // 微酒
        if (Objects.equals(batchPO.getBatchType(), BatchTypeEnum.微酒.getType())) {
            return orderIds;
            // 第三方
        }
        if (BatchTypeEnum.isApplyOrderBatch(batchPO.getBatchType())) {
            return orderIds;
            // 酒批
        }
        // 过滤订单
        if (batchPO.getBatchOrderType() == null
            || Objects.equals(batchPO.getBatchOrderType(), BatchOrderTypeEnum.普通订单.getType())) {
            // 1、过滤掉没有生成拣货任务的出库单id
            List<Long> noBatchTaskOrderIds = outStockOrderItemMapper.listOrderIdNoBatchTask(orderIds);
            if (!org.springframework.util.CollectionUtils.isEmpty(noBatchTaskOrderIds)) {
                LOG.info("未全部生成拣货任务的出库单id：{}", JSON.toJSONString(noBatchTaskOrderIds));
                Map<Long, Long> noBatchTaskOrderMap =
                    noBatchTaskOrderIds.stream().collect(Collectors.toMap(k -> k, v -> v));
                orderIds = orderIds.stream().filter(p -> Objects.isNull(noBatchTaskOrderMap.get(p)))
                    .collect(Collectors.toList());
            }

            // 2、过滤掉没有完成拣货的出库单id
            if (!org.springframework.util.CollectionUtils.isEmpty(orderIds)) {
                List<Long> noPickOrderIds = outStockOrderItemMapper.listOrderIdNoPick(orderIds);
                if (!org.springframework.util.CollectionUtils.isEmpty(noPickOrderIds)) {
                    LOG.info("未完成拣货的出库单id：{}", JSON.toJSONString(noPickOrderIds));
                    Map<Long, Long> noPickOrderIdMap =
                        noPickOrderIds.stream().collect(Collectors.toMap(k -> k, v -> v));
                    orderIds = orderIds.stream().filter(p -> Objects.isNull(noPickOrderIdMap.get(p)))
                        .collect(Collectors.toList());
                }
            }
        }

        return orderIds;
    }

}
