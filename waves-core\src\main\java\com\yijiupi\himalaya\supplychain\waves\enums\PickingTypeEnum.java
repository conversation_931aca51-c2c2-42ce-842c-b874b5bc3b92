package com.yijiupi.himalaya.supplychain.waves.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 拣货方式 常量类
 *
 * <AUTHOR> 2018/3/13
 */
public enum PickingTypeEnum {
    /**
     * 枚举
     */
    订单拣货((byte)1, "订单"), 产品拣货((byte)2, "产品"), 通道拣货((byte)3, " 通道");

    /**
     * type
     */
    private byte type;
    /**
     * 名称
     */
    private String valueName;

    PickingTypeEnum(byte type, String valueName) {
        this.type = type;
        this.valueName = valueName;
    }

    public byte getType() {
        return type;
    }

    public String getValueName() {
        return valueName;
    }

    /**
     * 根据枚举值获取枚举对象名称
     * 
     * @param value
     * @return
     */
    public static String getEnumByValue(Byte value) {
        PickingTypeEnum pickingTypeEnum = null;
        if (value != null) {
            pickingTypeEnum = cache.get(value);
        }
        return pickingTypeEnum == null ? null : pickingTypeEnum.name();
    }

    public static String getEnumByName(Byte value) {
        PickingTypeEnum pickingTypeEnum = null;
        if (value != null) {
            pickingTypeEnum = cache.get(value);
        }
        return pickingTypeEnum == null ? null : pickingTypeEnum.getValueName();
    }

    private static Map<Byte, PickingTypeEnum> cache =
        EnumSet.allOf(PickingTypeEnum.class).stream().collect(Collectors.toMap(p -> p.type, p -> p));
}
