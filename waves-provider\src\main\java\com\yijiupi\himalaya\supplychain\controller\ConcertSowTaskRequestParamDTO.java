package com.yijiupi.himalaya.supplychain.controller;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 播种任务列表查询对象
 */
public class ConcertSowTaskRequestParamDTO implements Serializable {

    private static final long serialVersionUID = -1019080855983257297L;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 分拣任务id
     */
    private List<Long> taskIds;
    private Long locationId;

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public List<Long> getTaskIds() {
        return taskIds;
    }

    public void setTaskIds(List<Long> taskIds) {
        this.taskIds = taskIds;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
