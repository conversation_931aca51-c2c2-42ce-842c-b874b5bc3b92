package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title: BatchTaskItemPickCompleteDetailQueryDTO
 * @description:
 * @date 2022-11-11 16:22
 */
public class BatchTaskItemPickCompleteDetailQueryDTO implements Serializable {
    /**
     * 拣货任务id
     */
    private String batchTaskId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 产品skuId
     */
    private Long skuId;

    /**
     * 获取 拣货任务id
     *
     * @return batchTaskId 拣货任务id
     */
    public String getBatchTaskId() {
        return this.batchTaskId;
    }

    /**
     * 设置 拣货任务id
     *
     * @param batchTaskId 拣货任务id
     */
    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 产品skuId
     *
     * @return skuId 产品skuId
     */
    public Long getSkuId() {
        return this.skuId;
    }

    /**
     * 设置 产品skuId
     *
     * @param skuId 产品skuId
     */
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }
}
