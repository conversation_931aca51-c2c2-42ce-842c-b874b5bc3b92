package com.yijiupi.himalaya.supplychain.waves.advice;


import com.yijiupi.himalaya.distributedlock.constants.Constants;

import java.lang.annotation.*;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/5/24
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface MarkMethodInvokeFlag {

    String key() default "";

    String conditions() default Constants.DEFAULT_VALUE;

    String warehouseId() default "1";

    String userId() default  "1";
}