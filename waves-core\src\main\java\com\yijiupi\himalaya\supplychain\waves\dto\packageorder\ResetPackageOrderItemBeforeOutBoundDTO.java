package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.io.Serializable;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/3/11
 */
public class ResetPackageOrderItemBeforeOutBoundDTO implements Serializable {
    /**
     * 出库批次号
     */
    private String boundNo;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 获取 出库批次号
     *
     * @return boundNo 出库批次号
     */
    public String getBoundNo() {
        return this.boundNo;
    }

    /**
     * 设置 出库批次号
     *
     * @param boundNo 出库批次号
     */
    public void setBoundNo(String boundNo) {
        this.boundNo = boundNo;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }
}
