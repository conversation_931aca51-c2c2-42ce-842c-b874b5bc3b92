package com.yijiupi.himalaya.supplychain.waves.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.batch.IOrderLocationPalletManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderpallet.OrderLocationPalletBL;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.BatchOrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletQueryDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/23
 */
@Service
public class OrderLocationPalletManageServiceImpl implements IOrderLocationPalletManageService {

    @Autowired
    private OrderLocationPalletBL orderLocationPalletBL;

    private static final Logger logger = LoggerFactory.getLogger(OrderLocationPalletManageServiceImpl.class);

    @Override
    public void addPallet(OrderLocationPalletDTO dto) {
        orderLocationPalletBL.addPallet(dto);
    }

    /**
     * 更新订单货位托盘关系
     */
    @Override
    public void editPallet(OrderLocationPalletDTO dto) {
        AssertUtils.notNull(dto.getBatchTaskId(), "拣货任务 id 不能为空");
        logger.info("更新订单货位托盘关系: {}", JSON.toJSONString(dto, SerializerFeature.WriteMapNullValue));
        orderLocationPalletBL.editPallet(dto);
    }

    @Override
    public List<OrderLocationPalletDTO> findPalletByCondition(OrderLocationPalletQueryDTO queryDTO) {
        return orderLocationPalletBL.findPalletByCondition(queryDTO);
    }

    @Override
    public void deletePalletByCondition(OrderLocationPalletQueryDTO deleteDTO) {
        orderLocationPalletBL.deletePalletByCondition(deleteDTO);
    }

    @Override
    public void addPalletByOrderId(OrderLocationPalletDTO dto) {
        orderLocationPalletBL.addPalletByOrderId(dto);
    }

    /**
     * 批量根据订单id新增订单货位托盘关系
     *
     * @param dto
     */
    @Override
    public void addPalletByOrderIdBatch(BatchOrderLocationPalletDTO dto) {
        AssertUtils.notEmpty(dto.getPalletDTOList(), "订单托盘关系信息不能为空！");
        AssertUtils.notNull(dto.getOptUserId(), "操作人不能为空！");

        orderLocationPalletBL.addPalletByOrderIdBatch(dto);
    }
}
