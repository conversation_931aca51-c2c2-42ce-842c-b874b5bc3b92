package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/16
 */
public class OrderPickSequenceQueryDTO implements Serializable {
    private static final long serialVersionUID = -6937497524582618597L;
    /**
     * 城市id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 订单编号
     */
    private List<String> orderNos;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<String> getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }
}
