package com.yijiupi.himalaya.supplychain.controller.sow;

import com.yijiupi.himalaya.base.search.PageCondition;

import java.util.List;

public class BatchTaskSowNoParam extends PageCondition {
    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 任务状态
     */
    private List<Integer> taskStates;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 播种任务编号集合
     */
    private List<String> sowTaskNos;
    /**
     * 播种任务名称
     */
    private String sowTaskName;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 条码或者产品名称
     */
    private String content;


    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public List<Integer> getTaskStates() {
        return taskStates;
    }

    public void setTaskStates(List<Integer> taskStates) {
        this.taskStates = taskStates;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public List<String> getSowTaskNos() {
        return sowTaskNos;
    }

    public void setSowTaskNos(List<String> sowTaskNos) {
        this.sowTaskNos = sowTaskNos;
    }

    public String getSowTaskName() {
        return sowTaskName;
    }

    public void setSowTaskName(String sowTaskName) {
        this.sowTaskName = sowTaskName;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 条码或者产品名称
     *
     * @return content 条码或者产品名称
     */
    public String getContent() {
        return this.content;
    }

    /**
     * 设置 条码或者产品名称
     *
     * @param content 条码或者产品名称
     */
    public void setContent(String content) {
        this.content = content;
    }
}
