package com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation.bo;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * <AUTHOR>
 * @since 2023-11-23 16:38
 **/
public class OutStockOrderBO {

    private final OutStockOrderPO order;

    private final boolean isAllot;

    private final Integer toWarehouseId;

    public OutStockOrderBO(OutStockOrderPO order, boolean isAllot, Integer toWarehouseId) {
        this.order = order;
        this.isAllot = isAllot;
        this.toWarehouseId = toWarehouseId;
    }

    public static OutStockOrderBO of(OutStockOrderPO order, boolean isAllot, Integer fromWarehouseId) {
        return new OutStockOrderBO(order, isAllot, fromWarehouseId);
    }

    public OutStockOrderPO getOrder() {
        return order;
    }

    public boolean isAllot() {
        return isAllot;
    }

    public Integer getToWarehouseId() {
        return toWarehouseId;
    }
}
