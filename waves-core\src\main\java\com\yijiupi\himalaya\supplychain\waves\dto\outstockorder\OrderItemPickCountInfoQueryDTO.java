package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
public class OrderItemPickCountInfoQueryDTO implements Serializable {
    /**
     * 出库单项id
     */
    private List<Long> orderItemIdList;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 获取 出库单项id
     *
     * @return orderItemIdList 出库单项id
     */
    public List<Long> getOrderItemIdList() {
        return this.orderItemIdList;
    }

    /**
     * 设置 出库单项id
     *
     * @param orderItemIdList 出库单项id
     */
    public void setOrderItemIdList(List<Long> orderItemIdList) {
        this.orderItemIdList = orderItemIdList;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }
}
