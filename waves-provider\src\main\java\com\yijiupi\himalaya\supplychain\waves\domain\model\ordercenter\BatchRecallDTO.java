package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-15 15:26
 **/
public class BatchRecallDTO implements Serializable {
    /**
     * 取货仓库 id
     */
    private Integer pickupId;
    /**
     * 订单 id
     */
    private List<Long> orderIdList;
    /**
     * 操作人 id
     */
    private Integer optUserId;

    public Integer getPickupId() {
        return pickupId;
    }

    public void setPickupId(Integer pickupId) {
        this.pickupId = pickupId;
    }

    public List<Long> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Long> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public Integer getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }
}
