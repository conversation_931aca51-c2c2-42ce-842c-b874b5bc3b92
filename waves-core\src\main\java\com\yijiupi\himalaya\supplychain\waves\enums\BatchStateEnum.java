package com.yijiupi.himalaya.supplychain.waves.enums;

import org.springframework.util.StringUtils;

/**
 * 波次状态枚举
 * 
 * <AUTHOR>
 * @since 2018/4/2 17:00
 */
public enum BatchStateEnum {
    /**
     * 待调度
     */
    PENDINGSCHEDULE(0, "待调度"),

    /**
     * 待拣货
     */
    PENDINGPICKING(1, "待拣货"),

    /**
     * 拣货中
     */
    PICKING(2, "拣货中"),

    /**
     * 已拣货
     */
    PICKINGEND(3, "已拣货"),

    /**
     * 已出库
     */
    ALREADYOUTOFSTORE(4, "已出库"),

    /**
     * 已取消
     */
    CANCEL(5, "已取消"),

    /**
     * 已作废
     */
    TOVOID(6, "已作废"),

    /**
     * 播种中
     */
    SOWN(7, "播种中"),

    /**
     * 复核中
     */
    REVIEWING(8, "复核中"),

    /**
     * 归档中
     */
    FILE_ARCHIVING(17, "归档中"),

    /**
     * 已归档
     */
    FILE_ARCHIVED(18, "已归档"),;

    /**
     * type
     */
    private final Integer type;
    /**
     * text
     */
    private final String text;

    BatchStateEnum(Integer type, String text) {
        this.type = type;
        this.text = text;
    }

    public Integer getType() {
        return this.type;
    }

    public String getText() {
        return this.text;
    }

    public static BatchStateEnum getEnum(Integer type) {
        BatchStateEnum e = null;

        if (type != null) {
            for (BatchStateEnum o : values()) {
                if (o.getType().equals(type)) {
                    e = o;
                }
            }
        }

        return e;
    }

    public static BatchStateEnum getEnum(String text) {
        BatchStateEnum e = null;
        if (!StringUtils.isEmpty(text)) {
            for (BatchStateEnum o : values()) {
                if (o.getText().equals(text)) {
                    e = o;
                }
            }
        }
        return e;
    }

    public static boolean couldPick(Integer state) {
        if (PICKINGEND.getType().equals(state)) {
            return Boolean.FALSE;
        }
        if (ALREADYOUTOFSTORE.getType().equals(state)) {
            return Boolean.FALSE;
        }
        if (CANCEL.getType().equals(state)) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    public boolean valueEquals(Number number) {
        return number != null && this.type.equals(number.intValue());
    }

}
