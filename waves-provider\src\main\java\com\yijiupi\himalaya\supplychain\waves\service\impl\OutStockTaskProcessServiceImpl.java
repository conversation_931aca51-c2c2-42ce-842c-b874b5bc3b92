package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.common.util.resourcelock.annotation.ResourceLockAnnotation;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.lack.MarkLackLockHelper;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.lack.OutStockOrderLackBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder.OutStockOrderQueryBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.OrderItemTaskInfoDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.supplychain.ordercenter.consant.SyncTraceTypeConstants;
import com.yijiupi.himalaya.supplychain.waves.batch.IOutStockTaskProcessService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderCenterBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OutStockOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.MarkLackBO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOutStockDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.PartSendWsmDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> 2018-04-17
 */
@Service(timeout = 120000)
public class OutStockTaskProcessServiceImpl implements IOutStockTaskProcessService {
    @Autowired
    private OutStockOrderBL outStockOrderBL;
    @Autowired
    private OrderCenterBL orderCenterBL;
    @Autowired
    private OutStockOrderLackBL outStockOrderLackBL;
    @Autowired
    private OutStockOrderQueryBL outStockOrderQueryBL;

    private static final Logger LOG = LoggerFactory.getLogger(OutStockTaskProcessServiceImpl.class);

    @Override
    public void outStockTaskProcess(List<OutStockOrderNewDTO> outStockOrderNewDTOList) {
        outStockOrderBL.outStockTaskProcess(outStockOrderNewDTOList);
    }

    @Override
    @DistributeLock(conditions = "#outStockDTO.warehouseId", sleepMills = 3000, key = "outStockProcess")
    public void outStockProcess(OutStockDTO outStockDTO) {
        AssertUtils.notNull(outStockDTO.getWarehouseId(), "仓库ID不能为空");
        String refOrderNo = outStockDTO.getOutStockOrderDTOList().get(0).getRefOrderNo();
        outStockOrderBL.checkOrderExits(refOrderNo, outStockDTO.getWarehouseId());
        try {
            preProcessOrder(outStockDTO);
            outStockOrderBL.outStockProcess(outStockDTO);
        } catch (Exception oe) {
            LOG.error(String.format("处理出库单异常：%s,参数：%s", oe.getMessage(), JSON.toJSONString(outStockDTO)), oe);
            throw oe;
        } finally {
            proccessInventory(outStockDTO);
        }
    }

    private void preProcessOrder(OutStockDTO inStockDTO) {
        inStockDTO.getOutStockOrderDTOList().forEach(order -> {
            // 如果没设置是否立即变更库存，统一设置为false
            if (order.getDirectChangeStock() == null) {
                order.setDirectChangeStock(false);
            }
        });
    }

    private void proccessInventory(OutStockDTO outStockDTO) {
        List<OutStockOrderNewDTO> lstOutStockOrderDTOS = new ArrayList<>();
        outStockDTO.getOutStockOrderDTOList().forEach(p -> {
            if (p.getDirectChangeStock() != null && p.getDirectChangeStock()) {
                lstOutStockOrderDTOS.add(p);
            }
        });
        if (lstOutStockOrderDTOS.size() > 0) {
            outStockOrderBL.processInventoryByErp(lstOutStockOrderDTOS);
        }
    }

    /**
     * 根据订单号模糊查询已拣货的订单号列表
     *
     * @param outStockOrderDTO
     * @return
     */
    @Override
    public List<String> listRefOrderNoByLike(OutStockOrderDTO outStockOrderDTO) {
        return outStockOrderBL.listRefOrderNoByLike(outStockOrderDTO);
    }

    /**
     * 团购订单出库(不校验波次状态)
     * 
     * @param lstOrderIds
     */
    @Override
    public void directOutStockGroupOrder(List<Long> lstOrderIds) {
        LOG.info("团购订单直接出库参数:{}", JSON.toJSONString(lstOrderIds));
        AssertUtils.notEmpty(lstOrderIds, "参数不能为空");
        outStockOrderBL.directOutStockGroupOrder(lstOrderIds);
    }

    /**
     * 直接出库
     * 
     * @param directOutStockDTOS
     * @param orgId
     * @param warehouseId
     */
    @Override
    public void directOutStockByOrder(List<DirectOutStockDTO> directOutStockDTOS, Integer orgId, Integer warehouseId) {
        LOG.info("订单出库参数:{}", JSON.toJSONString(directOutStockDTOS));
        AssertUtils.notEmpty(directOutStockDTOS, "参数不能为空");
        outStockOrderBL.directOutStockByOrder(directOutStockDTOS, orgId, warehouseId, true);
    }

    /**
     * 直接出库
     * 
     * @param directOutStockDTOS
     * @param orgId
     * @param warehouseId
     */
    @Override
    public void directOutStockByOrderWithNoCheckBatch(List<DirectOutStockDTO> directOutStockDTOS, Integer orgId,
        Integer warehouseId) {
        LOG.info("订单出库参数:{}", JSON.toJSONString(directOutStockDTOS));
        AssertUtils.notEmpty(directOutStockDTOS, "参数不能为空");
        outStockOrderBL.directOutStockByOrder(directOutStockDTOS, orgId, warehouseId, false);
    }

    /**
     * 查询订单出库位
     */
    @Override
    public List<OutStockOrderLocationDTO>
        findOutStockOrderLocation(List<OutStockOrderLocationDTO> outStockOrderLocationDTOS) {
        return outStockOrderBL.findOutStockOrderLocation(outStockOrderLocationDTOS);
    }

    @Override
    public void saasDirectOutStockByOrder(BatchOutStockDTO batch) {
        outStockOrderBL.saasDirectOutStockByOrder(batch);
    }

    /**
     * 直接出库
     * 
     * @param directOutStockSetDTO
     */
    @Override
    public void directOutStockByOrderNew(DirectOutStockSetDTO directOutStockSetDTO) {
        LOG.info("商户订单出库参数:{}", JSON.toJSONString(directOutStockSetDTO));
        AssertUtils.notNull(directOutStockSetDTO, "参数不能为空");
        AssertUtils.notEmpty(directOutStockSetDTO.getdTOList(), "参数不能为空");
        outStockOrderBL.directOutStockByOrder(directOutStockSetDTO.getdTOList(), directOutStockSetDTO.getOrgId(),
            directOutStockSetDTO.getWarehouseId(), true);
    }

    /**
     * 缺货通知
     * 
     * @param lackDTOList
     * @param stringBuffer
     * @param operatorUserId
     */
    @Deprecated
    @Override
    public void invokeOrderCenterByLack(List<OutStockLackDTO> lackDTOList, StringBuffer stringBuffer,
        Integer operatorUserId) {
        LOG.info("订单缺货通知参数:{}", JSON.toJSONString(lackDTOList));
        AssertUtils.notEmpty(lackDTOList, "参数不能为空");
        List<PartSendWsmDTO> partSendWsmDTOList = lackDTOList.stream().map(p -> {
            PartSendWsmDTO partSendWsmDTO = new PartSendWsmDTO();
            BeanUtils.copyProperties(p, partSendWsmDTO);
            return partSendWsmDTO;
        }).collect(Collectors.toList());
        orderCenterBL.orderMarkNotify(new MarkLackBO(partSendWsmDTOList, null, operatorUserId));
    }

    /**
     * 订单缺货通知修改
     *
     * @param lackDTOList
     * @param bufferMap : key 是businessId
     * @param operatorUserId
     */
    @Override
    public void notifyOrderCenterLack(List<OutStockLackDTO> lackDTOList, Map<String, StringBuffer> bufferMap,
        Integer operatorUserId) {
        LOG.info("订单缺货通知参数:{}", JSON.toJSONString(lackDTOList));
        AssertUtils.notEmpty(lackDTOList, "参数不能为空");
        List<PartSendWsmDTO> partSendWsmDTOList = lackDTOList.stream().map(p -> {
            PartSendWsmDTO partSendWsmDTO = new PartSendWsmDTO();
            BeanUtils.copyProperties(p, partSendWsmDTO);
            return partSendWsmDTO;
        }).collect(Collectors.toList());
        orderCenterBL.orderMarkNotify(new MarkLackBO(partSendWsmDTOList, bufferMap, operatorUserId));
    }

    /**
     * 订单缺货通知修改
     *
     * @param lackDTO
     */
    @Override
    public void notifyOrderCenterLackProduct(NotifyOrderCenterLackDTO lackDTO) {
        LOG.info("订单缺货通知参数:{}", JSON.toJSONString(lackDTO));
        AssertUtils.notEmpty(lackDTO.getLackDTOList(), "参数不能为空");
        List<PartSendWsmDTO> partSendWsmDTOList = lackDTO.getLackDTOList().stream().map(p -> {
            PartSendWsmDTO partSendWsmDTO = new PartSendWsmDTO();
            BeanUtils.copyProperties(p, partSendWsmDTO);
            return partSendWsmDTO;
        }).collect(Collectors.toList());
        Integer traceType = SyncTraceTypeConstants.getTraceTypByOrderExtendMapEnum(lackDTO.getLackType());
        orderCenterBL.orderMarkNotify(
            new MarkLackBO(partSendWsmDTOList, lackDTO.getBufferMap(), lackDTO.getOperatorUserId(), traceType));
    }

    @Override
    @ResourceLockAnnotation(queryServiceClass = MarkLackLockHelper.MarkLackLockQuery.class, lockedMessage = "仓库正在作业, 请稍后再试")
    public void markPartSend(OutStockMarkLackDTO dto) {
        List<OutStockLackDTO> lackDTOList = dto.getOutStockLackDTOList();
        LOG.info("供应链标记订单缺货参数:{}", JSON.toJSONString(dto));
        AssertUtils.notEmpty(lackDTOList, "参数不能为空");
        List<String> refOrderNoList = dto.getOutStockLackDTOList().stream().map(OutStockLackDTO :: getOrderNo).distinct().collect(Collectors.toList());
        Integer warehouseId = dto.getOutStockLackDTOList().get(0).getWarehouseId();
        try {
            List<OrderItemTaskInfoDTO> orderItemTaskInfoDTOList = outStockOrderQueryBL.findRelateBathTaskIdByOrderNo(refOrderNoList, warehouseId);
            if (CollectionUtils.isEmpty(orderItemTaskInfoDTOList)) {
                outStockOrderLackBL.markPartSend(dto);
                return;
            }
            outStockOrderLackBL.markPartSend(dto, orderItemTaskInfoDTOList);
        } catch (Exception e) {
            if (e.getMessage().contains("已经加锁")) {
                throw new BusinessValidateException("拣货任务正在处理中，请稍后再试！");
            }
            throw e;
        }
    }

    /**
     * 缺货标记试算接口
     */
    @Override
    @ResourceLockAnnotation(queryServiceClass = MarkLackLockHelper.MarkLackLockQuery.class, lockedMessage = "仓库正在作业, 请稍后再试")
    public List<OutStockLackCalResultDTO> calMarkPartSend(OutStockLackDTO outStockLackDTO) {
        return orderCenterBL.calMarkPartSend(outStockLackDTO);
    }

    /**
     * 标记缺货的时候，获取拣货中标记缺货的数据 波次状态为待拣货 和 已拣货的不用返回。 订单项对应的拣货任务明细状态是待拣货的也不用处理
     */
    @Override
    public List<PartMarkPickInfoDTO> getPartMarkPickInfo(PartMarkPickInfoQueryDTO queryDTO) {
        AssertUtils.notEmpty(queryDTO.getBusinessIds(), "订单信息不能为空！");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库信息不能为空！");
        return orderCenterBL.getPartMarkPickInfo(queryDTO);
    }

    /**
     * 检测是否对装箱了的订单项标记缺货或是装箱了的赠品是否有缺货
     *
     * @param dto 标记缺货参数
     */
    @Override
    public List<CheckHasPackagedOrderResult> checkHasPackagedOrder(OutStockMarkLackDTO dto) {
        return orderCenterBL.checkHasPackagedOrder(dto);
    }
}
