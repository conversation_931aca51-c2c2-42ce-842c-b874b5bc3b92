package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.Pager;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.orgsettings.service.ICarService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutBoundBatchDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutBoundBatchQueryDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutBoundBatchQueryService;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IPassageService;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.PackageOrderItemConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.SowConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.model.OutStockOrderItemQuerySO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderItemPickCountInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderItemPickCountInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.secondsort.*;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderSaveDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SownOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 二次分拣
 *
 * <AUTHOR>
 * @date 2021/6/2 15:31
 */

@Service
public class SecondSortBL {
    private static final Logger LOG = LoggerFactory.getLogger(SecondSortBL.class);

    @Autowired
    private PackageOrderItemMapper packageOrderItemMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;

    @Autowired
    private BatchTaskMapper batchTaskMapper;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private SowTaskItemMapper sowTaskItemMapper;

    @Autowired
    private SowTaskMapper sowTaskMapper;
    @Autowired
    private SowManagerBL sowManagerBL;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private SowOrderMapper sowOrderMapper;
    @Autowired
    private PackageOrderItemBL packageOrderItemBL;
    @Reference
    private IOutBoundBatchQueryService iOutBoundBatchQueryService;
    @Reference
    private ILocationService iLocationService;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Reference
    private IVariableValueService iVariableValueService;
    @Reference(timeout = 120000)
    private ICarService iCarService;
    @Autowired
    private BatchOrderProcessBL batchOrderProcessBL;
    @Reference
    private IPassageService iPassageService;

    /**
     * 修改二次分拣托盘
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSecondSortTaskPallet(SecondSortDTO dto) {
        AssertUtils.notNull(dto.getBoxCode(), "托盘编号不能为空");
        /** 存在则修改，不存在则新增 */
        if (dto.getId() != null) {
            PackageOrderItemPO packageOrderItemPO = packageOrderItemMapper.selectByPrimaryKey(dto.getId());
            if (packageOrderItemPO == null) {
                return;
            }
            updatePackageOrderItemById(packageOrderItemPO, dto.getBoxCode());
        } else {
            AssertUtils.notNull(dto.getOrgId(), "城市id不能为空");
            AssertUtils.notNull(dto.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(dto.getBatchTaskItemId(), "拣货任务项不能为空");
            AssertUtils.notNull(dto.getOrderNo(), "订单不能为空");
            AssertUtils.notNull(dto.getOrderItemId(), "订单明细id不能为空");
            List<PackageOrderItemPO> packageOrderItems = findPackageOrderItem(dto);
            if (CollectionUtils.isEmpty(packageOrderItems)) {
                return;
            }
            PackageOrderItemPO packageOrderItemPO = packageOrderItems.get(0);
            if (packageOrderItemPO.getId() != null) {
                updatePackageOrderItemById(packageOrderItemPO, dto.getBoxCode());
            } else {
                insertPackageOrderItem(packageOrderItemPO, dto);
            }
        }
    }

    /**
     * 更新托盘号
     *
     * @param packageOrderItemPO
     * @param boxCode
     */
    private void updatePackageOrderItemById(PackageOrderItemPO packageOrderItemPO, String boxCode) {
        PackageOrderItemDTO dto = new PackageOrderItemDTO();
        BeanUtils.copyProperties(packageOrderItemPO, dto);
        dto.setBoxCode(boxCode);
        dto.setBoxCodeNo(boxCode);
        if (dto.getOverUnitTotalCount() == null) {
            dto.setOverUnitTotalCount(dto.getUnitTotalCount());
        }
        packageOrderItemMapper.updateBoxCodeById(dto);
    }

    private List<PackageOrderItemPO> findPackageOrderItem(SecondSortDTO dto) {
        SecondSortQueryDTO queryDTO = new SecondSortQueryDTO();
        queryDTO.setOrgId(dto.getOrgId());
        queryDTO.setWarehouseId(dto.getWarehouseId());
        queryDTO.setBatchTaskId(dto.getBatchTaskId());
        queryDTO.setBatchTaskItemId(dto.getBatchTaskItemId());
        queryDTO.setRefOrderNo(dto.getOrderNo());
        queryDTO.setRefOrderItemId(dto.getOrderItemId());
        return packageOrderItemBL.listSowBoxcodeItems(queryDTO);
    }

    private void insertPackageOrderItem(PackageOrderItemPO packageOrderItemPO, SecondSortDTO dto) {
        packageOrderItemPO.setPackageType(PackageTypeEnum.二次分拣.getType());
        packageOrderItemPO.setId(UuidUtil.getUUidInt());
        packageOrderItemPO.setBoxCode(dto.getBoxCode());
        packageOrderItemPO.setBoxCodeNo(dto.getBoxCode());
        packageOrderItemPO.setCreateTime(new Date());
        packageOrderItemPO.setLastUpdateTime(new Date());
        packageOrderItemPO.setCreateUser(dto.getUserName());
        packageOrderItemPO.setLastUpdateUser(dto.getUserName());
        if (packageOrderItemPO.getOverUnitTotalCount() == null) {
            packageOrderItemPO.setOverUnitTotalCount(packageOrderItemPO.getUnitTotalCount());
        }
        packageOrderItemMapper.insertBatch(Collections.singletonList(packageOrderItemPO));
    }

    /**
     * 开始二次分拣，如果已经二级分拣过，则取已有数据；如果不存在，则新增二次分拣 0、根据拣货任务明细找到播种任务 1、如果播种任务存在 1-1、拣货任务明细找到是否存在播种项id（batchtaskitem）
     * 1-2、如果存在，根据播种项id找到播种任务，校验集货位与拣货任务出库为是否一致，不一致报错提示（sowtaskitem） 1-3、根据拣货任务项在orderitemtaskinfo找到订单项，
     * 1-4、是否所有订单项已播种，并返回（packageorderitem） 2、如果没关联播种项id，则重新生成
     * 2-1、根据拣货任务项，在orderitemtaskinfo找到波次，根据波次判断播种任务是否存在（sowtask） 2-2、如果不存在播种任务，则创建播种任务 2-3、如果不存在播种明细，则创建播种明细项
     * 2-4、创建订单项播种号，并返回（packageorderitem）
     * <p>
     * 目前只针对一个波次生成一个播种任务的情况
     *
     * @param queryDTO
     * @return
     */

    @Transactional(rollbackFor = Exception.class)
    public SowSecondDTO beginSecondSortTask(SecondSortQueryDTO queryDTO) {
        /** 校验二次分拣 */
        checkSecondSow(queryDTO);
        /** 1、根据拣货任务明细找到播种任务 */
        SowTaskPO sowTaskPO = findSowByBatchItemId(queryDTO.getBatchTaskItemId());
        if (sowTaskPO != null) {
            /** 2、如果拣货任务明细关联播种任务，则直接返回存在播种任务号 */
            return findExistsPackageOrderItems(sowTaskPO, queryDTO);
        }
        /** 3、如果拣货任务明细没有关联播种任务，则创建二次分拣播种任务和播种编号 */
        return createSecondSortTask(queryDTO);
    }

    /**
     * 校验二次分拣参数
     *
     * @param queryDTO
     */
    private void checkSecondSow(SecondSortQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO.getBatchTaskItemId(), "拣货任务明细id不能为空");
        AssertUtils.notNull(queryDTO.getBatchTaskId(), "拣货任务id不能为空");
        AssertUtils.notNull(queryDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(queryDTO.getSorterId(), "分拣员id不能为空");
        AssertUtils.notNull(queryDTO.getSorterName(), "分拣员名称不能为空");
        AssertUtils.notNull(queryDTO.getLocationId(), "出库位id不能为空");
        AssertUtils.notNull(queryDTO.getLocationName(), "出库位名称不能为空");
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(queryDTO.getBatchTaskId());
        if (batchTaskPO == null) {
            throw new BusinessException("拣货任务不存在");
        }
        BatchPO batchPO = batchMapper.selectBatchByBatchNo(queryDTO.getOrgId(), batchTaskPO.getBatchNo());
        if (batchPO.getSowType() == null || batchPO.getSowType() != 2) {
            throw new BusinessValidateException("非二次分拣无法操作");
        }
    }

    /**
     * 根据拣货任务明细找到播种任务
     *
     * @param sowTaskPO
     * @param queryDTO
     * @return
     */
    private SowSecondDTO findExistsPackageOrderItems(SowTaskPO sowTaskPO, SecondSortQueryDTO queryDTO) {
        /** 1、如果播种存在，则校验集货位与拣货任务出库为是否一致 */
        if (sowTaskPO != null && !sowTaskPO.getLocationId().equals(queryDTO.getLocationId())) {
            throw new BusinessValidateException("播种任务集货位为" + sowTaskPO.getLocationName() + "，与拣货任务不同,无法播种");
        }
        /** 2、根据拣货任务明细找到播种号详情 */
        return findPackageOrderItemByBatchItemIds(queryDTO.getBatchTaskItemId(), queryDTO.getBatchTaskId(),
                queryDTO.getOrgId(), queryDTO.getWarehouseId());
    }

    /**
     * 根据拣货任务明细找到播种号详情
     *
     * @param batchItemId
     * @param orgId
     * @param warehouseId
     * @return
     */
    private SowSecondDTO findPackageOrderItemByBatchItemIds(String batchItemId, String batchTaksId, Integer orgId,
                                                            Integer warehouseId) {
        SecondSortQueryDTO queryDTO = new SecondSortQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setOrgId(orgId);
        queryDTO.setBatchTaskItemId(batchItemId);
        queryDTO.setBatchTaskId(batchTaksId);
        return listSowPackageOrderItems(queryDTO);
    }

    /**
     * 拣货任务明细找到是否存在播种任务（batchtaskitem）
     */
    private SowTaskPO findSowByBatchItemId(String batchtaskItemId) {
        /** 根据拣货任务明细查找拣货任务 */
        BatchTaskItemPO batchTaskItemPO = batchTaskItemMapper.selectBatchTaskItemById(batchtaskItemId);
        if (batchTaskItemPO == null) {
            throw new BusinessException("拣货任务项不存在");
        }

        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskByNo(batchTaskItemPO.getBatchTaskNo());
        if (batchTaskPO == null) {
            throw new BusinessException("拣货任务项不存在");
        }
        /** 如果拣货任务明细没关联播种任务，则直接返回 */
        Long sowTaskItemId = batchTaskItemPO.getSowTaskItemId();
        if (sowTaskItemId == null) {
            return null;
        }
        /** 如果拣货任务明细关联了播种任务，则查找播种任务 */
        Long sowTaskId = batchTaskPO.getSowTaskId();
        Integer orgId = Integer.valueOf(batchTaskPO.getOrgId());
        SowTaskPO sowTaskPO = sowTaskMapper.selectByPrimaryKey(sowTaskId);
        if (sowTaskPO == null) {
            batchTaskMapper.updateBatchTaskSowTaskById(Collections.singletonList(batchTaskPO.getId()), null, null);
            if (batchTaskItemPO.getSowTaskItemId() != null) {
                batchTaskItemPO.setSowTaskItemId(null);
                batchTaskItemMapper.updateBatchItemSowTaskId(batchTaskItemPO);
            }

            List<OutStockOrderItemDTO> outStockOrderItemDTOS =
                    outStockOrderItemMapper.findBySowTaskItemIds(orgId, Collections.singletonList(sowTaskItemId));
            for (OutStockOrderItemDTO outStockOrderItemDTO : outStockOrderItemDTOS) {
                if (outStockOrderItemDTO.getSowTaskId() != null) {
                    outStockOrderItemDTO.setSowOrderId(null);
                    outStockOrderItemDTO.setSowTaskId(null);
                    outStockOrderItemDTO.setSowTaskNo(null);
                    outStockOrderItemDTO.setSowTaskItemId(null);
                    outStockOrderItemMapper.updateSowTaskById(outStockOrderItemDTO);
                }
            }
            sowOrderMapper.deleteByIds(Arrays.asList(sowTaskId), orgId);
        }
        return sowTaskPO;
    }

    /**
     * 新增二次分拣数据 2-1、根据拣货任务项，在orderitemtaskinfo找到波次，根据波次判断播种任务是否存在（sowtask） 2-2、如果不存在播种任务，则创建播种任务 2-3、如果不存在播种明细，则创建播种明细项
     * 2-4、创建订单项播种号，并返回（packageorderitem）
     */
    public SowSecondDTO createSecondSortTask(SecondSortQueryDTO queryDTO) {
        /** 根据拣货任务明细项id获取拣货任务 */
        BatchTaskItemPO batchTaskItemPO = batchTaskItemMapper.selectBatchTaskItemById(queryDTO.getBatchTaskItemId());
        /** 根据拣货任务，获取波次 */
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskItemPO.getBatchTaskId());

        BatchPO batchPO = batchMapper.selectBatchByBatchNo(queryDTO.getOrgId(), batchTaskPO.getBatchNo());
        /** 根据波次判断播种任务是否存在 */
        List<SowTaskPO> sowTaskPOS = findSecondSowByBatchNo(batchTaskPO.getBatchNo());
        /** 创建播种任务 */
        List<PackageOrderItemDTO> secondSows;
        // if(CollectionUtils.isNotEmpty(sowTaskPOS)){
        // secondSows = createNewAllSecondSow(queryDTO, batchPO);
        // }
        if (CollectionUtils.isNotEmpty(sowTaskPOS)) {
            /** 如果播种任务存在，则只新增播种明细等 */
            secondSows = createPartSecondSow(queryDTO, sowTaskPOS.get(0), batchPO, batchTaskItemPO);
        } else {
            /** 如果播种任务不存在，则新增播种任务和明细等 */
            secondSows = createPartSecondSow(queryDTO, null, batchPO, batchTaskItemPO);
        }
        return convertSowSecondDTO(secondSows, batchTaskPO.getId(), queryDTO.getOrgId(), queryDTO.getWarehouseId());

    }

    /**
     * 根据波次找到二级分拣播种任务
     *
     * @param batchNo
     * @return
     */
    private List<SowTaskPO> findSecondSowByBatchNo(String batchNo) {
        List<SowTaskPO> sowTaskPOS = sowTaskMapper.findSowTaskByBatchNos(Collections.singletonList(batchNo));
        sowTaskPOS = sowTaskPOS.stream().filter(it -> it.getSowTaskType().equals(SowTaskTypeEnum.二次分拣播种.getType()))
                .collect(Collectors.toList());
        return sowTaskPOS;
    }

    /**
     * 创建所有播种任务
     *
     * @param queryDTO
     * @return
     */
    public List<PackageOrderItemDTO> createNewAllSecondSow(SecondSortQueryDTO queryDTO, BatchPO batchPO) {
        /** 根据波次获取所有订单 */
        List<OutStockOrderPO> outStockOrderList =
                outStockOrderMapper.findByBatchNos(Collections.singletonList(batchPO.getBatchNo()), queryDTO.getOrgId());
        /** 根据波次获取所有拣货任务项 */
        List<BatchTaskItemPO> batchTaskItemList =
                batchTaskItemMapper.findBatchTaskItemsByBatchIds(Collections.singletonList(batchPO.getId()));
        /** 3、创建播种任务，播种任务详情，播种号详情 */
        return sowManagerBL.createSecondSow(outStockOrderList, queryDTO.getLocationId(), queryDTO.getLocationName(),
                null, batchTaskItemList, batchPO);
    }

    /**
     * 创建播种任务（支持仅创建播种明细）
     *
     * @param queryDTO
     * @return
     */
    public List<PackageOrderItemDTO> createPartSecondSow(SecondSortQueryDTO queryDTO, SowTaskPO sowTaskPO,
                                                         BatchPO batchPO, BatchTaskItemPO batchTaskItemPO) {
        Long locationId = queryDTO.getLocationId();
        String locationName = queryDTO.getLocationName();
        /** 获取出库明细项,根据拣货任务项id，找到对应订单项id，在出库单查出对应数据 */
        List<OutStockOrderPO> outStockOrderList = outStockOrderMapper
                .listByBatchTaskItemIds(Collections.singletonList(queryDTO.getBatchTaskItemId()), queryDTO.getOrgId());
        /** 2、直接创建播种明细，并新增托盘号详情 */
        return sowManagerBL.createSecondSow(outStockOrderList, locationId, locationName, sowTaskPO,
                Collections.singletonList(batchTaskItemPO), batchPO);
    }

    /**
     * 获取出库明细项,根据拣货任务项id，找到对应订单项id，在出库单查出对应数据
     *
     * @param batchTaksItemId
     * @param orgId
     * @return
     */
    public List<OutStockOrderPO> findOutStockOrderByBatchTaskItemId(String batchTaksItemId, Integer orgId) {
        /** 根据拣货任务明细项id获取拣货任务 */
        BatchTaskItemPO batchTaskItemPO = batchTaskItemMapper.selectBatchTaskItemById(batchTaksItemId);
        /** 根据拣货任务，获取波次 */
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskItemPO.getBatchTaskId());
        /** 根据波次获取所有订单 */
        return outStockOrderMapper.findByBatchNos(Collections.singletonList(batchTaskPO.getBatchNo()), orgId);
    }

    /**
     * 根据拣货任务项查找播种任务
     *
     * @param sowTaksId
     * @return
     */
    public List<SowTaskItemDTO> findSowTaskByBatchTaskId(Long sowTaksId) {
        List<SowTaskItemPO> sowTaskItemPOS = sowTaskItemMapper.findBySowTaskIds(Collections.singletonList(sowTaksId));
        return SowConverter.sowTaskItemPOS2SowtaskItemDTOS(sowTaskItemPOS);
    }

    /**
     * 根据拣货任务项、skuid以及订单号查询对应托盘信息
     *
     * @param queryDTO
     * @return
     */
    public SowSecondDTO listSowPackageOrderItems(SecondSortQueryDTO queryDTO) {
        AssertUtils.isTrue(
                queryDTO.getBatchTaskItemId() != null || CollectionUtils.isNotEmpty(queryDTO.getBatchTaskItemIds()),
                "拣货任务明细id不能为空");
        AssertUtils.notNull(queryDTO.getBatchTaskId(), "拣货任务id不能为空");
        AssertUtils.notNull(queryDTO.getOrgId(), "城市id不能为空");
        List<PackageOrderItemDTO> packageOrderItemDTOS = listSowBoxcodeItems(queryDTO);
        return convertSowSecondDTO(packageOrderItemDTOS, queryDTO.getBatchTaskId(), queryDTO.getOrgId(),
                queryDTO.getWarehouseId());
    }

    /**
     * 根据拣货任务Id查询对应托盘信息
     *
     * @param
     * @return
     */
    public List<String> getBoxCodeListByBatchTaskId(String batchTaskId, Integer orgId, Integer warehouseId) {
        SecondSortQueryDTO queryDTO = new SecondSortQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setOrgId(orgId);
        queryDTO.setBatchTaskId(batchTaskId);
        List<PackageOrderItemDTO> itemDTOS = listSowBoxcodeItems(queryDTO);
        return itemDTOS.stream().filter(it -> CollectionUtils.isNotEmpty(it.getBoxCodeList()))
                .flatMap(it -> it.getBoxCodeList().stream()).distinct().collect(Collectors.toList());
    }

    /**
     * 填充托盘信息
     */
    private SowSecondDTO convertSowSecondDTO(List<PackageOrderItemDTO> packageOrderItemDTOS, String batchTaskId,
                                             Integer orgId, Integer warehouseId) {
        SowSecondDTO sowSecondDTO = new SowSecondDTO();
        /** 过滤数量为0的数据 */
        if (CollectionUtils.isNotEmpty(packageOrderItemDTOS)) {
            packageOrderItemDTOS = packageOrderItemDTOS.stream()
                    .filter(it -> it.getUnitTotalCount() != null && it.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0)
                    .collect(Collectors.toList());

        }
        fullDefaultOrderBox(packageOrderItemDTOS, orgId, warehouseId);
        sowSecondDTO.setPackageOrderItemList(packageOrderItemDTOS);
        /** 获取已经分配的托盘号 */
        sowSecondDTO.setBoxCodeList(getBoxCodeListByBatchTaskId(batchTaskId, orgId, warehouseId));
        return sowSecondDTO;
    }

    /**
     * 如果待播种的产品对应的订单，已经在其他产品中播种过（分配了托盘），则默认显示已分配的托盘。
     */
    private void fullDefaultOrderBox(List<PackageOrderItemDTO> packageOrderItemDTOS, Integer orgId,
                                     Integer warehouseId) {
        if (CollectionUtils.isEmpty(packageOrderItemDTOS)) {
            return;
        }
        Set<Long> refOrderIds = packageOrderItemDTOS.stream()
                .filter(it -> it.getRefOrderId() != null && StringUtils.isEmpty(it.getBoxCode()))
                .map(PackageOrderItemDTO::getRefOrderId).collect(Collectors.toSet());
        SecondSortQueryDTO queryDTO = new SecondSortQueryDTO();
        queryDTO.setRefOrderIdList(new ArrayList<>(refOrderIds));
        queryDTO.setOrgId(orgId);
        queryDTO.setWarehouseId(warehouseId);
        List<PackageOrderItemPO> pos = packageOrderItemBL.listSowBoxcodeItems(queryDTO);
        if (CollectionUtils.isEmpty(pos)) {
            return;
        }
        Map<Long, String> packageOrderItemMap =
                pos.stream().filter(it -> StringUtils.isNotEmpty(it.getBoxCode())).collect(
                        Collectors.toMap(PackageOrderItemPO::getRefOrderId, PackageOrderItemPO::getBoxCode, (t1, t2) -> t1));
        packageOrderItemDTOS.stream().filter(it -> it.getRefOrderId() != null && StringUtils.isEmpty(it.getBoxCode())
                && packageOrderItemMap.get(it.getRefOrderId()) != null).forEach(it -> {
            it.setBoxCode(packageOrderItemMap.get(it.getRefOrderId()));
            it.setBoxCodeNo(packageOrderItemMap.get(it.getRefOrderId()));
            if (StringUtils.isNotEmpty(it.getBoxCode())) {
                String[] split = it.getBoxCode().split(",");
                it.setBoxCodeList(Arrays.asList(split));
            }
        });
    }

    /**
     * 根据拣货任务，订单查询托盘信息
     *
     * @param queryDTO
     * @return
     */
    public List<PackageOrderItemDTO> listSowBoxcodeItems(SecondSortQueryDTO queryDTO) {
        List<PackageOrderItemPO> pos = packageOrderItemBL.listSowBoxcodeItems(queryDTO);
        return PackageOrderItemConvertor.convertPo2Dto(pos);
    }

    public void completePallet(SecondSowCompleteDTO completeDTO) {
        List<SowPalletDTO> items = completeDTO.getItems();
        for (SowPalletDTO item : items) {
            SecondSortDTO secondSortDTO = new SecondSortDTO();
            secondSortDTO.setBatchTaskItemId(completeDTO.getBatchTaskItemId());
            secondSortDTO.setWarehouseId(completeDTO.getWarehouseId());
            secondSortDTO.setOrgId(completeDTO.getOrgId());
            secondSortDTO.setBoxCode(item.getBoxCode());
            secondSortDTO.setBatchTaskId(completeDTO.getBatchTaskId());
            secondSortDTO.setOrderNo(item.getOrderNo());
            secondSortDTO.setId(item.getPalletId());
            secondSortDTO.setOrderItemId(item.getOrderItemId());
            secondSortDTO.setUserId(completeDTO.getOperatorUserId().longValue());
            updateSecondSortTaskPallet(secondSortDTO);
        }
    }

    /**
     * 播种完成接口
     *
     * @param completeDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void completeSecondSow(SecondSowCompleteDTO completeDTO) {
        checkComplete(completeDTO);
        String batchTaskId = completeDTO.getBatchTaskId();
        /** 完成分配托盘号 */
        completePallet(completeDTO);
        /** 根据拣货任务找到波次 */
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);
        /** 根据拣货任务项找到对应托盘信息 */
        List<PackageOrderItemPO> packageOrderItemPOS =
                getSowSkuByBatchTaskItemId(completeDTO.getOrgId(), completeDTO.getWarehouseId(), batchTaskPO.getBatchId());
        /** 没有托盘信息则不更新播种任务状态 */
        if (CollectionUtils.isEmpty(packageOrderItemPOS)) {
            return;
        }
        /** 创建完成播种参数 */
        createSowOrderSave(completeDTO, batchTaskPO.getBatchNo(), packageOrderItemPOS);

    }

    /**
     * 创建完成播种参数
     *
     * @param completeDTO
     * @param batcnNo
     * @return
     */
    private void createSowOrderSave(SecondSowCompleteDTO completeDTO, String batcnNo,
                                    List<PackageOrderItemPO> packageOrderItemPOS) {
        PackageOrderItemPO packageOrderItemPO = packageOrderItemPOS.stream()
                .filter(it -> it.getBatchTaskItemId().equals(completeDTO.getBatchTaskItemId())).findAny().orElse(null);
        if (packageOrderItemPO == null) {
            return;
        }
        Long skuId = packageOrderItemPO.getSkuId();
        List<PackageOrderItemPO> currentItems =
                packageOrderItemPOS.stream().filter(it -> it.getSkuId().equals(skuId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(currentItems)) {
            return;
        }
        PackageOrderItemPO allNotPallet = packageOrderItemPOS.stream()
                .filter(
                        it -> it.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0 && StringUtils.isEmpty(it.getBoxCode()))
                .findAny().orElse(null);

        PackageOrderItemPO isNotPallet = currentItems.stream()
                .filter(
                        it -> it.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0 && StringUtils.isEmpty(it.getBoxCode()))
                .findAny().orElse(null);
        Set<Long> needOutStockIdSet =
                currentItems.stream().map(PackageOrderItemPO::getRefOrderId).collect(Collectors.toSet());
        /** 获取播种任务 */
        List<SowTaskPO> sowTaskPOS = sowTaskMapper.listSowTaskByBatchNos(Collections.singletonList(batcnNo));
        SowTaskPO sowTaskPO = sowTaskPOS.get(0);
        /** 获取播种明细 */
        List<SowTaskItemDTO> sowTaskItems = findSowTaskByBatchTaskId(sowTaskPO.getId());
        /** 播种明细 */
        sowTaskItems =
                sowTaskItems.stream().filter(it -> it.getProductSkuId().equals(skuId)).collect(Collectors.toList());
        sowTaskItems.forEach(it -> {
            if (isNotPallet == null) {
                it.setState(SowTaskStateEnum.已播种.getType());
            }
            if (it.getOverUnitTotalCount() == null) {
                it.setOverUnitTotalCount(BigDecimal.ZERO);
            }
            it.setToLocationId(sowTaskPO.getLocationId());
            it.setToLocationName(sowTaskPO.getLocationName());
        });
        List<SownOrderItemDTO> sownOrderItemDTOS =
                sowManagerBL.findSownOrderItems(sowTaskPO.getSowTaskNo(), completeDTO.getOrgId());
        sownOrderItemDTOS = sownOrderItemDTOS.stream().filter(it -> needOutStockIdSet.contains(it.getOutStockOrderId()))
                .collect(Collectors.toList());
        SowOrderSaveDTO sowOrderSaveDTO = new SowOrderSaveDTO();
        sowOrderSaveDTO.setSowTaskId(sowTaskPO.getId());
        sowOrderSaveDTO.setSowTaskNo(sowTaskPO.getSowTaskNo());
        sowOrderSaveDTO.setOperatorUserId(completeDTO.getOperatorUserId());
        sowOrderSaveDTO.setOperatorUserName(completeDTO.getOperatorUserName());
        sowOrderSaveDTO.setSowTaskItemDTOS(sowTaskItems);
        sowOrderSaveDTO.setSownOrderItems(sownOrderItemDTOS);
        sowOrderSaveDTO.setOrgId(completeDTO.getOrgId());
        sowOrderSaveDTO.setSowTaskType(SowTaskTypeEnum.二次分拣播种.getType());
        if (allNotPallet == null) {
            sowOrderSaveDTO.setCompleteTime(new Date());
            sowOrderSaveDTO.setState(SowTaskStateEnum.已播种.getType());
        } else {
            sowOrderSaveDTO.setState(SowTaskStateEnum.播种中.getType());
        }
        LOG.info("完成播种日志:{}", JSON.toJSONString(sowOrderSaveDTO));
        /** 完成播种 */
        sowManagerBL.completeSowTaskItems(sowOrderSaveDTO);
    }

    /**
     * 检验二次分拣完成参数
     *
     * @param completeDTO
     */
    private void checkComplete(SecondSowCompleteDTO completeDTO) {
        AssertUtils.notNull(completeDTO.getBatchTaskId(), "拣货任务id不能为空");
        AssertUtils.notNull(completeDTO.getBatchTaskItemId(), "拣货任务项id不能为空");
        AssertUtils.notNull(completeDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(completeDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(completeDTO.getOperatorUserId(), "操作人id不能为空");
        AssertUtils.notNull(completeDTO.getOperatorUserName(), "操作人名称不能为空");
        AssertUtils.notEmpty(completeDTO.getItems(), "托盘项不能为空");

    }

    /**
     * 根据拣货任务项id获取播种产品
     *
     * @param
     * @return
     */
    private List<PackageOrderItemPO> getSowSkuByBatchTaskItemId(Integer orgId, Integer warehouseId, String batchId) {
        SecondSortQueryDTO queryDTO = new SecondSortQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setOrgId(orgId);
        queryDTO.setBatchId(batchId);
        return packageOrderItemBL.listSowBoxcodeItems(queryDTO);
    }

    /**
     * 获取未二次分拣播种完成拣货任务
     *
     * @param batchTaskSortQueryDTO
     * @return
     */
    public PageList<BatchTaskSortDTO> findBatchTaskSecondSortList(BatchTaskSortQueryDTO batchTaskSortQueryDTO) {
        AssertUtils.notNull(batchTaskSortQueryDTO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(batchTaskSortQueryDTO.getWarehouseId(), "仓库id不能为空");
        PageList<BatchTaskSortDTO> result = new PageList<>();
        // 查找已完成的播种任务项
        PageResult<BatchTaskSortPO> poPageResult = batchTaskMapper.findBatchTaskSecondSortList(batchTaskSortQueryDTO);
        PageList<BatchTaskSortPO> pageList = poPageResult.toPageList();
        if (CollectionUtils.isEmpty(pageList.getDataList())) {
            result.setPager(pageList.getPager());
            return result;
        }
        BatchTaskItemQueryDTO taskItemQueryDTO = new BatchTaskItemQueryDTO();
        taskItemQueryDTO.setCityId(batchTaskSortQueryDTO.getCityId());
        taskItemQueryDTO.setWarehouseId(batchTaskSortQueryDTO.getWarehouseId());
        taskItemQueryDTO
                .setBatchTaskIdList(poPageResult.stream().map(BatchTaskSortPO::getId).collect(Collectors.toList()));
        taskItemQueryDTO.setPageSize(Integer.MAX_VALUE);
        List<BatchTaskItemDTO> batchTaskItemList = batchTaskItemMapper.listBatchTaskItem(taskItemQueryDTO).getResult();
        if (CollectionUtils.isEmpty(batchTaskItemList)) {
            LOG.info("[二次分拣待播种分拣任务]无拣货任务项，请求：{}", JSON.toJSONString(batchTaskSortQueryDTO));
            result.setPager(pageList.getPager());
            return result;
        }
        Map<String, List<BatchTaskItemDTO>> taskItemGroup =
                batchTaskItemList.stream().collect(Collectors.groupingBy(BatchTaskItemDTO::getBatchTaskId));

        List<BatchTaskSortDTO> batchTaskSortList = new ArrayList<>();
        pageList.getDataList().forEach(it -> {
            List<BatchTaskItemDTO> curTaskItemList = taskItemGroup.get(it.getId());
            if (CollectionUtils.isEmpty(curTaskItemList)) {
                return;
            }
            // 未播种完成的（若已拣货，但全缺，则认为已播种）
            if (curTaskItemList.stream()
                    .anyMatch(ele -> (ele.getSownUnitTotalCount() == null
                            || ele.getSownUnitTotalCount().byteValue() == SowTaskStateEnum.待播种.getType())
                            && ele.getUnitTotalCount().compareTo(ele.getLackUnitCount()) != 0)) {
                BatchTaskSortDTO batchTaskSortDTO = new BatchTaskSortDTO();
                BeanUtils.copyProperties(it, batchTaskSortDTO);
                batchTaskSortList.add(batchTaskSortDTO);
            }
        });
        result.setDataList(batchTaskSortList);
        result.setPager(new Pager(pageList.getPager().getCurrentPage(), pageList.getPager().getPageSize(),
                batchTaskSortList.size()));
        return result;
    }

    /**
     * 查询二次分拣播种的出库单项
     */
    public List<SecondPickSowItemDTO> querySecondPickSowOrderItem(Integer orgId, Integer warehouseId,
                                                                  List<BatchTaskItemDTO> batchTaskItemList) {
        List<SecondPickSowItemDTO> secondItemList = new ArrayList<>();

        List<String> batchTaskItemIds =
                batchTaskItemList.stream().map(BatchTaskItemDTO::getId).collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoList =
                orderItemTaskInfoMapper.listOrderItemTaskInfoByBatchTaskItemIds(batchTaskItemIds);
        if (CollectionUtils.isEmpty(orderItemTaskInfoList)) {
            LOG.info("[二次分拣播种查询]无出库单拣货任务关联，分拣任务项ID：{}", batchTaskItemIds);
            return secondItemList;
        }

        OutStockOrderItemQuerySO querySO = new OutStockOrderItemQuerySO();
        querySO.setOrgId(orgId);
        querySO.setWarehouseId(warehouseId);
        querySO.setIds(orderItemTaskInfoList.stream().map(OrderItemTaskInfoPO::getRefOrderItemId).distinct()
                .collect(Collectors.toList()));
        PageResult<OutStockOrderItemPO> outOrderItemList = outStockOrderItemMapper.listOrderItem(querySO);
        if (CollectionUtils.isEmpty(outOrderItemList)) {
            LOG.info("[二次分拣播种查询]无出库单项，分拣任务项ID：{}", batchTaskItemIds);
            return secondItemList;
        }
        // 查找出库单
        List<OutStockOrderPO> outOrderList = outStockOrderMapper.listOrderByIds(outOrderItemList.stream()
                .map(OutStockOrderItemPO::getOutstockorderId).distinct().collect(Collectors.toList()), orgId, warehouseId);
        if (CollectionUtils.isEmpty(outOrderList)) {
            LOG.info("[二次分拣播种查询]无出库单，分拣任务项ID：{}", batchTaskItemIds);
            return secondItemList;
        }

        if (outOrderList.stream().anyMatch(ord -> StringUtils.isEmpty(ord.getBoundNo()))) {
            throw new BusinessValidateException(
                    "出库批次号为空，出库单号：" + outOrderList.stream().filter(ord -> StringUtils.isEmpty(ord.getBoundNo()))
                            .map(OutStockOrderPO::getReforderno).skip(5).collect(Collectors.toList()));
        }

        List<OutBoundBatchDTO> outBoundList = null;
        List<String> boundNoList = outOrderList.stream().map(OutStockOrderPO::getBoundNo).filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(boundNoList)) {
            OutBoundBatchQueryDTO boundQueryDTO = new OutBoundBatchQueryDTO();
            boundQueryDTO.setOrgId(orgId);
            boundQueryDTO.setWarehouseId(warehouseId);
            boundQueryDTO.setBoundBatchNoList(boundNoList);
            boundQueryDTO.setPageSize(Integer.MAX_VALUE);
            outBoundList = iOutBoundBatchQueryService.pageListOutBoundBatch(boundQueryDTO).getDataList();
            if (CollectionUtils.isEmpty(outBoundList)) {
                throw new BusinessException("出库批次不存在，出库批次："
                        + outOrderList.stream().map(OutStockOrderPO::getBoundNo).collect(Collectors.toList()));
            }
        }

        // 查找货位顺序
        Map<Long, Integer> locationSeq = new HashMap<>();
        List<Long> toLocationIdList = outOrderItemList.stream().map(OutStockOrderItemPO::getLocationId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(toLocationIdList)) {
            LocationInfoQueryDTO locationQueryDTO = new LocationInfoQueryDTO();
            locationQueryDTO.setCityId(orgId);
            locationQueryDTO.setWarehouseId(warehouseId);
            locationQueryDTO.setLocationIdList(toLocationIdList);
            locationQueryDTO.setPageSize(Integer.MAX_VALUE);
            List<LoactionDTO> toLocationList = iLocationService.pageListLocation(locationQueryDTO).getDataList();
            if (CollectionUtils.isNotEmpty(toLocationList)) {
                locationSeq = toLocationList.stream().filter(loc -> loc.getSequence() != null)
                        .collect(Collectors.toMap(LoactionDTO::getId, LoactionDTO::getSequence, (k1, k2) -> k1));
            }
        }

        for (OutStockOrderItemPO outItem : outOrderItemList) {
            if (outItem.getUnittotalcount().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            List<OrderItemTaskInfoPO> curTaskRefList = orderItemTaskInfoList.stream()
                    .filter(ref -> Objects.equals(ref.getRefOrderItemId(), outItem.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(curTaskRefList)) {
                throw new BusinessException("出库单项与拣货任务项关联不存在，出库单项：" + outItem.getId());
            }
            List<String> curBatchTaskItemIds =
                    curTaskRefList.stream().map(OrderItemTaskInfoPO::getBatchTaskItemId).collect(Collectors.toList());
            List<BatchTaskItemDTO> curBatchTaskItemList = batchTaskItemList.stream()
                    .filter(ele -> curBatchTaskItemIds.contains(ele.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(curBatchTaskItemList)) {
                throw new BusinessException("拣货任务项不存在，出库单项：" + outItem.getId());
            }
            OutStockOrderPO curOrd = outOrderList.stream()
                    .filter(ord -> Objects.equals(outItem.getOutstockorderId(), ord.getId())).findFirst().orElse(null);
            if (curOrd == null) {
                throw new BusinessException("出库单不存在，出库单项：" + outItem.getId());
            }
            OutBoundBatchDTO curBound = outBoundList.stream()
                    .filter(bou -> Objects.equals(curOrd.getBoundNo(), bou.getBoundBatchNo())).findFirst().orElse(null);
            if (curBound == null) {
                throw new BusinessException("出库批次不存在，出库批次：" + curOrd.getBoundNo());
            }

            SecondPickSowItemDTO secondItemDTO = new SecondPickSowItemDTO();
            secondItemDTO.setOrderItemId(outItem.getId());
            secondItemDTO.setBatchTaskItemId(outItem.getBatchTaskItemId());
            secondItemDTO.setProductName(outItem.getProductname());
            secondItemDTO.setProductSkuId(outItem.getSkuid());
            secondItemDTO.setPackageCount(outItem.getPackagecount());
            secondItemDTO.setUnitCount(outItem.getUnitcount());
            secondItemDTO.setPackageName(outItem.getPackagename());
            secondItemDTO.setUnitName(outItem.getUnitname());
            secondItemDTO.setState(StringUtils.isInteger(outItem.getRemark()) ? Byte.parseByte(outItem.getRemark())
                    : SowTaskStateEnum.待播种.getType());

            secondItemDTO.setDeliveryUserName(curBound.getPickUpUserName());
            secondItemDTO.setOutBoundBatchNo(curBound.getBoundBatchNo());
            secondItemDTO.setToLocationId(outItem.getLocationId());
            secondItemDTO.setToLocationName(outItem.getLocationName());
            if (secondItemDTO.getToLocationId() != null && locationSeq.size() > 0) {
                secondItemDTO.setToLocationSequence(locationSeq.get(secondItemDTO.getToLocationId()));
            }

            secondItemDTO.setTaskPickSate(curBatchTaskItemList.stream()
                    .allMatch(ele -> Objects.equals(ele.getTaskState(), TaskStateEnum.已完成.getType()))
                    ? TaskStateEnum.已完成.getType() : TaskStateEnum.未分拣.getType());
            secondItemDTO.setTaskSowSate(curBatchTaskItemList.stream()
                    .allMatch(ele -> ele.getSownUnitTotalCount() != null
                            && ele.getSownUnitTotalCount().byteValue() == SowTaskStateEnum.已播种.getType())
                    ? SowTaskStateEnum.已播种.getType() : SowTaskStateEnum.待播种.getType());
            secondItemDTO.setBatchTaskItemIdList(curBatchTaskItemIds);
            secondItemList.add(secondItemDTO);
        }

        return secondItemList;
    }

    /**
     * 查询二次分拣播种的出库单项
     */
    public List<SecondPickSowItemDTO> queryBatchTaskPickingSortOrderItem(Integer orgId, Integer warehouseId,
                                                                         List<BatchTaskItemDTO> batchTaskItemList) {
        List<SecondPickSowItemDTO> secondItemList = new ArrayList<>();

        List<String> batchTaskItemIds =
                batchTaskItemList.stream().map(BatchTaskItemDTO::getId).collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoList =
                orderItemTaskInfoMapper.listOrderItemTaskInfoByBatchTaskItemIds(batchTaskItemIds);
        if (CollectionUtils.isEmpty(orderItemTaskInfoList)) {
            LOG.info("[二次分拣播种查询]无出库单拣货任务关联，分拣任务项ID：{}", batchTaskItemIds);
            return secondItemList;
        }

        OutStockOrderItemQuerySO querySO = new OutStockOrderItemQuerySO();
        querySO.setOrgId(orgId);
        querySO.setWarehouseId(warehouseId);
        querySO.setIds(orderItemTaskInfoList.stream().map(OrderItemTaskInfoPO::getRefOrderItemId).distinct()
                .collect(Collectors.toList()));
        PageResult<OutStockOrderItemPO> outOrderItemList = outStockOrderItemMapper.listOrderItem(querySO);
        if (CollectionUtils.isEmpty(outOrderItemList)) {
            LOG.info("[二次分拣播种查询]无出库单项，分拣任务项ID：{}", batchTaskItemIds);
            return secondItemList;
        }
        // 查找出库单
        List<OutStockOrderPO> outOrderList = outStockOrderMapper.listOrderByIds(outOrderItemList.stream()
                .map(OutStockOrderItemPO::getOutstockorderId).distinct().collect(Collectors.toList()), orgId, warehouseId);
        if (CollectionUtils.isEmpty(outOrderList)) {
            LOG.info("[二次分拣播种查询]无出库单，分拣任务项ID：{}", batchTaskItemIds);
            return secondItemList;
        }

        // if (outOrderList.stream().anyMatch(ord -> StringUtils.isEmpty(ord.getBoundNo()))) {
        // throw new BusinessValidateException(
        // "出库批次号为空，出库单号：" + outOrderList.stream().filter(ord -> StringUtils.isEmpty(ord.getBoundNo()))
        // .map(OutStockOrderPO::getReforderno).skip(5).collect(Collectors.toList()));
        // }

        // List<OutBoundBatchDTO> outBoundList = null;
        // List<String> boundNoList = outOrderList.stream().map(OutStockOrderPO::getBoundNo).filter(Objects::nonNull)
        // .distinct().collect(Collectors.toList());
        // if (CollectionUtils.isNotEmpty(boundNoList)) {
        // OutBoundBatchQueryDTO boundQueryDTO = new OutBoundBatchQueryDTO();
        // boundQueryDTO.setOrgId(orgId);
        // boundQueryDTO.setWarehouseId(warehouseId);
        // boundQueryDTO.setBoundBatchNoList(boundNoList);
        // boundQueryDTO.setPageSize(Integer.MAX_VALUE);
        // outBoundList = iOutBoundBatchQueryService.pageListOutBoundBatch(boundQueryDTO).getDataList();
        // if (CollectionUtils.isEmpty(outBoundList)) {
        // throw new BusinessException("出库批次不存在，出库批次："
        // + outOrderList.stream().map(OutStockOrderPO::getBoundNo).collect(Collectors.toList()));
        // }
        // }

        // 查找货位顺序
        Map<Long, Integer> locationSeq = new HashMap<>();
        List<Long> toLocationIdList = outOrderItemList.stream().map(OutStockOrderItemPO::getLocationId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(toLocationIdList)) {
            LocationInfoQueryDTO locationQueryDTO = new LocationInfoQueryDTO();
            locationQueryDTO.setCityId(orgId);
            locationQueryDTO.setWarehouseId(warehouseId);
            locationQueryDTO.setLocationIdList(toLocationIdList);
            locationQueryDTO.setPageSize(Integer.MAX_VALUE);
            List<LoactionDTO> toLocationList = iLocationService.pageListLocation(locationQueryDTO).getDataList();
            if (CollectionUtils.isNotEmpty(toLocationList)) {
                locationSeq = toLocationList.stream().filter(loc -> loc.getSequence() != null)
                        .collect(Collectors.toMap(LoactionDTO::getId, LoactionDTO::getSequence, (k1, k2) -> k1));
            }
        }

        for (OutStockOrderItemPO outItem : outOrderItemList) {
            if (outItem.getUnittotalcount().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            List<OrderItemTaskInfoPO> curTaskRefList = orderItemTaskInfoList.stream()
                    .filter(ref -> Objects.equals(ref.getRefOrderItemId(), outItem.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(curTaskRefList)) {
                throw new BusinessException("出库单项与拣货任务项关联不存在，出库单项：" + outItem.getId());
            }
            List<String> curBatchTaskItemIds =
                    curTaskRefList.stream().map(OrderItemTaskInfoPO::getBatchTaskItemId).collect(Collectors.toList());
            List<BatchTaskItemDTO> curBatchTaskItemList = batchTaskItemList.stream()
                    .filter(ele -> curBatchTaskItemIds.contains(ele.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(curBatchTaskItemList)) {
                throw new BusinessException("拣货任务项不存在，出库单项：" + outItem.getId());
            }
            OutStockOrderPO curOrd = outOrderList.stream()
                    .filter(ord -> Objects.equals(outItem.getOutstockorderId(), ord.getId())).findFirst().orElse(null);
            if (curOrd == null) {
                throw new BusinessException("出库单不存在，出库单项：" + outItem.getId());
            }
            // OutBoundBatchDTO curBound = outBoundList.stream()
            // .filter(bou -> Objects.equals(curOrd.getBoundNo(), bou.getBoundBatchNo())).findFirst().orElse(null);
            // if (curBound == null) {
            // throw new BusinessException("出库批次不存在，出库批次：" + curOrd.getBoundNo());
            // }

            SecondPickSowItemDTO secondItemDTO = new SecondPickSowItemDTO();
            secondItemDTO.setOrderItemId(outItem.getId());
            secondItemDTO.setBatchTaskItemId(outItem.getBatchTaskItemId());
            secondItemDTO.setProductName(outItem.getProductname());
            secondItemDTO.setProductSkuId(outItem.getSkuid());
            secondItemDTO.setPackageCount(outItem.getPackagecount());
            secondItemDTO.setUnitCount(outItem.getUnitcount());
            secondItemDTO.setPackageName(outItem.getPackagename());
            secondItemDTO.setUnitName(outItem.getUnitname());
            secondItemDTO.setState(StringUtils.isInteger(outItem.getRemark()) ? Byte.parseByte(outItem.getRemark())
                    : SowTaskStateEnum.待播种.getType());

            // secondItemDTO.setDeliveryUserName(curBound.getPickUpUserName());
            // secondItemDTO.setOutBoundBatchNo(curBound.getBoundBatchNo());
            secondItemDTO.setToLocationId(outItem.getLocationId());
            secondItemDTO.setToLocationName(outItem.getLocationName());
            if (secondItemDTO.getToLocationId() != null && locationSeq.size() > 0) {
                secondItemDTO.setToLocationSequence(locationSeq.get(secondItemDTO.getToLocationId()));
            }

            secondItemDTO.setTaskPickSate(curBatchTaskItemList.stream()
                    .allMatch(ele -> Objects.equals(ele.getTaskState(), TaskStateEnum.已完成.getType()))
                    ? TaskStateEnum.已完成.getType() : TaskStateEnum.未分拣.getType());
            secondItemDTO.setTaskSowSate(curBatchTaskItemList.stream()
                    .allMatch(ele -> ele.getSownUnitTotalCount() != null
                            && ele.getSownUnitTotalCount().byteValue() == SowTaskStateEnum.已播种.getType())
                    ? SowTaskStateEnum.已播种.getType() : SowTaskStateEnum.待播种.getType());
            secondItemDTO.setBatchTaskItemIdList(curBatchTaskItemIds);
            secondItemList.add(secondItemDTO);
        }

        return secondItemList;
    }

    /**
     * 查询二次分拣播种项
     */
    public List<SecondPickSowItemDTO> querySecondPickSow(SecondPickSowQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库id不能为空");
        List<String> reqBatchTaskItemIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(queryDTO.getBatchTaskItemId())) {
            reqBatchTaskItemIds.add(queryDTO.getBatchTaskItemId());
        }
        if (CollectionUtils.isNotEmpty(queryDTO.getBatchTaskItemIds())) {
            reqBatchTaskItemIds.addAll(queryDTO.getBatchTaskItemIds());
        }
        if (StringUtils.isNotEmpty(queryDTO.getBatchTaskId()) && CollectionUtils.isNotEmpty(reqBatchTaskItemIds)) {
            throw new BusinessValidateException("分拣任务项ID、分拣任务ID不能都为非空");
        }
        if (StringUtils.isEmpty(queryDTO.getBatchTaskId()) && CollectionUtils.isEmpty(reqBatchTaskItemIds)) {
            throw new BusinessValidateException("分拣任务项ID、分拣任务ID不能都为空");
        }

        BatchTaskItemQueryDTO taskItemQueryDTO = new BatchTaskItemQueryDTO();
        taskItemQueryDTO.setCityId(queryDTO.getOrgId());
        taskItemQueryDTO.setWarehouseId(queryDTO.getWarehouseId());
        taskItemQueryDTO.setBatchTaskId(queryDTO.getBatchTaskId());
        taskItemQueryDTO.setBatchTaskItemIdList(reqBatchTaskItemIds);
        taskItemQueryDTO.setPageSize(Integer.MAX_VALUE);
        List<BatchTaskItemDTO> batchTaskItemList = batchTaskItemMapper.listBatchTaskItem(taskItemQueryDTO).getResult();
        if (CollectionUtils.isEmpty(batchTaskItemList)) {
            LOG.info("[二次分拣播种查询]无出库单拣货任务项，请求：{}", JSON.toJSONString(queryDTO));
            return Collections.emptyList();
        }

        String batchTaskId = getBatchTaskId(queryDTO, batchTaskItemList);
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);

        BatchPO batchPO = batchMapper.selectBatchByBatchNo(queryDTO.getOrgId(), batchTaskItemList.get(0).getBatchNo());
        if (batchPO == null) {
            LOG.info("[二次分拣播种查询]波次不存在，请求：{}", JSON.toJSONString(queryDTO));
            return Collections.emptyList();
        }

        if (BatchTaskKindOfPickingConstants.PICKING_SORTING.equals(batchTaskPO.getKindOfPicking())) {
            return groupSecondPickSowItem(
                    queryBatchTaskPickingSortOrderItem(queryDTO.getOrgId(), queryDTO.getWarehouseId(), batchTaskItemList),
                    queryDTO.getIgnoreDeliveryUser());

        }

        if (Objects.equals(batchPO.getSowType(), BatchSowTypeEnum.二次分拣.getType())) {
            return groupSecondPickSowItem(
                    querySecondPickSowOrderItem(queryDTO.getOrgId(), queryDTO.getWarehouseId(), batchTaskItemList),
                    queryDTO.getIgnoreDeliveryUser());
        } else {
            List<SecondPickSowItemDTO> secondItemList = new ArrayList<>();
            for (BatchTaskItemDTO batchTaskItem : batchTaskItemList) {
                SecondPickSowItemDTO secondItemDTO = new SecondPickSowItemDTO();
                secondItemDTO.setBatchTaskItemId(batchTaskItem.getId());
                secondItemDTO.setProductName(batchTaskItem.getProductName());
                secondItemDTO.setProductSkuId(batchTaskItem.getSkuId());
                secondItemDTO.setPackageCount(batchTaskItem.getPackageCount());
                secondItemDTO.setUnitCount(batchTaskItem.getUnitCount());
                secondItemDTO.setPackageName(batchTaskItem.getPackageName());
                secondItemDTO.setUnitName(batchTaskItem.getUnitName());
                secondItemDTO.setToLocationId(batchTaskItem.getToLocationId());
                secondItemDTO.setToLocationName(batchTaskItem.getToLocationName());
                secondItemList.add(secondItemDTO);
            }
            return secondItemList;
        }
    }

    private String getBatchTaskId(SecondPickSowQueryDTO queryDTO, List<BatchTaskItemDTO> batchTaskItemList) {
        if (StringUtils.isNotEmpty(queryDTO.getBatchTaskId())) {
            return queryDTO.getBatchTaskId();
        }

        long batchTaskCount = batchTaskItemList.stream().map(BatchTaskItemDTO::getBatchTaskId).distinct().count();
        if (batchTaskCount > 2) {
            throw new BusinessValidateException("拣货任务明细属于不同拣货任务!");
        }

        return batchTaskItemList.stream().map(BatchTaskItemDTO::getBatchTaskId).distinct().findFirst().get();
    }

    /**
     * 完成二次分拣播种项
     */
    public void completeSecondPickSowItem(CompleteSecondPickSowItemDTO completeDTO) {
        AssertUtils.notNull(completeDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(completeDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(completeDTO.getOrderItemIdList(), "出库单项ID不能为空");

        OutStockOrderItemQuerySO querySO = new OutStockOrderItemQuerySO();
        querySO.setOrgId(completeDTO.getOrgId());
        querySO.setWarehouseId(completeDTO.getWarehouseId());
        querySO.setIds(completeDTO.getOrderItemIdList());
        PageResult<OutStockOrderItemPO> outItemList = outStockOrderItemMapper.listOrderItem(querySO);
        if (CollectionUtils.isEmpty(outItemList)) {
            throw new BusinessException("出库单项不存在");
        }
        if (outItemList.stream()
                .allMatch(ele -> Objects.equals(ele.getRemark(), String.valueOf(SowTaskStateEnum.已播种.getType())))) {
            throw new BusinessValidateException("产品已播种");
        }
        // 1.更新出库单二次分拣状态
        List<OutStockOrderItemPO> updateList = new ArrayList<>();
        outItemList.forEach(item -> {
            OutStockOrderItemPO itemUpdate = new OutStockOrderItemPO();
            itemUpdate.setId(item.getId());
            itemUpdate.setRemark(String.valueOf(SowTaskStateEnum.已播种.getType()));
            updateList.add(itemUpdate);
        });
        outStockOrderItemMapper.updateBatchByPOList(updateList);
        LOG.info("[完成二次分拣播种项]出库单项更新成功，出库单项ID：{}", completeDTO.getOrderItemIdList());
    }

    /**
     * 完成二次分拣播种
     */
    public void completeSecondPickSow(CompleteSecondPickSowDTO completeDTO) {
        AssertUtils.notNull(completeDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(completeDTO.getWarehouseId(), "仓库id不能为空");
        List<String> batchTaskItemIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(completeDTO.getBatchTaskItemId())) {
            batchTaskItemIds.add(completeDTO.getBatchTaskItemId());
        }
        if (CollectionUtils.isNotEmpty(completeDTO.getBatchTaskItemIds())) {
            batchTaskItemIds.addAll(completeDTO.getBatchTaskItemIds());
        }
        AssertUtils.notEmpty(batchTaskItemIds, "分拣任务项ID不能为空");

        List<OrderItemTaskInfoPO> orderItemTaskInfoList =
                orderItemTaskInfoMapper.listOrderItemTaskInfoByBatchTaskItemIds(batchTaskItemIds);
        if (CollectionUtils.isEmpty(orderItemTaskInfoList)) {
            LOG.info("[二次分拣播种完成]无出库单拣货任务关联，分拣任务项ID：{}", completeDTO.getBatchTaskItemId());
            return;
        }

        List<OutStockOrderItemDTO> outOrderItemList = outStockOrderItemMapper.findByItemIds(orderItemTaskInfoList
                .stream().map(OrderItemTaskInfoPO::getRefOrderItemId).distinct().collect(Collectors.toList()), null);
        if (CollectionUtils.isEmpty(outOrderItemList)) {
            LOG.info("[二次分拣播种完成]无出库单项，分拣任务项ID：{}", completeDTO.getBatchTaskItemId());
            return;
        }

        Map<String, List<OrderItemTaskInfoPO>> orderItemTaskGroup =
                orderItemTaskInfoList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getBatchTaskItemId));

        // 完成每一个分拣任务项
        List<BatchTaskItemPO> completeTaskItemList = new ArrayList<>();
        for (String batchTaskItemId : batchTaskItemIds) {
            List<OrderItemTaskInfoPO> orderItemTaskList = orderItemTaskGroup.get(batchTaskItemId);
            if (CollectionUtils.isEmpty(orderItemTaskList)) {
                throw new BusinessException("拣货任务对应的出库单项不存在，出库单项：" + batchTaskItemId);
            }
            List<Long> orderItemIds = orderItemTaskList.stream().map(OrderItemTaskInfoPO::getRefOrderItemId).distinct()
                    .collect(Collectors.toList());

            boolean isCompleteSow = outOrderItemList.stream().filter(oi -> orderItemIds.contains(oi.getId()))
                    .allMatch(item -> Objects.equals(item.getRemark(), String.valueOf(SowTaskStateEnum.已播种.getType()))
                            || item.getUnitTotalCount().compareTo(BigDecimal.ZERO) == 0);
            if (!isCompleteSow) {
                throw new BusinessValidateException("拣货任务项有产品未完成播种，产品名：" + outOrderItemList.stream()
                        .filter(oi -> orderItemIds.contains(oi.getId())
                                && Objects.equals(oi.getRemark(), String.valueOf(SowTaskStateEnum.待播种.getType()))
                                && oi.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0)
                        .map(OutStockOrderItemDTO::getProductName).distinct().limit(5).collect(Collectors.joining(",")));
            }

            BatchTaskItemPO updateItem = new BatchTaskItemPO();
            updateItem.setId(batchTaskItemId);
            updateItem.setSownUnitTotalCount(new BigDecimal(SowTaskStateEnum.已播种.getType()));
            updateItem.setLastUpdateUser(completeDTO.getOptUserName());
            completeTaskItemList.add(updateItem);
        }
        LOG.info("[二次分拣播种完成]更新：{}", JSON.toJSONString(completeTaskItemList));
        batchTaskItemMapper.batchUpdateBatchTaskItem(completeTaskItemList);
    }

    /**
     * 二次分拣出库位同步
     */
    public void secondPickLocationSync(SecondPickLocationSyncDTO syncDTO) {
        AssertUtils.notNull(syncDTO.getOrgId(), "城市不能为空");
        AssertUtils.notNull(syncDTO.getWarehouseId(), "仓库不能为空");
        AssertUtils.notEmpty(syncDTO.getLocationList(), "批次货位列表不能为空");
        syncDTO.getLocationList().forEach(loc -> {
            AssertUtils.notNull(loc.getRefOrderNoList(), "出库单列表不能为空");
        });
        OutStockOrderItemQuerySO itemQuerySO = new OutStockOrderItemQuerySO();
        itemQuerySO.setOrgId(syncDTO.getOrgId());
        itemQuerySO.setWarehouseId(syncDTO.getWarehouseId());
        itemQuerySO.setRefOrderNoList(syncDTO.getLocationList().stream().flatMap(e -> e.getRefOrderNoList().stream())
                .collect(Collectors.toList()));
        List<OutStockOrderItemPO> orderItemList = outStockOrderItemMapper.listOrderItem(itemQuerySO);
        if (CollectionUtils.isEmpty(orderItemList)) {
            LOG.info("[二次分拣出库位同步]出库单项不存在，请求：{}", JSON.toJSONString(syncDTO));
            return;
        }

        List<OutStockOrderItemPO> locationUpdateList = new ArrayList<>();
        syncDTO.getLocationList().forEach(batchLocation -> {
            if (batchLocation.getLocationId() == null) {
                LOG.info("[二次分拣出库位同步]出库位为空，出库单：{}", batchLocation.getRefOrderNoList());
                return;
            }
            List<OutStockOrderItemPO> curOrderItemList =
                    orderItemList.stream().filter(item -> batchLocation.getRefOrderNoList().contains(item.getRefOrderNo()))
                            .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(curOrderItemList)) {
                LOG.info("[二次分拣出库位同步]出库单项不存在，出库单：{}", batchLocation.getRefOrderNoList());
                return;
            }
            curOrderItemList.forEach(sourceItem -> {
                OutStockOrderItemPO updateDTO = new OutStockOrderItemPO();
                updateDTO.setId(sourceItem.getId());
                updateDTO.setLocationId(batchLocation.getLocationId());
                updateDTO.setLocationName(batchLocation.getLocationName());
                updateDTO.setLastUpdateUser(syncDTO.getOptUserName());

                updateDTO.setRefOrderNo(sourceItem.getRefOrderNo());
                locationUpdateList.add(updateDTO);
            });
        });

        if (CollectionUtils.isNotEmpty(locationUpdateList)) {
            LOG.info("[二次分拣出库位同步]货位更新请求：{}", JSON.toJSONString(locationUpdateList));
            Lists.partition(locationUpdateList, 50)
                    .forEach(itemPart -> outStockOrderItemMapper.updateBatchByPOList(itemPart));
        }
    }

    /**
     * 对二次分拣播种项进行聚合
     */
    public List<SecondPickSowItemDTO> groupSecondPickSowItem(List<SecondPickSowItemDTO> secondPickSowItemList,
                                                             Boolean isIgnoreDeliveryUser) {
        List<SecondPickSowItemDTO> comSowItemList = new ArrayList<>();

        if (CollectionUtils.isEmpty(secondPickSowItemList)) {
            return comSowItemList;
        }

        Map<String,
                List<SecondPickSowItemDTO>> sowItemGroupByLocation = secondPickSowItemList.stream()
                .collect(Collectors.groupingBy(ele -> String.format("%s_%s_%s", ele.getToLocationId(),
                        ele.getProductSkuId(), isIgnoreDeliveryUser ? null : ele.getDeliveryUserName())));
        BigDecimal packageAmount = BigDecimal.ZERO;
        BigDecimal unitAmount = BigDecimal.ZERO;
        for (String key : sowItemGroupByLocation.keySet()) {
            List<SecondPickSowItemDTO> curSowItemList = sowItemGroupByLocation.get(key);

            SecondPickSowItemDTO firstSowItem = curSowItemList.get(0);

            SecondPickSowItemDTO comSowItem = new SecondPickSowItemDTO();
            // 基础信息
            comSowItem.setProductName(firstSowItem.getProductName());
            comSowItem.setProductSkuId(firstSowItem.getProductSkuId());
            comSowItem.setPackageName(firstSowItem.getPackageName());
            comSowItem.setUnitName(firstSowItem.getUnitName());
            comSowItem.setDeliveryUserName(firstSowItem.getDeliveryUserName());
            comSowItem.setToLocationId(firstSowItem.getToLocationId());
            comSowItem.setToLocationName(firstSowItem.getToLocationName());
            comSowItem.setToLocationSequence(firstSowItem.getToLocationSequence());

            // 聚合数量
            BigDecimal comPackageCount = BigDecimal.ZERO;
            BigDecimal comUnitCount = BigDecimal.ZERO;
            Byte comTaskPickSate = TaskStateEnum.已完成.getType();
            Byte comTaskSowSate = SowTaskStateEnum.已播种.getType();
            Byte comTempSowSate = SowTaskStateEnum.已播种.getType();
            List<Long> comOrderItemIdList = new ArrayList<>();
            List<String> comBatchTaskItemIds = new ArrayList<>();
            for (SecondPickSowItemDTO curSowItem : curSowItemList) {
                packageAmount = packageAmount.add(curSowItem.getPackageCount());
                unitAmount = unitAmount.add(curSowItem.getUnitCount());

                comPackageCount = comPackageCount.add(curSowItem.getPackageCount());
                comUnitCount = comUnitCount.add(curSowItem.getUnitCount());
                if (Objects.equals(curSowItem.getTaskPickSate(), TaskStateEnum.未分拣.getType())) {
                    comTaskPickSate = TaskStateEnum.未分拣.getType();
                }
                if (Objects.equals(curSowItem.getTaskSowSate(), SowTaskStateEnum.待播种.getType())) {
                    comTaskSowSate = SowTaskStateEnum.待播种.getType();
                }
                if (Objects.equals(curSowItem.getState(), SowTaskStateEnum.待播种.getType())) {
                    comTempSowSate = SowTaskStateEnum.待播种.getType();
                }

                comOrderItemIdList.add(curSowItem.getOrderItemId());
                comBatchTaskItemIds.addAll(curSowItem.getBatchTaskItemIdList());
            }
            comSowItem.setPackageCount(comPackageCount);
            comSowItem.setUnitCount(comUnitCount);
            comSowItem.setState(comTempSowSate);
            comSowItem.setTaskPickSate(comTaskPickSate);
            comSowItem.setTaskSowSate(comTaskSowSate);
            comSowItem.setOrderItemIdList(comOrderItemIdList);
            comSowItem.setBatchTaskItemIdList(comBatchTaskItemIds);

            comSowItemList.add(comSowItem);
        }

        return comSowItemList;
    }

    /**
     * 查询订单项实际拣货数量
     *
     * @param queryDTO
     * @return
     */
    public List<OrderItemPickCountInfoDTO> findOrderItemPickCount(OrderItemPickCountInfoQueryDTO queryDTO) {
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList = orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderItemIds(queryDTO.getOrderItemIdList());

        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return Collections.emptyList();
        }

        List<OutStockOrderItemPO> orderItemPOList = outStockOrderItemMapper.listByIds(queryDTO.getOrderItemIdList());
        if (CollectionUtils.isEmpty(orderItemPOList)) {
            return Collections.emptyList();
        }

        Map<Long, List<OrderItemTaskInfoPO>> orderItemTaskInfoGroupMap =
                orderItemTaskInfoPOList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId));

        Map<Long, OutStockOrderItemPO> orderItemPOMap =
                orderItemPOList.stream().collect(Collectors.toMap(OutStockOrderItemPO::getId, v -> v));

        List<OrderItemPickCountInfoDTO> resultList = new ArrayList<>();
        for (Long orderItemId : queryDTO.getOrderItemIdList()) {
            List<OrderItemTaskInfoPO> itemTaskInfoList = orderItemTaskInfoGroupMap.get(orderItemId);
            if (CollectionUtils.isEmpty(itemTaskInfoList)) {
                continue;
            }
            BigDecimal lackCount = itemTaskInfoList.stream().map(OrderItemTaskInfoPO::getLackUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            OutStockOrderItemPO orderItemPO = orderItemPOMap.get(orderItemId);
            OrderItemPickCountInfoDTO orderItemPickCountInfoDTO = new OrderItemPickCountInfoDTO();
            BigDecimal pickUnitTotalCount = orderItemPO.getOriginalUnitTotalCount().subtract(lackCount);
            orderItemPickCountInfoDTO.setCount(pickUnitTotalCount);
            orderItemPickCountInfoDTO.setItemOriCount(orderItemPO.getOriginalUnitTotalCount());
            orderItemPickCountInfoDTO.setOrderItemId(orderItemId);
            BigDecimal[] count = pickUnitTotalCount.divideAndRemainder(orderItemPO.getSpecquantity());
            orderItemPickCountInfoDTO.setPickPackageCount(count[0].stripTrailingZeros().toPlainString());
            orderItemPickCountInfoDTO.setPickUnitCount(count[1].stripTrailingZeros().toPlainString());
            resultList.add(orderItemPickCountInfoDTO);
        }

        return resultList;
    }
}
