package com.yijiupi.himalaya.supplychain.concertsow.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.concertsow.domain.bl.ConcertSowTaskBL;
import com.yijiupi.himalaya.supplychain.waves.concertsow.dto.ConcertSowTaskRequestDTO;
import com.yijiupi.himalaya.supplychain.waves.concertsow.service.IConcertSowTaskService;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.PDASowTaskInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskReceiveDTO;

/**
 * <AUTHOR> 多人协作播种
 */
@Service
public class IConcertSowTaskServiceImpl implements IConcertSowTaskService {

    @Autowired
    private ConcertSowTaskBL concertSowTaskBL;

    /**
     * 领取播种任务明细
     *
     * @param request
     */
    @Override
    public void getSowTask(ConcertSowTaskRequestDTO request) {
        concertSowTaskBL.getSowTask(request);
    }

    /**
     * 待播种任务查询
     */
    @Override
    public SowTaskDTO queryWaitSowTask(SowTaskReceiveDTO sowTaskReceiveDTO) {
        return concertSowTaskBL.queryWaitSowTask(sowTaskReceiveDTO);
    }

    /**
     * 根据播种任务编号查询拣波次任务
     *
     * @param sowTaskQueryDTO
     * @return
     */
    @Override
    public List<PDASowTaskInfoDTO> listBatchTaskBySowTaskNos(SowTaskQueryDTO sowTaskQueryDTO) {
        return concertSowTaskBL.listBatchTaskBySowTaskNos(sowTaskQueryDTO);
    }

    /**
     * 二次分拣通用查询
     *
     * @param sowTaskQueryDTO
     * @return
     */
    @Override
    public PageList<BatchTaskDTO> listBatchTaskInfo(SowTaskQueryDTO sowTaskQueryDTO) {
        return concertSowTaskBL.listBatchTaskInfo(sowTaskQueryDTO);
    }
}
