package com.yijiupi.himalaya.supplychain.waves.batch;

import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDetailRecoverDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
public interface IBatchTaskItemManageService {

    void updateBatchTaskItem(BatchTaskItemBatchUpdateDTO batchTaskItemBatchUpdateDTO);

    void updateBatchTask(BatchTaskBatchUpdateDTO batchTaskBatchUpdateDTO);

    void clearOrderItemTaskInfo(List<Long> ids);

    void updateNotPickedBatchTaskItemLocationFromOldToNew(UpdateNotPickedBatchTaskItemLocationFromOldToNewDTO dto);

    /**
     * 完成拣货任务明细
     * 
     * @param batchTaskItemDTO
     * @return
     */
    Map<String, List<BatchTaskDTO>> completeBatchTaskItem(CompleteBatchTaskItemDTO batchTaskItemDTO);

    /**
     * 修复缺货的detail数据
     */
    void recoverOrderStockOrderItemDetail(RecoverOrderStockOrderItemDetailDTO dto);

    void recoverDetailUseOrderCenter(RecoverOrderStockOrderItemDetailDTO dto);

    void recoverDetailUseWMS(RecoverOrderStockOrderItemDetailDTO dto);

    void recoverDetailOutStockCount(RecoverOrderStockOrderItemDetailDTO dto);

    List<OutStockOrderItemDetailDTO> recoverByBatchInventoryRecord(RecoverOrderStockOrderItemDetailDTO dto);

    /**
     * 修复所有场景的detail数据
     * 
     * @param dto
     * @return
     */
    List<OutStockOrderItemDetailDTO> recoverDetailList(OutStockOrderItemDetailRecoverDTO dto);
}
