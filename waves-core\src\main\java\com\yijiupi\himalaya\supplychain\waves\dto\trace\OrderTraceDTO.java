package com.yijiupi.himalaya.supplychain.waves.dto.trace;

import java.io.Serializable;
import java.util.Date;

public class OrderTraceDTO implements Serializable {

    private static final long serialVersionUID = -4352061844543436235L;
    /** 编号 */
    private Long id;

    /** 单据类型（波次策略=1，分配策略=2，上架策略=3，波次=4，拣货任务=5，上架单=6，上架任务=7，移库单=8，出库单=9，入库单=10） */
    private Byte businesstype;

    /** 单据ID */
    private Long businessId;

    /** 单据编号 */
    private String businessno;

    /** 操作类型（新增=1，修改=2，删除=3，取消=4，其他=5） */
    private Byte eventtype;

    /** 日志内容 */
    private String description;

    /** 运行日志内容 */
    private String diagnoseinfo;

    /** 备注 */
    private String remark;

    /** 创建人 */
    private String createuser;

    /** 创建时间 */
    private Date createtime;

    /** 城市id */
    private Integer orgId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Byte getBusinesstype() {
        return businesstype;
    }

    public void setBusinesstype(Byte businesstype) {
        this.businesstype = businesstype;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getBusinessno() {
        return businessno;
    }

    public void setBusinessno(String businessno) {
        this.businessno = businessno == null ? null : businessno.trim();
    }

    public Byte getEventtype() {
        return eventtype;
    }

    public void setEventtype(Byte eventtype) {
        this.eventtype = eventtype;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getDiagnoseinfo() {
        return diagnoseinfo;
    }

    public void setDiagnoseinfo(String diagnoseinfo) {
        this.diagnoseinfo = diagnoseinfo == null ? null : diagnoseinfo.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getCreateuser() {
        return createuser;
    }

    public void setCreateuser(String createuser) {
        this.createuser = createuser == null ? null : createuser.trim();
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }
}