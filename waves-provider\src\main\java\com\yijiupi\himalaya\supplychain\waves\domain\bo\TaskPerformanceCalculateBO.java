package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import java.util.Date;
import java.util.List;
import java.util.function.Consumer;

import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/25
 */
public class TaskPerformanceCalculateBO<T> {
    /**
     * 任务id列表
     */
    private List<String> taskIds;
    /**
     * 操作人id
     */
    private Integer operateUserId;
    /**
     * 操作时间
     */
    private String operateTime;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 操作
     */
    private Consumer<T> consumer;
    /**
     * 绩效所属班次
     */
    private String performanceDate;

    public TaskPerformanceCalculateBO() {}

    public TaskPerformanceCalculateBO(List<String> taskIds, Integer operateUserId, String operateTime) {
        this.taskIds = taskIds;
        this.operateUserId = operateUserId;
        this.operateTime = operateTime;
    }

    public TaskPerformanceCalculateBO(List<String> taskIds, Integer operateUserId, String operateTime,
        Integer warehouseId, Integer orgId) {
        this.taskIds = taskIds;
        this.operateUserId = operateUserId;
        this.operateTime = operateTime;
        this.warehouseId = warehouseId;
        this.orgId = orgId;
    }

    /**
     * 获取 任务id列表
     *
     * @return taskIds 任务id列表
     */
    public List<String> getTaskIds() {
        return this.taskIds;
    }

    /**
     * 设置 任务id列表
     *
     * @param taskIds 任务id列表
     */
    public void setTaskIds(List<String> taskIds) {
        this.taskIds = taskIds;
    }

    /**
     * 获取 操作人id
     *
     * @return operateUserId 操作人id
     */
    public Integer getOperateUserId() {
        return this.operateUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param operateUserId 操作人id
     */
    public void setOperateUserId(Integer operateUserId) {
        this.operateUserId = operateUserId;
    }

    /**
     * 获取 操作时间
     *
     * @return operateTime 操作时间
     */
    public String getOperateTime() {
        return this.operateTime;
    }

    /**
     * 设置 操作时间
     *
     * @param operateTime 操作时间
     */
    public void setOperateTime(String operateTime) {
        this.operateTime = operateTime;
    }

    /**
     * 获取 操作
     *
     * @return consumer 操作
     */
    public Consumer<T> getConsumer() {
        return this.consumer;
    }

    /**
     * 设置 操作
     *
     * @param consumer 操作
     */
    public void setConsumer(Consumer<T> consumer) {
        this.consumer = consumer;
    }

    /**
     * 获取 绩效所属班次
     *
     * @return performanceDate 绩效所属班次
     */
    public String getPerformanceDate() {
        return this.performanceDate;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 设置 绩效所属班次
     *
     * @param performanceDate 绩效所属班次
     */
    public void setPerformanceDate(String performanceDate) {
        this.performanceDate = performanceDate;
    }

    public static TaskPerformanceCalculateBO getInstance(List<String> taskIds, Integer operateUserId,
        String operateTime) {
        return new TaskPerformanceCalculateBO(taskIds, operateUserId, operateTime);
    }

    public static TaskPerformanceCalculateBO getInstance(List<String> taskIds, Integer operateUserId) {
        Date date = new Date();
        return new TaskPerformanceCalculateBO(taskIds, operateUserId, DateUtils.getCurrentDateTime(date));
    }

    public static TaskPerformanceCalculateBO getInstance(List<String> taskIds, Integer operateUserId,
        Consumer<?> consumer) {
        Date date = new Date();
        TaskPerformanceCalculateBO bo =
            new TaskPerformanceCalculateBO(taskIds, operateUserId, DateUtils.getCurrentDateTime(date));
        bo.setConsumer(consumer);

        return bo;
    }

    public static TaskPerformanceCalculateBO<BatchTaskPO> getBatchTaskInstance(List<String> taskIds,
        Integer operateUserId, Integer warehouseId, Integer orgId, Consumer<BatchTaskPO> consumer) {
        Date date = new Date();
        TaskPerformanceCalculateBO bo = new TaskPerformanceCalculateBO(taskIds, operateUserId,
            DateUtils.getCurrentDateTime(date), warehouseId, orgId);

        Consumer<TaskPerformanceCalculateBO<SowTaskPO>> batchTaskConsumer =
            taskPerformanceCalculateBO -> taskPerformanceCalculateBO.getTaskIds().forEach(taskId -> {
                BatchTaskPO updateBatchTaskPO = new BatchTaskPO();
                updateBatchTaskPO.setId(taskId);
                updateBatchTaskPO.setShiftOfPerformance(bo.getPerformanceDate());
                consumer.accept(updateBatchTaskPO);
            });
        bo.setConsumer(batchTaskConsumer);
        return bo;
    }

    public static TaskPerformanceCalculateBO<SowTaskPO> getSowTaskInstance(List<String> taskIds, Integer operateUserId,
        Integer warehouseId, Integer orgId, Consumer<SowTaskPO> consumer) {
        Date date = new Date();
        TaskPerformanceCalculateBO bo = new TaskPerformanceCalculateBO(taskIds, operateUserId,
            DateUtils.getCurrentDateTime(date), warehouseId, orgId);

        Consumer<TaskPerformanceCalculateBO<SowTaskPO>> sowTaskConsumer =
            taskPerformanceCalculateBO -> taskPerformanceCalculateBO.getTaskIds().forEach(taskId -> {
                SowTaskPO updateSowTaskPO = new SowTaskPO();
                // updateBatchTaskPO.setId(taskId);
                updateSowTaskPO.setId(Long.valueOf(taskId));
                updateSowTaskPO.setShiftOfPerformance(bo.getPerformanceDate());
                consumer.accept(updateSowTaskPO);
            });
        bo.setConsumer(sowTaskConsumer);
        return bo;
    }

}
