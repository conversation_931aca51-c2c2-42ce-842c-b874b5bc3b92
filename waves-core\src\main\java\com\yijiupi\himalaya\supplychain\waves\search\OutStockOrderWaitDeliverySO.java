package com.yijiupi.himalaya.supplychain.waves.search;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yijiupi.himalaya.base.search.PageCondition;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 待出库订单查询
 *
 * <AUTHOR>
 * @date 2019/4/17 12:02
 */
public class OutStockOrderWaitDeliverySO extends PageCondition implements Serializable {

    private static final long serialVersionUID = 2015910288764789607L;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 产品skuId集合
     */
    private List<String> skuIds;

    /**
     * 城市名称集合
     */
    private List<String> cityNameList;

    /**
     * 线路id集合
     */
    private List<Long> routeIdList;

    /**
     * 自提点id集合
     */
    private List<Integer> addressIdList;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 期望配送时间开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expStartTime;

    /**
     * 期望配送时间结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expEndTime;

    public Date getExpStartTime() {
        return expStartTime;
    }

    public void setExpStartTime(Date expStartTime) {
        this.expStartTime = expStartTime;
    }

    public Date getExpEndTime() {
        return expEndTime;
    }

    public void setExpEndTime(Date expEndTime) {
        this.expEndTime = expEndTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<String> getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(List<String> skuIds) {
        this.skuIds = skuIds;
    }

    public List<String> getCityNameList() {
        return cityNameList;
    }

    public void setCityNameList(List<String> cityNameList) {
        this.cityNameList = cityNameList;
    }

    public List<Long> getRouteIdList() {
        return routeIdList;
    }

    public void setRouteIdList(List<Long> routeIdList) {
        this.routeIdList = routeIdList;
    }

    public List<Integer> getAddressIdList() {
        return addressIdList;
    }

    public void setAddressIdList(List<Integer> addressIdList) {
        this.addressIdList = addressIdList;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }
}
