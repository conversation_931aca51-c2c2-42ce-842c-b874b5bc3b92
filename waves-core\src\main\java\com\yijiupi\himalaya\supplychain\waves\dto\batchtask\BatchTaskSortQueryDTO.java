package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import com.yijiupi.himalaya.supplychain.waves.enums.TaskWarehouseFeatureTypeConstants;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 按拣货员查找拣货任务
 *
 * <AUTHOR>
 * @since 2018/11/30 9:16
 */
public class BatchTaskSortQueryDTO implements Serializable {
    private static final long serialVersionUID = 4135762387533439623L;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private List<Byte> taskState;

    /**
     * 列表类别 1：待拣货或拣货中列表 2：已拣货列表 3：待领取列表
     */
    private Integer type;

    /**
     * 当前拣货员所属的拣货组id
     */
    private List<Long> sortGroupId;

    /**
     * 当前页
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 拣货任务模式：0:指派，1:抢单
     */
    private Byte batchTaskType;

    /**
     * 拣货员类型：1：大件拣货员（按产品） 2：小件拣货员（按订单）
     */
    private Byte sorterType;

    /**
     * 拣货排序优先级：1：分区优先（分区分单不播种） 2：波次优先（分区分单播种）
     */
    private Byte orderByType;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 通道id列表
     */
    private List<Long> passageIds;
    /**
     * 分仓属性：0、不分仓；1、酒饮；2、休百
     * 
     * @see TaskWarehouseFeatureTypeConstants
     */
    private Byte splitWarehouseAttr;
    /**
     * @see TaskWarehouseFeatureTypeConstants
     */
    private List<Byte> splitWarehouseAttrList;

    public Byte getOrderByType() {
        return orderByType;
    }

    public void setOrderByType(Byte orderByType) {
        this.orderByType = orderByType;
    }

    public Byte getSorterType() {
        return sorterType;
    }

    public void setSorterType(Byte sorterType) {
        this.sorterType = sorterType;
    }

    public Byte getBatchTaskType() {
        return batchTaskType;
    }

    public void setBatchTaskType(Byte batchTaskType) {
        this.batchTaskType = batchTaskType;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<Long> getSortGroupId() {
        return sortGroupId;
    }

    public void setSortGroupId(List<Long> sortGroupId) {
        this.sortGroupId = sortGroupId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public List<Byte> getTaskState() {
        return taskState;
    }

    public void setTaskState(List<Byte> taskState) {
        this.taskState = taskState;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * 获取 通道id列表
     *
     * @return passageIds 通道id列表
     */
    public List<Long> getPassageIds() {
        return this.passageIds;
    }

    /**
     * 设置 通道id列表
     *
     * @param passageIds 通道id列表
     */
    public void setPassageIds(List<Long> passageIds) {
        this.passageIds = passageIds;
    }

    /**
     * 获取 分仓属性：0、不分仓；1、酒饮；2、休百 @see TaskWarehouseFeatureTypeConstants
     *
     * @return splitWarehouseAttr 分仓属性：0、不分仓；1、酒饮；2、休百 @see TaskWarehouseFeatureTypeConstants
     */
    public Byte getSplitWarehouseAttr() {
        return this.splitWarehouseAttr;
    }

    /**
     * 设置 分仓属性：0、不分仓；1、酒饮；2、休百 @see TaskWarehouseFeatureTypeConstants
     *
     * @param splitWarehouseAttr 分仓属性：0、不分仓；1、酒饮；2、休百 @see TaskWarehouseFeatureTypeConstants
     */
    public void setSplitWarehouseAttr(Byte splitWarehouseAttr) {
        this.splitWarehouseAttr = splitWarehouseAttr;
    }

    /**
     * 获取 @see TaskWarehouseFeatureTypeConstants
     *
     * @return splitWarehouseAttrList @see TaskWarehouseFeatureTypeConstants
     */
    public List<Byte> getSplitWarehouseAttrList() {
        return this.splitWarehouseAttrList;
    }

    /**
     * 设置 @see TaskWarehouseFeatureTypeConstants
     *
     * @param splitWarehouseAttrList @see TaskWarehouseFeatureTypeConstants
     */
    public void setSplitWarehouseAttrList(List<Byte> splitWarehouseAttrList) {
        this.splitWarehouseAttrList = splitWarehouseAttrList;
    }
}
