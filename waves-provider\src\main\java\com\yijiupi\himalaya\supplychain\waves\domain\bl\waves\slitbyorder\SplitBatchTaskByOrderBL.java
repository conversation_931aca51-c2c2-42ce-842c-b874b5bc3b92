package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.slitbyorder;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.SourceType;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderFeatureBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderRequestBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/13
 */
public abstract class SplitBatchTaskByOrderBL {

    @Autowired
    protected OrderFeatureBL orderFeatureBL;

    // 占位符解释：0 特征； 1 是否是促销订单； 2 地址id； 4，5 冗余
    protected static final String pattern = "{0}-{1}-{2}-{3}-{4}";
    protected static final String DEFAULT_GROUP = "default";
    protected final static List<Byte> abnormalSourceTypes =
        Arrays.asList(SourceType.SUPPLY_AGAIN.getValue(), SourceType.WRONG_DELIVERY.getValue());
    protected static final Logger LOG = LoggerFactory.getLogger(SplitBatchTaskByOrderBL.class);

    List<OutStockOrderPO> supportOrderList(SplitBatchTaskByOrderRequestBO bo) {
        return null;
    }

    public List<SplitBatchTaskByOrderResultBO> splitBatchTaskByOrder(SplitBatchTaskByOrderRequestBO bo) {
        List<OutStockOrderPO> outStockOrderPOList = supportOrderList(bo);
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return Collections.emptyList();
        }
        return doSplitBatchTaskByOrder(bo, outStockOrderPOList);
    }

    protected abstract List<SplitBatchTaskByOrderResultBO> doSplitBatchTaskByOrder(SplitBatchTaskByOrderRequestBO bo,
        List<OutStockOrderPO> outStockOrderPOList);

    public List<SplitBatchTaskByOrderResultBO> splitByOrderFeatureAndAddress(List<OutStockOrderPO> waveOrders) {
        List<Long> outStockOrderIds =
            waveOrders.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());

        final Map<Long, List<Byte>> featureMap = orderFeatureBL.getOrderFeatureMap(outStockOrderIds);

        Map<String, List<OutStockOrderPO>> totalMap = waveOrders.stream()
            .collect(Collectors.groupingBy(order -> groupOrderByFeatureAndAddress(order, featureMap)));

        return SplitBatchTaskByOrderResultBO.getNormalTaskList(totalMap);
    }

    private String groupOrderByFeatureAndAddress(OutStockOrderPO order, Map<Long, List<Byte>> featureMap) {
        List<Byte> featureList = featureMap.get(order.getId());
        boolean isPromotion = SplitWaveOrderUtil.orderIsPromotion(order);
        Byte promotionCon = isPromotion ? ConditionStateEnum.是.getType() : ConditionStateEnum.否.getType();
        Integer addressId = Objects.nonNull(order.getAddressId()) ? order.getAddressId() : -1;

        String defaultKey = MessageFormat.format(pattern, DEFAULT_GROUP, DEFAULT_GROUP, addressId);
        if (CollectionUtils.isEmpty(featureList)) {
            return defaultKey;
        }
        if (featureList.stream().anyMatch(OrderFeatureConstant.FEATURE_TYPE_DRINKING::equals)) {
            return MessageFormat.format(pattern, OrderFeatureConstant.FEATURE_TYPE_DRINKING.toString(), promotionCon,
                addressId);
        }
        if (featureList.stream().anyMatch(OrderFeatureConstant.FEATURE_TYPE_REST::equals)) {
            return MessageFormat.format(pattern, OrderFeatureConstant.FEATURE_TYPE_REST.toString(), promotionCon,
                addressId);
        }
        return defaultKey;
    }
}
