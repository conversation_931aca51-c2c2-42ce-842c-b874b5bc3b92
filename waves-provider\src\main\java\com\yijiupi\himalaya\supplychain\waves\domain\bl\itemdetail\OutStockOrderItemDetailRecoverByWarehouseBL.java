package com.yijiupi.himalaya.supplychain.waves.domain.bl.itemdetail;

import java.util.Arrays;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDetailRecoverByWarehouseDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/29
 */
@Service
public class OutStockOrderItemDetailRecoverByWarehouseBL {

    @Reference(timeout = 60000)
    private IWarehouseQueryService warehouseQueryService;
    private static final List<Integer> OUTBOUNDTYPE_LIST =
        Arrays.asList(OutBoundTypeEnum.ALLOT_ORDER.getCode(), OutBoundTypeEnum.SALE_ORDER.getCode(),
            OutBoundTypeEnum.ALLOT_ORDER.getCode(), OutBoundTypeEnum.SELF_PICKUP_SALE_ORDER.getCode(),
            OutBoundTypeEnum.INTERNAL_DELIVERY_ORDER.getCode(), OutBoundTypeEnum.OFFLINE_OUT_ORDER.getCode());

    protected static final Logger LOGGER = LoggerFactory.getLogger(OutStockOrderItemDetailRecoverByWarehouseBL.class);

    /**
     * 根据仓库，查询出
     * 
     * @param dto
     */
    public void recoverDetailByWarehouse(OutStockOrderItemDetailRecoverByWarehouseDTO dto) {
        // 已启用仓库
        // 城市仓库((byte) 0),
        // 集货点((byte) 5),
        // 店仓合一((byte) 6),
        // 前置仓((byte) 8)
        List<Warehouse> warehouseList = warehouseQueryService.listEnableWarehouseByTypes(Arrays.asList(0, 1, 5, 6, 8));
        if (CollectionUtils.isEmpty(warehouseList)) {
            LOGGER.info("[更新残次品额度]没有查询到任何仓库");
            return;
        }
        for (Warehouse warehouseElem : warehouseList) {
            if (warehouseElem.getCityId() == null || warehouseElem.getCityId() < 100) {
                continue;
            }
        }
    }

}
