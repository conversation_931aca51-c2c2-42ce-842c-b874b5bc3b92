package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 分包明细同步
 * @author: <PERSON>
 * @date: 2018年07月25日 09:50
 */
public class PackageBoxItemDTO implements Serializable {
    private static final long serialVersionUID = 335385090734149198L;
    /**
     * 用户ID
     */
    private Integer createUserId;
    /**
     * 城市ID
     */
    private Integer cityId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 业务单id
     */
    private Long businessId;
    /**
     * 订单项ID
     */
    private Long businessItemId;
    /**
     * 包装箱码
     */
    private String packageBoxNO;
    /**
     * 箱码编号
     */
    private String packageBoxSequence;
    /**
     * 包装数量
     */
    private BigDecimal packageCount;
    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 包装类型: 0.包装箱 1.托盘
     */
    private Byte packageType;

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Long getBusinessItemId() {
        return businessItemId;
    }

    public void setBusinessItemId(Long businessItemId) {
        this.businessItemId = businessItemId;
    }

    public String getPackageBoxNO() {
        return packageBoxNO;
    }

    public void setPackageBoxNO(String packageBoxNO) {
        this.packageBoxNO = packageBoxNO;
    }

    public String getPackageBoxSequence() {
        return packageBoxSequence;
    }

    public void setPackageBoxSequence(String packageBoxSequence) {
        this.packageBoxSequence = packageBoxSequence;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Byte getPackageType() {
        return packageType;
    }

    public void setPackageType(Byte packageType) {
        this.packageType = packageType;
    }

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }
}
