package com.yijiupi.himalaya.supplychain.waves.dto.soworder;

import java.io.Serializable;

public class SowAddressDTO implements Serializable {

    /**
     * 播种订单id
     */
    private Long sowOrderId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 容器编号
     */
    private Integer containerNo;

    /**
     * 自提点名称
     */
    private String shopName;

    /**
     * 自提点出库位id
     */
    private Long toLocationId;

    /**
     * 自提点出库位名称
     */
    private String toLocationName;

    /**
     * 容器货位id
     */
    private Long locationId;

    /**
     * 容器货位名称
     */
    private String locationName;

    /**
     * 线路名称
     */
    private String routeName;

    public Long getSowOrderId() {
        return sowOrderId;
    }

    public void setSowOrderId(Long sowOrderId) {
        this.sowOrderId = sowOrderId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Integer getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(Integer containerNo) {
        this.containerNo = containerNo;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }
}
