package com.yijiupi.himalaya.supplychain.waves.dto.secondsort;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/26
 */
public class CompleteSecondPickSowDTO implements Serializable {
    private static final long serialVersionUID = 358723158861519124L;
    /**
     * 城市ID
     */
    private Integer orgId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 分拣任务项ID
     */
    private String batchTaskItemId;
    /**
     * 分拣任务项ID
     */
    private List<String> batchTaskItemIds;
    /**
     * 分拣任务ID
     */
    private String batchTaskId;
    /**
     * 操作人ID
     */
    private Integer optUserId;
    /**
     * 操作人名称
     */
    private String optUserName;

    public Integer getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }

    public String getOptUserName() {
        return optUserName;
    }

    public void setOptUserName(String optUserName) {
        this.optUserName = optUserName;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public List<String> getBatchTaskItemIds() {
        return batchTaskItemIds;
    }

    public void setBatchTaskItemIds(List<String> batchTaskItemIds) {
        this.batchTaskItemIds = batchTaskItemIds;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getBatchTaskItemId() {
        return batchTaskItemId;
    }

    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }
}
