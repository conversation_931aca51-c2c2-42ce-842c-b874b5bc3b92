package com.yijiupi.himalaya.supplychain.waves.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

public enum BillReviewStateEnum {
    /**
     * 枚举
     */
    待复核((byte)0), 复核中((byte)1), 已复核((byte)2);

    /**
     * type
     */
    private byte type;

    BillReviewStateEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     * 
     * @param value
     * @return
     */
    public static String getEnumByValue(Byte value) {
        BillReviewStateEnum billReviewStateEnum = null;
        if (value != null) {
            billReviewStateEnum = cache.get(value);
        }
        return billReviewStateEnum == null ? null : billReviewStateEnum.name();
    }

    private static Map<Byte, BillReviewStateEnum> cache =
        EnumSet.allOf(BillReviewStateEnum.class).stream().collect(Collectors.toMap(p -> p.type, p -> p));
}
