package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.util.List;

/**
 * 生成波次分配优先级
 *
 * <AUTHOR>
 * @date 2019-08-22 17:36
 */
public class BatchAllotPriorityDTO implements Serializable {
    private static final long serialVersionUID = 8109675279436664946L;

    /**
     * 缺货产品列表
     */
    private List<String> productList;

    /**
     * 城市优先级
     */
    private List<String> cityList;

    /**
     * 线路优先级
     */
    private List<String> routeList;

    public List<String> getProductList() {
        return productList;
    }

    public void setProductList(List<String> productList) {
        this.productList = productList;
    }

    public List<String> getCityList() {
        return cityList;
    }

    public void setCityList(List<String> cityList) {
        this.cityList = cityList;
    }

    public List<String> getRouteList() {
        return routeList;
    }

    public void setRouteList(List<String> routeList) {
        this.routeList = routeList;
    }
}
