package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/16
 */
public class SowTaskItemQueryDTO implements Serializable {
    private static final long serialVersionUID = 5365420182181849996L;
    /**
     * 城市id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务项id列表
     */
    private List<Long> sowTaskItemIds;
    /**
     * 播种任务项id
     */
    private Long sowTaskItemId;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public List<Long> getSowTaskItemIds() {
        return sowTaskItemIds;
    }

    public void setSowTaskItemIds(List<Long> sowTaskItemIds) {
        this.sowTaskItemIds = sowTaskItemIds;
    }

    public Long getSowTaskItemId() {
        return sowTaskItemId;
    }

    public void setSowTaskItemId(Long sowTaskItemId) {
        this.sowTaskItemId = sowTaskItemId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
