package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO;

/**
 * 出库单项拣货项关联表
 *
 * <AUTHOR>
 * @date 2020-09-04 15:32
 */
@Mapper
public interface OrderItemTaskInfoDetailMapper {

    int deleteByPrimaryKey(Long id);

    /**
     * 批量新增
     */
    void insertBatch(@Param("list") List<OrderItemTaskInfoDetailPO> recordPOList);

    /**
     * 批量更新分配数量（覆盖）
     */
    void updateBatch(@Param("list") List<OrderItemTaskInfoDetailPO> recordPOList);

    /**
     * 批量更新分配数量（增减）
     */
    void updateBatchCount(@Param("list") List<OrderItemTaskInfoDetailPO> recordPOList);

    int insertSelective(OrderItemTaskInfoDetailPO record);

    OrderItemTaskInfoDetailPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderItemTaskInfoDetailPO record);

    void deleteByIds(@Param("list") List<Long> delInfoDetailIds);

    List<Long> selectIdsByTaskInfoIds(@Param("taskInfoIds") List<Long> taskInfoIds);

}
