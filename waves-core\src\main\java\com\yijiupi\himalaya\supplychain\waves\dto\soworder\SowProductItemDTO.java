package com.yijiupi.himalaya.supplychain.waves.dto.soworder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;

public class SowProductItemDTO implements Serializable {

    /**
     * 出库单id
     */
    private Long outStockOrderId;

    /**
     * 出库单项id
     */
    private Long outStockOrderItemId;

    /**
     * 商品id
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 包装规格名称
     */
    private String specName;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal specQuantity;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 大单位数量
     */
    private BigDecimal packageCount;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 小单位数量
     */
    private BigDecimal unitCount;

    /**
     * 取货位id
     */
    private Long pickLocationId;

    /**
     * 取货位名称
     */
    private String pickLocationName;

    /**
     * 货位序号
     */
    private Integer locationSequence;

    /**
     * 容器号码
     */
    private Integer containerNo;

    /**
     * 播种数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 自提点名称
     */
    private String shopName;

    /**
     * 容器货位id
     */
    private Long containerLocationId;

    /**
     * 容器货位名称
     */
    private String containerLocationName;

    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Byte source;

    private String sourceText;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Byte channel;

    private String channelText;
    /**
     * 规格id
     */
    private Long productSpecificationId;

    /**
     * 瓶码集合
     */
    private List<String> unitCode;

    /**
     * 箱码集合
     */
    private List<String> packageCode;

    /**
     * 包装箱编号
     */
    private List<PackageOrderItemDTO> packageOrderItems;

    /**
     * 已包装数量
     */
    private BigDecimal packedTotalCount;

    /**
     * 已包装数量
     */
    private BigDecimal unPackagedTotalCount;

    /**
     * 出库位id
     */
    private Long toLocationId;

    /**
     * 出库位名称
     */
    private String toLocationName;
    /**
     * 播种状态 枚举：SowTaskStateEnum
     */
    private Byte state;

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Long getOutStockOrderItemId() {
        return outStockOrderItemId;
    }

    public void setOutStockOrderItemId(Long outStockOrderItemId) {
        this.outStockOrderItemId = outStockOrderItemId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public Long getPickLocationId() {
        return pickLocationId;
    }

    public void setPickLocationId(Long pickLocationId) {
        this.pickLocationId = pickLocationId;
    }

    public String getPickLocationName() {
        return pickLocationName;
    }

    public void setPickLocationName(String pickLocationName) {
        this.pickLocationName = pickLocationName;
    }

    public Integer getLocationSequence() {
        return locationSequence;
    }

    public void setLocationSequence(Integer locationSequence) {
        this.locationSequence = locationSequence;
    }

    public Integer getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(Integer containerNo) {
        this.containerNo = containerNo;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Long getContainerLocationId() {
        return containerLocationId;
    }

    public void setContainerLocationId(Long containerLocationId) {
        this.containerLocationId = containerLocationId;
    }

    public String getContainerLocationName() {
        return containerLocationName;
    }

    public void setContainerLocationName(String containerLocationName) {
        this.containerLocationName = containerLocationName;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public Byte getChannel() {
        return channel;
    }

    public void setChannel(Byte channel) {
        this.channel = channel;
    }

    public Long getOutStockOrderId() {
        return outStockOrderId;
    }

    public void setOutStockOrderId(Long outStockOrderId) {
        this.outStockOrderId = outStockOrderId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public List<String> getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(List<String> unitCode) {
        this.unitCode = unitCode;
    }

    public List<String> getPackageCode() {
        return packageCode;
    }

    public void setPackageCode(List<String> packageCode) {
        this.packageCode = packageCode;
    }

    public List<PackageOrderItemDTO> getPackageOrderItems() {
        return packageOrderItems;
    }

    public void setPackageOrderItems(List<PackageOrderItemDTO> packageOrderItems) {
        this.packageOrderItems = packageOrderItems;
    }

    public BigDecimal getPackedTotalCount() {
        return packedTotalCount;
    }

    public void setPackedTotalCount(BigDecimal packedTotalCount) {
        this.packedTotalCount = packedTotalCount;
    }

    public BigDecimal getUnPackagedTotalCount() {
        return unPackagedTotalCount;
    }

    public void setUnPackagedTotalCount(BigDecimal unPackagedTotalCount) {
        this.unPackagedTotalCount = unPackagedTotalCount;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public String getSourceText() {
        return sourceText;
    }

    public void setSourceText(String sourceText) {
        this.sourceText = sourceText;
    }

    public String getChannelText() {
        return channelText;
    }

    public void setChannelText(String channelText) {
        this.channelText = channelText;
    }
}
