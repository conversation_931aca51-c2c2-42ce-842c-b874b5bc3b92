package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/8/15
 */
public class OutStockLackCalResultDTO implements Serializable {
    /**
     * oms 的 itemid
     */
    private Long businessItemId;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 小单位数量
     */
    private BigDecimal unitTotalCount;
    /**
     * 产品类型；0-普通产品，1-赠品，2-组合产品
     */
    private Integer productType;
    /**
     * 组合id，产品类型为组合产品时有值
     */
    private Long compositeId;

    /**
     * 获取 oms 的 itemid
     *
     * @return businessItemId oms 的 itemid
     */
    public Long getBusinessItemId() {
        return this.businessItemId;
    }

    /**
     * 设置 oms 的 itemid
     *
     * @param businessItemId oms 的 itemid
     */
    public void setBusinessItemId(Long businessItemId) {
        this.businessItemId = businessItemId;
    }

    /**
     * 获取 订单id
     *
     * @return orderId 订单id
     */
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 订单id
     *
     * @param orderId 订单id
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 小单位数量
     *
     * @return unitTotalCount 小单位数量
     */
    public BigDecimal getUnitTotalCount() {
        return this.unitTotalCount;
    }

    /**
     * 设置 小单位数量
     *
     * @param unitTotalCount 小单位数量
     */
    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    /**
     * 获取 产品类型；0-普通产品，1-赠品，2-组合产品
     *
     * @return productType 产品类型；0-普通产品，1-赠品，2-组合产品
     */
    public Integer getProductType() {
        return this.productType;
    }

    /**
     * 设置 产品类型；0-普通产品，1-赠品，2-组合产品
     *
     * @param productType 产品类型；0-普通产品，1-赠品，2-组合产品
     */
    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    /**
     * 获取 组合id，产品类型为组合产品时有值
     *
     * @return compositeId 组合id，产品类型为组合产品时有值
     */
    public Long getCompositeId() {
        return this.compositeId;
    }

    /**
     * 设置 组合id，产品类型为组合产品时有值
     *
     * @param compositeId 组合id，产品类型为组合产品时有值
     */
    public void setCompositeId(Long compositeId) {
        this.compositeId = compositeId;
    }
}
