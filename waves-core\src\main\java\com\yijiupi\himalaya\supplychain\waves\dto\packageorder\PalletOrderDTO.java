package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

public class PalletOrderDTO implements Serializable {

    /**
     * 托盘号
     */
    private String boxCodeNo;

    /**
     * 拣货员名称
     */
    private String sorter;

    /**
     * 产品种类数
     */
    private Integer skuCount;

    /**
     * 已拣大数量
     */
    private BigDecimal packageCount;

    /**
     * 已拣小数量
     */
    private BigDecimal unitCount;

    /**
     * 出库位
     */
    private Long locationId;

    /**
     * 出库位名称
     */
    private String locationName;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operatingTime;

    /**
     * 包装明细id
     */
    private Long id;

    /**
     * 波次号
     */
    private String batchNo;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 出库单id
     */
    private Long refOrderId;

    public String getBoxCodeNo() {
        return boxCodeNo;
    }

    public void setBoxCodeNo(String boxCodeNo) {
        this.boxCodeNo = boxCodeNo;
    }

    public String getSorter() {
        return sorter;
    }

    public void setSorter(String sorter) {
        this.sorter = sorter;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getOperatingTime() {
        return operatingTime;
    }

    public void setOperatingTime(Date operatingTime) {
        this.operatingTime = operatingTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public Long getRefOrderId() {
        return refOrderId;
    }

    public void setRefOrderId(Long refOrderId) {
        this.refOrderId = refOrderId;
    }
}
