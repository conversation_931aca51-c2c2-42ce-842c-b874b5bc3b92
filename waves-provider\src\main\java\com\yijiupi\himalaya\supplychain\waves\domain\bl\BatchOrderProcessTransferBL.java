package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.baseutil.NullUtils;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.enums.PassagePickTypeEnum;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.CarDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.ICarService;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutBoundBatchDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutBoundBatchManageService;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserService;
import com.yijiupi.himalaya.supplychain.virtualwarehouse.service.IVirtualWarehouseManageService;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.CreateBatchByRefOrderNoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchTitleConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.WavesStrategyBOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.mq.CheckOrderBatchMQ;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.*;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.UpdateLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.secondsort.SecondPickLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.secondsort.SecondPickLocationSyncDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 波次策略执行中转类
 *
 * <AUTHOR>
 * @Date 2018-03-16
 */
@Service
public class BatchOrderProcessTransferBL {

    private static final Logger LOG = LoggerFactory.getLogger(BatchOrderProcessTransferBL.class);

    @Autowired
    private BatchOrderProcessBL batchOrderProcessBL;

    @Autowired
    private OutStockOrderBL outStockOrderBL;

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;

    @Autowired
    private BatchOrderAllotBL batchOrderAllotBL;

    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    @Autowired
    private CheckOrderBatchMQ checkOrderBatchMQ;

    @Reference
    private IVariableValueService iVariableValueService;

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;

    @Autowired
    private BatchOrderBL batchOrderBL;

    @Autowired
    private ThirdPartyOutStockBL thirdPartyOutStockBL;
    @Autowired
    private OrderCenterBL orderCenterBL;
    @Reference
    private IOutBoundBatchManageService iOutBoundBatchManageService;
    @Autowired
    private SecondSortBL secondSortBl;
    @Reference
    private ICarService iCarService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Reference
    private IOrgService iOrgService;
    @Reference
    private IAdminUserService iAdminUserService;

    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private WavesStrategyBOConvertor wavesStrategyBOConvertor;
    @Autowired
    private BatchTitleConvertor batchTitleConvertor;
    @Autowired
    private GlobalCache globalCache;

    /**
     * 手动新增波次（只有3.0） --客户端调用
     * 
     * @see com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.CreateBatchByManualOperationBL
     * <AUTHOR>
     * @date 2018/4/2 15:06
     */
    @Deprecated
    public void createBatch(BatchCreateDTO batchCreateDTO) {
        if (StringUtils.isEmpty(batchCreateDTO.getBatchName())) {
            batchCreateDTO.setBatchName("手动生成波次");
        }
        LOG.info("手动新增波次{}", JSON.toJSONString(batchCreateDTO));

        // 组装波次参数
        WavesStrategyBO wavesStrategyDTO = wavesStrategyBOConvertor.getWavesStrategyDTOByString(
            batchCreateDTO.getWarehouseId(), batchCreateDTO.getPassPickType(), batchCreateDTO.getPickingType(),
            batchCreateDTO.getPickingGroupStrategy(), batchCreateDTO.getGroupType(), batchCreateDTO.getOrderPickFlag());
        wavesStrategyDTO.setDeliveryCarId(batchCreateDTO.getDeliveryCarId());
        wavesStrategyDTO.setLogisticsCompanyId(batchCreateDTO.getLogisticsCompanyId());
        // 查询订单
        List<OutStockOrderPO> outStockOrderPOList;
        if (Objects.equals(batchCreateDTO.getBatchType(), BatchTypeEnum.微酒.getType())) {
            outStockOrderPOList = outStockOrderBL.getOutStockOrderByWine(batchCreateDTO.getOrderIdList());
        } else if (BatchTypeEnum.isApplyOrderBatch(batchCreateDTO.getBatchType())) {
            // 第三方出库新流程，微酒和易经销都需要拣货
            outStockOrderPOList = thirdPartyOutStockBL.getOutStockOrderByThirdParty(batchCreateDTO.getOrderIdList());
        } else {
            outStockOrderPOList = outStockOrderMapper.findByOrderId(
                batchCreateDTO.getOrderIdList().stream().map(Long::valueOf).collect(Collectors.toList()));
            // 检验订单异常
            batchOrderProcessBL.validateOrderException(outStockOrderPOList);
        }

        checkStateErrorOrders(outStockOrderPOList);

        ProcessBatchDTO processBatchDTO = new ProcessBatchDTO();
        processBatchDTO.setBatchName(batchCreateDTO.getBatchName());
        processBatchDTO.setOperateUser(batchCreateDTO.getOperateUser());
        processBatchDTO.setExpressFlag(batchCreateDTO.getExpressFlag());
        processBatchDTO.setBatchType(batchCreateDTO.getBatchType());
        processBatchDTO.setOrderPickFlag(batchCreateDTO.getOrderPickFlag());
        // 默认按线路或片区拆分
        processBatchDTO.setGroupFlag(ConditionStateEnum.是.getType());
        processBatchDTO.setOperateUserId(batchCreateDTO.getOperateUserId());
        // 创建波次
        processCreateBatch(outStockOrderPOList, wavesStrategyDTO, processBatchDTO);

    }

    /**
     * 创建波次统一处理
     * 
     * @param outStockOrderPOList 待处理的订单
     * @param wavesStrategyDTO 拆分波次的条件
     * @param processBatchDTO 拆分波次的条件（扩展）
     */
    @Transactional(rollbackFor = Exception.class)
    public void processCreateBatch(List<OutStockOrderPO> outStockOrderPOList, WavesStrategyBO wavesStrategyDTO,
        ProcessBatchDTO processBatchDTO) {
        // 给订单加锁 并过滤已加锁的订单
        // 给订单加分布式锁提前了
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            LOG.info("生成波次失败，订单已全部锁定,执行结束");
            throw new BusinessValidateException("生成波次失败，订单已全部锁定,执行结束");

        }

        Map<Long, String> boundNoStockOrderMap =
            outStockOrderPOList.stream().filter(m -> StringUtils.isNotEmpty(m.getBoundNo()))
                .collect(Collectors.toMap(OutStockOrderPO::getId, OutStockOrderPO::getBoundNo));
        Map<Long, String> pickUpUserStockOrderMap =
            outStockOrderPOList.stream().filter(m -> StringUtils.isNotEmpty(m.getPickUpUserName()))
                .collect(Collectors.toMap(OutStockOrderPO::getId, OutStockOrderPO::getPickUpUserName));
        // 再次过滤
        List<String> orderIds = outStockOrderPOList.stream().filter(p -> p.getId() != null)
            .map(p -> p.getId().toString()).distinct().collect(Collectors.toList());
        if (Objects.equals(processBatchDTO.getBatchType(), BatchTypeEnum.微酒.getType())) {
            outStockOrderPOList = outStockOrderBL.getOutStockOrderByWine(orderIds);
        } else if (BatchTypeEnum.isApplyOrderBatch(processBatchDTO.getBatchType())) {
            outStockOrderPOList = thirdPartyOutStockBL.getOutStockOrderByThirdParty(orderIds);
        } else {
            outStockOrderPOList = Lists.partition(orderIds, 180).stream()
                .map(ids -> outStockOrderMapper
                    .findByOrderId(ids.stream().map(Long::valueOf).collect(Collectors.toList())))
                .filter(com.alibaba.dubbo.common.utils.CollectionUtils::isNotEmpty).flatMap(Collection::stream)
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            LOG.info("生成波次失败，订单已生波次,执行结束");
            throw new BusinessValidateException("生成波次失败，订单已生波次,执行结束");
        }

        outStockOrderPOList = filerOutStockOrderList(outStockOrderPOList);

        outStockOrderPOList.stream().filter(m -> StringUtils.isBlank(m.getBoundNo())).forEach(m -> {
            m.setBoundNo(boundNoStockOrderMap.get(m.getId()));
        });
        outStockOrderPOList.forEach(m -> {
            m.setPickUpUserName(pickUpUserStockOrderMap.get(m.getId()));
        });

        List<String> refOrderNos =
            outStockOrderPOList.stream().map(p -> p.getReforderno()).distinct().collect(Collectors.toList());

        // 设置订单来源
        setOrderSource(outStockOrderPOList);

        // 设置订单目的仓库
        setOrderToWarehouse(outStockOrderPOList, processBatchDTO.getOrderList());

        // 设置团购订单
        setGroupBuyOrder(outStockOrderPOList, wavesStrategyDTO, processBatchDTO);

        // 1、按是否跨库拆分波次
        Map<Byte, List<OutStockOrderPO>> crossWarehouseMap = outStockOrderPOList.stream()
            .collect(Collectors.groupingBy(p -> p.getCrossWareHouse() == null ? 0 : p.getCrossWareHouse()));
        crossWarehouseMap.forEach((crossWarehouse, crossWarehouseOrderList) -> {

            // 2、按订单来源拆分波次
            Map<Byte, List<OutStockOrderPO>> orderSourceMap = crossWarehouseOrderList.stream()
                .collect(Collectors.groupingBy(p -> p.getOrderSource() == null ? 0 : p.getOrderSource()));
            orderSourceMap.forEach((orderSource, orderSourceOrderList) -> {

                // 创建波次
                ProcessBatchDTO orderSourceProcessBatchDTO = new ProcessBatchDTO();
                BeanUtils.copyProperties(processBatchDTO, orderSourceProcessBatchDTO);
                orderSourceProcessBatchDTO.setCrossWareHouse(crossWarehouse);
                if (orderSource != 0) {
                    // 外部平台订单（拼多多）
                    orderSourceProcessBatchDTO.setExpressFlag(ExpressFlagEnum.快递直发订单.getType());
                    orderSourceProcessBatchDTO.setBatchType(BatchTypeEnum.外部平台.getType());
                }
                LOG.info("不同订单来源[{}]创建波次：{}", orderSource, JSON.toJSONString(
                    orderSourceOrderList.stream().map(p -> p.getReforderno()).collect(Collectors.toList())));
                batchOrderProcessBL.createBatchByOrders(orderSourceOrderList, wavesStrategyDTO,
                    orderSourceProcessBatchDTO);
            });
        });

        // 检查订单是否重复生波次(发消息)
        CheckOrderRepeatDTO checkOrderRepeatDTO = new CheckOrderRepeatDTO();
        checkOrderRepeatDTO.setWarehouseId(wavesStrategyDTO.getWarehouseId());
        checkOrderRepeatDTO.setRefOrderNos(refOrderNos);
        checkOrderBatchMQ.send(checkOrderRepeatDTO);

        LOG.info("波次处理结束：{}", JSON.toJSONString(refOrderNos));
    }

    // 过滤重复订单
    private List<OutStockOrderPO> filerOutStockOrderList(List<OutStockOrderPO> orderList) {
        orderList.stream().collect(Collectors.groupingBy(OutStockOrderPO::getReforderno)).forEach((key, orders) -> {
            if (orders.size() > 1) {
                LOG.error("订单号重复，订单号：{}", key);
            }
        });
        return orderList.stream()
            .collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(OutStockOrderPO::getReforderno))),
                ArrayList::new));
    }

    /**
     * 通过订单号 手动新增波次（只有2.5）--外部接口调用
     * 
     * @see CreateBatchByRefOrderNoBL
     * <AUTHOR>
     * @date 2018/4/2 15:06
     */
    @Deprecated
    public void createBatchByRefOrderNo(BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO) {
        LOG.info("新增波次{}", JSON.toJSONString(batchCreateByRefOrderNoDTO));
        WavesStrategyBO.WavesStrategyBOBuilder builder = WavesStrategyBO.WavesStrategyBOBuilder
            .buildWavesStrategyBuilder().withWarehouseId(batchCreateByRefOrderNoDTO.getWarehouseId())
            .withPassPickType(batchCreateByRefOrderNoDTO.getPassPickType())
            .withPickingType(batchCreateByRefOrderNoDTO.getPickingType())
            .withGroupType(batchCreateByRefOrderNoDTO.getGroupType())
            .withPickingGroupStrategy(batchCreateByRefOrderNoDTO.getPickingGroupStrategy())
            .withDeliveryCarId(batchCreateByRefOrderNoDTO.getDeliveryCarId())
            .withLogisticsCompanyId(batchCreateByRefOrderNoDTO.getLogisticsCompanyId());
        WavesStrategyBO wavesStrategyDTO = wavesStrategyBOConvertor.createByBuilder(builder);
        wavesStrategyDTO.setDeliveryCarId(batchCreateByRefOrderNoDTO.getDeliveryCarId());
        wavesStrategyDTO.setLogisticsCompanyId(batchCreateByRefOrderNoDTO.getLogisticsCompanyId());
        List<String> refOrderNoList = batchCreateByRefOrderNoDTO.getRefOrderNos();
        // 内配订单
        if (!CollectionUtils.isEmpty(batchCreateByRefOrderNoDTO.getOrderList())) {
            refOrderNoList = batchCreateByRefOrderNoDTO.getOrderList().stream()
                .filter(p -> StringUtils.isNotEmpty(p.getRefOrderNo())).map(p -> p.getRefOrderNo()).distinct()
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(refOrderNoList)) {
            throw new BusinessException("生成波次失败，订单不能为空！");
        }
        Long toLocationId = batchCreateByRefOrderNoDTO.getToLocationId();
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findNoWaveOrderByRefOrderNo(refOrderNoList,
            batchCreateByRefOrderNoDTO.getWarehouseId());
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            LOG.warn("订单不能为空");
            return;
        }
        // 判断是否选择了出库位
        if (!ObjectUtils.isEmpty(toLocationId)) {
            // 修改出库位信息
            List<Long> collect = outStockOrderPOList.stream()
                .flatMap(o -> o.getItems().stream().map(OutStockOrderItemPO::getId)).collect(Collectors.toList());
            outStockOrderItemMapper.updateOutStockOrderItemLocationByIds(outStockOrderPOList.get(0).getOrgId(),
                toLocationId, batchCreateByRefOrderNoDTO.getToLocationName(), collect);
        } else {
            // updateLocation(batchCreateByRefOrderNoDTO, outStockOrderPOList);
        }

        // 分组方式 1:按线路，2：按片区，3：按司机
        // if (batchCreateByRefOrderNoDTO.getGroupType() != null && batchCreateByRefOrderNoDTO.getGroupType() == 2) {
        outStockOrderPOList = batchOrderProcessBL.getOrderByIndex(outStockOrderPOList, refOrderNoList, false);
        // }

        // 检验订单异常
        batchOrderProcessBL.validateOrderException(outStockOrderPOList);

        // 给内配单原单的产品设置关联货位
        batchOrderProcessBL.addProductLocationByAllocationOrder(outStockOrderPOList);

        checkStateErrorOrders(outStockOrderPOList);

        ProcessBatchDTO processBatchDTO = new ProcessBatchDTO();
        processBatchDTO.setBatchName(batchCreateByRefOrderNoDTO.getTitle());
        processBatchDTO.setOperateUser(batchCreateByRefOrderNoDTO.getOperateUser());
        processBatchDTO.setLocationName(batchCreateByRefOrderNoDTO.getLocationName());
        processBatchDTO.setDriverName(batchCreateByRefOrderNoDTO.getDriverName());
        processBatchDTO.setAllocationFlag(batchCreateByRefOrderNoDTO.getAllocationFlag());
        processBatchDTO.setToLocationId(batchCreateByRefOrderNoDTO.getToLocationId());
        processBatchDTO.setToLocationName(batchCreateByRefOrderNoDTO.getToLocationName());
        processBatchDTO.setNotThrowException(ConditionStateEnum.是.getType());
        processBatchDTO.setOrderList(batchCreateByRefOrderNoDTO.getOrderList());
        processBatchDTO.setOrderPickFlag(batchCreateByRefOrderNoDTO.getOrderPickFlag());
        processBatchDTO.setOperateUserId(batchCreateByRefOrderNoDTO.getOperateUserId());

        // 处理配送任务
        this.handleByDeliveryTask(batchCreateByRefOrderNoDTO.getDeliveryTaskList(), outStockOrderPOList,
            processBatchDTO, wavesStrategyDTO);

        // 创建波次
        processCreateBatch(outStockOrderPOList, wavesStrategyDTO, processBatchDTO);
    }

    public void updateLocationByRefOrder(UpdateLocationDTO updateLocationDTO) {
        List<String> refOrderNoList = updateLocationDTO.getRefOrderNos();
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findOutStockOrderByRefOrderNo(refOrderNoList,
            updateLocationDTO.getWarehouseId(), updateLocationDTO.getBoundNos());

        updateLocation(outStockOrderPOList);
    }

    @Reference
    private IVirtualWarehouseManageService iVirtualWarehouseManageService;

    @Deprecated
    private void updateLocation(List<OutStockOrderPO> outStockOrderPOList) {
        // 判断尾货是否为空
        List<OutStockOrderItemPO> collect = outStockOrderPOList.stream()
            .flatMap(o -> o.getItems().stream().filter(oItem -> ObjectUtils.isEmpty(oItem.getLocationId())))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            LOG.info("都已经设置出库位：{}", JSON.toJSONString(
                outStockOrderPOList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList())));
            return;
        }

        Map<String, List<OutStockOrderPO>> boundMap =
            outStockOrderPOList.stream().collect(Collectors.groupingBy(OutStockOrderPO::getBoundNo));

        for (Map.Entry<String, List<OutStockOrderPO>> entry : boundMap.entrySet()) {
            List<OutStockOrderItemPO> locationOrderList = entry.getValue().stream().flatMap(m -> m.getItems().stream())
                .filter(m -> Objects.nonNull(m.getLocationId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(locationOrderList)) {
                LOG.info("没设置出库位：{}", JSON.toJSONString(
                    entry.getValue().stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList())));
                OutStockOrderPO outStockOrderPO = entry.getValue().get(0);
                LoactionDTO loactionDTO = null;
                if (null != outStockOrderPO.getRemark()) {
                    loactionDTO =
                        iVirtualWarehouseManageService.getLocationIdByDeliveryUserId(outStockOrderPO.getOrgId(),
                            outStockOrderPO.getWarehouseId(), String.valueOf(outStockOrderPO.getRemark()));
                }
                Long locationId = null != loactionDTO ? loactionDTO.getId() : null;
                String locationName = null != loactionDTO ? loactionDTO.getName() : "";
                if (Objects.nonNull(locationId)) {
                    outStockOrderItemMapper.updateOutStockOrderItemLocationByIds(outStockOrderPOList.get(0).getOrgId(),
                        locationId, locationName,
                        entry.getValue().stream().flatMap(o -> o.getItems().stream().map(OutStockOrderItemPO::getId))
                            .collect(Collectors.toList()));
                }
                continue;
            }

            outStockOrderItemMapper.updateOutStockOrderItemLocationByIds(outStockOrderPOList.get(0).getOrgId(),
                locationOrderList.get(0).getLocationId(), locationOrderList.get(0).getLocationName(),
                entry.getValue().stream().flatMap(o -> o.getItems().stream().map(OutStockOrderItemPO::getId))
                    .collect(Collectors.toList()));
        }
    }

    /**
     * 按产品+订单 手动新增波次（知花知果）
     */
    public void createBatchByProductAndOrder(BatchCreateByProductAndOrderDTO batchCreateByProductAndOrderDTO) {
        // 检查是否存在不拣货 + 不播种的产品
        validateProduct(batchCreateByProductAndOrderDTO.getProductList());
        LOG.info("按产品+订单新增波次{}", JSON.toJSONString(batchCreateByProductAndOrderDTO));
        // 默认开启货位通道 + (拣货方式)按产品拣货 + (拣货分组策略)类目
        WavesStrategyBO wavesStrategyDTO =
            wavesStrategyBOConvertor.getWavesStrategyDTOByString(batchCreateByProductAndOrderDTO.getWarehouseId(),
                PassagePickTypeEnum.按货位通道.getType(), (byte)WavesStrategyConstants.PICKINGTYPE_PRODUCT,
                (byte)WavesStrategyConstants.PICKINGGROUPSTRATEGY_CATEGORY,
                batchCreateByProductAndOrderDTO.getGroupType(), null);
        wavesStrategyDTO.setDeliveryCarId(batchCreateByProductAndOrderDTO.getDeliveryCarId());
        wavesStrategyDTO.setLogisticsCompanyId(batchCreateByProductAndOrderDTO.getLogisticsCompanyId());
        // 处理订单项，支持一键全选
        List<OutStockOrderPO> outStockOrderPOList =
            batchOrderProcessBL.getOrderListByItemIds(batchCreateByProductAndOrderDTO);
        // 是否根据优先级分配订单
        if (batchCreateByProductAndOrderDTO.getAllotPriority() != null) {
            outStockOrderPOList = batchOrderAllotBL.listOutStockOrderByAllotPriority(
                batchCreateByProductAndOrderDTO.getCityId(), batchCreateByProductAndOrderDTO.getWarehouseId(),
                batchCreateByProductAndOrderDTO.getAllotPriority(), outStockOrderPOList);
        }
        // 给订单项的产品添加是否拣货、是否播种属性
        addProductSkuPickAndSow(batchCreateByProductAndOrderDTO.getProductList(), outStockOrderPOList);
        // 创建波次
        ProcessBatchDTO processBatchDTO = new ProcessBatchDTO();
        processBatchDTO.setOperateUser(batchCreateByProductAndOrderDTO.getOperateUser());
        processBatchDTO.setBatchCreateType(BatchCreateTypeEnum.按产品和订单组合.getType());
        processBatchDTO.setGroupFlag(batchCreateByProductAndOrderDTO.getGroupFlag());
        processBatchDTO.setTrainFlag(batchCreateByProductAndOrderDTO.getTrainFlag());
        processBatchDTO.setTrainType(batchCreateByProductAndOrderDTO.getTrainType());
        processBatchDTO.setOperateUserId(batchCreateByProductAndOrderDTO.getOperateUserId());
        // 创建波次
        processCreateBatch(outStockOrderPOList, wavesStrategyDTO, processBatchDTO);
    }

    private void checkStateErrorOrders(List<OutStockOrderPO> outStockOrderPOList) {
        // 排除已作废、已取消状态订单
        if (!CollectionUtils.isEmpty(outStockOrderPOList)) {
            outStockOrderPOList.removeIf(p -> Objects.equals(p.getState(), (int)OutStockOrderStateEnum.已作废.getType())
                || Objects.equals(p.getState(), (int)OutStockOrderStateEnum.已取消.getType()));
        }
        List<Integer> normalStateList =
            Arrays.asList((int)OutStockOrderStateEnum.待调度.getType(), (int)OutStockOrderStateEnum.调拨中.getType());
        // 记录状态异常订单
        if (!CollectionUtils.isEmpty(outStockOrderPOList)
            && outStockOrderPOList.stream().anyMatch(p -> !normalStateList.contains(p.getState()))) {
            List<String> lstTmp = outStockOrderPOList.stream().filter(p -> !normalStateList.contains(p.getState()))
                .map(p -> String.format("%s-%s", p.getReforderno(), p.getState())).collect(Collectors.toList());
            LOG.info(String.format("生波次发现状态异常订单，单号：%s", JSON.toJSONString(lstTmp)));
        }
    }

    /**
     * 检查是否存在不拣货 + 不播种的产品
     */
    private void validateProduct(List<BatchCreateChooseProductDTO> productDTOList) {
        if (!CollectionUtils.isEmpty(productDTOList)) {
            productDTOList.forEach(p -> {
                if (ConditionStateEnum.否.getType().equals(p.getPick())
                    && ConditionStateEnum.否.getType().equals(p.getSow())) {
                    throw new BusinessValidateException("新增波次失败，不能存在不拣货且不播种的产品，请重新选择产品！");
                }
            });
        }
    }

    /**
     * 给订单项的产品添加是否拣货、是否播种属性
     */
    private void addProductSkuPickAndSow(List<BatchCreateChooseProductDTO> productDTOList,
        List<OutStockOrderPO> outStockOrderPOList) {
        if (!CollectionUtils.isEmpty(outStockOrderPOList)) {
            outStockOrderPOList.forEach(order -> {
                order.getItems().forEach(item -> {
                    List<BatchCreateChooseProductDTO> filterList = productDTOList.stream()
                        .filter(p -> Objects.equals(p.getProductSkuId(), item.getSkuid())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(filterList)) {
                        // 是否拣货
                        item.setPick(filterList.get(0).getPick());
                        // 是否播种
                        item.setSow(filterList.get(0).getSow());
                    } else {
                        // 默认不拣货、要播种
                        item.setPick(ConditionStateEnum.否.getType());
                        item.setSow(ConditionStateEnum.是.getType());
                    }
                });
            });
        }
    }

    /**
     * 检查订单是否重复生波次
     */
    public void checkOrderCreateBatchRepeat(CheckOrderRepeatDTO checkOrderRepeatDTO) {
        if (checkOrderRepeatDTO == null) {
            return;
        }
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList = orderItemTaskInfoMapper
            .listOrderRelateBatch(checkOrderRepeatDTO.getRefOrderNos(), checkOrderRepeatDTO.getWarehouseId());
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            LOG.info("[检查订单是否重复生波次]找不到波次：{}", JSON.toJSONString(checkOrderRepeatDTO));
            return;
        }
        checkOrderRepeatDTO.getRefOrderNos().forEach(orderNo -> {
            List<String> batchNoList =
                orderItemTaskInfoPOList.stream().filter(p -> Objects.equals(p.getRefOrderNo(), orderNo))
                    .map(p -> p.getBatchNo()).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(batchNoList) && batchNoList.size() > 1) {
                LOG.warn("[检查订单是否重复生波次]订单[{}]重复生成波次：{}，仓库id：{}", orderNo, JSON.toJSONString(batchNoList),
                    checkOrderRepeatDTO.getWarehouseId());
            } else {
                LOG.info("[检查订单是否重复生波次]订单[{}]正常生成波次：{}，仓库id：{}", orderNo, JSON.toJSONString(batchNoList),
                    checkOrderRepeatDTO.getWarehouseId());
            }
        });
    }

    /**
     * 设置订单来源
     */
    private void setOrderSource(List<OutStockOrderPO> outStockOrderPOList) {
        // 找出快递直发订单去查对应的订单来源
        List<OutStockOrderPO> expressOrderList = outStockOrderPOList.stream()
            .filter(p -> Objects.equals(p.getDeliveryMode(), (byte)7)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(expressOrderList)) {
            return;
        }
        expressOrderList.forEach(m -> m.setOrderSource(m.getOrderSourceType()));
        // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 -- Handled
    }

    /**
     * 设置订单目的仓库 <br />
     * TODO 这里的参数orderList未赋值
     */
    private void setOrderToWarehouse(List<OutStockOrderPO> outStockOrderPOList, List<BatchCreateOrderDTO> orderList) {
        if (CollectionUtils.isEmpty(outStockOrderPOList) || CollectionUtils.isEmpty(orderList)) {
            return;
        }
        LOG.info("设置订单目的仓库");
        outStockOrderPOList.forEach(order -> {
            Optional<BatchCreateOrderDTO> optional =
                orderList.stream().filter(p -> Objects.equals(p.getRefOrderNo(), order.getReforderno())).findFirst();
            if (optional.isPresent()) {
                // 目的仓库
                order.setToWarehouseId(optional.get().getToWarehouseId());
                order.setToWarehouseName(optional.get().getToWarehouseName());
            }
        });
    }

    /**
     * 设置团购订单
     */
    private void setGroupBuyOrder(List<OutStockOrderPO> outStockOrderPOList, WavesStrategyDTO wavesStrategyDTO,
        ProcessBatchDTO processBatchDTO) {
        // 1、获取贸易伙伴配置，判断是否是网格仓
        BatchWorkSettingDTO workSettingDTO = batchOrderBL.getBatchWorkSetting(wavesStrategyDTO.getWarehouseId());
        if (workSettingDTO == null) {
            return;
        }
        LOG.info("获取贸易伙伴配置：{}", JSON.toJSONString(workSettingDTO));
        AssertUtils.notNull(workSettingDTO.getSowType(), "贸易伙伴的播种方式不能为空");
        AssertUtils.notNull(workSettingDTO.getLargeLocationId(), "贸易伙伴的整件收货位Id不能为空");
        AssertUtils.notNull(workSettingDTO.getLargeLocationName(), "贸易伙伴的整件收货位不能为空");
        AssertUtils.notNull(workSettingDTO.getSmallLocationId(), "贸易伙伴的拆零收货位Id不能为空");
        AssertUtils.notNull(workSettingDTO.getSmallLocationName(), "贸易伙伴的拆零收货位不能为空");
        AssertUtils.notNull(workSettingDTO.getSowLocationId(), "贸易伙伴的播种库区Id不能为空");
        AssertUtils.notNull(workSettingDTO.getSowLocationName(), "贸易伙伴的播种库区不能为空");
        AssertUtils.notNull(workSettingDTO.getDeadline(), "贸易伙伴的作业截止时间不能为空");

        // 2、设置拣货参数
        processBatchDTO.setWorkSetting(workSettingDTO);
        // 按线路或片区拆分波次
        processBatchDTO.setGroupFlag(ConditionStateEnum.是.getType());
        // 按线路
        wavesStrategyDTO.setOrderSelection((byte)WavesStrategyConstants.ORDERSELECTION_WAY);
        // 按产品拣货
        wavesStrategyDTO.setPickingType((byte)WavesStrategyConstants.PICKINGTYPE_PRODUCT);
        // 拣货分组策略(货位)
        wavesStrategyDTO.setPickingGroupStrategy((byte)WavesStrategyConstants.PICKINGGROUPSTRATEGY_LOCATION);

        // 3、设置产品特征大小件
        if (!CollectionUtils.isEmpty(outStockOrderPOList)) {
            // 获取产品大小件
            List<OutStockOrderItemPO> orderItemPOList =
                outStockOrderPOList.stream().filter(p -> p != null && !CollectionUtils.isEmpty(p.getItems()))
                    .flatMap(p -> p.getItems().stream()).collect(Collectors.toList());
            List<Long> skuIdList =
                orderItemPOList.stream().map(p -> p.getSkuid()).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(skuIdList)) {
                List<ProductSkuDTO> productSkuDTOList =
                    iProductSkuQueryService.findSkuInfoWithConfig(wavesStrategyDTO.getWarehouseId(), skuIdList);
                if (!CollectionUtils.isEmpty(productSkuDTOList)) {
                    productSkuDTOList.forEach(sku -> {
                        if (sku == null || sku.getProductFeature() == null) {
                            return;
                        }
                        orderItemPOList.stream().filter(p -> Objects.equals(p.getSkuid(), sku.getProductSkuId()))
                            .forEach(p -> {
                                p.setProductFeature(sku.getProductFeature());
                            });
                    });
                }
            }

            // 验证产品是否配置大小件
            List<String> errorProductList = orderItemPOList.stream().filter(p -> p.getProductFeature() == null)
                .map(p -> String.format("[%s]%s", p.getSkuid(), p.getProductname())).distinct()
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(errorProductList)) {
                LOG.info("以下产品没有配置产品特征大小件：{}", JSON.toJSONString(errorProductList));
            }
        }
    }

    /**
     * 通过创建波次请求，创建出库批次
     */
    private void handleByDeliveryTask(List<DeliveryTaskDTO> deliveryTaskList, List<OutStockOrderPO> outOrderList,
        ProcessBatchDTO processBatchDTO, WavesStrategyDTO wavesStrategyDTO) {
        if (CollectionUtils.isEmpty(deliveryTaskList)) {
            return;
        }
        if (CollectionUtils.isEmpty(outOrderList)) {
            LOG.info("[通过波次创建出库批次]出库单列表为空");
            return;
        }
        Integer orgId = outOrderList.get(0).getOrgId();
        Integer warehouseId = outOrderList.get(0).getWarehouseId();

        // 未开启二次分拣时，注意：这里决定不了已经开启酒批二次分拣，因为通道也要为二次分拣，但能决定没有开启二次分拣
        if (deliveryTaskList.size() == 1 || !globalCache.isOpenSecondSortFromCache(warehouseId)) {
            if (processBatchDTO.getBatchName() == null) {
                processBatchDTO.setBatchName(batchTitleConvertor.getBatchTitleByDeliveryTask(deliveryTaskList.get(0)));
                LOG.info("title生成结果：{}", processBatchDTO.getBatchName());
            }

            LOG.info("仓库未开启二次分拣，仓库：{}，车次数量：{}", warehouseId, deliveryTaskList.size());
            return;
        }
        wavesStrategyDTO.setIsOpenSecondSort(true);
        // 目前，只有酒饮二次分拣，才支持多车次创建波次
        wavesStrategyDTO.setIsMultiOutBound(true);

        List<OutBoundBatchDTO> boundAddList = new ArrayList<>();
        List<SecondPickLocationDTO> batchLocationList = new ArrayList<>();
        Map<Long, DeliveryTaskDTO> orderDeliveryTaskMap = new HashMap<>();
        for (DeliveryTaskDTO deliveryTask : deliveryTaskList) {
            if (CollectionUtils.isEmpty(deliveryTask.getOrderNoList())) {
                LOG.info("[多出库批次创建波次]无出库单号，出库批次：{}，出库单号：{}", deliveryTask.getDeliveryTaskNo(),
                    deliveryTask.getOrderNoList());
                continue;
            }
            List<OutStockOrderPO> curOrderList =
                outOrderList.stream().filter(ord -> deliveryTask.getOrderNoList().contains(ord.getReforderno()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(curOrderList)) {
                LOG.info("[多出库批次创建波次]无出库单，出库批次：{}，出库单号：{}", deliveryTask.getDeliveryTaskNo(),
                    deliveryTask.getOrderNoList());
                continue;
            }
            curOrderList.forEach(m -> {
                orderDeliveryTaskMap.put(m.getId(), deliveryTask);
            });
            OutBoundBatchDTO outBound = new OutBoundBatchDTO();
            outBound.setOrderId(curOrderList.stream().map(OutStockOrderPO::getId).collect(Collectors.toList()));

            outBound.setOrgId(orgId);
            outBound.setWarehouseId(warehouseId);
            outBound.setRelatedBatchId(
                !StringUtils.isNumeric(deliveryTask.getTaskId()) ? null : Long.valueOf(deliveryTask.getTaskId()));
            outBound.setRelatedBatchNo(deliveryTask.getDeliveryTaskNo());
            outBound.setPickUpUserName(deliveryTask.getDeliveryUserName());

            // 未设置出库位时
            if (curOrderList.stream().flatMap(ord -> ord.getItems().stream())
                .anyMatch(item -> item.getLocationId() == null)) {
                CarDTO carInfo = iCarService.findOne(deliveryTask.getDeliveryCarId());
                LOG.info("[多出库批次创建波次]出库位为空，车辆信息：{}，车辆ID：{}", JSON.toJSONString(carInfo),
                    deliveryTask.getDeliveryCarId());
                if (carInfo != null) {
                    SecondPickLocationDTO secondPickLocationDTO = new SecondPickLocationDTO();
                    secondPickLocationDTO.setLocationId(carInfo.getDefaultDeliveryLocation_Id());
                    secondPickLocationDTO.setLocationName(carInfo.getDefaultDeliveryLocation());
                    secondPickLocationDTO.setRefOrderNoList(
                        curOrderList.stream().map(OutStockOrderPO::getReforderno).collect(Collectors.toList()));
                    batchLocationList.add(secondPickLocationDTO);
                }
            }

            outBound.setCarInfo(deliveryTask.getDeliveryCarName());
            outBound.setCreateUser(processBatchDTO.getOperateUser());

            Set<Integer> warehouseAllocationTypeSet =
                    curOrderList.stream()
                            .map(OutStockOrderPO::getWarehouseAllocationType)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            if (warehouseAllocationTypeSet.size() == 1) {
                outBound.setWarehouseAllocationType(warehouseAllocationTypeSet.iterator().next());
            }
            for (OutStockOrderPO outStockOrderPO : curOrderList) {
                if (outStockOrderPO.getWarehouseAllocationType() == null) {
                    outBound.setWarehouseAllocationType(null);
                    break;
                }
            }

            boundAddList.add(outBound);
        }
        LOG.info("[多出库批次创建波次]新增出库批次，请求：{}", JSON.toJSONString(boundAddList));
        Map<String, List<Long>> outBoundStockOrderMap = iOutBoundBatchManageService.batchAddUpdate(boundAddList);
        if (!CollectionUtils.isEmpty(outBoundStockOrderMap)) {
            Map<Long, String> stockOrderOutBoundMap = new HashMap<>();
            for (Map.Entry<String, List<Long>> entry : outBoundStockOrderMap.entrySet()) {
                entry.getValue().forEach(m -> stockOrderOutBoundMap.put(m, entry.getKey()));
            }
            outOrderList.forEach(m -> {
                m.setBoundNo(stockOrderOutBoundMap.get(m.getId()));
                DeliveryTaskDTO deliveryTaskDTO = orderDeliveryTaskMap.get(m.getId());
                // 总单二次分拣拼拣货任务名字用
                if (Objects.nonNull(deliveryTaskDTO)) {
                    // 只能先加这里，污染PO对象 . 因为生成拣货任务的时候 车次信息已经丢失，总单二次分拣无法再找到出库位.放在orderItem中，这个对象里的locationId意义不停的在变，不停的再赋值
                    // 设置出库位的时候，赌一把，orderItem数据库中已经有值
                    m.setPickUpUserName(NullUtils.getDefaultStr(deliveryTaskDTO.getDeliveryUserName()) + "-"
                        + NullUtils.getDefaultStr(deliveryTaskDTO.getDeliveryCarName()));
                }
            });
        }

        if (!CollectionUtils.isEmpty(batchLocationList)) {
            SecondPickLocationSyncDTO locationSyncDTO = new SecondPickLocationSyncDTO();
            locationSyncDTO.setOrgId(orgId);
            locationSyncDTO.setWarehouseId(warehouseId);
            locationSyncDTO.setLocationList(batchLocationList);
            LOG.info("[多出库批次创建波次]无初始出库位置，出库位再同步：{}", JSON.toJSONString(locationSyncDTO));
            secondSortBl.secondPickLocationSync(locationSyncDTO);
        }
    }

}
