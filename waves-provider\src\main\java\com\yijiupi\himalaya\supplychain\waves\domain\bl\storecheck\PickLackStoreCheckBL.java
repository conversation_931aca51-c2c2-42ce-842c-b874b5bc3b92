package com.yijiupi.himalaya.supplychain.waves.domain.bl.storecheck;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.baseutil.NullUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.instockorder.enums.YesOrNoEnum;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryQueryService;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckParam;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.CategoryPeriodConfigTypeEnum;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.OutStockAlarmEnum;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IInStockConfigService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.storecheck.domain.dto.StartStoreCheckDTO;
import com.yijiupi.himalaya.supplychain.storecheck.domain.dto.StoreCheckDTO;
import com.yijiupi.himalaya.supplychain.storecheck.domain.dto.StoreCheckItemDTO;
import com.yijiupi.himalaya.supplychain.storecheck.domain.dto.StoreCheckItemDetailsDTO;
import com.yijiupi.himalaya.supplychain.storecheck.domain.enums.StoreCheckTypeEnum;
import com.yijiupi.himalaya.supplychain.storecheck.domain.so.StoreCheckSO;
import com.yijiupi.himalaya.supplychain.storecheck.service.StoreCheckOrderService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.LocationAreaService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskQueryBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.variable.VariableManager;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.util.Pair;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import static com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum.*;

/**
 * <AUTHOR>
 * @since 2024-08-27 14:32
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class PickLackStoreCheckBL {

    @Resource
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    @Resource
    private OutStockOrderItemMapper outStockOrderItemMapper;

    @Resource
    private GlobalCache globalCache;

    @Resource
    private VariableManager variableManager;

    @Resource
    private BatchTaskQueryBL batchTaskQueryBL;

    @Resource
    private BatchTaskItemMapper batchTaskItemMapper;

    @Reference
    private StoreCheckOrderService storeCheckOrderService;

    @Reference
    private IProductSkuService productSkuService;

    @Reference
    private IWarehouseInventoryQueryService warehouseInventoryQueryService;

    @Reference
    private IBatchInventoryQueryService batchInventoryQueryService;

    @Reference
    private LocationAreaService locationAreaService;

    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Reference
    private IBatchInventoryQueryService iBatchInventoryQueryService;

    @Reference
    private IInStockConfigService inStockConfigService;

    private static final String STORE_CHECK_REMARK = "拣货缺货自动盘点单";

    private static final String STORE_CHECK_NOTE_NAME = "缺货盘点单";

    private static final String LACK_STORE_CHECK = "LACK_STORE_CHECK";

    private static final Logger logger = LoggerFactory.getLogger(PickLackStoreCheckBL.class);

    /**
     * 拣货缺货生成盘亏单
     *
     * @param orders 缺货的订单项
     * @param opUserId 操作人 id
     */
    @Async("waveTaskExecutor")
    public void generateLossOrder(List<OutStockOrderPO> orders, Integer opUserId) {
        logger.info("拣货缺货生成盘点单: {}, 操作人: {}", JSON.toJSONString(orders), opUserId);
        // orders 里字段不全
        Integer warehouseId = orders.get(0).getWarehouseId();
        if (!variableManager.checkBooleanKey(LACK_STORE_CHECK, warehouseId)) {
            logger.info("没开配置, 跳过盘点单创建");
            return;
        }
        // 获取无需生成盘点的skuid
        List<Long> noStoreCheckSkuIds = getNoStoreCheckSkuIds(orders);
        List<LackInfo> lackInfoList = queryLackInfo(orders).stream().filter(it -> !noStoreCheckSkuIds.contains(it.skuId)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(lackInfoList)){
            logger.info("拣货缺货产品禁止出库，无需生成盘亏单");
            return;
        }
        Map<Long, LackInfo> lackInfoMap = lackInfoList.stream()
                .collect(Collectors.toMap(LackInfo::getOrderItemId, Function.identity()));
        Map<Long, Integer> itemUserMap = queryItemUserMap(lackInfoMap.keySet());
        Map<Integer, List<LackInfo>> userLackMap = lackInfoMap.entrySet().stream()
                .map(it -> Pair.of(it.getValue(), itemUserMap.get(it.getKey())))
                .collect(Collectors.groupingBy(Pair::getSecond, Collectors.mapping(Pair::getFirst, Collectors.toList())));
        // 按提交人生成盘点单
        for (Map.Entry<Integer, List<LackInfo>> entry : userLackMap.entrySet()) {
            Integer userId = entry.getKey();
            List<LackInfo> lackInfos = entry.getValue();
            Integer orgId = lackInfos.get(0).orgId;
            List<Long> lackSkuIds = lackInfos.stream().map(it -> it.skuId).distinct().collect(Collectors.toList());
            // 生成盘点单参数, 使用 batchTaskItem 上的操作人 id
            StoreCheckDTO storeCheckDTO = newStoreCheck(orders, lackInfos, userId);
            if (storeCheckDTO == null) {
                logger.info("拣货缺货生成盘点单, 盘点数据为空, 跳过后续处理");
                continue;
            }
            logger.info("拣货缺货生成盘点单, 入参: {}", JSON.toJSONString(storeCheckDTO));
            // 保存盘点单
            String storeCheckId = storeCheckOrderService.saveStoreCheck(storeCheckDTO);
            if (storeCheckOrderService.hasUnAuditCheckResult(orgId, warehouseId, lackSkuIds)) {
                logger.info("存在未审核的盘点单, 本次自动生成的盘点单将不会自动提交");
                continue;
            }
            // 提交盘点单
            StoreCheckDTO submitCheck = newSubmitCheck(queryStoreCheckInfo(storeCheckDTO, storeCheckId), lackInfos);
            if (submitCheck.getItems().stream().anyMatch(Objects::isNull)) {
                logger.info("存在未盘点的项, 需要用户手动盘点，不自动提交");
                continue;
            }
            // 开始盘点
            storeCheckOrderService.startStoreCheck(newStartCheck(storeCheckId));
            logger.info("提交拣货缺货生成的盘点单, 入参: {}", JSON.toJSONString(submitCheck));
            storeCheckOrderService.submitStoreCheck(submitCheck);
        }
    }

    /**
     * 查询拣货任务项 id 与 完成拣货的拣货人 id 关系<br/>
     * <b>只查了有缺货的项</b>
     *
     * @param orderItemIds 订单项 id
     * @return key 是 订单项 id, value 是 完成拣货的完成人的 map
     */
    private Map<Long, Integer> queryItemUserMap(Collection<Long> orderItemIds) {
        Map<Long, String> orderItemTaskMap = orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderItemIds(orderItemIds).stream()
                .filter(it -> it.getLackUnitCount().compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.toMap(OrderItemTaskInfoPO::getRefOrderItemId, OrderItemTaskInfoPO::getBatchTaskItemId, (a, b) -> a));
        Collection<String> batchTaskItemIds = orderItemTaskMap.values();
        logger.info("查询拣货任务详情, 入参: {}", batchTaskItemIds);
        List<BatchTaskItemPO> taskItems = batchTaskItemMapper.findBatchTaskItemByIds(batchTaskItemIds);
        logger.info("查询拣货任务详情, 结果: {}", JSON.toJSONString(taskItems, SerializerFeature.WriteMapNullValue));
        Map<String, Integer> taskCompleteUserMap = Optional.ofNullable(taskItems.stream().filter(p -> p.getCompleteUserId() != null)
                .collect(Collectors.toMap(BatchTaskItemPO::getId, BatchTaskItemPO::getCompleteUserId))).orElse(Collections.emptyMap());
        return orderItemTaskMap.entrySet().stream().filter(it -> taskCompleteUserMap.containsKey(it.getValue()))
                .map(it -> Pair.of(it.getKey(), taskCompleteUserMap.get(it.getValue())))
                .collect(Collectors.toMap(Pair::getFirst, Pair::getSecond));
    }

    /**
     * 拼接缺货盘点单
     */
    @Nullable
    private StoreCheckDTO newStoreCheck(List<OutStockOrderPO> orders, List<LackInfo> lackInfos, Integer opUserId) {
        OutStockOrderPO order = orders.get(0);
        Integer warehouseId = order.getWarehouseId();
        String userName = globalCache.getUserName(opUserId);
        StoreCheckDTO storeCheckDTO = new StoreCheckDTO();
        storeCheckDTO.setCreateUser(userName);
        storeCheckDTO.setNoteName(STORE_CHECK_NOTE_NAME);
        storeCheckDTO.setWarehouseId(warehouseId);
        storeCheckDTO.setOrgId(lackInfos.get(0).orgId);
        storeCheckDTO.setNoteType(StoreCheckTypeEnum.普通盘点.getType());
        storeCheckDTO.setTakeTime(new Date());
        storeCheckDTO.setOperatorUserName(userName);
        storeCheckDTO.setOperatorUserId(opUserId);
        storeCheckDTO.setRemark(STORE_CHECK_REMARK);
        storeCheckDTO.setCompanyCode("YJP");
        storeCheckDTO.setTakeTime(new Date());
        List<StoreCheckItemDTO> items =
            lackInfos.stream().map(this::newItem).filter(Objects::nonNull).collect(Collectors.toList());
        if (items.isEmpty()) {
            return null;
        }
        storeCheckDTO.setAddItems(items);
        return storeCheckDTO;
    }

    /**
     * 生成盘点单项
     *
     * @param lackInfo 缺货信息
     * @return 盘点单项
     */
    @Nullable
    private StoreCheckItemDTO newItem(LackInfo lackInfo) {
        // 库存小于等于 0 不会盘
        BigDecimal warehouseInventory = lackInfo.inventory.storeTotalCount;
        if (warehouseInventory.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        BigDecimal finalInventory =
            warehouseInventory.subtract(lackInfo.pickOccupyCount).subtract(lackInfo.defectiveStoreCount);
        if (finalInventory.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        // 库存减去缺货数量后小于 0 不会盘
        if (finalInventory.subtract(lackInfo.lackUnitTotalCount).compareTo(BigDecimal.ZERO) < 0) {
            return null;
        }
        StoreCheckItemDTO dto = new StoreCheckItemDTO();
        dto.setProductSkuId(lackInfo.skuId);
        return dto;
    }

    /**
     * 开始盘点
     *
     * @param id 盘点单 id
     */
    private StartStoreCheckDTO newStartCheck(String id) {
        StartStoreCheckDTO startStoreCheckDTO = new StartStoreCheckDTO();
        startStoreCheckDTO.setId(id);
        return startStoreCheckDTO;
    }

    /**
     * 构造一个提交盘点单的参数
     *
     * @param lackInfos 缺货信息
     * @param storeCheck 盘点单信息
     * @return 盘点单提交参数
     */
    private StoreCheckDTO newSubmitCheck(StoreCheckDTO storeCheck, List<LackInfo> lackInfos) {
        String userId = storeCheck.getOperatorUserId().toString();
        StoreCheckDTO submitDTO = new StoreCheckDTO();
        // 盘点单id
        submitDTO.setId(storeCheck.getId());
        // 城市id
        submitDTO.setOrgId(storeCheck.getOrgId());
        // 盘点人员
        submitDTO.setOperatorUserName(storeCheck.getOperatorUserName());
        // 备注
        submitDTO.setRemark(storeCheck.getRemark());
        // 最后修改人
        submitDTO.setLastUpdateUser(userId);
        // 明细项
        Map<Long, LackInfo> lackMap = lackInfos.stream().collect(Collectors.toMap(it -> it.skuId, Function.identity()));
        List<StoreCheckItemDTO> items =
            storeCheck.getItems().stream().map(it -> newCheckItem(it, lackMap, userId)).collect(Collectors.toList());
        submitDTO.setItems(items);
        return submitDTO;
    }

    /**
     * 构造盘点结果明细
     *
     * @param item 盘点单明细
     * @param lackMap sku 缺货信息
     */
    private StoreCheckItemDTO newCheckItem(StoreCheckItemDTO item, Map<Long, LackInfo> lackMap, String userId) {
        LackInfo lackInfo = lackMap.get(item.getProductSkuId());
        // 2025-01-21 没有缺货的产品，不需要处理
        if (lackInfo == null) {
            return null;
        }
        BigDecimal[] lackCount = lackInfo.lackUnitTotalCount.divideAndRemainder(item.getSpecQuantity());
        // 仓库库存减去缺货数量得到盘亏后的实际录入数量
        BigDecimal warehouseInventory = lackInfo.inventory.storeTotalCount.subtract(lackInfo.lackUnitTotalCount);
        BigDecimal[] inventories = warehouseInventory.divideAndRemainder(item.getSpecQuantity());
        // 录入数量(大)
        item.setPackageCount(inventories[0]);
        // 录入数量(小)
        item.setUnitCount(inventories[1]);
        // 录入小单位总数量
        item.setUnitTotalCount(warehouseInventory);
        // 是否有差异
        item.setDiff(YesOrNoEnum.YES.getValue().byteValue());
        // 差异大数量, 负数为盘亏
        item.setDiffPackageCount(lackCount[0].negate());
        // 差异小数量, 负数为盘亏
        item.setDiffUnitCount(lackCount[1].negate());
        // 差异小数量总计, 负数为盘亏
        item.setDiffTotalCount(lackInfo.lackUnitTotalCount.negate());
        // 最后修改人
        item.setLastUpdateUser(userId);
        // 备注
        item.setRemark(STORE_CHECK_REMARK);
        // 用批次库存数据填充 detail
        List<BatchInventoryDTO> batchInventories = lackInfo.batchInventories;
        logger.info("准备构造盘点单 detail, 批次库存数据为: {}", JSON.toJSONString(batchInventories));
        List<StoreCheckItemDetailsDTO> details =
            batchInventories.stream().map(it -> newCheckDetail(it, lackInfo, item)).collect(Collectors.toList());
        item.setDetails(details);
        return item;
    }

    private StoreCheckItemDetailsDTO newCheckDetail(BatchInventoryDTO bi, LackInfo lackInfo, StoreCheckItemDTO item) {
        StoreCheckItemDetailsDTO detail = new StoreCheckItemDetailsDTO();
        detail.setOrgId(lackInfo.orgId);
        detail.setNoteItemId(item.getId());
        detail.setProductionDate(bi.getProductionDate());
        detail.setBatchTime(bi.getBatchTime());
        detail.setProductSkuId(bi.getProductSkuId());
        detail.setProductSpecificationId(bi.getProductSpecificationId());
        BigDecimal storeTotalCount = bi.getStoreTotalCount();
        // 如果批次库存比缺货数量大, 那就直接用批次库存减去缺货数量, 并将缺货数量置为 0
        if (storeTotalCount.compareTo(lackInfo.lackUnitTotalCount) >= 0) {
            storeTotalCount = storeTotalCount.subtract(lackInfo.lackUnitTotalCount);
            lackInfo.lackUnitTotalCount = BigDecimal.ZERO;
        } else if (lackInfo.lackUnitTotalCount.compareTo(BigDecimal.ZERO) != 0) {
            // 否则用缺货数量减去批次库存, 得到分摊后的缺货数量, 并将本批次库存数量改成 0
            lackInfo.subtract(storeTotalCount);
            storeTotalCount = BigDecimal.ZERO;
        }
        detail.setUnitTotalCount(storeTotalCount);
        detail.setLocationId(bi.getLocationId());
        detail.setLocationName(bi.getLocationName());
        detail.setChannel(bi.getChannel().byteValue());
        detail.setSource(bi.getSource().byteValue());
        detail.setRemark(STORE_CHECK_REMARK);
        detail.setCreateTime(new Date());
        detail.setUnitCount(null);
        detail.setPackageCount(null);
        Optional.ofNullable(detail.getProductionDate()).map(DateUtils::getDateFormat)
            .ifPresent(detail::setProductionDateStr);
        detail.setRefNoteItemId(null);
        detail.setRefNoteItemDetailId(null);
        detail.setOwnerId(null);
        detail.setSecOwnerId(null);
        // 提交为0数据 0 : 提交时不为 0 1 : 提交时为 0
        detail.setSubmitZero((byte)detail.getUnitTotalCount().compareTo(BigDecimal.ZERO));
        return detail;
    }

    /**
     * 查询订单项缺货信息
     *
     * @param orders 订单数据
     * @return 订单项缺货数据
     */
    private List<LackInfo> queryLackInfo(List<OutStockOrderPO> orders) {
        OutStockOrderPO order = orders.get(0);
        Integer warehouseId = order.getWarehouseId();
        Set<Long> lackOrderIds = orders.stream().map(OutStockOrderPO::getId).collect(Collectors.toSet());
        List<OutStockOrderItemPO> itemList = outStockOrderItemMapper.findByOutstockorderIdList(lackOrderIds);
        Map<Long, OutStockOrderItemPO> itemMap =
            itemList.stream().collect(Collectors.toMap(OutStockOrderItemPO::getId, Function.identity()));
        Integer orgId = itemList.get(0).getOrgId();
        List<Long> skuIds = itemMap.values().stream().map(OutStockOrderItemPO::getSkuid).collect(Collectors.toList());
        Map<Long, Map<Long, List<BatchInventoryDTO>>> batchInventoryMap = queryWarehouseInventory(warehouseId, skuIds);
        Map<Long, DiskAdjustmentVO> inventoryMap = findDiskAdjustment(orgId, warehouseId, skuIds);

        // 分拣占用
        Map<Long, BigDecimal> pickOccupyCount = findPickedCountBySkuIdForSCM25(orgId, warehouseId, skuIds);
        // 残次品数量
        Map<Long, BigDecimal> defectiveStoreMap = findDefectiveBatchInventoryList(skuIds, warehouseId);

        List<String> locationIds = batchInventoryMap.values().stream().map(Map::keySet).flatMap(Collection::stream)
            .distinct().map(String::valueOf).collect(Collectors.toList());
        Map<Long, LocationReturnDTO> locationMap = locationAreaService.findLocationListById(locationIds).stream()
            .collect(Collectors.toMap(LocationReturnDTO::getId, Function.identity()));
        // 仓库是否开启货位库存
        Boolean isOpenStock = warehouseConfigService.isOpenLocationStock(warehouseId);
        return orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(lackOrderIds).stream()
            .filter(it -> it.getLackUnitCount().compareTo(BigDecimal.ZERO) != 0)
            .collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderItemId, sumLackCount())).entrySet().stream()
            .map(it -> LackInfo.of(itemMap.get(it.getKey()), it.getValue(), inventoryMap, batchInventoryMap,
                locationMap, isOpenStock, pickOccupyCount, defectiveStoreMap))
            .collect(Collectors.groupingBy(it -> it.skuId)).values().stream()
            .map(it -> it.stream().reduce(LackInfo::reduce)).filter(Optional::isPresent).map(Optional::get)
            .collect(Collectors.toList());
    }

    /**
     * 查询批次库存
     *
     * @param warehouseId 仓库 id
     * @param skuIds skuId
     * @return key 为 skuId, value 为库存信息的 map
     */
    private Map<Long, Map<Long, List<BatchInventoryDTO>>> queryWarehouseInventory(Integer warehouseId,
        Collection<Long> skuIds) {
        BatchInventoryQueryDTO query = new BatchInventoryQueryDTO();
        query.setSkuIds(new ArrayList<>(skuIds));
        query.setWarehouseId(warehouseId);
        // 不查询 0 库存
        query.setShowAll(null);
        List<BatchInventoryDTO> result = batchInventoryQueryService.findBatchInventoryList(query).getDataList();
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyMap();
        }
        return result.stream().collect(Collectors.groupingBy(BatchInventoryDTO::getProductSkuId, groupByLocationId()));
    }

    /**
     * 查询已保存的盘点单信息
     *
     * @param dto 盘点单主表信息
     * @param id 盘点单 id
     * @return 带明细的盘点单信息
     */
    private StoreCheckDTO queryStoreCheckInfo(StoreCheckDTO dto, String id) {
        StoreCheckSO so = new StoreCheckSO();
        so.setNoteId(id);
        so.setPageNum(1);
        so.setPageSize(Integer.MAX_VALUE);
        logger.info("查询盘点单明细: {}", JSON.toJSONString(so));
        PageList<StoreCheckItemDTO> items = storeCheckOrderService.listStoreCheckItem(so);
        logger.info("查询盘点单明细返回: {}", JSON.toJSONString(items, SerializerFeature.WriteMapNullValue));
        dto.setId(id);
        dto.setItems(items.getDataList());
        return dto;
    }

    /**
     * 统计 orderItemTaskInfo 缺货数量
     *
     * @return 缺货数量
     */
    private Collector<OrderItemTaskInfoPO, ?, BigDecimal> sumLackCount() {
        return Collectors.mapping(OrderItemTaskInfoPO::getLackUnitCount,
            Collectors.reducing(BigDecimal.ZERO, BigDecimal::add));
    }

    /**
     * 按照货位 id 分组
     */
    private Collector<BatchInventoryDTO, ?, Map<Long, List<BatchInventoryDTO>>> groupByLocationId() {
        return Collectors.groupingBy(it -> NullUtils.getLong(it.getLocationId()));
    }

    /**
     * 动盘调整查询
     */
    private Map<Long, DiskAdjustmentVO> findDiskAdjustment(Integer cityId, Integer warehouseId, List<Long> skuIds) {
        Map<Long, ProductSkuInfoReturnDTO> mapProduct = productSkuService.getProductInfoBySkuId(skuIds);
        List<Long> existsProductIds = new ArrayList<>();
        for (Map.Entry<Long, ProductSkuInfoReturnDTO> entry : mapProduct.entrySet()) {
            if (entry.getValue() != null) {
                existsProductIds.add(entry.getKey());
            }
        }
        Map<Long, BigDecimal> mapJiupiInventory = warehouseInventoryQueryService
            .getWarehouseInventoryList(existsProductIds, warehouseId, ProductChannelType.JIUPI, null);
        Map<Long, BigDecimal> mapLargeInventory = warehouseInventoryQueryService
            .getWarehouseInventoryList(existsProductIds, warehouseId, ProductChannelType.LARGE, null);
        // 获取产品关联的合并产品的库存总和
        Map<Long, BigDecimal> refInventory =
            warehouseInventoryQueryService.getRefProductInventoryMap(cityId, warehouseId, existsProductIds);
        List<DiskAdjustmentVO> list = new ArrayList<>();
        for (Long productSkuId : existsProductIds) {
            if (mapJiupiInventory.get(productSkuId) != null && mapProduct.get(productSkuId) != null
                && mapProduct.get(productSkuId).getPackageQuantity() != null) {
                BigDecimal integerLarge = mapLargeInventory.get(productSkuId);
                BigDecimal storeTotalCount =
                    mapJiupiInventory.get(productSkuId).add(integerLarge == null ? BigDecimal.ZERO : integerLarge);
                // 若开启了合并产品盘点，则需要把关联的产品库存合并起来
                if (refInventory != null && !refInventory.isEmpty() && refInventory.containsKey(productSkuId)) {
                    storeTotalCount = storeTotalCount.add(refInventory.get(productSkuId));
                }
                list.add(DiskAdjustmentVO.of(productSkuId, storeTotalCount));
            }
        }
        return list.stream().collect(Collectors.toMap(it -> it.productSkuId, Function.identity()));
    }

    private Map<Long, BigDecimal> findPickedCountBySkuIdForSCM25(Integer cityId, Integer warehouseId, List<Long> skuIds) {
        return batchTaskQueryBL.findPickedCountBySkuIdForSCM25(cityId, warehouseId, skuIds, Boolean.FALSE);
    }

    private Map<Long, BigDecimal> findDefectiveBatchInventoryList(List<Long> skuIdList, Integer warehouseId) {
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setSkuIds(skuIdList);
        batchInventoryQueryDTO.setWarehouseId(warehouseId);

        List<Integer> subCategoryList = new ArrayList<>();
        subCategoryList.add(LocationEnum.残次品位.getType());
        subCategoryList.add(LocationAreaEnum.残次品区.getType());
        batchInventoryQueryDTO.setSubCategoryList(subCategoryList);
        List<BatchInventoryDTO> defectiveBatchStoreList =
            iBatchInventoryQueryService.findBatchInventoryList(batchInventoryQueryDTO).getDataList();

        if (CollectionUtils.isEmpty(defectiveBatchStoreList)) {
            return Collections.emptyMap();
        }
        // 残次品数量
        return defectiveBatchStoreList.stream()
            .collect(Collectors.groupingBy(BatchInventoryDTO::getProductSkuId, Collectors.mapping(
                BatchInventoryDTO::getStoreTotalCount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }

    private static class LackInfo {
        /**
         * 城市 id
         */
        private final Integer orgId;
        /**
         * skuId
         */
        private final Long skuId;
        /**
         * 出库单项 id
         */
        private final Long orderItemId;
        /**
         * 缺货小数量
         */
        private BigDecimal lackUnitTotalCount;

        /**
         * 仓库库存
         */
        private final DiskAdjustmentVO inventory;

        /**
         * 批次库存数据
         */
        private final List<BatchInventoryDTO> batchInventories;

        /**
         * 货位 id -> 货位信息 map
         */
        private final Map<Long, LocationReturnDTO> locationMap;
        /**
         * 分拣占用数量
         */

        private final BigDecimal pickOccupyCount;
        /**
         * 残次品数量
         */
        private final BigDecimal defectiveStoreCount;

        private LackInfo(Integer orgId, Long skuId, Long orderItemId, BigDecimal lackUnitTotalCount, DiskAdjustmentVO inventory,
                         Map<Long, List<BatchInventoryDTO>> batchInventoryMap, Map<Long, LocationReturnDTO> locationMap,
                         Boolean isOpenStock, BigDecimal pickOccupyCount, BigDecimal defectiveStoreCount) {
            this.orgId = orgId;
            this.skuId = skuId;
            this.orderItemId = orderItemId;
            this.lackUnitTotalCount = lackUnitTotalCount;
            this.inventory = inventory;
            this.locationMap = locationMap;
            if (batchInventoryMap == null) {
                logger.info("批次库存信息为空");
                batchInventoryMap = Collections.emptyMap();
            }
            this.batchInventories = batchInventoryMap.entrySet().stream().sorted(sortLocation(this, isOpenStock))
                .map(Map.Entry::getValue).flatMap(Collection::stream).collect(Collectors.toList());
            this.pickOccupyCount = pickOccupyCount;
            this.defectiveStoreCount = defectiveStoreCount;
        }

        private LackInfo(Integer orgId, Long skuId, Long orderItemId, BigDecimal lackUnitTotalCount, DiskAdjustmentVO inventory,
                         Map<Long, LocationReturnDTO> locationMap, List<BatchInventoryDTO> batchInventories,
                         BigDecimal pickOccupyCount, BigDecimal defectiveStoreCount) {
            this.orgId = orgId;
            this.skuId = skuId;
            this.orderItemId = orderItemId;
            this.lackUnitTotalCount = lackUnitTotalCount;
            this.inventory = inventory;
            this.locationMap = locationMap;
            this.batchInventories = batchInventories;
            this.pickOccupyCount = pickOccupyCount;
            this.defectiveStoreCount = defectiveStoreCount;
        }

        public void subtract(BigDecimal lackUnitTotalCount) {
            this.lackUnitTotalCount = lackUnitTotalCount.subtract(lackUnitTotalCount);
        }

        public static LackInfo of(OutStockOrderItemPO item, BigDecimal lackUnitTotalCount,
            Map<Long, DiskAdjustmentVO> inventoryMap, Map<Long, Map<Long, List<BatchInventoryDTO>>> batchInventoryMap,
            Map<Long, LocationReturnDTO> locationMap, Boolean isOpenStock, Map<Long, BigDecimal> pickOccupyCountMap,
            Map<Long, BigDecimal> defectiveStoreMap) {
            Integer orgId = item.getOrgId();
            Long skuId = item.getSkuid();
            Map<Long, List<BatchInventoryDTO>> batchInventory = batchInventoryMap.get(skuId);
            DiskAdjustmentVO inventory = inventoryMap.get(skuId);
            BigDecimal pickOccupyCount = pickOccupyCountMap.getOrDefault(skuId, BigDecimal.ZERO);
            BigDecimal defectiveStoreCount = defectiveStoreMap.getOrDefault(skuId, BigDecimal.ZERO);
            return new LackInfo(orgId, skuId, item.getId(), lackUnitTotalCount, inventory, batchInventory, locationMap, isOpenStock,
                    pickOccupyCount, defectiveStoreCount);
        }

        public static LackInfo reduce(LackInfo a, LackInfo b) {
            return copy(b, a.lackUnitTotalCount.add(b.lackUnitTotalCount));
        }

        public static LackInfo copy(LackInfo lackInfo, BigDecimal lackUnitTotalCount) {
            Integer orgId = lackInfo.orgId;
            Long skuId = lackInfo.skuId;
            DiskAdjustmentVO inventory = lackInfo.inventory;
            Map<Long, LocationReturnDTO> locationMap = lackInfo.locationMap;
            return new LackInfo(orgId, skuId, lackInfo.orderItemId, lackUnitTotalCount, inventory, locationMap, lackInfo.batchInventories,
                lackInfo.pickOccupyCount, lackInfo.defectiveStoreCount);
        }

        /**
         * 按货位类型排序
         *
         * @param lackInfo 缺货信息
         * @param isOpenStock 是否开启货位库存
         * @return 排序器
         */
        private Comparator<Map.Entry<Long, List<BatchInventoryDTO>>> sortLocation(LackInfo lackInfo,
            Boolean isOpenStock) {
            // 未开货位库存随机分摊
            if (!isOpenStock) {
                return Map.Entry.comparingByKey();
            }
            // 开启货位库存按照 拣货区、零拣区（零拣位，分拣位，存储位），再处理 周转区（出库位、集货位） 分摊
            Map<Long, LocationReturnDTO> locationMap = lackInfo.locationMap;
            return Comparator.comparing((Map.Entry<Long, List<BatchInventoryDTO>> it) -> compareLocationEnum(it.getKey(), locationMap, 零拣位))
                .thenComparing(it -> compareLocationEnum(it.getKey(), locationMap, 分拣位))
                .thenComparing(it -> compareLocationEnum(it.getKey(), locationMap, 存储位))
                .thenComparing(it -> compareLocationEnum(it.getKey(), locationMap, 出库位))
                .thenComparing(it -> compareLocationEnum(it.getKey(), locationMap, 集货位)).reversed();
        }

        /**
         * 比较货位类型
         *
         * @param id 货位 id
         * @param locationMap 货位 map
         * @param locationEnum 货位类型枚举
         * @return 该货位类型是否为指定类型
         */
        private boolean compareLocationEnum(Long id, Map<Long, LocationReturnDTO> locationMap,
            LocationEnum locationEnum) {
            Byte subcategory = locationMap.getOrDefault(id, new LocationReturnDTO()).getSubcategory();
            return Objects.equals(locationEnum.getType().byteValue(), subcategory);
        }

        public Long getOrderItemId() {
            return orderItemId;
        }

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

    /**
     * <AUTHOR>
     * @since 2017年8月16日 上午10:52:08
     */
    private static class DiskAdjustmentVO {
        /**
         * 产品SkuId
         */
        private final Long productSkuId;
        /**
         * 库存总数量
         */
        private final BigDecimal storeTotalCount;

        private DiskAdjustmentVO(Long productSkuId, BigDecimal storeTotalCount) {
            this.productSkuId = productSkuId;
            this.storeTotalCount = storeTotalCount;
        }

        public static DiskAdjustmentVO of(Long productSkuId, BigDecimal storeTotalCount) {
            return new DiskAdjustmentVO(productSkuId, storeTotalCount);
        }
    }

    private List<Long> getNoStoreCheckSkuIds(List<OutStockOrderPO> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }

        OutStockOrderPO outStockOrderPO = orders.stream().filter(p -> p != null).findFirst().orElse(new OutStockOrderPO());
        Integer warehouseId = outStockOrderPO.getWarehouseId();
        Warehouse warehouse = globalCache.getWarehouse(warehouseId);
        Integer orgId = warehouse.getCityId();
        boolean isOpenStock = warehouseConfigService.isOpenLocationStock(warehouseId);
        Set<Long> lackOrderIds = orders.stream().filter(p -> p != null && p.getId() != null).map(OutStockOrderPO::getId)
                .collect(Collectors.toSet());
        Set<String> batchTaskItemIds = orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(lackOrderIds).stream()
                .filter(p -> p != null).map(p -> p.getBatchTaskItemId()).collect(Collectors.toSet());
        Map<Long,
                Set<Long>> skuLocationIdMap = batchTaskItemMapper.selectBatchTaskItemByIds(batchTaskItemIds).stream()
                .filter(p -> p != null && p.getSkuId() != null).collect(Collectors.groupingBy(p -> p.getSkuId(),
                        Collectors.mapping(BatchTaskItemPO::getLocationId, Collectors.toSet())));

        List<OutStockConfigCheckDTO> checkProductDTOList = new ArrayList<>();
        skuLocationIdMap.forEach((skuId, locationIds) -> {
            if (CollectionUtils.isEmpty(locationIds)) {
                return;
            }

            if (isOpenStock) {
                locationIds.stream().forEach(locationId -> {
                    OutStockConfigCheckDTO checkProductDTO = new OutStockConfigCheckDTO();
                    checkProductDTO.setSkuId(skuId);
                    checkProductDTO.setLocationId(locationId);
                    checkProductDTOList.add(checkProductDTO);
                });
            } else {
                OutStockConfigCheckDTO checkProductDTO = new OutStockConfigCheckDTO();
                checkProductDTO.setSkuId(skuId);
                checkProductDTOList.add(checkProductDTO);
            }
        });

        if (CollectionUtils.isEmpty(checkProductDTOList)) {
            return Collections.emptyList();
        }

        OutStockConfigCheckParam checkParam = new OutStockConfigCheckParam();
        checkParam.setWarehouseId(warehouseId);
        checkParam.setCheckProductDTOList(checkProductDTOList);
        checkParam.setExcludeSubcategoryList(
                Arrays.asList(LocationEnum.残次品位.getType().byteValue(), LocationAreaEnum.残次品区.getType().byteValue()));
        checkParam.setCategoryPeriodConfigType(CategoryPeriodConfigTypeEnum.FORBID_SALES_PERIOD.getValue());
        List<Long> checkSkuIds = inStockConfigService.checkByCategoryPeriodConfigType(checkParam).stream()
                .filter(p -> p != null && p.getSkuId() != null
                        && Objects.equals(p.getAlarm(), OutStockAlarmEnum.出库禁止.getType()))
                .map(p -> p.getSkuId()).distinct().collect(Collectors.toList());
        logger.info("拣货缺货无需生成盘点单的sku: {}", JSON.toJSONString(checkSkuIds));
        return checkSkuIds;
    }
}
