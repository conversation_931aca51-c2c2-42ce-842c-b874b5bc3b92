package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange;

import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo.OutStockOrderAdminCancelHandlePickBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo.OutStockOrderAdminCancelHandlePickResultBO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchStateEnum;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @title: OutStockOrderPickingCancelBL
 * @description:
 * @date 2023-05-25 09:06
 */
@Service
public class OutStockOrderPickingCancelBL extends OutStockOrderAdminCancelBaseBL {

    @Override
    public boolean support(OutStockOrderAdminCancelHandlePickBO bo) {
        if (OutStockOrderStateEnum.拣货中.getType() == bo.getOutStockOrderPO().getState()) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    public boolean baseSupport(OutStockOrderAdminCancelHandlePickBO bo) {
        if (Objects.isNull(bo.getBatchPO())) {
            return Boolean.FALSE;
        }
        Byte batchState = Byte.valueOf(bo.getBatchPO().getState().toString());
        if (BatchStateEnum.PICKING.getType().byteValue() == batchState) {
            return Boolean.TRUE;
        }
        if (OutStockOrderStateEnum.拣货中.getType() == bo.getOutStockOrderPO().getState()) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    protected OutStockOrderAdminCancelHandlePickResultBO doCancel(OutStockOrderAdminCancelHandlePickBO bo) {

        return null;
    }
}
