package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskQueryPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderSaveDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskReceiveDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskUpdateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.UpdateSowerDTO;
import com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto.VirtualWarehouseSortingTaskRequestDTO;

/**
 * 播种任务
 */
public interface SowTaskMapper {

    SowTaskPO selectByPrimaryKey(Long id);

    int insert(SowTaskPO record);

    int insertSelective(SowTaskPO record);

    int updateByPrimaryKeySelective(SowTaskPO record);

    /**
     * 根据波次id查询所有播种任务
     *
     * @param batchId
     * @return
     */
    List<SowTaskPO> listSowTaskByBatchId(@Param("batchId") String batchId, @Param("orgId") Integer orgId);

    /**
     * 查询仓库所有未完成播种任务的存放货位
     *
     * @return
     */
    List<SowTaskPO> listNoFinishSowTaskCount(@Param("warehouseId") Integer warehouseId);

    /**
     * 查询仓库所有未完成的播种任务
     * 
     * @param warehouseId
     * @return
     */
    List<SowTaskPO> listNoFinishSowTask(@Param("warehouseId") Integer warehouseId);

    /**
     * 根据波次编号查询播种任务列表
     *
     * @param batchNos
     * @return
     */
    List<SowTaskPO> listSowTaskByBatchNos(@Param("list") List<String> batchNos);

    /**
     * 查询播种任务列表
     *
     * @param sowTaskQueryDTO
     * @param currentPage
     * @param pageSize
     * @return
     */
    PageResult<SowTaskPO> findList(@Param("dto") SowTaskQueryDTO sowTaskQueryDTO, @Param("pageNum") Integer currentPage,
        @Param("pageSize") Integer pageSize);

    /**
     * 根据播种任务编号查询播种任务订单关联信息
     *
     * @param sowTaskNo
     * @return
     */
    List<SowOrderInfoPO> listOutStockOrderBySowTaskNo(@Param("sowTaskNo") String sowTaskNo,
        @Param("orgId") Integer orgId);

    /**
     * 根据播种任务编号查询播种任务列表
     */
    List<SowTaskPO> findSowTaskByTaskNos(@Param("orgId") Integer orgId, @Param("list") List<String> sowTaskNos);

    /**
     * 批量保存播种任务
     *
     * @param lstSowTaskPO
     */
    void insertSowTaskList(@Param("list") List<SowTaskPO> lstSowTaskPO);

    /**
     * 根据播种任务编号更改播种状态
     *
     * @param sowTaskNo
     * @param state
     */
    void updateSowTaskState(@Param("sowTaskNo") String sowTaskNo, @Param("state") Byte state,
        @Param("orgId") Integer orgId);

    void updateSowTaskStateByDTO(@Param("updateSowerDTO") SowOrderSaveDTO sowOrderSaveDTO);

    /**
     * 查找集货位下可播种的播种任务(通过时间倒序找到第一个拣货完成的播种任务)
     * 
     * @param locationName
     * @param warehouseId
     * @param orgId
     * @return
     */
    SowTaskPO findCanSowingTaskByLocationName(@Param("locationName") String locationName,
        @Param("warehouseId") Integer warehouseId, @Param("orgId") Integer orgId);

    List<SowTaskPO> findCanSowingTaskListByLocationName(SowTaskReceiveDTO taskReceiveDTO);

    /**
     * 根据波次编号删除播种任务
     * 
     * @param sowTaskIds
     */
    void deleteBySowTaskIds(@Param("list") List<Long> sowTaskIds, @Param("orgId") Integer orgId);

    /**
     * 根据波次编号查询播种任务
     * 
     * @param lstNoProcessBatchNos
     * @return
     */
    List<SowTaskPO> findSowTaskByBatchNos(@Param("list") List<String> lstNoProcessBatchNos);

    /**
     * 通过仓库id获取该仓库下所有待播种的播种任务
     * 
     * @param orgId
     * @param warehouseId
     * @return
     */
    List<SowTaskPO> findSowTaskByWarehouseId(@Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId);

    /**
     * 通过拣货任务编号查询播种任务关联信息
     * 
     * @param batchTaskIds
     * @return
     */
    List<SowTaskInfoPO> findSowTaskInfoByBatchTaskIds(@Param("list") List<String> batchTaskIds,
        @Param("orgId") Integer orgId);

    /**
     * 通过播种任务id或编号查询播种任务
     */
    SowTaskPO findSowTaskByIdOrNo(@Param("orgId") Integer orgId, @Param("id") Long id,
        @Param("sowTaskNo") String sowTaskNo);

    /**
     * 查询仓库下已占用的容器id
     */
    List<Long> findUsedContainerLocationIds(@Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId);

    /**
     * 根据播种任务编号集合查询播种任务列表
     */
    List<SowTaskPO> listSowTaskByNos(@Param("list") List<String> sowTaskNos, @Param("orgId") Integer orgId);

    /**
     * 指派播种人
     */
    void assignmentSower(@Param("updateSowerDTO") UpdateSowerDTO updateSowerDTO);

    /**
     * 查找波次状态为拣货中的播种任务
     */
    List<SowTaskPO> findPickingSowTaskByWarehouseId(@Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId);

    List<BatchOrderDTO> listBatchOrderByWarehouse(@Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId);

    SowTaskPO getSowTaskByBatchTaskId(@Param("batchTaskId") String batchTaskId);

    List<SowTaskPO> findSowingByLocationId(@Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId,
        @Param("locationId") Long locationId, @Param("sowTaskId") Long sowTaskId);

    List<SowTaskPO> findAllSowTaskByIds(@Param("orgId") Integer orgId,
        @Param("sowTaskIds") Collection<Long> sowTaskIds);

    void batchUpdateCount(@Param("sowTaskUpdateDTOS") List<SowTaskUpdateDTO> sowTaskUpdateDTOS);

    SowTaskPO getAllById(@Param("id") Long id);

    SowTaskPO getAllBySowTaskNo(@Param("orgId") Integer orgId, @Param("sowTaskNo") String sowTaskNo);

    void completeByIds(@Param("ids") List<Long> ids);

    void receiveSowTask(@Param("update") UpdateSowerDTO updateSowerDTO);

    void batchUpdateState(@Param("updateList") List<SowTaskPO> updateTaskList);

    List<String> findBatchNos(@Param("orgId") Integer orgId, @Param("sowTaskNos") List<String> sowTaskNos);

    /**
     * 查询状态为待播种和播种中的货位id
     */
    List<Long> findByLocationIds(@Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId,
        @Param("locationIds") List<Long> locationIds);

    /**
     * 查询待播种信息
     * 
     * @param request
     * @return
     */
    List<SowTaskPO> queryWaitSortingTaskList(VirtualWarehouseSortingTaskRequestDTO request);

    /**
     * 根据分拣位分组查询
     * 
     * @param request
     * @return
     */
    List<String> queryWaitSortingTaskCount(VirtualWarehouseSortingTaskRequestDTO request);

    /**
     * 查询分拣出库位
     * 
     * @param request
     * @return
     */
    List<Long> queryWaitSortingTaskPage(VirtualWarehouseSortingTaskRequestDTO request);

    SowTaskPO getSowTaskByStatus(@Param("sow") SowTaskReceiveDTO sowTaskReceiveDTO, @Param("status") byte status);

    /**
     * 根据orgId id 查询
     */
    SowTaskPO getSowTaskById(@Param("id") Long id, @Param("orgId") Integer orgId);

    List<SowTaskPO> findSowTaskByIds(@Param("sowTaskIds") List<Long> sowTaskIds);

    /**
     * 查询待播种任务
     */
    SowTaskPO queryWaitSowTask(SowTaskReceiveDTO sowTaskReceiveDTO);

    /**
     * 通过 skuId, orgId, states, operatorId 查询播种任务
     */
    List<SowTaskPO> findSowTaskBySkuIds(SowTaskQueryDTO queryDTO);

    /**
     * 分页查询可追加的播种任务
     *
     * @param query 查询条件
     * @return 查询结果
     */
    PageResult<SowTaskPO> pageListAppendableSowTask(SowTaskQueryDTO query);

    /**
     * 查询波次下播种任务数量
     *
     * @param batchNo
     * @param orgId
     * @return
     */
    int findSowTaskCountByBatchNo(@Param("batchNo") String batchNo, @Param("orgId") Integer orgId);

    /**
     * 
     * @param queryDTO
     * @return
     */
    PageResult<SowTaskPO> findSowTaskByCon(SowTaskQueryPO queryDTO);

}