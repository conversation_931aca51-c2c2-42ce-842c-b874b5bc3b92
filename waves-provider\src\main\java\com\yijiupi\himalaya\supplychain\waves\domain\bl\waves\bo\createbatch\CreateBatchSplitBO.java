package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch;

import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/6/13
 */
public class CreateBatchSplitBO {

    private List<OutStockOrderPO> normalOrderList;

    private List<OutStockOrderPO> npOrderList;

    /**
     * 获取
     *
     * @return normalOrderList
     */
    public List<OutStockOrderPO> getNormalOrderList() {
        return this.normalOrderList;
    }

    /**
     * 设置
     *
     * @param normalOrderList
     */
    public void setNormalOrderList(List<OutStockOrderPO> normalOrderList) {
        this.normalOrderList = normalOrderList;
    }

    /**
     * 获取
     *
     * @return npOrderList
     */
    public List<OutStockOrderPO> getNpOrderList() {
        return this.npOrderList;
    }

    /**
     * 设置
     *
     * @param npOrderList
     */
    public void setNpOrderList(List<OutStockOrderPO> npOrderList) {
        this.npOrderList = npOrderList;
    }
}
