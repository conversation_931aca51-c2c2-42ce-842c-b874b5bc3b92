package com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 分拣任务列表
 */
public class VirtualWarehouseSortingTaskListDTO implements Serializable {
    private static final long serialVersionUID = 264465607091658421L;
    /**
     * 拣货位置
     */
    private String pickLocation;
    /**
     * 产品数量
     */
    private Integer productCount;
    /**
     * 大件数量
     */
    private BigDecimal packageCount;
    /**
     * 小件数量
     */
    private BigDecimal unitCount;
    /**
     * 任务id
     */
    private List<Long> taskIds;

    private Long locationId;

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getPickLocation() {
        return pickLocation;
    }

    public void setPickLocation(String pickLocation) {
        this.pickLocation = pickLocation;
    }

    public Integer getProductCount() {
        return productCount;
    }

    public void setProductCount(Integer productCount) {
        this.productCount = productCount;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public List<Long> getTaskIds() {
        return taskIds;
    }

    public void setTaskIds(List<Long> taskIds) {
        this.taskIds = taskIds;
    }
}
