<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.StockUpRecordPOMapper">
  <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordPO">
    <!--@mbg.generated-->
    <!--@Table stockuprecord-->
    <id column="Id" jdbcType="BIGINT" property="id" />
    <result column="OrgId" jdbcType="INTEGER" property="orgId" />
    <result column="WarehouseId" jdbcType="INTEGER" property="warehouseId" />
    <result column="TaskNo" jdbcType="VARCHAR" property="taskNo" />
    <result column="LicensePlate" jdbcType="VARCHAR" property="licensePlate" />
    <result column="DriverName" jdbcType="VARCHAR" property="driverName" />
    <result column="Location" jdbcType="VARCHAR" property="location" />
    <result column="PackageCount" jdbcType="DECIMAL" property="packageCount" />
    <result column="UnitCount" jdbcType="DECIMAL" property="unitCount" />
    <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    Id, OrgId, WarehouseId, TaskNo, LicensePlate, DriverName, `Location`, PackageCount, 
    UnitCount, CreateTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from stockuprecord
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from stockuprecord
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordPO">
    <!--@mbg.generated-->
    insert into stockuprecord (Id, OrgId, WarehouseId, 
      TaskNo, LicensePlate, DriverName, 
      `Location`, PackageCount, UnitCount, 
      CreateTime)
    values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER}, 
      #{taskNo,jdbcType=VARCHAR}, #{licensePlate,jdbcType=VARCHAR}, #{driverName,jdbcType=VARCHAR}, 
      #{location,jdbcType=VARCHAR}, #{packageCount,jdbcType=DECIMAL}, #{unitCount,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordPO">
    <!--@mbg.generated-->
    insert into stockuprecord
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="orgId != null">
        OrgId,
      </if>
      <if test="warehouseId != null">
        WarehouseId,
      </if>
      <if test="taskNo != null">
        TaskNo,
      </if>
      <if test="licensePlate != null">
        LicensePlate,
      </if>
      <if test="driverName != null">
        DriverName,
      </if>
      <if test="location != null">
        `Location`,
      </if>
      <if test="packageCount != null">
        PackageCount,
      </if>
      <if test="unitCount != null">
        UnitCount,
      </if>
      <if test="createTime != null">
        CreateTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="warehouseId != null">
        #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="taskNo != null">
        #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="licensePlate != null">
        #{licensePlate,jdbcType=VARCHAR},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="packageCount != null">
        #{packageCount,jdbcType=DECIMAL},
      </if>
      <if test="unitCount != null">
        #{unitCount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordPO">
    <!--@mbg.generated-->
    update stockuprecord
    <set>
      <if test="orgId != null">
        OrgId = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="warehouseId != null">
        WarehouseId = #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="taskNo != null and taskNo != ''">
        TaskNo = #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="licensePlate != null and licensePlate != ''">
        LicensePlate = #{licensePlate,jdbcType=VARCHAR},
      </if>
      <if test="driverName != null and driverName != ''">
        DriverName = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="location != null and location != ''">
        `Location` = #{location,jdbcType=VARCHAR},
      </if>
      <if test="packageCount != null">
        PackageCount = #{packageCount,jdbcType=DECIMAL},
      </if>
      <if test="unitCount != null">
        UnitCount = #{unitCount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordPO">
    <!--@mbg.generated-->
    update stockuprecord
    set OrgId = #{orgId,jdbcType=INTEGER},
      WarehouseId = #{warehouseId,jdbcType=INTEGER},
      TaskNo = #{taskNo,jdbcType=VARCHAR},
      LicensePlate = #{licensePlate,jdbcType=VARCHAR},
      DriverName = #{driverName,jdbcType=VARCHAR},
      `Location` = #{location,jdbcType=VARCHAR},
      PackageCount = #{packageCount,jdbcType=DECIMAL},
      UnitCount = #{unitCount,jdbcType=DECIMAL},
      CreateTime = #{createTime,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update stockuprecord
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="OrgId = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.orgId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="WarehouseId = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TaskNo = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.taskNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="LicensePlate = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.licensePlate,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="DriverName = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.driverName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`Location` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.location,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PackageCount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.packageCount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="UnitCount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.unitCount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="CreateTime = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where Id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update stockuprecord
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="OrgId = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orgId != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.orgId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="WarehouseId = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.warehouseId != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TaskNo = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.taskNo != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.taskNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="LicensePlate = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.licensePlate != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.licensePlate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="DriverName = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.driverName != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.driverName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`Location` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.location != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.location,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PackageCount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.packageCount != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.packageCount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="UnitCount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.unitCount != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.unitCount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="CreateTime = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where Id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into stockuprecord
    (Id, OrgId, WarehouseId, TaskNo, LicensePlate, DriverName, `Location`, PackageCount, 
      UnitCount, CreateTime)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.orgId,jdbcType=INTEGER}, #{item.warehouseId,jdbcType=INTEGER}, 
        #{item.taskNo,jdbcType=VARCHAR}, #{item.licensePlate,jdbcType=VARCHAR}, #{item.driverName,jdbcType=VARCHAR}, 
        #{item.location,jdbcType=VARCHAR}, #{item.packageCount,jdbcType=DECIMAL}, #{item.unitCount,jdbcType=DECIMAL}, 
        #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from stockuprecord where Id in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=BIGINT}
    </foreach>
  </delete>
  <insert id="insertOrUpdate" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordPO">
    <!--@mbg.generated-->
    insert into stockuprecord
    (Id, OrgId, WarehouseId, TaskNo, LicensePlate, DriverName, `Location`, PackageCount, 
      UnitCount, CreateTime)
    values
    (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER}, 
      #{taskNo,jdbcType=VARCHAR}, #{licensePlate,jdbcType=VARCHAR}, #{driverName,jdbcType=VARCHAR}, 
      #{location,jdbcType=VARCHAR}, #{packageCount,jdbcType=DECIMAL}, #{unitCount,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP})
    on duplicate key update 
    Id = #{id,jdbcType=BIGINT}, 
    OrgId = #{orgId,jdbcType=INTEGER}, 
    WarehouseId = #{warehouseId,jdbcType=INTEGER}, 
    TaskNo = #{taskNo,jdbcType=VARCHAR}, 
    LicensePlate = #{licensePlate,jdbcType=VARCHAR}, 
    DriverName = #{driverName,jdbcType=VARCHAR}, 
    `Location` = #{location,jdbcType=VARCHAR}, 
    PackageCount = #{packageCount,jdbcType=DECIMAL}, 
    UnitCount = #{unitCount,jdbcType=DECIMAL}, 
    CreateTime = #{createTime,jdbcType=TIMESTAMP}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordPO">
    <!--@mbg.generated-->
    insert into stockuprecord
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="orgId != null">
        OrgId,
      </if>
      <if test="warehouseId != null">
        WarehouseId,
      </if>
      <if test="taskNo != null and taskNo != ''">
        TaskNo,
      </if>
      <if test="licensePlate != null and licensePlate != ''">
        LicensePlate,
      </if>
      <if test="driverName != null and driverName != ''">
        DriverName,
      </if>
      <if test="location != null and location != ''">
        `Location`,
      </if>
      <if test="packageCount != null">
        PackageCount,
      </if>
      <if test="unitCount != null">
        UnitCount,
      </if>
      <if test="createTime != null">
        CreateTime,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="warehouseId != null">
        #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="taskNo != null and taskNo != ''">
        #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="licensePlate != null and licensePlate != ''">
        #{licensePlate,jdbcType=VARCHAR},
      </if>
      <if test="driverName != null and driverName != ''">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="location != null and location != ''">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="packageCount != null">
        #{packageCount,jdbcType=DECIMAL},
      </if>
      <if test="unitCount != null">
        #{unitCount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        Id = #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        OrgId = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="warehouseId != null">
        WarehouseId = #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="taskNo != null and taskNo != ''">
        TaskNo = #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="licensePlate != null and licensePlate != ''">
        LicensePlate = #{licensePlate,jdbcType=VARCHAR},
      </if>
      <if test="driverName != null and driverName != ''">
        DriverName = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="location != null and location != ''">
        `Location` = #{location,jdbcType=VARCHAR},
      </if>
      <if test="packageCount != null">
        PackageCount = #{packageCount,jdbcType=DECIMAL},
      </if>
      <if test="unitCount != null">
        UnitCount = #{unitCount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
</mapper>