package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation;

import java.math.BigDecimal;
import java.util.*;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CreateBatchLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;

/**
 * <AUTHOR>
 * @title: CreateBatchLocationRecommendBL
 * @description: 推荐货位库存
 * @date 2022-09-27 09:31
 */
@Component
public class CreateBatchLocationRecommendBL extends CreateBatchLocationBaseBL {

    @Override
    public boolean doSupport(CreateBatchLocationBO bo) {
        boolean isOpenStock = bo.getIsOpenStock();
        boolean isOpenLocationGroup = bo.getIsOpenLocationGroup();
        if (isOpenStock && !isOpenLocationGroup) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public List<OutStockOrderPO> doSetLocation(List<OutStockOrderPO> outStockOrderList, CreateBatchLocationBO bo) {
        // 是否开启整箱拆零拣货
        boolean isLargePick = false;
        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(bo.getWarehouseId());
        if (warehouseConfigDTO != null) {
            isLargePick = Objects.equals(warehouseConfigDTO.getLargePick(), ConditionStateEnum.是.getType());
        }

        // 给没开启货位库存的产品设置最老的生产日期和批次时间
        setProductDateAndBatchTime(outStockOrderList);
        // 将订单项按详细拆分
        setOutStockOrderItemDetail(outStockOrderList);

        // 查询产品关联货位
        Map<Long, List<ProductLoactionItemDTO>> productLocationMap = getRecommendLocationMap(outStockOrderList, bo);

        // 记录找不到推荐货位配置的产品名称
        Set<String> nameSet = new HashSet<>();
        for (int s = outStockOrderList.size() - 1; s >= 0; s--) {
            OutStockOrderPO outStockOrderPO = outStockOrderList.get(s);
            List<OutStockOrderItemPO> outStockOrderItemPOList = outStockOrderPO.getItems();
            if (isLargePick) {
                LOG.info("开启了整箱拆零拣货，可能按货位拆分");
                // 开启整箱拆零拣货，可能按货位拆分
                List<OutStockOrderItemPO> newOutStockOrderItemPOList =
                    getOrderItemByLargePick(outStockOrderItemPOList, productLocationMap, nameSet);
                outStockOrderPO.setItems(newOutStockOrderItemPOList);
            } else {
                for (OutStockOrderItemPO stockOrderItemPO : outStockOrderItemPOList) {
                    List<ProductLoactionItemDTO> productLoactionItemDTOS =
                        productLocationMap.get(stockOrderItemPO.getSkuid());
                    ProductLoactionItemDTO productLoactionItemDTO = null;
                    if (!CollectionUtils.isEmpty(productLoactionItemDTOS)) {
                        // 如果产品配置了多个货位，优先使用零拣位和分拣位，再存储位
                        productLoactionItemDTO = getProductLoactionItemDTO(productLoactionItemDTOS);
                        setOrderItemByLocation(stockOrderItemPO, productLoactionItemDTO);
                    } else {
                        // 找不到推荐货位配置则记录产品名称
                        nameSet.add(stockOrderItemPO.getProductname());
                    }
                }
            }
        }
        LOG.info("以下商品未找到合适货位 {}", String.join(",", nameSet));
        return outStockOrderList;
    }

    /**
     * 开启整箱拆零拣货，需要按货位拆分
     */
    private List<OutStockOrderItemPO> getOrderItemByLargePick(List<OutStockOrderItemPO> outStockOrderItemPOList,
        Map<Long, List<ProductLoactionItemDTO>> productLoactionMap, Set<String> nameSet) {
        // 整件货位
        List<Integer> largeLocationList = Arrays.asList(LocationEnum.存储位.getType());
        // 拆零货位
        List<Integer> smallLocationList = Arrays.asList(LocationEnum.零拣位.getType(), LocationEnum.分拣位.getType());

        List<OutStockOrderItemPO> largePickItemList = new ArrayList<>();
        outStockOrderItemPOList.forEach(item -> {
            OutStockOrderItemPO newItemPO = new OutStockOrderItemPO();
            BeanUtils.copyProperties(item, newItemPO);
            // 当前产品的关联货位
            List<ProductLoactionItemDTO> productLoactionDTOS = productLoactionMap.get(newItemPO.getSkuid());
            if (CollectionUtils.isEmpty(productLoactionDTOS)) {
                // 找不到关联货位
                nameSet.add(newItemPO.getProductname());
            } else {
                // 小件优先查找货位
                ProductLoactionItemDTO smallProductLoactionDTO =
                    getProductLoactionItemDTOByCondition(productLoactionDTOS, smallLocationList, largeLocationList);
                // 大件优先查找货位
                ProductLoactionItemDTO largeProductLoactionDTO =
                    getProductLoactionItemDTOByCondition(productLoactionDTOS, largeLocationList, smallLocationList);

                // 如果产品包含大件和小件，同时关联了整件货位和拆零货位，则需要拆分
                if (newItemPO.getPackagecount() != null && newItemPO.getPackagecount().compareTo(BigDecimal.ZERO) > 0
                    && newItemPO.getUnitcount() != null && newItemPO.getUnitcount().compareTo(BigDecimal.ZERO) > 0
                    && productLoactionDTOS.stream().anyMatch(
                        p -> p.getSubcategory() != null && largeLocationList.contains(p.getSubcategory().intValue()))
                    && productLoactionDTOS.stream().anyMatch(
                        p -> p.getSubcategory() != null && smallLocationList.contains(p.getSubcategory().intValue()))) {
                    // 1、拆分出一个只有小件数量的订单项
                    OutStockOrderItemPO smallItemPO = new OutStockOrderItemPO();
                    BeanUtils.copyProperties(newItemPO, smallItemPO);
                    smallItemPO.setPackagecount(BigDecimal.ZERO);
                    smallItemPO.setUnittotalcount(smallItemPO.getPackagecount().multiply(smallItemPO.getSpecquantity())
                        .add(smallItemPO.getUnitcount()));
                    // 小件订单项分配拆零货位
                    setOrderItemByLocation(smallItemPO, smallProductLoactionDTO);
                    largePickItemList.add(smallItemPO);

                    // 2、处理只有大件的订单项
                    newItemPO.setUnitcount(BigDecimal.ZERO);
                    newItemPO.setUnittotalcount(newItemPO.getPackagecount().multiply(newItemPO.getSpecquantity())
                        .add(newItemPO.getUnitcount()));
                    // 大件订单项分配整件货位
                    setOrderItemByLocation(newItemPO, largeProductLoactionDTO);

                } else if (item.getPackagecount() != null && item.getPackagecount().compareTo(BigDecimal.ZERO) > 0) {
                    // 大件优先分配整件货位
                    setOrderItemByLocation(newItemPO, largeProductLoactionDTO);
                } else {
                    // 小件优先分配拆零货位
                    setOrderItemByLocation(newItemPO, smallProductLoactionDTO);
                }
            }
            // 记录处理了分配货位后的订单项
            largePickItemList.add(newItemPO);
        });
        return largePickItemList;
    }

    /**
     * 按配置货位拣货出库的时候，如果产品配置了多个货位，优先使用零拣位和分拣位，再存储位
     * 
     * @return
     */
    private ProductLoactionItemDTO getProductLoactionItemDTO(List<ProductLoactionItemDTO> productLoactionItemDTOS) {
        if (CollectionUtils.isEmpty(productLoactionItemDTOS)) {
            return null;
        }

        ProductLoactionItemDTO productLoactionItemDTO = null;
        // 按配置货位拣货出库的时候，如果产品配置了多个货位，优先使用零拣位和分拣位，再存储位
        if (productLoactionItemDTOS.stream()
            .anyMatch(p -> p.getSubcategory() != null && LocationEnum.零拣位.getType() == p.getSubcategory().intValue())) {
            Optional<ProductLoactionItemDTO> optional = productLoactionItemDTOS.stream()
                .filter(p -> p.getSubcategory() != null && LocationEnum.零拣位.getType() == p.getSubcategory().intValue())
                .findFirst();
            if (optional.isPresent()) {
                productLoactionItemDTO = optional.get();
            }
        } else if (productLoactionItemDTOS.stream()
            .anyMatch(p -> p.getSubcategory() != null && LocationEnum.分拣位.getType() == p.getSubcategory().intValue())) {
            Optional<ProductLoactionItemDTO> optional = productLoactionItemDTOS.stream()
                .filter(p -> p.getSubcategory() != null && LocationEnum.分拣位.getType() == p.getSubcategory().intValue())
                .findFirst();
            if (optional.isPresent()) {
                productLoactionItemDTO = optional.get();
            }
        } else if (productLoactionItemDTOS.stream()
            .anyMatch(p -> p.getSubcategory() != null && LocationEnum.存储位.getType() == p.getSubcategory().intValue())) {
            Optional<ProductLoactionItemDTO> optional = productLoactionItemDTOS.stream()
                .filter(p -> p.getSubcategory() != null && LocationEnum.存储位.getType() == p.getSubcategory().intValue())
                .findFirst();
            if (optional.isPresent()) {
                productLoactionItemDTO = optional.get();
            }
        }
        if (productLoactionItemDTO == null) {
            productLoactionItemDTO = productLoactionItemDTOS.get(0);
        }
        return productLoactionItemDTO;
    }

}