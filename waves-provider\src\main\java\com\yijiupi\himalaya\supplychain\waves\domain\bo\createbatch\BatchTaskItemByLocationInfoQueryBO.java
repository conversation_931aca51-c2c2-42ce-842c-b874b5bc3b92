package com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/7/3
 */
public class BatchTaskItemByLocationInfoQueryBO {
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * skuId列表
     */
    private List<String> productSkuIds;
    /**
     * 货位id列表
     */
    private List<String> locationIds;
    /**
     * 拣货任务状态列表
     */
    private List<Byte> taskStateList;
    /**
     * 组织机构id
     */
    private Integer orgId;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 skuId列表
     *
     * @return productSkuIds skuId列表
     */
    public List<String> getProductSkuIds() {
        return this.productSkuIds;
    }

    /**
     * 设置 skuId列表
     *
     * @param productSkuIds skuId列表
     */
    public void setProductSkuIds(List<String> productSkuIds) {
        this.productSkuIds = productSkuIds;
    }

    /**
     * 获取 货位id列表
     *
     * @return locationIds 货位id列表
     */
    public List<String> getLocationIds() {
        return this.locationIds;
    }

    /**
     * 设置 货位id列表
     *
     * @param locationIds 货位id列表
     */
    public void setLocationIds(List<String> locationIds) {
        this.locationIds = locationIds;
    }

    /**
     * 获取 拣货任务状态列表
     *
     * @return taskStateList 拣货任务状态列表
     */
    public List<Byte> getTaskStateList() {
        return this.taskStateList;
    }

    /**
     * 设置 拣货任务状态列表
     *
     * @param taskStateList 拣货任务状态列表
     */
    public void setTaskStateList(List<Byte> taskStateList) {
        this.taskStateList = taskStateList;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }
}
