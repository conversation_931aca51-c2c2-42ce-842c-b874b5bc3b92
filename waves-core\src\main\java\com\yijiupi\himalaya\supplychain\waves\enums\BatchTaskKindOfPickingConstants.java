package com.yijiupi.himalaya.supplychain.waves.enums;

import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;

import java.util.Objects;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/10/28
 */
public final class BatchTaskKindOfPickingConstants {

    public static final Byte DEFAULT = 1;
    /**
     * 边拣边播
     */
    public static final Byte PICKING_SORTING = 3;

    /**
     * 勿用，已废弃
     */
    @Deprecated
    public static final Byte DIGITAL = 2;

    public static Byte convertToPickPattern(Byte kindOfPicking) {
        if (DEFAULT.equals(kindOfPicking)) {
            return BatchTaskPickPatternEnum.人工拣货.getType();
        }
        if (DIGITAL.equals(kindOfPicking)) {
            return BatchTaskPickPatternEnum.电子标签.getType();
        }

        return BatchTaskPickPatternEnum.人工拣货.getType();
    }

    public static Byte convertToPickPattern(BatchTaskDTO dto) {
        if (Objects.nonNull(dto.getPickPattern())) {
            return dto.getPickPattern();
        }

        return convertToPickPattern(dto.getKindOfPicking());
    }

}