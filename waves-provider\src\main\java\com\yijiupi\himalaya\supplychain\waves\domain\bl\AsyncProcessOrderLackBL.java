package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 缺货异步处理
 */
@Service
public class AsyncProcessOrderLackBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessOrderLackBL.class);

    @Autowired
    private OutStockOrderBL outStockOrderBL;

    /**
     * 异步处理缺货通知OMS
     *
     * @param batchNo
     * @param operatorUserId
     */
    @Async(value = "waveTaskExecutor")
    public void asyncProcessOrderLack(String batchNo, Integer operatorUserId) {
        LOGGER.info("异步处理缺货通知 batchNo={}", batchNo);
        outStockOrderBL.processOrderLack(batchNo, operatorUserId);
    }
}
