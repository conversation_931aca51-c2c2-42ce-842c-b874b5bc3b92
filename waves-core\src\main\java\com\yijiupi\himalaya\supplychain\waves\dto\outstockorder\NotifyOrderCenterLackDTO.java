package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/5/7
 */
public class NotifyOrderCenterLackDTO implements Serializable {
    /**
     * 缺货信息
     */
    private List<OutStockLackDTO> lackDTOList;
    /**
     * 描述信息
     */
    private Map<String, StringBuffer> bufferMap;
    /**
     * 操作人
     */
    private Integer operatorUserId;
    /**
     * 缺货类型 <br />
     * 
     * @see OrderExtendMapEnum
     */
    private String lackType;

    public NotifyOrderCenterLackDTO() {}

    public NotifyOrderCenterLackDTO(List<OutStockLackDTO> lackDTOList, Map<String, StringBuffer> bufferMap,
        Integer operatorUserId, String lackType) {
        this.lackDTOList = lackDTOList;
        this.bufferMap = bufferMap;
        this.operatorUserId = operatorUserId;
        this.lackType = lackType;
    }

    /**
     * 获取 缺货信息
     *
     * @return lackDTOList 缺货信息
     */
    public List<OutStockLackDTO> getLackDTOList() {
        return this.lackDTOList;
    }

    /**
     * 设置 缺货信息
     *
     * @param lackDTOList 缺货信息
     */
    public void setLackDTOList(List<OutStockLackDTO> lackDTOList) {
        this.lackDTOList = lackDTOList;
    }

    /**
     * 获取 描述信息
     *
     * @return bufferMap 描述信息
     */
    public Map<String, StringBuffer> getBufferMap() {
        return this.bufferMap;
    }

    /**
     * 设置 描述信息
     *
     * @param bufferMap 描述信息
     */
    public void setBufferMap(Map<String, StringBuffer> bufferMap) {
        this.bufferMap = bufferMap;
    }

    /**
     * 获取 操作人
     *
     * @return operatorUserId 操作人
     */
    public Integer getOperatorUserId() {
        return this.operatorUserId;
    }

    /**
     * 设置 操作人
     *
     * @param operatorUserId 操作人
     */
    public void setOperatorUserId(Integer operatorUserId) {
        this.operatorUserId = operatorUserId;
    }

    /**
     * 获取 缺货类型 <br >
     * 
     * @see OrderExtendMapEnum
     *
     * @return lackType 缺货类型 <br >
     * @see OrderExtendMapEnum
     */
    public String getLackType() {
        return this.lackType;
    }

    /**
     * 设置 缺货类型 <br >
     * 
     * @see OrderExtendMapEnum
     *
     * @param lackType 缺货类型 <br >
     * @see OrderExtendMapEnum
     */
    public void setLackType(String lackType) {
        this.lackType = lackType;
    }

}
