package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.user.dto.sign.UserSignBelongDateQueryDTO;
import com.yijiupi.himalaya.supplychain.user.dto.sign.UserSignBelongDateResultDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.TaskPerformanceCalculateBO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/25
 */
public class UserSignBelongDateQueryDTOConvertor {

    public static UserSignBelongDateQueryDTO convert(TaskPerformanceCalculateBO calculateBO) {
        UserSignBelongDateQueryDTO queryDTO = new UserSignBelongDateQueryDTO();
        queryDTO.setUserId(calculateBO.getOperateUserId());
        queryDTO.setOperateTime(calculateBO.getOperateTime());
        queryDTO.setOrgId(calculateBO.getOrgId());
        queryDTO.setWarehouseId(calculateBO.getWarehouseId());

        return queryDTO;
    }

    public static void fillPerformanceDate(TaskPerformanceCalculateBO calculateBO,
        List<UserSignBelongDateResultDTO> belongDateResultDTOS) {
        Map<String, UserSignBelongDateResultDTO> resultDTOMap = belongDateResultDTOS.stream()
            .collect(Collectors.toMap(k -> getKey(k.getUserId(), k.getOperateTime()), v -> v));

        UserSignBelongDateResultDTO userSignBelongDateResultDTO =
            resultDTOMap.get(getKey(calculateBO.getOperateUserId(), calculateBO.getOperateTime()));
        calculateBO.setPerformanceDate(userSignBelongDateResultDTO.getBelongDate());
    }

    private static String getKey(Integer userId, String operateTime) {
        return userId + "-" + operateTime;
    }

}
