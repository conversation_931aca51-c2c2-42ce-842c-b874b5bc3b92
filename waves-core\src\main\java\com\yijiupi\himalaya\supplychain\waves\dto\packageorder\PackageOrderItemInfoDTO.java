package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023-11-02 15:12
 **/
public class PackageOrderItemInfoDTO implements Serializable {

    /**
     * packageOrderItem 表主键 id
     */
    private Long packageOrderItemId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * skuId
     */
    private Long productSkuId;

    /**
     * 包装规格名称
     */
    private String productSpec;

    /**
     * 包装规格系数
     */
    private BigDecimal specQuantity;

    /**
     * 销售规格系数
     */
    private BigDecimal saleSpecQuantity;
    /**
     * 销售规格名称
     */
    private String saleSpecName;

    /**
     * 总共小单位数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 小单位名称
     */
    private String unitName;

    public Long getPackageOrderItemId() {
        return packageOrderItemId;
    }

    public void setPackageOrderItemId(Long packageOrderItemId) {
        this.packageOrderItemId = packageOrderItemId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getProductSpec() {
        return productSpec;
    }

    public void setProductSpec(String productSpec) {
        this.productSpec = productSpec;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public BigDecimal getSaleSpecQuantity() {
        return saleSpecQuantity;
    }

    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    public String getSaleSpecName() {
        return saleSpecName;
    }

    public void setSaleSpecName(String saleSpecName) {
        this.saleSpecName = saleSpecName;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }
}
