<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.SowOrderMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="INTEGER" property="orgId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="SowTask_Id" jdbcType="VARCHAR" property="sowTaskId"/>
        <result column="SowTaskNo" jdbcType="VARCHAR" property="sowTaskNo"/>
        <result column="OutStockOrder_Id" jdbcType="BIGINT" property="outStockOrderId"/>
        <result column="RefOrderNo" jdbcType="VARCHAR" property="refOrderNo"/>
        <result column="SkuCount" jdbcType="INTEGER" property="skuCount"/>
        <result column="PackageAmount" jdbcType="DECIMAL" property="packageAmount"/>
        <result column="UnitAmount" jdbcType="DECIMAL" property="unitAmount"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="SownSkuCount" jdbcType="INTEGER" property="sownSkuCount"/>
        <result column="SownPackageAmount" jdbcType="DECIMAL" property="sownPackageAmount"/>
        <result column="SownUnitAmount" jdbcType="DECIMAL" property="sownUnitAmount"/>
        <result column="SowOrderSequence" jdbcType="INTEGER" property="sowOrderSequence"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="LocationId" property="locationId" jdbcType="BIGINT"/>
        <result column="LocationName" property="locationName" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id, Org_Id, Warehouse_Id, SowTask_Id, SowTaskNo, OutStockOrder_Id, RefOrderNo, SkuCount,
        PackageAmount, UnitAmount, CreateUser, LastUpdateUser, CreateTime, LastUpdateTime,
        SownSkuCount, SownPackageAmount, SownUnitAmount, SowOrderSequence, Remark, LocationId, LocationName
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from soworder
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO">
        insert into soworder (Id, Org_Id, Warehouse_Id,
        SowTask_Id, SowTaskNo, OutStockOrder_Id,
        RefOrderNo, SkuCount, PackageAmount,
        UnitAmount, CreateUser, LastUpdateUser,
        CreateTime, LastUpdateTime, SownSkuCount,
        SownPackageAmount, SownUnitAmount, SowOrderSequence, Remark, LocationId, LocationName)
        values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER},
        #{sowtaskId,jdbcType=VARCHAR}, #{sowtaskno,jdbcType=VARCHAR}, #{outstockorderId,jdbcType=BIGINT},
        #{reforderno,jdbcType=VARCHAR}, #{skucount,jdbcType=INTEGER}, #{packageamount,jdbcType=DECIMAL},
        #{unitamount,jdbcType=DECIMAL}, #{createuser,jdbcType=VARCHAR}, #{lastupdateuser,jdbcType=VARCHAR},
        #{createtime,jdbcType=TIMESTAMP}, #{lastupdatetime,jdbcType=TIMESTAMP}, #{sownSkuCount,jdbcType=INTEGER},
        #{sownPackageAmount,jdbcType=DECIMAL}, #{sownUnitAmount,jdbcType=DECIMAL}, #{sowOrderSequence,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR}, #{locationId,jdbcType=BIGINT}, #{locationName,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO">
        insert into soworder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="orgId != null">
                Org_Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="sowtaskId != null">
                SowTask_Id,
            </if>
            <if test="sowtaskno != null">
                SowTaskNo,
            </if>
            <if test="outstockorderId != null">
                OutStockOrder_Id,
            </if>
            <if test="reforderno != null">
                RefOrderNo,
            </if>
            <if test="skucount != null">
                SkuCount,
            </if>
            <if test="packageamount != null">
                PackageAmount,
            </if>
            <if test="unitamount != null">
                UnitAmount,
            </if>
            <if test="createuser != null">
                CreateUser,
            </if>
            <if test="lastupdateuser != null">
                LastUpdateUser,
            </if>
            <if test="createtime != null">
                CreateTime,
            </if>
            <if test="lastupdatetime != null">
                LastUpdateTime,
            </if>
            <if test="sownSkuCount != null">
                SownSkuCount,
            </if>
            <if test="sownPackageAmount != null">
                SownPackageAmount,
            </if>
            <if test="sownUnitAmount != null">
                SownUnitAmount,
            </if>
            <if test="sowOrderSequence != null">
                SowOrderSequence,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="locationId != null ">
                LocationId,
            </if>
            <if test="locationName != null ">
                LocationName,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="sowtaskId != null">
                #{sowtaskId,jdbcType=VARCHAR},
            </if>
            <if test="sowtaskno != null">
                #{sowtaskno,jdbcType=VARCHAR},
            </if>
            <if test="outstockorderId != null">
                #{outstockorderId,jdbcType=BIGINT},
            </if>
            <if test="reforderno != null">
                #{reforderno,jdbcType=VARCHAR},
            </if>
            <if test="skucount != null">
                #{skucount,jdbcType=INTEGER},
            </if>
            <if test="packageamount != null">
                #{packageamount,jdbcType=DECIMAL},
            </if>
            <if test="unitamount != null">
                #{unitamount,jdbcType=DECIMAL},
            </if>
            <if test="createuser != null">
                #{createuser,jdbcType=VARCHAR},
            </if>
            <if test="lastupdateuser != null">
                #{lastupdateuser,jdbcType=VARCHAR},
            </if>
            <if test="createtime != null">
                #{createtime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastupdatetime != null">
                #{lastupdatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="sownSkuCount != null">
                #{sownSkuCount,jdbcType=INTEGER},
            </if>
            <if test="sownPackageAmount != null">
                #{sownPackageAmount,jdbcType=DECIMAL},
            </if>
            <if test="sownUnitAmount != null">
                #{sownUnitAmount,jdbcType=DECIMAL},
            </if>
            <if test="sowOrderSequence != null">
                #{sowOrderSequence,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="locationId != null ">
                #{locationId,jdbcType=BIGINT},
            </if>
            <if test="locationName != null ">
                #{locationName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Oct 25 18:05:17 CST 2022.
        -->
        update soworder
        <set>
            <if test="orgId != null">
                Org_Id = #{orgId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="sowTaskId != null">
                SowTask_Id = #{sowTaskId,jdbcType=BIGINT},
            </if>
            <if test="sowTaskNo != null">
                SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR},
            </if>
            <if test="outStockOrderId != null">
                OutStockOrder_Id = #{outStockOrderId,jdbcType=BIGINT},
            </if>
            <if test="refOrderNo != null">
                RefOrderNo = #{refOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="skuCount != null">
                SkuCount = #{skuCount,jdbcType=INTEGER},
            </if>
            <if test="packageAmount != null">
                PackageAmount = #{packageAmount,jdbcType=DECIMAL},
            </if>
            <if test="unitAmount != null">
                UnitAmount = #{unitAmount,jdbcType=DECIMAL},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sownSkuCount != null">
                SownSkuCount = #{sownSkuCount,jdbcType=INTEGER},
            </if>
            <if test="sownPackageAmount != null">
                SownPackageAmount = #{sownPackageAmount,jdbcType=DECIMAL},
            </if>
            <if test="sownUnitAmount != null">
                SownUnitAmount = #{sownUnitAmount,jdbcType=DECIMAL},
            </if>
            <if test="sowOrderSequence != null">
                SowOrderSequence = #{sowOrderSequence,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="locationId != null">
                LocationId = #{locationId,jdbcType=BIGINT},
            </if>
            <if test="locationName != null">
                LocationName = #{locationName,jdbcType=VARCHAR},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO">
        update soworder
        set Org_Id = #{orgId,jdbcType=INTEGER},
        Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
        SowTask_Id = #{sowtaskId,jdbcType=VARCHAR},
        SowTaskNo = #{sowtaskno,jdbcType=VARCHAR},
        OutStockOrder_Id = #{outstockorderId,jdbcType=BIGINT},
        RefOrderNo = #{reforderno,jdbcType=VARCHAR},
        SkuCount = #{skucount,jdbcType=INTEGER},
        PackageAmount = #{packageamount,jdbcType=DECIMAL},
        UnitAmount = #{unitamount,jdbcType=DECIMAL},
        CreateUser = #{createuser,jdbcType=VARCHAR},
        LastUpdateUser = #{lastupdateuser,jdbcType=VARCHAR},
        CreateTime = #{createtime,jdbcType=TIMESTAMP},
        LastUpdateTime = #{lastupdatetime,jdbcType=TIMESTAMP},
        SownSkuCount = #{sownSkuCount,jdbcType=INTEGER},
        SownPackageAmount = #{sownPackageAmount,jdbcType=DECIMAL},
        SownUnitAmount = #{sownUnitAmount,jdbcType=DECIMAL},
        SowOrderSequence = #{sowOrderSequence,jdbcType=INTEGER},
        Remark = #{remark,jdbcType=VARCHAR},
        LocationId = #{locationId,jdbcType=BIGINT},
        LocationName = #{locationName,jdbcType=VARCHAR}
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="insertSowOrderList" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO">
        insert into soworder (Id, Org_id, Warehouse_Id,
        SowTask_Id, SowTaskNo, OutStockOrder_Id,
        RefOrderNo, SkuCount, PackageAmount,
        UnitAmount, CreateUser, SowOrderSequence, Remark,
        LocationId, LocationName
        )
        values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.orgId,jdbcType=INTEGER}, #{item.warehouseId,jdbcType=INTEGER},
            #{item.sowTaskId,jdbcType=BIGINT}, #{item.sowTaskNo,jdbcType=VARCHAR},
            #{item.outStockOrderId,jdbcType=BIGINT},
            #{item.refOrderNo,jdbcType=VARCHAR}, #{item.skuCount,jdbcType=INTEGER},
            #{item.packageAmount,jdbcType=DECIMAL},
            #{item.unitAmount,jdbcType=DECIMAL}, #{item.createUser,jdbcType=VARCHAR},
            #{item.sowOrderSequence,jdbcType=INTEGER},
            #{item.remark,jdbcType=VARCHAR},
            #{item.locationId,jdbcType=BIGINT}, #{item.locationName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="updateSownAmountByOrderId">
        UPDATE soworder
        SET SownSkuCount=
        CASE OutStockOrder_Id
        <foreach collection="sownOrderItemDTOS" item="sownOrderItemDTO">
            WHEN #{sownOrderItemDTO.outStockOrderId,jdbcType=BIGINT}
            THEN #{sownOrderItemDTO.sownSkuCount,jdbcType=INTEGER}
        </foreach>
        END
        , SownPackageAmount=
        CASE OutStockOrder_Id
        <foreach collection="sownOrderItemDTOS" item="sownOrderItemDTO">
            WHEN #{sownOrderItemDTO.outStockOrderId,jdbcType=BIGINT}
            THEN #{sownOrderItemDTO.sownPackageAmount,jdbcType=DECIMAL}
        </foreach>
        END
        , SownUnitAmount=
        CASE OutStockOrder_Id
        <foreach collection="sownOrderItemDTOS" item="sownOrderItemDTO">
            WHEN #{sownOrderItemDTO.outStockOrderId,jdbcType=BIGINT}
            THEN #{sownOrderItemDTO.sownUnitAmount,jdbcType=DECIMAL}
        </foreach>
        END
        , LastUpdateUser= #{lastUpdateUser,jdbcType=VARCHAR}
        WHERE SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR}
        AND Org_Id = #{orgId,jdbcType=INTEGER}
        AND OutStockOrder_Id IN
        <foreach collection="sownOrderItemDTOS" item="sownOrderItemDTO" index="index" open="(" separator="," close=")">
            #{sownOrderItemDTO.outStockOrderId,jdbcType=BIGINT}
        </foreach>
    </update>

    <delete id="deleteBySowTaskIds">
        delete from soworder
        where SowTask_Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and Org_Id = #{orgId,jdbcType=INTEGER}
    </delete>

    <select id="listRefOrderNoBySowTaskNo" resultType="java.lang.String">
        select distinct RefOrderNo from soworder
        where SowTaskNo in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="findByOrderIds" resultType="com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderDTO">
        select so.Id, so.Org_Id as orgId, so.Warehouse_Id as warehouseId, so.SowTask_Id as sowTaskId, so.SowTaskNo,
        so.OutStockOrder_Id as outStockOrderId, so.RefOrderNo, so.SkuCount,
        so.PackageAmount, so.UnitAmount, so.CreateUser, so.LastUpdateUser, so.CreateTime, so.LastUpdateTime,
        so.SownSkuCount, so.SownPackageAmount, so.SownUnitAmount, so.SowOrderSequence, so.Remark, st.Location_Id as
        sowLocationId,
        st.LocationName as sowLocationName
        from soworder so left join sowtask st on so.SowTask_Id = st.Id
        where OutStockOrder_Id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="findUnpackOrderList"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.soworder.UnpackOrderDTO">
        SELECT
        distinct os.id,os.RefOrderNo,b.BatchNo,b.BatchName,st.Location_Id as sowLocationId,st.LocationName as
        sowLocationName,
        so.SowOrderSequence,so.SkuCount,so.PackageAmount,so.UnitAmount,bt.ToLocationName,bt.ToLocation_Id as
        toLocationId,st.LastUpdateTime
        FROM outstockorder os
        INNER JOIN soworder so on so.OutStockOrder_Id = os.id
        INNER JOIN sowtask st on st.id = so.SowTask_Id
        INNER JOIN batchtask bt on bt.SowTask_Id = st.id
        INNER JOIN batch b on st.Batch_Id = b.id
        where os.Org_Id = #{unpackOrderQuery.orgId,jdbcType=INTEGER} and os.Warehouse_Id =
        #{unpackOrderQuery.warehouseId,jdbcType=INTEGER}
        and st.State = 2
        <if test="unpackOrderQuery.refOrderNo!=null and unpackOrderQuery.refOrderNo!='' ">
            and os.RefOrderNo = #{unpackOrderQuery.refOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="unpackOrderQuery.batchNo!=null and unpackOrderQuery.batchNo!='' ">
            and os.BatchNo = #{unpackOrderQuery.batchNo,jdbcType=VARCHAR}
        </if>
        <if test="unpackOrderQuery.toLocationName!=null and unpackOrderQuery.toLocationName!='' ">
            and bt.ToLocationName = #{unpackOrderQuery.toLocationName,jdbcType=VARCHAR}
        </if>
        <if test="unpackOrderQuery.sowLocationName!=null and unpackOrderQuery.sowLocationName!='' ">
            and st.LocationName = #{unpackOrderQuery.sowLocationName}
        </if>
        <if test="unpackOrderQuery.startTime!=null">
            and st.LastUpdateTime <![CDATA[ >= ]]> #{unpackOrderQuery.startTime}
        </if>
        <if test="unpackOrderQuery.endTime!=null">
            and st.LastUpdateTime <![CDATA[ <= ]]> #{unpackOrderQuery.endTime}
        </if>
        ORDER BY st.LastUpdateTime desc,os.BatchNo,st.Location_Id
    </select>
    <select id="findSownOrderItems"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.soworder.SownOrderItemDTO">
        select
        OutStockOrder_Id as outStockOrderId, RefOrderNo,
        SkuCount as sownSkuCount, PackageAmount as sownPackageAmount, UnitAmount as sownUnitAmount
        from soworder
        where SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR} and Org_Id = #{orgId,jdbcType=INTEGER}
    </select>
    <select id="findSowLocationByNos"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowLocationInfoPO">
        SELECT
        so.Org_Id AS orgId,
        so.SowTask_Id AS sowTaskId,
        so.SowTaskNo,
        so.OutStockOrder_Id AS outStockOrderId,
        so.RefOrderNo,
        so.SowOrderSequence,
        st.Location_Id AS sowLocationId,
        st.LocationName AS sowLocationName
        FROM
        soworder so
        WHERE so.Org_Id = #{orgId, jdbcType=INTEGER}
        AND so.SowTaskNo IN
        <foreach collection="sowTaskNos" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="listBySowTaskNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from soworder
        where Org_Id = #{orgId,jdbcType=INTEGER}
        and SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR}
    </select>

    <update id="batchUpdateLocation">
        UPDATE soworder
        SET LocationId =
        CASE Id
        <foreach collection="updateSowOrderPOS" item="item">
            WHEN #{item.id,jdbcType=INTEGER}
            THEN #{item.locationId,jdbcType=BIGINT}
        </foreach>
        END
        , LocationName =
        CASE Id
        <foreach collection="updateSowOrderPOS" item="item">
            WHEN #{item.id,jdbcType=INTEGER}
            THEN #{item.locationName,jdbcType=VARCHAR}
        </foreach>
        END, LastUpdateUser= #{lastUpdateUser,jdbcType=VARCHAR}
        WHERE Org_Id = #{orgId,jdbcType=INTEGER}
        AND Id IN
        <foreach collection="updateSowOrderPOS" item="item" index="index" open="(" separator="," close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="listSowingPrintItems"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowProductItemDTO">
        SELECT
        osi.Outstockorder_Id AS outStockOrderId,
        osi.id AS outStockOrderItemId,
        osi.SkuId,
        osi.ProductName,
        osi.SpecName,
        osi.SpecQuantity,
        osi.PackageName,
        osi.PackageCount,
        osi.UnitName,
        osi.UnitCount,
        osi.LocationId AS pickLocationId,
        osi.LocationName AS pickLocationName,
        so.SowOrderSequence AS containerNo,
        osi.UnitTotalCount,
        so.SowTaskNo,
        so.Remark AS shopName,
        so.LocationId AS containerLocationId,
        so.LocationName AS containerLocationName,
        osi.Source,
        osi.Channel,
        osi.ProductSpecification_Id AS productSpecificationId
        FROM
        soworder so
        INNER JOIN outstockorderitem osi ON osi.SowOrder_Id = so.Id
        AND osi.Org_id = so.Org_Id
        WHERE so.Org_Id = #{orgId,jdbcType=INTEGER}
        AND so.SowTaskNo IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="listSowAddressBySowTaskNo"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowAddressDTO">
        SELECT
        DISTINCT osi.SowOrder_Id AS sowOrderId,
        osi.SowTaskNo,
        so.SowOrderSequence AS containerNo,
        so.Remark AS shopName,
        bt.ToLocation_Id AS toLocationId,
        bt.ToLocationName,
        so.LocationId,
        so.LocationName,
        os.RouteName
        FROM
        outstockorderitem osi
        INNER JOIN batchtask bt ON bt.id = osi.Batchtask_Id
        INNER JOIN soworder so ON so.Id = osi.SowOrder_Id
        INNER JOIN outstockorder os ON osi.Outstockorder_Id = os.id
        WHERE
        osi.SowTaskNo = #{query.sowTaskNo,jdbcType=VARCHAR}
        AND osi.Org_id = #{query.orgId,jdbcType=INTEGER}
        <if test="query.locationId != null ">
            AND so.LocationId = #{query.locationId,jdbcType=BIGINT}
        </if>
        <if test="query.locationName != null and query.locationName != '' ">
            AND so.LocationName = #{query.locationName,jdbcType=VARCHAR}
        </if>
    </select>

    <update id="updateSownAmount">
        UPDATE soworder
        SET SownSkuCount=
        CASE SowOrderSequence
        <foreach collection="sownOrderItemDTOS" item="sownOrderItemDTO">
            WHEN #{sownOrderItemDTO.sowOrderSequence,jdbcType=INTEGER}
            THEN #{sownOrderItemDTO.sownSkuCount,jdbcType=INTEGER}
        </foreach>
        END
        , SownPackageAmount=
        CASE SowOrderSequence
        <foreach collection="sownOrderItemDTOS" item="sownOrderItemDTO">
            WHEN #{sownOrderItemDTO.sowOrderSequence,jdbcType=INTEGER}
            THEN #{sownOrderItemDTO.sownPackageAmount,jdbcType=DECIMAL}
        </foreach>
        END
        , SownUnitAmount=
        CASE SowOrderSequence
        <foreach collection="sownOrderItemDTOS" item="sownOrderItemDTO">
            WHEN #{sownOrderItemDTO.sowOrderSequence,jdbcType=INTEGER}
            THEN #{sownOrderItemDTO.sownUnitAmount,jdbcType=DECIMAL}
        </foreach>
        END
        , LastUpdateUser= #{lastUpdateUser,jdbcType=VARCHAR}
        WHERE SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR}
        AND Org_Id = #{orgId,jdbcType=INTEGER}
        AND SowOrderSequence IN
        <foreach collection="sownOrderItemDTOS" item="sownOrderItemDTO" index="index" open="(" separator="," close=")">
            #{sownOrderItemDTO.sowOrderSequence,jdbcType=INTEGER}
        </foreach>
    </update>

    <select id="listSowProductItem"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowProductItemDTO">
        SELECT
        osi.Outstockorder_Id AS outStockOrderId,
        osi.id AS outStockOrderItemId,
        osi.SkuId,
        osi.ProductName,
        osi.SpecName,
        osi.SpecQuantity,
        osi.PackageName,
        osi.PackageCount,
        osi.UnitName,
        osi.UnitCount,
        bti.LocationId AS pickLocationId,
        bti.LocationName AS pickLocationName,
        so.SowOrderSequence AS containerNo,
        osi.UnitTotalCount,
        so.SowTaskNo,
        so.Remark AS shopName,
        so.LocationId AS containerLocationId,
        so.LocationName AS containerLocationName,
        osi.Source,
        osi.Channel,
        osi.ProductSpecification_Id AS productSpecificationId,
        bt.ToLocation_Id AS toLocationId,
        bt.ToLocationName,
        st.State
        FROM
        soworder so
        INNER JOIN sowtask st ON st.SowTaskNo = so.SowTaskNo
        AND st.Org_id = so.Org_Id
        INNER JOIN outstockorderitem osi ON osi.SowOrder_Id = so.Id
        AND osi.Org_id = so.Org_Id
        INNER JOIN batchtaskitem bti ON osi.BatchTaskItem_Id = bti.id
        AND bti.Org_id = so.Org_Id
        INNER JOIN batchtask bt ON osi.Batchtask_Id = bt.id
        AND bt.Org_id = so.Org_Id
        WHERE so.Org_Id = #{query.orgId,jdbcType=INTEGER}
        <if test="query.sowTaskNo != null and query.sowTaskNo != '' ">
            AND so.SowTaskNo = #{query.sowTaskNo,jdbcType=VARCHAR}
        </if>
        <if test="query.containerNo != null ">
            AND so.SowOrderSequence = #{query.containerNo,jdbcType=INTEGER}
        </if>
        <if test="query.locationId != null ">
            AND so.LocationId = #{query.locationId,jdbcType=BIGINT}
        </if>
        <if test="query.locationName != null and query.locationName != '' ">
            AND so.LocationName = #{query.locationName,jdbcType=VARCHAR}
        </if>
        <if test="query.state != null ">
            AND st.State = #{query.state,jdbcType=TINYINT}
        </if>
        <if test="query.packageState != null ">
            AND so.PackageState = #{query.packageState,jdbcType=TINYINT}
        </if>
    </select>

    <!--  <select id="findByOrderNos" resultType="com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderDTO">
        select so.Id, so.Org_Id as orgId, so.Warehouse_Id as warehouseId, so.SowTask_Id as sowTaskId, so.SowTaskNo, so.OutStockOrder_Id as outStockOrderId, so.RefOrderNo, so.SkuCount,
        so.PackageAmount, so.UnitAmount, so.CreateUser, so.LastUpdateUser, so.CreateTime, so.LastUpdateTime,
        so.SownSkuCount, so.SownPackageAmount, so.SownUnitAmount, so.SowOrderSequence, so.Remark, st.Location_Id as sowLocationId,
        st.LocationName as sowLocationName
        from soworder so left join sowtask st on so.SowTask_Id = st.Id
        where so.Org_Id = #{orgId,jdbcType=INTEGER}
        and so.RefOrderNo in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </select>-->


    <select id="findByOrderNos" resultType="com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO">
        select
        st.Id, st.Org_id, st.Warehouse_Id, st.Batch_id, st.BatchNo, st.SowTaskNo, st.SowTaskName, st.State,
        st.OrderCount,
        st.SkuCount, st.PackageAmount, st.UnitAmount, st.Location_Id, st.LocationName, st.OperationMode,
        st.CreateTime, st.CreateUser, st.LastUpdateTime, st.LastUpdateUser
        from soworder so
        inner join sowtask st on so.SowTask_Id = st.Id
        where so.Org_id = #{orgId,jdbcType=INTEGER}
        and st.OperationMode = 1
        <if test="list != null and list.size >0 ">
            and so.RefOrderNo in
            <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="getMaxSowOrderSequenceByDate" resultType="java.lang.Integer">
        select max(SowOrderSequence)
        from soworder
        where Org_Id = #{orgId,jdbcType=INTEGER}
        and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and CreateTime <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        and CreateTime <![CDATA[ < ]]> #{endTime,jdbcType=TIMESTAMP}
    </select>

    <select id="findByBatchNos" resultMap="BaseResultMap">
        select
        so.Id, so.Org_Id, so.Warehouse_Id, so.SowTask_Id, so.SowTaskNo, so.OutStockOrder_Id, so.RefOrderNo, so.SkuCount,
        so.PackageAmount, so.UnitAmount, so.CreateUser, so.LastUpdateUser, so.CreateTime, so.LastUpdateTime,
        so.SownSkuCount, so.SownPackageAmount, so.SownUnitAmount, so.SowOrderSequence, so.Remark, so.LocationId,
        so.LocationName
        from soworder so
        inner join outstockorder os on os.id = so.OutStockOrder_Id
        where os.BatchNo in
        <foreach collection="batchNos" item="batchNo" separator="," open="(" close=")">
            #{batchNo}
        </foreach>
    </select>

    <delete id="deleteByIds">
        delete from soworder
        where Org_Id = #{orgId,jdbcType=INTEGER}
        and Id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="findBySowTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from soworder
        where sowTask_Id = #{sowTaskId,jdbcType=BIGINT}
    </select>

    <select id="findFixSowOrderList" resultMap="BaseResultMap">
        select b.sowordersequence,b.sowTask_Id as sowTask_Id,count(b.sowTask_Id)
        from sowtask a
        inner join soworder b
        on a.Id = b.sowtask_id where
        a.createtime between #{timeS} and #{timeE}
        and a.sowTaskType not in (3, 4)
        and a.state in (0,1)
        <if test="warehouseIds != null and warehouseIds.size > 0">
            and a.warehouse_id in
            <foreach collection="warehouseIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="sowTaskIds != null and sowTaskIds.size > 0">
            and a.Id in
            <foreach collection="sowTaskIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        group by b.sowordersequence,b.sowtask_id having count(b.sowTask_Id) >= 2;

    </select>

</mapper>