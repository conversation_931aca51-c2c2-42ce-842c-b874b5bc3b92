package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/25
 */
public class SplitBatchTaskByOrderTypeBO {
    /**
     * 调拨、内配拣货任务信息
     */
    private List<WaveCreateDTO> allotWaveCreateDTO;
    /**
     * 普通订单拣货任务信息
     */
    private List<WaveCreateDTO> normalWaveCreateDTO;
    /**
     * 有混合批次库存的促销单，单独创建按订单拣货的任务任务
     */
    private List<WaveCreateDTO> promotionWaveCreateDTO;

    public SplitBatchTaskByOrderTypeBO() {}

    public SplitBatchTaskByOrderTypeBO(List<WaveCreateDTO> allotWaveCreateDTO,
        List<WaveCreateDTO> normalWaveCreateDTO) {
        this.allotWaveCreateDTO = allotWaveCreateDTO;
        this.normalWaveCreateDTO = normalWaveCreateDTO;
    }

    /**
     * 获取 调拨、内配拣货任务信息
     *
     * @return allotWaveCreateDTO 调拨、内配拣货任务信息
     */
    public List<WaveCreateDTO> getAllotWaveCreateDTO() {
        return this.allotWaveCreateDTO;
    }

    /**
     * 设置 调拨、内配拣货任务信息
     *
     * @param allotWaveCreateDTO 调拨、内配拣货任务信息
     */
    public void setAllotWaveCreateDTO(List<WaveCreateDTO> allotWaveCreateDTO) {
        this.allotWaveCreateDTO = allotWaveCreateDTO;
    }

    /**
     * 获取 普通订单拣货任务信息
     *
     * @return normalWaveCreateDTO 普通订单拣货任务信息
     */
    public List<WaveCreateDTO> getNormalWaveCreateDTO() {
        return this.normalWaveCreateDTO;
    }

    /**
     * 设置 普通订单拣货任务信息
     *
     * @param normalWaveCreateDTO 普通订单拣货任务信息
     */
    public void setNormalWaveCreateDTO(List<WaveCreateDTO> normalWaveCreateDTO) {
        this.normalWaveCreateDTO = normalWaveCreateDTO;
    }

    public static SplitBatchTaskByOrderTypeBO getDefault(List<WaveCreateDTO> allotWaveCreateDTO,
                                                         List<WaveCreateDTO> normalWaveCreateDTO) {
        return new SplitBatchTaskByOrderTypeBO(allotWaveCreateDTO, normalWaveCreateDTO);
    }

    /**
     * 获取 有混合批次库存的促销单，单独创建按订单拣货的任务任务
     *
     * @return promotionWaveCreateDTO 有混合批次库存的促销单，单独创建按订单拣货的任务任务
     */
    public List<WaveCreateDTO> getPromotionWaveCreateDTO() {
        return this.promotionWaveCreateDTO;
    }

    /**
     * 设置 有混合批次库存的促销单，单独创建按订单拣货的任务任务
     *
     * @param promotionWaveCreateDTO 有混合批次库存的促销单，单独创建按订单拣货的任务任务
     */
    public void setPromotionWaveCreateDTO(List<WaveCreateDTO> promotionWaveCreateDTO) {
        this.promotionWaveCreateDTO = promotionWaveCreateDTO;
    }

    public List<WaveCreateDTO> getWaveCreateDTO() {
        List<WaveCreateDTO> totalWaveCreateDTOList = new ArrayList<WaveCreateDTO>();
        if (!CollectionUtils.isEmpty(normalWaveCreateDTO)) {
            totalWaveCreateDTOList.addAll(normalWaveCreateDTO);
        }

        if (!CollectionUtils.isEmpty(promotionWaveCreateDTO)) {
            totalWaveCreateDTOList.addAll(promotionWaveCreateDTO);
        }

        return totalWaveCreateDTOList;
    }

}
