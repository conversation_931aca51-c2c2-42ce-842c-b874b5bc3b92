package com.yijiupi.himalaya.supplychain.waves.batch;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOrderDetailQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOrderInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.MergePickOrderInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.MergePickOrderInfoResultDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.OrderPrintInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.*;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderByProductSO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderItemProductSO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderSearchSO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderWaitDeliverySO;

import java.util.List;
import java.util.Map;

/**
 * 波次订单信息
 *
 * <AUTHOR> 2018/3/16
 */
public interface IBatchOrderInfoQueryService {

    /**
     * 查询未拣货完成订单（非已出库和已拣货状态）
     *
     * @param warehouseId
     * @param lstOrderNo
     * @return
     */
    List<String> findNotCompleteBatchOrder(Integer warehouseId, List<String> lstOrderNo);

    /**
     * 根据波次编号查询该波次关联的订单列表
     *
     * @param batchNo
     * @return
     */
    PageList<BatchOrderInfoDTO> findBatchOrderInfoListByBatchNo(String batchNo, Integer currentPage, Integer pageSize);

    /**
     * 根据波次编号查询该波次关联的订单列表
     *
     * @param queryDTO
     * @return
     */
    PageList<BatchOrderInfoDTO> findBatchOrderDetail(BatchOrderDetailQueryDTO queryDTO);

    /**
     * 查询可用的订单
     *
     * @param dto
     * @return
     */
    PageList<OutStockOrderDTO> findEnableOutStockOrderList(OutStockOrderSearchSO dto);

    /**
     * 查询出库单列表
     */
    PageList<OutStockOrderDTO> findOutStockOrderList(OutStockOrderSearchSO dto);

    /**
     * 查询出库单列表
     */
    PageList<OutStockOrderDTO> findOutStockOrderList(OutStockOrderSearchSO dto, boolean descending);

    /**
     * 查询订单打印数据
     */
    List<OrderPrintInfoDTO> findOrderPrintInfo(Integer orgId, Integer warehouseId);

    /**
     * 查找出库单待出库的产品集合
     */
    List<Long> listOutStockOrderProduct(OutStockOrderItemProductSO so);

    /**
     * 查找待出库订单项列表
     */
    PageList<OutStockOrderWaitDeliveryDTO> listOutStockOrderItemWaitDelivery(OutStockOrderWaitDeliverySO so);

    /**
     * 根据波次id查询订单产品明细
     */
    PageList<OutStockOrderByProductDTO> listOrderProductByBatchId(OutStockOrderByProductSO so);

    /**
     * 根据波次id查询产品汇总
     */
    PageList<OutStockOrderByProductDTO> listProductGroupByBatchId(OutStockOrderByProductSO so);

    /**
     * 查询待出库的延迟配送订单
     */
    List<OutStockOrderDTO> findDelayOrderByWarehouseId(Integer warehouseId);

    /**
     * 获取产品分配波次的数量
     *
     * @return
     */
    Map<Long, ProductAllotTypeDTO> getProductAllotCountMap(Integer cityId, Integer warehouseId,
        List<Long> productSkuIds);

    /***
     * 查询出库单（详情）列表
     *
     * @param dto
     * @param descending 是否根据订单创建时间降序
     * @return
     */
    PageList<OutStockOrderDTO> findOutStockOrderItemList(OutStockOrderSearchSO dto, boolean descending);

    /**
     * 查询出库单项变更记录
     *
     * @return
     */
    PageList<OutStockOrderItemChangeRecordDTO> listOrderItemChangeRecord(OutStockOrderItemChangeRecordSO recordSO);

    /**
     * 根据订单ID获取订单明细
     *
     * @return
     */
    List<OutStockOrderItemDTO> listOutStockOrderItemByOrderId(Long orderId);

    /**
     * 查询订单的快递面单信息
     */
    String findExpressBillByOrderNo(Integer cityId, Integer warehouseId, String orderNo);

    /**
     * 查询出库单的分拣顺序
     */
    List<OrderPickSequenceDTO> listPickSequence(OrderPickSequenceQueryDTO queryDTO);

    /**
     * 查询出库单列表
     */
    PageList<OutStockOrderDTO> pageListOrder(OutStockOrderSearchSO dto);

    /**
     * 根据波次编号查询该波次关联的订单列表
     *
     * @param outStockOrderSearchSO
     * @return
     */
    PageList<BatchOrderInfoDTO> querySaasBatchDetails(OutStockOrderSearchSO outStockOrderSearchSO);

    /**
     * 查询出库单列表(商户)
     */
    PageList<OutStockOrderDTO> findOutStockOrderListNew(OutStockOrderSearchSO dto);

    /***
     * 根据订单号或者面单号查询订单(商户)
     *
     * @param dto
     * @param dto
     * @return
     */
    PageList<OutStockOrderPagesDTO> getOrderByCode(OrderQueryDTO dto);

    /**
     * 扫单复核查询
     *
     * @return
     */
    ScanOrderForReviewDTO queryScanOrderInfoFroReview(ScanOrderForReviewQueryDTO queryDTO);

    /**
     * 托盘复核查询
     *
     * @return
     */
    PalletReviewInfoDTO queryPalletReviewInfo(PalletReviewQueryDTO queryDTO);

    /**
     * 查询是否是合并拣货
     *
     * @param queryDTO
     * @return
     */
    List<MergePickOrderInfoResultDTO> queryMergePickOrderInfo(MergePickOrderInfoQueryDTO queryDTO);

}
