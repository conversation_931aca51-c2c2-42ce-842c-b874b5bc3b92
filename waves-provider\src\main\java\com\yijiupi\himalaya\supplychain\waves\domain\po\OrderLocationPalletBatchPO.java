package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/30
 */
public class OrderLocationPalletBatchPO {

    private List<Long> ids;

    private Long locationId;

    private String locationName;

    private String lastUpdateUser;

    /**
     * 获取
     *
     * @return ids
     */
    public List<Long> getIds() {
        return this.ids;
    }

    /**
     * 设置
     *
     * @param ids
     */
    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    /**
     * 获取
     *
     * @return locationId
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置
     *
     * @param locationId
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取
     *
     * @return locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置
     *
     * @param locationName
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取
     *
     * @return lastUpdateUser
     */
    public String getLastUpdateUser() {
        return this.lastUpdateUser;
    }

    /**
     * 设置
     *
     * @param lastUpdateUser
     */
    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }
}
