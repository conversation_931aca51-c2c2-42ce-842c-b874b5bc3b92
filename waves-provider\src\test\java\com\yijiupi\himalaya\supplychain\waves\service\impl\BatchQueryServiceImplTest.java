package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.util.Collections;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemLackDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;

/**
 * <AUTHOR> 2018/3/16
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class BatchQueryServiceImplTest {

    @Autowired
    private BatchQueryServiceImpl waveQueryService;

    /**
     * 查询波次列表
     *
     * @throws Exception
     */
    @Test
    public void findBatchOrderList() throws Exception {
        BatchQueryDTO batchQueryDTO = new BatchQueryDTO();
        // batchQueryDTO.setBatchName("线路");
        batchQueryDTO.setBatchNo("BC9991418042400004");
        batchQueryDTO.setWarehouseId(0);
        // batchQueryDTO.setRefOrderNo("12345647");
        // batchQueryDTO.setState((byte) 0);
        // batchQueryDTO.setStartTime(new Date());
        PageList<BatchDTO> batchOrderList = waveQueryService.findBatchOrderList(batchQueryDTO);
    }

    @Test
    public void findBatchOrderLoctionList() {
        List<OutStockOrderLocationDTO> batchOrderLoctionList =
            waveQueryService.findBatchOrderLoctionList(Collections.singletonList(9991807180901340L));

    }

    @Test
    public void listLackItemByBatchNos() {
        List<BatchTaskItemLackDTO> bc999118060700031 =
            waveQueryService.listLackItemByBatchNos(Collections.singletonList("BC999118060700031"));

    }
}