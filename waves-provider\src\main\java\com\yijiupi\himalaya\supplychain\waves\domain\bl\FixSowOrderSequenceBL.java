package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.FixSowOrderSequenceDTO;

/**
 * <AUTHOR>
 * @title: FixSowOrderSequenceBL
 * @description:
 * @date 2022-10-25 15:42
 */
@Service
public class FixSowOrderSequenceBL {

    @Autowired
    private SowOrderMapper sowOrderMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private SowManagerBL sowManagerBL;

    /**
     * 修复sowOrder上的SowOrderSequence
     *
     * @param dto
     */
    @Async
    public void fixSowOrderSequence(FixSowOrderSequenceDTO dto) {
        AssertUtils.hasText(dto.getTimeS(), "开始时间不能为空");
        AssertUtils.hasText(dto.getTimeE(), "结束时间不能为空");
        List<SowOrderPO> sowOrderList = sowOrderMapper.findFixSowOrderList(dto);
        if (CollectionUtils.isEmpty(sowOrderList)) {
            return;
        }
        List<Long> sowTaskIds =
            sowOrderList.stream().map(SowOrderPO::getSowTaskId).distinct().collect(Collectors.toList());

        for (Long sowTaskId : sowTaskIds) {
            sowManagerBL.fixSowOrderSequence(sowTaskId);
        }

    }

}
