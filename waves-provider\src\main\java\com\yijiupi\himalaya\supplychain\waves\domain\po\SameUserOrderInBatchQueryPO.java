package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/18
 */
public class SameUserOrderInBatchQueryPO {

    private Integer addressId;

    private Integer warehouseId;

    private String orderCreateTime;

    private List<Byte> stateList;

    private Byte orderFeatureType;

    private Integer orderSequence;

    private List<Integer> addressIdList;

    /**
     * 获取
     *
     * @return addressId
     */
    public Integer getAddressId() {
        return this.addressId;
    }

    /**
     * 设置
     *
     * @param addressId
     */
    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    /**
     * 获取
     *
     * @return warehouseId
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置
     *
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取
     *
     * @return orderCreateTime
     */
    public String getOrderCreateTime() {
        return this.orderCreateTime;
    }

    /**
     * 设置
     *
     * @param orderCreateTime
     */
    public void setOrderCreateTime(String orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    /**
     * 获取
     *
     * @return orderFeatureType
     */
    public Byte getOrderFeatureType() {
        return this.orderFeatureType;
    }

    /**
     * 设置
     *
     * @param orderFeatureType
     */
    public void setOrderFeatureType(Byte orderFeatureType) {
        this.orderFeatureType = orderFeatureType;
    }

    /**
     * 获取
     *
     * @return stateList
     */
    public List<Byte> getStateList() {
        return this.stateList;
    }

    /**
     * 设置
     *
     * @param stateList
     */
    public void setStateList(List<Byte> stateList) {
        this.stateList = stateList;
    }

    /**
     * 获取
     *
     * @return orderSequence
     */
    public Integer getOrderSequence() {
        return this.orderSequence;
    }

    /**
     * 设置
     *
     * @param orderSequence
     */
    public void setOrderSequence(Integer orderSequence) {
        this.orderSequence = orderSequence;
    }

    /**
     * 获取
     *
     * @return addressIdList
     */
    public List<Integer> getAddressIdList() {
        return this.addressIdList;
    }

    /**
     * 设置
     *
     * @param addressIdList
     */
    public void setAddressIdList(List<Integer> addressIdList) {
        this.addressIdList = addressIdList;
    }
}
