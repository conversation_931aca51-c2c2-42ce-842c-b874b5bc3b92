package com.yijiupi.himalaya.supplychain.waves.service.impl;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOrderInfoDTO;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class BatchOrderInfoQueryServiceImplTest {

    @Autowired
    private BatchOrderInfoQueryServiceImpl batchOrderInfoQueryService;

    /**
     * 根据波次编号查询该波次关联的订单列表
     *
     * @throws Exception
     */
    @Test
    public void findBatchOrderInfoListByBatchNo() throws Exception {
        PageList<BatchOrderInfoDTO> batchOrderInfoListByBatchNo =
            batchOrderInfoQueryService.findBatchOrderInfoListByBatchNo("BC999118041500001", 1, 20);
    }
    // @Test
    // public void testBatchOrderProcess() throws Exception {
    // Map<Long, List<BatchLocationInfoDTO>> result = Maps.newHashMap();
    // BatchLocationInfoDTO b1 = new BatchLocationInfoDTO();
    // b1.setTotalCount(2);
    // BatchLocationInfoDTO b2 = new BatchLocationInfoDTO();
    // b1.setTotalCount(3);
    // List<BatchLocationInfoDTO> list1 = Lists.newArrayList(b1,b2);
    // result.put(Long.valueOf(123),list1);
    //
    // result.forEach((key,p)->{
    // Object o = p.get(0);
    // });
    // }

    // public static void main(String[] args) {
    // Map<Long, List<BatchLocationInfoDTO>> result = Maps.newHashMap();
    // BatchLocationInfoDTO b1 = new BatchLocationInfoDTO();
    // b1.setTotalCount(2);
    // b1.setLocationName("测试1");
    // b1.setLocationCategory((byte) 1);
    // b1.setLocationId(1L);
    // BatchLocationInfoDTO b2 = new BatchLocationInfoDTO();
    // b2.setLocationId(2L);
    // b2.setLocationName("测试");
    // b2.setLocationCategory((byte) 1);
    // b2.setTotalCount(6);
    // List<BatchLocationInfoDTO> list1 = Lists.newArrayList(b1, b2);
    // result.put(Long.valueOf(123), list1);
    //
    // // 遍历listitem 根据skuid 生成batskitem
    // List<OutStockOrderItemPO> lstOrderItems = new ArrayList<>();
    // for (int i = 0; i < 2; i++) {
    // OutStockOrderItemPO outStockOrderItemPO = new OutStockOrderItemPO();
    // outStockOrderItemPO.setSkuid(123L);
    // outStockOrderItemPO.setUnittotalcount(3);
    // lstOrderItems.add(outStockOrderItemPO);
    // }
    //
    // Map<Long, List<OutStockOrderItemPO>> collect =
    // lstOrderItems.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid));
    //
    // List<OutStockOrderItemPO> outStockOrderItemPOS = new ArrayList<>();
    // collect.forEach((key, value) -> {
    // List<BatchLocationInfoDTO> batchLocationInfoDTOList = result.get(Long.valueOf(key));
    // Map<Long, BatchLocationInfoDTO> batchLocationInfoMap =
    // batchLocationInfoDTOList.stream().collect(Collectors.toMap(BatchLocationInfoDTO::getLocationId, (p) -> p));
    // if (!CollectionUtils.isEmpty(batchLocationInfoDTOList)) {
    // value.forEach(e -> {
    //
    // // 进行计算 得到一个<LocationId,BatchLocationInfoDTO>得map map得size是关键 代表这一个订单项需要拆成几个新的订单项
    // // LinkedHashMap<Long, BatchLocationInfoDTO> holdIdNumMap = new LinkedHashMap<>();
    // int needReleaseSumNum = e.getUnittotalcount();
    // for (Map.Entry<Long, BatchLocationInfoDTO> entry : batchLocationInfoMap.entrySet()) {
    // BatchLocationInfoDTO batchLocationInfoDTO = entry.getValue();
    // if (needReleaseSumNum <= 0) {
    // break;
    // }
    // if (batchLocationInfoDTO.getTotalCount() <= 0) {
    // continue;
    // }
    // int releaseNum = Math.min(needReleaseSumNum, batchLocationInfoDTO.getTotalCount());
    // needReleaseSumNum -= releaseNum;
    //
    // OutStockOrderItemPO outStockOrderItemPO = new OutStockOrderItemPO();
    // BeanUtils.copyProperties(e, outStockOrderItemPO);
    // outStockOrderItemPO.setLocationId(batchLocationInfoDTO.getLocationId());
    // outStockOrderItemPO.setLocationName(batchLocationInfoDTO.getLocationName());
    // outStockOrderItemPO.setLocationCategory(Integer.valueOf(batchLocationInfoDTO.getLocationCategory()));
    // outStockOrderItemPOS.add(outStockOrderItemPO);
    // // if (batchLocationInfoDTOList.get(batchLocationInfoDTOList.size() -
    // 1).getLocationId().equals(batchLocationInfoDTO.getLocationId())) {
    // // holdIdNumMap.put(entry.getKey(), batchLocationInfoDTO);
    // // } else {
    // // holdIdNumMap.put(entry.getKey(), batchLocationInfoDTO);
    // // }
    // batchLocationInfoDTO.setTotalCount(batchLocationInfoDTO.getTotalCount() - releaseNum);
    // batchLocationInfoMap.put(entry.getKey(), batchLocationInfoDTO);
    // }
    // // // 遍历map 生成新的outStockOrderItemPO
    // // holdIdNumMap.forEach((k, v) -> {
    // // OutStockOrderItemDTO outStockOrderItemPO = new OutStockOrderItemDTO();
    // // BeanUtils.copyProperties(e, outStockOrderItemPO);
    // // outStockOrderItemPO.setLocationId(v.getLocationId());
    // // outStockOrderItemPO.setLocationName(v.getLocationName());
    // // outStockOrderItemPO.setLocationIdentify(Integer.valueOf(v.getLocationIdentify()));
    // // outStockOrderItemPOS.add(outStockOrderItemPO);
    // // });
    // });
    //
    // } else {
    // // 为空
    // }
    // });
    // }

    // public static LinkedHashMap<Long, BatchLocationInfoDTO> getholdIdNumMap(List<BatchLocationInfoDTO>
    // batchLocationInfoDTOList, OutStockOrderItemPO outStockOrderItemPOS) {
    // LinkedHashMap<Long, BatchLocationInfoDTO> holdIdNumMap = new LinkedHashMap<>();
    // int needReleaseSumNum = outStockOrderItemPOS.getUnittotalcount();
    // for (BatchLocationInfoDTO hold : batchLocationInfoDTOList) {
    // if (needReleaseSumNum <= 0) {
    // break;
    // }
    // int releaseNum = Math.min(needReleaseSumNum, hold.getTotalCount());
    // needReleaseSumNum -= releaseNum;
    //
    // if (batchLocationInfoDTOList.get(batchLocationInfoDTOList.size() -
    // 1).getLocationId().equals(hold.getLocationId())) {
    // holdIdNumMap.put(hold.getLocationId(), hold);
    // } else {
    // holdIdNumMap.put(hold.getLocationId(), hold);
    // }
    // }
    // return holdIdNumMap;
    // }
}