package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 单据复核
 */
public class BillReviewPO {
    /**
     * id
     */
    private Long id;

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 单据编号
     */
    private String businessNo;

    /**
     * 单据id
     */
    private String businessId;

    /**
     * 单据类型 0:订单 1:拣货任务
     */
    private Byte businessType;

    /**
     * 复核状态 0:待复核 1:复核中 2:已复核
     */
    private Byte state;

    /**
     * 复核人名称
     */
    private String reviewer;

    /**
     * 复核人id
     */
    private Integer reviewerId;

    /**
     * 应复核大单位数量
     */
    private BigDecimal packageAmount;

    /**
     * 应复核小单位数量
     */
    private BigDecimal unitAmount;

    /**
     * 实际复核大单位数量
     */
    private BigDecimal overPackageAmount;

    /**
     * 实际复核小单位数量
     */
    private BigDecimal overUnitAmount;

    /**
     * 已装箱箱数
     */
    private Integer packagedBoxAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String lastUpdateUser;

    /**
     * 修改时间
     */
    private Date lastUpdateTime;

    /**
     * 关联单据编号
     */
    private String relatedBusinessNo;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    public Integer getReviewerId() {
        return reviewerId;
    }

    public void setReviewerId(Integer reviewerId) {
        this.reviewerId = reviewerId;
    }

    public BigDecimal getPackageAmount() {
        return packageAmount;
    }

    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    public BigDecimal getUnitAmount() {
        return unitAmount;
    }

    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    public BigDecimal getOverPackageAmount() {
        return overPackageAmount;
    }

    public void setOverPackageAmount(BigDecimal overPackageAmount) {
        this.overPackageAmount = overPackageAmount;
    }

    public BigDecimal getOverUnitAmount() {
        return overUnitAmount;
    }

    public void setOverUnitAmount(BigDecimal overUnitAmount) {
        this.overUnitAmount = overUnitAmount;
    }

    public Integer getPackagedBoxAmount() {
        return packagedBoxAmount;
    }

    public void setPackagedBoxAmount(Integer packagedBoxAmount) {
        this.packagedBoxAmount = packagedBoxAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getRelatedBusinessNo() {
        return relatedBusinessNo;
    }

    public void setRelatedBusinessNo(String relatedBusinessNo) {
        this.relatedBusinessNo = relatedBusinessNo;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }
}