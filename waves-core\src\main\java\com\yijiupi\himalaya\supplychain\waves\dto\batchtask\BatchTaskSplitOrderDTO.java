package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 拣货任务待拆分订单
 * 
 * <AUTHOR>
 * @date 2018/7/10 15:30
 */
public class BatchTaskSplitOrderDTO implements Serializable {
    private static final long serialVersionUID = -4122371395920699057L;

    /**
     * 订单编号
     */
    private String refOrderNo;

    /**
     * 商品种类数量
     */
    private Integer skuCount;

    /**
     * 大单位数量
     */
    private BigDecimal packageCount;

    /**
     * 小单位数量
     */
    private BigDecimal unitCount;

    private BigDecimal unitTotalCount;

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }
}
