package com.yijiupi.himalaya.supplychain.waves.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.algorithm.service.MetricsAnalysisService;
import com.yijiupi.himalaya.supplychain.waves.batch.SeedingAlgorithmService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.PickingTaskItemSortingBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.SeedingAlgorithmManageBL;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/12 17:39
 * @Version 1.0
 */
@Service
public class SeedingAlgorithmServiceImpl implements SeedingAlgorithmService {

	@Autowired
	private SeedingAlgorithmManageBL seedingAlgorithmManageBL;
	@Autowired
	private PickingTaskItemSortingBL pickingTaskItemSortingBL;
	@Reference
	private MetricsAnalysisService metricsAnalysisService;

	@Override
	public void replaySowingTaskByBatchNos(List<String> batchNos, Integer orgId, Integer version) {
		seedingAlgorithmManageBL.replaySowingTaskByBatchNos(batchNos, orgId, version);
	}

	@Override
	public void clearSowingTaskReplayData(List<String> batchNos, Integer orgId, Integer version) {
		seedingAlgorithmManageBL.clearSowingTaskReplayData(batchNos, orgId, version);
	}

	@Override
	public void compareHistoricalPickingSorting(List<String> batchTaskNos) {
		pickingTaskItemSortingBL.compareHistoricalPickingSorting(batchTaskNos);
	}

	@Override
	public void printWarehouseGrid(Integer warehouseId, Integer floor) {
		pickingTaskItemSortingBL.printWarehouseGrid(warehouseId, floor);
	}

	@Override
	public void clearAlgoExecutionResult(List<Long> resultIds) {
		metricsAnalysisService.clearAlgoExecutionResult(resultIds);
	}
}
