package com.yijiupi.himalaya.supplychain.controller.sow;

import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskStateEnum;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 播种任务相关
 */
public class SowTaskReceiveParamDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 播种任务编号
     */
    private List<String> sowTaskNos;

    /**
     * 播种人
     */
    private String sower;

    /**
     * 播种人id
     */
    private Integer sowerId;

    /**
     * 集货位名称
     */
    private String locationName;

    /**
     * 播种任务状态(指派的时候不修改状态)
     */
    private Byte state = SowTaskStateEnum.播种中.getType();

    /**
     * 产品条码(可选, 传了就优先查指定条码的播种任务)
     */
    private String code;

    /**
     * 播种任务类型, 0.播种墙播种 1.打包复核 3-二次分拣播种
     */
    private List<Byte> sowTaskType;

    public List<Byte> getSowTaskType() {
        return sowTaskType;
    }

    public void setSowTaskType(List<Byte> sowTaskType) {
        this.sowTaskType = sowTaskType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<String> getSowTaskNos() {
        return sowTaskNos;
    }

    public void setSowTaskNos(List<String> sowTaskNos) {
        this.sowTaskNos = sowTaskNos;
    }

    public String getSower() {
        return sower;
    }

    public void setSower(String sower) {
        this.sower = sower;
    }

    public Integer getSowerId() {
        return sowerId;
    }

    public void setSowerId(Integer sowerId) {
        this.sowerId = sowerId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }
}
