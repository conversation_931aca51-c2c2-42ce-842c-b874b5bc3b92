package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.PackageOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/6/7 13:45
 */
@Component
public class PackageOrderItemConvertor {

    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private SowTaskMapper sowTaskMapper;

    @Reference
    private ILocationService iLocationService;

    public static List<PackageOrderItemDTO> convertPo2Dto(List<PackageOrderItemPO> PackageOrderItemPOs) {
        List<PackageOrderItemDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isEmpty(PackageOrderItemPOs)) {
            return dtos;
        }
        PackageOrderItemPOs.forEach(it -> {
            PackageOrderItemDTO dto = new PackageOrderItemDTO();
            BeanUtils.copyProperties(it, dto);
            if (StringUtils.isNotEmpty(it.getBoxCode())) {
                String[] split = dto.getBoxCode().split(",");
                dto.setBoxCodeList(Arrays.asList(split));
            }
            dtos.add(dto);
        });
        return dtos;
    }

    /**
     * 获取出库位信息
     *
     * @param batchTaskIds
     * @param orgId
     * @return 出库位对象，不会返回空，只会返回里面什么都没有的对象
     */
    public OutStockLocationBO getOutStockLocation(List<String> batchTaskIds, List<OutStockOrderItemPO> orderItemPOList,
        Integer orgId) {

        if (CollectionUtils.isEmpty(orderItemPOList)) {
            return OutStockLocationBO.getInstance();
        }

        Optional<OutStockOrderItemPO> outStockOrderItemPOOptional =
            orderItemPOList.stream().filter(m -> Objects.nonNull(m.getLocationId())).findAny();

        OutStockOrderItemPO outStockOrderItemPO = outStockOrderItemPOOptional.orElse(null);
        if (Objects.nonNull(outStockOrderItemPO)) {
            return new OutStockLocationBO(outStockOrderItemPO.getLocationId(), outStockOrderItemPO.getLocationName());
        }

        if (CollectionUtils.isEmpty(batchTaskIds)) {
            return OutStockLocationBO.getInstance();
        }

        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findBatchTaskByIds(batchTaskIds);

        if (CollectionUtils.isEmpty(batchTaskPOList)) {
            return OutStockLocationBO.getInstance();
        }

        Optional<BatchTaskPO> batchTaskPOOptional =
            batchTaskPOList.stream().filter(m -> Objects.nonNull(m.getToLocationId())).findFirst();

        BatchTaskPO batchTaskPO = batchTaskPOOptional.orElse(null);
        if (Objects.isNull(batchTaskPO)) {
            return OutStockLocationBO.getInstance();
        }

        OutStockLocationBO bo = new OutStockLocationBO(batchTaskPO.getToLocationId(), batchTaskPO.getToLocationName());

        return bo;
    }

    private SowTaskPO getSowTaskInfo(List<BatchTaskPO> batchTaskPOList, Integer orgId) {

        Optional<Long> sowTaskIdOptional = batchTaskPOList.stream().filter(m -> Objects.nonNull(m.getSowTaskId()))
            .map(BatchTaskPO::getSowTaskId).distinct().findFirst();

        Long sowTaskId = sowTaskIdOptional.orElse(null);
        if (Objects.isNull(sowTaskId)) {
            return null;
        }

        SowTaskPO sowTaskPO = sowTaskMapper.getSowTaskById(sowTaskId, orgId);

        return sowTaskPO;
    }

    public static class OutStockLocationBO {
        public Long locationId;
        public String locationName;

        public OutStockLocationBO() {

        }

        public OutStockLocationBO(Long locationId, String locationName) {
            this.locationId = locationId;
            this.locationName = locationName;
        }

        public static OutStockLocationBO getInstance() {
            return new OutStockLocationBO();
        }
    }

    public Integer getPalletNo(Long locationId) {
        if (Objects.isNull(locationId)) {
            return null;
        }
        List<LoactionDTO> loactionDTOList = iLocationService.findLocationByIds(Collections.singletonList(locationId));
        if (CollectionUtils.isEmpty(loactionDTOList)) {
            return null;
        }
        Optional<LoactionDTO> locationOptional = loactionDTOList.stream().findFirst();
        if (locationOptional.isPresent()) {
            return locationOptional.get().getPalletCount();
        }

        return null;
    }

}
