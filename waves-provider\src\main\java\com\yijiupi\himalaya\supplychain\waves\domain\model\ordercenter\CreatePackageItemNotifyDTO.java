package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 装箱时，通知订单中台消息体DTO
 * 
 * <AUTHOR>
 * @Date 2022/6/1
 */
public class CreatePackageItemNotifyDTO implements Serializable {
    private static final long serialVersionUID = 2901523850949456825L;
    /**
     * oms订单项ID
     */
    private Long orderItemId;
    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;
    /**
     * 操作人Id
     */
    private String optUserId;
    /**
     * 装箱时间
     */
    private Date createPackageTime;

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public String getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(String optUserId) {
        this.optUserId = optUserId;
    }

    public Date getCreatePackageTime() {
        return createPackageTime;
    }

    public void setCreatePackageTime(Date createPackageTime) {
        this.createPackageTime = createPackageTime;
    }
}
