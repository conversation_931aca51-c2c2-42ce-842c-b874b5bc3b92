package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor;

import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.enums.PassagePickTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.SourceType;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.PickingTypeEnum;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: WaveCreateDTOSplitConvertor
 * @description:
 * @date 2023-06-05 10:53
 */
public class WaveCreateDTOSplitConvertor {

    public static List<WaveCreateDTO> splitWaveByWarehouse(List<OutStockOrderPO> normalOrders,
                                                           WavesStrategyBO wavesStrategyDTO, ProcessBatchDTO processBatchDTO) {
        Map<Integer, List<OutStockOrderPO>> orderMap = normalOrders.stream()
                .collect(Collectors.groupingBy(p -> p.getToWarehouseId() == null ? 0 : p.getToWarehouseId()));
        List<WaveCreateDTO> waveCreateDTOList = new ArrayList<>();
        orderMap.forEach((toWarehouseId, warehouseOrderList) -> {
            List<WaveCreateDTO> list = splitByAwardOutOrder(wavesStrategyDTO, processBatchDTO, warehouseOrderList);
            if (!CollectionUtils.isEmpty(list)) {
                waveCreateDTOList.addAll(list);
            }
        });

        return waveCreateDTOList;
    }

    public static List<WaveCreateDTO> splitSupplyAgainList(List<WaveCreateDTO> waveCreateList) {
        List<WaveCreateDTO> splitWaveCreateList = new ArrayList<>();

        List<Byte> abnormalSourceTypes =
                Arrays.asList(SourceType.SUPPLY_AGAIN.getValue(), SourceType.WRONG_DELIVERY.getValue());
        for (WaveCreateDTO waveCreateDTO : waveCreateList) {
            // 正常订单
            List<OutStockOrderPO> normalOrderList = waveCreateDTO.getOrders().stream()
                    .filter(o -> !abnormalSourceTypes.contains(o.getOrderSourceType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(normalOrderList)) {
                WaveCreateDTO normalCreateDTO = new WaveCreateDTO();
                BeanUtils.copyProperties(waveCreateDTO, normalCreateDTO);
                normalCreateDTO.setWavesStrategyDTO(normalCreateDTO.getWavesStrategyDTO());
                normalCreateDTO.setOrders(normalOrderList);
                splitWaveCreateList.add(normalCreateDTO);
            }

            // 漏货补发
            List<OutStockOrderPO> pickLeakOrderList = waveCreateDTO.getOrders().stream()
                    .filter(m -> Objects.equals(m.getOrderSourceType(), SourceType.SUPPLY_AGAIN.getValue()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(pickLeakOrderList)) {
                WaveCreateDTO supplyAgainCreateDTO = new WaveCreateDTO();
                BeanUtils.copyProperties(waveCreateDTO, supplyAgainCreateDTO);
                WavesStrategyBO wavesStrategyBO = new WavesStrategyBO();
                BeanUtils.copyProperties(waveCreateDTO.getWavesStrategyDTO(), wavesStrategyBO);
                wavesStrategyBO.setPickingType(PickingTypeEnum.订单拣货.getType());
                wavesStrategyBO.setPassPickType(PassagePickTypeEnum.不开启.getType());
                supplyAgainCreateDTO.setWavesStrategyDTO(wavesStrategyBO);
                supplyAgainCreateDTO.setOrders(pickLeakOrderList);
                splitWaveCreateList.add(supplyAgainCreateDTO);
            }

            // 错发换过
            List<OutStockOrderPO> wrongDeliveryOrderList = waveCreateDTO.getOrders().stream()
                    .filter(m -> Objects.equals(m.getOrderSourceType(), SourceType.WRONG_DELIVERY.getValue()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(wrongDeliveryOrderList)) {
                WaveCreateDTO wrongDeliveryCreateDTO = new WaveCreateDTO();
                BeanUtils.copyProperties(waveCreateDTO, wrongDeliveryCreateDTO);
                WavesStrategyBO wavesStrategyBO = new WavesStrategyBO();
                BeanUtils.copyProperties(waveCreateDTO.getWavesStrategyDTO(), wavesStrategyBO);
                wavesStrategyBO.setPickingType(PickingTypeEnum.订单拣货.getType());
                wavesStrategyBO.setPassPickType(PassagePickTypeEnum.不开启.getType());
                wrongDeliveryCreateDTO.setWavesStrategyDTO(wavesStrategyBO);
                wrongDeliveryCreateDTO.setOrders(wrongDeliveryOrderList);
                splitWaveCreateList.add(wrongDeliveryCreateDTO);
            }
        }

        return splitWaveCreateList;
    }

    /**
     * 开启实时分拣，按片区id拆分拣货任务
     *
     * @param wavesStrategyDTO
     * @param processBatchDTO
     * @param waveCreateDTOList
     * @param getOrderFeatureMap
     * @return
     */
    public static List<WaveCreateDTO> splitWaveByTrayLocation(WavesStrategyBO wavesStrategyDTO,
        ProcessBatchDTO processBatchDTO, List<WaveCreateDTO> waveCreateDTOList,
        Function<List<Long>, Map<Long, List<Byte>>> getOrderFeatureMap) {
        if (BooleanUtils.isFalse(isRealTimePickingByOrderNeedSplit(wavesStrategyDTO, processBatchDTO))) {
            return waveCreateDTOList;
        }

        List<WaveCreateDTO> newCreateDTOList = new ArrayList<>();

        for (WaveCreateDTO waveCreateDTO : waveCreateDTOList) {
            List<OutStockOrderPO> orderList = waveCreateDTO.getOrders();

            List<OutStockOrderPO> inAreaList = getInAreaList(wavesStrategyDTO, orderList);
            List<OutStockOrderPO> notInAreaList = getNotInAreaList(wavesStrategyDTO, orderList);

            if (!CollectionUtils.isEmpty(inAreaList)) {
                Map<String, List<OutStockOrderPO>> splitOrderMap =
                    splitBatchTaskByAddressAndFeature(inAreaList, getOrderFeatureMap);

                splitOrderMap.forEach((k, orders) -> {
                    WaveCreateDTO newWaveCreateDTO = new WaveCreateDTO();
                    BeanUtils.copyProperties(waveCreateDTO, newWaveCreateDTO);
                    newWaveCreateDTO.setOrders(orders);
                    WavesStrategyBO wavesStrategyBO = new WavesStrategyBO();
                    BeanUtils.copyProperties(wavesStrategyDTO, wavesStrategyBO);
                    newWaveCreateDTO.setWavesStrategyDTO(wavesStrategyBO);
                    newCreateDTOList.add(newWaveCreateDTO);
                });
            }

            if (!CollectionUtils.isEmpty(notInAreaList)) {
                WaveCreateDTO newWaveCreateDTO = new WaveCreateDTO();
                BeanUtils.copyProperties(waveCreateDTO, newWaveCreateDTO);
                newWaveCreateDTO.setOrders(notInAreaList);
                WavesStrategyBO wavesStrategyBO = new WavesStrategyBO();
                BeanUtils.copyProperties(wavesStrategyDTO, wavesStrategyBO);
                newWaveCreateDTO.setWavesStrategyDTO(wavesStrategyBO);
                newCreateDTOList.add(waveCreateDTO);
            }
        }

        return newCreateDTOList;
    }

    private static boolean isRealTimePickingByOrderNeedSplit(WavesStrategyBO wavesStrategyDTO,
                                                             ProcessBatchDTO processBatchDTO) {
        if (BooleanUtils.isFalse(wavesStrategyDTO.hasOpenWarehouseRealTimePickingByOrder())) {
            return Boolean.FALSE;
        }
        // if (BooleanUtils
        // .isFalse(Objects.equals(processBatchDTO.getOrderPickFlag(), OrderPickFlagEnum.按用户拆分.getType()))) {
        if (BooleanUtils.isFalse(
                Objects.equals(wavesStrategyDTO.getOrderSelection(), WavesStrategyConstants.ORDERSELECTION_USER))) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    private static Map<String, List<OutStockOrderPO>> splitBatchTaskByAddressAndFeature(
            List<OutStockOrderPO> waveOrders, Function<List<Long>, Map<Long, List<Byte>>> getOrderFeatureMap) {
        List<Long> orderIds = waveOrders.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());

        Map<Long, List<Byte>> orderFeatureMap = getOrderFeatureMap.apply(orderIds);

        return waveOrders.stream().collect(Collectors.groupingBy(outStockOrderPO -> {
            if (CollectionUtils.isEmpty(orderFeatureMap)
                    || CollectionUtils.isEmpty(orderFeatureMap.get(outStockOrderPO.getId()))) {
                return outStockOrderPO.getAddressId().toString();
            }
            List<Byte> featureList = orderFeatureMap.get(outStockOrderPO.getId());
            boolean isDrinking =
                    featureList.stream().anyMatch(feature -> feature.equals(OrderFeatureConstant.FEATURE_TYPE_DRINKING));
            boolean isRest =
                    featureList.stream().anyMatch(feature -> feature.equals(OrderFeatureConstant.FEATURE_TYPE_REST));

            if (isDrinking) {
                return outStockOrderPO.getAddressId().toString().concat("-")
                        .concat(OrderFeatureConstant.FEATURE_TYPE_DRINKING.toString());
            }
            if (isRest) {
                return outStockOrderPO.getAddressId().toString().concat("-")
                        .concat(OrderFeatureConstant.FEATURE_TYPE_REST.toString());
            }

            return outStockOrderPO.getAddressId().toString();
        }));
    }

    private static List<OutStockOrderPO> getInAreaList(WavesStrategyBO wavesStrategyDTO,
                                                       List<OutStockOrderPO> orderList) {
        if (CollectionUtils.isEmpty(wavesStrategyDTO.getAreaIds())) {
            return orderList;
        }

        return orderList.stream().filter(m -> wavesStrategyDTO.getAreaIds().contains(m.getAreaId()))
                .collect(Collectors.toList());
    }

    private static List<OutStockOrderPO> getNotInAreaList(WavesStrategyBO wavesStrategyDTO,
                                                          List<OutStockOrderPO> orderList) {
        if (CollectionUtils.isEmpty(wavesStrategyDTO.getAreaIds())) {
            return Collections.emptyList();
        }

        return orderList.stream().filter(m -> !wavesStrategyDTO.getAreaIds().contains(m.getAreaId()))
                .collect(Collectors.toList());
    }

    private static List<WaveCreateDTO> splitByAwardOutOrder(WavesStrategyBO wavesStrategyDTO,
                                                            ProcessBatchDTO processBatchDTO, List<OutStockOrderPO> outStockOrderList) {
        if (CollectionUtils.isEmpty(outStockOrderList)) {
            return Collections.emptyList();
        }
        List<OutStockOrderPO> awardOutStockOrderList = outStockOrderList.stream()
                .filter(m -> m.getOrdertype() == OutStockOrderTypeEnum.实物兑奖单.getType()).collect(Collectors.toList());
        List<OutStockOrderPO> normalOutStockOrderList = outStockOrderList.stream()
                .filter(m -> m.getOrdertype() != OutStockOrderTypeEnum.实物兑奖单.getType()).collect(Collectors.toList());

        List<WaveCreateDTO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(awardOutStockOrderList)) {
            WaveCreateDTO waveCreateDTO =
                    createWaveCreateDTO(wavesStrategyDTO, processBatchDTO, awardOutStockOrderList);
            waveCreateDTO.setIsAwardOrderTask(Boolean.TRUE);
            list.add(waveCreateDTO);
        }

        if (!CollectionUtils.isEmpty(normalOutStockOrderList)) {
            list.add(createWaveCreateDTO(wavesStrategyDTO, processBatchDTO, normalOutStockOrderList));
        }

        return list;
    }

    private static WaveCreateDTO createWaveCreateDTO(WavesStrategyBO wavesStrategyDTO, ProcessBatchDTO processBatchDTO,
                                                     List<OutStockOrderPO> outStockOrderList) {
        Boolean allocationFlag = processBatchDTO.getAllocationFlag();
        String title = processBatchDTO.getBatchName();
        Integer cityId = processBatchDTO.getCityId();
        String operateUser = processBatchDTO.getOperateUser();
        String locationName = processBatchDTO.getLocationName();
        String driverName = processBatchDTO.getDriverName();

        WaveCreateDTO createDTO = new WaveCreateDTO();
        createDTO.setOrders(outStockOrderList);
        createDTO.setWavesStrategyDTO(wavesStrategyDTO);
        createDTO.setOperateUser(operateUser);
        createDTO.setOperateUserId(processBatchDTO.getOperateUserId());
        createDTO.setTitle(title);
        createDTO.setCityId(cityId);
        createDTO.setLocationName(locationName);
        createDTO.setDriverName(driverName);
        createDTO.setAllocationFlag(allocationFlag);
        createDTO.setToLocationId(processBatchDTO.getToLocationId());
        createDTO.setToLocationName(processBatchDTO.getToLocationName());
        createDTO.setToWarehouseId(outStockOrderList.get(0).getToWarehouseId());
        createDTO.setToWarehouseName(outStockOrderList.get(0).getToWarehouseName());
        createDTO.setWorkSetting(processBatchDTO.getWorkSetting());
        createDTO.setIsAwardOrderTask(Boolean.FALSE);

        return createDTO;
    }

}
