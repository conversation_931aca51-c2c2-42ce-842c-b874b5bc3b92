package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowLocationInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.*;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.FixSowOrderSequenceDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;

/**
 * 播种任务_订单关联表
 */
@Mapper
public interface SowOrderMapper {

    int insert(SowOrderPO record);

    int insertSelective(SowOrderPO record);

    SowOrderPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SowOrderPO record);

    int updateByPrimaryKey(SowOrderPO record);

    /**
     * 批量保存播种任务_订单关联信息
     * 
     * @param lstSowOrders
     */
    void insertSowOrderList(@Param("list") List<SowOrderPO> lstSowOrders);

    /**
     * 记录播种任务与订单关联信息的播种数量
     * 
     * @param sowOrderDTO
     */
    void updateSownAmountByOrderId(@Param("sowTaskNo") String sowTaskNo,
        @Param("sownOrderItemDTOS") List<SownOrderItemDTO> sownOrderItems,
        @Param("lastUpdateUser") String operatorUserName, @Param("orgId") Integer orgId);

    /**
     * 根据播种任务编号删除关联信息
     * 
     * @param sowTaskNos
     */
    void deleteBySowTaskIds(@Param("list") List<Long> sowTaskIds, @Param("orgId") Integer orgId);

    /**
     * 根据播种任务编号查询关联的订单编号
     * 
     * @param sowTaskNo
     * @return
     */
    List<String> listRefOrderNoBySowTaskNo(@Param("list") List<String> sowTaskNo);

    /**
     * 根据订单号查询关联播种信息
     * 
     * @param orderPageList
     * @return
     */
    List<SowOrderDTO> findByOrderIds(@Param("list") List<String> orderIds);

    /**
     * 根据订单号查询关联播种信息
     *
     * @param
     * @return
     */
    PageResult<SowTaskDTO> findByOrderNos(@Param("list") List<String> orderIds, @Param("orgId") Integer orgId,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 条件查询打包订单列表
     * 
     * @param unpackOrderQueryDTO
     * @return
     */
    PageResult<UnpackOrderDTO> findUnpackOrderList(@Param("unpackOrderQuery") UnpackOrderQueryDTO unpackOrderQueryDTO,
        @Param("pageNum") Integer currentPage, @Param("pageSize") Integer pageSize);

    /**
     * 根据播种任务编号查询集货位、箱号相关信息
     * 
     * @param sowTaskNos
     * @param orgId
     * @return
     */
    List<SowLocationInfoPO> findSowLocationByNos(@Param("sowTaskNos") List<String> sowTaskNos,
        @Param("orgId") Integer orgId);

    /**
     * 根据播种任务编号查询订单相关信息
     * 
     * @param sowTaskNo
     * @param orgId
     * @return
     */
    List<SownOrderItemDTO> findSownOrderItems(@Param("sowTaskNo") String sowTaskNo, @Param("orgId") Integer orgId);

    List<SowOrderPO> listBySowTaskNo(@Param("sowTaskNo") String sowTaskNo, @Param("orgId") Integer orgId);

    /**
     * 批量修改容器货位
     */
    void batchUpdateLocation(@Param("updateSowOrderPOS") List<SowOrderPO> updateSowOrderPOS,
        @Param("orgId") Integer orgId, @Param("lastUpdateUser") String operatorUserName);

    /**
     * 查询播种订单明细
     */
    List<SowProductItemDTO> listSowingPrintItems(@Param("list") List<String> sowTaskNos, @Param("orgId") Integer orgId);

    /**
     * 查询播种自提点信息
     */
    List<SowAddressDTO> listSowAddressBySowTaskNo(@Param("query") SowAddressQueryDTO sowAddressQueryDTO);

    /**
     * 通过容器编号修改已播种数量
     */
    void updateSownAmount(@Param("sowTaskNo") String sowTaskNo,
        @Param("sownOrderItemDTOS") List<SownOrderItemDTO> sownOrderItems,
        @Param("lastUpdateUser") String operatorUserName, @Param("orgId") Integer orgId);

    List<SowProductItemDTO> listSowProductItem(@Param("query") SowProductItemQueryDTO sowProductItemQueryDTO);

    Integer getMaxSowOrderSequenceByDate(@Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId,
        @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<SowOrderPO> findByBatchNos(@Param("batchNos") List<String> batchNos);

    void deleteByIds(@Param("ids") List<Long> sowOrderIds, @Param("orgId") Integer orgId);

    List<SowOrderPO> findFixSowOrderList(FixSowOrderSequenceDTO dto);

    List<SowOrderPO> findBySowTaskId(@Param("sowTaskId") Long sowTaskId);

}
