package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
public class OrderExpressListSyncDTO implements Serializable {
    private static final long serialVersionUID = 2678120085407005392L;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 操作人id
     */
    private String optUserId;
    /**
     * 操作人
     */
    private String optUserName;

    /**
     * 物流商快递信息集合
     */
    private List<OrderExpressDispatchDTO> orderExpressDispatchDTOList;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(String optUserId) {
        this.optUserId = optUserId;
    }

    public String getOptUserName() {
        return optUserName;
    }

    public void setOptUserName(String optUserName) {
        this.optUserName = optUserName;
    }

    public List<OrderExpressDispatchDTO> getOrderExpressDispatchDTOList() {
        return orderExpressDispatchDTOList;
    }

    public void setOrderExpressDispatchDTOList(List<OrderExpressDispatchDTO> orderExpressDispatchDTOList) {
        this.orderExpressDispatchDTOList = orderExpressDispatchDTOList;
    }

    public static class OrderExpressDispatchDTO implements Serializable {
        private static final long serialVersionUID = -8478489820252630098L;
        /**
         * 包裹id
         */
        private Long expressId;
        /**
         * 订单id
         */
        private Long orderId;
        /**
         * 物流商id（编码）
         */
        private String logisticsId;
        /**
         * 物流商
         */
        private String logisticName;
        /**
         * 物流快递单号
         */
        private String trackNumber;
        /**
         * 操作人id
         */
        private String optUserId;
        /**
         * 操作人名称
         */
        private String optUserName;
        /**
         * 配送时间
         */
        private Date deliveryTime;
        /**
         * 发货人手机号
         */
        private String customerNo;
        /**
         * 配送状态
         */
        private Integer deliveryState;
        /**
         * 物流信息详情
         */
        private List<OrderExpressDispatchItemDTO> orderDeliveryItemDTOList;

        public Long getExpressId() {
            return expressId;
        }

        public void setExpressId(Long expressId) {
            this.expressId = expressId;
        }

        public Long getOrderId() {
            return orderId;
        }

        public void setOrderId(Long orderId) {
            this.orderId = orderId;
        }

        public String getLogisticsId() {
            return logisticsId;
        }

        public void setLogisticsId(String logisticsId) {
            this.logisticsId = logisticsId;
        }

        public String getLogisticName() {
            return logisticName;
        }

        public void setLogisticName(String logisticName) {
            this.logisticName = logisticName;
        }

        public String getTrackNumber() {
            return trackNumber;
        }

        public void setTrackNumber(String trackNumber) {
            this.trackNumber = trackNumber;
        }

        public String getOptUserId() {
            return optUserId;
        }

        public void setOptUserId(String optUserId) {
            this.optUserId = optUserId;
        }

        public String getOptUserName() {
            return optUserName;
        }

        public void setOptUserName(String optUserName) {
            this.optUserName = optUserName;
        }

        public Date getDeliveryTime() {
            return deliveryTime;
        }

        public void setDeliveryTime(Date deliveryTime) {
            this.deliveryTime = deliveryTime;
        }

        public String getCustomerNo() {
            return customerNo;
        }

        public void setCustomerNo(String customerNo) {
            this.customerNo = customerNo;
        }

        public Integer getDeliveryState() {
            return deliveryState;
        }

        public void setDeliveryState(Integer deliveryState) {
            this.deliveryState = deliveryState;
        }

        public List<OrderExpressDispatchItemDTO> getOrderDeliveryItemDTOList() {
            return orderDeliveryItemDTOList;
        }

        public void setOrderDeliveryItemDTOList(List<OrderExpressDispatchItemDTO> orderDeliveryItemDTOList) {
            this.orderDeliveryItemDTOList = orderDeliveryItemDTOList;
        }
    }

    public static class OrderExpressDispatchItemDTO implements Serializable {
        /**
         * 订单包裹配送项id
         */
        private Long id;
        /**
         * 订单id
         */
        private Long orderId;
        /**
         * 订单明细id
         */
        private Long orderItemId;
        /**
         * 业务明细id
         */
        private Long businessItemId;
        /**
         * 快递包裹id
         */
        private Long deliveryId;
        /**
         * 产品数量
         */
        private BigDecimal count;
        /**
         * 创建时间
         */
        private Date createTime;
        /**
         * 更新时间
         */
        private Date lastUpdateTime;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Long getOrderId() {
            return orderId;
        }

        public void setOrderId(Long orderId) {
            this.orderId = orderId;
        }

        public Long getOrderItemId() {
            return orderItemId;
        }

        public void setOrderItemId(Long orderItemId) {
            this.orderItemId = orderItemId;
        }

        public Long getBusinessItemId() {
            return businessItemId;
        }

        public void setBusinessItemId(Long businessItemId) {
            this.businessItemId = businessItemId;
        }

        public Long getDeliveryId() {
            return deliveryId;
        }

        public void setDeliveryId(Long deliveryId) {
            this.deliveryId = deliveryId;
        }

        public BigDecimal getCount() {
            return count;
        }

        public void setCount(BigDecimal count) {
            this.count = count;
        }

        public Date getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Date createTime) {
            this.createTime = createTime;
        }

        public Date getLastUpdateTime() {
            return lastUpdateTime;
        }

        public void setLastUpdateTime(Date lastUpdateTime) {
            this.lastUpdateTime = lastUpdateTime;
        }
    }
}
