package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

public class DirectOutStockSetDTO implements Serializable {
    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 出库list
     */
    private List<DirectOutStockDTO> dTOList;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<DirectOutStockDTO> getdTOList() {
        return dTOList;
    }

    public void setdTOList(List<DirectOutStockDTO> dTOList) {
        this.dTOList = dTOList;
    }
}
