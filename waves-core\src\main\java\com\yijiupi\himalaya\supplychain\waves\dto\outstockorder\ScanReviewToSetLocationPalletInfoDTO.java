package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
public class ScanReviewToSetLocationPalletInfoDTO implements Serializable {
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 出库位id
     */
    private Long locationId;
    /**
     * 出库位名称
     */
    private String locationName;
    /**
     * 托盘号列表
     */
    private List<String> palletNoList;
    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 获取 订单号
     *
     * @return orderNo 订单号
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置 订单号
     *
     * @param orderNo 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 出库位id
     *
     * @return locationId 出库位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 出库位id
     *
     * @param locationId 出库位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 出库位名称
     *
     * @return locationName 出库位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 出库位名称
     *
     * @param locationName 出库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 托盘号列表
     *
     * @return palletNoList 托盘号列表
     */
    public List<String> getPalletNoList() {
        return this.palletNoList;
    }

    /**
     * 设置 托盘号列表
     *
     * @param palletNoList 托盘号列表
     */
    public void setPalletNoList(List<String> palletNoList) {
        this.palletNoList = palletNoList;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }
}
