package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.util.Collections;
import java.util.List;

import org.assertj.core.api.Assertions;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderitemdetail.OutStockOrderItemDetailModBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchBaseBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.CreateBatchByManualOperationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.RecoverOrderItemDetailBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskSortOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;

/**
 * <AUTHOR>
 * @Title: himalaya-supplychain-microservice-waves
 * @Package com.yijiupi.himalaya.supplychain.waves.domain.bl
 * @Description:
 * @date 2018/3/30 10:26
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class BatchOrderProcessBLTest {

    @Autowired
    private BatchOrderProcessBL batchOrderProcessBL;

    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;
    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;
    @Autowired
    private CreateBatchByManualOperationBL createBatchByManualOperationBL;

    @Test
    public void processBatchOrderByStrateId() {
        batchOrderProcessBL.processBatchOrderByStrateId(1, "2017-04-02 19:44:32", "2018-04-30 19:44:32");
    }

    @Test
    public void createBatch() {
        String json =
            // "{\"orderIdList\":[\"5454394588897842692\",\"5453728765501871628\"],\"cityId\":998,\"warehouseId\":9981,\"pickingType\":1,\"pickingGroupStrategy\":3,\"operateUser\":\"测试李\",\"passPickType\":0,\"orderPickFlag\":2,\"groupType\":1}";
            // "{\"orderIdList\":[\"5453728765501871628\",\"5454394588897842692\",\"5403397736031161544\",\"5398571784211165833\"],\"cityId\":998,\"warehouseId\":9981,\"pickingType\":1,\"pickingGroupStrategy\":3,\"operateUser\":\"测试李\",\"passPickType\":0}";
            "{\"orderIdList\":[\"5453728765501871628\",\"5454394588897842692\"],\"cityId\":998,\"warehouseId\":9981,\"pickingType\":2,\"pickingGroupStrategy\":2,\"operateUser\":\"测试李\",\"passPickType\":1,\"groupType\":1}";
        BatchCreateDTO batchCreateDTO = JSON.parseObject(json, BatchCreateDTO.class);
        CreateBatchBaseBO createBatchBaseBO = new CreateBatchBaseBO(batchCreateDTO.getWarehouseId(), batchCreateDTO);
        createBatchByManualOperationBL.createBatch(createBatchBaseBO);
    }

    @Test
    public void updateBatch() {
        List<BatchTaskDTO> BatchTaskDTOS = Lists.newArrayList();
        BatchTaskDTO p1 = new BatchTaskDTO();
        p1.setId("2022102800067");
        p1.setBatchTaskNo("BT998122102800010");
        p1.setSorterId(125);
        p1.setSorter("刘亦菲");

        BatchTaskDTO p2 = new BatchTaskDTO();
        p2.setId("2022103100022");
        p2.setBatchTaskNo("BT998122103100003");
        p2.setSorter("刘亦菲");
        p2.setSorterId(125);
        BatchTaskDTOS.add(p1);
        BatchTaskDTOS.add(p2);
        int i = batchOrderTaskBL.updateBatch(BatchTaskDTOS, null);

        Assertions.assertThat(i).isNotNull();
    }

    @Test
    public void findOutStockOrderPOSByStrate() {
        WavesStrategyDTO wavesStrategyDTO = new WavesStrategyDTO();
        wavesStrategyDTO.setOrderType("0,1,10,12,13,14");
        wavesStrategyDTO.setWarehouseId(1031);
        List<OutStockOrderPO> outStockOrderPOSByStrate = batchOrderProcessBL
            .findOutStockOrderPOSByStrate(wavesStrategyDTO, "2018-05-29 08:38:00", "2018-05-29 18:00:00 ");
    }

    @Autowired
    private OutStockOrderItemDetailModBL outStockOrderItemDetailModBL;

    @Test
    public void recoverDetailTest() {
        RecoverOrderItemDetailBO bo = new RecoverOrderItemDetailBO();
        bo.setOutStockOrderIds(Collections.singletonList(5305157404796482380L));
        bo.setWarehouseId(bo.getWarehouseId());
        bo.setBatchNo("1");
        bo.setUseOrderCenterDetail(Boolean.FALSE);
        bo.setHandleEqualDetail(Boolean.TRUE);
        outStockOrderItemDetailModBL.recoverDetail(bo);
    }

    @Test
    public void findBatchTaskSortItemByOrderTest() {
        PageList<BatchTaskSortOrderDTO> result = batchOrderTaskBL.findBatchTaskSortItemByOrder("2025060400400", 1, 20);

        Assertions.assertThat(result).isNotNull();
    }

}
