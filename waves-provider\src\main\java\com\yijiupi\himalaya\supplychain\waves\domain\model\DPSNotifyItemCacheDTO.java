package com.yijiupi.himalaya.supplychain.waves.domain.model;

import com.yijiupi.himalaya.supplychain.wcs.enums.AGVActionEnums;

public class DPSNotifyItemCacheDTO {
    /**
     * 物料箱id
     */
    private String boxID;
    /**
     * 任务id
     */
    private String batchID;
    /**
     * 状态
     */
    private String status;
    /**
     * @see AGVActionEnums
     */
    private String action;

    /**
     * 波次任务 id
     */
    private String batchTaskId;

    public String getBoxID() {
        return boxID;
    }

    public void setBoxID(String boxID) {
        this.boxID = boxID;
    }

    public String getBatchID() {
        return batchID;
    }

    public void setBatchID(String batchID) {
        this.batchID = batchID;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }
}
