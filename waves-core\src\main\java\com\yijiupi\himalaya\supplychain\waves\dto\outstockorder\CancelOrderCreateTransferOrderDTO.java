package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: CancelOrderCreateTransferOrderDTO
 * @description:
 * @date 2023-05-26 11:53
 */
public class CancelOrderCreateTransferOrderDTO implements Serializable {
    /**
     * 出库单id列表
     */
    private List<Long> outStockOrderIds;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 操作人id
     */
    private String optUserId;

    /**
     * 获取 出库单id列表
     *
     * @return outStockOrderIds 出库单id列表
     */
    public List<Long> getOutStockOrderIds() {
        return this.outStockOrderIds;
    }

    /**
     * 设置 出库单id列表
     *
     * @param outStockOrderIds 出库单id列表
     */
    public void setOutStockOrderIds(List<Long> outStockOrderIds) {
        this.outStockOrderIds = outStockOrderIds;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public String getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(String optUserId) {
        this.optUserId = optUserId;
    }
}
