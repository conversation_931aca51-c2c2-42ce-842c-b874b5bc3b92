package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/7/28
 */
public class BatchTaskItemUpdateDTOConvertor {

    public static List<BatchTaskItemCompleteDTO> convertList(List<BatchTaskItemPO> updateBatchTaskItemPOList,
        List<BatchTaskItemPO> batchTaskItemPOList) {
        Date currentTime = new Date();
        Map<String, BatchTaskItemPO> batchTaskItemMap =
            updateBatchTaskItemPOList.stream().collect(Collectors.toMap(BatchTaskItemPO::getId, v -> v));

        return batchTaskItemPOList.stream().filter(m -> Objects.nonNull(batchTaskItemMap.get(m.getId()))).map(m -> {
            BatchTaskItemCompleteDTO updateDTO = new BatchTaskItemCompleteDTO();
            updateDTO.setCompleteTime(currentTime);
            updateDTO.setStartTime(currentTime);
            updateDTO.setFromLocationId(m.getLocationId());
            updateDTO.setFromLocationName(m.getLocationName());
            updateDTO.setId(m.getId());
            updateDTO.setLackPackageCount(BigDecimal.ZERO);
            updateDTO.setLackUnitCount(BigDecimal.ZERO);
            updateDTO.setOverSortPackageCount(m.getPackageCount());
            updateDTO.setOverSortUnitCount(m.getUnitCount());
            // 先不管这个参数 初次提交的参数
            // updateDTO.setSubmitFlag();
            return updateDTO;
        }).collect(Collectors.toList());
    }

}
