package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.pickup;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoSearchDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskQueryBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.PickedDetailQueryParam;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickedDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickedProductDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-06-13 11:34
 **/
@Service
public class BatchTaskPickupBL {

    @Reference
    private IProductSkuService productSkuService;

    @Resource
    private BatchOrderTaskBL batchOrderTaskBL;

    @Resource
    private BatchTaskQueryBL batchTaskQueryBL;

    public List<PickedProductDTO> findPickedDetailForSCM25(PickedDetailQueryParam param) {
        AssertUtils.notNull(param.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(param.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.isTrue(StringUtils.isNotBlank(param.getProductName()), "产品名称不能为空");
        return querySkuByProductName(param).stream().filter(Objects::nonNull).filter(p -> p.getSkuId() != null)
                .map(p -> queryPickedProductInfo(param, p)).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private PickedProductDTO queryPickedProductInfo(PickedDetailQueryParam param, ProductSkuInfoReturnDTO p) {
        Integer cityId = param.getCityId();
        Integer warehouseId = param.getWarehouseId();
        Long skuId = p.getSkuId();
        List<PickedDetailDTO> pickedDetails = batchTaskQueryBL.findPickedDetailBySkuIdForSCM25(cityId, warehouseId, skuId, true);
        if (CollectionUtils.isEmpty(pickedDetails)) {
            return null;
        }
        PickedProductDTO pickedProduct = new PickedProductDTO();
        pickedProduct.setSkuId(skuId);
        pickedProduct.setProductName(p.getProductName());
        pickedProduct.setDetailDTOList(pickedDetails);
        return pickedProduct;
    }

    private List<ProductSkuInfoReturnDTO> querySkuByProductName(PickedDetailQueryParam param) {
        ProductSkuInfoSearchDTO searchDTO = new ProductSkuInfoSearchDTO();
        searchDTO.setPrecise(0); // 模糊查询
        searchDTO.setPageNum(1);
        searchDTO.setPageSize(10); // 根据产品名称模糊查询限制数据条数
        searchDTO.setCityId(param.getCityId());
        searchDTO.setProductSkuId(param.getProductSkuId());
        searchDTO.setProductName(StringUtils.trimToNull(param.getProductName()));
        List<ProductSkuInfoReturnDTO> result = productSkuService.getProductSkuInfo(searchDTO).getDataList();
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyList();
        }
        return result;
    }

}
