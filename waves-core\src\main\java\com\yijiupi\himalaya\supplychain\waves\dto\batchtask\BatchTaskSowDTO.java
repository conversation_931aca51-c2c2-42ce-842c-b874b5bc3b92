package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.Map;

public class BatchTaskSowDTO implements Serializable {

    /**
     * id
     */
    private String id;
    /**
     * 波次任务号
     */
    private String batchTaskNo;
    /**
     * 拣货属性
     */
    private String batchTaskName;

    /**
     * 集货位id
     */
    private Long sowLocationId;

    /**
     * 集货位名称
     */
    private String sowLocationName;

    /**
     * 订单箱号信息
     */
    private Map<String, Integer> orderSequenceMap;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    public String getBatchTaskName() {
        return batchTaskName;
    }

    public void setBatchTaskName(String batchTaskName) {
        this.batchTaskName = batchTaskName;
    }

    public Long getSowLocationId() {
        return sowLocationId;
    }

    public void setSowLocationId(Long sowLocationId) {
        this.sowLocationId = sowLocationId;
    }

    public String getSowLocationName() {
        return sowLocationName;
    }

    public void setSowLocationName(String sowLocationName) {
        this.sowLocationName = sowLocationName;
    }

    public Map<String, Integer> getOrderSequenceMap() {
        return orderSequenceMap;
    }

    public void setOrderSequenceMap(Map<String, Integer> orderSequenceMap) {
        this.orderSequenceMap = orderSequenceMap;
    }
}
