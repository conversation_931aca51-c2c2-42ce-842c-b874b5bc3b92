package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;

/**
 * 订单项打印顺序同步对象
 *
 * <AUTHOR>
 * @date 2019/6/5 11:26
 */
public class OrderPrintSequenceDTO implements Serializable {

    private static final long serialVersionUID = -5509115205861860568L;

    /**
     * 订单id
     */
    private Long businessId;

    /**
     * 订单项id
     */
    private Long businessItemId;

    /**
     * 排序
     */
    private Integer sequnce;

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Long getBusinessItemId() {
        return businessItemId;
    }

    public void setBusinessItemId(Long businessItemId) {
        this.businessItemId = businessItemId;
    }

    public Integer getSequnce() {
        return sequnce;
    }

    public void setSequnce(Integer sequnce) {
        this.sequnce = sequnce;
    }
}
