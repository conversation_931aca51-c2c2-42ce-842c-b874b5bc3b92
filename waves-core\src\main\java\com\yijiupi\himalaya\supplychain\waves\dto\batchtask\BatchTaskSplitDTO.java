package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * 拆分拣货任务
 * 
 * <AUTHOR>
 * @date 2018/7/9 16:08
 */
public class BatchTaskSplitDTO implements Serializable {

    private static final long serialVersionUID = 695741485746205448L;

    /**
     * 拣货方式 1 按订单拣货 2 按产品拣货
     */
    private Byte pickingType;

    /**
     * 拣货任务编号
     */
    private String batchTaskNo;

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 拣货子任务列表
     */
    private List<BatchTaskDTO> batchTaskList;

    /**
     * 操作人
     */
    private Integer operateUserId;
    /**
     * 仓库id
     */
    private Integer warehouseId;

    public String getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public List<BatchTaskDTO> getBatchTaskList() {
        return batchTaskList;
    }

    public void setBatchTaskList(List<BatchTaskDTO> batchTaskList) {
        this.batchTaskList = batchTaskList;
    }

    public Byte getPickingType() {
        return pickingType;
    }

    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    /**
     * 获取 操作人
     *
     * @return operateUserId 操作人
     */
    public Integer getOperateUserId() {
        return this.operateUserId;
    }

    /**
     * 设置 操作人
     *
     * @param operateUserId 操作人
     */
    public void setOperateUserId(Integer operateUserId) {
        this.operateUserId = operateUserId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
