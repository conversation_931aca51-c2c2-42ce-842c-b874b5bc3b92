package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;

/**
 * 出库单对应的出库位
 */
public class WaveNoItemModel implements Serializable {

    private static final long serialVersionUID = 4623455949798447001L;

    /**
     * 订单号
     */
    private String businessNo;

    /**
     * 出库位名称
     */
    private String locationName;

    /**
     * 出库位id
     */
    private Long locationId;

    /**
     * 分拣员名称
     */
    private String sorterName;

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getSorterName() {
        return sorterName;
    }

    public void setSorterName(String sorterName) {
        this.sorterName = sorterName;
    }
}
