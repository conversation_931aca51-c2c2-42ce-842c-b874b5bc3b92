package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * <AUTHOR>
 * @Title: himalaya-supplychain-microservice-waves
 * @Package com.yijiupi.himalaya.supplychain.waves.enums
 * @Description:
 * @date 2018/4/17 13:47
 */
public class OrderTraceTypeConstants {

    /**
     * 波次=1，拣货任务=2，上架单=3，上架任务=4，移库单=5，出库单=6，入库单=7
     */
    public static final Byte 波次策略 = 1;
    /**
     * 分配策略 = 2
     */
    public static final Byte 分配策略 = 2;
    /**
     * 上架单 = 3
     */
    public static final Byte 上架单 = 3;
    /**
     * 上架任务 = 4
     */
    public static final Byte 上架任务 = 4;
    /**
     * 移库单 = 5
     */
    public static final Byte 移库单 = 5;
    /**
     * 出库单 = 6
     */
    public static final Byte 出库单 = 6;
    /**
     * 入库单 = 7
     */
    public static final Byte 入库单 = 7;
    /**
     * 出库批次 = 8
     */
    public static final Byte 出库批次 = 8;

}
