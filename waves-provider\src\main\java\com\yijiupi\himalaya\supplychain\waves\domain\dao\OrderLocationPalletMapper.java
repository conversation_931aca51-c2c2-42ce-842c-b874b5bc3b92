package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.Collection;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.domain.bo.orderpallet.OrderInPalletQueryBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderLocationPalletBatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderLocationPalletPO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletQueryDTO;
import org.apache.ibatis.annotations.Param;

public interface OrderLocationPalletMapper {

    int insertSelectiveBatch(@Param("list") List<OrderLocationPalletPO> poList);

    List<OrderLocationPalletDTO> selectByIds(@Param("ids") List<Long> Ids);

    List<OrderLocationPalletDTO> selectByConditions(@Param("query") OrderLocationPalletQueryDTO query);

    int deleteByPrimaryKeyBatch(@Param("list") List<Long> ids);

    List<OrderLocationPalletDTO> selectByOrderIdsAndOrgId(@Param("orderIds") Collection<Long> orderIds,
        @Param("orgId") Integer orgId);

    int updateByPrimaryKeySelective(OrderLocationPalletPO record);

    int updateBatchSelective(List<OrderLocationPalletDTO> list);

    List<OrderLocationPalletDTO> selectByBatchTaskId(String batchTaskId);

    void deleteByBatchTaskId(String batchTaskId);

    List<OrderLocationPalletPO> findInPalletOrderList(OrderInPalletQueryBO queryBO);

    int batchUpdateLocationInfo(OrderLocationPalletBatchPO po);
}
