package com.yijiupi.himalaya.supplychain.waves.search;

import java.io.Serializable;
import java.util.List;

/**
 * Created by 余明 on 2018-03-15.
 */
public class OutStockOrderSearchSO implements Serializable {
    /**
     * 仓库Id
     */
    private Integer wareHouseId;

    /**
     * 开始
     */
    private String timeS;
    /**
     * 结束
     */
    private String timeE;

    /**
     * 搜索的订单类型
     */
    private List<Integer> orderTypes;

    /**
     * 订单筛选方式（排序用） 1 按区域 2 按线路
     */
    private Integer order_selection;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 线路id
     */
    private String routeId;

    /**
     * 订单号
     */
    private String refOrderNo;
    /**
     * 当前页
     */
    private Integer currentPage = 1;
    /**
     * @Fields 每页的数量
     */
    private Integer pageSize = 100;

    /**
     * 不被搜索的业务单据类型
     */
    private List<Byte> notBusinessTypes;

    /**
     * 指定搜索的业务单据类型
     */
    private List<Byte> businessTypes;

    /**
     * 订单属性（0: 默认 1: 大件订单 2: 小件订单 3: 单品）
     */
    private List<Byte> packageAttribute;

    /**
     * 订单状态 待处理(0), 待拣货(1), 拣货中(2), 已拣货/待出库(3), 已出库(4)
     */
    private List<Byte> orderStates;

    /**
     * 收货地址-城市
     */
    private String city;

    /**
     * 收货地址-批量城市
     */
    private List<String> lstCity;
    /**
     * 收货地址-区
     */
    private String county;
    /**
     * 收货地址-街道
     */
    private String street;

    /**
     * 是否查询订单明细
     */
    private boolean isSearchItem;

    /**
     * 产品规格id
     */
    private Long productSpecificationId;

    /**
     * 产品Skuid
     */
    private String skuId;

    /**
     * 城市id 分库用
     */
    private Integer cityId;
    private Integer orgId;
    /**
     * 订单项Id
     */
    private List<Long> orderItemIds;
    /**
     * 播种任务项Id
     */
    private List<Long> sowTaskItemIds;
    /**
     * 出库单Id
     */
    private List<Long> orderIds;

    /**
     * 订单下推状态 0未下推,1已下推,2下推失败,3不下推
     */
    private Byte pushState;
    /**
     * 线路id列表
     */
    private List<String> routeIdList;
    /**
     * 片区id列表
     */
    private List<String> areaIdList;
    /**
     * 订单行数上限
     */
    private Integer maxCount;
    /**
     * 订单行数下限
     */
    private Integer minCount;
    /**
     * 1 整件，其他：不处理
     */
    private Integer packageOrder;

    /**
     * 订单来源
     */
    private List<Byte> orderSourceTypeList;

    /**
     * 分仓配置类型
     */
    private List<Integer> allocationConfigTypes;

    /**
     * 地址id列表
     */
    private List<Integer> addressIdList;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;
    /**
     * 订单序号
     */
    private Integer orderSequence;

    private String batchNo;

    public List<String> getLstCity() {
        return lstCity;
    }

    public void setLstCity(List<String> lstCity) {
        this.lstCity = lstCity;
    }

    public List<String> getAreaIdList() {
        return areaIdList;
    }

    public void setAreaIdList(List<String> areaIdList) {
        this.areaIdList = areaIdList;
    }

    public List<String> getRouteIdList() {
        return routeIdList;
    }

    public void setRouteIdList(List<String> routeIdList) {
        this.routeIdList = routeIdList;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public List<Long> getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(List<Long> orderIds) {
        this.orderIds = orderIds;
    }

    public List<Long> getSowTaskItemIds() {
        return sowTaskItemIds;
    }

    public void setSowTaskItemIds(List<Long> sowTaskItemIds) {
        this.sowTaskItemIds = sowTaskItemIds;
    }

    public List<Long> getOrderItemIds() {
        return orderItemIds;
    }

    public void setOrderItemIds(List<Long> orderItemIds) {
        this.orderItemIds = orderItemIds;
    }

    public List<Byte> getBusinessTypes() {
        return businessTypes;
    }

    public void setBusinessTypes(List<Byte> businessTypes) {
        this.businessTypes = businessTypes;
    }

    public List<Byte> getOrderStates() {
        return orderStates;
    }

    public void setOrderStates(List<Byte> orderStates) {
        this.orderStates = orderStates;
    }

    /**
     * /** 期望配送时间开始
     */
    private String expTimeS;
    /**
     * 期望配送时间结束
     */
    private String expTimeE;

    /**
     * 快递直发标示 1：快递直发订单
     */
    private Byte expressFlag;

    public Byte getExpressFlag() {
        return expressFlag;
    }

    public void setExpressFlag(Byte expressFlag) {
        this.expressFlag = expressFlag;
    }

    /**
     * 下单时间排序 （默认/0：降序 1：升序）
     */
    private Byte orderByCreateTime;

    /**
     * 扫描订单号/快递单号
     */
    private String scanOrderNo;

    /**
     * 订单号集合
     */
    private List<String> refOrderNoList;
    /**
     * 新增单据类型
     */
    private Integer outBoundType;

    /**
     * 新增单据类型列表
     */
    private List<Integer> outBoundTypeList;

    /**
     * 集货位id
     */
    private Long locationId;

    public Integer getOutBoundType() {
        return outBoundType;
    }

    public void setOutBoundType(Integer outBoundType) {
        this.outBoundType = outBoundType;
    }

    public List<String> getRefOrderNoList() {
        return refOrderNoList;
    }

    public void setRefOrderNoList(List<String> refOrderNoList) {
        this.refOrderNoList = refOrderNoList;
    }

    public String getScanOrderNo() {
        return scanOrderNo;
    }

    public void setScanOrderNo(String scanOrderNo) {
        this.scanOrderNo = scanOrderNo;
    }

    public Byte getOrderByCreateTime() {
        return orderByCreateTime;
    }

    public void setOrderByCreateTime(Byte orderByCreateTime) {
        this.orderByCreateTime = orderByCreateTime;
    }

    public String getExpTimeS() {
        return expTimeS;
    }

    public void setExpTimeS(String expTimeS) {
        this.expTimeS = expTimeS;
    }

    public String getExpTimeE() {
        return expTimeE;
    }

    public void setExpTimeE(String expTimeE) {
        this.expTimeE = expTimeE;
    }

    public List<Byte> getPackageAttribute() {
        return packageAttribute;
    }

    public void setPackageAttribute(List<Byte> packageAttribute) {
        this.packageAttribute = packageAttribute;
    }

    public List<Byte> getNotBusinessTypes() {
        return notBusinessTypes;
    }

    public void setNotBusinessTypes(List<Byte> notBusinessTypes) {
        this.notBusinessTypes = notBusinessTypes;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getRouteId() {
        return routeId;
    }

    public void setRouteId(String routeId) {
        this.routeId = routeId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Integer getOrder_selection() {
        return order_selection;
    }

    public void setOrder_selection(Integer order_selection) {
        this.order_selection = order_selection;
    }

    public Integer getWareHouseId() {
        return wareHouseId;
    }

    public void setWareHouseId(Integer wareHouseId) {
        this.wareHouseId = wareHouseId;
    }

    public String getTimeS() {
        return timeS;
    }

    public void setTimeS(String timeS) {
        this.timeS = timeS;
    }

    public String getTimeE() {
        return timeE;
    }

    public void setTimeE(String timeE) {
        this.timeE = timeE;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<Integer> getOrderTypes() {
        return orderTypes;
    }

    public void setOrderTypes(List<Integer> orderTypes) {
        this.orderTypes = orderTypes;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public boolean isSearchItem() {
        return isSearchItem;
    }

    public void setSearchItem(boolean isSearchItem) {
        this.isSearchItem = isSearchItem;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Byte getPushState() {
        return pushState;
    }

    public void setPushState(Byte pushState) {
        this.pushState = pushState;
    }

    public List<Integer> getOutBoundTypeList() {
        return outBoundTypeList;
    }

    public void setOutBoundTypeList(List<Integer> outBoundTypeList) {
        this.outBoundTypeList = outBoundTypeList;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 是否查询订单明细
     *
     * @return isSearchItem 是否查询订单明细
     */
    public boolean isIsSearchItem() {
        return this.isSearchItem;
    }

    /**
     * 设置 是否查询订单明细
     *
     * @param isSearchItem 是否查询订单明细
     */
    public void setIsSearchItem(boolean isSearchItem) {
        this.isSearchItem = isSearchItem;
    }

    /**
     * 获取
     *
     * @return maxCount
     */
    public Integer getMaxCount() {
        return this.maxCount;
    }

    /**
     * 设置
     *
     * @param maxCount
     */
    public void setMaxCount(Integer maxCount) {
        this.maxCount = maxCount;
    }

    /**
     * 获取
     *
     * @return minCount
     */
    public Integer getMinCount() {
        return this.minCount;
    }

    /**
     * 设置
     *
     * @param minCount
     */
    public void setMinCount(Integer minCount) {
        this.minCount = minCount;
    }

    /**
     * 获取 0 小件，1 整件
     *
     * @return packageOrder 0 小件，1 整件
     */
    public Integer getPackageOrder() {
        return this.packageOrder;
    }

    /**
     * 设置 0 小件，1 整件
     *
     * @param packageOrder 0 小件，1 整件
     */
    public void setPackageOrder(Integer packageOrder) {
        this.packageOrder = packageOrder;
    }

    public List<Byte> getOrderSourceTypeList() {
        return orderSourceTypeList;
    }

    public void setOrderSourceTypeList(List<Byte> orderSourceTypeList) {
        this.orderSourceTypeList = orderSourceTypeList;
    }

    public List<Integer> getAllocationConfigTypes() {
        return allocationConfigTypes;
    }

    public void setAllocationConfigTypes(List<Integer> allocationConfigTypes) {
        this.allocationConfigTypes = allocationConfigTypes;
    }

    public List<Integer> getAddressIdList() {
        return addressIdList;
    }

    public void setAddressIdList(List<Integer> addressIdList) {
        this.addressIdList = addressIdList;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }

    /**
     * 获取 订单序号
     *
     * @return orderSequence 订单序号
     */
    public Integer getOrderSequence() {
        return this.orderSequence;
    }

    /**
     * 设置 订单序号
     *
     * @param orderSequence 订单序号
     */
    public void setOrderSequence(Integer orderSequence) {
        this.orderSequence = orderSequence;
    }
}
