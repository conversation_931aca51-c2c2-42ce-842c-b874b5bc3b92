package com.yijiupi.himalaya.supplychain.waves.domain.bo;

/**
 * 拣货点异常信息
 * <AUTHOR>
 * @Date 2025/5/23 16:04
 * @Version 1.0
 */
public class LocationPointAbnormalBO {

	/**
	 * 仓库 id
	 */
	private Integer warehouseId;

	private String batchTaskNo;

	/**
	 * 商品名称
	 */
	private String productName;

	private Long skuId;

	/**
	 * 货位id
	 */
	private Long locationId;

	/**
	 * 货位名称
	 */
	private String locationName;

	private String reason;

	public Integer getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(Integer warehouseId) {
		this.warehouseId = warehouseId;
	}

	public String getBatchTaskNo() {
		return batchTaskNo;
	}

	public void setBatchTaskNo(String batchTaskNo) {
		this.batchTaskNo = batchTaskNo;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	public Long getLocationId() {
		return locationId;
	}

	public void setLocationId(Long locationId) {
		this.locationId = locationId;
	}

	public String getLocationName() {
		return locationName;
	}

	public void setLocationName(String locationName) {
		this.locationName = locationName;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}
}
