package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchOrderInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemLackDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.GroupBuyWorkScheduleDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.GroupBuyWorkScheduleSO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;

/**
 * 波次列表
 */
@Mapper
public interface BatchMapper {

    /**
     * 查询波次列表
     *
     * @param batchQueryDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<BatchPO> findList(@Param("dto") BatchQueryDTO batchQueryDTO, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    /**
     * 根据订单号查询波次NO
     *
     * @param orderNo
     * @return
     */
    String findBatchNoByOrderId(@Param("warehouseId") Integer warehouseId, @Param("orderNo") String orderNo);

    /**
     * 根据波次编号查询该波次关联的订单列表
     *
     * @param batchNo
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<BatchOrderInfoDTO> findBatchOrderInfoListByBatchNo(@Param("batchNo") String batchNo,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据波次编号查询该波次
     */
    BatchOrderInfoDTO selectBatchInfoByBatchNo(@Param("batchNo") String batchNo);

    /**
     * 批量生产波次
     *
     * @param batchPO
     * @return
     */
    int insertBatch(@Param("list") List<BatchPO> batchPO);

    /**
     * 单个新增波次
     *
     * @param batchPO
     * @return
     */
    int insertSelective(@Param("batchPO") BatchPO batchPO);

    /**
     * 根据波次编号删除波次
     * 
     * @param batchNo
     * @return
     */
    int deleteByBatchNo(@Param("list") List<String> batchNo);

    /**
     * 根据波次id删除波次
     * 
     * @return
     */
    int deleteByBatchIds(@Param("list") List<String> batchIds);

    /**
     * 查询出待拣货的batchNo
     *
     * @param batchNoList
     * @return
     */
    List<String> findDaiJianHuoBatchNo(List<String> batchNoList);

    /**
     * 更新波次状态
     *
     * @param batchIds
     * @param state
     */
    void updateBatchState(@Param("list") List<String> batchIds, @Param("state") Integer state,
        @Param("orgId") Integer orgId);

    /**
     * 更新波次状态
     *
     * @param batchNos
     * @param state
     */
    void updateBatchStateByNos(@Param("list") List<String> batchNos, @Param("state") Integer state,
        @Param("orgId") Integer orgId);

    /**
     * 根据batchNo修改状态
     *
     * @param batchTaskNo
     * @param state
     */
    void updateStateByTaskNo(@Param("batchTaskNo") String batchTaskNo, @Param("state") Integer state);

    /**
     * 根据多个波次编号查询对应的波次详情
     */
    List<BatchPO> listByBatchNos(@Param("list") Collection<String> batchNos);

    /**
     * 根据多个波次Id查询对应的波次详情
     *
     * @param batchIds
     * @return
     */
    List<BatchPO> listByBatchIds(@Param("list") List<String> batchIds);

    /**
     * 根据订单号，查询订单存放的周转区货位
     *
     * @param orderIds
     * @return
     */
    List<OutStockOrderLocationDTO> findBatchOrderLoctionList(@Param("list") List<Long> orderIds);

    /**
     * 根据波次编号，查找所有标记缺货的项
     *
     * @param batchNos
     * @return
     */
    List<BatchTaskItemLackDTO> listLackItemByBatchNos(@Param("list") List<String> batchNos);

    /**
     * 根据拣货任务id和拣货任务编号，查找所有标记缺货的项(全部缺货)
     *
     * @param batchNos
     * @return
     */
    List<BatchTaskItemLackDTO> listLackItemByBatchTask(@Param("batchTaskId") String batchTaskId,
        @Param("batchTaskNo") String batchTaskNo);

    /**
     * 根据拣货任务项id查找所有标记缺货的项
     * 
     * @return
     */
    List<BatchTaskItemLackDTO> listLackItemByBatchTaskItemIds(@Param("list") List<String> batchTaskItemId);

    /**
     * 查找有播种任务的拣货任务项id
     * 
     * @return
     */
    List<String> getIncludeSowTaskBatchTaskItemIds(@Param("list") List<String> batchTaskItemId);

    /**
     * 根据波次编号查询波次详情
     *
     * @param batchNo
     * @return
     */
    BatchPO selectBatchByBatchNo(@Param("orgId") Integer orgId, @Param("batchNo") String batchNo);

    /**
     * 根据拣货任务编号查询波次详情
     *
     * @param taskNo
     * @return
     */
    BatchPO selectBatchByTaskNo(@Param("orgId") Integer orgId, @Param("taskNo") String taskNo);

    List<BatchPO> findBatchByNos(@Param("batchNos") Collection<String> batchNos, @Param("orgId") Integer orgId);

    List<BatchPO> findBatchByOrderNos(@Param("orderNos") List<String> orderNos, @Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId);

    List<BatchPO> findBatchByIds(@Param("batchIds") List<String> batchIds);

    /**
     * 获取波次号查询波次下所有订单id集合
     * 
     * @return
     */
    List<String> listOrderIdByBatchNo(@Param("batchNo") String batchNo);

    /**
     * 批量修改波次的统计信息
     * 
     * @return
     */
    void updateBatchStatistics(@Param("list") List<BatchPO> batchPOs);

    /**
     * 获取团购订单作业进度列表
     *
     * @return
     */
    PageResult<GroupBuyWorkScheduleDTO> listWorkScheduleByGroupBuy(GroupBuyWorkScheduleSO queryDTO);

    /**
     * 修改二次分拣信息
     *
     */
    void updateSecondSort(BatchPO po);

    void updateBatchInfo(BatchPO po);

    /**
     * 根据波次获取状态
     */
    List<BatchPO> listBatchStatusByBatchNos(@Param("batchNos") List<String> batchNos, @Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId);

}
