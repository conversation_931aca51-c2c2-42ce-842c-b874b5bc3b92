package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigType;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/1/22
 */
public class FeatureConfigConvertor {

    public static Byte getConfigFeatureType(Byte orderFeatureType) {
        if (orderFeatureType.equals(OrderFeatureConstant.FEATURE_TYPE_DRINKING)) {
            return WarehouseAllocationConfigType.DRINKING.getValue().byteValue();
        }
        if (orderFeatureType.equals(OrderFeatureConstant.FEATURE_TYPE_REST)) {
            return WarehouseAllocationConfigType.REST.getValue().byteValue();
        }

        return null;
    }

}
