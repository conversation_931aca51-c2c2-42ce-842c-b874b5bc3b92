package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
public class BatchTaskItemBatchUpdateDTO implements Serializable {

    private List<String> batchTaskItemIds;

    private Byte largePickPattern;

    private Byte taskState;

    private Long locationId;

    private String locationName;

    /**
     * 获取
     *
     * @return batchTaskItemIds
     */
    public List<String> getBatchTaskItemIds() {
        return this.batchTaskItemIds;
    }

    /**
     * 设置
     *
     * @param batchTaskItemIds
     */
    public void setBatchTaskItemIds(List<String> batchTaskItemIds) {
        this.batchTaskItemIds = batchTaskItemIds;
    }

    /**
     * 获取
     *
     * @return largePickPattern
     */
    public Byte getLargePickPattern() {
        return this.largePickPattern;
    }

    /**
     * 设置
     *
     * @param largePickPattern
     */
    public void setLargePickPattern(Byte largePickPattern) {
        this.largePickPattern = largePickPattern;
    }

    /**
     * 获取
     *
     * @return taskState
     */
    public Byte getTaskState() {
        return this.taskState;
    }

    /**
     * 设置
     *
     * @param taskState
     */
    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    /**
     * 获取
     *
     * @return locationId
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置
     *
     * @param locationId
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取
     *
     * @return locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置
     *
     * @param locationName
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
