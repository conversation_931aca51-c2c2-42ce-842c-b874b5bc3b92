package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.sowtask;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskResultBO;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.split.WaveSplitRobotBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.util.OrderRebuildUtil;
import com.yijiupi.himalaya.supplychain.waves.util.RobotPickConstants;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 *
 * <AUTHOR>
 * @date 2024/8/18
 */
public abstract class CreateSowTaskBaseBL {

    @Autowired
    protected CreateSowTaskByOutStockOrderBL createSowTaskByOutStockOrderBL;
    @Autowired
    protected WaveSplitRobotBL waveSplitRobotBL;
    protected static final Logger LOG = LoggerFactory.getLogger(CreateSowTaskBaseBL.class);

    public boolean support(CreateSowTaskBO bo) {
        return doSupport(bo);
    }

    protected abstract boolean doSupport(CreateSowTaskBO bo);

    public CreateSowTaskResultBO createSowTask(CreateSowTaskBO bo) {
        if (BooleanUtils.isFalse(support(bo))) {
            return null;
        }

        return doCreateSowTask(bo);
    }

    protected abstract CreateSowTaskResultBO doCreateSowTask(CreateSowTaskBO bo);

    // 如果开了播种墙不打包，大件的订单也进 播种，排除；返回需要播种的订单
    protected List<OutStockOrderPO> openLiquidNotPackageCreateInvolveSowOrderList(WarehouseConfigDTO warehouseConfigDTO,
        WavesStrategyDTO wavesStrategyDTO, List<OutStockOrderPO> orders,
        List<OutStockOrderItemPO> packageStoreItemList) {
        if (!RobotPickConstants.openLiquidNotPackage(warehouseConfigDTO, wavesStrategyDTO)) {
            return orders;
        }
        if (CollectionUtils.isEmpty(packageStoreItemList)) {
            return orders;
        }

        List<OutStockOrderItemPO> totalOutStockOrderItemList =
            orders.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList());

        List<OutStockOrderItemPO> lstOtherItems = totalOutStockOrderItemList.stream()
            .filter(p -> packageStoreItemList.stream()
                .noneMatch(q -> q.getId().equals(p.getId()) && Objects.equals(q.getLocationId(), p.getLocationId())
                    && Objects.equals(q.getLargePick(), p.getLargePick())))
            .collect(Collectors.toList());

        List<OutStockOrderPO> otherOrderList = OrderRebuildUtil.getOrdersByPassageItems(orders, lstOtherItems);

        return otherOrderList;
    }

    protected WaveCreateDTO createLiquidNotPackageWaveCreateDTO(WarehouseConfigDTO warehouseConfigDTO,
        WaveCreateDTO createDTO, List<OutStockOrderItemPO> packageStoreItemList, List<OutStockOrderPO> orders) {
        // 开启了液晶屏的，先把整件的全部拿出来，设置货位为存储区，按订单生成拣货任务，并设置出库位为周转区
        WaveCreateDTO waveCreateDTO = waveSplitRobotBL.createLiquidNotPackageWaveCreateDTO(warehouseConfigDTO,
            packageStoreItemList, createDTO, orders);
        if (Objects.nonNull(waveCreateDTO)) {
            return waveCreateDTO;
        }
        return SplitWaveOrderUtil.copyWaveCreateDTO(createDTO, orders, packageStoreItemList);
    }
}
