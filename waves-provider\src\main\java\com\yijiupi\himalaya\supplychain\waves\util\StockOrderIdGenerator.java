/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.waves.util;

import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * @Title: DriverMarkServiceImpl.java
 * @Package com.yijiupi.himalaya.supplychain.delivery.service.impl.driver
 * @Description: id生成器.
 * <AUTHOR>
 * @date 2017年9月5日 下午3:55:03
 * @version
 */
@Component
public class StockOrderIdGenerator {
    private DateFormat dateFormat = new SimpleDateFormat("yyMMddHH");
    private NumberFormat nf;
    private BoundValueOperations<Object, Object> boundValueOperations;
    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @PostConstruct
    public void init() {
        nf = NumberFormat.getInstance();
        // 设置是否使用分组
        nf.setGroupingUsed(false);
        // 设置最大整数位数
        nf.setMaximumIntegerDigits(5);
        // 设置最小整数位数
        nf.setMinimumIntegerDigits(5);
        boundValueOperations =
            redisTemplate.boundValueOps(String.format("supf:stockorder:IdGenerator:%s", "stockorder_id"));
    }

    /*
     * 生成打印ID
     */
    public Long generatorLocationId(int cityId) {
        return generator(cityId, 2);
    }

    public Long generator(int cityId, int type) {
        // if (cityId > 999) {
        // throw new RuntimeException("生成订单编号时传入的城市编号和订单类型不合法：cityId[" + cityId + "],type[" + type + "]");
        // }
        StringBuilder stringBuilder = new StringBuilder();
        Date date = new Date();
        stringBuilder.append(cityId).append(type).append(dateFormat.format(date)).append(getSequence());
        return Long.valueOf(stringBuilder.toString());
    }

    private String getSequence() {
        long sequence = boundValueOperations.increment(1);
        return nf.format(sequence);
    }
}
