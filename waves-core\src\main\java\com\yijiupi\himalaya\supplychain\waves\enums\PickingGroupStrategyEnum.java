package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 拣货分组策略 常量类
 *
 * <AUTHOR> 2018/3/13
 */
public enum PickingGroupStrategyEnum {
    /**
     * 枚举
     */
    货区((byte)1), 货位((byte)2), 类目((byte)3);

    /**
     * type
     */
    private Byte type;

    PickingGroupStrategyEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 返回枚举
     *
     * @param type
     * @return
     */
    public static PickingGroupStrategyEnum getEnum(Integer type) {
        PickingGroupStrategyEnum e = null;

        if (type != null) {
            for (PickingGroupStrategyEnum o : values()) {
                if (o.getType() == type) {
                    e = o;
                }
            }
        }
        return e;
    }

}
