package com.yijiupi.himalaya.supplychain.waves.dto.secondsort;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/4
 */
public class SecondPickLocationSyncDTO implements Serializable {
    private static final long serialVersionUID = 5310993602691874776L;
    /**
     * 城市ID
     */
    private Integer orgId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 批次货位列表
     */
    private List<SecondPickLocationDTO> locationList;
    /**
     * 操作人ID
     */
    private Integer optUserId;
    /**
     * 操作人名称
     */
    private String optUserName;

    public List<SecondPickLocationDTO> getLocationList() {
        return locationList;
    }

    public void setLocationList(List<SecondPickLocationDTO> locationList) {
        this.locationList = locationList;
    }

    public Integer getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }

    public String getOptUserName() {
        return optUserName;
    }

    public void setOptUserName(String optUserName) {
        this.optUserName = optUserName;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
