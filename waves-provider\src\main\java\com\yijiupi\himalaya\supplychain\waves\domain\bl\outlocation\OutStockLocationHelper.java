package com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation;

import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.RecommendOutLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-11-24 09:14
 **/
public class OutStockLocationHelper {

    /**
     * 把推荐出库位的查询条件拼接程字符串
     */
    public static String getOutLocationQueryKey(LocationRuleDTO rule) {
        return getOutLocationQueryKey(rule, null, null);
    }

    /**
     * 把推荐出库位的查询条件拼接程字符串
     */
    public static String getOutLocationQueryKey(LocationRuleDTO rule, Long carId, Long LogisticsCompanyId) {
        return String.format("%s_%s_%s_%s_%s_%s_%s_%s_%s", rule.getRuleId(), rule.getAddressId(), rule.getProvince(),
                rule.getCity(), rule.getDistrict(), rule.getStreet(), rule.getAreaId(), LogisticsCompanyId, carId);
    }

    /**
     * 把推荐出库位的查询条件拼接程字符串
     */
    public static String getOutLocationQueryKey(RecommendOutLocationQueryDTO rule) {
        return String.format("%s_%s_%s_%s_%s_%s_%s_%s_%s", rule.getRuleId(), rule.getAddressId(), rule.getProvince(),
                rule.getCity(), rule.getDistrict(), rule.getStreet(), rule.getAreaId(), rule.getLogisticsCompanyId(),
                rule.getCarId());
    }


    /**
     * 补齐目标仓库信息
     */
    public static void fillWarehouseInfo(List<OutStockOrderLocationDTO> orderLocation, Function<List<Integer>, List<Warehouse>> warehouseQuery) {
        List<Integer> toWarehouseIds = orderLocation.stream().map(OutStockOrderLocationDTO::getToWarehouseId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(toWarehouseIds)) {
            List<Warehouse> warehouses = warehouseQuery.apply(toWarehouseIds);
            if (!CollectionUtils.isEmpty(warehouses)) {
                Map<Integer, Warehouse> warehouseMap = warehouses.stream()
                        .collect(Collectors.toMap(Warehouse::getId, Function.identity(), (key1, key2) -> key2));
                Map<Integer, List<OutStockOrderLocationDTO>> orderLocationMap = orderLocation.stream()
                        .filter(location -> location.getToWarehouseId() != null)
                        .collect(Collectors.groupingBy(OutStockOrderLocationDTO::getToWarehouseId));
                for (Map.Entry<Integer, List<OutStockOrderLocationDTO>> entry : orderLocationMap.entrySet()) {
                    Warehouse warehouse = warehouseMap.get(entry.getKey());
                    if (warehouse != null) {
                        List<OutStockOrderLocationDTO> locations = entry.getValue();
                        for (OutStockOrderLocationDTO location : locations) {
                            location.setToCity(warehouse.getCity());
                            location.setToWarehouseName(warehouse.getName());
                        }
                    }
                }
            }
        }
    }

}
