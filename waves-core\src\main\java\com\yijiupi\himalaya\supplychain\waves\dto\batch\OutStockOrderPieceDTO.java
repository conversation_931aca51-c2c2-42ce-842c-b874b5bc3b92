package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023-12-11 15:02
 **/
public class OutStockOrderPieceDTO implements Serializable {

    /**
     * 出库单 id
     */
    private Long outStockOrderId;

    /**
     * 重新计算后的大件数量
     */
    private BigDecimal packageCount;

    /**
     * 重新计算后的小件数量
     */
    private BigDecimal unitCount;

    private OutStockOrderPieceDTO(Long outStockOrderId, BigDecimal packageCount, BigDecimal unitCount) {
        this.outStockOrderId = outStockOrderId;
        this.packageCount = packageCount;
        this.unitCount = unitCount;
    }

    public static OutStockOrderPieceDTO of(Long outStockOrderId, BigDecimal packageCount, BigDecimal unitCount) {
        return new OutStockOrderPieceDTO(outStockOrderId, packageCount, unitCount);
    }

    public Long getOutStockOrderId() {
        return outStockOrderId;
    }

    public OutStockOrderPieceDTO setOutStockOrderId(Long outStockOrderId) {
        this.outStockOrderId = outStockOrderId;
        return this;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public OutStockOrderPieceDTO setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
        return this;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public OutStockOrderPieceDTO setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
        return this;
    }
}
