package com.yijiupi.himalaya.supplychain.waves.dto.stockup;

import com.yijiupi.himalaya.base.search.PagerCondition;

/**
 * <AUTHOR>
 * @since 2024-10-11 15:17
 **/
public class StockUpRecordQueryParam extends PagerCondition {

    private Long recordId;

    private Integer orgId;

    private Integer warehouseId;

    private String startTime;

    private String endTime;

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
