package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yijiupi.himalaya.base.search.PagerCondition;

/**
 * <AUTHOR> 2018/3/17
 */
public class BatchTaskItemQueryDTO extends PagerCondition implements Serializable {

    private static final long serialVersionUID = -4801234497833801622L;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 波次任务编号
     */
    private String batchTaskNo;

    /**
     * 波次编号
     */
    private String batchNo;

    /**
     * 订单编号
     */
    private String refOrderNo;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 分拣员id
     */
    private Integer sorterId;

    /**
     * 分拣员
     */
    private String sorter;

    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte taskState;

    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private List<Byte> taskStateList;

    /**
     * 创建起始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 创建结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 排序标示：默认需要排序
     */
    private Boolean sortFlag = true;
    /**
     * 分拣任务项ID列表
     */
    private List<String> batchTaskItemIdList;
    /**
     * 波次任务编号
     */
    private String batchTaskId;
    /**
     * 波次任务编号列表
     */
    private List<String> batchTaskIdList;

    public List<String> getBatchTaskIdList() {
        return batchTaskIdList;
    }

    public void setBatchTaskIdList(List<String> batchTaskIdList) {
        this.batchTaskIdList = batchTaskIdList;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public List<String> getBatchTaskItemIdList() {
        return batchTaskItemIdList;
    }

    public void setBatchTaskItemIdList(List<String> batchTaskItemIdList) {
        this.batchTaskItemIdList = batchTaskItemIdList;
    }

    public List<Byte> getTaskStateList() {
        return taskStateList;
    }

    public void setTaskStateList(List<Byte> taskStateList) {
        this.taskStateList = taskStateList;
    }

    public Boolean getSortFlag() {
        return sortFlag;
    }

    public void setSortFlag(Boolean sortFlag) {
        this.sortFlag = sortFlag;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getSorterId() {
        return sorterId;
    }

    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    public String getSorter() {
        return sorter;
    }

    public void setSorter(String sorter) {
        this.sorter = sorter;
    }

    public Byte getTaskState() {
        return taskState;
    }

    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }
}
