package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.math.BigDecimal;
import java.util.Date;

public class SowOrderInfoPO {
    /**
     * 订单编号
     */
    private String refOrderNo;

    /**
     * 下单时间
     */
    private Date orderCreateTime;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 地址
     */
    private String detailAddress;

    /**
     * 订单类型0=酒批订单，1=招商订单，2=经销商直配订单，3=大货批发订单，4=经济人撮合订单，10=大商转配送订单，11=轻加盟订单，12=经销商订单，13=易经销订单，14=兑奖订单
     */
    private Byte orderType;

    /**
     * 播种任务下的订单商品种类数
     */
    private Integer skuCount;

    /**
     * 播种任务下的订单大件总数
     */
    private BigDecimal packageAmount;

    /**
     * 播种任务下的订单小件总数
     */
    private BigDecimal unitAmount;

    /**
     * 已播种Sku数量
     */
    private Integer sownSkuCount;

    /**
     * 已播种大件数量
     */
    private BigDecimal sownPackageAmount;

    /**
     * 已播种小件数量
     */
    private BigDecimal sownUnitAmount;

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Date getOrderCreateTime() {
        return orderCreateTime;
    }

    public void setOrderCreateTime(Date orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    public Byte getOrderType() {
        return orderType;
    }

    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public BigDecimal getPackageAmount() {
        return packageAmount;
    }

    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    public BigDecimal getUnitAmount() {
        return unitAmount;
    }

    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    public Integer getSownSkuCount() {
        return sownSkuCount;
    }

    public void setSownSkuCount(Integer sownSkuCount) {
        this.sownSkuCount = sownSkuCount;
    }

    public BigDecimal getSownPackageAmount() {
        return sownPackageAmount;
    }

    public void setSownPackageAmount(BigDecimal sownPackageAmount) {
        this.sownPackageAmount = sownPackageAmount;
    }

    public BigDecimal getSownUnitAmount() {
        return sownUnitAmount;
    }

    public void setSownUnitAmount(BigDecimal sownUnitAmount) {
        this.sownUnitAmount = sownUnitAmount;
    }
}
