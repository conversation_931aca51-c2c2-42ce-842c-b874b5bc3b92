<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.StockUpRecordPOMapper">
    <select id="selectByTaskNO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stockuprecord
        <where>
            WarehouseId = #{warehouseId,jdbcType=INTEGER}
            and taskNo = #{taskNO,jdbcType=VARCHAR}
        </where>
    </select>

    <select id="pageListStockUpRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stockuprecord
        <where>
            WarehouseId = #{warehouseId,jdbcType=INTEGER}
            and OrgId = #{orgId,jdbcType=INTEGER}
            <if test="recordId != null">
                and Id = #{recordId,jdbcType=BIGINT}
            </if>
            <if test="startTime != null and startTime != ''">
                and CreateTime >= #{startTime,jdbcType=VARCHAR}
            </if>
            <if test="endTime != null and endTime != ''">
                and CreateTime <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>