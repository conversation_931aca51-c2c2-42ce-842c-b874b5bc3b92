package com.yijiupi.himalaya.supplychain.waves.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderProcessTransferBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.CheckOrderRepeatDTO;

/**
 * 检查订单是否重复生波次
 *
 * <AUTHOR>
 * @date 12/17/20 2:29 PM
 */
@Service
public class CheckOrderBatchListener {

    private static final Logger LOG = LoggerFactory.getLogger(CheckOrderBatchListener.class);

    @Autowired
    private BatchOrderProcessTransferBL batchOrderProcessTransferBL;

    /**
     * 检查订单是否重复生波次
     */
    @RabbitListener(queues = "${mq.supplychain.createBatch.checkOrder}")
    public void checkOrder(Message message) {
        try {
            // 延迟1秒
            Thread.sleep(1000);

            String json = new String(message.getBody(), "UTF-8");
            String messageId = message.getMessageProperties().getMessageId();
            LOG.info("检查订单>>>【mq.supplychain.createBatch.checkOrder】[{}]{}", messageId, json);
            CheckOrderRepeatDTO checkOrderRepeatDTO = JSON.parseObject(json, CheckOrderRepeatDTO.class);
            batchOrderProcessTransferBL.checkOrderCreateBatchRepeat(checkOrderRepeatDTO);
        } catch (Exception e) {
            LOG.error("[检查订单是否重复生波次]异常", e);
        }
    }

}
