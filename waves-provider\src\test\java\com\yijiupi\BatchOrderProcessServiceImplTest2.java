package com.yijiupi;

import java.util.Arrays;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchOrderProcessService;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;
import com.yijiupi.junit.runner.GeneralRunner;

/**
 * <AUTHOR>
 * @title: BatchOrderProcessServiceImplTest2
 * @description:
 * @date 2022-12-29 09:47
 */
@RunWith(GeneralRunner.class)
public class BatchOrderProcessServiceImplTest2 {

    @Reference
    private IBatchOrderProcessService iBatchOrderProcessService;
    private static final Logger LOG = LoggerFactory.getLogger(BatchOrderProcessServiceImplTest2.class);

    // 1
    @Test
    public void createBatchTest() {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setCityId(998);
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setPickingType((byte)2);
        batchCreateDTO.setPickingGroupStrategy((byte)2);
        batchCreateDTO.setPassPickType((byte)1);
        batchCreateDTO.setGroupType(1);
        batchCreateDTO.setOperateUser("临时外包-张裔譞阿里");
        batchCreateDTO.setOrderIdList(Arrays.asList("5335597517408037731"));

        iBatchOrderProcessService.createBatch(batchCreateDTO);

        Assert.assertNotNull(batchCreateDTO);
    }

    @Test
    public void createBatchTest2() {
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setCityId(998);
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setPickingType((byte)1);
        batchCreateDTO.setPickingGroupStrategy((byte)3);
        batchCreateDTO.setPassPickType((byte)1);
        batchCreateDTO.setGroupType(1);
        batchCreateDTO.setOperateUser("临时外包-张裔譞阿里");
        batchCreateDTO.setOrderIdList(Arrays.asList("998000230110170777"));

        iBatchOrderProcessService.createBatch(batchCreateDTO);

        Assert.assertNotNull(batchCreateDTO);
    }

}
