package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;
import java.math.BigDecimal;

public class SowTaskCancelItemDTO implements Serializable {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单项id
     */
    private Long orderItemId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 变更后的数量
     */
    private BigDecimal overUnitTotalCount;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 差异大单位数量
     */
    private BigDecimal changePackageCount;

    /**
     * 差异小单位数量
     */
    private BigDecimal changeUnitCount;

    /**
     * 差异小单位总数量
     */
    private BigDecimal changeCount;

    /**
     * 播种任务明细id
     */
    private Long sowTaskItemId;

    /**
     * 拣货变更数量
     */
    private BigDecimal batchTaskChangeCount;

    public BigDecimal getChangeCount() {
        return changeCount;
    }

    public void setChangeCount(BigDecimal changeCount) {
        this.changeCount = changeCount;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getOverUnitTotalCount() {
        return overUnitTotalCount;
    }

    public void setOverUnitTotalCount(BigDecimal overUnitTotalCount) {
        this.overUnitTotalCount = overUnitTotalCount;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Long getSowTaskItemId() {
        return sowTaskItemId;
    }

    public void setSowTaskItemId(Long sowTaskItemId) {
        this.sowTaskItemId = sowTaskItemId;
    }

    public BigDecimal getChangePackageCount() {
        return changePackageCount;
    }

    public void setChangePackageCount(BigDecimal changePackageCount) {
        this.changePackageCount = changePackageCount;
    }

    public BigDecimal getChangeUnitCount() {
        return changeUnitCount;
    }

    public void setChangeUnitCount(BigDecimal changeUnitCount) {
        this.changeUnitCount = changeUnitCount;
    }

    public BigDecimal getBatchTaskChangeCount() {
        return batchTaskChangeCount;
    }

    public void setBatchTaskChangeCount(BigDecimal batchTaskChangeCount) {
        this.batchTaskChangeCount = batchTaskChangeCount;
    }
}
