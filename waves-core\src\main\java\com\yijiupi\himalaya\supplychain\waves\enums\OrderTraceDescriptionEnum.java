package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 操作记录 - 日志内容
 * 
 * <AUTHOR>
 * @date 2018/5/22 17:06
 */
public enum OrderTraceDescriptionEnum {
    /**
     * 波次
     */
    自动组建波次成功, 手动组建波次成功, 波次删除, 波次开始拣货, 波次完成拣货, 波次完成出库,

    /**
     * 波次任务
     */
    新建拣货任务成功, 指派拣货员, 开始拣货, 提交拣货, 拣货完成, 删除拣货任务, 打印拣货单, 拆分的拣货任务新建成功, 拆分拣货任务成功, 合并的拣货任务新建成功, 合并拣货任务成功, 领取拣货任务成功, 通知电子标签成功,

    /**
     * 上架任务
     */
    新建上架任务, 指派上架员, 开始上架, 上架任务完成, 打印上架单,

    /**
     * 播种任务
     */
    新建播种任务成功, 播种任务领取成功, 播种任务完成, 播种任务删除, 播种任务追加成功,

    /**
     * 加工单
     */
    新建加工单, 指派加工员, 开始加工, 加工单完成, 打印加工单,

    /**
     * 补货任务
     */
    新建补货任务, 指派补货员, 开始补货, 补货中, 补货任务完成,

    /**
     * 打包复核
     */
    指派复核, 开始打包复核, 打包复核完成,

    /**
     * 出库批次
     */
    创建出库批次, 出库交接单打印成功, 出库交接单上传成功, 订单移除, 仓库确认出库;
}
