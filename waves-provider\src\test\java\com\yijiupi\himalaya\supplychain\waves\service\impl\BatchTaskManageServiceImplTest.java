package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.*;

/**
 * <AUTHOR> 2018/5/8
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class BatchTaskManageServiceImplTest {

    @Autowired
    private BatchTaskManageServiceImpl batchTaskManageService;

    @Test
    public void batchTaskComplete() throws Exception {
        batchTaskManageService.batchTaskComplete(null);
    }

    @Test
    public void findAreaListByTaskNo() {
        List<String> lstTmp =
            batchTaskManageService.findAreaListByTaskNo(Collections.singletonList("BT100118060200002"));

    }

    @Test
    public void listNotSplitOrderByBatchTaskNo() {
        PageList<BatchTaskSplitOrderDTO> batchTaskSplitOrderDTOPageList =
            batchTaskManageService.listNotSplitOrderByBatchTaskNo("BT402118071100006", 1, 20);
        System.out.println(JSON.toJSONString(batchTaskSplitOrderDTOPageList));
    }

    /**
     * 拆分拣货任务
     */
    @Test
    public void batchTaskConfirmSplit() {
        BatchTaskSplitDTO batchTaskSplitDTO = new BatchTaskSplitDTO();
        batchTaskSplitDTO.setBatchTaskNo("BT402118071100006");
        batchTaskSplitDTO.setPickingType((byte)1);
        batchTaskSplitDTO.setOperateUser("余攀");
        List<BatchTaskDTO> batchTaskList = new ArrayList<>();

        BatchTaskDTO batchTaskDTO = new BatchTaskDTO();
        batchTaskDTO.setBatchTaskNo("BT402118071100006-1");
        batchTaskDTO.setSorterId(1);
        batchTaskDTO.setSorter("张三");
        batchTaskDTO.setOrderCount(1);
        batchTaskDTO.setSkuCount(1);
        batchTaskDTO.setPackageAmount(new BigDecimal(2));
        batchTaskDTO.setUnitAmount(new BigDecimal(0));
        List<BatchTaskItemDTO> batchTaskItemDTOList = new ArrayList<>();
        BatchTaskItemDTO batchTaskItemDTO = new BatchTaskItemDTO();
        batchTaskItemDTO.setId("");
        batchTaskItemDTO.setRefOrderNo("402818700099");
        batchTaskItemDTOList.add(batchTaskItemDTO);
        batchTaskDTO.setBatchTaskItemList(batchTaskItemDTOList);
        batchTaskList.add(batchTaskDTO);

        BatchTaskDTO batchTaskDTO2 = new BatchTaskDTO();
        batchTaskDTO2.setBatchTaskNo("BT402118071100006-2");
        batchTaskDTO2.setSorterId(2);
        batchTaskDTO2.setSorter("李四");
        batchTaskDTO2.setOrderCount(1);
        batchTaskDTO2.setSkuCount(1);
        batchTaskDTO2.setPackageAmount(new BigDecimal(1));
        batchTaskDTO2.setUnitAmount(new BigDecimal(0));
        List<BatchTaskItemDTO> batchTaskItemDTOList2 = new ArrayList<>();
        BatchTaskItemDTO batchTaskItemDTO2 = new BatchTaskItemDTO();
        batchTaskItemDTO2.setId("");
        batchTaskItemDTO2.setRefOrderNo("402818700126");
        batchTaskItemDTOList2.add(batchTaskItemDTO2);
        batchTaskDTO2.setBatchTaskItemList(batchTaskItemDTOList2);
        batchTaskList.add(batchTaskDTO2);

        batchTaskSplitDTO.setBatchTaskList(batchTaskList);
        batchTaskManageService.batchTaskConfirmSplit(batchTaskSplitDTO);
    }

    /**
     * 合并拣货任务
     */
    @Test
    public void batchTaskConfirmMerge() {
        BatchTaskMergeDTO batchTaskMergeDTO = new BatchTaskMergeDTO();
        batchTaskMergeDTO.setWarehouseId(4021);
        batchTaskMergeDTO.setOrgId("402");
        batchTaskMergeDTO.setSorterId(123);
        batchTaskMergeDTO.setSorter("王麻子");
        batchTaskMergeDTO.setOperateUser("余攀");
        List<String> batchTaskNoList = new ArrayList<>();
        batchTaskNoList.add("BT402118071100006-1");
        batchTaskNoList.add("BT402118071100006-2");
        batchTaskMergeDTO.setBatchTaskNoList(batchTaskNoList);
        batchTaskManageService.batchTaskConfirmMerge(batchTaskMergeDTO);
    }
}