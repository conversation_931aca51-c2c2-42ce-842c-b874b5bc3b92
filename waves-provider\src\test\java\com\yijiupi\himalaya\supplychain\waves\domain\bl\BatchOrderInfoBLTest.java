package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchFinishedBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskCompleteBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskItemCompleteBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchBaseBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.AppendSowTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch.CreateBatchByManualOperationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.lock.LockCreateBatchBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.AppendSowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.DeleteBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.CompleteBatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskConfirmDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskUpdateLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderWaitDeliveryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.PalletReviewQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.ProductAllotTypeDTO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderItemProductSO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderSearchSO;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderWaitDeliverySO;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR> 2018/4/2
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class BatchOrderInfoBLTest {

    @Autowired
    private BatchOrderInfoBL batchOrderInfoBL;
    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;

    @Test
    public void batchTaskCompleteTest() {
        String json =
            "{\"batchTaskItemList\":[{\"id\":\"2024022000173\",\"isWine\":true,\"lackUnitCount\":1.0,\"productIdentityKey\":\"null_null_0_0_null_null\"},{\"id\":\"2024022000172\",\"isWine\":true,\"lackUnitCount\":1.0,\"productIdentityKey\":\"null_null_0_0_null_null\"}],\"batchTaskNo\":[\"BT998124022000003\"],\"locationId\":5112738057499284508,\"locationName\":\"CKA07-04\",\"operateUser\":\"黄景瑜\",\"operateUserId\":68196250,\"orgId\":998,\"sorterId\":17630,\"sorterName\":\"肖丽君\",\"warehouseId\":9981}";

        BatchTaskConfirmDTO batchTaskConfirmDTO = JSON.parseObject(json, BatchTaskConfirmDTO.class);

        batchOrderTaskBL.batchTaskComplete(batchTaskConfirmDTO);
    }

    @Test
    public void updateBatchTaskItemTest() {
        String json =
            "[{\"completeTime\":1714974297000,\"containerList\":[],\"fromLocationId\":5269574289392649092,\"fromLocationName\":\"WL-D10-02-33\",\"id\":\"2024022000172\",\"lackPackageCount\":0,\"lackUnitCount\":2,\"orderSequence\":6897,\"overSortPackageCount\":0,\"overSortUnitCount\":2,\"startTime\":1714974288000,\"submitFlag\":0}]";

        List<BatchTaskItemCompleteDTO> batchTaskItemUpdateDTOList =
            JSON.parseObject(json, new TypeReference<List<BatchTaskItemCompleteDTO>>() {}.getType());

        batchOrderTaskBL.updateBatchTaskItem(batchTaskItemUpdateDTOList, "2024022000171", "魏雪", 9981, null, null, 998,
            7867, (byte)0);
    }

    @Autowired
    private CreateBatchByManualOperationBL createBatchByManualOperationBL;
    @Autowired
    private LockCreateBatchBL lockCreateBatchBL;

    @Test
    public void createBatchTest() {

        lockCreateBatchBL.releaseLock(Arrays.asList(5320020797739031078L, 5314238585624255239L), 9981);
        String json =
            "{\"batchName\":\"手动生成波次\",\"cityId\":998,\"operateUser\":\"黄景瑜\",\"operateUserId\":68196250,\"orderIdList\":[\"5314238585624255239\",\"5320020797739031078\"],\"passPickType\":1,\"pickingGroupStrategy\":2,\"pickingType\":2,\"warehouseId\":9981}";
        BatchCreateDTO batchCreateDTO = JSON.parseObject(json, BatchCreateDTO.class);
        CreateBatchBaseBO createBatchBaseBO = new CreateBatchBaseBO(batchCreateDTO.getWarehouseId(), batchCreateDTO);
        createBatchByManualOperationBL.createBatch(createBatchBaseBO);
    }

    @Test
    public void getProductAllotCountMap() {
        List<Long> productSkuIds = Collections.singletonList(Long.valueOf("1"));
        Map<Long, ProductAllotTypeDTO> map = batchOrderInfoBL.getProductAllotCountMap(0, 20001, productSkuIds);
        System.out.println(JSON.toJSONString(map));
    }

    @Test
    public void findEnableOutStockOrderList() throws Exception {
        OutStockOrderSearchSO outStockOrderSearchSO = new OutStockOrderSearchSO();
        outStockOrderSearchSO.setWareHouseId(9991);
        outStockOrderSearchSO.setRefOrderNo("");
        outStockOrderSearchSO.setTimeS("2018-05-10 00:00:00");
        outStockOrderSearchSO.setTimeE("2018-07-10 23:59:59");
        outStockOrderSearchSO.setPageSize(100);
        outStockOrderSearchSO.setCurrentPage(1);
        PageList<OutStockOrderDTO> enableOutStockOrderList =
            batchOrderInfoBL.findEnableOutStockOrderList(outStockOrderSearchSO);
        System.out.println(JSON.toJSONString(enableOutStockOrderList));
    }

    @Test
    public void listOutStockOrderProduct() throws Exception {
        OutStockOrderItemProductSO so = new OutStockOrderItemProductSO();
        so.setCityId(999);
        so.setWarehouseId(9991);
        List<Long> list = batchOrderInfoBL.listOutStockOrderProduct(so);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void listOutStockOrderItemWaitDelivery() throws Exception {
        OutStockOrderWaitDeliverySO so = new OutStockOrderWaitDeliverySO();
        List<String> skuids = Collections.singletonList("169818873665957209");
        so.setSkuIds(skuids);
        so.setCityId(2000);
        PageList<OutStockOrderWaitDeliveryDTO> pageResult = batchOrderInfoBL.listOutStockOrderItemWaitDelivery(so);
        System.out.println(JSON.toJSONString(pageResult));
    }

    @Autowired
    private BatchFinishedBL batchFinishedBL;

    @Test
    public void finishBatch() throws InterruptedException {
        // BC164123121200007
        batchFinishedBL.completeWave("BC164123121200003", 125, 164);
        CountDownLatch countDownLatch = new CountDownLatch(1);

        countDownLatch.await();
    }

    @Autowired
    private BatchTaskItemCompleteBL batchTaskItemCompleteBL;

    @Test
    public void completeBatchTaskItemTest() {
        CompleteBatchTaskItemDTO completeBatchTaskItemDTO = new CompleteBatchTaskItemDTO();

        completeBatchTaskItemDTO.setBatchTaskId("2024091400133");
        completeBatchTaskItemDTO.setCityId(998);
        completeBatchTaskItemDTO.setWarehouseId(9981);
        completeBatchTaskItemDTO.setLocationId(5112738511406863382L);
        completeBatchTaskItemDTO.setLocationName("CKA09-04");
        completeBatchTaskItemDTO.setUserId(125);
        completeBatchTaskItemDTO.setUserName("wangran");
        completeBatchTaskItemDTO.setContainerFlag((byte)0);

        Date date = new Date();
        List<BatchTaskItemCompleteDTO> itemCompleteDTOS = new ArrayList<>();
        BatchTaskItemCompleteDTO batchTaskItemCompleteDTO = new BatchTaskItemCompleteDTO();
        batchTaskItemCompleteDTO.setCompleteTime(date);
        batchTaskItemCompleteDTO.setFromLocationId(169368317356519199L);
        batchTaskItemCompleteDTO.setFromLocationName("A33-33");
        batchTaskItemCompleteDTO.setId("2024091400135");
        batchTaskItemCompleteDTO.setLackPackageCount(BigDecimal.valueOf(1));
        batchTaskItemCompleteDTO.setLackUnitCount(BigDecimal.ZERO);
        batchTaskItemCompleteDTO.setOverSortPackageCount(BigDecimal.valueOf(1));
        batchTaskItemCompleteDTO.setOverSortUnitCount(BigDecimal.ZERO);
        batchTaskItemCompleteDTO.setStartTime(date);
        batchTaskItemCompleteDTO.setSubmitFlag((byte)0);

        itemCompleteDTOS.add(batchTaskItemCompleteDTO);
        completeBatchTaskItemDTO.setBatchTaskItemUpdateDTOList(itemCompleteDTOS);
        Map<String, List<BatchTaskDTO>> map = batchTaskItemCompleteBL.completeBatchTaskItem(completeBatchTaskItemDTO);

        Assertions.assertThat(map).isNotNull();
    }

    @Test
    public void completeBatchTaskItemTest2() {
        String json =
            "{\"batchTaskId\":\"2024110800205\",\"batchTaskItemUpdateDTOList\":[{\"completeTime\":1731055680737,\"id\":\"2024110800206\",\"lackUnitCount\":0.0,\"overSortUnitCount\":64.0,\"submitFlag\":1}],\"cityId\":998,\"operateSource\":2,\"userId\":67795309,\"userName\":\"测试李\",\"warehouseId\":9981}";
        CompleteBatchTaskItemDTO batchTaskItemCompleteDTO =
            JSON.parseObject(json, new TypeReference<CompleteBatchTaskItemDTO>() {}.getType());
        Map<String, List<BatchTaskDTO>> map = batchTaskItemCompleteBL.completeBatchTaskItem(batchTaskItemCompleteDTO);

        Assertions.assertThat(map).isNotNull();
    }

    @Autowired
    private AppendSowTaskBL appendSowTaskBL;

    @Test
    public void deleteBatchTest() throws InterruptedException {
        // {\"batchNoList\":[\"BC108124112100039\",\"BC108124112100037\"],\"checkOrderState\":true,\"ignoreBatchInventory\":null,\"ignoreBatchState\":false,\"isIsOpCancel\":false,\"operateUser\":\"刘喜娇\",\"operateUserId\":48010}
        DeleteBatchDTO deleteBatchDTO = new DeleteBatchDTO();
        deleteBatchDTO.setBatchNoList(Collections.singletonList("BC998124112100007"));
        deleteBatchDTO.setOperateUser("wr");
        deleteBatchDTO.setOperateUserId(125);

        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setOrderIdList(Collections.singletonList("5388877789334928236"));
        batchCreateDTO.setCityId(998);
        batchCreateDTO.setWarehouseId(9981);
        batchCreateDTO.setPickingType((byte)2);
        batchCreateDTO.setPickingGroupStrategy((byte)2);
        batchCreateDTO.setPassPickType((byte)0);
        batchCreateDTO.setOperateUser("wr");
        // :{"orderIdList":["5385898761554866560"],"cityId":998,"warehouseId":9981,"pickingType":2,"pickingGroupStrategy":2,"operateUser":"李响","passPickType":0}
        appendSowTaskBL.addAndDeleteBatch(deleteBatchDTO, batchCreateDTO);
    }

    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;

    @Test
    public void getBatchInfoTest() {
        List<OutStockOrderPO> outStockOrderPOList =
            outStockOrderMapper.findByOrderId(Collections.singletonList(5388877789334928236L));
        BatchPO batchPO = batchMapper.selectBatchByBatchNo(998, "BC998124112100007");
        AssertUtils.notNull(batchPO);
    }

    @Test
    public void handleBatchNotStartTest() throws InterruptedException {
        BatchPO batchPO = batchMapper.selectBatchByBatchNo(998, "BC998124112100011");

        AppendSowTaskDTO appendSowTaskDTO = new AppendSowTaskDTO();
        appendSowTaskDTO.setOutStockOrderIds(Collections.singletonList("5388877789334928236"));
        appendSowTaskDTO.setWarehouseId(9981);
        appendSowTaskDTO.setOptUserId(125);
        appendSowTaskDTO.setSowTaskId(1L);
        appendSowTaskDTO.setOrgId(998);

        appendSowTaskBL.handleBatchNotStart(appendSowTaskDTO, batchPO);
    }

    @Test
    public void queryPalletReviewInfoTest() {
        PalletReviewQueryDTO queryDTO = new PalletReviewQueryDTO();
        queryDTO.setWarehouseId(9981);
        queryDTO.setPalletNo("1");
        batchOrderInfoBL.queryPalletReviewInfo(queryDTO);
    }

    @Test
    public void updateBatchTaskLocationTest() {
        String json =
            "{\"batchTaskIdList\":[\"2025061700128\"],\"locationId\":5464292529614532225,\"locationName\":\"CKK4-2\",\"operateUser\":\"测试李\"}";
        BatchTaskUpdateLocationDTO updateLocationDTO = JSON.parseObject(json, BatchTaskUpdateLocationDTO.class);

        batchOrderTaskBL.updateBatchTaskLocationByCk(updateLocationDTO);
    }

    @Autowired
    private BatchTaskCompleteBL batchTaskCompleteBL;

    @Test
    public void completeBatchTaskTest() {
        // String json =
        // "{\"batchTaskId\":\"2025061700128\",\"operateSource\":1,\"optUserId\":67795309,\"orderPalletInfoDTOS\":[{\"orderId\":5418950330619056677,\"orderNo\":\"998503600005\",\"palletNoList\":[\"3\"]}],\"warehouseId\":9981}";
        String json =
            "{\"batchTaskId\":\"2025061700130\",\"appCode\":\"PDA\",\"orderPalletInfoDTOS\":[{\"palletNoList\":[2,3,4],\"orderNo\":\"998503600005\",\"orderId\":\"5418950330619056677\"}],\"cityId\":\"998\"}";
        BatchTaskCompleteDTO batchTaskCompleteDTO = JSON.parseObject(json, BatchTaskCompleteDTO.class);
        batchTaskCompleteBL.completeBatchTask(batchTaskCompleteDTO);
    }

}