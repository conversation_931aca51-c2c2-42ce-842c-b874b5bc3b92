package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor;

import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductFeatureEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.LocationDivideHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @title: LocationDivideHelperBOConvertor
 * @description:
 * @date 2023-02-08 10:28
 */
public class LocationDivideHelperBOConvertor {

    private static final Logger LOG = LoggerFactory.getLogger(LocationDivideHelperBOConvertor.class);

    public static List<LocationDivideHelperBO> convert(Map<Long, List<OutStockOrderItemPO>> skuGroupMap,
                                                       Map<Long, Byte> skuFeatureMap) {

        List<LocationDivideHelperBO> boList = new ArrayList<>();
        for (Map.Entry<Long, List<OutStockOrderItemPO>> entry : skuGroupMap.entrySet()) {
            Byte feature = skuFeatureMap.get(entry.getKey());
            if (BooleanUtils.isFalse(isNeedSplit(feature))) {
                continue;
            }

            BigDecimal totalUnitCount = entry.getValue().stream().map(OutStockOrderItemPO::getUnittotalcount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            OutStockOrderItemPO itemPO = entry.getValue().stream().findFirst().get();

            LocationDivideHelperBO bo = new LocationDivideHelperBO();
            bo.setSkuId(entry.getKey());
            bo.setUnitTotalCount(totalUnitCount);
            bo.setSpecQuantity(itemPO.getSpecquantity());
            bo.setSaleSpecQuantity(itemPO.getSalespecquantity());
            BigDecimal packageCount = entry.getValue().stream().map(OutStockOrderItemPO::getPackagecount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal unitCount = entry.getValue().stream().map(OutStockOrderItemPO::getUnitcount).reduce(BigDecimal.ZERO, BigDecimal::add);
            bo.setPackageCount(packageCount);
            bo.setUnitCount(unitCount);
            bo.setItemList(entry.getValue());

            boList.add(bo);
        }

        return boList;
    }

    public static List<OutStockOrderItemPO> convertItem(Map<Long, List<OutStockOrderItemPO>> skuGroupMap,
                                                        Map<Long, Byte> skuFeatureMap) {

        List<OutStockOrderItemPO> outStockOrderItemPOList = new ArrayList<>();
        for (Map.Entry<Long, List<OutStockOrderItemPO>> entry : skuGroupMap.entrySet()) {
            Byte feature = skuFeatureMap.get(entry.getKey());
            if (BooleanUtils.isTrue(isNeedSplit(feature))) {
                continue;
            }

            outStockOrderItemPOList.addAll(entry.getValue());
        }

        return outStockOrderItemPOList;
    }

    private static boolean isNeedSplit(Byte feature) {
        if (Objects.isNull(feature)) {
            return Boolean.FALSE;
        }
        if (ProductFeatureEnum.小件.getType() == feature) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

}
