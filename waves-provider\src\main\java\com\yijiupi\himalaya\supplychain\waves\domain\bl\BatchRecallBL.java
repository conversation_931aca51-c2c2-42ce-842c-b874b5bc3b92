package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter.BatchRecallDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.util.RestTemplateUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-08-15 15:20
 **/
@Service
public class BatchRecallBL {

    @SuppressWarnings("HttpUrlsUsage")
    private static final String BASE_URL = "http://in-scapi.yjp.com/apitms";
    private static final String TMS_BATCH_RECALL_URL = BASE_URL + "/ordercenter/IWarehousePickService/recallWaveNo";
    private static final String TMS_BATCH_RECALL_URL_NEW = "http://in-scapi.yjp.com/apitms/newservice/IWmsCombineNewService/recallWaveNo";
    private static final Logger logger = LoggerFactory.getLogger(BatchRecallBL.class);
    @Resource
    private RestTemplateUtils restTemplateUtils;
    @Resource
    private OutStockOrderMapper outStockOrderMapper;

    /**
     * 波次召回 通知 TMS, 兼容新老系统
     */
    public void batchRecall(List<String> batchNos, Integer optUserId, Integer warehouseId, Integer orgId) {
        List<OutStockOrderPO> outstockOrderList = outStockOrderMapper.findByBatchNos(batchNos, orgId);
        List<Long> orderIds = outstockOrderList.stream().map(OutStockOrderPO::getBusinessId)
                .filter(NumberUtils::isDigits).map(Long::valueOf)
                .collect(Collectors.toList());
        if (orderIds.isEmpty()) {
            logger.info("没有 orderId, 跳过后续处理: {}", batchNos);
            return;
        }
        BatchRecallDTO dto = new BatchRecallDTO();
        dto.setOptUserId(optUserId);
        dto.setPickupId(warehouseId);
        dto.setOrderIdList(orderIds);
        logger.info("开启订单中台, 调用重构 TMS 波次召回接口: {}", JSON.toJSONString(dto));
        String result = restTemplateUtils.postJson(TMS_BATCH_RECALL_URL, JSON.toJSONString(dto));
        logger.info("重构 TMS 波次召回接口返回值: {}", result);
    }

}
