package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 二级仓酒饮内配单扫码备货记录表
 *
 * <AUTHOR>
 * @since 2024-10-11 14:32
 **/
public class StockUpRecordPO implements Serializable {
    /**
     * 主键 id
     */
    private Long id;

    /**
     * 城市 id
     */
    private Integer orgId;

    /**
     * 仓库 id
     */
    private Integer warehouseId;

    /**
     * 车次编号
     */
    private String taskNo;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 出库位
     */
    private String location;

    /**
     * 大件数量
     */
    private BigDecimal packageCount;

    /**
     * 小件数量
     */
    private BigDecimal unitCount;

    /**
     * 创建时间
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo == null ? null : taskNo.trim();
    }

    public String getLicensePlate() {
        return licensePlate;
    }

    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate == null ? null : licensePlate.trim();
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() +
               " [" +
               "Hash = " + hashCode() +
               ", id=" + id +
               ", orgId=" + orgId +
               ", warehouseId=" + warehouseId +
               ", taskNo=" + taskNo +
               ", licensePlate=" + licensePlate +
               ", driverName=" + driverName +
               ", location=" + location +
               ", packageCount=" + packageCount +
               ", unitCount=" + unitCount +
               ", createTime=" + createTime +
               "]";
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StockUpRecordPO other = (StockUpRecordPO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
               && (this.getOrgId() == null ? other.getOrgId() == null : this.getOrgId().equals(other.getOrgId()))
               && (this.getWarehouseId() == null ? other.getWarehouseId() == null : this.getWarehouseId().equals(other.getWarehouseId()))
               && (this.getTaskNo() == null ? other.getTaskNo() == null : this.getTaskNo().equals(other.getTaskNo()))
               && (this.getLicensePlate() == null ? other.getLicensePlate() == null : this.getLicensePlate().equals(other.getLicensePlate()))
               && (this.getDriverName() == null ? other.getDriverName() == null : this.getDriverName().equals(other.getDriverName()))
               && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
               && (this.getPackageCount() == null ? other.getPackageCount() == null : this.getPackageCount().equals(other.getPackageCount()))
               && (this.getUnitCount() == null ? other.getUnitCount() == null : this.getUnitCount().equals(other.getUnitCount()))
               && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrgId() == null) ? 0 : getOrgId().hashCode());
        result = prime * result + ((getWarehouseId() == null) ? 0 : getWarehouseId().hashCode());
        result = prime * result + ((getTaskNo() == null) ? 0 : getTaskNo().hashCode());
        result = prime * result + ((getLicensePlate() == null) ? 0 : getLicensePlate().hashCode());
        result = prime * result + ((getDriverName() == null) ? 0 : getDriverName().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getPackageCount() == null) ? 0 : getPackageCount().hashCode());
        result = prime * result + ((getUnitCount() == null) ? 0 : getUnitCount().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }
}