package com.yijiupi.himalaya.supplychain.controller.sow;

import java.io.Serializable;
import java.util.List;

public class SowPackage implements Serializable {

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 自提点名称
     */
    private String shopName;

    /**
     * 出库位id
     */
    private Long toLocationId;

    /**
     * 出库位名称
     */
    private String toLocationName;

    /**
     * 播种包装明细
     */
    private List<SowPackageProductItem> sowPackageProductItems;

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public List<SowPackageProductItem> getSowPackageProductItems() {
        return sowPackageProductItems;
    }

    public void setSowPackageProductItems(List<SowPackageProductItem> sowPackageProductItems) {
        this.sowPackageProductItems = sowPackageProductItems;
    }
}
