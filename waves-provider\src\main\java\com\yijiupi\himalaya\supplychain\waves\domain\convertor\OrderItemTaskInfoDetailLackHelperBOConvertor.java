package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.OrderItemTaskInfoDetailLackHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/12
 */
@Component
public class OrderItemTaskInfoDetailLackHelperBOConvertor {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderItemTaskInfoDetailLackHelperBOConvertor.class);

    @Deprecated
    public static List<OrderItemTaskInfoDetailLackHelperBO> convert(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        List<OutStockOrderPO> outStockOrderPOList) {

        List<OrderItemTaskInfoDetailLackHelperBO> boList = new ArrayList<>();
        Map<Long, OutStockOrderPO> outStockOrderPOMap =
            outStockOrderPOList.stream().collect(Collectors.toMap(OutStockOrderPO::getId, v -> v));
        for (OrderItemTaskInfoPO taskInfoPO : orderItemTaskInfoPOList) {
            if (CollectionUtils.isNotEmpty(taskInfoPO.getDetailList())) {
                for (OrderItemTaskInfoDetailPO orderItemTaskInfoDetailPO : taskInfoPO.getDetailList()) {
                    OrderItemTaskInfoDetailLackHelperBO detailLackHelperBO = new OrderItemTaskInfoDetailLackHelperBO();
                    detailLackHelperBO.setOrderItemTaskInfoId(orderItemTaskInfoDetailPO.getTaskInfoId());
                    detailLackHelperBO.setOrderItemTaskInfoDetailId(orderItemTaskInfoDetailPO.getId());
                    detailLackHelperBO.setUnitTotalCount(orderItemTaskInfoDetailPO.getUnitTotalCount());
                    detailLackHelperBO.setOutStockOrderPO(outStockOrderPOMap.get(taskInfoPO.getRefOrderId()));
                    detailLackHelperBO.setSecOwnerId(orderItemTaskInfoDetailPO.getSecOwnerId());
                    detailLackHelperBO.setOrderItemTaskInfoPO(taskInfoPO);
                    detailLackHelperBO.setOrderItemTaskInfoDetailPO(orderItemTaskInfoDetailPO);
//                    detailLackHelperBO.setSaleSpecQuantity();
                    boList.add(detailLackHelperBO);
                }
            }
        }

        return boList;
    }

    public static List<OrderItemTaskInfoDetailLackHelperBO> convert(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
                                                                    List<OutStockOrderPO> outStockOrderPOList, BatchTaskItemDTO batchTaskItemDTO) {

        List<OrderItemTaskInfoDetailLackHelperBO> boList = new ArrayList<>();
        Map<Long, OutStockOrderPO> outStockOrderPOMap =
                outStockOrderPOList.stream().collect(Collectors.toMap(OutStockOrderPO::getId, v -> v));
        for (OrderItemTaskInfoPO taskInfoPO : orderItemTaskInfoPOList) {
            if (CollectionUtils.isNotEmpty(taskInfoPO.getDetailList())) {
                for (OrderItemTaskInfoDetailPO orderItemTaskInfoDetailPO : taskInfoPO.getDetailList()) {
                    OrderItemTaskInfoDetailLackHelperBO detailLackHelperBO = new OrderItemTaskInfoDetailLackHelperBO();
                    detailLackHelperBO.setOrderItemTaskInfoId(orderItemTaskInfoDetailPO.getTaskInfoId());
                    detailLackHelperBO.setOrderItemTaskInfoDetailId(orderItemTaskInfoDetailPO.getId());
                    detailLackHelperBO.setUnitTotalCount(orderItemTaskInfoDetailPO.getUnitTotalCount());
                    detailLackHelperBO.setOutStockOrderPO(outStockOrderPOMap.get(taskInfoPO.getRefOrderId()));
                    detailLackHelperBO.setSecOwnerId(orderItemTaskInfoDetailPO.getSecOwnerId());
                    detailLackHelperBO.setOrderItemTaskInfoPO(taskInfoPO);
                    detailLackHelperBO.setOrderItemTaskInfoDetailPO(orderItemTaskInfoDetailPO);
                    detailLackHelperBO.setSaleSpecQuantity(batchTaskItemDTO.getSaleSpecQuantity());
                    boList.add(detailLackHelperBO);
                }
            }
        }

        return boList;
    }


    public static List<OrderItemTaskInfoDetailPO> convertOrderItemTaskInfo(
        List<OrderItemTaskInfoDetailLackHelperBO> boList, List<OrderItemTaskInfoDetailPO> orderItemTaskInfoPOList) {
        Map<Long, OrderItemTaskInfoDetailPO> detailPOMap =
            orderItemTaskInfoPOList.stream().collect(Collectors.toMap(OrderItemTaskInfoDetailPO::getId, v -> v));
        return boList.stream().map(bo -> detailPOMap.get(bo.getOrderItemTaskInfoDetailId()))
            .collect(Collectors.toList());
    }

    /**
     * 对orderItemTaskInfoDetail进行再排序。因为有可能同一个订单项不同货主，分配的不是销售规格的整数倍，导致分摊错误
     * @param boList
     * @return
     */
    public static List<OrderItemTaskInfoDetailLackHelperBO> reSortLackHelperBOList(List<OrderItemTaskInfoDetailLackHelperBO> boList) {
        try {
            boolean couldSaleSpecQuantity = boList.stream().anyMatch(m -> m.getUnitTotalCount().remainder(m.getSaleSpecQuantity()).compareTo(BigDecimal.ZERO) != 0);
            if (BooleanUtils.isFalse(couldSaleSpecQuantity)) {
                return boList;
            }

            // key :orderItemTaskInfoId； value: 排序号
            Map<Long, Integer> taskInfoSequenceMap = new HashMap<>();
            for (int i = 0; i < boList.size(); i++) {
                taskInfoSequenceMap.putIfAbsent(boList.get(i).getOrderItemTaskInfoId(), i);
            }

            boList.forEach(bo -> {
                bo.setSequence(taskInfoSequenceMap.get(bo.getOrderItemTaskInfoId()));
            });

            boList.sort(Comparator.comparingInt(OrderItemTaskInfoDetailLackHelperBO::getSequence));
        } catch (Exception e) {
            LOGGER.warn("重新排序报错，", e);
        }
        return boList;
    }


}
