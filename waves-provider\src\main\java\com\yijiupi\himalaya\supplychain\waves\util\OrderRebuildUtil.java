package com.yijiupi.himalaya.supplychain.waves.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.constants.WarehouseConfigConstants;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationLargePickCargoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;

/**
 * <AUTHOR>
 * @title: OrderRebuildUtil
 * @description:
 * @date 2022-12-05 09:39
 */
public class OrderRebuildUtil {

    /**
     * 将订单项组装成订单，但先将订单项按货位 是 存储位 还是 其他货位 进行了拆分，然后再组装。<br />
     * 有可能一个订单的两个订单项，组装后拆成两个订单
     * 
     * @param orders
     * @param orderItems
     * @param warehouseConfigDTO
     * @param wavesStrategyDTO
     * @return
     */
    public static List<OutStockOrderPO> getOrdersByPassageItems(List<OutStockOrderPO> orders,
        List<OutStockOrderItemPO> orderItems, WarehouseConfigDTO warehouseConfigDTO,
        WavesStrategyDTO wavesStrategyDTO) {
        // 如果没有开启配置的，不处理
        if (BooleanUtils.isFalse(RobotPickConstants.isWarehouseOpenLargePick(wavesStrategyDTO, warehouseConfigDTO))) {
            return getOrdersByPassageItems(orders, orderItems);
        }

        List<OutStockOrderItemPO> otherItemList = orderItems.stream()
            .filter(m -> Objects.nonNull(m.getSubCategory())
                && CreateBatchLocationLargePickCargoBL.smallLocationList.contains(m.getSubCategory().intValue()))
            .collect(Collectors.toList());
        List<OutStockOrderItemPO> storeItemList = orderItems.stream().filter(
            m -> Objects.nonNull(m.getSubCategory()) && LocationEnum.存储位.getType().byteValue() == m.getSubCategory())
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(otherItemList)) {
            return getOrdersByPassageItems(orders, storeItemList);
        }

        if (CollectionUtils.isEmpty(storeItemList)) {
            return getOrdersByPassageItems(orders, otherItemList);
        }

        List<OutStockOrderPO> totalOrderList = new ArrayList<>();
        totalOrderList.addAll(getOrdersByPassageItems(orders, storeItemList));
        totalOrderList.addAll(getOrdersByPassageItems(orders, otherItemList));

        return totalOrderList;
    }

    public static List<OutStockOrderPO> getOrdersByPassageItems(List<OutStockOrderPO> orders,
        List<OutStockOrderItemPO> orderItems) {
        // 根据通道，把订单项拆分，然后合并成新的订单对象
        List<Long> lstOrderIds =
            orderItems.stream().map(p -> p.getOutstockorderId()).distinct().collect(Collectors.toList());
        List<OutStockOrderPO> lstOrders = new ArrayList<>();
        lstOrderIds.forEach(orderId -> {
            // 查找原订单信息
            OutStockOrderPO outStockOrderPOTmp = orders.stream().filter(p -> p.getId().equals(orderId)).findAny().get();
            // 拼接新的订单项
            List<OutStockOrderItemPO> lstItems =
                orderItems.stream().filter(p -> p.getOutstockorderId().equals(orderId)).collect(Collectors.toList());
            // 如果新老订单项数量不一致，需要拼接新的订单
            if (outStockOrderPOTmp.getItems().size() != lstItems.size()) {
                OutStockOrderPO outStockOrderPONew = new OutStockOrderPO();
                BeanUtils.copyProperties(outStockOrderPOTmp, outStockOrderPONew);
                outStockOrderPONew.setItems(lstItems);
                outStockOrderPONew.setUnitamount(
                    lstItems.stream().map(p -> p.getUnitcount()).reduce(BigDecimal.ZERO, BigDecimal::add));
                outStockOrderPONew.setPackageamount(
                    lstItems.stream().map(p -> p.getPackagecount()).reduce(BigDecimal.ZERO, BigDecimal::add));
                long skuCount = lstItems.stream().map(OutStockOrderItemPO::getSkuid).distinct().count();
                outStockOrderPONew.setSkucount((int)skuCount);
                lstOrders.add(outStockOrderPONew);
            } else {
                lstOrders.add(outStockOrderPOTmp);
            }
        });
        return lstOrders;
    }

    public static List<OutStockOrderPO> copyOrderItemCreateOrder(List<OutStockOrderPO> orders,
        List<OutStockOrderItemPO> orderItems) {
        // 根据通道，把订单项拆分，然后合并成新的订单对象
        List<Long> lstOrderIds =
            orderItems.stream().map(p -> p.getOutstockorderId()).distinct().collect(Collectors.toList());
        List<OutStockOrderPO> lstOrders = new ArrayList<>();
        lstOrderIds.forEach(orderId -> {
            // 查找原订单信息
            OutStockOrderPO outStockOrderPOTmp = orders.stream().filter(p -> p.getId().equals(orderId)).findAny().get();
            // 拼接新的订单项
            List<OutStockOrderItemPO> lstItems =
                orderItems.stream().filter(p -> p.getOutstockorderId().equals(orderId)).collect(Collectors.toList());

            OutStockOrderPO outStockOrderPONew = new OutStockOrderPO();
            BeanUtils.copyProperties(outStockOrderPOTmp, outStockOrderPONew);
            outStockOrderPONew.setItems(lstItems);
            outStockOrderPONew
                .setUnitamount(lstItems.stream().map(p -> p.getUnitcount()).reduce(BigDecimal.ZERO, BigDecimal::add));
            outStockOrderPONew.setPackageamount(
                lstItems.stream().map(p -> p.getPackagecount()).reduce(BigDecimal.ZERO, BigDecimal::add));
            outStockOrderPONew.setSkucount(lstItems.size());
            lstOrders.add(outStockOrderPONew);
        });
        return lstOrders;
    }

    /**
     *
     * @param orderItemPOS 总的item
     * @param filteredOrderItemList 需要过滤出去的item
     * @return
     */
    public static List<OutStockOrderItemPO> filterOrderItem(List<OutStockOrderItemPO> orderItemPOS,
        List<OutStockOrderItemPO> filteredOrderItemList) {
        if (CollectionUtils.isEmpty(filteredOrderItemList)) {
            return orderItemPOS;
        }
        Map<String, String> filterdItemMap = filteredOrderItemList.stream()
            .collect(Collectors.toMap(OrderRebuildUtil::getFilteredItemKey, OrderRebuildUtil::getFilteredItemKey));

        return orderItemPOS.stream().filter(m -> Objects.isNull(filterdItemMap.get(getFilteredItemKey(m))))
            .collect(Collectors.toList());
    }

    public static List<OutStockOrderItemPO> filterLiquidNotPackageOrderItem(List<OutStockOrderItemPO> orderItemPOS,
        WaveCreateDTO waveCreateDTO, WarehouseConfigDTO warehouseConfigDTO) {
        if (Objects.isNull(waveCreateDTO)) {
            return orderItemPOS;
        }
        if (CollectionUtils.isEmpty(waveCreateDTO.getOrders())) {
            return orderItemPOS;
        }

        if (!WarehouseConfigConstants.SOW_CONTROL_TYPE_LIQUID_NOT_PACK.equals(warehouseConfigDTO.getSowControlType())) {
            return orderItemPOS;
        }

        if (BooleanUtils.isFalse(
            RobotPickConstants.isWarehouseOpenLargePick(waveCreateDTO.getWavesStrategyDTO(), warehouseConfigDTO))) {
            return orderItemPOS;
        }

        return orderItemPOS.stream()
            .filter(m -> !BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick()))
            .collect(Collectors.toList());
    }

    private static String getFilteredItemKey(OutStockOrderItemPO item) {
        return item.getId() + "-" + item.getLocationId();
    }

}
