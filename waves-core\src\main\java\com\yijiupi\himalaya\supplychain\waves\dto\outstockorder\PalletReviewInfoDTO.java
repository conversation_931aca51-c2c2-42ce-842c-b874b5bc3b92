package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
public class PalletReviewInfoDTO implements Serializable {

    /**
     * 托盘信息
     */
    private PalletReviewLocationInfoDTO locationInfo;
    /**
     * 酒饮产品信息
     */
    private List<PalletReviewProductInfoDTO> productInfos;
    /**
     * 休食订单信息
     */
    private List<PalletReviewOrderInfoDTO> orderInfos;

    public PalletReviewLocationInfoDTO getLocationInfo() {
        return locationInfo;
    }

    public void setLocationInfo(PalletReviewLocationInfoDTO locationInfo) {
        this.locationInfo = locationInfo;
    }

    public List<PalletReviewProductInfoDTO> getProductInfos() {
        return productInfos;
    }

    public void setProductInfos(List<PalletReviewProductInfoDTO> productInfos) {
        this.productInfos = productInfos;
    }

    public List<PalletReviewOrderInfoDTO> getOrderInfos() {
        return orderInfos;
    }

    public void setOrderInfos(List<PalletReviewOrderInfoDTO> orderInfos) {
        this.orderInfos = orderInfos;
    }
}
