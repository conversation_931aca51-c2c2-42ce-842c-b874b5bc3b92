package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.PickUpDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemUpdatePO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/25
 */
@Service
public class BatchInventoryTransferBL {

    @Reference
    private IBatchInventoryManageService iBatchInventoryManageService;

    private static final String PROMPT_MSG = "产品总库存不足，请联系仓管确认缺货或者盘点加库存。";
    private static final Logger LOGGER = LoggerFactory.getLogger(BatchInventoryTransferBL.class);

    public List<PickUpDTO> checkInventoryTransferByPickUp(List<PickUpDTO> pickUpDTOList,
        List<BatchTaskItemUpdatePO> poList, BatchTaskPO batchTaskPO, boolean isCrossWareHouse) {
        if (!isCrossWareHouse) {
            LOGGER.info("校验移库入参：{}", JSON.toJSONString(pickUpDTOList));
            try {
                pickUpDTOList = iBatchInventoryManageService.checkInventoryTransferByPickUp(pickUpDTOList);

            } catch (DataValidateException e) {
                if (e.getMessage().contains("库存不足")) {
                    String locationName = poList.stream().map(BatchTaskItemUpdatePO::getFromLocationName)
                        .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.joining(","));

                    throw new DataValidateException(String.format("%s, 货位名称：%s, 操作人:%s", e.getMessage(), locationName,
                        getPickUserName(poList, batchTaskPO)));
                } else {
                    throw e;
                }
            }
            LOGGER.info("校验移库结果为：{}", JSON.toJSONString(pickUpDTOList));
            return pickUpDTOList;
        }

        return pickUpDTOList;
    }

    private String getPickUserName(List<BatchTaskItemUpdatePO> poList, BatchTaskPO batchTaskPO) {
        if (CollectionUtils.isNotEmpty(poList)) {
            return poList.get(0).getCompleteUser();
        }

        return batchTaskPO.getSorter();
    }

    public List<PickUpDTO> checkInventoryTransferByPickUp(List<PickUpDTO> pickUpDTOList) {
        LOGGER.info("校验移库入参：{}", JSON.toJSONString(pickUpDTOList));
        List<PickUpDTO> handledPickUpDTOList =
            iBatchInventoryManageService.checkInventoryTransferByPickUp(pickUpDTOList);
        return handledPickUpDTOList;
    }

    // public List<PickUpDTO> checkInventoryTransferByPickUp(List<PickUpDTO> pickUpDTOList) {
    // Map<String, List<PickUpDTO>> negativePickUpMap =
    // pickUpDTOList.stream().filter(m -> m.getCount().compareTo(BigDecimal.ZERO) < 0)
    // .collect(Collectors.groupingBy(PickUpDTO::getBusinessId));
    //
    // List<PickUpDTO> positivePickUpDTOList = resetNegativePickUpList(pickUpDTOList);
    // List<PickUpDTO> handledPickUpDTOList =
    // iBatchInventoryManageService.checkInventoryTransferByPickUp(positivePickUpDTOList);
    //
    // if (org.springframework.util.CollectionUtils.isEmpty(negativePickUpMap)) {
    // return handledPickUpDTOList;
    // }
    //
    // Map<String, List<PickUpDTO>> handledPickUpDTOMap =
    // handledPickUpDTOList.stream().collect(Collectors.groupingBy(PickUpDTO::getBusinessId));
    //
    // for (Map.Entry<String, List<PickUpDTO>> entry : handledPickUpDTOMap.entrySet()) {
    // List<PickUpDTO> negativeList = negativePickUpMap.get(entry.getKey());
    // if (CollectionUtils.isNotEmpty(negativeList)) {
    // entry.getValue().forEach(m -> m.setCount(BigDecimal.ZERO.subtract(m.getCount())));
    // }
    // }
    //
    // return handledPickUpDTOList;
    // }

    public List<PickUpDTO> resetNegativePickUpList(List<PickUpDTO> pickUpDTOList) {
        List<PickUpDTO> negativePickUpList = pickUpDTOList.stream()
            .filter(m -> m.getCount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(negativePickUpList)) {
            return pickUpDTOList;
        }

        List<PickUpDTO> totalPickUpDTOList = new ArrayList<>();

        List<PickUpDTO> positivePickUpList = pickUpDTOList.stream()
            .filter(m -> m.getCount().compareTo(BigDecimal.ZERO) >= 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(positivePickUpList)) {
            totalPickUpDTOList.addAll(positivePickUpList);
        }

        List<PickUpDTO> convertPickUpDTOList =
            negativePickUpList.stream().map(this::convertNegativeToPositive).collect(Collectors.toList());

        totalPickUpDTOList.addAll(convertPickUpDTOList);

        return totalPickUpDTOList;
    }

    private PickUpDTO convertNegativeToPositive(PickUpDTO negativePickUpDTO) {
        PickUpDTO positivePickUpDTO = new PickUpDTO();
        BeanUtils.copyProperties(negativePickUpDTO, positivePickUpDTO);
        positivePickUpDTO.setFromChannel(negativePickUpDTO.getToChannel());
        positivePickUpDTO.setFromSource(negativePickUpDTO.getToSource());
        positivePickUpDTO.setFromLocationId(negativePickUpDTO.getLocationId());
        positivePickUpDTO.setFromLocationName(negativePickUpDTO.getLocationName());

        positivePickUpDTO.setToChannel(negativePickUpDTO.getFromChannel());
        positivePickUpDTO.setToSource(negativePickUpDTO.getFromSource());
        positivePickUpDTO.setLocationId(negativePickUpDTO.getFromLocationId());
        positivePickUpDTO.setLocationName(negativePickUpDTO.getFromLocationName());

        return positivePickUpDTO;
    }

}
