package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 分拣员分拣改变itemPO
 *
 * <AUTHOR> 2018-03-26
 */
public class BatchTaskItemUpdatePO implements Serializable {
    /**
     * 波次任务详情id
     */
    private String id;
    /**
     * 已拣货数量
     */
    private BigDecimal overSortCount;
    /**
     * 缺省数量
     */
    private BigDecimal lackCount;
    /**
     * 分拣状态
     */
    private Byte taskState;
    /**
     * 更新人
     */
    private String lastUpdateUser;
    /**
     * 是否缺货 1 缺货 0不缺货'
     */
    private Byte isLack = 0;

    /**
     * 来源货位id
     */
    private Long fromLocationId;
    /**
     * 来源货位名称
     */
    private String fromLocationName;

    /**
     * 开始拣货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 完成拣货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeTime;
    /**
     * 已播种小数量（二次分拣中，记录播种状态）
     */
    private BigDecimal sownUnitTotalCount;

    private Integer completeUserId;

    private String completeUser;

    /**
     * 小单位件数
     */
    private BigDecimal unitCount;
    /**
     * 大单位件数
     */
    private BigDecimal packageCount;
    /**
     * 小单位总件数
     */
    private BigDecimal unitTotalCount;
    /**
     * 备注
     */
    private String remark;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getSownUnitTotalCount() {
        return sownUnitTotalCount;
    }

    public void setSownUnitTotalCount(BigDecimal sownUnitTotalCount) {
        this.sownUnitTotalCount = sownUnitTotalCount;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public Long getFromLocationId() {
        return fromLocationId;
    }

    public void setFromLocationId(Long fromLocationId) {
        this.fromLocationId = fromLocationId;
    }

    public String getFromLocationName() {
        return fromLocationName;
    }

    public void setFromLocationName(String fromLocationName) {
        this.fromLocationName = fromLocationName;
    }

    /**
     * 获取 波次任务详情id
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 波次任务详情id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 已拣货数量
     */
    public BigDecimal getOverSortCount() {
        return this.overSortCount;
    }

    /**
     * 设置 已拣货数量
     */
    public void setOverSortCount(BigDecimal overSortCount) {
        this.overSortCount = overSortCount;
    }

    /**
     * 获取 更新人
     */
    public String getLastUpdateUser() {
        return this.lastUpdateUser;
    }

    /**
     * 设置 更新人
     */
    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    /**
     * 获取 分拣状态
     */
    public Byte getTaskState() {
        return this.taskState;
    }

    /**
     * 设置 分拣状态
     */
    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    /**
     * 获取 缺省数量
     */
    public BigDecimal getLackCount() {
        return this.lackCount;
    }

    /**
     * 设置 缺省数量
     */
    public void setLackCount(BigDecimal lackCount) {
        this.lackCount = lackCount;
    }

    /**
     * 获取 是否缺货
     */
    public Byte getIsLack() {
        return this.isLack;
    }

    /**
     * 设置 是否缺货
     */
    public void setIsLack(Byte isLack) {
        this.isLack = isLack;
    }

    /**
     * 获取
     *
     * @return completeUserId
     */
    public Integer getCompleteUserId() {
        return this.completeUserId;
    }

    /**
     * 设置
     *
     * @param completeUserId
     */
    public void setCompleteUserId(Integer completeUserId) {
        this.completeUserId = completeUserId;
    }

    /**
     * 获取
     *
     * @return completeUser
     */
    public String getCompleteUser() {
        return this.completeUser;
    }

    /**
     * 设置
     *
     * @param completeUser
     */
    public void setCompleteUser(String completeUser) {
        this.completeUser = completeUser;
    }

    /**
     * 获取 小单位件数
     *
     * @return unitCount 小单位件数
     */
    public BigDecimal getUnitCount() {
        return this.unitCount;
    }

    /**
     * 设置 小单位件数
     *
     * @param unitCount 小单位件数
     */
    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    /**
     * 获取 大单位件数
     *
     * @return packageCount 大单位件数
     */
    public BigDecimal getPackageCount() {
        return this.packageCount;
    }

    /**
     * 设置 大单位件数
     *
     * @param packageCount 大单位件数
     */
    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    /**
     * 获取 小单位总件数
     *
     * @return unitTotalCount 小单位总件数
     */
    public BigDecimal getUnitTotalCount() {
        return this.unitTotalCount;
    }

    /**
     * 设置 小单位总件数
     *
     * @param unitTotalCount 小单位总件数
     */
    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }
}
