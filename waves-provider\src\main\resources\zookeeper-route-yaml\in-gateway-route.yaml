- hosts:
    - "in-wms-api.*"
    - "scapi.*"
  signVerify: true
  stripPath: false
  timeout: 60000
  priority: 1
  pathRouters:
    - paths:
        - "/oldpda/batchTask/listBatchTaskItemPeriodConfig"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-waves#/batchTask/listBatchTaskItemPeriodConfig"
    - paths:
        - "/oldpda/batchTask/findOrderNoByCode"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-waves#/batchTask/findOrderNoByCode"
      priority: 1
    - paths:
        - "/oldpda/batchTask/findSameUserPalletInfo"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-waves#/batchTask/findSameUserPalletInfo"
      priority: 1
    - paths:
        - "/oldpda/agvTask/completeAgvTask"
        - "/oldpda/AgvTaskController/completeAgvTask"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-waves#/agvTask/completeAgvTask"
    - paths:
        - "/oldpda/agvTask/moveAgvTask"
        - "/oldpda/AgvTaskController/moveAgvTask"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-waves#/agvTask/moveAgvTask"
    - paths:
        - "/oldpda/batchTask/checkByForbidSalesDays"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-waves#/batchTask/checkByForbidSalesDays"

    - paths:
        - "/oldpda/productlocation/addPalletByOrderId"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-waves#/productlocation/addPalletByOrderId"
      priority: 1
    - paths:
        - "/oldpda/productlocation/findPalletByCondition"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-waves#/productlocation/findPalletByCondition"
      priority: 1
    - paths:
        - "/oldpda/productlocation/deletePalletByCondition"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-waves#/productlocation/deletePalletByCondition"
      priority: 1
    - paths:
        - "/oldpda/productlocation/addPalletByOrderIdBatch"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-waves#/productlocation/addPalletByOrderIdBatch"
      priority: 1
    - paths:
        - "/pda/concertSow/**"
        - "/oldpda/concertSow/**"
      priority: 1
      proxyUrl: "http://#supplychain-microservice-waves#/"
    - paths:
        - "/oldpda/batchTask/completeBatchTask"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-waves#/batchTask/completeBatchTask"
      priority: 1