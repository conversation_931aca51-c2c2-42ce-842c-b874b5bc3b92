package com.yijiupi.himalaya.supplychain.waves.batch;

import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.BatchOrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletQueryDTO;

import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/23
 */
public interface IOrderLocationPalletManageService {
    /**
     * 订单货位托盘关系新增
     */
    void addPallet(OrderLocationPalletDTO dto);

    /**
     * 更新订单货位托盘关系
     */
    void editPallet(OrderLocationPalletDTO dto);

    /**
     * 订单货位托盘关系查询
     *
     * @param queryDTO 查询条件
     */
    List<OrderLocationPalletDTO> findPalletByCondition(OrderLocationPalletQueryDTO queryDTO);

    /**
     * 订单货位托盘关系数据删除
     */
    void deletePalletByCondition(OrderLocationPalletQueryDTO deleteDTO);

    /**
     * 根据订单id新增订单货位托盘关系
     */
    void addPalletByOrderId(OrderLocationPalletDTO dto);

    /**
     * 批量根据订单id新增订单货位托盘关系
     *
     * @param dto
     */
    void addPalletByOrderIdBatch(BatchOrderLocationPalletDTO dto);
}
