<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.SowOrderPrePOMapper">
  <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPrePO">
    <!--@mbg.generated-->
    <!--@Table soworderpre-->
    <id column="Id" jdbcType="BIGINT" property="id" />
    <result column="OrgId" jdbcType="BIGINT" property="orgId" />
    <result column="WarehouseId" jdbcType="INTEGER" property="warehouseId" />
    <result column="SowTaskPreId" jdbcType="BIGINT" property="sowTaskPreId" />
    <result column="RefOrderNo" jdbcType="VARCHAR" property="refOrderNo" />
    <result column="Version" jdbcType="INTEGER" property="version" />
    <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime" />
    <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    Id, OrgId, WarehouseId, SowTaskPreId, RefOrderNo, Version, CreateTime, LastUpdateTime
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from soworderpre
    where Id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from soworderpre
    where Id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPrePO">
    <!--@mbg.generated-->
    insert into soworderpre
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="orgId != null">
        OrgId,
      </if>
      <if test="warehouseId != null">
        WarehouseId,
      </if>
      <if test="sowTaskPreId != null">
        SowTaskPreId,
      </if>
      <if test="refOrderNo != null and refOrderNo != ''">
        RefOrderNo,
      </if>
      <if test="version != null">
        Version,
      </if>
      <if test="createTime != null">
        CreateTime,
      </if>
      <if test="lastUpdateTime != null">
        LastUpdateTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
          #{orgId,jdbcType=BIGINT},
      </if>
      <if test="warehouseId != null">
        #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="sowTaskPreId != null">
        #{sowTaskPreId,jdbcType=BIGINT},
      </if>
      <if test="refOrderNo != null and refOrderNo != ''">
        #{refOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPrePO">
    <!--@mbg.generated-->
    update soworderpre
    <set>
      <if test="orgId != null">
        OrgId = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="warehouseId != null">
        WarehouseId = #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="sowTaskPreId != null">
        SowTaskPreId = #{sowTaskPreId,jdbcType=BIGINT},
      </if>
      <if test="refOrderNo != null and refOrderNo != ''">
        RefOrderNo = #{refOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        Version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateTime != null">
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>

  <select id="findSowOrderPreSowTaskPreId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from soworderpre
    where OrgId = #{orgId,jdbcType=INTEGER}
    and SowTaskPreId = #{sowTaskPreId,jdbcType=VARCHAR}
    and version = #{version,jdbcType=INTEGER}
  </select>
</mapper>