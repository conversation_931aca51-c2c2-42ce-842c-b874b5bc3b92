package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/29
 */
public class OutStockOrderItemDetailRecoverByWarehouseDTO implements Serializable {

    /**
     * 是否处理item和detail数量相等的数据
     */
    private boolean handleEqualCountItem;
    /**
     * 是否用订单中台的数据创建detail
     */
    private boolean useOrderCenterDetail;
    /**
     * 是否用库存记录创建detail
     */
    private boolean useInventoryRecord;
    /**
     * 开始时间
     */
    private String timeS;
    /**
     * 结束时间
     */
    private String timeE;

    /**
     * 获取 是否处理item和detail数量相等的数据
     *
     * @return handleEqualCountItem 是否处理item和detail数量相等的数据
     */
    public boolean isHandleEqualCountItem() {
        return this.handleEqualCountItem;
    }

    /**
     * 设置 是否处理item和detail数量相等的数据
     *
     * @param handleEqualCountItem 是否处理item和detail数量相等的数据
     */
    public void setHandleEqualCountItem(boolean handleEqualCountItem) {
        this.handleEqualCountItem = handleEqualCountItem;
    }

    /**
     * 获取 是否用订单中台的数据创建detail
     *
     * @return useOrderCenterDetail 是否用订单中台的数据创建detail
     */
    public boolean isUseOrderCenterDetail() {
        return this.useOrderCenterDetail;
    }

    /**
     * 设置 是否用订单中台的数据创建detail
     *
     * @param useOrderCenterDetail 是否用订单中台的数据创建detail
     */
    public void setUseOrderCenterDetail(boolean useOrderCenterDetail) {
        this.useOrderCenterDetail = useOrderCenterDetail;
    }

    /**
     * 获取 是否用库存记录创建detail
     *
     * @return useInventoryRecord 是否用库存记录创建detail
     */
    public boolean isUseInventoryRecord() {
        return this.useInventoryRecord;
    }

    /**
     * 设置 是否用库存记录创建detail
     *
     * @param useInventoryRecord 是否用库存记录创建detail
     */
    public void setUseInventoryRecord(boolean useInventoryRecord) {
        this.useInventoryRecord = useInventoryRecord;
    }

    /**
     * 获取 开始时间
     *
     * @return timeS 开始时间
     */
    public String getTimeS() {
        return this.timeS;
    }

    /**
     * 设置 开始时间
     *
     * @param timeS 开始时间
     */
    public void setTimeS(String timeS) {
        this.timeS = timeS;
    }

    /**
     * 获取 结束时间
     *
     * @return timeE 结束时间
     */
    public String getTimeE() {
        return this.timeE;
    }

    /**
     * 设置 结束时间
     *
     * @param timeE 结束时间
     */
    public void setTimeE(String timeE) {
        this.timeE = timeE;
    }
}
