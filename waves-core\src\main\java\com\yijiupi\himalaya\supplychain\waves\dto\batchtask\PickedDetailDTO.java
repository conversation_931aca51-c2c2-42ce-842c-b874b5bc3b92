package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 分拣占用详情信息
 */
public class PickedDetailDTO implements Serializable {
    /**
     * 城市ID
     */
    private Integer orgId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 单据编号(出库单)
     */
    private String refOrderNo;

    /**
     * 出库单ID
     */
    private Long outStockOrderId;

    /**
     * 波次任务编号
     */
    private String batchTaskNo;

    /**
     * 波次编号
     */
    private String batchNo;

    /**
     * 拣任务状态 未分拣(0), 分拣中(1), 已完成(2) TaskItemStateEnum
     */
    private Byte batchTaskState;

    private String batchTaskStateText;

    /**
     * 出库单状态： OutStockOrderStateEnum
     */
    private Byte outStockOrderState;

    private String outStockOrderStateText;

    /**
     * 包装规格名称
     */
    private String specName;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 包装规格转化系数
     */
    private BigDecimal specQuantity;

    /**
     * 是否内配 0：否 1：是
     */
    private Byte isInternal = 0;

    /**
     * 分拣占用总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 小单位数量
     */
    private BigDecimal unitCount;

    /**
     * 大单位数量
     */
    private BigDecimal packageCount;

    /**
     * 货位id
     */
    private Long locationId;

    /**
     * 货位名称
     */
    private String locationName;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Long getOutStockOrderId() {
        return outStockOrderId;
    }

    public void setOutStockOrderId(Long outStockOrderId) {
        this.outStockOrderId = outStockOrderId;
    }

    public String getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Byte getBatchTaskState() {
        return batchTaskState;
    }

    public void setBatchTaskState(Byte batchTaskState) {
        this.batchTaskState = batchTaskState;
    }

    public String getBatchTaskStateText() {
        return batchTaskStateText;
    }

    public void setBatchTaskStateText(String batchTaskStateText) {
        this.batchTaskStateText = batchTaskStateText;
    }

    public Byte getOutStockOrderState() {
        return outStockOrderState;
    }

    public void setOutStockOrderState(Byte outStockOrderState) {
        this.outStockOrderState = outStockOrderState;
    }

    public String getOutStockOrderStateText() {
        return outStockOrderStateText;
    }

    public void setOutStockOrderStateText(String outStockOrderStateText) {
        this.outStockOrderStateText = outStockOrderStateText;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Byte getIsInternal() {
        return isInternal;
    }

    public void setIsInternal(Byte isInternal) {
        this.isInternal = isInternal;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
