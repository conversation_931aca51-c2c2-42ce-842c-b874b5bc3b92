package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 拣货任务项禁止销售配置查询结果
 *
 * <AUTHOR>
 * @Date 2025/4/18
 */
public class BatchTaskItemPeriodConfigResultDTO implements Serializable {

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 货位Id
     */
    private Long locationId;

    /**
     * 产品来源
     */
    private Integer source;

    /**
     * 库存渠道
     */
    private Integer channel;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 告警的方式 0=出库禁止，1=出库提醒
     */
    private Byte alarm;

    /**
     * 货位名称（实际告警）
     */
    private String realLocationName;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productTime;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;

    /**
     * 库存（小单位）
     */
    private BigDecimal totalCountMinUnit;

    /**
     * 保质期
     */
    private String shelfLife;

    /**
     * 一级类目名称
     */
    private String statisticsClass;

    /**
     * 二级类目名称
     */
    private String secondStatisticsClass;

    /**
     * 最晚生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date latestProductDate;

    /**
     * 分仓属性：0、默认；1、酒饮，2、休百
     */
    private Byte storageAttribute;

    /**
     * 是否过期
     */
    private Boolean expiredFlag;

    /**
     * 保质期
     */
    private Integer monthOfShelfLife;
    /**
     * 保质期单位
     */
    private Integer shelfLifeUnit;

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Byte getAlarm() {
        return alarm;
    }

    public void setAlarm(Byte alarm) {
        this.alarm = alarm;
    }

    public String getRealLocationName() {
        return realLocationName;
    }

    public void setRealLocationName(String realLocationName) {
        this.realLocationName = realLocationName;
    }

    public Date getProductTime() {
        return productTime;
    }

    public void setProductTime(Date productTime) {
        this.productTime = productTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public BigDecimal getTotalCountMinUnit() {
        return totalCountMinUnit;
    }

    public void setTotalCountMinUnit(BigDecimal totalCountMinUnit) {
        this.totalCountMinUnit = totalCountMinUnit;
    }

    public String getShelfLife() {
        return shelfLife;
    }

    public void setShelfLife(String shelfLife) {
        this.shelfLife = shelfLife;
    }

    public String getStatisticsClass() {
        return statisticsClass;
    }

    public void setStatisticsClass(String statisticsClass) {
        this.statisticsClass = statisticsClass;
    }

    public String getSecondStatisticsClass() {
        return secondStatisticsClass;
    }

    public void setSecondStatisticsClass(String secondStatisticsClass) {
        this.secondStatisticsClass = secondStatisticsClass;
    }

    public Date getLatestProductDate() {
        return latestProductDate;
    }

    public void setLatestProductDate(Date latestProductDate) {
        this.latestProductDate = latestProductDate;
    }

    public Byte getStorageAttribute() {
        return storageAttribute;
    }

    public void setStorageAttribute(Byte storageAttribute) {
        this.storageAttribute = storageAttribute;
    }

    public String getProductTimeStr() {
        return this.productTime == null ? "" : (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(this.productTime);
    }

    public Boolean getExpiredFlag() {
        return expiredFlag;
    }

    public void setExpiredFlag(Boolean expiredFlag) {
        this.expiredFlag = expiredFlag;
    }

    public Integer getMonthOfShelfLife() {
        return monthOfShelfLife;
    }

    public void setMonthOfShelfLife(Integer monthOfShelfLife) {
        this.monthOfShelfLife = monthOfShelfLife;
    }

    public Integer getShelfLifeUnit() {
        return shelfLifeUnit;
    }

    public void setShelfLifeUnit(Integer shelfLifeUnit) {
        this.shelfLifeUnit = shelfLifeUnit;
    }
}
