package com.yijiupi.himalaya.supplychain.waves.dto.soworder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/6/8
 */
public class SowPackageItemQueryDTO implements Serializable {
    private static final long serialVersionUID = -1793512388511727293L;
    /**
     * 城市id
     */
    private Integer orgId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }
}
