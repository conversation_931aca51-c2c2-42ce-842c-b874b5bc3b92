package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 关联的出库单
 *
 * <AUTHOR> 2018/3/16
 */
public class OutStockOrderDTO implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 订单编号
     */
    private String refOrderNo;
    /**
     * 出库单类型：普通订单（0），招商订单和分销商订单（1），经销商直配订单（2），大货批发订单（3），大商转配送（10），轻加盟订单（11），经销商订单酒批配送（12）
     */
    private Byte orderType;

    /**
     * 出库单类型：普通订单（0），招商订单和分销商订单（1），经销商直配订单（2），大货批发订单（3），大商转配送（10），轻加盟订单（11），经销商订单酒批配送（12）
     */
    private String orderTypeText;
    /**
     * 应付金额
     */
    private BigDecimal orderAmount;
    /**
     * 商品种类数
     */
    private Integer skuCount;
    /**
     * 大件总数
     */
    private BigDecimal packageAmount;
    /**
     * 小件总数
     */
    private BigDecimal unitAmount;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 收货地址
     */
    private String detailAddress;
    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderCreateTime;

    /**
     * 开始拣货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date picktime;

    /**
     * 出库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outstocktime;

    /**
     * 出库操作人
     */
    private String outstockuser;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createuser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createtime;

    /**
     * 更新人
     */
    private String lastupdateuser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastupdatetime;

    /**
     * 发货仓库id
     */
    private Integer warehouseId;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 商品详情
     */
    private List<OutStockOrderItemDTO> itemList;

    /** 区域id */
    private Long areaId;

    /** 区域名称 */
    private String areaName;

    /** 线路id */
    private Long routeId;

    /** 线路名称 */
    private String routeName;

    /**
     * 收货人姓名
     */
    private String username;

    /**
     * 店铺名称
     */
    private String shopname;

    /**
     * 收货人手机号
     */
    private String mobileno;

    /**
     * 订单属性（0: 默认 1: 大件订单 2: 小件订单 3: 单品）
     */
    private List<Byte> packageAttribute;

    /**
     * 订单属性（0: 默认 1: 大件订单 2: 小件订单 3: 单品）
     */
    private String packageAttributeText;

    /**
     * 预计出库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expectedOutStockTime;

    /**
     * 配送方式
     */
    private Byte deliveryMode;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String county;

    /**
     * 街道
     */
    private String street;

    /**
     * 用户id
     */
    private Integer addressId;

    /**
     * 优先级
     */
    private Integer priority;

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public Date getExpectedOutStockTime() {
        return expectedOutStockTime;
    }

    public void setExpectedOutStockTime(Date expectedOutStockTime) {
        this.expectedOutStockTime = expectedOutStockTime;
    }

    public Byte getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(Byte deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    /**
     * 订单状态 待处理(0), 待拣货(1), 拣货中(2), 已拣货/待出库(3), 已出库(4)
     */
    private Integer state;
    /**
     * 订单状态 待处理(0), 待拣货(1), 拣货中(2), 已拣货/待出库(3), 已出库(4)
     */
    private String stateText;
    /**
     * 波次编号
     */
    private String batchno;

    /**
     * 波次Id
     */
    private String batchId;

    /**
     * 关联单据id
     */
    private String businessId;

    /**
     * 关林单据编号
     */
    private String businessNo;

    /**
     * 物流信息
     */
    private List<LogisticsDTO> logisticsList;

    /**
     * 物流单订单类型 0酒批单1退货单
     */
    private Integer logisticsOrderType;

    /**
     * 订单来源
     */
    private Byte orderSource;

    /**
     * 订单来源文本
     */
    private String orderSourceText;

    /**
     * 订单来源
     */
    private Byte orderSourceType;

    public Date getPicktime() {
        return picktime;
    }

    public void setPicktime(Date picktime) {
        this.picktime = picktime;
    }

    public Date getOutstocktime() {
        return outstocktime;
    }

    public void setOutstocktime(Date outstocktime) {
        this.outstocktime = outstocktime;
    }

    public String getOutstockuser() {
        return outstockuser;
    }

    public void setOutstockuser(String outstockuser) {
        this.outstockuser = outstockuser;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateuser() {
        return createuser;
    }

    public void setCreateuser(String createuser) {
        this.createuser = createuser;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getLastupdateuser() {
        return lastupdateuser;
    }

    public void setLastupdateuser(String lastupdateuser) {
        this.lastupdateuser = lastupdateuser;
    }

    public Date getLastupdatetime() {
        return lastupdatetime;
    }

    public void setLastupdatetime(Date lastupdatetime) {
        this.lastupdatetime = lastupdatetime;
    }

    public Byte getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(Byte orderSource) {
        this.orderSource = orderSource;
    }

    public String getOrderSourceText() {
        return orderSourceText;
    }

    public void setOrderSourceText(String orderSourceText) {
        this.orderSourceText = orderSourceText;
    }

    public Integer getLogisticsOrderType() {
        return logisticsOrderType;
    }

    public void setLogisticsOrderType(Integer logisticsOrderType) {
        this.logisticsOrderType = logisticsOrderType;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public List<Byte> getPackageAttribute() {
        return packageAttribute;
    }

    public void setPackageAttribute(List<Byte> packageAttribute) {
        this.packageAttribute = packageAttribute;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getShopname() {
        return shopname;
    }

    public void setShopname(String shopname) {
        this.shopname = shopname;
    }

    public String getMobileno() {
        return mobileno;
    }

    public void setMobileno(String mobileno) {
        this.mobileno = mobileno;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Long getRouteId() {
        return routeId;
    }

    public void setRouteId(Long routeId) {
        this.routeId = routeId;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    /**
     * 获取 id
     *
     * @return id id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 id
     *
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 订单编号
     *
     * @return refOrderNo 订单编号
     */
    public String getRefOrderNo() {
        return this.refOrderNo;
    }

    /**
     * 设置 订单编号
     *
     * @param refOrderNo 订单编号
     */
    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    /**
     * 获取 出库单类型：普通订单（0），招商订单和分销商订单（1），经销商直配订单（2），大货批发订单（3），大商转配送（10），轻加盟订单（11），经销商订单酒批配送（12）
     *
     * @return orderType 出库单类型：普通订单（0），招商订单和分销商订单（1），经销商直配订单（2），大货批发订单（3），大商转配送（10），轻加盟订单（11），经销商订单酒批配送（12）
     */
    public Byte getOrderType() {
        return this.orderType;
    }

    /**
     * 设置 出库单类型：普通订单（0），招商订单和分销商订单（1），经销商直配订单（2），大货批发订单（3），大商转配送（10），轻加盟订单（11），经销商订单酒批配送（12）
     *
     * @param orderType 出库单类型：普通订单（0），招商订单和分销商订单（1），经销商直配订单（2），大货批发订单（3），大商转配送（10），轻加盟订单（11），经销商订单酒批配送（12）
     */
    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }

    /**
     * 获取 应付金额
     *
     * @return orderAmount 应付金额
     */
    public BigDecimal getOrderAmount() {
        return this.orderAmount;
    }

    /**
     * 设置 应付金额
     *
     * @param orderAmount 应付金额
     */
    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    /**
     * 获取 商品种类数
     *
     * @return skuCount 商品种类数
     */
    public Integer getSkuCount() {
        return this.skuCount;
    }

    /**
     * 设置 商品种类数
     *
     * @param skuCount 商品种类数
     */
    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    /**
     * 获取 大件总数
     *
     * @return packageAmount 大件总数
     */
    public BigDecimal getPackageAmount() {
        return this.packageAmount;
    }

    /**
     * 设置 大件总数
     *
     * @param packageAmount 大件总数
     */
    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    /**
     * 获取 小件总数
     *
     * @return unitAmount 小件总数
     */
    public BigDecimal getUnitAmount() {
        return this.unitAmount;
    }

    /**
     * 设置 小件总数
     *
     * @param unitAmount 小件总数
     */
    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    /**
     * 获取 店铺名称
     *
     * @return shopName 店铺名称
     */
    public String getShopName() {
        return this.shopName;
    }

    /**
     * 设置 店铺名称
     *
     * @param shopName 店铺名称
     */
    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    /**
     * 获取 收货地址
     *
     * @return detailAddress 收货地址
     */
    public String getDetailAddress() {
        return this.detailAddress;
    }

    /**
     * 设置 收货地址
     *
     * @param detailAddress 收货地址
     */
    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    /**
     * 获取 下单时间
     *
     * @return orderCreateTime 下单时间
     */
    public Date getOrderCreateTime() {
        return this.orderCreateTime;
    }

    /**
     * 设置 下单时间
     *
     * @param orderCreateTime 下单时间
     */
    public void setOrderCreateTime(Date orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    /**
     * 获取 发货仓库id
     *
     * @return warehouseId 发货仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 发货仓库id
     *
     * @param warehouseId 发货仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 仓库名称
     *
     * @return warehouseName 仓库名称
     */
    public String getWarehouseName() {
        return this.warehouseName;
    }

    /**
     * 设置 仓库名称
     *
     * @param warehouseName 仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    /**
     * 获取 商品详情
     */
    public List<OutStockOrderItemDTO> getItemList() {
        return this.itemList;
    }

    /**
     * 设置 商品详情
     */
    public void setItemList(List<OutStockOrderItemDTO> itemList) {
        this.itemList = itemList;
    }

    /**
     * @return the stateText
     */
    public String getStateText() {
        return stateText;
    }

    /**
     * @param stateText the stateText to set
     */
    public void setStateText(String stateText) {
        this.stateText = stateText;
    }

    /**
     * @return the orderTypeText
     */
    public String getOrderTypeText() {
        return orderTypeText;
    }

    /**
     * @param orderTypeText the orderTypeText to set
     */
    public void setOrderTypeText(String orderTypeText) {
        this.orderTypeText = orderTypeText;
    }

    /**
     * @return the packageAttributeText
     */
    public String getPackageAttributeText() {
        return packageAttributeText;
    }

    /**
     * @param packageAttributeText the packageAttributeText to set
     */
    public void setPackageAttributeText(String packageAttributeText) {
        this.packageAttributeText = packageAttributeText;
    }

    public List<LogisticsDTO> getLogisticsList() {
        return logisticsList;
    }

    public void setLogisticsList(List<LogisticsDTO> logisticsList) {
        this.logisticsList = logisticsList;
    }

    public Byte getOrderSourceType() {
        return orderSourceType;
    }

    public void setOrderSourceType(Byte orderSourceType) {
        this.orderSourceType = orderSourceType;
    }

    public Integer getAddressId() {
        return addressId;
    }

    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

}
