package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter.PartlyMarkOrderDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/3
 */
public class PartlyMarkOrderFailBO {
    /**
     * 标记信息
     */
    private PartlyMarkOrderDTO partlyMarkOrderDTO;
    /**
     * 失败信息
     */
    private String errorMessage;

    public PartlyMarkOrderFailBO() {}

    public PartlyMarkOrderFailBO(PartlyMarkOrderDTO partlyMarkOrderDTO, String errorMessage) {
        this.partlyMarkOrderDTO = partlyMarkOrderDTO;
        this.errorMessage = errorMessage;
    }

    /**
     * 获取 标记信息
     *
     * @return partlyMarkOrderDTO 标记信息
     */
    public PartlyMarkOrderDTO getPartlyMarkOrderDTO() {
        return this.partlyMarkOrderDTO;
    }

    /**
     * 设置 标记信息
     *
     * @param partlyMarkOrderDTO 标记信息
     */
    public void setPartlyMarkOrderDTO(PartlyMarkOrderDTO partlyMarkOrderDTO) {
        this.partlyMarkOrderDTO = partlyMarkOrderDTO;
    }

    /**
     * 获取 失败信息
     *
     * @return errorMessage 失败信息
     */
    public String getErrorMessage() {
        return this.errorMessage;
    }

    /**
     * 设置 失败信息
     *
     * @param errorMessage 失败信息
     */
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
