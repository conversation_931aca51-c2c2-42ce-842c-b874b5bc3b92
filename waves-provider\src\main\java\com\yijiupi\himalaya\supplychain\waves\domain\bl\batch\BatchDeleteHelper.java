package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.ordercenter.OrderCenterNotifyBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder.OutStockOrderStateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.DeleteBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import static com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum.FS_SALE_SELF_PICKUP_SALE_ORDER;
import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @since 2024-08-15 09:22
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class BatchDeleteHelper {

    @Resource
    private BatchMapper batchMapper;

    @Resource
    private OutStockOrderBL outStockOrderBL;

    @Resource
    private BatchRecallBL batchRecallBL;

    @Resource
    private OrderCenterNotifyBL orderCenterNotifyBL;

    @Resource
    private ThirdPartyOutStockBL thirdPartyOutStockBL;

    @Resource
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    @Resource
    private OutStockOrderStateBL outStockOrderStateBL;

    @Resource
    private OrderCenterBL orderCenterBL;

    @Resource
    private OutStockOrderItemMapper outStockOrderItemMapper;

    @Resource
    private OutStockOrderMapper outStockOrderMapper;

    @Resource
    private PackageOrderItemBL packageOrderItemBL;

    @Transactional(rollbackFor = Throwable.class)
    public void restoreOrderState(List<BatchPO> batchPOList, DeleteBatchDTO deleteBatchDTO) {
        // （0） 删除包装箱
        deletePackageItems(batchPOList, deleteBatchDTO.getOperateUserId());
        // （1）酒批类型的波次
        handleJiuPiBatch(batchPOList, deleteBatchDTO);
        // （2） 微酒类型的波次
        handleWineBatch(batchPOList);
        // （3） 第三方类型的波次
        handleThirdPartyBatch(batchPOList);
    }

    private void handleJiuPiBatch(List<BatchPO> batchPOList, DeleteBatchDTO deleteBatchDTO) {
        // 2、清空订单波次信息
        List<String> batchNos = batchPOList.stream().filter(
            p -> !BatchTypeEnum.微酒.valueEquals(p.getBatchType()) && !BatchTypeEnum.isApplyOrderBatch(p.getBatchType()))
            .map(BatchPO::getBatchNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchNos)) {
            return;
        }
        Integer warehouseId = batchPOList.get(0).getWarehouseId();
        Integer operateUserId = deleteBatchDTO.getOperateUserId();
        Integer orgId = batchPOList.get(0).getOrgId();
        // 给TMS发通知
        if (BooleanUtils.isFalse(deleteBatchDTO.getIsIsOpCancel())) {
            batchRecallBL.batchRecall(batchNos, operateUserId, warehouseId, orgId);
        }
        // 同步中台 trace
        orderCenterNotifyBL.syncBatchDeleteTrace(batchNos, operateUserId, warehouseId, orgId);
        // 释放订单表相关波次信息(清空batchId,batchNo)
        deleteOutStockOrder(batchNos, operateUserId, orgId, warehouseId);
    }

    private void handleWineBatch(List<BatchPO> batchPOList) {
        List<BatchPO> wineBatchPOList = batchPOList.stream().filter(p -> BatchTypeEnum.微酒.valueEquals(p.getBatchType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wineBatchPOList)) {
            return;
        }
        List<String> wineBatchNos = wineBatchPOList.stream().map(BatchPO::getBatchNo).collect(Collectors.toList());
        List<OrderItemTaskInfoPO> wineOrderTaskInfoList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByBatchNos(wineBatchNos);
        wineBatchPOList.forEach(batchPO -> {
            // 查找订单id
            List<Long> orderIds =
                wineOrderTaskInfoList.stream().filter(p -> Objects.equals(p.getBatchNo(), batchPO.getBatchNo()))
                    .map(OrderItemTaskInfoPO::getRefOrderId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderIds)) {
                // 兼容老数据
                List<String> orderIdList = batchMapper.listOrderIdByBatchNo(batchPO.getBatchNo());
                orderIds =
                    orderIdList.stream().filter(StringUtils::hasText).map(Long::valueOf).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(orderIds)) {
                throw new BusinessValidateException("删除失败，订单为空！波次号：" + batchPO.getBatchNo());
            }
            outStockOrderBL.updateOrderByWine(orderIds, batchPO.getId(), batchPO.getBatchNo(),
                TaskStateEnum.未分拣.getType());
        });
    }

    private void handleThirdPartyBatch(List<BatchPO> batchPOList) {
        List<BatchPO> thirdPartyBatches = batchPOList.stream()
            .filter(p -> BatchTypeEnum.isApplyOrderBatch(p.getBatchType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(thirdPartyBatches)) {
            return;
        }
        List<String> thirdPartyBatchNos =
            thirdPartyBatches.stream().map(BatchPO::getBatchNo).collect(Collectors.toList());
        List<OrderItemTaskInfoPO> thirdPartyOrderTaskInfoList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByBatchNos(thirdPartyBatchNos);
        thirdPartyBatches.forEach(batchPO -> {
            // 查找订单id
            String batchNo = batchPO.getBatchNo();
            List<Long> orderIds =
                thirdPartyOrderTaskInfoList.stream().filter(p -> Objects.equals(p.getBatchNo(), batchNo))
                    .map(OrderItemTaskInfoPO::getRefOrderId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderIds)) {
                // 兼容老数据
                List<String> orderIdList = batchMapper.listOrderIdByBatchNo(batchNo);
                orderIds =
                    orderIdList.stream().filter(StringUtils::hasText).map(Long::valueOf).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(orderIds)) {
                throw new BusinessValidateException("删除失败，订单为空！波次号：" + batchNo);
            }
            byte type = TaskStateEnum.未分拣.getType();
            thirdPartyOutStockBL.updateOrderByThirdParty(orderIds, batchPO.getId(), batchNo, type);
        });
    }

    /**
     * 删除出库单的波次信息
     */
    private void deleteOutStockOrder(List<String> batchNos, Integer operateUserId, Integer orgId, Integer warehouseId) {
        // 1、释放订单表(清空batchId,batchNo)
        // 旧版本 - 波次跟订单关联
        outStockOrderStateBL.cleanBatch(batchNos, orgId);
        // 新版本 - 波次跟订单项关联
        List<Long> updateOrderIds = outStockOrderItemMapper.listOrderIdByBatchNo(batchNos);
        if (!CollectionUtils.isEmpty(updateOrderIds)) {
            Map<Byte,
                Set<Long>> orderMap = outStockOrderMapper.findByOrderIds(updateOrderIds).stream()
                    .filter(m -> Objects.nonNull(m.getOutBoundType()))
                    .collect(groupingBy(OutStockOrderPO::getOutBoundType, toIds()));
            for (Map.Entry<Byte, Set<Long>> entry : orderMap.entrySet()) {
                byte type = OutStockOrderStateEnum.待调度.getType();
                // 大客户订单波次删除后状态为 待拣货
                if (FS_SALE_SELF_PICKUP_SALE_ORDER.valueEquals(entry.getKey())) {
                    type = OutStockOrderStateEnum.待拣货.getType();
                }
                outStockOrderStateBL.updateStateByOrderIds(updateOrderIds, type);
            }
        }
        // 2、释放订单项表(清空batchTaskId,batchTaskNo,sowTaskId,sowTaskNo)
        outStockOrderItemMapper.cleanBatchTaskByBatchNo(batchNos);
        // 3、通知订单中台
        orderCenterBL.cancelWaveNotify(updateOrderIds, operateUserId, orgId, warehouseId);
    }

    private void deletePackageItems(List<BatchPO> batches, Integer operateUserId) {
        if (batches.isEmpty()) {
            return;
        }
        Set<String> batchNos = batches.stream().map(BatchPO::getBatchNo).collect(Collectors.toSet());
        BatchPO batch = batches.get(0);
        Integer orgId = batch.getOrgId();
        Integer warehouseId = batch.getWarehouseId();
        List<String> orderNos = outStockOrderMapper.findByBatchNos(batchNos, orgId).stream()
            .map(OutStockOrderPO::getReforderno).collect(Collectors.toList());
        if (orderNos.isEmpty()) {
            return;
        }
        packageOrderItemBL.removePackageByRefOrderNos(orderNos, orgId, warehouseId, operateUserId);
    }

    private static Collector<OutStockOrderPO, ?, Set<Long>> toIds() {
        return Collectors.mapping(OutStockOrderPO::getId, Collectors.toSet());
    }

}
