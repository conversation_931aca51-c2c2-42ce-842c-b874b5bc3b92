package com.yijiupi.himalaya.supplychain.waves.batch;

import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchProgressDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-02-29 10:07
 **/
public interface IBatchProgressService {

    /**
     * 获取指定仓库的分拣进度
     *
     * @param warehouseId 仓库 id
     * @return 该仓库的分拣进度
     */
    List<BatchProgressDTO> getBatchProgresses(Integer warehouseId);

}
