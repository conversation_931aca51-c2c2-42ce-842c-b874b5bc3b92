package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
public class PalletReviewProductInfoDTO implements Serializable {
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 条码
     */
    private List<String> packageCode;
    /**
     * 箱码
     */
    private List<String> unitCode;
    /**
     * 数量
     */
    private String countDesc;
    /**
     * 拣货员
     */
    private String sorterName;
    /**
     * 规格名称
     */
    private String specName;

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public List<String> getPackageCode() {
        return packageCode;
    }

    public void setPackageCode(List<String> packageCode) {
        this.packageCode = packageCode;
    }

    public List<String> getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(List<String> unitCode) {
        this.unitCode = unitCode;
    }

    public String getCountDesc() {
        return countDesc;
    }

    public void setCountDesc(String countDesc) {
        this.countDesc = countDesc;
    }

    public String getSorterName() {
        return sorterName;
    }

    public void setSorterName(String sorterName) {
        this.sorterName = sorterName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }
}
