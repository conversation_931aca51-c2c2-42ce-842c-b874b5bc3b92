package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.util.List;

/**
 * 配送车次信息
 * 
 * <AUTHOR>
 * @Date 2022/7/27
 */
public class DeliveryTaskDTO implements Serializable {
    private static final long serialVersionUID = -1922742426214128999L;
    /**
     * 配送批次id
     */
    private String taskId;

    /**
     * 配送批次号(酒批配送单号、或者第三方物流公司单号)
     */
    private String deliveryTaskNo;
    /**
     * 配送司机id
     */
    private Integer deliveryUserId;
    /**
     * deliveryCarId 配送员名字
     */
    private String deliveryUserName;
    /**
     * 配送车辆id
     */
    private Long deliveryCarId;
    /**
     * 配送车辆名称
     */
    private String deliveryCarName;
    /**
     * 货位名称
     */
    private String storageLocation;
    /**
     * 配送订单列表
     */
    private List<String> orderNoList;
    /**
     * 目标仓库ID
     */
    private Integer toWarehouseId;
    /**
     * 是否为物流（true=物流，false=非物流）
     */
    private Boolean logistics;

    public Boolean getLogistics() {
        return logistics;
    }

    public void setLogistics(Boolean logistics) {
        this.logistics = logistics;
    }

    public Integer getToWarehouseId() {
        return toWarehouseId;
    }

    public void setToWarehouseId(Integer toWarehouseId) {
        this.toWarehouseId = toWarehouseId;
    }

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public void setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getDeliveryTaskNo() {
        return deliveryTaskNo;
    }

    public void setDeliveryTaskNo(String deliveryTaskNo) {
        this.deliveryTaskNo = deliveryTaskNo;
    }

    public Integer getDeliveryUserId() {
        return deliveryUserId;
    }

    public void setDeliveryUserId(Integer deliveryUserId) {
        this.deliveryUserId = deliveryUserId;
    }

    public String getDeliveryUserName() {
        return deliveryUserName;
    }

    public void setDeliveryUserName(String deliveryUserName) {
        this.deliveryUserName = deliveryUserName;
    }

    public Long getDeliveryCarId() {
        return deliveryCarId;
    }

    public void setDeliveryCarId(Long deliveryCarId) {
        this.deliveryCarId = deliveryCarId;
    }

    public String getDeliveryCarName() {
        return deliveryCarName;
    }

    public void setDeliveryCarName(String deliveryCarName) {
        this.deliveryCarName = deliveryCarName;
    }

    public String getStorageLocation() {
        return storageLocation;
    }

    public void setStorageLocation(String storageLocation) {
        this.storageLocation = storageLocation;
    }
}
