package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.DefaultLocationConfigDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderBusinessType;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductImageDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskSortOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskSortOrderOtherDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSortItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.PickingGroupStrategyEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.PickingTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.utils.StreamUtils;

/**
 * 波次任务
 *
 * <AUTHOR> 2018/3/16
 */
public class WaveOrderTaskConvertor {
    private static final Logger LOGGER = LoggerFactory.getLogger(WaveOrderTaskConvertor.class);

    public static List<BatchTaskDTO> batchOrderTaskPOS2BatchOrderTaskDTOS(List<BatchTaskPO> batchTaskPOS) {
        ArrayList<BatchTaskDTO> batchTaskDTOS = new ArrayList<>();
        for (BatchTaskPO batchTaskPO : batchTaskPOS) {
            batchTaskDTOS.add(batchOrderTaskPO2BatchOrderTaskDTO(batchTaskPO));
        }
        return batchTaskDTOS;
    }

    public static BatchTaskDTO batchOrderTaskPO2BatchOrderTaskDTO(BatchTaskPO batchTaskPO) {
        BatchTaskDTO batchTaskDTO = new BatchTaskDTO();
        BeanUtils.copyProperties(batchTaskPO, batchTaskDTO);
        if (CollectionUtils.isNotEmpty(batchTaskPO.getBatchTaskItemList())) {
            batchTaskDTO.setBatchTaskItemList(batchTaskItemPOToDTO(batchTaskPO.getBatchTaskItemList()));
        }
        batchTaskDTO.setTaskStateText(TaskStateEnum.getType(batchTaskDTO.getTaskState()));
        batchTaskDTO.setPickingTypeText(PickingTypeEnum.getEnumByValue(batchTaskDTO.getPickingType()));
        batchTaskDTO.setOrderSelectionText(getOrderSelectionText(batchTaskDTO.getOrderSelection()));
        batchTaskDTO.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(batchTaskPO.getCreateTime()));
        return batchTaskDTO;
    }

    public static String getOrderSelectionText(Byte orderSelection) {
        if (WavesStrategyConstants.ORDERSELECTION_AREA == orderSelection) {
            return "按区域";
        }
        if (WavesStrategyConstants.ORDERSELECTION_WAY == orderSelection) {
            return "按线路";
        }
        if (WavesStrategyConstants.ORDERSELECTION_USER == orderSelection) {
            return "按用户";
        }
        if (WavesStrategyConstants.ORDERSELECTION_ORDER == orderSelection) {
            return "按订单";
        }

        return "";
    }

    public static List<BatchTaskSortDTO> batchTaskPOSTObatchTaskDTOS(PageResult<BatchTaskPO> poPageResult) {
        List<BatchTaskSortDTO> batchTaskDTOS = new ArrayList<>();
        poPageResult.forEach(p -> {
            BatchTaskSortDTO dto = new BatchTaskSortDTO();
            BeanUtils.copyProperties(p, dto);
            if (PickingTypeEnum.产品拣货.getType() == dto.getPickingType()) {
                if (PickingGroupStrategyEnum.货位.getType() == p.getPickingGroupStrategy()
                    || PickingGroupStrategyEnum.货区.getType() == p.getPickingGroupStrategy()) {
                    dto.setPickingValue(p.getLocationName());
                }
                if (PickingGroupStrategyEnum.类目.getType() == p.getPickingGroupStrategy()) {
                    dto.setPickingValue(p.getCategoryName());
                }
            }
            batchTaskDTOS.add(dto);
        });
        return batchTaskDTOS;
    }

    public static List<BatchTaskSortOrderDTO> batchTakItemMapTODTO(List<BatchTaskItemDTO> lstItem,
        Map<Long, ProductCodeDTO> codeMap, List<LocationReturnDTO> locationDTOList, List<SowOrderDTO> sowOrderDTOS,
        List<BatchTaskSortOrderOtherDTO> otherBatchTaskDTOList, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        Map<Long, String> configNameMap, Map<Long, List<Long>> relationMap, Map<Long, String> ownerNameMap,
        List<OutStockOrderLocationDTO> orderLocationDTOS, DefaultLocationConfigDTO defaultLocationConfigDTO,
        Map<Long, Boolean> stockAgeMap, Map<Long, ProductImageDTO> productImageDTOMap,
        Map<Long, List<String>> orderPalletMap, List<OutStockOrderItemPO> outStockOrderItemPOList) {
        List<BatchTaskSortOrderDTO> list = new ArrayList<>();
        // key 为订单id
        for (BatchTaskItemDTO itemDTO : lstItem) {
            if (list.stream().anyMatch(p -> p.getOrderId().equals(itemDTO.getRefOrderId()))) {
                continue;
            }
            BatchTaskSortOrderDTO dto = new BatchTaskSortOrderDTO();
            dto.setOrderId(itemDTO.getRefOrderId());
            dto.setOrderNoteno(itemDTO.getRefOrderNo());
            dto.setProvince(itemDTO.getProvince());
            dto.setCity(itemDTO.getCity());
            dto.setCounty(itemDTO.getCounty());
            dto.setStreet(itemDTO.getStreet());
            dto.setShopName(itemDTO.getShopName());
            dto.setUserName(itemDTO.getUserName());
            dto.setMobileNo(itemDTO.getMobileNo());
            dto.setOrderSequence(itemDTO.getOrderSequence());
            dto.setDetailAddress(itemDTO.getDetailAddress());
            dto.setOrderType(itemDTO.getOrderType());
            dto.setPackageAttribute(itemDTO.getPackageAttribute());
            dto.setDeliveryMode(itemDTO.getDeliveryMode());
            dto.setAddressId(itemDTO.getAddressId());
            if (Objects.nonNull(itemDTO.getOutBoundType())
                && OutBoundTypeEnum.INTERNAL_DELIVERY_ORDER.getCode().byteValue() == itemDTO.getOutBoundType()) {
                dto.setBusinessType(OutStockOrderBusinessType.内配订单.getType());
            } else {
                dto.setBusinessType(itemDTO.getBusinessType());
            }
            dto.setAllotType(itemDTO.getAllotType());
            dto.setAreaName(itemDTO.getAreaName());
            dto.setRouteName(itemDTO.getRouteName());
            List<BatchTaskItemDTO> lstChildItems = lstItem.stream()
                .filter(p -> p.getRefOrderId().equals(itemDTO.getRefOrderId())).collect(Collectors.toList());
            List<BatchTaskSortItemDTO> batchTaskSortItemDTOList =
                getBatchTaskSortItemDTOList(codeMap, lstChildItems, locationDTOList, orderItemTaskInfoPOList,
                    configNameMap, relationMap, ownerNameMap, stockAgeMap, productImageDTOMap, outStockOrderItemPOList);
            dto.setBatchTaskItemDTOList(batchTaskSortItemDTOList);
            if (CollectionUtils.isNotEmpty(sowOrderDTOS)) {
                Optional<SowOrderDTO> sowOrderDTO = sowOrderDTOS.stream()
                    .filter(item -> item.getOutStockOrderId().equals(Long.valueOf(dto.getOrderId()))).findAny();
                if (sowOrderDTO.isPresent()) {
                    dto.setSowLocationId(sowOrderDTO.get().getSowLocationId());
                    dto.setSowLocationName(sowOrderDTO.get().getSowLocationName());
                    dto.setSowOrderSequence(sowOrderDTO.get().getSowOrderSequence());
                }
            }
            // 当订单分在多个拣货任务时，订单在其他拣货任务的拣货数量
            if (CollectionUtils.isNotEmpty(otherBatchTaskDTOList)) {
                List<BatchTaskSortOrderOtherDTO> otherDTOList = otherBatchTaskDTOList.stream()
                    .filter(p -> p.getRefOrderId().equals(itemDTO.getRefOrderId())).collect(Collectors.toList());
                dto.setOtherBatchTaskDTOList(otherDTOList);
            }
            // 是否需要生内配单
            dto.setAllocationFlag(BatchTaskConvertor.convertToBoolean(itemDTO.getCreateAllocation()));
            // 二级仓出库位
            Optional<
                OutStockOrderLocationDTO> optionalOutStockOrderLocationDTO =
                    orderLocationDTOS.stream()
                        .filter(p -> Objects.equals(p.getOmsOrderId(), (StreamUtils.isNum(itemDTO.getBusinessId())
                            ? Long.valueOf(itemDTO.getBusinessId()) : Long.valueOf(itemDTO.getRefOrderId()))))
                        .findFirst();
            if (optionalOutStockOrderLocationDTO.isPresent()) {
                dto.setSecondToLocationId(optionalOutStockOrderLocationDTO.get().getToLocationId());
                dto.setSecondToLocationName(optionalOutStockOrderLocationDTO.get().getToLocationName());
                dto.setSecondWarehouseName(optionalOutStockOrderLocationDTO.get().getToWarehouseName());
            }
            // 内配单原单拣货任务详情的拣货位为空时，默认为内配单出库位
            if (Objects.equals(dto.getAllocationFlag(), true)) {
                if (CollectionUtils.isNotEmpty(dto.getBatchTaskItemDTOList())) {
                    dto.getBatchTaskItemDTOList().forEach(p -> {
                        if (p != null && defaultLocationConfigDTO != null && p.getLocationId() == null) {
                            p.setLocationId(defaultLocationConfigDTO.getTemporaryLocationId());
                            p.setLocationName(defaultLocationConfigDTO.getTemporaryLocationName());
                        }
                    });
                }
            }

            // 订单托盘数据
            String orderId = dto.getOrderId();
            if (orderPalletMap != null && !StringUtils.isEmpty(orderId) && StringUtils.isNumeric(orderId)
                && CollectionUtils.isNotEmpty(orderPalletMap.get(Long.valueOf(orderId)))) {
                dto.setPalletNoList(orderPalletMap.get(Long.valueOf(orderId)));
            }

            // 设置批次类型
            dto.setOutBoundType(itemDTO.getOutBoundType());
            list.add(dto);
        }
        return list;
    }

    /**
     * 配置SKU编码
     *
     * @param
     * @param batchTaskItemDTOList
     * @return
     */
    public static List<BatchTaskSortItemDTO> getBatchTaskSortItemDTOList(Map<Long, ProductCodeDTO> codeMap,
        List<BatchTaskItemDTO> batchTaskItemDTOList, List<LocationReturnDTO> locationDTOList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, Map<Long, String> configNameMap,
        Map<Long, List<Long>> relationMap, Map<Long, String> ownerNameMap, Map<Long, Boolean> stockAgeMap,
        Map<Long, ProductImageDTO> productImageDTOMap, List<OutStockOrderItemPO> outStockOrderItemPOList) {
        List<BatchTaskSortItemDTO> batchTaskSortItemDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(batchTaskItemDTOList)) {
            return batchTaskSortItemDTOList;
        }
        Map<Long, OutStockOrderItemPO> outStockOrderItemPOMap =
            outStockOrderItemPOList.stream().collect(Collectors.toMap(OutStockOrderItemPO::getId, v -> v));

        for (BatchTaskItemDTO batchTaskItemDTO : batchTaskItemDTOList) {
            Long skuId = batchTaskItemDTO.getSkuId();
            BatchTaskSortItemDTO batchTaskSortItemDTO = new BatchTaskSortItemDTO();
            BeanUtils.copyProperties(batchTaskItemDTO, batchTaskSortItemDTO);
            // 拿到瓶码和箱码
            // ProductInfoSpecification productInfoSpecification = productInfoSpecificationMap.get(skuId);
            // AssertUtils.notNull(productInfoSpecification, "拿不到产品规格信息");
            // List<String> packageCode = new ArrayList<>();
            // if (productInfoSpecification.getBoxCodes() != null) {
            // packageCode =
            // productInfoSpecification.getBoxCodes().stream().map(Code::getCode).collect(Collectors.toList());
            // }
            // List<String> unitCode = new ArrayList<>();
            // if (productInfoSpecification.getProductInfo().getBottleCode() != null) {
            // unitCode = Arrays.asList(productInfoSpecification.getProductInfo().getBottleCode().getCode());
            // }
            ProductCodeDTO productCodeDTO = codeMap.get(skuId);
            if (productCodeDTO != null) {
                batchTaskSortItemDTO.setPackageCode(productCodeDTO.getPackageCode());
                batchTaskSortItemDTO.setUnitCode(productCodeDTO.getUnitCode());
            }
            // 货位类型
            if (CollectionUtils.isNotEmpty(locationDTOList)) {
                Optional<LocationReturnDTO> locationReturnDTO =
                    locationDTOList.stream().filter(p -> p.getId().equals(batchTaskItemDTO.getLocationId())).findAny();
                if (locationReturnDTO.isPresent()) {
                    batchTaskSortItemDTO.setLocationType(locationReturnDTO.get().getSubcategory());
                    batchTaskSortItemDTO.setLocationTypeName(locationReturnDTO.get().getSubcategoryName());
                    batchTaskSortItemDTO.setLocationSequence(locationReturnDTO.get().getSequence());
                }
            }
            // 订单项id
            batchTaskSortItemDTO.setRefOrderItemId("0");
            if (CollectionUtils.isNotEmpty(outStockOrderItemPOList)) {
                // 2025-02-10 查找拣货任务订单项关联，给OrderItemID复制，后续打包用
                orderItemTaskInfoPOList.stream()
                    .filter(p -> Objects.equals(p.getBatchTaskItemId(), batchTaskSortItemDTO.getId())
                        && p.getRefOrderItemId() != null)
                    .findAny().ifPresent(taskItem -> {
                        batchTaskSortItemDTO.setRefOrderItemId(taskItem.getRefOrderItemId().toString());
                    });
            }
            if (Objects.nonNull(batchTaskSortItemDTO.getRefOrderItemId())) {
                OutStockOrderItemPO outStockOrderItemPO =
                    outStockOrderItemPOMap.get(batchTaskSortItemDTO.getRefOrderItemId());
                if (Objects.nonNull(outStockOrderItemPO)) {
                    batchTaskSortItemDTO.setIsPromotion(outStockOrderItemPO.getIsAdvent());
                }
            }

            // 控货策略名称
            if (batchTaskSortItemDTO.getControlConfigId() != null && configNameMap != null) {
                batchTaskSortItemDTO.setControlConfigName(configNameMap.get(batchTaskSortItemDTO.getControlConfigId()));
            }
            // 关联产品
            if (relationMap != null) {
                batchTaskSortItemDTO.setRelationProductSkuIds(relationMap.get(skuId));
            }
            // 货主名称
            if (ownerNameMap != null && batchTaskSortItemDTO.getOwnerId() != null) {
                batchTaskSortItemDTO.setOwnerName(ownerNameMap.get(batchTaskSortItemDTO.getOwnerId()));
            }
            // 是否是库龄管控产品
            if (stockAgeMap != null) {
                batchTaskSortItemDTO.setInventoryAgeFlag(stockAgeMap.get(skuId));
            }
            // 产品图片
            if (productImageDTOMap != null) {
                ProductImageDTO productImageDTO = productImageDTOMap.get(skuId);
                if (productImageDTO != null) {
                    batchTaskSortItemDTO.setDefaultImageFile(productImageDTO.getDefaultImageFile());
                    batchTaskSortItemDTO.setImageFiles(productImageDTO.getImageFiles());
                }
            }

            batchTaskSortItemDTOList.add(batchTaskSortItemDTO);
        }
        return batchTaskSortItemDTOList;
    }

    public static List<BatchTaskItemDTO> batchTaskItemPOToDTO(List<BatchTaskItemPO> poList) {
        List<BatchTaskItemDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(poList)) {
            return dtoList;
        }
        poList.forEach(po -> {
            dtoList.add(batchTaskItemPOToDto(po));
        });
        return dtoList;
    }

    public static BatchTaskItemDTO batchTaskItemPOToDto(BatchTaskItemPO po) {
        if (po == null) {
            return null;
        }
        BatchTaskItemDTO dto = new BatchTaskItemDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }
}
