package com.yijiupi.himalaya.supplychain.waves.concertsow.service;

import java.util.List;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.concertsow.dto.ConcertSowTaskRequestDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.PDASowTaskInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskReceiveDTO;

/**
 * 多人协作播种任务
 *
 * <AUTHOR>
 */
public interface IConcertSowTaskService {

    /**
     * 领取播种任务明细
     *
     * @param request
     */
    void getSowTask(ConcertSowTaskRequestDTO request);

    /**
     * 待播种任务查询
     *
     * @param sowTaskReceiveDTO
     */
    SowTaskDTO queryWaitSowTask(SowTaskReceiveDTO sowTaskReceiveDTO);

    /**
     * 根据播种任务查询波次任务列表
     * 
     */
    List<PDASowTaskInfoDTO> listBatchTaskBySowTaskNos(SowTaskQueryDTO sowTaskQueryDTO);

    /**
     * 二次分拣通用查询
     *
     * @param sowTaskQueryDTO
     * @return
     */
    PageList<BatchTaskDTO> listBatchTaskInfo(SowTaskQueryDTO sowTaskQueryDTO);

}
