package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.yijiupi.himalaya.supplychain.waves.enums.BatchAttrSettingWayConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.OrderPickFlagEnum;
import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;

/**
 * <AUTHOR>
 * @title: WavesStrategyBO
 * @description: 以后彻底删除对WavesStrategyDTO的依赖，太多地方引用，暂时用继承简单处理
 * @date 2022-10-18 11:10
 */
public class WavesStrategyBO extends WavesStrategyDTO {
    /**
     * 是否总的开启机器人拣货配置
     */
    private Byte openRobotPickConfig;
    /**
     * 是否开启托盘位
     */
    private Byte openTrayLocationConfig;
    /**
     * 开启实时分拣
     */
    private Byte openWarehouseRealTimePickingByOrder;
    /**
     * 福州分拣，酒饮休食必须一起分单的片区id列表
     */
    private List<Long> areaIds;

    /**
     * 波次属性设置方式：0：无；1、线路；2、片区
     *
     * @see BatchAttrSettingWayConstants
     */
    private Byte batchAttrSettingWay;

    /**
     * 是否开启电子标签分拣
     */
    private Boolean openDigitalPickingSystem = Boolean.FALSE;

    /**
     * @see OrderPickFlagEnum
     */
    private Byte orderPickFlag;
    /**
     * 是否开启分仓配置
     */
    private Boolean openWarehouseSeparateAttributeConfig;
    /**
     * 特征类型
     */
    private Byte featureType;
    /**
     * 是否按用户拣货
     */
    private boolean pickByCustomer;
    /**
     * 是否开启酒饮二次分拣
     */
    private Boolean openSecondSort;
    /**
     * 整件订单单独分拣
     */
    private Integer packageOrderPickAlone = ConditionStateEnum.否.getType().intValue();


    public Byte getFeatureType() {
        return featureType;
    }

    public void setFeatureType(Byte featureType) {
        this.featureType = featureType;
    }

    /**
     * 获取 是否总的开启机器人拣货配置
     *
     * @return openRobotPickConfig 是否总的开启机器人拣货配置
     */
    public Byte getOpenRobotPickConfig() {
        return this.openRobotPickConfig;
    }

    /**
     * 设置 是否总的开启机器人拣货配置
     *
     * @param openRobotPickConfig 是否总的开启机器人拣货配置
     */
    public void setOpenRobotPickConfig(Byte openRobotPickConfig) {
        this.openRobotPickConfig = openRobotPickConfig;
    }

    /**
     * 获取 是否开启托盘位
     *
     * @return openTrayLocationConfig 是否开启托盘位
     */
    public Byte getOpenTrayLocationConfig() {
        return this.openTrayLocationConfig;
    }

    /**
     * 设置 是否开启托盘位
     *
     * @param openTrayLocationConfig 是否开启托盘位
     */
    public void setOpenTrayLocationConfig(Byte openTrayLocationConfig) {
        this.openTrayLocationConfig = openTrayLocationConfig;
    }

    /**
     * 获取 福州分拣，酒饮休食必须一起分单的片区id列表
     *
     * @return areaIds 福州分拣，酒饮休食必须一起分单的片区id列表
     */
    public List<Long> getAreaIds() {
        return CollectionUtils.isEmpty(this.areaIds) ? Collections.emptyList() : this.areaIds;
    }

    /**
     * 设置 福州分拣，酒饮休食必须一起分单的片区id列表
     *
     * @param areaIds 福州分拣，酒饮休食必须一起分单的片区id列表
     */
    public void setAreaIds(List<Long> areaIds) {
        this.areaIds = areaIds;
    }

    /**
     * 获取 开启实时分拣
     *
     * @return openWarehouseRealTimePickingByOrder 开启实时分拣
     */
    public Byte getOpenWarehouseRealTimePickingByOrder() {
        return this.openWarehouseRealTimePickingByOrder;
    }

    /**
     * 设置 开启实时分拣
     *
     * @param openWarehouseRealTimePickingByOrder 开启实时分拣
     */
    public void setOpenWarehouseRealTimePickingByOrder(Byte openWarehouseRealTimePickingByOrder) {
        this.openWarehouseRealTimePickingByOrder = openWarehouseRealTimePickingByOrder;
    }

    /**
     * 获取 是否开启电子标签分拣
     *
     * @return openDigitalPickingSystem 是否开启电子标签分拣
     */
    public Boolean isOpenDigitalPickingSystem() {
        return this.openDigitalPickingSystem;
    }

    /**
     * 设置 是否开启电子标签分拣
     *
     * @param openDigitalPickingSystem 是否开启电子标签分拣
     */
    public void setOpenDigitalPickingSystem(Boolean openDigitalPickingSystem) {
        this.openDigitalPickingSystem = openDigitalPickingSystem;
    }

    /**
     * 获取 是否开启分仓配置
     *
     * @return openWarehouseSeparateAttributeConfig 是否开启分仓配置
     */
    public Boolean getOpenWarehouseSeparateAttributeConfig() {
        return this.openWarehouseSeparateAttributeConfig;
    }

    /**
     * 设置 是否开启分仓配置
     *
     * @param openWarehouseSeparateAttributeConfig 是否开启分仓配置
     */
    public void setOpenWarehouseSeparateAttributeConfig(Boolean openWarehouseSeparateAttributeConfig) {
        this.openWarehouseSeparateAttributeConfig = openWarehouseSeparateAttributeConfig;
    }

    /**
     * 获取 是否开启电子标签分拣
     *
     * @return openDigitalPickingSystem 是否开启电子标签分拣
     */
    public Boolean getOpenDigitalPickingSystem() {
        return this.openDigitalPickingSystem;
    }

    /**
     * 获取 特征类型
     *
     * @see BatchAttrSettingWayConstants
     */
    public Byte getBatchAttrSettingWay() {
        return batchAttrSettingWay;
    }

    /**
     * 波次属性设置方式：0：无；1、线路；2、片区
     *
     * @see BatchAttrSettingWayConstants
     */
    public void setBatchAttrSettingWay(Byte batchAttrSettingWay) {
        this.batchAttrSettingWay = batchAttrSettingWay;
    }

    public Byte getOrderPickFlag() {
        return orderPickFlag;
    }

    public void setOrderPickFlag(Byte orderPickFlag) {
        this.orderPickFlag = orderPickFlag;
    }

    /**
     * 获取 是否按用户拣货
     *
     * @return pickByCustomer 是否按用户拣货
     */
    public boolean isPickByCustomer() {
        return this.pickByCustomer;
    }

    /**
     * 设置 是否按用户拣货
     *
     * @param pickByCustomer 是否按用户拣货
     */
    public void setPickByCustomer(boolean pickByCustomer) {
        this.pickByCustomer = pickByCustomer;
    }

    /**
     * 获取 是否开启酒饮二次分拣
     *
     * @return openSecondSort 是否开启酒饮二次分拣
     */
    public Boolean getOpenSecondSort() {
        return this.openSecondSort;
    }

    /**
     * 设置 是否开启酒饮二次分拣
     *
     * @param openSecondSort 是否开启酒饮二次分拣
     */
    public void setOpenSecondSort(Boolean openSecondSort) {
        this.openSecondSort = openSecondSort;
    }

    /**
     * 获取 整件订单单独分拣
     *
     * @return packageOrderPickAlone 整件订单单独分拣
     */
    public Integer getPackageOrderPickAlone() {
        return this.packageOrderPickAlone;
    }

    /**
     * 设置 整件订单单独分拣
     *
     * @param packageOrderPickAlone 整件订单单独分拣
     */
    public void setPackageOrderPickAlone(Integer packageOrderPickAlone) {
        if (Objects.isNull(packageOrderPickAlone)) {
            return;
        }
        this.packageOrderPickAlone = packageOrderPickAlone;
    }

    public static final class WavesStrategyBOBuilder {
        private Integer warehouseId;
        private Byte pickingType;
        private Byte orderSelection;
        private Byte pickingGroupStrategy;
        private Integer groupType;
        private Byte passPickType;
        private Long deliveryCarId;
        private Long logisticsCompanyId;
        private Byte orderPickFlag;

        private WavesStrategyBOBuilder() {
        }

        public static WavesStrategyBOBuilder buildWavesStrategyBuilder() {
            return new WavesStrategyBOBuilder();
        }

        public WavesStrategyBOBuilder withDeliveryCarId(Long deliveryCarId) {
            this.deliveryCarId = deliveryCarId;
            return this;
        }

        public WavesStrategyBOBuilder withLogisticsCompanyId(Long logisticsCompanyId) {
            this.logisticsCompanyId = logisticsCompanyId;
            return this;
        }

        public WavesStrategyBOBuilder withWarehouseId(Integer warehouseId) {
            this.warehouseId = warehouseId;
            return this;
        }

        public WavesStrategyBOBuilder withPickingType(Byte pickingType) {
            this.pickingType = pickingType;
            return this;
        }

        public WavesStrategyBOBuilder withOrderSelection(Byte orderSelection) {
            this.orderSelection = orderSelection;
            return this;
        }

        public WavesStrategyBOBuilder withPickingGroupStrategy(Byte pickingGroupStrategy) {
            this.pickingGroupStrategy = pickingGroupStrategy;
            return this;
        }

        public WavesStrategyBOBuilder withGroupType(Integer groupType) {
            this.groupType = groupType;
            return this;
        }

        public WavesStrategyBOBuilder withPassPickType(Byte passPickType) {
            this.passPickType = passPickType;
            return this;
        }

        public WavesStrategyBOBuilder withOrderPickFlag(Byte orderPickFlag) {
            this.orderPickFlag = orderPickFlag;
            return this;
        }

        public WavesStrategyBO build() {
            WavesStrategyBO wavesStrategyBO = new WavesStrategyBO();
            wavesStrategyBO.setWarehouseId(warehouseId);
            wavesStrategyBO.setPickingType(pickingType);
            wavesStrategyBO.setOrderSelection(orderSelection);
            wavesStrategyBO.setPickingGroupStrategy(pickingGroupStrategy);
            wavesStrategyBO.setGroupType(groupType);
            wavesStrategyBO.setPassPickType(passPickType);
            wavesStrategyBO.setDeliveryCarId(deliveryCarId);
            wavesStrategyBO.setLogisticsCompanyId(logisticsCompanyId);
            wavesStrategyBO.setOrderPickFlag(orderPickFlag);
            return wavesStrategyBO;
        }
    }

    public boolean hasOpenTrayLocationConfig() {
        if (Objects.isNull(openTrayLocationConfig)) {
            return Boolean.FALSE;
        }
        if (ConditionStateEnum.是.getType().equals(openTrayLocationConfig)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    public boolean hasOpenWarehouseRealTimePickingByOrder() {
        if (Objects.isNull(openWarehouseRealTimePickingByOrder)) {
            return Boolean.FALSE;
        }
        if (ConditionStateEnum.是.getType().equals(openWarehouseRealTimePickingByOrder)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    public boolean isSettingWayAboutDriver() {
        if (Objects.isNull(batchAttrSettingWay)) {
            return Boolean.FALSE;
        }
        if (BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_ROUTE.equals(batchAttrSettingWay)) {
            return Boolean.TRUE;
        }
        if (BatchAttrSettingWayConstants.BATCH_ATTR_SETTING_WAY_DRIVER.equals(batchAttrSettingWay)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }
}
