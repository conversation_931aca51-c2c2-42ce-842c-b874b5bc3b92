package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo.OutStockOrderAdminCancelHandlePickBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo.OutStockOrderAdminCancelHandlePickResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2023-05-25 09:07
 */
public abstract class OutStockOrderAdminCancelBaseBL {

    @Autowired
    protected OutStockOrderMapper outStockOrderMapper;
    @Autowired
    protected OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    protected BatchTaskMapper batchTaskMapper;
    @Autowired
    protected BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    protected SowTaskMapper sowTaskMapper;
    @Autowired
    protected SowTaskItemMapper sowTaskItemMapper;
    @Autowired
    protected SowOrderMapper sowOrderMapper;
    @Autowired
    protected BatchMapper batchMapper;
    @Autowired
    protected OutStockOrderItemMapper outStockOrderItemMapper;
    protected static final Logger LOG = LoggerFactory.getLogger(OutStockOrderAdminCancelBaseBL.class);

    @Deprecated
    public boolean support(OutStockOrderAdminCancelHandlePickBO bo) {
        return Boolean.FALSE;
    }

    public boolean baseSupport(OutStockOrderAdminCancelHandlePickBO bo) {
        return Boolean.FALSE;
    }

    public OutStockOrderAdminCancelHandlePickResultBO cancel(OutStockOrderAdminCancelHandlePickBO bo) {
        return doCancel(bo);
    }

    protected abstract OutStockOrderAdminCancelHandlePickResultBO doCancel(OutStockOrderAdminCancelHandlePickBO bo);

}

