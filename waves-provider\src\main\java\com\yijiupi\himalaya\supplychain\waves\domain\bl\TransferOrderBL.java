package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.constants.WarehouseConfigConstants;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderItemDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.StoreTransferEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.StoreTransferStateEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.IStoreTransferOrderService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.TransferOrderLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.LocationTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.search.OutStockOrderSearchSO;

import javax.annotation.Resource;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2024/5/30
 */
@Service
public class TransferOrderBL {

    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private IStoreTransferOrderService iStoreTransferOrderService;
    @Reference
    private IBatchInventoryQueryService iBatchInventoryQueryService;
    @Reference
    private ILocationService iLocationService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Resource
    private GlobalCache globalCache;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;

    private static final Logger LOGGER = LoggerFactory.getLogger(TransferOrderBL.class);

    public void transferOrderLocation(TransferOrderLocationDTO dto) {
        OutStockOrderSearchSO querySO = new OutStockOrderSearchSO();
        querySO.setOrgId(dto.getOrgId());
        querySO.setWareHouseId(dto.getWarehouseId());
        querySO.setOrderIds(dto.getOrderIds());
        List<OutStockOrderPO> orderPOList = outStockOrderMapper.listAllInfo(querySO);
        handle2p5plusLocation(orderPOList, dto);
    }

    private void handle2p5plusLocation(List<OutStockOrderPO> orderPOList, TransferOrderLocationDTO dto) {
        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(dto.getWarehouseId());
        if (WarehouseConfigConstants.CARGO_STOCK_OFF.equals(warehouseConfigDTO.getIsOpenCargoStock())
            || WarehouseConfigConstants.CARGO_STOCK_OFF.equals(warehouseConfigDTO.getIsOpenLocationStockGroup())) {
            return;
        }

        for (OutStockOrderPO orderPO : orderPOList) {
            List<OutStockOrderItemPO> itemList = orderPO.getItems().stream()
                .filter(item -> Objects.nonNull(item.getSowTaskItemId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(itemList)) {
                continue;
            }

            List<Long> skuIds = itemList.stream().map(m -> m.getSkuid()).distinct().collect(Collectors.toList());

            BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
            batchInventoryQueryDTO.setWarehouseId(dto.getWarehouseId());
            batchInventoryQueryDTO.setSkuIds(skuIds);
            batchInventoryQueryDTO.setLocationCategory(CategoryEnum.CARGO_AREA.getValue());
            batchInventoryQueryDTO.setSubCategoryList(Arrays.asList(LocationAreaEnum.周转区.getType()));
            PageList<BatchInventoryDTO> pageList =
                iBatchInventoryQueryService.findBatchInventoryList(batchInventoryQueryDTO);
            LOGGER.info("查到货位库存！， 入参 :{}", JSON.toJSONString(pageList));
            if (CollectionUtils.isEmpty(pageList.getDataList())) {
                LOGGER.info("没查到货位库存！， 入参 :{}", JSON.toJSONString(batchInventoryQueryDTO));
                return;
            }

            LoactionDTO newLocation = getOutStockLocationArea(dto.getOrgId(), dto.getWarehouseId());

            List<Long> locationIds = pageList.getDataList().stream().map(BatchInventoryDTO::getLocationId).distinct()
                .collect(Collectors.toList());

            List<LoactionDTO> locationList = iLocationService.findLocationByIds(locationIds);
            Map<Long, LoactionDTO> locationMap =
                locationList.stream().collect(Collectors.toMap(LoactionDTO::getId, v -> v));

            Warehouse warehouse = iWarehouseQueryService.findWarehouseById(dto.getWarehouseId());
            String userName = getUserName(orderPO);

            Date currentTime = new Date();
            StoreTransferOrderDTO storeTransferOrderDTO = new StoreTransferOrderDTO();
            storeTransferOrderDTO.setOrg_id(warehouse.getCityId());
            // storeTransferOrderDTO.setStoreTransferNo();
            storeTransferOrderDTO.setState(StoreTransferStateEnum.待移库.getType());
            storeTransferOrderDTO.setTransferType(StoreTransferEnum.人工移库.getType());
            storeTransferOrderDTO.setStartTime(currentTime);
            // storeTransferOrderDTO.setFinishTime();
            storeTransferOrderDTO.setWarehouse_Id(dto.getWarehouseId());
            storeTransferOrderDTO.setWarehouseName(warehouse.getName());
            storeTransferOrderDTO.setSorter_id(dto.getUserId());
            storeTransferOrderDTO.setSorterName(userName);
            storeTransferOrderDTO.setRemark("货位关联");
            storeTransferOrderDTO.setCreateUser(userName);
            storeTransferOrderDTO.setStartTimeStr(DateUtils.getTimeFormat(currentTime));
            // storeTransferOrderDTO.setIgnoreProductionDate();
            // storeTransferOrderDTO.setIgnoreHasNotEnoughStore();
            // storeTransferOrderDTO.setProcessLocationInventory();
            // storeTransferOrderDTO.setLocationIds();
            // storeTransferOrderDTO.setStoreTransferId();
            // storeTransferOrderDTO.setOperateUser();

            Map<Long, List<BatchInventoryDTO>> batchInventoryGroupMap =
                pageList.getDataList().stream().collect(Collectors.groupingBy(BatchInventoryDTO::getProductSkuId));
            List<StoreTransferOrderItemDTO> storeTransferOrderItemDTOS = itemList.stream().map(itemPO -> {

                List<BatchInventoryDTO> inventoryDTOList = batchInventoryGroupMap.get(itemPO.getSkuid());
                BatchInventoryDTO inventory = inventoryDTOList.get(0);

                StoreTransferOrderItemDTO itemDTO = new StoreTransferOrderItemDTO();
                LoactionDTO oldLocation = locationMap.get(inventory.getLocationId());

                itemDTO.setProductSpecificationId(inventory.getProductSpecificationId());
                itemDTO.setSecOwnerId(inventory.getSecOwnerId());
                itemDTO.setBatchTime(inventory.getBatchTime());
                itemDTO.setExpireTime(inventory.getExpireTime());
                itemDTO.setProductionDate(inventory.getProductionDate());
                itemDTO.setOrg_id(warehouse.getCityId());
                // itemDTO.setStoreTransfer_id();
                itemDTO.setState(StoreTransferStateEnum.待移库.getType());
                itemDTO.setOwner_id(inventory.getOwnerId());
                itemDTO.setOwnerName(inventory.getOwnerName());
                itemDTO.setSkuId(inventory.getProductSkuId());
                itemDTO.setProductName(inventory.getProductSkuName());
                // itemDTO.setProductBrand();
                // itemDTO.setCategoryName();
                itemDTO.setSpecName(inventory.getSpecificationName());
                itemDTO.setSpecQuantity(inventory.getPackageQuantity());
                itemDTO.setPackageName(inventory.getPackageName());
                BigDecimal[] count = itemPO.getUnittotalcount().divideAndRemainder(itemPO.getSpecquantity());
                itemDTO.setPackageCount(count[0]);
                itemDTO.setUnitName(inventory.getUnitName());
                itemDTO.setUnitCount(count[1]);
                // itemDTO.setUnitTotalCount();
                itemDTO.setOverMovePackageCount(count[0]);
                itemDTO.setOverMoveUnitCount(count[1]);
                itemDTO.setFromChannel(inventory.getChannel().toString());
                itemDTO.setToLocationName(newLocation.getName());
                itemDTO.setToChannel(inventory.getChannel().toString());
                itemDTO.setRemark("货位关联");
                itemDTO.setCreateUser(userName);
                // itemDTO.setCreatetime();
                // itemDTO.setLastupdateuser();
                // itemDTO.setLastupdatetime();
                // itemDTO.setId();
                itemDTO.setToLocation_id(newLocation.getId());
                itemDTO.setFromLocationName(oldLocation.getName());
                itemDTO.setFromLocation_id(oldLocation.getId());
                // itemDTO.setPackageCode();
                // itemDTO.setUnitCode();
                // itemDTO.setFromChannelText();
                // itemDTO.setToChannelText();
                // itemDTO.setStateText();
                itemDTO.setProductStoreId(inventory.getProductStoreId());
                itemDTO.setProductStoreBatchId(inventory.getStoreBatchId());
                itemDTO.setBatchAttributeInfoNo(inventory.getBatchAttributeInfoNo());
                // itemDTO.setAutoAllotFlag();
                // itemDTO.setFromLocationSubcategory();
                // itemDTO.setToLocationSubcategory();
                // itemDTO.setVesselId();
                // itemDTO.setVesselName();
                // itemDTO.setRelationOrderType();
                // itemDTO.setRelationProductionDate();

                return itemDTO;
            }).collect(Collectors.toList());
            storeTransferOrderDTO.setStoreTransferOrderItemDTOS(storeTransferOrderItemDTOS);
            LOGGER.info("进行库存转移，参数：{}", JSON.toJSONString(storeTransferOrderDTO));
            iStoreTransferOrderService.updateStoreTranferNoOrder(storeTransferOrderDTO);
        }

    }

    private String getUserName(OutStockOrderPO orderPO) {
        String userName = "系统";
        if (StringUtils.isNotEmpty(orderPO.getLastupdateuser())) {
            userName = globalCache.getUserName(orderPO.getLastupdateuser());
        }
        return userName;
    }

    private LoactionDTO getOutStockLocationArea(Integer cityId, Integer warehouseId) {
        LocationQueryDTO dto = new LocationQueryDTO();
        dto.setSubcategory(LocationTypeEnum.AREA.byteValue());
        dto.setLocSubcategory(LocationAreaEnum.集货区.getType().byteValue());
        dto.setCityId(cityId);
        dto.setWarehouseId(warehouseId);
        List<LoactionDTO> loactionDTOList = iLocationService.findLocationListByIdAndCategory(dto);
        if (org.springframework.util.CollectionUtils.isEmpty(loactionDTOList)) {
            return null;
        }

        return loactionDTOList.stream().findFirst().get();
    }

}
