package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 播种任务
 */
public class SowTaskPO implements Serializable {

    /**
     * 播种任务id
     */
    private Long id;

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 波次表id
     */
    private String batchId;

    /**
     * 波次编号
     */
    private String batchNo;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 播种任务名称
     */
    private String sowTaskName;

    /**
     * 播种类型 待播种(0),播种中(1),已播种(2)
     */
    private Byte sowTaskType;

    /**
     * 播种状态 待播种(0),播种中(1),已播种(2)
     */
    private Byte state;

    /**
     * 订单数量
     */
    private Integer orderCount;

    /**
     * 商品种类
     */
    private Integer skuCount;

    /**
     * 大件总数
     */
    private BigDecimal packageAmount;

    /**
     * 小件总数
     */
    private BigDecimal unitAmount;

    /**
     * 集货位id
     */
    private Long locationId;

    /**
     * 集货位名称
     */
    private String locationName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    private Date lastUpdateTime;

    /**
     * 最后更新人
     */
    private String lastUpdateUser;

    /**
     * 操作方式:(0:拣货+播种,1:不拣货+播种,2:拣货+不播种)
     */
    private Byte operationMode;

    /**
     * 播种开始时间
     */
    private Date startTime;

    /**
     * 播种完成时间
     */
    private Date completeTime;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 操作人id
     */
    private Integer operatorId;

    /**
     * 播种明细项
     */
    private List<SowTaskItemPO> sowTaskItemPOS;

    /**
     * 自提点数量
     */
    private Integer addressCount;

    /**
     * 备注
     */
    private String remark;
    /**
     * 分拣员id
     */
    private Integer sorterId;
    /**
     * 分拣员名称
     */
    private String sorterName;
    /**
     * 打包状态
     */
    private Byte packageState;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;
    /**
     * 绩效所属班次
     */
    private String shiftOfPerformance;

    public Byte getPackageState() {
        return packageState;
    }

    public void setPackageState(Byte packageState) {
        this.packageState = packageState;
    }

    public Integer getSorterId() {
        return sorterId;
    }

    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    public String getSorterName() {
        return sorterName;
    }

    public void setSorterName(String sorterName) {
        this.sorterName = sorterName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public String getSowTaskName() {
        return sowTaskName;
    }

    public void setSowTaskName(String sowTaskName) {
        this.sowTaskName = sowTaskName;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public BigDecimal getPackageAmount() {
        return packageAmount;
    }

    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    public BigDecimal getUnitAmount() {
        return unitAmount;
    }

    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Byte getOperationMode() {
        return operationMode;
    }

    public void setOperationMode(Byte operationMode) {
        this.operationMode = operationMode;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public Byte getSowTaskType() {
        return sowTaskType;
    }

    public void setSowTaskType(Byte sowTaskType) {
        this.sowTaskType = sowTaskType;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public List<SowTaskItemPO> getSowTaskItemPOS() {
        return sowTaskItemPOS;
    }

    public void setSowTaskItemPOS(List<SowTaskItemPO> sowTaskItemPOS) {
        this.sowTaskItemPOS = sowTaskItemPOS;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getAddressCount() {
        return addressCount;
    }

    public void setAddressCount(Integer addressCount) {
        this.addressCount = addressCount;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }

    /**
     * 获取 绩效所属班次
     *
     * @return shiftOfPerformance 绩效所属班次
     */
    public String getShiftOfPerformance() {
        return this.shiftOfPerformance;
    }

    /**
     * 设置 绩效所属班次
     *
     * @param shiftOfPerformance 绩效所属班次
     */
    public void setShiftOfPerformance(String shiftOfPerformance) {
        this.shiftOfPerformance = shiftOfPerformance;
    }
}