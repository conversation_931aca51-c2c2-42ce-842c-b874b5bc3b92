package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.yijiupi.himalaya.supplychain.waves.utils.CustomJsonDateDeserializer;

/**
 * <AUTHOR>
 * @since 2018/3/16
 */
public class BatchTaskQueryDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 波次任务号(拣货任务编号)
     */
    private String batchTaskNo;
    /**
     * 波次编号
     */
    private String batchNo;
    /**
     * 分拣员id
     */
    private Integer sorterId;
    /**
     * 分拣员
     */
    private String sorter;
    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte taskState;

    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private List<Byte> taskStateList;
    /**
     * 是否打印
     */
    private Byte printed;
    /**
     * 创建起始时间
     */
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date startTime;
    /**
     * 创建结束时间
     */
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date endTime;
    /**
     * 当前页
     */
    private Integer currentPage = 1;
    /**
     * 每页的数量
     */
    private Integer pageSize = 100;
    /**
     * 订单号
     */
    private List<String> refOrderNoList;
    /**
     * 订单ID
     */
    private List<String> refOrderIdList;
    /**
     * 是否查询关联的分拣任务项
     */
    private Boolean isQueryItem;
    /**
     * 查询条件类型
     */
    private List<Integer> queryCondition;
    /**
     * 分拣任务项ID
     */
    private String batchTaskId;

    private String sowTaskNo;

    private List<Integer> taskStates;

    /**
     * 备注
     */
    private String remark;

    /**
     * 任务属性
     */
    private String property;

    /**
     * 分仓属性
     */
    private List<Byte> taskWarehouseFeatureTypes;
    /**
     * 拣货方式
     * 
     * @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum
     */
    private Byte pickPattern;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;
    /**
     * 仓库id列表
     */
    private List<Integer> warehouseIds;
    /**
     * 绩效所属班次 YYYY-dd-MM
     */
    private String shiftOfPerformance;

    public List<Integer> getTaskStates() {
        return taskStates;
    }

    public void setTaskStates(List<Integer> taskStates) {
        this.taskStates = taskStates;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public List<Integer> getQueryCondition() {
        return queryCondition;
    }

    public void setQueryCondition(List<Integer> queryCondition) {
        this.queryCondition = queryCondition;
    }

    /**
     * 波次任务号(拣货任务编号)
     */
    private List<String> batchTaskNoList;

    public List<String> getBatchTaskNoList() {
        return batchTaskNoList;
    }

    public void setBatchTaskNoList(List<String> batchTaskNoList) {
        this.batchTaskNoList = batchTaskNoList;
    }

    public Boolean getQueryItem() {
        return isQueryItem;
    }

    public void setQueryItem(Boolean queryItem) {
        isQueryItem = queryItem;
    }

    public List<String> getRefOrderNoList() {
        return refOrderNoList;
    }

    public void setRefOrderNoList(List<String> refOrderNoList) {
        this.refOrderNoList = refOrderNoList;
    }

    public List<String> getRefOrderIdList() {
        return refOrderIdList;
    }

    public void setRefOrderIdList(List<String> refOrderIdList) {
        this.refOrderIdList = refOrderIdList;
    }

    public List<Byte> getTaskStateList() {
        return taskStateList;
    }

    public void setTaskStateList(List<Byte> taskStateList) {
        this.taskStateList = taskStateList;
    }

    /**
     * 获取 波次任务号
     *
     * @return batchTaskNo 波次任务号
     */
    public String getBatchTaskNo() {
        return this.batchTaskNo;
    }

    /**
     * 设置 波次任务号
     *
     * @param batchTaskNo 波次任务号
     */
    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    /**
     * 获取 分拣员id
     *
     * @return sorterId 分拣员id
     */
    public Integer getSorterId() {
        return this.sorterId;
    }

    /**
     * 设置 分拣员id
     *
     * @param sorterId 分拣员id
     */
    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    /**
     * 获取 创建起始时间
     *
     * @return startTime 创建起始时间
     */
    public Date getStartTime() {
        return this.startTime;
    }

    /**
     * 设置 创建起始时间
     *
     * @param startTime 创建起始时间
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * 获取 创建结束时间
     *
     * @return endTime 创建结束时间
     */
    public Date getEndTime() {
        return this.endTime;
    }

    /**
     * 设置 创建结束时间
     *
     * @param endTime 创建结束时间
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * 获取 当前页
     *
     * @return currentPage 当前页
     */
    public Integer getCurrentPage() {
        return this.currentPage;
    }

    /**
     * 设置 当前页
     *
     * @param currentPage 当前页
     */
    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    /**
     * 获取 @Fields 每页的数量
     *
     * @return pageSize @Fields 每页的数量
     */
    public Integer getPageSize() {
        return this.pageSize;
    }

    /**
     * 设置 @Fields 每页的数量
     *
     * @param pageSize @Fields 每页的数量
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 获取 波次编号
     *
     * @return batchNo 波次编号
     */
    public String getBatchNo() {
        return this.batchNo;
    }

    /**
     * 设置 波次编号
     *
     * @param batchNo 波次编号
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * 获取 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     *
     * @return taskState 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     */
    public Byte getTaskState() {
        return this.taskState;
    }

    /**
     * 设置 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     *
     * @param taskState 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     */
    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    /**
     * 获取 是否打印
     */
    public Byte getPrinted() {
        return this.printed;
    }

    /**
     * 设置 是否打印
     */
    public void setPrinted(Byte IsPrinted) {
        this.printed = IsPrinted;
    }

    /**
     * 获取 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getSorter() {
        return sorter;
    }

    public void setSorter(String sorter) {
        this.sorter = sorter;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    public List<Byte> getTaskWarehouseFeatureTypes() {
        return taskWarehouseFeatureTypes;
    }

    public void setTaskWarehouseFeatureTypes(List<Byte> taskWarehouseFeatureTypes) {
        this.taskWarehouseFeatureTypes = taskWarehouseFeatureTypes;
    }

    /**
     * 获取 是否查询关联的分拣任务项
     *
     * @return isQueryItem 是否查询关联的分拣任务项
     */
    public Boolean getIsQueryItem() {
        return this.isQueryItem;
    }

    /**
     * 设置 是否查询关联的分拣任务项
     *
     * @param isQueryItem 是否查询关联的分拣任务项
     */
    public void setIsQueryItem(Boolean isQueryItem) {
        this.isQueryItem = isQueryItem;
    }

    /**
     * 获取 拣货方式 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum
     *
     * @return pickPattern 拣货方式 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum
     */
    public Byte getPickPattern() {
        return this.pickPattern;
    }

    /**
     * 设置 拣货方式 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum
     *
     * @param pickPattern 拣货方式 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum
     */
    public void setPickPattern(Byte pickPattern) {
        this.pickPattern = pickPattern;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }

    /**
     * 获取 仓库id列表
     *
     * @return warehouseIds 仓库id列表
     */
    public List<Integer> getWarehouseIds() {
        return this.warehouseIds;
    }

    /**
     * 设置 仓库id列表
     *
     * @param warehouseIds 仓库id列表
     */
    public void setWarehouseIds(List<Integer> warehouseIds) {
        this.warehouseIds = warehouseIds;
    }

    /**
     * 获取 绩效所属班次
     *
     * @return shiftOfPerformance 绩效所属班次
     */
    public String getShiftOfPerformance() {
        return this.shiftOfPerformance;
    }

    /**
     * 设置 绩效所属班次
     *
     * @param shiftOfPerformance 绩效所属班次
     */
    public void setShiftOfPerformance(String shiftOfPerformance) {
        this.shiftOfPerformance = shiftOfPerformance;
    }
}
