package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.BeanUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BillReviewPO;
import com.yijiupi.himalaya.supplychain.waves.dto.billreview.BillReviewDTO;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;

public class BillReviewConvertor {

    public static BillReviewPO billReviewDTO2BillReviewPO(BillReviewDTO billReviewDTO) {
        if (billReviewDTO == null) {
            return null;
        }
        BillReviewPO billReviewPO = new BillReviewPO();
        BeanUtils.copyProperties(billReviewDTO, billReviewPO);
        return billReviewPO;
    }

    public static BillReviewPO billReviewDTO2NewBillReviewPO(BillReviewDTO billReviewDTO) {
        if (billReviewDTO == null) {
            return null;
        }
        if (billReviewDTO.getId() == null) {
            billReviewDTO.setId(UuidUtil.getUUidInt());
        }
        BillReviewPO billReviewPO = new BillReviewPO();
        BeanUtils.copyProperties(billReviewDTO, billReviewPO);

        billReviewPO.setCreateTime(new Date());
        billReviewPO.setLastUpdateTime(new Date());
        return billReviewPO;
    }

    public static List<BillReviewPO> billReviewDTOS2BillReviewPOS(List<BillReviewDTO> billReviewDTOS) {
        if (CollectionUtils.isEmpty(billReviewDTOS)) {
            return null;
        }
        List<BillReviewPO> billReviewPOS = new ArrayList<>();
        billReviewDTOS.forEach(billReviewDTO -> {
            billReviewPOS.add(billReviewDTO2BillReviewPO(billReviewDTO));
        });
        return billReviewPOS;
    }

    public static BillReviewDTO billReviewPO2BillReviewDTO(BillReviewPO billReview) {
        if (billReview == null) {
            return null;
        }
        BillReviewDTO billReviewDTO = new BillReviewDTO();
        BeanUtils.copyProperties(billReview, billReviewDTO);
        return billReviewDTO;
    }
}
