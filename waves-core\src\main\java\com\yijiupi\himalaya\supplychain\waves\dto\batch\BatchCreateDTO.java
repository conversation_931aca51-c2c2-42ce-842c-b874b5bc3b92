package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.enums.BatchGroupTypeEnum;

/**
 * <AUTHOR>
 * @Title: himalaya-supplychain-microservice-waves
 * @Package com.yijiupi.himalaya.supplychain.waves.dto.batch
 * @Description:
 * @date 2018/4/2 10:04
 */
public class BatchCreateDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 拣货方式 1 按订单拣货 2 按产品拣货
     */
    private Byte pickingType;
    /**
     * 拣货分组策略 1货区 2货位 3类目
     */
    private Byte pickingGroupStrategy;

    /**
     * 分组方式 1:按线路，2：按片区，3：按司机 4：按用户 5：按订单
     * 
     * @see BatchGroupTypeEnum
     */
    private Integer groupType;

    /**
     * 订单id集合
     */
    private List<String> orderIdList;

    /**
     * 操作人
     */
    private String operateUser;
    /**
     * 开启拣货通道 0 不开启,1 开启
     */
    private Byte passPickType;

    /**
     * 波次名称
     */
    private String batchName;

    /**
     * 拆分波次 0: 否，1：按订单拆分 2：按用户拆分
     */
    private Byte orderPickFlag;

    /**
     * 波次区分标示 1：快递直发订单，2：ERP调拨出库单
     */
    private Byte expressFlag;

    /**
     * 波次类别 0：酒批(默认) 1：微酒
     */
    private Byte batchType;

    /**
     * 不被搜索的业务单据类型
     */
    private List<Byte> notBusinessTypes;

    /**
     * 指定搜索的业务单据类型
     */
    private List<Byte> businessTypes;
    /**
     * 搜索的订单类型
     */
    private List<Byte> orderTypes;

    /**
     * 操作人
     */
    private Integer operateUserId;
    /**
     * 配送车辆ID
     */
    private Long deliveryCarId;
    /**
     * 物流公司ID
     */
    private Long logisticsCompanyId;

    public Long getDeliveryCarId() {
        return deliveryCarId;
    }

    public void setDeliveryCarId(Long deliveryCarId) {
        this.deliveryCarId = deliveryCarId;
    }

    public Long getLogisticsCompanyId() {
        return logisticsCompanyId;
    }

    public void setLogisticsCompanyId(Long logisticsCompanyId) {
        this.logisticsCompanyId = logisticsCompanyId;
    }

    public List<Byte> getNotBusinessTypes() {
        return notBusinessTypes;
    }

    public void setNotBusinessTypes(List<Byte> notBusinessTypes) {
        this.notBusinessTypes = notBusinessTypes;
    }

    public List<Byte> getBusinessTypes() {
        return businessTypes;
    }

    public void setBusinessTypes(List<Byte> businessTypes) {
        this.businessTypes = businessTypes;
    }

    public List<Byte> getOrderTypes() {
        return orderTypes;
    }

    public void setOrderTypes(List<Byte> orderTypes) {
        this.orderTypes = orderTypes;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getGroupType() {
        return groupType;
    }

    public void setGroupType(Integer groupType) {
        this.groupType = groupType;
    }

    public Byte getBatchType() {
        return batchType;
    }

    public void setBatchType(Byte batchType) {
        this.batchType = batchType;
    }

    public Byte getExpressFlag() {
        return expressFlag;
    }

    public void setExpressFlag(Byte expressFlag) {
        this.expressFlag = expressFlag;
    }

    public Byte getOrderPickFlag() {
        return orderPickFlag;
    }

    public void setOrderPickFlag(Byte orderPickFlag) {
        this.orderPickFlag = orderPickFlag;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public Byte getPassPickType() {
        return passPickType;
    }

    public void setPassPickType(Byte passPickType) {
        this.passPickType = passPickType;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Byte getPickingType() {
        return pickingType;
    }

    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    public Byte getPickingGroupStrategy() {
        return pickingGroupStrategy;
    }

    public void setPickingGroupStrategy(Byte pickingGroupStrategy) {
        this.pickingGroupStrategy = pickingGroupStrategy;
    }

    public List<String> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<String> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public Integer getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(Integer operateUserId) {
        this.operateUserId = operateUserId;
    }
}
