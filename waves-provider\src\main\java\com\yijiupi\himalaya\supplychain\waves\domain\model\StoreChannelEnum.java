package com.yijiupi.himalaya.supplychain.waves.domain.model;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/11/12 17:47
 */
public enum StoreChannelEnum {
    /**
     * 枚举
     */
    酒批((byte)0), 大宗产品((byte)1);

    /**
     * type
     */
    private Byte type;

    @SuppressWarnings("unchecked")
    private static Map<Byte, String> cache =
        (Map<Byte, String>)EnumSet.allOf(StoreChannelEnum.class).stream().collect(Collectors.toMap((p) -> {
            return p.type;
        }, (p) -> {
            return p.name();
        }));

    StoreChannelEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 返回枚举
     *
     * @param type
     * @return
     */
    public static StoreChannelEnum getEnum(Integer type) {
        StoreChannelEnum e = null;

        if (type != null) {
            for (StoreChannelEnum o : values()) {
                if (o.getType() == type) {
                    e = o;
                }
            }
        }
        return e;
    }

    public static String getEnmuName(Byte value) {
        String name = null;
        if (value != null) {
            name = (String)cache.get(value);
        }

        return name;
    }
}
