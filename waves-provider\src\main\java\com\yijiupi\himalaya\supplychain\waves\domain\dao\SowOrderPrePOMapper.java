package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPrePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SowOrderPrePOMapper {
    int deleteByPrimaryKey(Long id);

    int insertSelective(SowOrderPrePO record);

    SowOrderPrePO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SowOrderPrePO record);

    List<SowOrderPrePO> findSowOrderPreSowTaskPreId(@Param("sowTaskPreId") Long sowTaskPreId, @Param("version") Integer version, @Param("orgId") Integer orgId);
}