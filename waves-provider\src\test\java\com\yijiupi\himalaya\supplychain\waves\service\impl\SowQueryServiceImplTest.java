package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskQueryService;
import com.yijiupi.himalaya.supplychain.waves.batch.ISowQueryService;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskStateEnum;
import com.yijiupi.junit.runner.GeneralRunner;
import org.assertj.core.api.Assertions;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskQueryDTO;

@RunWith(GeneralRunner.class)
public class SowQueryServiceImplTest {

    @Reference
    private ISowQueryService sowQueryService;

    @Reference
    private IBatchTaskQueryService batchTaskQueryService;

    @Test
    public void findSowTaskListTest() {
        SowTaskQueryDTO sowTaskQueryDTO = new SowTaskQueryDTO();
        sowTaskQueryDTO.setOrgId(998);
        sowTaskQueryDTO.setBatchNo("BC998122101200006");
        PageList<SowTaskDTO> sowTaskList = sowQueryService.findSowTaskList(sowTaskQueryDTO);
        System.out.println(sowTaskList.getDataList());
    }

    @Test
    public void listOutStockOrderBySowTaskNoTest() {
        List<SowOrderInfoDTO> sowOrderInfoDTOS = sowQueryService.listOutStockOrderBySowTaskNo("BZ201811020001", 999);
        System.out.println(sowOrderInfoDTOS);
    }

    @Test
    public void listBatchTaskBySowTaskNo() {
        List<Integer> list = new ArrayList<>();
        List<BatchTaskDTO> batchTaskDTOS =
            batchTaskQueryService.listBatchTaskBySowTaskNo("BC998122101200006-2", list, 998);
        Assertions.assertThat(batchTaskDTOS).isNotNull();
    }

    @Test
    public void findSowTaskBySkuIds() {
        SowTaskQueryDTO queryDTO = new SowTaskQueryDTO();
        queryDTO.setSkuIds(Lists.newArrayList(5167024690248710227L));
        queryDTO.setStates(Arrays.asList(SowTaskStateEnum.待播种.getType(), SowTaskStateEnum.播种中.getType()));
        List<SowTaskLocationDTO> list = sowQueryService.findSowTaskBySkuIds(queryDTO);
        Assert.assertNotNull(list);
        System.out.println(JSON.toJSONString(list, SerializerFeature.PrettyFormat));
    }
}
