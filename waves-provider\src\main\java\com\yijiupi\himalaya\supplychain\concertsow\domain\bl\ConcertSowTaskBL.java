package com.yijiupi.himalaya.supplychain.concertsow.domain.bl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.waves.concertsow.dto.ConcertSowTaskRequestDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OutStockOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskSowQueryBL;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.PDASowTaskInfoDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.PackageOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.PDASowTaskInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskReceiveDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskItemReceiveStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.utils.StreamUtils;
import org.springframework.util.StopWatch;

/**
 * 多人协作播种
 */
@Service
@SuppressWarnings("NonAsciiCharacters")
public class ConcertSowTaskBL {

    @Autowired
    private SowTaskMapper sowTaskMapper;

    @Autowired
    private SowTaskItemMapper sowTaskItemMapper;

    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;

    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;

    @Autowired
    private BatchTaskSowQueryBL batchTaskSowQueryBL;

    @Resource
    private OutStockOrderBL outStockOrderBL;

    @Reference
    private IProductSkuService productSkuService;

    @Autowired
    private PackageOrderItemMapper packageOrderItemMapper;

    private static final Logger LOGGER = LoggerFactory.getLogger(ConcertSowTaskBL.class);
    @Autowired
    private GlobalCache globalCache;

    /**
     * 领取播种任务明细
     */
    @Transactional(rollbackFor = Throwable.class)
    public void getSowTask(ConcertSowTaskRequestDTO request) {
        LOGGER.info("领取播种任务，入参{}", JSON.toJSONString(request));
        List<SowTaskItemPO> sowTaskItemPOS = sowTaskItemMapper.findSowTaskNoByItemId(request);
        if (CollectionUtils.isEmpty(sowTaskItemPOS)) {
            throw new BusinessValidateException("暂无可领取任务");
        }
        if (sowTaskItemPOS.stream().allMatch(s -> s.getState().compareTo(SowTaskStateEnum.待播种.getType()) != 0)) {
            throw new BusinessValidateException(
                "该产品已经被" + sowTaskItemPOS.get(0).getSortingUserName() + "领取分拣，请移交给对方进行分拣");
        }
        List<SowTaskItemPO> sowTaskItemPOList =
            sowTaskItemMapper.findByItemIds(request.getCityId(), request.getTaskIds());
        List<Long> sowTaskItemIds =
            sowTaskItemPOList.stream().filter(m -> m.getState() != SowTaskStateEnum.已播种.getType())
                .map(SowTaskItemPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sowTaskItemIds)) {
            throw new BusinessValidateException("该产品都已播种！");
        }
        // 修改播种任务项状态
        sowTaskItemMapper.getSortingTask(request.getCityId(), sowTaskItemIds, request.getUserId(),
            request.getUserName());

        // 修改播种任务状态
        List<String> collect = sowTaskItemPOS.stream().map(SowTaskItemPO::getSowTaskNo).collect(Collectors.toList());
        for (String no : collect) {
            sowTaskMapper.updateSowTaskState(no, SowTaskStateEnum.播种中.getType(), request.getCityId());
        }
    }

    /**
     * 查询待播种任务信息
     */
    public SowTaskDTO queryWaitSowTask(SowTaskReceiveDTO sowTaskReceiveDTO) {
        LOGGER.info("ConcertSowTaskBL.queryWaitSowTask 查询待播种任务信息，入参={}", JSON.toJSONString(sowTaskReceiveDTO));
        AssertUtils.notNull(sowTaskReceiveDTO, "查询参数不能为空");
        AssertUtils.notNull(sowTaskReceiveDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(sowTaskReceiveDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(sowTaskReceiveDTO.getSowTaskType(), "播种任务类型不能为空");

        SowTaskPO sowTaskPO;
        // 区分待播种查询方式，根据用户id查询到已领取播种任务明细后，该播种剩余未领取播种明细不能领取
        boolean canReceive = false;
        // 根据用户id查询已领取播种任务
        sowTaskPO = sowTaskMapper.queryWaitSowTask(sowTaskReceiveDTO);
        LOGGER.info("ConcertSowTaskBL.queryWaitSowTask 根据用户id查询已领取播种任务，结果={}", JSON.toJSONString(sowTaskPO));
        if (ObjectUtils.isEmpty(sowTaskPO)) {
            // 查询待领取任务
            canReceive = true;
            sowTaskReceiveDTO.setSowerId(null);
            sowTaskPO = sowTaskMapper.queryWaitSowTask(sowTaskReceiveDTO);
            LOGGER.info("ConcertSowTaskBL.queryWaitSowTask 查询待领取任务，结果={}", JSON.toJSONString(sowTaskPO));
        }

        if (ObjectUtils.isEmpty(sowTaskPO)) {
            LOGGER.info("没有可领取的播种任务，请检查播种任务下的拣货任务项是否完成");
            return null;
        }

        SowTaskDTO sowTaskDTO = new SowTaskDTO();
        BeanUtils.copyProperties(sowTaskPO, sowTaskDTO);
        sowTaskDTO.setCanReceive(canReceive);
        // 查询播种任务下sku信息
        List<String> list = batchTaskItemMapper.listBatchTaskSkuId(sowTaskPO.getId(), sowTaskReceiveDTO.getOrgId());
        // 查询sku属于大件还是小件
        if (!CollectionUtils.isEmpty(list)
            && !CollectionUtils.isEmpty(list.stream().filter(Objects::nonNull).collect(Collectors.toList()))) {
            sowTaskDTO.setOrderFeature(String.valueOf(productSkuService.getProductFeatureBySkuId(
                list.stream().filter(Objects::nonNull).map(Long::valueOf).collect(Collectors.toList()),
                sowTaskReceiveDTO.getWarehouseId())));
        }

        return sowTaskDTO;
    }

    /**
     * 根据播种任务和波次任务状态获取波次任务
     */
    public List<PDASowTaskInfoDTO> listBatchTaskBySowTaskNos(SowTaskQueryDTO sowTaskQueryDTO) {
        // LOGGER.info("ConcertSowTaskBL.listBatchTaskBySowTaskNos 根据播种任务和波次任务状态获取波次任务，入参={}",
        // JSON.toJSONString(sowTaskQueryDTO));
        AssertUtils.notNull(sowTaskQueryDTO, "查询参数不能为空");
        Integer orgId = sowTaskQueryDTO.getOrgId();
        AssertUtils.notNull(orgId, "城市id不能为空");
        AssertUtils.notNull(sowTaskQueryDTO.getSowTaskNo(), "播种任务编号不能为空");
        AssertUtils.notNull(sowTaskQueryDTO.getOperatorId(), "操作人id不能为空");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("查询任务信息");
        // 当前用户
        Integer sorterId = sowTaskQueryDTO.getOperatorId();
        List<Integer> taskStateList = sowTaskQueryDTO.getStates() != null
            ? sowTaskQueryDTO.getStates().stream().map(Byte::intValue).collect(Collectors.toList()) : new ArrayList<>();
        List<String> sowTaskNos = Collections.singletonList(sowTaskQueryDTO.getSowTaskNo());
        List<BatchTaskDTO> batchTaskDTOList =
            batchTaskSowQueryBL.findBatchTaskListBySowTaskNos(sowTaskNos, taskStateList, orgId);
        stopWatch.stop();
        if (CollectionUtils.isEmpty(batchTaskDTOList)) {
            return new ArrayList<>();
        }
        List<OutStockOrderItemDTO> outStockOrderItemDTOList =
            batchTaskDTOList.stream().map(BatchTaskDTO::getOutStockOrderItemList)
                .filter(it -> !CollectionUtils.isEmpty(it)).flatMap(Collection::stream).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outStockOrderItemDTOList)) {
            throw new BusinessException("没有关联的出库订单项信息");
        }
        stopWatch.start("查询货位信息");
        Map<String, OutStockOrderLocationDTO> locationMap = queryLocationMap(outStockOrderItemDTOList);
        stopWatch.stop();

        stopWatch.start("查询装箱信息");
        // 根据出库订单项id查询打包数据
        Map<Long, List<PackageOrderItemDTO>> packageMap = getPackageOrderItemMap(orgId, outStockOrderItemDTOList);
        stopWatch.stop();

        // 通过用户、播种任务项状态赋标记领取状态
        for (OutStockOrderItemDTO item : outStockOrderItemDTOList) {
            OutStockOrderLocationDTO locationInfo =
                locationMap.getOrDefault(item.getRefOrderNo(), new OutStockOrderLocationDTO());
            // 这里只需要当前仓库的出库位
            if (item.getLocationId() == null) {
                item.setLocationId(locationInfo.getLocationId());
                item.setLocationName(locationInfo.getLocationName());
            }
            if (item.getToLocationId() == null) {
                item.setToLocationId(locationInfo.getLocationId());
                item.setToLocationName(locationInfo.getLocationName());
            }
            item.setReceiveState(getReceiveState(item, sorterId));
            // 二级仓出库位,仓库名称
            item.setSecondLocationId(locationInfo.getToLocationId());
            item.setSecondLocationName(locationInfo.getToLocationName());
            item.setSecondWarehouseName(locationInfo.getToWarehouseName());
            // 填充打包数据
            if (packageMap != null && !packageMap.isEmpty()) {
                List<PackageOrderItemDTO> packageList = packageMap.get(item.getId());
                if (!CollectionUtils.isEmpty(packageList)) {
                    item.setBoxCodeList(
                        packageList.stream().map(PackageOrderItemDTO::getBoxCode).collect(Collectors.toList()));
                    item.setPackageType(packageList.get(0).getPackageType());
                }
            }
        }
        if (stopWatch.getTotalTimeMillis() > 1500) {
            LOGGER.info("根据播种任务和波次任务状态获取波次任务查询,入参:{} 时间{}", JSON.toJSONString(sowTaskQueryDTO),
                stopWatch.prettyPrint());
        }

        Integer warehouseId = batchTaskDTOList.get(0).getWarehouseId();

        // 开启了按客户拣货，相同地址合并拣货
        if (globalCache.getPickByCustomerFromCache(warehouseId)) {
            PDASowTaskInfoDTOConvertor.pickByCustomerSetValue(batchTaskDTOList);
        }

        return PDASowTaskInfoDTOConvertor.convert(batchTaskDTOList);
    }

    /**
     * 拣货任务通用查询
     */
    public PageList<BatchTaskDTO> listBatchTaskInfo(SowTaskQueryDTO sowTaskQueryDTO) {
        LOGGER.info("通用查询波次播种任务详情:{}", JSON.toJSONString(sowTaskQueryDTO));
        AssertUtils.notNull(sowTaskQueryDTO, "查询参数不能为空");
        AssertUtils.notNull(sowTaskQueryDTO.getOrgId(), "城市id不能为空");
        PageList<BatchTaskDTO> batchTaskDTOPageList = batchOrderTaskBL.listBatchTaskInfo(sowTaskQueryDTO);
        // LOGGER.info("通用查询结果:{}", JSON.toJSONString(batchTaskDTOPageList));
        if (CollectionUtils.isEmpty(batchTaskDTOPageList.getDataList())) {
            return new PageList<>();
        }
        return batchTaskDTOPageList;
    }

    private byte getReceiveState(OutStockOrderItemDTO item, Integer sorterId) {
        byte receiveState = SowTaskItemReceiveStateEnum.不可领.getType();
        if (Objects.equals(SowTaskStateEnum.待播种.getType(), item.getSowTaskItemState())) {
            if (Objects.equals(sorterId, item.getSowTaskItemSorterId()) || item.getSowTaskItemSorterId() == null) {
                receiveState = SowTaskItemReceiveStateEnum.待领.getType();
            } else {
                receiveState = SowTaskItemReceiveStateEnum.不可领.getType();
            }
        } else if (Objects.equals(SowTaskStateEnum.播种中.getType(), item.getSowTaskItemState())) {
            if (Objects.equals(sorterId, item.getSowTaskItemSorterId()) || item.getSowTaskItemSorterId() == null) {
                receiveState = SowTaskItemReceiveStateEnum.已领.getType();
            } else {
                receiveState = SowTaskItemReceiveStateEnum.不可领.getType();
            }
        } else if (Objects.equals(SowTaskStateEnum.已播种.getType(), item.getSowTaskItemState())) {
            receiveState = SowTaskItemReceiveStateEnum.已播种.getType();
        }
        return receiveState;
    }

    /**
     * 获取推荐出库位
     *
     * @param orderItems 订单项
     * @return 推荐出库位信息
     */
    private Map<String, OutStockOrderLocationDTO> queryLocationMap(List<OutStockOrderItemDTO> orderItems) {
        List<OutStockOrderLocationDTO> locationQuery = orderItems.stream()
            .collect(Collectors.groupingBy(OutStockOrderItemDTO::getRefOrderNo)).entrySet().stream().map(it -> {
                OutStockOrderItemDTO item = it.getValue().get(0);
                Long omsOrderId = StreamUtils.isNum(item.getBusinessId()) ? Long.valueOf(item.getBusinessId())
                    : item.getOutstockorder_Id();
                OutStockOrderLocationDTO outStockOrderLocationDTO = new OutStockOrderLocationDTO();
                outStockOrderLocationDTO.setWarehouseId(item.getWarehouseId());
                outStockOrderLocationDTO.setOrderNo(it.getKey());
                outStockOrderLocationDTO.setOmsOrderId(omsOrderId);
                return outStockOrderLocationDTO;
            }).collect(Collectors.toList());
        return outStockOrderBL.findOutStockOrderLocation(locationQuery).stream()
            .collect(Collectors.toMap(OutStockOrderLocationDTO::getOrderNo, Function.identity(), (key1, key2) -> key2));
    }

    /**
     * 根据出库订单项id查询打包数据
     *
     * @param orgId
     * @param outStockOrderItemDTOList
     * @return 根据出库订单项id查询打包数据
     */
    private Map<Long, List<PackageOrderItemDTO>> getPackageOrderItemMap(Integer orgId,
        List<OutStockOrderItemDTO> outStockOrderItemDTOList) {
        if (CollectionUtils.isEmpty(outStockOrderItemDTOList)) {
            return Collections.EMPTY_MAP;
        }

        List<Long> refOrderItemIdList = outStockOrderItemDTOList.stream().filter(p -> p.getId() != null)
            .map(OutStockOrderItemDTO::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refOrderItemIdList)) {
            return Collections.EMPTY_MAP;
        }

        // 通过itemId查询
        List<PackageOrderItemDTO> packageOrderItems =
            packageOrderItemMapper.listPackageItemsByItemIds(refOrderItemIdList, orgId);
        // LOGGER.info("根据出库订单项id查询打包数据 入参:{}, 结果:{}",
        // JSON.toJSONString(refOrderItemIdList),JSON.toJSONString(packageOrderItems));
        if (CollectionUtils.isEmpty(packageOrderItems)) {
            return Collections.EMPTY_MAP;
        }

        return packageOrderItems.stream().filter(item -> item.getRefOrderItemId() != null)
            .collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderItemId));
    }

}
