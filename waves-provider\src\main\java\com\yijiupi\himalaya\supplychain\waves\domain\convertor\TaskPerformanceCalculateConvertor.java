package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.waves.domain.bo.TaskPerformanceCalculateBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.performance.TaskPerformanceCalculateDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
@Component
public class TaskPerformanceCalculateConvertor {

    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private SowTaskMapper sowTaskMapper;

    public List<TaskPerformanceCalculateBO> convert(TaskPerformanceCalculateDTO dto) {
        List<TaskPerformanceCalculateBO> batchTaskBOList = convertBatchTask(dto);
        List<TaskPerformanceCalculateBO> sowTaskBOList = convertSowTask(dto);

        List<TaskPerformanceCalculateBO> totalList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(batchTaskBOList)) {
            totalList.addAll(batchTaskBOList);
        }
        if (!CollectionUtils.isEmpty(sowTaskBOList)) {
            totalList.addAll(sowTaskBOList);
        }

        return totalList;
    }

    public List<TaskPerformanceCalculateBO> convertBatchTask(TaskPerformanceCalculateDTO dto) {
        if (!TaskPerformanceCalculateDTO.TYPE_BATCH_TASK.equals(dto.getType())) {
            return null;
        }
        List<String> taskIds = dto.getTaskIds();
        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findBatchTaskByIds(taskIds);
        if (CollectionUtils.isEmpty(batchTaskPOList)) {
            return null;
        }

        return batchTaskPOList.stream().filter(m -> TaskStateEnum.已完成.getType() == m.getTaskState())
            .map(batchTaskPO -> {
                return TaskPerformanceCalculateBO.getBatchTaskInstance(Collections.singletonList(batchTaskPO.getId()),
                    batchTaskPO.getSorterId(), batchTaskPO.getWarehouseId(), Integer.valueOf(batchTaskPO.getOrgId()),
                    updateBatchTaskPO -> batchTaskMapper.updateByPrimaryKeySelective(updateBatchTaskPO));
            }).collect(Collectors.toList());
    }

    private List<TaskPerformanceCalculateBO> convertSowTask(TaskPerformanceCalculateDTO dto) {
        if (!TaskPerformanceCalculateDTO.TYPE_SOW_TASK.equals(dto.getType())) {
            return null;
        }
        List<Long> taskIds = dto.getTaskIds().stream().map(Long::valueOf).collect(Collectors.toList());
        List<SowTaskPO> sowTaskPOS = sowTaskMapper.findSowTaskByIds(taskIds);
        if (CollectionUtils.isEmpty(sowTaskPOS)) {
            return Collections.emptyList();
        }

        sowTaskPOS = sowTaskPOS.stream().filter(m -> SowTaskStateEnum.已播种.getType() == m.getState())
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(sowTaskPOS)) {
            return Collections.emptyList();
        }

        return sowTaskPOS.stream().map(sowTaskPO -> {
            return TaskPerformanceCalculateBO.getSowTaskInstance(
                Collections.singletonList(sowTaskPO.getId().toString()), sowTaskPO.getOperatorId(),
                sowTaskPO.getWarehouseId(), sowTaskPO.getOrgId(),
                updateSowTaskPO -> sowTaskMapper.updateByPrimaryKeySelective(updateSowTaskPO));
        }).collect(Collectors.toList());
    }

}
