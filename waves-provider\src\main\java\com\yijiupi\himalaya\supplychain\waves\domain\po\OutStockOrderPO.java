package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 出库单(OUTSTOCKORDER)
 * Created by 余明 on 2018-03-15.
 */
public class OutStockOrderPO implements java.io.Serializable {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -2598267362430709537L;

    /**
     * id
     */
    private Long id;

    /**
     * 订单项明细
     */
    private List<OutStockOrderItemPO> items;

    /**
     * 订单编号
     */
    private String reforderno;

    /**
     * 城市ID，分库用（第三方订单使用一个新的自定义CityId）
     */
    private Integer orgId;

    /**
     * 波次编号
     */
    private String batchno;

    /**
     * 波次Id
     */
    private String batchId;

    /**
     * 波次状态： 待调度(0),待拣货(1),拣货中(2),已拣货(3),已出库(4),已取消(5),已作废(6)
     */
    private Integer state;

    /**
     * 企业编码（SAAS用）：易酒批(YJP)
     */
    private String companycode;

    /**
     * 出库单类型：普通订单（0），招商订单和分销商订单（1），经销商直配订单（2），大货批发订单（3），大商转配送（10），轻加盟订单（11），经销商订单酒批配送（12）
     */
    private Integer ordertype;

    /**
     * 应付金额
     */
    private BigDecimal orderamount;

    /**
     * 商品种类数
     */
    private Integer skucount;

    /**
     * 大件总数
     */
    private BigDecimal packageamount;

    /**
     * 小件总数
     */
    private BigDecimal unitamount;

    /**
     * 发货仓库id
     */
    private Integer warehouseId;

    /**
     * 收货人姓名
     */
    private String username;

    /**
     * 店铺名称
     */
    private String shopname;

    /**
     * 收货人手机号
     */
    private String mobileno;

    /**
     * 收货地址
     */
    private String detailaddress;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String county;

    /**
     * 街道
     */
    private String street;

    /**
     * 托管方ID
     */
    private Long parterId;

    /**
     * 托管方名称
     */
    private String parterName;

    /**
     * 下单时间
     */
    private Date ordercreatetime;

    /**
     * 开始拣货时间
     */
    private Date picktime;

    /**
     * 出库时间
     */
    private Date outstocktime;

    /**
     * 出库操作人
     */
    private String outstockuser;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createuser;

    /**
     * 创建时间
     */
    private Date createtime;

    /**
     * 更新人
     */
    private String lastupdateuser;

    /**
     * 更新时间
     */
    private Date lastupdatetime;

    /** 区域id */
    private Long areaId;

    /** 区域名称 */
    private String areaName;

    /** 线路id */
    private Long routeId;

    /** 线路名称 */
    private String routeName;

    /** 线路排序号 */
    private Integer routeSequence;

    /**
     * 配送状态 -1未标记0全部配送 1部分发货2部分配送3延迟配送4配送失败5延迟配送已入库
     */
    private Integer deliveryMarkState;

    /**
     * 下单城市Id
     */
    private Integer fromCityId;

    /**
     * 订单序号
     */
    private Integer orderSequence;

    /**
     * 是否需要生内配单,0:否，1:是
     */
    private Byte createAllocation;

    /**
     * 用户id
     */
    private Integer addressId;

    /**
     * 订单属性（1: 大件订单 2: 小件订单）
     */
    private Byte packageAttribute;

    private Date expectedOutStockTime;

    /**
     * 配送方式
     */
    private Byte deliveryMode;

    /**
     * 关联单据id
     */
    private String businessId;

    /**
     * 关林单据编号
     */
    private String businessNo;
    /**
     * 货主id
     */
    private Long ownerId;
    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 关联单编号
     */
    private String associatedBusinessNo;

    /**
     * 关联单id
     */
    private String associatedBusinessId;

    /**
     * 业务类型
     */
    private Byte allotType;

    /**
     * 订单来源
     */
    private Byte orderSource;

    /**
     * 目的仓库id
     */
    private Integer toWarehouseId;

    /**
     * 目的仓库
     */
    private String toWarehouseName;

    /**
     * 是否跨库：0:否 1:是
     */
    private Byte crossWareHouse;

    /**
     * 播种货位id
     */
    private Long sowLocationId;

    /**
     * 播种货位名称
     */
    private String sowLocationName;

    /**
     * 货位顺序
     */
    private Integer LocationSequence;

    /**
     * 出库批次编号
     */
    private String boundNo;

    /**
     * 货位名称list
     */
    private List<String> locationNameList;

    /**
     * 推荐出库位的查询 key (用于匹配推荐出库位)<br/>
     * <b>非标准模型, 使用前得查看是否手动 set 了值</b>
     */
    private String outLocationQueryKey;
    /**
     * 采购商ID
     */
    private String purchaserId;

    /**
     * 大原始件总数
     */
    private BigDecimal originalPackageAmount;

    /**
     * 原始小件总数
     */
    private BigDecimal originalUnitAmount;

    private Integer pushState;

    /**
     * 订单来源 {@link com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.SourceType}
     */
    private Byte orderSourceType;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;


    public Integer getPushState() {
        return pushState;
    }

    public void setPushState(Integer pushState) {
        this.pushState = pushState;
    }

    public String getPurchaserId() {
        return purchaserId;
    }

    public void setPurchaserId(String purchaserId) {
        this.purchaserId = purchaserId;
    }

    private String sowTaskId;

    private String pickUpUserName;

    private Byte outBoundType;

    /**
     * 优先级
     */
    private Integer priority;

    public String getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(String sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getOutLocationQueryKey() {
        return outLocationQueryKey;
    }

    public void setOutLocationQueryKey(String outLocationQueryKey) {
        this.outLocationQueryKey = outLocationQueryKey;
    }

    public Byte getCrossWareHouse() {
        return crossWareHouse;
    }

    public void setCrossWareHouse(Byte crossWareHouse) {
        this.crossWareHouse = crossWareHouse;
    }

    public Integer getToWarehouseId() {
        return toWarehouseId;
    }

    public void setToWarehouseId(Integer toWarehouseId) {
        this.toWarehouseId = toWarehouseId;
    }

    public String getToWarehouseName() {
        return toWarehouseName;
    }

    public void setToWarehouseName(String toWarehouseName) {
        this.toWarehouseName = toWarehouseName;
    }

    public Byte getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(Byte orderSource) {
        this.orderSource = orderSource;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    public Date getExpectedOutStockTime() {
        return expectedOutStockTime;
    }

    public void setExpectedOutStockTime(Date expectedOutStockTime) {
        this.expectedOutStockTime = expectedOutStockTime;
    }

    public Byte getPackageAttribute() {
        return packageAttribute;
    }

    public void setPackageAttribute(Byte packageAttribute) {
        this.packageAttribute = packageAttribute;
    }

    public Byte getCreateAllocation() {
        return createAllocation;
    }

    public void setCreateAllocation(Byte createAllocation) {
        this.createAllocation = createAllocation;
    }

    public Integer getOrderSequence() {
        return orderSequence;
    }

    public void setOrderSequence(Integer orderSequence) {
        this.orderSequence = orderSequence;
    }

    public Integer getFromCityId() {
        return fromCityId;
    }

    public void setFromCityId(Integer fromCityId) {
        this.fromCityId = fromCityId;
    }

    public Integer getDeliveryMarkState() {
        return deliveryMarkState;
    }

    public void setDeliveryMarkState(Integer deliveryMarkState) {
        this.deliveryMarkState = deliveryMarkState;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Long getRouteId() {
        return routeId;
    }

    public void setRouteId(Long routeId) {
        this.routeId = routeId;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public Integer getRouteSequence() {
        return routeSequence;
    }

    public void setRouteSequence(Integer routeSequence) {
        this.routeSequence = routeSequence;
    }

    /**
     * 获取id
     *
     * @return id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置id
     *
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取订单编号
     *
     * @return 订单编号
     */
    public String getReforderno() {
        return this.reforderno;
    }

    /**
     * 设置订单编号
     *
     * @param reforderno 订单编号
     */
    public void setReforderno(String reforderno) {
        this.reforderno = reforderno;
    }

    /**
     * 获取城市ID，分库用（第三方订单使用一个新的自定义CityId）
     *
     * @return 城市ID
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置城市ID，分库用（第三方订单使用一个新的自定义CityId）
     *
     * @param orgId 城市ID
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取波次编号
     *
     * @return 波次编号
     */
    public String getBatchno() {
        return this.batchno;
    }

    /**
     * 设置波次编号
     *
     * @param batchno 波次编号
     */
    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    /**
     * 获取波次Id
     *
     * @return 波次Id
     */
    public String getBatchId() {
        return this.batchId;
    }

    /**
     * 设置波次Id
     *
     * @param batchId 波次Id
     */
    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    /**
     * 获取波次状态： 待调度(0),待拣货(1),拣货中(2),已拣货(3),已出库(4),已取消(5),已作废(6)
     *
     * @return 波次状态： 待调度(0)
     */
    public Integer getState() {
        return this.state;
    }

    /**
     * 设置波次状态： 待调度(0),待拣货(1),拣货中(2),已拣货(3),已出库(4),已取消(5),已作废(6)
     *
     * @param state 波次状态： 待调度(0)
     */
    public void setState(Integer state) {
        this.state = state;
    }

    /**
     * 获取企业编码（SAAS用）：易酒批(YJP)
     *
     * @return 企业编码（SAAS用）
     */
    public String getCompanycode() {
        return this.companycode;
    }

    /**
     * 设置企业编码（SAAS用）：易酒批(YJP)
     *
     * @param companycode 企业编码（SAAS用）
     */
    public void setCompanycode(String companycode) {
        this.companycode = companycode;
    }

    /**
     * 获取出库单类型：普通订单（0），招商订单和分销商订单（1），经销商直配订单（2），大货批发订单（3），大商转配送（10），轻加盟订单（11），经销商订单酒批配送（12）
     *
     * @return 出库单类型：普通订单（0）
     */
    public Integer getOrdertype() {
        return this.ordertype;
    }

    /**
     * 设置出库单类型：普通订单（0），招商订单和分销商订单（1），经销商直配订单（2），大货批发订单（3），大商转配送（10），轻加盟订单（11），经销商订单酒批配送（12）
     *
     * @param ordertype 出库单类型：普通订单（0）
     */
    public void setOrdertype(Integer ordertype) {
        this.ordertype = ordertype;
    }

    /**
     * 获取应付金额
     *
     * @return 应付金额
     */
    public BigDecimal getOrderamount() {
        return this.orderamount;
    }

    /**
     * 设置应付金额
     *
     * @param orderamount 应付金额
     */
    public void setOrderamount(BigDecimal orderamount) {
        this.orderamount = orderamount;
    }

    /**
     * 获取商品种类数
     *
     * @return 商品种类数
     */
    public Integer getSkucount() {
        return this.skucount;
    }

    /**
     * 设置商品种类数
     *
     * @param skucount 商品种类数
     */
    public void setSkucount(Integer skucount) {
        this.skucount = skucount;
    }

    /**
     * 获取大件总数
     *
     * @return 大件总数
     */
    public BigDecimal getPackageamount() {
        return this.packageamount;
    }

    /**
     * 设置大件总数
     *
     * @param packageamount 大件总数
     */
    public void setPackageamount(BigDecimal packageamount) {
        this.packageamount = packageamount;
    }

    /**
     * 获取小件总数
     *
     * @return 小件总数
     */
    public BigDecimal getUnitamount() {
        return this.unitamount;
    }

    /**
     * 设置小件总数
     *
     * @param unitamount 小件总数
     */
    public void setUnitamount(BigDecimal unitamount) {
        this.unitamount = unitamount;
    }

    /**
     * 获取发货仓库id
     *
     * @return 发货仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置发货仓库id
     *
     * @param warehouseId 发货仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取收货人姓名
     *
     * @return 收货人姓名
     */
    public String getUsername() {
        return this.username;
    }

    /**
     * 设置收货人姓名
     *
     * @param username 收货人姓名
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * 获取店铺名称
     *
     * @return 店铺名称
     */
    public String getShopname() {
        return this.shopname;
    }

    /**
     * 设置店铺名称
     *
     * @param shopname 店铺名称
     */
    public void setShopname(String shopname) {
        this.shopname = shopname;
    }

    /**
     * 获取收货人手机号
     *
     * @return 收货人手机号
     */
    public String getMobileno() {
        return this.mobileno;
    }

    /**
     * 设置收货人手机号
     *
     * @param mobileno 收货人手机号
     */
    public void setMobileno(String mobileno) {
        this.mobileno = mobileno;
    }

    /**
     * 获取收货地址
     *
     * @return 收货地址
     */
    public String getDetailaddress() {
        return this.detailaddress;
    }

    /**
     * 设置收货地址
     *
     * @param detailaddress 收货地址
     */
    public void setDetailaddress(String detailaddress) {
        this.detailaddress = detailaddress;
    }

    /**
     * 获取省
     *
     * @return 省
     */
    public String getProvince() {
        return this.province;
    }

    /**
     * 设置省
     *
     * @param province 省
     */
    public void setProvince(String province) {
        this.province = province;
    }

    /**
     * 获取市
     *
     * @return 市
     */
    public String getCity() {
        return this.city;
    }

    /**
     * 设置市
     *
     * @param city 市
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * 获取区
     *
     * @return 区
     */
    public String getCounty() {
        return this.county;
    }

    /**
     * 设置区
     *
     * @param county 区
     */
    public void setCounty(String county) {
        this.county = county;
    }

    /**
     * 获取街道
     *
     * @return 街道
     */
    public String getStreet() {
        return this.street;
    }

    /**
     * 设置街道
     *
     * @param street 街道
     */
    public void setStreet(String street) {
        this.street = street;
    }

    /**
     * 获取托管方ID
     *
     * @return 托管方ID
     */
    public Long getParterId() {
        return this.parterId;
    }

    /**
     * 设置托管方ID
     *
     * @param parterId 托管方ID
     */
    public void setParterId(Long parterId) {
        this.parterId = parterId;
    }

    /**
     * 获取托管方名称
     *
     * @return 托管方名称
     */
    public String getParterName() {
        return this.parterName;
    }

    /**
     * 设置托管方名称
     *
     * @param parterName 托管方名称
     */
    public void setParterName(String parterName) {
        this.parterName = parterName;
    }

    /**
     * 获取下单时间
     *
     * @return 下单时间
     */
    public Date getOrdercreatetime() {
        return this.ordercreatetime;
    }

    /**
     * 设置下单时间
     *
     * @param ordercreatetime 下单时间
     */
    public void setOrdercreatetime(Date ordercreatetime) {
        this.ordercreatetime = ordercreatetime;
    }

    /**
     * 获取开始拣货时间
     *
     * @return 开始拣货时间
     */
    public Date getPicktime() {
        return this.picktime;
    }

    /**
     * 设置开始拣货时间
     *
     * @param picktime 开始拣货时间
     */
    public void setPicktime(Date picktime) {
        this.picktime = picktime;
    }

    /**
     * 获取出库时间
     *
     * @return 出库时间
     */
    public Date getOutstocktime() {
        return this.outstocktime;
    }

    /**
     * 设置出库时间
     *
     * @param outstocktime 出库时间
     */
    public void setOutstocktime(Date outstocktime) {
        this.outstocktime = outstocktime;
    }

    /**
     * 获取出库操作人
     *
     * @return 出库操作人
     */
    public String getOutstockuser() {
        return this.outstockuser;
    }

    /**
     * 设置出库操作人
     *
     * @param outstockuser 出库操作人
     */
    public void setOutstockuser(String outstockuser) {
        this.outstockuser = outstockuser;
    }

    /**
     * 获取备注
     *
     * @return 备注
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * 设置备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取创建人
     *
     * @return 创建人
     */
    public String getCreateuser() {
        return this.createuser;
    }

    /**
     * 设置创建人
     *
     * @param createuser 创建人
     */
    public void setCreateuser(String createuser) {
        this.createuser = createuser;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Date getCreatetime() {
        return this.createtime;
    }

    /**
     * 设置创建时间
     *
     * @param createtime 创建时间
     */
    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    /**
     * 获取更新人
     *
     * @return 更新人
     */
    public String getLastupdateuser() {
        return this.lastupdateuser;
    }

    /**
     * 设置更新人
     *
     * @param lastupdateuser 更新人
     */
    public void setLastupdateuser(String lastupdateuser) {
        this.lastupdateuser = lastupdateuser;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public Date getLastupdatetime() {
        return this.lastupdatetime;
    }

    /**
     * 设置更新时间
     *
     * @param lastupdatetime 更新时间
     */
    public void setLastupdatetime(Date lastupdatetime) {
        this.lastupdatetime = lastupdatetime;
    }

    public List<OutStockOrderItemPO> getItems() {
        return items;
    }

    public void setItems(List<OutStockOrderItemPO> items) {
        this.items = items;
    }

    public Integer getAddressId() {
        return addressId;
    }

    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    public Byte getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(Byte deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    /**
     * @return the 货主id
     */
    public Long getOwnerId() {
        return ownerId;
    }

    /**
     * @param 货主id the ownerId to set
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * @return the 货主名称
     */
    public String getOwnerName() {
        return ownerName;
    }

    /**
     * @param 货主名称 the ownerName to set
     */
    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getAssociatedBusinessNo() {
        return associatedBusinessNo;
    }

    public void setAssociatedBusinessNo(String associatedBusinessNo) {
        this.associatedBusinessNo = associatedBusinessNo;
    }

    public String getAssociatedBusinessId() {
        return associatedBusinessId;
    }

    public void setAssociatedBusinessId(String associatedBusinessId) {
        this.associatedBusinessId = associatedBusinessId;
    }

    public Byte getAllotType() {
        return allotType;
    }

    public void setAllotType(Byte allotType) {
        this.allotType = allotType;
    }

    public Long getSowLocationId() {
        return sowLocationId;
    }

    public void setSowLocationId(Long sowLocationId) {
        this.sowLocationId = sowLocationId;
    }

    public String getSowLocationName() {
        return sowLocationName;
    }

    public void setSowLocationName(String sowLocationName) {
        this.sowLocationName = sowLocationName;
    }

    public Integer getLocationSequence() {
        return LocationSequence;
    }

    public void setLocationSequence(Integer locationSequence) {
        LocationSequence = locationSequence;
    }

    public String getBoundNo() {
        return boundNo;
    }

    public void setBoundNo(String boundNo) {
        this.boundNo = boundNo;
    }

    public List<String> getLocationNameList() {
        return locationNameList;
    }

    public void setLocationNameList(List<String> locationNameList) {
        this.locationNameList = locationNameList;
    }

    public BigDecimal getOriginalPackageAmount() {
        return originalPackageAmount;
    }

    public void setOriginalPackageAmount(BigDecimal originalPackageAmount) {
        this.originalPackageAmount = originalPackageAmount;
    }

    public BigDecimal getOriginalUnitAmount() {
        return originalUnitAmount;
    }

    public void setOriginalUnitAmount(BigDecimal originalUnitAmount) {
        this.originalUnitAmount = originalUnitAmount;
    }

    public String getPickUpUserName() {
        return pickUpUserName;
    }

    public void setPickUpUserName(String pickUpUserName) {
        this.pickUpUserName = pickUpUserName;
    }

    /**
     * 获取
     *
     * @return outBoundType
     */
    public Byte getOutBoundType() {
        return this.outBoundType;
    }

    /**
     * 设置
     *
     * @param outBoundType
     */
    public void setOutBoundType(Byte outBoundType) {
        this.outBoundType = outBoundType;
    }

    public Byte getOrderSourceType() {
        return orderSourceType;
    }

    public void setOrderSourceType(Byte orderSourceType) {
        this.orderSourceType = orderSourceType;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }
}
