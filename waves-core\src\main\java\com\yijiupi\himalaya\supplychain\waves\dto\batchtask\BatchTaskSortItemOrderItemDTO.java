package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/1/3
 */
public class BatchTaskSortItemOrderItemDTO implements Serializable {
    /**
     * 订单项id
     */
    private Long orderItemId;
    /**
     * 出库位id
     */
    private Long toLocationId;
    /**
     * 出库位名称
     */
    private String toLocationName;
    /**
     * 大数量
     */
    private BigDecimal packageCount;
    /**
     * 小数量
     */
    private BigDecimal unitCount;

    /**
     * 获取 出库位id
     *
     * @return toLocationId 出库位id
     */
    public Long getToLocationId() {
        return this.toLocationId;
    }

    /**
     * 设置 出库位id
     *
     * @param toLocationId 出库位id
     */
    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    /**
     * 获取 出库位名称
     *
     * @return toLocationName 出库位名称
     */
    public String getToLocationName() {
        return this.toLocationName;
    }

    /**
     * 设置 出库位名称
     *
     * @param toLocationName 出库位名称
     */
    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    /**
     * 获取 大数量
     *
     * @return packageCount 大数量
     */
    public BigDecimal getPackageCount() {
        return this.packageCount;
    }

    /**
     * 设置 大数量
     *
     * @param packageCount 大数量
     */
    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    /**
     * 获取 小数量
     *
     * @return unitCount 小数量
     */
    public BigDecimal getUnitCount() {
        return this.unitCount;
    }

    /**
     * 设置 小数量
     *
     * @param unitCount 小数量
     */
    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    /**
     * 获取 订单项id
     *
     * @return orderItemId 订单项id
     */
    public Long getOrderItemId() {
        return this.orderItemId;
    }

    /**
     * 设置 订单项id
     *
     * @param orderItemId 订单项id
     */
    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }
}
