package com.yijiupi.himalaya.supplychain.waves.batch;

import com.yijiupi.himalaya.supplychain.waves.dto.batch.*;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchAllotPriorityDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByProductAndOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByRefOrderNoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.UpdateOrderItemCountDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.OrderPrintInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.ScanReviewToSetLocationPalletInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.UpdateLocationDTO;

/**
 * Created by 余明 on 2018-03-15.
 */
public interface IBatchOrderProcessService {

    /**
     * @param wareStrategyId 波次策略id
     * @param orderStartTime 订单开始时间
     * @param orderEndTime 订单结束时间
     * @return
     * @Description: 根据波次策略Id生成波次及波次任务
     * <AUTHOR>
     * @date 2018/4/2 15:07
     */
    @Deprecated
    void processBatchOrderByStrateId(Integer wareStrategyId, String orderStartTime, String orderEndTime);

    /**
     * @param batchCreateDTO
     * @return
     * @Description: 手动新增波次
     * <AUTHOR>
     * @date 2018/4/2 15:06
     */
    void createBatch(BatchCreateDTO batchCreateDTO);

    /**
     * @param batchCreateByRefOrderNoDTO
     * @return
     * @Description: 通过订单号 手动新增波次
     * <AUTHOR>
     * @date 2018/4/2 15:06
     */
    void createBatchByRefOrderNo(BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO);

    /**
     * 追加播种任务
     * 
     * @param appendSowTaskDTO
     */
    void appendSowTask(AppendSowTaskDTO appendSowTaskDTO);

    /**
     * 按产品+订单 手动新增波次（知花知果）
     */
    void createBatchByProductAndOrder(BatchCreateByProductAndOrderDTO batchCreateByProductAndOrderDTO);

    /**
     * 获取波次分配的优先级及缺货产品
     */
    BatchAllotPriorityDTO getBatchAllotPriority(BatchCreateByProductAndOrderDTO batchCreateByProductAndOrderDTO);

    /**
     * 订单打印数据保存
     */
    void saveOrderPrintInfo(OrderPrintInfoDTO orderPrintInfoDTO);

    void fixCount(UpdateOrderItemCountDTO dto);

    void updateLocation(UpdateLocationDTO updateLocationDTO);

    /**
     * 扫码复核后设置出库位和托盘位
     *
     * @param dto
     */
    void scanReviewToSetLocationPalletInfo(ScanReviewToSetLocationPalletInfoDTO dto);

    /**
     * 按客户创建波次检查
     */
    List<OutStockOrderDTO> checkCreateBatchByCustomer(BatchCreateDTO batchCreateDTO);
}
