package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 播种任务
 */
public class SowTaskItemPO {

    /**
     * 播种任务明细id
     */
    private Long id;

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 播种明细状态 待播种(0),播种中(1),已播种(2)
     */
    private Byte state;

    /**
     * skuId
     */
    private Long productSkuId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 规格id
     */
    private Long productSpecificationId;

    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 二级货主id
     */
    private Long secOwnerId;

    /**
     * 规格系数
     */
    private BigDecimal specQuantity;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 大件数量
     */
    private BigDecimal packageCount;

    /**
     * 大件名称
     */
    private String packageName;

    /**
     * 小件数量
     */
    private BigDecimal unitCount;

    /**
     * 小件名称
     */
    private String unitName;

    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 已播种小单位总数量
     */
    private BigDecimal overUnitTotalCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    private Date lastUpdateTime;

    /**
     * 最后更新人
     */
    private String lastUpdateUser;

    /**
     * 播种开始时间
     */
    private Date startTime;

    /**
     * 播种完成时间
     */
    private Date completeTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 订单项id
     */
    private Long orderItemId;

    /**
     * 缺货小单位总数量
     */
    private BigDecimal lackUnitTotalCount;

    /**
     * 波次表id
     */
    private String batchId;
    /**
     * 集货位ID
     */
    private Long locationId;
    /**
     * 集货位名称
     */
    private String locationName;
    /**
     * 分拣人名称
     */
    private String sortingUserName;

    /**
     * 分拣人id
     */
    private Integer sorterId;
    /**
     * 分拣人名称
     */
    private String sorterName;

    public String getSortingUserName() {
        return sortingUserName;
    }

    public void setSortingUserName(String sortingUserName) {
        this.sortingUserName = sortingUserName;
    }

    /**
     * 销售规格
     */
    private BigDecimal saleSpecQuantity;
    /**
     * 出库单ID
     */
    private Long outStockOrderId;
    /**
     * 出库单ID
     */
    private Long outStockOrderItemId;

    public Long getOutStockOrderId() {
        return outStockOrderId;
    }

    public void setOutStockOrderId(Long outStockOrderId) {
        this.outStockOrderId = outStockOrderId;
    }

    public Long getOutStockOrderItemId() {
        return outStockOrderItemId;
    }

    public void setOutStockOrderItemId(Long outStockOrderItemId) {
        this.outStockOrderItemId = outStockOrderItemId;
    }

    public BigDecimal getSaleSpecQuantity() {
        return saleSpecQuantity;
    }

    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public BigDecimal getOverUnitTotalCount() {
        return overUnitTotalCount;
    }

    public void setOverUnitTotalCount(BigDecimal overUnitTotalCount) {
        this.overUnitTotalCount = overUnitTotalCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public BigDecimal getLackUnitTotalCount() {
        return lackUnitTotalCount;
    }

    public void setLackUnitTotalCount(BigDecimal lackUnitTotalCount) {
        this.lackUnitTotalCount = lackUnitTotalCount;
    }


    /**
     * 获取 分拣人id
     *
     * @return sorterId 分拣人id
     */
    public Integer getSorterId() {
        return this.sorterId;
    }

    /**
     * 设置 分拣人id
     *
     * @param sorterId 分拣人id
     */
    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    /**
     * 获取 分拣人名称
     *
     * @return sorterName 分拣人名称
     */
    public String getSorterName() {
        return this.sorterName;
    }

    /**
     * 设置 分拣人名称
     *
     * @param sorterName 分拣人名称
     */
    public void setSorterName(String sorterName) {
        this.sorterName = sorterName;
    }
}