package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 订单属性
 *
 * <AUTHOR>
 * @date 2019-09-12 10:42
 */
public class OrderPickTypeConstant {

    /**
     * 拣货方式:默认
     */
    public static final Byte PICKTYPE_DEFAULT = 0;
    /**
     * 拣货方式:大件
     */
    public static final Byte PICKTYPE_PACKAGE = 1;
    /**
     * 拣货方式:小件
     */
    public static final Byte PICKTYPE_BOTTLE = 2;
    /**
     * 拣货方式:快递直发
     */
    public static final Byte PICKTYPE_LOGIC = 3;
    /**
     * 拣货方式:统采优先
     */
    public static final Byte PICKTYPE_CENTRAL_PURCHASE = 4;

}
