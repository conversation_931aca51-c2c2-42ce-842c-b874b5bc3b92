package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.pickup;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskFinishHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishNeedOpBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.PickUpDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchSowTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/12
 */
@Service
public class BatchTaskItemFinishPickUpNormalDecoratorBL extends BatchTaskItemFinishPickUpBaseDecoratorBL {

    @Override
    boolean support(BatchTaskFinishHelperBO batchTaskFinishHelperBO) {
        Byte containerFlag = batchTaskFinishHelperBO.getBatchTaskItemFinishBO().getContainerFlag();
        BatchPO batchPO = batchTaskFinishHelperBO.getBatchPO();
        if (Objects.equals(containerFlag, ConditionStateEnum.是.getType())) {
            return Boolean.FALSE;
        }
        if (Objects.equals(batchPO.getSowType(), BatchSowTypeEnum.二次分拣.getType())) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    @Override
    protected void doCompleteBatchTaskItemPickUp(BatchTaskItemFinishNeedOpBO bo,
        BatchTaskFinishHelperBO batchTaskFinishHelperBO, List<BatchTaskItemDTO> needCompleteBatchTaskItemList) {
        BatchTaskItemFinishBO batchTaskItemFinishBO = batchTaskFinishHelperBO.getBatchTaskItemFinishBO();
        Integer warehouseId = batchTaskFinishHelperBO.getBatchTaskPO().getWarehouseId();
        Long locationId = batchTaskItemFinishBO.getLocationId();
        Long sowLocationId = batchTaskFinishHelperBO.getSowLocationId();
        Map<String, BatchTaskItemCompleteDTO> map = batchTaskFinishHelperBO.getBatchTaskItemCompleteDTOMap();
        Map<Long, BigDecimal> taskInfoDetailOverSortCountMap =
            batchTaskFinishHelperBO.getTaskInfoDetailOverSortCountMap();
        List<OrderItemTaskInfoPO> relatedOrderItemTaskInfoList =
            batchTaskFinishHelperBO.getUpdateBatchTaskItemRelateOrderItemTaskInfoPOList();

        List<PickUpDTO> totalPickUpDTO = new ArrayList<>();
        for (BatchTaskItemDTO batchTaskItemDTO : needCompleteBatchTaskItemList) {
            BatchTaskItemCompleteDTO completeDTO = map.get(batchTaskItemDTO.getId());
            Long fromLocationId = BatchTaskFinishHelperBO.getFromLocationId(completeDTO, batchTaskItemDTO);
            List<OrderItemTaskInfoPO> tmpOrderItemTaskInfoList = relatedOrderItemTaskInfoList.stream()
                .filter(p -> p.getBatchTaskItemId().equals(batchTaskItemDTO.getId())).collect(Collectors.toList());

            BigDecimal overSortCount = BatchTaskFinishHelperBO.getOverSortUnitTotalCount(completeDTO, batchTaskItemDTO);
            List<PickUpDTO> pickUpDTO = getPickUpDTO(warehouseId, locationId, batchTaskItemDTO, overSortCount,
                fromLocationId, completeDTO, sowLocationId, tmpOrderItemTaskInfoList, taskInfoDetailOverSortCountMap);
            if (CollectionUtils.isNotEmpty(pickUpDTO)) {
                totalPickUpDTO.addAll(pickUpDTO);
            }
        }
        LOGGER.info("relatedOrderItemTaskInfoList数据为：{}", JSON.toJSONString(relatedOrderItemTaskInfoList));
        LOGGER.info("needCompleteBatchTaskItemList数据为：{}", JSON.toJSONString(needCompleteBatchTaskItemList));
        LOGGER.info("生成移库单后数据为：{}", JSON.toJSONString(totalPickUpDTO));
        bo.intPickUpDTOList(totalPickUpDTO);
    }

    /**
     * 获取拣货容器位移库入参 <br />
     * 直接抄de，以后优化
     * 
     * @return
     */
    private List<PickUpDTO> getPickUpDTO(Integer warehouseId, Long locationId, BatchTaskItemDTO batchTaskItemDTO,
        BigDecimal overSortCount, Long fromLocationId, BatchTaskItemCompleteDTO updateDTO, Long sowLocationId,
        List<OrderItemTaskInfoPO> nowBatchTaskItemInfoList, Map<Long, BigDecimal> taskInfoDetailOverSortCountMap) {
        List<PickUpDTO> nowPickUpDTOList = new ArrayList<>();
        // 根据关联移库
        if (CollectionUtils.isNotEmpty(nowBatchTaskItemInfoList)) {
            nowBatchTaskItemInfoList.forEach(info -> {
                // 关联明细
                if (CollectionUtils.isNotEmpty(info.getDetailList())) {
                    info.getDetailList().forEach(detail -> {
                        PickUpDTO pickUpDTO = new PickUpDTO();
                        BigDecimal pickCount = detail.getUnitTotalCount();
                        // 重复拣货时，移库数量 = 本次拣货数量 - 上次拣货数量
                        if (TaskStateEnum.已完成.getType() == batchTaskItemDTO.getTaskState()) {
                            // 上次拣货数量
                            BigDecimal prevPickCount = BigDecimal.ZERO;
                            if (taskInfoDetailOverSortCountMap != null
                                && taskInfoDetailOverSortCountMap.get(detail.getId()) != null) {
                                prevPickCount = taskInfoDetailOverSortCountMap.get(detail.getId());
                            }
                            pickCount = pickCount.subtract(prevPickCount);
                        }
                        if (pickCount.compareTo(BigDecimal.ZERO) == 0) {
                            LOGGER.info("拣货数量为0，不需要移库");
                            return;
                        }
                        pickUpDTO.setFromLocationId(fromLocationId);
                        PickUpDTOConvertor.setPickupToLocation(locationId, sowLocationId, pickUpDTO);
                        pickUpDTO.setProductSkuId(batchTaskItemDTO.getSkuId());
                        pickUpDTO.setWarehouseId(warehouseId);
                        pickUpDTO.setFromChannel(batchTaskItemDTO.getChannel().intValue());
                        pickUpDTO.setFromSource(batchTaskItemDTO.getSource().intValue());
                        pickUpDTO.setToChannel(batchTaskItemDTO.getChannel().intValue());
                        pickUpDTO.setToSource(batchTaskItemDTO.getSource().intValue());
                        pickUpDTO.setCount(pickCount);
                        // 生产日期和批次时间
                        pickUpDTO.setProductionDate(updateDTO != null ? updateDTO.getProductionDate() : null);
                        pickUpDTO.setBatchTime(updateDTO != null ? updateDTO.getBatchTime() : null);
                        // 自动分配库存
                        pickUpDTO.setAutoAllotFlag(true);
                        pickUpDTO.setSecOwnerId(detail.getSecOwnerId());
                        pickUpDTO.setBusinessId(info.getId().toString());
                        // 排除负库存
                        pickUpDTO.setExcludeNegativeFlag(true);
                        nowPickUpDTOList.add(pickUpDTO);
                    });
                }
            });
        }

        // 兼容老数据
        if (CollectionUtils.isEmpty(nowPickUpDTOList)) {
            PickUpDTO pickUpDTO = new PickUpDTO();
            BigDecimal pickCount = overSortCount;
            // 重复拣货时，移库数量 = 本次拣货数量 - 上次拣货数量
            if (TaskStateEnum.已完成.getType() == batchTaskItemDTO.getTaskState()) {
                pickCount = overSortCount.subtract(batchTaskItemDTO.getOverSortCount());
            }
            if (pickCount.compareTo(BigDecimal.ZERO) == 0) {
                LOGGER.info("拣货数量为0，不需要移库");
                return nowPickUpDTOList;
            }
            pickUpDTO.setFromLocationId(fromLocationId);
            PickUpDTOConvertor.setPickupToLocation(locationId, sowLocationId, pickUpDTO);
            pickUpDTO.setProductSkuId(batchTaskItemDTO.getSkuId());
            pickUpDTO.setWarehouseId(warehouseId);
            pickUpDTO.setFromChannel(batchTaskItemDTO.getChannel().intValue());
            pickUpDTO.setFromSource(batchTaskItemDTO.getSource().intValue());
            pickUpDTO.setToChannel(batchTaskItemDTO.getChannel().intValue());
            pickUpDTO.setToSource(batchTaskItemDTO.getSource().intValue());
            pickUpDTO.setCount(pickCount);
            // 生产日期和批次时间
            pickUpDTO.setProductionDate(updateDTO != null ? updateDTO.getProductionDate() : null);
            pickUpDTO.setBatchTime(updateDTO != null ? updateDTO.getBatchTime() : null);
            // 自动分配库存
            pickUpDTO.setAutoAllotFlag(true);
            // 排除负库存
            pickUpDTO.setExcludeNegativeFlag(true);
            nowPickUpDTOList.add(pickUpDTO);
        }

        return nowPickUpDTOList;
    }

}
