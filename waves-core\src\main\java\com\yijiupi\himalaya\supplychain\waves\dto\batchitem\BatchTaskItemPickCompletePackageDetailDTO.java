package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title: BatchTaskItemPickCompletePackageDetailDTO
 * @description:
 * @date 2022-11-11 15:24
 */
public class BatchTaskItemPickCompletePackageDetailDTO implements Serializable {
    /**
     * 产品名称
     */
    private String productName;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 大件数量
     */
    private BigDecimal packageCount;
    /**
     * 小件数量
     */
    private BigDecimal unitCount;
    /**
     * 小件总数量
     */
    private BigDecimal unitTotalCount;
    /**
     * 包装规格
     */
    private String specName;
    /**
     * 销售规格
     */
    private String saleSpec;
    /**
     * 包装规格系数
     */
    private BigDecimal specQuantity;
    /**
     * 销售规格系数
     */
    private BigDecimal saleSpecQuantity;
    /**
     * 大单位
     */
    private String packageName;
    /**
     * 小单位
     */
    private String unitName;
    /**
     * 订单号
     */
    private String businessNo;

    /**
     * 获取 产品名称
     *
     * @return productName 产品名称
     */
    public String getProductName() {
        return this.productName;
    }

    /**
     * 设置 产品名称
     *
     * @param productName 产品名称
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 获取 skuId
     *
     * @return skuId skuId
     */
    public Long getSkuId() {
        return this.skuId;
    }

    /**
     * 设置 skuId
     *
     * @param skuId skuId
     */
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    /**
     * 获取 大件数量
     *
     * @return packageCount 大件数量
     */
    public BigDecimal getPackageCount() {
        return this.packageCount;
    }

    /**
     * 设置 大件数量
     *
     * @param packageCount 大件数量
     */
    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    /**
     * 获取 小件数量
     *
     * @return unitCount 小件数量
     */
    public BigDecimal getUnitCount() {
        return this.unitCount;
    }

    /**
     * 设置 小件数量
     *
     * @param unitCount 小件数量
     */
    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    /**
     * 获取 小件总数量
     *
     * @return unitTotalCount 小件总数量
     */
    public BigDecimal getUnitTotalCount() {
        return this.unitTotalCount;
    }

    /**
     * 设置 小件总数量
     *
     * @param unitTotalCount 小件总数量
     */
    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    /**
     * 获取 包装规格
     *
     * @return specName 包装规格
     */
    public String getSpecName() {
        return this.specName;
    }

    /**
     * 设置 包装规格
     *
     * @param specName 包装规格
     */
    public void setSpecName(String specName) {
        this.specName = specName;
    }

    /**
     * 获取 销售规格
     *
     * @return saleSpec 销售规格
     */
    public String getSaleSpec() {
        return this.saleSpec;
    }

    /**
     * 设置 销售规格
     *
     * @param saleSpec 销售规格
     */
    public void setSaleSpec(String saleSpec) {
        this.saleSpec = saleSpec;
    }

    /**
     * 获取 包装规格系数
     *
     * @return specQuantity 包装规格系数
     */
    public BigDecimal getSpecQuantity() {
        return this.specQuantity;
    }

    /**
     * 设置 包装规格系数
     *
     * @param specQuantity 包装规格系数
     */
    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    /**
     * 获取 销售规格系数
     *
     * @return saleSpecQuantity 销售规格系数
     */
    public BigDecimal getSaleSpecQuantity() {
        return this.saleSpecQuantity;
    }

    /**
     * 设置 销售规格系数
     *
     * @param saleSpecQuantity 销售规格系数
     */
    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    /**
     * 获取 大单位
     *
     * @return packageName 大单位
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * 设置 大单位
     *
     * @param packageName 大单位
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取 小单位
     *
     * @return unitName 小单位
     */
    public String getUnitName() {
        return this.unitName;
    }

    /**
     * 设置 小单位
     *
     * @param unitName 小单位
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * 获取 订单号
     *
     * @return businessNo 订单号
     */
    public String getBusinessNo() {
        return this.businessNo;
    }

    /**
     * 设置 订单号
     *
     * @param businessNo 订单号
     */
    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }
}
