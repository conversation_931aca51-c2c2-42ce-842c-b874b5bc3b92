/*
 * @ClassName GatherTaskDTO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-10-30 17:42:36
 */
package com.yijiupi.himalaya.supplychain.waves.search;

import java.io.Serializable;

public class GatherTaskSO implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 5378595921671899520L;
    /**
     * @Fields id 集货任务ID
     */
    private Long id;
    /**
     * @Fields orgId 城市id
     */
    private Integer orgId;
    /**
     * @Fields warehouseId 仓库id
     */
    private Integer warehouseId;
    /**
     * @Fields batchTaskId 波次任务Id
     */
    private String batchTaskId;
    /**
     * @Fields batchtaskNo 波次任务编号
     */
    private String batchtaskNo;
    /**
     * 库位Id
     */
    private Long locationId;
    /**
     * 库位名称
     */
    private String locationName;
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 集货任务产品明细id
     */
    private Long gatherTaskProductId;
    /**
     * @Fields status 状态 0=待完成 1=已完成
     */
    private Byte status;

    /**
     * 获取 集货任务ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 集货任务ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 城市id
     */
    public Integer getOrgId() {
        return orgId;
    }

    /**
     * 设置 城市id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 仓库id
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 波次任务Id
     */
    public String getBatchTaskId() {
        return batchTaskId;
    }

    /**
     * 设置 波次任务Id
     */
    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId == null ? null : batchTaskId.trim();
    }

    /**
     * 获取 波次任务编号
     */
    public String getBatchtaskNo() {
        return batchtaskNo;
    }

    /**
     * 设置 波次任务编号
     */
    public void setBatchtaskNo(String batchtaskNo) {
        this.batchtaskNo = batchtaskNo == null ? null : batchtaskNo.trim();
    }

    /**
     * @return the 库位名称
     */
    public String getLocationName() {
        return locationName;
    }

    /**
     * @param 库位名称 the locationName to set
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * @return the 产品名称
     */
    public String getProductName() {
        return productName;
    }

    /**
     * @param 产品名称 the productName to set
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 获取 状态 0=待完成 1=已完成
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置 状态 0=待完成 1=已完成
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * @return the locationId
     */
    public Long getLocationId() {
        return locationId;
    }

    /**
     * @param locationId the locationId to set
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * @return the gatherTaskProductId
     */
    public Long getGatherTaskProductId() {
        return gatherTaskProductId;
    }

    /**
     * @param gatherTaskProductId the gatherTaskProductId to set
     */
    public void setGatherTaskProductId(Long gatherTaskProductId) {
        this.gatherTaskProductId = gatherTaskProductId;
    }

}