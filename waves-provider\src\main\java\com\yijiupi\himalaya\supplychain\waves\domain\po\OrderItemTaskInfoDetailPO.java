package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 出库单项拣货关联表
 *
 * <AUTHOR>
 * @date 2020-09-04 15:21
 */
public class OrderItemTaskInfoDetailPO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 城市ID
     */
    private Integer orgId;

    /**
     * 出库单项拣货关联ID
     */
    private Long taskInfoId;

    /**
     * 二级货主ID
     */
    private Long secOwnerId;

    /**
     * 货主ID
     */
    private Long ownerId;

    /**
     * 产品规格ID
     */
    private Long productSpecificationId;

    /**
     * 分配小单位总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 最后修改人
     */
    private String lastUpdateUser;
    /**
     * 原始数量
     */
    private BigDecimal originalUnitTotalCount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getTaskInfoId() {
        return taskInfoId;
    }

    public void setTaskInfoId(Long taskInfoId) {
        this.taskInfoId = taskInfoId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    /**
     * 获取 原始数量
     *
     * @return originalUnitTotalCount 原始数量
     */
    public BigDecimal getOriginalUnitTotalCount() {
        return this.originalUnitTotalCount;
    }

    /**
     * 设置 原始数量
     *
     * @param originalUnitTotalCount 原始数量
     */
    public void setOriginalUnitTotalCount(BigDecimal originalUnitTotalCount) {
        this.originalUnitTotalCount = originalUnitTotalCount;
    }
}