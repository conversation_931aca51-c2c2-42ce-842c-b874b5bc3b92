package com.yijiupi.himalaya.supplychain.waves.util;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.yijiupi.himalaya.base.utils.AssertUtils;

/**
 * Created by 余明 on 2018-03-24.
 */
@Component
public class UuidUtil {

    private static BatchNoGenerator batchNoGenerator;

    @Autowired
    private BatchNoGenerator batchNoGenerator_Private;
    private static int sequence = 100000;

    public synchronized static Long getUUidInt() {
        UuidUtil.sequence++;
        // 产生一个时间戳
        long now = System.currentTimeMillis();
        String uuid = now + "" + sequence;
        if (sequence >= 999999) {
            sequence = 100000;
        }
        return Long.parseLong(uuid);
    }

    @PostConstruct
    public void beforeInit() {
        batchNoGenerator = batchNoGenerator_Private;
    }

    public static String generator(Integer warehouseId, String type) {
        AssertUtils.notNull(warehouseId, "仓库Id不能为空！");
        return batchNoGenerator.generator(warehouseId, type);
    }

    public static String generatorId() {
        return batchNoGenerator.generatorId();
    }

    /**
     * 波次名称：年+月+日+时+份+3位流水
     * 
     * @return
     */
    public static String generatorBatchName(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库Id不能为空！");
        return batchNoGenerator.generatorBatchName(warehouseId);
    }

}
