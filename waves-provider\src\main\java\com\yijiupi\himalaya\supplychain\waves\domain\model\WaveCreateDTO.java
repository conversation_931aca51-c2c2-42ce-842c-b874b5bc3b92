package com.yijiupi.himalaya.supplychain.waves.domain.model;

import java.io.Serializable;
import java.util.List;

import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchWorkSettingDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;

/**
 * Created by 余明 on 2018-08-09.
 */
public class WaveCreateDTO implements Serializable {
    /**
     * 城市Id
     */
    private Integer cityId;
    /**
     * 策略名称
     */
    private WavesStrategyBO wavesStrategyDTO;
    /**
     * 自定义波次名称
     */
    private String title;

    /**
     * 拣货通道属性
     */
    private PassageDTO passageDTO;
    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 操作人id
     */
    private Integer operateUserId;

    private List<OutStockOrderPO> orders;

    /**
     * 播种任务Id
     */
    private Long sowId;

    /**
     * 播种任务编号
     */
    private String sowNo;

    /**
     * 追加车次存放货位
     */
    private String locationName;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 内配单标识：内配单波次时传true, 否则传false
     */
    private Boolean allocationFlag = false;

    /**
     * 拣货任务是否需要拣货标识 true: 需要（默认）， false：不需要（不拣货、直接播种的拣货任务）
     */
    private Boolean pickFlag = true;

    /**
     * 指定出库位id
     */
    private Long toLocationId;

    /**
     * 指定出库位名称
     */
    private String toLocationName;

    /**
     * 目的仓库id
     */
    private Integer toWarehouseId;

    /**
     * 目的仓库
     */
    private String toWarehouseName;

    /**
     * 网格仓的作业设置（如果为null, 表示非网格仓）
     */
    private BatchWorkSettingDTO workSetting;

    /**
     * 拣货任务类型 1: 按自提点
     */
    private Byte BatchTaskType;

    /**
     * 自提点id
     */
    private Integer addressId;

    /**
     * 播种类型 1-总单播种 2-二次分拣 3-快速播种
     */
    private Integer sowType;
    /**
     * 是否机器人分拣 0:不开启,1:开启
     */
    private Byte isRobotPick;
    /**
     * 是否是整件拆零拣货 生成的零散拣货任务 ：0、不是，1、是 如果是整件拣货，则生成的拣货任务非机器人拣货任务
     */
    private Byte largePick = BatchTaskItemLargePickPatternConstants.LARGE_PICK_NONE;
    /**
     * 是否是兑奖单拣货任务
     */
    private boolean isAwardOrderTask;

    /**
     * 追加拣货任务排序序号
     */
    private Integer appendSequence;

    /**
     * 前置仓内配单是否可以按产品拣货
     */
    private boolean openFrontWarehouseOpenNPProductPick = Boolean.FALSE;
    /**
     * 拣货任务方式
     * 
     * @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskKindOfPickingConstants
     */
    private Byte kindOfPicking;

    public Integer getSowType() {
        return sowType;
    }

    public void setSowType(Integer sowType) {
        this.sowType = sowType;
    }

    public Integer getAddressId() {
        return addressId;
    }

    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    public Byte getBatchTaskType() {
        return BatchTaskType;
    }

    public void setBatchTaskType(Byte batchTaskType) {
        BatchTaskType = batchTaskType;
    }

    public BatchWorkSettingDTO getWorkSetting() {
        return workSetting;
    }

    public void setWorkSetting(BatchWorkSettingDTO workSetting) {
        this.workSetting = workSetting;
    }

    public Integer getToWarehouseId() {
        return toWarehouseId;
    }

    public void setToWarehouseId(Integer toWarehouseId) {
        this.toWarehouseId = toWarehouseId;
    }

    public String getToWarehouseName() {
        return toWarehouseName;
    }

    public void setToWarehouseName(String toWarehouseName) {
        this.toWarehouseName = toWarehouseName;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public Long getSowId() {
        return sowId;
    }

    public void setSowId(Long sowId) {
        this.sowId = sowId;
    }

    public String getSowNo() {
        return sowNo;
    }

    public void setSowNo(String sowNo) {
        this.sowNo = sowNo;
    }

    public Boolean getPickFlag() {
        return pickFlag;
    }

    public void setPickFlag(Boolean pickFlag) {
        this.pickFlag = pickFlag;
    }

    public Boolean getAllocationFlag() {
        return allocationFlag;
    }

    public void setAllocationFlag(Boolean allocationFlag) {
        this.allocationFlag = allocationFlag;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public PassageDTO getPassageDTO() {
        return passageDTO;
    }

    public void setPassageDTO(PassageDTO passageDTO) {
        this.passageDTO = passageDTO;
    }

    public WavesStrategyBO getWavesStrategyDTO() {
        return wavesStrategyDTO;
    }

    public void setWavesStrategyDTO(WavesStrategyBO wavesStrategyDTO) {
        this.wavesStrategyDTO = wavesStrategyDTO;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public List<OutStockOrderPO> getOrders() {
        return orders;
    }

    public void setOrders(List<OutStockOrderPO> orders) {
        this.orders = orders;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    /**
     * 获取 是否机器人分拣 0:不开启1:开启
     *
     * @return isRobotPick 是否机器人分拣 0:不开启1:开启
     */
    public Byte getIsRobotPick() {
        return this.isRobotPick;
    }

    /**
     * 设置 是否机器人分拣 0:不开启1:开启
     *
     * @param isRobotPick 是否机器人分拣 0:不开启1:开启
     */
    public void setIsRobotPick(Byte isRobotPick) {
        this.isRobotPick = isRobotPick;
    }

    /**
     * 获取 是否是整件拆零拣货 生成的零散拣货任务 ：0、不是，1、是 如果是整件拣货，则生成的拣货任务非机器人拣货任务
     *
     * @return largePick 是否是整件拆零拣货 生成的零散拣货任务 ：0、不是，1、是 如果是整件拣货，则生成的拣货任务非机器人拣货任务
     */
    public Byte getLargePick() {
        return this.largePick;
    }

    /**
     * 设置 是否是整件拆零拣货 生成的零散拣货任务 ：0、不是，1、是 如果是整件拣货，则生成的拣货任务非机器人拣货任务
     *
     * @param largePick 是否是整件拆零拣货 生成的零散拣货任务 ：0、不是，1、是 如果是整件拣货，则生成的拣货任务非机器人拣货任务
     */
    public void setLargePick(Byte largePick) {
        this.largePick = largePick;
    }

    /**
     * 获取 是否是兑奖单拣货任务
     *
     * @return isAwardOrderTask 是否是兑奖单拣货任务
     */
    public boolean isIsAwardOrderTask() {
        return this.isAwardOrderTask;
    }

    /**
     * 设置 是否是兑奖单拣货任务
     *
     * @param isAwardOrderTask 是否是兑奖单拣货任务
     */
    public void setIsAwardOrderTask(boolean isAwardOrderTask) {
        this.isAwardOrderTask = isAwardOrderTask;
    }

    /**
     * 获取 前置仓内配单是否可以按产品拣货
     *
     * @return openFrontWarehouseOpenNPProductPick 前置仓内配单是否可以按产品拣货
     */
    public boolean getOpenFrontWarehouseOpenNPProductPick() {
        return this.openFrontWarehouseOpenNPProductPick;
    }

    /**
     * 设置 前置仓内配单是否可以按产品拣货
     *
     * @param openFrontWarehouseOpenNPProductPick 前置仓内配单是否可以按产品拣货
     */
    public void setOpenFrontWarehouseOpenNPProductPick(boolean openFrontWarehouseOpenNPProductPick) {
        this.openFrontWarehouseOpenNPProductPick = openFrontWarehouseOpenNPProductPick;
    }

    /**
     * 获取 追加拣货任务排序序号
     *
     * @return appendSequence 追加拣货任务排序序号
     */
    public Integer getAppendSequence() {
        return this.appendSequence;
    }

    /**
     * 设置 追加拣货任务排序序号
     *
     * @param appendSequence 追加拣货任务排序序号
     */
    public void setAppendSequence(Integer appendSequence) {
        this.appendSequence = appendSequence;
    }

    public Integer getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(Integer operateUserId) {
        this.operateUserId = operateUserId;
    }

    /**
     * 获取 拣货任务方式 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskKindOfPickingConstants
     *
     * @return kindOfPicking 拣货任务方式 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskKindOfPickingConstants
     */
    public Byte getKindOfPicking() {
        return this.kindOfPicking;
    }

    /**
     * 设置 拣货任务方式 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskKindOfPickingConstants
     *
     * @param kindOfPicking 拣货任务方式 @see com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskKindOfPickingConstants
     */
    public void setKindOfPicking(Byte kindOfPicking) {
        this.kindOfPicking = kindOfPicking;
    }

    public String getPassageName() {
        return passageDTO == null ? "" : passageDTO.getPassageName();
    }

}
