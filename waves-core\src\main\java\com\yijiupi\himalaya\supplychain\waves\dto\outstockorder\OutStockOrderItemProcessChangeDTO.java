package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 处理订单取消或修改（订单项）
 *
 * <AUTHOR>
 * @date 2020-06-22 16:11
 */
public class OutStockOrderItemProcessChangeDTO implements Serializable {

    private static final long serialVersionUID = 557987824239929788L;

    /**
     * 订单项id
     */
    private Long id;

    /**
     * 产品skuId
     */
    private Long skuId;

    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 差异小单位总数量（变少是负数，变多则为正数）
     */
    private BigDecimal diffCount;

    public BigDecimal getDiffCount() {
        return diffCount;
    }

    public void setDiffCount(BigDecimal diffCount) {
        this.diffCount = diffCount;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }
}
