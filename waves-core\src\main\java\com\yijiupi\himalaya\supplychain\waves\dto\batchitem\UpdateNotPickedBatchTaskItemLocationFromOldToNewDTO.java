package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import java.io.Serializable;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/6
 */
public class UpdateNotPickedBatchTaskItemLocationFromOldToNewDTO implements Serializable {
    /**
     * 机构id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 旧的货位id
     */
    private Long oldLocationId;
    /**
     * 新的货位id
     */
    private Long newLocationId;
    /**
     * 新的货位名称
     */
    private String newLocationName;


    /**
     * 获取 机构id
     *
     * @return orgId 机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 机构id
     *
     * @param orgId 机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 旧的货位id
     *
     * @return oldLocationId 旧的货位id
     */
    public Long getOldLocationId() {
        return this.oldLocationId;
    }

    /**
     * 设置 旧的货位id
     *
     * @param oldLocationId 旧的货位id
     */
    public void setOldLocationId(Long oldLocationId) {
        this.oldLocationId = oldLocationId;
    }

    /**
     * 获取 新的货位id
     *
     * @return newLocationId 新的货位id
     */
    public Long getNewLocationId() {
        return this.newLocationId;
    }

    /**
     * 设置 新的货位id
     *
     * @param newLocationId 新的货位id
     */
    public void setNewLocationId(Long newLocationId) {
        this.newLocationId = newLocationId;
    }

    /**
     * 获取 新的货位名称
     *
     * @return newLocationName 新的货位名称
     */
    public String getNewLocationName() {
        return this.newLocationName;
    }

    /**
     * 设置 新的货位名称
     *
     * @param newLocationName 新的货位名称
     */
    public void setNewLocationName(String newLocationName) {
        this.newLocationName = newLocationName;
    }
}
