package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.decoratesplitbatchtask;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.SplitBatchTaskDecorateHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.SplitBatchTaskByOrderTypeBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 内配单原单(单独生成一个按订单拣货的拣货任务)
 * 
 * <AUTHOR>
 * @date 2025/6/4
 */
@Component
public class SplitBatchTaskByAllotOrderDecorateBL extends SplitBatchTaskConcreteDecorateBL {

    @Override
    public void splitBatchTaskByOrderType(SplitBatchTaskByOrderTypeBO bo, SplitBatchTaskDecorateHelperBO helperBO) {
        WavesStrategyBO wavesStrategyDTO = helperBO.getWavesStrategyDTO();
        List<OutStockOrderPO> orders = helperBO.getOrders();
        ProcessBatchDTO processBatchDTO = helperBO.getProcessBatchDTO();

        List<OutStockOrderPO> createAllocationOrders = orders.stream()
            .filter(p -> Objects.nonNull(p.getOutBoundType())
                && OutBoundTypeEnum.SALE_ORDER.getCode().byteValue() == p.getOutBoundType()
                && OrderConstant.ALLOT_TYPE_ALLOCATION.equals(p.getAllotType()))
            .collect(Collectors.toList());
        List<WaveCreateDTO> allotWaveCreateDTO =
            splitAllotOrderBatchTaskList(createAllocationOrders, wavesStrategyDTO, processBatchDTO);
        bo.setAllotWaveCreateDTO(allotWaveCreateDTO);
    }

    private List<WaveCreateDTO> splitAllotOrderBatchTaskList(List<OutStockOrderPO> createAllocationOrders,
        WavesStrategyDTO wavesStrategyDTO, ProcessBatchDTO processBatchDTO) {
        if (CollectionUtils.isEmpty(createAllocationOrders)) {
            return Collections.emptyList();
        }
        String title = processBatchDTO.getBatchName();
        Integer cityId = processBatchDTO.getCityId();
        String operateUser = processBatchDTO.getOperateUser();
        String locationName = processBatchDTO.getLocationName();
        String driverName = processBatchDTO.getDriverName();

        if (BooleanUtils.isFalse(processBatchDTO.getOpenFrontWarehouseOpenNPProductPick())) {
            LOG.info("内配单原单单独生成一个按订单拣货的拣货任务。。。");
            WaveCreateDTO allocationCreateDTO =
                SplitWaveOrderUtil.getWaveCreateDTO(createAllocationOrders, wavesStrategyDTO,
                    (byte)WavesStrategyConstants.PICKINGTYPE_ORDER, wavesStrategyDTO.getPickingGroupStrategy(), title,
                    operateUser, cityId, locationName, driverName, null, null, false, processBatchDTO);
            allocationCreateDTO.setToLocationId(processBatchDTO.getToLocationId());
            allocationCreateDTO.setToLocationName(processBatchDTO.getToLocationName());

            // LOG.info("生成波次信息为1:{}", JSON.toJSONString(allocationCreateDTO));
            return Collections.singletonList(allocationCreateDTO);
        }

        List<WaveCreateDTO> waveCreateDTOS = new ArrayList<>();

        List<Long> ids =
            createAllocationOrders.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
        Map<Long, List<Byte>> orderFeatureMap = orderFeatureBL.getOrderFeatureMap(ids);

        // LOG.info("查找到的特征为:{}", JSON.toJSONString(orderFeatureMap));

        List<OutStockOrderPO> npProductOrderList = createAllocationOrders.stream()
            .filter(m -> com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(orderFeatureMap.get(m.getId())))
            .filter(m -> orderFeatureMap.get(m.getId()).stream()
                .anyMatch(feature -> feature.equals(OrderFeatureConstant.FEATURE_TYPE_DRINKING)))
            .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(npProductOrderList)) {
            WaveCreateDTO allocationCreateDTO =
                SplitWaveOrderUtil.getWaveCreateDTO(npProductOrderList, wavesStrategyDTO,
                    (byte)WavesStrategyConstants.PICKINGTYPE_PRODUCT, wavesStrategyDTO.getPickingGroupStrategy(), title,
                    operateUser, cityId, locationName, driverName, null, null, false, processBatchDTO);
            allocationCreateDTO.setToLocationId(processBatchDTO.getToLocationId());
            allocationCreateDTO.setToLocationName(processBatchDTO.getToLocationName());
            allocationCreateDTO
                .setOpenFrontWarehouseOpenNPProductPick(processBatchDTO.getOpenFrontWarehouseOpenNPProductPick());
            waveCreateDTOS.add(allocationCreateDTO);

        }

        Map<Long, Long> npProductOrderMap =
            npProductOrderList.stream().map(OutStockOrderPO::getId).collect(Collectors.toMap(k -> k, v -> v));
        List<OutStockOrderPO> npOrderOrderList = createAllocationOrders.stream()
            .filter(m -> Objects.isNull(npProductOrderMap.get(m.getId()))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(npOrderOrderList)) {
            // LOG.info("生成波次信息为2:{}", JSON.toJSONString(waveCreateDTOS));
            return waveCreateDTOS;
        }

        WaveCreateDTO allocationCreateDTO = SplitWaveOrderUtil.getWaveCreateDTO(npOrderOrderList, wavesStrategyDTO,
            (byte)WavesStrategyConstants.PICKINGTYPE_ORDER, wavesStrategyDTO.getPickingGroupStrategy(), title,
            operateUser, cityId, locationName, driverName, null, null, false, processBatchDTO);
        allocationCreateDTO.setToLocationId(processBatchDTO.getToLocationId());
        allocationCreateDTO.setToLocationName(processBatchDTO.getToLocationName());
        allocationCreateDTO
            .setOpenFrontWarehouseOpenNPProductPick(processBatchDTO.getOpenFrontWarehouseOpenNPProductPick());
        waveCreateDTOS.add(allocationCreateDTO);

        // LOG.info("生成波次信息为3:{}", JSON.toJSONString(waveCreateDTOS));
        return waveCreateDTOS;
    }

    /**
     * Get the order value of this object.
     * <p>
     * Higher values are interpreted as lower priority. As a consequence, the object with the lowest value has the
     * highest priority (somewhat analogous to Servlet {@code load-on-startup} values).
     * <p>
     * Same order values will result in arbitrary sort positions for the affected objects.
     *
     * @return the order value
     * @see #HIGHEST_PRECEDENCE
     * @see #LOWEST_PRECEDENCE
     */
    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}
