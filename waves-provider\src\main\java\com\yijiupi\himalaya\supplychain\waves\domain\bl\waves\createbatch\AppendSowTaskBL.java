package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchOrderProcessService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.*;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskChangeNotifyBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.lock.BatchInfoDistributeLockBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.batchlocation.CreateBatchLocationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchBaseBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.ExistSowTaskBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CreateBatchLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.split.SplitBatchTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.CreateBatchTaskByPassageResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderTypeBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchCreateDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.SowConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.WavesStrategyBOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.AppendSowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.DeleteBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/21
 */
@Service
public class AppendSowTaskBL {

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private SowTaskMapper sowTaskMapper;
    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private OrderFeatureBL orderFeatureBL;
    @Autowired
    private BatchOrderBL batchOrderBL;
    @Autowired
    private BatchInfoDistributeLockBL batchInfoDistributeLockBL;
    @Autowired
    private WavesStrategyBOConvertor wavesStrategyBOConvertor;
    @Autowired
    private CreateBatchLocationBL createBatchLocationBL;
    @Autowired
    private BatchOrderProcessBL batchOrderProcessBL;
    @Autowired
    private SplitBatchTaskBL splitBatchTaskBL;
    @Autowired
    private GlobalCache globalCache;
    @Autowired
    private SowManagerBL sowManagerBL;
    @Autowired
    private OrderTraceBL orderTraceBL;
    @Autowired
    private CreateBatchByManualOperationBL createBatchByManualOperationBL;
    @Autowired
    private CreateBatchByRefOrderNoBL createBatchByRefOrderNoBL;
    @Autowired
    private ComputerStrategyConditionBL computerStrategyConditionBL;
    @Autowired
    private SplitBatchTaskByPassageBL splitBatchTaskByPassageBL;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchCreateDTOConvertor batchCreateDTOConvertor;
    @Autowired
    private OrderCenterBL orderCenterBL;
    @Autowired
    private BatchTaskChangeNotifyBL batchTaskChangeNotifyBL;

    @Autowired
    private IBatchOrderProcessService iBatchOrderProcessService;

    private static final Logger LOG = LoggerFactory.getLogger(AppendSowTaskBL.class);

    /**
     * 追加播种任务 <br/>
     * 完成播种任务接口：SowManagerBL#completeSowTaskItems <br/>
     * 领取播种任务的接口：sowManage/receiveSowTask <br />
     * 
     * @param appendSowTaskDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void appendSowTask(AppendSowTaskDTO appendSowTaskDTO) {
        LOG.info("追加波次入参:{}", JSON.toJSONString(appendSowTaskDTO));
        SowTaskPO sowTaskPO =
            sowTaskMapper.getSowTaskById(appendSowTaskDTO.getSowTaskId(), appendSowTaskDTO.getOrgId());

        AssertUtils.notNull(sowTaskPO, "播种任务不存在");

        if (SowTaskStateEnum.已播种.getType() == sowTaskPO.getState()) {
            throw new BusinessValidateException("播种任务已完成！");
        }

        BatchPO batchPO = batchMapper.selectBatchByBatchNo(appendSowTaskDTO.getOrgId(), sowTaskPO.getBatchNo());
        AssertUtils.notNull(batchPO, "波次信息不存在");

        if (BooleanUtils.isFalse(BatchStateEnum.couldPick(batchPO.getState().intValue()))) {
            throw new BusinessValidateException("波次已完成！");
        }

        if (Objects.nonNull(batchPO.getSowType()) && BatchSowTypeEnum.二次分拣.getType() == batchPO.getSowType()) {
            throw new BusinessValidateException("总单二次分拣不支持追加播种任务！");
        }

        List<Long> appendOrderIds =
            appendSowTaskDTO.getOutStockOrderIds().stream().map(Long::valueOf).collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findByOrderId(appendOrderIds);

        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            LOG.info("无未生成波次的出库单");
            return;
        }

        doAppendSowTask(appendSowTaskDTO, sowTaskPO, batchPO, outStockOrderPOList);

        // 通知中台创建波次
        CreateBatchBaseBO createBatchBaseBO = new CreateBatchBaseBO();
        BatchCreateDTO batchCreateDTO = new BatchCreateDTO();
        batchCreateDTO.setOperateUserId(appendSowTaskDTO.getOptUserId());
        createBatchBaseBO.setBatchCreateDTO(batchCreateDTO);
        orderCenterBL.createWaveNotifyByOrder(outStockOrderPOList, createBatchBaseBO);
        // 如果播种任务在播种中，通知供应链客户端
    }

    private void doAppendSowTask(AppendSowTaskDTO appendSowTaskDTO, SowTaskPO sowTaskPO, BatchPO batchPO,
        List<OutStockOrderPO> appendOrderPOList) {
        try {
            List<Long> outStockOrderIds =
                appendOrderPOList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());

            // 暂时注释
            validateOrdersCouldAppend(outStockOrderIds, appendOrderPOList);
            // 处理波次尚未开始的场景
            if (BatchStateEnum.PENDINGPICKING.getType() == batchPO.getState().intValue()) {
                handleBatchNotStart(appendSowTaskDTO, batchPO);
                return;
            }

            if (BatchStateEnum.PICKING.getType() == batchPO.getState().intValue()
                || BatchStateEnum.SOWN.getType() == batchPO.getState().intValue()) {
                handleBatchTaskStart(appendSowTaskDTO, batchPO, sowTaskPO, appendOrderPOList);
                return;
            }

            // 添加trace
        } catch (Exception e) {
            LOG.warn("追加波次报错", e);
            throw e;
        }
    }

    /**
     * 处理波次未开始的场景
     * 
     * @param appendSowTaskDTO
     * @param batchPO
     */
    public void handleBatchNotStart(AppendSowTaskDTO appendSowTaskDTO, BatchPO batchPO) {
        List<OutStockOrderPO> outStockOrderList = outStockOrderMapper
            .findSimpleInfoByBatchNos(Collections.singletonList(batchPO.getBatchNo()), batchPO.getOrgId());

        validateBatchHasRobotTask(batchPO);

        List<Long> appendOutStockOrderIds =
            appendSowTaskDTO.getOutStockOrderIds().stream().map(Long::valueOf).collect(Collectors.toList());
        List<OutStockOrderPO> appendOutStockOrderList = outStockOrderMapper.selectByIds(appendOutStockOrderIds);

        CreateBatchBaseBO createBatchBaseBO = batchCreateDTOConvertor.getAppendBatchCreateParams(batchPO.getBatchNo(),
            outStockOrderList, appendOutStockOrderList);

        DeleteBatchDTO deleteBatchDTO = new DeleteBatchDTO();
        deleteBatchDTO.setBatchNoList(Arrays.asList(batchPO.getBatchNo()));
        deleteBatchDTO.setOperateUser(globalCache.getUserName(appendSowTaskDTO.getOptUserId()));
        deleteBatchDTO.setOperateUserId(appendSowTaskDTO.getOptUserId());
        batchOrderBL.deleteBatchOrder(deleteBatchDTO);

        createBatch(createBatchBaseBO);
    }

    /**
     * 验证是否是包含机器人拣货任务的波次，如果包含机器人拣货任务，不支持追加波次
     * 
     * @param batchPO
     */
    private void validateBatchHasRobotTask(BatchPO batchPO) {
        // 有机器人拣货任务的波次，不支持删除波次再重新添加
        if (!globalCache.checkWarehouseIsRobotPicking(batchPO.getWarehouseId())) {
            return;
        }

        Integer count = batchTaskMapper.countRobotTaskInBatch(batchPO.getBatchNo(), batchPO.getOrgId());
        if (Objects.isNull(count) || count == 0) {
            return;
        }

        throw new BusinessValidateException("波次中有机器人拣货任务，该波次不支持追加！");
    }

    /**
     * 直接创建波次
     * 
     * @param createBatchBaseBO
     */
    private void createBatch(CreateBatchBaseBO createBatchBaseBO) {
        if (Objects.nonNull(createBatchBaseBO.getBatchCreateDTO())) {
            iBatchOrderProcessService.createBatch(createBatchBaseBO.getBatchCreateDTO());
        }

        if (Objects.nonNull(createBatchBaseBO.getBatchCreateByRefOrderNoDTO())) {
            iBatchOrderProcessService.createBatchByRefOrderNo(createBatchBaseBO.getBatchCreateByRefOrderNoDTO());
        }
    }

    /**
     * 处理播种中有拣货任务已经开始的场景 <br />
     * 1、获取原来波次的分仓信息；2、找出和波次分仓属性相同的订单，进行追加；分仓信息不相同的订单，重新生成波次
     * 
     * @param appendSowTaskDTO 追加播种任务对象
     * @param batchPO 波次
     * @param existSowTaskPO 已存在的播种任务
     * @param appendOutStockOrderList 需要追加的订单信息
     */
    //
    private void handleBatchTaskStart(AppendSowTaskDTO appendSowTaskDTO, BatchPO batchPO, SowTaskPO existSowTaskPO,
        List<OutStockOrderPO> appendOutStockOrderList) {
        // 还原创建波次参数
        CreateBatchBaseBO createBatchBaseBO =
            batchCreateDTOConvertor.getBatchAppendParams(batchPO.getBatchNo(), appendOutStockOrderList);
        LOG.info("【追加波次】还原参数为：{}", JSON.toJSONString(createBatchBaseBO));
        WavesStrategyBO wavesStrategyBO = batchCreateDTOConvertor.getAppendWavesStrategyBO(createBatchBaseBO);

        // 没开启分仓，直接追加
        if (BooleanUtils.isFalse(globalCache.openWarehouseSeparateAttributeConfig(batchPO.getWarehouseId()))) {
            appendWhenBatchTaskHaveStart(appendSowTaskDTO, batchPO, existSowTaskPO, appendOutStockOrderList,
                createBatchBaseBO, wavesStrategyBO);
            return;
        }

        List<OutStockOrderPO> batchOutStockOrderList = findOutStockOrderInBatch(batchPO);
        if (CollectionUtils.isEmpty(batchOutStockOrderList)) {
            throw new BusinessValidateException("波次内出库单信息为空，不能追加！");
        }
        Optional<OutStockOrderPO> sampleOrderOptional =
            batchOutStockOrderList.stream().filter(Objects::nonNull).findFirst();
        if (BooleanUtils.isFalse(sampleOrderOptional.isPresent())) {
            throw new BusinessValidateException("波次内出库单信息为空，不能追加！");
        }
        OutStockOrderPO sampleOrder = sampleOrderOptional.get();
        Map<Long, List<Byte>> sampleOrderFeatureMap =
            orderFeatureBL.getOrderFeatureMap(Collections.singletonList(sampleOrder.getId()));

        List<Byte> orderFeatureList = sampleOrderFeatureMap.get(sampleOrder.getId());

        Map<Byte, List<OutStockOrderPO>> splitOrderByFeatureMap =
            splitBatchTaskBL.splitOrderByFeature(appendOutStockOrderList);

        splitOrderByFeatureMap.forEach((feature, orderPOList) -> {
            boolean isFeatureSameAsBatch = orderFeatureList.stream().anyMatch(m -> feature.equals(m));
            if (isFeatureSameAsBatch) {
                wavesStrategyBO.setFeatureType(feature);
                appendWhenBatchTaskHaveStart(appendSowTaskDTO, batchPO, existSowTaskPO, appendOutStockOrderList,
                    orderPOList, createBatchBaseBO, wavesStrategyBO);
            } else {
                CreateBatchBaseBO createNewBatchBaseBO =
                    batchCreateDTOConvertor.getBatchCreateParams(batchPO, orderPOList);
                createBatch(createBatchBaseBO);
            }
        });

    }

    private void appendWhenBatchTaskHaveStart(AppendSowTaskDTO appendSowTaskDTO, BatchPO batchPO,
        SowTaskPO existSowTaskPO, List<OutStockOrderPO> appendOutStockOrderList, CreateBatchBaseBO createBatchBaseBO,
        WavesStrategyBO wavesStrategyBO) {
        List<Long> appendOrderIds =
            appendSowTaskDTO.getOutStockOrderIds().stream().map(Long::valueOf).collect(Collectors.toList());

        List<OutStockOrderPO> batchOutStockOrderList = findOutStockOrderInBatch(batchPO);

        appendWhenBatchTaskHaveStart(appendSowTaskDTO, batchPO, existSowTaskPO, appendOutStockOrderList,
            batchOutStockOrderList, createBatchBaseBO, wavesStrategyBO);
    }

    private void appendWhenBatchTaskHaveStart(AppendSowTaskDTO appendSowTaskDTO, BatchPO batchPO,
        SowTaskPO existSowTaskPO, List<OutStockOrderPO> appendOutStockOrderList,
        List<OutStockOrderPO> batchOutStockOrderList, CreateBatchBaseBO createBatchBaseBO,
        WavesStrategyBO wavesStrategyBO) {

        ProcessBatchDTO processBatchDTO =
            batchCreateDTOConvertor.getAppendProcessBatchDTOAndHandleDeliveryTask(createBatchBaseBO, wavesStrategyBO);

        // 过滤出可以追加到波次的订单
        List<OutStockOrderPO> filterInBatchOrderList = splitBatchTaskBL.filterAppendOutStockOrderInBatch(
            appendOutStockOrderList, batchPO, wavesStrategyBO, processBatchDTO, batchOutStockOrderList);

        // 如果没有，直接走生成波次接口
        if (CollectionUtils.isEmpty(filterInBatchOrderList)) {
            CreateBatchBaseBO createNewBatchBaseBO =
                batchCreateDTOConvertor.getBatchCreateParams(batchPO, appendOutStockOrderList);
            createBatch(createBatchBaseBO);
            return;
        }

        // 验证订单是否符合波次信息 TODO
        // 先区分按产品还是按订单
        // 按产品就判断订单是否都在波次线路/片区，或者不分线路片区。不分线路片区，直接添加，分线路片区，不在波次线路/片区里的订单新生成波次
        // 按订单拣货，

        // 如果没有符合条件的订单，把这些不符合条件的订单，直接生成新波次 TODO
        // 符合条件的订单，直接生成拣货任务、播种任务

        // 完成还需要通知tms和中台

        // 符合条件的追加订单，直接生成拣货任务
        createTaskAndSowByPassage(wavesStrategyBO, batchPO, processBatchDTO, batchOutStockOrderList,
            filterInBatchOrderList, existSowTaskPO, appendSowTaskDTO);

        // 不符合条件的追加订单，生成波次
        List<OutStockOrderPO> notFitBatchOrderList = appendOutStockOrderList.stream()
            .filter(m -> filterInBatchOrderList.stream().noneMatch(t -> m.getId().equals(t.getId())))
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(notFitBatchOrderList)) {
            CreateBatchBaseBO createNewBatchBaseBO =
                batchCreateDTOConvertor.getBatchCreateParams(batchPO, notFitBatchOrderList);
            createBatch(createBatchBaseBO);
        }

        batchTaskChangeNotifyBL.notifySupplyChainClientSowTaskChange(existSowTaskPO);
    }

    private void createTaskAndSowByPassage(WavesStrategyBO wavesStrategyBO, BatchPO batchPO,
        ProcessBatchDTO processBatchDTO, List<OutStockOrderPO> batchOutStockOrderList,
        List<OutStockOrderPO> appendOutStockOrderList, SowTaskPO sowTaskPO, AppendSowTaskDTO appendSowTaskDTO) {

        CreateBatchLocationBO createBatchLocationBO = createBatchLocationBL.handleLocation(appendOutStockOrderList,
            processBatchDTO.getWorkSetting(), wavesStrategyBO);

        LOG.info("【追加波次】处理设置locationid订单：{}", JSON.toJSONString(createBatchLocationBO));

        processBatchDTO
            .setOpenFrontWarehouseOpenNPProductPick(createBatchLocationBO.getOpenFrontWarehouseOpenNPProductPick());
        processBatchDTO.setCityId(batchPO.getOrgId());

        validatePickingBatchAppend(createBatchLocationBO, wavesStrategyBO, processBatchDTO, appendOutStockOrderList,
            batchOutStockOrderList);

        List<OutStockOrderPO> totalOrderList = createBatchLocationBO.getTotalOrderList();

        List<BatchTaskPO> batchTaskPOList =
            batchTaskMapper.selectBatchTaskBySowTaskIds(Collections.singletonList(sowTaskPO.getId()));
        ExistSowTaskBO existSowTaskBO = new ExistSowTaskBO();
        existSowTaskBO.setSowTaskPO(sowTaskPO);
        existSowTaskBO.setPassageId(
            batchTaskPOList.stream().map(BatchTaskPO::getPassageId).filter(Objects::nonNull).findAny().get());

        // 内配单创建拣货任务
        SplitBatchTaskByOrderTypeBO splitBatchTaskByOrderTypeBO =
            splitBatchTaskBL.splitBatchTaskByOrderType(wavesStrategyBO, totalOrderList, processBatchDTO);
        List<WaveCreateDTO> allotWaveCreateDTO = splitBatchTaskByOrderTypeBO.getAllotWaveCreateDTO();
        if (!org.springframework.util.CollectionUtils.isEmpty(allotWaveCreateDTO)) {
            // 创建拣货任务
            allotWaveCreateDTO
                .forEach(m -> batchOrderProcessBL.processBatchTaskResult(m, batchPO, Collections.emptyList()));
        }

        WarehouseConfigDTO warehouseConfigDTO = globalCache.getWarehouseConfigDTO(wavesStrategyBO.getWarehouseId());

        // 普通订单走通道创建拣货任务
        List<WaveCreateDTO> normalWaveCreateDTO = splitBatchTaskByOrderTypeBO.getNormalWaveCreateDTO();
        List<CreateBatchTaskByPassageResultBO> createBatchTaskByPassageBOS =
            createBatchTaskByPassage(normalWaveCreateDTO, batchPO, existSowTaskBO);

        if (CollectionUtils.isEmpty(createBatchTaskByPassageBOS)) {
            return;
        }

        List<WaveCreateDTO> batchWaveCreateList =
            createBatchTaskByPassageBOS.stream().filter(m -> CollectionUtils.isNotEmpty(m.getWaveCreateDTOList()))
                .flatMap(m -> m.getWaveCreateDTOList().stream()).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(batchWaveCreateList)) {
            batchWaveCreateList
                .forEach(m -> batchOrderProcessBL.processBatchTaskResult(m, batchPO, Collections.emptyList()));
        }

        existSowTaskInfoCreateBatchTask(createBatchTaskByPassageBOS, batchPO, sowTaskPO, totalOrderList);

        batchOutStockOrderList.addAll(totalOrderList);
        // 更新波次状态，数量等信息
        updateBatchInfo(batchPO, batchOutStockOrderList);
        batchOrderProcessBL.updateOrderState(totalOrderList, batchPO, appendSowTaskDTO.getOptUserId());
    }

    private void existSowTaskInfoCreateBatchTask(List<CreateBatchTaskByPassageResultBO> createBatchTaskByPassageBOS,
        BatchPO batchPO, SowTaskPO sowTaskPO, List<OutStockOrderPO> appendOutStockOrderList) {
        // 处理存在播种的拣货任务信息
        List<CreateSowTaskResultBO> existSowTaskResultList =
            createBatchTaskByPassageBOS.stream().map(CreateBatchTaskByPassageResultBO::getExistSowTaskList)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(existSowTaskResultList)) {
            return;
        }

        List<WaveCreateDTO> waveCreateDTOList =
            existSowTaskResultList.stream().map(CreateSowTaskResultBO::getWaveCreateDTOList)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        List<SowOrderPO> sowOrderPOList = existSowTaskResultList.stream().map(CreateSowTaskResultBO::getSowOrderPOList)
            .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());

        Integer maxSequence = getMaxBatchTaskSequence(batchPO);

        if (CollectionUtils.isNotEmpty(waveCreateDTOList)) {
            for (WaveCreateDTO waveCreateDTO : waveCreateDTOList) {
                waveCreateDTO.setAppendSequence(++maxSequence);
            }
            // 创建拣货任务
            waveCreateDTOList
                .forEach(m -> batchOrderProcessBL.processBatchTaskResult(m, batchPO, Collections.emptyList()));
        }

        List<SowTaskPO> existSowTaskPOList = existSowTaskResultList.stream()
            .map(CreateSowTaskResultBO::getSowTaskPOList).flatMap(Collection::stream).collect(Collectors.toList());

        // 对sowOrder进行排序
        if (CollectionUtils.isNotEmpty(sowOrderPOList)) {
            setSowOrderSequence(appendOutStockOrderList, sowOrderPOList, sowTaskPO);
            sowManagerBL.insertSowOrderList(sowOrderPOList);
        }

        existSowTaskPOList.forEach(task -> {
            // 更新播种任务信息
            sowManagerBL.appendSowTask(task);
        });
    }

    // 获取追加的排序号
    private Integer getMaxBatchTaskSequence(BatchPO batchPO) {
        Integer maxSequence = batchTaskMapper.selectMaxTaskSequenceByBatchNo(batchPO.getBatchNo(), batchPO.getOrgId());
        if (Objects.isNull(maxSequence)) {
            return 0;
        }

        return maxSequence;
    }

    private List<CreateBatchTaskByPassageResultBO> createBatchTaskByPassage(List<WaveCreateDTO> normalWaveCreateDTO,
        BatchPO batchPO, ExistSowTaskBO existSowTaskBO) {
        if (CollectionUtils.isEmpty(normalWaveCreateDTO)) {
            return Collections.emptyList();
        }
        List<CreateBatchTaskByPassageResultBO> createBatchTaskByPassageBOS = new ArrayList<>();
        if (org.springframework.util.CollectionUtils.isEmpty(normalWaveCreateDTO)) {
            return Collections.emptyList();
        }

        BatchBO batchBO = new BatchBO(batchPO);
        int sowTaskCount = sowTaskMapper.findSowTaskCountByBatchNo(batchPO.getBatchNo(), batchPO.getOrgId());
        batchBO.setSowTaskNum(sowTaskCount);

        normalWaveCreateDTO.forEach(tmpCreateDTO -> {
            // 创建拣货、播种任务PO数据
            CreateBatchTaskByPassageResultBO createBatchTaskByPassageBO = splitBatchTaskByPassageBL
                .createBatchTaskByPassage(tmpCreateDTO, batchBO, existSowTaskBO, Collections.emptyList());
            if (Objects.nonNull(createBatchTaskByPassageBO)) {
                createBatchTaskByPassageBOS.add(createBatchTaskByPassageBO);
            }
        });

        return createBatchTaskByPassageBOS;
    }

    private void setSowOrderSequence(List<OutStockOrderPO> appendOutStockOrderList, List<SowOrderPO> sowOrderPOList,
        SowTaskPO existSowTaskPO) {
        Map<String, String> sowOrderNoMap = sowOrderPOList.stream()
            .collect(Collectors.toMap(SowOrderPO::getRefOrderNo, SowOrderPO::getRefOrderNo, (v1, v2) -> v1));

        List<OutStockOrderPO> filterOutStockOrderList = appendOutStockOrderList.stream()
            .filter(o -> Objects.nonNull(sowOrderNoMap.get(o.getReforderno()))).collect(Collectors.toList());

        Map<String, Integer> sequenceMap = SowConverter.getSowSequence(filterOutStockOrderList);

        for (int i = 0; i < sowOrderPOList.size(); i++) {
            Integer sequence = sequenceMap.get(sowOrderPOList.get(i).getRefOrderNo());
            sowOrderPOList.get(i).setSowOrderSequence(existSowTaskPO.getOrderCount() + i);
        }

    }

    private void updateBatchInfo(BatchPO batchPO, List<OutStockOrderPO> outStockOrderPOList) {
        List<OutStockOrderItemPO> waveOrderItems =
            outStockOrderPOList.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList());
        BatchPO updateBatchPO = new BatchPO();
        updateBatchPO.setId(batchPO.getId());
        updateBatchPO.setState(BatchStateEnum.PICKING.getType().byteValue());
        BigDecimal packageCount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.piecePackageNumberLimitType);
        updateBatchPO.setPackageAmount(packageCount);
        BigDecimal unitCount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.pieceUnitNumberLimitType);
        updateBatchPO.setUnitAmount(unitCount);
        BigDecimal orderCount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.orderCountLimitType);
        updateBatchPO.setOrderCount(orderCount.intValue());
        BigDecimal orderAmount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.orderAmountLimitType);
        updateBatchPO.setOrderAmount(orderAmount);
        BigDecimal skuCount = computerStrategyConditionBL.computerStrategyConditionByOrderItems(waveOrderItems,
            WavesStrategyLimitType.skuCountLimitType);
        updateBatchPO.setSkuCount(skuCount.intValue());

        batchMapper.updateBatchInfo(updateBatchPO);
    }

    /**
     * 验证是否是休食订单
     * 
     * @param outStockOrderIds
     * @param outStockOrderPOList
     */
    private void validateOrdersCouldAppend(List<Long> outStockOrderIds, List<OutStockOrderPO> outStockOrderPOList) {
        Map<Long, List<Byte>> orderFeatureMap = orderFeatureBL.getOrderFeatureMap(outStockOrderIds);
        Map<Long, OutStockOrderPO> outStockOrderPOMap =
            outStockOrderPOList.stream().collect(Collectors.toMap(OutStockOrderPO::getId, v -> v));

        outStockOrderIds.forEach(id -> {
            List<Byte> featureList = orderFeatureMap.get(id);
            if (CollectionUtils.isNotEmpty(featureList)) {
                boolean isRestOrder =
                    featureList.stream().anyMatch(m -> OrderFeatureConstant.FEATURE_TYPE_REST.equals(m));
                if (BooleanUtils.isFalse(isRestOrder)) {
                    OutStockOrderPO outStockOrderPO = outStockOrderPOMap.get(id);

                    throw new BusinessValidateException(
                        "只有休食订单能追加到播种任务，订单:" + outStockOrderPO.getReforderno() + "不是休食订单！");
                }
            }
        });

        // TODO 验证订单类型和波次类型是否匹配
    }

    private void validatePickingBatchAppend(CreateBatchLocationBO createBatchLocationBO,
        WavesStrategyBO wavesStrategyBO, ProcessBatchDTO processBatchDTO, List<OutStockOrderPO> appendOrderList,
        List<OutStockOrderPO> batchOutStockOrderList) {

    }

    @Transactional(rollbackFor = Exception.class)
    public void addAndDeleteBatch(DeleteBatchDTO deleteBatchDTO, BatchCreateDTO batchCreateDTO) {
        List<Long> orderIds = batchCreateDTO.getOrderIdList().stream().map(Long::valueOf).collect(Collectors.toList());

        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findByOrderId(orderIds);
        batchOrderBL.deleteBatchOrder(deleteBatchDTO);
        // iBatchManageService.deleteBatchOrder(deleteBatchDTO);
        CreateBatchBaseBO createBatchBaseBO = new CreateBatchBaseBO();
        createBatchBaseBO.setBatchCreateDTO(batchCreateDTO);

        outStockOrderPOList = outStockOrderMapper.findByOrderId(orderIds);
        createBatchByManualOperationBL.createBatch(createBatchBaseBO);
    }

    @Transactional(rollbackFor = Exception.class)
    public String getSowTaskBatchNo(AppendSowTaskDTO appendSowTaskDTO) {
        SowTaskPO sowTaskPO =
            sowTaskMapper.getSowTaskById(appendSowTaskDTO.getSowTaskId(), appendSowTaskDTO.getOrgId());

        AssertUtils.notNull(sowTaskPO, "播种任务不存在");

        return sowTaskPO.getBatchNo();
    }

    private List<OutStockOrderPO> findOutStockOrderInBatch(BatchPO batchPO) {
        List<Long> orderIds = outStockOrderMapper.findOrderIdsInBatch(batchPO.getBatchNo(), batchPO.getOrgId());

        List<OutStockOrderPO> outStockOrderPOList = Lists.partition(orderIds, 100).stream().map(ids -> {
            List<OutStockOrderPO> orderList = outStockOrderMapper.findByOrderIds(ids);
            return orderList;
        }).flatMap(Collection::stream).collect(Collectors.toList());

        return outStockOrderPOList;
    }
}
