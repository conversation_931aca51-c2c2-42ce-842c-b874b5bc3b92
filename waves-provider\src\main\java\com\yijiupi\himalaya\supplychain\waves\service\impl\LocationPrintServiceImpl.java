package com.yijiupi.himalaya.supplychain.waves.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.batch.ILocationPrintService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.LocationContainerBL;
import com.yijiupi.himalaya.supplychain.waves.search.LocationContainerSO;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/4/20
 */
@Service
public class LocationPrintServiceImpl implements ILocationPrintService {
    @Autowired
    private LocationContainerBL locationContainerBL;

    /**
     * 创建货位容器逻辑
     *
     * @param locationContainerSO
     */
    @Override
    public void createLocationContainer(LocationContainerSO locationContainerSO) {
        validateCommon(locationContainerSO);
        locationContainerBL.createContainer(locationContainerSO);
    }

    /**
     * @param locationContainerSO
     * @return
     */
    @Override
    public String printLocationContainer(LocationContainerSO locationContainerSO) {
        validateCommon(locationContainerSO);
        return locationContainerBL.printContainer(locationContainerSO);
    }

    public void validateCommon(LocationContainerSO locationContainerSO) {
        AssertUtils.notNull(locationContainerSO, "参数不能为空!");
        AssertUtils.notNull(locationContainerSO.getOrgId(), "城市id不能为空!");
        AssertUtils.notNull(locationContainerSO.getWarehouseId(), "仓库id不能为空!");
        AssertUtils.notNull(locationContainerSO.getLocationId(), "货位id不能为空!");
        AssertUtils.notNull(locationContainerSO.getLocationName(), "货位名称不能为空!");
        AssertUtils.notNull(locationContainerSO.getRefOrderNo(), "订单号不能为空!");
    }

}
