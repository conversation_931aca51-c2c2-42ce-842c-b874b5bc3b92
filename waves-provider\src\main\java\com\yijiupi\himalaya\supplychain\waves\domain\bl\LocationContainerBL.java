package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.OutStockOrderItemQuerySO;
import com.yijiupi.himalaya.supplychain.waves.domain.mq.OrderToLocationSyncMQ;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.WaveNoItemModel;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.WaveNoModel;
import com.yijiupi.himalaya.supplychain.waves.search.LocationContainerSO;
import com.yijiupi.himalaya.supplychain.waves.util.BatchNoGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;


/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/4/20
 */
@Service
public class LocationContainerBL {


    private static Logger LOG = LoggerFactory.getLogger(LocationContainerBL.class);
    @Reference
    private ILocationService iLocationService;
    @Autowired
    private BatchNoGenerator batchNoGenerator;

    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Reference
    private IWarehouseQueryService warehouseQueryService;
    @Reference
    private IVariableValueService iVariableValueService;

    @Autowired
    private OrderToLocationSyncMQ orderToLocationSyncMQ;

    /**
     * 生成货位容器标识
     *
     * @param locationContainerSO
     */
    public String createContainer(LocationContainerSO locationContainerSO) {
        LOG.info("生成货位容器标识入参:{}", JSON.toJSONString(locationContainerSO));
        VariableValueQueryDTO variableValue = new VariableValueQueryDTO();
        variableValue.setOrgId(locationContainerSO.getOrgId());
        variableValue.setWarehouseId(locationContainerSO.getWarehouseId());
        variableValue.setVariableKey("wms_need_distribution_container");
        VariableDefAndValueDTO variableDefAndValueDTO = iVariableValueService.detailVariable(variableValue);
        String variableData = null;
        if (Objects.isNull(variableDefAndValueDTO)) {
            variableData = "false";
        } else {
            variableData = variableDefAndValueDTO.getVariableData();
        }
        //boolean openOrderCenter = warehouseQueryService.isOpenOrderCenter(locationContainerSO.getWarehouseId());
        List<LoactionDTO> locationList = iLocationService.findLocationByIds(Arrays.asList(locationContainerSO.getLocationId()));
        if (CollectionUtils.isEmpty(locationList)) {
            LOG.warn(String.format("货位id:[%s]不存在!", locationContainerSO.getLocationId()));
        }
        String containerCode = null;
        LOG.info("是否开启配置:[{}]", variableData);
        //开启灰度 生成货位容器标识
        if (Boolean.valueOf(variableData)) {
            OutStockOrderItemQuerySO querySO = new OutStockOrderItemQuerySO();
            BeanUtils.copyProperties(locationContainerSO, querySO);
            querySO.setRefOrderNoList(Arrays.asList(locationContainerSO.getRefOrderNo()));
            List<OutStockOrderItemPO> outstockorderItemList = outStockOrderItemMapper.listOrderItem(querySO).toPageList().getDataList();
            if (CollectionUtils.isEmpty(outstockorderItemList)) {
                LOG.info("订单[{}]没有订单项!", locationContainerSO.getRefOrderNo());
                return containerCode;
            }
            boolean assigned = outstockorderItemList.stream().anyMatch(p -> {
                return p.getLocationName() != null && p.getLocationName().contains("(");
            });
            if (assigned) {
                //发送货位信息生成运单
                sendTMSLocationName(outstockorderItemList, locationContainerSO, outstockorderItemList.get(0).getLocationName());
                return outstockorderItemList.get(0).getLocationName();
            }
            //生成容器位下标
            Long containerIndex = batchNoGenerator.containerGenerator(locationContainerSO.getWarehouseId(), locationContainerSO.getLocationName());
            containerCode = String.format("%s(%02d)", locationContainerSO.getLocationName(), containerIndex);
            LOG.info("货位[{}]生成新出库位名称标识：[{}]", locationContainerSO.getLocationId(), containerCode);
            String finalContainerCode = containerCode;
            outstockorderItemList.forEach(outstockorderItemDTO -> {
                OutStockOrderItemPO outStockOrderItemPO = new OutStockOrderItemPO();
                outStockOrderItemPO.setId(outstockorderItemDTO.getId());
                outStockOrderItemPO.setLocationId(outstockorderItemDTO.getLocationId());
                outStockOrderItemPO.setLocationName(finalContainerCode);
                outStockOrderItemMapper.updateLocation(outStockOrderItemPO);
            });
            if (CollectionUtils.isEmpty(outstockorderItemList)) {
                LOG.warn("订单号不存在!");
            }
            //发送货位信息生成运单
            sendTMSLocationName(outstockorderItemList, locationContainerSO, containerCode);
        }
        return containerCode;
    }

    /**
     * 打印容器下标
     *
     * @param locationContainerSO
     * @return
     */
    public String printContainer(LocationContainerSO locationContainerSO) {
        LOG.info("打印容器下标入参:{}", JSON.toJSONString(locationContainerSO));
        List<LoactionDTO> locationList = iLocationService.findLocationByIds(Arrays.asList(locationContainerSO.getLocationId()));
        if (CollectionUtils.isEmpty(locationList)) {
            LOG.warn(String.format("货位id:[%s]不存在!", locationContainerSO.getLocationId()));
        }
        String containerIdex = createContainer(locationContainerSO);
        //开启灰度 生成货位容器标识
        if (StringUtils.isEmpty(containerIdex)) {
            LOG.info("{}仓库未开启对应配置!", locationContainerSO.getWarehouseId());
            return locationContainerSO.getLocationName();
        } else {
            return containerIdex;
        }
    }

    /**
     * 发送货位信息
     *
     * @param outstockorderItemList
     * @param locationContainerSO
     * @param containerCode
     */
    public void sendTMSLocationName(List<OutStockOrderItemPO> outstockorderItemList, LocationContainerSO locationContainerSO, String containerCode) {
        WaveNoModel waveNoModel = new WaveNoModel();
        waveNoModel.setWaveNo(outstockorderItemList.get(0).getBatchno());
        waveNoModel.setWarehouseId(locationContainerSO.getWarehouseId());
        WaveNoItemModel waveNoItemList = new WaveNoItemModel();
        waveNoItemList.setLocationId(locationContainerSO.getLocationId());
        waveNoItemList.setBusinessNo(locationContainerSO.getRefOrderNo());
        waveNoItemList.setLocationName(containerCode);
        waveNoModel.setOrderItems(Arrays.asList(waveNoItemList));
        orderToLocationSyncMQ.send(waveNoModel);
    }
}
