package com.yijiupi.himalaya.supplychain.waves.domain.model;

/**
 * 分組策略
 * 
 * <AUTHOR>
 * @date: 2019年11月13日 上午9:16:00
 */
public enum SaaSPickingGroupStrategyEnum {
    /**
     * 枚举
     */
    货区((byte)10), 货位((byte)2), 类目((byte)3);

    /**
     * type
     */
    private Byte type;

    SaaSPickingGroupStrategyEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    public static String getType(Byte type) {
        if (null == type) {
            return null;
        }
        for (SaaSPickingGroupStrategyEnum pickingGroupStrategyEnum : SaaSPickingGroupStrategyEnum.values()) {
            if (pickingGroupStrategyEnum.getType() == type) {
                return pickingGroupStrategyEnum.toString();
            }
        }
        return null;
    }
}
