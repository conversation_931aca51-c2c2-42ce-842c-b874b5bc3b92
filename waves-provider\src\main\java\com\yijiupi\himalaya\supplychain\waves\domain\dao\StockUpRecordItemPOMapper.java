package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordItemPO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2024-10-11 16:11
 **/
@Mapper
public interface StockUpRecordItemPOMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insert(StockUpRecordItemPO record);

    int insertOrUpdate(StockUpRecordItemPO record);

    int insertOrUpdateSelective(StockUpRecordItemPO record);

    int insertSelective(StockUpRecordItemPO record);

    StockUpRecordItemPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StockUpRecordItemPO record);

    int updateByPrimaryKey(StockUpRecordItemPO record);

    int updateBatch(List<StockUpRecordItemPO> list);

    int updateBatchSelective(List<StockUpRecordItemPO> list);

    int batchInsert(@Param("list") List<StockUpRecordItemPO> list);

    List<StockUpRecordItemPO> selectByRecordIds(List<Long> recordIds);

    int deleteByRecordId(Long recordId);
}