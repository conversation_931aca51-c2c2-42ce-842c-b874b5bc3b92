package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch;

import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/4/28
 */
public class OutStockOrderHandleLocationBO {
    /**
     * 普通订单列表
     */
    private List<OutStockOrderPO> normalOrderList;
    /**
     * 内配按产品拣货的出库单列表
     */
    private List<OutStockOrderPO> npProductOrderList;

    public OutStockOrderHandleLocationBO() {}

    public OutStockOrderHandleLocationBO(List<OutStockOrderPO> normalOrderList) {
        this.normalOrderList = normalOrderList;
    }

    public OutStockOrderHandleLocationBO(List<OutStockOrderPO> normalOrderList,
        List<OutStockOrderPO> npProductOrderList) {
        this.normalOrderList = normalOrderList;
        this.npProductOrderList = npProductOrderList;
    }

    /**
     * 获取
     *
     * @return normalOrderList
     */
    public List<OutStockOrderPO> getNormalOrderList() {
        return this.normalOrderList;
    }

    /**
     * 设置
     *
     * @param normalOrderList
     */
    public void setNormalOrderList(List<OutStockOrderPO> normalOrderList) {
        this.normalOrderList = normalOrderList;
    }

    /**
     * 获取 内配按产品拣货的出库单列表
     *
     * @return npProductOrderList 内配按产品拣货的出库单列表
     */
    public List<OutStockOrderPO> getNpProductOrderList() {
        return this.npProductOrderList;
    }

    /**
     * 设置 内配按产品拣货的出库单列表
     *
     * @param npProductOrderList 内配按产品拣货的出库单列表
     */
    public void setNpProductOrderList(List<OutStockOrderPO> npProductOrderList) {
        this.npProductOrderList = npProductOrderList;
    }

    public static OutStockOrderHandleLocationBO getDefaultNormal(List<OutStockOrderPO> normalOrderList) {
        return new OutStockOrderHandleLocationBO(normalOrderList);
    }

    public static OutStockOrderHandleLocationBO getDefault() {
        return new OutStockOrderHandleLocationBO();
    }

    public static OutStockOrderHandleLocationBO getDefaultBoth(List<OutStockOrderPO> normalOrderList,
        List<OutStockOrderPO> npProductOrderList) {
        return new OutStockOrderHandleLocationBO(normalOrderList, npProductOrderList);
    }
}
