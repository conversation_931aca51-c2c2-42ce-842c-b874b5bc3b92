package com.yijiupi.himalaya.supplychain.waves.domain.bl.itemdetail;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeInfoResultDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.OutStockOrderItemDetailRecoverBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.OutStockOrderItemDetailRecoverHandleByItemBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.utils.QuantityShareUtils;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 修复已出库的订单的detail。 根据orderItemTaskInfo或者库存变更记录
 * 
 * <AUTHOR>
 * @date 2024/9/29
 */
@Service
public class OutStockOrderItemDetailRecoverOutStockBatchBL extends OutStockOrderItemDetailRecoverBaseBL {

    @Reference
    private IBatchInventoryQueryService iBatchInventoryQueryService;
    private static final List<Byte> OUT_ORDER_STATE = Arrays.asList(OutStockOrderStateEnum.已出库.getType());

    @Override
    List<OutStockOrderPO> doFilterOrderList(OutStockOrderItemDetailRecoverBO bo) {
        // 已出库，且用库存变更记录处理
        List<OutStockOrderPO> orderList = bo.getOutStockOrderPOS().stream()
            .filter(m -> OutStockOrderStateEnum.已出库.getType() == m.getState().byteValue() && BooleanUtils.isTrue(bo.isUseInventoryRecord())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyList();
        }

        return orderList;
    }

    @Override
    protected List<OutStockOrderItemDetailPO> doRecover(List<OutStockOrderItemDetailRecoverHandleByItemBO> itemBOList) {
        List<OutStockOrderPO> outStockOrderPOList = itemBOList.stream()
            .map(OutStockOrderItemDetailRecoverHandleByItemBO::getOutStockOrderPO).collect(Collectors.toList());

        List<OutStockOrderItemDetailPO> outStockOrderItemDetailPOS = new ArrayList<>();
        for (OutStockOrderPO outStockOrderPO : outStockOrderPOList) {
            ProductStoreBatchChangeInfoQueryDTO queryDTO = new ProductStoreBatchChangeInfoQueryDTO();
            queryDTO.setOrderId(outStockOrderPO.getId().toString());
            // queryDTO.setJiupiEventType();
            queryDTO.setWarehouseId(outStockOrderPO.getWarehouseId());
            queryDTO.setOrgId(outStockOrderPO.getOrgId());
            List<ProductStoreBatchChangeInfoResultDTO> recordList =
                iBatchInventoryQueryService.findChangeRecordInfoByOrderInfo(queryDTO);
            if (CollectionUtils.isEmpty(recordList)) {
                LOGGER.info("订单{} 库存变更记录不存在", outStockOrderPO.getReforderno());
                continue;
            }
            List<ProductStoreBatchChangeInfoResultDTO> filterRecordList = filterRecordList(recordList);
            LOGGER.info("订单{} 过滤后的记录为：{}", outStockOrderPO.getReforderno(), JSON.toJSONString(filterRecordList));
            Map<Long, List<ProductStoreBatchChangeInfoResultDTO>> skuRecordGroupMap = filterRecordList.stream()
                .collect(Collectors.groupingBy(ProductStoreBatchChangeInfoResultDTO::getProductSkuId));
            List<OutStockOrderItemDetailPO> tmpList = splitDetail(skuRecordGroupMap, outStockOrderPO);
            if (CollectionUtils.isNotEmpty(tmpList)) {
                outStockOrderItemDetailPOS.addAll(tmpList);
            }
        }

        // return OutStockOrderItemDetailConverter.convertToDTO(outStockOrderItemDetailPOS);

        return outStockOrderItemDetailPOS;
    }

    // 这里有特殊场景。 比如订单项有12件，但变更记录有四条。 A货位 6 件，B 货位 -6件，A 货位 6件，A货位6件
    // 把负库存数量分摊到正的里面
    public List<ProductStoreBatchChangeInfoResultDTO>
        filterRecordList(List<ProductStoreBatchChangeInfoResultDTO> recordList) {
        Map<String, List<ProductStoreBatchChangeInfoResultDTO>> recordResultList =
            recordList.stream().collect(Collectors.groupingBy(
                k -> String.format("%s-%s-%s", k.getProductSkuId(), k.getProductSpecificationId(), k.getSecOwnerId())));
        List<ProductStoreBatchChangeInfoResultDTO> totalList = new ArrayList<>();
        for (Map.Entry<String, List<ProductStoreBatchChangeInfoResultDTO>> entry : recordResultList.entrySet()) {
            List<ProductStoreBatchChangeInfoResultDTO> valueList = entry.getValue();
            BigDecimal positiveCount =
                valueList.stream().map(ProductStoreBatchChangeInfoResultDTO::getTotalCountMinUnit)
                    .filter(totalCountMinUnit -> totalCountMinUnit.compareTo(BigDecimal.ZERO) > 0)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (positiveCount.compareTo(BigDecimal.ZERO) == 0) {
                valueList.forEach(record -> record.setTotalCountMinUnit(record.getTotalCountMinUnit().abs()));
                totalList.addAll(valueList);
                continue;
            }

            List<ProductStoreBatchChangeInfoResultDTO> negativeList = valueList.stream()
                .filter(m -> m.getTotalCountMinUnit().compareTo(BigDecimal.ZERO) <= 0).collect(Collectors.toList());
            negativeList.forEach(item -> item.setTotalCountMinUnit(item.getTotalCountMinUnit().abs()));

            List<QuantityShareUtils.CountHelper> helperList = negativeList.stream()
                .map(m -> new QuantityShareUtils.CountHelper(m.getId(), m.getTotalCountMinUnit().abs()))
                .collect(Collectors.toList());
            QuantityShareUtils.CountShareResultHelper countShareResultHelper =
                QuantityShareUtils.shareCount(helperList, positiveCount.abs());

            Map<String, ProductStoreBatchChangeInfoResultDTO> changeRecordMap =
                negativeList.stream().collect(Collectors.toMap(ProductStoreBatchChangeInfoResultDTO::getId, v -> v));

            countShareResultHelper.getShareHelperList().forEach(share -> {
                ProductStoreBatchChangeInfoResultDTO resultDTO = changeRecordMap.get(share.getId());
                resultDTO.setTotalCountMinUnit(share.getCount().abs());
                totalList.add(resultDTO);
            });
        }

        return totalList;
    }

    public List<OutStockOrderItemDetailPO> splitDetail(
        Map<Long, List<ProductStoreBatchChangeInfoResultDTO>> skuRecordGroupMap, OutStockOrderPO outStockOrderPO) {
        List<OutStockOrderItemDetailPO> totalList = new ArrayList<>();
        for (OutStockOrderItemPO outStockOrderItem : outStockOrderPO.getItems()) {
            List<ProductStoreBatchChangeInfoResultDTO> changeInfoResultDTOS =
                skuRecordGroupMap.get(outStockOrderItem.getSkuid());
            if (CollectionUtils.isEmpty(changeInfoResultDTOS)) {
                LOGGER.info("订单{} 的 订单项 {} {} 库存变更记录不存在", outStockOrderPO.getReforderno(), outStockOrderItem.getId(),
                    outStockOrderItem.getProductname());
                continue;
            }
            BigDecimal totalUnitCount =
                changeInfoResultDTOS.stream().map(ProductStoreBatchChangeInfoResultDTO::getTotalCountMinUnit)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).abs();
            // 如果一个sku只对应一个订单项
            if (totalUnitCount.compareTo(outStockOrderItem.getUnittotalcount().abs()) == 0) {
                List<OutStockOrderItemDetailPO> tmpList = changeInfoResultDTOS.stream()
                    .filter(m -> m.getTotalCountMinUnit().compareTo(BigDecimal.ZERO) != 0).map(record -> {
                        OutStockOrderItemDetailPO detailPO = new OutStockOrderItemDetailPO();

                        detailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.OUT_STOCK_ORDER_ITEM_DETAIL));
                        detailPO.setOrgId(outStockOrderItem.getOrgId());
                        detailPO.setOutStockOrderItemId(outStockOrderItem.getId());
                        detailPO.setLocationId(outStockOrderItem.getLocationId());
                        detailPO.setLocationName(outStockOrderItem.getLocationName());
                        detailPO.setBatchTime(outStockOrderItem.getBatchTime());
                        detailPO.setProductionDate(outStockOrderItem.getProductionDate());

                        detailPO.setUnitTotalCount(record.getTotalCountMinUnit().abs());
                        detailPO.setProductSpecificationId(outStockOrderItem.getProductSpecificationId());
                        detailPO.setOwnerId(record.getOwnerId());
                        detailPO.setSecOwnerId(record.getSecOwnerId());
                        detailPO.setOutStockUnitTotalCount(detailPO.getUnitTotalCount());
                        return detailPO;
                    }).collect(Collectors.toList());

                totalList.addAll(tmpList);
            } else {
                List<QuantityShareUtils.CountHelper> helperList = changeInfoResultDTOS.stream()
                    .map(m -> new QuantityShareUtils.CountHelper(m.getId(), m.getTotalCountMinUnit().abs()))
                    .collect(Collectors.toList());
                QuantityShareUtils.CountShareResultHelper countShareResultHelper =
                    QuantityShareUtils.shareCount(helperList, outStockOrderItem.getUnittotalcount());

                Map<String, ProductStoreBatchChangeInfoResultDTO> changeRecordMap = changeInfoResultDTOS.stream()
                    .collect(Collectors.toMap(ProductStoreBatchChangeInfoResultDTO::getId, v -> v));

                countShareResultHelper.getShareHelperList().stream()
                    .filter(QuantityShareUtils.CountHelper::isHaveChanged).forEach(share -> {
                        ProductStoreBatchChangeInfoResultDTO productStoreBatchChangeInfoResultDTO =
                            changeRecordMap.get(share.getId());

                        OutStockOrderItemDetailPO detailPO = new OutStockOrderItemDetailPO();

                        detailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.OUT_STOCK_ORDER_ITEM_DETAIL));
                        detailPO.setOrgId(outStockOrderItem.getOrgId());
                        detailPO.setOutStockOrderItemId(outStockOrderItem.getId());
                        detailPO.setLocationId(outStockOrderItem.getLocationId());
                        detailPO.setLocationName(outStockOrderItem.getLocationName());
                        detailPO.setBatchTime(outStockOrderItem.getBatchTime());
                        detailPO.setProductionDate(outStockOrderItem.getProductionDate());

                        detailPO.setUnitTotalCount(productStoreBatchChangeInfoResultDTO.getTotalCountMinUnit().abs()
                            .subtract(share.getCount()));
                        detailPO.setProductSpecificationId(outStockOrderItem.getProductSpecificationId());
                        detailPO.setOwnerId(productStoreBatchChangeInfoResultDTO.getOwnerId());
                        detailPO.setSecOwnerId(productStoreBatchChangeInfoResultDTO.getSecOwnerId());
                        detailPO.setOutStockUnitTotalCount(detailPO.getUnitTotalCount());

                        productStoreBatchChangeInfoResultDTO.setTotalCountMinUnit(share.getCount());

                        totalList.add(detailPO);
                    });
            }
        }

        return totalList;
    }
}
