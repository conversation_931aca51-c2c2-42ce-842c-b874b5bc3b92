package com.yijiupi.himalaya.supplychain.controller;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.ICSTaskManageBL;
import com.yijiupi.himalaya.supplychain.waves.dto.agv.AgvTaskCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.agv.AgvTaskMoveDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.base.BaseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
@RestController
public class ICSTaskController {

    @Autowired
    private ICSTaskManageBL icsTaskManageBL;

    /**
     * 满载去卸货
     *
     * @param agvTaskCompleteDTO
     * @return
     */
    @RequestMapping(value = "agvTask/completeAgvTask", method = RequestMethod.POST)
    public BaseResult completeICSTask(@RequestBody AgvTaskCompleteDTO agvTaskCompleteDTO) {
        icsTaskManageBL.completeICSTask(agvTaskCompleteDTO);
        return BaseResult.getSuccessResult();
    }

    /**
     * 移动agv小车
     *
     * @param agvTaskMoveDTO
     * @return
     */
    @RequestMapping(value = "agvTask/moveAgvTask", method = RequestMethod.POST)
    public BaseResult moveICSTask(@RequestBody AgvTaskMoveDTO agvTaskMoveDTO) {
        icsTaskManageBL.moveICSTask(agvTaskMoveDTO);
        return BaseResult.getSuccessResult();
    }
}
