package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.yijiupi.himalaya.supplychain.ordercenter.enums.RejectTypeEnum;
import com.yijiupi.himalaya.supplychain.pushorder.dto.RejectOrderDTO;

public class OrderCenterConverter {

    public static List<RejectOrderDTO> convertToRejectOrderDTOList(List<String> omsOrderIdList, Integer opUserId) {
        List<RejectOrderDTO> rejectOrderList = new ArrayList<>();
        omsOrderIdList.stream().forEach(omsOrderId -> {
            RejectOrderDTO rejectOrderDTO = new RejectOrderDTO();
            rejectOrderDTO.setOrderId(Long.valueOf(omsOrderId));
            rejectOrderDTO.setRejectType(RejectTypeEnum.全部缺货.getType());
            rejectOrderDTO.setOptUserId(opUserId != null ? String.valueOf(opUserId) : "");
            rejectOrderDTO.setHandleTime(new Date());
            rejectOrderList.add(rejectOrderDTO);
        });

        return rejectOrderList;
    }
}
