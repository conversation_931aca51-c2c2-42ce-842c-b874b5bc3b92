package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title: BatchTaskItemPackageReviewDetailDTO
 * @description:
 * @date 2023-01-05 09:47
 */
public class BatchTaskItemPackageReviewDetailDTO implements Serializable {

    /**
     * 集货位id
     */
    private Long sowLocationId;

    /**
     * 集货位名称
     */
    private String sowLocationName;

    /**
     * 仓库Id
     */
    private Integer warehouseId;
    /**
     * 集货信息
     */
    private BatchTaskItemPickCompleteCollectDetailDTO collectDetail;
    /**
     * 打包信息
     */
    private BatchTaskItemPickCompletePackageDetailDTO packageDetail;
    /**
     * 产品skuId
     */
    private Long skuId;

    /**
     * 获取 集货位id
     *
     * @return sowLocationId 集货位id
     */
    public Long getSowLocationId() {
        return this.sowLocationId;
    }

    /**
     * 设置 集货位id
     *
     * @param sowLocationId 集货位id
     */
    public void setSowLocationId(Long sowLocationId) {
        this.sowLocationId = sowLocationId;
    }

    /**
     * 获取 集货位名称
     *
     * @return sowLocationName 集货位名称
     */
    public String getSowLocationName() {
        return this.sowLocationName;
    }

    /**
     * 设置 集货位名称
     *
     * @param sowLocationName 集货位名称
     */
    public void setSowLocationName(String sowLocationName) {
        this.sowLocationName = sowLocationName;
    }

    /**
     * 获取 仓库Id
     *
     * @return warehouseId 仓库Id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库Id
     *
     * @param warehouseId 仓库Id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 产品skuId
     *
     * @return skuId 产品skuId
     */
    public Long getSkuId() {
        return this.skuId;
    }

    /**
     * 设置 产品skuId
     *
     * @param skuId 产品skuId
     */
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    /**
     * 获取 集货信息
     *
     * @return collectDetail 集货信息
     */
    public BatchTaskItemPickCompleteCollectDetailDTO getCollectDetail() {
        return this.collectDetail;
    }

    /**
     * 设置 集货信息
     *
     * @param collectDetail 集货信息
     */
    public void setCollectDetail(BatchTaskItemPickCompleteCollectDetailDTO collectDetail) {
        this.collectDetail = collectDetail;
    }

    /**
     * 获取 打包信息
     *
     * @return packageDetail 打包信息
     */
    public BatchTaskItemPickCompletePackageDetailDTO getPackageDetail() {
        return this.packageDetail;
    }

    /**
     * 设置 打包信息
     *
     * @param packageDetail 打包信息
     */
    public void setPackageDetail(BatchTaskItemPickCompletePackageDetailDTO packageDetail) {
        this.packageDetail = packageDetail;
    }
}
