package com.yijiupi.himalaya.supplychain.waves.util;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/3/12
 */
// public final class ProcessLackUtils {
//
// // 具体缺货逻辑
// public static void processLack(List<ItemLackHelperBO> helperList, LackInfo lackInfo, int i) {
// if (lackInfo.lackCount.compareTo(BigDecimal.ZERO) == 0 || i >= helperList.size()) {
// return;
// }
// ItemLackHelperBO itemLackHelperBO = helperList.get(i);
// BigDecimal beforeLackUnitTotalCount = itemLackHelperBO.getUnitTotalCount();
// BigDecimal afterLackUnitTotalCount = getCurrentUnitTotalCount(helperList.get(i), lackInfo.lackCount);
// BigDecimal currentLackUnitTotalCount = beforeLackUnitTotalCount.subtract(afterLackUnitTotalCount);
//
// lackInfo.lackCount = lackInfo.lackCount.subtract(currentLackUnitTotalCount);
// ItemLackHelperBO lackHelperBO = helperList.get(i);
//
// lackHelperBO.setUnitTotalCount(afterLackUnitTotalCount);
// lackHelperBO.setLackUnitTotalCount(currentLackUnitTotalCount);
//
// processLack(helperList, lackInfo, i + 1);
// }
//
// // 计算订单缺货后的数量
// private static BigDecimal getCurrentUnitTotalCount(ItemLackHelperBO lackHelperBO, BigDecimal lackCount) {
// BigDecimal afterLackCount = lackHelperBO.getUnitTotalCount().subtract(lackCount);
// // 如果缺货数量是负数，即 还原订单
// if (lackCount.compareTo(BigDecimal.ZERO) < 0) {
// // 如果没有原始数量，直接返回
// if (Objects.isNull(lackHelperBO.getOriginalUnitTotalCount())) {
// return afterLackCount;
// }
// // 如果有原始数量，则和原始数量比较，大于原始数量，返回原始数量
// if (afterLackCount.compareTo(lackHelperBO.getOriginalUnitTotalCount()) > 0) {
// return lackHelperBO.getOriginalUnitTotalCount();
// }
//
// return afterLackCount;
// }
// // 缺货数量是正数，即正常缺货
// if (lackCount.compareTo(BigDecimal.ZERO) > 0) {
// // 如果把原始单缺成负数，返回 0
// if (lackCount.compareTo(BigDecimal.ZERO) < 0) {
// return BigDecimal.ZERO;
// }
//
// // 否则 返回缺货后的数量
// return lackCount;
// }
//
// return lackHelperBO.getUnitTotalCount();
// }
//
// public static class LackInfo {
// BigDecimal lackCount;
//
// public LackInfo(BigDecimal lackCount) {
// this.lackCount = lackCount;
// }
// }
//
// }
