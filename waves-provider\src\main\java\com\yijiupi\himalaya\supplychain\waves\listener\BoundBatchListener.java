package com.yijiupi.himalaya.supplychain.waves.listener;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BoundBatchBL;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * 入库批次的MQ监听
 * 
 * <AUTHOR>
 * @Date 2022/1/19
 */
@Service
public class BoundBatchListener {
    private static final Logger LOG = LoggerFactory.getLogger(ScheduleJobListener.class);

    @Autowired
    private BoundBatchBL boundBatchBL;

    @RabbitListener(queues = "${mq.supplychain.outBoundBatch.updateStorageLocation}")
    public void updateStorageLocation(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            String messageId = message.getMessageProperties().getMessageId();
            LOG.info("更新出库批次的出库位>>>【mq.supplychain.boundBatch.updateStorageLocation】[{}]{}", messageId, json);
            List<OutStockOrderPO> outOrderPOList = JSON.parseArray(json, OutStockOrderPO.class);
            boundBatchBL.updateOutboundBatchLocation(outOrderPOList);
        } catch (Exception e) {
            LOG.error("更新出库批次的出库位失败", e);
        }
    }
}
