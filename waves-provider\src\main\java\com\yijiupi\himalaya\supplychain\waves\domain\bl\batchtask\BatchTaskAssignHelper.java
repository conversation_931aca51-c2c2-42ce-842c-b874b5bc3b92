package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskPickPatternEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import com.yijiupi.himalaya.supplychain.wcs.dto.task.DPSTaskBatchModHumanPickDTO;
import com.yijiupi.himalaya.supplychain.wcs.dto.task.DPSTaskModHumanPickDTO;
import com.yijiupi.himalaya.supplychain.wcs.dto.task.DPSTaskModPickBatchResultDTO;
import com.yijiupi.himalaya.supplychain.wcs.dto.task.DPSTaskModPickResultDTO;
import com.yijiupi.himalaya.supplychain.wcs.service.task.IDPSTaskService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-11-11 11:32
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class BatchTaskAssignHelper {

    @Resource
    private BatchTaskMapper batchTaskMapper;

    @Resource
    private BatchTaskItemMapper batchTaskItemMapper;

    @Reference
    private IDPSTaskService dpsTaskService;

    /**
     * 处理机器人拣货
     *
     * @param tasks         db 里的拣货任务
     * @param BatchTaskDTOS 指派拣货任务入参
     * @param optUserName   操作人
     * @return 操作成功任务条数
     */
    public Integer handleRobot(List<BatchTaskPO> tasks, List<BatchTaskDTO> BatchTaskDTOS, String optUserName) {
        List<BatchTaskPO> robotPickTaskList = tasks.stream().filter(m -> Objects.nonNull(m.getPickPattern()))
                .filter(m -> BatchTaskPickPatternEnum.机器人拣货.getType().equals(m.getPickPattern()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(robotPickTaskList)) {
            return 0;
        }
        Integer cityId = tasks.get(0).getOrgId() != null ? Integer.valueOf(tasks.get(0).getOrgId()) : null;
        Integer sorterId = BatchTaskDTOS.get(0).getSorterId();
        String sorter = BatchTaskDTOS.get(0).getSorter();
        List<DPSTaskModHumanPickDTO> humanPickDTOList = robotPickTaskList.stream().map(m -> {
            DPSTaskModHumanPickDTO humanPickDTO = new DPSTaskModHumanPickDTO();
            humanPickDTO.setOptUserName(optUserName);
            humanPickDTO.setBatchTaskId(m.getId());
            return humanPickDTO;
        }).collect(Collectors.toList());
        DPSTaskBatchModHumanPickDTO dto = new DPSTaskBatchModHumanPickDTO();
        dto.setTaskList(humanPickDTOList);
        dto.setOptUserName(optUserName);
        DPSTaskModPickBatchResultDTO batchResultDTO = dpsTaskService.batchModHumanPick(dto);
        if (CollectionUtils.isEmpty(batchResultDTO.getSuccessList())) {
            return 0;
        }
        Map<Long, DPSTaskModPickResultDTO> resultMap = batchResultDTO.getSuccessList().stream()
                .collect(Collectors.toMap(DPSTaskModPickResultDTO::getBatchTaskId, v -> v));
        List<BatchTaskPO> updateTaskList = robotPickTaskList.stream().filter(m -> Objects.nonNull(resultMap.get(Long.valueOf(m.getId())))).map(m -> {
            BatchTaskPO batchTaskPO = new BatchTaskPO();
            batchTaskPO.setId(m.getId());
            batchTaskPO.setSorterId(sorterId);
            batchTaskPO.setSorter(sorter);
            batchTaskPO.setPickPattern(BatchTaskPickPatternEnum.人机混拣.getType());
            return batchTaskPO;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateTaskList)) {
            return 0;
        }
        updateTaskList.forEach(m -> batchTaskMapper.updateByPrimaryKeySelective(m));
        // batchTaskMapper.updateBatch(updateTaskList, cityId);
        List<String> successBatchTaskIds = batchResultDTO.getSuccessList().stream()
                .map(DPSTaskModPickResultDTO::getBatchTaskId).map(String::valueOf).collect(Collectors.toList());
        List<String> transferTaskItemIds = batchResultDTO.getSuccessList().stream().filter(m -> CollectionUtils.isNotEmpty(m.getTransferTaskItemIds()))
                .flatMap(m -> m.getTransferTaskItemIds().stream()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(transferTaskItemIds)) {
            List<BatchTaskItemPO> batchTaskItemPOList = batchTaskItemMapper.listBatchTaskItemByTaskId(successBatchTaskIds, cityId);
            if (CollectionUtils.isEmpty(batchTaskItemPOList)) {
                return 0;
            }
            transferTaskItemIds = batchTaskItemPOList.stream().map(BatchTaskItemPO::getId).collect(Collectors.toList());
        }
        List<BatchTaskItemPO> itemUpdateList = transferTaskItemIds.stream().map(item -> {
            BatchTaskItemPO updateItemPO = new BatchTaskItemPO();
            updateItemPO.setCompleteUser(sorter);
            updateItemPO.setCompleteUserId(sorterId);
            updateItemPO.setId(item);
            updateItemPO.setLargePickPattern(BatchTaskItemLargePickPatternConstants.LARGE_PICK_SMALL_HUMAN);
            return updateItemPO;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(itemUpdateList)) {
            Lists.partition(itemUpdateList, 250).forEach(batchTaskItemMapper::updateBatchTaskItemSorterBatch);
        }
        List<String> batchTaskIs = updateTaskList.stream().map(BatchTaskPO::getId).collect(Collectors.toList());
        // List<BatchTaskItemPO> itemList = batchTaskItemMapper.findBatchTaskItemDTOListByTaskIds(batchTaskIs);
        return batchTaskItemMapper.findBatchTaskItemCountByTaskIds(batchTaskIs);
    }

    /**
     * 处理普通拣货员拣货
     *
     * @param lstBatchTasks db 里的拣货任务
     * @param batchTaskDTOS 指派拣货任务入参
     * @param optUserName   操作人
     * @return 操作成功任务条数
     */
    public Integer handleNormal(List<BatchTaskPO> lstBatchTasks, List<BatchTaskDTO> batchTaskDTOS, String optUserName) {
        List<BatchTaskPO> normalPickTaskList = lstBatchTasks.stream()
                .filter(m -> Objects.isNull(m.getPickPattern()) || !BatchTaskPickPatternEnum.机器人拣货.getType().equals(m.getPickPattern()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(normalPickTaskList)) {
            return 0;
        }
        Integer cityId = lstBatchTasks.get(0).getOrgId() != null ? Integer.valueOf(lstBatchTasks.get(0).getOrgId()) : null;
        Integer sorterId = batchTaskDTOS.get(0).getSorterId();
        String sorter = batchTaskDTOS.get(0).getSorter();
        List<BatchTaskPO> updateTaskList = normalPickTaskList.stream().map(m -> {
            BatchTaskPO batchTaskPO = new BatchTaskPO();
            BeanUtils.copyProperties(m, batchTaskPO);
            batchTaskPO.setSorterId(sorterId);
            batchTaskPO.setSorter(sorter);
            return batchTaskPO;
        }).collect(Collectors.toList());
        List<String> batchTaskIs = normalPickTaskList.stream().map(BatchTaskPO::getId).collect(Collectors.toList());
        batchTaskMapper.updateBatch(updateTaskList, cityId);
        // SCM-18334 【WMS】完成拣货操作人记录有误, 指派拣货员时不处理 已完成的 item
        List<BatchTaskItemPO> itemUpdateList = batchTaskItemMapper.findBatchTaskItemDTOListByTaskIds(batchTaskIs).stream()
                .filter(it -> !TaskStateEnum.已完成.valueEquals(it.getTaskState())).map(item -> {
                    BatchTaskItemPO updateItemPO = new BatchTaskItemPO();
                    updateItemPO.setCompleteUser(sorter);
                    updateItemPO.setCompleteUserId(sorterId);
                    updateItemPO.setId(item.getId());
                    return updateItemPO;
                }).collect(Collectors.toList());
        Lists.partition(itemUpdateList, 250).forEach(batchTaskItemMapper::updateBatchTaskItemSorterBatch);
        // List<BatchTaskItemPO> itemList = batchTaskItemMapper.findBatchTaskItemDTOListByTaskIds(batchTaskIs);
        return batchTaskItemMapper.findBatchTaskItemCountByTaskIds(batchTaskIs);
    }

}
