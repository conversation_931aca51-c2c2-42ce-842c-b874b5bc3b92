<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper">
    <!-- 结果映射 -->
    <resultMap id="findOutStockOrderResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="REFORDERNO" property="reforderno" jdbcType="VARCHAR"/>
        <result column="ORG_ID" property="orgId" jdbcType="INTEGER"/>
        <result column="FromCityId" property="fromCityId" jdbcType="INTEGER"/>
        <result column="BATCHNO" property="batchno" jdbcType="VARCHAR"/>
        <result column="BATCH_ID" property="batchId" jdbcType="VARCHAR"/>
        <result column="STATE" property="state" jdbcType="TINYINT"/>
        <result column="COMPANYCODE" property="companycode" jdbcType="VARCHAR"/>
        <result column="ORDERTYPE" property="ordertype" jdbcType="TINYINT"/>
        <result column="ORDERAMOUNT" property="orderamount" jdbcType="DECIMAL"/>
        <result column="SKUCOUNT" property="skucount" jdbcType="INTEGER"/>
        <result column="PACKAGEAMOUNT" property="packageamount" jdbcType="DECIMAL"/>
        <result column="UNITAMOUNT" property="unitamount" jdbcType="DECIMAL"/>
        <result column="WAREHOUSE_ID" property="warehouseId" jdbcType="VARCHAR"/>
        <result column="USERNAME" property="username" jdbcType="VARCHAR"/>
        <result column="SHOPNAME" property="shopname" jdbcType="VARCHAR"/>
        <result column="MOBILENO" property="mobileno" jdbcType="VARCHAR"/>
        <result column="DETAILADDRESS" property="detailaddress" jdbcType="VARCHAR"/>
        <result column="AddressId" property="addressId" jdbcType="INTEGER"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="COUNTY" property="county" jdbcType="VARCHAR"/>
        <result column="STREET" property="street" jdbcType="VARCHAR"/>
        <result column="parterId" property="parterId" jdbcType="BIGINT"/>
        <result column="parterName" property="parterName" jdbcType="VARCHAR"/>
        <result column="ORDERCREATETIME" property="ordercreatetime" jdbcType="TIMESTAMP"/>
        <result column="PICKTIME" property="picktime" jdbcType="TIMESTAMP"/>
        <result column="OUTSTOCKTIME" property="outstocktime" jdbcType="TIMESTAMP"/>
        <result column="OUTSTOCKUSER" property="outstockuser" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="CREATEUSER" property="createuser" jdbcType="VARCHAR"/>
        <result column="CREATETIME" property="createtime" jdbcType="TIMESTAMP"/>
        <result column="LASTUPDATEUSER" property="lastupdateuser" jdbcType="VARCHAR"/>
        <result column="LASTUPDATETIME" property="lastupdatetime" jdbcType="TIMESTAMP"/>
        <result column="Area_Id" property="areaId" jdbcType="BIGINT"/>
        <result column="AreaName" property="areaName" jdbcType="VARCHAR"/>
        <result column="Route_Id" property="routeId" jdbcType="VARCHAR"/>
        <result column="RouteName" property="routeName" jdbcType="VARCHAR"/>
        <result column="RouteSequence" property="routeSequence" jdbcType="INTEGER"/>
        <result column="DeliveryMarkState" property="deliveryMarkState" jdbcType="INTEGER"/>
        <result column="OrderSequence" property="orderSequence" jdbcType="INTEGER"/>
        <result column="CreateAllocation" property="createAllocation" jdbcType="TINYINT"/>
        <result column="PackageAttribute" property="packageAttribute" jdbcType="TINYINT"/>
        <result column="DeliveryMode" property="deliveryMode" jdbcType="TINYINT"/>
        <result column="ExpectedOutStockTime" property="expectedOutStockTime" jdbcType="TIMESTAMP"/>
        <result column="Business_Id" property="businessId" jdbcType="VARCHAR"/>
        <result column="BusinessNo" property="businessNo" jdbcType="VARCHAR"/>
        <result column="Owner_Id" property="ownerId" jdbcType="BIGINT"/>
        <result column="OwnerName" property="ownerName" jdbcType="VARCHAR"/>
        <result column="AssociatedBusinessNo" property="associatedBusinessNo" jdbcType="VARCHAR"/>
        <result column="AssociatedBusiness_Id" property="associatedBusinessId" jdbcType="VARCHAR"/>
        <result column="AllotType" property="allotType" jdbcType="TINYINT"/>
        <result column="outBoundType" property="outBoundType" jdbcType="TINYINT"/>
        <result column="OrderSourceType" property="orderSourceType" jdbcType="TINYINT"/>
        <result column="Priority" property="priority" jdbcType="INTEGER"/>
        <collection property="items" ofType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO">
            <id column="itemID" property="id" jdbcType="BIGINT"/>
            <result column="ORG_ID" property="orgId" jdbcType="INTEGER"/>
            <result column="BATCHTASKNO" property="batchtaskno" jdbcType="VARCHAR"/>
            <result column="OUTSTOCKORDER_ID" property="outstockorderId" jdbcType="BIGINT"/>
            <result column="BATCHTASK_ID" property="batchtaskId" jdbcType="VARCHAR"/>
            <result column="REFORDER_ID" property="reforderId" jdbcType="VARCHAR"/>
            <result column="PRODUCTNAME" property="productname" jdbcType="VARCHAR"/>
            <result column="SKUID" property="skuid" jdbcType="VARCHAR"/>
            <result column="PRODUCTBRAND" property="productbrand" jdbcType="VARCHAR"/>
            <result column="CATEGORYNAME" property="categoryname" jdbcType="VARCHAR"/>
            <result column="SPECNAME" property="specname" jdbcType="VARCHAR"/>
            <result column="SPECQUANTITY" property="specquantity" jdbcType="DECIMAL"/>
            <result column="SALESPEC" property="salespec" jdbcType="VARCHAR"/>
            <result column="SALESPECQUANTITY" property="salespecquantity" jdbcType="DECIMAL"/>
            <result column="PACKAGENAME" property="packagename" jdbcType="VARCHAR"/>
            <result column="PACKAGECOUNT" property="packagecount" jdbcType="DECIMAL"/>
            <result column="UNITNAME" property="unitname" jdbcType="VARCHAR"/>
            <result column="UNITCOUNT" property="unitcount" jdbcType="DECIMAL"/>
            <result column="UNITTOTALCOUNT" property="unittotalcount" jdbcType="DECIMAL"/>
            <result column="SALEMODEL" property="salemodel" jdbcType="TINYINT"/>
            <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
            <result column="CREATETIME" property="createtime" jdbcType="TIMESTAMP"/>
            <result column="SOURCE" property="source" jdbcType="TINYINT"/>
            <result column="CHANNEL" property="channel" jdbcType="TINYINT"/>
            <result column="TotalAmount" property="totalAmount" jdbcType="DECIMAL"/>
            <result column="Item_Owner_Id" property="ownerId" jdbcType="BIGINT"/>
            <result column="SecOwner_Id" property="secOwnerId" jdbcType="BIGINT"/>
            <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
            <result column="ControlConfig_Id" property="controlConfigId" jdbcType="BIGINT"/>
            <result column="LocationId" property="locationId" jdbcType="BIGINT"/>
            <result column="LocationName" property="locationName" jdbcType="VARCHAR"/>
            <result column="BatchTaskItem_Id" property="batchTaskItemId" jdbcType="VARCHAR"/>
            <result column="BusinessItemId" property="businessItemId" jdbcType="VARCHAR"/>
            <result column="SowTask_Id" property="sowTaskId" jdbcType="VARCHAR"/>
            <result column="SowTaskNo" property="sowTaskNo" jdbcType="VARCHAR"/>
            <result column="SowOrder_Id" property="sowOrderId" jdbcType="VARCHAR"/>
            <result column="SowTaskItem_Id" property="sowTaskItemId" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <resultMap id="findOutStockOrderDetailResultMap"
               type="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="REFORDERNO" property="reforderno" jdbcType="VARCHAR"/>
        <result column="ORG_ID" property="orgId" jdbcType="INTEGER"/>
        <result column="FromCityId" property="fromCityId" jdbcType="INTEGER"/>
        <result column="BATCHNO" property="batchno" jdbcType="VARCHAR"/>
        <result column="BATCH_ID" property="batchId" jdbcType="VARCHAR"/>
        <result column="STATE" property="state" jdbcType="TINYINT"/>
        <result column="COMPANYCODE" property="companycode" jdbcType="VARCHAR"/>
        <result column="ORDERTYPE" property="ordertype" jdbcType="TINYINT"/>
        <result column="ORDERAMOUNT" property="orderamount" jdbcType="DECIMAL"/>
        <result column="SKUCOUNT" property="skucount" jdbcType="INTEGER"/>
        <result column="PACKAGEAMOUNT" property="packageamount" jdbcType="DECIMAL"/>
        <result column="UNITAMOUNT" property="unitamount" jdbcType="DECIMAL"/>
        <result column="WAREHOUSE_ID" property="warehouseId" jdbcType="INTEGER"/>
        <result column="USERNAME" property="username" jdbcType="VARCHAR"/>
        <result column="SHOPNAME" property="shopname" jdbcType="VARCHAR"/>
        <result column="MOBILENO" property="mobileno" jdbcType="VARCHAR"/>
        <result column="DETAILADDRESS" property="detailaddress" jdbcType="VARCHAR"/>
        <result column="AddressId" property="addressId" jdbcType="INTEGER"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="COUNTY" property="county" jdbcType="VARCHAR"/>
        <result column="STREET" property="street" jdbcType="VARCHAR"/>
        <result column="parterId" property="parterId" jdbcType="BIGINT"/>
        <result column="parterName" property="parterName" jdbcType="VARCHAR"/>
        <result column="ORDERCREATETIME" property="ordercreatetime" jdbcType="TIMESTAMP"/>
        <result column="PICKTIME" property="picktime" jdbcType="TIMESTAMP"/>
        <result column="OUTSTOCKTIME" property="outstocktime" jdbcType="TIMESTAMP"/>
        <result column="OUTSTOCKUSER" property="outstockuser" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="CREATEUSER" property="createuser" jdbcType="VARCHAR"/>
        <result column="CREATETIME" property="createtime" jdbcType="TIMESTAMP"/>
        <result column="LASTUPDATEUSER" property="lastupdateuser" jdbcType="VARCHAR"/>
        <result column="LASTUPDATETIME" property="lastupdatetime" jdbcType="TIMESTAMP"/>
        <result column="Area_Id" property="areaId" jdbcType="BIGINT"/>
        <result column="AreaName" property="areaName" jdbcType="VARCHAR"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="Route_Id" property="routeId" jdbcType="VARCHAR"/>
        <result column="RouteName" property="routeName" jdbcType="VARCHAR"/>
        <result column="RouteSequence" property="routeSequence" jdbcType="INTEGER"/>
        <result column="DeliveryMarkState" property="deliveryMarkState" jdbcType="INTEGER"/>
        <result column="OrderSequence" property="orderSequence" jdbcType="INTEGER"/>
        <result column="CreateAllocation" property="createAllocation" jdbcType="TINYINT"/>
        <result column="PackageAttribute" property="packageAttribute" jdbcType="TINYINT"/>
        <result column="DeliveryMode" property="deliveryMode" jdbcType="TINYINT"/>
        <result column="ExpectedOutStockTime" property="expectedOutStockTime" jdbcType="TIMESTAMP"/>
        <result column="Business_Id" property="businessId" jdbcType="VARCHAR"/>
        <result column="BusinessNo" property="businessNo" jdbcType="VARCHAR"/>
        <result column="Owner_Id" property="ownerId" jdbcType="BIGINT"/>
        <result column="OwnerName" property="ownerName" jdbcType="VARCHAR"/>
        <result column="AssociatedBusinessNo" property="associatedBusinessNo" jdbcType="VARCHAR"/>
        <result column="AssociatedBusiness_Id" property="associatedBusinessId" jdbcType="VARCHAR"/>
        <result column="AllotType" property="allotType" jdbcType="TINYINT"/>
        <result column="CrossWareHouse" property="crossWareHouse" jdbcType="TINYINT"/>
        <result column="boundNo" property="boundNo" jdbcType="VARCHAR"/>
        <result column="Purchaser_Id" property="purchaserId" jdbcType="VARCHAR"/>
        <result column="OriginalPackageAmount" property="originalPackageAmount" jdbcType="DECIMAL"/>
        <result column="OriginalUnitAmount" property="originalUnitAmount" jdbcType="DECIMAL"/>
        <result column="OrderSourceType" property="orderSourceType" jdbcType="TINYINT"/>
        <result column="OutBoundType" property="outBoundType" jdbcType="TINYINT"/>
        <result column="lastupdateuser" property="lastupdateuser" jdbcType="VARCHAR"/>
        <result column="WarehouseAllocationType" property="warehouseAllocationType" jdbcType="INTEGER"/>

        <collection property="items" ofType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO">
            <id column="itemID" property="id" jdbcType="BIGINT"/>
            <result column="ORG_ID" property="orgId" jdbcType="INTEGER"/>
            <result column="BATCHTASKNO" property="batchtaskno" jdbcType="VARCHAR"/>
            <result column="OUTSTOCKORDER_ID" property="outstockorderId" jdbcType="BIGINT"/>
            <result column="BATCHTASK_ID" property="batchtaskId" jdbcType="VARCHAR"/>
            <result column="REFORDER_ID" property="reforderId" jdbcType="VARCHAR"/>
            <result column="PRODUCTNAME" property="productname" jdbcType="VARCHAR"/>
            <result column="SKUID" property="skuid" jdbcType="VARCHAR"/>
            <result column="PRODUCTBRAND" property="productbrand" jdbcType="VARCHAR"/>
            <result column="CATEGORYNAME" property="categoryname" jdbcType="VARCHAR"/>
            <result column="SPECNAME" property="specname" jdbcType="VARCHAR"/>
            <result column="SPECQUANTITY" property="specquantity" jdbcType="DECIMAL"/>
            <result column="SALESPEC" property="salespec" jdbcType="VARCHAR"/>
            <result column="SALESPECQUANTITY" property="salespecquantity" jdbcType="DECIMAL"/>
            <result column="PACKAGENAME" property="packagename" jdbcType="VARCHAR"/>
            <result column="PACKAGECOUNT" property="packagecount" jdbcType="DECIMAL"/>
            <result column="UNITNAME" property="unitname" jdbcType="VARCHAR"/>
            <result column="UNITCOUNT" property="unitcount" jdbcType="DECIMAL"/>
            <result column="UNITTOTALCOUNT" property="unittotalcount" jdbcType="DECIMAL"/>
            <result column="SALEMODEL" property="salemodel" jdbcType="TINYINT"/>
            <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
            <result column="CREATETIME" property="createtime" jdbcType="TIMESTAMP"/>
            <result column="SOURCE" property="source" jdbcType="TINYINT"/>
            <result column="CHANNEL" property="channel" jdbcType="TINYINT"/>
            <result column="TotalAmount" property="totalAmount" jdbcType="DECIMAL"/>
            <result column="Item_Owner_Id" property="ownerId" jdbcType="BIGINT"/>
            <result column="SecOwner_Id" property="secOwnerId" jdbcType="BIGINT"/>
            <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
            <result column="ControlConfig_Id" property="controlConfigId" jdbcType="BIGINT"/>
            <result column="LocationId" property="locationId" jdbcType="BIGINT"/>
            <result column="LocationName" property="locationName" jdbcType="VARCHAR"/>
            <result column="BatchTaskItem_Id" property="batchTaskItemId" jdbcType="VARCHAR"/>
            <result column="BusinessItemId" property="businessItemId" jdbcType="VARCHAR"/>
            <result column="SowTask_Id" property="sowTaskId" jdbcType="BIGINT"/>
            <result column="SowTaskItem_Id" property="sowTaskItemId" jdbcType="BIGINT"/>
            <result column="OriginalUnitTotalCount" property="originalUnitTotalCount" jdbcType="DECIMAL"/>
            <result column="IsGift" property="isGift" jdbcType="TINYINT"/>
            <result column="IsAdvent" property="isAdvent" jdbcType="TINYINT"/>
            <collection property="itemDetails"
                        ofType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO">
                <id column="itemDetailID" property="id" jdbcType="BIGINT"/>
                <result column="detail_orgId" property="orgId" jdbcType="INTEGER"/>
                <result column="OutStockOrderItem_Id" property="outStockOrderItemId" jdbcType="BIGINT"/>
                <result column="detail_locationId" property="locationId" jdbcType="BIGINT"/>
                <result column="detail_locationName" property="locationName" jdbcType="VARCHAR"/>
                <result column="detail_productionDate" property="productionDate" jdbcType="TIMESTAMP"/>
                <result column="detail_batchTime" property="batchTime" jdbcType="TIMESTAMP"/>
                <result column="detail_unitTotalCount" property="unitTotalCount" jdbcType="DECIMAL"/>
                <result column="detail_productSpecificationId" property="productSpecificationId" jdbcType="BIGINT"/>
                <result column="detail_ownerId" property="ownerId" jdbcType="BIGINT"/>
                <result column="detail_secOwnerId" property="secOwnerId" jdbcType="BIGINT"/>
                <result column="detail_batchAttributeInfoNo" property="batchAttributeInfoNo" jdbcType="VARCHAR"/>
            </collection>
        </collection>
    </resultMap>

    <!--  所有字段的 resultMap  -->
    <resultMap id="OutStockOrderAllResultMap"
               type="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="reforderno" column="RefOrderNo" jdbcType="VARCHAR"/>
        <result property="boundNo" column="boundNo" jdbcType="VARCHAR"/>
        <result property="orgId" column="Org_Id" jdbcType="INTEGER"/>
        <result property="batchno" column="BatchNo" jdbcType="VARCHAR"/>
        <result property="batchId" column="Batch_Id" jdbcType="VARCHAR"/>
        <result property="state" column="State" jdbcType="TINYINT"/>
        <result property="companycode" column="CompanyCode" jdbcType="VARCHAR"/>
        <result property="ordertype" column="OrderType" jdbcType="TINYINT"/>
        <result property="outBoundType" column="outBoundType" jdbcType="TINYINT"/>
        <result property="orderamount" column="OrderAmount" jdbcType="DECIMAL"/>
        <result property="packageamount" column="PayableAmount" jdbcType="DECIMAL"/>
        <result property="skucount" column="SkuCount" jdbcType="INTEGER"/>
        <result property="packageamount" column="PackageAmount" jdbcType="DECIMAL"/>
        <result property="unitamount" column="UnitAmount" jdbcType="DECIMAL"/>
        <result property="warehouseId" column="Warehouse_Id" jdbcType="INTEGER"/>
        <result property="username" column="UserName" jdbcType="VARCHAR"/>
        <result property="shopname" column="ShopName" jdbcType="VARCHAR"/>
        <result property="mobileno" column="MobileNo" jdbcType="VARCHAR"/>
        <result property="detailaddress" column="DetailAddress" jdbcType="VARCHAR"/>
        <result property="addressId" column="AddressId" jdbcType="INTEGER"/>
        <result property="province" column="Province" jdbcType="VARCHAR"/>
        <result property="city" column="City" jdbcType="VARCHAR"/>
        <result property="county" column="County" jdbcType="VARCHAR"/>
        <result property="street" column="Street" jdbcType="VARCHAR"/>
        <result property="parterId" column="ParterId" jdbcType="VARCHAR"/>
        <result property="parterName" column="ParterName" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="ordercreatetime" column="OrderCreateTime" jdbcType="TIMESTAMP"/>
        <result property="picktime" column="PickTime" jdbcType="TIMESTAMP"/>
        <result property="outstocktime" column="OutStockTime" jdbcType="TIMESTAMP"/>
        <result property="outstockuser" column="OutStockUser" jdbcType="VARCHAR"/>
        <result property="areaId" column="Area_Id" jdbcType="VARCHAR"/>
        <result property="areaName" column="AreaName" jdbcType="VARCHAR"/>
        <result property="routeId" column="Route_Id" jdbcType="VARCHAR"/>
        <result property="routeName" column="RouteName" jdbcType="VARCHAR"/>
        <result property="routeSequence" column="RouteSequence" jdbcType="INTEGER"/>
        <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
        <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
        <result property="lastupdateuser" column="lastupdateuser" jdbcType="VARCHAR"/>
        <result property="lastupdatetime" column="lastupdatetime" jdbcType="TIMESTAMP"/>
        <result property="fromCityId" column="FromCityId" jdbcType="INTEGER"/>
        <result property="deliveryMode" column="DeliveryMode" jdbcType="TINYINT"/>
        <result property="deliveryMarkState" column="DeliveryMarkState" jdbcType="TINYINT"/>
        <result property="orderSequence" column="OrderSequence" jdbcType="INTEGER"/>
        <result property="createAllocation" column="CreateAllocation" jdbcType="TINYINT"/>
        <result property="businessId" column="Business_Id" jdbcType="VARCHAR"/>
        <result property="packageAttribute" column="PackageAttribute" jdbcType="TINYINT"/>
        <result property="purchaserId" column="Purchaser_Id" jdbcType="VARCHAR"/>
        <result property="associatedBusinessNo" column="AssociatedBusinessNo" jdbcType="VARCHAR"/>
        <result property="businessNo" column="BusinessNo" jdbcType="VARCHAR"/>
        <result property="associatedBusinessId" column="AssociatedBusiness_Id" jdbcType="VARCHAR"/>
        <result property="ownerId" column="Owner_Id" jdbcType="BIGINT"/>
        <result property="ownerName" column="OwnerName" jdbcType="VARCHAR"/>
        <result property="crossWareHouse" column="CrossWareHouse" jdbcType="TINYINT"/>
        <result property="pushState" column="PushState" jdbcType="TINYINT"/>
        <result property="allotType" column="AllotType" jdbcType="TINYINT"/>
        <result property="originalPackageAmount" column="OriginalPackageAmount" jdbcType="DECIMAL"/>
        <result property="originalUnitAmount" column="OriginalUnitAmount" jdbcType="DECIMAL"/>
        <result property="orderSourceType" column="OrderSourceType" jdbcType="TINYINT"/>
        <result property="businessId" column="business_Id" jdbcType="VARCHAR"/>
        <result property="outBoundType" column="OutBoundType" jdbcType="TINYINT"/>
        <result property="warehouseAllocationType" column="WarehouseAllocationType" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        RefOrderNo,
        boundNo,
        Org_Id,
        BatchNo,
        Batch_Id,
        State,
        CompanyCode,
        OrderType,
        outBoundType,
        BusinessType,
        OrderAmount,
        PayableAmount,
        SkuCount,
        PackageAmount,
        UnitAmount,
        Warehouse_Id,
        UserName,
        ShopName,
        MobileNo,
        DetailAddress,
        AddressId,
        Province,
        City,
        County,
        Street,
        ParterId,
        ParterName,
        remark,
        OrderCreateTime,
        PickTime,
        OutStockTime,
        OutStockUser,
        Area_Id,
        AreaName,
        Route_Id,
        RouteName,
        RouteSequence,
        createuser,
        createtime,
        lastupdateuser,
        lastupdatetime,
        FromCityId,
        DeliveryMode,
        NewNoteNo,
        NewNoteId,
        DeliveryMarkState,
        OrderSequence,
        CreateAllocation,
        WarehouseName,
        OrgName,
        ErpState,
        Business_Id,
        ImgBusinessId,
        PackageAttribute,
        BusinessState,
        Purchaser_Id,
        PurchaserName,
        ExpectedOutStockTime,
        AssociatedBusinessNo,
        BusinessNo,
        AssociatedBusiness_Id,
        Owner_Id,
        OwnerName,
        InternalContacts,
        CrossWareHouse,
        PushState,
        PushTime,
        PushUser,
        AllotType,
        OriginalPackageAmount,
        OriginalUnitAmount,
        PickUpCode,
        OrderSourceType,
        WarehouseAllocationType
    </sql>

    <sql id="outstockorderSql">
        outstockorder
        .
        ID
        ,
        outstockorder.REFORDERNO,
        outstockorder.ORG_ID,
        outstockorder.BATCHNO,
        outstockorder.BATCH_ID,
        outstockorder.STATE,
        outstockorder.COMPANYCODE,
        outstockorder.ORDERTYPE,
        outstockorder.ORDERAMOUNT,
        outstockorder.SKUCOUNT,
        outstockorder.PACKAGEAMOUNT,
        outstockorder.UNITAMOUNT,
        outstockorder.WAREHOUSE_ID,
        outstockorder.USERNAME,
        outstockorder.SHOPNAME,
        outstockorder.MOBILENO,
        outstockorder.DETAILADDRESS,
        outstockorder.PROVINCE,
        outstockorder.CITY,
        outstockorder.COUNTY,
        outstockorder.STREET,
        outstockorder.parterId,
        outstockorder.parterName,
        outstockorder.ORDERCREATETIME,
        outstockorder.PICKTIME,
        outstockorder.OUTSTOCKTIME,
        outstockorder.OUTSTOCKUSER,
        outstockorder.REMARK,
        outstockorder.CREATEUSER,
        outstockorder.CREATETIME,
        outstockorder.LASTUPDATEUSER,
        outstockorder.LASTUPDATETIME,
        outstockorder.Area_Id,
        outstockorder.AreaName,
        outstockorder.Route_Id,
        outstockorder.RouteName,
        outstockorder.RouteSequence,
        outstockorder.RouteSequence,
        outstockorder.DeliveryMarkState,
        outstockorder.FromCityId,
        outstockorder.OrderSequence,
        outstockorder.AddressId,
        outstockorder.expectedOutStockTime,
        outstockorder.DeliveryMode,
        outstockorder.PackageAttribute,
        outstockorder.Business_Id,
        outstockorder.BusinessNo,
        outstockorder.Owner_Id,
        outstockorder.OwnerName,
        outstockorder.CreateAllocation,
        outstockorder.BusinessType,
        outstockorder.AllotType,
        outstockorder.boundNo,
        outstockorder.PushState,
        outstockorder.outBoundType,
        outstockorderitem.ID AS itemID,
        outstockorderitem.ORG_ID,
        outstockorderitem.BATCHTASKNO,
        outstockorderitem.BATCHTASK_ID,
        outstockorderitem.OUTSTOCKORDER_ID,
        outstockorderitem.PRODUCTNAME,
        outstockorderitem.SKUID,
        outstockorderitem.PRODUCTBRAND,
        outstockorderitem.CATEGORYNAME,
        outstockorderitem.SPECNAME,
        outstockorderitem.SPECQUANTITY,
        outstockorderitem.SALESPEC,
        outstockorderitem.SALESPECQUANTITY,
        outstockorderitem.PACKAGENAME,
        outstockorderitem.PACKAGECOUNT,
        outstockorderitem.UNITNAME,
        outstockorderitem.UNITCOUNT,
        outstockorderitem.UNITTOTALCOUNT,
        outstockorderitem.SaleModel,
        outstockorderitem.REMARK,
        outstockorderitem.CREATETIME,
        outstockorderitem.SOURCE,
        outstockorderitem.CHANNEL,
        outstockorderitem.TotalAmount,
        outstockorderitem.Owner_Id as Item_Owner_Id,
        outstockorderitem.SecOwner_Id,
        outstockorderitem.ProductSpecification_Id,
        outstockorderitem.ControlConfig_Id,
        outstockorderitem.LocationId,
        outstockorderitem.LocationName,
        outstockorderitem.SowTaskItem_Id,
        outstockorderitem.SowOrder_Id,
        outstockorderitem.SowTask_Id,
        outstockorderitem.SowTaskNo,
        outstockorderitem.BatchTaskItem_Id,
        outstockorderitem.BusinessItemId
    </sql>

    <sql id="outstockorderDetailSql">
        outstockorder.ID,
        outstockorder.REFORDERNO,
        outstockorder.ORG_ID,
        outstockorder.BATCHNO,
        outstockorder.BATCH_ID,
        outstockorder.STATE,
        outstockorder.COMPANYCODE,
        outstockorder.ORDERTYPE,
        outstockorder.ORDERAMOUNT,
        outstockorder.SKUCOUNT,
        outstockorder.PACKAGEAMOUNT,
        outstockorder.UNITAMOUNT,
        outstockorder.WAREHOUSE_ID,
        outstockorder.USERNAME,
        outstockorder.SHOPNAME,
        outstockorder.MOBILENO,
        outstockorder.DETAILADDRESS,
        outstockorder.PROVINCE,
        outstockorder.CITY,
        outstockorder.COUNTY,
        outstockorder.STREET,
        outstockorder.parterId,
        outstockorder.parterName,
        outstockorder.ORDERCREATETIME,
        outstockorder.PICKTIME,
        outstockorder.OUTSTOCKTIME,
        outstockorder.OUTSTOCKUSER,
        outstockorder.REMARK,
        outstockorder.CREATEUSER,
        outstockorder.CREATETIME,
        outstockorder.LASTUPDATEUSER,
        outstockorder.LASTUPDATETIME,
        outstockorder.Area_Id,
        outstockorder.AreaName,
        outstockorder.Route_Id,
        outstockorder.RouteName,
        outstockorder.RouteSequence,
        outstockorder.DeliveryMarkState,
        outstockorder.FromCityId,
        outstockorder.OrderSequence,
        outstockorder.AddressId,
        outstockorder.expectedOutStockTime,
        outstockorder.DeliveryMode,
        outstockorder.PackageAttribute,
        outstockorder.Business_Id,
        outstockorder.BusinessNo,
        outstockorder.Owner_Id,
        outstockorder.OwnerName,
        outstockorder.CreateAllocation,
        outstockorder.AllotType,
        outstockorder.CrossWareHouse,
        outstockorder.boundNo,
        outstockorder.Purchaser_Id,
        outstockorder.OriginalPackageAmount,
        outstockorder.OriginalUnitAmount,
        outstockorder.outBoundType,
        outstockorder.orderSourceType,
        outstockorder.lastupdateuser,
        outstockorderitem.ID AS itemID,
        outstockorderitem.ORG_ID,
        outstockorderitem.BATCHTASKNO,
        outstockorderitem.BATCHTASK_ID,
        outstockorderitem.OUTSTOCKORDER_ID,
        outstockorderitem.PRODUCTNAME,
        outstockorderitem.SKUID,
        outstockorderitem.PRODUCTBRAND,
        outstockorderitem.CATEGORYNAME,
        outstockorderitem.SPECNAME,
        outstockorderitem.SPECQUANTITY,
        outstockorderitem.SALESPEC,
        outstockorderitem.SALESPECQUANTITY,
        outstockorderitem.PACKAGENAME,
        outstockorderitem.PACKAGECOUNT,
        outstockorderitem.UNITNAME,
        outstockorderitem.UNITCOUNT,
        outstockorderitem.UNITTOTALCOUNT,
        outstockorderitem.SaleModel,
        outstockorderitem.REMARK,
        outstockorderitem.CREATETIME,
        outstockorderitem.SOURCE,
        outstockorderitem.CHANNEL,
        outstockorderitem.TotalAmount,
        outstockorderitem.Owner_Id as Item_Owner_Id,
        outstockorderitem.SecOwner_Id,
        outstockorderitem.ProductSpecification_Id,
        outstockorderitem.ControlConfig_Id,
        outstockorderitem.LocationId,
        outstockorderitem.LocationName,
        outstockorderitem.BatchTaskItem_Id,
        outstockorderitem.BusinessItemId,
        outstockorderitem.SowTask_Id,
        outstockorderitem.SowTaskItem_Id,
        outstockorderitem.OriginalUnitTotalCount,
        outstockorderitem.IsGift,
        outstockorderitem.IsAdvent,
        outstockorderitemdetail.Id as itemDetailID,
        outstockorderitemdetail.Org_Id as detail_orgId,
        outstockorderitemdetail.OutStockOrderItem_Id as OutStockOrderItem_Id,
        outstockorderitemdetail.Location_Id as detail_locationId,
        outstockorderitemdetail.LocationName as detail_locationName,
        outstockorderitemdetail.ProductionDate as detail_productionDate,
        outstockorderitemdetail.BatchTime as detail_batchTime,
        outstockorderitemdetail.UnitTotalCount as detail_unitTotalCount,
        outstockorderitemdetail.ProductSpecification_Id as detail_productSpecificationId,
        outstockorderitemdetail.Owner_Id as detail_ownerId,
        outstockorderitemdetail.SecOwner_Id as detail_secOwnerId,
        outstockorderitemdetail.BatchAttributeInfoNo as detail_batchAttributeInfoNo,
        outstockorder.WarehouseAllocationType
    </sql>

    <!-- 订单类型 -->
    <sql id="orderTypeListSql">
        (0,1,2,3,4,6,7,8,9,10,11,12,13,14,25,30,50,52)
    </sql>

    <select id="findOutStockOrderPOList" resultMap="findOutStockOrderResultMap">
        select
        outstockorder.ID, outstockorder.REFORDERNO, outstockorder.ORG_ID,
        outstockorder.BATCHNO,
        outstockorder.BATCH_ID, outstockorder.STATE, outstockorder.COMPANYCODE, outstockorder.ORDERTYPE,
        outstockorder.ORDERAMOUNT,
        outstockorder.SKUCOUNT, outstockorder.PACKAGEAMOUNT, outstockorder.UNITAMOUNT,
        outstockorder.WAREHOUSE_ID,
        outstockorder.USERNAME, outstockorder.SHOPNAME, outstockorder.MOBILENO,
        outstockorder.DETAILADDRESS,
        outstockorder.PROVINCE, outstockorder.CITY, outstockorder.COUNTY, outstockorder.STREET, outstockorder.parterId,
        outstockorder.parterName, outstockorder.ORDERCREATETIME, outstockorder.PICKTIME, outstockorder.OUTSTOCKTIME,
        outstockorder.OUTSTOCKUSER,
        outstockorder.REMARK, outstockorder.CREATEUSER, outstockorder.CREATETIME, outstockorder.LASTUPDATEUSER,
        outstockorder.LASTUPDATETIME,outstockorder.Area_Id,outstockorder.AreaName,outstockorder.Route_Id,outstockorder.RouteName,outstockorder.RouteSequence,
        outstockorder.DeliveryMarkState,outstockorder.FromCityId,
        outstockorder.OrderSequence,outstockorder.expectedOutStockTime
        ,outstockorder.Business_Id,outstockorder.BusinessNo,
        outstockorder.Owner_Id,outstockorder.OwnerName,outstockorder.CreateAllocation
        from outstockorder outstockorder
        WHERE outstockorder.State = 0 AND outstockorder.DeliveryMode != 4 AND outstockorder.DeliveryMarkState = -1
        AND (outstockorder.BATCHNO = '' OR outstockorder.BATCHNO IS NULL)
        <!--默认排除快递直发订单-->
        AND outstockorder.PackageAttribute != 3
        <if test="dto.wareHouseId != null ">
            AND outstockorder.WAREHOUSE_ID = #{dto.wareHouseId}
        </if>
        <if test="dto.orderTypes!=null ">
            AND outstockorder.ordertype in
            <foreach item="item" index="index" collection="dto.orderTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.orderTypes==null ">
            AND outstockorder.ordertype in
            <include refid="orderTypeListSql"/>
        </if>
        <if test="dto.notBusinessTypes!=null">
            AND outstockorder.BusinessType not in
            <foreach item="item" index="index" collection="dto.notBusinessTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.timeS!=null">
            AND outstockorder.OrderCreateTime <![CDATA[ >= ]]> #{dto.timeS,jdbcType=VARCHAR}
        </if>
        <if test="dto.timeE!=null">
            AND outstockorder.OrderCreateTime <![CDATA[ <= ]]> #{dto.timeE,jdbcType=VARCHAR}
        </if>
        <if test="dto.order_selection==0">
            ORDER BY outstockorder.OrderCreateTime ASC
        </if>
        <if test="dto.order_selection==1">
            ORDER BY outstockorder.COUNTY ASC
        </if>
        <if test="dto.order_selection==2">
            ORDER BY outstockorder.OrderCreateTime DESC
        </if>
    </select>

    <select id="findOutStockOrderPOPageCount" resultType="java.lang.String">
        select id from
        (SELECT DISTINCT id,ORDERCREATETIME from outstockorder o
        where o.State =0
        AND (o.BATCHNO = '' OR o.BATCHNO IS NULL)
        <if test="dto.wareHouseId != null">
            AND o.WAREHOUSE_ID = #{dto.wareHouseId}
        </if>

        <if test="dto.refOrderNo != null and dto.refOrderNo!=''">
            AND o.RefOrderNo = #{dto.refOrderNo}
        </if>

        <if test="dto.areaId != null">
            AND o.Area_Id = #{dto.areaId}
        </if>
        <if test="dto.routeId != null">
            AND o.Route_Id = #{dto.routeId}
        </if>
        <if test="dto.timeS!=null">
            AND o.ORDERCREATETIME <![CDATA[ >= ]]> #{dto.timeS,jdbcType=VARCHAR}
        </if>
        <if test="dto.timeE!=null">
            AND o.ORDERCREATETIME <![CDATA[ <= ]]> #{dto.timeE,jdbcType=VARCHAR}
        </if>
        <if test="dto.expTimeS!=null">
            AND o.ExpectedOutStockTime <![CDATA[ >= ]]> #{dto.expTimeS,jdbcType=VARCHAR}
        </if>
        <if test="dto.expTimeE!=null">
            AND o.ExpectedOutStockTime <![CDATA[ <= ]]> #{dto.expTimeE,jdbcType=VARCHAR}
        </if>
        <if test="dto.orderTypes!=null">
            AND o.ordertype in
            <foreach item="item" index="index" collection="dto.orderTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.orderTypes==null">
            AND o.ordertype in
            <include refid="orderTypeListSql"/>
        </if>
        <if test="dto.notBusinessTypes!=null">
            AND o.BusinessType not in
            <foreach item="item" index="index" collection="dto.notBusinessTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--默认排除快递直发订单-->
        <if test="dto.packageAttribute==null or dto.packageAttribute.size()==0">
            AND o.PackageAttribute != 3
        </if>
        <if test="dto.packageAttribute!=null and dto.packageAttribute.size()>0">
            AND o.PackageAttribute in
            <foreach item="item" index="index" collection="dto.packageAttribute" open="(" separator="," close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>

        <if test="dto.warehouseAllocationType != null and dto.warehouseAllocationType != ''">
            and (o.WarehouseAllocationType = #{dto.warehouseAllocationType,jdbcType=INTEGER}
            or o.WarehouseAllocationType is null)
        </if>
        order by o.OrderCreateTime DESC) tmpOrderId
    </select>

    <select id="findOutStockOrderList" resultMap="findOutStockOrderResultMap">
        select
        o.ID,
        o.REFORDERNO,
        o.ORG_ID,
        o.STATE,
        o.COMPANYCODE,
        o.ORDERTYPE,
        o.ORDERAMOUNT,
        o.SKUCOUNT,
        o.PACKAGEAMOUNT,
        o.UNITAMOUNT,
        o.WAREHOUSE_ID,
        o.USERNAME, o.SHOPNAME, o.MOBILENO, o.DETAILADDRESS,
        o.PROVINCE, o.CITY, o.COUNTY, o.STREET, o.parterId, o.parterName,
        o.ORDERCREATETIME, o.PICKTIME, o.OUTSTOCKTIME, o.OUTSTOCKUSER, o.REMARK,
        o.CREATEUSER, o.CREATETIME, o.LASTUPDATEUSER, o.LASTUPDATETIME,
        o.Area_Id, o.AreaName, o.Route_Id, o.RouteName, o.RouteSequence, o.RouteSequence, o.DeliveryMarkState,
        o.FromCityId, o.OrderSequence, o.AddressId,
        o.BATCHNO, o.BATCH_ID, o.Business_Id, o.BusinessNo,o.Owner_Id,o.OwnerName, o.ExpectedOutStockTime
        from outstockorder o
        <where>
            <if test="dto.wareHouseId != null">
                o.WAREHOUSE_ID = #{dto.wareHouseId}
            </if>
            <if test="dto.refOrderNo != null and dto.refOrderNo!=''">
                AND o.RefOrderNo = #{dto.refOrderNo}
            </if>
            <if test="dto.refOrderNoList != null">
                AND o.RefOrderNo in
                <foreach item="item" index="index" collection="dto.refOrderNoList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.areaId != null">
                AND o.Area_Id = #{dto.areaId}
            </if>
            <if test="dto.routeId != null">
                AND o.Route_Id = #{dto.routeId}
            </if>
            <if test="dto.timeS!=null">
                AND o.ORDERCREATETIME <![CDATA[ >= ]]> #{dto.timeS,jdbcType=VARCHAR}
            </if>
            <if test="dto.timeE!=null">
                AND o.ORDERCREATETIME <![CDATA[ <= ]]> #{dto.timeE,jdbcType=VARCHAR}
            </if>
            <if test="dto.orderTypes!=null">
                AND o.ordertype in
                <foreach item="item" index="index" collection="dto.orderTypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.outBoundType != null">
                AND o.outBoundType=#{dto.outBoundType}
            </if>
            <if test="dto.orderTypes==null and dto.outBoundType == null">
                AND o.ordertype in
                <include refid="orderTypeListSql"/>
            </if>
            <if test="dto.businessTypes!=null">
                AND o.BusinessType in
                <foreach item="item" index="index" collection="dto.businessTypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.notBusinessTypes!=null">
                AND o.BusinessType not in
                <foreach item="item" index="index" collection="dto.notBusinessTypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!--默认排除快递直发订单-->
            <if test="dto.expressFlag==null or dto.expressFlag!=1">
                AND o.DeliveryMode != 7
            </if>
            <if test="dto.expressFlag!=null and dto.expressFlag==1">
                AND o.DeliveryMode = 7
            </if>
            <!--订单特征-->
            <if test="dto.packageAttribute!=null and dto.packageAttribute.size()>0">
                AND EXISTS (
                SELECT f.id FROM orderfeature f where f.Order_id = o.Id and f.FeatureType in
                <foreach item="item" index="index" collection="dto.packageAttribute" open="(" separator="," close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
                )
            </if>
            <if test="dto.orderStates!=null">
                AND o.state in
                <foreach item="item" index="index" collection="dto.orderStates" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.expTimeS!=null">
                AND o.ExpectedOutStockTime <![CDATA[ >= ]]> #{dto.expTimeS,jdbcType=VARCHAR}
            </if>
            <if test="dto.expTimeE!=null">
                AND o.ExpectedOutStockTime <![CDATA[ <= ]]> #{dto.expTimeE,jdbcType=VARCHAR}
            </if>
            <!-- 收货城市, 区, 街道查询 -->
            <if test="dto.city!=null and dto.city !=''">
                AND o.City = #{dto.city,jdbcType=VARCHAR}
            </if>
            <if test="dto.county!=null and dto.county !=''">
                AND o.County = #{dto.county,jdbcType=VARCHAR}
            </if>
            <if test="dto.street!=null and dto.street !=''">
                AND o.Street = #{dto.street,jdbcType=VARCHAR}
            </if>
            <if test="dto.outBoundType!=null">
                AND o.outBoundType =#{dto.outBoundType}
            </if>
        </where>
        order by o.OrderCreateTime
        <if test="dto.orderByCreateTime == null or dto.orderByCreateTime == 0">
            DESC
        </if>
    </select>

    <select id="findOutStockOrderIdsByBatchId" resultType="java.lang.Long">
        SELECT distinct o.id
        from outstockorder o
        inner join outstockorderitem oi on o.id = oi.Outstockorder_Id
        where oi.Batch_Id = #{batchId}
    </select>

    <select id="findByOrderId" resultMap="findOutStockOrderDetailResultMap" parameterType="java.util.List">
        select
        <include refid="outstockorderDetailSql"/>
        from outstockorder outstockorder
        inner join outstockorderitem outstockorderitem on outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        left join outstockorderitemdetail outstockorderitemdetail on outstockorderitem.id =
        outstockorderitemdetail.OutStockOrderItem_Id
        where (outstockorder.BATCHNO = '' OR outstockorder.BATCHNO IS NULL)
        AND (outstockorderitem.BATCHNO = '' OR outstockorderitem.BATCHNO IS NULL)
        AND (outstockorder.PACKAGEAMOUNT !=0 or outstockorder.UNITAMOUNT !=0)
        AND outstockorder.id in
        <foreach item="item" index="index" collection="OrderIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        order by outstockorder.OrderCreateTime DESC
    </select>

    <select id="findByOrderIdWithoutCon" resultMap="findOutStockOrderDetailResultMap" parameterType="java.util.List">
        select
        <include refid="outstockorderDetailSql"/>
        from outstockorder outstockorder
        inner join outstockorderitem outstockorderitem on outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        left join outstockorderitemdetail outstockorderitemdetail on outstockorderitem.id =
        outstockorderitemdetail.OutStockOrderItem_Id
        where (outstockorder.PACKAGEAMOUNT !=0 or outstockorder.UNITAMOUNT !=0)
        AND outstockorder.id in
        <foreach item="item" index="index" collection="OrderIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        order by outstockorder.OrderCreateTime DESC
    </select>

    <select id="findByOrderIds" resultMap="findOutStockOrderDetailResultMap" parameterType="java.util.List">
        select
        <include refid="outstockorderDetailSql"/>
        from outstockorder outstockorder
        inner join outstockorderitem outstockorderitem on outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        left join outstockorderitemdetail outstockorderitemdetail on outstockorderitem.id =
        outstockorderitemdetail.OutStockOrderItem_Id
        where outstockorder.id in
        <foreach item="item" index="index" collection="OrderIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listOutStockOrderByOrderId" resultMap="findOutStockOrderDetailResultMap" parameterType="java.util.List">
        select
        <include refid="outstockorderDetailSql"/>
        from outstockorder outstockorder
        inner join outstockorderitem outstockorderitem on outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        left join outstockorderitemdetail outstockorderitemdetail on outstockorderitem.id =
        outstockorderitemdetail.OutStockOrderItem_Id
        where outstockorder.id in
        <foreach item="item" index="index" collection="OrderIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listOutStockOrderByOrderNo" resultMap="findOutStockOrderResultMap" parameterType="java.util.List">
        select
        <include refid="outstockorderSql"/>
        from outstockorder outstockorder,
        outstockorderitem outstockorderitem
        where outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        AND outstockorder.REFORDERNO in
        <foreach item="item" index="index" collection="OrderNos"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="orgId != null">
            and outstockorder.ORG_ID = #{orgId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and outstockorder.WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="findCreateAllocationByOrderId"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO"
            parameterType="java.util.List">
        select id, CreateAllocation
        from outstockorder
        where id in
        <foreach item="item" index="index" collection="OrderIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findNoWaveOrderByRefOrderNo" resultMap="findOutStockOrderDetailResultMap"
            parameterType="java.util.List">
        select
        <include refid="outstockorderDetailSql"/>
        from outstockorder outstockorder
        inner join outstockorderitem outstockorderitem on outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        left join outstockorderitemdetail outstockorderitemdetail on outstockorderitem.id =
        outstockorderitemdetail.OutStockOrderItem_Id
        where (outstockorder.BATCHNO = '' OR outstockorder.BATCHNO IS NULL)
        AND (outstockorderitem.BATCHNO = '' OR outstockorderitem.BATCHNO IS NULL)
        AND outstockorder.WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
        AND outstockorder.REFORDERNO in
        <foreach item="item" index="index" collection="refOrderNos"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findOrderByRefOrderNo" resultMap="findOutStockOrderDetailResultMap" parameterType="java.util.List">
        select
        IFNULL(batch.State,outstockorder.State) as State,outstockorder.RefOrderNo
        from outstockorder left join batch on outstockorder.BatchNo=batch.BatchNo
        where outstockorder.WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
        AND outstockorder.REFORDERNO in
        <foreach item="item" index="index" collection="refOrderNos"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findByOrderItemId" resultMap="findOutStockOrderResultMap" parameterType="java.util.List">
        select
        <include refid="outstockorderSql"/>
        from outstockorder outstockorder,
        outstockorderitem outstockorderitem
        where outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        AND (outstockorderitem.Batchtask_Id = '' OR outstockorderitem.Batchtask_Id IS NULL)
        AND outstockorderitem.id in
        <foreach item="item" index="index" collection="orderItemIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findDelayOrderByWarehouseId" resultMap="findOutStockOrderResultMap">
        select
        <include refid="outstockorderSql"/>
        from outstockorder outstockorder,
        outstockorderitem outstockorderitem
        where outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        and outstockorder.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and outstockorder.State in (0,1,2,3)
        and outstockorder.DeliveryMarkState = 3
    </select>

    <update id="batchUpdateOutStockOrder" parameterType="java.util.List">
        update outstockorder
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="batchno =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchno}
                </foreach>
            </trim>
            <trim prefix="batch_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.batchId}
                </foreach>
            </trim>
            <trim prefix="orderSequence =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.orderSequence != null">
                        when id=#{item.id} then #{item.orderSequence}
                    </if>
                </foreach>
            </trim>
        </trim>
        ,state = 1
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
        and (BatchNo = '' or BatchNo is null)
    </update>

    <update id="updateStateByOrderIds">
        update outstockorder
        set state = #{state,jdbcType=TINYINT}
        where Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="findOrderIdByItemState" resultType="java.lang.Long">
        select o.id
        from outstockorder o
        where o.id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and (select count(*) from outstockorderitem oi where oi.Outstockorder_Id = o.id and (oi.Batchtask_Id = '' or
        oi.Batchtask_Id is null) and oi.UnitTotalCount > 0) = 0;
    </select>

    <update id="cleanBatch">
        update outstockorder
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="BatchNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when BatchNo=#{item} then null
                </foreach>
            </trim>
            <trim prefix="Batch_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when BatchNo=#{item} then null
                </foreach>
            </trim>
        </trim>
        ,State = 0,Remark=null
        where BatchNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <insert id="insertList">
        INSERT into outstockorder(id, FromCityId, RefOrderNo,
        Org_Id, BatchNo, Batch_Id,
        State, CompanyCode,
        OrderType, BusinessType, OrderAmount,
        PayableAmount, SkuCount, PackageAmount,
        UnitAmount, Warehouse_Id,
        UserName, ShopName, MobileNo,
        DetailAddress, AddressId, Province,
        City, County, Street,
        ParterId, ParterName, Remark,
        OrderCreateTime, DeliveryMode, PickTime,
        OutStockTime, OutStockUser, Area_Id,
        AreaName, Route_Id, RouteName,
        RouteSequence, createuser,
        lastupdateuser,NewNoteNo,NewNoteId,OrderSequence,Business_Id,BusinessNo)
        values
        <foreach collection="recordList" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.fromCityId,jdbcType=INTEGER}, #{item.refOrderNo,jdbcType=VARCHAR},
            #{item.org_Id,jdbcType=INTEGER}, #{item.batchNo,jdbcType=VARCHAR}, #{item.batch_Id,jdbcType=VARCHAR},
            #{item.state,jdbcType=TINYINT}, #{item.companyCode,jdbcType=VARCHAR},
            #{item.orderType,jdbcType=TINYINT}, #{item.businessType,jdbcType=TINYINT},
            #{item.orderAmount,jdbcType=DECIMAL},
            #{item.payableAmount,jdbcType=DECIMAL}, #{item.skuCount,jdbcType=INTEGER},
            #{item.packageAmount,jdbcType=DECIMAL},
            #{item.unitAmount,jdbcType=DECIMAL}, #{item.warehouse_Id,jdbcType=INTEGER},
            #{item.userName,jdbcType=VARCHAR}, #{item.shopName,jdbcType=VARCHAR}, #{item.mobileNo,jdbcType=VARCHAR},
            #{item.detailAddress,jdbcType=VARCHAR}, #{item.addressId,jdbcType=INTEGER},
            #{item.province,jdbcType=VARCHAR},
            #{item.city,jdbcType=VARCHAR}, #{item.county,jdbcType=VARCHAR}, #{item.street,jdbcType=VARCHAR},
            #{item.parterId,jdbcType=BIGINT}, #{item.parterName,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
            now(), #{item.deliveryMode,jdbcType=TINYINT},
            #{item.pickTime,jdbcType=TIMESTAMP},
            #{item.outStockTime,jdbcType=TIMESTAMP}, #{item.outStockUser,jdbcType=VARCHAR},
            #{item.area_Id,jdbcType=BIGINT},
            #{item.areaName,jdbcType=VARCHAR}, #{item.route_Id,jdbcType=BIGINT}, #{item.routeName,jdbcType=VARCHAR},
            #{item.routeSequence,jdbcType=INTEGER}, #{item.createuser,jdbcType=VARCHAR},
            #{item.lastupdateuser,jdbcType=VARCHAR}, #{item.newNoteNo,jdbcType=VARCHAR},
            #{item.newNoteId,jdbcType=VARCHAR},
            #{item.orderSequence,jdbcType=INTEGER},
            #{item.businessId,jdbcType=VARCHAR}, #{item.businessNo,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="listRefOrderNoByLike" resultType="java.lang.String">
        SELECT RefOrderNo FROM outstockorder
        WHERE State = 3
        AND OrderType in
        <include refid="orderTypeListSql"/>
        <if test="outStockOrderPO.warehouseId != null and outStockOrderPO.warehouseId != ''">
            AND warehouse_id = #{outStockOrderPO.warehouseId,jdbcType=VARCHAR}
        </if>
        <if test="outStockOrderPO.reforderno != null and outStockOrderPO.reforderno != ''">
            AND RefOrderNo like concat('%',#{outStockOrderPO.reforderno,jdbcType=VARCHAR})
        </if>
    </select>

    <!--<select id="findOutStockOrderPOPageList" resultMap="findOutStockOrderResultMap">-->
    <!--SELECT-->
    <!--o.ID,o.REFORDERNO,o.ORG_ID,o.BATCHNO,o.BATCH_ID,o.STATE,o.COMPANYCODE,o.ORDERTYPE,o.ORDERAMOUNT,o.SKUCOUNT,o.PACKAGEAMOUNT,o.UNITAMOUNT,-->
    <!--o.WAREHOUSE_ID,o.USERNAME,o.SHOPNAME,o.MOBILENO,o.DETAILADDRESS,o.PROVINCE,o.CITY,o.COUNTY,o.STREET,o.parterId,o.parterName,o.ORDERCREATETIME,-->
    <!--o.PICKTIME,o.OUTSTOCKTIME,o.OUTSTOCKUSER,o.REMARK,o.CREATEUSER,o.CREATETIME,o.LASTUPDATEUSER,o.LASTUPDATETIME,o.Area_Id,o.AreaName,o.Route_Id,-->
    <!--o.RouteName,o.RouteSequence,o.DeliveryMarkState,o.FromCityId,-->
    <!--oitem.ID AS itemID,oitem.ORG_ID,oitem.BATCHTASKNO,oitem.BATCHTASK_ID,oitem.Outstockorder_Id,oitem.PRODUCTNAME,oitem.SKUID,oitem.PRODUCTBRAND,-->
    <!--oitem.CATEGORYNAME,oitem.SPECNAME,oitem.SPECQUANTITY,oitem.SALESPEC,oitem.SALESPECQUANTITY,oitem.PACKAGENAME,oitem.PACKAGECOUNT,oitem.UNITNAME,-->
    <!--oitem.UNITCOUNT,oitem.UNITTOTALCOUNT,oitem.SaleModel,oitem.REMARK,oitem.CREATETIME,oitem.SOURCE,oitem.CHANNEL-->
    <!--FROM-->
    <!--outstockorder o,-->
    <!--outstockorderitem oitem-->
    <!--WHERE-->
    <!--o.id = oitem.Outstockorder_Id-->
    <!--AND (o.BATCHNO = '' OR o.BATCHNO IS NULL)-->
    <!--and o.State =0-->
    <!--<if test="dto.wareHouseId != null">-->
    <!--AND o.WAREHOUSE_ID = #{dto.wareHouseId}-->
    <!--</if>-->

    <!--<if test="dto.refOrderNo != null and dto.refOrderNo!=''">-->
    <!--AND o.REFORDERNO = #{dto.refOrderNo}-->
    <!--</if>-->

    <!--<if test="dto.areaId != null">-->
    <!--AND o.Area_Id = #{dto.areaId}-->
    <!--</if>-->
    <!--<if test="dto.routeId != null">-->
    <!--AND o.Route_Id = #{dto.routeId}-->
    <!--</if>-->
    <!--<if test="dto.orderTypes!=null">-->
    <!--AND o.ordertype in-->
    <!--<foreach item="item" index="index" collection="dto.orderTypes" open="(" separator="," close=")">-->
    <!--#{item}-->
    <!--</foreach>-->
    <!--</if>-->
    <!--<if test="dto.orderTypes==null">-->
    <!--AND o.ordertype in (0,1,2,3,4,6,7,8,9,10,11,12,13,14)-->
    <!--</if>-->
    <!--<if test="dto.timeS!=null">-->
    <!--AND o.ORDERCREATETIME <![CDATA[ >= ]]> #{dto.timeS,jdbcType=VARCHAR}-->
    <!--</if>-->
    <!--<if test="dto.timeE!=null">-->
    <!--AND o.ORDERCREATETIME <![CDATA[ <= ]]> #{dto.timeE,jdbcType=VARCHAR}-->
    <!--</if>-->
    <!--<if test="dto.order_selection==0">-->
    <!--ORDER BY o.CreateTime ASC-->
    <!--</if>-->
    <!--<if test="dto.order_selection==1">-->
    <!--ORDER BY o.COUNTY ASC-->
    <!--</if>-->
    <!--<if test="dto.order_selection==2">-->
    <!--ORDER BY o.CreateTime DESC-->
    <!--</if>-->
    <!--order by o.ORDERCREATETIME desc-->
    <!--</select>-->

    <select id="selectByRefOrderNo" resultMap="findOutStockOrderResultMap">
        select
        ID,
        REFORDERNO,
        ORG_ID,
        BATCHNO,
        BATCH_ID,
        STATE,
        COMPANYCODE,
        ORDERTYPE,
        ORDERAMOUNT,
        SKUCOUNT,
        PACKAGEAMOUNT,
        UNITAMOUNT,
        WAREHOUSE_ID,
        USERNAME,
        SHOPNAME,
        MOBILENO,
        DETAILADDRESS,
        PROVINCE,
        CITY,
        COUNTY,
        STREET,
        parterId,
        parterName,
        ORDERCREATETIME,
        PICKTIME,
        OUTSTOCKTIME,
        OUTSTOCKUSER,
        REMARK,
        CREATEUSER,
        CREATETIME,
        LASTUPDATEUSER,
        LASTUPDATETIME,
        Area_Id,
        AreaName,
        Route_Id,
        RouteName,
        RouteSequence,
        DeliveryMarkState,
        FromCityId,
        OrderSequence,expectedOutStockTime,
        Business_Id,BusinessNo,
        Owner_Id,OwnerName, AllotType
        from outstockorder
        where RefOrderNo = #{refOrderNo, jdbcType=VARCHAR}
        <if test="warehouseId != null">
            and WAREHOUSE_ID = #{warehouseId, jdbcType=INTEGER}
        </if>
        <if test="cityId != null">
            and ORG_ID = #{cityId, jdbcType=INTEGER}
        </if>
    </select>

    <select id="listByRefOrderNo" resultMap="findOutStockOrderResultMap">
        select
        ID,
        REFORDERNO,
        ORG_ID,
        BATCHNO,
        BATCH_ID,
        STATE,
        COMPANYCODE,
        ORDERTYPE,
        ORDERAMOUNT,
        SKUCOUNT,
        PACKAGEAMOUNT,
        UNITAMOUNT,
        WAREHOUSE_ID,
        USERNAME,
        SHOPNAME,
        MOBILENO,
        DETAILADDRESS,
        PROVINCE,
        CITY,
        COUNTY,
        STREET,
        parterId,
        parterName,
        ORDERCREATETIME,
        PICKTIME,
        OUTSTOCKTIME,
        OUTSTOCKUSER,
        REMARK,
        CREATEUSER,
        CREATETIME,
        LASTUPDATEUSER,
        LASTUPDATETIME,
        Area_Id,
        AreaName,
        Route_Id,
        RouteName,
        RouteSequence,
        DeliveryMarkState,
        FromCityId,
        OrderSequence,expectedOutStockTime,
        Business_Id,BusinessNo,
        Owner_Id,OwnerName,
        BusinessType
        from outstockorder
        where RefOrderNo in
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        <if test="cityId != null">
            and ORG_ID = #{cityId, jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and WAREHOUSE_ID = #{warehouseId, jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectByRefOrderId" resultMap="findOutStockOrderResultMap">
        select ID,
        REFORDERNO,
        ORG_ID,
        BATCHNO,
        BATCH_ID,
        STATE,
        COMPANYCODE,
        ORDERTYPE,
        ORDERAMOUNT,
        SKUCOUNT,
        PACKAGEAMOUNT,
        UNITAMOUNT,
        WAREHOUSE_ID,
        USERNAME,
        SHOPNAME,
        MOBILENO,
        DETAILADDRESS,
        PROVINCE,
        CITY,
        COUNTY,
        STREET,
        parterId,
        parterName,
        ORDERCREATETIME,
        PICKTIME,
        OUTSTOCKTIME,
        OUTSTOCKUSER,
        REMARK,
        CREATEUSER,
        CREATETIME,
        LASTUPDATEUSER,
        LASTUPDATETIME,
        Area_Id,
        AreaName,
        Route_Id,
        RouteName,
        RouteSequence,
        DeliveryMarkState,
        FromCityId,
        OrderSequence,
        expectedOutStockTime,
        Business_Id,
        BusinessNo,
        Owner_Id,
        OwnerName,
        BusinessType,
        OrderSourceType
        from outstockorder
        where id = #{refOrderId, jdbcType=BIGINT}
    </select>

    <select id="listRefOrderNoByBatchNo" resultType="java.lang.String">
        select distinct o.RefOrderNo
        from outstockorder o
        inner join outstockorderitem oi on o.id = oi.Outstockorder_Id
        where oi.BatchNo = #{batchNo,jdbcType=VARCHAR}
    </select>

    <select id="findOrderIdByBatchNo" resultType="java.lang.Long">
        select distinct o.Id
        from outstockorder o
        where o.BatchNo = #{batchNo,jdbcType=VARCHAR}
        and o.Org_Id = #{orgId, jdbcType=TINYINT}
    </select>


    <sql id="listOrderTemplate">
        select
        ID,
        REFORDERNO,
        ORG_ID,
        WAREHOUSE_ID as warehouseId,
        BATCHNO,
        BATCH_ID,
        STATE,
        COMPANYCODE,
        ORDERTYPE,
        ORDERAMOUNT,
        SKUCOUNT,
        PACKAGEAMOUNT,
        UNITAMOUNT,
        WAREHOUSE_ID,
        USERNAME,
        SHOPNAME,
        MOBILENO,
        DETAILADDRESS,
        PROVINCE,
        CITY,
        COUNTY,
        STREET,
        parterId,
        parterName,
        ORDERCREATETIME,
        PICKTIME,
        OUTSTOCKTIME,
        OUTSTOCKUSER,
        REMARK,
        CREATEUSER,
        CREATETIME,
        LASTUPDATEUSER,
        LASTUPDATETIME,
        Area_Id,
        AreaName,
        Route_Id,
        RouteName,
        RouteSequence,
        DeliveryMarkState,
        FromCityId,
        OrderSequence,expectedOutStockTime,
        Business_Id as businessId,
        BusinessNo,
        BusinessType,
        AssociatedBusiness_Id as associatedBusinessId,
        boundNo,
        OrderSourceType as orderSourceType
        from outstockorder
        where
        id in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        <if test="orgId != null">
            and Org_Id = #{orgId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
    </sql>
    <select id="findSimpleByIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO">
        <include refid="listOrderTemplate"/>
    </select>

    <select id="listOrderByIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO">
        <include refid="listOrderTemplate"/>
    </select>

    <sql id="waitDeliverySql">
        o.State = 0
        and (oi.Batchtask_Id = '' or oi.Batchtask_Id is null)
        and o.ordertype = 30
        and oi.UnitTotalCount > 0
        <!-- 排除快递直发订单 -->
        and o.PackageAttribute != 3
    </sql>

    <select id="listOutStockOrderProduct" resultType="java.lang.Long">
        select
        distinct oi.SkuId
        from outstockorder o inner join outstockorderitem oi on o.id = oi.Outstockorder_Id
        where
        <include refid="waitDeliverySql"/>
        and o.Org_Id = #{cityId,jdbcType=INTEGER}
        and o.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </select>

    <select id="listOutStockOrderItemWaitDelivery"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderWaitDeliveryDTO">
        select
        o.Org_Id as cityId,
        o.id as refOrderId,
        o.RefOrderNo as refOrderNo,
        o.OrderCreateTime as orderCreateTime,
        o.Area_Id as areaId,
        o.AreaName as areaName,
        o.Route_Id as routeId,
        o.RouteName as routeName,
        o.AddressId as addressId,
        o.ShopName as shopName,
        oi.id as orderItemId,
        oi.SkuId as skuId,
        oi.ProductName as productName,
        oi.SpecName as specName,
        oi.SaleSpec as saleSpec,
        oi.PackageName as packageName,
        oi.PackageCount as packageCount,
        oi.UnitName as unitName,
        oi.UnitCount as unitCount,
        oi.UnitTotalCount as unitTotalCount,
        o.userName,
        o.mobileNo,
        o.detailAddress,
        o.province,
        o.city,
        o.county,
        o.street,o.expectedOutStockTime,
        o.Business_Id,o.BusinessNo
        from outstockorder o inner join outstockorderitem oi on o.id = oi.Outstockorder_Id
        where
        <include refid="waitDeliverySql"/>
        and oi.SkuId in
        <foreach item="item" index="index" collection="skuIds" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="cityId != null">
            and o.Org_Id = #{cityId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and o.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="cityNameList != null and cityNameList.size() > 0">
            and o.City in
            <foreach collection="cityNameList" item="cityName" open="(" separator="," close=")">
                #{cityName,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="routeIdList != null and routeIdList.size() > 0">
            and o.Route_Id in
            <foreach collection="routeIdList" item="routeId" open="(" separator="," close=")">
                #{routeId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="addressIdList != null and addressIdList.size() > 0">
            and o.AddressId in
            <foreach collection="addressIdList" item="addressId" open="(" separator="," close=")">
                #{addressId,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="startTime != null">
            and <![CDATA[ o.OrderCreateTime >= #{startTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="endTime != null">
            and <![CDATA[ o.OrderCreateTime <= #{endTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="expStartTime != null">
            and <![CDATA[ o.expectedOutStockTime >= #{expStartTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="expEndTime != null">
            and <![CDATA[ o.expectedOutStockTime <= #{expEndTime,jdbcType=TIMESTAMP} ]]>
        </if>

        <if test="warehouseAllocationType != null and warehouseAllocationType != ''">
            and (o.WarehouseAllocationType = #{warehouseAllocationType,jdbcType=INTEGER}
            or o.WarehouseAllocationType is null)
        </if>
        order by o.OrderCreateTime desc, o.id, oi.id
    </select>

    <select id="listOrderProductByBatchId"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderByProductDTO">
        select o.RefOrderNo as refOrderNo,
        oi.id as orderItemId,
        oi.SkuId as skuId,
        oi.ProductName as productName,
        oi.SpecName as specName,
        oi.SaleSpec as saleSpec,
        oi.PackageName as packageName,
        oi.PackageCount as packageCount,
        oi.UnitName as unitName,
        oi.UnitCount as unitCount,
        oi.UnitTotalCount as unitTotalCount,
        o.OrderCreateTime,
        o.expectedOutStockTime
        from outstockorder o
        inner join outstockorderitem oi on o.id = oi.Outstockorder_Id
        where oi.Batch_Id = #{batchId,jdbcType=VARCHAR}
        order by o.OrderCreateTime desc, o.id, oi.id
    </select>

    <select id="listProductGroupByBatchId"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderByProductDTO">
        select oi.SkuId as skuId,
        oi.ProductName as productName,
        oi.SpecName as specName,
        oi.SaleSpec as saleSpec,
        oi.PackageName as packageName,
        sum(oi.PackageCount) as packageCount,
        oi.UnitName as unitName,
        sum(oi.UnitCount) as unitCount,
        sum(oi.UnitTotalCount) as unitTotalCount
        from outstockorder o
        inner join outstockorderitem oi on o.id = oi.Outstockorder_Id
        where oi.Batch_Id = #{batchId,jdbcType=VARCHAR}
        group by oi.SkuId, oi.ProductName, oi.SpecName, oi.SaleSpec, oi.PackageName, oi.UnitName
    </select>

    <select id="listOrderByBatchNo"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO">
        select distinct o.id,
        o.RefOrderNo as refOrderNo,
        DATE_FORMAT(o.OrderCreateTime, '%Y-%m-%d %H:%i:%s') AS orderCreateTime,
        o.ShopName as shopName,
        o.DetailAddress as detailAddress,
        o.OrderType as orderType,
        o.OrderAmount as orderAmount,
        o.SkuCount as skuCount,
        o.PackageAmount as packageAmount,
        o.UnitAmount as unitAmount,
        o.Warehouse_Id as warehouseId,
        o.Business_Id as businessId
        from outstockorder o
        inner join outstockorderitem oi on o.id = oi.Outstockorder_Id
        where o.BatchNo = #{batchNo}
    </select>

    <select id="listProductAllotStore"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.ProductAllotStoreDTO">
        select s.SkuId as productSkuId, s.allotType, sum(s.UnitTotalCount) as unitTotalCount
        from (
        select oi.SkuId, if(o.State=0, 0, 1) as allotType, oi.UnitTotalCount
        from outstockorder o inner join outstockorderitem oi on o.id = oi.Outstockorder_Id
        where o.Org_Id = #{cityId,jdbcType=INTEGER}
        and o.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and o.State in (0,1,2,3)
        and oi.SkuId in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        ) s group by s.SkuId, s.allotType
    </select>

    <select id="findByBatchNos" resultMap="findOutStockOrderDetailResultMap">
        select
        <include refid="outstockorderDetailSql"/>
        from outstockorder outstockorder
        inner join outstockorderitem outstockorderitem on outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        left join outstockorderitemdetail outstockorderitemdetail on outstockorderitem.id =
        outstockorderitemdetail.OutStockOrderItem_Id
        where outstockorder.Org_Id = #{orgId,jdbcType=INTEGER}
        and outstockorder.BatchNo in
        <foreach item="item" index="index" collection="batchNos" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findSimpleInfoByBatchNos" resultMap="OutStockOrderAllResultMap">
        select
        <include refid="Base_Column_List"/>
        from outstockorder outstockorder
        where outstockorder.Org_Id = #{orgId,jdbcType=INTEGER}
        and outstockorder.BatchNo in
        <foreach item="item" index="index" collection="batchNos" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>


    <select id="findOutStockOrderItemList" resultMap="findOutStockOrderResultMap">
        select
        o.ID,
        o.REFORDERNO,
        o.ORG_ID,
        o.STATE,
        o.COMPANYCODE,
        o.ORDERTYPE,
        o.ORDERAMOUNT,
        o.SKUCOUNT,
        o.PACKAGEAMOUNT,
        o.UNITAMOUNT,
        o.WAREHOUSE_ID,
        o.USERNAME, o.SHOPNAME, o.MOBILENO, o.DETAILADDRESS,
        o.PROVINCE, o.CITY, o.COUNTY, o.STREET, o.parterId, o.parterName,
        o.ORDERCREATETIME, o.PICKTIME, o.OUTSTOCKTIME, o.OUTSTOCKUSER, o.REMARK,
        o.CREATEUSER, o.CREATETIME, o.LASTUPDATEUSER, o.LASTUPDATETIME,
        o.Area_Id, o.AreaName, o.Route_Id, o.RouteName, o.RouteSequence, o.RouteSequence, o.DeliveryMarkState,
        o.FromCityId, o.OrderSequence, o.AddressId,
        o.BATCHNO, o.BATCH_ID, o.Business_Id, o.BusinessNo,o.Owner_Id,o.OwnerName, o.ExpectedOutStockTime,
        o.DeliveryMode, o.OrderSourceType
        ,
        osi.ID AS itemID, osi.ORG_ID, osi.BATCHTASKNO,
        osi.BATCHTASK_ID,
        osi.OUTSTOCKORDER_ID,
        osi.OUTSTOCKORDER_ID REFORDER_ID,
        osi.PRODUCTNAME,
        osi.SKUID, osi.PRODUCTBRAND,
        osi.CATEGORYNAME, osi.SPECNAME, osi.SPECQUANTITY,
        osi.SALESPEC, osi.SALESPECQUANTITY,
        osi.PACKAGENAME, osi.PACKAGECOUNT, osi.UNITNAME,
        osi.UNITCOUNT, osi.UNITTOTALCOUNT,
        osi.SaleModel, osi.REMARK,
        osi.CREATETIME,osi.SOURCE,osi.CHANNEL,
        osi.Owner_Id, osi.SecOwner_Id, osi.ProductSpecification_Id,
        osi.ControlConfig_Id

        from outstockorder o
        inner join
        <choose>
            <when test="dto.skuId != null or dto.productSpecificationId != null">
                (
                select * from outstockorderitem
                where 1=1
                <if test="dto.skuId != null and dto.skuId != ''">
                    and SkuId = #{dto.skuId,jdbcType=VARCHAR}
                </if>
                <if test="dto.productSpecificationId != null">
                    and ProductSpecification_Id = #{dto.productSpecificationId,jdbcType=BIGINT}
                </if>
                ) as
            </when>
            <otherwise>
                outstockorderitem
            </otherwise>
        </choose>
        osi on o.id = osi.OUTSTOCKORDER_ID
        <where>
            <if test="dto.orgId != null">
                o.Org_Id = #{dto.orgId}
            </if>
            <if test="dto.wareHouseId != null">
                AND o.WAREHOUSE_ID = #{dto.wareHouseId}
            </if>
            <if test="dto.refOrderNo != null and dto.refOrderNo!=''">
                AND o.RefOrderNo = #{dto.refOrderNo}
            </if>
            <if test="dto.areaId != null">
                AND o.Area_Id = #{dto.areaId}
            </if>
            <if test="dto.routeId != null">
                AND o.Route_Id = #{dto.routeId}
            </if>
            <if test="dto.timeS!=null">
                AND o.OrderCreateTime <![CDATA[ >= ]]> #{dto.timeS,jdbcType=VARCHAR}
            </if>
            <if test="dto.timeE!=null">
                AND o.OrderCreateTime <![CDATA[ <= ]]> #{dto.timeE,jdbcType=VARCHAR}
            </if>
            <if test="dto.orderTypes!=null">
                AND o.ordertype in
                <foreach item="item" index="index" collection="dto.orderTypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.orderTypes==null">
                AND o.ordertype in
                <include refid="orderTypeListSql"/>
            </if>
            <if test="dto.notBusinessTypes!=null">
                AND o.BusinessType not in
                <foreach item="item" index="index" collection="dto.notBusinessTypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.packageAttribute!=null and dto.packageAttribute.size()>0">
                AND o.PackageAttribute in
                <foreach item="item" index="index" collection="dto.packageAttribute" open="(" separator="," close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="dto.orderStates!=null">
                AND o.state in
                <foreach item="item" index="index" collection="dto.orderStates" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.expTimeS!=null">
                AND o.ExpectedOutStockTime <![CDATA[ >= ]]> #{dto.expTimeS,jdbcType=VARCHAR}
            </if>
            <if test="dto.expTimeE!=null">
                AND o.ExpectedOutStockTime <![CDATA[ <= ]]> #{dto.expTimeE,jdbcType=VARCHAR}
            </if>
            <!-- 收货城市, 区, 街道查询 -->
            <if test="dto.city!=null and dto.city !=''">
                AND o.City = #{dto.city,jdbcType=VARCHAR}
            </if>
            <if test="dto.county!=null and dto.county !=''">
                AND o.County = #{dto.county,jdbcType=VARCHAR}
            </if>
            <if test="dto.street!=null and dto.street !=''">
                AND o.Street = #{dto.street,jdbcType=VARCHAR}
            </if>
            <if test="dto.orderSourceTypeList != null and dto.orderSourceTypeList.size() > 0">
                AND o.OrderSourceType in
                <foreach item="item" index="index" collection="dto.orderSourceTypeList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by o.OrderCreateTime
        <choose>
            <when test="descending">
                DESC
            </when>
            <otherwise>
                ASC
            </otherwise>
        </choose>
    </select>

    <select id="findOutStockOrderListNew" resultMap="findOutStockOrderResultMap">
        select
        o.ID,
        o.REFORDERNO,
        o.ORG_ID,
        o.STATE,
        o.COMPANYCODE,
        o.ORDERTYPE,
        o.ORDERAMOUNT,
        o.SKUCOUNT,
        o.PACKAGEAMOUNT,
        o.UNITAMOUNT,
        o.WAREHOUSE_ID,
        o.USERNAME, o.SHOPNAME, o.MOBILENO, o.DETAILADDRESS,
        o.PROVINCE, o.CITY, o.COUNTY, o.STREET, o.parterId, o.parterName,
        o.ORDERCREATETIME, o.PICKTIME, o.OUTSTOCKTIME, o.OUTSTOCKUSER, o.REMARK,
        o.CREATEUSER, o.CREATETIME, o.LASTUPDATEUSER, o.LASTUPDATETIME,
        o.Area_Id, o.AreaName, o.Route_Id, o.RouteName, o.RouteSequence, o.RouteSequence, o.DeliveryMarkState,
        o.FromCityId, o.OrderSequence, o.AddressId,
        o.BATCHNO, o.BATCH_ID, o.Business_Id, o.BusinessNo,o.Owner_Id,o.OwnerName, o.ExpectedOutStockTime
        from outstockorder o
        <where>
            <if test="dto.wareHouseId != null">
                o.WAREHOUSE_ID = #{dto.wareHouseId}
            </if>
            <if test="dto.refOrderNo != null and dto.refOrderNo!=''">
                AND o.RefOrderNo = #{dto.refOrderNo}
            </if>
            <if test="dto.areaId != null">
                AND o.Area_Id = #{dto.areaId}
            </if>
            <if test="dto.routeId != null">
                AND o.Route_Id = #{dto.routeId}
            </if>
            <if test="dto.timeS!=null">
                AND o.OrderCreateTime <![CDATA[ >= ]]> #{dto.timeS,jdbcType=VARCHAR}
            </if>
            <if test="dto.timeE!=null">
                AND o.OrderCreateTime <![CDATA[ <= ]]> #{dto.timeE,jdbcType=VARCHAR}
            </if>
            <if test="dto.orderTypes!=null">
                AND o.ordertype in
                <foreach item="item" index="index" collection="dto.orderTypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.packageAttribute!=null and dto.packageAttribute.size()>0">
                AND o.PackageAttribute in
                <foreach item="item" index="index" collection="dto.packageAttribute" open="(" separator="," close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="dto.orderStates!=null">
                AND o.state in
                <foreach item="item" index="index" collection="dto.orderStates" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.expTimeS!=null">
                AND o.ExpectedOutStockTime <![CDATA[ >= ]]> #{dto.expTimeS,jdbcType=VARCHAR}
            </if>
            <if test="dto.expTimeE!=null">
                AND o.ExpectedOutStockTime <![CDATA[ <= ]]> #{dto.expTimeE,jdbcType=VARCHAR}
            </if>
            <!-- 收货城市, 区, 街道查询 -->
            <if test="dto.city!=null and dto.city !=''">
                AND o.City = #{dto.city,jdbcType=VARCHAR}
            </if>
            <if test="dto.county!=null and dto.county !=''">
                AND o.County = #{dto.county,jdbcType=VARCHAR}
            </if>
            <if test="dto.street!=null and dto.street !=''">
                AND o.Street = #{dto.street,jdbcType=VARCHAR}
            </if>

            <if test="dto.warehouseAllocationType != null and dto.warehouseAllocationType != ''">
                and (o.WarehouseAllocationType = #{dto.warehouseAllocationType,jdbcType=INTEGER}
                or o.WarehouseAllocationType is null)
            </if>

        </where>
        order by o.OrderCreateTime
        <choose>
            <when test="descending">
                DESC
            </when>
            <otherwise>
                ASC
            </otherwise>
        </choose>
    </select>

    <select id="findPickedCountBySkuIdsForCreateAllocation"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickedCountDTO">
        SELECT oi.SkuId as skuId, sum(oi.UnitTotalCount) as unitTotalCount
        FROM outstockorderitem oi
        INNER JOIN outstockorder oo on oo.id = oi.outstockorder_id
        WHERE oo.Org_id = #{orgId,jdbcType=INTEGER}
        AND oo.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND oo.RefOrderNo IN
        <foreach collection="orderNoList" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        AND oo.State in (0,1,2,3)
        and oi.SkuId IN
        <foreach collection="skuIdList" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and oo.CreateAllocation = 1
        group by oi.SkuId
    </select>

    <select id="findPickedDetailBySkuIdsForCreateAllocation"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickedDetailDTO">
        SELECT
        oo.Org_id as orgId, oo.Warehouse_Id as warehouseId,
        oi.skuId, oi.ProductName as productName, oo.RefOrderNo as refOrderNo, oo.id as outStockOrderId, 1 as isInternal,
        oo.State as outStockOrderState , oi.SpecName as specName, oi.UnitName as unitName, oi.PackageName as
        packageName, oi.SpecQuantity as specQuantity,
        oi.UnitCount as unitCount, oi.PackageCount as packageCount, oi.UnitTotalCount as unitTotalCount
        FROM outstockorderitem oi
        INNER JOIN outstockorder oo on oo.id = oi.outstockorder_id
        WHERE oo.Org_id = #{orgId,jdbcType=INTEGER}
        AND oo.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND oo.RefOrderNo IN
        <foreach collection="orderNoList" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        AND oo.State in (0,1,2,3)
        and oi.SkuId IN
        <foreach collection="skuIdList" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and oo.CreateAllocation = 1
    </select>

    <select id="listByBatchTaskItemIds" resultMap="findOutStockOrderResultMap">
        select
        <include refid="outstockorderSql"/>
        from outstockorderitem outstockorderitem
        inner join outstockorder outstockorder on outstockorderitem.Outstockorder_Id = outstockorder.id and
        outstockorderitem.Org_id = outstockorder.Org_id
        where outstockorderitem.Org_id = #{orgId,jdbcType=INTEGER}
        and outstockorderitem.BatchTaskItem_Id in
        <foreach collection="batchTaskItemIds" item="batchTaskItemId" open="(" separator="," close=")">
            #{batchTaskItemId,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getByOrderNo" resultMap="findOutStockOrderResultMap">
        select ID,
        REFORDERNO,
        ORG_ID,
        BATCHNO,
        BATCH_ID,
        STATE,
        COMPANYCODE,
        ORDERTYPE,
        ORDERAMOUNT,
        SKUCOUNT,
        PACKAGEAMOUNT,
        UNITAMOUNT,
        WAREHOUSE_ID,
        USERNAME,
        SHOPNAME,
        MOBILENO,
        DETAILADDRESS,
        PROVINCE,
        CITY,
        COUNTY,
        STREET,
        parterId,
        parterName,
        ORDERCREATETIME,
        PICKTIME,
        OUTSTOCKTIME,
        OUTSTOCKUSER,
        REMARK,
        CREATEUSER,
        CREATETIME,
        LASTUPDATEUSER,
        LASTUPDATETIME,
        Area_Id,
        AreaName,
        Route_Id,
        RouteName,
        RouteSequence,
        DeliveryMarkState,
        FromCityId,
        OrderSequence,
        expectedOutStockTime,
        Business_Id,
        BusinessNo,
        Owner_Id,
        OwnerName,
        AssociatedBusinessNo,
        AssociatedBusiness_Id,
        AddressId
        from outstockorder
        where Org_Id = #{orgId,jdbcType=INTEGER}
        and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and RefOrderNo = #{orderNo,jdbcType=VARCHAR, jdbcType=VARCHAR}
    </select>

    <select id="listSimpleBySowTaskNo" resultMap="findOutStockOrderResultMap">
        select
        <include refid="outstockorderSql"/>
        from outstockorderitem outstockorderitem
        inner join outstockorder outstockorder on outstockorderitem.Outstockorder_Id = outstockorder.id and
        outstockorderitem.Org_id = outstockorder.Org_id
        where outstockorderitem.Org_id = #{orgId,jdbcType=INTEGER}
        and outstockorderitem.SowTaskNo = #{sowTaskNo,jdbcType=VARCHAR}
    </select>

    <select id="listOutStockOrderAllByOrderNo" resultMap="findOutStockOrderDetailResultMap">
        select
        <include refid="outstockorderDetailSql"/>
        from outstockorder outstockorder
        inner join outstockorderitem outstockorderitem on outstockorderitem.Outstockorder_Id = outstockorder.id and
        outstockorderitem.Org_id = outstockorder.Org_id
        left join outstockorderitemdetail outstockorderitemdetail on outstockorderitemdetail.OutStockOrderItem_Id =
        outstockorderitem.id and outstockorderitemdetail.Org_id = outstockorderitem.Org_id
        where
        outstockorder.REFORDERNO in
        <foreach item="item" index="index" collection="OrderNos"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="orgId != null">
            and outstockorder.ORG_ID = #{orgId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and outstockorder.WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="listOutStockOrderByBatchIds" resultMap="findOutStockOrderResultMap" parameterType="java.util.List">
        select
        <include refid="outstockorderSql"/>
        from outstockorder outstockorder,
        outstockorderitem outstockorderitem
        where outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        and outstockorder.ORG_ID = #{orgId,jdbcType=INTEGER}
        and outstockorder.WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
        AND outstockorder.Batch_Id in
        <foreach item="item" index="index" collection="batchIds" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="listSimpleBySowTaskNos" resultMap="findOutStockOrderResultMap">
        select
        <include refid="outstockorderSql"/>
        from outstockorderitem outstockorderitem
        inner join outstockorder outstockorder on outstockorderitem.Outstockorder_Id = outstockorder.id and
        outstockorderitem.Org_id = outstockorder.Org_id
        where outstockorderitem.Org_id = #{orgId,jdbcType=INTEGER}
        and outstockorderitem.SowTaskNo in
        <foreach collection="sowTaskNos" item="sowTaskNo" open="(" separator="," close=")">
            #{sowTaskNo}
        </foreach>
    </select>

    <select id="listSorterByOrderNos"
            resultType="com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskSorterDTO">
        select
        distinct
        o.RefOrderNo,
        o.id as orderId,
        task.Sorter_Id as sorterId,
        task.SorterName
        from
        outstockorder o inner join orderitemtaskinfo info on o.id = info.RefOrder_Id
        inner join batchtask task on task.BatchTaskNo = info.BatchTaskNo and info.Org_Id = task.Org_id
        where o.Warehouse_Id = #{warehouseId} and o.RefOrderNo in
        <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
    </select>


    <select id="listOutStockOrderByBatchTaskId" resultMap="findOutStockOrderResultMap">
        SELECT DISTINCT o.ID,
        o.STATE
        from outstockorder o
        INNER JOIN orderitemtaskinfo ot on ot.RefOrder_Id = o.Id
        where o.ORG_ID = #{orgId,jdbcType=INTEGER}
        and o.WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
        and ot.BatchTask_Id = #{batchTaskId,jdbcType=INTEGER}
    </select>

    <select id="listOutStockOrder" resultMap="findOutStockOrderResultMap">
        select
        distinct o.ID,
        o.REFORDERNO,
        o.ORG_ID,
        o.STATE,
        o.COMPANYCODE,
        o.ORDERTYPE,
        o.ORDERAMOUNT,
        o.SKUCOUNT,
        o.PACKAGEAMOUNT,
        o.UNITAMOUNT,
        o.WAREHOUSE_ID,
        o.USERNAME, o.SHOPNAME, o.MOBILENO, o.DETAILADDRESS,
        o.PROVINCE, o.CITY, o.COUNTY, o.STREET, o.parterId, o.parterName,
        o.ORDERCREATETIME, o.PICKTIME, o.OUTSTOCKTIME, o.OUTSTOCKUSER, o.REMARK,
        o.CREATEUSER, o.CREATETIME, o.LASTUPDATEUSER, o.LASTUPDATETIME,
        o.Area_Id, o.AreaName, o.Route_Id, o.RouteName, o.RouteSequence, o.DeliveryMarkState,
        o.FromCityId, o.OrderSequence, o.AddressId,
        o.BATCHNO, o.BATCH_ID, o.Business_Id, o.BusinessNo,o.Owner_Id,o.OwnerName, o.ExpectedOutStockTime,
        o.OrderSourceType,IFNULL(o.Priority,0) as Priority
        from outstockorder o
        <if test="dto.packageAttribute!=null and dto.packageAttribute.size()>0">
            left join orderfeature f on f.Order_id = o.Id
        </if>
        <if test="dto.maxCount != null">
            inner join (
            select concat(o.AddressId,o.ShopName,o.UserName,o.MobileNo,o.DetailAddress) as AddressStr
            from outstockorder o
            where o.Warehouse_Id = #{dto.wareHouseId} and o.PayableAmount > 0
            <if test="dto.orderStates!=null">
                AND o.state in
                <foreach item="item" index="index" collection="dto.orderStates" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.outBoundType!=null">
                AND o.outBoundType =#{dto.outBoundType}
            </if>
            <if test="dto.outBoundTypeList != null and dto.outBoundTypeList.size() > 0">
                AND o.outBoundType in
                <foreach item="item" index="index" collection="dto.outBoundTypeList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            group by o.UserName,o.ShopName,o.MobileNo,o.DetailAddress,o.AddressId
            having sum(o.SkuCount) between #{dto.minCount} and #{dto.maxCount}
            ) tmp on tmp.AddressStr = concat(o.AddressId,o.ShopName,o.UserName,o.MobileNo,o.DetailAddress)
        </if>
        <if test="dto.maxCount == null and dto.minCount != null">
            inner join (
            select concat(o.AddressId,o.ShopName,o.UserName,o.MobileNo,o.DetailAddress) as AddressStr
            from outstockorder o
            where o.Warehouse_Id = #{dto.wareHouseId} and o.PayableAmount > 0
            <if test="dto.orderStates!=null">
                AND o.state in
                <foreach item="item" index="index" collection="dto.orderStates" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.outBoundType!=null">
                AND o.outBoundType =#{dto.outBoundType}
            </if>
            <if test="dto.outBoundTypeList != null and dto.outBoundTypeList.size() > 0">
                AND o.outBoundType in
                <foreach item="item" index="index" collection="dto.outBoundTypeList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            group by o.UserName,o.ShopName,o.MobileNo,o.DetailAddress,o.AddressId
            having sum(o.SkuCount) <![CDATA[ >= ]]> #{dto.minCount}
            ) tmp on tmp.AddressStr = concat(o.AddressId,o.ShopName,o.UserName,o.MobileNo,o.DetailAddress)
        </if>
        <where>
            <if test="dto.wareHouseId != null">
                o.WAREHOUSE_ID = #{dto.wareHouseId}
            </if>
            <if test="dto.refOrderNo != null and dto.refOrderNo!=''">
                AND o.RefOrderNo = #{dto.refOrderNo}
            </if>
            <if test="dto.refOrderNoList != null">
                AND o.RefOrderNo in
                <foreach item="item" index="index" collection="dto.refOrderNoList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.areaId != null">
                AND o.Area_Id = #{dto.areaId,jdbcType=VARCHAR}
            </if>
            <if test="dto.areaIdList != null and dto.areaIdList.size() > 0">
                AND o.Area_Id in
                <foreach item="item" index="index" collection="dto.areaIdList" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="dto.routeId != null">
                AND o.Route_Id = #{dto.routeId,jdbcType=VARCHAR}
            </if>
            <if test="dto.routeIdList != null and dto.routeIdList.size() > 0">
                AND o.Route_Id in
                <foreach item="item" index="index" collection="dto.routeIdList" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="dto.timeS!=null">
                AND o.ORDERCREATETIME <![CDATA[ >= ]]> #{dto.timeS,jdbcType=VARCHAR}
            </if>
            <if test="dto.timeE!=null">
                AND o.ORDERCREATETIME <![CDATA[ <= ]]> #{dto.timeE,jdbcType=VARCHAR}
            </if>
            <if test="dto.orderTypes!=null">
                AND o.ordertype in
                <foreach item="item" index="index" collection="dto.orderTypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.businessTypes!=null">
                AND o.BusinessType in
                <foreach item="item" index="index" collection="dto.businessTypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.notBusinessTypes!=null">
                AND o.BusinessType not in
                <foreach item="item" index="index" collection="dto.notBusinessTypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!--默认排除快递直发订单-->
            <if test="dto.expressFlag==null or dto.expressFlag!=1">
                AND o.DeliveryMode != 7
            </if>
            <if test="dto.expressFlag!=null and dto.expressFlag==1">
                AND o.DeliveryMode = 7
            </if>
            <!--订单特征-->
            <if test="dto.packageAttribute!=null and dto.packageAttribute.size()>0">
                and f.FeatureType in
                <foreach item="item" index="index" collection="dto.packageAttribute" open="(" separator="," close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="dto.orderStates!=null">
                AND o.state in
                <foreach item="item" index="index" collection="dto.orderStates" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.expTimeS!=null">
                AND o.ExpectedOutStockTime <![CDATA[ >= ]]> #{dto.expTimeS,jdbcType=VARCHAR}
            </if>
            <if test="dto.expTimeE!=null">
                AND o.ExpectedOutStockTime <![CDATA[ <= ]]> #{dto.expTimeE,jdbcType=VARCHAR}
            </if>
            <!-- 收货城市, 区, 街道查询 -->
            <if test="dto.city!=null and dto.city !=''">
                AND o.City = #{dto.city,jdbcType=VARCHAR}
            </if>
            <if test="dto.lstCity != null and dto.lstCity.size() > 0">
                AND o.City in
                <foreach item="item" index="index" collection="dto.lstCity" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.county!=null and dto.county !=''">
                AND o.County = #{dto.county,jdbcType=VARCHAR}
            </if>
            <if test="dto.street!=null and dto.street !=''">
                AND o.Street = #{dto.street,jdbcType=VARCHAR}
            </if>
            <if test="dto.outBoundType!=null">
                AND o.outBoundType =#{dto.outBoundType}
            </if>
            <if test="dto.pushState != null">
                AND o.PushState = #{dto.pushState}
            </if>
            <if test="dto.outBoundTypeList != null and dto.outBoundTypeList.size() > 0">
                AND o.outBoundType in
                <foreach item="item" index="index" collection="dto.outBoundTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.packageOrder != null and dto.packageOrder == 1">
                AND o.PackageAmount >= 1 AND o.UnitAmount = 0
            </if>
            <if test="dto.packageOrder != null and dto.packageOrder == 0">
                AND o.PackageAmount = 0 AND o.UnitAmount >= 0
            </if>
            <if test="dto.orderSourceTypeList != null and dto.orderSourceTypeList.size() > 0">
                AND o.OrderSourceType in
                <foreach item="item" index="index" collection="dto.orderSourceTypeList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.warehouseAllocationType != null and dto.warehouseAllocationType != ''">
                and (o.WarehouseAllocationType = #{dto.warehouseAllocationType,jdbcType=INTEGER}
                or o.WarehouseAllocationType is null)
            </if>
        </where>
        order by Priority desc,o.OrderCreateTime
        <if test="dto.orderByCreateTime == null or dto.orderByCreateTime == 0">
            DESC
        </if>
    </select>

    <select id="listByOrderItem" resultMap="findOutStockOrderResultMap" parameterType="java.util.List">
        select
        <include refid="outstockorderSql"/>
        from outstockorder outstockorder inner join outstockorderitem outstockorderitem
        on outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        where
        outstockorder.Org_id = #{orgId,jdbcType=INTEGER}
        <if test="orderItemIds != null and orderItemIds.size()>0">
            AND outstockorderitem.id in
            <foreach item="item" index="index" collection="orderItemIds"
                     open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="wareHouseId != null">
            and outstockorder.WAREHOUSE_ID = #{wareHouseId,jdbcType=INTEGER}
        </if>
        <if test="sowTaskItemIds != null and sowTaskItemIds.size()>0">
            AND outstockorderitem.SowTaskItem_Id in
            <foreach item="item" index="index" collection="sowTaskItemIds"
                     open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="orderIds != null and orderIds.size()>0">
            AND outstockorder.id in
            <foreach item="item" index="index" collection="orderIds"
                     open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>
    <select id="querySowBoundBySowTaskId"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO">
        select SowTask_Id as sowTaskId,boundNo
        from outstockorder
        left join outstockorderitem on outstockorder.id = outstockorderitem.Outstockorder_Id
        where outstockorderitem.SowTask_Id in
        <foreach collection="collect" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and outstockorderitem.Org_id = #{cityId};
    </select>
    <update id="updatePushStateByOrderIds">
        update outstockorder
        set PushState = #{pushState,jdbcType=TINYINT}
        where Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <select id="queryOutBoundType" resultType="java.lang.Integer">
        select outBoundType
        from outstockorder where Org_Id=#{orgId} and BatchNo in
        <foreach collection="batchNos" item="batchNo" open="(" separator="," close=")">
            #{batchNo}
        </foreach>
    </select>

    <select id="listAllInfo" resultMap="findOutStockOrderDetailResultMap">
        select
        <include refid="outstockorderDetailSql"/>
        from outstockorder outstockorder
        inner join outstockorderitem outstockorderitem on outstockorderitem.Outstockorder_Id = outstockorder.id and
        outstockorderitem.Org_id = outstockorder.Org_id
        left join outstockorderitemdetail outstockorderitemdetail on outstockorderitemdetail.OutStockOrderItem_Id =
        outstockorderitem.id and outstockorderitemdetail.Org_id = outstockorderitem.Org_id
        where
        outstockorder.ORG_ID = #{orgId,jdbcType=INTEGER}
        <if test="wareHouseId != null">
            and outstockorder.WAREHOUSE_ID = #{wareHouseId,jdbcType=INTEGER}
        </if>
        <if test="orderIds != null and orderIds.size() > 0">
            AND outstockorder.id in
            <foreach item="item" index="index" collection="orderIds"
                     open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>
    <select id="queryBatchNoByOrder"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO">
        select RefOrderNo,BatchNo
        from outstockorder
        where RefOrderNo in
        <foreach item="item" index="index" collection="refOrderIdList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        and Org_Id =#{orgId}
    </select>


    <update id="updateBatchByPOList" parameterType="list">
        update outstockorder
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="unitamount =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.unitamount != null ">
                        when id = #{po.id} then #{po.unitamount}
                    </if>
                    <if test="po.unitamount == null ">
                        when id = #{po.id} then outstockorder.UnitAmount
                    </if>
                </foreach>
            </trim>
            <trim prefix="packageamount =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.packageamount != null ">
                        when id = #{po.id} then #{po.packageamount}
                    </if>
                    <if test="po.packageamount == null ">
                        when id = #{po.id} then outstockorder.PackageAmount
                    </if>
                </foreach>
            </trim>
            <trim prefix="state =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.state != null ">
                        when id = #{po.id} then #{po.state}
                    </if>
                    <if test="po.state == null ">
                        when id = #{po.id} then outstockorder.State
                    </if>
                </foreach>
            </trim>
            <trim prefix="outstocktime =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.outstocktime != null ">
                        when id = #{po.id} then #{po.outstocktime}
                    </if>
                    <if test="po.outstocktime == null ">
                        when id = #{po.id} then outstockorder.OutStockTime
                    </if>
                </foreach>
            </trim>
            Lastupdatetime = now()
        </trim>
        where id in
        <foreach collection="list" item="po" index="index" open="(" close=")" separator=",">
            #{po.id}
        </foreach>
    </update>

    <select id="queryRouteAndDriverByOrder"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO">
        select o.RouteName as routeName, ob.pickUpUserName as pickUpUserName
        from outstockorder o
        inner join outstockorderitem oi on o.Id = oi.outstockorder_id and o.Org_Id = oi.Org_id
        inner join outboundbatch ob on ob.boundBatchNo = o.boundNo and o.Org_Id = ob.orgId
        where o.Org_id = #{orgId,jdbcType=INTEGER}
        and oi.SowTaskItem_Id in
        <foreach collection="sowTaskItemIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        <if test="locationId != null">
            and oi.locationId = #{locationId,jdbcType=BIGINT}
        </if>
        and o.RouteName is not null and ob.pickUpUserName is not null
    </select>

    <select id="findByBatchIdByRefNos" resultType="java.lang.String">
        select
        outstockorder.BATCH_ID
        from outstockorder outstockorder
        where outstockorder.REFORDERNO in
        <foreach item="item" index="index" collection="OrderNos"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="warehouseId != null">
            and outstockorder.WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="findByBatchIdByIds" resultType="java.lang.String">
        select
        outstockorder.BATCH_ID
        from outstockorder outstockorder
        where outstockorder.id in
        <foreach item="item" index="index" collection="orderIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="warehouseId != null">
            and outstockorder.WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="findByBatchTaskNos" resultMap="findOutStockOrderDetailResultMap">
        select
        <include refid="outstockorderDetailSql"/>
        from outstockorder outstockorder
        inner join outstockorderitem outstockorderitem on outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        left join outstockorderitemdetail outstockorderitemdetail on outstockorderitem.id =
        outstockorderitemdetail.OutStockOrderItem_Id
        inner join orderitemtaskinfo info on info.RefOrder_Id = outstockorder.id
        where outstockorder.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and info.BatchTaskNo in
        <foreach item="item" index="index" collection="batchTaskNos" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findByOutBoundNoList" resultMap="findOutStockOrderDetailResultMap">
        select
        <include refid="outstockorderDetailSql"/>
        from outstockorder outstockorder
        inner join outstockorderitem outstockorderitem on outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        left join outstockorderitemdetail outstockorderitemdetail on outstockorderitem.id =
        outstockorderitemdetail.OutStockOrderItem_Id
        where outstockorder.Org_Id = #{orgId,jdbcType=INTEGER}
        and outstockorder.BoundNo in
        <foreach item="item" index="index" collection="boundNos" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findIdsByBusinessIds" resultType="java.lang.Long">
        select id
        from outstockorder outstockorder
        where outstockorder.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and outstockorder.Business_Id in
        <foreach item="item" index="index" collection="businessIds" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>


    <select id="findByBusinessIds" resultMap="findOutStockOrderDetailResultMap">
        select
        <include refid="outstockorderDetailSql"/>
        from outstockorder outstockorder
        inner join outstockorderitem outstockorderitem on outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        left join outstockorderitemdetail outstockorderitemdetail on outstockorderitem.id =
        outstockorderitemdetail.OutStockOrderItem_Id
        where outstockorder.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and outstockorder.Business_Id in
        <foreach item="item" index="index" collection="businessIds" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findIdByOrderNos" resultMap="OutStockOrderAllResultMap">
        select
        <include refid="Base_Column_List"/>
        from outstockorder
        where WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
        AND REFORDERNO in
        <foreach item="item" index="index" collection="refOrderNos" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findIdByIds" resultMap="OutStockOrderAllResultMap">
        select
        <include refid="Base_Column_List"/>
        from outstockorder
        <where>
            <if test="warehouseId != null">
                and WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
            </if>
            AND id in
            <foreach item="item" index="index" collection="ids"
                     open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </where>
    </select>

    <select id="findRoutInfoByRefOrderNo" resultMap="findOutStockOrderDetailResultMap" parameterType="java.util.List">
        select
        Route_Id as RouteId, RouteName as RouteName,RefOrderNo
        from outstockorder
        where WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
        AND REFORDERNO in
        <foreach item="item" index="index" collection="refOrderNos"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        and route_id is not null limit 0,1
    </select>

    <select id="findOutStockOrderByRefOrderNo" resultMap="findOutStockOrderDetailResultMap"
            parameterType="java.util.List">
        select
        <include refid="outstockorderDetailSql"/>
        from outstockorder outstockorder
        inner join outstockorderitem outstockorderitem on outstockorder.id = outstockorderitem.OUTSTOCKORDER_ID
        left join outstockorderitemdetail outstockorderitemdetail on outstockorderitem.id =
        outstockorderitemdetail.OutStockOrderItem_Id
        where outstockorder.WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
        <if test="refOrderNos != null and refOrderNos.size() > 0">
            AND outstockorder.REFORDERNO in
            <foreach item="item" index="index" collection="refOrderNos"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="boundNos != null and boundNos.size() > 0">
            AND outstockorder.BoundNo in
            <foreach item="item" index="index" collection="boundNos"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="selectByBusinessIdAndWarehouseId" resultMap="findOutStockOrderResultMap">
        select
        <include refid="Base_Column_List"/>
        from outstockorder
        where business_Id In
        <foreach item="item" index="index" collection="businessIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        AND warehouse_Id = #{warehouseId}
    </select>

    <update id="clearBatchInfo">
        update outstockorder set
        batchNo = null, batch_id = null
        where id in
        <foreach collection="idList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="findSimpleOrderInfoByOutBoundNoList"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO">
        select
        oo.batch_Id as batchId,
        oo.warehouse_id warehouseId,
        oo.batchNo,
        oo.id,oo.reforderno,oo.business_id as businessId,
        oo.UserName,
        oo.ShopName,
        oo.MobileNo,
        oo.DetailAddress,
        oo.Province,
        oo.City,
        oo.County,
        oo.Street,
        oo.state,
        oo.orderSourceType,
        oo.Business_Id as BusinessId
        from outstockorder oo
        where 1=1
        <if test="warehouseId != null">
            and oo.WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
        </if>
        AND oo.BoundNo in
        <foreach item="boundNo" index="index" collection="boundNos"
                 open="(" separator="," close=")">
            #{boundNo,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="queryPickUpUserNameByCondition"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO">
        select o.id as id, ob.pickUpUserName as pickUpUserName
        from outstockorder o
        inner join outboundbatch ob on ob.boundBatchNo = o.boundNo and o.Warehouse_Id = ob.warehouseId
        where o.Org_id = #{dto.orgId,jdbcType=INTEGER}
        <if test="dto.wareHouseId != null">
            and o.Warehouse_Id = #{dto.wareHouseId,jdbcType=INTEGER}
        </if>
        <if test="dto.orderIds != null and dto.orderIds.size() > 0">
            and o.id in
            <foreach collection="dto.orderIds" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        and ob.pickUpUserName is not null
    </select>

    <select id="selectByIds" resultMap="OutStockOrderAllResultMap">
        select * from outstockorder where id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findSimpleInfoByBusinessIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO">
        select
        oo.batch_Id as batchId,
        oo.warehouse_id warehouseId,
        oo.batchNo,
        oo.id,oo.reforderno,oo.business_id as businessId,
        oo.UserName,
        oo.ShopName,
        oo.MobileNo,
        oo.DetailAddress,
        oo.Province,
        oo.City,
        oo.County,
        oo.Street,
        oo.state,
        oo.orderSourceType,
        oo.Business_Id as BusinessId
        from outstockorder oo
        where 1=1
        <if test="warehouseId != null">
            and oo.WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
        </if>
        AND oo.Business_Id in
        <foreach item="businessId" index="index" collection="businessIds"
                 open="(" separator="," close=")">
            #{businessId,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findSimpleInfoByIds"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO">
        select
        oo.batch_Id as batchId,
        oo.warehouse_id warehouseId,
        oo.batchNo,
        oo.id,oo.reforderno,oo.business_id as businessId,
        oo.UserName,
        oo.ShopName,
        oo.MobileNo,
        oo.DetailAddress,
        oo.Province,
        oo.City,
        oo.County,
        oo.Street,
        oo.state,
        oo.orderSourceType,
        oo.Business_Id as BusinessId
        from outstockorder oo
        where 1=1
        <if test="warehouseId != null">
            and oo.WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER}
        </if>
        AND oo.id in
        <foreach item="id" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>


    <select id="findSameUserOrderInBatch" resultMap="OutStockOrderAllResultMap">
        select
        a.id,
        a.RefOrderNo,
        a.boundNo,
        a.Org_Id,
        a.BatchNo,
        a.Batch_Id,
        a.State,
        a.CompanyCode,
        a.OrderType,
        a.outBoundType,
        a.BusinessType,
        a.OrderAmount,
        a.PayableAmount,
        a.SkuCount,
        a.PackageAmount,
        a.UnitAmount,
        a.Warehouse_Id,
        a.UserName,
        a.ShopName,
        a.MobileNo,
        a.DetailAddress,
        a.AddressId,
        a.Province,
        a.City,
        a.County,
        a.Street,
        a.ParterId,
        a.ParterName,
        a.remark,
        a.OrderCreateTime,
        a.PickTime,
        a.OutStockTime,
        a.OutStockUser,
        a.Area_Id,
        a.AreaName,
        a.Route_Id,
        a.RouteName,
        a.RouteSequence,
        a.createuser,
        a.createtime,
        a.lastupdateuser,
        a.lastupdatetime,
        a.FromCityId,
        a.DeliveryMode,
        a.OrderSequence,
        a.CreateAllocation,
        a.Business_Id,
        a.AssociatedBusinessNo,
        a.BusinessNo,
        a.AssociatedBusiness_Id,
        a.Owner_Id,
        a.OwnerName,
        a.CrossWareHouse,
        a.AllotType,
        a.PickUpCode,
        a.OrderSourceType
        from outstockorder a
        <if test="orderFeatureType != null">
            inner join orderFeature b
            on a.id = b.Order_Id
        </if>
        where a.addressId = #{addressId, jdbcType=INTEGER}
        and a.warehouse_Id = #{warehouseId, jdbcType=INTEGER}
        <if test="orderFeatureType != null">
            and b.FeatureType = #{orderFeatureType, jdbcType=TINYINT}
        </if>
        <if test="stateList != null">
            and a.state in
            <foreach item="item" index="index" collection="stateList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="addressIdList != null">
            and a.AddressId in
            <foreach item="item" index="index" collection="addressIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orderSequence != null">
            and a.OrderSequence = #{orderSequence,jdbcType=INTEGER}
        </if>
        and a.orderCreateTime <![CDATA[ >= ]]> #{orderCreateTime,jdbcType=VARCHAR}
    </select>

    <update id="updateOrderSequenceByOrderIds">
        update outstockorder
        set OrderSequence = #{orderSequence,jdbcType=INTEGER}
        where Id in
        <foreach collection="orderIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="findByCondition" resultType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO">
        select
        o.id,
        o.Warehouse_Id as warehouseId,
        o.RefOrderNo,
        o.BatchNo,
        o.State,
        o.AddressId,
        o.PackageAmount,
        o.UnitAmount,
        o.UserName,
        o.ShopName,
        o.MobileNo,
        o.DetailAddress,
        o.Province,
        o.City,
        o.County,
        o.Street,
        o.orderSourceType,
        f.FeatureType as packageAttribute
        from outstockorder o
        inner join orderfeature f on f.Order_id = o.Id
        <where>
            <if test="dto.wareHouseId != null">
                o.Warehouse_Id = #{dto.wareHouseId,jdbcType=INTEGER}
            </if>
            <if test="dto.orderIds != null and dto.orderIds.size() > 0">
                AND o.id in
                <foreach item="item" index="index" collection="dto.orderIds" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <!--订单特征-->
            <if test="dto.packageAttribute != null and dto.packageAttribute.size() > 0">
                and f.FeatureType in
                <foreach item="item" index="index" collection="dto.packageAttribute" open="(" separator="," close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="dto.orderStates != null and dto.orderStates.size() > 0">
                AND o.state in
                <foreach item="item" index="index" collection="dto.orderStates" open="(" separator="," close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="dto.addressIdList != null and dto.addressIdList.size() > 0">
                AND o.addressId in
                <foreach item="item" index="index" collection="dto.addressIdList" open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="dto.orgId != null">
                AND o.Org_Id = #{dto.orgId,jdbcType=INTEGER}
            </if>
            <if test="dto.orderSequence != null">
                AND o.OrderSequence = #{dto.orderSequence,jdbcType=INTEGER}
            </if>
        </where>
    </select>
    <select id="findOrderIdsInBatch" resultType="java.lang.Long">
        select id from outstockorder
        where batchNo = #{batchNo, jdbcType=VARCHAR}
        and org_Id = #{orgId, jdbcType=INTEGER}
    </select>

    <select id="findOutStockOrderLocationByBatchNo"
            resultType="com.yijiupi.himalaya.supplychain.waves.domain.bo.OutStockOrderLocationBO">
        select o.BatchNo, o.RefOrderNo, oi.id orderitemid, oi.SkuId, c.LocationId
        from outstockorder o
        inner join outstockorderitem oi on o.id = oi.Outstockorder_Id
        inner join orderitemtaskinfo b on oi.id = b.reforderitem_id
        inner join batchtaskitem c on b.BatchTaskItem_Id = c.id
        where o.org_Id = #{orgId,jdbcType=INTEGER}
        and o.BatchNo = #{batchNo,jdbcType=VARCHAR}
    </select>
</mapper>