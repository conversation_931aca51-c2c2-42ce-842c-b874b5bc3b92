package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/27
 */
public class OutStockOrderItemDetailModDecoratorBO {
    /**
     * 老的detail列表
     */
    private List<OutStockOrderItemPO> oldOutStockOrderItemPOList;
    /**
     * 带detail的列表
     */
    private List<OrderItemTaskInfoPO> orderItemTaskInfoPOList;
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;


    /**
     * 获取 老的detail列表
     *
     * @return oldOutStockOrderItemPOList 老的detail列表
     */
    public List<OutStockOrderItemPO> getOldOutStockOrderItemPOList() {
        return this.oldOutStockOrderItemPOList;
    }

    /**
     * 设置 老的detail列表
     *
     * @param oldOutStockOrderItemPOList 老的detail列表
     */
    public void setOldOutStockOrderItemPOList(List<OutStockOrderItemPO> oldOutStockOrderItemPOList) {
        this.oldOutStockOrderItemPOList = oldOutStockOrderItemPOList;
    }

    /**
     * 获取 带detail的列表
     *
     * @return orderItemTaskInfoPOList 带detail的列表
     */
    public List<OrderItemTaskInfoPO> getOrderItemTaskInfoPOList() {
        return this.orderItemTaskInfoPOList;
    }

    /**
     * 设置 带detail的列表
     *
     * @param orderItemTaskInfoPOList 带detail的列表
     */
    public void setOrderItemTaskInfoPOList(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        this.orderItemTaskInfoPOList = orderItemTaskInfoPOList;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
