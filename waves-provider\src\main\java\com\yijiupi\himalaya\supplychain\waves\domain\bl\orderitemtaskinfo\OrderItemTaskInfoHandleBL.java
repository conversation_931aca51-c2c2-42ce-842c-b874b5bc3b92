package com.yijiupi.himalaya.supplychain.waves.domain.bl.orderitemtaskinfo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoDetailMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.OrderItemTaskInfoUpdateDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/6/5
 */
@Service
public class OrderItemTaskInfoHandleBL {

    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private OrderItemTaskInfoDetailMapper orderItemTaskInfoDetailMapper;

    /**
     * 更新数量工具
     *
     * @param updateDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderItemTaskInfo(OrderItemTaskInfoUpdateDTO updateDTO) {
        OrderItemTaskInfoPO orderItemTaskInfoPO = new OrderItemTaskInfoPO();
        orderItemTaskInfoPO.setId(updateDTO.getId());
        orderItemTaskInfoPO.setMoveCount(updateDTO.getMoveCount());
        orderItemTaskInfoPO.setLackUnitCount(updateDTO.getLackUnitCount());
        orderItemTaskInfoPO.setOverSortCount(updateDTO.getOverSortCount());

        orderItemTaskInfoMapper.updateByPrimaryKeySelective(orderItemTaskInfoPO);

        if (CollectionUtils.isEmpty(updateDTO.getDetailList())) {
            return;
        }
        updateDTO.getDetailList().forEach(m -> {
            OrderItemTaskInfoDetailPO record = new OrderItemTaskInfoDetailPO();
            record.setId(m.getId());
            record.setUnitTotalCount(m.getUnitTotalCount());
            orderItemTaskInfoDetailMapper.updateByPrimaryKeySelective(record);
        });
    }
}
