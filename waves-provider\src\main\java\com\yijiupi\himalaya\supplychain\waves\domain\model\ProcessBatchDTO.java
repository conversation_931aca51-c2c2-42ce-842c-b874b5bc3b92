package com.yijiupi.himalaya.supplychain.waves.domain.model;

import java.io.Serializable;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchWorkSettingDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchCreateTypeEnum;

/**
 * 处理波次对象
 *
 * <AUTHOR>
 * @date 2019/4/16 17:17
 */
public class ProcessBatchDTO implements Serializable {
    private static final long serialVersionUID = 6619702407310273582L;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 波次名称
     */
    private String batchName;

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 追加车次存放货位
     */
    private String locationName;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 内配单标识：内配单波次时传true, 默认false
     */
    private Boolean allocationFlag = false;

    /**
     * 波次生成方式 默认0: 按订单组合 1：按产品+按订单组合
     */
    private Integer batchCreateType = BatchCreateTypeEnum.按订单组合.getType();

    /**
     * 自动创建标识：调度生成的波次为ture, 默认false
     */
    private Boolean autoCreateFlag = false;

    /**
     * 是否拆分（按线路或片区拆分波次） 0:否 1:是
     */
    private Byte groupFlag;

    /**
     * 拆分波次 0: 否，1：按订单拆分 2：按用户拆分
     */
    private Byte orderPickFlag;

    /**
     * 是否生成车次 0:否 1:是
     */
    private Byte trainFlag;

    /**
     * 车次拆分方式 0：不拆分，1:按线路，2：按片区
     */
    private Integer trainType;

    /**
     * 波次区分标示 1：快递直发订单，2：ERP调拨出库单
     */
    private Byte expressFlag;

    /**
     * 波次类别 0：酒批(默认) 1：微酒
     */
    private Byte batchType;

    /**
     * 指定出库位id
     */
    private Long toLocationId;

    /**
     * 指定出库位名称
     */
    private String toLocationName;

    /**
     * 不抛出异常 默认0：需要抛异常， 1：不抛异常
     */
    private Byte notThrowException;

    /**
     * 订单集合
     */
    private List<BatchCreateOrderDTO> orderList;

    /**
     * 网格仓的作业设置（如果为null, 表示非网格仓）
     */
    private BatchWorkSettingDTO workSetting;

    /**
     * 是否跨库：0:否 1:是
     */
    private Byte crossWareHouse;

    /**
     * 操作人
     */
    private Integer operateUserId;
    /**
     * 前置仓内配单是否可以按产品拣货
     */
    private boolean openFrontWarehouseOpenNPProductPick = Boolean.FALSE;

    public Byte getCrossWareHouse() {
        return crossWareHouse;
    }

    public void setCrossWareHouse(Byte crossWareHouse) {
        this.crossWareHouse = crossWareHouse;
    }

    public BatchWorkSettingDTO getWorkSetting() {
        return workSetting;
    }

    public void setWorkSetting(BatchWorkSettingDTO workSetting) {
        this.workSetting = workSetting;
    }

    public List<BatchCreateOrderDTO> getOrderList() {
        return orderList;
    }

    public void setOrderList(List<BatchCreateOrderDTO> orderList) {
        this.orderList = orderList;
    }

    public Byte getNotThrowException() {
        return notThrowException;
    }

    public void setNotThrowException(Byte notThrowException) {
        this.notThrowException = notThrowException;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public Byte getBatchType() {
        return batchType;
    }

    public void setBatchType(Byte batchType) {
        this.batchType = batchType;
    }

    public Byte getExpressFlag() {
        return expressFlag;
    }

    public void setExpressFlag(Byte expressFlag) {
        this.expressFlag = expressFlag;
    }

    public Byte getTrainFlag() {
        return trainFlag;
    }

    public void setTrainFlag(Byte trainFlag) {
        this.trainFlag = trainFlag;
    }

    public Integer getTrainType() {
        return trainType;
    }

    public void setTrainType(Integer trainType) {
        this.trainType = trainType;
    }

    public Byte getOrderPickFlag() {
        return orderPickFlag;
    }

    public void setOrderPickFlag(Byte orderPickFlag) {
        this.orderPickFlag = orderPickFlag;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public Boolean getAllocationFlag() {
        return allocationFlag;
    }

    public void setAllocationFlag(Boolean allocationFlag) {
        this.allocationFlag = allocationFlag;
    }

    public Integer getBatchCreateType() {
        return batchCreateType;
    }

    public void setBatchCreateType(Integer batchCreateType) {
        this.batchCreateType = batchCreateType;
    }

    public Boolean getAutoCreateFlag() {
        return autoCreateFlag;
    }

    public void setAutoCreateFlag(Boolean autoCreateFlag) {
        this.autoCreateFlag = autoCreateFlag;
    }

    public Byte getGroupFlag() {
        return groupFlag;
    }

    public void setGroupFlag(Byte groupFlag) {
        this.groupFlag = groupFlag;
    }

    public Integer getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(Integer operateUserId) {
        this.operateUserId = operateUserId;
    }

    /**
     * 获取 前置仓内配单是否可以按产品拣货
     *
     * @return openFrontWarehouseOpenNPProductPick 前置仓内配单是否可以按产品拣货
     */
    public boolean getOpenFrontWarehouseOpenNPProductPick() {
        return this.openFrontWarehouseOpenNPProductPick;
    }

    /**
     * 设置 前置仓内配单是否可以按产品拣货
     *
     * @param openFrontWarehouseOpenNPProductPick 前置仓内配单是否可以按产品拣货
     */
    public void setOpenFrontWarehouseOpenNPProductPick(boolean openFrontWarehouseOpenNPProductPick) {
        this.openFrontWarehouseOpenNPProductPick = openFrontWarehouseOpenNPProductPick;
    }
}
