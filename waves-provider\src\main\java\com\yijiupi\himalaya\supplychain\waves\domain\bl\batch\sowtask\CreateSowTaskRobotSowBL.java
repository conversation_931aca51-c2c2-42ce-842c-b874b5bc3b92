package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.sowtask;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskResultBO;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskByOutStockOrderResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;
import com.yijiupi.himalaya.supplychain.waves.util.RobotPickConstants;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 开了机器人，开了整件拆零，没开液晶不打包
 * 
 * <AUTHOR>
 * @date 2024/8/18
 */
@Service
public class CreateSowTaskRobotSowBL extends CreateSowTaskBaseBL {

    @Override
    public boolean doSupport(CreateSowTaskBO bo) {
        if (BooleanUtils
            .isTrue(RobotPickConstants.openLiquidNotPackage(bo.getWarehouseConfigDTO(), bo.getWavesStrategyDTO()))) {
            return Boolean.FALSE;
        }
        return BooleanUtils
            .isTrue(RobotPickConstants.isPassageRobotPickOpen(bo.getWavesStrategyDTO(), bo.getPassageDTO()))
            && BooleanUtils.isTrue(
                RobotPickConstants.isWarehouseOpenLargePick(bo.getWavesStrategyDTO(), bo.getWarehouseConfigDTO()));
    }

    @Override
    public CreateSowTaskResultBO doCreateSowTask(CreateSowTaskBO bo) {
        List<OutStockOrderPO> splitOrderList = bo.getSplitOrderList();

        // 开了机器人，开了整件拆零，没开 液晶不打包

        // 存储位有两种，一种是单项拣货任务的（大件数>0），一种是多项拣货任务的（大件数=0，小件数>0）
        // 单项整件有存储位 单项整件无存储位
        List<OutStockOrderItemPO> packageStoreItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick()))
            .filter(SplitWaveOrderUtil::isStoreItem).collect(Collectors.toList());

        List<OutStockOrderItemPO> packageNotStoreItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE.equals(m.getLargePick()))
            .filter(m -> BooleanUtils.isFalse(SplitWaveOrderUtil.isStoreItem(m))).collect(Collectors.toList());

        List<OutStockOrderItemPO> multiStorePackageItemList =
            splitOrderList.stream().flatMap(m -> m.getItems().stream())
                .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY.equals(m.getLargePick()))
                .filter(SplitWaveOrderUtil::isStoreItem).collect(Collectors.toList());

        List<OutStockOrderItemPO> multiNotStorePackageItemList =
            splitOrderList.stream().flatMap(m -> m.getItems().stream())
                .filter(m -> BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY.equals(m.getLargePick()))
                .filter(m -> !SplitWaveOrderUtil.isStoreItem(m)).collect(Collectors.toList());

        List<OutStockOrderItemPO> unitItemList = splitOrderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> !BatchTaskItemLargePickPatternConstants.isLargePick(m.getLargePick()))
            .collect(Collectors.toList());

        // 通道开启了机器人，则生成机器人拣货任务，根据拣货货位判断； 对 otherItemList 还要拆 CreateSowTaskByOutStockOrderResultBO
        CreateSowTaskByOutStockOrderResultBO orderResultBO =
            createSowTaskByOutStockOrderBL.createSowTaskByOutStockOrder(bo);
        WaveCreateDTO waveCreateDTO = orderResultBO.getWaveCreateDTO();

        List<WaveCreateDTO> waveCreateList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(packageStoreItemList)) {
            WaveCreateDTO createDTO =
                SplitWaveOrderUtil.copyWaveCreateDTO(waveCreateDTO, splitOrderList, packageStoreItemList);
            createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE);
            createDTO.setPassageDTO(null);
            waveCreateList.add(createDTO);
        }

        if (CollectionUtils.isNotEmpty(packageNotStoreItemList)) {
            WaveCreateDTO createDTO =
                SplitWaveOrderUtil.copyWaveCreateDTO(waveCreateDTO, splitOrderList, packageNotStoreItemList);
            createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_WHOLE);
            waveCreateList.add(createDTO);
        }

        if (CollectionUtils.isNotEmpty(multiStorePackageItemList)) {
            WaveCreateDTO createDTO =
                SplitWaveOrderUtil.copyWaveCreateDTO(waveCreateDTO, splitOrderList, multiStorePackageItemList);
            createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY);
            waveCreateList.add(createDTO);
        }

        if (CollectionUtils.isNotEmpty(multiNotStorePackageItemList)) {
            WaveCreateDTO createDTO =
                SplitWaveOrderUtil.copyWaveCreateDTO(waveCreateDTO, splitOrderList, multiNotStorePackageItemList);
            createDTO.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY);
            waveCreateList.add(createDTO);
        }

        if (CollectionUtils.isNotEmpty(unitItemList)) {
            List<WaveCreateDTO> otherCreateDTOList =
                waveSplitRobotBL.splitOtherWave(waveCreateDTO, splitOrderList, unitItemList);
            waveCreateList.addAll(otherCreateDTOList);
        }

        CreateSowTaskResultBO createSowTaskResultBO = new CreateSowTaskResultBO();
        createSowTaskResultBO.setWaveCreateDTOList(waveCreateList);
        createSowTaskResultBO.setSowOrderPOList(orderResultBO.getSowOrdersList());
        createSowTaskResultBO.setSowTaskPOList(orderResultBO.getSowTaskPOList());

        return createSowTaskResultBO;
    }
}
