package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.util.List;

public class BatchOutStockDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 波次编号
     */
    private List<String> batchNos;

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 操作人id
     */
    private Integer operateUserId;

    /**
     * 物流公司名称
     */
    private String logisticsCompany;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 物流公司编码
     */
    private String logisticsCompayCode;

    /**
     * 客户录入信息，京东商家编码或顺丰手机号后四位
     */
    private String customerNo;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public List<String> getBatchNos() {
        return batchNos;
    }

    public void setBatchNos(List<String> batchNos) {
        this.batchNos = batchNos;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public Integer getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(Integer operateUserId) {
        this.operateUserId = operateUserId;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getLogisticsCompayCode() {
        return logisticsCompayCode;
    }

    public void setLogisticsCompayCode(String logisticsCompayCode) {
        this.logisticsCompayCode = logisticsCompayCode;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }
}
