/*
 * @ClassName GatherTaskProductDTO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-10-31 10:56:33
 */
package com.yijiupi.himalaya.supplychain.waves.search;

import java.io.Serializable;
import java.math.BigDecimal;

public class CompleteGatherTaskProductSO implements Serializable {
    private static final long serialVersionUID = 5984533243129761028L;
    /**
     * @Fields id id
     */
    private Long id;
    /**
     * @Fields orgId 城市id
     */
    private Integer orgId;
    /**
     * @Fields gatherTaskId 集货任务id
     */
    private Long gatherTaskId;

    /**
     * @Fields productSkuId 产品ID
     */
    private Long productSkuId;
    /**
     * @Fields productName 产品名称
     */
    private String productName;
    /**
     * @Fields specificationId 规格ID
     */
    private Long specificationId;
    /**
     * @Fields productSpecName 规格名称
     */
    private String productSpecName;
    /**
     * @Fields specQuantity 规格大小单位转换系数
     */
    private BigDecimal specQuantity;
    /**
     * @Fields totalCount 集货总数量
     */
    private BigDecimal totalCount;
    /**
     * @Fields takeCount 已集货总数量
     */
    private BigDecimal takeCount;
    /**
     * @Fields createUser 创建人
     */
    private Long createUser;

    /**
     * 获取 id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 城市id
     */
    public Integer getOrgId() {
        return orgId;
    }

    /**
     * 设置 城市id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 集货任务id
     */
    public Long getGatherTaskId() {
        return gatherTaskId;
    }

    /**
     * 设置 集货任务id
     */
    public void setGatherTaskId(Long gatherTaskId) {
        this.gatherTaskId = gatherTaskId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getSpecificationId() {
        return specificationId;
    }

    public void setSpecificationId(Long specificationId) {
        this.specificationId = specificationId;
    }

    public String getProductSpecName() {
        return productSpecName;
    }

    public void setProductSpecName(String productSpecName) {
        this.productSpecName = productSpecName;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getTakeCount() {
        return takeCount;
    }

    public void setTakeCount(BigDecimal takeCount) {
        this.takeCount = takeCount;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }
}