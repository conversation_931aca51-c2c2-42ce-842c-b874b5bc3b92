package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SowTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskFinalResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.sowtask.CreateSowTaskContextBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.ExistSowTaskBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.CreateSowTaskByPassageBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: SowTaskCreateBL
 * @description:
 * @date 2023-02-10 10:31
 */
@Service
public class SowTaskCreateBL {

    @Autowired
    private CreateSowTaskContextBL createSowTaskContextBL;
    @Autowired
    private CreateBatchChangeOrderSequenceBL createBatchChangeOrderSequenceBL;

    private static final Logger LOG = LoggerFactory.getLogger(SowTaskCreateBL.class);

    public CreateSowTaskFinalResultBO createSowTask(CreateSowTaskByPassageBO createSowTaskByPassageBO) {
        WavesStrategyBO wavesStrategyBO = createSowTaskByPassageBO.getWavesStrategyDTO();
        PassageDTO passageDTO = createSowTaskByPassageBO.getPassageDTO();
        BatchBO batchBO = createSowTaskByPassageBO.getBatchBO();
        List<OutStockOrderPO> orderList = createSowTaskByPassageBO.getOrderList();
        WarehouseConfigDTO warehouseConfigDTO = createSowTaskByPassageBO.getWarehouseConfigDTO();

        if (BooleanUtils.isFalse(wavesStrategyBO.getIsMultiOutBound())) {
            LOG.info("[单出库批次]创建播种");
            return createSowTaskByOneOutBound(createSowTaskByPassageBO);
        }

        // 多个出库批次时
        if (wavesStrategyBO.getIsOpenSecondSort()
            && Objects.equals(passageDTO.getSowType(), SowTypeEnum.二次分拣.getType())) {
            logOrderPassageInfo("[多出库批次创建波次]走酒饮二次分拣通道，通道：{}，出库批次号：{}，出库单号：{}", passageDTO, orderList);
            validateSecPickLocation(orderList);
            batchBO.getBatchPO().setSowType(passageDTO.getSowType());

            return createSowTaskByOneOutBound(createSowTaskByPassageBO);
        }

        // 多个出库批次，但不走酒饮二次分拣通道
        // TODO ：处理一个播种任务属于多个outBound场景
        logOrderInfo("[多出库批次创建波次]不走酒饮二次分拣通道，出库批次号：{}，出库单号：{}", orderList);
        return createSowTaskByMultiOutBound(createSowTaskByPassageBO);
    }

    /**
     * 根据一个出库批次的出库单list，创建多个播种任务
     */
    private CreateSowTaskFinalResultBO createSowTaskByOneOutBound(CreateSowTaskByPassageBO createSowTaskByPassageBO) {
        WavesStrategyBO wavesStrategyBO = createSowTaskByPassageBO.getWavesStrategyDTO();
        PassageDTO passageDTO = createSowTaskByPassageBO.getPassageDTO();
        List<OutStockOrderPO> outStockOrderPOList = createSowTaskByPassageBO.getOrderList();
        WarehouseConfigDTO warehouseConfigDTO = createSowTaskByPassageBO.getWarehouseConfigDTO();
        List<WaveCreateDTO> totalWaveCreateDTOList = createSowTaskByPassageBO.getWaveCreateDTOList();
        ExistSowTaskBO existSowTaskBO = createSowTaskByPassageBO.getExistSowTaskPO();

        if (Objects.isNull(existSowTaskBO)) {
            return CreateSowTaskFinalResultBO.getDefaultNormal(createSowTask(createSowTaskByPassageBO,
                splitSowTaskByOrders(outStockOrderPOList, warehouseConfigDTO, wavesStrategyBO, passageDTO)));
        }

        // 如果被追加的播种任务对应的通道id是空的，直接返回
        if (Objects.isNull(existSowTaskBO.getPassageId())) {
            return CreateSowTaskFinalResultBO.getDefaultNormal(createSowTask(createSowTaskByPassageBO,
                splitSowTaskByOrders(outStockOrderPOList, warehouseConfigDTO, wavesStrategyBO, passageDTO)));
        }

        // 如果通道和追加的播种的通道不匹配，正常创建播种任务
        if (!existSowTaskBO.getPassageId().equals(passageDTO.getId())) {
            return CreateSowTaskFinalResultBO.getDefaultNormal(createSowTask(createSowTaskByPassageBO,
                splitSowTaskByOrders(outStockOrderPOList, warehouseConfigDTO, wavesStrategyBO, passageDTO)));
        }

        SowTaskPO existSowTaskPO = existSowTaskBO.getSowTaskPO();

        Integer totalGrids = warehouseConfigDTO.getTotalGrids();
        if (existSowTaskPO.getOrderCount() >= totalGrids) {
            return CreateSowTaskFinalResultBO.getDefaultNormal(createSowTask(createSowTaskByPassageBO,
                splitSowTaskByOrders(outStockOrderPOList, warehouseConfigDTO, wavesStrategyBO, passageDTO)));
        }

        List<CreateSowTaskResultBO> totalBoList = new ArrayList<>();
        int leftGrids = totalGrids - existSowTaskPO.getOrderCount();

        // 根据格子数量拆分 追加的订单
        List<OutStockOrderPO> handleExistSowTaskOrderList =
            getAppendExistSowTaskOrderList(outStockOrderPOList, leftGrids);
        // 剩余的订单
        List<OutStockOrderPO> leftOrderList = getLeftNotAppendOrderList(outStockOrderPOList, leftGrids);

        // 创建正常的播种任务
        if (CollectionUtils.isNotEmpty(leftOrderList)) {
            List<CreateSowTaskResultBO> createSowTaskResultBOList = createSowTask(createSowTaskByPassageBO,
                splitSowTaskByOrders(leftOrderList, warehouseConfigDTO, wavesStrategyBO, passageDTO));
            totalBoList.addAll(createSowTaskResultBOList);
        }

        // 创建追加的播种任务
        CreateSowTaskBO createSowTaskBO = CreateSowTaskByPassageBO.convertToCreateSowTaskBO(createSowTaskByPassageBO);
        createSowTaskBO.setSplitOrderList(handleExistSowTaskOrderList);
        CreateSowTaskResultBO createAppendSowTaskResultBO = createSowTaskContextBL.createSowTask(createSowTaskBO);

        CreateSowTaskResultBO existSowTaskResultBO =
            handleExistSowTaskInfo(createAppendSowTaskResultBO, createSowTaskByPassageBO, existSowTaskPO);

        if (Objects.isNull(existSowTaskResultBO)) {
            return CreateSowTaskFinalResultBO.getDefaultNormal(totalBoList);
        }

        return CreateSowTaskFinalResultBO.getDefault(totalBoList, Collections.singletonList(existSowTaskResultBO));
    }

    private List<OutStockOrderPO> getAppendExistSowTaskOrderList(List<OutStockOrderPO> outStockOrderPOList,
        int leftGrids) {
        if (outStockOrderPOList.size() <= leftGrids) {
            return outStockOrderPOList;
        }

        return outStockOrderPOList.subList(0, leftGrids);
    }

    private List<OutStockOrderPO> getLeftNotAppendOrderList(List<OutStockOrderPO> outStockOrderPOList, int leftGrids) {
        if (outStockOrderPOList.size() <= leftGrids) {
            return Collections.emptyList();
        }

        return outStockOrderPOList.subList(leftGrids, outStockOrderPOList.size());
    }

    private CreateSowTaskResultBO handleExistSowTaskInfo(CreateSowTaskResultBO createSowTaskResultBO,
        CreateSowTaskByPassageBO createSowTaskByPassageBO, SowTaskPO existSowTaskPO) {
        if (CollectionUtils.isEmpty(createSowTaskResultBO.getSowTaskPOList())) {
            return null;
        }

        if (Objects.isNull(existSowTaskPO)) {
            return createSowTaskResultBO;
        }

        int orderCount = createSowTaskResultBO.getSowTaskPOList().stream()
            .collect(Collectors.summingInt(sowTaskPO -> sowTaskPO.getOrderCount()));
        int skuCount = createSowTaskResultBO.getSowTaskPOList().stream()
            .collect(Collectors.summingInt(sowTaskPO -> sowTaskPO.getSkuCount()));
        BigDecimal packageCount = createSowTaskResultBO.getSowTaskPOList().stream()
            .map(sowTaskPO -> sowTaskPO.getPackageAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal unitCount = createSowTaskResultBO.getSowTaskPOList().stream()
            .map(sowTaskPO -> sowTaskPO.getUnitAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);

        List<SowTaskItemPO> sowTaskItemPOList = createSowTaskResultBO.getSowTaskPOList().stream()
            .flatMap(m -> m.getSowTaskItemPOS().stream()).collect(Collectors.toList());

        // 排除入参中列表含有已存在播种的数据
        List<SowOrderPO> totalSowOrderList = createSowTaskByPassageBO.getSowOrderPOList();
        List<SowTaskPO> totalSowTaskList = createSowTaskByPassageBO.getSowTaskPOList();
        List<WaveCreateDTO> totalWaveCreateList = createSowTaskByPassageBO.getWaveCreateDTOList();
        if (CollectionUtils.isNotEmpty(totalSowTaskList)) {
            totalSowTaskList.removeIf(m -> createSowTaskResultBO.getSowTaskPOList().stream().map(SowTaskPO::getId)
                .anyMatch(taskId -> taskId.equals(m.getId())));
        }

        if (CollectionUtils.isNotEmpty(totalSowOrderList)) {
            totalSowOrderList.removeIf(m -> createSowTaskResultBO.getSowOrderPOList().stream().map(SowOrderPO::getId)
                .anyMatch(orderId -> orderId.equals(m.getId())));
        }

        // 更改已存在的sowTask的数量
        existSowTaskPO.setOrderCount(existSowTaskPO.getOrderCount() + orderCount);
        existSowTaskPO.setSkuCount(existSowTaskPO.getSkuCount() + skuCount);
        existSowTaskPO.setPackageAmount(existSowTaskPO.getPackageAmount().add(packageCount));
        existSowTaskPO.setUnitAmount(existSowTaskPO.getUnitAmount().add(unitCount));

        // sowTaskItem设置存在的sowTask的信息
        sowTaskItemPOList.forEach(sowTaskItemPO -> {
            sowTaskItemPO.setSowTaskNo(existSowTaskPO.getSowTaskNo());
            sowTaskItemPO.setSowTaskId(existSowTaskPO.getId());
        });

        existSowTaskPO.setSowTaskItemPOS(sowTaskItemPOList);

        // sowOrder设置为存在的sowTask的信息
        createSowTaskResultBO.getSowOrderPOList().forEach(sowOrderPO -> {
            sowOrderPO.setSowTaskId(existSowTaskPO.getId());
            sowOrderPO.setSowTaskNo(existSowTaskPO.getSowTaskNo());
        });

        createSowTaskResultBO.getWaveCreateDTOList().forEach(waveCreateDTO -> {
            waveCreateDTO.setSowId(existSowTaskPO.getId());
            waveCreateDTO.setSowNo(existSowTaskPO.getSowTaskNo());
            waveCreateDTO.setSowType(existSowTaskPO.getSowTaskType().intValue());
            waveCreateDTO.getOrders().stream().flatMap(o -> o.getItems().stream()).forEach(item -> {
                item.setSowTaskId(existSowTaskPO.getId());
                item.setSowTaskNo(existSowTaskPO.getSowTaskNo());
            });
        });

        CreateSowTaskResultBO existSowTaskResultBO = new CreateSowTaskResultBO();
        existSowTaskResultBO.setSowTaskPOList(Collections.singletonList(existSowTaskPO));
        existSowTaskResultBO.setSowOrderPOList(createSowTaskResultBO.getSowOrderPOList());
        existSowTaskResultBO.setWaveCreateDTOList(createSowTaskResultBO.getWaveCreateDTOList());

        return existSowTaskResultBO;
    }

    private List<CreateSowTaskResultBO> createSowTask(CreateSowTaskByPassageBO createSowTaskByPassageBO,
        Map<Integer, List<OutStockOrderPO>> orderMap) {
        List<WaveCreateDTO> totalWaveCreateDTOList = createSowTaskByPassageBO.getWaveCreateDTOList();
        List<CreateSowTaskResultBO> resultBOS = new ArrayList<>();
        for (List<OutStockOrderPO> oneSplitOrderList : orderMap.values()) {
            if (CollectionUtils.isEmpty(oneSplitOrderList)) {
                continue;
            }

            // 如果开了机器人拣货 和 整件拆零拣货的， 单独拆出来
            // WaveCreateDTO createDTO =
            // createSowTaskByOutStockOrder(wavesStrategyDTO, passageDTO, title, operateUser,
            // cityId,warehouseConfigDTO, batchPO, lstLocations,
            // lstSowTaskPO,locationName, lstSowOrders, driverName,
            // allocationFlag,needRecheck, oneSplitOrderList);
            CreateSowTaskBO createSowTaskBO =
                CreateSowTaskByPassageBO.convertToCreateSowTaskBO(createSowTaskByPassageBO);
            createSowTaskBO.setSplitOrderList(oneSplitOrderList);
            CreateSowTaskResultBO createSowTaskResultBO = createSowTaskContextBL.createSowTask(createSowTaskBO);

            totalWaveCreateDTOList.addAll(createSowTaskResultBO.getWaveCreateDTOList());

            resultBOS.add(createSowTaskResultBO);
        }

        return resultBOS;
    }

    /**
     * 根据多个出库批次的出库单list，创建多个播种任务
     */
    private CreateSowTaskFinalResultBO createSowTaskByMultiOutBound(CreateSowTaskByPassageBO createSowTaskByPassageBO) {
        List<CreateSowTaskFinalResultBO> totalBoList = new ArrayList<>();

        List<OutStockOrderPO> multiOutBoundOrderList = createSowTaskByPassageBO.getOrderList();
        Map<String, List<OutStockOrderPO>> outStockOrderGroup = multiOutBoundOrderList.stream()
            .collect(Collectors.groupingBy(ele -> String.format("%s", ele.getBoundNo())));

        CreateSowTaskByPassageBO copyBo = CreateSowTaskByPassageBO.copyBO(createSowTaskByPassageBO);

        outStockOrderGroup.forEach((boundNo, orderList) -> {
            if (Objects.equals(boundNo, "null")) {
                LOG.info("[多出库批次创建波次]出库批次号为空，订单号：{}",
                    orderList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
                throw new BusinessValidateException("多出库批次创建波次，出库批次号为空");
            }
            copyBo.setOrderList(orderList);
            totalBoList.add(createSowTaskByOneOutBound(copyBo));
        });
        CreateSowTaskFinalResultBO finalResultBO = new CreateSowTaskFinalResultBO();
        finalResultBO.setNormalSowTaskList(totalBoList.stream().map(CreateSowTaskFinalResultBO::getNormalSowTaskList)
            .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList()));
        finalResultBO.setExistSowTaskList(totalBoList.stream().map(CreateSowTaskFinalResultBO::getExistSowTaskList)
            .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList()));

        return finalResultBO;
    }

    // 拆分播种任务
    private Map<Integer, List<OutStockOrderPO>> splitSowTaskByOrders(List<OutStockOrderPO> outStockOrderPOList,
        WarehouseConfigDTO warehouseConfigDTO, WavesStrategyBO wavesStrategyBO, PassageDTO passageDTO) {
        Map<Integer, List<OutStockOrderPO>> listMap = new HashMap<>();
        if (isOneBound(wavesStrategyBO, passageDTO)) {
            listMap.put(1, outStockOrderPOList);
            return listMap;
        }

        // 1、按配置格子数量，拆分拣货任务
        // 酒批仓库按订单组成波次，按订单拆分成播种任务
        return splitSowTask(outStockOrderPOList, warehouseConfigDTO);
    }

    /**
     * 拆分播种任务，按仓库配置中播种分拣最大箱数进行拆分 <br/>
     * 直接拷贝
     * 
     * @param lstOrders
     * @return
     */
    private Map<Integer, List<OutStockOrderPO>> splitSowTask(List<OutStockOrderPO> lstOrders,
        WarehouseConfigDTO warehouseConfigDTO) {
        Map<Integer, List<OutStockOrderPO>> splitMap = new HashMap<>();

        Integer totalGrids = warehouseConfigDTO.getTotalGrids();
        if (null == totalGrids || 0 == totalGrids) {
            throw new BusinessValidateException("当前仓库没有配置播种分拣最大箱数");
        }

        for (int i = 0; i < lstOrders.size(); i += totalGrids) {
            if (i + totalGrids > lstOrders.size()) {
                totalGrids = lstOrders.size() - i;
            }
            List<OutStockOrderPO> newList = lstOrders.subList(i, i + totalGrids);
            splitMap.put(splitMap.size(), newList);
        }

        return splitMap;
    }

    private boolean isOneBound(WavesStrategyDTO wavesStrategyDTO, PassageDTO passageDTO) {
        if (passageDTO.getSowType() == SowTypeEnum.虚仓二次分拣播种.getType()) {
            return Boolean.TRUE;
        }
        if (BooleanUtils.isFalse(wavesStrategyDTO.getIsMultiOutBound())) {
            return Boolean.FALSE;
        }

        if (BooleanUtils.isFalse(wavesStrategyDTO.getIsOpenSecondSort())) {
            return Boolean.FALSE;
        }

        if (passageDTO.getSowType() == SowTypeEnum.二次分拣.getType()) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    private void logOrderInfo(String message, List<OutStockOrderPO> orderList) {
        LOG.info(message, orderList.stream().map(OutStockOrderPO::getBoundNo).distinct().collect(Collectors.toList()),
            orderList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
    }

    private void logOrderPassageInfo(String message, PassageDTO passageDTO, List<OutStockOrderPO> orderList) {
        LOG.info(message, passageDTO.getPassageName(),
            orderList.stream().map(OutStockOrderPO::getBoundNo).distinct().collect(Collectors.toList()),
            orderList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
    }

    private void validateSecPickLocation(List<OutStockOrderPO> orderList) {
        if (orderList.stream().flatMap(ord -> ord.getItems().stream())
            .anyMatch(it -> Objects.isNull(it.getLocationId()))) {
            LOG.info("[二次分拣]出库位为空，订单号：{}",
                orderList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
            throw new BusinessException("二次分拣，出库位为空");
        }
    }

}
