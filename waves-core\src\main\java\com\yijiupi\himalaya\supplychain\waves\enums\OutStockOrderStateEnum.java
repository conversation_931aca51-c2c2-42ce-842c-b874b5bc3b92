package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * <AUTHOR>
 * @Title: himalaya-supplychain-microservice-goodspursueoperate
 * @Package com.yijiupi.himalaya.supplychain.goodspursueoperate.enums
 * @Description:
 * @date 2018/5/5 16:01
 */
public enum OutStockOrderStateEnum {
    /**
     * 枚举
     */
    待调度((byte) 0), 待拣货((byte) 1), 拣货中((byte) 2), 已拣货((byte) 3), 已出库((byte) 4), 已取消((byte) 5), 已作废((byte) 6), 待审核((byte) 7), 待出库((byte) 8),
    调拨中((byte) 9), 已冲销((byte) 10), 已锁定((byte) 15), 归档中((byte) 17), 已归档((byte) 18);

    /**
     * type
     */
    private Byte type;

    OutStockOrderStateEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 返回枚举
     *
     * @param type
     * @return
     */
    public static OutStockOrderStateEnum getEnum(Byte type) {
        OutStockOrderStateEnum e = null;

        if (type != null) {
            for (OutStockOrderStateEnum o : values()) {
                if (o.getType() == type) {
                    e = o;
                }
            }
        }
        return e;
    }
}
