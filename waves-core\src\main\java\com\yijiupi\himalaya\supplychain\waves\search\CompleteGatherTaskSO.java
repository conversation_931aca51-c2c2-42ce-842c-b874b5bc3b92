/*
 * @ClassName GatherTaskDTO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-10-30 17:42:36
 */
package com.yijiupi.himalaya.supplychain.waves.search;

import java.io.Serializable;
import java.util.List;

public class CompleteGatherTaskSO implements Serializable {

    private static final long serialVersionUID = 5378595921671899520L;
    /**
     * @Fields id 集货任务ID
     */
    private Long id;
    /**
     * @Fields orgId 城市id
     */
    private Integer orgId;
    /**
     * @Fields warehouseId 仓库id
     */
    private Integer warehouseId;
    /**
     * @Fields batchTaskId 波次任务Id
     */
    private String batchTaskId;
    /**
     * @Fields batchtaskNo 波次任务编号
     */
    private String batchtaskNo;
    /**
     * @Fields status 状态 0=待完成 1=已完成
     */
    private Byte status;

    /** 集货任务名 */
    private String TaskName;

    /** 创建人 */
    private Long CreateUser;

    /** 更新人 */
    private Long LastUpdateUser;

    /** 集货货位信息集合 */
    private List<CompleteGatherTaskLocationSO> gathertasklocations;

    /** 集货产品信息集合 */
    private List<CompleteGatherTaskProductSO> gathertaskproducts;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public String getBatchtaskNo() {
        return batchtaskNo;
    }

    public void setBatchtaskNo(String batchtaskNo) {
        this.batchtaskNo = batchtaskNo;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getTaskName() {
        return TaskName;
    }

    public void setTaskName(String taskName) {
        TaskName = taskName;
    }

    public Long getCreateUser() {
        return CreateUser;
    }

    public void setCreateUser(Long createUser) {
        CreateUser = createUser;
    }

    public Long getLastUpdateUser() {
        return LastUpdateUser;
    }

    public void setLastUpdateUser(Long lastUpdateUser) {
        LastUpdateUser = lastUpdateUser;
    }

    public List<CompleteGatherTaskLocationSO> getGathertasklocations() {
        return gathertasklocations;
    }

    public void setGathertasklocations(List<CompleteGatherTaskLocationSO> gathertasklocations) {
        this.gathertasklocations = gathertasklocations;
    }

    public List<CompleteGatherTaskProductSO> getGathertaskproducts() {
        return gathertaskproducts;
    }

    public void setGathertaskproducts(List<CompleteGatherTaskProductSO> gathertaskproducts) {
        this.gathertaskproducts = gathertaskproducts;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }
}