package com.yijiupi.himalaya.supplychain.waves.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.batch.IDigitalBatchTaskQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.DigitalBatchTaskQueryBL;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/10/30
 */
@Service
public class DigitalBatchTaskQueryServiceImpl implements IDigitalBatchTaskQueryService {

    @Autowired
    private DigitalBatchTaskQueryBL digitalBatchTaskQueryBL;

    /**
     * 查询电子标签拣货任务信息
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<DigitalBatchTaskDTO> findDigitalBatchTaskList(DigitalBatchTaskQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库信息不能为空！");

        return digitalBatchTaskQueryBL.findDigitalBatchTaskList(queryDTO);
    }
}
