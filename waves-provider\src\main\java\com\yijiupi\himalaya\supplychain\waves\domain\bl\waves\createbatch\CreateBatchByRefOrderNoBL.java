package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.locationrule.LocationRuleQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationRuleEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationRuleQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchBaseBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByRefOrderNoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchGroupTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/10/16
 */
@Service
public class CreateBatchByRefOrderNoBL extends CreateBatchBaseBL {
    @Reference
    private ILocationRuleQueryService iLocationRuleQueryService;

    @Override
    protected void doCreateBatch(CreateBatchBaseBO createBatchBaseBO, List<OutStockOrderPO> outStockOrderPOList) {
        BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO = createBatchBaseBO.getBatchCreateByRefOrderNoDTO();

        WavesStrategyBO wavesStrategyDTO =
            batchCreateDTOConvertor.getByBatchCreateByRefOrderNoDTO(batchCreateByRefOrderNoDTO);

        List<String> refOrderNoList = getRefOrderNoList(createBatchBaseBO);

        handleLocation(batchCreateByRefOrderNoDTO, outStockOrderPOList);

        // 分组方式 1:按线路，2：按片区，3：按司机
        // if (batchCreateByRefOrderNoDTO.getGroupType() != null && batchCreateByRefOrderNoDTO.getGroupType() == 2) {
        outStockOrderPOList = batchOrderProcessBL.getOrderByIndex(outStockOrderPOList, refOrderNoList, false);
        // }

        // 检验订单异常
        batchOrderProcessBL.validateOrderException(outStockOrderPOList);

        // 给内配单原单的产品设置关联货位
        batchOrderProcessBL.addProductLocationByAllocationOrder(outStockOrderPOList);

        createBatchValidateBL.checkStateErrorOrders(outStockOrderPOList);

        ProcessBatchDTO processBatchDTO =
            batchCreateDTOConvertor.convertProcessBatchDTOByBatchCreateByRefOrderNoDTO(batchCreateByRefOrderNoDTO);

        // 处理配送任务
        this.handleByDeliveryTask(batchCreateByRefOrderNoDTO, outStockOrderPOList, processBatchDTO, wavesStrategyDTO);

        // setMapScheduleDefaultLocation(processBatchDTO, batchCreateByRefOrderNoDTO);
        // 创建波次
        processCreateBatch(outStockOrderPOList, wavesStrategyDTO, processBatchDTO);

    }

    private void setMapScheduleDefaultLocation(ProcessBatchDTO processBatchDTO, BatchCreateByRefOrderNoDTO createDTO) {
        if (Objects.isNull(createDTO.getDeliveryCarId())) {
            return;
        }

        if (Objects.nonNull(processBatchDTO.getToLocationId())) {
            return;
        }
        if (Objects.isNull(createDTO.getGroupType())) {
            return;
        }
        if (!BatchGroupTypeEnum.按用户.getType().equals(createDTO.getGroupType())) {
            return;
        }

        LocationRuleQueryDTO locationRuleQueryDTO = new LocationRuleQueryDTO();
        locationRuleQueryDTO.setRuleIdList(Collections.singletonList(createDTO.getDeliveryCarId().toString()));
        locationRuleQueryDTO.setWarehouseId(createDTO.getWarehouseId());
        locationRuleQueryDTO.setRuleType(LocationRuleEnum.CAR.getCode());
        List<LocationRuleDTO> locationRuleDTOList =
            iLocationRuleQueryService.findLocationRuleByCon(locationRuleQueryDTO);
        LOG.info("根据车辆查询出库位 ,入参： {}； 结果：{}", JSON.toJSONString(locationRuleQueryDTO),
            JSON.toJSONString(locationRuleDTOList));
        if (CollectionUtils.isEmpty(locationRuleDTOList)) {
            return;
        }
        LocationRuleDTO locationRuleDTO = locationRuleDTOList.get(0);
        processBatchDTO.setToLocationId(locationRuleDTO.getLocationId());
        processBatchDTO.setToLocationName(locationRuleDTO.getLocationName());
    }

    private void handleLocation(BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO,
        List<OutStockOrderPO> outStockOrderPOList) {
        Long toLocationId = batchCreateByRefOrderNoDTO.getToLocationId();
        if (Objects.nonNull(toLocationId)) {
            // 修改出库位信息
            List<Long> collect = outStockOrderPOList.stream()
                .flatMap(o -> o.getItems().stream().map(OutStockOrderItemPO::getId)).collect(Collectors.toList());
            outStockOrderItemMapper.updateOutStockOrderItemLocationByIds(outStockOrderPOList.get(0).getOrgId(),
                toLocationId, batchCreateByRefOrderNoDTO.getToLocationName(), collect);

            return;
        }
        updateLocation(batchCreateByRefOrderNoDTO, outStockOrderPOList);
    }

    @Override
    protected void doPreCreateBatch(CreateBatchBaseBO createBatchBaseBO) {
        BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO = createBatchBaseBO.getBatchCreateByRefOrderNoDTO();
        LOG.info("新增波次{}", JSON.toJSONString(batchCreateByRefOrderNoDTO));
    }

    @Override
    protected void validateParam(CreateBatchBaseBO createBatchBaseBO) {
        List<String> refOrderNoList = getRefOrderNoList(createBatchBaseBO);

        AssertUtils.notEmpty(refOrderNoList, "生成波次失败，订单不能为空！");
    }

    private List<String> getRefOrderNoList(CreateBatchBaseBO createBatchBaseBO) {
        BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO = createBatchBaseBO.getBatchCreateByRefOrderNoDTO();
        List<String> refOrderNoList = batchCreateByRefOrderNoDTO.getRefOrderNos();
        // 内配订单
        if (!CollectionUtils.isEmpty(batchCreateByRefOrderNoDTO.getOrderList())) {
            refOrderNoList = batchCreateByRefOrderNoDTO.getOrderList().stream().map(BatchCreateOrderDTO::getRefOrderNo)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        }

        return refOrderNoList;
    }

    @Override
    protected void validateBusiness(CreateBatchBaseBO createBatchBaseBO, List<OutStockOrderPO> outStockOrderPOList) {
        AssertUtils.notEmpty(outStockOrderPOList, "出库单不存在！");
    }

    @Override
    protected List<OutStockOrderPO> getOutStockOrder(CreateBatchBaseBO createBatchBaseBO) {
        List<String> refOrderNoList = getRefOrderNoList(createBatchBaseBO);
        List<OutStockOrderPO> outStockOrderPOList =
            outStockOrderMapper.findNoWaveOrderByRefOrderNo(refOrderNoList, createBatchBaseBO.getWarehouseId());
        return outStockOrderPOList;
    }

    @Override
    protected List<OutStockOrderPO> filterOutStockOrder(List<OutStockOrderPO> outStockOrderPOList) {
        return outStockOrderPOList;
    }
}
