package com.yijiupi.himalaya.supplychain.waves.batch;

import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.dto.batch.TransferOrderLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskLocationScaleDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskProductScaleDTO;
import com.yijiupi.himalaya.supplychain.waves.search.CompleteGatherTaskSO;
import com.yijiupi.himalaya.supplychain.waves.search.GatherTaskSO;

/**
 * 集货任务任务接口
 *
 * <AUTHOR> 2019/10/31
 */
public interface IGatherTaskService {

    /**
     * 生成集货任务
     * 
     * @param batchTaskId
     * @param userId
     */
    void createGatherTask(String batchTaskId, Long userId);

    /**
     * 查询集货任务所有出库位 必传batchTaskId 过滤locationName
     * 
     * @param so
     * @return
     */
    List<GatherTaskLocationScaleDTO> findGatherTaskLocation(GatherTaskSO so);

    /**
     * 查询集货任务出库位明细 必传batchTaskId、locationId
     * 
     * @param so
     * @return
     */
    GatherTaskLocationScaleDTO findGatherTaskLocationDetail(GatherTaskSO so);

    /**
     * 查询集货任务所有产品 必传 batchTaskId 过滤productName
     * 
     * @param so
     * @return
     */
    List<GatherTaskProductScaleDTO> findGatherTaskProduct(GatherTaskSO so);

    /**
     * 根据集货产品出库位明细 必传 batchTaskId、gatherTaskProductId
     * 
     * @param so
     * @return
     */
    GatherTaskProductScaleDTO findGatherTaskProductDetail(GatherTaskSO so);

    /**
     * 修改集货任务出库位集货状态 只传id、status(0=待集货，1=已集货)、lastUpdateUser
     * 
     * @param dto
     */
    void updateGatherTaskLocationStatus(GatherTaskLocationDTO dto);

    /**
     * 完成集货任务，不生成波次 包含生成集货任务，修改集货任务状态
     * 
     * @param completeGatherTaskSO
     */
    void completeGatherTask(CompleteGatherTaskSO completeGatherTaskSO);

    void transferOrderLocation(TransferOrderLocationDTO dto);

}
