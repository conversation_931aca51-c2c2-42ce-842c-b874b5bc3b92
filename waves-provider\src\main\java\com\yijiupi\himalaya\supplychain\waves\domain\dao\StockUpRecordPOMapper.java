package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordPO;
import com.yijiupi.himalaya.supplychain.waves.dto.stockup.StockUpRecordQueryParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-10-11 14:32
 **/
@Mapper
public interface StockUpRecordPOMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insert(StockUpRecordPO record);

    int insertOrUpdate(StockUpRecordPO record);

    int insertOrUpdateSelective(StockUpRecordPO record);

    int insertSelective(StockUpRecordPO record);

    StockUpRecordPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StockUpRecordPO record);

    int updateByPrimaryKey(StockUpRecordPO record);

    int updateBatch(List<StockUpRecordPO> list);

    int updateBatchSelective(List<StockUpRecordPO> list);

    int batchInsert(@Param("list") List<StockUpRecordPO> list);

    StockUpRecordPO selectByTaskNO(@Param("taskNO") String taskNO, @Param("warehouseId") Integer warehouseId);

    PageResult<StockUpRecordPO> pageListStockUpRecord(StockUpRecordQueryParam param);
}