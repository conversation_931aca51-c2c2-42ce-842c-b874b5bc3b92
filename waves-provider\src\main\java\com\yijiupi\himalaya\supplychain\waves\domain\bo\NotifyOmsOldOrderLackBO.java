package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemLackDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 没有orderItemTaskInfo的时候组装的缺货信息
 * 
 * <AUTHOR>
 * @date 2023/12/11
 */
public class NotifyOmsOldOrderLackBO {
    /**
     *
     */
    List<BatchTaskItemLackDTO> filterBatchTaskItemLackDTOS;

    /**
     *
     */
    List<String> refOrderNos;
    /**
     *
     */
    String batchNo;
    /**
     *
     */
    Integer operatorUserId;
    /**
     * 仓库id
     */
    private Integer warehouseId;

    public NotifyOmsOldOrderLackBO() {}

    public NotifyOmsOldOrderLackBO(List<BatchTaskItemLackDTO> filterBatchTaskItemLackDTOS, List<String> refOrderNos,
        String batchNo, Integer operatorUserId) {
        this.filterBatchTaskItemLackDTOS = filterBatchTaskItemLackDTOS;
        this.refOrderNos = refOrderNos;
        this.batchNo = batchNo;
        this.operatorUserId = operatorUserId;
    }

    /**
     * 获取
     *
     * @return filterBatchTaskItemLackDTOS
     */
    public List<BatchTaskItemLackDTO> getFilterBatchTaskItemLackDTOS() {
        return this.filterBatchTaskItemLackDTOS;
    }

    /**
     * 设置
     *
     * @param filterBatchTaskItemLackDTOS
     */
    public void setFilterBatchTaskItemLackDTOS(List<BatchTaskItemLackDTO> filterBatchTaskItemLackDTOS) {
        this.filterBatchTaskItemLackDTOS = filterBatchTaskItemLackDTOS;
    }

    /**
     * 获取
     *
     * @return refOrderNos
     */
    public List<String> getRefOrderNos() {
        return this.refOrderNos;
    }

    /**
     * 设置
     *
     * @param refOrderNos
     */
    public void setRefOrderNos(List<String> refOrderNos) {
        this.refOrderNos = refOrderNos;
    }

    /**
     * 获取
     *
     * @return batchNo
     */
    public String getBatchNo() {
        return this.batchNo;
    }

    /**
     * 设置
     *
     * @param batchNo
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * 获取
     *
     * @return operatorUserId
     */
    public Integer getOperatorUserId() {
        return this.operatorUserId;
    }

    /**
     * 设置
     *
     * @param operatorUserId
     */
    public void setOperatorUserId(Integer operatorUserId) {
        this.operatorUserId = operatorUserId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
