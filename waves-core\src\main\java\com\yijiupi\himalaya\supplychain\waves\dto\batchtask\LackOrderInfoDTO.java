/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since Mar 26, 2019 3:17:22 PM
 */
public class LackOrderInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String orderNo;
    /**
     * 组合1，兑奖配送2
     */
    private Integer orderType;

    public static LackOrderInfoDTO of(String orderNo, Integer orderType) {
        LackOrderInfoDTO lackOrderInfoDTO = new LackOrderInfoDTO();
        lackOrderInfoDTO.setOrderNo(orderNo);
        lackOrderInfoDTO.setOrderType(orderType);
        return lackOrderInfoDTO;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public int getOrderType() {
        return orderType;
    }

    public void setOrderType(int orderType) {
        this.orderType = orderType;
    }

}
