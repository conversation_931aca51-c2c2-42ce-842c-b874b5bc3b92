package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/7/8
 */
public class BatchTaskQueryPO {

    private List<Integer> warehouseIds;

    private List<Byte> taskStateList;

    private String startTime;

    private String endTime;
    /**
     * 当前页
     */
    private Integer currentPage = 1;
    /**
     * 每页的数量
     */
    private Integer pageSize = 100;

    /**
     * 获取
     *
     * @return warehouseIds
     */
    public List<Integer> getWarehouseIds() {
        return this.warehouseIds;
    }

    /**
     * 设置
     *
     * @param warehouseIds
     */
    public void setWarehouseIds(List<Integer> warehouseIds) {
        this.warehouseIds = warehouseIds;
    }

    /**
     * 获取
     *
     * @return taskStateList
     */
    public List<Byte> getTaskStateList() {
        return this.taskStateList;
    }

    /**
     * 设置
     *
     * @param taskStateList
     */
    public void setTaskStateList(List<Byte> taskStateList) {
        this.taskStateList = taskStateList;
    }

    /**
     * 获取
     *
     * @return startTime
     */
    public String getStartTime() {
        return this.startTime;
    }

    /**
     * 设置
     *
     * @param startTime
     */
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    /**
     * 获取
     *
     * @return endTime
     */
    public String getEndTime() {
        return this.endTime;
    }

    /**
     * 设置
     *
     * @param endTime
     */
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    /**
     * 获取 当前页
     *
     * @return currentPage 当前页
     */
    public Integer getCurrentPage() {
        return this.currentPage;
    }

    /**
     * 设置 当前页
     *
     * @param currentPage 当前页
     */
    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    /**
     * 获取 每页的数量
     *
     * @return pageSize 每页的数量
     */
    public Integer getPageSize() {
        return this.pageSize;
    }

    /**
     * 设置 每页的数量
     *
     * @param pageSize 每页的数量
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
