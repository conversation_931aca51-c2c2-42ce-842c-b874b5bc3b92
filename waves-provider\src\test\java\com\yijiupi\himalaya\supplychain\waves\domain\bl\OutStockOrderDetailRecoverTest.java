package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.util.Arrays;
import java.util.List;

import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.WavesApp;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.itemdetail.OutStockOrderItemDetailRecoverContextBL;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDetailRecoverDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WavesApp.class)
public class OutStockOrderDetailRecoverTest {

    @Autowired
    private OutStockOrderItemDetailRecoverContextBL outStockOrderItemDetailRecoverContextBL;

    @Test
    public void recoverDetailListTest() {
        OutStockOrderItemDetailRecoverDTO dto = new OutStockOrderItemDetailRecoverDTO();
        dto.setOutStockOrderIds(Arrays.asList(5305157404796482380L));
        List<OutStockOrderItemDetailDTO> dtoList = outStockOrderItemDetailRecoverContextBL.recoverDetailList(dto);

        Assertions.assertThat(dtoList).isNotNull();
    }

}
