package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationBusinessTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/4/23
 */
@Service
public class BatchTaskItemFinishValidateBL {

    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;

    @Reference
    private ILocationService iLocationService;

    public void validatePromotionTransferLegal(List<PickUpDTO> pickUpDTOList, List<BatchTaskItemPO> oldBatchTaskItemPOS,
        BatchTaskPO batchTaskPO, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        if (CollectionUtils.isEmpty(pickUpDTOList)) {
            return;
        }

        List<Long> outStockOrderIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderId)
            .distinct().collect(Collectors.toList());

        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.selectByIds(outStockOrderIds);

        validatePromotionTransfer(pickUpDTOList, oldBatchTaskItemPOS, batchTaskPO, outStockOrderPOList);
    }

    /**
     * <br />
     * 此接口验证促销产品是否选择了正品货位。正品选择了促销货位可以不用管。 <br />
     * <br />
     * SCM-21027 临过期产品商城促销方案 产品的正常订单可用库存，可以包括促销的批次库存。举例：100件可乐，20件促销，80件正常销售，客户正常销售订单下单90件，促销下单20件，
     * 1.则10件促销库存允许给正常订单，促销订单可缺货10件。这部分库存按照先拣先得的原则，正常订单先拣货完成，促销订单拣货库存不足，则促销订单做缺货，
     * 促销订单先拣货完成，正常订单库存不足则正常订单做缺货。正常订单的拣货任务中，优先指派正常的关联货位， <br />
     * 当正常库存不足时， 目标货位自动指向促销的关联货位。但促销订单不允许占用正常销售库存。 <br />
     * 2.促销产品生成拣货任务时，因为单独分配了关联货位，所以是单独的任务项（除非正常订单按批次库存分配时，也分配到了促销货位，但这种场景是正确的）
     *
     * @param pickUpDTOList
     * @param oldBatchTaskItemPOS
     * @param batchTaskPO
     */
    public void validatePromotionTransfer(List<PickUpDTO> pickUpDTOList, List<BatchTaskItemPO> oldBatchTaskItemPOS,
        BatchTaskPO batchTaskPO, List<OutStockOrderPO> outStockOrderPOList) {
        if (CollectionUtils.isEmpty(pickUpDTOList)) {
            return;
        }

        Map<Long, LoactionDTO> loactionDTOMap = convertToLocationMap(pickUpDTOList);

        List<String> batchTaskItemIds =
            oldBatchTaskItemPOS.stream().map(BatchTaskItemPO::getId).distinct().collect(Collectors.toList());
        Integer orgId = Integer.parseInt(batchTaskPO.getOrgId());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByBatchTaskItemIdsAndOrgId(batchTaskItemIds, orgId);
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            return;
        }

        List<Long> orderItemIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderItemId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderItemPO> orderItemPOList = outStockOrderItemMapper.listByIds(orderItemIds);
        // 第三方出库单生成的拣货任务没出库单项
        if (CollectionUtils.isEmpty(orderItemPOList)) {
            return;
        }
        List<OutStockOrderItemPO> promotionItemList = orderItemPOList.stream()
            .filter(m -> ConditionStateEnum.是.getType().equals(m.getIsAdvent())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(promotionItemList)) {
            return;
        }

        Map<Long, OutStockOrderItemPO> promotionItemMap =
            promotionItemList.stream().collect(Collectors.toMap(OutStockOrderItemPO::getId, v -> v));

        Map<String, OrderItemTaskInfoPO> orderItemTaskInfoPOMap =
            orderItemTaskInfoPOList.stream().collect(Collectors.toMap(k -> k.getId().toString(), v -> v));

        Map<Long, OutStockOrderPO> outStockOrderPOMap =
            outStockOrderPOList.stream().collect(Collectors.toMap(OutStockOrderPO::getId, v -> v));

        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            OrderItemTaskInfoPO orderItemTaskInfoPO = orderItemTaskInfoPOMap.get(pickUpDTO.getBusinessId());
            OutStockOrderItemPO outStockOrderItemPO = promotionItemMap.get(orderItemTaskInfoPO.getRefOrderItemId());
            if (Objects.isNull(outStockOrderItemPO)) {
                continue;
            }
            Long locationId = pickUpDTO.getFromLocationId();
            LoactionDTO loactionDTO = loactionDTOMap.get(locationId);
            if (LocationBusinessTypeEnum.促销.getType().equals(loactionDTO.getBusinessType())) {
                continue;
            }

            OutStockOrderPO outStockOrderPO = outStockOrderPOMap.get(outStockOrderItemPO.getOutstockorderId());
            // 促销产品使用了 正品货位，报错
            throw new BusinessValidateException("促销订单 " + outStockOrderPO.getReforderno() + " 拣货不允许占用正品库存");
        }

    }

    private Map<Long, LoactionDTO> convertToLocationMap(List<PickUpDTO> pickUpDTOList) {
        List<Long> locationIds = pickUpDTOList.stream().map(PickUpDTO::getFromLocationId).filter(Objects::nonNull)
            .distinct().collect(Collectors.toList());

        List<LoactionDTO> loactionDTOList = iLocationService.findLocationByIds(locationIds);

        return loactionDTOList.stream().collect(Collectors.toMap(LoactionDTO::getId, v -> v));
    }

}
