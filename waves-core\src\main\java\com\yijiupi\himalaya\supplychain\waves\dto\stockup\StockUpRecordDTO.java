package com.yijiupi.himalaya.supplychain.waves.dto.stockup;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-10-11 15:00
 **/
public class StockUpRecordDTO implements Serializable {
    /**
     * 主键 id
     */
    private Long id;

    /**
     * 城市 id
     */
    private Integer orgId;

    /**
     * 仓库 id
     */
    private Integer warehouseId;

    /**
     * 车次编号
     */
    private String taskNo;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 出库位
     */
    private String location;

    /**
     * 大件数量
     */
    private BigDecimal packageCount;

    /**
     * 小件数量
     */
    private BigDecimal unitCount;

    /**
     * 备货进度
     */
    private String progress;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 车次明细, 包含车次内所有产品信息
     */
    private List<StockUpRecordItemDTO> items;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public String getLicensePlate() {
        return licensePlate;
    }

    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public String getProgress() {
        return progress;
    }

    public void setProgress(String progress) {
        this.progress = progress;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public List<StockUpRecordItemDTO> getItems() {
        return items;
    }

    public void setItems(List<StockUpRecordItemDTO> items) {
        this.items = items;
    }
}
