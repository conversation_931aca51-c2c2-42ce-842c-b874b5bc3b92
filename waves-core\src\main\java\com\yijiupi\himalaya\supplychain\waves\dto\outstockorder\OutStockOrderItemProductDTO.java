package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;

/**
 * 出库单产品
 *
 * <AUTHOR>
 * @date 2019/4/15 15:48
 */
public class OutStockOrderItemProductDTO implements Serializable {

    private static final long serialVersionUID = -6908307854586533387L;

    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 产品规格ID
     */
    private Long productSpecificationId;

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }
}
