package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

public class DirectOutStockDTO implements Serializable {
    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 操作人id
     */
    private Integer operateUserId;

    /**
     * 物流信息
     */
    private List<LogisticsDTO> logisticsList;

    /**
     * 物流公司
     */
    private String logisticsCompany;
    /**
     * 物流公司编码
     */
    private String logisticsCompayCode;
    /**
     * 快递单号
     */
    private String logisticsNo;
    /**
     * 客户录入信息，京东商家编码或顺丰手机号后四位
     */
    private String customerNo;

    /**
     * 溯源码id
     */
    private List<Long> sourceCodeIdList;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public Integer getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(Integer operateUserId) {
        this.operateUserId = operateUserId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public List<LogisticsDTO> getLogisticsList() {
        return logisticsList;
    }

    public void setLogisticsList(List<LogisticsDTO> logisticsList) {
        this.logisticsList = logisticsList;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsCompayCode() {
        return logisticsCompayCode;
    }

    public void setLogisticsCompayCode(String logisticsCompayCode) {
        this.logisticsCompayCode = logisticsCompayCode;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public List<Long> getSourceCodeIdList() {
        return sourceCodeIdList;
    }

    public void setSourceCodeIdList(List<Long> sourceCodeIdList) {
        this.sourceCodeIdList = sourceCodeIdList;
    }
}
