package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 * 标记缺货的时候，拣货缺货的信息
 * <AUTHOR>
 * @date 2023/9/7
 */
public class PartMarkPickInfoDTO implements Serializable {
    /**
     * 是否处理计算逻辑：0：否；1：处理
     */
    private Byte shouldHandleCal;
    /**
     * businessId
     */
    private Long orderId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 订单项信息
     */
    private List<PartMarkPickItemInfoDTO> itemList;

    public PartMarkPickInfoDTO() {
    }

    public PartMarkPickInfoDTO(Byte shouldHandleCal, Long orderId) {
        this.shouldHandleCal = shouldHandleCal;
        this.orderId = orderId;
    }

    /**
     * 获取 是否处理计算逻辑：0：否；1：处理
     *
     * @return shouldHandleCal 是否处理计算逻辑：0：否；1：处理
     */
    public Byte getShouldHandleCal() {
        return this.shouldHandleCal;
    }

    /**
     * 设置 是否处理计算逻辑：0：否；1：处理
     *
     * @param shouldHandleCal 是否处理计算逻辑：0：否；1：处理
     */
    public void setShouldHandleCal(Byte shouldHandleCal) {
        this.shouldHandleCal = shouldHandleCal;
    }

    /**
     * 获取 businessId
     *
     * @return orderId businessId
     */
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 businessId
     *
     * @param orderId businessId
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 订单号
     *
     * @return orderNo 订单号
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置 订单号
     *
     * @param orderNo 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取 订单项信息
     *
     * @return itemList 订单项信息
     */
    public List<PartMarkPickItemInfoDTO> getItemList() {
        return this.itemList;
    }

    /**
     * 设置 订单项信息
     *
     * @param itemList 订单项信息
     */
    public void setItemList(List<PartMarkPickItemInfoDTO> itemList) {
        this.itemList = itemList;
    }

    public static PartMarkPickInfoDTO getDefault(Long businessId) {
        return new PartMarkPickInfoDTO(ConditionStateEnum.否.getType(), businessId);
    }

}
