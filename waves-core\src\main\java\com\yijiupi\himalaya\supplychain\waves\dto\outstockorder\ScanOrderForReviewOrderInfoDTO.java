package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/18
 */
public class ScanOrderForReviewOrderInfoDTO implements Serializable {
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 拣货员
     */
    private String sorterName;
    /**
     * 完成时间
     */
    private String completeTime;
    /**
     * 线路/片区名称
     */
    private String areaRouteName;
    /**
     * 托盘出库位信息
     */
    private List<ScanOrderForReviewLocationInfoDTO> locationList;
    /**
     * 打包箱数
     */
    private Integer packageCount;
    /**
     * 酒饮订单件数
     */
    private String winePackageCount;

    /**
     * 获取 订单号
     *
     * @return orderNo 订单号
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置 订单号
     *
     * @param orderNo 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取 拣货员
     *
     * @return sorterName 拣货员
     */
    public String getSorterName() {
        return this.sorterName;
    }

    /**
     * 设置 拣货员
     *
     * @param sorterName 拣货员
     */
    public void setSorterName(String sorterName) {
        this.sorterName = sorterName;
    }

    /**
     * 获取 完成时间
     *
     * @return completeTime 完成时间
     */
    public String getCompleteTime() {
        return this.completeTime;
    }

    /**
     * 设置 完成时间
     *
     * @param completeTime 完成时间
     */
    public void setCompleteTime(String completeTime) {
        this.completeTime = completeTime;
    }

    /**
     * 获取 线路片区名称
     *
     * @return areaRouteName 线路片区名称
     */
    public String getAreaRouteName() {
        return this.areaRouteName;
    }

    /**
     * 设置 线路片区名称
     *
     * @param areaRouteName 线路片区名称
     */
    public void setAreaRouteName(String areaRouteName) {
        this.areaRouteName = areaRouteName;
    }

    /**
     * 获取 打包箱数
     *
     * @return packageCount 打包箱数
     */
    public Integer getPackageCount() {
        return this.packageCount;
    }

    /**
     * 设置 打包箱数
     *
     * @param packageCount 打包箱数
     */
    public void setPackageCount(Integer packageCount) {
        this.packageCount = packageCount;
    }

    /**
     * 获取 酒饮订单件数
     *
     * @return winePackageCount 酒饮订单件数
     */
    public String getWinePackageCount() {
        return this.winePackageCount;
    }

    /**
     * 设置 酒饮订单件数
     *
     * @param winePackageCount 酒饮订单件数
     */
    public void setWinePackageCount(String winePackageCount) {
        this.winePackageCount = winePackageCount;
    }

    /**
     * 获取 托盘出库位信息
     *
     * @return locationList 托盘出库位信息
     */
    public List<ScanOrderForReviewLocationInfoDTO> getLocationList() {
        return this.locationList;
    }

    /**
     * 设置 托盘出库位信息
     *
     * @param locationList 托盘出库位信息
     */
    public void setLocationList(List<ScanOrderForReviewLocationInfoDTO> locationList) {
        this.locationList = locationList;
    }

    /**
     * 获取 订单id
     *
     * @return orderId 订单id
     */
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 订单id
     *
     * @param orderId 订单id
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
}
