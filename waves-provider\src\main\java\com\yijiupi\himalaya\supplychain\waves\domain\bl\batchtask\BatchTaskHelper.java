package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.WavesStrategyDTO;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupListDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupUserDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupUserSelectSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.EnableStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortGroupPickWayConstants;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ISortGroupService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import static com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum.FS_SALE_SELF_PICKUP_SALE_ORDER;
import static com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderProcessBL.AWARD_NAME;

/**
 * <AUTHOR>
 * @since 2024-05-11 14:13
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class BatchTaskHelper {

    @Resource
    private BatchMapper batchMapper;
    @Resource
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private GlobalCache globalCache;

    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private ISortGroupService sortGroupService;
    private static final Logger LOG = LoggerFactory.getLogger(BatchTaskHelper.class);

    /**
     * 播种墙类型：1:数字型，2:液晶型，3: 液晶不打包
     */
    private static final Byte SOW_NOT_PACKAGE = 3;

    private static final String NP_PRODUCT_BATCH_TASK_NAME = "【内配单分拣】";

    private static final Logger logger = LoggerFactory.getLogger(BatchTaskHelper.class);

    /**
     * 去除拣货任务中的生成了整件任务的订单项的整件数量
     *
     * @param batchTasks 拣货任务
     * @return 去除整件数量后的拣货任务
     */
    public List<BatchTaskDTO> removeBatchTaskCount(List<BatchTaskDTO> batchTasks) {
        if (CollectionUtils.isEmpty(batchTasks) || !isNotPack(batchTasks.get(0).getWarehouseId())) {
            return batchTasks;
        }
        // 过滤出整件拆零的拣货任务项
        Map<String, BatchTaskItemDTO> largeTaskItemMap =
            batchTasks.stream().map(BatchTaskDTO::getBatchTaskItemList).flatMap(Collection::stream)
                .filter(it -> !BatchTaskItemLargePickPatternConstants.isLargePick(it.getLargePickPattern()))
                .collect(Collectors.toMap(BatchTaskItemDTO::getId, Function.identity()));
        logger.info("largeTaskItemMap: {}", JSON.toJSONString(largeTaskItemMap, SerializerFeature.WriteMapNullValue));
        if (largeTaskItemMap.isEmpty()) {
            return batchTasks;
        }
        Map<Long, OutStockOrderItemDTO> orderItemMap = batchTasks.stream().map(BatchTaskDTO::getOutStockOrderItemList)
            .flatMap(Collection::stream).collect(Collectors.toMap(OutStockOrderItemDTO::getId, Function.identity()));
        // 拣货任务项和订单项的关系
        Set<String> batchTaskItemIds = largeTaskItemMap.keySet();
        Map<String, Set<Long>> batchTaskOrderItemMap = orderItemTaskInfoMapper
            .listOrderItemTaskInfoByBatchTaskItemIds(batchTaskItemIds).stream().collect(groupByTaskItemId());
        logger.info("batchTaskOrderItemMap: {}",
            JSON.toJSONString(batchTaskOrderItemMap, SerializerFeature.WriteMapNullValue));
        // 订单项和拣货任务项的关系
        Map<Set<Long>, Set<String>> orderItemBatchTaskMap = batchTaskOrderItemMap.entrySet().stream().collect(
            Collectors.groupingBy(Map.Entry::getValue, Collectors.mapping(Map.Entry::getKey, Collectors.toSet())));
        // 过滤出整件拆零的订单项
        List<OutStockOrderItemDTO> largeOrderItems = batchTaskItemIds.stream()
            .filter(batchTaskOrderItemMap::containsKey).map(batchTaskOrderItemMap::get).flatMap(Collection::stream)
            .map(orderItemMap::get).filter(Objects::nonNull).collect(Collectors.toList());
        logger.info("orderItemBatchTaskMap: {}",
            JSON.toJSONString(orderItemBatchTaskMap, SerializerFeature.WriteMapNullValue));
        // 处理订单项的数量
        for (OutStockOrderItemDTO item : largeOrderItems) {
            BigDecimal unitTotalCount = item.getUnitTotalCount();
            // 拣货任务项与订单项的数量不一致才需要处理
            boolean isDiffTotalCount =
                orderItemBatchTaskMap.entrySet().stream().filter(it -> it.getKey().contains(item.getId()))
                    .map(Map.Entry::getValue).flatMap(it -> it.stream().map(largeTaskItemMap::get))
                    .anyMatch(it -> it.getUnitTotalCount().compareTo(unitTotalCount) != 0);
            if (!isDiffTotalCount) {
                continue;
            }
            // 大件小数量
            BigDecimal packageUnitCount = item.getPackageCount().multiply(item.getSpecQuantity());
            item.setUnitTotalCount(unitTotalCount.subtract(packageUnitCount));
        }
        return batchTasks;
    }

    /**
     * 添加拣货属性
     */
    public void setBatchTaskName(BatchTaskPO task, WavesStrategyBO strategy, String passageName, WaveCreateDTO create,
        boolean hasAllotOrder) {
        task.setPickingType(strategy.getPickingType());
        task.setPickingGroupStrategy(strategy.getPickingGroupStrategy());
        // 按通道拣货，把名字拼在拣货任务属性前边
        passageName = StringUtils.isNotEmpty(passageName) ? passageName + "-" : "";
        String toWarehouseName = create.getToWarehouseName();
        toWarehouseName = StringUtils.isNotEmpty(toWarehouseName) ? toWarehouseName + "-" : "";
        String sortGroupName = task.getSortGroupName();
        sortGroupName = StringUtils.isNotEmpty(sortGroupName) ? sortGroupName + "-" : "";
        String pickType = PickingTypeEnum.getEnumByName(strategy.getPickingType());
        task.setBatchTaskName(String.format("%s%s%s%s", toWarehouseName, passageName, sortGroupName, pickType));
        task.setRemark(create.isIsAwardOrderTask() ? AWARD_NAME : "");
        if (create.isIsAwardOrderTask()) {
            task.setBatchTaskName(task.getBatchTaskName().concat("-").concat(AWARD_NAME));
        }
        if (strategy.getIsOpenSecondSort() && strategy.getIsMultiOutBound()
            && StringUtils.isNotEmpty(create.getDriverName())) {
            task.setBatchTaskName(task.getBatchTaskName().concat("-").concat(create.getDriverName()));
        }
        if (BooleanUtils.isTrue(create.getOpenFrontWarehouseOpenNPProductPick()) && hasAllotOrder) {
            String existName = StringUtils.isNotEmpty(task.getBatchTaskName()) ? "-" + task.getBatchTaskName() : "";
            task.setBatchTaskName(NP_PRODUCT_BATCH_TASK_NAME.concat(existName));
        }
        BatchPO batch = batchMapper.findBatchByIds(Collections.singletonList(task.getBatchId())).get(0);
        if (BatchTypeEnum.大客户.valueEquals(batch.getBatchType()) || anyBigCustomerOrder(create.getOrders())) {
            task.setBatchTaskName("融销产品-按" + pickType);
        }
        if (BatchTaskKindOfPickingConstants.PICKING_SORTING.equals(create.getKindOfPicking())) {
            task.setBatchTaskName(task.getBatchTaskName().concat("-").concat("边拣边播"));
        }
    }

    private Collector<OrderItemTaskInfoPO, ?, Map<String, Set<Long>>> groupByTaskItemId() {
        Collector<OrderItemTaskInfoPO, ?, Set<Long>> mapToOrderItemSet =
            Collectors.mapping(OrderItemTaskInfoPO::getRefOrderItemId, Collectors.toSet());
        return Collectors.groupingBy(OrderItemTaskInfoPO::getBatchTaskItemId, mapToOrderItemSet);
    }

    private boolean isNotPack(Integer warehouseId) {
        return SOW_NOT_PACKAGE.equals(warehouseConfigService.getConfigByWareHouseId(warehouseId).getSowControlType());
    }

    private boolean anyBigCustomerOrder(List<OutStockOrderPO> orders) {
        if (orders == null || orders.isEmpty()) {
            return false;
        }
        return orders.stream().anyMatch(it -> FS_SALE_SELF_PICKUP_SALE_ORDER.valueEquals(it.getOutBoundType()));
    }

    public void handleSorterGroupBatchTask(SortGroupListDTO sortGroupListDTO, BatchTaskPO batchTaskPO, Long sowId,
        WavesStrategyBO wavesStrategyDTO) {

        if (Objects.isNull(batchTaskPO.getPickPattern())) {
            batchTaskPO.setPickPattern(BatchTaskPickPatternEnum.人工拣货.getType());
        }
        // 按产品拣货：根据货位，货区，类目，获取分拣员
        if (Objects.isNull(sortGroupListDTO)) {
            return;
        }

        Long groupId = sortGroupListDTO.getId();
        batchTaskPO.setSortGroupId(groupId);
        batchTaskPO.setSortGroupName(sortGroupListDTO.getName());

        if (needHandleSorter(wavesStrategyDTO, sowId)) {
            SortGroupUserDTO sortGroupUserDTO = getSortUserConfig(groupId);
            batchTaskPO.setSorter(sortGroupUserDTO.getUserName());
            batchTaskPO.setSorterId(sortGroupUserDTO.getUserId());
        } else {
            LOG.info("抢单模式下，拣货任务不分配拣货员！");
        }

        resetDigitalBatchTaskPickPattern(batchTaskPO, sortGroupListDTO, wavesStrategyDTO);
    }

    private void resetDigitalBatchTaskPickPattern(BatchTaskPO batchTaskPO, SortGroupListDTO sortGroupListDTO,
        WavesStrategyBO wavesStrategyBO) {
        // 没有开启电子标签，不走以下逻辑
        if (BooleanUtils.isFalse(wavesStrategyBO.getOpenDigitalPickingSystem())) {
            return;
        }
        if (SortGroupPickWayConstants.DIGITAL.equals(sortGroupListDTO.getSortGroupPickWay())) {
            batchTaskPO.setPickPattern(BatchTaskPickPatternEnum.电子标签.getType());
        }
    }

    private boolean needHandleSorter(WavesStrategyBO wavesStrategyDTO, Long sowTaskId) {
        if (BooleanUtils.isTrue(globalCache.openDigitalTag(wavesStrategyDTO.getWarehouseId()))
            && Objects.nonNull(sowTaskId)) {
            return Boolean.TRUE;
        }

        // 获取当前仓库的拣货任务模式
        WarehouseConfigDTO warehouseConfigDTO = null;
        try {
            warehouseConfigDTO = globalCache.getWarehouseConfigDTO(wavesStrategyDTO.getWarehouseId());
        } catch (Exception e) {
            LOG.info("获取仓库的拣货任务模式失败！", e);
        }
        if (warehouseConfigDTO != null && Objects.equals(warehouseConfigDTO.getBatchTaskType(),
            com.yijiupi.himalaya.supplychain.enums.BatchTaskTypeEnum.抢单.getType())) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * 获取分拣员
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2018/4/26 9:24
     */
    private SortGroupUserDTO getSortUserConfig(Long groupId) {
        SortGroupUserSelectSO sortGroupUserSelectSO = new SortGroupUserSelectSO();
        // 指定分区Id
        sortGroupUserSelectSO.setGroupId(groupId);
        List<SortGroupUserDTO> sortGroupUserDTOS = sortGroupService.listGroupUserBySelect(sortGroupUserSelectSO);
        SortGroupUserDTO sortGroupUserDTO = new SortGroupUserDTO();
        if (CollectionUtils.isEmpty(sortGroupUserDTOS)) {
            return sortGroupUserDTO;
        }
        List<SortGroupUserDTO> enableUserList = sortGroupUserDTOS.stream()
            .filter(m -> EnableStateEnum.ENABLE.getType() == m.getState()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(enableUserList)) {
            LOG.info("无可用分拣员 ， groupId : {}", groupId);
            return sortGroupUserDTO;
        }

        sortGroupUserDTO = enableUserList.get(0);
        return sortGroupUserDTO;
    }

    public void setTaskWarehouseFeatureType(BatchTaskPO batchTaskPO, WavesStrategyBO wavesStrategyBO) {
        if (BooleanUtils.isFalse(wavesStrategyBO.getOpenWarehouseSeparateAttributeConfig())) {
            batchTaskPO.setTaskWarehouseFeatureType(TaskWarehouseFeatureTypeConstants.TASK_WAREHOUSE_FEATURE_DEFAULT);
            return;
        }

        if (Objects.isNull(wavesStrategyBO.getFeatureType())) {
            batchTaskPO.setTaskWarehouseFeatureType(TaskWarehouseFeatureTypeConstants.TASK_WAREHOUSE_FEATURE_DEFAULT);
            return;
        }

        if (wavesStrategyBO.getFeatureType() <= 0) {
            batchTaskPO.setTaskWarehouseFeatureType(TaskWarehouseFeatureTypeConstants.TASK_WAREHOUSE_FEATURE_DEFAULT);
            return;
        }

        if (OrderFeatureConstant.FEATURE_TYPE_DRINKING.equals(wavesStrategyBO.getFeatureType())) {
            batchTaskPO.setTaskWarehouseFeatureType(TaskWarehouseFeatureTypeConstants.TASK_WAREHOUSE_FEATURE_WINE);
            return;
        }
        if (OrderFeatureConstant.FEATURE_TYPE_REST.equals(wavesStrategyBO.getFeatureType())) {
            batchTaskPO.setTaskWarehouseFeatureType(TaskWarehouseFeatureTypeConstants.TASK_WAREHOUSE_FEATURE_REST);
        }
    }

}
