package com.yijiupi.himalaya.supplychain.waves.domain.bl.address;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.baseutil.NullUtils;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.BizUserAddress;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.UserAddressConstant;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IUserAddressService;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/9/18
 */
@Service
public class UserAddressTransferBL {

    @Reference
    private IUserAddressService iUserAddressService;

    public Map<Integer, Integer> getTransferAddressIdByOrder(Integer ignored, List<OutStockOrderPO> outStockOrders) {
        List<Integer> addressIds = outStockOrders.stream().map(OutStockOrderPO::getAddressId)
                .filter(Objects::nonNull).filter(it -> !NullUtils.isNullInteger(it))
                .distinct().collect(Collectors.toList());
        return getTransferAddressId(addressIds);
    }

    private Map<Integer, Integer> getTransferAddressId(List<Integer> addressIds) {
        List<BizUserAddress> bizUserAddressesList = iUserAddressService.findBizUserIdByFromAddressId(addressIds);
        if (CollectionUtils.isEmpty(bizUserAddressesList)) {
            return addressIds.stream().distinct().collect(Collectors.toMap(k -> k, v -> v));
        }
        return bizUserAddressesList.stream().filter(this::filterValidAddress)
                .collect(Collectors.toMap(k -> k.getFromAddressId().intValue(), BizUserAddress::getId));
    }

    /**
     * 只需要 {@link BizUserAddress#getFromAddressType()} 为 {@link UserAddressConstant#ADDRESS_TYPE_JIUPI} 或为 null 的地址
     *
     * @param address 地址信息
     */
    private boolean filterValidAddress(BizUserAddress address) {
        Byte fromAddressType = address.getFromAddressType();
        return fromAddressType == null || UserAddressConstant.ADDRESS_TYPE_JIUPI.equals(fromAddressType);
    }

}
