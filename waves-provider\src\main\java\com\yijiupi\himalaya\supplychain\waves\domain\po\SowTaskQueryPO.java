package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.util.List;

import com.yijiupi.himalaya.base.search.PageCondition;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/7/8
 */
public class SowTaskQueryPO extends PageCondition {

    private List<Integer> warehouseIds;

    private List<Byte> states;

    private String startTime;

    private String endTime;

    /**
     * 获取
     *
     * @return warehouseIds
     */
    public List<Integer> getWarehouseIds() {
        return this.warehouseIds;
    }

    /**
     * 设置
     *
     * @param warehouseIds
     */
    public void setWarehouseIds(List<Integer> warehouseIds) {
        this.warehouseIds = warehouseIds;
    }

    /**
     * 获取
     *
     * @return states
     */
    public List<Byte> getStates() {
        return this.states;
    }

    /**
     * 设置
     *
     * @param states
     */
    public void setStates(List<Byte> states) {
        this.states = states;
    }

    /**
     * 获取
     *
     * @return startTime
     */
    public String getStartTime() {
        return this.startTime;
    }

    /**
     * 设置
     *
     * @param startTime
     */
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    /**
     * 获取
     *
     * @return endTime
     */
    public String getEndTime() {
        return this.endTime;
    }

    /**
     * 设置
     *
     * @param endTime
     */
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
