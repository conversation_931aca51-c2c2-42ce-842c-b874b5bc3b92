package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Title: himalaya-supplychain-microservice-waves
 * @Package com.yijiupi.himalaya.supplychain.waves.dto.batch
 * @Description:
 * @date 2018/4/2 10:04
 */
public class BatchCreateByRefOrderNoDTO implements Serializable {
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 拣货方式 1 按订单拣货 2 按产品拣货
     */
    private Byte pickingType;
    /**
     * 拣货分组策略 1货区 2货位 3类目
     */
    private Byte pickingGroupStrategy;

    private List<String> refOrderNos;

    /**
     * 操作人
     */
    private String operateUser;

    private String title;

    /**
     * 分组方式 1:按线路，2：按片区，3：按司机
     */
    private Integer groupType;

    /**
     * 通道拣货方式：0 不开启,1 按类目通道,2 按货位通道
     */
    private Byte passPickType;

    /**
     * 追加车次存放货位
     */
    private String locationName;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 内配单标识：内配单生成波次时传true, 否则传false
     */
    private Boolean allocationFlag = false;

    /**
     * 指定出库位id
     */
    private Long toLocationId;

    /**
     * 指定出库位名称
     */
    private String toLocationName;

    /**
     * 订单集合
     */
    private List<BatchCreateOrderDTO> orderList;

    /**
     * 拆分波次 0: 否，1：按订单拆分 2：按用户拆分
     */
    private Byte orderPickFlag;

    /**
     * 操作人ID
     */
    private Integer operateUserId;
    /**
     * 配送车辆ID
     */
    private Long deliveryCarId;
    /**
     * 物流公司ID
     */
    private Long logisticsCompanyId;
    /**
     * 配送车次信息
     */
    private List<DeliveryTaskDTO> deliveryTaskList;

    /**
     * 扩展属性
     * @see BatchCreateByRefOrderNoExtendDTO
     */
    private String extendInfo;

    public List<DeliveryTaskDTO> getDeliveryTaskList() {
        return deliveryTaskList;
    }

    public void setDeliveryTaskList(List<DeliveryTaskDTO> deliveryTaskList) {
        this.deliveryTaskList = deliveryTaskList;
    }

    public Long getDeliveryCarId() {
        return deliveryCarId;
    }

    public void setDeliveryCarId(Long deliveryCarId) {
        this.deliveryCarId = deliveryCarId;
    }

    public Long getLogisticsCompanyId() {
        return logisticsCompanyId;
    }

    public void setLogisticsCompanyId(Long logisticsCompanyId) {
        this.logisticsCompanyId = logisticsCompanyId;
    }

    public Byte getOrderPickFlag() {
        return orderPickFlag;
    }

    public void setOrderPickFlag(Byte orderPickFlag) {
        this.orderPickFlag = orderPickFlag;
    }

    public List<BatchCreateOrderDTO> getOrderList() {
        return orderList;
    }

    public void setOrderList(List<BatchCreateOrderDTO> orderList) {
        this.orderList = orderList;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public Boolean getAllocationFlag() {
        return allocationFlag;
    }

    public void setAllocationFlag(Boolean allocationFlag) {
        this.allocationFlag = allocationFlag;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Byte getPassPickType() {
        return passPickType;
    }

    public void setPassPickType(Byte passPickType) {
        this.passPickType = passPickType;
    }

    public Integer getGroupType() {
        return groupType;
    }

    public void setGroupType(Integer groupType) {
        this.groupType = groupType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Byte getPickingType() {
        return pickingType;
    }

    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    public Byte getPickingGroupStrategy() {
        return pickingGroupStrategy;
    }

    public void setPickingGroupStrategy(Byte pickingGroupStrategy) {
        this.pickingGroupStrategy = pickingGroupStrategy;
    }

    public List<String> getRefOrderNos() {
        return refOrderNos;
    }

    public void setRefOrderNos(List<String> refOrderNos) {
        this.refOrderNos = refOrderNos;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public Integer getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(Integer operateUserId) {
        this.operateUserId = operateUserId;
    }

    /**
     * 获取 扩展属性
     *
     * @return extendInfo 扩展属性
     */
    public String getExtendInfo() {
        return this.extendInfo;
    }

    /**
     * 设置 扩展属性
     *
     * @param extendInfo 扩展属性
     */
    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo;
    }
}
