package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * <AUTHOR>
 * @Title: himalaya-supplychain-microservice-waves
 * @Package com.yijiupi.himalaya.supplychain.waves.enums
 * @Description:
 * @date 2018/4/17 13:47
 */
public class ScheduleStrategyQueryConstants {

    /**
     *
     */
    public static final Byte TIMER = 1;
    /**
     *
     */
    public static final Byte AFTERBATCH = 2;

    /**
     * 波次分组
     */
    public static final byte GROUP_TYPE_BATCH = 1;
    /**
     * 区域分组
     */
    public static final byte GROUP_TYPE_AREA = 2;
    /**
     * 线路分组
     */
    public static final byte GROUP_TYPE_ROUTING = 3;

    /**
     * 区域排序
     */
    public static final byte SORT_TYPE_AREA = 1;
    /**
     * 线路排序
     */
    public static final byte SORT_TYPE_ROUTING = 2;

    /**
     * 区域合并
     */
    public static final byte COMBINE_TYPE_AREA = 1;
    /**
     * 线路合并
     */
    public static final byte COMBINE_TYPE_ROUTING = 2;

    /**
     * 车次合并类型 1合并 2不合并
     */
    public static final byte COMBINE_TYPE_MERGE_YES = 1;
    public static final byte COMBINE_TYPE_MERGE_NO = 2;

    /**
     * 是否考虑车容 1考虑2不考虑
     */
    public static final byte CAPACITY_TYPE_YES = 1;
    public static final byte CAPACITY_TYPE_NO = 2;

}
