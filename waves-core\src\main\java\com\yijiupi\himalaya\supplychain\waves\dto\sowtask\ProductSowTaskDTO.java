package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 播种任务的产品聚合
 * 
 * <AUTHOR>
 * @Date 2022/6/25
 */
public class ProductSowTaskDTO implements Serializable {
    private static final long serialVersionUID = -1465180326021684397L;
    /**
     * 城市ID
     */
    private Integer orgId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 产品skuId
     */
    private Long productSkuId;
    /**
     * 产品skuId
     */
    private BigDecimal saleSpecQuantity;
    /**
     * 缺货小单位数量
     */
    private BigDecimal lackUnitCount;
    /**
     * 操作人名称
     */
    private String optUserName;
    /**
     * 操作人id
     */
    private Integer optUserId;
    /**
     * 播种列表
     */
    private List<SowTaskInfoDTO> sowTaskList;

    public BigDecimal getSaleSpecQuantity() {
        return saleSpecQuantity;
    }

    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public BigDecimal getLackUnitCount() {
        return lackUnitCount;
    }

    public void setLackUnitCount(BigDecimal lackUnitCount) {
        this.lackUnitCount = lackUnitCount;
    }

    public String getOptUserName() {
        return optUserName;
    }

    public void setOptUserName(String optUserName) {
        this.optUserName = optUserName;
    }

    public Integer getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }

    public List<SowTaskInfoDTO> getSowTaskList() {
        return sowTaskList;
    }

    public void setSowTaskList(List<SowTaskInfoDTO> sowTaskList) {
        this.sowTaskList = sowTaskList;
    }
}
