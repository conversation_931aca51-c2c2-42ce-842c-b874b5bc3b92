package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.slitbyorder;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.SourceType;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderRequestBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.SplitBatchTaskByOrderResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 错发换货单独生成拣货任务
 * 
 * <AUTHOR>
 * @date 2025/6/13
 */
@Component
public class SplitBatchTaskWrongDeliveryByOrderBL extends SplitBatchTaskByOrderBL {
    @Override
    List<OutStockOrderPO> supportOrderList(SplitBatchTaskByOrderRequestBO bo) {
        List<OutStockOrderPO> waveOrders = bo.getCreateDTO().getOrders();

        List<OutStockOrderPO> wrongDeliveryOrderList = waveOrders.stream()
            .filter(m -> Objects.equals(m.getOrderSourceType(), SourceType.WRONG_DELIVERY.getValue()))
            .collect(Collectors.toList());

        return wrongDeliveryOrderList;
    }

    @Override
    protected List<SplitBatchTaskByOrderResultBO> doSplitBatchTaskByOrder(SplitBatchTaskByOrderRequestBO bo,
        List<OutStockOrderPO> outStockOrderPOList) {
        Map<String, List<OutStockOrderPO>> pickLeakOrderMap = outStockOrderPOList.stream()
            .collect(Collectors.toMap(k -> k.getId().toString(), v -> Collections.singletonList(v)));

        return SplitBatchTaskByOrderResultBO.getWrongDeliveryOrderTaskList(pickLeakOrderMap);
    }
}
