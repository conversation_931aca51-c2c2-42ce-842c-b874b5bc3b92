package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.pickup;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskFinishHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishNeedOpBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.PickUpDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.model.OutStockOrderItemQuerySO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchSowTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/12
 */
@Service
public class BatchTaskItemFinishPickUpSecSortDecoratorBL extends BatchTaskItemFinishPickUpBaseDecoratorBL {

    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;

    @Override
    boolean support(BatchTaskFinishHelperBO batchTaskFinishHelperBO) {
        Byte containerFlag = batchTaskFinishHelperBO.getBatchTaskItemFinishBO().getContainerFlag();
        if (Objects.equals(containerFlag, ConditionStateEnum.是.getType())) {
            return Boolean.FALSE;
        }
        BatchPO batchPO = batchTaskFinishHelperBO.getBatchPO();
        if (Objects.equals(batchPO.getSowType(), BatchSowTypeEnum.二次分拣.getType())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    protected void doCompleteBatchTaskItemPickUp(BatchTaskItemFinishNeedOpBO bo,
        BatchTaskFinishHelperBO batchTaskFinishHelperBO, List<BatchTaskItemDTO> needCompleteBatchTaskItemList) {
        Integer warehouseId = batchTaskFinishHelperBO.getBatchTaskPO().getWarehouseId();
        Long sowLocationId = batchTaskFinishHelperBO.getSowLocationId();
        Map<String, BatchTaskItemCompleteDTO> map = batchTaskFinishHelperBO.getBatchTaskItemCompleteDTOMap();
        Map<Long, BigDecimal> taskInfoDetailOverSortCountMap =
            batchTaskFinishHelperBO.getTaskInfoDetailOverSortCountMap();
        List<SowTaskDTO> sowTasks = batchTaskFinishHelperBO.getSowTasks();
        List<OrderItemTaskInfoPO> relatedOrderItemTaskInfoList =
            batchTaskFinishHelperBO.getUpdateBatchTaskItemRelateOrderItemTaskInfoPOList();
        List<PickUpDTO> totalPickUpDTO = new ArrayList<>();
        for (BatchTaskItemDTO batchTaskItemDTO : needCompleteBatchTaskItemList) {
            BatchTaskItemCompleteDTO completeDTO = map.get(batchTaskItemDTO.getId());
            Long fromLocationId = BatchTaskFinishHelperBO.getFromLocationId(completeDTO, batchTaskItemDTO);
            List<OrderItemTaskInfoPO> tmpOrderItemTaskInfoList = relatedOrderItemTaskInfoList.stream()
                .filter(p -> p.getBatchTaskItemId().equals(batchTaskItemDTO.getId())).collect(Collectors.toList());
            List<PickUpDTO> pickUpDTO = getSecondSortPickUpDTO(warehouseId, batchTaskItemDTO, fromLocationId,
                completeDTO, sowLocationId, tmpOrderItemTaskInfoList, taskInfoDetailOverSortCountMap, sowTasks);
            if (CollectionUtils.isNotEmpty(pickUpDTO)) {
                totalPickUpDTO.addAll(pickUpDTO);
            }
        }

        bo.intPickUpDTOList(totalPickUpDTO);
    }

    private List<PickUpDTO> getSecondSortPickUpDTO(Integer warehouseId, BatchTaskItemDTO batchTaskItemDTO,
        Long fromLocationId, BatchTaskItemCompleteDTO updateDTO, Long sowLocationId,
        List<OrderItemTaskInfoPO> nowBatchTaskItemInfoList, Map<Long, BigDecimal> taskInfoDetailOverSortCountMap,
        List<SowTaskDTO> sowTasks) {
        if (CollectionUtils.isEmpty(nowBatchTaskItemInfoList)) {
            return Collections.emptyList();
        }
        List<PickUpDTO> pickUpDTOList = new ArrayList<>();
        OutStockOrderItemQuerySO querySO = new OutStockOrderItemQuerySO();
        querySO.setOrgId(batchTaskItemDTO.getOrgId());
        querySO.setWarehouseId(warehouseId);
        querySO.setIds(
            nowBatchTaskItemInfoList.stream().map(OrderItemTaskInfoPO::getRefOrderItemId).collect(Collectors.toList()));
        PageResult<OutStockOrderItemPO> outStockOrderItemList = outStockOrderItemMapper.listOrderItem(querySO);
        if (CollectionUtils.isEmpty(outStockOrderItemList)) {
            LOGGER.info("[酒饮二次分拣]出库单项为空，查询条件：{}", JSON.toJSONString(querySO));
            return Collections.emptyList();
        }

        Map<Long, Long> orderItemToLocationMap = initOrderItemToLocationMap(outStockOrderItemList, sowTasks);
        LOGGER.info("[酒饮二次分拣]出库单项的出库位：{}", orderItemToLocationMap);

        List<PickUpDTO> nowPickUpDTOList = new ArrayList<>();

        nowBatchTaskItemInfoList.forEach(info -> {
            // 关联明细
            if (CollectionUtils.isEmpty(info.getDetailList())) {
                return;
            }
            info.getDetailList().forEach(detail -> {
                PickUpDTO pickUpDTO = new PickUpDTO();
                BigDecimal pickCount = detail.getUnitTotalCount();
                // 重复拣货时，移库数量 = 本次拣货数量 - 上次拣货数量
                if (TaskStateEnum.已完成.getType() == batchTaskItemDTO.getTaskState()) {
                    // 上次拣货数量
                    BigDecimal prevPickCount = BigDecimal.ZERO;
                    if (taskInfoDetailOverSortCountMap != null
                        && taskInfoDetailOverSortCountMap.get(detail.getId()) != null) {
                        prevPickCount = taskInfoDetailOverSortCountMap.get(detail.getId());
                    }
                    pickCount = pickCount.subtract(prevPickCount);
                }
                if (pickCount.compareTo(BigDecimal.ZERO) == 0) {
                    LOGGER.info("拣货数量为0，不需要移库");
                    return;
                }
                Long toLocationId = orderItemToLocationMap.get(info.getRefOrderItemId());
                if (toLocationId == null) {
                    LOGGER.info("出库位为空，不能移库，出库单项ID：{}，移库数量：{}", info.getRefOrderItemId(), pickCount);
                    throw new BusinessValidateException("二次分拣，出库位不能为空");
                }
                pickUpDTO.setFromLocationId(fromLocationId);
                PickUpDTOConvertor.setPickupToLocation(toLocationId, sowLocationId, pickUpDTO);
                pickUpDTO.setProductSkuId(batchTaskItemDTO.getSkuId());
                pickUpDTO.setWarehouseId(warehouseId);
                pickUpDTO.setFromChannel(batchTaskItemDTO.getChannel().intValue());
                pickUpDTO.setFromSource(batchTaskItemDTO.getSource().intValue());
                pickUpDTO.setToChannel(batchTaskItemDTO.getChannel().intValue());
                pickUpDTO.setToSource(batchTaskItemDTO.getSource().intValue());
                pickUpDTO.setCount(pickCount);
                // 生产日期和批次时间
                pickUpDTO.setProductionDate(updateDTO != null ? updateDTO.getProductionDate() : null);
                pickUpDTO.setBatchTime(updateDTO != null ? updateDTO.getBatchTime() : null);
                // 自动分配库存
                pickUpDTO.setAutoAllotFlag(true);
                pickUpDTO.setSecOwnerId(detail.getSecOwnerId());
                pickUpDTO.setBusinessId(info.getId().toString());
                // 排除负库存
                pickUpDTO.setExcludeNegativeFlag(true);
                nowPickUpDTOList.add(pickUpDTO);
            });
        });
        LOGGER.info("[酒批二次分拣]移库请求：{}", JSON.toJSONString(nowPickUpDTOList));
        pickUpDTOList.addAll(nowPickUpDTOList);
        return pickUpDTOList;
    }

    private Map<Long, Long> initOrderItemToLocationMap(PageResult<OutStockOrderItemPO> outStockOrderItemList,
        List<SowTaskDTO> sowTasks) {
        Map<Long, Long> orderItemToLocationMap =
            outStockOrderItemList.stream().filter(ele -> ele.getLocationId() != null).collect(
                Collectors.toMap(OutStockOrderItemPO::getId, OutStockOrderItemPO::getLocationId, (k1, k2) -> k1));
        LOGGER.info("[酒饮二次分拣]出库单项的出库位：{}", orderItemToLocationMap);
        if (CollectionUtils.isEmpty(sowTasks)) {
            return orderItemToLocationMap;
        }

        Map<Long, SowTaskDTO> sowTaskDTOMap =
            sowTasks.stream().collect(Collectors.toMap(SowTaskDTO::getId, v -> v, (v1, v2) -> v1));

        Map<Long, Long> orderItemToLocationSowMap =
            outStockOrderItemList.stream().filter(ele -> Objects.nonNull(sowTaskDTOMap.get(ele.getSowTaskId())))
                .collect(Collectors.toMap(OutStockOrderItemPO::getId, v -> {
                    SowTaskDTO sowTaskDTO = sowTaskDTOMap.get(v.getSowTaskId());
                    return sowTaskDTO.getLocationId();
                }, (k1, k2) -> k1));

        for (Map.Entry<Long, Long> entry : orderItemToLocationSowMap.entrySet()) {
            Long locationId = orderItemToLocationMap.get(entry.getKey());
            if (Objects.isNull(locationId)) {
                orderItemToLocationMap.put(entry.getKey(), entry.getValue());
            }
        }

        return orderItemToLocationMap;
    }
}
