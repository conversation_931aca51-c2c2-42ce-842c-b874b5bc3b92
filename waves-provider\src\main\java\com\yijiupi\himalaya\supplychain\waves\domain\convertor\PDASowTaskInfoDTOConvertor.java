package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.PDASowTaskInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.PDASowTaskRefBatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.PDASowTaskRefOutStockOrderItemDTO;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/1
 */
public class PDASowTaskInfoDTOConvertor {

    public static List<PDASowTaskInfoDTO> convert(List<BatchTaskDTO> batchTaskDTOList) {
        List<PDASowTaskInfoDTO> pdaSowTaskInfoDTOS = new ArrayList<>();
        Map<String, List<BatchTaskDTO>> batchTaskMap =
            batchTaskDTOList.stream().collect(Collectors.groupingBy(BatchTaskDTO::getSowTaskNo));
        for (Map.Entry<String, List<BatchTaskDTO>> entry : batchTaskMap.entrySet()) {
            List<BatchTaskDTO> batchTaskDTOS = entry.getValue();
            BatchTaskDTO tmpBatchTaskDTO = batchTaskDTOS.get(0);
            PDASowTaskInfoDTO dto = new PDASowTaskInfoDTO();
            List<PDASowTaskRefBatchTaskItemDTO> batchTaskItemList = batchTaskDTOS.stream()
                    .map(BatchTaskDTO::getBatchTaskItemList)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(Collection::stream)
                    .map(PDASowTaskRefBatchTaskItemDTO::new).collect(Collectors.toList());
            List<PDASowTaskRefOutStockOrderItemDTO> outStockOrderItemList = batchTaskDTOS.stream()
                    .map(BatchTaskDTO::getOutStockOrderItemList)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(Collection::stream)
                    .map(PDASowTaskRefOutStockOrderItemDTO::new)
                    .collect(Collectors.toList());
            dto.setBatchTaskItemList(batchTaskItemList);
            dto.setOutStockOrderItemList(outStockOrderItemList);
            dto.setSowTaskNo(tmpBatchTaskDTO.getSowTaskNo());
            dto.setSowTaskId(tmpBatchTaskDTO.getSowTaskId());
            dto.setBatchNo(tmpBatchTaskDTO.getBatchNo());
            dto.setSowLocationId(tmpBatchTaskDTO.getSowLocationId());
            dto.setSowLocationName(tmpBatchTaskDTO.getSowLocationName());

            pdaSowTaskInfoDTOS.add(dto);
        }

        return pdaSowTaskInfoDTOS;
    }

    // 合并batchTaskItem上的orderSequence和sowOrderSequence
    public static void pickByCustomerSetValue(List<BatchTaskDTO> batchTaskDTOList) {
        //2025-02-08 去掉播种任务自动计算合并拣货逻辑
//        if (CollectionUtils.isEmpty(batchTaskDTOList)) {
//            return;
//        }
//
//        Map<Integer, List<OutStockOrderItemDTO>> itemAddressIdGroupMap =
//            batchTaskDTOList.stream().flatMap(m -> m.getOutStockOrderItemList().stream())
//                .collect(Collectors.groupingBy(OutStockOrderItemDTO::getAddressId));
//
//        for (Map.Entry<Integer, List<OutStockOrderItemDTO>> entry : itemAddressIdGroupMap.entrySet()) {
//            Optional<Integer> orderSequenceOptional = entry.getValue().stream()
//                .map(OutStockOrderItemDTO::getOrderSequence).filter(Objects::nonNull).min(Integer::compareTo);
//            Optional<Integer> sowOrderSequenceOptional = entry.getValue().stream()
//                .map(OutStockOrderItemDTO::getSowOrderSequence).filter(Objects::nonNull).min(Integer::compareTo);
//            entry.getValue().forEach(item -> {
//                item.setOrderSequence(orderSequenceOptional.orElseGet(() -> 0));
//                item.setSowOrderSequence(sowOrderSequenceOptional.orElseGet(() -> 0));
//            });
//        }
    }

}
