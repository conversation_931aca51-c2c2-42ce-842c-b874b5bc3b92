package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;

/**
 * @description 仓管的名称、id
 * <AUTHOR>
 * @Date 2021/7/8 14:51
 */
public class WarehouseKeeper implements Serializable {
    private static final long serialVersionUID = -9153991357308367900L;

    /**
     * 仓管名称
     */
    private String name;
    /**
     * 仓管id
     */
    private Integer id;

    /**
     * OA的id
     */
    private Long businessId;

    public WarehouseKeeper() {}

    public WarehouseKeeper(String name, Integer id) {
        this.name = name;
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }
}
