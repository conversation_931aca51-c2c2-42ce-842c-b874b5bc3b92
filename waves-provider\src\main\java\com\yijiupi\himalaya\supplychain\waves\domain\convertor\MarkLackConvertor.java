package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.ordercenter.enums.productTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.OrderCenterCalOrderLackDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.OrderCenterCalOrderLackItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.OrderCenterCalOrderLackItemResultDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.PartSendWsmDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockLackCalResultDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockLackDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/8/15
 */
public class MarkLackConvertor {

    private static final Integer MARKTYPE_LACK = 1;

    public static StringBuffer getLackInfo(PartSendWsmDTO partSendWsmDTO,
        Map<String, OutStockOrderItemPO> orderItemPOMap) {
        Map<Long, Integer> itemShipMap = partSendWsmDTO.getItemShipMap();
        Map<Long, Integer> itemMap = partSendWsmDTO.getItemMap();

        Map<Long, Integer> lackMap = new HashMap<>();

        for (Map.Entry<Long, Integer> entry : itemShipMap.entrySet()) {
            int count = entry.getValue() - itemMap.get(entry.getKey());
            if (count != 0) {
                lackMap.put(entry.getKey(), count);
            }
        }

        if (org.springframework.util.CollectionUtils.isEmpty(lackMap)) {
            return new StringBuffer();
        }

        StringBuffer stringBuffer = new StringBuffer();
        for (Map.Entry<Long, Integer> entry : lackMap.entrySet()) {
            OutStockOrderItemPO outStockOrderItemPO = orderItemPOMap.get(entry.getKey().toString());
            BigDecimal lackUnitTotalCount = BigDecimal.valueOf(entry.getValue());

            stringBuffer.append(outStockOrderItemPO.getProductname()).append(" 缺货 ")
                .append(lackUnitTotalCount.stripTrailingZeros().toPlainString())
                .append(outStockOrderItemPO.getUnitname());
        }

        return stringBuffer;
    }

    public static OrderCenterCalOrderLackDTO convertOrderCenterCalOrderLackDTO(OutStockOrderPO outStockOrderPO,
        OutStockLackDTO outStockLackDTO) {
        OrderCenterCalOrderLackDTO lackDTO = new OrderCenterCalOrderLackDTO();
        lackDTO.setOrderId(Long.valueOf(outStockOrderPO.getBusinessId()));
        lackDTO.setMarkType(MARKTYPE_LACK);

        Map<Long, Integer> itemMap = outStockLackDTO.getItemMap();
        List<OrderCenterCalOrderLackItemDTO> items = outStockOrderPO.getItems().stream().map(item -> {
            OrderCenterCalOrderLackItemDTO itemDTO = new OrderCenterCalOrderLackItemDTO();
            itemDTO.setOrderItemId(Long.valueOf(item.getBusinessItemId()));
            itemDTO.setUnitCount(item.getUnittotalcount());

            Integer count = itemMap.get(itemDTO.getOrderItemId());
            if (Objects.nonNull(count)) {
                itemDTO.setUnitCount(BigDecimal.valueOf(count));
            }

            return itemDTO;
        }).collect(Collectors.toList());

        lackDTO.setItems(items);

        return lackDTO;
    }

    public static List<OutStockLackCalResultDTO>
        convertOutStockLackCalResultDTOList(List<OrderCenterCalOrderLackItemResultDTO> itemResultDTOList) {
        if (CollectionUtils.isEmpty(itemResultDTOList)) {
            return Collections.emptyList();
        }

        List<OutStockLackCalResultDTO> resultDTOS = itemResultDTOList.stream().map(item -> {
            OutStockLackCalResultDTO resultDTO = new OutStockLackCalResultDTO();
            resultDTO.setBusinessItemId(item.getOrderItemId());
            resultDTO.setOrderId(item.getOrderId());
            resultDTO.setUnitTotalCount(Objects.equals(item.getProductType(), productTypeEnum.赠品.getType())
                ? item.getActualUnitCount() : item.getWorkingUnitCount());
            resultDTO.setProductType(item.getProductType());
            resultDTO.setCompositeId(item.getCompositeId());

            return resultDTO;
        }).collect(Collectors.toList());

        return resultDTOS;
    }

    public static void fillShipMapInfo(OutStockOrderPO outStockOrderPO, PartSendWsmDTO partSendWsmDTO) {
        Map<Long, Integer> itemShipMap = partSendWsmDTO.getItemShipMap();
        Map<Long, Integer> itemMap = partSendWsmDTO.getItemMap();
        outStockOrderPO.getItems().stream()
            .filter(m -> Objects.isNull(itemShipMap.get(Long.valueOf(m.getBusinessItemId())))).forEach(item -> {
                Long businessId = Long.valueOf(item.getBusinessItemId());
                itemShipMap.put(businessId, item.getUnittotalcount().intValue());
                itemMap.put(businessId, item.getUnittotalcount().intValue());
            });
    }

}
