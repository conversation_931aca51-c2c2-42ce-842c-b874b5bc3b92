package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 操作记录 - 操作类型
 * 
 * <AUTHOR>
 * @date 2018/5/22 17:00
 */
public enum OrderTraceEventTypeEnum {
    /**
     * 枚举
     */
    新增((byte)1), 修改((byte)2), 删除((byte)3), 取消((byte)4), 其他((byte)5);

    /**
     * type
     */
    private Byte type;

    OrderTraceEventTypeEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

}
