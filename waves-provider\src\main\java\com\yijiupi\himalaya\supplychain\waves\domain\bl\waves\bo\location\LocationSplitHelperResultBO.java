package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location;

import java.math.BigDecimal;
import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;

/**
 * <AUTHOR>
 * @title: LocationSplitHelperResultBO
 * @description: 这个对象适用按skuId分组的
 * @see LocationDivideHelperResultBO
 * @date 2022-12-01 10:07
 */
public class LocationSplitHelperResultBO {
    /**
     * 产品skuId
     */
    private Long skuId;
    /**
     * 小件数
     */
    private BigDecimal unitTotalCount;
    /**
     * 包装规格数
     */
    private BigDecimal specQuantity;
    /**
     * 销售规格数
     */
    private BigDecimal saleSpecQuantity;
    /**
     * 大件数
     */
    private BigDecimal packageCount;
    /**
     * 小件数
     */
    private BigDecimal unitCount;
    /**
     * 走存储位订单项列表
     */
    private List<OutStockOrderItemPO> packageItemList;
    /**
     * 走零拣位的订单项列表
     */
    private List<OutStockOrderItemPO> unitItemList;

    /**
     * 获取 产品skuId
     *
     * @return skuId 产品skuId
     */
    public Long getSkuId() {
        return this.skuId;
    }

    /**
     * 设置 产品skuId
     *
     * @param skuId 产品skuId
     */
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    /**
     * 获取 小件数
     *
     * @return unitTotalCount 小件数
     */
    public BigDecimal getUnitTotalCount() {
        return this.unitTotalCount;
    }

    /**
     * 设置 小件数
     *
     * @param unitTotalCount 小件数
     */
    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    /**
     * 获取 包装规格数
     *
     * @return specQuantity 包装规格数
     */
    public BigDecimal getSpecQuantity() {
        return this.specQuantity;
    }

    /**
     * 设置 包装规格数
     *
     * @param specQuantity 包装规格数
     */
    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    /**
     * 获取 销售规格数
     *
     * @return saleSpecQuantity 销售规格数
     */
    public BigDecimal getSaleSpecQuantity() {
        return this.saleSpecQuantity;
    }

    /**
     * 设置 销售规格数
     *
     * @param saleSpecQuantity 销售规格数
     */
    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    /**
     * 获取 大件数
     *
     * @return packageCount 大件数
     */
    public BigDecimal getPackageCount() {
        return this.packageCount;
    }

    /**
     * 设置 大件数
     *
     * @param packageCount 大件数
     */
    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    /**
     * 获取 小件数
     *
     * @return unitCount 小件数
     */
    public BigDecimal getUnitCount() {
        return this.unitCount;
    }

    /**
     * 设置 小件数
     *
     * @param unitCount 小件数
     */
    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    /**
     * 获取 走存储位订单项列表
     *
     * @return packageItemList 走存储位订单项列表
     */
    public List<OutStockOrderItemPO> getPackageItemList() {
        return this.packageItemList;
    }

    /**
     * 设置 走存储位订单项列表
     *
     * @param packageItemList 走存储位订单项列表
     */
    public void setPackageItemList(List<OutStockOrderItemPO> packageItemList) {
        this.packageItemList = packageItemList;
    }

    /**
     * 获取 走零拣位的订单项列表
     *
     * @return unitItemList 走零拣位的订单项列表
     */
    public List<OutStockOrderItemPO> getUnitItemList() {
        return this.unitItemList;
    }

    /**
     * 设置 走零拣位的订单项列表
     *
     * @param unitItemList 走零拣位的订单项列表
     */
    public void setUnitItemList(List<OutStockOrderItemPO> unitItemList) {
        this.unitItemList = unitItemList;
    }
}
