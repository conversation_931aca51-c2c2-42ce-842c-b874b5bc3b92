package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 产品分配类型对应的数量
 *
 * <AUTHOR>
 * @date 2019-08-21 21:21
 */
public class ProductAllotTypeDTO implements Serializable {

    private static final long serialVersionUID = 46190018881817338L;

    /**
     * 未分配波次的数量
     */
    private BigDecimal notAllotCount;

    /**
     * 已分配波次的数量
     */
    private BigDecimal allotCount;

    public BigDecimal getNotAllotCount() {
        return notAllotCount;
    }

    public void setNotAllotCount(BigDecimal notAllotCount) {
        this.notAllotCount = notAllotCount;
    }

    public BigDecimal getAllotCount() {
        return allotCount;
    }

    public void setAllotCount(BigDecimal allotCount) {
        this.allotCount = allotCount;
    }
}