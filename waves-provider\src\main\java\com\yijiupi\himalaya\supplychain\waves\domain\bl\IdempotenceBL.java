package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.util.RedisKeyConstant;
import com.yijiupi.himalaya.supplychain.waves.util.RedisUtil;

@Service
public class IdempotenceBL {

    @Autowired
    private RedisUtil<String> redisUtil;

    public List<SowTaskItemDTO> getNoProcessSowTaskItems(List<SowTaskItemDTO> sowTaskItemDTOS) {
        List<SowTaskItemDTO> noProcessSowTaskItems = new ArrayList<>();
        sowTaskItemDTOS.forEach(item -> {
            String value = String.format("%s-%s", item.getId(), item.getSowLocationId());
            String key = String.format("%s-%s", RedisKeyConstant.SOWTASK_ITEM_COMPLETE, value);
            boolean lock = redisUtil.setNX(key, value, 7, TimeUnit.DAYS);
            if (lock) {
                noProcessSowTaskItems.add(item);
            }
        });
        return noProcessSowTaskItems;
    }

    public void delSowTaskItemsKey(List<SowTaskItemDTO> sowTaskItemDTOS) {
        if (CollectionUtils.isEmpty(sowTaskItemDTOS)) {
            return;
        }
        List<String> keyList = new ArrayList<>();
        sowTaskItemDTOS.forEach(item -> {
            String value = String.format("%s-%s", item.getId(), item.getSowLocationId());
            String key = String.format("%s-%s", RedisKeyConstant.SOWTASK_ITEM_COMPLETE, value);
            keyList.add(key);
        });

        redisUtil.batchDel(keyList);
    }
}
