package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchPickingBL;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskItemManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderProcessBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OutStockOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskItemBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskItemCompleteBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.itemdetail.OutStockOrderItemDetailRecoverContextBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderitemdetail.OutStockOrderItemDetailModBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.RecoverOrderItemDetailBO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDetailRecoverDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
@Service(timeout = 6000)
public class BatchTaskItemManageServiceImpl implements IBatchTaskItemManageService {

    @Autowired
    private BatchTaskItemBL batchTaskItemBL;
    @Autowired
    private BatchOrderProcessBL batchOrderProcessBL;
    @Autowired
    private BatchTaskItemCompleteBL batchTaskItemCompleteBL;
    @Autowired
    private OutStockOrderBL outStockOrderBL;
    @Autowired
    private BatchPickingBL batchPickingBL;
    @Autowired
    private OutStockOrderItemDetailModBL outStockOrderItemDetailModBL;
    @Autowired
    private OutStockOrderItemDetailRecoverContextBL outStockOrderItemDetailRecoverContextBL;

    private static final Logger LOGGER = LoggerFactory.getLogger(BatchTaskItemManageServiceImpl.class);

    @Override
    public void updateBatchTaskItem(BatchTaskItemBatchUpdateDTO batchTaskItemBatchUpdateDTO) {
        batchTaskItemBL.updateBatchTaskItem(batchTaskItemBatchUpdateDTO);
    }

    @Override
    public void updateBatchTask(BatchTaskBatchUpdateDTO batchTaskBatchUpdateDTO) {
        batchTaskItemBL.updateBatchTask(batchTaskBatchUpdateDTO);
    }

    @Override
    public void clearOrderItemTaskInfo(List<Long> ids) {
        batchOrderProcessBL.clearOrderItemTaskInfo(ids);
    }

    @Override
    public void
        updateNotPickedBatchTaskItemLocationFromOldToNew(UpdateNotPickedBatchTaskItemLocationFromOldToNewDTO dto) {
        batchTaskItemBL.updateNotPickedBatchTaskItemLocationFromOldToNew(dto);
    }

    /**
     * 完成拣货任务明细
     *
     * @param batchTaskItemDTO
     * @return
     */
    @Override
    public Map<String, List<BatchTaskDTO>> completeBatchTaskItem(CompleteBatchTaskItemDTO batchTaskItemDTO) {
        try {
            batchPickingBL.beginPicking(batchTaskItemDTO.getBatchTaskId(), batchTaskItemDTO.getUserId());
            return batchTaskItemCompleteBL.completeBatchTaskItem(batchTaskItemDTO);
        } catch (Exception e) {
            if (e.getMessage().contains("已经加锁")) {
                throw new BusinessValidateException("拣货任务正在处理中，请稍后再试！");
            }
            throw e;
        }
    }

    /**
     * 修复缺货的detail数据
     *
     * @param dto
     */
    @Override
    public void recoverOrderStockOrderItemDetail(RecoverOrderStockOrderItemDetailDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");

        // List<String> businessIds = outStockOrderBL.getOutStockOrderBusinessIds(dto.getOutStockOrderIds(),
        // dto.getRefOrderNos(), dto.getWarehouseId());
        // if (CollectionUtils.isEmpty(businessIds)) {
        // throw new BusinessValidateException("未查到出库单！");
        // }

        RecoverOrderItemDetailBO bo = new RecoverOrderItemDetailBO();
        bo.setOutStockOrderIds(dto.getOutStockOrderIds());
        bo.setWarehouseId(bo.getWarehouseId());
        bo.setBatchNo("1");
        bo.setUseOrderCenterDetail(dto.getUseOrderCenterDetail());
        outStockOrderItemDetailModBL.lackFailedRecoverDetail(bo);
    }

    @Override
    public void recoverDetailUseOrderCenter(RecoverOrderStockOrderItemDetailDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");

        RecoverOrderItemDetailBO bo = new RecoverOrderItemDetailBO();
        bo.setOutStockOrderIds(dto.getOutStockOrderIds());
        bo.setWarehouseId(bo.getWarehouseId());
        bo.setBatchNo("1");
        bo.setUseOrderCenterDetail(Boolean.TRUE);
        bo.setHandleEqualDetail(dto.isHandleEqualDetail());
        outStockOrderItemDetailModBL.recoverDetail(bo);
    }

    @Override
    public void recoverDetailUseWMS(RecoverOrderStockOrderItemDetailDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");

        RecoverOrderItemDetailBO bo = new RecoverOrderItemDetailBO();
        bo.setOutStockOrderIds(dto.getOutStockOrderIds());
        bo.setWarehouseId(bo.getWarehouseId());
        bo.setBatchNo("1");
        bo.setUseOrderCenterDetail(Boolean.FALSE);
        bo.setHandleEqualDetail(dto.isHandleEqualDetail());
        outStockOrderItemDetailModBL.recoverDetail(bo);
    }

    @Override
    public void recoverDetailOutStockCount(RecoverOrderStockOrderItemDetailDTO dto) {
        outStockOrderItemDetailModBL.recoverDetailOutStockCount(dto);
    }

    @Override
    public List<OutStockOrderItemDetailDTO> recoverByBatchInventoryRecord(RecoverOrderStockOrderItemDetailDTO dto) {
        RecoverOrderItemDetailBO bo = new RecoverOrderItemDetailBO();
        bo.setOutStockOrderIds(dto.getOutStockOrderIds());
        bo.setWarehouseId(bo.getWarehouseId());
        bo.setBatchNo("1");
        bo.setUseOrderCenterDetail(Boolean.FALSE);
        bo.setHandleEqualDetail(dto.isHandleEqualDetail());
        return outStockOrderItemDetailModBL.recoverByBatchInventoryRecord(bo);
    }

    /**
     * 修复所有场景的detail数据
     *
     * @param dto
     * @return
     */
    @Override
    public List<OutStockOrderItemDetailDTO> recoverDetailList(OutStockOrderItemDetailRecoverDTO dto) {
        LOGGER.info("修复detail，入参为:{}", JSON.toJSONString(dto));
        OutStockOrderItemDetailRecoverDTO copyDTO = new OutStockOrderItemDetailRecoverDTO();
        BeanUtils.copyProperties(dto, copyDTO);
        List<List<Long>> outStockOrderIds = Lists.partition(dto.getOutStockOrderIds(), 30);
        outStockOrderIds.forEach(ids -> {
            copyDTO.setOutStockOrderIds(ids);
            outStockOrderItemDetailRecoverContextBL.recoverDetailList(copyDTO);
            outStockOrderItemDetailRecoverContextBL.sendOrderCenterDetailInfo(copyDTO);
        });

        outStockOrderItemDetailRecoverContextBL.sendOutStockInfoToErp(dto);

        return Collections.emptyList();
    }

}
