package com.yijiupi.himalaya.supplychain.waves.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.exceptions.HasLockedException;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OutStockOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchFinishedBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.BatchChangeReCalPieceBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.BatchOrderChangeBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.lock.BatchInfoDistributeLockBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder.OutStockOrderQueryBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.tms.NotifyTmsBatchCreateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.lock.LockCreateBatchBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.OrderItemTaskInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.*;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.CancelOrderCreateTransferOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.CancelOutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderProcessChangeDTO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2018/4/8
 */
@Service(timeout = 60000)
public class BatchManageServiceImpl implements IBatchManageService {

    @Resource
    private BatchOrderBL batchOrderBL;
    @Resource
    private OutStockOrderBL outStockOrderBL;
    @Resource
    private BatchOrderChangeBL batchOrderChangeBL;
    @Resource
    private BatchChangeReCalPieceBL batchChangeReCalPieceBL;
    @Autowired
    private BatchFinishedBL batchFinishedBL;
    @Autowired
    private OutStockOrderQueryBL outStockOrderQueryBL;
    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;
    @Autowired
    private NotifyTmsBatchCreateBL notifyTmsBatchCreateBL;
    @Autowired
    private BatchInfoDistributeLockBL batchInfoDistributeLockBL;
    @Autowired
    private LockCreateBatchBL lockCreateBatchBL;
    private static final Logger LOG = LoggerFactory.getLogger(BatchManageServiceImpl.class);

    /**
     * 根据波次编号删除波次【客户端删除波次】
     */
    @Override
    public void deleteBatchOrder(DeleteBatchDTO deleteBatchDTO) {
        deleteBatchDTO.resetBatchNoDTOList();
        try {
            batchOrderBL.deleteBatchOrder(deleteBatchDTO);
            lockCreateBatchBL.removeBatchCache(deleteBatchDTO.getBatchNoList());
        } catch (Exception e) {
            if (e.getMessage().contains("已经加锁")) {
                throw new BusinessValidateException("选中波次已被处理，请刷新界面后，重新选择波次进行删除操作！");
            }
            throw e;
        }


    }

    /**
     * 根据波次编号删除波次(及波次任务,波次任务详情,释放订单)
     */
    @Override
    public void deleteBatchOrder(List<String> batchNo, String operateUser, Boolean ignoreBatchInventory) {
        DeleteBatchDTO deleteBatchDTO = new DeleteBatchDTO();
        deleteBatchDTO.setBatchNoList(batchNo);
        deleteBatchDTO.setOperateUser(operateUser);
        deleteBatchDTO.setIgnoreBatchInventory(ignoreBatchInventory);
        batchOrderBL.deleteBatchOrder(deleteBatchDTO);
    }

    /**
     * 移除订单时，删除波次相关信息
     */
    @Override
    public void deleteBatchByRemoveOrder(List<String> batchNoList) {
        batchOrderBL.deleteBatchByRemoveOrder(batchNoList);
    }

    /**
     * 根据波次编号查询波次详情
     */
    @Override
    public BatchDTO selectBatchByBatchNo(Integer orgId, String batchNo) {
        AssertUtils.notNull(orgId, "OrgID不能为空");
        AssertUtils.notNull(batchNo, "波次编号不能为空");
        return batchOrderBL.selectBatchByBatchNo(orgId, batchNo);
    }

    /**
     * 根据拣货任务编号查询波次详情
     */
    @Override
    public BatchDTO selectBatchByTaskNo(Integer orgId, String taskNo) {
        return batchOrderBL.selectBatchByTaskNo(orgId, taskNo);
    }

    /**
     * 波次出库
     */
    @Override
    public void batchOutStock(BatchOutStockDTO batchOutStockDTO) {
        AssertUtils.notEmpty(batchOutStockDTO.getBatchNos(), "波次编号不能为空");
        AssertUtils.notNull(batchOutStockDTO.getOrgId(), "城市id不能为空");
        batchOrderBL.batchOutStock(batchOutStockDTO);
    }

    /**
     * 根据单号修改波次状态
     */
    @Override
    public void updateBatchStateByOrderNos(BatchUpdateDTO batchUpdateDTO) {
        AssertUtils.notEmpty(batchUpdateDTO.getOrderNos(), "波次编号不能为空");
        AssertUtils.notNull(batchUpdateDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(batchUpdateDTO.getWarehouseId(), "仓库id不能为空");
        LOG.info("更新波次状态: {}", JSON.toJSONString(batchUpdateDTO, SerializerFeature.WriteDateUseDateFormat));
        batchOrderBL.updateBatchStateByOrderNos(batchUpdateDTO);
    }

    /**
     * 根据波次编号完成波次（op后台工具）
     */
    @Override
    public void updateBatchStateByBatchNo(String batchNo) {
        try {
            batchOrderBL.finishScopBatchStateByBatchNo(batchNo, "系统管理员", 1);
            // batchFinishedBL.completeWave(batchNo, 1, null);
        } catch (HasLockedException e) {
            throw new BusinessValidateException("正在处理，请稍后再试！");
        }
    }

    @Override
    public void batchUpdateBatchStateByBatchNo(List<String> batchNo) {
        batchFinishedBL.batchUpdateBatchStateByBatchNo(batchNo);
    }

    /**
     * 处理订单变更时相关拣货逻辑
     */
    @Override
    public void processOrderChangeWithBatch(List<OutStockOrderProcessChangeDTO> processChangeDTOS, String operatorUser,
        Byte changeType) {
        if (CollectionUtils.isEmpty(processChangeDTOS)) {
            return;
        }
        String batchNo = UUID.randomUUID().toString();
        try {
            Long outStockOrderId = processChangeDTOS.get(0).getId();
            OutStockOrderDTO outStockOrderDTO = outStockOrderQueryBL.findByOrderId(outStockOrderId);
            batchNo = StringUtils.isNotEmpty(outStockOrderDTO.getBatchno()) ? outStockOrderDTO.getBatchno()
                : outStockOrderId.toString();
        } catch (Exception e) {
            LOG.warn("查询波次信息报错，", e);
        }
        List<Long> outStockOrderItemIds = processChangeDTOS.stream().flatMap(m -> m.getItemList().stream())
            .filter(m -> m.getDiffCount().compareTo(BigDecimal.ZERO) != 0).map(OutStockOrderItemProcessChangeDTO::getId)
            .collect(Collectors.toList());
        List<OrderItemTaskInfoDTO> orderItemTaskInfoDTOS =
            outStockOrderQueryBL.findRelateBathTaskId(outStockOrderItemIds);

        if (CollectionUtils.isEmpty(orderItemTaskInfoDTOS)) {
            batchOrderBL.waitProcessOrderChangeWithBatch(processChangeDTOS, operatorUser, changeType, batchNo);
            return;
        }

        batchOrderTaskBL.waitProcessOrderChangeWithBatch(processChangeDTOS, operatorUser, changeType, batchNo,
            orderItemTaskInfoDTOS);
    }

    /**
     * 根据波次通知TMS更新出库位
     */
    @Override
    public void syncOrderToLocationByScop(String batchNo) {
        outStockOrderBL.syncOrderToLocationByScop(batchNo);
    }

    /**
     * 根据波次通知TMS更新出库位
     *
     * @param batchNos
     */
    @Override
    public void batchSyncOrderToLocationByScop(List<String> batchNos) {
        batchNos.forEach(batchNo -> {
            outStockOrderBL.syncOrderToLocationByScop(batchNo);
        });
    }

    @Override
    public void updateBatchByDeliveryTask(BatchUpdateDTO batchUpdateDTO) {
        batchOrderBL.updateBatchByDeliveryTask(batchUpdateDTO);
    }

    @Override
    public void saasDeleteBatchOrder(DeleteBatchDTO deleteBatchDTO) {
        batchOrderBL.deleteBatchOrder(deleteBatchDTO);
    }

    /**
     * 取消出库单
     */
    @Override
    public void cancelOutStockOrder(CancelOutStockOrderDTO cancelOutStockOrderDTO) {

    }

    /**
     * 已拣货的取消订单生成移库单
     */
    @Override
    public void cancelOrderCreateTransferOrder(CancelOrderCreateTransferOrderDTO dto) {
        AssertUtils.notEmpty(dto.getOutStockOrderIds(), "出库单信息不能为空！");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");

        batchOrderChangeBL.cancelOrderCreateTransferOrder(dto);
    }

    /**
     * 通过 businessId 和仓库 id 查询订单详情
     *
     * @return 订单详情
     */
    @Override
    public List<ConfirmReturnDTO> orderItemDetail(String businessId, Integer warehouseId) {
        return batchOrderChangeBL.orderItemDetail(businessId, warehouseId);
    }

    @Override
    public void updateBatchTaskItemSkuId(List<BatchTaskItemDTO> batchTaskItemDTOList) {
        batchOrderChangeBL.updateBatchTaskItemSkuId(batchTaskItemDTOList);
    }

    /**
     * 重算出库单大小件信息
     *
     * @param outBoundNos 出库批次号
     * @param orgId 城市 id
     * @return 计算结果 key 为出库批次号, value 为 大小件信息的 map
     */
    @Override
    public Map<String, OutBoundBatchPieceDTO> reCalPieceInfo(Set<String> outBoundNos, Integer orgId) {
        return batchChangeReCalPieceBL.reCalPieceInfo(outBoundNos, orgId);
    }

    /**
     * 重算入库批次大小件信息
     *
     * @param param 计算参数
     * @return 计算结果 key 为入库批次号, value 为 大小件信息的 map
     */
    @Override
    public Map<String, OutBoundBatchPieceDTO> reCalPieceInfo(List<InBoundBatchPieceParam> param) {
        return batchChangeReCalPieceBL.reCalPieceInfo(param);
    }

    /**
     * 取消出库单处理波次信息
     *
     * @param cancelOutStockOrderDTO
     */
    @Override
    public void cancelOutStockOrderHandleBatchInfo(CancelOutStockOrderDTO cancelOutStockOrderDTO) {
        batchOrderChangeBL.cancelOutStockOrderHandleBatchInfo(cancelOutStockOrderDTO);
    }

    /**
     * 处理异常单处理波次信息
     *
     * @param cancelOutStockOrderDTO
     */
    @Override
    public void exceptionOrderHandleBatchInfo(CancelOutStockOrderDTO cancelOutStockOrderDTO) {
        batchOrderChangeBL.exceptionOrderHandleBatchInfo(cancelOutStockOrderDTO);
    }

    /**
     * 校验波次状态<br/>
     * 若波次内存在订单状态不为 期望状态 则抛出异常
     *
     * @param batchNo 波次编号
     * @param orgId 城市 id
     * @param state 期望状态
     */
    @Override
    public void checkBatchOrderState(String batchNo, Integer orgId, Number state) {
        AssertUtils.notNull(batchNo, "波次号不能为空");
        AssertUtils.notNull(state, "状态不能为空");
        batchOrderChangeBL.checkBatchOrderState(batchNo, orgId, state.intValue());
    }

    /**
     * 通过拣货任务 id 查询出库单
     *
     * @param batchTaskId 拣货任务 id
     * @param orgId 城市 id
     * @return 出库单
     */
    @Override
    public List<OutStockOrderDTO> selectByBatchTaskId(String batchTaskId, Integer orgId) {
        AssertUtils.notNull(batchTaskId, "拣货任务 id 不能为空");
        AssertUtils.notNull(orgId, "城市 id 不能为空");
        return batchOrderChangeBL.selectByBatchTaskId(batchTaskId, orgId);
    }

    /**
     * 更新拣货任务的托盘信息
     *
     * @param batchTaskId 拣货任务 id
     * @param toPalletNo 托盘信息
     */
    @Override
    public void updateBatchTaskPallet(String batchTaskId, String toPalletNo) {
        batchOrderChangeBL.updateBatchTaskPallet(batchTaskId, toPalletNo);
    }

    /**
     * 更新波次状态
     *
     * @param batchUpdateDTO
     */
    @Override
    public void updateBatchInfo(BatchUpdateDTO batchUpdateDTO) {
        batchOrderChangeBL.updateBatchInfo(batchUpdateDTO);
    }

    /**
     * 波次删除返架产品信息
     */
    @Override
    public List<BatchTaskItemDTO> listBatchReturnProductInfo(DeleteBatchDTO deleteBatchDTO) {
        return batchOrderBL.listBatchReturnProductInfo(deleteBatchDTO);
    }
}
