package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo;

import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemChangeRecordPO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: OutStockOrderAdminCancelHandlePickTransferResultBO
 * @description: 移库单的
 * @date 2023-05-25 09:27
 */
public class OutStockOrderAdminCancelHandlePickTransferResultBO extends OutStockOrderAdminCancelHandlePickResultBO {
    /**
     * 记录
     */
    private List<OutStockOrderItemChangeRecordPO> recordList;
    /**
     * 移库单, key 是 outStockOrderId
     */
    private Map<Long, StoreTransferOrderDTO> storeTransferOrderDTO;

    /**
     * 获取 记录
     *
     * @return recordList 记录
     */
    public List<OutStockOrderItemChangeRecordPO> getRecordList() {
        return this.recordList;
    }

    /**
     * 设置 记录
     *
     * @param recordList 记录
     */
    public void setRecordList(List<OutStockOrderItemChangeRecordPO> recordList) {
        this.recordList = recordList;
    }


    /**
     * 获取 移库单 key 是 outStockOrderId
     *
     * @return storeTransferOrderDTO 移库单 key 是 outStockOrderId
     */
    public Map<Long, StoreTransferOrderDTO> getStoreTransferOrderDTO() {
        return this.storeTransferOrderDTO;
    }

    /**
     * 设置 移库单 key 是 outStockOrderId
     *
     * @param storeTransferOrderDTO 移库单 key 是 outStockOrderId
     */
    public void setStoreTransferOrderDTO(Map<Long, StoreTransferOrderDTO> storeTransferOrderDTO) {
        this.storeTransferOrderDTO = storeTransferOrderDTO;
    }
}
