package com.yijiupi.himalaya.supplychain.waves.domain.bl.digitaltag;

import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchTaskSortBO;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/5
 */
@Service
public class SortGroupBatchTaskSortBL {

    @Autowired
    private SowTaskMapper sowTaskMapper;
    @Autowired
    private GlobalCache globalCache;
    @Autowired
    private BatchTaskMapper batchTaskMapper;

    private static final Logger LOG = LoggerFactory.getLogger(SortGroupBatchTaskSortBL.class);

    public List<DigitalBatchTaskDTO> sortBatchTask(List<DigitalBatchTaskDTO> batchTaskDTOList,
        DigitalBatchTaskQueryDTO queryDTO) {
        if (CollectionUtils.isEmpty(batchTaskDTOList)) {
            return Collections.emptyList();
        }

        queryDTO.setSortGroupId(null);
        queryDTO.setSortGroupIds(null);
        queryDTO.setBatchTaskIds(null);
        queryDTO.setBatchTaskNoList(null);
        List<DigitalBatchTaskDTO> totalBatchTaskList = batchTaskMapper.findDigitalBatchTaskInfo(queryDTO);
        fillToLocationInfo(totalBatchTaskList);
        Integer warehouseId = batchTaskDTOList.get(0).getWarehouseId();

        List<BatchTaskSortBO> batchTaskSortBOS = BatchTaskSortBO.convert(totalBatchTaskList);
        Map<Long, List<BatchTaskSortBO>> batchTaskSortMap =
            batchTaskSortBOS.stream().collect(Collectors.groupingBy(BatchTaskSortBO::getSortGroupId));

        if (globalCache.isOpenDigitalTagBatchTaskOptimizeSort(warehouseId)) {
            try {
                sortDigitalBatchTaskByShare(batchTaskSortBOS, batchTaskSortMap, warehouseId);
            } catch (Exception e) {
                LOG.warn("分摊失败, ", e);
                sortDigitalBatchTaskByCreateTime(batchTaskSortMap);
            }
        } else {
            sortDigitalBatchTaskByCreateTime(batchTaskSortMap);
        }

        BatchTaskSortBO.revert(batchTaskSortBOS, batchTaskDTOList);
        return batchTaskDTOList;
    }

    // 如果按时间排序，则每个分组按时间和拣货任务状态排序就可以
    private void sortDigitalBatchTaskByCreateTime(Map<Long, List<BatchTaskSortBO>> batchTaskSortMap) {
        // 内配/调拨的拣货任务，优先处理
        Comparator<BatchTaskSortBO> batchOrderTypeSort = (o1, o2) -> {
            if (BatchOrderTypeEnum.内配_调拨订单.getType().equals(o1.getBatchOrderType().byteValue())
                && BatchOrderTypeEnum.内配_调拨订单.getType().equals(o2.getBatchOrderType().byteValue())) {
                return 0;
            }
            if (BatchOrderTypeEnum.内配_调拨订单.getType().equals(o1.getBatchOrderType().byteValue())) {
                return -1;
            }
            if (BatchOrderTypeEnum.内配_调拨订单.getType().equals(o2.getBatchOrderType().byteValue())) {
                return 1;
            }
            return o2.getBatchOrderType().compareTo(o1.getBatchOrderType());
        };
        batchTaskSortMap.values().forEach(boList -> {
            // 拣货中状态的优先处理
            boList.sort(Comparator.comparing(BatchTaskSortBO::getState, Comparator.reverseOrder())
                // 内配/调拨的拣货任务，优先处理
                .thenComparing(BatchTaskSortBO::getBatchOrderType, Comparator.reverseOrder())
                // 按时间顺序处理
                .thenComparing(batchOrderTypeSort).thenComparing(BatchTaskSortBO::getCreateTime));
            for (int i = 0; i < boList.size(); i++) {
                boList.get(i).setSequence(i + 1);
            }
        });
    }

    // 根据播种台分摊排序
    private void sortDigitalBatchTaskByShare(List<BatchTaskSortBO> boList,
        Map<Long, List<BatchTaskSortBO>> batchTaskSortMap, Integer warehouseId) {
        // 分区id
        List<Long> sortGroupIds =
            boList.stream().map(BatchTaskSortBO::getSortGroupId).distinct().collect(Collectors.toList());

        // 有拣货任务的分区的数量
        int sortGroupLength = sortGroupIds.size();

        // 有播种任务的集货位（播种台）的数量
        List<Long> collectionLocationIds =
            boList.stream().map(BatchTaskSortBO::getLocationId).distinct().collect(Collectors.toList());

        // 未完成的播种任务
        Map<Long, List<SowTaskPO>> sowTaskGroupByLocationMap = getLocationSowTask(warehouseId);

        // 有分拣中的任务，和没有分拣中的任务
        // 1、确定播种台数量；2、确定每个分区的拣货任务属于的播种台

        // 最终的排序结果
        Map<Long, List<BatchTaskSortBO>> sequenceBatchTaskMap = new HashMap<>(6);

        List<BatchTaskSortBO> copyBoList = new ArrayList<>();
        boList.forEach(bo -> {
            BatchTaskSortBO newBo = new BatchTaskSortBO();
            BeanUtils.copyProperties(bo, newBo);
            copyBoList.add(newBo);
        });

        // 计算分组下，拣货任务数量最多的实际数量
        int maxLengthCollection = getMaxLengthCollection(batchTaskSortMap);

        for (int t = 0; t < maxLengthCollection; t++) {

            Map<Long, BatchTaskSortBO> roundBatchTaskMap = new HashMap<>();
            // 轮训分组id
            for (int i = 0; i < sortGroupLength; i++) {
                Long sortGroupId = sortGroupIds.get(i);
                sequenceBatchTaskMap.putIfAbsent(sortGroupId, new ArrayList<>());
                List<BatchTaskSortBO> seuenceList = sequenceBatchTaskMap.get(sortGroupId);
                List<BatchTaskSortBO> batchTaskList = batchTaskSortMap.get(sortGroupId);
                if (CollectionUtils.isEmpty(batchTaskList)) {
                    continue;
                }
                BatchTaskSortBO batchTaskSortBO =
                    getFirstPriorityBatchTask(batchTaskList, sowTaskGroupByLocationMap, roundBatchTaskMap);
                batchTaskList.removeIf(task -> task.getBatchTaskId().equals(batchTaskSortBO.getBatchTaskId()));
                seuenceList.add(batchTaskSortBO);
                roundBatchTaskMap.put(batchTaskSortBO.getLocationId(), batchTaskSortBO);
            }

            LOG.info("每轮结果为:{}", JSON.toJSONString(roundBatchTaskMap));
        }

        LOG.info("排序结果为:{}", JSON.toJSONString(sequenceBatchTaskMap));
        sequenceBatchTaskMap.forEach((key, value) -> {
            for (int i = 0; i < value.size(); i++) {
                value.get(i).setSequence(i + 1);
            }
        });

    }

    private int getMaxLengthCollection(Map<Long, List<BatchTaskSortBO>> batchTaskSortMap) {
        Collection<List<BatchTaskSortBO>> boCollection = batchTaskSortMap.values();
        int maxLength = 0;
        for (List<BatchTaskSortBO> boList : boCollection) {
            if (boList.size() > maxLength) {
                maxLength = boList.size();
            }
        }

        return maxLength;
    }

    private BatchTaskSortBO getFirstPriorityBatchTask(List<BatchTaskSortBO> batchTaskList,
        Map<Long, List<SowTaskPO>> sowTaskGroupByLocationMap, Map<Long, BatchTaskSortBO> roundBatchTaskMap) {

        if (batchTaskList.size() == 1) {
            return batchTaskList.get(0);
        }

        // 已经开始分拣的，排第一
        Optional<BatchTaskSortBO> pickingTaskBOOptional =
            batchTaskList.stream().filter(m -> TaskStateEnum.分拣中.getType() == m.getState()).findAny();

        if (pickingTaskBOOptional.isPresent()) {
            return pickingTaskBOOptional.get();
        }

        // 内配/调拨的，优先处理
        Optional<BatchTaskSortBO> allotTaskOptional = batchTaskList.stream()
            .filter(m -> BatchOrderTypeEnum.内配_调拨订单.getType().equals(m.getBatchOrderType().byteValue())).findAny();

        if (allotTaskOptional.isPresent()) {
            return allotTaskOptional.get();
        }

        // 然后找到播种台在播种的
        batchTaskList.sort(Comparator.comparing(BatchTaskSortBO::getCreateTime, Comparator.naturalOrder()));
        List<BatchTaskSortBO> sowingBatchTaskList = batchTaskList.stream().filter(m -> {
            List<SowTaskPO> sowTaskPOList =
                sowTaskGroupByLocationMap.getOrDefault(m.getLocationId(), Collections.emptyList());

            Optional<SowTaskPO> sowTaskPOOptional = sowTaskPOList.stream()
                .filter(sowTaskPO -> SowTaskStateEnum.播种中.getType() == sowTaskPO.getState()).findAny();

            // 移除已经处理的播种任务
            if (sowTaskPOOptional.isPresent()) {
                SowTaskPO sowingTask = sowTaskPOOptional.get();
                sowTaskPOList.removeIf(sowTask -> sowTask.getId().equals(sowingTask.getId()));

                return Boolean.TRUE;
            }

            return Boolean.FALSE;
        }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sowingBatchTaskList)) {
            return sowingBatchTaskList.stream().findFirst().get();
        }

        // 如果没有，按时间排序，直接选择第一个最先调度的
        if (CollectionUtils.isEmpty(roundBatchTaskMap)) {
            batchTaskList.sort(Comparator.comparing(BatchTaskSortBO::getCreateTime));
            return batchTaskList.get(0);
        }

        // 这一轮已经有这个播种台的数据，且，就不处理
        List<BatchTaskSortBO> notArrangeBatchTaskList = batchTaskList.stream()
            .filter(m -> Objects.isNull(roundBatchTaskMap.get(m.getLocationId()))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(notArrangeBatchTaskList)) {
            notArrangeBatchTaskList.sort(Comparator.comparing(BatchTaskSortBO::getCreateTime));
            return notArrangeBatchTaskList.get(0);
        }

        batchTaskList.sort(Comparator.comparing(BatchTaskSortBO::getCreateTime));
        return batchTaskList.get(0);
    }

    private Map<Long, List<SowTaskPO>> getLocationSowTask(Integer warehouseId) {
        List<SowTaskPO> sowTaskPOS = sowTaskMapper.listNoFinishSowTask(warehouseId);
        if (CollectionUtils.isEmpty(sowTaskPOS)) {
            return Collections.emptyMap();
        }

        return sowTaskPOS.stream().collect(Collectors.groupingBy(SowTaskPO::getLocationId));
    }

    public void fillToLocationInfo(List<DigitalBatchTaskDTO> batchTaskDTOS) {
        List<Long> sowTaskIds =
            batchTaskDTOS.stream().map(DigitalBatchTaskDTO::getSowTaskId).distinct().collect(Collectors.toList());

        List<SowTaskPO> sowTaskPOList = sowTaskMapper.findSowTaskByIds(sowTaskIds);

        Map<Long, SowTaskPO> sowTaskPOMap = sowTaskPOList.stream().collect(Collectors.toMap(SowTaskPO::getId, v -> v));

        batchTaskDTOS.forEach(taskDTO -> {
            SowTaskPO sowTaskPO = sowTaskPOMap.get(taskDTO.getSowTaskId());
            if (Objects.nonNull(sowTaskPO)) {
                taskDTO.setToLocationId(sowTaskPO.getLocationId());
                taskDTO.setToLocationName(sowTaskPO.getLocationName());
            }
        });
    }

}
