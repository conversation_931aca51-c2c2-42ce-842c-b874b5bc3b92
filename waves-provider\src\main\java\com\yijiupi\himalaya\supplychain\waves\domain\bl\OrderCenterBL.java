package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.constants.Constants;
import com.yijiupi.himalaya.supplychain.dubbop.dto.base.PageableResult;
import com.yijiupi.himalaya.supplychain.ordercenter.consant.SyncTraceMapKeyConstants;
import com.yijiupi.himalaya.supplychain.ordercenter.consant.SyncTraceTypeConstants;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.OrderApiResult;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.OrderCommonDetailDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.PageParam;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.SyncTraceDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.orderdocument.OrderDocumentDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.query.OrderBaseQuery;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.query.OrderQuery;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.query.OrdersApiResult;
import com.yijiupi.himalaya.supplychain.ordercenter.service.OrderCenterService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.JiupiOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OrderExtendMapEnum;
import com.yijiupi.himalaya.supplychain.pushorder.dto.RejectOrderDTO;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.ordercenter.OrderCenterNotifyBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderconstraint.OrderConstraintCheckBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderitemdetail.OutStockOrderItemDetailModBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.CreateBatchBaseBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.MarkLackBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.PartlyMarkOrderFailBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.RecoverOrderItemDetailBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.WMSOutOfStockFailBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.MarkLackConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.OrderCenterConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.PartMarkPickInfoDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter.*;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.OrderCenterCalOrderLackDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.OrderCenterCalOrderLackItemResultDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.PartSendWsmDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.*;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.MarkTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.himalaya.supplychain.waves.util.ServiceAbilityClientUtil;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单中台的访问
 *
 * <AUTHOR>
 * @since 2022/1/20
 */
@Service
public class OrderCenterBL {
    private static final Logger LOG = LoggerFactory.getLogger(OrderCenterBL.class);
    /**
     * 订单中台http客户端
     */
    @Autowired
    private ServiceAbilityClientUtil serviceAbilityClientUtil;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private OrderCenterNotifyBL orderCenterNotifyBL;
    @Autowired
    private GlobalCache globalCache;
    @Autowired
    private OutStockOrderItemDetailModBL outStockOrderItemDetailModBL;

    @Value("${ordercenter.sdk.timeout}")
    private Integer timeout;

    @Reference
    private OrderCenterService orderCenterService;
    @Reference
    private IAdminUserQueryService adminUserQueryService;

    @Resource
    private PackageOrderItemMapper packageOrderItemMapper;

    @Resource
    private OrderConstraintCheckBL orderConstraintCheckBL;

    private final String BATCH_OUT_REJECT_NOTIFY_URL = "/modify/RejectOrderService/handleRejectOrder";

    private final String SYNC_EXPRESS_INFO_NOTIFY_URL = "/outstock/DispatchService/syncExpressInfoList";

    private final String ORDER_OUT_STOCK_NOTIFY_URL = "/outstock/OutStockService/orderOutStockNotify";

    /**
     * 缺货发货试算接口
     */
    private static final String ORDER_MARK_LACK_CAL_URL = "/modify/OrderMarkModifyService/changeCountPreview";

    @Async(value = "waveTaskExecutor")
    @Transactional(rollbackFor = Throwable.class)
    public void createWaveNotifyByOrder(List<OutStockOrderPO> orders, CreateBatchBaseBO createBatchBaseBO) {
        List<String> refOrderNos = orders.stream().map(OutStockOrderPO :: getReforderno).collect(Collectors.toList());

        LOG.info("波次创建完毕通知中台: {}", JSON.toJSONString(refOrderNos, SerializerFeature.WriteMapNullValue));
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        Set<Long> orderIds = orders.stream().map(OutStockOrderPO::getId).collect(Collectors.toSet());
        //设置操作人名称，Trace展示用
        if(StringUtils.isEmpty(createBatchBaseBO.getOperateUserName()) && NumberUtils.isDigits(createBatchBaseBO.getOperateUserId())) {
            String userName = globalCache.getUserName(NumberUtils.toInt(createBatchBaseBO.getOperateUserId()));
            if(createBatchBaseBO.getBatchCreateDTO() != null){
                createBatchBaseBO.getBatchCreateDTO().setOperateUser(userName);
            }
            else if (createBatchBaseBO.getBatchCreateByRefOrderNoDTO() != null){
                createBatchBaseBO.getBatchCreateByRefOrderNoDTO().setOperateUser(userName);
            }
        }
        List<CreateWaveNotifyDTO> notifies = outStockOrderMapper.selectByIds(orderIds).stream().map(it -> {
            String businessId = it.getBusinessId();
            String batchNo = it.getBatchno();
            if (!NumberUtils.isDigits(businessId) || StringUtils.isEmpty(batchNo)) {
                LOG.info("出库单 {} 状态异常, 跳过处理", JSON.toJSONString(it, SerializerFeature.WriteMapNullValue));
                return null;
            }
            CreateWaveNotifyDTO notify = new CreateWaveNotifyDTO();
            notify.setWarehouseId(it.getWarehouseId());
            notify.setOrderId(Long.valueOf(businessId));
            notify.setWaveNo(batchNo);
            notify.setCreateWaveTime(it.getLastupdatetime());
            notify.setOptUserId(createBatchBaseBO.getOperateUserId() == null ? "1" : createBatchBaseBO.getOperateUserId().toString());
            notify.setOptUserName(createBatchBaseBO.getOperateUserName());
            return notify;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        createWaveNotify(notifies);
    }

    /**
     * 创建波次时，通知订单中台
     */
    public void createWaveNotify(List<CreateWaveNotifyDTO> createWaveList) {
        if (CollectionUtils.isEmpty(createWaveList)) {
            LOG.info("[创建波次时通知订单中台]请求为空");
            return;
        }
        createWaveList.forEach(elem -> {
            AssertUtils.notNull(elem.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(elem.getOrderId(), "订单id不能为空");
            AssertUtils.notNull(elem.getWaveNo(), "波次编号不能为空");
            AssertUtils.notNull(elem.getCreateWaveTime(), "创建波次时间不能为空");
            AssertUtils.notNull(elem.getOptUserId(), "操作人不能为空");
        });

        try {
            String response = serviceAbilityClientUtil.getInstance().invoke("/outstock/WaveService/createWaveNotify",
                    timeout, String.class, createWaveList);
            LOG.info("[创建波次时通知订单中台]成功，请求：{}，响应：{}", JSON.toJSONString(createWaveList), response);
        } catch (Exception ex) {
            LOG.error("[创建波次时通知订单中台]异常，请求：{}", JSON.toJSONString(createWaveList), ex);
        }
        for (CreateWaveNotifyDTO createWaveNotifyDTO : createWaveList) {
            SyncTraceDTO syncTraceDTO = new SyncTraceDTO();
            syncTraceDTO.setTraceType(SyncTraceTypeConstants.CREATE_WAVE_V2);
            Map<String, String> traceParamMap = new HashMap<>();
            traceParamMap.put(SyncTraceMapKeyConstants.ORDER_NO, createWaveNotifyDTO.getWaveNo());
            traceParamMap.put(SyncTraceMapKeyConstants.OP_USER_ID, createWaveNotifyDTO.getOptUserName());
            String warehouseName = globalCache.getWarehouse(createWaveNotifyDTO.getWarehouseId()).getName();
            traceParamMap.put(SyncTraceMapKeyConstants.WAREHOUSE_NAME, warehouseName);
            syncTraceDTO.setTraceParamMap(traceParamMap);
            List<Long> orderIdList = new ArrayList<>();
            orderIdList.add(createWaveNotifyDTO.getOrderId());
            orderCenterService.syncTrace(syncTraceDTO, orderIdList);
        }
    }

    /**
     * 取消波次时，通知订单中台
     */
    public void cancelWaveNotify(List<CancelWaveNotifyDTO> cancelWaveList) {
        if (CollectionUtils.isEmpty(cancelWaveList)) {
            LOG.info("[取消波次时通知订单中台]请求为空");
            return;
        }
        cancelWaveList.forEach(elem -> {
            AssertUtils.notNull(elem.getOrderId(), "订单id不能为空");
            AssertUtils.notNull(elem.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(elem.getOptUserId(), "操作人不能为空");
        });
        try {
            String response = serviceAbilityClientUtil.getInstance().invoke("/outstock/WaveService/cancelWaveNotify",
                timeout, String.class, cancelWaveList);
            LOG.info("[取消波次时通知订单中台]成功，请求：{}，响应：{}", JSON.toJSONString(cancelWaveList), response);
        } catch (Exception ex) {
            LOG.error("[取消波次时通知订单中台]异常，请求：{}", JSON.toJSONString(cancelWaveList), ex);
        }
    }

    /**
     * 完成拣货时，通知订单中台
     */
    public void completePickNotify(List<CompletePickNotifyDTO> pickNotifyList) {
        if (CollectionUtils.isEmpty(pickNotifyList)) {
            LOG.info("[完成拣货通知订单中台]请求为空");
            return;
        }
        pickNotifyList.forEach(elem -> {
            AssertUtils.notNull(elem.getOrderId(), "订单id不能为空");
            AssertUtils.notNull(elem.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(elem.getCompletePickTime(), "完成拣货时间不能为空");
            AssertUtils.notNull(elem.getOptUserId(), "操作人id不能为空");
        });
        try {
            String response = serviceAbilityClientUtil.getInstance().invoke("/outstock/WaveService/completePickNotify",
                timeout, String.class, pickNotifyList);
            LOG.info("[完成拣货通知订单中台]成功，请求：{}，响应：{}", JSON.toJSONString(pickNotifyList), response);
        } catch (Exception ex) {
            LOG.error("[完成拣货通知订单中台]异常，请求：{}", JSON.toJSONString(pickNotifyList), ex);
        }

        orderCenterNotifyBL.completePickNotifyTrace(pickNotifyList);
    }

    /**
     * 调拨拣货缺货时，通知订单中台
     */
    @Deprecated
    public void orderLackNotifySend(List<WMSOutOfStockDTO> wmsOutOfStockDTOList) {
        AssertUtils.notEmpty(wmsOutOfStockDTOList, "缺货通知参数不能为空");
        wmsOutOfStockDTOList.forEach(elem -> {
            AssertUtils.notNull(elem.getOrderId(), "订单id不能为空");
            AssertUtils.notNull(elem.getOutOfStockTime(), "缺货时间不能为空");
            AssertUtils.notNull(elem.getOptUserId(), "操作人不能为空");
            AssertUtils.notEmpty(elem.getItems(), "缺货明细不能为空");
            elem.getItems().stream().forEach(item -> {
                AssertUtils.notNull(item.getOrderItemId(), "订单项Id不能为空");
                AssertUtils.notNull(item.getAfterCount(), "缺货后数量不能为空");
                AssertUtils.notNull(item.getBeforeCount(), "缺货前数量不能为空");
                AssertUtils.notNull(item.getOutOfStocklackCount(), "缺货数量不能为空");
            });
        });

        if (CollectionUtils.isNotEmpty(wmsOutOfStockDTOList)) {
            wmsOutOfStockDTOList.forEach(wmsOutOfStockDTO -> {
                try {
                    String invoke = serviceAbilityClientUtil.getInstance()
                        .invoke("/outstock/OutOfStockService/outOfStock", timeout, String.class, wmsOutOfStockDTO);
                    LOG.info("拣货缺货时，回调订单中台WMSOutOfStockDTO返回结果:{}", invoke);
                } catch (Exception e) {
                    LOG.error("回调订单中台WMSOutOfStockDTO异常", e);
                }
            });
        }

    }

    /**
     * 调拨拣货缺货时，通知订单中台
     */
    public void orderLackNotifySend(List<WMSOutOfStockDTO> wmsOutOfStockDTOList, MarkLackBO markLackBO) {
        AssertUtils.notEmpty(wmsOutOfStockDTOList, "缺货通知参数不能为空");
        wmsOutOfStockDTOList.forEach(elem -> {
            AssertUtils.notNull(elem.getOrderId(), "订单id不能为空");
            AssertUtils.notNull(elem.getOutOfStockTime(), "缺货时间不能为空");
            AssertUtils.notNull(elem.getOptUserId(), "操作人不能为空");
            AssertUtils.notEmpty(elem.getItems(), "缺货明细不能为空");
            elem.getItems().forEach(item -> {
                AssertUtils.notNull(item.getOrderItemId(), "订单项Id不能为空");
                AssertUtils.notNull(item.getAfterCount(), "缺货后数量不能为空");
                AssertUtils.notNull(item.getBeforeCount(), "缺货前数量不能为空");
                AssertUtils.notNull(item.getOutOfStocklackCount(), "缺货数量不能为空");
            });
        });

        List<WMSOutOfStockDTO> successMarkList = new ArrayList<>();
        List<WMSOutOfStockFailBO> failMarkList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(wmsOutOfStockDTOList)) {
            wmsOutOfStockDTOList.forEach(wmsOutOfStockDTO -> {
                try {
                    String invoke = serviceAbilityClientUtil.getInstance()
                        .invoke("/outstock/OutOfStockService/outOfStock", timeout, String.class, wmsOutOfStockDTO);
                    LOG.info("拣货缺货时，回调订单中台WMSOutOfStockDTO返回结果:{}", invoke);
                    successMarkList.add(wmsOutOfStockDTO);
                } catch (Exception e) {
                    LOG.error("回调订单中台WMSOutOfStockDTO异常，入参：" + JSON.toJSONString(wmsOutOfStockDTO), e);
                    failMarkList.add(new WMSOutOfStockFailBO(wmsOutOfStockDTO, e.getMessage()));
                }
            });
        }

        Map<String, StringBuffer> bufferMap = markLackBO.getBufferMap();
        Integer operatorUserId = markLackBO.getOperatorUserId();
        if (CollectionUtils.isNotEmpty(successMarkList)) {
            for (WMSOutOfStockDTO dto : successMarkList) {
                StringBuffer stringBuffer = bufferMap.get(dto.getOrderId().toString());
                if (Objects.isNull(stringBuffer)
                    || org.apache.commons.lang3.StringUtils.isBlank(stringBuffer.toString())) {
                    continue;
                }

                SyncTraceDTO syncTraceDTO = new SyncTraceDTO();
                Integer traceType =
                    Optional.ofNullable(markLackBO.getTraceType()).orElse(SyncTraceTypeConstants.ORDER_PICK_STOCK_OUT);
                syncTraceDTO.setTraceType(traceType);
                Map<String, String> traceParamMap = new HashMap<>();
                traceParamMap.put(SyncTraceMapKeyConstants.OP_USER_ID, globalCache.getUserName(operatorUserId));
                traceParamMap.put(SyncTraceMapKeyConstants.DESCRIBE, stringBuffer.toString());
                syncTraceDTO.setTraceParamMap(traceParamMap);
                orderCenterService.syncTrace(syncTraceDTO, Collections.singletonList(dto.getOrderId()));
            }
        }

        if (CollectionUtils.isNotEmpty(failMarkList)) {
            for (WMSOutOfStockFailBO bo : failMarkList) {
                WMSOutOfStockDTO dto = bo.getWmsOutOfStockDTO();
                StringBuffer stringBuffer = bufferMap.get(dto.getOrderId().toString());
                if (Objects.isNull(stringBuffer)
                    || org.apache.commons.lang3.StringUtils.isBlank(stringBuffer.toString())) {
                    continue;
                }

                SyncTraceDTO syncTraceDTO = new SyncTraceDTO();
                syncTraceDTO.setTraceType(SyncTraceTypeConstants.ORDER_PICK_STOCK_OUT_FAIL);
                Map<String, String> traceParamMap = new HashMap<>();
                traceParamMap.put(SyncTraceMapKeyConstants.OP_USER_ID, globalCache.getUserName(operatorUserId));
                traceParamMap.put(SyncTraceMapKeyConstants.DESCRIBE, stringBuffer.toString());
                String errorMsg = "中台标记" + SyncTraceTypeConstants.getTraceDesc(markLackBO.getTraceType()) + "失败";
                traceParamMap.put(SyncTraceMapKeyConstants.ERRORMSG, errorMsg);
                syncTraceDTO.setTraceParamMap(traceParamMap);
                orderCenterService.syncTrace(syncTraceDTO, Collections.singletonList(dto.getOrderId()));
            }
            // 标记失败的接口，要重新计算detail信息
            handleDbLackFailedOrderItemDetail(failMarkList);
        }
    }

    /**
     * 订单标记拣货缺货时，通知订单中台 <br/>
     * bufferMap: key 是businessId
     */
    public void orderMarkNotifySend(MarkLackBO markLackBO, List<PartlyMarkOrderDTO> partlyMarkOrderDTOList) {
        Map<String, StringBuffer> bufferMap = markLackBO.getBufferMap();
        Integer operatorUserId = Objects.isNull(markLackBO.getOperatorUserId()) ? 1 : markLackBO.getOperatorUserId();
        AssertUtils.notEmpty(partlyMarkOrderDTOList, "标记缺货通知参数不能为空");
        partlyMarkOrderDTOList.forEach(elem -> {
            AssertUtils.notNull(elem.getOrderId(), "订单id不能为空");
            AssertUtils.notNull(elem.getMarkType(), "标记类型不能为空");
            AssertUtils.notEmpty(elem.getItems(), "订单重算数据明细不能为空");
            elem.getItems().stream().forEach(item -> {
                AssertUtils.notNull(item.getOrderItemId(), "订单项Id不能为空");
                AssertUtils.notNull(item.getUnitCount(), "标记实际数量不能为空");
            });
        });

        List<PartlyMarkOrderDTO> successMarkList = new ArrayList<>();
        List<PartlyMarkOrderFailBO> failMarkList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(partlyMarkOrderDTOList)) {
            partlyMarkOrderDTOList.forEach(partlyMarkOrderDTO -> {
                try {
                    LOG.info("订单标记拣货缺货时，回调订单中台partlyMarkOrderDTO入参:{}", JSON.toJSONString(partlyMarkOrderDTO));
                    String invoke = serviceAbilityClientUtil.getInstance().invoke(
                        "/modify/OrderMarkModifyService/changeCountMark", timeout, String.class, partlyMarkOrderDTO);
                    LOG.info("订单标记拣货缺货时，回调订单中台partlyMarkOrderDTO返回结果:{}", invoke);
                    successMarkList.add(partlyMarkOrderDTO);
                    // throw new BusinessValidateException("调用中台异常！");
                } catch (Exception e) {
                    LOG.error("回调订单中台partlyMarkOrderDTO异常，入参:" + JSON.toJSONString(partlyMarkOrderDTO), e);
                    failMarkList.add(new PartlyMarkOrderFailBO(partlyMarkOrderDTO, e.getMessage()));
                }
            });

            if (CollectionUtils.isNotEmpty(successMarkList)) {
                for (PartlyMarkOrderDTO dto : successMarkList) {
                    StringBuffer stringBuffer = bufferMap.get(dto.getOrderId().toString());
                    if (Objects.isNull(stringBuffer)
                        || org.apache.commons.lang3.StringUtils.isBlank(stringBuffer.toString())) {
                        continue;
                    }

                    SyncTraceDTO syncTraceDTO = new SyncTraceDTO();
                    Integer traceType = Optional.ofNullable(markLackBO.getTraceType())
                        .orElse(SyncTraceTypeConstants.ORDER_PICK_STOCK_OUT);
                    syncTraceDTO.setTraceType(traceType);
                    Map<String, String> traceParamMap = new HashMap<>();
                    traceParamMap.put(SyncTraceMapKeyConstants.OP_USER_ID,
                        globalCache.getAdminTrueName(operatorUserId));
                    traceParamMap.put(SyncTraceMapKeyConstants.DESCRIBE, stringBuffer.toString());
                    syncTraceDTO.setTraceParamMap(traceParamMap);
                    orderCenterService.syncTrace(syncTraceDTO, Collections.singletonList(dto.getOrderId()));
                }
            }
            if (CollectionUtils.isNotEmpty(failMarkList)) {
                for (PartlyMarkOrderFailBO bo : failMarkList) {
                    PartlyMarkOrderDTO dto = bo.getPartlyMarkOrderDTO();
                    StringBuffer stringBuffer = bufferMap.get(dto.getOrderId().toString());
                    if (Objects.isNull(stringBuffer)
                        || org.apache.commons.lang3.StringUtils.isBlank(stringBuffer.toString())) {
                        continue;
                    }

                    SyncTraceDTO syncTraceDTO = new SyncTraceDTO();
                    syncTraceDTO.setTraceType(SyncTraceTypeConstants.ORDER_PICK_STOCK_OUT_FAIL);
                    Map<String, String> traceParamMap = new HashMap<>();
                    traceParamMap.put(SyncTraceMapKeyConstants.OP_USER_ID,
                        globalCache.getAdminTrueName(operatorUserId));
                    traceParamMap.put(SyncTraceMapKeyConstants.DESCRIBE, stringBuffer.toString());
                    String errorMsg = "中台标记" + SyncTraceTypeConstants.getTraceDesc(markLackBO.getTraceType()) + "失败";
                    traceParamMap.put(SyncTraceMapKeyConstants.ERRORMSG, errorMsg);
                    syncTraceDTO.setTraceParamMap(traceParamMap);
                    LOG.info("缺货失败同步trace:{}", JSON.toJSONString(syncTraceDTO));
                    orderCenterService.syncTrace(syncTraceDTO, Collections.singletonList(dto.getOrderId()));
                }
            }
            handleNormalLackFailedOrderItemDetail(failMarkList);
        }
    }

    // 处理调拨
    private void handleDbLackFailedOrderItemDetail(List<WMSOutOfStockFailBO> failMarkList) {
        if (CollectionUtils.isEmpty(failMarkList)) {
            return;
        }
        List<String> orderIds = failMarkList.stream().map(WMSOutOfStockFailBO::getWmsOutOfStockDTO)
            .map(WMSOutOfStockDTO::getOrderId).distinct().map(String::valueOf).collect(Collectors.toList());
        Integer warehouseId = failMarkList.stream().map(WMSOutOfStockFailBO::getWmsOutOfStockDTO)
            .map(WMSOutOfStockDTO::getWarehouseId).distinct().findFirst().get();

        handleLackFailedOrderItemDetail(orderIds, warehouseId);
    }

    // 处理普通缺货
    private void handleNormalLackFailedOrderItemDetail(List<PartlyMarkOrderFailBO> failMarkList) {
        if (CollectionUtils.isEmpty(failMarkList)) {
            return;
        }

        List<String> orderIds = failMarkList.stream().map(PartlyMarkOrderFailBO::getPartlyMarkOrderDTO)
            .map(PartlyMarkOrderDTO::getOrderId).distinct().map(String::valueOf).collect(Collectors.toList());
        Integer warehouseId = failMarkList.stream().map(PartlyMarkOrderFailBO::getPartlyMarkOrderDTO)
            .map(PartlyMarkOrderDTO::getWarehouseId).distinct().findFirst().get();

        handleLackFailedOrderItemDetail(orderIds, warehouseId);
    }

    private void handleLackFailedOrderItemDetail(List<String> orderIds, Integer warehouseId) {
        List<OutStockOrderPO> outStockOrderPOList =
            outStockOrderMapper.findSimpleInfoByBusinessIds(orderIds, warehouseId);
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }
        outStockOrderPOList = outStockOrderPOList.stream().filter(m -> StringUtils.isNotEmpty(m.getBatchno()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }
        Map<String, List<OutStockOrderPO>> orderGroupBatchMap =
            outStockOrderPOList.stream().collect(Collectors.groupingBy(OutStockOrderPO::getBatchno));
        for (Map.Entry<String, List<OutStockOrderPO>> entry : orderGroupBatchMap.entrySet()) {
            List<OutStockOrderPO> orderList = entry.getValue();
            RecoverOrderItemDetailBO bo = new RecoverOrderItemDetailBO();
            bo.setBusinessIds(
                orderList.stream().map(OutStockOrderPO::getBusinessId).distinct().collect(Collectors.toList()));
            bo.setWarehouseId(warehouseId);
            bo.setBatchNo(entry.getKey());
            outStockOrderItemDetailModBL.lackFailedRecoverDetail(bo);
        }
    }

    /**
     * 装箱时，通知订单中台
     */
    public void createPackageNotify(List<CreatePackageNotifyDTO> createPackageNotifyList) {
        if (CollectionUtils.isEmpty(createPackageNotifyList)) {
            LOG.info("[装箱时通知订单中台]请求为空");
            return;
        }
        createPackageNotifyList.forEach(elem -> {
            AssertUtils.notNull(elem.getOrderId(), "订单id不能为空");
            AssertUtils.notNull(elem.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(elem.getBoxCode(), "箱号不能为空");
            elem.getItems().forEach(item -> {
                AssertUtils.notNull(item.getOrderItemId(), "订单项id不能为空");
                AssertUtils.notNull(item.getUnitTotalCount(), "装箱数量不能为空");
                AssertUtils.notNull(item.getCreatePackageTime(), "装箱时间不能为空");
                AssertUtils.notNull(item.getOptUserId(), "操作人id不能为空");
            });
        });
        try {
            String response = serviceAbilityClientUtil.getInstance()
                .invoke("/yijiupi/PackageService/createPackageNotify", timeout, String.class, createPackageNotifyList);
            LOG.info("[装箱时通知订单中台]成功，请求：{}，响应：{}", JSON.toJSONString(createPackageNotifyList), response);
        } catch (Exception ex) {
            LOG.error("[装箱时通知订单中台]异常，请求：{}", JSON.toJSONString(createPackageNotifyList), ex);
        }
    }

    /**
     * 创建波次时通知订单中台
     */
    public void createWaveNotify(List<OutStockOrderPO> outOrderList, BatchPO batchPO) {
        if (CollectionUtils.isEmpty(outOrderList) || batchPO == null) {
            return;
        }
        List<Long> orderIdList =
            outOrderList.stream().map(OutStockOrderPO::getId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            return;
        }

        List<String> businessIdIdList = outOrderList.stream().map(OutStockOrderPO::getBusinessId)
            .filter(Objects::nonNull).collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderList = CollectionUtils.isNotEmpty(businessIdIdList) ? outOrderList
            : outStockOrderMapper.listOrderByIds(orderIdList, batchPO.getOrgId(), batchPO.getWarehouseId());

        if (CollectionUtils.isEmpty(outStockOrderList)) {
            LOG.info("[创建波次时通知订单中台]无含businessId的出库单，源订单：{}", JSON.toJSONString(outOrderList));
            return;
        }

        Date createTime = batchPO.getCreateTime() == null ? new Date() : batchPO.getCreateTime();
        List<CreateWaveNotifyDTO> createWaveDTOS = new ArrayList<>();
        outStockOrderList.forEach(orderElem -> {
            if (StringUtils.isEmpty(orderElem.getBusinessId())) {
                return;
            }
            CreateWaveNotifyDTO createWaveDTO = new CreateWaveNotifyDTO();
            createWaveDTO.setOrderId(Long.valueOf(orderElem.getBusinessId()));
            createWaveDTO.setWaveNo(batchPO.getBatchNo());
            createWaveDTO.setWarehouseId(batchPO.getWarehouseId());
            createWaveDTO.setCreateWaveTime(createTime);
            createWaveDTO.setOptUserName(batchPO.getCreateUser());
            createWaveDTO.setOptUserId(batchPO.getCreateUserId() == null ? "1" : batchPO.getCreateUserId().toString());
            createWaveDTOS.add(createWaveDTO);
        });

        this.createWaveNotify(createWaveDTOS);
    }

    /**
     * 取消波次时通知订单中台
     */
    public void cancelWaveNotify(List<Long> outOrderIds, Integer operateUserId, Integer orgId, Integer warehouseId) {
        if (CollectionUtils.isEmpty(outOrderIds)) {
            return;
        }
        // 查询出库单
        List<OutStockOrderPO> outStockOrderList = outStockOrderMapper.listOrderByIds(outOrderIds, orgId, warehouseId);
        if (CollectionUtils.isEmpty(outStockOrderList)) {
            LOG.info("[取消波次时通知订单中台]查询不到出库单，查询条件：{}", JSON.toJSONString(outOrderIds));
            return;
        }
        List<CancelWaveNotifyDTO> cancelWaveList = new ArrayList<>();
        outStockOrderList.forEach(orderPO -> {
            if (StringUtils.isEmpty(orderPO.getBusinessId())) {
                return;
            }
            CancelWaveNotifyDTO cancelWaveDTO = new CancelWaveNotifyDTO();
            cancelWaveDTO.setWarehouseId(warehouseId);
            cancelWaveDTO.setOrderId(Long.valueOf(orderPO.getBusinessId()));
            cancelWaveDTO.setOptUserId(operateUserId == null ? "1" : operateUserId.toString());
            cancelWaveList.add(cancelWaveDTO);
        });
        this.cancelWaveNotify(cancelWaveList);
    }

    /**
     * 将出库单转为WMS完成拣货通知DTO
     */
    public CompletePickNotifyDTO toCompletePickNotifyDTO(OutStockOrderPO outOrderPO) {
        if (StringUtils.isEmpty(outOrderPO.getBusinessId())) {
            return null;
        }
        CompletePickNotifyDTO pickNotifyDTO = new CompletePickNotifyDTO();
        pickNotifyDTO.setOrderId(Long.valueOf(outOrderPO.getBusinessId()));
        pickNotifyDTO.setWarehouseId(outOrderPO.getWarehouseId());
        pickNotifyDTO.setCompletePickTime(outOrderPO.getPicktime() == null ? new Date() : outOrderPO.getPicktime());

        return pickNotifyDTO;
    }

    /**
     * 缺货事件通知订单中台
     */
    @Deprecated
    public void orderLackNotify(List<PartSendWsmDTO> partSendWsmDTOList) {
        if (CollectionUtils.isEmpty(partSendWsmDTOList) || partSendWsmDTOList == null) {
            return;
        }

        List<WMSOutOfStockDTO> wmsOutOfStockDTOList = new ArrayList<>();
        partSendWsmDTOList.stream().forEach(dto -> {
            WMSOutOfStockDTO wmsOutOfStockDTO = new WMSOutOfStockDTO();
            wmsOutOfStockDTO.setOrderId(dto.getBusinessId());
            wmsOutOfStockDTO.setOutOfStockTime(new Date());
            wmsOutOfStockDTO.setOptUserId(String.valueOf(dto.getUserId()));

            wmsOutOfStockDTO.setWarehouseId(getWarehouseId(dto));

            List<WMSOutOfStockItemDTO> itemList = new ArrayList();
            dto.getItemShipMap().forEach((omsOrderItemId, oldPackageCount) -> {
                BigDecimal afterCount = BigDecimal.valueOf(dto.getItemMap().get(omsOrderItemId));

                WMSOutOfStockItemDTO wmsOutOfStockItemDTO = new WMSOutOfStockItemDTO();
                wmsOutOfStockItemDTO.setOrderItemId(omsOrderItemId);
                wmsOutOfStockItemDTO.setBeforeCount(BigDecimal.valueOf(oldPackageCount));
                wmsOutOfStockItemDTO.setOutOfStocklackCount(BigDecimal.valueOf(oldPackageCount).subtract(afterCount));
                wmsOutOfStockItemDTO.setAfterCount(afterCount);
                itemList.add(wmsOutOfStockItemDTO);
            });
            wmsOutOfStockDTO.setItems(itemList);
            wmsOutOfStockDTOList.add(wmsOutOfStockDTO);
        });

        this.orderLackNotifySend(wmsOutOfStockDTOList);
    }

    private Integer getWarehouseId(PartSendWsmDTO dto) {
        if (Objects.isNull(dto.getExtendMap())) {
            return null;
        }
        Object warehouseId = dto.getExtendMap().get(OrderExtendMapEnum.取货缺货.getType());
        if (Objects.nonNull(warehouseId)) {
            return Integer.valueOf(warehouseId.toString());
        }

        warehouseId = dto.getExtendMap().get(OrderExtendMapEnum.标记缺货.getType());
        if (Objects.nonNull(warehouseId)) {
            return Integer.valueOf(warehouseId.toString());
        }

        if (Objects.isNull(dto.getExtendMap().get(OrderExtendMapEnum.拣货缺货.getType()))) {
            return null;
        }

        return Integer.valueOf(dto.getExtendMap().get(OrderExtendMapEnum.拣货缺货.getType()).toString());
    }

    /**
     * 调拨缺货
     */
    public void orderLackNotify(MarkLackBO markLackBO) {
        List<PartSendWsmDTO> partSendWsmDTOList = markLackBO.getPartSendWsmDTOList();
        if (CollectionUtils.isEmpty(partSendWsmDTOList)) {
            return;
        }
        List<WMSOutOfStockDTO> wmsOutOfStockDTOList = new ArrayList<>();
        partSendWsmDTOList.forEach(dto -> {
            WMSOutOfStockDTO wmsOutOfStockDTO = new WMSOutOfStockDTO();
            wmsOutOfStockDTO.setOrderId(dto.getBusinessId());
            wmsOutOfStockDTO.setOutOfStockTime(new Date());
            wmsOutOfStockDTO.setOptUserId(String.valueOf(dto.getUserId()));
            wmsOutOfStockDTO.setWarehouseId(getWarehouseId(dto));

            List<WMSOutOfStockItemDTO> itemList = new ArrayList<>();
            dto.getItemShipMap().forEach((omsOrderItemId, oldPackageCount) -> {
                BigDecimal afterCount = BigDecimal.valueOf(dto.getItemMap().get(omsOrderItemId));

                WMSOutOfStockItemDTO wmsOutOfStockItemDTO = new WMSOutOfStockItemDTO();
                wmsOutOfStockItemDTO.setOrderItemId(omsOrderItemId);
                wmsOutOfStockItemDTO.setBeforeCount(BigDecimal.valueOf(oldPackageCount));
                wmsOutOfStockItemDTO.setOutOfStocklackCount(BigDecimal.valueOf(oldPackageCount).subtract(afterCount));
                wmsOutOfStockItemDTO.setAfterCount(afterCount);
                itemList.add(wmsOutOfStockItemDTO);
            });
            wmsOutOfStockDTO.setItems(itemList);
            wmsOutOfStockDTOList.add(wmsOutOfStockDTO);
        });

        this.orderLackNotifySend(wmsOutOfStockDTOList, markLackBO);
    }

    /**
     * 订单标记通知订单中台
     */
    public void orderMarkNotify(MarkLackBO markLackBO) {
        List<PartSendWsmDTO> partSendWsmDTOList = markLackBO.getPartSendWsmDTOList();
        Map<String, StringBuffer> bufferMap = markLackBO.getBufferMap();
        Integer operatorUserId = markLackBO.getOperatorUserId();

        if (CollectionUtils.isEmpty(partSendWsmDTOList) || partSendWsmDTOList == null) {
            return;
        }

        List<PartlyMarkOrderDTO> partlyMarkOrderDTOList = new ArrayList<>();
        // 获取订单项对应二级货主
        Map<Long, Long> secOwnerIdMap = getOrderItemSecOwnerIdMap(partSendWsmDTOList);

        partSendWsmDTOList.stream().forEach(dto -> {
            PartlyMarkOrderDTO partlyMarkOrderDTO = new PartlyMarkOrderDTO();
            partlyMarkOrderDTO.setOrderId(dto.getBusinessId());
            partlyMarkOrderDTO.setMarkType(MarkTypeEnum.缺货发货.getType().intValue());
            // 新增仓库id
            if (dto.getExtendMap() != null && !dto.getExtendMap().isEmpty()) {
                Object warehosueId = getWarehouseId(dto);
                partlyMarkOrderDTO.setWarehouseId(warehosueId != null ? Integer.valueOf(warehosueId.toString()) : null);
            }

            List<PartlyMarkOrderItemDTO> itemList = new ArrayList();
            dto.getItemShipMap().forEach((omsOrderItemId, oldPackageCount) -> {
                BigDecimal afterCount = BigDecimal.valueOf(dto.getItemMap().get(omsOrderItemId));

                PartlyMarkOrderItemDTO partlyMarkOrderItemDTO = new PartlyMarkOrderItemDTO();
                partlyMarkOrderItemDTO.setOrderItemId(omsOrderItemId);
                partlyMarkOrderItemDTO.setUnitCount(afterCount);
                // 向上修改数量时需传二级货主信息
                if (afterCount.compareTo(new BigDecimal(oldPackageCount)) > 0
                    && secOwnerIdMap.get(omsOrderItemId) != null) {
                    List<PartlyMarkOrderItemSecOwnerDTO> itemSecOwnerList = new ArrayList();
                    PartlyMarkOrderItemSecOwnerDTO itemSecOwnerDTO = new PartlyMarkOrderItemSecOwnerDTO();
                    itemSecOwnerDTO.setOrderItemId(omsOrderItemId);
                    itemSecOwnerDTO.setUnitCount(afterCount);
                    itemSecOwnerDTO.setSecOwnerId(secOwnerIdMap.get(secOwnerIdMap.get(omsOrderItemId)));
                    itemSecOwnerList.add(itemSecOwnerDTO);
                    partlyMarkOrderItemDTO.setPartlyMarkOrderItemSecOwnerList(itemSecOwnerList);
                }
                itemList.add(partlyMarkOrderItemDTO);
            });
            partlyMarkOrderDTO.setItems(itemList);
            partlyMarkOrderDTOList.add(partlyMarkOrderDTO);
        });

        this.orderMarkNotifySend(markLackBO, partlyMarkOrderDTOList);
    }

    /**
     * 缺货标记试算接口
     */
    public List<OutStockLackCalResultDTO> calMarkPartSend(OutStockLackDTO outStockLackDTO) {
        List<Long> ids = outStockOrderMapper.findIdsByBusinessIds(
            Collections.singletonList(outStockLackDTO.getBusinessId().toString()), outStockLackDTO.getWarehouseId());
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessValidateException("出库单不存在！");
        }
        List<OutStockOrderPO> outStockOrderList = outStockOrderMapper.findByOrderIds(ids);
        if (CollectionUtils.isEmpty(outStockOrderList)) {
            throw new BusinessValidateException("出库单不存在！");
        }
        orderConstraintCheckBL.checkNormalOrderLack(outStockOrderList);
        OutStockOrderPO outStockOrderPO = outStockOrderList.get(0);
        List<OrderCenterCalOrderLackItemResultDTO> itemResultDTOList = calOrderLackByOrderCenter(
            MarkLackConvertor.convertOrderCenterCalOrderLackDTO(outStockOrderPO, outStockLackDTO));
        return MarkLackConvertor.convertOutStockLackCalResultDTOList(itemResultDTOList);
    }

    /**
     * 中台缺货标记试算接口
     */
    public List<OrderCenterCalOrderLackItemResultDTO> calOrderLackByOrderCenter(OrderCenterCalOrderLackDTO dto) {
        try {
            LOG.info("订单标记拣货缺货时，试算接口入参:{}", JSON.toJSONString(dto));
            OrdersApiResult<OrderCenterCalOrderLackItemResultDTO> result =
                serviceAbilityClientUtil.getInstance().invoke(ORDER_MARK_LACK_CAL_URL, timeout,
                    new TypeToken<OrdersApiResult<OrderCenterCalOrderLackItemResultDTO>>() {}.getType(), dto);
            LOG.info("订单标记拣货缺货时，试算接口返回接口:{}", JSON.toJSONString(result));
            if (result.getCode() != 200) {
                LOG.error("订单标记拣货缺货时，试算接口失败{}", result.getMsg());
            }

            return result.getData();

        } catch (Exception e) {
            LOG.error("订单标记拣货缺货时，试算接口异常", e);
            throw new BusinessValidateException(e.getMessage());
        }
    }

    /**
     * 获取订单项对应二级货主
     */
    private Map<Long, Long> getOrderItemSecOwnerIdMap(List<PartSendWsmDTO> partSendWsmDTOList) {
        Map<Long, Long> secOwnerIdMap = new HashMap<>();
        try {
            if (CollectionUtils.isEmpty(partSendWsmDTOList)) {
                return Collections.emptyMap();
            }
            List<PartSendWsmDTO> partSendWsmDTOS = partSendWsmDTOList.stream()
                .filter(p -> p.getBusinessId() != null && p.getExtendMap() != null && getWarehouseId(p) != null)
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(partSendWsmDTOS)) {
                return Collections.emptyMap();
            }
            Integer warehouseId = getWarehouseId(partSendWsmDTOS.get(0));
            List<String> businessIds = partSendWsmDTOS.stream().filter(Objects::nonNull)
                .map(PartSendWsmDTO::getBusinessId).distinct().map(String::valueOf).collect(Collectors.toList());
            List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.findByBusinessIds(businessIds, warehouseId);
            if (CollectionUtils.isEmpty(outStockOrderPOS)) {
                return Collections.emptyMap();
            }
            List<OutStockOrderItemPO> outStockOrderItemPOS = outStockOrderPOS.stream().map(OutStockOrderPO::getItems)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
                .filter(p -> StringUtils.isNumeric(p.getBusinessItemId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(outStockOrderItemPOS)) {
                return Collections.emptyMap();
            }
            Map<String, List<OutStockOrderItemPO>> itemPOMap =
                outStockOrderItemPOS.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getBusinessItemId));
            itemPOMap.forEach((businessItemId, items) -> {
                Long secOwnerId = null;
                List<OutStockOrderItemDetailPO> details = items.stream().map(OutStockOrderItemPO::getItemDetails)
                    .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
                    .filter(p -> p.getSecOwnerId() != null).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(details)) {
                    secOwnerId = details.get(0).getSecOwnerId();
                }
                secOwnerIdMap.put(Long.valueOf(businessItemId), secOwnerId);
            });
        } catch (Exception e) {
            LOG.error("获取订单项二级货主异常", e);
        }
        LOG.info("getOrderItemSecOwnerIdMap 获取订单项二级货主 {}", JSON.toJSONString(secOwnerIdMap));
        return secOwnerIdMap;
    }

    /**
     * 装箱时，通知订单中台
     */
    public void realCreatePackageNotify(List<PackageOrderItemPO> packageItemList) {
        Map<String, List<PackageOrderItemPO>> pkgOrderItemGroup = packageItemList.stream()
            .filter(item -> StringUtils.isNumeric(item.getOrderBusinessId())
                && StringUtils.isNumeric(item.getOrderItemBusinessId()))
            .collect(Collectors.groupingBy(
                elem -> String.format("%s_%s_%s", elem.getOrderBusinessId(), elem.getBoxCodeNo(), elem.getBoxCode())));

        if (pkgOrderItemGroup.isEmpty()) {
            LOG.info("[装箱时通知订单中台]oms订单信息为空");
            return;
        }
        List<CreatePackageNotifyDTO> createPkgNotifyList = new ArrayList<>();
        pkgOrderItemGroup.forEach((pkgKey, pkgItemList) -> {
            PackageOrderItemPO firstPkgItem = pkgItemList.get(0);
            CreatePackageNotifyDTO pkgNotifyDTO = new CreatePackageNotifyDTO();
            pkgNotifyDTO.setOrderId(Long.valueOf(firstPkgItem.getOrderBusinessId()));
            pkgNotifyDTO.setWarehouseId(firstPkgItem.getWarehouseId());
            pkgNotifyDTO.setBoxCodeNo(firstPkgItem.getBoxCodeNo());
            pkgNotifyDTO.setBoxCode(firstPkgItem.getBoxCode());

            List<CreatePackageItemNotifyDTO> items = new ArrayList<>();
            pkgItemList.forEach(pkgItem -> {
                CreatePackageItemNotifyDTO pkgItemNotify = new CreatePackageItemNotifyDTO();
                pkgItemNotify.setOrderItemId(Long.valueOf(pkgItem.getOrderItemBusinessId()));
                pkgItemNotify.setUnitTotalCount(pkgItem.getUnitTotalCount());
                pkgItemNotify
                    .setOptUserId(pkgItem.getOperatorId() == null ? "1" : String.valueOf(pkgItem.getOperatorId()));
                pkgItemNotify
                    .setCreatePackageTime(pkgItem.getOperatingTime() == null ? new Date() : pkgItem.getOperatingTime());

                items.add(pkgItemNotify);
            });
            pkgNotifyDTO.setItems(items);
            createPkgNotifyList.add(pkgNotifyDTO);
        });

        this.createPackageNotify(createPkgNotifyList);
    }

    /**
     * 销售单快递批量发货通知中台
     */
    public void expressDispatchNotify(List<ExpressDispatchNotifyDTO> notifyDTOList) {
        AssertUtils.notEmpty(notifyDTOList, "销售单快递批量发货通知参数不能为空");
        notifyDTOList.forEach(elem -> {
            AssertUtils.notNull(elem.getOrderId(), "订单id不能为空");
            AssertUtils.notNull(elem.getOptUserId(), "操作人id不能为空");
            AssertUtils.notNull(elem.getOptUserName(), "操作人不能为空");
        });

        if (CollectionUtils.isNotEmpty(notifyDTOList)) {
            notifyDTOList.forEach(notifyDTO -> {
                try {
                    LOG.info("销售单快递批量发货通知中台，通知订单中台ExpressDispatchNotifyDTO入参:{}", JSON.toJSONString(notifyDTO));
                    String invoke = serviceAbilityClientUtil.getInstance().invoke(
                        "/outstock/DispatchService/expressDispatchNotifyList", timeout, String.class, notifyDTO);
                    LOG.info("销售单快递批量发货通知中台，通知订单中台expressDispatchNotify返回结果:{}", invoke);
                } catch (Exception e) {
                    LOG.error("通知订单中台expressDispatchNotify异常", e);
                }
            });
        }
    }

    /**
     * 快递订单全缺通知消息到中台
     *
     * @param omsIdList
     * @param operateUserId
     */
    public void sendRejectOrderMessage(List<String> omsIdList, Integer operateUserId) {
        List<RejectOrderDTO> rejectOrderDTOList =
            OrderCenterConverter.convertToRejectOrderDTOList(omsIdList, operateUserId);
        LOG.info("快递订单全缺通知消息到中台，参数:{}", JSON.toJSONString(rejectOrderDTOList));
        if (CollectionUtils.isNotEmpty(rejectOrderDTOList)) {
            rejectOrderDTOList.forEach(rejectOrderDTONotify -> {
                try {
                    String invoke = serviceAbilityClientUtil.getInstance().invoke(BATCH_OUT_REJECT_NOTIFY_URL, timeout,
                        String.class, rejectOrderDTONotify);
                    LOG.info("快递订单全缺通知消息到中台 OrderCenterBL.sendRejectOrderMessage invoke={}", invoke);
                } catch (Exception e) {
                    LOG.error("快递订单全缺通知消息到中台 OrderCenterBL.sendRejectOrderMessage", e);
                }
            });
        }
    }

    /**
     * 订单快递信息批量同步通知
     */
    public void syncExpressInfoList(List<OrderExpressListSyncDTO> notifyDTOList) {
        LOG.info("syncExpressInfoList 入参:{}", JSON.toJSONString(notifyDTOList));
        AssertUtils.notEmpty(notifyDTOList, "订单快递信息批量同步通知参数不能为空");
        notifyDTOList.forEach(elem -> {
            AssertUtils.notNull(elem.getOrderId(), "订单id不能为空");
            AssertUtils.notNull(elem.getOptUserId(), "操作人id不能为空");
            AssertUtils.notNull(elem.getOptUserName(), "操作人不能为空");
            AssertUtils.notEmpty(elem.getOrderExpressDispatchDTOList(), "物流商快递信息集合不能为空");
        });

        if (CollectionUtils.isNotEmpty(notifyDTOList)) {
            notifyDTOList.forEach(notifyDTO -> {
                try {
                    LOG.info("订单快递信息批量同步通知订单中台syncExpressInfoList,入参:{}", JSON.toJSONString(notifyDTO));
                    String invoke = serviceAbilityClientUtil.getInstance().invoke(SYNC_EXPRESS_INFO_NOTIFY_URL, timeout,
                        String.class, notifyDTO);
                    LOG.info("订单快递信息批量同步通知订单中台syncExpressInfoList,返回结果:{}", invoke);
                } catch (Exception e) {
                    LOG.error("订单快递信息批量同步通知订单中台syncExpressInfoList异常", e);
                }
                SyncTraceDTO syncTraceDTO = new SyncTraceDTO();
                syncTraceDTO.setTraceType(SyncTraceTypeConstants.SYNC_EXPRESS);
                Map<String, String> traceParamMap = new HashMap<>();
                traceParamMap.put(SyncTraceMapKeyConstants.OP_USER_ID, globalCache.getUserName(notifyDTO.getOrderId()));
                List<String> collect = notifyDTO.getOrderExpressDispatchDTOList().stream()
                    .map(OrderExpressListSyncDTO.OrderExpressDispatchDTO::getTrackNumber).collect(Collectors.toList());
                traceParamMap.put(SyncTraceMapKeyConstants.ORDER_NO,
                    org.apache.commons.lang3.StringUtils.join(collect, ","));
                syncTraceDTO.setTraceParamMap(traceParamMap);
                List<Long> orderIdList = new ArrayList<>();
                orderIdList.add(notifyDTO.getOrderId());
                orderCenterService.syncTrace(syncTraceDTO, orderIdList);
            });
        }
    }

    /**
     * 订单出库通知
     */
    public void orderOutStockNotify(List<OrderOutStockNotifyDTO> notifyDTOList) {
        LOG.info("orderOutStockNotify 入参:{}", JSON.toJSONString(notifyDTOList));
        AssertUtils.notEmpty(notifyDTOList, "订单出库通知参数不能为空");
        notifyDTOList.forEach(elem -> {
            AssertUtils.notNull(elem.getCityId(), "城市id不能为空");
            AssertUtils.notNull(elem.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(elem.getOrderId(), "订单id不能为空");
            AssertUtils.notNull(elem.getOptUserId(), "操作人id不能为空");
            AssertUtils.notEmpty(elem.getOrderItemOutStockList(), "订单出库明细集合不能为空");
        });

        if (CollectionUtils.isNotEmpty(notifyDTOList)) {
            notifyDTOList.forEach(notifyDTO -> {
                try {
                    LOG.info("订单出库通知订单中台orderOutStockNotify,入参:{}", JSON.toJSONString(notifyDTO));
                    String invoke = serviceAbilityClientUtil.getInstance().invoke(ORDER_OUT_STOCK_NOTIFY_URL, timeout,
                        String.class, notifyDTO);
                    LOG.info("订单出库通知通知订单中台orderOutStockNotify,返回结果:{}", invoke);
                } catch (Exception e) {
                    LOG.error("订单出库通知通知订单中台orderOutStockNotify异常", e);
                }

                SyncTraceDTO syncTraceDTO = new SyncTraceDTO();
                syncTraceDTO.setTraceType(SyncTraceTypeConstants.OUT_BOUND_ORDER);
                Map<String, String> traceParamMap = new HashMap<>(16);
                traceParamMap.put(SyncTraceMapKeyConstants.OP_USER_ID, globalCache.getUserName(notifyDTO.getOptUserId()));
                traceParamMap.put(SyncTraceMapKeyConstants.ORDER_NO, String.valueOf(notifyDTO.getOrderId()));
                syncTraceDTO.setTraceParamMap(traceParamMap);
                List<Long> orderIdList = new ArrayList<>();
                orderIdList.add(notifyDTO.getOrderId());
                orderCenterService.syncTrace(syncTraceDTO, orderIdList);
            });
        }
    }

    /**
     * 标记缺货的时候，获取拣货中标记缺货的数据 波次状态为待拣货 和 已拣货的不用返回。 订单项对应的拣货任务明细状态是待拣货的也不用处理
     */
    public List<PartMarkPickInfoDTO> getPartMarkPickInfo(PartMarkPickInfoQueryDTO queryDTO) {
        List<String> businessIds = queryDTO.getBusinessIds().stream().map(String::valueOf).collect(Collectors.toList());
        List<Long> outStockOrderIds = outStockOrderMapper.findIdsByBusinessIds(businessIds, queryDTO.getWarehouseId());
        if (outStockOrderIds.isEmpty()) {
            return queryDTO.getBusinessIds().stream().map(PartMarkPickInfoDTO::getDefault).collect(Collectors.toList());
        }
        List<OutStockOrderPO> outStockOrderList = outStockOrderMapper.findByOrderIds(outStockOrderIds);
        List<String> batchInfoList = outStockOrderList.stream().map(OutStockOrderPO::getBatchno)
            .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchInfoList)) {
            return queryDTO.getBusinessIds().stream().map(PartMarkPickInfoDTO::getDefault).collect(Collectors.toList());
        }
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(outStockOrderIds);
        List<String> batchTaskItemIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getBatchTaskItemId)
            .distinct().collect(Collectors.toList());
        List<String> batchNoList = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getBatchNo).distinct()
            .collect(Collectors.toList());
        List<BatchTaskItemPO> batchTaskItemList = batchTaskItemMapper.findBatchTaskItemByIds(batchTaskItemIds);
        List<BatchPO> batchPOList = batchMapper.listByBatchNos(batchNoList);
        return PartMarkPickInfoDTOConvertor.convert(outStockOrderList, orderItemTaskInfoPOList, batchTaskItemList,
            batchPOList);
    }

    /**
     * 订单通用查询查询(分页)
     */
    public List<OrderDocumentDTO> findOrderCenterByPage(OrderQuery qrderQuery, PageParam pageParam) {
        LOG.info("findOrderCenterByPage，入参qrderQuery:{},pageParam:{}", JSON.toJSONString(qrderQuery),
            JSON.toJSONString(pageParam));
        if (null == qrderQuery || null == pageParam) {
            return new ArrayList<>();
        }

        try {
            OrderApiResult<PageableResult<OrderDocumentDTO>> orderPageResult = serviceAbilityClientUtil.getInstance()
                .invoke("/aggregatequery/OrderCommonQueryService/findOrderByPageExternal",
                    new TypeToken<OrderApiResult<PageableResult<OrderDocumentDTO>>>() {}.getType(), qrderQuery,
                    pageParam);
            if (orderPageResult.getCode() != 200) {
                LOG.error("调取订单通用查询查询接口报错{}", orderPageResult.getMsg());
            }

            LOG.info("订单通用查询查询-findOrderCenterByPage，查询结果:{}", JSON.toJSONString(orderPageResult.getData()));
            return orderPageResult.getData().getDatas();

        } catch (Exception e) {
            LOG.warn("findOrderCenterByPage调用中台异常", e);
            return new ArrayList<>();
        }
    }

    public List<OrderDocumentDTO> findOrderCenterByPage(List<String> businessIds) {
        List<Long> businessIdStr =
            businessIds.stream().filter(p -> StringUtils.isNotEmpty(p) && StringUtils.isNumeric(p)).map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(businessIdStr)) {
            return new ArrayList<>();
        }
        OrderQuery orderQuery = new OrderQuery();
        OrderBaseQuery orderBaseQuery = new OrderBaseQuery();
        orderBaseQuery.setOrderIdList(businessIdStr);
        orderBaseQuery.setFirstOrderType(1);
        orderQuery.setOrderBase(orderBaseQuery);
        PageParam pageParam = new PageParam();
        pageParam.setPageIndex(1);
        pageParam.setPageSize(businessIdStr.size());
        return findOrderCenterByPage(orderQuery, pageParam);
    }

    public List<CheckHasPackagedOrderResult> checkHasPackagedOrder(OutStockMarkLackDTO dto) {
        List<OutStockLackDTO> lackList = dto.getOutStockLackDTOList();
        OutStockLackDTO outStockLackDTO = lackList.get(0);
        List<String> businessId = Collections.singletonList(outStockLackDTO.getBusinessId().toString());
        Integer warehouseId = outStockLackDTO.getWarehouseId();
        List<OutStockOrderPO> outStockOrderList = outStockOrderMapper.findByBusinessIds(businessId, warehouseId);
        if (outStockOrderList.isEmpty()) {
            throw new BusinessValidateException("出库单不存在！");
        }
        OutStockOrderPO order = outStockOrderList.get(0);
        Integer orgId = order.getOrgId();
        String orderNo = order.getReforderno();
        Map<String, OutStockOrderItemPO> itemMap = order.getItems().stream()
            .collect(Collectors.toMap(OutStockOrderItemPO::getBusinessItemId, Function.identity()));
        Map<Long, List<PackageOrderItemDTO>> packageMap =
            packageOrderItemMapper.listPackageOrderItemByOrderNo(orderNo, orgId, warehouseId).stream()
                .collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderItemId));
        if (packageMap.isEmpty()) {
            return Collections.emptyList();
        }
        // 找出缺货后的数据
        Map<String, Integer> lackMap = lackList.stream().flatMap(it -> {
            Map<Long, Integer> itemLackMap = it.getItemMap();
            // 缺货前数量 - 缺货后数量 = 缺货数量
            return it.getItemShipMap().keySet().stream().map(String::valueOf).map(key -> Pair.of(key,
                itemMap.get(key).getOriginalUnitTotalCount().intValue() - itemLackMap.get(Long.valueOf(key))));
        }).filter(it -> it.getSecond() > 0).collect(Collectors.toMap(Pair::getFirst, Pair::getSecond));
        if (lackMap.isEmpty()) {
            return Collections.emptyList();
        }
        return itemMap.entrySet().stream().filter(it -> lackMap.containsKey(it.getKey())).map(it -> {
            OutStockOrderItemPO orderItem = it.getValue();
            CheckHasPackagedOrderResult result = new CheckHasPackagedOrderResult();
            List<PackageOrderItemDTO> packageItems = packageMap.get(orderItem.getId());
            boolean hasPackaged = packageItems != null;
            result.setHasPackaged(hasPackaged);
            result.setGift(orderItem.getIsGift());
            result.setProductName(orderItem.getProductname());
            if (hasPackaged) {
                result.setBoxCodeNo(
                    packageItems.stream().map(PackageOrderItemDTO::getBoxCodeNo).collect(Collectors.toSet()));
            }
            result.setPackageSpec(orderItem.getSpecquantity());
            result.setSaleSpec(orderItem.getSalespecquantity());
            result.setLackUnitCount(lackMap.get(it.getKey()));
            return result;
        }).filter(CheckHasPackagedOrderResult::getHasPackaged).collect(Collectors.toList());
    }

}
