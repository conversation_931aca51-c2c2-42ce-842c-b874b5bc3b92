package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import java.math.BigDecimal;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/3/12
 */
public class ItemLackHelperBO {

    private Long id;

    private BigDecimal unitTotalCount;

    private BigDecimal lackUnitTotalCount;

    private BigDecimal originalUnitTotalCount;

    /**
     * 获取
     *
     * @return id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置
     *
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取
     *
     * @return unitTotalCount
     */
    public BigDecimal getUnitTotalCount() {
        return this.unitTotalCount;
    }

    /**
     * 设置
     *
     * @param unitTotalCount
     */
    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    /**
     * 获取
     *
     * @return lackUnitTotalCount
     */
    public BigDecimal getLackUnitTotalCount() {
        return this.lackUnitTotalCount;
    }

    /**
     * 设置
     *
     * @param lackUnitTotalCount
     */
    public void setLackUnitTotalCount(BigDecimal lackUnitTotalCount) {
        this.lackUnitTotalCount = lackUnitTotalCount;
    }

    /**
     * 获取
     *
     * @return originalUnitTotalCount
     */
    public BigDecimal getOriginalUnitTotalCount() {
        return this.originalUnitTotalCount;
    }

    /**
     * 设置
     *
     * @param originalUnitTotalCount
     */
    public void setOriginalUnitTotalCount(BigDecimal originalUnitTotalCount) {
        this.originalUnitTotalCount = originalUnitTotalCount;
    }
}
