package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-11-02 15:03
 **/
public class PackageOrderInfoDTO implements Serializable {

    /**
     * 出库单 id
     */
    private Long outStockOrderId;

    /**
     * 订单号
     */
    private String refOrderNo;

    /**
     * 订单类型
     */
    private Byte orderType;

    /**
     * 订单类型名称
     */
    private String orderTypeName;

    /**
     * 配送员
     */
    private String driverName;

    /**
     * 店铺名称
     */
    private String companyName;

    /**
     * 下单时间
     */
    private String orderTime;

    /**
     * 订单标记
     */
    private Byte orderMark;

    /**
     * 订单标记 描述
     */
    private String orderMarkDes;

    /**
     * 联系方式 (姓名)
     */
    private String contactName;

    /**
     * 联系方式 (电话)
     */
    private String contactMobile;

    /**
     * 订单状态
     */
    private Byte orderStatus;

    /**
     * 订单状态 描述
     */
    private String orderStatusDes;

    /**
     * 收货地址 省
     */
    private String province;

    /**
     * 收货地址 市
     */
    private String city;

    /**
     * 收获地址 区
     */
    private String district;

    /**
     * 收货地址 详细地址
     */
    private String detailAddress;

    /**
     * 应付金额
     */
    private BigDecimal payableAmount;

    /**
     * 优惠合计
     */
    private BigDecimal discountAmount;

    /**
     * 发货仓库
     */
    private String senderWarehouse;

    /**
     * 订单明细
     */
    private List<PackageOrderItemInfoDTO> packageOrderItems;

    public Long getOutStockOrderId() {
        return outStockOrderId;
    }

    public void setOutStockOrderId(Long outStockOrderId) {
        this.outStockOrderId = outStockOrderId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Byte getOrderType() {
        return orderType;
    }

    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }

    public String getOrderTypeName() {
        return orderTypeName;
    }

    public void setOrderTypeName(String orderTypeName) {
        this.orderTypeName = orderTypeName;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(String orderTime) {
        this.orderTime = orderTime;
    }

    public Byte getOrderMark() {
        return orderMark;
    }

    public void setOrderMark(Byte orderMark) {
        this.orderMark = orderMark;
    }

    public String getOrderMarkDes() {
        return orderMarkDes;
    }

    public void setOrderMarkDes(String orderMarkDes) {
        this.orderMarkDes = orderMarkDes;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public Byte getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Byte orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getOrderStatusDes() {
        return orderStatusDes;
    }

    public void setOrderStatusDes(String orderStatusDes) {
        this.orderStatusDes = orderStatusDes;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    public BigDecimal getPayableAmount() {
        return payableAmount;
    }

    public void setPayableAmount(BigDecimal payableAmount) {
        this.payableAmount = payableAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public String getSenderWarehouse() {
        return senderWarehouse;
    }

    public void setSenderWarehouse(String senderWarehouse) {
        this.senderWarehouse = senderWarehouse;
    }

    public List<PackageOrderItemInfoDTO> getPackageOrderItems() {
        return packageOrderItems;
    }

    public void setPackageOrderItems(List<PackageOrderItemInfoDTO> packageOrderItems) {
        this.packageOrderItems = packageOrderItems;
    }
}
