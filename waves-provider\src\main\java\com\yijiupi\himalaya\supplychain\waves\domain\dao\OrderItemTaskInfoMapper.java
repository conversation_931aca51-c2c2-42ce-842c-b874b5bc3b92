package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 出库单项拣货关联表
 *
 * <AUTHOR>
 * @date 2020-06-09 15:32
 */
@Mapper
public interface OrderItemTaskInfoMapper {

    void insertBatch(@Param("list") List<OrderItemTaskInfoPO> recordPOList);

    /**
     * 根据订单id查询出库单项拣货关联信息
     */
    List<OrderItemTaskInfoPO> listOrderItemTaskInfoByBatchNos(@Param("list") List<String> batchNos);

    /**
     * 根据波次编号及出库单类型，检查订单能否删除
     *
     * @param batchNos
     * @return
     */
    int checkBatchCanDeleteByBatchNoAndOutBoundType(@Param("list") List<String> batchNos,
        @Param("outBoundType") Integer outBoundType);

    List<OrderItemTaskInfoPO> listOrderItemTaskInfoByOrderIds(@Param("list") Collection<Long> orderIds);

    /**
     * 根据订单项id查询出库单项拣货关联信息
     */
    List<OrderItemTaskInfoPO> listOrderItemTaskInfoByOrderItemIds(@Param("list") Collection<Long> orderItemIds);

    /**
     * 根据拣货任务项id查询出库单项拣货关联信息
     */
    List<OrderItemTaskInfoPO>
        listOrderItemTaskInfoByBatchTaskItemIds(@Param("list") Collection<String> batchTaskItemIds);

    /**
     * 根据订单id查询出库单项拣货关联信息（包含detail）
     */
    List<OrderItemTaskInfoPO> listTaskInfoAndDetailByOrderIds(@Param("list") List<Long> orderIds);

    /**
     * 根据订单项id查询出库单项拣货关联信息（包含detail）
     */
    List<OrderItemTaskInfoPO> listTaskInfoAndDetailByOrderItemIds(@Param("list") List<Long> orderItemIds);

    /**
     * 根据拣货任务项id查询出库单项拣货关联信息（包含detail）
     */
    List<OrderItemTaskInfoPO> listTaskInfoAndDetailByBatchTaskItemIds(@Param("list") List<String> batchTaskItemIds);

    /**
     * 根据拣货任务id查询出库单项拣货关联信息（包含detail）
     */
    List<OrderItemTaskInfoPO> listTaskInfoAndDetailByBatchTaskIds(@Param("list") List<String> batchTaskIds);

    List<OrderItemTaskInfoPO> listTaskInfoByBatchTaskIds(@Param("list") Collection<String> batchTaskIds);

    void deleteByIds(@Param("list") List<Long> ids);

    void deleteByBatchNos(@Param("list") List<String> batchNos);

    void updateBatch(@Param("list") List<OrderItemTaskInfoPO> recordPOList);

    /**
     * 批量更新拣货任务Id和编号
     */
    void updateBatchByBatchTask(@Param("list") List<OrderItemTaskInfoPO> recordPOList);

    /**
     * 根据订单号查询关联的波次
     */
    List<OrderItemTaskInfoPO> listOrderRelateBatch(@Param("list") List<String> refOrderNos,
        @Param("warehouseId") Integer warehouseId);

    /**
     * 查询缺货产品
     *
     * @return
     */
    List<Long> getLackProductSkuId(@Param("cityId") Integer cityId, @Param("warehouseId") Integer warehouseId,
        @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    int updateByPrimaryKeySelective(OrderItemTaskInfoPO orderItemTaskInfoPO);

    List<Long> listRefOrderIdByBatchTaskIds(@Param("list") Collection<String> batchTaskIds);

    List<OrderItemTaskInfoPO> findByBatchTaskAndOrder(@Param("batchTaskIds") List<String> batchTaskIds,
        @Param("orderIds") List<Long> orderIds);

    /**
     * 根据拣货任务项id查询出库单项拣货关联信息（包含detail）
     */
    List<OrderItemTaskInfoPO> listTaskInfoAndDetailByBatchTaskItemIdsAndOrgId(
            @Param("list") List<String> batchTaskItemIds, @Param("orgId") Integer orgId);

    /**
     * 根据拣货任务项id查询出库单项拣货关联信息
     */
    List<OrderItemTaskInfoPO> listTaskInfoByBatchTaskItemIds(@Param("list") List<String> batchTaskItemIds, @Param("orgId") Integer orgId);


}
