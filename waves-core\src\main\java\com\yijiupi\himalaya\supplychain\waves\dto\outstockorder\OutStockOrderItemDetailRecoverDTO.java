package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/27
 */
public class OutStockOrderItemDetailRecoverDTO {

    private List<Long> outStockOrderIds;

    private Integer warehouseId;

    private Integer orderId;
    /**
     * 是否处理item和detail数量相等的数据
     */
    private Boolean handleEqualCountItem = Boolean.TRUE;
    /**
     * 是否用订单中台的数据创建detail
     */
    private Boolean useOrderCenterDetail;
    /**
     * 是否用库存记录创建detail
     */
    private Boolean useInventoryRecord;

    /**
     * 是否给中台发送变更消息
     */
    private Boolean sendDetailToOmsOrderCenter;
    /**
     * 是否修复detail，默认true
     */
    private Boolean recoverDetailOrNot = Boolean.TRUE;
    /**
     * erp变动参数
     */
    private OutStockOrderItemDetailRecoverErpDTO erpDTO;
    /**
     * 获取
     *
     * @return outStockOrderIds
     */
    public List<Long> getOutStockOrderIds() {
        return this.outStockOrderIds;
    }

    /**
     * 设置
     *
     * @param outStockOrderIds
     */
    public void setOutStockOrderIds(List<Long> outStockOrderIds) {
        this.outStockOrderIds = outStockOrderIds;
    }

    /**
     * 获取
     *
     * @return warehouseId
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置
     *
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取
     *
     * @return orderId
     */
    public Integer getOrderId() {
        return this.orderId;
    }

    /**
     * 设置
     *
     * @param orderId
     */
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 是否处理item和detail数量相等的数据
     *
     * @return handleEqualCountItem 是否处理item和detail数量相等的数据
     */
    public Boolean isHandleEqualCountItem() {
        return this.handleEqualCountItem;
    }

    /**
     * 设置 是否处理item和detail数量相等的数据
     *
     * @param handleEqualCountItem 是否处理item和detail数量相等的数据
     */
    public void setHandleEqualCountItem(Boolean handleEqualCountItem) {
        this.handleEqualCountItem = handleEqualCountItem;
    }

    /**
     * 获取 是否用订单中台的数据创建
     *
     * @return useOrderCenterDetail 是否用订单中台的数据创建
     */
    public Boolean isUseOrderCenterDetail() {
        return this.useOrderCenterDetail;
    }

    /**
     * 设置 是否用订单中台的数据创建
     *
     * @param useOrderCenterDetail 是否用订单中台的数据创建
     */
    public void setUseOrderCenterDetail(Boolean useOrderCenterDetail) {
        this.useOrderCenterDetail = useOrderCenterDetail;
    }

    /**
     * 获取 是否用库存记录创建detail
     *
     * @return useInventoryRecord 是否用库存记录创建detail
     */
    public Boolean isUseInventoryRecord() {
        return this.useInventoryRecord;
    }

    /**
     * 设置 是否用库存记录创建detail
     *
     * @param useInventoryRecord 是否用库存记录创建detail
     */
    public void setUseInventoryRecord(Boolean useInventoryRecord) {
        this.useInventoryRecord = useInventoryRecord;
    }

    /**
     * 获取 是否给中台发送变更消息
     *
     * @return sendDetailToOmsOrderCenter 是否给中台发送变更消息
     */
    public Boolean isSendDetailToOmsOrderCenter() {
        return this.sendDetailToOmsOrderCenter;
    }

    /**
     * 设置 是否给中台发送变更消息
     *
     * @param sendDetailToOmsOrderCenter 是否给中台发送变更消息
     */
    public void setSendDetailToOmsOrderCenter(Boolean sendDetailToOmsOrderCenter) {
        this.sendDetailToOmsOrderCenter = sendDetailToOmsOrderCenter;
    }

    /**
     * 获取 是否修复detail，默认true
     *
     * @return recoverDetailOrNot 是否修复detail，默认true
     */
    public Boolean isRecoverDetailOrNot() {
        return this.recoverDetailOrNot;
    }

    /**
     * 设置 是否修复detail，默认true
     *
     * @param recoverDetailOrNot 是否修复detail，默认true
     */
    public void setRecoverDetailOrNot(boolean recoverDetailOrNot) {
        this.recoverDetailOrNot = recoverDetailOrNot;
    }


    /**
     * 获取 erp变动参数
     *
     * @return erpDTO erp变动参数
     */
    public OutStockOrderItemDetailRecoverErpDTO getErpDTO() {
        return this.erpDTO;
    }

    /**
     * 设置 erp变动参数
     *
     * @param erpDTO erp变动参数
     */
    public void setErpDTO(OutStockOrderItemDetailRecoverErpDTO erpDTO) {
        this.erpDTO = erpDTO;
    }
}
