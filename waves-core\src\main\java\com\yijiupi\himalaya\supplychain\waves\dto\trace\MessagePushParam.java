package com.yijiupi.himalaya.supplychain.waves.dto.trace;

import java.io.Serializable;
import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-06-03 10:48
 **/
public class MessagePushParam implements Serializable {

    /**
     * 角色 code
     */
    private Collection<String> roleCodes;

    /**
     * 用户 id
     */
    private Collection<Integer> userIds;

    /**
     * 仓库 id
     */
    private Integer warehouseId;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 额外参数
     */
    private Map<String, String> extraMap;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 0：往账号的最新的 device 上推送信息
     * 1：往账号关联的所有 device 设备上推送信息
     */
    private Integer accountPushType = 0;

    /**
     * 按角色推消息
     *
     * @param roleCodes   角色 code
     * @param warehouseId 仓库 id
     * @param content     消息内容
     */
    public static MessagePushParam of(Collection<String> roleCodes, Integer warehouseId, String content) {
        MessagePushParam result = new MessagePushParam();
        result.setRoleCodes(roleCodes);
        result.setWarehouseId(warehouseId);
        result.setContent(content);
        return result;
    }

    /**
     * 按用户推送消息
     *
     * @param userIds 用户 id
     * @param content 消息内容
     */
    public static MessagePushParam of(Collection<Integer> userIds, String content, Map<String, String> extra, String title) {
        MessagePushParam result = new MessagePushParam();
        result.setUserIds(userIds);
        result.setContent(content);
        result.setExtraMap(extra);
        result.setTitle(title);
        return result;
    }

    public Collection<String> getRoleCodes() {
        return roleCodes;
    }

    public void setRoleCodes(Collection<String> roleCodes) {
        this.roleCodes = roleCodes;
    }

    public Collection<Integer> getUserIds() {
        return userIds;
    }

    public void setUserIds(Collection<Integer> userIds) {
        this.userIds = userIds;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Map<String, String> getExtraMap() {
        return extraMap;
    }

    public void setExtraMap(Map<String, String> extraMap) {
        this.extraMap = extraMap;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getAccountPushType() {
        return accountPushType;
    }

    public void setAccountPushType(Integer accountPushType) {
        this.accountPushType = accountPushType;
    }
}
