package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title: PackageCodePrintQueryParam
 * @description:
 * @date 2022-09-23 10:19
 */
public class PackageCodePrintQueryParam implements Serializable {
    /**
     * 出库单号
     */
    private String refOrderNo;
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 获取 出库单号
     *
     * @return refOrderNo 出库单号
     */
    public String getRefOrderNo() {
        return this.refOrderNo;
    }

    /**
     * 设置 出库单号
     *
     * @param refOrderNo 出库单号
     */
    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
