package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: BatchTaskBySowTaskQueryParam
 * @description:
 * @date 2022-09-23 14:42
 */
public class BatchTaskBySowTaskQueryParam implements Serializable {
    /**
     * 播种任务编号
     */
    private List<String> sowTaskNos;

    /**
     * 任务状态
     */
    private List<Integer> taskStates;

    /**
     * 组织机构id
     */
    private Integer orgId;

    /**
     * 获取 播种任务编号
     *
     * @return sowTaskNos 播种任务编号
     */
    public List<String> getSowTaskNos() {
        return this.sowTaskNos;
    }

    /**
     * 设置 播种任务编号
     *
     * @param sowTaskNos 播种任务编号
     */
    public void setSowTaskNos(List<String> sowTaskNos) {
        this.sowTaskNos = sowTaskNos;
    }

    /**
     * 获取 任务状态
     *
     * @return taskStates 任务状态
     */
    public List<Integer> getTaskStates() {
        return this.taskStates;
    }

    /**
     * 设置 任务状态
     *
     * @param taskStates 任务状态
     */
    public void setTaskStates(List<Integer> taskStates) {
        this.taskStates = taskStates;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }
}
