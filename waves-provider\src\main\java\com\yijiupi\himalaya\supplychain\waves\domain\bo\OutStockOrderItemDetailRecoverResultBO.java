package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/27
 */
public class OutStockOrderItemDetailRecoverResultBO {


    private List<OutStockOrderItemPO> detailList;

    /**
     * 获取
     *
     * @return detailList
     */
    public List<OutStockOrderItemPO> getDetailList() {
        return this.detailList;
    }

    /**
     * 设置
     *
     * @param detailList
     */
    public void setDetailList(List<OutStockOrderItemPO> detailList) {
        this.detailList = detailList;
    }
}
