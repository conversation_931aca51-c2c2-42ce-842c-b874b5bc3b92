package com.yijiupi.himalaya.supplychain.waves.dto.secondsort;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-06-02
 */
public class SecondSortQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 城市id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 拣货任务id
     */
    private String batchTaskId;
    /**
     * 拣货任务详情id
     */
    private String batchTaskItemId;

    /**
     * 拣货任务详情id
     */
    private List<String> batchTaskItemIds;

    /**
     * 产品编码
     */
    private Long skuId;
    /**
     * 关联订单编号
     */
    private String refOrderNo;
    /**
     * 关联订单id
     */
    private Long refOrderId;

    private Long refOrderItemId;

    /**
     * 分拣员(姓名)
     */
    private String sorterName;
    /**
     * 分拣员id
     */
    private Integer sorterId;

    /** 出库位id */
    private Long locationId;

    /** 出库位名称 */
    private String locationName;

    private List<Long> refOrderIdList;

    private String batchId;

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public List<String> getBatchTaskItemIds() {
        return batchTaskItemIds;
    }

    public void setBatchTaskItemIds(List<String> batchTaskItemIds) {
        this.batchTaskItemIds = batchTaskItemIds;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public String getBatchTaskItemId() {
        return batchTaskItemId;
    }

    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Long getRefOrderId() {
        return refOrderId;
    }

    public void setRefOrderId(Long refOrderId) {
        this.refOrderId = refOrderId;
    }

    public String getSorterName() {
        return sorterName;
    }

    public void setSorterName(String sorterName) {
        this.sorterName = sorterName;
    }

    public Integer getSorterId() {
        return sorterId;
    }

    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    public List<Long> getRefOrderIdList() {
        return refOrderIdList;
    }

    public void setRefOrderIdList(List<Long> refOrderIdList) {
        this.refOrderIdList = refOrderIdList;
    }

    public Long getRefOrderItemId() {
        return refOrderItemId;
    }

    public void setRefOrderItemId(Long refOrderItemId) {
        this.refOrderItemId = refOrderItemId;
    }
}