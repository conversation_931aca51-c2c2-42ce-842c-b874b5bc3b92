package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/2/26
 */
public class PackageOrderItemModBO {

    private List<PackageOrderItemDTO> updateList;

    private List<PackageOrderItemDTO> deleteList;

    /**
     * 获取
     *
     * @return updateList
     */
    public List<PackageOrderItemDTO> getUpdateList() {
        return this.updateList;
    }

    /**
     * 设置
     *
     * @param updateList
     */
    public void setUpdateList(List<PackageOrderItemDTO> updateList) {
        this.updateList = updateList;
    }

    /**
     * 获取
     *
     * @return deleteList
     */
    public List<PackageOrderItemDTO> getDeleteList() {
        return this.deleteList;
    }

    /**
     * 设置
     *
     * @param deleteList
     */
    public void setDeleteList(List<PackageOrderItemDTO> deleteList) {
        this.deleteList = deleteList;
    }
}
