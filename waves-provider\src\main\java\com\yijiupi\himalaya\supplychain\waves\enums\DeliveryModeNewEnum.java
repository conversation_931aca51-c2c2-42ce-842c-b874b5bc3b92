package com.yijiupi.himalaya.supplychain.waves.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2019/8/22 14:26
 */
public enum DeliveryModeNewEnum {

    /**
     * 酒批配送
     */
    JPPS("仓库配送", (byte)0),
    /**
     * 合作商配送
     */
    HZSPS("合作商配送", (byte)1),
    /**
     * 配送商配送
     */
    PSSPS("配送商配送", (byte)2),
    /**
     * 客户自提
     */
    KHZT("客户自提", (byte)4),

    /**
     * 快递直发
     */
    KDZF("快递直发", (byte)7);

    private Byte value;

    private String text;

    DeliveryModeNewEnum(String text, Byte value) {
        this.text = text;
        this.value = value;
    }

    /**
     * @return the value
     */
    public Byte getValue() {
        return value;
    }

    /**
     * @return the text
     */
    public String getText() {
        return text;
    }

    public static String getTextByValue(Byte value) {
        if (null == value) {
            return "";
        }
        for (DeliveryModeNewEnum bt : values()) {
            if (bt.getValue().equals(value)) {
                return bt.getText();
            }
        }
        return "";
    }

    public static Byte getValueByText(String text) {
        if (StringUtils.isEmpty(text)) {
            return null;
        }
        for (DeliveryModeNewEnum bt : values()) {
            if (text.equals(bt.getText())) {
                return bt.getValue();
            }
        }
        return null;
    }
}
