package com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch;

import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/25
 */
public class CreateExistSowTaskPO {

    private SowTaskPO existSowTaskPO;

    private List<SowOrderPO> sowOrderPOList;

    /**
     * 获取
     *
     * @return existSowTaskPO
     */
    public SowTaskPO getExistSowTaskPO() {
        return this.existSowTaskPO;
    }

    /**
     * 设置
     *
     * @param existSowTaskPO
     */
    public void setExistSowTaskPO(SowTaskPO existSowTaskPO) {
        this.existSowTaskPO = existSowTaskPO;
    }

    /**
     * 获取
     *
     * @return sowOrderPOList
     */
    public List<SowOrderPO> getSowOrderPOList() {
        return this.sowOrderPOList;
    }

    /**
     * 设置
     *
     * @param sowOrderPOList
     */
    public void setSowOrderPOList(List<SowOrderPO> sowOrderPOList) {
        this.sowOrderPOList = sowOrderPOList;
    }
}
