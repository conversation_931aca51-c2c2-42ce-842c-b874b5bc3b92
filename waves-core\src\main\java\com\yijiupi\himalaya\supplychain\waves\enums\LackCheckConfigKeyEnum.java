package com.yijiupi.himalaya.supplychain.waves.enums;

public class LackCheckConfigKeyEnum {
    /**
     * SKU再次标记缺货时，无需校验的时限
     */
    public static final String LACK_NO_CHECK_HOUR_LIMIT = "lack_no_check_hour_limit";
    /**
     * 进行二维码校验的小时时限
     */
    public static final String LACK_QR_CHECK_HOUR_LIMIT = "lack_qr_check_hour_limit";
    /**
     * 进行二维码校验的缺货sku的数量限制
     */
    public static final String LACK_QR_CHECK_SKUCOUNT_LIMIT = "lack_qr_check_skuCount_limit";

    /**
     * 所有配置项
     */
    public static final String[] ALL_KEY_ARRAY =
        new String[] {LACK_NO_CHECK_HOUR_LIMIT, LACK_QR_CHECK_HOUR_LIMIT, LACK_QR_CHECK_SKUCOUNT_LIMIT};
}
