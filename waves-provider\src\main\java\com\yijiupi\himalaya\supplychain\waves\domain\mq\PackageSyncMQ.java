package com.yijiupi.himalaya.supplychain.waves.domain.mq;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderCenterBL;
import com.yijiupi.himalaya.supplychain.waves.domain.po.PackageOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageBoxItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.PackageTypeEnum;

/**
 * 订单二次包装信息同步消息到OMS
 *
 * <AUTHOR>
 * @date 2018/7/25 11:33
 */
@Component
public class PackageSyncMQ {

    private static final Logger LOG = LoggerFactory.getLogger(PackageSyncMQ.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${ex.supplychain.orderpackage.packageSync}")
    private String packageSyncExchange;

    @Value("${ex.supplychain.orderpackage.packageDelete}")
    private String packageDelateExchange;

    @Value("${ex.baseservice.message.yjp_Message_Push}")
    private String appMsgPushExchange;
    @Autowired
    private OrderCenterBL orderCenterBL;

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    /**
     * 保存发送消息
     */
    public void send(List<PackageOrderItemPO> packageOrderItemPOList) {
        if (!CollectionUtils.isEmpty(packageOrderItemPOList)) {
            List<PackageOrderItemPO> notifyList = packageOrderItemPOList.stream()
                .filter(po -> po.getPackageType() == null || po.getPackageType() == PackageTypeEnum.包装箱.getType())
                .collect(Collectors.toList());
            // 打拖不发送消息
            if (!CollectionUtils.isEmpty(notifyList)) {
                List<PackageBoxItemDTO> packageBoxItemDTOList =
                    notifyList.stream().map(this::convertToBoxItemDTO).collect(Collectors.toList());
                LOG.info("二次包装信息同步发送消息: {}", JSON.toJSONString(notifyList));
                rabbitTemplate.convertAndSend(packageSyncExchange, null, packageBoxItemDTOList);
                orderCenterBL.realCreatePackageNotify(notifyList);

            }
        }
    }

    /**
     * 合并处理，同一产品装多个箱时，将箱号用“，”拼接起来
     *
     * @return
     */
    private List<PackageBoxItemDTO> mergeProcess(List<PackageBoxItemDTO> packageBoxItemDTOList) {
        List<PackageBoxItemDTO> mergeList = new ArrayList<>();
        packageBoxItemDTOList.forEach(p -> {
            List<PackageBoxItemDTO> filterList = mergeList.stream()
                .filter(q -> String.format("%s-%s", q.getBusinessId(), q.getBusinessItemId())
                    .equals(String.format("%s-%s", p.getBusinessId(), p.getBusinessItemId())))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterList)) {
                mergeList.add(p);
            } else {
                filterList.get(0).setPackageBoxNO(filterList.get(0).getPackageBoxNO() + "," + p.getPackageBoxNO());
                filterList.get(0)
                    .setPackageBoxSequence(filterList.get(0).getPackageBoxSequence() + "," + p.getPackageBoxSequence());
                filterList.get(0).setPackageCount(filterList.get(0).getPackageCount().add(p.getPackageCount()));
                filterList.get(0).setUnitTotalCount(filterList.get(0).getUnitTotalCount().add(p.getUnitTotalCount()));
            }
        });
        return mergeList;
    }

    /**
     * 删除发送消息
     *
     * @param refOrderNos
     */
    public void sendRemove(List<String> refOrderNos, Integer orgId, Integer warehouseId) {
        if (!CollectionUtils.isEmpty(refOrderNos)) {
            List<PackageBoxItemDTO> packageBoxItemDTOList = new ArrayList<>();
            refOrderNos.forEach(p -> {
                PackageBoxItemDTO packageBoxItemDTO = new PackageBoxItemDTO();
                packageBoxItemDTO.setBusinessNo(p);
                packageBoxItemDTO.setCityId(orgId);
                packageBoxItemDTO.setWarehouseId(warehouseId);
                packageBoxItemDTOList.add(packageBoxItemDTO);
            });
            LOG.info("删除二次包装信息同步发送消息:{}", JSON.toJSONString(packageBoxItemDTOList));
            rabbitTemplate.convertAndSend(packageDelateExchange, null, packageBoxItemDTOList);
        }
    }

    /**
     * DTO转换
     *
     * @return
     */
    private PackageBoxItemDTO convertToBoxItemDTO(PackageOrderItemPO po) {
        if (null == po) {
            return null;
        }
        PackageBoxItemDTO packageBoxItemDTO = new PackageBoxItemDTO();
        packageBoxItemDTO.setCreateUserId(-1);
        packageBoxItemDTO.setCityId(po.getOrgId());
        packageBoxItemDTO.setWarehouseId(po.getWarehouseId());
        packageBoxItemDTO.setBusinessId(po.getBusinessId() != null ? Long.valueOf(po.getBusinessId()) : null);
        packageBoxItemDTO.setBusinessNo(po.getRefOrderNo());
        packageBoxItemDTO
            .setBusinessItemId(po.getBusinessItemId() != null ? Long.valueOf(po.getBusinessItemId()) : null);
        packageBoxItemDTO.setPackageBoxNO(po.getBoxCodeNo());
        packageBoxItemDTO.setPackageBoxSequence(po.getBoxCode());
        packageBoxItemDTO.setPackageCount(po.getPackageCount());
        packageBoxItemDTO.setUnitTotalCount(po.getUnitTotalCount());
        packageBoxItemDTO.setRemark(po.getRemark());
        packageBoxItemDTO.setPackageType(po.getPackageType());
        return packageBoxItemDTO;
    }

}
