package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.batch.ISowManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.FixSowOrderSequenceBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.SowManagerBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderItemDetailAllotDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderSaveDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderUpdateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SownOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.*;
import com.yijiupi.himalaya.supplychain.waves.dto.trace.OrderTraceDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.SowTaskStateEnum;

@Service(timeout = 120000)
// @Transactional
public class SowManageServiceImpl implements ISowManageService {
    private static final Logger LOG = LoggerFactory.getLogger(SowManageServiceImpl.class);

    @Autowired
    private SowManagerBL sowManagerBL;
    @Autowired
    private FixSowOrderSequenceBL fixSowOrderSequenceBL;
    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;

    @Autowired
    private SowTaskMapper sowTaskMapper;

    /**
     * 提交播种任务并保存
     *
     */
    @Override
    public void saveSowOrder(SowOrderSaveDTO sowOrderSaveDTO) {
        AssertUtils.notNull(sowOrderSaveDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(sowOrderSaveDTO.getSowTaskNo(), "播种任务编号不能为空");
        AssertUtils.notNull(sowOrderSaveDTO.getOrgId(), "orgId不能为空");
        sowManagerBL.saveSowOrder(sowOrderSaveDTO);
    }

    /**
     * 播种任务领取
     */
    @Override
    public SowTaskDTO receiveSowTaskByClient(SowTaskReceiveDTO taskReceiveDTO) {
        Integer warehouseId = taskReceiveDTO.getWarehouseId();
        String locationName = taskReceiveDTO.getLocationName();
        Integer orgId = taskReceiveDTO.getOrgId();
        AssertUtils.notNull(warehouseId, "仓库 id 不能为空");
        AssertUtils.notNull(locationName, "必须关联集货位");
        AssertUtils.notNull(orgId, "城市 id 不能为空");
        AssertUtils.notEmpty(taskReceiveDTO.getSowTaskType(), "播种任务类型不能为空");
        // 查找可播种的任务(可播种任务判断:当前播种任务下必有一个拣货任务完成)
        SowTaskDTO canSowingTask = sowManagerBL.findCanSowingTask(taskReceiveDTO);
        LOG.warn("领取的播种任务为:{}", JSON.toJSONString(canSowingTask));
        // 更新该播种任务信息，添加操作日志
        SowTaskDTO updateSowTaskDTO = new SowTaskDTO();
        BeanUtils.copyProperties(canSowingTask, updateSowTaskDTO);
        updateSowTaskDTO.setState(SowTaskStateEnum.播种中.getType());
        updateSowTaskDTO.setOperatorName(taskReceiveDTO.getSower());
        updateSowTaskDTO.setOperatorId(taskReceiveDTO.getSowerId());
        sowManagerBL.updateSowTaskById(updateSowTaskDTO);
        return canSowingTask;
    }

    /**
     * 修改播种任务信息（通过id）
     *
     * @param sowTaskDTO
     */
    @Override
    public void updateSowTask(SowTaskDTO sowTaskDTO) {
        AssertUtils.notNull(sowTaskDTO.getId(), "播种任务id不能为空");
        AssertUtils.notNull(sowTaskDTO.getOrgId(), "城市id不能为空");
        sowManagerBL.updateSowTaskById(sowTaskDTO);
    }

    /**
     * 为播种任务设置集货区
     *
     * @param batchTaskId
     * @return
     */
    @Override
    public Long setSowLocation(String batchTaskId) {
        AssertUtils.notNull(batchTaskId, "拣货任务id不能为空");
        return sowManagerBL.setSowLocation(batchTaskId);
    }

    /**
     * 通过SCOP提交播种任务
     *
     * @param sowOrderSaveDTO
     */
    @Override
    public void saveSowOrderBySCOP(SowOrderSaveDTO sowOrderSaveDTO) {
        String sowTaskNo = sowOrderSaveDTO.getSowTaskNo();
        Integer orgId = sowOrderSaveDTO.getOrgId();
        AssertUtils.notNull(sowOrderSaveDTO.getSowTaskNo(), "播种任务编号不能为空");
        AssertUtils.notNull(sowOrderSaveDTO.getOrgId(), "orgId不能为空");

        SowTaskDTO sowTaskDTO = sowManagerBL.getAllBySowTaskNo(orgId, sowTaskNo);
        if (Objects.isNull(sowTaskDTO)) {
            try {
                sowTaskDTO = sowManagerBL.getAllBySowTaskId(orgId, Long.valueOf(sowTaskNo));
            } catch (Exception e) {

            }
        }

        if (sowTaskDTO == null) {
            throw new BusinessException("编号为:" + sowTaskNo + "的播种任务不存在");
        }

        List<SowTaskItemDTO> sowTaskItemDTOS = sowTaskDTO.getSowTaskItemDTOS();
        if (CollectionUtils.isNotEmpty(sowTaskItemDTOS)) {
            sowTaskItemDTOS = sowTaskItemDTOS.stream().filter(item -> item.getState() != SowTaskStateEnum.已播种.getType())
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sowTaskItemDTOS)) {
                sowTaskItemDTOS = sowManagerBL.assignmentSowTaskItemLocation(orgId, sowTaskItemDTOS);
            }
        }
        List<SownOrderItemDTO> sownOrderItemDTOS = sowManagerBL.findSownOrderItems(sowTaskNo, orgId);
        sowOrderSaveDTO.setSowTaskId(sowTaskDTO.getId());
        sowOrderSaveDTO.setWarehouseId(sowTaskDTO.getWarehouseId());
        sowOrderSaveDTO.setBatchNo(sowTaskDTO.getBatchNo());
        sowOrderSaveDTO.setSownOrderItems(sownOrderItemDTOS);
        sowOrderSaveDTO.setSowTaskItemDTOS(sowTaskItemDTOS);
        sowOrderSaveDTO.setStartTime(new Date());
        sowOrderSaveDTO.setCompleteTime(new Date());
        sowManagerBL.completeSowTaskItems(sowOrderSaveDTO);
    }

    /**
     * 开始播种(知花知果)
     */
    @Override
    public void startSowing(String sowTaskNo, Integer orgId, String sower) {
        AssertUtils.notNull(sowTaskNo, "播种任务编号不能为空");
        AssertUtils.notNull(orgId, "orgId不能为空");
        AssertUtils.notNull(sower, "播种人不能为空");
        sowManagerBL.startSowing(sowTaskNo, orgId, sower);
    }

    /**
     * 指定播种人
     */
    @Override
    public void assignmentSower(UpdateSowerDTO updateSowerDTO) {
        AssertUtils.notNull(updateSowerDTO.getOrgId(), "大区id不能为空");
        AssertUtils.notNull(updateSowerDTO.getSower(), "播种人不能为空");
        AssertUtils.notEmpty(updateSowerDTO.getSowTaskNos(), "播种任务编号不能为空");

        sowManagerBL.assignmentSower(updateSowerDTO);
    }

    @Override
    public void delSowTask(List<String> sowTaskNos) {
        sowManagerBL.delSowTask(sowTaskNos);
    }

    @Override
    public void updateSowLocation(SowTaskDTO sowTaskDTO) {
        sowManagerBL.updateSowLocation(sowTaskDTO);
    }

    @Override
    public void cancelSowTaskByOrder(List<SowTaskCancelDTO> sowTaskCancelDTOS) {
        sowManagerBL.cancelSowTaskByOrder(sowTaskCancelDTOS);
    }

    @Override
    public void completeSowTaskItems(SowOrderSaveDTO sowOrderSaveDTO) {
        AssertUtils.notEmpty(sowOrderSaveDTO.getSowTaskItemDTOS(), "播种明细不能为空");
        sowManagerBL.completeSowTaskItems(sowOrderSaveDTO);
    }

    @Override
    public void updateSowOrderLocation(SowOrderUpdateDTO sowOrderUpdateDTO) {
        AssertUtils.notEmpty(sowOrderUpdateDTO.getOrderItemIds(), "订单信息不能为空");
        sowManagerBL.updateSowOrderLocation(sowOrderUpdateDTO);
    }

    /**
     * 播种任务领取
     */
    @Override
    public void receiveSowTask(SowTaskReceiveDTO sowTaskReceiveDTO) {
        AssertUtils.notNull(sowTaskReceiveDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(sowTaskReceiveDTO.getWarehouseId(), "仓库id不能为空");
        if (StringUtils.isEmpty(sowTaskReceiveDTO.getLocationName())) {
            AssertUtils.notEmpty(sowTaskReceiveDTO.getSowTaskNos(), "播种任务编号不能为空");
        }
        if (CollectionUtils.isEmpty(sowTaskReceiveDTO.getSowTaskNos())) {
            AssertUtils.notNull(sowTaskReceiveDTO.getLocationName(), "集货位不能为空");
        }
        sowManagerBL.receiveSowTask(sowTaskReceiveDTO);
    }

    /**
     * 自提点播种任务完成
     */
    @Override
    public List<Long> completeAddressSowTaskItems(SowOrderSaveDTO sowOrderSaveDTO) {
        AssertUtils.notEmpty(sowOrderSaveDTO.getSowTaskItemDTOS(), "播种明细不能为空");
        return sowManagerBL.completeAddressSowTaskItems(sowOrderSaveDTO);
    }

    @Override
    public List<SowTaskDTO> listSowTaskByBatchNos(List<String> batchNos) {
        return sowManagerBL.listSowTaskByBatchNos(batchNos);
    }

    @Override
    public void sowTaskLack(SowTaskProductLackReviewDTO lackReviewDTO) {
        sowManagerBL.sowTaskLack(lackReviewDTO);
    }

    /**
     * 播种任务完成PDA
     */
    @Override
    public void completeSowTaskItemsPDA(SowOrderSaveDTO sowOrderSaveDTO) {
        AssertUtils.notEmpty(sowOrderSaveDTO.getSowTaskItemDTOS(), "PDA播种明细不能为空");
        sowManagerBL.completeSowTaskItemsPDAWithLock(sowOrderSaveDTO);
    }

    @Override
    public void completeSowTaskByProduct(ProductSowTaskDTO completeDTO) {
        sowManagerBL.completeSowTaskByProduct(completeDTO);
    }

    /**
     * 查询播种任务是否播种复核
     * 
     * @param traceDTOList
     * @return
     */
    @Override
    public List<CheckSowReviewDTO> checkSowReview(List<OrderTraceDTO> traceDTOList) {
        return sowManagerBL.checkSowReview(traceDTOList);
    }

    /**
     * 查询播种明细
     * 
     * @param orgId
     * @param taskIds
     * @return
     */
    @Override
    public List<SowTaskItemDTO> listSowTaskItemBySowTaskIds(Integer orgId, List<Long> taskIds) {
        return sowManagerBL.listSowTaskItemBySowTaskIds(orgId, taskIds);
    }

    @Override
    public SowTaskDTO getSowTask(SowTaskReceiveDTO sowTaskReceiveDTO) {
        return sowManagerBL.getSowTask(sowTaskReceiveDTO);
    }

    @Override
    public void updateSowOrderLocationBySowTaskItemIds(SowOrderUpdateDTO sowOrderUpdateDTO) {
        sowManagerBL.updateSowOrderLocationBySowTaskItemIds(sowOrderUpdateDTO);
    }

    @Override
    public SowTaskRouteAndDriverDTO getRouteAndDriver(SowTaskDTO sowTaskDTO) {
        return sowManagerBL.getRouteAndDriver(sowTaskDTO);
    }

    /**
     * 修复sowOrder上的SowOrderSequence
     *
     * @param dto
     */
    @Override
    public void fixSowOrderSequence(FixSowOrderSequenceDTO dto) {
        fixSowOrderSequenceBL.fixSowOrderSequence(dto);
    }

    @Override
    public void updateSowTaskState(SowOrderSaveDTO sowOrderSaveDTO) {
        sowManagerBL.updateSowTask(sowOrderSaveDTO);
    }

    /**
     * @param orderItemIds
     * @param warehouseId
     * @return
     */
    @Override
    public Map<Long, List<OrderItemDetailAllotDTO>> getOrderItemDetailAllotMap(List<Long> orderItemIds,
        Integer warehouseId) {
        return batchOrderTaskBL.getOrderItemDetailAllotMap(orderItemIds, warehouseId, false);
    }

}
