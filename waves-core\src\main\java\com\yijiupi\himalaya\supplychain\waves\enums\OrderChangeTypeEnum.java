package com.yijiupi.himalaya.supplychain.waves.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单变更类型
 *
 * <AUTHOR>
 * @date 2020-06-09 17:15
 */
public enum OrderChangeTypeEnum {
    /**
     * 订单取消
     */
    订单取消((byte)0),
    /**
     * 订单修改
     */
    订单修改((byte)1),
    /**
     * 部分配送
     */
    部分配送((byte)2),
    /**
     * 配送失败
     */
    配送失败((byte)3);

    /**
     * type
     */
    private Byte type;

    OrderChangeTypeEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     * 
     * @param value
     * @return
     */
    public static String getEnumByValue(Byte value) {
        OrderChangeTypeEnum orderChangeTypeEnum = null;
        if (value != null) {
            orderChangeTypeEnum = cache.get(value);
        }
        return orderChangeTypeEnum == null ? null : orderChangeTypeEnum.name();
    }

    private static Map<Byte, OrderChangeTypeEnum> cache =
        EnumSet.allOf(OrderChangeTypeEnum.class).stream().collect(Collectors.toMap(p -> p.type, p -> p));
}
