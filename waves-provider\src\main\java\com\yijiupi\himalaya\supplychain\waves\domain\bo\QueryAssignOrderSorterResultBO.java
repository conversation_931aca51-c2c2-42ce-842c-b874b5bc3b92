package com.yijiupi.himalaya.supplychain.waves.domain.bo;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 */
public class QueryAssignOrderSorterResultBO {

    private Long taskID;

    private Long orderId;

    private Integer sorterId;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getTaskID() {
        return taskID;
    }

    public void setTaskID(Long taskID) {
        this.taskID = taskID;
    }

    public Integer getSorterId() {
        return sorterId;
    }

    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }
}
