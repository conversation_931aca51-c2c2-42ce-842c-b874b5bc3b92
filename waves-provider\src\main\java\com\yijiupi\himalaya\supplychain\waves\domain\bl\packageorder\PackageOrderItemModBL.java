package com.yijiupi.himalaya.supplychain.waves.domain.bl.packageorder;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yijiupi.himalaya.supplychain.waves.util.PageHelperUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutBoundBatchDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.outboundbatch.BatchQueryOutBoundBatchDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutBoundBatchQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.tms.TmsApiManager;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CountBoundCalculateBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor.CountBoundCalculateBOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.PackageOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.mq.PackageSyncMQ;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.PackageOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.ModPackageInfoByOutBoundInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.ResetPackageOrderItemBeforeOutBoundDTO;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/2/25
 */
@Service
public class PackageOrderItemModBL {

    @Resource
    private PackageOrderItemMapper packageOrderItemMapper;
    @Resource
    private OutStockOrderMapper outStockOrderMapper;
    @Resource
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Resource
    private PackageSyncMQ packageSyncMQ;
    @Resource
    private TmsApiManager tmsApiManager;

    @Reference
    private IOutBoundBatchQueryService iOutBoundBatchQueryService;

    private static final Logger LOGGER = LoggerFactory.getLogger(PackageOrderItemModBL.class);

    /**
     * 出库时如果打包信息有问题，重新计算
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void resetPackageOrderItemBeforeOutBound(ResetPackageOrderItemBeforeOutBoundDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");
        AssertUtils.notNull(dto.getOrgId(), "组织机构信息不能为空！");
        AssertUtils.hasText(dto.getBoundNo(), "出库批次信息不能为空！");

        ModPackageInfoByOutBoundInfoDTO infoDTO = new ModPackageInfoByOutBoundInfoDTO();
        infoDTO.setBoundNos(Collections.singletonList(dto.getBoundNo()));
        infoDTO.setOrgId(dto.getOrgId());
        infoDTO.setWarehouseId(dto.getWarehouseId());
        modLackPackageOrderItemByOutBoundInfo(infoDTO);
    }

    /**
     * 通过车次号修改
     * 
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void modLackPackageOrderItemByOutBoundInfo(ModPackageInfoByOutBoundInfoDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");
        AssertUtils.notNull(dto.getOrgId(), "组织机构信息不能为空！");
        List<OutStockOrderPO> outStockOrderPOList = getOutStockOrderList(dto);

        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }

        List<OutStockOrderItemPO> lackOutStockOrderItemList =
            outStockOrderPOList.stream().flatMap(m -> m.getItems().stream())
                .filter(m -> m.getOriginalUnitTotalCount().compareTo(m.getUnittotalcount()) != 0)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(lackOutStockOrderItemList)) {
            return;
        }
        List<Long> lackOutStockOrderItemIdList =
            lackOutStockOrderItemList.stream().map(OutStockOrderItemPO::getId).distinct().collect(Collectors.toList());
        batchCompleteModLackPackageOrderItem(lackOutStockOrderItemIdList);
    }

    /**
     * 重新同步包装箱信息工具
     *
     * @param dto
     */
    public void syncPackageOrderItemByOutBoundInfo(ModPackageInfoByOutBoundInfoDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");
        AssertUtils.notNull(dto.getOrgId(), "组织机构信息不能为空！");
        List<OutStockOrderPO> outStockOrderPOList = getOutStockOrderList(dto);

        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }
        List<Long> outStockOrderIds =
            outStockOrderPOList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
        List<PackageOrderItemDTO> packageOrderItemDTOList =
            packageOrderItemMapper.listPackageItemsByOrderIds(outStockOrderIds, null);

        Map<String, List<PackageOrderItemDTO>> packageItemGroupByNoMap =
            packageOrderItemDTOList.stream().collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderNo));

        Map<String, OutStockOrderPO> outStockOrderPOMap =
            outStockOrderPOList.stream().collect(Collectors.toMap(OutStockOrderPO::getReforderno, v -> v));

        OutStockOrderPO outStockOrderPO = outStockOrderPOList.get(0);

        sendMod(packageItemGroupByNoMap, outStockOrderPOMap, outStockOrderPO.getWarehouseId());
        sendRemove(packageItemGroupByNoMap, outStockOrderPOMap, outStockOrderPO.getWarehouseId());

    }

    private List<OutStockOrderPO> getOutStockOrderList(ModPackageInfoByOutBoundInfoDTO dto) {
        if (!CollectionUtils.isEmpty(dto.getOutStockOrderIds())) {
            List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findByOrderIds(dto.getOutStockOrderIds());
            return outStockOrderPOList;
        }

        return getOutStockOrderByBoundInfo(dto);
    }

    // 返回有包装箱的出库单，前置仓信息返回中心仓的出库单
    private List<OutStockOrderPO> getOutStockOrderByBoundInfo(ModPackageInfoByOutBoundInfoDTO dto) {
        if (!CollectionUtils.isEmpty(dto.getBoundNos())) {
            List<OutStockOrderPO> outStockOrderPOList = PageHelperUtils.splitPageQuery(dto.getBoundNos(), 1,
                it -> outStockOrderMapper.findByOutBoundNoList(it, dto.getOrgId()));
            return outStockOrderPOList;
        }

        BatchQueryOutBoundBatchDTO batchQueryOutBoundBatchDTO = new BatchQueryOutBoundBatchDTO();
        batchQueryOutBoundBatchDTO.setRelatedBatchIds(dto.getRelatedBatchIds());
        batchQueryOutBoundBatchDTO.setRelatedBatchNos(dto.getRelatedBatchNos());
        batchQueryOutBoundBatchDTO.setPickOrderIds(dto.getPickOrderIds());
        batchQueryOutBoundBatchDTO.setPickOrderNos(dto.getPickOrderNos());
        batchQueryOutBoundBatchDTO.setOrgId(dto.getOrgId());
        batchQueryOutBoundBatchDTO.setWarehouseId(dto.getWarehouseId());
        List<OutBoundBatchDTO> outBoundBatchList =
            iOutBoundBatchQueryService.findOutBoundBatchList(batchQueryOutBoundBatchDTO);
        LOGGER.info("查询出库批次信息为：{}", JSON.toJSONString(outBoundBatchList));
        if (CollectionUtils.isEmpty(outBoundBatchList)) {
            return Collections.emptyList();
        }

        List<String> boundNoList =
            outBoundBatchList.stream().map(OutBoundBatchDTO::getBoundBatchNo).distinct().collect(Collectors.toList());

        List<OutStockOrderPO> simpleOutStockOrderPOList =
            outStockOrderMapper.findSimpleOrderInfoByOutBoundNoList(boundNoList, dto.getWarehouseId());

        if (CollectionUtils.isEmpty(simpleOutStockOrderPOList)) {
            return Collections.emptyList();
        }

        List<String> refOrderNos = simpleOutStockOrderPOList.stream().map(OutStockOrderPO::getReforderno).distinct()
            .collect(Collectors.toList());
        List<Long> outStockOrderIds = packageOrderItemMapper.findRefIdsByOrderNos(null, null, refOrderNos);

        LOGGER.info("查询出库单id信息为：{}", JSON.toJSONString(outStockOrderIds));
        if (CollectionUtils.isEmpty(outStockOrderIds)) {
            return Collections.emptyList();
        }

        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findByOrderIds(outStockOrderIds);
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return Collections.emptyList();
        }

        return outStockOrderPOList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchCompleteModLackPackageOrderItemByBatch(List<String> batchNos) {
        List<Long> outStockOrderItemIds = outStockOrderItemMapper.findLackItemIdByBatchNo(batchNos);
        if (CollectionUtils.isEmpty(outStockOrderItemIds)) {
            return;
        }

        batchCompleteModLackPackageOrderItem(outStockOrderItemIds);
    }

    /**
     * 订单缺货，同步修改缺货的装箱信息，并同步给tms <br />
     * 往多改 和 往少改 <br />
     * 实现：1、波次完成的时候同步箱号，解决拣货中标缺的箱号。 2、供应链的标记，判断波次是否完成，如果没完成就不修改箱号，波次完成了，才修改。
     * 
     * @param lackOrderItemIds <br />
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchCompleteModLackPackageOrderItem(List<Long> lackOrderItemIds) {
        LOGGER.info("缺货修改装箱数据订单项为:{}", JSON.toJSONString(lackOrderItemIds));
        if (CollectionUtils.isEmpty(lackOrderItemIds)) {
            return;
        }

        try {
            List<OutStockOrderItemPO> outStockOrderItemPOList = outStockOrderItemMapper.listByIds(lackOrderItemIds);
            if (CollectionUtils.isEmpty(outStockOrderItemPOList)) {
                return;
            }

            outStockOrderItemPOList = outStockOrderItemPOList.stream()
                .filter(m -> m.getUnittotalcount().compareTo(m.getOriginalUnitTotalCount()) != 0)
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(outStockOrderItemPOList)) {
                return;
            }

            OutStockOrderItemPO order = outStockOrderItemPOList.get(0);
            Integer orgId = order.getOrgId();
            Map<Long, List<PackageOrderItemDTO>> packageMap =
                packageOrderItemMapper.listPackageItemsByItemIds(lackOrderItemIds, orgId).stream()
                    .collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderItemId));
            if (packageMap.isEmpty()) {
                return;
            }
            List<OutStockOrderItemPO> packageItemList = outStockOrderItemPOList.stream()
                .filter(m -> !CollectionUtils.isEmpty(packageMap.get(m.getId()))).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(packageItemList)) {
                return;
            }

            List<PackageOrderItemDTO> updatePackgeItemList = getUpdatePackgeItemList(packageItemList, packageMap);
            List<PackageOrderItemDTO> deletePackageItemList = getDeletePackgeItemList(packageItemList, packageMap);

            if (!CollectionUtils.isEmpty(updatePackgeItemList)) {
                LOGGER.info("更新包装信息数据为:{}", JSON.toJSONString(updatePackgeItemList));
                updatePackgeItemList.forEach(item -> {
                    packageOrderItemMapper.updateBoxCodeById(item);
                });
            }

            if (!CollectionUtils.isEmpty(deletePackageItemList)) {
                LOGGER.info("删除包装信息数据为:{}", JSON.toJSONString(deletePackageItemList));
                deletePackageItemList.forEach(item -> {
                    PackageOrderItemDTO packageOrderItemDTO = new PackageOrderItemDTO();
                    packageOrderItemDTO.setId(item.getId());
                    packageOrderItemDTO.setPackageCount(BigDecimal.ZERO);
                    packageOrderItemDTO.setUnitCount(BigDecimal.ZERO);
                    packageOrderItemDTO.setUnitTotalCount(BigDecimal.ZERO);

                    packageOrderItemMapper.updateBoxCodeById(packageOrderItemDTO);
                });
                // packageOrderItemMapper.batchDelete(ids, orgId);

            }

            List<Long> lackOutStockOrderIds = outStockOrderItemPOList.stream()
                .map(OutStockOrderItemPO::getOutstockorderId).collect(Collectors.toList());
            List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findIdByIds(lackOutStockOrderIds, null);
            if (CollectionUtils.isEmpty(outStockOrderPOList)) {
                return;
            }

            Integer warehouseId = outStockOrderPOList.get(0).getWarehouseId();

            Map<String, OutStockOrderPO> orderPOMap =
                outStockOrderPOList.stream().collect(Collectors.toMap(OutStockOrderPO::getReforderno, v -> v));

            List<PackageOrderItemDTO> packageOrderItemDTOS =
                packageOrderItemMapper.listPackageItemsByOrderIds(lackOutStockOrderIds, null);
            if (CollectionUtils.isEmpty(packageOrderItemDTOS)) {
                sendRemove(Collections.emptyMap(), orderPOMap, warehouseId);
                return;
            }

            Map<String, List<PackageOrderItemDTO>> finalPackageMap =
                packageOrderItemDTOS.stream().collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderNo));
            sendMod(finalPackageMap, orderPOMap, warehouseId);
            sendRemove(finalPackageMap, orderPOMap, warehouseId);
        } catch (Exception e) {
            LOGGER.warn("修改装箱信息失败:" + JSON.toJSONString(lackOrderItemIds), e);
        }
    }

    public void sendModPackage(ModPackageInfoByOutBoundInfoDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");
        AssertUtils.notNull(dto.getOrgId(), "组织机构信息不能为空！");
        List<OutStockOrderPO> outStockOrderPOList = getOutStockOrderList(dto);

        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }
        List<Long> outStockOrderIds =
            outStockOrderPOList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList());
        List<PackageOrderItemDTO> packageOrderItemDTOList =
            packageOrderItemMapper.listPackageItemsByOrderIds(outStockOrderIds, null);

        Map<Long, String> orderBusinessIdMap = outStockOrderPOList.stream()
            .collect(Collectors.toMap(OutStockOrderPO::getId, OutStockOrderPO::getBusinessId));

        List<Long> outStockOrderItemIds = packageOrderItemDTOList.stream().map(PackageOrderItemDTO::getRefOrderItemId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderItemPO> outStockOrderItemPOList = outStockOrderItemMapper.listByIds(outStockOrderItemIds);
        Map<Long, String> itemBusinessIdMap = outStockOrderItemPOList.stream()
            .collect(Collectors.toMap(OutStockOrderItemPO::getId, OutStockOrderItemPO::getBusinessItemId));

        List<PackageOrderItemPO> packageOrderItemPOList = packageOrderItemDTOList.stream().map(packageItem -> {
            PackageOrderItemPO item = new PackageOrderItemPO();
            BeanUtils.copyProperties(packageItem, item);
            item.setId(Long.valueOf(UuidUtil.generatorId()));
            if (item.getCreateTime() == null) {
                item.setCreateTime(new Date());
            }
            String businessId = orderBusinessIdMap.get(packageItem.getRefOrderId());
            String businessItemId = itemBusinessIdMap.get(packageItem.getRefOrderItemId());
            item.setBusinessItemId(businessItemId);
            item.setBusinessId(businessId);
            item.setOrderItemBusinessId(businessItemId);
            item.setOrderBusinessId(businessId);
            return item;
        }).collect(Collectors.toList());

        packageSyncMQ.send(packageOrderItemPOList);

    }

    private void sendMod(Map<String, List<PackageOrderItemDTO>> finalPackageMap,
        Map<String, OutStockOrderPO> orderPOMap, Integer warehouseId) {

        List<PackageOrderItemDTO> packageOrderItemDTOList = orderPOMap.entrySet().stream()
            .filter(entry -> !CollectionUtils.isEmpty(finalPackageMap.get(entry.getKey())))
            .filter(entry -> !isAllLack(finalPackageMap.get(entry.getKey())))
            .flatMap(entry -> finalPackageMap.get(entry.getKey()).stream()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(packageOrderItemDTOList)) {
            return;
        }

        List<Long> outStockOrderIds = packageOrderItemDTOList.stream().map(PackageOrderItemDTO::getRefOrderId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findIdByIds(outStockOrderIds, warehouseId);
        Map<Long, String> orderBusinessIdMap = outStockOrderPOList.stream()
            .collect(Collectors.toMap(OutStockOrderPO::getId, OutStockOrderPO::getBusinessId));

        List<Long> outStockOrderItemIds = packageOrderItemDTOList.stream().map(PackageOrderItemDTO::getRefOrderItemId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderItemPO> outStockOrderItemPOList = outStockOrderItemMapper.listByIds(outStockOrderItemIds);
        Map<Long, String> itemBusinessIdMap = outStockOrderItemPOList.stream()
            .collect(Collectors.toMap(OutStockOrderItemPO::getId, OutStockOrderItemPO::getBusinessItemId));

        List<PackageOrderItemPO> packageOrderItemPOList = packageOrderItemDTOList.stream().map(packageItem -> {
            PackageOrderItemPO item = new PackageOrderItemPO();
            BeanUtils.copyProperties(packageItem, item);
            item.setId(Long.valueOf(UuidUtil.generatorId()));
            if (item.getCreateTime() == null) {
                item.setCreateTime(new Date());
            }
            String businessId = orderBusinessIdMap.get(packageItem.getRefOrderId());
            String businessItemId = itemBusinessIdMap.get(packageItem.getRefOrderItemId());
            item.setBusinessItemId(businessItemId);
            item.setBusinessId(businessId);
            item.setOrderItemBusinessId(businessItemId);
            item.setOrderBusinessId(businessId);
            return item;
        }).collect(Collectors.toList());

        packageSyncMQ.send(packageOrderItemPOList);
    }

    private boolean isAllLack(List<PackageOrderItemDTO> itemList) {
        BigDecimal totalUnitCount =
            itemList.stream().map(PackageOrderItemDTO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);

        return totalUnitCount.compareTo(BigDecimal.ZERO) == 0;
    }

    private void sendRemove(Map<String, List<PackageOrderItemDTO>> finalPackageMap,
        Map<String, OutStockOrderPO> orderPOMap, Integer warehouseId) {
        if (CollectionUtils.isEmpty(finalPackageMap)) {
            tmsApiManager.removePackage(new ArrayList<>(orderPOMap.keySet()), warehouseId);
            return;
        }

        List<String> removeOrderNos = orderPOMap.keySet().stream()
            .filter(outStockOrderPO -> CollectionUtils.isEmpty(finalPackageMap.get(outStockOrderPO))
                || isAllLack(finalPackageMap.get(outStockOrderPO)))
            .collect(Collectors.toList());

        tmsApiManager.removePackage(removeOrderNos, warehouseId);
    }

    public void removePackage(ModPackageInfoByOutBoundInfoDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");
        AssertUtils.notNull(dto.getOrgId(), "组织机构信息不能为空！");
        List<OutStockOrderPO> outStockOrderPOList = getOutStockOrderList(dto);

        List<String> removeOrderNos =
            outStockOrderPOList.stream().map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList());

        tmsApiManager.removePackage(removeOrderNos, dto.getWarehouseId());
    }

    private List<PackageOrderItemDTO> getDeletePackgeItemList(List<OutStockOrderItemPO> packageItemList,
        Map<Long, List<PackageOrderItemDTO>> packageMap) {
        List<PackageOrderItemDTO> deletePackgeItemList = new ArrayList<>();
        for (OutStockOrderItemPO orderItem : packageItemList) {
            List<PackageOrderItemDTO> packageItems = packageMap.get(orderItem.getId());
            if (orderItem.getUnittotalcount().compareTo(BigDecimal.ZERO) == 0) {
                deletePackgeItemList.addAll(packageItems);
            }
        }

        return deletePackgeItemList;
    }

    // 分摊逻辑
    private void processApportionmentQuantity(List<CountBoundCalculateBO> calBOList, BigDecimal unitTotalCount, int i) {
        if (unitTotalCount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        CountBoundCalculateBO countBoundCalculateBO = calBOList.get(i);
        BigDecimal tmpCount = countBoundCalculateBO.getUnitTotalCount().subtract(unitTotalCount);

        if (tmpCount.compareTo(BigDecimal.ZERO) >= 0) {
            countBoundCalculateBO.setUnitTotalCount(countBoundCalculateBO.getUnitTotalCount().subtract(unitTotalCount));
            return;
        }

        BigDecimal nextProcessCount = unitTotalCount.subtract(countBoundCalculateBO.getUnitTotalCount());
        countBoundCalculateBO.setUnitTotalCount(BigDecimal.ZERO);

        processApportionmentQuantity(calBOList, nextProcessCount, i + 1);
    }

    private boolean needDelete(PackageOrderItemDTO packageOrderItemDTO, OutStockOrderItemPO orderItem) {
        if (Objects.isNull(packageOrderItemDTO)) {
            return Boolean.FALSE;
        }
        if (orderItem.getUnittotalcount().compareTo(BigDecimal.ZERO) == 0) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    private List<PackageOrderItemDTO> getUpdatePackgeItemList(List<OutStockOrderItemPO> packageItemList,
        Map<Long, List<PackageOrderItemDTO>> packageMap) {
        List<PackageOrderItemDTO> updatePackgeItemList = new ArrayList<>();
        for (OutStockOrderItemPO orderItem : packageItemList) {
            List<PackageOrderItemDTO> packageItems = packageMap.get(orderItem.getId());
            List<PackageOrderItemDTO> notZeroPackageItemList = getNeedModPackageOrderItem(packageItems, orderItem);
            if (CollectionUtils.isEmpty(notZeroPackageItemList)) {
                continue;
            }
            List<CountBoundCalculateBO> countBoundCalculateBOList =
                CountBoundCalculateBOConvertor.convertPackageToSlitList(notZeroPackageItemList);
            BigDecimal totalPackageUnitCount = notZeroPackageItemList.stream()
                .map(PackageOrderItemDTO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal lackUnitTotalCount = totalPackageUnitCount.subtract(orderItem.getUnittotalcount());
            // 只处理包装箱数据 多于 订单项数据的情况
            if (lackUnitTotalCount.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            processApportionmentQuantity(countBoundCalculateBOList, lackUnitTotalCount, 0);

            List<PackageOrderItemDTO> needUpdateList = CountBoundCalculateBOConvertor
                .convertToPackageOrderList(countBoundCalculateBOList, notZeroPackageItemList);

            if (CollectionUtils.isEmpty(needUpdateList)) {
                continue;
            }

            needUpdateList.forEach(item -> {
                PackageOrderItemDTO updatePackageItem = new PackageOrderItemDTO();
                updatePackageItem.setId(item.getId());
                updatePackageItem.setUnitTotalCount(item.getUnitTotalCount());
                BigDecimal[] count = item.getUnitTotalCount().divideAndRemainder(item.getSpecQuantity());
                updatePackageItem.setPackageCount(count[0]);
                updatePackageItem.setUnitCount(count[1]);
                updatePackgeItemList.add(updatePackageItem);
            });
        }

        return updatePackgeItemList;
    }

    private List<PackageOrderItemDTO> getNeedModPackageOrderItem(List<PackageOrderItemDTO> packageOrderItemDTOList,
        OutStockOrderItemPO outStockOrderItemPO) {
        List<PackageOrderItemDTO> packedItemList = packageOrderItemDTOList.stream()
            .filter(m -> m.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(packedItemList)) {
            return null;
        }

        return packedItemList;
    }

}
