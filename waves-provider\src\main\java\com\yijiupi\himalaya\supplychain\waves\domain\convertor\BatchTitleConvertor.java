package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.Objects;

import org.springframework.stereotype.Component;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.baseutil.NullUtils;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.CarDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.ICarService;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.DeliveryTaskDTO;

/**
 * <AUTHOR>
 * @title: BatchTitleConvertor
 * @description:
 * @date 2022-12-02 14:00
 */
@Component
public class BatchTitleConvertor {

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Reference
    private IOrgService iOrgService;
    @Reference
    private IAdminUserService iAdminUserService;
    @Reference
    private ICarService iCarService;

    /**
     * 通过车次创建波次标题
     */
    public String getBatchTitleByDeliveryTask(DeliveryTaskDTO deliveryTask) {
        String title = getFirstTitleByDeliveryTask(deliveryTask);

        if (deliveryTask.getToWarehouseId() != null) {
            Warehouse warehouse = iWarehouseQueryService.findWarehouseById(deliveryTask.getToWarehouseId());
            if (warehouse != null) {
                OrgDTO city = iOrgService.getOrg(warehouse.getCityId());
                if (city != null) {
                    title = String.format("【%s】%s", city.getOrgName(), warehouse.getName());
                }
            }
        }

        return title;
    }

    /**
     * 获取第一优先级标题
     */
    private String getFirstTitleByDeliveryTask(DeliveryTaskDTO deliveryTask) {
        if (Objects.equals(deliveryTask.getLogistics(), Boolean.TRUE)) {
            return deliveryTask.getDeliveryTaskNo();
        }
        if (NullUtils.isNullLongNum(deliveryTask.getDeliveryCarId())) {
            return "";
        }
        CarDTO carDto = iCarService.findOne(deliveryTask.getDeliveryCarId());
        if (carDto == null) {
            return "";
        }
        String carName = NullUtils.getDefaultStr(carDto.getName());
        if (NullUtils.isNullInteger(deliveryTask.getDeliveryUserId())) {
            return carName;
        }
        AdminUser deliveryUser = iAdminUserService.getAdminUserWithoutAuthById(deliveryTask.getDeliveryUserId());

        if (deliveryUser == null) {
            return carName;
        }
        String deliveryUserName =
            deliveryUser.getTrueName() == null ? deliveryUser.getNickname() : deliveryUser.getTrueName();
        if (StringUtils.isEmpty(deliveryUserName)) {

        }
        return carName.concat("-").concat(deliveryUserName);
    }

}
