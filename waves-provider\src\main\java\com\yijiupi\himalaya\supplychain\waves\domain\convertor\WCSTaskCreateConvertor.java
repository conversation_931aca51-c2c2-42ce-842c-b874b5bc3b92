package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByRefOrderNoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateOrderDTO;
import com.yijiupi.himalaya.supplychain.wcs.dto.task.DPSPickTaskBatchCreateDTO;
import com.yijiupi.himalaya.supplychain.wcs.dto.task.DPSPickTaskCreateDTO;
import com.yijiupi.himalaya.supplychain.wcs.dto.task.DPSPickTaskItemCreateDTO;
import com.yijiupi.himalaya.supplychain.wcs.enums.WCSTaskTypeEnums;

/**
 * <AUTHOR>
 * @title: WPSTaskCreateConvertor
 * @description:
 * @date 2022-10-24 10:46
 */
public class WCSTaskCreateConvertor {

    private static final Logger LOG = LoggerFactory.getLogger(WCSTaskCreateConvertor.class);

    public static DPSPickTaskBatchCreateDTO convert(List<BatchTaskPO> batchTaskList, List<BatchTaskItemPO> itemList,
                                                    Map<Long, LocationReturnDTO> areaMap, List<SowTaskPO> sowTaskList, String optUserName) {
        Map<String, List<BatchTaskItemPO>> itemGroupMap =
            itemList.stream().collect(Collectors.groupingBy(BatchTaskItemPO::getBatchTaskId));

        Map<String, Long> batchTaskSowMap =
            batchTaskList.stream().collect(Collectors.toMap(BatchTaskPO::getId, BatchTaskPO::getSowTaskId));
        Map<Long, SowTaskPO> sowTaskMap = sowTaskList.stream().collect(Collectors.toMap(SowTaskPO::getId, v -> v));

        List<
            DPSPickTaskCreateDTO> dtoList =
                batchTaskList.stream()
                    .map(batchTask -> convert(batchTask, itemGroupMap.get(batchTask.getId()), areaMap,
                        sowTaskMap.get(batchTaskSowMap.get(batchTask.getId())), optUserName))
                    .collect(Collectors.toList());

        DPSPickTaskBatchCreateDTO dpsPickTaskBatchCreateDTO = new DPSPickTaskBatchCreateDTO();
        dpsPickTaskBatchCreateDTO.setDtoList(dtoList);

        return dpsPickTaskBatchCreateDTO;
    }

    private static DPSPickTaskCreateDTO convert(BatchTaskPO batchTaskPO, List<BatchTaskItemPO> itemList,
                                                Map<Long, LocationReturnDTO> areaMap, SowTaskPO sowTask, String optUserName) {
        DPSPickTaskCreateDTO dpsPickTaskCreateDTO = new DPSPickTaskCreateDTO();
        dpsPickTaskCreateDTO.setBatchTaskNo(batchTaskPO.getBatchTaskNo());
        dpsPickTaskCreateDTO.setBatchTaskId(batchTaskPO.getId());
        dpsPickTaskCreateDTO.setTaskType(WCSTaskTypeEnums.机器人分拣.getType());
        // dpsPickTaskCreateDTO.setOptUserId();
        dpsPickTaskCreateDTO.setOrgId(Integer.valueOf(batchTaskPO.getOrgId()));
        dpsPickTaskCreateDTO.setWarehouseId(batchTaskPO.getWarehouseId());
        dpsPickTaskCreateDTO.setOptUserName(optUserName);

        dpsPickTaskCreateDTO.setItemList(convert(itemList, areaMap, sowTask));

        return dpsPickTaskCreateDTO;
    }

    private static List<DPSPickTaskItemCreateDTO> convert(List<BatchTaskItemPO> itemList,
                                                          Map<Long, LocationReturnDTO> areaMap, SowTaskPO sowTask) {
        LOG.info("物料箱信息：{}", JSON.toJSONString(areaMap));
        return itemList.stream().map(item -> {
            LocationReturnDTO vesselDetailsDTO = areaMap.get(item.getLocationId());
            DPSPickTaskItemCreateDTO itemCreateDTO = new DPSPickTaskItemCreateDTO();
            itemCreateDTO.setBatchTaskItemId(item.getId());
            if (Objects.isNull(vesselDetailsDTO)) {
                LOG.info("未找到物料箱信息：{}", item.getSkuId());
                return null;
            }

            itemCreateDTO.setSourceLocationId(vesselDetailsDTO.getArea_Id());
            itemCreateDTO.setSourceLocationName(vesselDetailsDTO.getArea());
            itemCreateDTO.setVesselId(item.getLocationId());
            itemCreateDTO.setVesselNo(item.getLocationName());
            itemCreateDTO.setUnitTotalCount(item.getUnitTotalCount());
            itemCreateDTO.setSpecQuantity(item.getSpecQuantity());
            itemCreateDTO.setPackageName(item.getPackageName());
            itemCreateDTO.setUnitName(item.getUnitName());
            itemCreateDTO.setTargetLocationId(sowTask.getLocationId());
            itemCreateDTO.setTargetLocationName(sowTask.getLocationName());
            itemCreateDTO.setSkuId(item.getSkuId());
            itemCreateDTO.setProductName(item.getProductName());

            return itemCreateDTO;
        }).filter(Objects :: nonNull).collect(Collectors.toList());
    }

    public static List<String> getOrderNoList(BatchCreateByRefOrderNoDTO dto) {
        List<String> refOrderNoList = dto.getRefOrderNos();
        // 内配订单
        if (!org.springframework.util.CollectionUtils.isEmpty(dto.getOrderList())) {
            refOrderNoList = dto.getOrderList().stream().map(BatchCreateOrderDTO::getRefOrderNo)
                    .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        }

        return refOrderNoList;
    }
}
