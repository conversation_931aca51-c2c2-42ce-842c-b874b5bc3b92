package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.dto;

import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2023-12-11 11:49
 **/
public class BatchCompleteEvent extends ApplicationEvent {

    private BatchCompleteEvent(Object source) {
        super(source);
    }

    public static BatchCompleteEvent of(String batchNo) {
        return new BatchCompleteEvent(batchNo);
    }

    @Override
    public String getSource() {
        return (String) source;
    }

}
