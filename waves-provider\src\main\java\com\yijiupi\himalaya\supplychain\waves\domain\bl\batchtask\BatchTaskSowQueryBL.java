package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSpecificationDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductInfoSpecificationSerivce;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductImageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.LocationAreaService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OutStockOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.meituan.MTSaleInventoryBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.wcs.PassageBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;
import com.yijiupi.himalaya.supplychain.waves.util.RedisUtil;
import com.yijiupi.himalaya.supplychain.waves.utils.DateUtil;
import com.yijiupi.himalaya.supplychain.waves.utils.StreamUtils;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;
import com.yijiupi.supplychain.serviceutils.constant.redis.RedisConstant;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/12/15
 */
@Service
public class BatchTaskSowQueryBL {

    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private OutStockOrderBL outStockOrderBL;
    @Autowired
    private PassageBL passageBL;
    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;
    @Autowired
    private RedisUtil<OutStockOrderLocationDTO> redisUtil;
    @Resource
    private MTSaleInventoryBL mtSaleInventoryBL;

    @Reference
    private IProductSkuService iProductSkuService;
    @Reference
    private IProductSkuQueryService iProductSkuQueryService;
    @Reference
    private IProductInfoSpecificationSerivce iProductInfoSpecificationSerivce;
    @Reference
    private IProductLocationService iProductLocationService;
    @Reference
    private LocationAreaService locationAreaService;

    private static final long LONG_TIMEOUT = 8 * 60 * 1000;

    private static final long SHORT_TIMEOUT = 3 * 60 * 1000;

    public static final ExecutorService EXECUTOR =
        new ThreadPoolExecutor(30, 80, 200L, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(),
            new DefaultThreadFactory("batchSyncTask"), new ThreadPoolExecutor.CallerRunsPolicy());

    private static final Logger LOGGER = LoggerFactory.getLogger(BatchTaskSowQueryBL.class);

    public List<BatchTaskDTO> findBatchTaskListBySowTaskNos(List<String> sowTaskNos, List<Integer> taskStates,
        Integer cityId) {
        try {
            return findBatchTaskList(sowTaskNos, taskStates, cityId);
        } catch (Exception e) {
            LOGGER.warn("查询发生异常，入参:" + JSON.toJSONString(sowTaskNos), e);
            return Collections.emptyList();
        }
    }

    /**
     * @see BatchOrderTaskBL#listBatchTaskBySowTaskNos
     */
    private List<BatchTaskDTO> findBatchTaskList(List<String> sowTaskNos, List<Integer> taskStates, Integer cityId)
        throws ExecutionException, InterruptedException {
        StopWatch stopWatch = new StopWatch("查询播种任务");
        stopWatch.start("查询拣货任务");
        // 通过播种任务找到拣货任务
        List<BatchTaskPO> batchTaskPOS = batchTaskMapper.listBatchTaskRelatedBySowNos(sowTaskNos, cityId);
        stopWatch.stop();
        if (CollectionUtils.isEmpty(batchTaskPOS)) {
            return null;
        }
        Integer warehouseId = batchTaskPOS.get(0).getWarehouseId();
        // 通过拣货任务编号找到拣货任务详情信息
        stopWatch.start("查询拣货任务明细");
        List<BatchTaskItemDTO> batchTaskItemDTOS = batchTaskItemMapper.findBatchTaskItemDtoListByNo(
            batchTaskPOS.stream().map(BatchTaskPO::getBatchTaskNo).collect(Collectors.toList()));
        stopWatch.stop();
        List<BatchTaskItemDTO> batchTaskItemList = filterItemByState(taskStates, batchTaskItemDTOS);
        if (CollectionUtils.isEmpty(batchTaskItemList)) {
            LOGGER.info("未找到状态符合{}的拣货任务详情,播种任务编号:{}", JSON.toJSONString(taskStates), JSON.toJSONString(sowTaskNos));
            return Collections.emptyList();
        }
        // 为波次任务详情添加产品条码信息
        // 获取符合状态的拣货任务
        List<String> matchStateBatchNos =
            batchTaskItemList.stream().map(BatchTaskItemDTO::getBatchTaskNo).collect(Collectors.toList());
        batchTaskPOS =
            batchTaskPOS.stream().filter(batchTaskPO -> matchStateBatchNos.contains(batchTaskPO.getBatchTaskNo()))
                .collect(Collectors.toList());
        stopWatch.start("查询出库单明细");
        // 通过播种任务编号查找出库单信息
        List<OutStockOrderItemDTO> outStockOrderItems = outStockOrderItemMapper.findBySowTaskNos(sowTaskNos, cityId);
        stopWatch.stop();
        Set<Long> skuIds = outStockOrderItems.stream().map(OutStockOrderItemDTO::getSkuId).collect(Collectors.toSet());
        // 查询订单默认出库位
        CompletableFuture<Map<Long, OutStockOrderLocationDTO>> outStockOrderLocationMapFuture =
                supplyAsync(() -> getDefaultLocation(outStockOrderItems), EXECUTOR, Collections.emptyMap(), "查询出库位异常");
        // 查找拣货任务详情中商品的条码集合
        CompletableFuture<Map<Long, ProductCodeDTO>> productCodeInfoTask =
            supplyAsync(() -> iProductSkuService.getPackageAndUnitCode(skuIds, cityId), EXECUTOR,
                Collections.emptyMap(), "iProductSkuService.getPackageAndUnitCode");
        CompletableFuture<Map<Long, ProductImageDTO>> productImageUrlTask =
            supplyAsync(() -> iProductSkuQueryService.getProductImageUrl(new ArrayList<>(skuIds)), EXECUTOR,
                Collections.emptyMap(), "iProductSkuQueryService.getProductImageUrl");
        CompletableFuture<List<ProductSkuDTO>> productCharacteristicTask =
            supplyAsync(() -> iProductSkuService.getProductCharacteristic(skuIds, warehouseId), EXECUTOR,
                Collections.emptyList(), "iProductSkuService.getProductCharacteristic");
        CompletableFuture<Map<Long, ProductSpecificationDTO>> productSpecTask = supplyAsync(
            () -> iProductInfoSpecificationSerivce.findByProductSkuIds(new ArrayList<>(skuIds), warehouseId), EXECUTOR,
            Collections.emptyMap(), "iProductInfoSpecificationService.findByProductSkuIds");
        // 排序
        stopWatch.start("排序");
        batchTaskItemList = batchOrderTaskBL.processTaskItemIndex(batchTaskItemList);
        stopWatch.stop();
        // 阻塞等待异步任务执行结束
        CompletableFuture.allOf(productCodeInfoTask, productImageUrlTask, productCharacteristicTask, productSpecTask).get();
        // 查询已拣货没货位的产品关联货位信息
        Map<Long, List<LoactionDTO>> skuLocationMap = getSkuLocationMap(batchTaskItemList, warehouseId);
        Map<Long, ProductCodeDTO> packageAndUnitCode = productCodeInfoTask.get();
        Map<Long, ProductImageDTO> productImageUrl = productImageUrlTask.get();
        List<ProductSkuDTO> productCharacteristic = productCharacteristicTask.get();
        Map<Long, ProductSpecificationDTO> productSpecificationMap = productSpecTask.get();
        // 初始化拣货任务明细信息
        initBatchTaskItemInfo(batchTaskItemList, packageAndUnitCode, productImageUrl, skuLocationMap);
        // 初始化条码，出库位等信息
        initOutStockOrderItemInfo(outStockOrderItems, packageAndUnitCode, productImageUrl, productSpecificationMap);
        // 根据ToLocationId查询货位信息赋值货位顺序
        setOutStockOrderItemSequence(outStockOrderItems);
        setOutLocationInfo(outStockOrderItems, outStockOrderLocationMapFuture.get());
        // 封装拣货任务dto，加入拣货任务详情信息
        List<BatchTaskDTO> batchTaskDTOS =
                convertBatchTaskDTOList(batchTaskPOS, batchTaskItemList, outStockOrderItems, productCharacteristic);
        stopWatch.start("通道信息");
        // 填充通道编码
        passageBL.fillPassageCode(batchTaskDTOS);
        stopWatch.stop();
        stopWatch.start("填充销售库存");
        mtSaleInventoryBL.fillSaleInventory(batchTaskDTOS, warehouseId, cityId);
        stopWatch.stop();
        LOGGER.info("查询播种任务信息，入参为:{} ; 时间为:{}", JSON.toJSONString(sowTaskNos), stopWatch.prettyPrint());
        return batchTaskDTOS;
    }

    public static <U> CompletableFuture<U> supplyAsync(Supplier<U> supplier, Executor executor, U result, String info) {
        return CompletableFuture.supplyAsync(supplier, executor).exceptionally(ex -> {
            LOGGER.warn("查询接口" + info + "异常", ex);
            return result;
        });
    }

    private List<BatchTaskDTO> convertBatchTaskDTOList(List<BatchTaskPO> batchTaskPOS,
        List<BatchTaskItemDTO> batchTaskItemList, List<OutStockOrderItemDTO> outStockOrderItemDTOS,
        List<ProductSkuDTO> productCharacteristic) {
        List<BatchTaskDTO> batchTaskDTOS = new ArrayList<>();

        Map<String, List<BatchTaskItemDTO>> batchTaskItemGroupMap =
            batchTaskItemList.stream().collect(Collectors.groupingBy(BatchTaskItemDTO::getBatchTaskNo));
        Map<String, List<OutStockOrderItemDTO>> orderItemsGroupMap =
            outStockOrderItemDTOS.stream().collect(Collectors.groupingBy(OutStockOrderItemDTO::getBatchTaskNo));

        Map<Long, ProductSkuDTO> productCharacteristicMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productCharacteristic)) {
            productCharacteristicMap.putAll(productCharacteristic.stream()
                .collect(Collectors.toMap(ProductSkuDTO::getProductSkuId, v -> v, (v1, v2) -> v1)));
        }

        for (BatchTaskPO batchTaskPO : batchTaskPOS) {
            BatchTaskDTO batchTaskDTO = new BatchTaskDTO();
            BeanUtils.copyProperties(batchTaskPO, batchTaskDTO);
            String batchTaskNo = batchTaskDTO.getBatchTaskNo();
            Byte taskState = batchTaskDTO.getTaskState();
            List<BatchTaskItemDTO> batchTaskItems = batchTaskItemGroupMap.get(batchTaskNo);
            List<OutStockOrderItemDTO> orderItems = orderItemsGroupMap.get(batchTaskNo);
            orderItems.forEach(orderItem -> {
                orderItem.setSorter(batchTaskPO.getCompleteUser());
                orderItem.setSorterId(batchTaskPO.getCompleteUserId());
                orderItem.setTaskState(taskState);
            });
            orderItems.sort(Comparator.comparing(OutStockOrderItemDTO::getCreatetime));
            LOGGER.info("productCharacteristic={}", JSON.toJSONString(productCharacteristic));
            batchTaskItems.forEach(taskItem -> {
                OutStockOrderItemDTO firstOrdItem = orderItems.stream()
                    .filter(ordItem -> Objects.equals(ordItem.getBatchTaskItemId(), taskItem.getId())).findFirst()
                    .orElse(null);
                ProductSkuDTO productSkuDTO =
                    productCharacteristicMap.getOrDefault(taskItem.getSkuId(), new ProductSkuDTO());
                taskItem.setSkuProperty(String.valueOf(productSkuDTO.getProductFeature()));
                if (firstOrdItem != null) {
                    taskItem.setSowTaskItemState(firstOrdItem.getSowTaskItemState());
                }
            });
            batchTaskDTO.setBatchTaskItemList(batchTaskItems);
            batchTaskDTO.setOutStockOrderItemList(orderItems);
            batchTaskDTO.setCreateTime(DateUtil.format(batchTaskPO.getCreateTime(), DateUtil.YYYYMMDD_HHMMSS));
            batchTaskDTOS.add(batchTaskDTO);
        }

        return batchTaskDTOS;
    }

    private void setOutStockOrderItemSequence(List<OutStockOrderItemDTO> outStockOrderItemDTOS) {
        // 根据ToLocationId查询货位信息赋值货位顺序
        List<String> locationIds = outStockOrderItemDTOS.stream().filter(p -> p.getToLocationId() != null)
            .map(p -> p.getToLocationId().toString()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationIds)) {
            return;
        }
        List<LocationReturnDTO> locationList = locationAreaService.findLocationListById(locationIds);
        if (CollectionUtils.isEmpty(locationList)) {
            return;
        }
        locationList.forEach(l -> {
            List<OutStockOrderItemDTO> outStockOrderItem = outStockOrderItemDTOS.stream()
                .filter(p -> p.getToLocationId() != null && p.getToLocationId().compareTo(l.getId()) == 0)
                .collect(Collectors.toList());
            outStockOrderItem.forEach(item -> {
                item.setLocationSequence(l.getSequence());
            });
        });
    }

    private Map<Long, List<LoactionDTO>> getSkuLocationMap(List<BatchTaskItemDTO> batchTaskItemList,
        Integer warehouseId) {
        // 查询已拣货没货位的产品关联货位信息
        List<Long> noLocationSkuIds = batchTaskItemList.stream()
            .filter(item -> item.getTaskState() == TaskStateEnum.已完成.getType() && item.getLocationId() == null)
            .map(BatchTaskItemDTO::getSkuId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noLocationSkuIds)) {
            return iProductLocationService.findLocationDTOBySkuId(warehouseId, noLocationSkuIds);
        }

        return Collections.emptyMap();
    }

    private Map<Long, OutStockOrderLocationDTO> getDefaultLocation(List<OutStockOrderItemDTO> outStockOrderItemDTOS) {
        StopWatch stopWatch = new StopWatch("查询出库位信息");
        stopWatch.start("查询出库位信息");
        // 查询订单默认出库位
        List<OutStockOrderLocationDTO> outStockOrderLocationDTOS = new ArrayList<>();
        outStockOrderItemDTOS.stream().collect(Collectors.groupingBy(OutStockOrderItemDTO::getOutstockorder_Id))
            .forEach((orderId, items) -> {
                OutStockOrderItemDTO item = items.get(0);
                OutStockOrderLocationDTO outStockOrderLocationDTO = new OutStockOrderLocationDTO();
                outStockOrderLocationDTO.setWarehouseId(item.getWarehouseId());
                String businessId = item.getBusinessId();
                outStockOrderLocationDTO
                    .setOmsOrderId(StreamUtils.isNum(businessId) ? Long.valueOf(businessId) : orderId);

                if (needFindOutLocation(item)) {
                    outStockOrderLocationDTOS.add(outStockOrderLocationDTO);
                }
            });

        if (CollectionUtils.isEmpty(outStockOrderLocationDTOS)) {
            return Collections.emptyMap();
        }

        Integer warehouseId = outStockOrderLocationDTOS.get(0).getWarehouseId();
        // List<OutStockOrderLocationDTO> outStockOrderLocationDTOList
        // = outStockOrderBL.findOutStockOrderLocation(outStockOrderLocationDTOS);

        List<OutStockOrderLocationDTO> outStockOrderLocationDTOList =
            findOutStockOrderLocation(outStockOrderLocationDTOS, warehouseId);

        Map<Long, OutStockOrderLocationDTO> result = outStockOrderLocationDTOList.stream().collect(
            Collectors.toMap(OutStockOrderLocationDTO::getOmsOrderId, Function.identity(), (key1, key2) -> key2));
        stopWatch.stop();
        LOGGER.info("查询出库位耗时:{}", stopWatch.prettyPrint());
        return result;
    }

    private List<OutStockOrderLocationDTO>
        findOutStockOrderLocation(List<OutStockOrderLocationDTO> outStockOrderLocationDTOS, Integer warehouseId) {
        List<OutStockOrderLocationDTO> existOutLocationList =
            getOutStockLocationFromCache(outStockOrderLocationDTOS, warehouseId);
        Set<Long> existOmsOrderIds =
            existOutLocationList.stream().map(OutStockOrderLocationDTO::getOmsOrderId).collect(Collectors.toSet());
        Set<Long> totalOmsOrderIds =
            outStockOrderLocationDTOS.stream().map(OutStockOrderLocationDTO::getOmsOrderId).collect(Collectors.toSet());

        Set<Long> notExistOmsOrderIds = Sets.difference(totalOmsOrderIds, existOmsOrderIds);
        if (CollectionUtils.isEmpty(notExistOmsOrderIds)) {
            return existOutLocationList;
        }

        Map<Long, Long> notExistOmsOrderIdMap = notExistOmsOrderIds.stream().collect(Collectors.toMap(k -> k, v -> v));
        List<OutStockOrderLocationDTO> notExistOmsOrderOrderLocation = outStockOrderLocationDTOS.stream()
            .filter(m -> Objects.nonNull(notExistOmsOrderIdMap.get(m.getOmsOrderId()))).collect(Collectors.toList());

        List<OutStockOrderLocationDTO> lastOmsOrderOrderLocation =
            getOutStockOrderLocationDTOAsync(notExistOmsOrderOrderLocation);
        if (CollectionUtils.isEmpty(lastOmsOrderOrderLocation)) {
            return existOutLocationList;
        }

        // 缓存
        cache(lastOmsOrderOrderLocation, warehouseId);

        if (CollectionUtils.isNotEmpty(existOutLocationList)) {
            lastOmsOrderOrderLocation.addAll(existOutLocationList);
        }

        return lastOmsOrderOrderLocation;
    }

    private List<OutStockOrderLocationDTO>
        getOutStockOrderLocationDTOAsync(List<OutStockOrderLocationDTO> notExistOmsOrderOrderLocation) {
        if (notExistOmsOrderOrderLocation.size() >= 800) {
            List<CompletableFuture<List<OutStockOrderLocationDTO>>> completableFutureList = new ArrayList<>();
            List<List<OutStockOrderLocationDTO>> subLists = Lists.partition(notExistOmsOrderOrderLocation, 3);
            for (List<OutStockOrderLocationDTO> lists : subLists) {
                CompletableFuture<List<OutStockOrderLocationDTO>> completableFuture = supplyAsync(
                    () -> outStockOrderBL.findOutStockOrderLocation(lists), EXECUTOR, Collections.emptyList(), "");
                completableFutureList.add(completableFuture);
            }

            List<OutStockOrderLocationDTO> result = new ArrayList<>();
            for (CompletableFuture<List<OutStockOrderLocationDTO>> completableFuture : completableFutureList) {
                try {
                    result.addAll(completableFuture.get());
                } catch (Exception e) {
                    LOGGER.warn("查询失败,入参:" + JSON.toJSONString(notExistOmsOrderOrderLocation), e);
                }
            }

            return result;
        }

        return outStockOrderBL.findOutStockOrderLocation(notExistOmsOrderOrderLocation);
    }

    private List<OutStockOrderLocationDTO>
        getOutStockLocationFromCache(List<OutStockOrderLocationDTO> outStockOrderLocationDTOS, Integer warehouseId) {
        List<OutStockOrderLocationDTO> existOutLocationList =
            outStockOrderLocationDTOS.stream().map(OutStockOrderLocationDTO::getOmsOrderId).distinct()
                .map(omsOrderId -> redisUtil.get(getKey(omsOrderId.toString(), warehouseId))).filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(existOutLocationList)) {
            return Collections.emptyList();
        }
        return existOutLocationList;
    }

    private void cache(List<OutStockOrderLocationDTO> locationDTOS, Integer warehouseId) {
        if (CollectionUtils.isEmpty(locationDTOS)) {
            return;
        }
        locationDTOS.stream().filter(m -> Objects.nonNull(m.getLocationId())).forEach(m -> {
            redisUtil.put(getKey(m.getOmsOrderId().toString(), warehouseId), m, LONG_TIMEOUT, TimeUnit.MILLISECONDS);
        });

        locationDTOS.stream().filter(m -> Objects.isNull(m.getLocationId())).forEach(m -> {
            redisUtil.put(getKey(m.getOmsOrderId().toString(), warehouseId), m, SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
        });
    }

    private String getKey(String omsOrderId, Integer warehouseId) {
        return RedisConstant.SUP_C.concat("SowOutLocation").concat(omsOrderId).concat(":")
            .concat(warehouseId.toString());
    }

    private boolean needFindOutLocation(OutStockOrderItemDTO itemDTO) {
        try {
            if (OrderConstant.ALLOT_TYPE_ALLOCATION.equals(itemDTO.getAllotType())) {
                return Boolean.TRUE;
            }
            if (Objects.nonNull(itemDTO.getOutBoundType())
                && OutBoundTypeEnum.INTERNAL_DELIVERY_ORDER.getCode().byteValue() == itemDTO.getOutBoundType()) {
                return Boolean.TRUE;
            }
            if (Objects.nonNull(itemDTO.getOutBoundType())
                && OutBoundTypeEnum.ALLOT_ORDER.getCode().byteValue() == itemDTO.getOutBoundType()) {
                return Boolean.TRUE;
            }

            if (Objects.nonNull(itemDTO.getLocationId())) {
                return Boolean.FALSE;
            }
        } catch (Exception e) {
            LOGGER.warn("设置是否内配出错，" + JSON.toJSONString(itemDTO), e);
        }

        return Boolean.TRUE;
    }

    private void initBatchTaskItemInfo(List<BatchTaskItemDTO> batchTaskItemList,
        Map<Long, ProductCodeDTO> packageAndUnitCode, Map<Long, ProductImageDTO> productImageUrl,
        Map<Long, List<LoactionDTO>> finalSkuLocationMap) {
        batchTaskItemList.forEach(item -> {
            ProductCodeDTO productCodeDTO = packageAndUnitCode.get(item.getSkuId());
            ProductImageDTO productImageDTO = productImageUrl.get(item.getSkuId());
            if (null != productCodeDTO) {
                item.setPackageCode(productCodeDTO.getPackageCode());
                item.setUnitCode(productCodeDTO.getUnitCode());
            }
            if (item.getLocationId() == null && CollectionUtils.isNotEmpty(finalSkuLocationMap.get(item.getSkuId()))) {
                LoactionDTO loactionDTO = finalSkuLocationMap.get(item.getSkuId()).get(0);
                item.setLocationId(loactionDTO.getId());
                item.setLocationName(loactionDTO.getName());
            }
            if (productImageDTO != null) {
                item.setDefaultImageFile(productImageDTO.getDefaultImageFile());
                item.setImageFiles(productImageDTO.getImageFiles());
            }
        });
    }

    private void initOutStockOrderItemInfo(List<OutStockOrderItemDTO> outStockOrderItemDTOS,
        Map<Long, ProductCodeDTO> packageAndUnitCode, Map<Long, ProductImageDTO> productImageUrl,
        Map<Long, ProductSpecificationDTO> productSpecificationMap) {

        for (OutStockOrderItemDTO item : outStockOrderItemDTOS) {
            ProductCodeDTO productCodeDTO = packageAndUnitCode.get(item.getSkuId());
            ProductImageDTO productImageDTO = productImageUrl.get(item.getSkuId());
            ProductSpecificationDTO productSpecificationDTO = productSpecificationMap.get(item.getSkuId());
            if (Objects.nonNull(productCodeDTO)) {
                item.setPackageCode(productCodeDTO.getPackageCode());
                item.setUnitCode(productCodeDTO.getUnitCode());
            }
            if (Objects.nonNull(productImageDTO)) {
                item.setDefaultImageFile(productImageDTO.getDefaultImageFile());
                item.setImageFiles(productImageDTO.getImageFiles());
            }
            if (Objects.nonNull(productSpecificationDTO)) {
                item.setLength(productSpecificationDTO.getLength());
                item.setHeight(productSpecificationDTO.getHeight());
                item.setWidth(productSpecificationDTO.getWidth());
                item.setProductFeature(productSpecificationDTO.getProductFeature());
            }
        }

    }

    private void setOutLocationInfo(List<OutStockOrderItemDTO> outStockOrderItemDTOS,
        Map<Long, OutStockOrderLocationDTO> outStockOrderLocationMap) {
        for (OutStockOrderItemDTO item : outStockOrderItemDTOS) {
            OutStockOrderLocationDTO outStockOrderLocationDTO =
                outStockOrderLocationMap.get(StreamUtils.isNum(item.getBusinessId())
                    ? Long.valueOf(item.getBusinessId()) : item.getOutstockorder_Id());
            if (Objects.nonNull(outStockOrderLocationDTO)) {
                if (Objects.nonNull(item.getLocationId())) {
                    item.setLocationId(outStockOrderLocationDTO.getLocationId());
                    item.setLocationName(outStockOrderLocationDTO.getLocationName());
                }
                if (Objects.nonNull(item.getToLocationId())) {
                    item.setToCity(outStockOrderLocationDTO.getToCity());
                    item.setToWarehouseName(outStockOrderLocationDTO.getToWarehouseName());
                    item.setToLocationId(outStockOrderLocationDTO.getToLocationId());
                    item.setToLocationName(outStockOrderLocationDTO.getToLocationName());
                }
            }
        }
    }

    private List<BatchTaskItemDTO> filterItemByState(List<Integer> taskStates,
        List<BatchTaskItemDTO> batchTaskItemDTOS) {
        if (CollectionUtils.isEmpty(taskStates)) {
            return batchTaskItemDTOS;
        }

        // 查出符合状态的拣货任务详情
        List<Byte> taskStateList =
            taskStates.stream().map(integer -> Byte.valueOf(integer.toString())).collect(Collectors.toList());
        List<BatchTaskItemDTO> batchTaskItemList = batchTaskItemDTOS.stream()
            .filter(batchTaskItemDTO -> taskStateList.contains(batchTaskItemDTO.getTaskState()))
            .collect(Collectors.toList());
        LOGGER.info("状态符合{}的拣货任务详情是: {}", JSON.toJSONString(taskStateList), JSON.toJSONString(batchTaskItemList));

        return batchTaskItemList;
    }

}
