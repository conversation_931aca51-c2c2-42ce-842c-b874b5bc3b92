package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 扫单复核
 * 
 * <AUTHOR>
 * @date 2024/12/18
 */
public class ScanOrderForReviewDTO implements Serializable {
    /**
     * 扫描订单的信息
     */
    private ScanOrderForReviewOrderInfoDTO orderDTO;
    /**
     * 同客户已拣货酒饮订单的信息
     */
    private List<ScanOrderForReviewWineOrderInfoDTO> wineOrderDTOList;
    /**
     * 同客户未拣货的酒饮订单列表
     */
    private List<ScanOrderForReviewWineOrderInfoDTO> notPickWineOrderDTOList;
    /**
     * 休食对应出库位信息
     */
    private List<ScanOrderForReviewLocationInfoDTO> orderLocationList;

    /**
     * 获取 扫描订单的信息
     *
     * @return orderDTO 扫描订单的信息
     */
    public ScanOrderForReviewOrderInfoDTO getOrderDTO() {
        return this.orderDTO;
    }

    /**
     * 设置 扫描订单的信息
     *
     * @param orderDTO 扫描订单的信息
     */
    public void setOrderDTO(ScanOrderForReviewOrderInfoDTO orderDTO) {
        this.orderDTO = orderDTO;
    }

    /**
     * 获取 同客户酒饮订单的信息
     *
     * @return wineOrderDTOList 同客户酒饮订单的信息
     */
    public List<ScanOrderForReviewWineOrderInfoDTO> getWineOrderDTOList() {
        return this.wineOrderDTOList;
    }

    /**
     * 设置 同客户酒饮订单的信息
     *
     * @param wineOrderDTOList 同客户酒饮订单的信息
     */
    public void setWineOrderDTOList(List<ScanOrderForReviewWineOrderInfoDTO> wineOrderDTOList) {
        this.wineOrderDTOList = wineOrderDTOList;
    }

    /**
     * 获取 未拣货的酒饮订单列表
     *
     * @return notPickWineOrderDTOList 未拣货的酒饮订单列表
     */
    public List<ScanOrderForReviewWineOrderInfoDTO> getNotPickWineOrderDTOList() {
        return this.notPickWineOrderDTOList;
    }

    /**
     * 设置 未拣货的酒饮订单列表
     *
     * @param notPickWineOrderDTOList 未拣货的酒饮订单列表
     */
    public void setNotPickWineOrderDTOList(List<ScanOrderForReviewWineOrderInfoDTO> notPickWineOrderDTOList) {
        this.notPickWineOrderDTOList = notPickWineOrderDTOList;
    }

    /**
     * 获取 休食对应出库位信息
     *
     * @return orderLocationList 休食对应出库位信息
     */
    public List<ScanOrderForReviewLocationInfoDTO> getOrderLocationList() {
        return this.orderLocationList;
    }

    /**
     * 设置 休食对应出库位信息
     *
     * @param orderLocationList 休食对应出库位信息
     */
    public void setOrderLocationList(List<ScanOrderForReviewLocationInfoDTO> orderLocationList) {
        this.orderLocationList = orderLocationList;
    }
}
