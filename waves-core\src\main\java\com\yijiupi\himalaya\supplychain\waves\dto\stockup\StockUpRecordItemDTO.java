package com.yijiupi.himalaya.supplychain.waves.dto.stockup;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-10-11 15:00
 **/
public class StockUpRecordItemDTO implements Serializable {
    /**
     * 城市 id
     */
    private Integer orgId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品 SkuId
     */
    private Long skuId;

    /**
     * 产品条码
     */
    private List<String> code;

    /**
     * 任务状态
     */
    private Byte taskState;

    /**
     * 拣货员名称
     */
    private String pickupUser;

    /**
     * 拣货完成时间
     */
    private Date completeTime;

    /**
     * 大件数量
     */
    private BigDecimal packageCount;

    /**
     * 小件数量
     */
    private BigDecimal unitCount;

    /**
     * 创建时间
     */
    private Date createTime;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public List<String> getCode() {
        return code;
    }

    public void setCode(List<String> code) {
        this.code = code;
    }

    public Byte getTaskState() {
        return taskState;
    }

    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    public String getPickupUser() {
        return pickupUser;
    }

    public void setPickupUser(String pickupUser) {
        this.pickupUser = pickupUser;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
