package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderPalletInfoDTO;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/19
 */
public class BatchTaskCompleteDTO implements Serializable {
    /**
     * 拣货任务id
     */
    private String batchTaskId;
    /**
     * 操作人
     */
    private Integer optUserId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 订单托盘信息集合
     */
    private List<OrderPalletInfoDTO> orderPalletInfoDTOS;
    /**
     * 来源端：PDA，控制器
     */
    private Byte operateSource;

    /**
     * 获取 拣货任务id
     *
     * @return batchTaskId 拣货任务id
     */
    public String getBatchTaskId() {
        return this.batchTaskId;
    }

    /**
     * 设置 拣货任务id
     *
     * @param batchTaskId 拣货任务id
     */
    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    /**
     * 获取 操作人
     *
     * @return optUserId 操作人
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人
     *
     * @param optUserId 操作人
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }

    public List<OrderPalletInfoDTO> getOrderPalletInfoDTOS() {
        return orderPalletInfoDTOS;
    }

    public void setOrderPalletInfoDTOS(List<OrderPalletInfoDTO> orderPalletInfoDTOS) {
        this.orderPalletInfoDTOS = orderPalletInfoDTOS;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public static final Byte SOURCE_PDA = 1;

    public static final Byte SOURCE_DIGITAl = 2;

    /**
     * 获取：来源端：PDA，控制器
     *
     * @return operateSource
     */
    public Byte getOperateSource() {
        return this.operateSource;
    }

    /**
     * 设置：来源端：PDA，控制器
     *
     * @param operateSource
     */
    public void setOperateSource(Byte operateSource) {
        this.operateSource = operateSource;
    }
}
