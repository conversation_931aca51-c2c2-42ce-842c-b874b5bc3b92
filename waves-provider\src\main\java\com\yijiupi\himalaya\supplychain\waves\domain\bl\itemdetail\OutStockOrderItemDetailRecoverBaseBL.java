package com.yijiupi.himalaya.supplychain.waves.domain.bl.itemdetail;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.secowner.OrderWithItemOwnersDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.secowner.OrderWithItemOwnersItemDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.secowner.OrderWithItemOwnersItemDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.ordercenter.OrderCenterQueryBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.OutStockOrderItemDetailRecoverBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.OutStockOrderItemDetailRecoverHandleByItemBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.OutStockOrderItemDetailConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/27
 */
public abstract class OutStockOrderItemDetailRecoverBaseBL {

    @Autowired
    protected OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private OrderCenterQueryBL orderCenterQueryBL;
    protected static final Logger LOGGER = LoggerFactory.getLogger(OutStockOrderItemDetailRecoverBaseBL.class);

    /**
     * 重新构造bo
     * 
     * @param bo
     * @return
     */
    public List<OutStockOrderItemDetailRecoverHandleByItemBO>
        filterAndCreateOrderList(OutStockOrderItemDetailRecoverBO bo) {
        List<OutStockOrderPO> orderList = bo.getOutStockOrderPOS();

        List<OutStockOrderPO> filterOrderList = doFilterOrderList(bo);

        if (CollectionUtils.isEmpty(filterOrderList)) {
            return Collections.emptyList();
        }

        List<OutStockOrderItemPO> itemList =
            filterOrderList.stream().flatMap(m -> m.getItems().stream()).collect(Collectors.toList());

        Map<Long, List<OutStockOrderItemDetailPO>> detailGroupMap = itemList.stream()
            .filter(item -> CollectionUtils.isNotEmpty(item.getItemDetails())).flatMap(m -> m.getItemDetails().stream())
            .collect(Collectors.groupingBy(OutStockOrderItemDetailPO::getOutStockOrderItemId));

        List<OutStockOrderItemPO> needUpdateItemList = itemList.stream().filter(item -> {
            List<OutStockOrderItemDetailPO> detailList =
                detailGroupMap.getOrDefault(item.getId(), Collections.emptyList());
            return needHandleDetail(item, detailList, bo);
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(needUpdateItemList)) {
            return Collections.emptyList();
        }

        List<Long> itemIds = itemList.stream().map(OutStockOrderItemPO::getId).distinct().collect(Collectors.toList());

        // key：OutStockOrderId
        Map<Long, List<OrderItemTaskInfoPO>> orderIdGroupMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(bo.getOrderItemTaskInfoPOList())) {
            Map<Long, List<OrderItemTaskInfoPO>> tmpOrderIdGroupMap = bo.getOrderItemTaskInfoPOList().stream()
                .collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderId));
            orderIdGroupMap.putAll(tmpOrderIdGroupMap);
        }

        Map<Long, OutStockOrderPO> outStockOrderPOMap =
            filterOrderList.stream().collect(Collectors.toMap(OutStockOrderPO::getId, v -> v));

        Map<Long, List<OutStockOrderItemPO>> needItemGroupMap =
            needUpdateItemList.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getOutstockorderId));

        List<OutStockOrderItemDetailRecoverHandleByItemBO> itemBOList = new ArrayList<>();
        for (Map.Entry<Long, List<OutStockOrderItemPO>> entry : needItemGroupMap.entrySet()) {
            List<OutStockOrderItemPO> value = entry.getValue();
            OutStockOrderItemDetailRecoverHandleByItemBO itemBO = new OutStockOrderItemDetailRecoverHandleByItemBO();
            OutStockOrderPO outStockOrderPO = outStockOrderPOMap.get(entry.getKey());
            itemBO.setOutStockOrderPO(outStockOrderPO);
            List<OrderItemTaskInfoPO> mtpOrderItemTaskInfoPOS = orderIdGroupMap.get(entry.getKey());
            itemBO.setOrderItemTaskInfoPOList(mtpOrderItemTaskInfoPOS);
            itemBO.setNeedHandleItemList(value);
            itemBO.setWarehouseId(outStockOrderPO.getWarehouseId());
            itemBO.setOrgId(outStockOrderPO.getOrgId());
            itemBO.setHandleEqualCountItem(bo.isHandleEqualCountItem());
            itemBO.setUseOrderCenterDetail(bo.isUseOrderCenterDetail());
            itemBO.setUseInventoryRecord(bo.isUseInventoryRecord());

            itemBOList.add(itemBO);
        }

        return itemBOList;
    }

    private boolean needHandleDetail(OutStockOrderItemPO item, List<OutStockOrderItemDetailPO> detailList,
        OutStockOrderItemDetailRecoverBO bo) {
        // detail不存在，直接返回true
        if (CollectionUtils.isEmpty(detailList)) {
            return Boolean.TRUE;
        }
        BigDecimal detailCount = detailList.stream().map(OutStockOrderItemDetailPO::getUnitTotalCount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 如果有小于0的detail 也返回true
        boolean anySmallThanZero = detailList.stream().map(OutStockOrderItemDetailPO::getUnitTotalCount)
            .anyMatch(m -> m.compareTo(BigDecimal.ZERO) < 0);

        // 处理item和detail数量不等、detail数量有小于零的、detail总数量小于零的、强制处理相等的场景
        return item.getUnittotalcount().compareTo(detailCount) != 0 || detailCount.compareTo(BigDecimal.ZERO) < 0
            || anySmallThanZero || bo.isHandleEqualCountItem();
    }

    abstract List<OutStockOrderPO> doFilterOrderList(OutStockOrderItemDetailRecoverBO bo);

    /**
     * 修复detail
     * 
     * @param bo
     * @return
     */
    public List<OutStockOrderItemDetailPO> recoverDetail(OutStockOrderItemDetailRecoverBO bo) {
        List<OutStockOrderItemDetailRecoverHandleByItemBO> itemBOList = filterAndCreateOrderList(bo);
        if (CollectionUtils.isEmpty(itemBOList)) {
            return Collections.emptyList();
        }

        return doRecover(itemBOList);
    }

    protected abstract List<OutStockOrderItemDetailPO>
        doRecover(List<OutStockOrderItemDetailRecoverHandleByItemBO> itemBOList);

    private List<OutStockOrderItemPO> filterOutStockOrderItemList(List<OutStockOrderItemPO> itemList,
        OutStockOrderItemDetailRecoverBO bo) {
        Map<Long, List<OutStockOrderItemDetailPO>> detailGroupMap = itemList.stream()
            .filter(item -> CollectionUtils.isNotEmpty(item.getItemDetails())).flatMap(m -> m.getItemDetails().stream())
            .collect(Collectors.groupingBy(OutStockOrderItemDetailPO::getOutStockOrderItemId));

        // 找出需要处理的订单项
        List<OutStockOrderItemPO> needUpdateItemList = itemList.stream().filter(item -> {
            List<OutStockOrderItemDetailPO> detailList =
                detailGroupMap.getOrDefault(item.getId(), Collections.emptyList());
            BigDecimal detailCount = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(detailList)) {
                detailCount = detailList.stream().map(OutStockOrderItemDetailPO::getUnitTotalCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            boolean anySmallThanZero = detailList.stream().map(OutStockOrderItemDetailPO::getUnitTotalCount)
                .anyMatch(m -> m.compareTo(BigDecimal.ZERO) < 0);

            return item.getUnittotalcount().compareTo(detailCount) != 0 || detailCount.compareTo(BigDecimal.ZERO) < 0
                || anySmallThanZero || bo.isHandleEqualCountItem();
        }).collect(Collectors.toList());

        return needUpdateItemList;
    }

    /**
     * 获取中台的detail分配信息 返回值 key 是 outStockOrderItemId
     */
    protected Map<Long, OrderWithItemOwnersItemDTO> getOrderCenterSecOwnerByOrderIds(
        List<OutStockOrderPO> outStockOrderPOS, List<OutStockOrderItemPO> needUpdateItemList) {
        List<Long> outStockOrderIds = needUpdateItemList.stream().map(OutStockOrderItemPO::getOutstockorderId)
            .distinct().collect(Collectors.toList());
        Map<Long, String> idToBusinessIdMap =
            outStockOrderPOS.stream().collect(Collectors.toMap(OutStockOrderPO::getId, OutStockOrderPO::getBusinessId));
        Map<String, Long> businessIdToIdMap =
            outStockOrderPOS.stream().collect(Collectors.toMap(OutStockOrderPO::getBusinessId, OutStockOrderPO::getId));

        List<Long> businessIds =
            outStockOrderIds.stream().map(idToBusinessIdMap::get).map(Long::valueOf).collect(Collectors.toList());
        List<OrderWithItemOwnersDTO> orderWithItemOwnersDTOList = orderCenterQueryBL.getSecOwnerByOrderIds(businessIds);
        if (CollectionUtils.isEmpty(orderWithItemOwnersDTOList)) {
            return Collections.emptyMap();
        }

        Map<String, Long> businessItemIdToItemIdMap = needUpdateItemList.stream()
            .collect(Collectors.toMap(OutStockOrderItemPO::getBusinessItemId, OutStockOrderItemPO::getId));
        // key 是 outStockOrderItemId
        Map<Long, OrderWithItemOwnersItemDTO> orderWithItemOwnersItemDTOMap = orderWithItemOwnersDTOList.stream()
            .flatMap(m -> m.getOrderItems().stream())
            .filter(item -> Objects.nonNull(businessItemIdToItemIdMap.get(item.getOrderItemId().toString())))
            .collect(Collectors.toMap(item -> businessItemIdToItemIdMap.get(item.getOrderItemId().toString()), v -> v));

        return orderWithItemOwnersItemDTOMap;
    }

    protected List<OutStockOrderItemPO> getNeedHandleItemList(OutStockOrderItemDetailRecoverHandleByItemBO bo) {
        if (BooleanUtils.isTrue(bo.isHandleEqualCountItem())) {
            return bo.getOutStockOrderPO().getItems();
        }

        return bo.getNeedHandleItemList();
    }

    /**
     * 用中台货主信息，生成detail
     * 
     * @param orderWithItemOwnersItemDTO
     * @param item
     * @return
     */
    protected List<OutStockOrderItemDetailPO>
        genDetailWithOrderCenterInfo(OrderWithItemOwnersItemDTO orderWithItemOwnersItemDTO, OutStockOrderItemPO item) {
        // 如果订单中台detail不存在，随意生成一个
        if (Objects.isNull(orderWithItemOwnersItemDTO)
            || CollectionUtils.isEmpty(orderWithItemOwnersItemDTO.getOrderItemOwners())) {
            LOGGER.info("中台未查到detail信息，出库单ItemId:{}", item.getId());
            // 走自己生成的逻辑
            return OutStockOrderItemDetailConverter.convertNewOutStockOrderItemDetail(item);
        }

        List<OrderWithItemOwnersItemDetailDTO> orderCenterItemDetailList =
            orderWithItemOwnersItemDTO.getOrderItemOwners();

        BigDecimal centerUnitTotalCount = orderCenterItemDetailList.stream()
            .map(OrderWithItemOwnersItemDetailDTO::getCount).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (centerUnitTotalCount.compareTo(item.getUnittotalcount()) != 0) {
            // 如果中台的 和 wms的数量不一致，生成久批自己的货主
            LOGGER.info("中台和wms的detail数量不一致，出库单ItemId:{}", item.getId());
            return OutStockOrderItemDetailConverter.convertNewOutStockOrderItemDetail(item);
        }

        // 直接用中台的货主信息
        return OutStockOrderItemDetailConverter
            .convertOutStockOrderItemDetailWithOrderCenterDetail(orderWithItemOwnersItemDTO, item);
    }

}
