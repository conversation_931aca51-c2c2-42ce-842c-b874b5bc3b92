package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.supplychain.outstock.service.IWMSOrderExceptionService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderTraceBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchTaskItemUpdateDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskConfirmDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.CompleteRobotBatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskItemStateEnum;
import com.yijiupi.supplychain.serviceutils.constant.redis.RedisConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/12/13
 */
@Service
@SuppressWarnings("NonAsciiCharacters")
public class BatchTaskFinishBL {

    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private SowTaskMapper sowTaskMapper;
    @Autowired
    private OrderTraceBL orderTraceBL;
    @Autowired
    private BatchTaskFinishConcreteBL batchTaskFinishConcreteBL;
 
    private static final Logger LOGGER = LoggerFactory.getLogger(BatchTaskFinishBL.class);

    @Deprecated
    @DistributeLock(conditions = "#batchTaskId", sleepMills = 3000, key = RedisConstant.SUP_F + "completeBatchTaskItem")
    @Transactional(rollbackFor = RuntimeException.class)
    public Map<String, List<BatchTaskDTO>> updateBatchTaskItem(
            List<BatchTaskItemCompleteDTO> batchTaskItemUpdateDTOList, String batchTaskId, String userName,
            Integer warehouseId, Long locationId, String locationName, Integer cityId, Integer userId, Byte containerFlag) {
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);
        if (Objects.isNull(batchTaskPO)) {
            throw new BusinessValidateException("拣货任务不存在！");
        }
        return batchOrderTaskBL.updateBatchTaskItem(batchTaskItemUpdateDTOList, batchTaskId, userName, warehouseId,
                locationId, locationName, cityId, userId, containerFlag);
    }

    @DistributeLock(conditions = "#batchTaskId", sleepMills = 3000, key = RedisConstant.SUP_F + "completeBatchTaskItem")
    @Transactional(rollbackFor = RuntimeException.class)
    public Map<String, List<BatchTaskDTO>> completeBatchTaskItem(
            List<BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOList, String batchTaskId, String userName,
            Integer warehouseId, Long locationId, String locationName, Integer cityId, Integer userId, Byte containerFlag) {
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);
        if (Objects.isNull(batchTaskPO)) {
            throw new BusinessValidateException("拣货任务不存在！");
        }
        BatchTaskItemFinishBO batchTaskItemFinishBO = new BatchTaskItemFinishBO();
        batchTaskItemFinishBO.setBatchTaskId(batchTaskId);
        batchTaskItemFinishBO.setUserName(userName);
        batchTaskItemFinishBO.setWarehouseId(warehouseId);
        batchTaskItemFinishBO.setLocationId(locationId);
        batchTaskItemFinishBO.setLocationName(locationName);
        batchTaskItemFinishBO.setCityId(cityId);
        batchTaskItemFinishBO.setUserId(userId);
        batchTaskItemFinishBO.setContainerFlag(containerFlag);
        batchTaskItemFinishBO.setBatchTaskItemCompleteDTOS(batchTaskItemCompleteDTOList);

        return batchTaskFinishConcreteBL.finishBatchTaskItem(batchTaskItemFinishBO);
    }

    /**
     * 完成拣货任务明细，明细全部完成后自动完成拣货任务
     */
    @DistributeLock(conditions = "#batchTaskId", sleepMills = 3000, expireMills = 60000, key = BatchTaskConstants.BATCH_TASK_COMPLETE_KEY,
            lockType = DistributeLock.LockType.WAITLOCK)
    @Transactional(rollbackFor = RuntimeException.class)
    public void completeRobotBatchTask(CompleteRobotBatchTaskDTO dto) {
        LOGGER.info("机器人完成拣货任务，入参:{}", JSON.toJSONString(dto));
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(dto.getBatchTaskId());
        if (Objects.isNull(batchTaskPO)) {
            LOGGER.info("拣货任务不存在，入参:{}", JSON.toJSONString(dto));
            return;
        }
        try {
            List<BatchTaskItemPO> batchTaskItemList = batchTaskItemMapper
                    .listBatchTaskItemByTaskId(Collections.singletonList(dto.getBatchTaskId()), dto.getOrgId());

            Map<String, String> batchTaskItemMap =
                    dto.getComleteBatchTaskItemIdList().stream().collect(Collectors.toMap(k -> k, v -> v));
            List<BatchTaskItemPO> batchTaskItemPOList = batchTaskItemList.stream()
                    .filter(m -> Objects.nonNull(batchTaskItemMap.get(m.getId()))).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(batchTaskItemPOList)) {
                LOGGER.info("机器人完成拣货任务，未找到符合条件的拣货任务项，入参:{}", JSON.toJSONString(dto));
                return;
            }

            List<BatchTaskItemPO> updateBatchTaskItemList = batchTaskItemPOList.stream().map(m -> {
                BatchTaskItemPO updatePO = new BatchTaskItemPO();
                updatePO.setId(m.getId());
                updatePO.setTaskState(TaskItemStateEnum.已完成.getType());
                return updatePO;
            }).collect(Collectors.toList());

            // updateBatchTaskItemList.forEach(m -> batchTaskItemMapper.updateBatchTaskItem(m));

            // 更新拣货任务
            SowTaskPO sowTaskPO =
                    sowTaskMapper.getSowTaskById(batchTaskPO.getSowTaskId(), Integer.valueOf(batchTaskPO.getOrgId()));
            List<BatchTaskItemCompleteDTO> batchTaskItemUpdateDTOList =
                    BatchTaskItemUpdateDTOConvertor.convertList(updateBatchTaskItemList, batchTaskItemPOList);

            batchOrderTaskBL.updateBatchTaskItem(batchTaskItemUpdateDTOList, batchTaskPO.getId(),
                    BatchOrderTaskBL.ROBOT_NAME, batchTaskPO.getWarehouseId(), batchTaskPO.getLocationId(),
                    sowTaskPO.getLocationName(), Integer.valueOf(batchTaskPO.getOrgId()), 1,
                    ConditionStateEnum.否.getType());

            List<BatchTaskItemPO> taskItemList = batchTaskItemMapper
                    .listBatchTaskItemByTaskId(Collections.singletonList(dto.getBatchTaskId()), dto.getOrgId());
            if (CollectionUtils.isEmpty(taskItemList)) {
                LOGGER.info("机器人完成拣货任务，再次查询拣货任务项为空，入参:{}", JSON.toJSONString(dto));
                return;
            }
            boolean allFinished =
                    taskItemList.stream().allMatch(m -> TaskItemStateEnum.已完成.getType() == m.getTaskState());
            if (!allFinished) {
                return;
            }

            BatchTaskPO updateBatchTaskPO = new BatchTaskPO();
            updateBatchTaskPO.setId(dto.getBatchTaskId());
            updateBatchTaskPO.setCompleteTime(new Date());
            updateBatchTaskPO.setTaskState(TaskItemStateEnum.已完成.getType());

            batchTaskMapper.updateByPrimaryKeySelective(updateBatchTaskPO);
        } catch (DataValidateException | BusinessValidateException e) {
            LOGGER.warn("拣货任务完成失败，入参：" + JSON.toJSONString(dto), e);
            orderTraceBL.updateBatchTaskTrace(batchTaskPO, "拣货任务完成失败，原因:" + e.getMessage(), "机器人");
            throw e;
        } catch (Exception e) {
            LOGGER.warn("拣货任务完成失败，入参：" + JSON.toJSONString(dto), e);
            orderTraceBL.updateBatchTaskTrace(batchTaskPO, "拣货任务完成失败，原因: 系统异常", "机器人");
            throw e;
        }
    }

}
