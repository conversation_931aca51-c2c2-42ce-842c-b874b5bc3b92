/*
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation;

import java.util.List;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderLocationDTO;

/**
 * 基础获取出库位策略类
 *
 * <AUTHOR>
 * @since 2023/11/22
 */
public abstract class OutStockLocationBaseBL {

    @Reference
    protected IWarehouseQueryService warehouseQueryService;

    /**
     * 当前策略是否支持指定仓库
     *
     * @param warehouseId 仓库 id
     * @return 当前策略是否支持指定仓库, 默认实现为仅支持灰度仓库
     */
    public boolean support(Integer warehouseId) {
        return Boolean.TRUE;
    }

    /**
     * 获取出库位
     */
    public abstract List<OutStockOrderLocationDTO> getOutLocation(List<OutStockOrderLocationDTO> locations);

    /**
     * 通用方法, 通过仓库 id 查询仓库信息
     *
     * @param warehouseIds 仓库 id
     * @return 仓库信息
     */
    protected final List<Warehouse> queryWarehouseByIds(List<Integer> warehouseIds) {
        return warehouseQueryService.listWarehouseByIds(warehouseIds);
    }

}
