package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023-12-12 11:59
 **/
public class InBoundBatchPieceParam implements Serializable {

    /**
     * 入库批次号
     */
    private final String boundNo;

    /**
     * 入库批次里所有入库单关联的出库单 id
     */
    private final Set<InBoundBatchPieceOrderInfo> outStockOrderInfo;

    public static InBoundBatchPieceParam of(String boundNo, Set<InBoundBatchPieceOrderInfo> outStockOrderInfo) {
        return new InBoundBatchPieceParam(boundNo, outStockOrderInfo);
    }

    public InBoundBatchPieceParam(String boundNo, Set<InBoundBatchPieceOrderInfo> outStockOrderInfo) {
        this.boundNo = boundNo;
        this.outStockOrderInfo = outStockOrderInfo;
    }

    public boolean containsOrder(Long orderId) {
        if (CollectionUtils.isEmpty(outStockOrderInfo)) {
            return false;
        }
        return outStockOrderInfo.stream().anyMatch(it -> it.getOrderId().equals(orderId));
    }

    public String getBoundNo() {
        return boundNo;
    }

    public Set<InBoundBatchPieceOrderInfo> getOutStockOrderInfo() {
        return outStockOrderInfo;
    }

    @Override
    public String toString() {
        return "InBoundBatchPieceParam{" +
                "boundNo='" + boundNo + '\'' +
                ", outStockOrderInfo=" + outStockOrderInfo +
                '}';
    }

    public static final class InBoundBatchPieceOrderInfo implements Serializable {
        private final Long orderId;
        private final Integer orgId;

        public static InBoundBatchPieceOrderInfo of(Long orderId, Integer orgId) {
            return new InBoundBatchPieceOrderInfo(orderId, orgId);
        }

        public InBoundBatchPieceOrderInfo(Long orderId, Integer orgId) {
            this.orderId = orderId;
            this.orgId = orgId;
        }

        public Long getOrderId() {
            return orderId;
        }

        public Integer getOrgId() {
            return orgId;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            InBoundBatchPieceOrderInfo that = (InBoundBatchPieceOrderInfo) o;
            return Objects.equals(orderId, that.orderId) && Objects.equals(orgId, that.orgId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(orderId, orgId);
        }

        @Override
        public String toString() {
            return "InBoundBatchPieceOrderInfo{" +
                    "orderId=" + orderId +
                    ", orgId=" + orgId +
                    '}';
        }
    }

}
