package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.supplychain.waves.dto.trace.OrderTraceDTO;

public interface OrderTracePOMapper {

    /**
     * 单条插入
     * 
     * @param record
     * @return
     */
    int insert(OrderTraceDTO record);

    /**
     * 批量插入
     * 
     * @param record
     * @return
     */
    int insertUpdateBatch(@Param("list") List<OrderTraceDTO> record);

    /**
     * 根据业务Id或No查询所有日志
     * 
     * @param businessId
     * @param businessNo
     * @return
     */
    List<OrderTraceDTO> selectByBusinessIdOrNo(@Param("orgId") Integer orgId, @Param("businessId") Long businessId,
        @Param("businessNo") String businessNo);
}