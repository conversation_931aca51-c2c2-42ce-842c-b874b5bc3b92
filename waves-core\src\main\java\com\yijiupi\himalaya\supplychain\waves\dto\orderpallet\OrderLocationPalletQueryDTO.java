/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.waves.dto.orderpallet;

import java.io.Serializable;
import java.util.List;

/**
 * 订单货位托盘关系查询
 * 
 * <AUTHOR>
 * @since 2024年8月15日
 */
public class OrderLocationPalletQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 城市id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位id集合
     */
    private List<Long> locationIdList;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单id集合
     */
    private List<Long> orderIdList;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 波次任务Id
     */
    private String batchTaskId;
    /**
     * 波次任务Id集合
     */
    private List<String> batchTaskIdList;
    /**
     * 托盘号集合
     */
    private List<String> palletNoList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public List<Long> getLocationIdList() {
        return locationIdList;
    }

    public void setLocationIdList(List<Long> locationIdList) {
        this.locationIdList = locationIdList;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public List<Long> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Long> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public List<String> getBatchTaskIdList() {
        return batchTaskIdList;
    }

    public void setBatchTaskIdList(List<String> batchTaskIdList) {
        this.batchTaskIdList = batchTaskIdList;
    }

    public List<String> getPalletNoList() {
        return palletNoList;
    }

    public void setPalletNoList(List<String> palletNoList) {
        this.palletNoList = palletNoList;
    }
}
