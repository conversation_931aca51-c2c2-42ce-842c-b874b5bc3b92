package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 波次删除对象
 *
 * <AUTHOR>
 * @since 4/2/21 9:45 AM
 */
public class DeleteBatchDTO implements Serializable {

    private static final long serialVersionUID = -9126562168333414564L;
    /**
     * 波次号
     */
    private List<String> batchNoList;

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 是否忽略批次库存
     */
    private Boolean ignoreBatchInventory;

    /**
     * 是否忽略波次状态
     */
    private Boolean ignoreBatchState;

    /**
     * 操作人ID
     */
    private Integer operateUserId;
    /**
     * 是否后台删除
     */
    private boolean isOpCancel = Boolean.FALSE;

    /**
     * 是否校验出库单状态
     */
    private boolean checkOrderState = true;

    private List<DeleteBatchNoDTO> batchNoDTOList;

    public List<String> getBatchNoList() {
        return batchNoList;
    }

    public void setBatchNoList(List<String> batchNoList) {
        this.batchNoList = batchNoList;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public Boolean getIgnoreBatchInventory() {
        return ignoreBatchInventory;
    }

    public void setIgnoreBatchInventory(Boolean ignoreBatchInventory) {
        this.ignoreBatchInventory = ignoreBatchInventory;
    }

    public Boolean getIgnoreBatchState() {
        return ignoreBatchState;
    }

    public void setIgnoreBatchState(Boolean ignoreBatchState) {
        this.ignoreBatchState = ignoreBatchState;
    }

    public Integer getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(Integer operateUserId) {
        this.operateUserId = operateUserId;
    }

    /**
     * 获取 是否后台删除
     *
     * @return isOpCancel 是否后台删除
     */
    public boolean getIsIsOpCancel() {
        return this.isOpCancel;
    }

    /**
     * 设置 是否后台删除
     *
     * @param isOpCancel 是否后台删除
     */
    public void setIsOpCancel(boolean isOpCancel) {
        this.isOpCancel = isOpCancel;
    }

    public boolean isCheckOrderState() {
        return checkOrderState;
    }

    public void setCheckOrderState(boolean checkOrderState) {
        this.checkOrderState = checkOrderState;
    }

    /**
     * 获取
     *
     * @return batchNoDTOList
     */
    public List<DeleteBatchNoDTO> getBatchNoDTOList() {
        return this.batchNoDTOList;
    }

    /**
     * 设置
     *
     * @param batchNoDTOList
     */
    public void setBatchNoDTOList(List<DeleteBatchNoDTO> batchNoDTOList) {
        this.batchNoDTOList = batchNoDTOList;
    }

    public void resetBatchNoDTOList() {
        this.batchNoDTOList = batchNoList.stream().map(m -> new DeleteBatchNoDTO(m)).collect(Collectors.toList());
    }

}
