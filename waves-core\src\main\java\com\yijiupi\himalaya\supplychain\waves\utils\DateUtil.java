package com.yijiupi.himalaya.supplychain.waves.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Title: supplychain-microservice-settings
 * @Package com.yijiupi.himalaya.supplychain.util
 * @Description:
 * @date 2018/3/17 15:32
 */
public class DateUtil {
    public static final String YYYYMMDD_HHMMSS = "yyyy-MM-dd HH:mm:ss";
    public static final String HHMMSS = "HH:mm:ss";

    /**
     * @param date
     * @return cron
     * @Description: 将执行时间改为cron表达式
     * <AUTHOR>
     * @date 2018/3/19 14:56
     */
    public static String formatTimeToCronExpression(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("0 m H * * ?  ");
        String time = format.format(date);
        System.out.println(time);
        return time;
    }

    /**
     * 锁对象
     */
    private static final Object LOCK_OBJ = new Object();

    /**
     * 存放不同的日期模板格式的sdf的Map
     */
    private static Map<String, ThreadLocal<SimpleDateFormat>> sdfMap =
        new HashMap<String, ThreadLocal<SimpleDateFormat>>();

    /**
     * 返回一个ThreadLocal的sdf,每个线程只会new一次sdf
     *
     * @param pattern
     * @return
     */
    private static SimpleDateFormat getSdf(final String pattern) {
        ThreadLocal<SimpleDateFormat> tl = sdfMap.get(pattern);

        // 此处的双重判断和同步是为了防止sdfMap这个单例被多次put重复的sdf
        if (tl == null) {
            synchronized (LOCK_OBJ) {
                tl = sdfMap.get(pattern);
                if (tl == null) {
                    // 只有Map中还没有这个pattern的sdf才会生成新的sdf并放入map

                    // 这里是关键,使用ThreadLocal<SimpleDateFormat>替代原来直接new SimpleDateFormat
                    tl = new ThreadLocal<SimpleDateFormat>() {

                        @Override
                        protected SimpleDateFormat initialValue() {
                            System.out.println("thread: " + Thread.currentThread() + " init pattern: " + pattern);
                            return new SimpleDateFormat(pattern);
                        }
                    };
                    sdfMap.put(pattern, tl);
                }
            }
        }

        return tl.get();
    }

    public static void main(String[] args) throws Exception {
        // TreeMap map = new TreeMap();
        // String now = DateUtil.format(new Date(), DateUtil.HHMMSS);
        // String[] ar = {"19:44:32", "8:44:32", "15:44:32"};
        // for (int i = 0; i < 3; i++) {
        // long timeDisparity = DateUtil.getTimeDisparity(now, ar[i]);
        // System.out.println(timeDisparity);
        // map.put(Long.valueOf(timeDisparity), i);
        // }
        // System.out.println(map.firstKey().toString());
    }

    public static long getTimeDisparity(String time, String compateTime) throws Exception {
        Date d1 = parse(time, HHMMSS);
        Date d2 = parse(compateTime, HHMMSS);
        // 这样得到的差值是微秒级别
        long diff = d1.getTime() - d2.getTime();
        long days = diff / (1000 * 60 * 60 * 24);
        long hours = (diff - days * (1000 * 60 * 60 * 24)) / (1000 * 60 * 60);
        long minutes = (diff - days * (1000 * 60 * 60 * 24)) / (1000 * 60);
        // // 为负数需要加一天的分钟时间
        // if (minutes < 0) {
        // minutes += 24 * 60;
        // }
        return minutes;
    }

    /**
     * @param time
     * @return YYYYMMDD_HHMMSS
     * @Description: 获取HH:mm:ss格式的昨天的时间
     * <AUTHOR>
     * @date 2018/3/27 14:32
     */
    public static String getYesterdayTime(String time) throws Exception {
        Date parse = parse(time, HHMMSS);
        Calendar cal = Calendar.getInstance();
        int hours = parse.getHours();
        int minutes = parse.getMinutes();
        int seconds = parse.getSeconds();
        cal.add(Calendar.DATE, -1);
        cal.set(Calendar.HOUR_OF_DAY, hours);
        cal.set(Calendar.MINUTE, minutes);
        cal.set(Calendar.SECOND, seconds);
        return format(cal.getTime(), YYYYMMDD_HHMMSS);
    }

    /**
     * @param
     * @return YYYYMMDD_HHMMSS
     * @Description: 获取HH:mm:ss格式的今天的时间
     * <AUTHOR>
     * @date 2018/3/27 14:56
     */
    public static String getTodayTime(String time) throws Exception {
        Date parse = parse(time, HHMMSS);
        Calendar cal = Calendar.getInstance();
        int hours = parse.getHours();
        int minutes = parse.getMinutes();
        int seconds = parse.getSeconds();
        cal.set(Calendar.HOUR_OF_DAY, hours);
        cal.set(Calendar.MINUTE, minutes);
        cal.set(Calendar.SECOND, seconds);
        return format(cal.getTime(), YYYYMMDD_HHMMSS);
    }

    /**
     * 是用ThreadLocal<SimpleDateFormat>来获取SimpleDateFormat,这样每个线程只会有一个SimpleDateFormat
     *
     * @param date
     * @param pattern
     * @return
     */
    public static String format(Date date, String pattern) {
        return getSdf(pattern).format(date);
    }

    public static Date parse(String dateStr, String pattern) throws ParseException {
        return getSdf(pattern).parse(dateStr);
    }

}
