package com.yijiupi.himalaya.supplychain.waves.domain.bl.itemdetail;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.outstock.service.IOrderUpdateService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.BatchOutBoundParamDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutBoundBatchDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.outboundbatch.BatchQueryOutBoundBatchDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutBoundBatchManageService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutBoundBatchQueryService;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.OutStockOrderItemDetailRecoverBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.OutStockOrderItemDetailConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemDetailCommMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDetailRecoverDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/27
 */
@Service
public class OutStockOrderItemDetailRecoverContextBL {

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private OutStockOrderItemDetailCommMapper outStockOrderItemDetailCommMapper;

    @Autowired
    private List<OutStockOrderItemDetailRecoverBaseBL> recoverBaseBLList;

    @Reference
    private IOrderUpdateService iOrderUpdateService;
    @Reference
    private IOutBoundBatchManageService iOutBoundBatchManageService;
    @Reference
    private IOutBoundBatchQueryService iOutBoundBatchQueryService;


    protected static final Logger LOGGER = LoggerFactory.getLogger(OutStockOrderItemDetailRecoverContextBL.class);

    @Transactional(rollbackFor = Exception.class)
    public List<OutStockOrderItemDetailDTO> recoverDetailList(OutStockOrderItemDetailRecoverDTO dto) {
        AssertUtils.notEmpty(dto.getOutStockOrderIds(), "订单信息不能为空！");
        if (BooleanUtils.isFalse(dto.isRecoverDetailOrNot())) {
            return Collections.emptyList();
        }
        if (dto.getOutStockOrderIds().size() >= 100) {
            throw new BusinessValidateException("超过最大修复数量限制，请减少！");
        }

        List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.findByOrderIds(dto.getOutStockOrderIds());
        if (CollectionUtils.isEmpty(outStockOrderPOS)) {
            LOGGER.info("修复detail数据，没有找到出库单信息！");
            return Collections.emptyList();
        }

        List<Long> outStockOrderItemIds = outStockOrderPOS.stream().flatMap(m -> m.getItems().stream())
            .map(OutStockOrderItemPO::getId).distinct().collect(Collectors.toList());

        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(outStockOrderItemIds);

        OutStockOrderItemDetailRecoverBO bo = new OutStockOrderItemDetailRecoverBO();
        bo.setHandleEqualCountItem(dto.isHandleEqualCountItem());
        bo.setUseInventoryRecord(dto.isUseInventoryRecord());
        bo.setHandleEqualCountItem(dto.isHandleEqualCountItem());
        bo.setOutStockOrderPOS(outStockOrderPOS);
        bo.setOrderItemTaskInfoPOList(orderItemTaskInfoPOList);
        bo.setUseOrderCenterDetail(dto.isUseOrderCenterDetail());

        List<OutStockOrderItemDetailPO> updateDetailList =
            recoverBaseBLList.stream().map(boBL -> boBL.recoverDetail(bo)).filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(updateDetailList)) {
            LOGGER.info("没有要处理的detail数据");
            return Collections.emptyList();
        }

        List<Long> updateOrderItemIds = updateDetailList.stream().map(OutStockOrderItemDetailPO::getOutStockOrderItemId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderItemDetailPO> existDetailPOList =
            outStockOrderItemDetailCommMapper.findByItemIds(null, updateOrderItemIds);
        if (CollectionUtils.isNotEmpty(existDetailPOList)) {
            Lists.partition(existDetailPOList, 200).forEach(list -> {
                LOGGER.info("修复detail，删除detail数据为:{}", JSON.toJSONString(list));
            });

            existDetailPOList.forEach(detailPO -> {
                outStockOrderItemDetailCommMapper.deleteByPrimaryKey(detailPO.getId());
            });
        }

        Lists.partition(updateDetailList, 200).forEach(list -> {
            LOGGER.info("修复detail，插入detail数据为:{}", JSON.toJSONString(list));
        });

        outStockOrderItemDetailCommMapper.insertBatch(updateDetailList);

        List<Long> recoverOutStockOrderItemIds = updateDetailList.stream().map(OutStockOrderItemDetailPO :: getOutStockOrderItemId).distinct().collect(Collectors.toList());
        LOGGER.info("修复detail，修复的订单项为:{}", JSON.toJSONString(recoverOutStockOrderItemIds));

        return OutStockOrderItemDetailConverter.convertToDTO(updateDetailList);
    }

    public void sendOrderCenterDetailInfo(OutStockOrderItemDetailRecoverDTO dto) {
        if (BooleanUtils.isTrue(dto.isSendDetailToOmsOrderCenter())) {
            iOrderUpdateService.syncOmsStockOrderItemDetailChange(dto.getOutStockOrderIds());
        }
    }

    public void sendOutStockInfoToErp(OutStockOrderItemDetailRecoverDTO dto) {
        if (Objects.isNull(dto.getErpDTO())) {
            return;
        }
        if (BooleanUtils.isFalse(dto.getErpDTO().isRecoverErpOrNot())) {
            return;
        }

        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.selectByIds(dto.getOutStockOrderIds());
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return;
        }

        List<OutStockOrderPO> filterOrderList = outStockOrderPOList.stream().filter(m -> Objects.nonNull(m.getOutBoundType())).filter(this :: needSendErp).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterOrderList)) {
            return;
        }

        Map<String, List<OutStockOrderPO>> orderGroupBoundNo = filterOrderList.stream().collect(Collectors.groupingBy(OutStockOrderPO :: getBoundNo));

        if (BooleanUtils.isTrue(dto.getErpDTO().isSendAllBatch())) {
            sendAllBatchOrder(orderGroupBoundNo, new ArrayList<>(orderGroupBoundNo.keySet()));
        }

        if (BooleanUtils.isTrue(dto.getErpDTO().getSendChangeOrder())) {
            sendOnlyChangeOrder(orderGroupBoundNo, new ArrayList<>(orderGroupBoundNo.keySet()));
        }

    }

    private void sendAllBatchOrder(Map<String, List<OutStockOrderPO>> orderGroupBoundNo, List<String> boundNoList) {
        BatchQueryOutBoundBatchDTO queryOutBoundBatchDTO = new BatchQueryOutBoundBatchDTO();
        queryOutBoundBatchDTO.setOutBoundBatchNos(boundNoList);
        List<OutBoundBatchDTO> outBoundBatchDTOList = iOutBoundBatchQueryService.findOutBoundBatchList(queryOutBoundBatchDTO);
        Map<String, OutBoundBatchDTO> boundBatchNoMap = outBoundBatchDTOList.stream().collect(Collectors.toMap(OutBoundBatchDTO :: getBoundBatchNo, v -> v));

        for (Map.Entry<String, List<OutStockOrderPO>> entry : orderGroupBoundNo.entrySet()) {
            OutBoundBatchDTO outBoundBatchDTO =  boundBatchNoMap.get(entry.getKey());
            List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findSimpleOrderInfoByOutBoundNoList(Collections.singletonList(outBoundBatchDTO.getBoundBatchNo()), outBoundBatchDTO.getWarehouseId());

            BatchOutBoundParamDTO param = new BatchOutBoundParamDTO();
            param.setAheadType(1);
            param.setBatchNo(outBoundBatchDTO.getBoundBatchNo());
            param.setOrgId(outBoundBatchDTO.getOrgId());
            param.setTaskId(outBoundBatchDTO.getRelatedBatchId());
            param.setWarehouseId(outBoundBatchDTO.getWarehouseId());
            param.setBusinessIds(outStockOrderPOList.stream().map(OutStockOrderPO :: getBusinessId).collect(Collectors.toList()));
            iOutBoundBatchManageService.fixOutBoundNotify(param);
        }

    }

    private void sendOnlyChangeOrder(Map<String, List<OutStockOrderPO>> orderGroupBoundNo, List<String> boundNoList) {
        BatchQueryOutBoundBatchDTO queryOutBoundBatchDTO = new BatchQueryOutBoundBatchDTO();
        queryOutBoundBatchDTO.setOutBoundBatchNos(boundNoList);
        List<OutBoundBatchDTO> outBoundBatchDTOList = iOutBoundBatchQueryService.findOutBoundBatchList(queryOutBoundBatchDTO);
        Map<String, OutBoundBatchDTO> boundBatchNoMap = outBoundBatchDTOList.stream().collect(Collectors.toMap(OutBoundBatchDTO :: getBoundBatchNo, v -> v));


        for (Map.Entry<String, List<OutStockOrderPO>> entry : orderGroupBoundNo.entrySet()) {
            OutBoundBatchDTO outBoundBatchDTO =  boundBatchNoMap.get(entry.getKey());

            BatchOutBoundParamDTO param = new BatchOutBoundParamDTO();
            param.setAheadType(1);
            param.setBatchNo(outBoundBatchDTO.getBoundBatchNo());
            param.setOrgId(outBoundBatchDTO.getOrgId());
            param.setTaskId(outBoundBatchDTO.getRelatedBatchId());
            param.setWarehouseId(outBoundBatchDTO.getWarehouseId());
            param.setBusinessIds(entry.getValue().stream().map(OutStockOrderPO :: getBusinessId).collect(Collectors.toList()));
            iOutBoundBatchManageService.fixOutBoundNotify(param);
        }
    }

    private boolean needSendErp(OutStockOrderPO outStockOrderPO) {
        if (Objects.isNull(outStockOrderPO.getOutBoundType())) {
            return Boolean.FALSE;
        }

        if (OutStockOrderStateEnum.已出库.getType() != outStockOrderPO.getState().byteValue()) {
            return Boolean.FALSE;
        }

        if (outStockOrderPO.getOutBoundType().intValue() == OutBoundTypeEnum.INTERNAL_DELIVERY_ORDER.getCode()) {
            return Boolean.TRUE;
        }

        if (outStockOrderPO.getOutBoundType().intValue() == OutBoundTypeEnum.ALLOT_ORDER.getCode() && outStockOrderPO.getReforderno().contains("NPT")) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;

    }

}
