package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import java.util.List;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
public class NotifyOrderLackBO {
    /**
     * 操作人id
     */
    private Integer optUserId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 是否开启中台
     */
    private boolean isOpenOrderCenter;
    /**
     * 有orderitemtaskinfo，调用oms的缺货
     */
    private NotifyOmsOrderLackBO notifyOmsOrderLackBO;
    /**
     * 没有orderitemtaskinfo的缺货
     */
    private NotifyOmsOldOrderLackBO notifyOmsOldOrderLackBO;
    /**
     * 美团缺货信息
     */
    private NotifyMeiTuanLackBO meiTuanLackBO;

    /**
     * 缺货的订单项id
     */
    private List<Long> lackOrderItemIds;
    /**
     * 只包含缺货的order信息（不包含播种的订单）
     */
    private List<OutStockOrderPO> lackOrderList;
    /**
     * 缺货的所有订单的信息（包含播种的订单）
     */
    private List<OutStockOrderPO> totalLackOrderList;

    public NotifyOrderLackBO(Integer optUserId, Integer warehouseId, boolean isOpenOrderCenter,
        NotifyOrderCenterLackBO notifyOrderCenterLackBO) {
        this.optUserId = optUserId;
        this.warehouseId = warehouseId;
        this.isOpenOrderCenter = isOpenOrderCenter;
        this.notifyOrderCenterLackBO = notifyOrderCenterLackBO;
    }

    public NotifyOrderLackBO(Integer optUserId, Integer warehouseId, List<OutStockOrderPO> totalLackOrderList) {
        this.optUserId = optUserId;
        this.warehouseId = warehouseId;
        this.totalLackOrderList = totalLackOrderList;
    }

    /**
     * 调用中台的缺货
     */
    private NotifyOrderCenterLackBO notifyOrderCenterLackBO;

    public NotifyOrderLackBO() {}

    public NotifyOrderLackBO(Integer optUserId, Integer warehouseId, boolean isOpenOrderCenter,
        NotifyOmsOrderLackBO notifyOmsOrderLackBO) {
        this.optUserId = optUserId;
        this.warehouseId = warehouseId;
        this.isOpenOrderCenter = isOpenOrderCenter;
        this.notifyOmsOrderLackBO = notifyOmsOrderLackBO;
    }

    public NotifyOrderLackBO(Integer optUserId, Integer warehouseId, boolean isOpenOrderCenter,
        NotifyOmsOldOrderLackBO notifyOmsOldOrderLackBO) {
        this.optUserId = optUserId;
        this.warehouseId = warehouseId;
        this.isOpenOrderCenter = isOpenOrderCenter;
        this.notifyOmsOldOrderLackBO = notifyOmsOldOrderLackBO;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 是否开启中台
     *
     * @return isOpenOrderCenter 是否开启中台
     */
    public boolean isIsOpenOrderCenter() {
        return this.isOpenOrderCenter;
    }

    /**
     * 设置 是否开启中台
     *
     * @param isOpenOrderCenter 是否开启中台
     */
    public void setIsOpenOrderCenter(boolean isOpenOrderCenter) {
        this.isOpenOrderCenter = isOpenOrderCenter;
    }

    /**
     * 获取
     *
     * @return notifyOmsOrderLackBO
     */
    public NotifyOmsOrderLackBO getNotifyOmsOrderLackBO() {
        return this.notifyOmsOrderLackBO;
    }

    /**
     * 设置
     *
     * @param notifyOmsOrderLackBO
     */
    public void setNotifyOmsOrderLackBO(NotifyOmsOrderLackBO notifyOmsOrderLackBO) {
        this.notifyOmsOrderLackBO = notifyOmsOrderLackBO;
    }

    /**
     * 获取 没有orderitemtaskinfo的缺货
     *
     * @return notifyOmsOldOrderLackBO 没有orderitemtaskinfo的缺货
     */
    public NotifyOmsOldOrderLackBO getNotifyOmsOldOrderLackBO() {
        return this.notifyOmsOldOrderLackBO;
    }

    /**
     * 设置 没有orderitemtaskinfo的缺货
     *
     * @param notifyOmsOldOrderLackBO 没有orderitemtaskinfo的缺货
     */
    public void setNotifyOmsOldOrderLackBO(NotifyOmsOldOrderLackBO notifyOmsOldOrderLackBO) {
        this.notifyOmsOldOrderLackBO = notifyOmsOldOrderLackBO;
    }

    /**
     * 获取 调用中台的缺货
     *
     * @return notifyOrderCenterLackBO 调用中台的缺货
     */
    public NotifyOrderCenterLackBO getNotifyOrderCenterLackBO() {
        return this.notifyOrderCenterLackBO;
    }

    /**
     * 设置 调用中台的缺货
     *
     * @param notifyOrderCenterLackBO 调用中台的缺货
     */
    public void setNotifyOrderCenterLackBO(NotifyOrderCenterLackBO notifyOrderCenterLackBO) {
        this.notifyOrderCenterLackBO = notifyOrderCenterLackBO;
    }

    /**
     * 获取 美团缺货信息
     *
     * @return meiTuanLackBO 美团缺货信息
     */
    public NotifyMeiTuanLackBO getMeiTuanLackBO() {
        return this.meiTuanLackBO;
    }

    /**
     * 设置 美团缺货信息
     *
     * @param meiTuanLackBO 美团缺货信息
     */
    public void setMeiTuanLackBO(NotifyMeiTuanLackBO meiTuanLackBO) {
        this.meiTuanLackBO = meiTuanLackBO;
    }

    /**
     * 获取 缺货的订单项id
     *
     * @return lackOrderItemIds 缺货的订单项id
     */
    public List<Long> getLackOrderItemIds() {
        return this.lackOrderItemIds;
    }

    /**
     * 设置 缺货的订单项id
     *
     * @param lackOrderItemIds 缺货的订单项id
     */
    public void setLackOrderItemIds(List<Long> lackOrderItemIds) {
        this.lackOrderItemIds = lackOrderItemIds;
    }

    /**
     * 获取 只包含缺货的order信息
     *
     * @return lackOrderList 只包含缺货的order信息
     */
    public List<OutStockOrderPO> getLackOrderList() {
        return this.lackOrderList;
    }

    /**
     * 设置 只包含缺货的order信息
     *
     * @param lackOrderList 只包含缺货的order信息
     */
    public void setLackOrderList(List<OutStockOrderPO> lackOrderList) {
        this.lackOrderList = lackOrderList;
    }

    /**
     * 获取 缺货的所有订单的信息（包含播种的订单）
     *
     * @return totalLackOrderList 缺货的所有订单的信息（包含播种的订单）
     */
    public List<OutStockOrderPO> getTotalLackOrderList() {
        return this.totalLackOrderList;
    }

    /**
     * 设置 缺货的所有订单的信息（包含播种的订单）
     *
     * @param totalLackOrderList 缺货的所有订单的信息（包含播种的订单）
     */
    public void setTotalLackOrderList(List<OutStockOrderPO> totalLackOrderList) {
        this.totalLackOrderList = totalLackOrderList;
    }

    public static NotifyOrderLackBO getLackOrderNotifyBO(Integer optUserId, Integer warehouseId,
        List<OutStockOrderPO> totalLackOrderList) {
        return new NotifyOrderLackBO(optUserId, warehouseId, totalLackOrderList);
    }
}
