package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/7/2
 */
public class PickLocationModToBatchTaskItemDTO implements Serializable {
    /**
     * 产品skuId
     */
    private Long skuId;
    /**
     * 来源货位
     */
    private Long fromLocationId;
    /**
     * 目标货位
     */
    private Long toLocationId;

    /**
     * 获取 产品skuId
     *
     * @return skuId 产品skuId
     */
    public Long getSkuId() {
        return this.skuId;
    }

    /**
     * 设置 产品skuId
     *
     * @param skuId 产品skuId
     */
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    /**
     * 获取 来源货位
     *
     * @return fromLocationId 来源货位
     */
    public Long getFromLocationId() {
        return this.fromLocationId;
    }

    /**
     * 设置 来源货位
     *
     * @param fromLocationId 来源货位
     */
    public void setFromLocationId(Long fromLocationId) {
        this.fromLocationId = fromLocationId;
    }

    /**
     * 获取 目标货位
     *
     * @return toLocationId 目标货位
     */
    public Long getToLocationId() {
        return this.toLocationId;
    }

    /**
     * 设置 目标货位
     *
     * @param toLocationId 目标货位
     */
    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

}
