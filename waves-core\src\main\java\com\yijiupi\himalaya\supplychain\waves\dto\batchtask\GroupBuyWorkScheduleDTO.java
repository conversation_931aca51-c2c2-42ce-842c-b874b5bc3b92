package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 团购订单作业进度对象
 *
 * <AUTHOR>
 * @date 2/5/21 5:12 PM
 */
public class GroupBuyWorkScheduleDTO implements Serializable {

    private static final long serialVersionUID = 3951543812425531515L;

    /**
     * 波次id
     */
    private String batchId;

    /**
     * 工作状态 待作业：0 作业中：1 已作业：2
     */
    private Byte workState;

    /**
     * 线路Id
     */
    private String routeId;

    /**
     * 线路名称
     */
    private String routeName;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverMobile;

    /**
     * 自提点数量
     */
    private Integer addressCount;

    /**
     * 商品种类
     */
    private Integer skuCount;

    /**
     * 已作业商品种类
     */
    private Integer completeSkuCount;

    /**
     * 总数量
     */
    private BigDecimal totalCount;

    /**
     * 已播数量
     */
    private BigDecimal overSortCount;

    /**
     * 缺货数量
     */
    private BigDecimal lackCount;

    /**
     * 产品明细
     */
    List<GroupBuyWorkScheduleItemDTO> items;

    /**
     * 订单id（波次下任意一个订单）
     */
    private Long orderId;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getRouteId() {
        return routeId;
    }

    public void setRouteId(String routeId) {
        this.routeId = routeId;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getDriverMobile() {
        return driverMobile;
    }

    public void setDriverMobile(String driverMobile) {
        this.driverMobile = driverMobile;
    }

    public Integer getAddressCount() {
        return addressCount;
    }

    public void setAddressCount(Integer addressCount) {
        this.addressCount = addressCount;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getOverSortCount() {
        return overSortCount;
    }

    public void setOverSortCount(BigDecimal overSortCount) {
        this.overSortCount = overSortCount;
    }

    public BigDecimal getLackCount() {
        return lackCount;
    }

    public void setLackCount(BigDecimal lackCount) {
        this.lackCount = lackCount;
    }

    public Integer getCompleteSkuCount() {
        return completeSkuCount;
    }

    public void setCompleteSkuCount(Integer completeSkuCount) {
        this.completeSkuCount = completeSkuCount;
    }

    public List<GroupBuyWorkScheduleItemDTO> getItems() {
        return items;
    }

    public void setItems(List<GroupBuyWorkScheduleItemDTO> items) {
        this.items = items;
    }

    public Byte getWorkState() {
        return workState;
    }

    public void setWorkState(Byte workState) {
        this.workState = workState;
    }
}
