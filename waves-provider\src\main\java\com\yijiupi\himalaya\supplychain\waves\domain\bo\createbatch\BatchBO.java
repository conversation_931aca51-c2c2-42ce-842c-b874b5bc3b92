package com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch;

import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/23
 */
public class BatchBO {

    private BatchPO batchPO;

    /**
     * 用来设置播种任务的序号【重要】
     */
    private Integer sowTaskNum = 0;

    public BatchBO() {}

    public BatchBO(BatchPO batchPO) {
        this.batchPO = batchPO;
    }

    /**
     * 获取
     *
     * @return batchPO
     */
    public BatchPO getBatchPO() {
        return this.batchPO;
    }

    /**
     * 设置
     *
     * @param batchPO
     */
    public void setBatchPO(BatchPO batchPO) {
        this.batchPO = batchPO;
    }

    /**
     * 获取 用来设置播种任务的序号【重要】
     * 
     * @return sowTaskNum
     */
    public Integer getSowTaskNum() {
        return this.sowTaskNum;
    }

    /**
     * 设置 用来设置播种任务的序号【重要】
     * 
     * @param sowTaskNum
     */
    public void setSowTaskNum(Integer sowTaskNum) {
        this.sowTaskNum = sowTaskNum;
    }
}
