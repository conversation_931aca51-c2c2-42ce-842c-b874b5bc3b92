package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.distributedlock.exceptions.HasLockedException;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderFeatureBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderTraceBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchFinishedBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderpallet.OrderLocationPalletBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.perform.TaskPerformanceCalculateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.TaskPerformanceCalculateBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.orderpallet.OrderInPalletQueryBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.OrderPalletInfoDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.WavesStrategyBOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderLocationPalletPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderPalletInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import com.yijiupi.supplychain.serviceutils.constant.OrderFeatureConstant;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.dubbo.config.annotation.Reference;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2024/8/19
 */
@Service
public class BatchTaskCompleteBL {

    @Autowired
    private BatchFinishedBL batchFinishedBL;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private OrderTraceBL orderTraceBL;
    @Autowired
    private GlobalCache globalCache;
    @Autowired
    private WavesStrategyBOConvertor wavesStrategyBOConvertor;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private BatchTaskChangeNotifyBL batchTaskChangeNotifyBL;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OrderFeatureBL orderFeatureBL;
    @Autowired
    private TaskPerformanceCalculateBL taskPerformanceCalculateBL;

    @Autowired
    private OrderLocationPalletBL orderLocationPalletBL;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Resource
    private RedisTemplate<String, BigDecimal> redisTemplate;
    private static final String NOT_OPEN_LOCATION_STOCK_PREFIX = "NotOpenLocationStock:%s:%s";
    private static final Integer NOT_OPEN_LOCATION_STOCK_CACHE_EXPIRE_DAYS = 14;

    private static final BigDecimal MAC_CAPACITY = new BigDecimal(80);
    private static final Logger LOG = LoggerFactory.getLogger(BatchTaskCompleteBL.class);

    @DistributeLock(conditions = "#batchTaskCompleteDTO.batchTaskId", sleepMills = 3000, expireMills = 60000,
        key = BatchTaskConstants.BATCH_TASK_COMPLETE_KEY)
    @Transactional(rollbackFor = RuntimeException.class, propagation = Propagation.REQUIRES_NEW)
    public void completeBatchTask(BatchTaskCompleteDTO batchTaskCompleteDTO) {
        LOG.info("完成拣货任务 入参：{}", JSON.toJSONString(batchTaskCompleteDTO));
        String batchTaskId = batchTaskCompleteDTO.getBatchTaskId();
        String userName = globalCache.getUserName(batchTaskCompleteDTO.getOptUserId());
        // 2、修改波次任务状态
        BatchTaskPO batchTaskPO = batchTaskMapper.findBatchTaskById(batchTaskId);
        validateBatchTaskComplete(batchTaskPO);
        // 校验及保存托盘信息
        checkAndAddPallet(batchTaskPO, batchTaskCompleteDTO, userName);
        // 更新拣货任务托盘号
        String toPalletNo = getPalletNoStr(batchTaskCompleteDTO.getOrderPalletInfoDTOS());

        // 存在集货位时，出库位设置为空
        batchTaskMapper.updateBatchTaskById(batchTaskPO.getId(), TaskStateEnum.已完成.getType(), null, null,
            batchTaskCompleteDTO.getOptUserId(), userName, null, Integer.valueOf(batchTaskPO.getOrgId()), toPalletNo);

        if (batchTaskCompleteDTO.getOptUserId() != null
            && !Objects.equals(batchTaskPO.getSorterId(), batchTaskCompleteDTO.getOptUserId())) {
            batchTaskPO.setSorterId(batchTaskCompleteDTO.getOptUserId());
        }

        Integer orgId = Integer.valueOf(batchTaskPO.getOrgId());

        taskPerformanceCalculateBL.calculateAndModTaskPerformance(TaskPerformanceCalculateBO.getBatchTaskInstance(
            Collections.singletonList(batchTaskPO.getId()), batchTaskPO.getSorterId(), batchTaskPO.getWarehouseId(),
            orgId, updateBatchTaskPO -> batchTaskMapper.updateByPrimaryKeySelective(updateBatchTaskPO)));

        // -添加操作记录（完成拣货）
        orderTraceBL.updateBatchTaskTrace(batchTaskPO, OrderTraceDescriptionEnum.拣货完成.name(), userName);

        batchTaskChangeNotifyBL.notifyBatchTaskComplete(batchTaskPO);
        // 6、修改波次状态
        try {
            batchFinishedBL.completeWave(batchTaskPO.getBatchNo(), batchTaskCompleteDTO.getOptUserId(),
                Integer.valueOf(batchTaskPO.getOrgId()));
        } catch (HasLockedException e) {
            throw new BusinessValidateException("正在处理，请稍后再试！");
        }

        processNotOpenLocationStockCache(batchTaskCompleteDTO);
    }

    private void processNotOpenLocationStockCache(BatchTaskCompleteDTO batchTaskCompleteDTO) {
        try {
            Integer warehouseId = batchTaskCompleteDTO.getWarehouseId();
            if (warehouseConfigService.isOpenLocationStock(warehouseId)) {
                return;
            }

            String batchTaskId = batchTaskCompleteDTO.getBatchTaskId();
            /*
             * 2.5: 零捡位/分拣位当前库存 = 默认为上阶段预测需求库存-上阶段实际分拣消耗库存，捡货员通知补货后重置为零，
             * 补货后增加，捡货后减少，可以理解为2.5版本不太准的货位库存
             */
            List<BatchTaskItemDTO> batchTaskItemDtoListById = batchTaskItemMapper.findBatchTaskItemDtoListById(batchTaskId);
            if (CollectionUtils.isEmpty(batchTaskItemDtoListById)) {
                return;
            }

            for (BatchTaskItemDTO batchTaskItemDTO : batchTaskItemDtoListById) {
                String key = String.format(NOT_OPEN_LOCATION_STOCK_PREFIX, warehouseId, batchTaskItemDTO.getSkuId());
                BoundValueOperations<String, BigDecimal> ops = redisTemplate.boundValueOps(key);
                BigDecimal existInventory = ObjectUtils.defaultIfNull(ops.get(), BigDecimal.ZERO);
                // 已拣货数量
                BigDecimal overSortCount = batchTaskItemDTO.getOverSortCount();
                BigDecimal subtract = existInventory.subtract(overSortCount).setScale(0, BigDecimal.ROUND_HALF_UP);
                if (subtract.compareTo(BigDecimal.ZERO) >= 0) {
                    ops.set(subtract, NOT_OPEN_LOCATION_STOCK_CACHE_EXPIRE_DAYS, TimeUnit.DAYS);
                    LOG.info("未开启货位库存，拣货完成后扣减库存成功。key={}:subtract={}", key, subtract);
                }

                if (subtract.compareTo(BigDecimal.ZERO) < 0 && existInventory.compareTo(BigDecimal.ZERO) > 0) {
                    ops.set(BigDecimal.ZERO, NOT_OPEN_LOCATION_STOCK_CACHE_EXPIRE_DAYS, TimeUnit.DAYS);
                    LOG.info("未开启货位库存，拣货完成后库存为0。key={}:subtract={}:existInventory={}", key, subtract, existInventory);
                }
            }
        } catch (Exception e) {
            LOG.error("未开启货位库存，拣货完成后扣减库存失败。BatchTaskCompleteDTO={}", JSON.toJSONString(batchTaskCompleteDTO), e);
        }
    }

    /**
     * 拣货任务完成检查及新增托盘信息
     */
    public void checkAndAddPallet(BatchTaskPO batchTaskPO, BatchTaskCompleteDTO batchTaskCompleteDTO, String userName) {
        boolean isNeedPalletForStock =
            wavesStrategyBOConvertor.getOpenTrayPositionLocation(batchTaskPO.getWarehouseId());
        boolean isNeedPalletForSortingMode =
            wavesStrategyBOConvertor.getOpenPalletForSortingMode(batchTaskPO.getWarehouseId());
        if (!isNeedPalletForStock && !isNeedPalletForSortingMode) {
            return;
        }
        // 只处理按单拣货
        if (!Objects.equals(batchTaskPO.getPickingType(), PickingTypeEnum.订单拣货.getType())) {
            return;
        }

        AssertUtils.notEmpty(batchTaskCompleteDTO.getOrderPalletInfoDTOS(), "订单托盘信息不能为空");
        batchTaskCompleteDTO.getOrderPalletInfoDTOS().forEach(info -> {
            AssertUtils.notNull(info.getOrderId(), "订单id不能为空");
            AssertUtils.notNull(info.getOrderNo(), "订单号不能为空");
            AssertUtils.notEmpty(info.getPalletNoList(), "托盘号集合不能为空");
        });

        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoByBatchTaskIds(Collections.singletonList(batchTaskPO.getId()));
        List<Long> orderIdInBatchTaskList = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderId)
            .distinct().collect(Collectors.toList());

        Map<Long, List<OrderPalletInfoDTO>> palletMap = batchTaskCompleteDTO.getOrderPalletInfoDTOS().stream()
            .collect(Collectors.groupingBy(OrderPalletInfoDTO::getOrderId));

        // 开了接力分拣，酒饮单需绑定托盘
        if (isNeedPalletForSortingMode) {
            List<OutStockOrderPO> outStockOrderPOList =
                outStockOrderMapper.findSimpleByIds(orderIdInBatchTaskList, null, null);
            Map<Long, String> orderIdNoMap = outStockOrderPOList.stream()
                .collect(Collectors.toMap(OutStockOrderPO::getId, OutStockOrderPO::getReforderno));
            Map<Long, List<Byte>> orderFeatureMap = orderFeatureBL.getOrderFeatureMap(orderIdInBatchTaskList);
            orderFeatureMap.forEach((key, value) -> {
                List<OrderPalletInfoDTO> orderPalletInfoDTOS = palletMap.get(key);
                boolean haveWineFeature = value.stream().anyMatch(OrderFeatureConstant.FEATURE_TYPE_DRINKING::equals);
                if (haveWineFeature && CollectionUtils.isEmpty(orderPalletInfoDTOS)) {
                    throw new BusinessValidateException("酒饮订单 " + orderIdNoMap.get(key) + " 需绑定托盘！");
                }
            });
        }

        List<OrderLocationPalletDTO> palletDTOList =
            getAndValidateOrderLocationPalletInfoDTOS(batchTaskCompleteDTO, batchTaskPO, userName);

        palletDTOList.forEach(palletDTO -> {
            orderLocationPalletBL.addPallet(palletDTO);
        });

        // OrderLocationPalletDTO palletDTO = new OrderLocationPalletDTO();
        // palletDTO.setOrgId(Integer.valueOf(batchTaskPO.getOrgId()));
        // palletDTO.setWarehouseId(batchTaskPO.getWarehouseId());
        // palletDTO.setLocationId(batchTaskPO.getToLocationId());
        // palletDTO.setLocationName(batchTaskPO.getToLocationName());
        // palletDTO.setBatchTaskId(batchTaskPO.getId());
        // palletDTO.setOrderPalletInfoDTOS(batchTaskCompleteDTO.getOrderPalletInfoDTOS());
        // palletDTO.setLastUpdateUser(userName);
        // iOrderLocationPalletService.addPallet(palletDTO);
    }

    private List<OutStockOrderPO> filterOrderFeature(BatchTaskCompleteDTO batchTaskCompleteDTO,
        BatchTaskPO batchTaskPO) {
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoByBatchTaskIds(Collections.singletonList(batchTaskPO.getId()));
        List<Long> orderIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getRefOrderId).distinct()
            .collect(Collectors.toList());

        List<OutStockOrderPO> outStockOrderPOList =
            outStockOrderMapper.findSimpleInfoByIds(orderIds, batchTaskCompleteDTO.getWarehouseId());
        Map<Long, String> orderIdNoMap = outStockOrderPOList.stream()
            .collect(Collectors.toMap(OutStockOrderPO::getId, OutStockOrderPO::getReforderno));
        Map<Long, List<Byte>> orderFeatureMap = orderFeatureBL.getOrderFeatureMap(orderIds);

        outStockOrderPOList = outStockOrderPOList.stream().filter(m -> {
            List<Byte> orderFeatureList = orderFeatureMap.get(m.getId());
            return orderFeatureList.stream().anyMatch(t -> OrderFeatureConstant.FEATURE_TYPE_DRINKING.equals(t));
        }).collect(Collectors.toList());

        return outStockOrderPOList;
    }

    public void validateBatchTaskComplete(BatchTaskPO batchTaskPO) {
        if (Objects.isNull(batchTaskPO)) {
            throw new BusinessValidateException("拣货任务不存在，请查验！");
        }
        List<BatchTaskItemDTO> batchTaskItemDTOList =
            batchTaskItemMapper.findBatchTaskItemDtoListById(batchTaskPO.getId());
        if (CollectionUtils.isEmpty(batchTaskItemDTOList)) {
            throw new BusinessValidateException("拣货任务明细不存在！");
        }

        boolean hasNotFinished =
            batchTaskItemDTOList.stream().anyMatch(m -> TaskStateEnum.已完成.getType() != m.getTaskState());
        if (hasNotFinished) {
            throw new BusinessValidateException("还有拣货任务未完成，请查验！");
        }
    }

    private String getPalletNoStr(List<OrderPalletInfoDTO> orderPalletInfoDTOS) {
        if (CollectionUtils.isEmpty(orderPalletInfoDTOS)) {
            return null;
        }

        List<String> palletNoList = orderPalletInfoDTOS.stream()
            .filter(p -> !CollectionUtils.isEmpty(p.getPalletNoList())).map(OrderPalletInfoDTO::getPalletNoList)
            .flatMap(list -> list.stream().distinct()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(palletNoList)) {
            return null;
        }

        String palletNoStr = "#" + palletNoList.stream().distinct().sorted().collect(Collectors.joining("#"));
        return palletNoStr;
    }

    // 酒饮按分区拣货，如果订单的其中一个拣货任务已经设置托盘，其他拣货任务取这个托盘。
    private List<OrderLocationPalletDTO> getAndValidateOrderLocationPalletInfoDTOS(
        BatchTaskCompleteDTO batchTaskCompleteDTO, BatchTaskPO batchTaskPO, String userName) {
        if (BooleanUtils.isFalse(needHandleDrinkPickBySortGroup(batchTaskPO))) {
            OrderLocationPalletDTO palletDTO = new OrderLocationPalletDTO();
            palletDTO.setOrgId(Integer.valueOf(batchTaskPO.getOrgId()));
            palletDTO.setWarehouseId(batchTaskPO.getWarehouseId());
            palletDTO.setLocationId(batchTaskPO.getToLocationId());
            palletDTO.setLocationName(batchTaskPO.getToLocationName());
            palletDTO.setBatchTaskId(batchTaskPO.getId());
            palletDTO.setOrderPalletInfoDTOS(batchTaskCompleteDTO.getOrderPalletInfoDTOS());
            palletDTO.setLastUpdateUser(userName);
            return Collections.singletonList(palletDTO);
        }

        List<Long> orderIds = batchTaskCompleteDTO.getOrderPalletInfoDTOS().stream().map(OrderPalletInfoDTO::getOrderId)
            .distinct().collect(Collectors.toList());

        // 订单维度查询是否已经绑定了托盘
        Map<Long, List<OrderLocationPalletDTO>> hasBindPalletInfoMap =
            findOrderAlreadyBindOrderPalletInfoDTOS(batchTaskCompleteDTO, batchTaskPO);

        // 如果没有绑定托盘，订单的所有下属拣货任务都绑定
        // if (hasBindPalletInfoMap.isEmpty()) {
        // OrderLocationPalletDTO palletDTO = new OrderLocationPalletDTO();
        // palletDTO.setOrgId(Integer.valueOf(batchTaskPO.getOrgId()));
        // palletDTO.setWarehouseId(batchTaskPO.getWarehouseId());
        // palletDTO.setLocationId(batchTaskPO.getToLocationId());
        // palletDTO.setLocationName(batchTaskPO.getToLocationName());
        // palletDTO.setBatchTaskId(batchTaskPO.getId());
        // palletDTO.setOrderPalletInfoDTOS(batchTaskCompleteDTO.getOrderPalletInfoDTOS());
        // palletDTO.setLastUpdateUser(userName);
        // return Collections.singletonList(palletDTO);
        // }

        if (!hasBindPalletInfoMap.isEmpty()) {
            validateHasBindPalletInfo(batchTaskCompleteDTO, batchTaskPO, hasBindPalletInfoMap);
        }

        // 验证容量
        validateCapacity(batchTaskCompleteDTO, batchTaskPO);

        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(orderIds);

        orderItemTaskInfoPOList.removeIf(m -> m.getBatchTaskId().equals(batchTaskPO.getId()));

        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            OrderLocationPalletDTO palletDTO = new OrderLocationPalletDTO();
            palletDTO.setOrgId(Integer.valueOf(batchTaskPO.getOrgId()));
            palletDTO.setWarehouseId(batchTaskPO.getWarehouseId());
            palletDTO.setLocationId(batchTaskPO.getToLocationId());
            palletDTO.setLocationName(batchTaskPO.getToLocationName());
            palletDTO.setBatchTaskId(batchTaskPO.getId());
            palletDTO.setOrderPalletInfoDTOS(batchTaskCompleteDTO.getOrderPalletInfoDTOS());
            palletDTO.setLastUpdateUser(userName);
            return Collections.singletonList(palletDTO);
        }

        Map<Long, Set<String>> orderBatchTaskGroupMap =
            orderItemTaskInfoPOList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderId,
                Collectors.mapping(OrderItemTaskInfoPO::getBatchTaskId, Collectors.toSet())));

        List<String> batchTaskIds =
            orderBatchTaskGroupMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());

        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findBatchTaskByIds(batchTaskIds);
        Map<String, BatchTaskPO> batchTaskPOMap =
            batchTaskPOList.stream().collect(Collectors.toMap(BatchTaskPO::getId, v -> v));

        List<OrderLocationPalletDTO> totalPalletDTOList = new ArrayList();
        Map<Long, List<OrderPalletInfoDTO>> orderPalletInfoGroupMap = batchTaskCompleteDTO.getOrderPalletInfoDTOS()
            .stream().collect(Collectors.groupingBy(OrderPalletInfoDTO::getOrderId));
        for (Map.Entry<Long, Set<String>> entry : orderBatchTaskGroupMap.entrySet()) {
            Long orderId = entry.getKey();
            Set<String> inOrderBatchTaskIds = entry.getValue();
            List<OrderPalletInfoDTO> palletInfoDTOS = orderPalletInfoGroupMap.get(orderId);
            List<BatchTaskPO> tmpBatchTaskPOList = inOrderBatchTaskIds.stream()
                .map(batchTaskId -> batchTaskPOMap.get(batchTaskId)).collect(Collectors.toList());

            List<OrderLocationPalletDTO> palletDTOList = tmpBatchTaskPOList.stream().map(batchTask -> {
                OrderLocationPalletDTO palletDTO = new OrderLocationPalletDTO();
                palletDTO.setOrgId(Integer.valueOf(batchTask.getOrgId()));
                palletDTO.setWarehouseId(batchTask.getWarehouseId());
                palletDTO.setLocationId(batchTask.getToLocationId());
                palletDTO.setLocationName(batchTask.getToLocationName());
                palletDTO.setBatchTaskId(batchTask.getId());
                palletDTO.setOrderPalletInfoDTOS(palletInfoDTOS);
                palletDTO.setLastUpdateUser(userName);
                return palletDTO;
            }).collect(Collectors.toList());
            totalPalletDTOList.addAll(palletDTOList);
        }

        OrderLocationPalletDTO palletDTO = new OrderLocationPalletDTO();
        palletDTO.setOrgId(Integer.valueOf(batchTaskPO.getOrgId()));
        palletDTO.setWarehouseId(batchTaskPO.getWarehouseId());
        palletDTO.setLocationId(batchTaskPO.getToLocationId());
        palletDTO.setLocationName(batchTaskPO.getToLocationName());
        palletDTO.setBatchTaskId(batchTaskPO.getId());
        palletDTO.setOrderPalletInfoDTOS(batchTaskCompleteDTO.getOrderPalletInfoDTOS());
        palletDTO.setLastUpdateUser(userName);
        totalPalletDTOList.add(palletDTO);

        return totalPalletDTOList;
    }

    private Map<Long, List<OrderLocationPalletDTO>>
        findAlreadyBindOrderPalletInfoDTOS(BatchTaskCompleteDTO batchTaskCompleteDTO, BatchTaskPO batchTaskPO) {
        Map<Long, List<OrderLocationPalletDTO>> hasBindPalletInfoDTOS = new HashMap<>();
        batchTaskCompleteDTO.getOrderPalletInfoDTOS().forEach(orderPalletInfoDTO -> {
            OrderLocationPalletQueryDTO queryDTO = new OrderLocationPalletQueryDTO();
            queryDTO.setOrderId(orderPalletInfoDTO.getOrderId());
            queryDTO.setWarehouseId(batchTaskPO.getWarehouseId());
            queryDTO.setLocationId(batchTaskPO.getToLocationId());
            queryDTO.setPalletNoList(orderPalletInfoDTO.getPalletNoList());
            List<OrderLocationPalletDTO> list = orderLocationPalletBL.findPalletByCondition(queryDTO);
            if (!CollectionUtils.isEmpty(list)) {
                hasBindPalletInfoDTOS.put(orderPalletInfoDTO.getOrderId(), list);
            }
        });

        return hasBindPalletInfoDTOS;
    }

    /**
     * 查询订单是否已经绑定托盘
     *
     * @param batchTaskCompleteDTO
     * @param batchTaskPO
     * @return
     */
    private Map<Long, List<OrderLocationPalletDTO>>
        findOrderAlreadyBindOrderPalletInfoDTOS(BatchTaskCompleteDTO batchTaskCompleteDTO, BatchTaskPO batchTaskPO) {
        List<Long> orderIds = batchTaskCompleteDTO.getOrderPalletInfoDTOS().stream().map(OrderPalletInfoDTO::getOrderId)
            .distinct().collect(Collectors.toList());
        OrderLocationPalletQueryDTO queryDTO = new OrderLocationPalletQueryDTO();
        queryDTO.setOrderIdList(orderIds);
        queryDTO.setWarehouseId(batchTaskPO.getWarehouseId());
        List<OrderLocationPalletDTO> list = orderLocationPalletBL.findPalletByCondition(queryDTO);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.groupingBy(OrderLocationPalletDTO::getOrderId));
    }

    private boolean needHandleDrinkPickBySortGroup(BatchTaskPO batchTaskPO) {
        if (BatchTaskTypeEnum.酒饮按订单分区拣货.getType() != batchTaskPO.getBatchTaskType()) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    private void validateHasBindPalletInfo(BatchTaskCompleteDTO batchTaskCompleteDTO, BatchTaskPO batchTaskPO,
        Map<Long, List<OrderLocationPalletDTO>> hasBindPalletInfoMap) {
        List<Long> orderIds = batchTaskCompleteDTO.getOrderPalletInfoDTOS().stream().map(OrderPalletInfoDTO::getOrderId)
            .distinct().collect(Collectors.toList());

        // Map<Long, List<OrderLocationPalletDTO>> hasBindPalletInfoMap =
        // findAlreadyBindOrderPalletInfoDTOS(batchTaskCompleteDTO, batchTaskPO);
        if (hasBindPalletInfoMap.isEmpty()) {
            return;
        }

        // 验证绑定托盘是否一致
        Map<Long, List<String>> palletNoMap = batchTaskCompleteDTO.getOrderPalletInfoDTOS().stream()
            .collect(Collectors.toMap(OrderPalletInfoDTO::getOrderId, OrderPalletInfoDTO::getPalletNoList));

        Map<Long, Set<String>> differOrderPalletMap = new HashMap<>();
        for (Map.Entry<Long, List<OrderLocationPalletDTO>> entry : hasBindPalletInfoMap.entrySet()) {
            Long orderId = entry.getKey();
            Set<String> palletList = new HashSet<>(palletNoMap.get(orderId));
            Set<String> bindPalletList = new HashSet<>(entry.getValue().stream()
                .map(OrderLocationPalletDTO::getPalletNo).distinct().collect(Collectors.toList()));

            Set<String> differPalletList = Sets.difference(palletList, bindPalletList);
            if (!differPalletList.isEmpty()) {
                differOrderPalletMap.put(entry.getKey(), bindPalletList);
            }
        }

        throwExceptionMessage(orderIds, differOrderPalletMap, batchTaskPO);
    }

    private void throwExceptionMessage(List<Long> orderIds, Map<Long, Set<String>> hasBindPalletInfoMap,
        BatchTaskPO batchTaskPO) {
        if (hasBindPalletInfoMap.isEmpty()) {
            return;
        }
        List<OutStockOrderPO> outStockOrderPOList =
            outStockOrderMapper.findSimpleInfoByIds(orderIds, batchTaskPO.getWarehouseId());
        Map<Long, OutStockOrderPO> outStockOrderPOMap =
            outStockOrderPOList.stream().collect(Collectors.toMap(OutStockOrderPO::getId, v -> v));

        StringBuilder message = new StringBuilder();
        hasBindPalletInfoMap.forEach((orderId, orderPalletS) -> {
            OutStockOrderPO outStockOrderPO = outStockOrderPOMap.get(orderId);
            message.append("订单");
            message.append(outStockOrderPO.getReforderno());
            message.append("已绑定托盘");
            message.append(orderPalletS);
            message.append(";");
        });

        message.append("请重新指定托盘。");

        throw new BusinessValidateException(message.toString());
    }

    // 该托盘订单件数总计已超过X件，请使用其他托盘号
    private void validateCapacity(BatchTaskCompleteDTO batchTaskCompleteDTO, BatchTaskPO batchTaskPO) {
        OrderInPalletQueryBO queryBO = new OrderInPalletQueryBO();
        queryBO.setLocationId(batchTaskPO.getToLocationId());
        queryBO.setOrgId(Integer.valueOf(batchTaskPO.getOrgId()));
        queryBO.setWarehouseId(batchTaskPO.getWarehouseId());
        List<String> palletNoList =
            batchTaskCompleteDTO.getOrderPalletInfoDTOS().stream().map(OrderPalletInfoDTO::getPalletNoList)
                .flatMap(Collection::stream).distinct().collect(Collectors.toList());
        queryBO.setPalletNoList(palletNoList);
        List<OrderLocationPalletPO> orderInLocationPalletList = orderLocationPalletBL.findInPalletOrderList(queryBO);

        List<Long> orderIds = batchTaskCompleteDTO.getOrderPalletInfoDTOS().stream().map(OrderPalletInfoDTO::getOrderId)
            .distinct().collect(Collectors.toList());

        // Set<Long> differOrderIds =
        // Sets.difference(new HashSet<>(orderIdsInLocationPalletList), new HashSet<>(orderIds));
        // if (CollectionUtils.isEmpty(differOrderIds)) {
        // return;
        // }

        Set<Long> totalOrderIds = new HashSet<>();
        totalOrderIds.addAll(orderIds);

        if (!CollectionUtils.isEmpty(orderInLocationPalletList)) {
            List<Long> orderIdsInLocationPalletList = orderInLocationPalletList.stream()
                .map(OrderLocationPalletPO::getOrderId).distinct().collect(Collectors.toList());
            totalOrderIds.addAll(orderIdsInLocationPalletList);
        }

        Map<String, Set<Long>> orderIdLocationGroupMap = orderInLocationPalletList.stream().collect(Collectors
            .groupingBy(this::getPalletKey, Collectors.mapping(OrderLocationPalletPO::getOrderId, Collectors.toSet())));

        List<OrderLocationPalletPO> waitSetOrderLocationPalletPOList =
            OrderPalletInfoDTOConvertor.convert(batchTaskCompleteDTO.getOrderPalletInfoDTOS(), batchTaskPO);

        Map<String, Set<Long>> waitSetOrderIdLocationGroupMap =
            waitSetOrderLocationPalletPOList.stream().collect(Collectors.groupingBy(this::getPalletKey,
                Collectors.mapping(OrderLocationPalletPO::getOrderId, Collectors.toSet())));

        for (Map.Entry<String, Set<Long>> waitEntry : waitSetOrderIdLocationGroupMap.entrySet()) {
            Set<Long> set = orderIdLocationGroupMap.get(waitEntry.getKey());
            if (CollectionUtils.isEmpty(set)) {
                orderIdLocationGroupMap.put(waitEntry.getKey(), waitEntry.getValue());
            } else {
                set.addAll(waitEntry.getValue());
            }
        }

        List<OrderLocationPalletPO> totalList = new ArrayList<>();
        totalList.addAll(orderInLocationPalletList);
        totalList.addAll(waitSetOrderLocationPalletPOList);

        Map<String, OrderLocationPalletPO> orderLocationPalletPOMap =
            totalList.stream().collect(Collectors.toMap(this::getPalletKey, v -> v, (v1, v2) -> v1));

        List<OutStockOrderPO> orderList = outStockOrderMapper.findByOrderIds(totalOrderIds);
        Map<Long, OutStockOrderPO> outStockOrderPOMap =
            orderList.stream().collect(Collectors.toMap(OutStockOrderPO::getId, v -> v));

        BigDecimal totalCapacity = BigDecimal.valueOf(palletNoList.size()).multiply(MAC_CAPACITY);

        for (Map.Entry<String, Set<Long>> waitEntry : orderIdLocationGroupMap.entrySet()) {
            List<OutStockOrderPO> outStockOrderPOList =
                waitEntry.getValue().stream().map(outStockOrderPOMap::get).collect(Collectors.toList());
            BigDecimal totalCount = outStockOrderPOList.stream().flatMap(m -> m.getItems().stream()).map(item -> {
                BigDecimal[] count = item.getUnittotalcount().divideAndRemainder(item.getSpecquantity());
                return count[0];
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (totalCount.compareTo(totalCapacity) > 0) {
                LOG.info("超出的订单id为：{}", JSON.toJSONString(waitEntry.getValue()));
                throw new BusinessValidateException("该托盘订单件数总计已超过" + MAC_CAPACITY + "件，请使用其他托盘号");
            }
        }
    }

    private String getPalletKey(OrderLocationPalletPO palletPO) {
        return palletPO.getLocationId() + "-" + palletPO.getPalletNo();
    }

}
