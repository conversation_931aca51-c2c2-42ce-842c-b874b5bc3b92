package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
public class PalletReviewOrderInfoDTO implements Serializable {
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 订单序号
     */
    private Integer orderSequence;
    /**
     * 打包箱数
     */
    private Integer packageCount;

    /**
     * 拣货员
     */
    private String sorterName;

    /**
     * 箱号完整编码
     */
    private List<String> boxCodeNoList;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderSequence() {
        return orderSequence;
    }

    public void setOrderSequence(Integer orderSequence) {
        this.orderSequence = orderSequence;
    }

    public Integer getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(Integer packageCount) {
        this.packageCount = packageCount;
    }

    public String getSorterName() {
        return sorterName;
    }

    public void setSorterName(String sorterName) {
        this.sorterName = sorterName;
    }

    public List<String> getBoxCodeNoList() {
        return boxCodeNoList;
    }

    public void setBoxCodeNoList(List<String> boxCodeNoList) {
        this.boxCodeNoList = boxCodeNoList;
    }
}
