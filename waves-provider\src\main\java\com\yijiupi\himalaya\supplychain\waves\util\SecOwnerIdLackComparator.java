package com.yijiupi.himalaya.supplychain.waves.util;

import java.util.Comparator;

import com.yijiupi.himalaya.supplychain.enums.ProcessOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.util.SecOwnerIdComparator;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/6/5
 */
public class SecOwnerIdLackComparator implements Comparator<OrderItemTaskInfoDetailPO> {

    SecOwnerIdComparator comparator;

    public SecOwnerIdLackComparator(ProcessOrderTypeEnum type) {
        comparator = new SecOwnerIdComparator(type);
    }

    @Override
    public int compare(OrderItemTaskInfoDetailPO bo1, OrderItemTaskInfoDetailPO bo2) {
        if (bo1.getTaskInfoId().equals(bo2.getTaskInfoId())) {
            return 1;
        }
        return comparator.compare(bo1.getSecOwnerId(), bo2.getSecOwnerId());
    }
}
