package com.yijiupi.himalaya.supplychain.waves.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchProgressService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchProgressBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchProgressDTO;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-02-29 15:51
 **/
@Service(timeout = 30000)
public class BatchProgressServiceImpl implements IBatchProgressService {

    @Resource
    private BatchProgressBL batchProgressBL;

    /**
     * 获取指定仓库的分拣进度
     *
     * @param warehouseId 仓库 id
     * @return 该仓库的分拣进度
     */
    @Override
    public List<BatchProgressDTO> getBatchProgresses(Integer warehouseId) {
        return batchProgressBL.getBatchProgress(warehouseId);
    }

}
