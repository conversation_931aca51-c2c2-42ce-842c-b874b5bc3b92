package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter.OrderOutStockNotifyDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.DirectOutStockDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
public class OrderOutStockNotifyConvert {
    public static List<OrderOutStockNotifyDTO> convert(List<OutStockOrderPO> outStockOrderPOList,
                                                       List<DirectOutStockDTO> directOutStockDTOS) {
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            return new ArrayList<>();
        }

        List<OrderOutStockNotifyDTO> notifyDTOList = new ArrayList<>();
        outStockOrderPOList.stream().forEach(outStockOrder -> {
            OrderOutStockNotifyDTO orderOutStockNotifyDTO = new OrderOutStockNotifyDTO();
            orderOutStockNotifyDTO.setOptUserId(String.valueOf(directOutStockDTOS.get(0).getOperateUserId()));
            orderOutStockNotifyDTO.setWarehouseId(outStockOrder.getWarehouseId());
            orderOutStockNotifyDTO.setCityId(outStockOrder.getOrgId());
            orderOutStockNotifyDTO.setOutStockTime(new Date());
            orderOutStockNotifyDTO.setOrderId(Long.valueOf(outStockOrder.getBusinessId()));
            orderOutStockNotifyDTO.setOrderType(outStockOrder.getOrdertype());
            List<OrderOutStockNotifyDTO.OrderItemOutStockDTO> orderItemOutStockList = new ArrayList<>();
            List<OutStockOrderItemPO> outStockOrderItemPS = outStockOrder.getItems();
            for (OutStockOrderItemPO outStockOrderItemPO : outStockOrderItemPS) {
                OrderOutStockNotifyDTO.OrderItemOutStockDTO orderItemNotifyDTO =
                        new OrderOutStockNotifyDTO.OrderItemOutStockDTO();
                orderItemNotifyDTO.setOrderItemId(Long.valueOf(outStockOrderItemPO.getBusinessItemId()));
                orderItemNotifyDTO.setUnitTotalCount(outStockOrderItemPO.getUnittotalcount());
                List<OrderOutStockNotifyDTO.OrderOutStockDealerDTO> orderOutStockDealerList = new ArrayList<>();
                List<OutStockOrderItemDetailPO> outStockOrderItemDetailPOS = outStockOrderItemPO.getItemDetails();
                if (!CollectionUtils.isEmpty(outStockOrderItemDetailPOS)) {
                    for (OutStockOrderItemDetailPO outStockOrderItemDetailPO : outStockOrderItemDetailPOS) {
                        OrderOutStockNotifyDTO.OrderOutStockDealerDTO dealerNotifyDTO =
                                new OrderOutStockNotifyDTO.OrderOutStockDealerDTO();
                        dealerNotifyDTO.setSecOwnerId(outStockOrderItemDetailPO.getSecOwnerId());
                        dealerNotifyDTO.setUnitTotalCount(outStockOrderItemDetailPO.getUnitTotalCount());
                        dealerNotifyDTO.setOwnerId(outStockOrderItemDetailPO.getOwnerId());
                        dealerNotifyDTO
                                .setProductSpecificationId(outStockOrderItemDetailPO.getProductSpecificationId());
                        orderOutStockDealerList.add(dealerNotifyDTO);
                    }
                }
                orderItemNotifyDTO.setOrderOutStockDealerList(orderOutStockDealerList);
                orderItemOutStockList.add(orderItemNotifyDTO);
            }
            orderOutStockNotifyDTO.setOrderItemOutStockList(orderItemOutStockList);
            notifyDTOList.add(orderOutStockNotifyDTO);
        });

        return notifyDTOList;
    }
}
