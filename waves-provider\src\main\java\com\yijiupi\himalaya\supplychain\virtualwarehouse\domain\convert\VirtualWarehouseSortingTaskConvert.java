package com.yijiupi.himalaya.supplychain.virtualwarehouse.domain.convert;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto.VirtualWarehouseSortingTaskDetailsDTO;
import com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto.VirtualWarehouseSortingTaskListDTO;

public class VirtualWarehouseSortingTaskConvert {
    private static Logger LOGGER = LoggerFactory.getLogger(VirtualWarehouseSortingTaskConvert.class);

    public static List<VirtualWarehouseSortingTaskListDTO> convert(List<SowTaskPO> dataList,
        List<SowTaskItemPO> sowTaskItemPOS, List<String> countList) {
        List<VirtualWarehouseSortingTaskListDTO> warehouseSortingTaskListDTOS = new ArrayList<>();
        // 根据集货位id显示
        for (String locationId : countList) {
            try {
                VirtualWarehouseSortingTaskListDTO virtualWarehouseSortingTaskListDTO =
                    new VirtualWarehouseSortingTaskListDTO();
                // 获取该集货位下所有拣货任务
                if (ObjectUtils.isEmpty(locationId)) {
                    continue;
                }
                List<SowTaskPO> collect =
                    dataList.stream().filter(d -> d.getLocationId().compareTo(Long.valueOf(locationId)) == 0)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(collect)) {
                    continue;
                }
                List<Long> sowIdList = collect.stream().map(SowTaskPO::getId).collect(Collectors.toList());
                List<SowTaskItemPO> sowTaskItemList = sowTaskItemPOS.stream()
                    .filter(item -> sowIdList.contains(item.getSowTaskId())).collect(Collectors.toList());
                virtualWarehouseSortingTaskListDTO.setPickLocation(collect.get(0).getLocationName());
                virtualWarehouseSortingTaskListDTO.setProductCount(sowTaskItemList.stream()
                    .collect(Collectors.groupingBy(s -> s.getProductSkuId() + "" + s.getSaleSpecQuantity())).keySet()
                    .size());
                virtualWarehouseSortingTaskListDTO.setLocationId(Long.valueOf(locationId));
                virtualWarehouseSortingTaskListDTO.setUnitCount(
                    sowTaskItemList.stream().map(SowTaskItemPO::getUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add));
                virtualWarehouseSortingTaskListDTO.setPackageCount(sowTaskItemList.stream()
                    .map(SowTaskItemPO::getPackageCount).reduce(BigDecimal.ZERO, BigDecimal::add));
                virtualWarehouseSortingTaskListDTO
                    .setTaskIds(collect.stream().map(SowTaskPO::getId).collect(Collectors.toList()));
                warehouseSortingTaskListDTOS.add(virtualWarehouseSortingTaskListDTO);
            } catch (NumberFormatException e) {
                LOGGER.error("转换参数异常 locationId={}", locationId, e);
                throw new BusinessException("转换参数异常");
            }
        }
        return warehouseSortingTaskListDTOS;
    }

    public static List<VirtualWarehouseSortingTaskDetailsDTO> convert(List<SowTaskItemPO> sowTaskItemPOS,
        List<OutStockOrderItemPO> outStockOrderItemPO, Map<Long, ProductCodeDTO> packageAndUnitCode) {
        List<VirtualWarehouseSortingTaskDetailsDTO> virtualWarehouseSortingTaskDetailsDTOS = new ArrayList<>();

        Map<String, List<SowTaskItemPO>> collect = sowTaskItemPOS.stream()
            .collect(Collectors.groupingBy(s -> s.getProductSkuId() + "-" + s.getSaleSpecQuantity()));
        Map<String, List<OutStockOrderItemPO>> stringListMap = outStockOrderItemPO.stream()
            .collect(Collectors.groupingBy(s -> s.getSkuid() + "-" + s.getSalespecquantity()));
        Set<String> keySet = collect.keySet();
        for (String key : keySet) {
            String[] split = key.split("-");
            ProductCodeDTO productCodeDTO = packageAndUnitCode.get(Long.valueOf((split[0])));
            List<SowTaskItemPO> skuGroupSowTaskItemList = collect.get(key);
            VirtualWarehouseSortingTaskDetailsDTO virtualWarehouseSortingTaskDetailsDTO =
                new VirtualWarehouseSortingTaskDetailsDTO();
            SowTaskItemPO sowTaskItemPO = skuGroupSowTaskItemList.get(0);
            virtualWarehouseSortingTaskDetailsDTO.setSowNo(sowTaskItemPO.getSowTaskNo());
            virtualWarehouseSortingTaskDetailsDTO.setSowId(sowTaskItemPO.getSowTaskId());
            virtualWarehouseSortingTaskDetailsDTO.setBoundNo(outStockOrderItemPO.get(0).getBoundNo());
            virtualWarehouseSortingTaskDetailsDTO.setProductName(sowTaskItemPO.getProductName());
            virtualWarehouseSortingTaskDetailsDTO.setSkuId(sowTaskItemPO.getProductSkuId());
            virtualWarehouseSortingTaskDetailsDTO.setSpecInfo(sowTaskItemPO.getSpecName());
            virtualWarehouseSortingTaskDetailsDTO
                .setTaskState(Integer.valueOf(skuGroupSowTaskItemList.get(0).getState()));
            List<OutStockOrderItemPO> outStockOrderItemPOS = stringListMap.get(key);
            virtualWarehouseSortingTaskDetailsDTO.setToLocationName("0");
            if (!CollectionUtils.isEmpty(outStockOrderItemPOS)) {
                List<Long> longs = outStockOrderItemPOS.stream().filter(s -> !ObjectUtils.isEmpty(s.getLocationId()))
                    .map(OutStockOrderItemPO::getLocationId).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(longs)) {
                    virtualWarehouseSortingTaskDetailsDTO.setToLocationName(String.valueOf(longs.size()));
                }
            }
            virtualWarehouseSortingTaskDetailsDTO.setToLocationId(outStockOrderItemPO.get(0).getLocationId());
            BigDecimal unitTotalCount = Optional
                .ofNullable(skuGroupSowTaskItemList.stream().filter(s -> !ObjectUtils.isEmpty(s.getUnitTotalCount()))
                    .map(SowTaskItemPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add))
                .orElse(BigDecimal.ZERO);
            virtualWarehouseSortingTaskDetailsDTO.setPackageName(sowTaskItemPO.getPackageName());
            virtualWarehouseSortingTaskDetailsDTO.setUnitName(sowTaskItemPO.getUnitName());
            virtualWarehouseSortingTaskDetailsDTO.setLackUnitTotalCount(
                skuGroupSowTaskItemList.stream().filter(s -> !ObjectUtils.isEmpty(s.getLackUnitTotalCount()))
                    .map(SowTaskItemPO::getLackUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add));
            virtualWarehouseSortingTaskDetailsDTO
                .setTaskItemId(skuGroupSowTaskItemList.stream().map(SowTaskItemPO::getId).collect(Collectors.toList()));
            if (unitTotalCount.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal[] bigDecimals = unitTotalCount.divideAndRemainder(sowTaskItemPO.getSpecQuantity());
                virtualWarehouseSortingTaskDetailsDTO.setPackageCount(bigDecimals[0]);
                virtualWarehouseSortingTaskDetailsDTO.setUnitCount(bigDecimals[1]);
            }
            if (!ObjectUtils.isEmpty(productCodeDTO)) {
                virtualWarehouseSortingTaskDetailsDTO.setPackageCode(productCodeDTO.getPackageCode());
                virtualWarehouseSortingTaskDetailsDTO.setUnitCode(productCodeDTO.getUnitCode());
            }
            virtualWarehouseSortingTaskDetailsDTOS.add(virtualWarehouseSortingTaskDetailsDTO);
        }
        return virtualWarehouseSortingTaskDetailsDTOS.stream()
            .sorted(Comparator.comparing(VirtualWarehouseSortingTaskDetailsDTO::getTaskState))
            .collect(Collectors.toList());
    }

    public static List<VirtualWarehouseSortingTaskDetailsDTO> convertItem(List<SowTaskItemPO> sowTaskItemPOS,
        List<OutStockOrderItemPO> outStockOrderItemPOS, Map<Long, Integer> locationSeq) {
        List<VirtualWarehouseSortingTaskDetailsDTO> virtualWarehouseSortingTaskDetailsDTOS = new ArrayList<>();
        for (SowTaskItemPO sowTaskItemPO : sowTaskItemPOS) {
            OutStockOrderItemPO outStockOrderItemPO = outStockOrderItemPOS.stream()
                .filter(o -> o.getSowTaskItemId().compareTo(sowTaskItemPO.getId()) == 0
                    && o.getSkuid().compareTo(sowTaskItemPO.getProductSkuId()) == 0)
                .findFirst().orElse(new OutStockOrderItemPO());
            VirtualWarehouseSortingTaskDetailsDTO virtualWarehouseSortingTaskDetailsDTO =
                new VirtualWarehouseSortingTaskDetailsDTO();
            List<Long> taskItemId = new ArrayList<>();
            taskItemId.add(sowTaskItemPO.getId());
            virtualWarehouseSortingTaskDetailsDTO.setOrderItemId(outStockOrderItemPO.getId());
            virtualWarehouseSortingTaskDetailsDTO.setTaskItemId(taskItemId);
            virtualWarehouseSortingTaskDetailsDTO.setProductName(sowTaskItemPO.getProductName());
            virtualWarehouseSortingTaskDetailsDTO.setSkuId(sowTaskItemPO.getProductSkuId());
            virtualWarehouseSortingTaskDetailsDTO.setSowId(sowTaskItemPO.getSowTaskId());
            virtualWarehouseSortingTaskDetailsDTO.setSowNo(sowTaskItemPO.getSowTaskNo());
            virtualWarehouseSortingTaskDetailsDTO.setBoundNo(outStockOrderItemPO.getBoundNo());
            virtualWarehouseSortingTaskDetailsDTO.setSpecInfo(sowTaskItemPO.getSpecName());
            virtualWarehouseSortingTaskDetailsDTO.setToLocationName(outStockOrderItemPO.getLocationName());
            virtualWarehouseSortingTaskDetailsDTO.setToLocationId(outStockOrderItemPO.getLocationId());
            virtualWarehouseSortingTaskDetailsDTO.setTaskState(0);
            if (sowTaskItemPO.getState().compareTo(new Byte("2")) == 0) {
                virtualWarehouseSortingTaskDetailsDTO.setTaskState(1);
            }
            BigDecimal unitTotalCount = Optional.ofNullable(sowTaskItemPO.getUnitTotalCount()).orElse(BigDecimal.ZERO);
            virtualWarehouseSortingTaskDetailsDTO.setPackageCount(BigDecimal.ZERO);
            virtualWarehouseSortingTaskDetailsDTO.setUnitCount(BigDecimal.ZERO);
            virtualWarehouseSortingTaskDetailsDTO.setLackUnitTotalCount(sowTaskItemPO.getLackUnitTotalCount());
            if (unitTotalCount.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal[] bigDecimals = unitTotalCount.divideAndRemainder(sowTaskItemPO.getSpecQuantity());
                virtualWarehouseSortingTaskDetailsDTO.setPackageCount(bigDecimals[0]);
                virtualWarehouseSortingTaskDetailsDTO.setUnitCount(bigDecimals[1]);
            }
            virtualWarehouseSortingTaskDetailsDTO.setPackageName(outStockOrderItemPO.getPackagename());
            virtualWarehouseSortingTaskDetailsDTO.setUnitName(outStockOrderItemPO.getUnitname());
            virtualWarehouseSortingTaskDetailsDTO.setSaleSpecQuality(outStockOrderItemPO.getSalespecquantity());
            virtualWarehouseSortingTaskDetailsDTO.setSpecQuality(outStockOrderItemPO.getSpecquantity());
            if (outStockOrderItemPO.getLocationId() != null) {
                virtualWarehouseSortingTaskDetailsDTO
                    .setToLocationSequence(locationSeq.get(outStockOrderItemPO.getLocationId()));
            }
            virtualWarehouseSortingTaskDetailsDTOS.add(virtualWarehouseSortingTaskDetailsDTO);
            if (outStockOrderItemPO.getLocationId() != null) {
                virtualWarehouseSortingTaskDetailsDTO
                    .setToLocationSequence(locationSeq.get(outStockOrderItemPO.getLocationId()));
            }
        }

        virtualWarehouseSortingTaskDetailsDTOS.sort((o1, o2) -> {
            if (Objects.equals(o1.getToLocationSequence(), o2.getToLocationSequence())) {
                if (StringUtils.isEmpty(o1.getToLocationName())) {
                    return 1;
                } else if (StringUtils.isEmpty(o2.getToLocationName())) {
                    return -1;
                } else {
                    return o1.getToLocationName().compareTo(o2.getToLocationName());
                }
            } else {
                if (o1.getToLocationSequence() == null) {
                    return 1;
                } else if (o2.getToLocationSequence() == null) {
                    return -1;
                } else {
                    return o1.getToLocationSequence().compareTo(o2.getToLocationSequence());
                }
            }
        });
        return virtualWarehouseSortingTaskDetailsDTOS;
    }
}
