package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo.OutStockOrderAdminCancelHandlePickBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo.OutStockOrderAdminCancelHandlePickResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.ordercenter.OrderCenterNotifyBL;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskItemUpdateDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskUpdateDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;

/**
 * 注意一个订单项拆成两个拣货任务
 *
 * <AUTHOR>
 * @since 2023-05-25 09:06
 */
@Service
public class OutStockOrderWaitPickCancelBL extends OutStockOrderAdminCancelBaseBL {

    @Resource
    private OrderCenterNotifyBL orderCenterNotifyBL;

    private static final Logger logger = LoggerFactory.getLogger(OutStockOrderWaitPickCancelBL.class);

    @Override
    public boolean support(OutStockOrderAdminCancelHandlePickBO bo) {
        if (OutStockOrderStateEnum.待拣货.getType() == bo.getOutStockOrderPO().getState()) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    // 多个订单一起调度，没有拣货，取消了其中订单，取消的订单还在波次里面还有待出库批次
    @Override
    public boolean baseSupport(OutStockOrderAdminCancelHandlePickBO bo) {
        if (Objects.isNull(bo.getBatchPO())) {
            return Boolean.TRUE;
        }
        byte batchState = bo.getBatchPO().getState();
        if (BatchStateEnum.PENDINGPICKING.getType().byteValue() == batchState) {
            return Boolean.TRUE;
        }
        if (OutStockOrderStateEnum.待拣货.getType() == bo.getOutStockOrderPO().getState()) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    protected OutStockOrderAdminCancelHandlePickResultBO doCancel(OutStockOrderAdminCancelHandlePickBO bo) {
        List<OrderItemTaskInfoPO> taskInfos = bo.getOrderItemTaskInfoList();
        if (CollectionUtils.isEmpty(taskInfos)) {
            return null;
        }
        List<String> batchTaskItemIdList = taskInfos.stream().map(OrderItemTaskInfoPO::getBatchTaskItemId)
                .collect(Collectors.toList());
        // 获取拣货任务明细
        List<BatchTaskItemPO> items = batchTaskItemMapper.selectBatchTaskItemByIds(batchTaskItemIdList);
        List<String> batchTaskIds = taskInfos.stream().map(OrderItemTaskInfoPO::getBatchTaskId).distinct()
            .collect(Collectors.toList());
        // 获取拣货任务
        List<BatchTaskPO> tasks = batchTaskMapper.findTasksByBatchTaskId(batchTaskIds);
        boolean haveNotPick = tasks.stream().anyMatch(m -> TaskStateEnum.未分拣.getType() != m.getTaskState());
        if (BooleanUtils.isTrue(haveNotPick)) {
            return OutStockOrderAdminCancelHandlePickResultBO.newInstance();
        }
        OutStockOrderAdminCancelHandlePickResultBO resultBO = removeBathTask(taskInfos, bo, items, tasks);
        removeSowTask(bo.getOutStockOrderPO(), resultBO);

        clearOutStockOrderBatchInfo(bo.getOutStockOrderPO());
        return resultBO;
    }

    private OutStockOrderAdminCancelHandlePickResultBO removeBathTask(List<OrderItemTaskInfoPO> orderItemTaskInfoList,
        OutStockOrderAdminCancelHandlePickBO bo, List<BatchTaskItemPO> batchTaskItemList,
        List<BatchTaskPO> batchTaskList) {
        OutStockOrderAdminCancelHandlePickResultBO resultBO = new OutStockOrderAdminCancelHandlePickResultBO();
        Set<String> batchNos =
            orderItemTaskInfoList.stream().map(OrderItemTaskInfoPO::getBatchNo).collect(Collectors.toSet());
        List<BatchPO> batchList = batchMapper.listByBatchNos(batchNos);
        List<String> batchNoList = batchList.stream().map(BatchPO::getBatchNo).distinct().collect(Collectors.toList());
        if (batchNoList.isEmpty()) {
            logger.info("找不到波次: {}, {}", batchNos, JSON.toJSONString(orderItemTaskInfoList));
            return resultBO;
        }

        List<Long> outStockOrderItemIds = bo.getOutStockOrderPO().getItems().stream().map(OutStockOrderItemPO::getId)
            .distinct().collect(Collectors.toList());
        List<OrderItemTaskInfoPO> cancelOrderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(outStockOrderItemIds);
        if (CollectionUtils.isEmpty(cancelOrderItemTaskInfoPOList)) {
            logger.info("未找到拣货任务订单关联信息，不处理，订单号：{}", bo.getOutStockOrderPO().getReforderno());
            return resultBO;
        }

        List<OutStockOrderPO> outStockOrderPOList =
            outStockOrderMapper.findByBatchNos(batchNoList, bo.getOutStockOrderPO().getOrgId());
        outStockOrderPOList.removeIf(m -> m.getReforderno().equals(bo.getOutStockOrderPO().getReforderno()));
        // 所有的明细项，用于计算数量
        List<OrderItemTaskInfoPO> allOrderItemTaskInfoList = orderItemTaskInfoMapper.listTaskInfoByBatchTaskIds(
            batchTaskList.stream().map(BatchTaskPO::getId).distinct().collect(Collectors.toList()));
        // allOrderItemTaskInfoList.removeIf(m -> m.getRefOrderId().equals(bo.getOutStockOrderPO().getId()));
        List<OrderItemTaskInfoPO> leftOrderItemTaskInfoList = allOrderItemTaskInfoList.stream()
            .filter(m -> !m.getRefOrderId().equals(bo.getOutStockOrderPO().getId())).collect(Collectors.toList());

        // 所有的出库单明细项
        List<Long> outStockOrderIds = leftOrderItemTaskInfoList.stream().map(OrderItemTaskInfoPO::getRefOrderId)
            .distinct().collect(Collectors.toList());
        List<OutStockOrderItemPO> allOutStockOrderItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(outStockOrderIds)) {
            allOutStockOrderItemList.addAll(outStockOrderItemMapper.findByOutstockorderIdList(outStockOrderIds));
        }
        Map<Long, OutStockOrderItemPO> allOutStockOrderItemMap =
            allOutStockOrderItemList.stream().collect(Collectors.toMap(OutStockOrderItemPO::getId, v -> v));
        // 移除不在allOutStockOrderItemList中的订单，因为删除了出库单，这个OrderItemTaskInfo没有删除，所以当一个拣货任务中有两个以上被取消的时候，会有历史残留的已删除出库单的数据
        // allOrderItemTaskInfoList.removeIf(m -> Objects.isNull(allOutStockOrderItemMap.get(m.getRefOrderItemId())));
        leftOrderItemTaskInfoList = leftOrderItemTaskInfoList.stream()
            .filter(m -> Objects.nonNull(allOutStockOrderItemMap.get(m.getRefOrderItemId())))
            .collect(Collectors.toList());

        Map<String, List<OrderItemTaskInfoPO>> allOrderItemTaskInfoGroupByTaskMap =
            leftOrderItemTaskInfoList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getBatchTaskId));
        Map<String, List<OrderItemTaskInfoPO>> allOrderItemTaskInfoGroupByTaskItemMap =
            leftOrderItemTaskInfoList.stream().collect(Collectors.groupingBy(OrderItemTaskInfoPO::getBatchTaskItemId));
        List<BatchTaskItemUpdatePO> updateBatchTaskItemList = batchTaskItemList.stream().map(m -> {
            List<OrderItemTaskInfoPO> inOrderItemTaskInfoList = allOrderItemTaskInfoGroupByTaskItemMap.get(m.getId());
            BigDecimal packageCount = getPackageCount(inOrderItemTaskInfoList, allOutStockOrderItemMap);
            BigDecimal unitCount = getUnitCount(inOrderItemTaskInfoList, allOutStockOrderItemMap);
            BigDecimal unitTotalCount = getUnitTotalCount(inOrderItemTaskInfoList, allOutStockOrderItemMap);
            BatchTaskItemUpdatePO updateBatchTaskItemPO = new BatchTaskItemUpdatePO();
            updateBatchTaskItemPO.setId(m.getId());
            updateBatchTaskItemPO.setPackageCount(packageCount);
            updateBatchTaskItemPO.setUnitCount(unitCount);
            updateBatchTaskItemPO.setUnitTotalCount(unitTotalCount);
            updateBatchTaskItemPO.setOverSortCount(BigDecimal.ZERO);
            updateBatchTaskItemPO.setTaskState(m.getTaskState());
            updateBatchTaskItemPO.setIsLack(m.getIsLack());
            updateBatchTaskItemPO.setLastUpdateUser(bo.getOpUser());
            m.setPackageCount(packageCount);
            m.setUnitCount(unitCount);
            m.setUnitTotalCount(unitTotalCount);
            return updateBatchTaskItemPO;
        }).collect(Collectors.toList());
        resultBO.setUpdateBatchTaskItemList(updateBatchTaskItemList);
        // 更新拣货任务明细
        // batchTaskItemMapper.updateBatchTaskByItem(updateBatchTaskItemList);
        List<String> deleteBatchTaskItemIds =
            batchTaskItemList.stream().filter(m -> BigDecimal.ZERO.compareTo(m.getUnitTotalCount()) == 0)
                .map(BatchTaskItemPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteBatchTaskItemIds)) {
            resultBO.setDeleteBatchTaskItemIds(deleteBatchTaskItemIds);
        }
        List<BatchTaskPO> updateBatchTaskList = batchTaskList.stream().map(m -> {
            List<OrderItemTaskInfoPO> inBatchTaskOrderItemTaskInfoList =
                allOrderItemTaskInfoGroupByTaskMap.get(m.getId());
            BigDecimal packageCount = getPackageCount(inBatchTaskOrderItemTaskInfoList, allOutStockOrderItemMap);
            BigDecimal unitCount = getUnitCount(inBatchTaskOrderItemTaskInfoList, allOutStockOrderItemMap);
            int skuCount = 0;
            if (!CollectionUtils.isEmpty(inBatchTaskOrderItemTaskInfoList)) {
                skuCount = (int)inBatchTaskOrderItemTaskInfoList.stream()
                    .map(orderItemTaskInfo -> allOutStockOrderItemMap.get(orderItemTaskInfo.getRefOrderItemId()))
                    .map(OutStockOrderItemPO::getSkuid).distinct().count();
            }
            int orderCount = m.getOrderCount() - 1;
            BatchTaskPO updateBatchTask = new BatchTaskPO();
            updateBatchTask.setId(m.getId());
            updateBatchTask.setPackageAmount(packageCount);
            updateBatchTask.setUnitAmount(unitCount);
            updateBatchTask.setSkuCount(skuCount);
            updateBatchTask.setOrderCount(orderCount);
            m.setPackageAmount(packageCount);
            m.setUnitAmount(unitCount);
            m.setSkuCount(skuCount);
            m.setOrderCount(orderCount);
            return updateBatchTask;
        }).collect(Collectors.toList());
        resultBO.setUpdateBatchTaskList(updateBatchTaskList);
        // updateBatchTaskList.forEach(batchTask -> batchTaskMapper.updateByPrimaryKeySelective(batchTask));
        List<String> deleteBatchTaskIds = batchTaskList.stream().filter(m -> m.getOrderCount() == 0)
            .map(BatchTaskPO::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteBatchTaskIds)) {
            resultBO.setDeleteBatchTaskIds(deleteBatchTaskIds);
            // batchTaskMapper.deleteByBatchTaskId(deleteBatchTaskIds);
        }
        List<OrderItemTaskInfoPO> updateOrderItemTaskInfoPOList = orderItemTaskInfoList.stream().map(m -> {
            OrderItemTaskInfoPO updateOrderItemTaskInfoPO = new OrderItemTaskInfoPO();
            updateOrderItemTaskInfoPO.setId(m.getId());
            updateOrderItemTaskInfoPO.setUnitTotalCount(BigDecimal.ZERO);
            return updateOrderItemTaskInfoPO;
        }).collect(Collectors.toList());
        resultBO.setUpdateOrderItemTaskInfoList(updateOrderItemTaskInfoPOList);
        List<OrderItemTaskInfoDetailPO> orderItemTaskInfoDetailList =
            orderItemTaskInfoList.stream().flatMap(m -> m.getDetailList().stream()).collect(Collectors.toList());
        List<OrderItemTaskInfoDetailPO> updateOrderItemTaskInfoDetailList =
            orderItemTaskInfoDetailList.stream().map(m -> {
                OrderItemTaskInfoDetailPO orderItemTaskInfoDetailPO = new OrderItemTaskInfoDetailPO();
                orderItemTaskInfoDetailPO.setId(m.getId());
                orderItemTaskInfoDetailPO.setUnitTotalCount(BigDecimal.ZERO);
                return orderItemTaskInfoDetailPO;
            }).collect(Collectors.toList());
        resultBO.setUpdateOrderItemTaskInfoDetailList(updateOrderItemTaskInfoDetailList);
        Map<String, List<OutStockOrderPO>> outStockGroupByBatchMap =
            outStockOrderPOList.stream().collect(Collectors.groupingBy(OutStockOrderPO::getBatchno));
        List<BatchPO> updateBatchList = batchList.stream().map(m -> {
            List<OutStockOrderPO> outStockOrderInBatchList = outStockGroupByBatchMap.get(m.getBatchNo());
            BatchPO updateBatchPO = new BatchPO();
            BigDecimal packageAmount = BigDecimal.ZERO;
            BigDecimal unitAmount = BigDecimal.ZERO;
            int skuCount = 0;
            int orderCount = 0;
            if (CollectionUtils.isNotEmpty(outStockOrderInBatchList)) {
                LOG.info("剩余订单数量为：{}", JSON.toJSONString(outStockOrderInBatchList.stream()
                    .map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList())));
                outStockOrderInBatchList.forEach(order -> {
                    order.setPackageamount(order.getItems().stream().map(OutStockOrderItemPO::getPackagecount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                    order.setUnitamount(order.getItems().stream().map(OutStockOrderItemPO::getUnitcount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                });
                packageAmount = outStockOrderInBatchList.stream().map(OutStockOrderPO::getPackageamount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                unitAmount = outStockOrderInBatchList.stream().map(OutStockOrderPO::getUnitamount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                skuCount = (int)outStockOrderInBatchList.stream().flatMap(order -> order.getItems().stream())
                    .map(OutStockOrderItemPO::getSkuid).distinct().count();
                orderCount = (int)outStockOrderInBatchList.stream().map(OutStockOrderPO::getId).distinct().count();
            }
            updateBatchPO.setId(m.getId());
            updateBatchPO.setOrderCount(orderCount);
            updateBatchPO.setPackageAmount(packageAmount);
            updateBatchPO.setUnitAmount(unitAmount);
            updateBatchPO.setSkuCount(skuCount);
            m.setOrderCount(orderCount);
            m.setPackageAmount(packageAmount);
            m.setUnitAmount(unitAmount);
            m.setSkuCount(skuCount);
            return updateBatchPO;
        }).collect(Collectors.toList());
        resultBO.setUpdateBatchList(updateBatchList);
        List<String> deleteBatchIds =
            batchList.stream().filter(m -> m.getOrderCount() == 0).map(BatchPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteBatchIds)) {
            resultBO.setDeleteBatchIds(deleteBatchIds);
        }

        // 删除orderitemtaskinfo信息
        List<Long> deleteOrderItemTaskInfoIds = cancelOrderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getId)
            .distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cancelOrderItemTaskInfoPOList)) {
            resultBO.setDeleteOrderItemTaskInfoIds(deleteOrderItemTaskInfoIds);
        }
        List<OrderItemTaskInfoDetailPO> deleteOrderItemTaskInfoDetailList = cancelOrderItemTaskInfoPOList.stream()
            .flatMap(m -> m.getDetailList().stream()).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteOrderItemTaskInfoDetailList)) {
            List<Long> deleteOrderItemTaskInfoDetailIds = deleteOrderItemTaskInfoDetailList.stream()
                .map(OrderItemTaskInfoDetailPO::getId).distinct().collect(Collectors.toList());
            resultBO.setDeleteOrderItemTaskInfoDetailIds(deleteOrderItemTaskInfoDetailIds);
        }

        // List<OutStockOrderPO> boundOutStockList =
        // outStockOrderMapper.findByOutBoundNoList(Collections.singletonList(outStockOrderPO.getBoundNo()),
        // outStockOrderPO.getOrgId());
        // boundOutStockList.removeIf(m -> m.getReforderno().equals(outStockOrderPO.getReforderno()));
        // OutBoundBatchDTO outBoundBatchDTO = new OutBoundBatchDTO();
        // outBoundBatchDTO.setBoundBatchNo(outStockOrderPO.getBoundNo());
        // outBoundBatchDTO.setOrderCount(boundOutStockList.size());
        // BigDecimal packageCount = boundOutStockList.stream().map(OutStockOrderPO ::
        // getPackageamount).reduce(BigDecimal.ZERO, BigDecimal :: add);
        // BigDecimal unitCount = boundOutStockList.stream().map(OutStockOrderPO ::
        // getUnitamount).reduce(BigDecimal.ZERO, BigDecimal :: add);
        // outBoundBatchDTO.setPackageCount(packageCount);
        // outBoundBatchDTO.setUnitCount(unitCount);
        //
        // resultBO.setUpdateOutBoundBatchDTO(outBoundBatchDTO);
        return resultBO;
    }

    private BigDecimal getUnitTotalCount(List<OrderItemTaskInfoPO> inBatchTaskOrderItemTaskInfoList,
        Map<Long, OutStockOrderItemPO> allOutStockOrderItemMap) {
        if (CollectionUtils.isEmpty(inBatchTaskOrderItemTaskInfoList)) {
            return BigDecimal.ZERO;
        }

        return inBatchTaskOrderItemTaskInfoList.stream().map(OrderItemTaskInfoPO::getUnitTotalCount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal getPackageCount(List<OrderItemTaskInfoPO> inBatchTaskOrderItemTaskInfoList,
        Map<Long, OutStockOrderItemPO> allOutStockOrderItemMap) {
        if (CollectionUtils.isEmpty(inBatchTaskOrderItemTaskInfoList)) {
            return BigDecimal.ZERO;
        }

        return inBatchTaskOrderItemTaskInfoList.stream().map(orderItemTaskInfo -> {
            OutStockOrderItemPO outStockOrderItemPO =
                allOutStockOrderItemMap.get(orderItemTaskInfo.getRefOrderItemId());
            return orderItemTaskInfo.getUnitTotalCount().divideAndRemainder(outStockOrderItemPO.getSpecquantity())[0];
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal getUnitCount(List<OrderItemTaskInfoPO> inBatchTaskOrderItemTaskInfoList,
        Map<Long, OutStockOrderItemPO> allOutStockOrderItemMap) {
        if (CollectionUtils.isEmpty(inBatchTaskOrderItemTaskInfoList)) {
            return BigDecimal.ZERO;
        }

        return inBatchTaskOrderItemTaskInfoList.stream().map(orderItemTaskInfo -> {
            OutStockOrderItemPO outStockOrderItemPO =
                allOutStockOrderItemMap.get(orderItemTaskInfo.getRefOrderItemId());
            return orderItemTaskInfo.getUnitTotalCount().divideAndRemainder(outStockOrderItemPO.getSpecquantity())[1];
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private OutStockOrderAdminCancelHandlePickResultBO removeSowTask(OutStockOrderPO outStockOrder,
        OutStockOrderAdminCancelHandlePickResultBO resultBO) {
        List<Long> sowTaskIds =
            outStockOrder.getItems().stream().map(OutStockOrderItemPO::getSowTaskId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sowTaskIds)) {
            return resultBO;
        }

        Map<Long, List<OutStockOrderItemPO>> outStockItemGroupMap =
            outStockOrder.getItems().stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid));

        List<SowTaskPO> sowTaskList = sowTaskMapper.findAllSowTaskByIds(outStockOrder.getOrgId(), sowTaskIds);
        // TODO 这里有问题
        List<SowTaskItemUpdateDTO> sowTaskItemUpdateDTOS =
            sowTaskList.stream().flatMap(m -> m.getSowTaskItemPOS().stream())
                .filter(m -> CollectionUtils.isNotEmpty(outStockItemGroupMap.get(m.getProductSkuId()))).map(item -> {
                    List<OutStockOrderItemPO> outStockOrderItemList = outStockItemGroupMap.get(item.getProductSkuId());
                    BigDecimal unitTotalCount = outStockOrderItemList.stream()
                        .map(OutStockOrderItemPO::getUnittotalcount).reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal[] count = unitTotalCount.divideAndRemainder(item.getSpecQuantity());

                    SowTaskItemUpdateDTO updateSowTaskItemPO = new SowTaskItemUpdateDTO();
                    updateSowTaskItemPO.setSowTaskItemId(item.getId());
                    updateSowTaskItemPO.setPackageCount(item.getPackageCount().subtract(count[0]));
                    updateSowTaskItemPO.setUnitCount(item.getUnitCount().subtract(count[1]));
                    updateSowTaskItemPO.setUnitTotalCount(item.getUnitTotalCount().subtract(unitTotalCount));

                    item.setPackageCount(updateSowTaskItemPO.getPackageCount());
                    item.setUnitCount(updateSowTaskItemPO.getUnitCount());
                    item.setUnitTotalCount(updateSowTaskItemPO.getUnitTotalCount());

                    return updateSowTaskItemPO;
                }).collect(Collectors.toList());

        // sowTaskItemMapper.batchUpdateItem(sowTaskItemUpdateDTOS);
        resultBO.setSowTaskItemUpdateDTOS(sowTaskItemUpdateDTOS);

        List<Long> sowTaskItemIds = sowTaskList.stream().flatMap(m -> m.getSowTaskItemPOS().stream())
            .filter(m -> BigDecimal.ZERO.compareTo(m.getUnitTotalCount()) == 0).map(SowTaskItemPO::getId)
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sowTaskItemIds)) {
            // sowTaskItemMapper.deleteByIds(sowTaskItemIds, outStockOrder.getOrgId());
            resultBO.setDeleteSowTaskItemIds(sowTaskItemIds);
        }

        List<SowTaskUpdateDTO> updateSowTaskList = sowTaskList.stream().map(m -> {
            SowTaskUpdateDTO updateSowTask = new SowTaskUpdateDTO();
            updateSowTask.setSowTaskId(m.getId());
            BigDecimal packageCount = m.getSowTaskItemPOS().stream().map(SowTaskItemPO::getPackageCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal unitCount = m.getSowTaskItemPOS().stream().map(SowTaskItemPO::getUnitCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            int skuCount = (int)getSowTaskSkuIdCount(m.getSowTaskItemPOS());
            int orderCount = m.getOrderCount() - 1;
            updateSowTask.setPackageAmount(packageCount);
            updateSowTask.setUnitAmount(unitCount);
            updateSowTask.setSkuCount(skuCount);
            updateSowTask.setOrderCount(orderCount);
            m.setPackageAmount(packageCount);
            m.setUnitAmount(unitCount);
            m.setSkuCount(skuCount);
            m.setOrderCount(orderCount);

            return updateSowTask;
        }).collect(Collectors.toList());

        // sowTaskMapper.batchUpdateCount(updateSowTaskList);
        resultBO.setUpdateSowTaskList(updateSowTaskList);

        List<Long> deleteSowTaskIds =
            sowTaskList.stream().filter(m -> m.getSkuCount() == 0).map(SowTaskPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteSowTaskIds)) {
            resultBO.setDeleteSowTaskIds(deleteSowTaskIds);
            // sowTaskMapper.deleteBySowTaskIds(deleteSowTaskIds, outStockOrder.getOrgId());
        }

        List<String> outStockOrderIds = Collections.singletonList(String.valueOf(outStockOrder.getId()));
        List<SowOrderDTO> sowOrderList = sowOrderMapper.findByOrderIds(outStockOrderIds);
        if (CollectionUtils.isEmpty(sowOrderList)) {
            return resultBO;
        }

        List<Long> sowOrderIds = sowOrderList.stream().map(SowOrderDTO::getId).collect(Collectors.toList());
        // sowOrderMapper.deleteByIds(sowOrderIds, outStockOrder.getOrgId());
        resultBO.setDeleteSowOrderIds(sowOrderIds);

        return resultBO;
    }

    private long getSowTaskSkuIdCount(List<SowTaskItemPO> sowTaskItemList) {
        return sowTaskItemList.stream().filter(m -> BigDecimal.ZERO.compareTo(m.getUnitTotalCount()) != 0)
            .map(SowTaskItemPO::getProductSkuId).distinct().count();
    }

    // 清空待拣货出库单上的波次信息
    private void clearOutStockOrderBatchInfo(OutStockOrderPO outStockOrderPO) {

        List<Long> orderIds = Collections.singletonList(outStockOrderPO.getId());
        List<Long> orderItemIds =
            outStockOrderPO.getItems().stream().map(OutStockOrderItemPO::getId).distinct().collect(Collectors.toList());

        outStockOrderMapper.clearBatchInfo(orderIds);
        outStockOrderItemMapper.clearBatchInfo(orderItemIds);

        // 添加trace
        orderCenterNotifyBL.clearOutStockOrderBatchInfoTrace(outStockOrderPO);
    }
}
