package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IThirdPartyOutStockService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.thirdparty.InStockWavesUpdateDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.thirdparty.StockOutApplyResultDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.thirdparty.StockOutItemApplyResultDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskStateEnum;

@Service
public class ThirdPartyOutStockBL {
    private static final Logger LOG = LoggerFactory.getLogger(ThirdPartyOutStockBL.class);

    @Reference
    private IThirdPartyOutStockService iThirdPartyOutStockService;

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;

    /**
     * 获取第三方出库申请单
     * 
     * @return
     */
    public List<OutStockOrderPO> getOutStockOrderByThirdParty(List<String> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return null;
        }
        List<Long> ids = orderIdList.stream().map(Long::valueOf).collect(Collectors.toList());
        List<StockOutApplyResultDTO> stockOutApplyList = iThirdPartyOutStockService.findStockOutApply(ids);
        LOG.info("获取第三方出库申请单: {}", JSON.toJSONString(stockOutApplyList));
        if (CollectionUtils.isEmpty(stockOutApplyList)) {
            throw new BusinessValidateException("没有满足条件的第三方申请单，请刷新重试或者联系技术支持！");
        }

        // 过滤出未分拣的订单
        stockOutApplyList =
            stockOutApplyList.stream().filter(p -> Objects.equals(p.getBatchTaskState(), TaskStateEnum.未分拣.getType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockOutApplyList)) {
            throw new BusinessValidateException("不存在待拣货状态的第三方申请单，请刷新重试或者联系技术支持！");
        }

        List<OutStockOrderPO> outStockOrderPOList = new ArrayList<>();
        stockOutApplyList.forEach(stockOutApply -> {
            OutStockOrderPO orderPO = getOutStockOrderPO(stockOutApply);
            if (orderPO == null) {
                return;
            }
            outStockOrderPOList.add(orderPO);
        });
        LOG.info("第三方出库申请单格式化: {}", JSON.toJSONString(outStockOrderPOList));
        return outStockOrderPOList;
    }

    /**
     * 获取订单
     * 
     * @param stockDTO
     * @return
     */
    private OutStockOrderPO getOutStockOrderPO(StockOutApplyResultDTO stockDTO) {
        if (stockDTO == null) {
            return null;
        }
        OutStockOrderPO orderPO = new OutStockOrderPO();
        orderPO.setId(Long.valueOf(stockDTO.getId()));
        orderPO.setReforderno(stockDTO.getNoteNo());
        orderPO.setWarehouseId(stockDTO.getFromWarehouseId());
        orderPO.setState(stockDTO.getBatchTaskState() != null ? Integer.valueOf(stockDTO.getBatchTaskState()) : null);
        orderPO.setOrdertype(Integer.valueOf(OutStockOrderTypeEnum.第三方出库.getType()));
        orderPO.setOrderamount(BigDecimal.ZERO);
        orderPO.setPackageamount(new BigDecimal(stockDTO.getSpecificationscount()));
        orderPO.setUnitamount(new BigDecimal(stockDTO.getUnitcount()));
        orderPO.setSkucount(stockDTO.getProductCount());
        // 订单项
        if (!CollectionUtils.isEmpty(stockDTO.getItems())) {
            // 获取skuid信息
            List<Long> skuIds = stockDTO.getItems().stream().filter(p -> p != null && p.getProductSkuId() != null)
                .map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
            Map<Long, ProductSkuDTO> productSkuMap = iProductSkuQueryService.findBySkuWithMap(skuIds);

            List<OutStockOrderItemPO> itemPOList = new ArrayList<>();
            stockDTO.getItems().forEach(item -> {
                ProductSkuDTO skuDTO = productSkuMap != null ? productSkuMap.get(item.getProductSkuId()) : null;
                OutStockOrderItemPO itemPO = getOutStockOrderItemPO(item, skuDTO);
                if (itemPO == null) {
                    return;
                }
                itemPOList.add(itemPO);
            });
            if (!CollectionUtils.isEmpty(itemPOList)) {
                orderPO.setItems(itemPOList);
            }
        }
        return orderPO;
    }

    /**
     * 获取订单项
     * 
     * @param itemDTO
     * @return
     */
    private OutStockOrderItemPO getOutStockOrderItemPO(StockOutItemApplyResultDTO itemDTO, ProductSkuDTO skuDTO) {
        if (itemDTO == null) {
            return null;
        }
        OutStockOrderItemPO itemPO = new OutStockOrderItemPO();
        itemPO.setId(Long.valueOf(itemDTO.getItemId()));
        itemPO.setOutstockorderId(Long.valueOf(itemDTO.getNoteId()));
        itemPO.setSkuid(itemDTO.getProductSkuId());
        itemPO.setProductname(skuDTO != null ? skuDTO.getName() : itemDTO.getProductName());
        itemPO.setProductbrand(skuDTO != null ? skuDTO.getProductBrand() : itemDTO.getBrandName());
        itemPO.setCategoryname(skuDTO != null ? skuDTO.getStatisticsClass() : itemDTO.getCategory());
        itemPO.setSpecname(skuDTO != null ? skuDTO.getSpecificationName() : itemDTO.getSpecificationText());
        itemPO.setSpecquantity(
            skuDTO != null ? skuDTO.getPackageQuantity() : new BigDecimal(itemDTO.getPackagequantity()));
        itemPO.setSalespec(itemPO.getSpecname());
        itemPO.setSalespecquantity(itemPO.getSpecquantity());
        itemPO.setPackagename(skuDTO != null ? skuDTO.getPackageName() : itemDTO.getSpecification());
        itemPO.setPackagecount(new BigDecimal(itemDTO.getSpecificationscount()));
        itemPO.setUnitname(skuDTO != null ? skuDTO.getUnitName() : itemDTO.getUnit());
        itemPO.setUnitcount(new BigDecimal(itemDTO.getUnitcount()));
        itemPO.setUnittotalcount(new BigDecimal(itemDTO.getCount()));
        itemPO.setChannel(null != itemDTO.getProductChannel() ? itemDTO.getProductChannel().byteValue() : (byte)0);
        itemPO.setSource(null != itemDTO.getProductSource() ? itemDTO.getProductSource().byteValue() : (byte)0);
        itemPO.setProductSpecificationId(
            skuDTO != null ? skuDTO.getProductSpecificationId() : Long.valueOf(itemDTO.getProductSpecificationId()));
        return itemPO;
    }

    /**
     * 修改出库申请单状态
     */
    public void updateOrderByThirdParty(List<Long> orderIds, String batchId, String batchNo, Byte state) {
        // 修改微酒订单状态
        InStockWavesUpdateDTO inStockWavesUpdateDTO = new InStockWavesUpdateDTO();
        inStockWavesUpdateDTO.setIds(orderIds.stream().map(p -> p.toString()).collect(Collectors.toList()));
        inStockWavesUpdateDTO.setBatchId(batchId);
        inStockWavesUpdateDTO.setBatchNO(batchNo);
        inStockWavesUpdateDTO.setBatchTaskState(state);
        iThirdPartyOutStockService.updateBatchInStockOutApply(inStockWavesUpdateDTO);
        LOG.info("修改第三方待出库申请单状态: {}", JSON.toJSONString(inStockWavesUpdateDTO));
    }

}
