package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CountBoundCalculateBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.LocationSplitHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.LocationSplitHelperResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskItemLargePickPatternConstants;

/**
 * <AUTHOR>
 * @title: LocationSplitHelperResultBOConvertor
 * @description:
 * @date 2022-12-01 10:14
 */
public class LocationSplitHelperResultBOConvertor {

    private static final Logger LOG = LoggerFactory.getLogger(LocationSplitHelperResultBOConvertor.class);

    /**
     * 处理补货上下限情况的拆分 小件分配完货位后，将订单项产品数量汇总出来，与原始拣货货位的补货上限比较，超过上限则转换成大件规格，将转换后的整件数量汇总出来，生成多项整件拣货任务； 这里的订单项都是小件（unitCount >
     * 0）
     * 
     * @param bo
     * @param productSkuMap
     * @return
     */
    public static LocationSplitHelperResultBO convertPackage(LocationSplitHelperBO bo,
        Map<Long, ProductSkuDTO> productSkuMap) {
        LocationSplitHelperResultBO resultBO = convertBase(bo);

        // 如果小于等于补货上限，不用处理，直接按零拣位 走 通道，生成播种任务
        ProductSkuDTO productSkuDTO = productSkuMap.get(bo.getSkuId());
        if (BooleanUtils.isTrue(isLessMaxReplenishment(bo, productSkuDTO))) {
            resultBO.setPackageItemList(Collections.emptyList());
            resultBO.setUnitItemList(bo.getItemList());

            return resultBO;
        }

        // 如果大于补货上限，需要处理拆出一个小的，用来进通道，生成播种任务
        LocationSplitHelperResultBO result = convertPackage(bo);
        if (!CollectionUtils.isEmpty(result.getPackageItemList())) {
            result.getPackageItemList()
                .forEach(m -> m.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY));
        }
        return result;
    }

    /**
     * 是否小于补货上限； 如果小于等于补货上限，不用处理，直接按零拣位 走 通道，生成播种任务
     * 
     * @param bo
     * @param productSkuDTO
     * @return
     */
    private static boolean isLessMaxReplenishment(LocationSplitHelperBO bo, ProductSkuDTO productSkuDTO) {
        if (Objects.isNull(productSkuDTO.getMaxReplenishment())) {
            return Boolean.TRUE;
        }
        if (productSkuDTO.getMaxReplenishment().compareTo(BigDecimal.ZERO) == 0) {
            return Boolean.TRUE;
        }
        if (bo.getUnitTotalCount().compareTo(productSkuDTO.getMaxReplenishment()) <= 0) {
            return Boolean.TRUE;
        }

        // if (WarehouseConfigConstants.SOW_CONTROL_TYPE_LIQUID_NOT_PACK
        // .equals(bo.getWarehouseConfigDTO().getSowControlType())) {
        // return Boolean.TRUE;
        // }

        return Boolean.FALSE;
    }

    private static boolean isEqualMaxReplenishment(LocationSplitHelperBO bo, ProductSkuDTO productSkuDTO) {
        if (Objects.isNull(productSkuDTO.getMaxReplenishment())) {
            return Boolean.FALSE;
        }
        if (productSkuDTO.getMaxReplenishment().compareTo(BigDecimal.ZERO) == 0) {
            return Boolean.FALSE;
        }
        if (bo.getUnitTotalCount().compareTo(productSkuDTO.getMaxReplenishment()) >= 0) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    /**
     * 处理有大件有小件的
     *
     * @param bo
     * @return
     */
    public static LocationSplitHelperResultBO convertSplit(LocationSplitHelperBO bo,
        Map<Long, ProductSkuDTO> productSkuMap) {
        ProductSkuDTO productSkuDTO = productSkuMap.get(bo.getSkuId());

        LocationSplitHelperResultBO resultBO = convertBase(bo);
        // 小于等于补货上限，则都走零拣位
        if (BooleanUtils.isTrue(isLessMaxReplenishment(bo, productSkuDTO))) {
            resultBO.setPackageItemList(Collections.emptyList());
            resultBO.setUnitItemList(bo.getItemList());

            return resultBO;
        }

        // 从大到小排序
        bo.getItemList().sort((o1, o2) -> o2.getUnittotalcount().compareTo(o1.getUnittotalcount()));

        // 要拆出来的数量
        BigDecimal packageUnitTotalCount = bo.getPackageCount().multiply(bo.getSpecQuantity());
        int boundItemIndex = getBoundItemIndex(packageUnitTotalCount,
            CountBoundCalculateBOConvertor.convertItemList(bo.getItemList()), 0);
        List<OutStockOrderItemPO> packageItemList = bo.getItemList().subList(0, boundItemIndex + 1);

        BigDecimal boundPackageUnitTotalCount = packageItemList.stream().map(OutStockOrderItemPO::getUnittotalcount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 如果与临界值之前的件数比较 == 0，则临界值之后的走小件数，临界值之前的（包含临界值）走大件数
        // 如果与临界值之前的件数比较 < 0，则临界值之后的走小件数，临界值之前的走大件数，临界值的进行拆分
        BigDecimal diffUnitCount = packageUnitTotalCount.subtract(boundPackageUnitTotalCount);
        if (diffUnitCount.compareTo(BigDecimal.ZERO) == 0) {
            return convertBoundsBigger(bo, boundItemIndex);
        }

        // 如果是一项，则单独处理；如果是多项
        // 重新截取列表到前一位
        packageItemList = copyItemList(bo.getItemList().subList(0, boundItemIndex));

        // 如果截取到前一项是0，则算数量的
        // diffUnitCount 是临界订单项实际应该分摊的大件数，
        // TODO
        // diffUnitCount = packageUnitTotalCount;
        if (CollectionUtils.isEmpty(packageItemList)) {
            diffUnitCount = packageUnitTotalCount;
        } else {
            // 规格5， 1、8；2、5
            boundPackageUnitTotalCount = packageItemList.stream().map(OutStockOrderItemPO::getUnittotalcount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            diffUnitCount = packageUnitTotalCount.subtract(boundPackageUnitTotalCount);
        }

        OutStockOrderItemPO boundItem = bo.getItemList().get(boundItemIndex);

        List<OutStockOrderItemPO> unitItemList =
            copyItemList(bo.getItemList().subList(boundItemIndex + 1, bo.getItemList().size()));

        // 对临界项进行拆分
        OutStockOrderItemPO packageItem = getPackageItem(boundItem, diffUnitCount);
        OutStockOrderItemPO unitItem = getUnitItem(boundItem, diffUnitCount);
        setUuid(packageItem, unitItem);
        packageItemList.add(packageItem);
        unitItemList.add(unitItem);
        if (!com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(packageItemList)) {
            packageItemList.forEach(m -> m.setLargePick(BatchTaskItemLargePickPatternConstants.LARGE_PICK_MANY));
        }

        resultBO.setPackageItemList(packageItemList);
        resultBO.setUnitItemList(unitItemList);

        return resultBO;
    }

    /**
     * 处理按sku聚合后都是大件的 这里拆分出一个小件，否则到通道那里都是大件，没有小件，不会走播种任务，到后期再合并， 但有一个过滤条件（根据订单项id和locationId），如果没有进通道的
     * 单独生成一个拣货任务，这里也需要单独处理
     * <p>
     * 整件拆零的小件设置的是临拣位，是按detail去拆分， <br/>
     * FIXME
     * 
     * @param bo
     * @return
     */
    public static LocationSplitHelperResultBO convertPackage(LocationSplitHelperBO bo) {
        LocationSplitHelperResultBO resultBO = convertBase(bo);
        List<OutStockOrderItemPO> itemList = bo.getItemList();
        // 从小到大排序
        itemList.sort(Comparator.comparing(OutStockOrderItemPO::getUnittotalcount));
        // OutStockOrderItemPO itemPO = itemList.get(0);
        // BigDecimal splitUnitCount = itemPO.getUnittotalcount().subtract(BigDecimal.ONE);
        // // 这里拆分有两种情况：1、规格是5，销售数量也是5；2、规格是5，销售数量是2和3； 这里的
        // OutStockOrderItemPO packageItem = getPackageItem(itemPO, splitUnitCount);
        // OutStockOrderItemPO unitItem = getUnitItem(itemPO, splitUnitCount);
        // setUuid(packageItem, unitItem);
        // // 要处理子项，最终还要合并，而且小项 设置货位的时候 会按detail 进行拆分
        // if (bo.getItemList().size() == 1) {
        // resultBO.setPackageItemList(Collections.singletonList(packageItem));
        // resultBO.setUnitItemList(Collections.singletonList(unitItem));
        // return resultBO;
        // }

        // List<OutStockOrderItemPO> packageItemList = copyItemList(bo.getItemList().subList(1,
        // bo.getItemList().size()));
        List<OutStockOrderItemPO> packageItemList = copyItemList(bo.getItemList());
        // packageItemList.add(packageItem);

        resultBO.setPackageItemList(packageItemList);
        // resultBO.setUnitItemList(Collections.singletonList(unitItem));

        return resultBO;
    }

    private static void setUuid(OutStockOrderItemPO packageItem, OutStockOrderItemPO unitItem) {
        // String uuid = UUID.randomUUID().toString();
        // packageItem.setUuid(uuid);
        // unitItem.setUuid(uuid);
        packageItem.setUuid(UUID.randomUUID().toString());
        unitItem.setUuid(UUID.randomUUID().toString());
    }

    /**
     * 处理默认的
     * 
     * @param unitItemList
     * @return
     */
    public static LocationSplitHelperResultBO convertDefault(List<OutStockOrderItemPO> unitItemList) {
        OutStockOrderItemPO defaultItem = unitItemList.get(0);

        BigDecimal unitTotalCount =
            unitItemList.stream().map(OutStockOrderItemPO::getUnittotalcount).reduce(BigDecimal.ZERO, BigDecimal::add);
        LocationSplitHelperResultBO resultBO = new LocationSplitHelperResultBO();
        resultBO.setSkuId(defaultItem.getSkuid());
        resultBO.setUnitTotalCount(unitTotalCount);
        resultBO.setSpecQuantity(defaultItem.getSpecquantity());
        resultBO.setSaleSpecQuantity(defaultItem.getSalespecquantity());

        BigDecimal[] count = unitTotalCount.divideAndRemainder(defaultItem.getSpecquantity());
        resultBO.setPackageCount(count[0]);
        resultBO.setUnitCount(count[1]);

        resultBO.setPackageItemList(Collections.emptyList());
        resultBO.setUnitItemList(unitItemList);

        return resultBO;
    }

    /**
     * 处理都是小件的
     *
     * @param bo
     * @return
     */
    public static LocationSplitHelperResultBO convertUnit(LocationSplitHelperBO bo) {
        LocationSplitHelperResultBO resultBO = convertBase(bo);
        resultBO.setPackageItemList(Collections.emptyList());
        resultBO.setUnitItemList(bo.getItemList());

        return resultBO;
    }

    public static LocationSplitHelperResultBO convertBase(LocationSplitHelperBO bo) {
        LocationSplitHelperResultBO resultBO = new LocationSplitHelperResultBO();
        resultBO.setSkuId(bo.getSkuId());
        resultBO.setUnitTotalCount(bo.getUnitTotalCount());
        resultBO.setSpecQuantity(bo.getSpecQuantity());
        resultBO.setSaleSpecQuantity(bo.getSaleSpecQuantity());
        resultBO.setPackageCount(bo.getPackageCount());
        resultBO.setUnitCount(bo.getUnitCount());

        return resultBO;
    }

    public static LocationSplitHelperResultBO convertBoundsBigger(LocationSplitHelperBO bo, int boundItemLocation) {
        LOG.info("LocationSplitHelperResultBOConvertor  convertBoundsBigger");
        LocationSplitHelperResultBO resultBO = convertBase(bo);
        List<OutStockOrderItemPO> packageItemList = bo.getItemList().subList(0, boundItemLocation + 1);

        List<OutStockOrderItemPO> unitItemList =
            bo.getItemList().subList(boundItemLocation + 1, bo.getItemList().size());

        resultBO.setPackageItemList(copyItemList(packageItemList));
        resultBO.setUnitItemList(copyItemList(unitItemList));

        return resultBO;
    }

    /**
     * 处理有大件有小件的
     *
     * @param bo
     * @return
     */
    public static LocationSplitHelperResultBO convertSplit(LocationSplitHelperBO bo) {
        // 从大到小排序
        bo.getItemList().sort((o1, o2) -> o2.getUnittotalcount().compareTo(o1.getUnittotalcount()));

        // 总的大件数
        BigDecimal packageUnitTotalCount = bo.getPackageCount().multiply(bo.getSpecQuantity());
        int boundItemIndex = getBoundItemIndex(packageUnitTotalCount,
            CountBoundCalculateBOConvertor.convertItemList(bo.getItemList()), 0);
        List<OutStockOrderItemPO> packageItemList = bo.getItemList().subList(0, boundItemIndex + 1);

        BigDecimal boundPackageUnitTotalCount = packageItemList.stream().map(OutStockOrderItemPO::getUnittotalcount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 如果与临界值之前的件数比较 == 0，则临界值之后的走小件数，临界值之前的（包含临界值）走大件数
        // 如果与临界值之前的件数比较 < 0，则临界值之后的走小件数，临界值之前的走大件数，临界值的进行拆分
        BigDecimal diffUnitCount = packageUnitTotalCount.subtract(boundPackageUnitTotalCount);
        if (diffUnitCount.compareTo(BigDecimal.ZERO) == 0) {
            return convertBoundsBigger(bo, boundItemIndex);
        }

        // 如果是一项，则单独处理；如果是多项
        // 重新截取列表到前一位
        packageItemList = copyItemList(bo.getItemList().subList(0, boundItemIndex));

        // 如果截取到前一项是0，则算数量的
        // diffUnitCount 是临界订单项实际应该分摊的大件数，
        if (CollectionUtils.isEmpty(packageItemList)) {
            diffUnitCount = packageUnitTotalCount;
        } else {
            // 规格5， 1、8；2、5
            boundPackageUnitTotalCount = packageItemList.stream().map(OutStockOrderItemPO::getUnittotalcount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            diffUnitCount = packageUnitTotalCount.subtract(boundPackageUnitTotalCount);
        }

        OutStockOrderItemPO boundItem = bo.getItemList().get(boundItemIndex);

        List<OutStockOrderItemPO> unitItemList =
            copyItemList(bo.getItemList().subList(boundItemIndex + 1, bo.getItemList().size()));

        OutStockOrderItemPO packageItem = getPackageItem(boundItem, diffUnitCount);
        OutStockOrderItemPO unitItem = getUnitItem(boundItem, diffUnitCount);
        setUuid(packageItem, unitItem);
        // 对临界项进行拆分
        packageItemList.add(packageItem);
        unitItemList.add(unitItem);

        LocationSplitHelperResultBO resultBO = new LocationSplitHelperResultBO();
        BeanUtils.copyProperties(bo, resultBO);

        resultBO.setPackageItemList(packageItemList);
        resultBO.setUnitItemList(unitItemList);

        return resultBO;
    }

    private List<OutStockOrderItemPO> getItemList(LocationSplitHelperBO bo, int boundItemIndex) {
        if (boundItemIndex == 0) {
            return copyItemList(Collections.singletonList(bo.getItemList().get(0)));
        }

        return copyItemList(bo.getItemList().subList(0, boundItemIndex));
    }

    // 规格6，订单项1买了7个，订单项2买了7个，订单项3买了6个；订单项3需要拆，这里的入参就是订单项3、数量是【18（总的大件数量） - 14 (摊到负数的项的前一项的小件数总和) =】4

    /**
     * @param boundItem 临界订单项
     * @param packageUnitCount 这个订单项应该分摊的大件数量
     * @return
     */
    public static OutStockOrderItemPO getPackageItem(OutStockOrderItemPO boundItem, BigDecimal packageUnitCount) {
        OutStockOrderItemPO copyItem = new OutStockOrderItemPO();
        BeanUtils.copyProperties(boundItem, copyItem);

        BigDecimal[] count = packageUnitCount.divideAndRemainder(boundItem.getSpecquantity());
        copyItem.setUnittotalcount(packageUnitCount);
        copyItem.setUnitcount(count[1]);
        copyItem.setPackagecount(count[0]);

        List<OutStockOrderItemDetailPO> detailList = boundItem.getItemDetails();
        if (CollectionUtils.isEmpty(detailList)) {
            return copyItem;
        }

        detailList.sort((o1, o2) -> o2.getUnitTotalCount().compareTo(o1.getUnitTotalCount()));

        int index =
            getBoundItemIndex(packageUnitCount, CountBoundCalculateBOConvertor.convertItemDetailList(detailList), 0);

        List<OutStockOrderItemDetailPO> packageItemDetailList = detailList.subList(0, index + 1);
        BigDecimal packageTotalCount = detailList.stream().map(OutStockOrderItemDetailPO::getUnitTotalCount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal detailDifferCount = packageUnitCount.subtract(packageTotalCount);
        if (detailDifferCount.compareTo(BigDecimal.ZERO) == 0) {
            copyItem.setItemDetails(copyDetailList(packageItemDetailList));
            return copyItem;
        }

        packageItemDetailList = copyDetailList(detailList.subList(0, index));
        OutStockOrderItemDetailPO boundItemDetail = detailList.get(index);
        boundItemDetail.setUuid(UUID.randomUUID().toString());

        BigDecimal leftCount = getPackageLeftCount(packageItemDetailList, packageUnitCount);

        OutStockOrderItemDetailPO packageItemDetail =
            CountBoundCalculateBOConvertor.getItemDetail(boundItemDetail, CountBoundCalculateBOConvertor
                .getUpItem(CountBoundCalculateBOConvertor.convertItemDetail(boundItemDetail), leftCount));

        packageItemDetailList.add(packageItemDetail);
        copyItem.setItemDetails(packageItemDetailList);

        return copyItem;
    }

    private static BigDecimal getPackageLeftCount(List<OutStockOrderItemDetailPO> packageItemDetailList,
        BigDecimal packageUnitCount) {
        if (CollectionUtils.isEmpty(packageItemDetailList)) {
            return packageUnitCount;
        }

        return packageUnitCount.subtract(packageItemDetailList.stream()
            .map(OutStockOrderItemDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    // 规格6，订单项1买了7个，订单项2买了7个，订单项3买了6个；订单项3需要拆，这里的入参就是订单项3、数量是2
    public static OutStockOrderItemPO getUnitItem(OutStockOrderItemPO boundItem, BigDecimal packageUnitCount) {
        OutStockOrderItemPO copyItem = new OutStockOrderItemPO();
        BeanUtils.copyProperties(boundItem, copyItem);

        BigDecimal unitTotalCount = boundItem.getUnittotalcount().subtract(packageUnitCount);
        copyItem.setUnittotalcount(unitTotalCount);
        BigDecimal[] count = unitTotalCount.divideAndRemainder(boundItem.getSpecquantity());
        copyItem.setUnitcount(count[1]);
        copyItem.setPackagecount(count[0]);

        List<OutStockOrderItemDetailPO> detailList = boundItem.getItemDetails();

        if (CollectionUtils.isEmpty(detailList)) {
            return copyItem;
        }

        detailList.sort((o1, o2) -> o2.getUnitTotalCount().compareTo(o1.getUnitTotalCount()));

        int index =
            getBoundItemIndex(packageUnitCount, CountBoundCalculateBOConvertor.convertItemDetailList(detailList), 0);
        BigDecimal packageTotalCount = detailList.stream().map(OutStockOrderItemDetailPO::getUnitTotalCount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal detailDifferCount = packageUnitCount.subtract(packageTotalCount);

        List<OutStockOrderItemDetailPO> unitDetailList = detailList.subList(index + 1, detailList.size());
        if (detailDifferCount.compareTo(BigDecimal.ZERO) == 0) {
            copyItem.setItemDetails(copyDetailList(unitDetailList));
            return copyItem;
        }

        unitDetailList = copyDetailList(unitDetailList);
        OutStockOrderItemDetailPO boundItemDetail = detailList.get(index);

        BigDecimal leftUnitCount = getUnitLeftCount(unitDetailList, unitTotalCount);
        // 这里不考虑diffUnitCount等于0的情况，是因为上面处理过了
        OutStockOrderItemDetailPO unitItemDetail =
            CountBoundCalculateBOConvertor.getItemDetail(boundItemDetail, CountBoundCalculateBOConvertor
                .getUpItem(CountBoundCalculateBOConvertor.convertItemDetail(boundItemDetail), leftUnitCount));

        unitDetailList.add(unitItemDetail);
        copyItem.setItemDetails(copyDetailList(unitDetailList));

        return copyItem;
    }

    private static BigDecimal getUnitLeftCount(List<OutStockOrderItemDetailPO> unitDetailList,
        BigDecimal unitTotalCount) {
        if (CollectionUtils.isEmpty(unitDetailList)) {
            return unitTotalCount;
        }

        return unitTotalCount.subtract(unitDetailList.stream().map(OutStockOrderItemDetailPO::getUnitTotalCount)
            .reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    /**
     * 递归获取临界订单项
     *
     * @param packageUnitTotalCount 需要分摊的数量
     * @param itemList 待摊列表
     * @param i 列表索引值
     * @return 5 6 3
     */
    public static int getBoundItemIndex(BigDecimal packageUnitTotalCount, List<CountBoundCalculateBO> itemList, int i) {
        if (packageUnitTotalCount.subtract(itemList.get(i).getUnitTotalCount()).compareTo(BigDecimal.ZERO) <= 0) {
            return i;
        }

        BigDecimal tmpCount = packageUnitTotalCount.subtract(itemList.get(i).getUnitTotalCount());
        return getBoundItemIndex(tmpCount, itemList, i + 1);
    }

    public static List<OutStockOrderItemPO> copyItemList(List<OutStockOrderItemPO> packageItemDetailList) {
        return packageItemDetailList.stream().map(m -> {
            OutStockOrderItemPO itemPO = new OutStockOrderItemPO();
            BeanUtils.copyProperties(m, itemPO);

            return itemPO;
        }).collect(Collectors.toList());
    }

    public static List<OutStockOrderItemDetailPO>
        copyDetailList(List<OutStockOrderItemDetailPO> packageItemDetailList) {
        return packageItemDetailList.stream().map(m -> {
            OutStockOrderItemDetailPO detailPO = new OutStockOrderItemDetailPO();
            BeanUtils.copyProperties(m, detailPO);

            return detailPO;
        }).collect(Collectors.toList());
    }

    /**
     * 这里注意detail的合并
     *
     * @param storeItem
     * @param otherItem
     * @return
     */
    public static OutStockOrderItemPO mergeOrderItem(OutStockOrderItemPO storeItem, OutStockOrderItemPO otherItem) {
        OutStockOrderItemPO newItem = new OutStockOrderItemPO();
        BeanUtils.copyProperties(storeItem, newItem);

        BigDecimal unitTotalCount = storeItem.getUnittotalcount().add(otherItem.getUnittotalcount());
        BigDecimal[] count = unitTotalCount.divideAndRemainder(storeItem.getSpecquantity());
        newItem.setUnittotalcount(unitTotalCount);
        newItem.setUnitcount(count[1]);
        newItem.setPackagecount(count[0]);

        List<OutStockOrderItemDetailPO> itemDetail1 = storeItem.getItemDetails();
        List<OutStockOrderItemDetailPO> itemDetail2 = otherItem.getItemDetails();

        List<OutStockOrderItemDetailPO> totalDetailList = new ArrayList<>();
        totalDetailList.addAll(itemDetail1);
        totalDetailList.addAll(itemDetail2);

        Map<Long, List<OutStockOrderItemDetailPO>> detailSecOwnerGroupMap =
            totalDetailList.stream().collect(Collectors.groupingBy(OutStockOrderItemDetailPO::getSecOwnerId));

        List<OutStockOrderItemDetailPO> newTotalDetailList = new ArrayList<>();
        for (Map.Entry<Long, List<OutStockOrderItemDetailPO>> entry : detailSecOwnerGroupMap.entrySet()) {
            if (entry.getValue().size() == 1) {
                newTotalDetailList.addAll(entry.getValue());
            } else {
                // 合并
                List<OutStockOrderItemDetailPO> detailList = entry.getValue();
                OutStockOrderItemDetailPO oldItemDetail = detailList.get(0);
                OutStockOrderItemDetailPO newItemDetail = new OutStockOrderItemDetailPO();
                BeanUtils.copyProperties(oldItemDetail, newItemDetail);

                newItemDetail.setUnitTotalCount(detailList.stream().map(OutStockOrderItemDetailPO::getUnitTotalCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));

                newTotalDetailList.add(newItemDetail);
            }
        }

        newItem.setItemDetails(newTotalDetailList);

        return newItem;
    }

    /**
     * 设置货位前，会按订单项明细进行拆分，设置货位后，会在明细项上再进行拆分。 这时候再去 创建 OutStockOrderItemPO，就会有 OutStockOrderItemPO 的小件数总和 和
     * 明细项的小件数总和对不上的情况， 如果等于库存上限的情况也拆出一个多项拣货任务，就会出问题。 需要让两者保持一致。 比如同一个货主：2小件，设置库存之后，可能变成1 + 1小件。这时候再创建item，数量就对不上了。
     * 
     * @param buildOrderItemPO
     * @param outStockOrderItemPO
     */
    @Deprecated
    public static void setDetailList(OutStockOrderItemPO buildOrderItemPO, OutStockOrderItemPO outStockOrderItemPO) {
        List<OutStockOrderItemDetailPO> targetDetailList = new ArrayList<>();
        BigDecimal shouldSplitUnitTotalCount = buildOrderItemPO.getUnittotalcount();

        List<OutStockOrderItemDetailPO> detailList = outStockOrderItemPO.getItemDetails().stream()
            .filter(m -> m.getId().equals(buildOrderItemPO.getItemDetailId())).collect(Collectors.toList());
        BigDecimal detailUnitTotalCount = detailList.stream().map(OutStockOrderItemDetailPO::getUnitTotalCount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (shouldSplitUnitTotalCount.compareTo(detailUnitTotalCount) == 0) {
            detailList.forEach(m -> {
                OutStockOrderItemDetailPO itemDetailPO = new OutStockOrderItemDetailPO();
                BeanUtils.copyProperties(m, itemDetailPO);
                targetDetailList.add(itemDetailPO);
            });

            buildOrderItemPO.setItemDetails(targetDetailList);
            return;
        }

        // 这里应该只有一个明细数据
        OutStockOrderItemDetailPO outStockOrderItemDetailPO = detailList.get(0);

        BigDecimal leftUnitTotalCount =
            outStockOrderItemDetailPO.getUnitTotalCount().subtract(shouldSplitUnitTotalCount);
        outStockOrderItemDetailPO.setUnitTotalCount(leftUnitTotalCount);

        OutStockOrderItemDetailPO copyDetail = new OutStockOrderItemDetailPO();
        BeanUtils.copyProperties(outStockOrderItemDetailPO, copyDetail);
        copyDetail.setUnitTotalCount(shouldSplitUnitTotalCount);
        List<OutStockOrderItemDetailPO> copyDetailList = new ArrayList<>();
        copyDetailList.add(copyDetail);
        buildOrderItemPO.setItemDetails(copyDetailList);
    }

}
