package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/10
 */
public class NotifyOrderLackHelperBO {

    private List<OutStockOrderPO> orderList;
    private Map<Long, List<OrderItemTaskInfoPO>> originOrderItemTaskInfoGroupMap;
    private Map<Long, List<OrderItemTaskInfoPO>> lackOrderItemTaskInfoGroupMap;
    private Map<String, StringBuffer> orderLackInfoDesMap;
    private boolean isOrderCenter;
    private Integer warehouseId;
    private Integer optUserId;

    /**
     * 获取
     *
     * @return originOrderItemTaskInfoGroupMap
     */
    public Map<Long, List<OrderItemTaskInfoPO>> getOriginOrderItemTaskInfoGroupMap() {
        return this.originOrderItemTaskInfoGroupMap;
    }

    /**
     * 设置
     *
     * @param originOrderItemTaskInfoGroupMap
     */
    public void
        setOriginOrderItemTaskInfoGroupMap(Map<Long, List<OrderItemTaskInfoPO>> originOrderItemTaskInfoGroupMap) {
        this.originOrderItemTaskInfoGroupMap = originOrderItemTaskInfoGroupMap;
    }

    /**
     * 获取
     *
     * @return lackOrderItemTaskInfoGroupMap
     */
    public Map<Long, List<OrderItemTaskInfoPO>> getLackOrderItemTaskInfoGroupMap() {
        return this.lackOrderItemTaskInfoGroupMap;
    }

    /**
     * 设置
     *
     * @param lackOrderItemTaskInfoGroupMap
     */
    public void setLackOrderItemTaskInfoGroupMap(Map<Long, List<OrderItemTaskInfoPO>> lackOrderItemTaskInfoGroupMap) {
        this.lackOrderItemTaskInfoGroupMap = lackOrderItemTaskInfoGroupMap;
    }

    /**
     * 获取
     *
     * @return orderLackInfoDesMap
     */
    public Map<String, StringBuffer> getOrderLackInfoDesMap() {
        return this.orderLackInfoDesMap;
    }

    /**
     * 设置
     *
     * @param orderLackInfoDesMap
     */
    public void setOrderLackInfoDesMap(Map<String, StringBuffer> orderLackInfoDesMap) {
        this.orderLackInfoDesMap = orderLackInfoDesMap;
    }

    /**
     * 获取
     *
     * @return isOrderCenter
     */
    public boolean isIsOrderCenter() {
        return this.isOrderCenter;
    }

    /**
     * 设置
     *
     * @param isOrderCenter
     */
    public void setIsOrderCenter(boolean isOrderCenter) {
        this.isOrderCenter = isOrderCenter;
    }

    /**
     * 获取
     *
     * @return warehouseId
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置
     *
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取
     *
     * @return optUserId
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置
     *
     * @param optUserId
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }

    /**
     * 获取
     *
     * @return orderList
     */
    public List<OutStockOrderPO> getOrderList() {
        return this.orderList;
    }

    /**
     * 设置
     *
     * @param orderList
     */
    public void setOrderList(List<OutStockOrderPO> orderList) {
        this.orderList = orderList;
    }
}
