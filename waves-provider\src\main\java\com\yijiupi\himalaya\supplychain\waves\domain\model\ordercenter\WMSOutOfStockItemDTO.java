package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.io.Serializable;
import java.math.BigDecimal;

public class WMSOutOfStockItemDTO implements Serializable {

    // 订单项Id
    private Long orderItemId;

    // 缺货前数量
    private BigDecimal beforeCount;

    // 缺货数量
    private BigDecimal outOfStocklackCount;

    // 缺货后数量
    private BigDecimal afterCount;

    // 缺货原因
    private String reason;

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public BigDecimal getBeforeCount() {
        return beforeCount;
    }

    public void setBeforeCount(BigDecimal beforeCount) {
        this.beforeCount = beforeCount;
    }

    public BigDecimal getOutOfStocklackCount() {
        return outOfStocklackCount;
    }

    public void setOutOfStocklackCount(BigDecimal outOfStocklackCount) {
        this.outOfStocklackCount = outOfStocklackCount;
    }

    public BigDecimal getAfterCount() {
        return afterCount;
    }

    public void setAfterCount(BigDecimal afterCount) {
        this.afterCount = afterCount;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
