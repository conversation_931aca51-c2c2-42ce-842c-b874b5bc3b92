package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class PDASowTaskRefBatchTaskItemDTO implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte taskState;

    /**
     * 已拣货数量
     */
    private BigDecimal overSortCount;

    /**
     * 播种任务明细状态
     */
    private Byte sowTaskItemState;

    /**
     * skuId（赠品SKUId可能为null）
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 包装规格
     */
    private String specName;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 销售规格系数
     */
    private BigDecimal saleSpecQuantity;


    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal specQuantity;


    /**
     * 箱码集合
     *
     */
    private List<String> packageCode;

    /**
     * 瓶码集合
     *
     */
    private List<String> unitCode;


    public PDASowTaskRefBatchTaskItemDTO() {
    }

    public PDASowTaskRefBatchTaskItemDTO(BatchTaskItemDTO batchTaskItem) {
        this.id = batchTaskItem.getId();
        this.taskState = batchTaskItem.getTaskState();
        this.overSortCount = batchTaskItem.getOverSortCount();
        this.sowTaskItemState = batchTaskItem.getSowTaskItemState();
        this.skuId = batchTaskItem.getSkuId();
        this.productName = batchTaskItem.getProductName();
        this.specName = batchTaskItem.getSpecName();
        this.packageName = batchTaskItem.getPackageName();
        this.unitName = batchTaskItem.getUnitName();
        this.saleSpecQuantity = batchTaskItem.getSaleSpecQuantity();
        this.specQuantity = batchTaskItem.getSpecQuantity();
        this.packageCode = batchTaskItem.getPackageCode();
        this.unitCode = batchTaskItem.getUnitCode();
    }

    /**
     * 获取 主键
     *
     * @return id 主键
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 主键
     *
     * @param id 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     *
     * @return taskState 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     */
    public Byte getTaskState() {
        return this.taskState;
    }

    /**
     * 设置 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     *
     * @param taskState 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     */
    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    /**
     * 获取 已拣货数量
     *
     * @return overSortCount 已拣货数量
     */
    public BigDecimal getOverSortCount() {
        return this.overSortCount;
    }

    /**
     * 设置 已拣货数量
     *
     * @param overSortCount 已拣货数量
     */
    public void setOverSortCount(BigDecimal overSortCount) {
        this.overSortCount = overSortCount;
    }

    /**
     * 获取 播种任务明细状态
     *
     * @return sowTaskItemState 播种任务明细状态
     */
    public Byte getSowTaskItemState() {
        return this.sowTaskItemState;
    }

    /**
     * 设置 播种任务明细状态
     *
     * @param sowTaskItemState 播种任务明细状态
     */
    public void setSowTaskItemState(Byte sowTaskItemState) {
        this.sowTaskItemState = sowTaskItemState;
    }

    /**
     * 获取 skuId（赠品SKUId可能为null）
     *
     * @return skuId skuId（赠品SKUId可能为null）
     */
    public Long getSkuId() {
        return this.skuId;
    }

    /**
     * 设置 skuId（赠品SKUId可能为null）
     *
     * @param skuId skuId（赠品SKUId可能为null）
     */
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    /**
     * 获取 商品名称
     *
     * @return productName 商品名称
     */
    public String getProductName() {
        return this.productName;
    }

    /**
     * 设置 商品名称
     *
     * @param productName 商品名称
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 获取 包装规格
     *
     * @return specName 包装规格
     */
    public String getSpecName() {
        return this.specName;
    }

    /**
     * 设置 包装规格
     *
     * @param specName 包装规格
     */
    public void setSpecName(String specName) {
        this.specName = specName;
    }

    /**
     * 获取 大单位名称
     *
     * @return packageName 大单位名称
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * 设置 大单位名称
     *
     * @param packageName 大单位名称
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取 小单位名称
     *
     * @return unitName 小单位名称
     */
    public String getUnitName() {
        return this.unitName;
    }

    /**
     * 设置 小单位名称
     *
     * @param unitName 小单位名称
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * 获取 销售规格系数
     *
     * @return saleSpecQuantity 销售规格系数
     */
    public BigDecimal getSaleSpecQuantity() {
        return this.saleSpecQuantity;
    }

    /**
     * 设置 销售规格系数
     *
     * @param saleSpecQuantity 销售规格系数
     */
    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    /**
     * 获取 包装规格大小单位转换系数
     *
     * @return specQuantity 包装规格大小单位转换系数
     */
    public BigDecimal getSpecQuantity() {
        return this.specQuantity;
    }

    /**
     * 设置 包装规格大小单位转换系数
     *
     * @param specQuantity 包装规格大小单位转换系数
     */
    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    /**
     * 获取 箱码集合
     *
     * @return packageCode 箱码集合
     */
    public List<String> getPackageCode() {
        return this.packageCode;
    }

    /**
     * 设置 箱码集合
     *
     * @param packageCode 箱码集合
     */
    public void setPackageCode(List<String> packageCode) {
        this.packageCode = packageCode;
    }

    /**
     * 获取 瓶码集合
     *
     * @return unitCode 瓶码集合
     */
    public List<String> getUnitCode() {
        return this.unitCode;
    }

    /**
     * 设置 瓶码集合
     *
     * @param unitCode 瓶码集合
     */
    public void setUnitCode(List<String> unitCode) {
        this.unitCode = unitCode;
    }
}
