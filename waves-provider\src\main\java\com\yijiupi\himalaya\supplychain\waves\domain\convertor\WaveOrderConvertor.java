package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrderItem;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO;

/**
 * 波次相关转换器
 *
 * <AUTHOR> 2018/3/15
 */
public class WaveOrderConvertor {

    private static final Logger LOG = LoggerFactory.getLogger(WaveOrderConvertor.class);

    /**
     * 波次订单
     *
     * @param batchPOS
     * @return
     */
    public static List<BatchDTO> batchOrderPOS2BatchOrderDTOS(List<BatchPO> batchPOS) {
        ArrayList<BatchDTO> batchDTOS = new ArrayList<>();
        for (BatchPO batchPO : batchPOS) {
            batchDTOS.add(batchOrderPO2BatchOrderDTO(batchPO));
        }
        return batchDTOS;
    }

    public static BatchDTO batchOrderPO2BatchOrderDTO(BatchPO batchPO) {
        BatchDTO batchDTO = new BatchDTO();
        batchDTO.setId(batchPO.getId());
        batchDTO.setBatchNo(batchPO.getBatchNo());
        batchDTO.setBatchName(batchPO.getBatchName());
        batchDTO.setState(batchPO.getState());
        batchDTO.setOrderCount(batchPO.getOrderCount());
        batchDTO.setOrderAmount(batchPO.getOrderAmount());
        batchDTO.setOrderCount(batchPO.getOrderCount());
        batchDTO.setSkuCount(batchPO.getSkuCount());
        batchDTO.setPackageAmount(batchPO.getPackageAmount());
        batchDTO.setUnitAmount(batchPO.getUnitAmount());
        batchDTO.setCreateTime(batchPO.getCreateTime());
        batchDTO.setPickingType(batchPO.getPickingType());
        batchDTO.setRemark(batchPO.getRemark());
        return batchDTO;
    }

    /**
     * 可用订单转换
     *
     * @param outStockOrderPOS
     * @return
     */
    public static List<OutStockOrderDTO> outStockOrderPOS2OutStockOrderDTOS(List<OutStockOrderPO> outStockOrderPOS) {

        ArrayList<OutStockOrderDTO> outStockOrderDTOS = new ArrayList<>();
        for (OutStockOrderPO outStockOrderPO : outStockOrderPOS) {
            outStockOrderDTOS.add(outStockOrderPO2OutStockOrderDTO(outStockOrderPO));
        }
        return outStockOrderDTOS;
    }

    public static OutStockOrderDTO outStockOrderPO2OutStockOrderDTO(OutStockOrderPO outStockOrderPO) {
        if (null == outStockOrderPO) {
            return null;
        }
        OutStockOrderDTO outStockOrderDTO = new OutStockOrderDTO();
        BeanUtils.copyProperties(outStockOrderPO, outStockOrderDTO);
        outStockOrderDTO.setRefOrderNo(outStockOrderPO.getReforderno());
        outStockOrderDTO.setOrderCreateTime(outStockOrderPO.getOrdercreatetime());
        // SimpleDateFormat sdf = new SimpleDateFormat(" yyyy-MM-dd HH:mm:ss ");
        // outStockOrderDTO.setOrderCreateTime(sdf.format(outStockOrderPO.getOrdercreatetime()));
        Integer ordertype = outStockOrderPO.getOrdertype();
        outStockOrderDTO.setOrderType(ordertype == null ? null : ordertype.byteValue());
        outStockOrderDTO.setSkuCount(outStockOrderPO.getSkucount());
        outStockOrderDTO.setPackageAmount(outStockOrderPO.getPackageamount());
        outStockOrderDTO.setUnitAmount(outStockOrderPO.getUnitamount());
        outStockOrderDTO.setDetailAddress(outStockOrderPO.getDetailaddress());
        return outStockOrderDTO;
    }

    public static OutStockOrderPO outStockOrderItem2OutStockOrder(OutStockOrderPO order,
        List<OutStockOrderItemPO> outStockOrderItemPOS) {
        OutStockOrderPO outStockOrderPO = new OutStockOrderPO();
        if (order != null) {
            BeanUtils.copyProperties(order, outStockOrderPO);
            outStockOrderPO.setItems(outStockOrderItemPOS);
        }
        return outStockOrderPO;
    }

    public static OutStockOrderItemDTO
        outStockOrderItemPO2OutStockOrderItemDTO(OutStockOrderItemPO outStockOrderItemPO) {
        if (null == outStockOrderItemPO) {
            return null;
        }
        OutStockOrderItemDTO outStockOrderItemDTO = new OutStockOrderItemDTO();
        BeanUtils.copyProperties(outStockOrderItemPO, outStockOrderItemDTO);
        outStockOrderItemDTO.setId(outStockOrderItemPO.getId());
        outStockOrderItemDTO.setOrg_id(outStockOrderItemPO.getOrgId());
        outStockOrderItemDTO.setOutstockorder_Id(outStockOrderItemPO.getOutstockorderId());
        outStockOrderItemDTO.setBatchTaskNo(outStockOrderItemPO.getBatchtaskno());
        outStockOrderItemDTO.setBatchtask_Id(outStockOrderItemPO.getBatchtaskId());
        outStockOrderItemDTO.setProductName(outStockOrderItemPO.getProductname());
        outStockOrderItemDTO.setSkuId(outStockOrderItemPO.getSkuid());
        outStockOrderItemDTO.setProductBrand(outStockOrderItemPO.getProductbrand());
        outStockOrderItemDTO.setCategoryName(outStockOrderItemPO.getCategoryname());
        outStockOrderItemDTO.setSpecName(outStockOrderItemPO.getSpecname());
        outStockOrderItemDTO.setSpecQuantity(outStockOrderItemPO.getSpecquantity());
        outStockOrderItemDTO.setSaleSpec(outStockOrderItemPO.getSalespec());
        outStockOrderItemDTO.setSaleSpecQuantity(outStockOrderItemPO.getSalespecquantity());
        outStockOrderItemDTO.setPackageName(outStockOrderItemPO.getPackagename());
        outStockOrderItemDTO.setPackageCount(outStockOrderItemPO.getPackagecount());
        outStockOrderItemDTO.setUnitName(outStockOrderItemPO.getUnitname());
        outStockOrderItemDTO.setUnitCount(outStockOrderItemPO.getUnitcount());
        outStockOrderItemDTO.setUnitTotalCount(outStockOrderItemPO.getUnittotalcount());
        outStockOrderItemDTO.setSaleModel(
            outStockOrderItemPO.getSalemodel() == null ? null : outStockOrderItemPO.getSalemodel().byteValue());
        return outStockOrderItemDTO;
    }

    public static List<InventoryDeliveryJiupiOrder>
        outStockOrderPOS2InventoryDeliveryJiupiOrders(List<OutStockOrderPO> outStockOrderPOS) {
        if (CollectionUtils.isEmpty(outStockOrderPOS))
            return null;

        List<InventoryDeliveryJiupiOrder> deliveryJiupiOrders = new ArrayList<>();
        outStockOrderPOS.forEach(order -> {
            InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder = new InventoryDeliveryJiupiOrder();
            inventoryDeliveryJiupiOrder.setCityId(order.getOrgId());
            inventoryDeliveryJiupiOrder.setFromCityId(order.getFromCityId());
            inventoryDeliveryJiupiOrder.setWarehouseId(order.getWarehouseId());
            inventoryDeliveryJiupiOrder.setFromWarehouseId(order.getWarehouseId());
            inventoryDeliveryJiupiOrder.setAllotType(order.getAllotType());
            inventoryDeliveryJiupiOrder.setJiupiOrderType(order.getOrdertype());
            inventoryDeliveryJiupiOrder
                .setDeliveryMode(order.getDeliveryMode() != null ? order.getDeliveryMode().intValue() : null);
            inventoryDeliveryJiupiOrder.setOrderNo(order.getReforderno());
            inventoryDeliveryJiupiOrder.setOrderId(order.getId());
            inventoryDeliveryJiupiOrder.setOmsOrderId(order.getId());
            inventoryDeliveryJiupiOrder.setCreateUserName(order.getCreateuser());
            inventoryDeliveryJiupiOrder.setPackageAttribute(order.getPackageAttribute());
            // inventoryDeliveryJiupiOrder.setOwnerId(order.getOwnerId());
            inventoryDeliveryJiupiOrder.setItems(outStockOrderItems2InventoryDeliveryJiupiOrderItems(order.getItems()));
            deliveryJiupiOrders.add(inventoryDeliveryJiupiOrder);
        });

        return deliveryJiupiOrders;
    }

    private static List<InventoryDeliveryJiupiOrderItem>
        outStockOrderItems2InventoryDeliveryJiupiOrderItems(List<OutStockOrderItemPO> outStockOrderItemPOS) {
        if (CollectionUtils.isEmpty(outStockOrderItemPOS))
            return null;
        List<InventoryDeliveryJiupiOrderItem> items = new ArrayList<>();
        outStockOrderItemPOS.forEach(orderItem -> {
            if (CollectionUtils.isNotEmpty(orderItem.getItemDetails())) {
                orderItem.getItemDetails().forEach(detail -> {
                    InventoryDeliveryJiupiOrderItem item = new InventoryDeliveryJiupiOrderItem();
                    item.setProductSkuId(orderItem.getSkuid());
                    BigDecimal saleSpecQuantity =
                        orderItem.getSalespecquantity() == null ? BigDecimal.ONE : orderItem.getSalespecquantity();
                    item.setSaleSpecQuantity(saleSpecQuantity);
                    // try {
                    // deliverCount = detail.getUnitTotalCount().divide(saleSpecQuantity);
                    // } catch (ArithmeticException e) {
                    // LOG.info("波次出库组装数据出现无限不循环小数,saleSpecQuantity:{},deliverCount:{}", saleSpecQuantity,
                    // orderItem.getUnittotalcount());
                    // deliverCount = detail.getUnitTotalCount().divide(saleSpecQuantity, 6, BigDecimal.ROUND_HALF_UP);
                    // }
                    item.setBuyCount(detail.getUnitTotalCount());
                    item.setDeliverCount(detail.getUnitTotalCount());
                    item.setProductSpecification_Id(orderItem.getProductSpecificationId());
                    item.setOwnerId(detail.getOwnerId());
                    item.setSecOwnerId(detail.getSecOwnerId());
                    item.setOrderItem_Id(detail.getOutStockOrderItemId());
                    item.setOrderItemDetailId(detail.getId());
                    item.setOmsOrderItemId(detail.getOutStockOrderItemId());

                    items.add(item);
                });
            } else {
                // 兼容老流程
                InventoryDeliveryJiupiOrderItem item = new InventoryDeliveryJiupiOrderItem();
                item.setProductSkuId(orderItem.getSkuid());
                BigDecimal saleSpecQuantity =
                    orderItem.getSalespecquantity() == null ? BigDecimal.ONE : orderItem.getSalespecquantity();
                item.setSaleSpecQuantity(saleSpecQuantity);
                item.setBuyCount(orderItem.getUnittotalcount());
                item.setDeliverCount(orderItem.getUnittotalcount());
                item.setProductSpecification_Id(orderItem.getProductSpecificationId());
                item.setOwnerId(orderItem.getOwnerId());
                item.setOrderItem_Id(orderItem.getId());
                item.setOmsOrderItemId(orderItem.getId());

                items.add(item);
            }
        });

        return items;
    }

    public static List<InventoryDeliveryJiupiOrder>
        expressOutStockOrderPOS2InventoryDeliveryJiupiOrders(List<OutStockOrderPO> outStockOrderPOS) {
        if (CollectionUtils.isEmpty(outStockOrderPOS))
            return null;

        List<InventoryDeliveryJiupiOrder> deliveryJiupiOrders = new ArrayList<>();
        outStockOrderPOS.forEach(order -> {
            InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder = new InventoryDeliveryJiupiOrder();
            inventoryDeliveryJiupiOrder.setCityId(order.getOrgId());
            inventoryDeliveryJiupiOrder.setFromCityId(order.getFromCityId());
            inventoryDeliveryJiupiOrder.setWarehouseId(order.getWarehouseId());
            inventoryDeliveryJiupiOrder.setFromWarehouseId(order.getWarehouseId());
            inventoryDeliveryJiupiOrder.setAllotType(order.getAllotType());
            inventoryDeliveryJiupiOrder.setJiupiOrderType(order.getOrdertype());
            inventoryDeliveryJiupiOrder
                .setDeliveryMode(order.getDeliveryMode() != null ? order.getDeliveryMode().intValue() : null);
            inventoryDeliveryJiupiOrder.setOrderNo(order.getReforderno());
            inventoryDeliveryJiupiOrder.setOrderId(order.getId());
            inventoryDeliveryJiupiOrder
                .setOmsOrderId(order.getBusinessId() == null ? order.getId() : Long.valueOf(order.getBusinessId()));
            inventoryDeliveryJiupiOrder.setCreateUserName(order.getCreateuser());
            inventoryDeliveryJiupiOrder.setPackageAttribute(order.getPackageAttribute());
            // inventoryDeliveryJiupiOrder.setOwnerId(order.getOwnerId());
            inventoryDeliveryJiupiOrder
                .setItems(expressOutStockOrderItems2InventoryDeliveryJiupiOrderItems(order.getItems()));
            deliveryJiupiOrders.add(inventoryDeliveryJiupiOrder);
        });

        return deliveryJiupiOrders;
    }

    private static List<InventoryDeliveryJiupiOrderItem>
        expressOutStockOrderItems2InventoryDeliveryJiupiOrderItems(List<OutStockOrderItemPO> outStockOrderItemPOS) {
        if (CollectionUtils.isEmpty(outStockOrderItemPOS))
            return null;
        List<InventoryDeliveryJiupiOrderItem> items = new ArrayList<>();
        outStockOrderItemPOS.forEach(orderItem -> {
            if (CollectionUtils.isNotEmpty(orderItem.getItemDetails())) {
                orderItem.getItemDetails().forEach(detail -> {
                    InventoryDeliveryJiupiOrderItem item = new InventoryDeliveryJiupiOrderItem();
                    item.setProductSkuId(orderItem.getSkuid());
                    BigDecimal saleSpecQuantity =
                        orderItem.getSalespecquantity() == null ? BigDecimal.ONE : orderItem.getSalespecquantity();
                    item.setSaleSpecQuantity(saleSpecQuantity);
                    // try {
                    // deliverCount = detail.getUnitTotalCount().divide(saleSpecQuantity);
                    // } catch (ArithmeticException e) {
                    // LOG.info("波次出库组装数据出现无限不循环小数,saleSpecQuantity:{},deliverCount:{}", saleSpecQuantity,
                    // orderItem.getUnittotalcount());
                    // deliverCount = detail.getUnitTotalCount().divide(saleSpecQuantity, 6, BigDecimal.ROUND_HALF_UP);
                    // }
                    item.setBuyCount(detail.getUnitTotalCount());
                    item.setDeliverCount(detail.getUnitTotalCount());
                    item.setProductSpecification_Id(orderItem.getProductSpecificationId());
                    item.setOwnerId(detail.getOwnerId());
                    item.setSecOwnerId(detail.getSecOwnerId());
                    item.setOrderItem_Id(detail.getOutStockOrderItemId());
                    item.setOrderItemDetailId(detail.getId());
                    item.setOmsOrderItemId(orderItem.getBusinessItemId() == null ? orderItem.getId()
                        : Long.valueOf(orderItem.getBusinessItemId()));

                    items.add(item);
                });
            } else {
                // 兼容老流程
                InventoryDeliveryJiupiOrderItem item = new InventoryDeliveryJiupiOrderItem();
                item.setProductSkuId(orderItem.getSkuid());
                BigDecimal saleSpecQuantity =
                    orderItem.getSalespecquantity() == null ? BigDecimal.ONE : orderItem.getSalespecquantity();
                item.setSaleSpecQuantity(saleSpecQuantity);
                item.setBuyCount(orderItem.getUnittotalcount());
                item.setDeliverCount(orderItem.getUnittotalcount());
                item.setProductSpecification_Id(orderItem.getProductSpecificationId());
                item.setOwnerId(orderItem.getOwnerId());
                item.setOrderItem_Id(orderItem.getId());
                item.setOmsOrderItemId(orderItem.getBusinessItemId() == null ? orderItem.getId()
                    : Long.valueOf(orderItem.getBusinessItemId()));

                items.add(item);
            }
        });

        return items;
    }

}
