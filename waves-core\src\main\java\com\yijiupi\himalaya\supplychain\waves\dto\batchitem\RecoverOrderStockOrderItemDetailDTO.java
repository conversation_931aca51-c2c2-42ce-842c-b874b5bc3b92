package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/9
 */
public class RecoverOrderStockOrderItemDetailDTO implements Serializable {
    /**
     * 出库单id列表
     */
    private List<Long> outStockOrderIds;
    /**
     * 出库单号列表
     */
    private List<String> refOrderNos;
    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 是否使用订单中台的detail数据
     */
    private Boolean useOrderCenterDetail = Boolean.TRUE;
    /**
     * 是否处理detail和item数量一样的数据
     */
    private boolean handleEqualDetail = Boolean.FALSE;

    /**
     * 获取 出库单id列表
     *
     * @return outStockOrderIds 出库单id列表
     */
    public List<Long> getOutStockOrderIds() {
        return this.outStockOrderIds;
    }

    /**
     * 设置 出库单id列表
     *
     * @param outStockOrderIds 出库单id列表
     */
    public void setOutStockOrderIds(List<Long> outStockOrderIds) {
        this.outStockOrderIds = outStockOrderIds;
    }

    /**
     * 获取 出库单号列表
     *
     * @return refOrderNos 出库单号列表
     */
    public List<String> getRefOrderNos() {
        return this.refOrderNos;
    }

    /**
     * 设置 出库单号列表
     *
     * @param refOrderNos 出库单号列表
     */
    public void setRefOrderNos(List<String> refOrderNos) {
        this.refOrderNos = refOrderNos;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 是否使用订单中台的detail数据
     *
     * @return useOrderCenterDetail 是否使用订单中台的detail数据
     */
    public Boolean getUseOrderCenterDetail() {
        return this.useOrderCenterDetail;
    }

    /**
     * 设置 是否使用订单中台的detail数据
     *
     * @param useOrderCenterDetail 是否使用订单中台的detail数据
     */
    public void setUseOrderCenterDetail(Boolean useOrderCenterDetail) {
        this.useOrderCenterDetail = useOrderCenterDetail;
    }

    /**
     * 获取 是否处理detail和item数量一样的数据
     *
     * @return handleEqualDetail 是否处理detail和item数量一样的数据
     */
    public boolean isHandleEqualDetail() {
        return this.handleEqualDetail;
    }

    /**
     * 设置 是否处理detail和item数量一样的数据
     *
     * @param handleEqualDetail 是否处理detail和item数量一样的数据
     */
    public void setHandleEqualDetail(boolean handleEqualDetail) {
        this.handleEqualDetail = handleEqualDetail;
    }
}
