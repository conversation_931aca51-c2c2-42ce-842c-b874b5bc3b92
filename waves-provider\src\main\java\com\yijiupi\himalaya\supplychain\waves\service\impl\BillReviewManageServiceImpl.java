package com.yijiupi.himalaya.supplychain.waves.service.impl;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.batch.IBillReviewManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BillReviewBL;
import com.yijiupi.himalaya.supplychain.waves.dto.billreview.BillReviewDTO;

@Service(timeout = 120000)
public class BillReviewManageServiceImpl implements IBillReviewManageService {

    @Autowired
    private BillReviewBL billReviewBL;

    @Override
    public BillReviewDTO startBillReview(BillReviewDTO billReviewDTO) {
        AssertUtils.notNull(billReviewDTO.getBusinessNo(), "单据编号不能为空");
        AssertUtils.notNull(billReviewDTO.getReviewerId(), "复核人id不能为空");
        return billReviewBL.startBillReview(billReviewDTO);
    }

    @Override
    public void updateBillReview(BillReviewDTO billReviewDTO) {
        billReviewBL.updateBillReview(billReviewDTO);
    }
}
