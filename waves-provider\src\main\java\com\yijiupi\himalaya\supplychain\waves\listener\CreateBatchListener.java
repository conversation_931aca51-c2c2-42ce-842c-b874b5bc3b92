package com.yijiupi.himalaya.supplychain.waves.listener;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchOrderProcessService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.SecondSortBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByRefOrderNoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateByRefOrderNoExtendDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchCreateOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.DeliveryTaskDTO;

/**
 * 新增波次消息监听
 *
 * <AUTHOR>
 * @Date 2020-06-29 15:37
 */
@Service
public class CreateBatchListener {

    private static final Logger LOG = LoggerFactory.getLogger(CreateBatchListener.class);

    private static final int LIMIT = 3;
    @Reference
    private IBatchOrderProcessService iBatchOrderProcessService;

    @Autowired
    private SecondSortBL secondSortBL;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private GlobalCache globalCache;

    /**
     * 新增波次
     */
    @RabbitListener(queues = "${mq.supplychain.waves.createBatch}")
    public void startAdd(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            String messageId = message.getMessageProperties().getMessageId();
            LOG.info("监听创建波次消息>>>【mq.supplychain.waves.createBatch】[{}]{}", messageId, json);
            BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO =
                JSON.parseObject(json, BatchCreateByRefOrderNoDTO.class);
            BatchCreateByRefOrderNoExtendDTO extendDTO = getExtendInfo(batchCreateByRefOrderNoDTO);

            // 判断仓库是否开启了总单播种 如果开启了 则不拆分 如果没有开启 则拆分
            List<DeliveryTaskDTO> deliveryTaskList = batchCreateByRefOrderNoDTO.getDeliveryTaskList();
            if (CollectionUtils.isEmpty(deliveryTaskList) || deliveryTaskList.size() <= 1) {
                iBatchOrderProcessService.createBatchByRefOrderNo(batchCreateByRefOrderNoDTO);
                return;
            }

            if (isMergeTask(batchCreateByRefOrderNoDTO, extendDTO)) {
                BatchCreateByRefOrderNoDTO createMergeDTO = createMergeBatchCreateDTO(batchCreateByRefOrderNoDTO);
                iBatchOrderProcessService.createBatchByRefOrderNo(createMergeDTO);
                return;
            }

            Boolean openSecondSort = globalCache.isOpenSecondSortFromCache(batchCreateByRefOrderNoDTO.getWarehouseId());
            if (openSecondSort) {
                // 当前仓库开启了二次分拣
                iBatchOrderProcessService.createBatchByRefOrderNo(batchCreateByRefOrderNoDTO);
                return;
            }

            deliveryTaskList.stream().filter(dto -> !CollectionUtils.isEmpty(dto.getOrderNoList()))
                .forEach(deliveryTaskDTO -> iBatchOrderProcessService
                    .createBatchByRefOrderNo(createSecondPickDTO(batchCreateByRefOrderNoDTO, deliveryTaskDTO)));

        } catch (Exception e) {
            LOG.error("新增波次失败", e);
        }
    }

    private BatchCreateByRefOrderNoDTO createSecondPickDTO(BatchCreateByRefOrderNoDTO createDTO,
        DeliveryTaskDTO deliveryTaskDTO) {
        BatchCreateByRefOrderNoDTO newBatchCreateByRefOrderNoDTO = new BatchCreateByRefOrderNoDTO();
        BeanUtils.copyProperties(createDTO, newBatchCreateByRefOrderNoDTO);
        newBatchCreateByRefOrderNoDTO.setRefOrderNos(deliveryTaskDTO.getOrderNoList());
        newBatchCreateByRefOrderNoDTO.setDeliveryCarId(deliveryTaskDTO.getDeliveryCarId());
        newBatchCreateByRefOrderNoDTO.setDriverName(deliveryTaskDTO.getDeliveryUserName());
        newBatchCreateByRefOrderNoDTO
            .setTitle(newBatchCreateByRefOrderNoDTO.getTitle() + deliveryTaskDTO.getDeliveryUserName());
        newBatchCreateByRefOrderNoDTO.setDeliveryTaskList(Collections.singletonList(deliveryTaskDTO));
        List<BatchCreateOrderDTO> orderList = newBatchCreateByRefOrderNoDTO.getOrderList();
        if (!CollectionUtils.isEmpty(orderList)) {
            newBatchCreateByRefOrderNoDTO.setOrderList(
                orderList.stream().filter(o -> deliveryTaskDTO.getOrderNoList().contains(o.getRefOrderNo()))
                    .collect(Collectors.toList()));

        }

        return newBatchCreateByRefOrderNoDTO;
    }

    private BatchCreateByRefOrderNoDTO
        createMergeBatchCreateDTO(BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO) {
        BatchCreateByRefOrderNoDTO copyDTO = new BatchCreateByRefOrderNoDTO();
        BeanUtils.copyProperties(batchCreateByRefOrderNoDTO, copyDTO);
        copyDTO.setRefOrderNos(batchCreateByRefOrderNoDTO.getRefOrderNos());
        copyDTO.setDeliveryTaskList(Collections.emptyList());
        copyDTO.setTitle(getTitle(batchCreateByRefOrderNoDTO));
        // copyDTO.setTitle(getTitle(copyDTO.getTitle()));

        return copyDTO;
    }

    private String getTitle(BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO) {
        String titile =
            StringUtils.isEmpty(batchCreateByRefOrderNoDTO.getTitle()) ? "" : batchCreateByRefOrderNoDTO.getTitle();
        List<DeliveryTaskDTO> deliveryTaskList = batchCreateByRefOrderNoDTO.getDeliveryTaskList().stream()
            .filter(deliveryTask -> !CollectionUtils.isEmpty(deliveryTask.getOrderNoList()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deliveryTaskList)) {
            return titile;
        }
        int taskSize = deliveryTaskList.size();
        int limit = taskSize > LIMIT ? 3 : taskSize;
        for (int i = 0; i < limit; i++) {
            DeliveryTaskDTO deliveryTaskDTO = deliveryTaskList.get(i);
            OutStockOrderPO outStockOrderPO = outStockOrderMapper.findRoutInfoByRefOrderNo(
                deliveryTaskDTO.getOrderNoList(), batchCreateByRefOrderNoDTO.getWarehouseId());
            titile = titile.concat(getTitle(deliveryTaskDTO, outStockOrderPO));
        }

        return titile;
    }

    private String getTitle(DeliveryTaskDTO deliveryTaskDTO, OutStockOrderPO outStockOrderPO) {
        if (Objects.isNull(outStockOrderPO)) {
            return Strings.EMPTY;
        }
        String deliveryUserName = deliveryTaskDTO.getDeliveryUserName();
        if (StringUtils.isEmpty(deliveryUserName)) {
            return outStockOrderPO.getRouteName();
        }
        return outStockOrderPO.getRouteName().concat("-").concat(deliveryUserName).concat(";");
    }

    private boolean isMergeTask(BatchCreateByRefOrderNoDTO batchCreateByRefOrderNoDTO,
        BatchCreateByRefOrderNoExtendDTO extendDTO) {
        if (Objects.isNull(extendDTO)) {
            return Boolean.FALSE;
        }

        if (CollectionUtils.isEmpty(batchCreateByRefOrderNoDTO.getDeliveryTaskList())) {
            return Boolean.FALSE;
        }

        if (batchCreateByRefOrderNoDTO.getDeliveryTaskList().size() <= 1) {
            return Boolean.FALSE;
        }

        if (ConditionStateEnum.否.getType().equals(extendDTO.getMergeTask())) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    private BatchCreateByRefOrderNoExtendDTO getExtendInfo(BatchCreateByRefOrderNoDTO createDTO) {
        if (Objects.isNull(createDTO)) {
            return null;
        }

        String extendInfo = createDTO.getExtendInfo();
        if (StringUtils.isEmpty(extendInfo)) {
            return null;
        }

        BatchCreateByRefOrderNoExtendDTO extendDTO =
            JSON.parseObject(extendInfo, BatchCreateByRefOrderNoExtendDTO.class);
        if (Objects.isNull(extendDTO)) {
            return null;
        }

        return extendDTO;
    }

}
