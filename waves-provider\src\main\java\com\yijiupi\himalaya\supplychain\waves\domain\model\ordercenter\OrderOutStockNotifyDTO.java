package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
public class OrderOutStockNotifyDTO implements Serializable {
    private static final long serialVersionUID = -4158970753152144959L;

    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 出库时间
     */
    private Date outStockTime;
    /**
     * 操作人
     */
    private String optUserId;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 订单出库明细
     */
    private List<OrderItemOutStockDTO> orderItemOutStockList;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Date getOutStockTime() {
        return outStockTime;
    }

    public void setOutStockTime(Date outStockTime) {
        this.outStockTime = outStockTime;
    }

    public String getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(String optUserId) {
        this.optUserId = optUserId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public List<OrderItemOutStockDTO> getOrderItemOutStockList() {
        return orderItemOutStockList;
    }

    public void setOrderItemOutStockList(List<OrderItemOutStockDTO> orderItemOutStockList) {
        this.orderItemOutStockList = orderItemOutStockList;
    }

    public static class OrderItemOutStockDTO implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * oms订单id
         */
        private Long orderItemId;
        /**
         * 小件总数
         */
        private BigDecimal unitTotalCount;
        /**
         * 订单出库二级货主模型
         */
        private List<OrderOutStockDealerDTO> orderOutStockDealerList;

        public Long getOrderItemId() {
            return orderItemId;
        }

        public void setOrderItemId(Long orderItemId) {
            this.orderItemId = orderItemId;
        }

        public BigDecimal getUnitTotalCount() {
            return unitTotalCount;
        }

        public void setUnitTotalCount(BigDecimal unitTotalCount) {
            this.unitTotalCount = unitTotalCount;
        }

        public List<OrderOutStockDealerDTO> getOrderOutStockDealerList() {
            return orderOutStockDealerList;
        }

        public void setOrderOutStockDealerList(List<OrderOutStockDealerDTO> orderOutStockDealerList) {
            this.orderOutStockDealerList = orderOutStockDealerList;
        }
    }

    public static class OrderOutStockDealerDTO implements Serializable {
        private static final long serialVersionUID = -8068405065638522210L;
        /**
         * 二级货主
         */
        private Long secOwnerId;
        /**
         * 数量
         */
        private BigDecimal unitTotalCount;
        /**
         * 生产日期
         */
        private Date productionDate;
        /**
         * 货主id
         */
        private Long ownerId;
        /**
         * 产品规格id
         */
        private Long productSpecificationId;

        public Long getSecOwnerId() {
            return secOwnerId;
        }

        public void setSecOwnerId(Long secOwnerId) {
            this.secOwnerId = secOwnerId;
        }

        public BigDecimal getUnitTotalCount() {
            return unitTotalCount;
        }

        public void setUnitTotalCount(BigDecimal unitTotalCount) {
            this.unitTotalCount = unitTotalCount;
        }

        public Date getProductionDate() {
            return productionDate;
        }

        public void setProductionDate(Date productionDate) {
            this.productionDate = productionDate;
        }

        public Long getOwnerId() {
            return ownerId;
        }

        public void setOwnerId(Long ownerId) {
            this.ownerId = ownerId;
        }

        public Long getProductSpecificationId() {
            return productSpecificationId;
        }

        public void setProductSpecificationId(Long productSpecificationId) {
            this.productSpecificationId = productSpecificationId;
        }
    }

}
