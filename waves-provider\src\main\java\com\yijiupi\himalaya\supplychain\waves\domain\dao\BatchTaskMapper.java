package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.QueryAssignOrderSorterResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.pickingEndingLocationInfoBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskCompletePO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskQueryPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskSortPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.TransferSkuIdDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.*;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.digital.DigitalBatchTaskQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskQueryDTO;

/**
 * 波次任务
 *
 * <AUTHOR> 2018/3/16
 */
@Mapper
public interface BatchTaskMapper {

    /**
     * 查询波次任务列表
     * 
     * <AUTHOR>
     */
    PageResult<BatchTaskPO> findList(@Param("dto") BatchTaskQueryDTO batchTaskQueryDTO,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据拣货任务Id，查找此拣货任务关联的波次下边所有拣货任务
     *
     * @return
     */
    List<BatchTaskPO> findBatchsByTaskId(@Param("taskId") String batchTask_id);

    /**
     * 根据拣货任务编号，查找此拣货任务关联的波次下边所有拣货任务
     *
     * @return
     */
    List<BatchTaskPO> findBatchsByTaskNo(@Param("taskNo") String batchTaskNo);

    /**
     * 根据拣货任务Id，查找拣货任务
     *
     * @return
     */
    BatchTaskPO findBatchTaskById(@Param("taskId") String batchTask_id);

    /**
     * 根据拣货任务编号，查找拣货任务
     *
     * @return
     */
    BatchTaskPO findBatchTaskByNo(@Param("taskNo") String batchTaskNo);

    /**
     * 查找已拣货的波次任务列表
     */
    PageResult<BatchTaskSortPO> findBatchTaskSortList(BatchTaskSortQueryDTO batchTaskSortQueryDTO);

    /**
     * 查找待拣货的波次任务列表
     */
    PageResult<BatchTaskSortPO> findBatchTaskSortListByReceive(BatchTaskSortQueryDTO batchTaskSortQueryDTO);

    /**
     * 获取团购订单摘果任务列表
     *
     * @return
     */
    PageResult<GroupBuyBatchTaskDTO> listBatchTaskByGroupBuy(GroupBuyBatchTaskSO queryDTO);

    /**
     * 批量新增波次任务
     *
     * @param batchTaskPOs
     * @return
     */
    int insertList(@Param("batchTaskPOs") List<BatchTaskPO> batchTaskPOs);

    /**
     * 修改波次任务状态
     */
    void updateBatchTaskById(@Param("id") String batchTaskId, @Param("taskState") byte taskState,
        @Param("locationId") Long locationId, @Param("locationName") String locationName,
        @Param("pickUserId") Integer pickUserId, @Param("pickUser") String pickUser,
        @Param("startPickFlag") Boolean startPickFlag, @Param("orgId") Integer orgId,
        @Param("toPalletNo") String toPalletNo);

    /**
     * 批量更新拣货任务状态为已完成
     */
    void updateBatchTaskStateCompleteByIds(@Param("list") List<String> batchTaskIds);

    /**
     * 修改波次任务
     *
     * @param batchTaskId
     * @param taskState
     */
    void updateBatchTask(BatchTaskPO batchTaskPO);

    /**
     * 修改波次任务状态
     *
     * @param batchTaskNo
     * @param taskState
     */
    void updateBatchTaskByNo(@Param("taskNo") String batchTaskNo, @Param("taskState") byte taskState);

    /**
     * 批量修改波次任务状态
     *
     * @param batchTaskNo
     * @param taskState
     */
    void updateBatchTaskByNoList(@Param("list") List<String> batchTaskNoList, @Param("taskState") byte taskState);

    /**
     * 修改波次任务
     *
     * @return
     */
    int updateBatch(@Param("list") List<BatchTaskPO> batchTaskPOS, @Param("cityId") Integer cityId);

    /**
     * 记录打印
     *
     * @param taskNoList 拣货单编号
     * <AUTHOR>
     */
    void updatePrintCount(@Param("list") List<String> taskNoList, @Param("operateUser") String operateUser);

    /**
     * 修改拣货任务状态为已完成
     *
     * @param batchTaskNo
     * @param operateUser
     */
    void updateTaskState2Complete(@Param("list") List<String> batchTaskNo, @Param("operateUser") String operateUser,
        @Param("operateUserId") Integer operateUserId, @Param("locationId") Long locationId,
        @Param("locationName") String locationName, @Param("sorterName") String sorterName,
        @Param("sorterId") Integer sorterId, @Param("orgId") Integer orgId);

    /**
     * 修改拣货任务的周转区
     *
     * @param batchTaskNo
     * @param operateUser
     */
    void updateTaskLocation(@Param("list") List<String> SowTaskNo, @Param("operateUser") String operateUser,
        @Param("locationId") Long locationId, @Param("locationName") String locationName);

    /**
     * 修改拣货任务的周转区
     */
    void updateTaskLocationByTaskId(@Param("batchTaskIds") List<String> batchTaskIds,
        @Param("operateUser") String operateUser, @Param("locationId") Long locationId,
        @Param("locationName") String locationName, @Param("toPalletNo") String toPalletNo);

    /**
     * 根据波次编号删除波次任务
     *
     * @param batchNo
     */
    void deleteByBatchNo(@Param("list") List<String> batchNo);

    /**
     * 根据波次编号查询波次任务表主键
     *
     * @param batchNo
     * @return
     */
    List<String> findIdByBatchNo(@Param("list") List<String> batchNo);

    List<String> findIdByBatchIds(@Param("list") List<String> batchIds);

    /**
     * 根据波次编号查询波次任务表
     *
     * @param batchNo
     * @return
     */
    List<BatchTaskPO> findTaskByBatchNo(@Param("list") List<String> batchNo);

    /**
     * 根据任务编号查询波次任务表
     *
     * @return
     */
    List<BatchTaskPO> findTasksByBatchTaskNo(@Param("list") List<String> batchTaskNo);

    /**
     * 根据任务编号查询波次任务表
     */
    List<BatchTaskPO> findTasksByBatchTaskId(@Param("list") Collection<String> batchTaskId);

    /**
     * 根据波次任务号查询波次任务.
     */
    BatchTaskCompletePO findByBatchTaskNo(@Param("batchTaskNo") String batchTaskNo);

    /**
     * 根据波次任务号查询波次中是否存未完成的拣货任务
     */
    Integer findNoFinishTaskCountByTaskNo(@Param("batchTaskNo") String batchTaskNo);

    /**
     * 根据播种任务编号查询波次任务
     *
     * @param sowTaskNo
     * @return
     */
    List<BatchTaskPO> listBatchTaskBySowNo(@Param("sowTaskNo") String sowTaskNo, @Param("orgId") Integer orgId);

    /**
     * 根据播种任务编号查询波次任务表
     *
     * @return
     */
    List<BatchTaskPO> findBySowTaskNos(@Param("list") List<String> sowTaskNos, @Param("orgId") Integer orgId,
        @Param("states") List<Byte> states);

    /**
     * 根据拣货任务id获取拣货任务(含有集货位信息)
     *
     * @param batchTaskId
     * @return
     */
    List<BatchTaskPO> findBatchTaskInfoById(@Param("batchTaskId") String batchTaskId);

    /**
     * 根据播种任务编号查询未完成的拣货任务
     *
     * @param sowTaskNo
     * @return
     */
    List<BatchTaskPO> findNoFinishTaskBySowTaskNo(@Param("sowTaskNo") String sowTaskNo, @Param("orgId") Integer orgId);

    /**
     * 查询包含内配单的拣货任务
     *
     * @return
     */
    List<String> findbatchTaskIncludeAllocationById(@Param("orgId") Integer orgId,
        @Param("list") List<String> batchTaskIds);

    /**
     * 获取拣货任务的转换skuId
     *
     * @return
     */
    List<TransferSkuIdDTO> finTransferSkuId(@Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId,
        @Param("itemIds") List<String> batchTaskItemIds, @Param("orderNos") List<String> orderNos);

    List<PickedCountDTO> findPickedCountBySkuIds(@Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId, @Param("list") List<Long> productSkuIds);

    List<PickedDetailDTO> findPickedDetailBySkuIdForSCM25(@Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId, @Param("list") List<Long> productSkuIds);

    /**
     * 根据播种任务编号查询已完成的拣货任务
     *
     * @param sowTaskNo
     * @return
     */
    List<BatchTaskPO> findFinishTaskBySowTaskNo(@Param("sowTaskNo") String sowTaskNo, @Param("orgId") Integer orgId);

    void updateTaskLocationByIds(@Param("orgId") Integer orgId, @Param("batchTaskIds") List<String> batchTaskIds,
        @Param("toLocationId") Long toLocationId, @Param("toLocationName") String toLocationName,
        @Param("operator") String operator);

    /**
     * 根据订单ID获取出库位
     *
     * @param orderId
     * @param orgId
     * @return
     */
    String getToLocationByOrderId(@Param("orderId") Long orderId, @Param("orgId") Integer orgId);

    /**
     * 根据播种任务编号查询波次任务
     */
    List<BatchTaskPO> listBatchTaskRelatedBySowNos(@Param("sowTaskNos") List<String> sowTaskNos,
        @Param("orgId") Integer orgId);

    /**
     * 删除播种任务与拣货任务的关联关系
     *
     * @param sowTaskIds
     */
    void deleteSowTaskBySowTaskIds(@Param("sowTaskIds") List<Long> sowTaskIds);

    List<BatchTaskPO> selectBatchTaskBySowTaskIds(@Param("sowTaskIds") List<Long> sowTaskIds);

    /**
     * 查询已拣货未出库的拣货任务占用的周转区库存数量
     *
     * @return
     */
    List<BatchTaskLocationUseCountDTO> listBatchTaskLocationUseCount(@Param("cityId") Integer cityId,
        @Param("warehouseId") Integer warehouseId);

    List<BatchTaskDTO> findOrderRelatedBatchTaskLocation(@Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId, @Param("orderNo") String orderNo);

    /**
     * 根据拣货任务id删除波次任务
     */
    void deleteByBatchTaskId(@Param("list") List<String> batchTaskIds);

    /**
     * 批量修改拣货任务的统计信息
     *
     * @return
     */
    void updateBatchTaskStatistics(@Param("list") List<BatchTaskPO> batchTaskPOS);

    /**
     * 获取拣货任务不同状态的数量
     *
     * @return
     */
    List<BatchTaskStateCountDTO> getBatchTaskStateCount(@Param("cityId") Integer cityId,
        @Param("warehouseId") Integer warehouseId, @Param("userId") Integer userId);

    /**
     * 获取拣货任务分拣中数量
     *
     * @return
     */
    Integer getSortingBatchTaskCount(@Param("cityId") Integer cityId, @Param("warehouseId") Integer warehouseId,
        @Param("userId") Integer userId);

    /**
     * 获取拣货任务最长已进行时间（天数）
     *
     * @return
     */
    Integer getSortingBatchTaskMaxDays(@Param("cityId") Integer cityId, @Param("warehouseId") Integer warehouseId,
        @Param("userId") Integer userId);

    Integer updateBatchTaskSowTaskById(@Param("batchTaskIds") List<String> batchTaskIds,
        @Param("sowTaskId") Long sowTaskId, @Param("sowTaskNo") String sowTaskNo);

    PageResult<BatchTaskSortPO> findBatchTaskSecondSortList(BatchTaskSortQueryDTO batchTaskSortQueryDTO);

    List<BatchTaskPO> listBatchTask(BatchTaskQueryDTO batchTaskQueryDTO);

    /**
     * 根据id 查询出库位
     *
     * @param idList
     * @param orgId
     * @return
     */
    List<BatchTaskPO> listToLocationNameById(@Param("idList") List<Long> idList, @Param("orgId") Integer orgId);

    int updateByPrimaryKeySelective(BatchTaskPO batchTaskPO);

    int updateToClearRobotInfo(BatchTaskPO batchTaskPO);

    /**
     * 修改波次任务
     *
     * @param batchTaskPO
     */
    void updateBatchTaskContainer(BatchTaskPO batchTaskPO);

    /**
     * 根据拣货任务Id，查找拣货任务
     */
    List<BatchTaskPO> findBatchTaskByIds(@Param("taskIds") Collection<String> taskIds);

    /**
     * 根据订单AddressID查找当前用户还在分拣中的分拣员
     */
    List<QueryAssignOrderSorterResultBO> findAssignOrderSorter(@Param("warehouseId") Integer warehouseId,
        @Param("lstOrderId") List<Long> lstAddressID);

    /**
     * 查找仓库所有未分配分拣工的拣货任务
     */
    List<BatchTaskPO> findNoSorterBatchTaskByWarehouseId(@Param("warehouseId") Integer warehouseId);

    /**
     * 查询仓库中分拣员是否有未完成的任务
     */
    List<Integer> findNotFinishedBatchTaskBySorterAndWarehouseId(@Param("warehouseId") Integer warehouseId,
        @Param("lstSorterId") List<Integer> lstSorterId, @Param("lstTaskNo") List<String> lstTaskNo);

    PageResult<BatchTaskPO> listBatchTaskItem(@Param("sowTaskState") List<Integer> sowTaskState,
        @Param("batchState") List<Integer> batchState, @Param("query") SowTaskQueryDTO sowTaskQueryDTO,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    List<PickedCountDTO> findSowPickedCountBySkuIdForSCM25(@Param("orgId") Integer orgId,
        @Param("warehouseId") Integer warehouseId, @Param("list") List<Long> productSkuIds);

    List<BatchTaskPO> findTaskByBatchIdsAndState(@Param("list") List<String> batchNos,
        @Param("states") List<Byte> states);

    void updatePalletById(@Param("batchTaskId") String batchTaskId, @Param("toPalletNo") String toPalletNo);

    /**
     * 查找
     * 
     * @param queryDTO
     * @return
     */
    List<DigitalBatchTaskDTO> findDigitalBatchTaskInfo(DigitalBatchTaskQueryDTO queryDTO);

    /**
     * 根据拣货任务id修改拣货任务状态
     *
     * @param batchTaskIds
     * @param taskState
     */
    void updateBatchTaskByIdList(@Param("list") List<String> batchTaskIds, @Param("taskState") byte taskState,
        @Param("startTime") Date startTime);

    /**
     * 根据拣货任务id修改拣货方式
     *
     * @param batchTaskIds
     * @param kindOfPicking
     */
    void updateKindOfPickingByIdList(@Param("list") List<String> batchTaskIds,
        @Param("kindOfPicking") Byte kindOfPicking);

    Integer selectMaxTaskSequenceByBatchNo(@Param("batchNo") String batchNo, @Param("orgId") Integer orgId);

    Integer countRobotTaskInBatch(@Param("batchNo") String batchNo, @Param("orgId") Integer orgId);

    List<Long> findBatchTaskOneUserOrderIds(@Param("orgId") Integer orgId, @Param("addressId") Integer addressId,
        @Param("batchTaskId") String batchTaskId);

    List<PickedDetailDTO> findPickedDetailByCondition(PickedDetailQueryDTO queryDTO);

    /**
     * 根据分区id和仓库id，查询最近完成的一个拣货任务id
     *
     * @param batchTaskDTO
     * @return
     */
    BatchTaskDTO getSortGroupLatestBatchTask(BatchTaskDTO batchTaskDTO);

    List<pickingEndingLocationInfoBO>
        findPickingEndingLocationInfoByBatchTaskNos(@Param("batchTaskNos") List<String> batchTaskNos);

    PageResult<BatchTaskPO> findBatchTaskListByCon(@Param("queryPO") BatchTaskQueryPO batchTaskQueryPO,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);
}
