/*
 * @ClassName GatherTaskProductDTO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-10-31 10:56:33
 */
package com.yijiupi.himalaya.supplychain.waves.dto.gathertask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class GatherTaskProductScaleDTO implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 2356774043176587955L;

    /**
     * 出库位明细表id
     */
    private Long id;

    /**
     * @Fields productName 产品名称
     */
    private String productName;
    /**
     * @Fields specificationId 规格ID
     */
    private Long specificationId;
    /**
     * @Fields productSpecName 规格名称
     */
    private String productSpecName;
    /**
     * @Fields specQuantity 规格大小单位转换系数
     */
    private BigDecimal specQuantity;
    /**
     * @Fields totalCount 集货总数量
     */
    private BigDecimal totalCount;

    /**
     * 集货状态
     */
    private Integer status;

    /**
     * @Fields 集货进度
     */
    private String statusScale;
    /**
     * 产品出库位明细
     */
    private List<GatherTaskLocationScaleDTO> LocationScaleList;

    /**
     * 获取 id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 产品名称
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 设置 产品名称
     */
    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    /**
     * 获取 规格ID
     */
    public Long getSpecificationId() {
        return specificationId;
    }

    /**
     * 设置 规格ID
     */
    public void setSpecificationId(Long specificationId) {
        this.specificationId = specificationId;
    }

    /**
     * 获取 规格名称
     */
    public String getProductSpecName() {
        return productSpecName;
    }

    /**
     * 设置 规格名称
     */
    public void setProductSpecName(String productSpecName) {
        this.productSpecName = productSpecName == null ? null : productSpecName.trim();
    }

    /**
     * 获取 规格大小单位转换系数
     */
    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    /**
     * 设置 规格大小单位转换系数
     */
    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    /**
     * 获取 集货总数量
     */
    public BigDecimal getTotalCount() {
        return totalCount;
    }

    /**
     * 设置 集货总数量
     */
    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * @return the status
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * @param status the status to set
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * @return the statusScale
     */
    public String getStatusScale() {
        return statusScale;
    }

    /**
     * @param statusScale the statusScale to set
     */
    public void setStatusScale(String statusScale) {
        this.statusScale = statusScale;
    }

    /**
     * @return the locationScaleList
     */
    public List<GatherTaskLocationScaleDTO> getLocationScaleList() {
        return LocationScaleList;
    }

    /**
     * @param locationScaleList the locationScaleList to set
     */
    public void setLocationScaleList(List<GatherTaskLocationScaleDTO> locationScaleList) {
        LocationScaleList = locationScaleList;
    }
}