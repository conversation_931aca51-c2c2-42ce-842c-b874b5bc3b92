package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.github.pagehelper.PageHelper;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.wcs.RedisBoxCacheBL;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.LocationAreaService;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.SowConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskItemMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowPackageItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.*;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.*;
import com.yijiupi.himalaya.supplychain.waves.enums.OperationModeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.QueryConditionTypeEnum;

import javax.annotation.Resource;

@Service
public class SowQueryBL {

    @Autowired
    private SowTaskMapper sowTaskMapper;

    @Autowired
    private BatchTaskMapper batchTaskMapper;

    @Autowired
    private SowOrderMapper sowOrderMapper;

    @Reference
    private LocationAreaService locationAreaService;

    @Reference
    private IProductSkuService iProductSkuService;

    @Autowired
    private PackageOrderItemBL packageOrderItemBL;

    @Autowired
    private SowTaskItemMapper sowTaskItemMapper;

    @Resource
    private RedisBoxCacheBL redisBoxCacheBL;

    /**
     * 查询播种任务列表
     *
     * @param sowTaskQueryDTO
     * @return
     */
    public PageList<SowTaskDTO> findSowTaskList(SowTaskQueryDTO sowTaskQueryDTO) {
        PageList<SowTaskDTO> sowTaskDTOPageList = new PageList<>();
        PageResult<SowTaskPO> sowTaskPOS =
            sowTaskMapper.findList(sowTaskQueryDTO, sowTaskQueryDTO.getCurrentPage(), sowTaskQueryDTO.getPageSize());
        PageList<SowTaskPO> sowTaskPOPageList = sowTaskPOS.toPageList();
        List<SowTaskDTO> sowTaskDTOS = SowConverter.sowTaskPOS2SowtaskDTOS(sowTaskPOPageList.getDataList());

        // //获取拣货任务信息
        // List<String> sowTaskNos = sowTaskDTOS.stream().map(SowTaskDTO::getSowTaskNo).collect(Collectors.toList());
        // if (CollectionUtils.isNotEmpty(sowTaskNos)) {
        // List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findBySowTaskNos(sowTaskNos, sowTaskQueryDTO.getOrgId(),
        // null);
        // //遍历封装周转区信息
        // sowTaskDTOS.forEach(sowTaskDTO -> {
        // //获取该播种任务下的拣货任务
        // List<BatchTaskPO> batchTaskPOList = batchTaskPOS.stream().filter(batchTaskPO ->
        // batchTaskPO.getSowTaskNo().equals(sowTaskDTO.getSowTaskNo())).collect(Collectors.toList());
        // //周转区字符串拼接
        // List<Long> toLocationIds = batchTaskPOList.stream().filter(item -> item.getToLocationId() !=
        // null).map(BatchTaskPO::getToLocationId).distinct().collect(Collectors.toList());
        // String toLocationNames = batchTaskPOList.stream().filter(item -> item.getToLocationName() !=
        // null).map(BatchTaskPO::getToLocationName).distinct().collect(Collectors.joining(","));
        //
        // if (CollectionUtils.isNotEmpty(toLocationIds)) {
        // sowTaskDTO.setToLocationIds(Joiner.on(",").join(toLocationIds));
        // }
        // sowTaskDTO.setToLocationNames(toLocationNames);
        // });
        // }

        sowTaskDTOPageList.setDataList(sowTaskDTOS);
        sowTaskDTOPageList.setPager(sowTaskPOPageList.getPager());
        return sowTaskDTOPageList;
    }

    /**
     * 根据播种任务编号查询播种任务中的订单信息
     *
     * @param sowTaskNo
     * @return
     */
    public List<SowOrderInfoDTO> listOutStockOrderBySowNo(String sowTaskNo, Integer orgId) {
        List<SowOrderInfoPO> sowOrderInfoPOS = sowTaskMapper.listOutStockOrderBySowTaskNo(sowTaskNo, orgId);
        return SowConverter.sowOrderInfoPOS2SowOrderInfoDTOS(sowOrderInfoPOS);
    }

    /**
     * 根据播种单号，查询播种信息
     */
    public List<SowTaskDTO> findSowTaskByTaskNos(Integer orgId, List<String> sowTaskNos) {
        List<SowTaskPO> sowTaskPOS = sowTaskMapper.findSowTaskByTaskNos(orgId, sowTaskNos);
        return SowConverter.sowTaskPOS2SowtaskDTOS(sowTaskPOS);
    }

    /**
     * 条件查询打包订单
     * 
     * @param unpackOrderQueryDTO
     * @return
     */
    public PageList<UnpackOrderDTO> findUnpackOrderList(UnpackOrderQueryDTO unpackOrderQueryDTO) {
        PageList<UnpackOrderDTO> unpackOrderDTOPageList = new PageList<>();
        PageResult<UnpackOrderDTO> unpackOrderList = sowOrderMapper.findUnpackOrderList(unpackOrderQueryDTO,
            unpackOrderQueryDTO.getCurrentPage(), unpackOrderQueryDTO.getPageSize());
        unpackOrderDTOPageList.setDataList(unpackOrderList.getResult());
        unpackOrderDTOPageList.setPager(unpackOrderList.getPager());
        return unpackOrderDTOPageList;
    }

    /**
     * 查询播种打印数据
     * 
     * @param sowTaskNos
     * @param orgId
     * @return
     */
    public List<SowingPrintInfoDTO> listSowingPrintInfo(List<String> sowTaskNos, Integer orgId) {
        List<SowingPrintInfoDTO> sowingPrintInfoDTOS = new ArrayList<>();
        // 查询播种任务信息列表
        List<SowTaskPO> sowTaskPOS = sowTaskMapper.listSowTaskByNos(sowTaskNos, orgId);
        // 查询播种明细
        List<SowProductItemDTO> sowProductItemDTOS = sowOrderMapper.listSowingPrintItems(sowTaskNos, orgId);

        if (CollectionUtils.isEmpty(sowTaskPOS) || CollectionUtils.isEmpty(sowProductItemDTOS)) {
            throw new BusinessException("播种任务不存在");
        }

        sowTaskPOS.forEach(task -> {

            // 合并该播种任务下的同容器编号中同skuId的数据
            Map<Integer, List<SowProductItemDTO>> containerMap =
                sowProductItemDTOS.stream().filter(item -> item.getSowTaskNo().equals(task.getSowTaskNo()))
                    .collect(Collectors.groupingBy(SowProductItemDTO::getContainerNo));
            List<SowProductItemDTO> sowingPrintItems = MergeSowProductItemDTO(containerMap);

            if (task.getOperationMode() == null || OperationModeEnum.拣货播种.getType() == task.getOperationMode()) {
                sowingPrintItems.forEach(item -> {
                    item.setPickLocationId(task.getLocationId());
                    item.setPickLocationName(task.getLocationName());
                });
            }

            // 查询货位信息
            List<String> locationIds = sowingPrintItems.stream().filter(item -> item.getPickLocationId() != null)
                .map(item -> item.getPickLocationId().toString()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(locationIds)) {
                List<LocationReturnDTO> locationList = locationAreaService.findLocationListById(locationIds);
                Map<Long, List<LocationReturnDTO>> locationMap =
                    locationList.stream().collect(Collectors.groupingBy(LocationReturnDTO::getId));

                // 根据货位顺序对明细项进行排序
                sowingPrintItems.forEach(item -> {
                    List<LocationReturnDTO> locationReturnDTOS = locationMap.get(item.getPickLocationId());
                    if (CollectionUtils.isNotEmpty(locationReturnDTOS)) {
                        Integer locationSequence = locationReturnDTOS.get(0).getSequence();
                        item.setLocationSequence(locationSequence == null ? Integer.MAX_VALUE : locationSequence);
                    } else {
                        item.setLocationSequence(Integer.MAX_VALUE);
                    }
                });
                sowingPrintItems = sowingPrintItems.stream()
                    .sorted(Comparator.comparing(SowProductItemDTO::getLocationSequence)).collect(Collectors.toList());
            }

            SowingPrintInfoDTO sowingPrintInfoDTO = new SowingPrintInfoDTO();
            sowingPrintInfoDTO.setId(task.getId());
            sowingPrintInfoDTO.setSower(task.getOperatorName());
            sowingPrintInfoDTO.setSowTaskNo(task.getSowTaskNo());
            sowingPrintInfoDTO.setSowProductItemDTOS(sowingPrintItems);
            sowingPrintInfoDTO.setContainerCount(containerMap.size());
            sowingPrintInfoDTO.setSowTaskName(task.getSowTaskName());
            sowingPrintInfoDTO.setSowTotalCount(task.getSkuCount());

            sowingPrintInfoDTOS.add(sowingPrintInfoDTO);
        });

        return sowingPrintInfoDTOS;
    }

    /**
     * 合并该播种任务下的同容器编号中同sku的数据
     */
    private List<SowProductItemDTO> MergeSowProductItemDTO(Map<Integer, List<SowProductItemDTO>> containerMap) {
        List<SowProductItemDTO> sowingPrintItems = new ArrayList<>();
        containerMap.forEach((containerNo, sowingPrintInfos) -> {
            Map<Long, List<SowProductItemDTO>> skuMap =
                sowingPrintInfos.stream().collect(Collectors.groupingBy(SowProductItemDTO::getProductSpecificationId));
            skuMap.forEach((productSpecificationId, items) -> {
                SowProductItemDTO sowProductItemDTO = new SowProductItemDTO();
                BeanUtils.copyProperties(items.get(0), sowProductItemDTO);

                BigDecimal sowTotalCount =
                    items.stream().map(SowProductItemDTO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                sowProductItemDTO.setUnitTotalCount(sowTotalCount);
                BigDecimal packageCount =
                    items.stream().map(SowProductItemDTO::getPackageCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                sowProductItemDTO.setPackageCount(packageCount);
                BigDecimal unitCount =
                    items.stream().map(SowProductItemDTO::getUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                sowProductItemDTO.setUnitCount(unitCount);

                sowingPrintItems.add(sowProductItemDTO);
            });
        });
        return sowingPrintItems;
    }

    /**
     * 查询播种产品明细项
     */
    public List<SowProductItemDTO> listSowProductItem(SowProductItemQueryDTO sowProductItemQueryDTO) {
        return sowOrderMapper.listSowProductItem(sowProductItemQueryDTO);
    }

    /**
     * 查询播种任务自提点信息
     */
    public List<SowAddressDTO> listSowAddress(SowAddressQueryDTO sowAddressQueryDTO) {
        return sowOrderMapper.listSowAddressBySowTaskNo(sowAddressQueryDTO);
    }

    /**
     * 根据播种任务查询订单包装信息
     */
    public List<SowProductItemDTO> listSowPackageProductItem(SowProductItemQueryDTO sowProductItemQueryDTO) {

        List<SowProductItemDTO> sowProductItemDTOS = sowOrderMapper.listSowProductItem(sowProductItemQueryDTO);

        if (CollectionUtils.isNotEmpty(sowProductItemDTOS)) {
            Set<Long> skuIds = sowProductItemDTOS.stream().map(SowProductItemDTO::getSkuId).collect(Collectors.toSet());
            List<Long> outStockOrderItemIds =
                sowProductItemDTOS.stream().map(SowProductItemDTO::getOutStockOrderItemId).collect(Collectors.toList());

            // 查询条码信息
            Map<Long, ProductCodeDTO> packageAndUnitCode =
                iProductSkuService.getPackageAndUnitCode(skuIds, sowProductItemQueryDTO.getOrgId());

            // 查询包装信息
            List<PackageOrderItemDTO> packageOrderItemDTOS = packageOrderItemBL
                .listPackageOrderItemByOrderItemId(outStockOrderItemIds, sowProductItemQueryDTO.getOrgId());
            Map<Long, List<PackageOrderItemDTO>> packageOrderItemMap =
                packageOrderItemDTOS.stream().collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderItemId));

            sowProductItemDTOS.forEach(item -> {
                // 封装条码
                ProductCodeDTO productCodeDTO = packageAndUnitCode.get(item.getSkuId());
                if (productCodeDTO != null) {
                    item.setPackageCode(productCodeDTO.getPackageCode());
                    item.setUnitCode(productCodeDTO.getUnitCode());
                }

                // 计算未包装数量
                List<PackageOrderItemDTO> packageOrderItems = packageOrderItemMap.get(item.getOutStockOrderItemId());
                if (CollectionUtils.isNotEmpty(packageOrderItems)) {
                    BigDecimal packedTotalCount = packageOrderItems.stream().map(PackageOrderItemDTO::getPackageCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal surplusTotalCount = item.getPackageCount().subtract(packedTotalCount);
                    item.setPackageOrderItems(packageOrderItems);
                    item.setUnPackagedTotalCount(surplusTotalCount);
                    item.setPackedTotalCount(packedTotalCount);
                } else {
                    item.setPackedTotalCount(BigDecimal.ZERO);
                    item.setUnPackagedTotalCount(item.getPackageCount());
                }

            });
        }

        return sowProductItemDTOS;
    }

    /**
     * 分页查询播种明细数据
     */
    public PageList<SowOrderItemDTO> pageListSowOrderItems(SowOrderItemQueryDTO sowOrderItemQueryDTO) {
        PageResult<SowOrderItemDTO> result = sowTaskItemMapper.pageListSowOrderItems(sowOrderItemQueryDTO,
            sowOrderItemQueryDTO.getPageNum(), sowOrderItemQueryDTO.getPageSize());
        return result.toPageList();
    }

    /**
     * 分页查询自提点播种明细数据
     */
    public PageList<AddressSowTaskItemDTO> pageListAddressSowTaskItems(AddressSowTaskQueryDTO addressSowTaskQueryDTO) {
        PageResult<AddressSowTaskItemDTO> result;
        if (addressSowTaskQueryDTO.getQueryCondition().equals(QueryConditionTypeEnum.明细查询.getType())) {
            AssertUtils.notEmpty(addressSowTaskQueryDTO.getSowTaskItemIds(), "明细id不能为空");
            result = sowTaskItemMapper.pageListAddressSowTaskItemDetails(addressSowTaskQueryDTO,
                addressSowTaskQueryDTO.getPageNum(), addressSowTaskQueryDTO.getPageSize());
        } else {
            result = sowTaskItemMapper.pageListAddressSowTaskItems(addressSowTaskQueryDTO,
                addressSowTaskQueryDTO.getPageNum(), addressSowTaskQueryDTO.getPageSize());
        }

        return result.toPageList();
    }

    public List<SowTaskItemDTO> listSowOTaskItems(SowTaskItemQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO.getOrgId(), "城市id不能为空");
        return SowConverter.sowTaskItemPOS2SowtaskItemDTOS(sowTaskItemMapper.listSowOTaskItems(queryDTO));
    }

    /**
     * 播种任务的打包明细
     */
    public List<SowPackageItemDTO> findSowPackageItems(SowPackageItemQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "请求不能为空");
        AssertUtils.notNull(queryDTO.getOrgId(), "城市不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库不能为空");
        AssertUtils.notNull(queryDTO.getSowTaskNo(), "播种任务编号不能为空");

        List<SowPackageItemPO> sowPackageItemList = sowTaskItemMapper.findSowPackageItems(queryDTO);
        if (CollectionUtils.isEmpty(sowPackageItemList)) {
            return Collections.emptyList();
        }
        List<SowPackageItemDTO> sowPkgItemResult = new ArrayList<>();
        sowPackageItemList.stream()
            .collect(
                Collectors.groupingBy(elem -> String.format("%s_%s", elem.getOrderNo(), elem.getSowOrderSequence())))
            .forEach((key, sowPkgItemList) -> {
                SowPackageItemPO firstPkgItem = sowPkgItemList.get(0);
                SowPackageItemDTO sowPkgItemDTO = new SowPackageItemDTO();
                BeanUtils.copyProperties(firstPkgItem, sowPkgItemDTO);

                Set<String> boxCodeNoSet = new TreeSet<>();
                BigDecimal packageCount = BigDecimal.ZERO;
                BigDecimal unitCount = BigDecimal.ZERO;
                for (SowPackageItemPO pkgItem : sowPkgItemList) {
                    if (StringUtils.isNotEmpty(pkgItem.getBoxCodeNo())) {
                        boxCodeNoSet.add(pkgItem.getBoxCodeNo());
                    }
                    if (pkgItem.getPackageCount() != null) {
                        packageCount = packageCount.add(pkgItem.getPackageCount());
                    }
                    if (pkgItem.getUnitCount() != null) {
                        unitCount = unitCount.add(pkgItem.getUnitCount());
                    }
                }

                sowPkgItemDTO.setBoxCount(boxCodeNoSet.size());
                sowPkgItemDTO.setBoxCodeNoList(new ArrayList<>(boxCodeNoSet));
                sowPkgItemDTO.setPackageCount(packageCount);
                sowPkgItemDTO.setUnitCount(unitCount);
                sowPkgItemResult.add(sowPkgItemDTO);
            });

        return sowPkgItemResult;
    }

    public List<SowTaskItemDTO> findByOrderItemIds(List<Long> orderItemIds) {
        AssertUtils.notEmpty(orderItemIds, "orderItemIds不能为空");
        return SowConverter.sowTaskItemPOS2SowtaskItemDTOS(sowTaskItemMapper.findByOrderItemIds(orderItemIds));
    }

    public List<Long> findByLocationIds(Integer orgId, Integer warehouseId, List<Long> locationIds) {
        AssertUtils.notNull(orgId, "orgId不能为空");
        AssertUtils.notNull(warehouseId, "warehouseId不能为空");
        AssertUtils.notEmpty(locationIds, "locationIds不能为空");
        return sowTaskMapper.findByLocationIds(orgId, warehouseId, locationIds);
    }

    public List<SowTaskLocationDTO> findSowTaskBySkuIds(SowTaskQueryDTO queryDTO) {
        AssertUtils.notEmpty(queryDTO.getSkuIds(), "skuIds 不能为空");
        AssertUtils.notEmpty(queryDTO.getStates(), "states 不能为空");
        AssertUtils.notNull(queryDTO.getOrgId(), "orgId 不能为空");
        AssertUtils.notNull(queryDTO.getOperatorId(), "operatorId 不能为空");
        List<SowTaskPO> sowTaskList = sowTaskMapper.findSowTaskBySkuIds(queryDTO);
        List<SowTaskDTO> sowTaskDTOList = SowConverter.sowTaskPOS2SowtaskDTOS(sowTaskList);
        return sowTaskDTOList.stream()
                .map(it -> SowTaskLocationDTO.of(it.getLocationId(), it.getLocationName()))
                .collect(Collectors.toList());
    }


    public List<String> listWcsBoxNosBySowTaskId(Integer warehouseId, Long sowTaskId) {
        AssertUtils.notNull(warehouseId, "仓库 id 不能为空");
        AssertUtils.notNull(sowTaskId, "播种任务 id 不能为空");
        return redisBoxCacheBL.listWcsBoxNosBySowTaskId(warehouseId, sowTaskId);
    }

    public PageList<SowTaskDTO> pageListAppendableSowTask(SowTaskQueryDTO query) {
        AssertUtils.notNull(query.getWarehouseId(), "仓库 id 不能为空");
        PageHelper.startPage(query.getCurrentPage(), query.getPageSize());
        return sowTaskMapper.pageListAppendableSowTask(query).toPageList(SowConverter::sowTaskPOS2SowtaskDTOS);
    }
}
