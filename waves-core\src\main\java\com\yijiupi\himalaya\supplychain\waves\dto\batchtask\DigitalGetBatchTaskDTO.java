package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/10/31
 */
public class DigitalGetBatchTaskDTO implements Serializable {
    /**
     * 拣货任务号
     */
    private String batchTaskNo;
    /**
     * 拣货任务id
     */
    private String batchTaskId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 获取 拣货任务号
     *
     * @return batchTaskNo 拣货任务号
     */
    public String getBatchTaskNo() {
        return this.batchTaskNo;
    }

    /**
     * 设置 拣货任务号
     *
     * @param batchTaskNo 拣货任务号
     */
    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    /**
     * 获取 拣货任务id
     *
     * @return batchTaskId 拣货任务id
     */
    public String getBatchTaskId() {
        return this.batchTaskId;
    }

    /**
     * 设置 拣货任务id
     *
     * @param batchTaskId 拣货任务id
     */
    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }
}
