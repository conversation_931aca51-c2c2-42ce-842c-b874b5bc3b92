package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/18
 */
public class ScanOrderForReviewLocationInfoDTO implements Serializable {

    /**
     * 出库位id
     */
    private Long locationId;
    /**
     * 出库位名称
     */
    private String locationName;
    /**
     * 托盘位
     */
    private String palletNo;

    /**
     * 获取 出库位id
     *
     * @return locationId 出库位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 出库位id
     *
     * @param locationId 出库位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 出库位名称
     *
     * @return locationName 出库位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 出库位名称
     *
     * @param locationName 出库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 托盘位
     *
     * @return palletNo 托盘位
     */
    public String getPalletNo() {
        return this.palletNo;
    }

    /**
     * 设置 托盘位
     *
     * @param palletNo 托盘位
     */
    public void setPalletNo(String palletNo) {
        this.palletNo = palletNo;
    }
}
