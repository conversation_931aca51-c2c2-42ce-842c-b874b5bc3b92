package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/14
 */
public class UpdateOrderItemCountDTO  implements Serializable {

    private String batchTaskItemId;

    private BigDecimal unitTotalCount;

    private BigDecimal packageCount;

    private Long orderItemTaskInfoId;


    /**
     * 获取
     *
     * @return batchTaskItemId
     */
    public String getBatchTaskItemId() {
        return this.batchTaskItemId;
    }

    /**
     * 设置
     *
     * @param batchTaskItemId
     */
    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    /**
     * 获取
     *
     * @return unitTotalCount
     */
    public BigDecimal getUnitTotalCount() {
        return this.unitTotalCount;
    }

    /**
     * 设置
     *
     * @param unitTotalCount
     */
    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    /**
     * 获取
     *
     * @return orderItemTaskInfoId
     */
    public Long getOrderItemTaskInfoId() {
        return this.orderItemTaskInfoId;
    }

    /**
     * 设置
     *
     * @param orderItemTaskInfoId
     */
    public void setOrderItemTaskInfoId(Long orderItemTaskInfoId) {
        this.orderItemTaskInfoId = orderItemTaskInfoId;
    }

    /**
     * 获取
     *
     * @return packageCount
     */
    public BigDecimal getPackageCount() {
        return this.packageCount;
    }

    /**
     * 设置
     *
     * @param packageCount
     */
    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }
}
