package com.yijiupi.himalaya.supplychain.waves.dto.sowtask;

import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class PDASowTaskRefOutStockOrderItemDTO implements Serializable {

    /**
     * 订单项id
     */
    private Long id;

    /**
     * 关联出库单表id
     */
    private Long outstockorder_Id;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 订单编号
     */
    private String refOrderNo;

    /**
     * 货位顺序
     */
    private Integer locationSequence;

    /**
     * 领取状态：0:待领,1:已领,2:已播种,3:不可领
     */
    private Byte receiveState;

    /**
     * 线路名称
     */
    private String routeName;

    /**
     * 波次任务编号
     */
    private String batchTaskNo;

    /**
     * 波次任务Id
     */
    private String batchtask_Id;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 播种任务明细id
     */
    private Long sowTaskItemId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 产品SKUid
     */
    private Long skuId;

    /**
     * 包装规格
     */
    private String specName;

    /**
     * 销售规格名称
     */
    private String saleSpec;

    /**
     * 播种任务明细状态
     */
    private Byte sowTaskItemState;

    /**
     * 订单序号
     */
    private Integer orderSequence;

    /**
     * 下单地址区域
     */
    private String county;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 收货人姓名
     */
    private String userName;

    /**
     * 二级仓出库位名称
     */
    private String secondLocationName;

    /**
     * 二级仓库名称
     */
    private String secondWarehouseName;

    /**
     * 出库单类型.
     */
    private Byte outBoundType;

    /**
     * 出库位Id
     */
    private Long toLocationId;

    /**
     * 出库位名称
     */
    private String toLocationName;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 大单位数量
     */
    private BigDecimal packageCount;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 小单位数量
     */
    private BigDecimal unitCount;

    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal specQuantity;

    /**
     * 销售规格系数
     */
    private BigDecimal saleSpecQuantity;

    /**
     * 播种订单序号
     */
    private Integer sowOrderSequence;

    /**
     * 多箱号
     */
    private List<String> boxCodeList;

    /**
     * 箱码集合
     *
     */
    private List<String> packageCode;

    /**
     * 瓶码集合
     *
     */
    private List<String> unitCode;


    public PDASowTaskRefOutStockOrderItemDTO() {
    }

    public PDASowTaskRefOutStockOrderItemDTO(OutStockOrderItemDTO outStockOrderItem) {
        this.id = outStockOrderItem.getId();
        this.outstockorder_Id = outStockOrderItem.getOutstockorder_Id();
        this.businessId = outStockOrderItem.getBusinessId();
        this.refOrderNo = outStockOrderItem.getRefOrderNo();
        this.locationSequence = outStockOrderItem.getLocationSequence();
        this.receiveState = outStockOrderItem.getReceiveState();
        this.routeName = outStockOrderItem.getRouteName();
        this.batchTaskNo = outStockOrderItem.getBatchTaskNo();
        this.batchtask_Id = outStockOrderItem.getBatchtask_Id();
        this.sowTaskId = outStockOrderItem.getSowTaskId();
        this.sowTaskNo = outStockOrderItem.getSowTaskNo();
        this.sowTaskItemId = outStockOrderItem.getSowTaskItemId();
        this.productName = outStockOrderItem.getProductName();
        this.skuId = outStockOrderItem.getSkuId();
        this.specName = outStockOrderItem.getSpecName();
        this.saleSpec = outStockOrderItem.getSaleSpec();
        this.sowTaskItemState = outStockOrderItem.getSowTaskItemState();
        this.orderSequence = outStockOrderItem.getOrderSequence();
        this.county = outStockOrderItem.getCounty();
        this.shopName = outStockOrderItem.getShopName();
        this.userName = outStockOrderItem.getUserName();
        this.secondLocationName = outStockOrderItem.getSecondLocationName();
        this.secondWarehouseName = outStockOrderItem.getSecondWarehouseName();
        this.outBoundType = outStockOrderItem.getOutBoundType();
        this.toLocationId = outStockOrderItem.getToLocationId();
        this.toLocationName = outStockOrderItem.getToLocationName();
        this.packageName = outStockOrderItem.getPackageName();
        this.packageCount = outStockOrderItem.getPackageCount();
        this.unitName = outStockOrderItem.getUnitName();
        this.unitCount = outStockOrderItem.getUnitCount();
        this.unitTotalCount = outStockOrderItem.getUnitTotalCount();
        this.specQuantity = outStockOrderItem.getSpecQuantity();
        this.saleSpecQuantity = outStockOrderItem.getSaleSpecQuantity();
        this.sowOrderSequence = outStockOrderItem.getSowOrderSequence();
        this.boxCodeList = outStockOrderItem.getBoxCodeList();
        this.packageCode = outStockOrderItem.getPackageCode();
        this.unitCode = outStockOrderItem.getUnitCode();
    }

    /**
     * 获取 订单项id
     *
     * @return id 订单项id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 订单项id
     *
     * @param id 订单项id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 关联出库单表id
     *
     * @return outstockorder_Id 关联出库单表id
     */
    public Long getOutstockorder_Id() {
        return this.outstockorder_Id;
    }

    /**
     * 设置 关联出库单表id
     *
     * @param outstockorder_Id 关联出库单表id
     */
    public void setOutstockorder_Id(Long outstockorder_Id) {
        this.outstockorder_Id = outstockorder_Id;
    }

    /**
     * 获取 业务ID
     *
     * @return businessId 业务ID
     */
    public String getBusinessId() {
        return this.businessId;
    }

    /**
     * 设置 业务ID
     *
     * @param businessId 业务ID
     */
    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    /**
     * 获取 订单编号
     *
     * @return refOrderNo 订单编号
     */
    public String getRefOrderNo() {
        return this.refOrderNo;
    }

    /**
     * 设置 订单编号
     *
     * @param refOrderNo 订单编号
     */
    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    /**
     * 获取 货位顺序
     *
     * @return locationSequence 货位顺序
     */
    public Integer getLocationSequence() {
        return this.locationSequence;
    }

    /**
     * 设置 货位顺序
     *
     * @param locationSequence 货位顺序
     */
    public void setLocationSequence(Integer locationSequence) {
        this.locationSequence = locationSequence;
    }

    /**
     * 获取 领取状态：0:待领1:已领2:已播种3:不可领
     *
     * @return receiveState 领取状态：0:待领1:已领2:已播种3:不可领
     */
    public Byte getReceiveState() {
        return this.receiveState;
    }

    /**
     * 设置 领取状态：0:待领1:已领2:已播种3:不可领
     *
     * @param receiveState 领取状态：0:待领1:已领2:已播种3:不可领
     */
    public void setReceiveState(Byte receiveState) {
        this.receiveState = receiveState;
    }

    /**
     * 获取 线路名称
     *
     * @return routeName 线路名称
     */
    public String getRouteName() {
        return this.routeName;
    }

    /**
     * 设置 线路名称
     *
     * @param routeName 线路名称
     */
    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    /**
     * 获取 波次任务编号
     *
     * @return batchTaskNo 波次任务编号
     */
    public String getBatchTaskNo() {
        return this.batchTaskNo;
    }

    /**
     * 设置 波次任务编号
     *
     * @param batchTaskNo 波次任务编号
     */
    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    /**
     * 获取 波次任务Id
     *
     * @return batchtask_Id 波次任务Id
     */
    public String getBatchtask_Id() {
        return this.batchtask_Id;
    }

    /**
     * 设置 波次任务Id
     *
     * @param batchtask_Id 波次任务Id
     */
    public void setBatchtask_Id(String batchtask_Id) {
        this.batchtask_Id = batchtask_Id;
    }

    /**
     * 获取 播种任务id
     *
     * @return sowTaskId 播种任务id
     */
    public Long getSowTaskId() {
        return this.sowTaskId;
    }

    /**
     * 设置 播种任务id
     *
     * @param sowTaskId 播种任务id
     */
    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    /**
     * 获取 播种任务编号
     *
     * @return sowTaskNo 播种任务编号
     */
    public String getSowTaskNo() {
        return this.sowTaskNo;
    }

    /**
     * 设置 播种任务编号
     *
     * @param sowTaskNo 播种任务编号
     */
    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    /**
     * 获取 播种任务明细id
     *
     * @return sowTaskItemId 播种任务明细id
     */
    public Long getSowTaskItemId() {
        return this.sowTaskItemId;
    }

    /**
     * 设置 播种任务明细id
     *
     * @param sowTaskItemId 播种任务明细id
     */
    public void setSowTaskItemId(Long sowTaskItemId) {
        this.sowTaskItemId = sowTaskItemId;
    }

    /**
     * 获取 商品名称
     *
     * @return productName 商品名称
     */
    public String getProductName() {
        return this.productName;
    }

    /**
     * 设置 商品名称
     *
     * @param productName 商品名称
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 获取 产品SKUid
     *
     * @return skuId 产品SKUid
     */
    public Long getSkuId() {
        return this.skuId;
    }

    /**
     * 设置 产品SKUid
     *
     * @param skuId 产品SKUid
     */
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    /**
     * 获取 包装规格
     *
     * @return specName 包装规格
     */
    public String getSpecName() {
        return this.specName;
    }

    /**
     * 设置 包装规格
     *
     * @param specName 包装规格
     */
    public void setSpecName(String specName) {
        this.specName = specName;
    }

    /**
     * 获取 销售规格名称
     *
     * @return saleSpec 销售规格名称
     */
    public String getSaleSpec() {
        return this.saleSpec;
    }

    /**
     * 设置 销售规格名称
     *
     * @param saleSpec 销售规格名称
     */
    public void setSaleSpec(String saleSpec) {
        this.saleSpec = saleSpec;
    }

    /**
     * 获取 播种任务明细状态
     *
     * @return sowTaskItemState 播种任务明细状态
     */
    public Byte getSowTaskItemState() {
        return this.sowTaskItemState;
    }

    /**
     * 设置 播种任务明细状态
     *
     * @param sowTaskItemState 播种任务明细状态
     */
    public void setSowTaskItemState(Byte sowTaskItemState) {
        this.sowTaskItemState = sowTaskItemState;
    }

    /**
     * 获取 订单序号
     *
     * @return orderSequence 订单序号
     */
    public Integer getOrderSequence() {
        return this.orderSequence;
    }

    /**
     * 设置 订单序号
     *
     * @param orderSequence 订单序号
     */
    public void setOrderSequence(Integer orderSequence) {
        this.orderSequence = orderSequence;
    }

    /**
     * 获取 下单地址区域
     *
     * @return county 下单地址区域
     */
    public String getCounty() {
        return this.county;
    }

    /**
     * 设置 下单地址区域
     *
     * @param county 下单地址区域
     */
    public void setCounty(String county) {
        this.county = county;
    }

    /**
     * 获取 店铺名称
     *
     * @return shopName 店铺名称
     */
    public String getShopName() {
        return this.shopName;
    }

    /**
     * 设置 店铺名称
     *
     * @param shopName 店铺名称
     */
    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    /**
     * 获取 收货人姓名
     *
     * @return userName 收货人姓名
     */
    public String getUserName() {
        return this.userName;
    }

    /**
     * 设置 收货人姓名
     *
     * @param userName 收货人姓名
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * 获取 二级仓出库位名称
     *
     * @return secondLocationName 二级仓出库位名称
     */
    public String getSecondLocationName() {
        return this.secondLocationName;
    }

    /**
     * 设置 二级仓出库位名称
     *
     * @param secondLocationName 二级仓出库位名称
     */
    public void setSecondLocationName(String secondLocationName) {
        this.secondLocationName = secondLocationName;
    }

    /**
     * 获取 二级仓库名称
     *
     * @return secondWarehouseName 二级仓库名称
     */
    public String getSecondWarehouseName() {
        return this.secondWarehouseName;
    }

    /**
     * 设置 二级仓库名称
     *
     * @param secondWarehouseName 二级仓库名称
     */
    public void setSecondWarehouseName(String secondWarehouseName) {
        this.secondWarehouseName = secondWarehouseName;
    }

    /**
     * 获取 出库单类型.
     *
     * @return outBoundType 出库单类型.
     */
    public Byte getOutBoundType() {
        return this.outBoundType;
    }

    /**
     * 设置 出库单类型.
     *
     * @param outBoundType 出库单类型.
     */
    public void setOutBoundType(Byte outBoundType) {
        this.outBoundType = outBoundType;
    }

    /**
     * 获取 出库位Id
     *
     * @return toLocationId 出库位Id
     */
    public Long getToLocationId() {
        return this.toLocationId;
    }

    /**
     * 设置 出库位Id
     *
     * @param toLocationId 出库位Id
     */
    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    /**
     * 获取 出库位名称
     *
     * @return toLocationName 出库位名称
     */
    public String getToLocationName() {
        return this.toLocationName;
    }

    /**
     * 设置 出库位名称
     *
     * @param toLocationName 出库位名称
     */
    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    /**
     * 获取 大单位名称
     *
     * @return packageName 大单位名称
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * 设置 大单位名称
     *
     * @param packageName 大单位名称
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取 大单位数量
     *
     * @return packageCount 大单位数量
     */
    public BigDecimal getPackageCount() {
        return this.packageCount;
    }

    /**
     * 设置 大单位数量
     *
     * @param packageCount 大单位数量
     */
    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    /**
     * 获取 小单位名称
     *
     * @return unitName 小单位名称
     */
    public String getUnitName() {
        return this.unitName;
    }

    /**
     * 设置 小单位名称
     *
     * @param unitName 小单位名称
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * 获取 小单位数量
     *
     * @return unitCount 小单位数量
     */
    public BigDecimal getUnitCount() {
        return this.unitCount;
    }

    /**
     * 设置 小单位数量
     *
     * @param unitCount 小单位数量
     */
    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    /**
     * 获取 小单位总数量
     *
     * @return unitTotalCount 小单位总数量
     */
    public BigDecimal getUnitTotalCount() {
        return this.unitTotalCount;
    }

    /**
     * 设置 小单位总数量
     *
     * @param unitTotalCount 小单位总数量
     */
    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    /**
     * 获取 包装规格大小单位转换系数
     *
     * @return specQuantity 包装规格大小单位转换系数
     */
    public BigDecimal getSpecQuantity() {
        return this.specQuantity;
    }

    /**
     * 设置 包装规格大小单位转换系数
     *
     * @param specQuantity 包装规格大小单位转换系数
     */
    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    /**
     * 获取 销售规格系数
     *
     * @return saleSpecQuantity 销售规格系数
     */
    public BigDecimal getSaleSpecQuantity() {
        return this.saleSpecQuantity;
    }

    /**
     * 设置 销售规格系数
     *
     * @param saleSpecQuantity 销售规格系数
     */
    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    /**
     * 获取 播种订单序号
     *
     * @return sowOrderSequence 播种订单序号
     */
    public Integer getSowOrderSequence() {
        return this.sowOrderSequence;
    }

    /**
     * 设置 播种订单序号
     *
     * @param sowOrderSequence 播种订单序号
     */
    public void setSowOrderSequence(Integer sowOrderSequence) {
        this.sowOrderSequence = sowOrderSequence;
    }

    /**
     * 获取 多箱号
     *
     * @return boxCodeList 多箱号
     */
    public List<String> getBoxCodeList() {
        return this.boxCodeList;
    }

    /**
     * 设置 多箱号
     *
     * @param boxCodeList 多箱号
     */
    public void setBoxCodeList(List<String> boxCodeList) {
        this.boxCodeList = boxCodeList;
    }

    /**
     * 获取 箱码集合
     *
     * @return packageCode 箱码集合
     */
    public List<String> getPackageCode() {
        return this.packageCode;
    }

    /**
     * 设置 箱码集合
     *
     * @param packageCode 箱码集合
     */
    public void setPackageCode(List<String> packageCode) {
        this.packageCode = packageCode;
    }

    /**
     * 获取 瓶码集合
     *
     * @return unitCode 瓶码集合
     */
    public List<String> getUnitCode() {
        return this.unitCode;
    }

    /**
     * 设置 瓶码集合
     *
     * @param unitCode 瓶码集合
     */
    public void setUnitCode(List<String> unitCode) {
        this.unitCode = unitCode;
    }

}
