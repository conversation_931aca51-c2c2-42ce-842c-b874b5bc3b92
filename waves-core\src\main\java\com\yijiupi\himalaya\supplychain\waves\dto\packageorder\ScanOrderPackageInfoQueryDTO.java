package com.yijiupi.himalaya.supplychain.waves.dto.packageorder;

import java.io.Serializable;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
public class ScanOrderPackageInfoQueryDTO implements Serializable {
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 箱号完整箱码
     */
    private String boxCodeNo;
    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 箱号完整箱码
     *
     * @return boxCodeNo 箱号完整箱码
     */
    public String getBoxCodeNo() {
        return this.boxCodeNo;
    }

    /**
     * 设置 箱号完整箱码
     *
     * @param boxCodeNo 箱号完整箱码
     */
    public void setBoxCodeNo(String boxCodeNo) {
        this.boxCodeNo = boxCodeNo;
    }

    /**
     * 获取 创建时间
     *
     * @return createTime 创建时间
     */
    public String getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置 创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
}
