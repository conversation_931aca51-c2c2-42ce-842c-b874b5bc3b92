package com.yijiupi.himalaya.supplychain.waves.dto.soworder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <AUTHOR>
 * @Date 2022/6/8
 */
public class SowPackageItemDTO implements Serializable {
    private static final long serialVersionUID = 7452414483191725211L;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 播种订单序号
     */
    private Integer sowOrderSequence;
    /**
     * 出库位
     */
    private String toLocationName;
    /**
     * 出库位ID
     */
    private Long toLocationId;
    /**
     * 箱数
     */
    private Integer boxCount;
    /**
     * 箱号列表
     */
    private List<String> boxCodeNoList;
    /**
     * 打包时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date packageTime;
    /**
     * 打包数量-大单位数量
     */
    private BigDecimal packageCount;
    /**
     * 打包数量-小单位数量
     */
    private BigDecimal unitCount;
    /**
     * 打包员名称
     */
    private String operatorName;
    /**
     * 打包员ID
     */
    private Integer operatorId;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getSowOrderSequence() {
        return sowOrderSequence;
    }

    public void setSowOrderSequence(Integer sowOrderSequence) {
        this.sowOrderSequence = sowOrderSequence;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public Integer getBoxCount() {
        return boxCount;
    }

    public void setBoxCount(Integer boxCount) {
        this.boxCount = boxCount;
    }

    public List<String> getBoxCodeNoList() {
        return boxCodeNoList;
    }

    public void setBoxCodeNoList(List<String> boxCodeNoList) {
        this.boxCodeNoList = boxCodeNoList;
    }

    public Date getPackageTime() {
        return packageTime;
    }

    public void setPackageTime(Date packageTime) {
        this.packageTime = packageTime;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }
}
