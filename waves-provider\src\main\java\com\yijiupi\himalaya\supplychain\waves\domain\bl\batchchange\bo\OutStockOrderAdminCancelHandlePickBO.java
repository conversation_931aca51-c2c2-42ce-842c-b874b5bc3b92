package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchchange.bo;

import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: OutStockOrderAdminCancelHandlePickBO
 * @description:
 * @date 2023-05-25 09:12
 */
public class OutStockOrderAdminCancelHandlePickBO {
    /**
     * 出库单
     */
    private OutStockOrderPO outStockOrderPO;
    /**
     * 出库单拣货任务明细
     */
    private List<OrderItemTaskInfoPO> orderItemTaskInfoList;

    /**
     * 波次详情
     */
    private BatchPO batchPO;
    /**
     * 操作人
     */
    private String opUser;

    public OutStockOrderAdminCancelHandlePickBO() {
    }

    public OutStockOrderAdminCancelHandlePickBO(OutStockOrderPO outStockOrderPO, List<OrderItemTaskInfoPO> orderItemTaskInfoList) {
        this.outStockOrderPO = outStockOrderPO;
        this.orderItemTaskInfoList = orderItemTaskInfoList;
    }

    public OutStockOrderAdminCancelHandlePickBO(OutStockOrderPO order, Map<Long, List<OrderItemTaskInfoPO>> infoMap, Map<String, List<BatchPO>> batchMap) {
        this.outStockOrderPO = order;
        this.orderItemTaskInfoList = infoMap.get(order.getId());
        this.batchPO = batchMap.isEmpty() ? null : batchMap.get(order.getBatchno()).get(0);
    }

    /**
     * 获取 出库单
     *
     * @return outStockOrderPO 出库单
     */
    public OutStockOrderPO getOutStockOrderPO() {
        return this.outStockOrderPO;
    }

    /**
     * 设置 出库单
     *
     * @param outStockOrderPO 出库单
     */
    public void setOutStockOrderPO(OutStockOrderPO outStockOrderPO) {
        this.outStockOrderPO = outStockOrderPO;
    }

    /**
     * 获取 出库单拣货任务明细
     *
     * @return orderItemTaskInfoList 出库单拣货任务明细
     */
    public List<OrderItemTaskInfoPO> getOrderItemTaskInfoList() {
        return this.orderItemTaskInfoList;
    }

    /**
     * 设置 出库单拣货任务明细
     *
     * @param orderItemTaskInfoList 出库单拣货任务明细
     */
    public void setOrderItemTaskInfoList(List<OrderItemTaskInfoPO> orderItemTaskInfoList) {
        this.orderItemTaskInfoList = orderItemTaskInfoList;
    }

    public BatchPO getBatchPO() {
        return batchPO;
    }

    public void setBatchPO(BatchPO batchPO) {
        this.batchPO = batchPO;
    }

    /**
     * 获取 操作人
     *
     * @return opUser 操作人
     */
    public String getOpUser() {
        return this.opUser;
    }

    /**
     * 设置 操作人
     *
     * @param opUser 操作人
     */
    public void setOpUser(String opUser) {
        this.opUser = opUser;
    }
}
