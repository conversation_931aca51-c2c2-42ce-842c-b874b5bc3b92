package com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.ordercenter.OrderCenterNotifyBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-01-02 16:34
 **/
@Service
public class OutStockOrderStateBL {

    @Resource
    private OrderCenterNotifyBL orderCenterNotifyBL;
    @Resource
    private OutStockOrderMapper outStockOrderMapper;

    public int batchUpdateOutStockOrder(List<OutStockOrderPO> outStockOrderPOS) {
        int count = outStockOrderMapper.batchUpdateOutStockOrder(outStockOrderPOS);
        List<Long> orderIds = outStockOrderPOS.stream().map(OutStockOrderPO::getId).collect(Collectors.toList());
        orderCenterNotifyBL.syncOutStockOrderStateChangeTrace(orderIds, (byte) 1);
        return count;
    }

    public int updateStateByOrderIds(List<Long> orderIds, Byte state) {
        int count = outStockOrderMapper.updateStateByOrderIds(orderIds, state);
        orderCenterNotifyBL.syncOutStockOrderStateChangeTrace(orderIds, state);
        return count;
    }

    public void cleanBatch(List<String> batchNos, Integer orgId) {
        List<OutStockOrderPO> orders = outStockOrderMapper.findByBatchNos(batchNos, orgId);
        List<Long> orderIds = orders.stream().map(OutStockOrderPO::getId).collect(Collectors.toList());
        outStockOrderMapper.cleanBatch(batchNos);
        orderCenterNotifyBL.syncOutStockOrderStateChangeTrace(orderIds, (byte) 0);
    }

    public void updateBatchByPOList(List<OutStockOrderPO> updateOutOrdList) {
        outStockOrderMapper.updateBatchByPOList(updateOutOrdList);
        Map<Integer, List<OutStockOrderPO>> orderMap = updateOutOrdList.stream().filter(it -> it.getState() != null)
                .collect(Collectors.groupingBy(OutStockOrderPO::getState));
        for (Map.Entry<Integer, List<OutStockOrderPO>> entry : orderMap.entrySet()) {
            List<Long> orderIds = entry.getValue().stream().map(OutStockOrderPO::getId).collect(Collectors.toList());
            if (orderIds.isEmpty()) {
                continue;
            }
            orderCenterNotifyBL.syncOutStockOrderStateChangeTrace(orderIds, entry.getKey().byteValue());
        }
    }

}
