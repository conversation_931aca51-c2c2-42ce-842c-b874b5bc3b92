package com.yijiupi.himalaya.supplychain.waves.service.impl;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.batch.ITaskPerformanceService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.perform.HistoryTaskPerformanceCalculateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.perform.TaskPerformanceCalculateBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.HistoryTaskPerformanceCalDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.performance.TaskPerformanceCalculateDTO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
@Service
public class TaskPerformanceServiceImpl implements ITaskPerformanceService {

    @Autowired
    private HistoryTaskPerformanceCalculateBL historyTaskPerformanceCalculateBL;
    @Autowired
    private TaskPerformanceCalculateBL taskPerformanceCalculateBL;

    /**
     * 计算历史绩效
     *
     * @param dto
     */
    @Override
    public void calculateHistoryTaskPerformance(HistoryTaskPerformanceCalDTO dto) {
        historyTaskPerformanceCalculateBL.calculateHistoryTaskPerformance(dto);
    }

    /**
     * 根据任务id计算任务绩效
     *
     * @param dto
     */
    @Override
    public void calculateTaskPerformance(TaskPerformanceCalculateDTO dto) {
        AssertUtils.notEmpty(dto.getTaskIds(), "任务信息不能为空！");
        AssertUtils.notNull(dto.getType(), "类型信息不能为空！");

        taskPerformanceCalculateBL.calculateTaskPerformance(dto);
    }
}
