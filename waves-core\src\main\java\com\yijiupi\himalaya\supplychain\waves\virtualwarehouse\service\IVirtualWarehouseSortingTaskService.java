package com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.service;

import java.util.List;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto.VirtualWarehouseSortingTaskDetailsDTO;
import com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto.VirtualWarehouseSortingTaskListDTO;
import com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto.VirtualWarehouseSortingTaskRequestDTO;

/**
 * 虚仓分拣任务
 * 
 * <AUTHOR>
 */
public interface IVirtualWarehouseSortingTaskService {
    /**
     * 虚仓查询分拣任务信息(播种任务)
     * 
     * @param request
     * @return
     */
    PageList<VirtualWarehouseSortingTaskListDTO>
        queryWaitSortingTaskList(VirtualWarehouseSortingTaskRequestDTO request);

    /**
     * 查询分拣明细 根据产品维度
     * 
     * @param request
     * @return
     */
    List<VirtualWarehouseSortingTaskDetailsDTO>
        querySortingTaskListDetails(VirtualWarehouseSortingTaskRequestDTO request);

    /**
     * 领取分拣任务
     * 
     * @param request
     */
    void getSortingTask(VirtualWarehouseSortingTaskRequestDTO request);

    /**
     * 查询用户分拣任务信息
     * 
     * @param request
     * @return
     */
    List<VirtualWarehouseSortingTaskDetailsDTO>
        queryUserSortingTaskListDetails(VirtualWarehouseSortingTaskRequestDTO request);

}
