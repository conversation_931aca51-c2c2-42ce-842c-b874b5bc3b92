package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * 合并拣货任务
 * 
 * <AUTHOR>
 * @date 2018/7/11 14:18
 */
public class BatchTaskMergeDTO implements Serializable {
    private static final long serialVersionUID = -6408640490078805598L;

    /**
     * 待合并的拣货编号列表
     */
    private List<String> batchTaskNoList;

    /**
     * 分拣员(姓名)
     */
    private String sorter;
    /**
     * 分拣员id
     */
    private Integer sorterId;

    /**
     * 城市id
     */
    private String orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 操作人
     */
    private String operateUser;
    /**
     * 操作人id
     */
    private Integer optUserId;

    public List<String> getBatchTaskNoList() {
        return batchTaskNoList;
    }

    public void setBatchTaskNoList(List<String> batchTaskNoList) {
        this.batchTaskNoList = batchTaskNoList;
    }

    public String getSorter() {
        return sorter;
    }

    public void setSorter(String sorter) {
        this.sorter = sorter;
    }

    public Integer getSorterId() {
        return sorterId;
    }

    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 操作人id
     *
     * @return optUserId 操作人id
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人id
     *
     * @param optUserId 操作人id
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }
}
