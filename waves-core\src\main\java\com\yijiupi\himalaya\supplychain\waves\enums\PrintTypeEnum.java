package com.yijiupi.himalaya.supplychain.waves.enums;

/**
 * 拣货分组策略 常量类
 *
 * <AUTHOR> 2018/3/13
 */
public enum PrintTypeEnum {
    /**
     * 枚举
     */
    快递面单((byte)0);

    /**
     * type
     */
    private Byte type;

    PrintTypeEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 返回枚举
     *
     * @param type
     * @return
     */
    public static PrintTypeEnum getEnum(Integer type) {
        PrintTypeEnum e = null;

        if (type != null) {
            for (PrintTypeEnum o : values()) {
                if (o.getType() == type) {
                    e = o;
                }
            }
        }
        return e;
    }

    public static String getType(Byte type) {
        if (type == null) {
            return null;
        }
        if (type != null) {
            for (PrintTypeEnum o : values()) {
                if (o.getType() == type) {
                    return o.name();
                }
            }
        }
        return null;
    }
}
