package com.yijiupi.himalaya.supplychain.waves.batch;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.yijiupi.himalaya.supplychain.waves.dto.batch.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.CancelOrderCreateTransferOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.CancelOutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderProcessChangeDTO;

/**
 * 波次操作
 *
 * <AUTHOR>
 * @since 2018/4/8
 */
public interface IBatchManageService {

    /**
     * 根据波次编号删除波次(及波次任务,波次任务详情,释放订单)
     */
    void deleteBatchOrder(DeleteBatchDTO deleteBatchDTO);

    /**
     * 根据波次编号删除波次(及波次任务,波次任务详情,释放订单)
     *
     * @param ignoreBatchInventory 是否忽略货位库存，默认不忽略
     */
    @Deprecated
    void deleteBatchOrder(List<String> batchNo, String operateUser, Boolean ignoreBatchInventory);

    /**
     * 移除订单时，删除波次相关信息
     */
    void deleteBatchByRemoveOrder(List<String> batchNoList);

    /**
     * 根据波次编号查询波次详情
     */
    BatchDTO selectBatchByBatchNo(Integer orgId, String batchNo);

    /**
     * 根据拣货任务编号查询波次详情
     */
    BatchDTO selectBatchByTaskNo(Integer orgId, String taskNo);

    /**
     * 波次出库
     */
    void batchOutStock(BatchOutStockDTO batchOutStockDTO);

    /**
     * 根据订单号修改波次状态
     */
    void updateBatchStateByOrderNos(BatchUpdateDTO batchUpdateDTO);

    /**
     * 根据波次编号完成波次（op后台工具）
     */
    void updateBatchStateByBatchNo(String batchNo);

    void batchUpdateBatchStateByBatchNo(List<String> batchNo);

    /**
     * 处理订单变更时相关拣货逻辑
     */
    void processOrderChangeWithBatch(List<OutStockOrderProcessChangeDTO> processChangeDTOS, String operatorUser,
        Byte changeType);

    /**
     * 根据波次通知TMS更新出库位
     */
    void syncOrderToLocationByScop(String batchNo);

    /**
     * 根据波次通知TMS更新出库位
     */
    void batchSyncOrderToLocationByScop(List<String> batchNos);

    /**
     * 根据车次变更修改波次
     */
    void updateBatchByDeliveryTask(BatchUpdateDTO batchUpdateDTO);

    /**
     * saas删除波次
     */

    /**
     * 根据波次编号删除波次(及波次任务,波次任务详情,释放订单)
     */
    void saasDeleteBatchOrder(DeleteBatchDTO deleteBatchDTO);

    /**
     * 取消出库单
     */
    @Deprecated
    void cancelOutStockOrder(CancelOutStockOrderDTO cancelOutStockOrderDTO);

    /**
     * 已拣货的取消订单生成移库单
     */
    void cancelOrderCreateTransferOrder(CancelOrderCreateTransferOrderDTO dto);

    /**
     * 通过 businessId 和仓库 id 查询订单详情
     *
     * @return 订单详情
     */
    List<ConfirmReturnDTO> orderItemDetail(String businessId, Integer warehouseId);

    void updateBatchTaskItemSkuId(List<BatchTaskItemDTO> batchTaskItemDTOList);

    /**
     * 重算出库单大小件信息
     *
     * @param outBoundNos 出库批次号
     * @param orgId 城市 id
     * @return 计算结果 key 为出库批次号, value 为 大小件信息的 map
     */
    Map<String, OutBoundBatchPieceDTO> reCalPieceInfo(Set<String> outBoundNos, Integer orgId);

    /**
     * 重算入库批次大小件信息
     *
     * @param param 计算参数
     * @return 计算结果 key 为入库批次号, value 为 大小件信息的 map
     */
    Map<String, OutBoundBatchPieceDTO> reCalPieceInfo(List<InBoundBatchPieceParam> param);

    /**
     * 取消出库单处理波次信息
     */
    void cancelOutStockOrderHandleBatchInfo(CancelOutStockOrderDTO cancelOutStockOrderDTO);

    /**
     * 处理异常单处理波次信息
     */
    void exceptionOrderHandleBatchInfo(CancelOutStockOrderDTO cancelOutStockOrderDTO);

    /**
     * 校验波次状态<br/>
     * 若波次内存在订单状态不为 期望状态 则抛出异常
     *
     * @param batchNo 波次编号
     * @param orgId 城市 id
     * @param state 期望状态
     */
    void checkBatchOrderState(String batchNo, Integer orgId, Number state);

    /**
     * 通过拣货任务 id 查询出库单
     *
     * @param batchTaskId 拣货任务 id
     * @param orgId 城市 id
     * @return 出库单
     */
    List<OutStockOrderDTO> selectByBatchTaskId(String batchTaskId, Integer orgId);

    /**
     * 更新拣货任务的托盘信息
     *
     * @param batchTaskId 拣货任务 id
     * @param toPalletNo 托盘信息
     */
    void updateBatchTaskPallet(String batchTaskId, String toPalletNo);

    /**
     * 更新波次状态
     * 
     * @param batchUpdateDTO
     */
    void updateBatchInfo(BatchUpdateDTO batchUpdateDTO);

    /**
     * 波次删除返架产品信息
     */
    List<BatchTaskItemDTO> listBatchReturnProductInfo(DeleteBatchDTO deleteBatchDTO);
}
