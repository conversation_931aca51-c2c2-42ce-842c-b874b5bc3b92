package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/3/11
 */
public class RecoverOrderItemDetailBO {
    /**
     * 波次号
     */
    private String batchNo;
    /**
     * 订单号
     */
    private List<String> refOrderNos;
    /**
     * 业务id列表
     */
    private List<String> businessIds;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 是否使用订单中台的detail数据
     */
    private Boolean useOrderCenterDetail = Boolean.TRUE;
    /**
     * 出库单id
     */
    private List<Long> outStockOrderIds;
    /**
     * 是否处理detail和item数量一样的数据
     */
    private boolean handleEqualDetail = Boolean.FALSE;

    /**
     * 获取 波次号
     *
     * @return batchNo 波次号
     */
    public String getBatchNo() {
        return this.batchNo;
    }

    /**
     * 设置 波次号
     *
     * @param batchNo 波次号
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * 获取 订单号
     *
     * @return refOrderNos 订单号
     */
    public List<String> getRefOrderNos() {
        return this.refOrderNos;
    }

    /**
     * 设置 订单号
     *
     * @param refOrderNos 订单号
     */
    public void setRefOrderNos(List<String> refOrderNos) {
        this.refOrderNos = refOrderNos;
    }

    /**
     * 获取 业务id列表
     *
     * @return businessIds 业务id列表
     */
    public List<String> getBusinessIds() {
        return this.businessIds;
    }

    /**
     * 设置 业务id列表
     *
     * @param businessIds 业务id列表
     */
    public void setBusinessIds(List<String> businessIds) {
        this.businessIds = businessIds;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 是否使用订单中台的detail数据
     *
     * @return useOrderCenterDetail 是否使用订单中台的detail数据
     */
    public Boolean getUseOrderCenterDetail() {
        return this.useOrderCenterDetail;
    }

    /**
     * 设置 是否使用订单中台的detail数据
     *
     * @param useOrderCenterDetail 是否使用订单中台的detail数据
     */
    public void setUseOrderCenterDetail(Boolean useOrderCenterDetail) {
        this.useOrderCenterDetail = useOrderCenterDetail;
    }

    /**
     * 获取 出库单id
     *
     * @return outStockOrderIds 出库单id
     */
    public List<Long> getOutStockOrderIds() {
        return this.outStockOrderIds;
    }

    /**
     * 设置 出库单id
     *
     * @param outStockOrderIds 出库单id
     */
    public void setOutStockOrderIds(List<Long> outStockOrderIds) {
        this.outStockOrderIds = outStockOrderIds;
    }

    /**
     * 获取 是否处理detail和item数量一样的数据
     *
     * @return handleEqualDetail 是否处理detail和item数量一样的数据
     */
    public boolean isHandleEqualDetail() {
        return this.handleEqualDetail;
    }

    /**
     * 设置 是否处理detail和item数量一样的数据
     *
     * @param handleEqualDetail 是否处理detail和item数量一样的数据
     */
    public void setHandleEqualDetail(boolean handleEqualDetail) {
        this.handleEqualDetail = handleEqualDetail;
    }
}
