package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/8/15
 */
public class OutStockMarkLackDTO implements Serializable {
    /**
     * 缺货数据
     */
    private List<OutStockLackDTO> outStockLackDTOList;
    /**
     * 操作人
     */
    private Integer optUserId;

    /**
     * 获取 缺货数据
     *
     * @return outStockLackDTOList 缺货数据
     */
    public List<OutStockLackDTO> getOutStockLackDTOList() {
        return this.outStockLackDTOList;
    }

    /**
     * 设置 缺货数据
     *
     * @param outStockLackDTOList 缺货数据
     */
    public void setOutStockLackDTOList(List<OutStockLackDTO> outStockLackDTOList) {
        this.outStockLackDTOList = outStockLackDTOList;
    }

    /**
     * 获取 操作人
     *
     * @return optUserId 操作人
     */
    public Integer getOptUserId() {
        return this.optUserId;
    }

    /**
     * 设置 操作人
     *
     * @param optUserId 操作人
     */
    public void setOptUserId(Integer optUserId) {
        this.optUserId = optUserId;
    }
}
