package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.PushMessageConstants;
import com.yijiupi.himalaya.supplychain.waves.enums.TaskWarehouseFeatureTypeConstants;

/**
 * 波次任务列表DTO
 *
 * <AUTHOR> 2018/3/16
 */
public class BatchTaskDTO implements Serializable {

    private List<String> batchTaskIds;
    /**
     * id
     */
    private String id;
    /**
     * 波次任务号
     */
    private String batchTaskNo;
    /**
     * 拣货属性
     */
    private String batchTaskName;
    /**
     * 波次编号
     */
    private String batchNo;
    /**
     * 波次任务生产时间
     */
    private String createTime;
    /**
     * 分拣员(姓名)
     */
    private String sorter;
    /**
     * 分拣员id
     */
    private Integer sorterId;
    /**
     * 关联的订单应付总金额
     */
    private BigDecimal orderAmount;
    /**
     * 订单数量
     */
    private Integer orderCount;
    /**
     * 商品种类数量
     */
    private Integer skuCount;
    /**
     * 大件总数
     */
    private BigDecimal packageAmount;
    /**
     * 小件总数
     */
    private BigDecimal unitAmount;
    /**
     * 分拣任务状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte taskState;
    private String taskStateText;

    /**
     * 打印次数
     */
    private Integer printTimes;
    /**
     * 是否打印
     */
    private Boolean isPrinted;
    /**
     * 拣货方式 1 按订单拣货 2 按产品拣货',
     */
    private Byte pickingType;
    private String pickingTypeText;

    /**
     * 货位或者货区id
     */
    private Long toLocationId;
    /**
     * 货位或者货区名称
     */
    private String toLocationName;

    /**
     * 拣货任务详情列表
     */
    private List<BatchTaskItemDTO> batchTaskItemList;

    /**
     * 订单详情列表
     */
    private List<OutStockOrderItemDTO> outStockOrderItemList;

    /**
     * 片区名称
     */
    private String areaName;

    /**
     * 线路名称
     */
    private String routeName;

    /**
     * 订单筛选策略 1 按区域 2 按线路
     */
    private Byte orderSelection;
    private String orderSelectionText;

    /**
     * 播种任务id
     */
    private Long sowTaskId;

    /**
     * 播种任务编号
     */
    private String sowTaskNo;

    /**
     * 集货位id
     */
    private Long sowLocationId;

    /**
     * 集货位名称
     */
    private String sowLocationName;

    /**
     * 仓库Id
     */
    private Integer warehouseId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 播种订单序号
     */
    private Integer sowOrderSequence;

    /**
     * 分区id
     */
    private Long sortGroupId;

    /**
     * 是否需要拣货，默认需要 0:不需要 1:需要
     */
    private Byte needPick;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 波次名称
     */
    private String batchName;
    /**
     * 订单编号
     */
    private String refOrderNo;
    /**
     * 拣货操作人姓名
     */
    private String completeUser;
    /**
     * 拣货模式：拣货模式 1：人机混拣，2：人工拣货：3：机器人拣货
     */
    private Byte pickPattern;
    /**
     * 拣货模式文本
     */
    private Byte pickPatternText;
    /**
     * 通道ID
     */
    private Long passageId;
    /**
     * 通道编码
     */
    private String passageCode;

    /**
     * 托盘号
     */
    private String toPalletNo;
    /**
     * 拣货方式:1、默认；2、电子标签拣货
     *
     * @see PushMessageConstants.BatchTaskKindOfPickingConstants
     */
    private Byte kindOfPicking;

    /**
     * 是否存在电子标签
     */
    private boolean existSortGroupRfid;
    /**
     * 拣货任务特征
     *
     * @see TaskWarehouseFeatureTypeConstants
     */
    private Byte taskWarehouseFeatureType;

    /**
     * 第几次追加拣货任务
     */
    private Byte taskAppendSequence;
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 绩效所属班次
     */
    private String shiftOfPerformance;

    public String getCompleteUser() {
        return completeUser;
    }

    public void setCompleteUser(String completeUser) {
        this.completeUser = completeUser;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public Byte getNeedPick() {
        return needPick;
    }

    public void setNeedPick(Byte needPick) {
        this.needPick = needPick;
    }

    public Long getSortGroupId() {
        return sortGroupId;
    }

    public void setSortGroupId(Long sortGroupId) {
        this.sortGroupId = sortGroupId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Byte getOrderSelection() {
        return orderSelection;
    }

    public void setOrderSelection(Byte orderSelection) {
        this.orderSelection = orderSelection;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public Boolean getPrinted() {
        return isPrinted;
    }

    public void setPrinted(Boolean printed) {
        isPrinted = printed;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    /**
     * 获取 id
     *
     * @return id id
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 id
     *
     * @param id id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 波次任务号
     *
     * @return batchTaskNo 波次任务号
     */
    public String getBatchTaskNo() {
        return this.batchTaskNo;
    }

    /**
     * 设置 波次任务号
     *
     * @param batchTaskNo 波次任务号
     */
    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    /**
     * 获取 波次任务生产时间
     *
     * @return createTime 波次任务生产时间
     */
    public String getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置 波次任务生产时间
     *
     * @param createTime 波次任务生产时间
     */
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 分拣员(姓名)
     *
     * @return sorter 分拣员(姓名)
     */
    public String getSorter() {
        return this.sorter;
    }

    /**
     * 设置 分拣员(姓名)
     *
     * @param sorter 分拣员(姓名)
     */
    public void setSorter(String sorter) {
        this.sorter = sorter;
    }

    /**
     * 获取 商品种类数量
     *
     * @return skuCount 商品种类数量
     */
    public Integer getSkuCount() {
        return this.skuCount;
    }

    /**
     * 设置 商品种类数量
     *
     * @param skuCount 商品种类数量
     */
    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    /**
     * 获取 大件总数
     *
     * @return packageAmount 大件总数
     */
    public BigDecimal getPackageAmount() {
        return this.packageAmount;
    }

    /**
     * 设置 大件总数
     *
     * @param packageAmount 大件总数
     */
    public void setPackageAmount(BigDecimal packageAmount) {
        this.packageAmount = packageAmount;
    }

    /**
     * 获取 小件总数
     *
     * @return unitAmount 小件总数
     */
    public BigDecimal getUnitAmount() {
        return this.unitAmount;
    }

    /**
     * 设置 小件总数
     *
     * @param unitAmount 小件总数
     */
    public void setUnitAmount(BigDecimal unitAmount) {
        this.unitAmount = unitAmount;
    }

    /**
     * 获取 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     *
     * @return taskState 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     */
    public Byte getTaskState() {
        return this.taskState;
    }

    /**
     * 设置 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     *
     * @param taskState 分拣任务状态 未分拣(0) 分拣中(1) 已完成(2)
     */
    public void setTaskState(Byte taskState) {
        this.taskState = taskState;
    }

    /**
     * 获取 波次编号
     *
     * @return batchNo 波次编号
     */
    public String getBatchNo() {
        return this.batchNo;
    }

    /**
     * 设置 波次编号
     *
     * @param batchNo 波次编号
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * 获取 关联的订单应付总金额
     *
     * @return orderAmount 关联的订单应付总金额
     */
    public BigDecimal getOrderAmount() {
        return this.orderAmount;
    }

    /**
     * 设置 关联的订单应付总金额
     *
     * @param orderAmount 关联的订单应付总金额
     */
    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    /**
     * 获取 订单数量
     *
     * @return orderCount 订单数量
     */
    public Integer getOrderCount() {
        return this.orderCount;
    }

    /**
     * 设置 订单数量
     *
     * @param orderCount 订单数量
     */
    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    /**
     * 获取 分拣员id
     */
    public Integer getSorterId() {
        return this.sorterId;
    }

    /**
     * 设置 分拣员id
     */
    public void setSorterId(Integer sorterId) {
        this.sorterId = sorterId;
    }

    /**
     * 获取 打印次数
     */
    public Integer getPrintTimes() {
        return this.printTimes;
    }

    /**
     * 设置 打印次数
     */
    public void setPrintTimes(Integer printTimes) {
        this.printTimes = printTimes;
    }

    /**
     * 获取 是否打印
     */
    public Boolean getIsPrinted() {
        return this.isPrinted;
    }

    /**
     * 设置 是否打印
     */
    public void setIsPrinted(Boolean isPrinted) {
        this.isPrinted = isPrinted;
    }

    /**
     * 获取 拣货方式 1 按订单拣货 2 按产品拣货',
     */
    public Byte getPickingType() {
        return this.pickingType;
    }

    /**
     * 设置 拣货方式 1 按订单拣货 2 按产品拣货',
     */
    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    public List<BatchTaskItemDTO> getBatchTaskItemList() {
        return batchTaskItemList;
    }

    public void setBatchTaskItemList(List<BatchTaskItemDTO> batchTaskItemList) {
        this.batchTaskItemList = batchTaskItemList;
    }

    public String getBatchTaskName() {
        return batchTaskName;
    }

    public void setBatchTaskName(String batchTaskName) {
        this.batchTaskName = batchTaskName;
    }

    public Long getSowTaskId() {
        return sowTaskId;
    }

    public void setSowTaskId(Long sowTaskId) {
        this.sowTaskId = sowTaskId;
    }

    public String getSowTaskNo() {
        return sowTaskNo;
    }

    public void setSowTaskNo(String sowTaskNo) {
        this.sowTaskNo = sowTaskNo;
    }

    public Long getSowLocationId() {
        return sowLocationId;
    }

    public void setSowLocationId(Long sowLocationId) {
        this.sowLocationId = sowLocationId;
    }

    public String getSowLocationName() {
        return sowLocationName;
    }

    public void setSowLocationName(String sowLocationName) {
        this.sowLocationName = sowLocationName;
    }

    public List<OutStockOrderItemDTO> getOutStockOrderItemList() {
        return outStockOrderItemList;
    }

    public void setOutStockOrderItemList(List<OutStockOrderItemDTO> outStockOrderItemList) {
        this.outStockOrderItemList = outStockOrderItemList;
    }

    public Integer getSowOrderSequence() {
        return sowOrderSequence;
    }

    public void setSowOrderSequence(Integer sowOrderSequence) {
        this.sowOrderSequence = sowOrderSequence;
    }

    public String getTaskStateText() {
        return taskStateText;
    }

    public void setTaskStateText(String taskStateText) {
        this.taskStateText = taskStateText;
    }

    public String getPickingTypeText() {
        return pickingTypeText;
    }

    public void setPickingTypeText(String pickingTypeText) {
        this.pickingTypeText = pickingTypeText;
    }

    public String getOrderSelectionText() {
        return orderSelectionText;
    }

    public void setOrderSelectionText(String orderSelectionText) {
        this.orderSelectionText = orderSelectionText;
    }

    /**
     * 获取 拣货模式
     *
     * @return pickPattern 拣货模式
     */
    public Byte getPickPattern() {
        return this.pickPattern;
    }

    /**
     * 设置 拣货模式
     *
     * @param pickPattern 拣货模式
     */
    public void setPickPattern(Byte pickPattern) {
        this.pickPattern = pickPattern;
    }

    /**
     * 获取 拣货模式文本
     *
     * @return pickPatternText 拣货模式文本
     */
    public Byte getPickPatternText() {
        return this.pickPatternText;
    }

    /**
     * 设置 拣货模式文本
     *
     * @param pickPatternText 拣货模式文本
     */
    public void setPickPatternText(Byte pickPatternText) {
        this.pickPatternText = pickPatternText;
    }

    public Long getPassageId() {
        return passageId;
    }

    public BatchTaskDTO setPassageId(Long passageId) {
        this.passageId = passageId;
        return this;
    }

    public String getPassageCode() {
        return passageCode;
    }

    public BatchTaskDTO setPassageCode(String passageCode) {
        this.passageCode = passageCode;
        return this;
    }

    /**
     * 获取
     *
     * @return batchTaskIds
     */
    public List<String> getBatchTaskIds() {
        return this.batchTaskIds;
    }

    /**
     * 设置
     *
     * @param batchTaskIds
     */
    public void setBatchTaskIds(List<String> batchTaskIds) {
        this.batchTaskIds = batchTaskIds;
    }

    public String getToPalletNo() {
        return toPalletNo;
    }

    public void setToPalletNo(String toPalletNo) {
        this.toPalletNo = toPalletNo;
    }

    /**
     * 获取 拣货方式:1、默认；2、电子标签拣货 @see PushMessageConstants.BatchTaskKindOfPickingConstants
     *
     * @return kindOfPicking 拣货方式:1、默认；2、电子标签拣货 @see PushMessageConstants.BatchTaskKindOfPickingConstants
     */
    public Byte getKindOfPicking() {
        return this.kindOfPicking;
    }

    /**
     * 设置 拣货方式:1、默认；2、电子标签拣货 @see PushMessageConstants.BatchTaskKindOfPickingConstants
     *
     * @param kindOfPicking 拣货方式:1、默认；2、电子标签拣货 @see PushMessageConstants.BatchTaskKindOfPickingConstants
     */
    public void setKindOfPicking(Byte kindOfPicking) {
        this.kindOfPicking = kindOfPicking;
    }

    public boolean isExistSortGroupRfid() {
        return existSortGroupRfid;
    }

    public void setExistSortGroupRfid(boolean existSortGroupRfid) {
        this.existSortGroupRfid = existSortGroupRfid;
    }

    /**
     * 获取 拣货任务特征 @see TaskWarehouseFeatureTypeConstants
     *
     * @return taskWarehouseFeatureType 拣货任务特征 @see TaskWarehouseFeatureTypeConstants
     */
    public Byte getTaskWarehouseFeatureType() {
        return this.taskWarehouseFeatureType;
    }

    /**
     * 设置 拣货任务特征 @see TaskWarehouseFeatureTypeConstants
     *
     * @param taskWarehouseFeatureType 拣货任务特征 @see TaskWarehouseFeatureTypeConstants
     */
    public void setTaskWarehouseFeatureType(Byte taskWarehouseFeatureType) {
        this.taskWarehouseFeatureType = taskWarehouseFeatureType;
    }

    /**
     * 获取 第几次追加拣货任务
     *
     * @return taskAppendSequence 第几次追加拣货任务
     */
    public Byte getTaskAppendSequence() {
        return this.taskAppendSequence;
    }

    /**
     * 设置 第几次追加拣货任务
     *
     * @param taskAppendSequence 第几次追加拣货任务
     */
    public void setTaskAppendSequence(Byte taskAppendSequence) {
        this.taskAppendSequence = taskAppendSequence;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 绩效所属班次
     *
     * @return shiftOfPerformance 绩效所属班次
     */
    public String getShiftOfPerformance() {
        return this.shiftOfPerformance;
    }

    /**
     * 设置 绩效所属班次
     *
     * @param shiftOfPerformance 绩效所属班次
     */
    public void setShiftOfPerformance(String shiftOfPerformance) {
        this.shiftOfPerformance = shiftOfPerformance;
    }
}
