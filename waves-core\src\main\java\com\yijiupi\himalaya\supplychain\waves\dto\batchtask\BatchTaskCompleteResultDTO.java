package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 拣货完成响应
 *
 * <AUTHOR>
 * @Date 2021/9/28 14:43
 */
public class BatchTaskCompleteResultDTO implements Serializable {
    /**
     * 出库配置校验 - 禁止入库产品名称
     */
    private List<String> banProductNames;
    /**
     * 出库配置校验 - 提醒入库产品名称
     */
    private List<String> remindProductNames;

    /**
     * PDA需要通知的拣货信息
     */
    private Map<String, List<BatchTaskDTO>> batchTaskMsg;

    public List<String> getBanProductNames() {
        return banProductNames;
    }

    public void setBanProductNames(List<String> banProductNames) {
        this.banProductNames = banProductNames;
    }

    public List<String> getRemindProductNames() {
        return remindProductNames;
    }

    public void setRemindProductNames(List<String> remindProductNames) {
        this.remindProductNames = remindProductNames;
    }

    public Map<String, List<BatchTaskDTO>> getBatchTaskMsg() {
        return batchTaskMsg;
    }

    public void setBatchTaskMsg(Map<String, List<BatchTaskDTO>> batchTaskMsg) {
        this.batchTaskMsg = batchTaskMsg;
    }
}
