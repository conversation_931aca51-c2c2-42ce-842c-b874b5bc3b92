package com.yijiupi.himalaya.supplychain.waves.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

public enum PackageTypeEnum {
    /**
     * 枚举
     */
    包装箱((byte)0), 托盘((byte)1), 二次分拣((byte)2);

    /**
     * type
     */
    private byte type;

    PackageTypeEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     * 
     * @param value
     * @return
     */
    public static String getEnumByValue(Byte value) {
        PackageTypeEnum packageTypeEnum = null;
        if (value != null) {
            packageTypeEnum = cache.get(value);
        }
        return packageTypeEnum == null ? null : packageTypeEnum.name();
    }

    private static Map<Byte, PackageTypeEnum> cache =
        EnumSet.allOf(PackageTypeEnum.class).stream().collect(Collectors.toMap(p -> p.type, p -> p));
}
