package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.tms.NotifyTmsBatchCreateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.lock.LockCreateBatchBL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.common.constant.RedisConstant;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderTraceBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OutStockOrderBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.ThirdPartyOutStockBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder.OutStockOrderStateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.NotifyOrderLackBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchFinishedLackOrderConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 有时间抽出抽象方法，根据scop，普通波次完成，二次分拣完成做区分。
 * 
 * <AUTHOR>
 * @since 2023/11/21
 */
@Service
public class BatchFinishedBL {

    @Autowired
    private GlobalCache globalCache;
    @Autowired
    private BatchConvertor batchConvertor;
    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private OrderTraceBL orderTraceBL;
    @Autowired
    private OutStockOrderBL outStockOrderBL;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private ThirdPartyOutStockBL thirdPartyOutStockBL;
    @Autowired
    private BatchFinishedLackOrderBL batchFinishedLackOrderBL;
    @Autowired
    private BatchFinishedLackOrderConvertor batchFinishedLackOrderConvertor;
    @Autowired
    private LockCreateBatchBL lockCreateBatchBL;
    @Resource
    private OutStockOrderStateBL outStockOrderStateBL;

    private static final Logger LOG = LoggerFactory.getLogger(BatchFinishedBL.class);

    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateBatchStateByBatchNo(List<String> batchNos) {
        batchNos.forEach(batchNo -> {
            try {
                completeWave(batchNo, 1, null);
            } catch (Exception e) {
                LOG.warn("完成波次异常:" + batchNo, e);
            }
        });

    }

    /**
     * 正常完成波次：更新波次状态，处理缺货信息，同步tms出库位，删除outStockOrderItemDetail
     * 
     * @see BatchOrderBL#updateBatchStateByBatchNo
     * @param batchNo
     * @param operatorUserId
     * @param cityId
     */
    @DistributeLock(conditions = "#batchNo", sleepMills = 3000, expireMills = 60000,
        key = RedisConstant.SUP_F + "updateBatchStateByBatchNo", lockType = DistributeLock.LockType.WAITLOCK)
    @Transactional(rollbackFor = Exception.class)
    public void completeWave(String batchNo, Integer operatorUserId, Integer cityId) {
        AssertUtils.notNull(batchNo, "波次编号不能为空");
        LOG.info("波次完成开始，波次号：{}", batchNo);
        BatchPO batchPO = batchMapper.selectBatchByBatchNo(cityId, batchNo);
        if (Objects.isNull(batchPO)) {
            return;
        }

        // 已拣货和已出库的不用重复更新
        if (Objects.equals(BatchStateEnum.PICKINGEND.getType().byteValue(), batchPO.getState())
                || Objects.equals(BatchStateEnum.ALREADYOUTOFSTORE.getType().byteValue(), batchPO.getState())) {
            throw new BusinessValidateException("该波次已拣货或已出库，无法重复更新：" + batchNo);
        }

        String operateUser = globalCache.getAdminTrueName(operatorUserId);
        // 更新波次信息
        batchPO = finishBatch(batchPO, operateUser);

        if (batchPO.getState() != BatchStateEnum.PICKINGEND.getType().byteValue()) {
            return;
        }
        // 更新出库单
        List<Long> outStockOrderIds = finishOutStockOrderInBatch(batchPO);

        // 处理outstockorderitemdetail
        updateOutStockOrderItemDetail(outStockOrderIds);
        // 同步出库位
        syncOutStockLocation(batchPO.getBatchNo(), batchPO.getOrgId(), outStockOrderIds, operatorUserId);
        // 处理缺货
        handleLackOrder(batchPO.getBatchNo(), operatorUserId);

        lockCreateBatchBL.removeBatchCache(Collections.singletonList(batchPO.getBatchNo()));
        LOG.info("波次完成结束，波次号：{}", batchNo);
    }

    /**
     * 播种完成处理波次和出库位信息,删除outStockOrderItemDetail
     * 
     * @see OutStockOrderBL#processOrderLocationByProductSow
     * @param batchNo
     * @param operateUserId
     * @param sowTaskType
     * @param cityId
     */
    @DistributeLock(conditions = "#batchNo", sleepMills = 3000, expireMills = 60000, key = "updateBatchStateByBatchNo",
        lockType = DistributeLock.LockType.WAITLOCK)
    @Transactional(rollbackFor = Exception.class)
    public void secondPickCompleteWave(String batchNo, Integer operateUserId, Byte sowTaskType, Integer cityId) {
        LOG.info("二次分拣波次完成开始，波次号：{}", batchNo);
        BatchPO batchPO = batchMapper.selectBatchByBatchNo(cityId, batchNo);
        if (Objects.isNull(batchPO)) {
            return;
        }
        if ((sowTaskType == null || SowTaskTypeEnum.二次分拣播种.getType() != sowTaskType)
            && (Objects.equals(BatchStateEnum.PICKINGEND.getType().byteValue(), batchPO.getState())
                || Objects.equals(BatchStateEnum.ALREADYOUTOFSTORE.getType().byteValue(), batchPO.getState()))) {
            LOG.info("该波次已拣货或已出库，无法重复更新：" + batchNo);
            return;
        }
        String operateUser = globalCache.getAdminTrueName(operateUserId);
        // 更新波次信息
        batchPO = finishBatch(batchPO, operateUser);

        if (batchPO.getState() != BatchStateEnum.PICKINGEND.getType().byteValue()) {
            return;
        }

        List<Long> outStockOrderIds = getSowNeedSyncOutStockOrderIds(batchPO);
        if (CollectionUtils.isEmpty(outStockOrderIds)) {
            return;
        }
        // 处理outstockorderitemdetail
        updateOutStockOrderItemDetail(outStockOrderIds);
        // 2、完成拣货时通知TMS，波次下所有订单对应的出库位
        List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findTaskByBatchNo(Collections.singletonList(batchNo));
        outStockOrderBL.pureSyncOrderToLocation(batchNo, batchTaskPOS, outStockOrderIds);
        LOG.info("二次分拣波次完成结束，波次号：{}", batchNo);
    }

    /**
     * 处理波次状态
     * 
     * @see BatchOrderBL#isBatchStateFinish
     * @param batchPO
     * @param operateUser
     */
    private BatchPO finishBatch(BatchPO batchPO, String operateUser) {
        AssertUtils.notNull(batchPO, "波次不能为空");

        String batchId = batchPO.getId();
        Integer orgId = batchPO.getOrgId();
        String batchNo = batchPO.getBatchNo();

        // 查询波次下所有拣货任务
        List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findTaskByBatchNo(Collections.singletonList(batchNo));
        // 获取波次状态
        Integer batchState = batchConvertor.getBatchState(batchTaskPOS, batchId, orgId);
        // 更新波次状态
        batchMapper.updateBatchState(Collections.singletonList(batchId), batchState, orgId);
        batchPO.setState(batchState.byteValue());

        LOG.info("{}波次完成拣货时，波次状态为{}", batchNo, batchState);
        if (!batchState.equals(BatchStateEnum.PICKINGEND.getType())) {
            return batchPO;
        }

        // -添加操作记录（波次完成拣货）
        orderTraceBL.updateBatchTrace(Collections.singletonList(batchPO), OrderTraceDescriptionEnum.波次完成拣货.name(),
            operateUser);
        return batchPO;
    }

    /**
     *
     * @param batchPO
     * @return 更新了的outStockOrderId
     */
    private List<Long> finishOutStockOrderInBatch(BatchPO batchPO) {
        String batchNo = batchPO.getBatchNo();
        String batchId = batchPO.getId();
        // 1、更新完成拣货的出库单状态
        List<Long> lstOrderIds = outStockOrderMapper.findOutStockOrderIdsByBatchId(batchId);
        LOG.info("{}波次关联的订单id：{}", batchNo, JSON.toJSONString(lstOrderIds));
        lstOrderIds = batchConvertor.filterNeedFinishPickOrderIds(lstOrderIds, batchPO);
        LOG.info("{}完成拣货的出库单：{}", batchNo, JSON.toJSONString(lstOrderIds));
        updateStateByOrderIds(lstOrderIds, batchPO);

        return lstOrderIds;
    }

    /**
     * 波次完成 更新出库单项detail tms出库位信息 并处理缺货 <br />
     * 老代码有 更新出库单项detail 这个操作，但必须注意顺序，先删除然后再处理缺货，否则会有bug
     * 
     * @param batchNo
     * @param operatorUserId
     */
    @DistributeLock(conditions = "#batchNo", sleepMills = 3000, expireMills = 60000, key = "updateBatchStateByBatchNo",
        lockType = DistributeLock.LockType.WAITLOCK)
    @Transactional(rollbackFor = Exception.class)
    public void syncOutStockLocation(String batchNo, Integer cityId, List<Long> outStockOrderIds,
        Integer operatorUserId) {
        if (CollectionUtils.isEmpty(outStockOrderIds)) {
            return;
        }

        BatchPO batchPO = batchMapper.selectBatchByBatchNo(cityId, batchNo);
        if (Objects.isNull(batchPO)) {
            return;
        }

        if (batchPO.getState().byteValue() != BatchStateEnum.PICKINGEND.getType()) {
            LOG.info("波次未完成，{}", batchNo);
            return;
        }

        // 查询出库单
        List<OutStockOrderPO> orderPOList = outStockOrderMapper.listOutStockOrderByOrderId(outStockOrderIds);
        if (org.springframework.util.CollectionUtils.isEmpty(orderPOList)) {
            return;
        }

        // 查询订单项与拣货任务项关联
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderIds(outStockOrderIds);

        // 查询波次下所有拣货任务
        List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findTaskByBatchNo(Collections.singletonList(batchNo));

        // 同步出库位给tms FIXME
        outStockOrderBL.pureSyncOrderToLocation(orderPOList, orderItemTaskInfoPOList, batchNo, batchTaskPOS);
    }

    private List<Long> getSowNeedSyncOutStockOrderIds(BatchPO batchPO) {
        String batchId = batchPO.getId();
        String batchNo = batchPO.getBatchNo();

        List<Long> lstOrderIds = outStockOrderMapper.findOutStockOrderIdsByBatchId(batchId);
        if (org.springframework.util.CollectionUtils.isEmpty(lstOrderIds)) {
            LOG.info("[根据产品完成播种]波次：{},无对应出库单", batchNo);
            return Collections.emptyList();
        }

        // 1、过滤掉没有生成拣货任务的出库单id
        List<Long> noBatchTaskOrderIds = outStockOrderItemMapper.listOrderIdNoBatchTask(lstOrderIds);
        if (!org.springframework.util.CollectionUtils.isEmpty(noBatchTaskOrderIds)) {
            LOG.info("未全部生成拣货任务的出库单id：{}", JSON.toJSONString(noBatchTaskOrderIds));
            lstOrderIds =
                lstOrderIds.stream().filter(p -> !noBatchTaskOrderIds.contains(p)).collect(Collectors.toList());
        }
        if (org.springframework.util.CollectionUtils.isEmpty(lstOrderIds)) {
            LOG.info("[根据产品完成播种]波次：{},出库单未生成拣货任务", batchNo);
            return Collections.emptyList();
        }

        // 2、过滤掉没有完成拣货的出库单id
        List<Long> noPickOrderIds = outStockOrderItemMapper.listOrderIdNoPick(lstOrderIds);
        if (!org.springframework.util.CollectionUtils.isEmpty(noPickOrderIds)) {
            LOG.info("未完成拣货的出库单id：{}", JSON.toJSONString(noPickOrderIds));
            lstOrderIds = lstOrderIds.stream().filter(p -> !noPickOrderIds.contains(p)).collect(Collectors.toList());
        }

        LOG.info("[根据产品完成播种]波次：{},波次完成的出库单：{}", batchNo, lstOrderIds);
        if (org.springframework.util.CollectionUtils.isEmpty(lstOrderIds)) {
            return Collections.emptyList();
        }

        return lstOrderIds;
    }

    private void handleLackOrder(String batchNo, Integer operatorUserId) {
        NotifyOrderLackBO notifyOrderLackBO =
            batchFinishedLackOrderConvertor.convertOrderLackByBatch(batchNo, operatorUserId);
        LOG.warn("缺货信息:{}；事务名称:{}", JSON.toJSONString(notifyOrderLackBO),
            TransactionSynchronizationManager.getCurrentTransactionName());
        batchFinishedLackOrderBL.processOrderLack(notifyOrderLackBO, operatorUserId);
        // publisher.publishEvent(new BatchFinishedEvent(this, notifyOrderLackBO));
    }

    private void updateOutStockOrderItemDetail(List<Long> outStockOrderIds) {
        if (CollectionUtils.isEmpty(outStockOrderIds)) {
            return;
        }
        // 查询订单项与拣货任务项关联 FIXME
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderIds(outStockOrderIds);
        // 这步暂时不知道干嘛的，但是一直在调用，按照现在线上的bug来看，应该放在处理缺货之前
        // SCM-8969 【线上问题处理】历史完成拣货任务机制有问题，导致detail数量又不一致！！ 里面有具体原因
        LOG.info("[波次完成]更新出库单项detail:{}", JSON.toJSONString(orderItemTaskInfoPOList));
        outStockOrderBL.updateOutStockOrderItemDetail(orderItemTaskInfoPOList);
    }

    private void validateBatchInfo(BatchPO batchPO, String batchNo) {
        if (Objects.isNull(batchPO)) {
            throw new BusinessException("找不到波次：" + batchNo);
        }
        // 已拣货和已出库的不用重复更新
        if (Objects.equals(BatchStateEnum.PICKINGEND.getType().byteValue(), batchPO.getState())
            || Objects.equals(BatchStateEnum.ALREADYOUTOFSTORE.getType().byteValue(), batchPO.getState())) {
            throw new BusinessValidateException("该波次已拣货或已出库，无法重复更新：" + batchNo);
        }
    }

    public void updateStateByOrderIds(List<Long> lstOrderIds, BatchPO batchPO) {
        if (CollectionUtils.isEmpty(lstOrderIds)) {
            return;
        }

        // 微酒
        if (Objects.equals(batchPO.getBatchType(), BatchTypeEnum.微酒.getType())) {
            outStockOrderBL.updateOrderByWine(lstOrderIds, batchPO.getId(), batchPO.getBatchNo(),
                TaskStateEnum.已完成.getType());
            return;
            // 第三方
        }
        if (BatchTypeEnum.isApplyOrderBatch(batchPO.getBatchType())) {
            thirdPartyOutStockBL.updateOrderByThirdParty(lstOrderIds, batchPO.getId(), batchPO.getBatchNo(),
                TaskStateEnum.已完成.getType());
            return;
            // 酒批
        }
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.findIdByIds(lstOrderIds, null);

        List<Long> updateOrderIds =
            outStockOrderPOList.stream().filter(m -> m.getState().byteValue() != OutStockOrderStateEnum.已出库.getType())
                .map(OutStockOrderPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateOrderIds)) {
            LOG.warn("出库单都已出库:{}", JSON.toJSONString(updateOrderIds));
            return;
        }
        outStockOrderStateBL.updateStateByOrderIds(updateOrderIds, OutStockOrderStateEnum.已拣货.getType());
    }

}
