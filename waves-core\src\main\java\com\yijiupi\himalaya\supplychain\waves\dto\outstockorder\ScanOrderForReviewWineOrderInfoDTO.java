package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/18
 */
public class ScanOrderForReviewWineOrderInfoDTO implements Serializable {
    /**
     * 订单号
     */
    private String refOrderNo;
    /**
     * 托盘出库位信息
     */
    private List<ScanOrderForReviewLocationInfoDTO> locationList;

    /**
     * 获取 订单号
     *
     * @return refOrderNo 订单号
     */
    public String getRefOrderNo() {
        return this.refOrderNo;
    }

    /**
     * 设置 订单号
     *
     * @param refOrderNo 订单号
     */
    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    /**
     * 获取
     *
     * @return locationList
     */
    public List<ScanOrderForReviewLocationInfoDTO> getLocationList() {
        return this.locationList;
    }

    /**
     * 设置
     *
     * @param locationList
     */
    public void setLocationList(List<ScanOrderForReviewLocationInfoDTO> locationList) {
        this.locationList = locationList;
    }
}
