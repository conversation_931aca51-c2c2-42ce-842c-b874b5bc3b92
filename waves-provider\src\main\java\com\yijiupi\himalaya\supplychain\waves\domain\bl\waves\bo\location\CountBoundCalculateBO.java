package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title: CountBoundCalculateBO
 * @description: 与订单项 或 订单明细项 匹配
 * @date 2022-12-01 13:54
 */
public class CountBoundCalculateBO {
    /**
     * 小单位id
     */
    private BigDecimal unitTotalCount;
    /**
     * 主键
     */
    private Long id;
    /**
     * 大件数
     */
    private BigDecimal packageCount;
    /**
     * 小件数
     */
    private BigDecimal unitCount;
    /**
     * 包装规格系数
     */
    private BigDecimal specQuantity;

    /**
     * 获取 小单位id
     *
     * @return unitTotalCount 小单位id
     */
    public BigDecimal getUnitTotalCount() {
        return this.unitTotalCount;
    }

    /**
     * 设置 小单位id
     *
     * @param unitTotalCount 小单位id
     */
    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    /**
     * 获取 主键
     *
     * @return id 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 主键
     *
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 大件数
     *
     * @return packageCount 大件数
     */
    public BigDecimal getPackageCount() {
        return this.packageCount;
    }

    /**
     * 设置 大件数
     *
     * @param packageCount 大件数
     */
    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    /**
     * 获取 小件数
     *
     * @return unitCount 小件数
     */
    public BigDecimal getUnitCount() {
        return this.unitCount;
    }

    /**
     * 设置 小件数
     *
     * @param unitCount 小件数
     */
    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    /**
     * 获取 包装规格系数
     *
     * @return specQuantity 包装规格系数
     */
    public BigDecimal getSpecQuantity() {
        return this.specQuantity;
    }

    /**
     * 设置 包装规格系数
     *
     * @param specQuantity 包装规格系数
     */
    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }
}
