package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/12
 */
public class PickUpDTOConvertor {

    protected static final Logger LOGGER = LoggerFactory.getLogger(PickUpDTOConvertor.class);

    public static void setPickupToLocation(Long locationId, Long sowLocationId, PickUpDTO pickUpDTO) {
        // 如果有播种任务，拣货完成，先放到集货区
        if (sowLocationId != null) {
            // 拣货完成，先放到集货位
            pickUpDTO.setLocationId(sowLocationId);
            LOGGER.info(String.format("播种任务拣货完成，备货区货位：%s,先放到集货区：%s", locationId, JSON.toJSONString(pickUpDTO)));
        }
        if (pickUpDTO.getLocationId() == null) {
            pickUpDTO.setLocationId(locationId);
        }
    }
}
