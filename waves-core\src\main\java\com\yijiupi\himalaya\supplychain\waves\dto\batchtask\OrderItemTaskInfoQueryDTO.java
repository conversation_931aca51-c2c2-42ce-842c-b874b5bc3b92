package com.yijiupi.himalaya.supplychain.waves.dto.batchtask;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/18
 */
public class OrderItemTaskInfoQueryDTO implements Serializable {
    private static final long serialVersionUID = 8565786524475421813L;
    /**
     * 分拣任务项ID
     */
    private List<String> batchTaskItemIds;
    /**
     * 订单项ID
     */
    private List<Long> orderItemIds;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 城市ID
     */
    private Integer orgId;

    public List<String> getBatchTaskItemIds() {
        return batchTaskItemIds;
    }

    public void setBatchTaskItemIds(List<String> batchTaskItemIds) {
        this.batchTaskItemIds = batchTaskItemIds;
    }

    public List<Long> getOrderItemIds() {
        return orderItemIds;
    }

    public void setOrderItemIds(List<Long> orderItemIds) {
        this.orderItemIds = orderItemIds;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }
}
