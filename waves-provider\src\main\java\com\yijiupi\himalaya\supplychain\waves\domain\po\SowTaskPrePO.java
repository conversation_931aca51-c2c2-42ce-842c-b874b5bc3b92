package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.util.Date;

/**
 * 播种任务表_预览
 */
public class SowTaskPrePO {
    /**
    * id
    */
    private Long id;

    /**
     * 城市id
     */
    private Integer orgId;

    /**
    * 仓库id
    */
    private Integer warehouseId;

    /**
    * 波次表id
    */
    private String batchId;

    /**
    * 波次编号
    */
    private String batchNo;

    /**
    * 播种任务编号
    */
    private String sowTaskPreNo;

    /**
    * 版本号
    */
    private Integer version;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date lastUpdateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getSowTaskPreNo() {
        return sowTaskPreNo;
    }

    public void setSowTaskPreNo(String sowTaskPreNo) {
        this.sowTaskPreNo = sowTaskPreNo;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
}