package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;

/**
 * 生波次订单
 *
 * <AUTHOR>
 * @date 1/20/21 3:40 PM
 */
public class BatchCreateOrderDTO implements Serializable {

    /**
     * 订单号
     */
    private String refOrderNo;

    /**
     * 目的仓库id
     */
    private Integer toWarehouseId;

    /**
     * 目的仓库
     */
    private String toWarehouseName;

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Integer getToWarehouseId() {
        return toWarehouseId;
    }

    public void setToWarehouseId(Integer toWarehouseId) {
        this.toWarehouseId = toWarehouseId;
    }

    public String getToWarehouseName() {
        return toWarehouseName;
    }

    public void setToWarehouseName(String toWarehouseName) {
        this.toWarehouseName = toWarehouseName;
    }
}
