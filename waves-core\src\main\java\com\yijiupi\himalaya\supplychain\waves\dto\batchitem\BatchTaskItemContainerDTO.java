package com.yijiupi.himalaya.supplychain.waves.dto.batchitem;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 拣货容器位
 *
 * <AUTHOR>
 * @date 2019-11-13 11:56
 */
public class BatchTaskItemContainerDTO implements Serializable {

    private static final long serialVersionUID = -7549424451702856805L;

    /**
     * 拣货任务明细项ID
     */
    private Long batchTaskItemId;

    /**
     * 容器位ID
     */
    private Long locationId;

    /**
     * 容器位名称
     */
    private String locationName;

    /**
     * 拣货大数量
     */
    private BigDecimal pickPackageCount;

    /**
     * 拣货小数量
     */
    private BigDecimal pickUnitCount;

    /**
     * @return the 拣货任务明细项ID
     */
    public Long getBatchTaskItemId() {
        return batchTaskItemId;
    }

    /**
     * @param 拣货任务明细项ID the batchTaskItemId to set
     */
    public void setBatchTaskItemId(Long batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public BigDecimal getPickPackageCount() {
        return pickPackageCount;
    }

    public void setPickPackageCount(BigDecimal pickPackageCount) {
        this.pickPackageCount = pickPackageCount;
    }

    public BigDecimal getPickUnitCount() {
        return pickUnitCount;
    }

    public void setPickUnitCount(BigDecimal pickUnitCount) {
        this.pickUnitCount = pickUnitCount;
    }
}
