package com.yijiupi.himalaya.supplychain.waves.domain.bo;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/10
 */
public class SowSequenceResetBO {

    private Integer addressId;

    private Integer sequenece;

    public SowSequenceResetBO() {
    }

    public SowSequenceResetBO(Integer sequenece, Integer addressId) {
        this.sequenece = sequenece;
        this.addressId = addressId;
    }

    /**
     * 获取
     *
     * @return addressId
     */
    public Integer getAddressId() {
        return this.addressId;
    }

    /**
     * 设置
     *
     * @param addressId
     */
    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    /**
     * 获取
     *
     * @return sequenece
     */
    public Integer getSequenece() {
        return this.sequenece;
    }

    /**
     * 设置
     *
     * @param sequenece
     */
    public void setSequenece(Integer sequenece) {
        this.sequenece = sequenece;
    }
}
