package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/15
 */
public class DeliveryTaskChangeFetchOrderDTO implements Serializable {

    private static final long serialVersionUID = 876688579608610823L;
    /**
     * 取货单Id
     */
    private Long fetchOrderId;
    /**
     * 订单明细项信息集合
     */
    private List<DeliveryTaskChangeFetchOrderItemDTO> deliveryTaskChangeFetchOrderItemList;

    public Long getFetchOrderId() {
        return fetchOrderId;
    }

    public void setFetchOrderId(Long fetchOrderId) {
        this.fetchOrderId = fetchOrderId;
    }

    public List<DeliveryTaskChangeFetchOrderItemDTO> getDeliveryTaskChangeFetchOrderItemList() {
        return deliveryTaskChangeFetchOrderItemList;
    }

    public void setDeliveryTaskChangeFetchOrderItemList(
        List<DeliveryTaskChangeFetchOrderItemDTO> deliveryTaskChangeFetchOrderItemList) {
        this.deliveryTaskChangeFetchOrderItemList = deliveryTaskChangeFetchOrderItemList;
    }

    public static class DeliveryTaskChangeFetchOrderItemDTO implements Serializable {

        private static final long serialVersionUID = 8818799597521755341L;

        /**
         * 订单id
         */
        private Long orderId;

        /**
         * 订单项Id
         */
        private Long orderItemId;

        /**
         * 分配小单位数量
         */
        private BigDecimal scheduleUnitTotalCount;

        public Long getOrderId() {
            return orderId;
        }

        public void setOrderId(Long orderId) {
            this.orderId = orderId;
        }

        public Long getOrderItemId() {
            return orderItemId;
        }

        public void setOrderItemId(Long orderItemId) {
            this.orderItemId = orderItemId;
        }

        public BigDecimal getScheduleUnitTotalCount() {
            return scheduleUnitTotalCount;
        }

        public void setScheduleUnitTotalCount(BigDecimal scheduleUnitTotalCount) {
            this.scheduleUnitTotalCount = scheduleUnitTotalCount;
        }
    }
}
