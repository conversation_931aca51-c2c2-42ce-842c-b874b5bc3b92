package com.yijiupi.himalaya.supplychain.waves.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.waves.concertsow.dto.ConcertSowTaskRequestDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowPackageItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowOrderItemQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.SowPackageItemQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.AddressSowTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.AddressSowTaskQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskItemQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskItemUpdateDTO;
import com.yijiupi.himalaya.supplychain.waves.virtualwarehouse.dto.VirtualWarehouseSortingTaskRequestDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 播种任务
 */
public interface SowTaskItemMapper {

    SowTaskItemPO selectByPrimaryKey(Long id);

    int insert(SowTaskItemPO record);

    int insertSelective(SowTaskItemPO record);

    int updateByPrimaryKeySelective(SowTaskItemPO record);

    /**
     * 根据播种任务编号查询明细
     *
     * @param orgId
     * @param sowTaskNo
     * @return
     */
    List<SowTaskItemPO> findBySowTaskNo(@Param("orgId") Integer orgId, @Param("sowTaskId") Long sowTaskNo);

    List<SowTaskItemPO> findBySowTaskNos(@Param("orgId") Integer orgId, @Param("sowTaskNos") List<String> sowTaskNos);


    /**
     * 根据播种任务id查询明细
     *
     * @return
     */
    List<SowTaskItemPO> findBySowTaskIds(@Param("sowTaskIds") List<Long> sowTaskIds);

    void completeSowTaskItems(@Param("sowTaskItemPOS") List<SowTaskItemPO> sowTaskItemDTOS);

    void deleteByIds(@Param("sowTaskItemIds") List<Long> sowTaskItemIds, @Param("orgId") Integer orgId);

    void batchUpdateItem(@Param("sowTaskItemUpdateDTOS") List<SowTaskItemUpdateDTO> sowTaskItemUpdateDTOS);

    PageResult<SowOrderItemDTO> pageListSowOrderItems(@Param("query") SowOrderItemQueryDTO sowOrderItemQueryDTO,
                                                      @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    void batchInsert(@Param("list") List<SowTaskItemPO> sowTaskItemPOS);

    List<SowTaskItemPO> findByOrderItemIds(@Param("orderItemIds") List<Long> orderItemIds);

    void deleteBySowTaskIds(@Param("sowTaskIds") List<Long> sowTaskIds, @Param("orgId") Integer orgId);

    PageResult<AddressSowTaskItemDTO> pageListAddressSowTaskItems(
            @Param("query") AddressSowTaskQueryDTO addressSowTaskQueryDTO, @Param("pageNum") Integer pageNum,
            @Param("pageSize") Integer pageSize);

    PageResult<AddressSowTaskItemDTO> pageListAddressSowTaskItemDetails(
            @Param("query") AddressSowTaskQueryDTO addressSowTaskQueryDTO, @Param("pageNum") Integer pageNum,
            @Param("pageSize") Integer pageSize);

    List<SowTaskItemPO> findByItemIds(@Param("orgId") Integer orgId, @Param("ids") List<Long> ids);

    /**
     * 根据波次id查询播种明细
     *
     * @return
     */
    List<SowTaskItemPO> findByBatchIds(@Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId,
                                       @Param("batchIds") List<String> batchIds);

    List<SowTaskItemPO> listSowOTaskItems(SowTaskItemQueryDTO queryDTO);

    List<SowPackageItemPO> findSowPackageItems(@Param("query") SowPackageItemQueryDTO queryDTO);

    /**
     * 查询播种任务明细
     *
     * @param request
     * @return
     */
    List<SowTaskItemPO> querySortingTaskListDetails(VirtualWarehouseSortingTaskRequestDTO request);

    /**
     * 领取分拣任务
     *
     * @param cityId
     * @param taskIds
     * @param userId
     */
    void getSortingTask(@Param("cityId") Integer cityId, @Param("taskIds") List<Long> taskIds,
                        @Param("userId") Long userId, @Param("userName") String userName);

    /**
     * 查询用户播种中任务信息
     *
     * @param request
     * @return
     */
    List<SowTaskItemPO> queryUserSortingTaskListDetails(VirtualWarehouseSortingTaskRequestDTO request);

    /**
     * 查询播种明细
     *
     * @param orgId
     * @param taskIds
     * @return
     */
    List<SowTaskItemPO> listSowTaskItemBySowTaskIds(@Param("orgId") Integer orgId,
                                                    @Param("taskIds") List<Long> taskIds);

    /**
     * 查询播种任务编号
     *
     * @param request
     * @return
     */
    List<SowTaskItemPO> querySowTaskNoByItemId(VirtualWarehouseSortingTaskRequestDTO request);

    /**
     * 查询未播种产品
     *
     * @param collect
     * @param cityId
     * @return
     */
    List<SowTaskItemPO> queryNotSkuCount(@Param("collect") List<Long> collect, @Param("cityId") Integer cityId);

    /**
     * 查询播种任务编号
     *
     * @param request
     * @return
     */
    List<SowTaskItemPO> findSowTaskNoByItemId(ConcertSowTaskRequestDTO request);

    int findBySowTaskItemSkuCount(@Param("sowTaskId") Long sowTaskId);

}