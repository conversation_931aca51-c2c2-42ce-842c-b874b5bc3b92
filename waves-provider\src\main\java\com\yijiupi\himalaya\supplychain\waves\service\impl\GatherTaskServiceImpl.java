package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.batch.IGatherTaskService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.GatherTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.TransferOrderBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.TransferOrderLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskLocationScaleDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.gathertask.GatherTaskProductScaleDTO;
import com.yijiupi.himalaya.supplychain.waves.search.CompleteGatherTaskSO;
import com.yijiupi.himalaya.supplychain.waves.search.GatherTaskSO;

/**
 * 集货任务任务
 *
 * <AUTHOR> 2019/10/31
 */
@Service(timeout = 120000)
public class GatherTaskServiceImpl implements IGatherTaskService {

    @Autowired
    private GatherTaskBL gatherTaskBL;
    @Autowired
    private TransferOrderBL transferOrderBL;

    /**
     * 生成集货任务
     * 
     * @param batchTaskId
     * @param userId
     */
    @Override
    public void createGatherTask(String batchTaskId, Long userId) {
        AssertUtils.notNull(batchTaskId, "拣货任务编号不能为空！");
        AssertUtils.notNull(userId, "操作人编号不能为空！");
        gatherTaskBL.createGatherTask(batchTaskId, userId);
    }

    /**
     * 查询集货任务所有出库位 必传batchTaskId 过滤locationName
     * 
     * @param so
     * @return
     */
    @Override
    public List<GatherTaskLocationScaleDTO> findGatherTaskLocation(GatherTaskSO so) {
        AssertUtils.notNull(so.getBatchTaskId(), "拣货任务编号不能为空！");
        return gatherTaskBL.findGatherTaskLocation(so);
    }

    /**
     * 查询集货任务出库位明细 必传batchTaskId、locationId
     * 
     * @param so
     * @return
     */
    @Override
    public GatherTaskLocationScaleDTO findGatherTaskLocationDetail(GatherTaskSO so) {
        AssertUtils.notNull(so.getBatchTaskId(), "拣货任务编号不能为空！");
        AssertUtils.notNull(so.getLocationId(), "出库位编号不能为空！");
        return gatherTaskBL.findGatherTaskLocationDetail(so);
    }

    /**
     * 查询集货任务所有产品 必传 batchTaskId 过滤productName
     * 
     * @param so
     * @return
     */
    @Override
    public List<GatherTaskProductScaleDTO> findGatherTaskProduct(GatherTaskSO so) {
        AssertUtils.notNull(so.getBatchTaskId(), "拣货任务编号不能为空！");
        return gatherTaskBL.findGatherTaskProduct(so);
    }

    /**
     * 根据集货产品出库位明细 必传 batchTaskId、gatherTaskProductId
     * 
     * @param so
     * @return
     */
    @Override
    public GatherTaskProductScaleDTO findGatherTaskProductDetail(GatherTaskSO so) {
        AssertUtils.notNull(so.getBatchTaskId(), "拣货任务编号不能为空！");
        AssertUtils.notNull(so.getGatherTaskProductId(), "集货任务产品明细编号不能为空！");
        return gatherTaskBL.findGatherTaskProductDetail(so);
    }

    /**
     * 修改集货任务出库位集货状态 只传id、status(0=待集货，1=已集货)、lastUpdateUser
     * 
     * @param dto
     */
    @Override
    public void updateGatherTaskLocationStatus(GatherTaskLocationDTO dto) {
        AssertUtils.notNull(dto.getId(), "集货任务出库位编号不能为空！");
        AssertUtils.notNull(dto.getStatus(), "集货任务出库位状态不能为空！");
        AssertUtils.notNull(dto.getLastUpdateUser(), "集货任务出库位修改人不能为空！");
        gatherTaskBL.updateGatherTaskLocationStatus(dto);
    }

    @Override
    public void completeGatherTask(CompleteGatherTaskSO completeGatherTaskSO) {
        gatherTaskBL.completeGatherTask(completeGatherTaskSO);
    }

    @Override
    public void transferOrderLocation(TransferOrderLocationDTO dto) {
        transferOrderBL.transferOrderLocation(dto);
    }

}
