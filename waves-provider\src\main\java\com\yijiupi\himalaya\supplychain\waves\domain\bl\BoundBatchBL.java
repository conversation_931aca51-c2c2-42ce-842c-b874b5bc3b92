package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutBoundBatchDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutBoundBatchManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;

/**
 * 出库批次、入库批次的操作
 *
 * <AUTHOR>
 * @Date 2022/1/19
 */
@Service
public class BoundBatchBL {
    private static final Logger LOG = LoggerFactory.getLogger(BoundBatchBL.class);
    @Reference
    private IOutBoundBatchManageService iOutBoundBatchManageService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("ex.supplychain.outBoundBatch.updateStorageLocation")
    private String updateOutLocationEx;

    /**
     * 更新出库批次的出库位
     */
    public void updateOutboundBatchLocation(List<OutStockOrderPO> outOrderPOList) {
        if (CollectionUtils.isEmpty(outOrderPOList)) {
            return;
        }
        outOrderPOList.stream().filter(elem -> elem.getBoundNo() != null)
            .collect(Collectors.groupingBy(OutStockOrderPO::getBoundNo)).forEach((outboundNo, outOrderList) -> {
                List<String> locationNameList =
                    outOrderList.stream().filter(elem -> CollectionUtils.isNotEmpty(elem.getLocationNameList()))
                        .flatMap(elem -> elem.getLocationNameList().stream()).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(locationNameList)) {
                    return;
                }

                String outLocation = null;
                if (locationNameList.size() <= 2) {
                    outLocation = String.valueOf(locationNameList);
                } else {
                    outLocation = String.format("[%s等%d个]", locationNameList.get(0), locationNameList.size());
                }

                OutBoundBatchDTO updateInfo = new OutBoundBatchDTO();
                updateInfo.setBoundBatchNo(outboundNo);
                updateInfo.setStorageLocation(outLocation);
                updateInfo.setLastUpdateTime(new Date());
                iOutBoundBatchManageService.updateByNo(updateInfo);
                LOG.info("更新出库批次的出库位成功：{}", JSON.toJSONString(updateInfo));
            });
    }

    /**
     * 发送更新出库批次出库批次的消息
     */
    public void sendUpdateOutLocationMsg(List<OutStockOrderPO> orderPOList) {
        if (CollectionUtils.isEmpty(orderPOList)) {
            return;
        }
        rabbitTemplate.convertAndSend(updateOutLocationEx, null, orderPOList);
        // this.updateOutboundBatchLocation(orderPOList);
    }
}
