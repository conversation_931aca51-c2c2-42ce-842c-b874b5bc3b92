package com.yijiupi.himalaya.supplychain.waves.domain.bl.meituan;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.outstock.service.IOrderQueryService;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskDTO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-01-25 14:52
 **/
@Service
public class MTSaleInventoryBL {

    @Reference
    private IOrderQueryService orderQueryService;

    /**
     * 填充销售库存
     */
    public void fillSaleInventory(List<BatchTaskDTO> batchTasks, Integer warehouseId, Integer cityId) {
//        List<OutStockOrderItemDTO> items = batchTasks.stream().map(BatchTaskDTO::getOutStockOrderItemList)
//                .flatMap(Collection::stream)
//                .filter(it -> SourceType.MEI_TUAN.valueEquals(it.getOutStockOrderSource()))
//                .collect(Collectors.toList());
//        List<SaleInventoryQueryDTO> queries = SaleInventoryQueryDTOConvertor.convertAndFilterList(items, warehouseId, cityId);
//        Map<String, BigDecimal> saleInventoryMap = orderQueryService.getSaleInventoryMap(queries);
//        for (OutStockOrderItemDTO item : items) {
//            item.setSaleInventory(saleInventoryMap.get(String.valueOf(item.getProductSpecificationId())));
//        }
    }

}
