package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor;

import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.model.ProcessBatchDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.ExpressFlagEnum;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Objects;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/22
 */
public class WaveCreateConvertor {

    /**
     * 设置波次的订单类型
     * 
     * @param processBatchDTO
     * @param orders
     * @return
     */
    public static Byte getBatchOrderType(ProcessBatchDTO processBatchDTO, List<OutStockOrderPO> orders) {
        if (Objects.nonNull(processBatchDTO.getWorkSetting())) {
            return BatchOrderTypeEnum.团购订单.getType();
        }
        if (orders.stream()
            .anyMatch(order -> order.getOrdertype() != null && Objects.equals(order.getOrdertype().byteValue(),
                com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderTypeEnum.团购销售.getType()))) {
            return BatchOrderTypeEnum.团购订单.getType();
        }

        if (Objects.equals(processBatchDTO.getExpressFlag(), ExpressFlagEnum.快递直发订单.getType())) {
            return BatchOrderTypeEnum.快递直发订单.getType();
        }

        if (isAllotOrder(orders)) {
            return BatchOrderTypeEnum.内配_调拨订单.getType();
        }

        return BatchOrderTypeEnum.普通订单.getType();
    }

    private static boolean isAllotOrder(List<OutStockOrderPO> orderPOList) {
        // 是否是中心仓内配单
        boolean isCenterNpOrder = orderPOList.stream()
                .anyMatch(p -> Objects.nonNull(p.getOutBoundType())
                        && OutBoundTypeEnum.INTERNAL_DELIVERY_ORDER.getCode().byteValue() == p.getOutBoundType()
                        && OrderConstant.ALLOT_TYPE_ALLOCATION.equals(p.getAllotType()));
        if (BooleanUtils.isTrue(isCenterNpOrder)) {
            return Boolean.TRUE;
        }

        // 是否是中心仓调拨单
        boolean isAllotOrder = orderPOList.stream()
                .anyMatch(p -> Objects.nonNull(p.getOutBoundType())
                        && OutBoundTypeEnum.ALLOT_ORDER.getCode().byteValue() == p.getOutBoundType()
                        && OrderConstant.ALLOT_TYPE_ALLOCATION.equals(p.getAllotType()));
        if (BooleanUtils.isTrue(isAllotOrder)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

}
