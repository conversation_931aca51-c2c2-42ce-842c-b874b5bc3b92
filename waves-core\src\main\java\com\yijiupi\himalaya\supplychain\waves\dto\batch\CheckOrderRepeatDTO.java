package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.util.List;

/**
 * 检查订单是否重复生波次
 *
 * <AUTHOR>
 * @date 1/5/21 3:23 PM
 */
public class CheckOrderRepeatDTO implements Serializable {
    private static final long serialVersionUID = 4817734822137469190L;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 订单号
     */
    private List<String> refOrderNos;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<String> getRefOrderNos() {
        return refOrderNos;
    }

    public void setRefOrderNos(List<String> refOrderNos) {
        this.refOrderNos = refOrderNos;
    }
}
