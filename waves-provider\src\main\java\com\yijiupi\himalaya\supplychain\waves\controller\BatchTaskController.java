package com.yijiupi.himalaya.supplychain.waves.controller;

import java.util.List;

import javax.annotation.Resource;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskManageService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.BatchOrderTaskBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.PackageOrderItemBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskCompleteApplicationBL;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.*;
import com.yijiupi.supplychain.serviceutils.ThreadLocalUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckParam;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckResultDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.PackageOrderItemBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskQueryBL;
import com.yijiupi.himalaya.supplychain.waves.dto.base.BaseResult;
import com.yijiupi.himalaya.supplychain.waves.dto.base.ROResult;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemPeriodConfigQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemPeriodConfigResultDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.QueryOrderNoByCodeDTO;

/**
 * 波次API
 *
 * <AUTHOR>
 * @date 2025/4/30
 */
@RestController
public class BatchTaskController {

    private static final Logger LOGGER = LoggerFactory.getLogger(BatchTaskController.class);

    @Resource
    private PackageOrderItemBL packageOrderItemBL;
    @Autowired
    private BatchTaskQueryBL batchTaskQueryBL;
    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;

    @Reference
    private IBatchTaskManageService iBatchTaskManageService;

    /**
     * 拣货任务项禁止销售配置查询
     */
    @RequestMapping(value = "/batchTask/listBatchTaskItemPeriodConfig", method = RequestMethod.POST)
    public BaseResult listBatchTaskItemPeriodConfig(@RequestBody BatchTaskItemPeriodConfigQueryDTO queryDTO) {
        LOGGER.info("禁止销售检查入参：{}", JSON.toJSONString(queryDTO));
        List<BatchTaskItemPeriodConfigResultDTO> resultDTOS = batchTaskQueryBL.listBatchTaskItemPeriodConfig(queryDTO);
        return new ROResult(resultDTOS);
    }

    /**
     * 根据订单号或装箱码查找订单号
     */
    @RequestMapping(value = "/batchTask/findOrderNoByCode", method = RequestMethod.POST)
    public BaseResult findOrderNoByCode(@RequestBody QueryOrderNoByCodeDTO dto) {
        AssertUtils.notNull(dto.getCode(), "订单号或装箱码不能为空");
        AssertUtils.notNull(dto.getOrgId(), "组织机构Id不能为空");
        List<String> orderNoList =
            packageOrderItemBL.listOrderNoByCode(dto.getCode(), dto.getOrgId(), dto.getWarehouseId());
        ROResult dataResult = new ROResult<>(orderNoList);
        return dataResult;
    }

    /**
     * 查询同客户已完成的托盘信息
     */
    @RequestMapping(value = "/batchTask/findSameUserPalletInfo", method = RequestMethod.POST)
    public ROResult<UserSamePalletInfoResultDTO> findSameUserPalletInfo(@RequestBody UserSamePalletInfoQueryDTO dto) {
        AssertUtils.notNull(dto.getOrderNo(), "订单号不能为空");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空");
        return ROResult.getResult(batchOrderTaskBL.findSameUserPalletInfo(dto));
    }

    /**
     * 根据类目禁止销售期检查
     */
    @RequestMapping(value = "/batchTask/checkByForbidSalesDays", method = RequestMethod.POST)
    public BaseResult checkByForbidSalesDays(@RequestBody OutStockConfigCheckParam checkParam) {
        List<OutStockConfigCheckResultDTO> checkResultDTOList = batchTaskQueryBL.checkByForbidSalesDays(checkParam);
        return new ROResult(checkResultDTOList);
    }

    /**
     * 按订单 提交拣货任务
     *
     * @param batchTaskCompleteDTO
     * @return
     */
    @RequestMapping(value = "batchTask/completeBatchTask", method = RequestMethod.POST)
    public BaseResult completeBatchTask(@RequestBody BatchTaskCompleteDTO batchTaskCompleteDTO) {
        try {
            batchTaskCompleteDTO.setOperateSource(BatchTaskCompleteDTO.SOURCE_PDA);
            batchTaskCompleteDTO.setOptUserId(ThreadLocalUtil.getCurrentUserInfo());

            AssertUtils.notNull(batchTaskCompleteDTO.getBatchTaskId(), "拣货任务信息不能为空！");
            AssertUtils.notNull(batchTaskCompleteDTO.getOptUserId(), "操作人信息不能为空！");

            iBatchTaskManageService.completeBatchTask(batchTaskCompleteDTO);
            return BaseResult.getSuccessResult();
        } catch (Exception e) {
            LOGGER.error("拣货任务提交失败", e);
            BaseResult baseResult = BaseResult.getErrorResult(e.getMessage());
            baseResult.setSuccess(Boolean.FALSE);
            return baseResult;
        }
    }
}
