package com.yijiupi.himalaya.supplychain.waves.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.exceptions.HasLockedException;
import com.yijiupi.himalaya.supplychain.waves.batch.IPackageOrderService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.PackageOrderInfoBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.PackageOrderItemBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.SowManagerBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.packageorder.PackageOrderItemModBL;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.*;
import com.yijiupi.himalaya.supplychain.waves.search.PackageOrderItemSO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2018/7/13 10:22
 */
@Service(timeout = 120000)
public class PackageOrderServiceImpl implements IPackageOrderService {

    @Resource
    private PackageOrderItemBL packageOrderItemBL;

    @Resource
    private SowManagerBL sowManagerBL;

    @Resource
    private PackageOrderInfoBL packageOrderInfoBL;

    @Resource
    private PackageOrderItemModBL packageOrderItemModBL;

    private static final Logger LOGGER = LoggerFactory.getLogger(PackageOrderServiceImpl.class);

    /**
     * 根据出库单号查询包装信息详情
     */
    @Override
    public PageList<PackageOrderItemDTO> listPackageOrderItem(PackageOrderItemSO packageOrderItemSO) {
        return packageOrderItemBL.listPackageOrderItem(packageOrderItemSO);
    }

    /**
     * 新增包装详情
     */
    @Override
    public void savePackage(PackageOrderItemDTO packageOrderItemDTO) {
        packageOrderItemBL.savePackage(packageOrderItemDTO);
    }

    /**
     * 批量新增包装详情, 客户端播种打包走的这个接口
     */
    @Override
    public void savePackageBatch(List<PackageOrderItemDTO> packageOrderItemDTOList) {
        packageOrderItemBL.savePackageBatch(packageOrderItemDTOList, false, false);
    }

    /**
     * 批量新增包装详情(PDA)
     */
    @Override
    public void savePackageBatchByPDA(List<PackageOrderItemDTO> packageOrderItemDTOList) {
        try {
            packageOrderItemDTOList = filterDuplicateItem(packageOrderItemDTOList);
            PackageOrderItemDTO packageOrderItemDTO = packageOrderItemDTOList.get(0);
            packageOrderItemBL.savePackageBatchByPDA(packageOrderItemDTO.getRefOrderNo(), packageOrderItemDTOList,
                true);
        } catch (HasLockedException e) {
            LOGGER.warn("触发分布式互斥锁：{}", JSON.toJSONString(packageOrderItemDTOList));
            throw new BusinessValidateException("系统忙，请稍后再试！");
        }
    }

    /**
     * 装箱数据去重，根据订单项ID+箱号，合并数量
     *
     * @param packageOrderItemDTOList
     * @return
     */
    private List<PackageOrderItemDTO> filterDuplicateItem(List<PackageOrderItemDTO> packageOrderItemDTOList) {
        Map<String, PackageOrderItemDTO> uniqueItems = new HashMap<>();
        for (PackageOrderItemDTO item : packageOrderItemDTOList) {
            String key = item.getRefOrderItemId() + "-" + item.getBoxCode();
            if (uniqueItems.containsKey(key)) {
                PackageOrderItemDTO existingItem = uniqueItems.get(key);
                existingItem.setPackageCount(existingItem.getPackageCount().add(item.getPackageCount()));
                existingItem.setUnitCount(existingItem.getUnitCount().add(item.getUnitCount()));
                existingItem.setUnitTotalCount(existingItem.getUnitTotalCount().add(item.getUnitTotalCount()));
            } else {
                uniqueItems.put(key, item);
            }
        }
        if (uniqueItems.size() != packageOrderItemDTOList.size()) {
            LOGGER.warn("装箱数据合并 结果：{} ，原始数据：{}", JSON.toJSONString(uniqueItems.values()),
                JSON.toJSONString(packageOrderItemDTOList));
        }
        return new ArrayList<>(uniqueItems.values());
    }

    /**
     * 根据订单号获取打印子箱条码信息
     */
    @Override
    public List<PackageCodePrintDTO> listPackageCodePrint(String refOrderNo, Integer cityId, Integer warehouseId) {
        return packageOrderItemBL.listPackageCodePrint(refOrderNo, cityId, warehouseId);
    }

    /**
     * 根据订单号获取打印子箱条码信息
     */
    @Override
    public List<PackageCodePrintDTO> findPackageCodePrint(PackageCodePrintQueryParam queryParam) {
        return packageOrderItemBL.listPackageCodePrint(queryParam.getRefOrderNo(), queryParam.getOrgId(),
            queryParam.getWarehouseId());
    }

    /**
     * 根据播种任务编号获取打印子箱条码信息
     */
    @Override
    public List<PackageCodePrintDTO> listPackageCodePrintBySowTaskNo(Integer orgId, String sowTaskNo) {
        return packageOrderItemBL.listPackageCodePrintBySowTaskNo(orgId, sowTaskNo);
    }

    /**
     * 根据订单编号获取打印子箱条码信息
     */
    @Override
    public List<PackageCodePrintDTO> listPackageCodePrintByOrderNos(Integer orgId, Integer warehouseId,
        List<String> orderNos) {
        return packageOrderItemBL.listPackageCodePrintByOrderNos(orgId, warehouseId, orderNos);
    }

    /**
     * 批量新增包装详情
     */
    @Override
    public String savePackageBatchByOrderId(PackageOrderSaveDTO packageOrderSaveDTO) {
        Integer orgId = packageOrderSaveDTO.getOrgId();
        List<PackageOrderItemDTO> packageOrderItemList = packageOrderSaveDTO.getPackageOrderItemList();
        AssertUtils.notNull(orgId, "城市id不能为空");

        // 涉及播种包装时，需分摊数据
        if (packageOrderSaveDTO.getLocationName() != null && packageOrderSaveDTO.getPda()) {
            packageOrderItemList = sowManagerBL.sharePackageItemsBySowTaskNo(packageOrderSaveDTO);
        }

        // 包装数据校验
        // if (packageOrderSaveDTO.getPda()) {
        // packageOrderItemBL.checkPackageItems(packageOrderItemList, orgId);
        // }

        // String msg = null;

        // 包装数据校验,播种判断
        // if (packageOrderSaveDTO.getSowTaskNo() != null) {
        // msg = sowManagerBL.validateSowPackageItems(packageOrderItemList, packageOrderSaveDTO.getSowTaskNo(), orgId,
        // packageOrderSaveDTO.getOperator());
        // }

        packageOrderItemBL.savePackageBatchByOrderId(packageOrderItemList, orgId, packageOrderSaveDTO.getPda());

        return null;
    }

    /**
     * 根据箱号完整编码查询内配单号
     */
    @Override
    public List<String> listOrderNoByBoxCodeNo(String boxCodeNo) {
        return packageOrderItemBL.listOrderNoByBoxCodeNo(boxCodeNo);
    }

    /**
     * 根据条码判断箱码，订单号，查找对应订单信息
     */
    @Override
    public PackageOrderDTO getPackageOrderByBarCode(Integer orgId, Integer warehouseId, String barCode) {
        AssertUtils.notNull(orgId, "城市id不能为空");
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        AssertUtils.notNull(barCode, "条码不能为空");
        return packageOrderItemBL.getPackageOrderByBarCode(orgId, warehouseId, barCode);
    }

    /**
     * 根据出库单号查询包装信息详情(不做状态校验)
     */
    @Override
    public PageList<PackageOrderItemDTO> listPackageOrderItemAll(PackageOrderItemSO packageOrderItemSO) {
        return packageOrderItemBL.listPackageOrderItemAll(packageOrderItemSO);
    }

    /**
     * 根据单号查询已播种的包装信息
     */
    @Override
    public Map<String, List<PackageOrderItemDTO>> listSowPackageOrderItems(List<String> orderNos, Integer orgId,
        Integer warehouseId) {
        AssertUtils.notNull(orgId, "城市id不能为空");
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        AssertUtils.notEmpty(orderNos, "订单号不能为空");
        return packageOrderItemBL.listSowPackageOrderItems(orderNos, orgId, warehouseId);
    }

    /**
     * 整拖复核列表查询
     */
    @Override
    public PageList<PalletOrderDTO> pageListPalletOrders(PalletOrderQueryDTO palletOrderQueryDTO) {
        return packageOrderItemBL.pageListPalletOrders(palletOrderQueryDTO);
    }

    /**
     * 整拖复核明细查询
     */
    @Override
    public List<PalletOrderItemDTO> listPalletOrderItems(PalletOrderQueryDTO palletOrderQueryDTO) {
        return packageOrderItemBL.listPalletOrderItems(palletOrderQueryDTO);
    }

    /**
     * 整拖复核
     */
    @Override
    public void packageOrderItemReview(List<PackageOrderItemDTO> packageOrderItemDTOS) {
        packageOrderItemBL.packageOrderItemReview(packageOrderItemDTOS);
    }

    /**
     * 根据订单编号删除包装信息
     */
    @Override
    public void removePackageByRefOrderNos(List<String> refOrderNos, Integer orgId, Integer warehouseId) {
        LOGGER.info("removePackageByRefOrderNos: {}, {}, {}", refOrderNos, orgId, warehouseId);
        packageOrderItemBL.removePackageByRefOrderNos(refOrderNos, orgId, warehouseId);
    }

    /**
     * 删除包装信息
     */
    @Override
    public void removePackageInfo(RemovePackageInfoParam param) {
        List<String> refOrderNos = param.getRefOrderNos();
        Integer orgId = param.getOrgId();
        Integer warehouseId = param.getWarehouseId();
        Integer opUserId = param.getOpUserId();
        LOGGER.info("removePackageByRefOrderNos: {}", JSON.toJSONString(param, SerializerFeature.WriteMapNullValue));
        packageOrderItemBL.removePackageByRefOrderNos(refOrderNos, orgId, warehouseId, opUserId);
    }

    /**
     * 批量获取打包信息
     */
    @Override
    public List<PackageOrderItemDTO> listPackageOrderItemByOrderIdList(PackageOrderItemSO packageOrderItemSO) {
        return packageOrderItemBL.listPackageOrderItemByOrderIdList(packageOrderItemSO);
    }

    /**
     * 根据该批次中最大订单数
     */
    @Override
    public List<PackageOrderItemDTO> getBoxMaxCountByOrderIdList(PackageOrderItemSO packageOrderItemSO) {
        return packageOrderItemBL.getBoxMaxCountByOrderIdList(packageOrderItemSO);
    }

    /**
     * 通过订单号查询装箱信息
     *
     * @param param 查询参数
     * @return 装箱信息
     */
    @Override
    public PackageOrderInfoDTO findPackageOrderByOrderNo(PackageOrderItemSO param) {
        AssertUtils.notNull(param, "参数不能为空");
        AssertUtils.notNull(param.getRefOrderNo(), "订单号不能为空");
        AssertUtils.notNull(param.getOrgId(), "城市 id 不能为空");
        AssertUtils.notNull(param.getWarehouseId(), "仓库 id 不能为空");
        return packageOrderInfoBL.findPackageOrderByOrderNo(param);
    }

    /**
     * 获取订单打包信息, 给客户端 订单详情页面使用<br/>
     * 其中的 refOrderItemId 将会被替换成 businessItemId
     *
     * @param packageOrderItemSO 查询条件
     */
    @Override
    public List<PackageOrderItemDTO> queryPackageOrderItem(PackageOrderItemSO packageOrderItemSO) {
        return packageOrderItemBL.queryPackageOrderItem(packageOrderItemSO);
    }

    /**
     * 修改包装箱信息工具
     *
     * @param dto
     */
    @Override
    public void modLackPackageOrderItemByOutBoundInfo(ModPackageInfoByOutBoundInfoDTO dto) {
        packageOrderItemModBL.modLackPackageOrderItemByOutBoundInfo(dto);
    }

    /**
     * 重新同步包装箱信息工具
     *
     * @param dto
     */
    @Override
    public void syncPackageOrderItemByOutBoundInfo(ModPackageInfoByOutBoundInfoDTO dto) {
        packageOrderItemModBL.syncPackageOrderItemByOutBoundInfo(dto);
    }

    /**
     * 出库时如果打包信息有问题，重新计算
     *
     * @param dto
     */
    @Override
    public void resetPackageOrderItemBeforeOutBound(ResetPackageOrderItemBeforeOutBoundDTO dto) {
        packageOrderItemModBL.resetPackageOrderItemBeforeOutBound(dto);
    }

    /**
     * 扫描订单箱号查询包装箱数
     *
     * @param dto
     * @return
     */
    @Override
    public List<ScanOrderPackageInfoResultDTO> scanOrderPackageInfo(ScanOrderPackageInfoQueryDTO dto) {
        AssertUtils.notNull(dto.getBoxCodeNo(), "箱号信息不能为空！");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");

        return packageOrderItemBL.scanOrderPackageInfo(dto);
    }

    /**
     * 删除包装箱信息
     *
     * @param dto
     */
    @Override
    public void removePackage(ModPackageInfoByOutBoundInfoDTO dto) {
        packageOrderItemModBL.removePackage(dto);
    }

    /**
     * 修改
     *
     * @param dto
     */
    @Override
    public void sendModPackage(ModPackageInfoByOutBoundInfoDTO dto) {
        packageOrderItemModBL.sendModPackage(dto);
    }

}
