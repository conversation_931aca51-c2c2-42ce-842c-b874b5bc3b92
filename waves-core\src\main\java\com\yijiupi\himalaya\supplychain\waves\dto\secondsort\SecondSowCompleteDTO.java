package com.yijiupi.himalaya.supplychain.waves.dto.secondsort;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/10 16:53
 */
public class SecondSowCompleteDTO implements Serializable {
    private static final long serialVersionUID = 2548557937345071137L;

    /**
     * 拣货任务明细id
     */
    private String batchTaskItemId;
    /**
     * 拣货任务id
     */
    private String batchTaskId;
    /**
     * 城市id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 操作人id
     */
    private Integer operatorUserId;
    /**
     * 操作人名称
     */
    private String operatorUserName;

    private Long batchId;

    List<SowPalletDTO> items;

    public String getBatchTaskItemId() {
        return batchTaskItemId;
    }

    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getOperatorUserId() {
        return operatorUserId;
    }

    public void setOperatorUserId(Integer operatorUserId) {
        this.operatorUserId = operatorUserId;
    }

    public String getOperatorUserName() {
        return operatorUserName;
    }

    public void setOperatorUserName(String operatorUserName) {
        this.operatorUserName = operatorUserName;
    }

    public List<SowPalletDTO> getItems() {
        return items;
    }

    public void setItems(List<SowPalletDTO> items) {
        this.items = items;
    }

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }
}
