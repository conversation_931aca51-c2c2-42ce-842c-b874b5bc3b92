package com.yijiupi.himalaya.supplychain.waves.domain.bo;

import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.PartSendWsmDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/1/10
 */
public class NotifyMeiTuanLackBO {
    /**
     * 缺货信息
     */
    private List<PartSendWsmDTO> partSendWsmDTOList;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 缺货trace
     */
    Map<String, StringBuffer> bufferMap;
    /**
     * 操作人
     */
    Integer operatorUserId;

    /**
     * 获取 缺货信息
     *
     * @return partSendWsmDTOList 缺货信息
     */
    public List<PartSendWsmDTO> getPartSendWsmDTOList() {
        return this.partSendWsmDTOList;
    }

    /**
     * 设置 缺货信息
     *
     * @param partSendWsmDTOList 缺货信息
     */
    public void setPartSendWsmDTOList(List<PartSendWsmDTO> partSendWsmDTOList) {
        this.partSendWsmDTOList = partSendWsmDTOList;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 缺货trace
     *
     * @return bufferMap 缺货trace
     */
    public Map<String, StringBuffer> getBufferMap() {
        return this.bufferMap;
    }

    /**
     * 设置 缺货trace
     *
     * @param bufferMap 缺货trace
     */
    public void setBufferMap(Map<String, StringBuffer> bufferMap) {
        this.bufferMap = bufferMap;
    }

    /**
     * 获取 操作人
     *
     * @return operatorUserId 操作人
     */
    public Integer getOperatorUserId() {
        return this.operatorUserId;
    }

    /**
     * 设置 操作人
     *
     * @param operatorUserId 操作人
     */
    public void setOperatorUserId(Integer operatorUserId) {
        this.operatorUserId = operatorUserId;
    }
}
