package com.yijiupi.himalaya.supplychain.waves.service.impl;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserLoginService;
import com.yijiupi.himalaya.supplychain.waves.batch.ISowQueryService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.SowQueryBL;
import com.yijiupi.himalaya.supplychain.waves.dto.soworder.*;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.*;

@Service(timeout = 120000)
public class SowQueryServiceImpl implements ISowQueryService {

    @Autowired
    private SowQueryBL sowQueryBL;
    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @Reference
    private IAdminUserLoginService iAdminUserLoginService;
    private static final Logger LOG = LoggerFactory.getLogger(SowQueryServiceImpl.class);

    /**
     * 查询播种任务列表
     */
    @Override
    public PageList<SowTaskDTO> findSowTaskList(SowTaskQueryDTO sowTaskQueryDTO) {
        return sowQueryBL.findSowTaskList(sowTaskQueryDTO);
    }

    /**
     * 根据播种任务编号查询播种任务中的订单信息
     */
    @Override
    public List<SowOrderInfoDTO> listOutStockOrderBySowTaskNo(String sowTaskNo, Integer orgId) {
        AssertUtils.notNull(sowTaskNo, "播种任务编号不能为空");
        return sowQueryBL.listOutStockOrderBySowNo(sowTaskNo, orgId);
    }

    /**
     * 根据播种单号，查询播种信息
     */
    @Override
    public List<SowTaskDTO> findSowTaskByTaskNos(Integer orgId, List<String> sowTaskNos) {
        AssertUtils.notNull(orgId, "城市Id不能为空");
        AssertUtils.notEmpty(sowTaskNos, "播种任务单号不能为空");
        return sowQueryBL.findSowTaskByTaskNos(orgId, sowTaskNos);
    }

    /**
     * 条件查询打包订单
     */
    @Override
    public PageList<UnpackOrderDTO> findUnpackOrderList(UnpackOrderQueryDTO unpackOrderQueryDTO) {
        AssertUtils.notNull(unpackOrderQueryDTO.getOrgId(), "城市Id不能为空");
        AssertUtils.notNull(unpackOrderQueryDTO.getWarehouseId(), "仓库id不能为空");
        return sowQueryBL.findUnpackOrderList(unpackOrderQueryDTO);
    }

    /**
     * 查询播种打印数据
     */
    @Override
    public List<SowingPrintInfoDTO> listSowingPrintInfo(List<String> sowTaskNos, Integer orgId) {
        AssertUtils.notNull(orgId, "城市Id不能为空");
        AssertUtils.notEmpty(sowTaskNos, "播种任务编号不能为空");
        return sowQueryBL.listSowingPrintInfo(sowTaskNos, orgId);
    }

    /**
     * 查询播种产品明细项
     */
    @Override
    public List<SowProductItemDTO> listSowProductItem(SowProductItemQueryDTO sowProductItemQueryDTO) {
        AssertUtils.notNull(sowProductItemQueryDTO.getOrgId(), "orgId不能为空");
        return sowQueryBL.listSowProductItem(sowProductItemQueryDTO);
    }

    /**
     * 查询播种任务自提点信息
     */
    @Override
    public List<SowAddressDTO> listSowAddress(SowAddressQueryDTO sowAddressQueryDTO) {
        AssertUtils.notNull(sowAddressQueryDTO.getOrgId(), "城市Id不能为空");
        AssertUtils.notNull(sowAddressQueryDTO.getSowTaskNo(), "播种任务编号不能为空");
        return sowQueryBL.listSowAddress(sowAddressQueryDTO);
    }

    /**
     * 根据播种任务查询订单包装信息
     */
    @Override
    public List<SowProductItemDTO> listSowPackageProductItem(SowProductItemQueryDTO sowProductItemQueryDTO) {
        AssertUtils.notNull(sowProductItemQueryDTO.getOrgId(), "城市id不能为空");
        return sowQueryBL.listSowPackageProductItem(sowProductItemQueryDTO);
    }

    /**
     * 分页查询播种明细数据
     */
    @Override
    public PageList<SowOrderItemDTO> pageListSowOrderItems(SowOrderItemQueryDTO sowOrderItemQueryDTO) {
        return sowQueryBL.pageListSowOrderItems(sowOrderItemQueryDTO);
    }

    /**
     * 分页查询自提点播种明细数据
     */
    @Override
    public PageList<AddressSowTaskItemDTO> pageListAddressSowTaskItems(AddressSowTaskQueryDTO addressSowTaskQueryDTO) {
        AssertUtils.notNull(addressSowTaskQueryDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(addressSowTaskQueryDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(addressSowTaskQueryDTO.getQueryCondition(), "查询模式不能为空");
        return sowQueryBL.pageListAddressSowTaskItems(addressSowTaskQueryDTO);
    }

    @Override
    public List<SowTaskItemDTO> listSowOTaskItems(SowTaskItemQueryDTO queryDTO) {
        return sowQueryBL.listSowOTaskItems(queryDTO);
    }

    @Override
    public List<SowPackageItemDTO> findSowPackageItems(SowPackageItemQueryDTO queryDTO) {
        return sowQueryBL.findSowPackageItems(queryDTO);
    }

    @Override
    public List<SowTaskItemDTO> findByOrderItemIds(List<Long> orderItemIds) {
        return sowQueryBL.findByOrderItemIds(orderItemIds);
    }

    @Override
    public List<Long> findByLocationIds(Integer orgId, Integer warehouseId, List<Long> locationIds) {
        return sowQueryBL.findByLocationIds(orgId, warehouseId, locationIds);
    }

    @Override
    public List<SowOrderInfoDTO> listOutStockOrderDetails(SowTaskQueryDTO sowTaskQueryDTO) {
        return sowQueryBL.listOutStockOrderBySowNo(sowTaskQueryDTO.getSowTaskNo(), sowTaskQueryDTO.getOrgId());
    }

    /**
     * 根据 skuIds 和 states 查询播种任务的货位
     *
     * @param queryDTO 查询条件, 只有 skuId, states, orgId 和 operatorId 有用
     * @return 查询到的结果, 如果查不到就返回不可变的空集合 {@link Collections#emptyList()}
     */
    @Override
    public List<SowTaskLocationDTO> findSowTaskBySkuIds(SowTaskQueryDTO queryDTO) {
        return sowQueryBL.findSowTaskBySkuIds(queryDTO);
    }

    /**
     * 通过播种任务 id 查询缓存的物料箱号
     *
     * @param sowTaskId 播种任务 id
     * @param warehouseId 仓库 id
     */
    @Override
    public List<String> listWcsBoxNosBySowTaskId(Integer warehouseId, Long sowTaskId) {
        return sowQueryBL.listWcsBoxNosBySowTaskId(warehouseId, sowTaskId);
    }

    /**
     * 分页查询可追加的播种任务
     *
     * @param query 查询条件
     * @return 查询结果
     */
    @Override
    public PageList<SowTaskDTO> pageListAppendableSowTask(SowTaskQueryDTO query) {
        return sowQueryBL.pageListAppendableSowTask(query);
    }
}
