package com.yijiupi.himalaya.supplychain.waves.dto.outstockorder;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
public class PalletReviewLocationInfoDTO implements Serializable {

    /**
     * 线路/片区名称
     */
    private String routeAreaName;
    /**
     * 出库位id
     */
    private Long locationId;
    /**
     * 出库位名称
     */
    private String locationName;
    /**
     * 托盘位
     */
    private String palletNo;
    /**
     * 其它托盘位
     */
    private List<String> otherPalletNos;

    public String getRouteAreaName() {
        return routeAreaName;
    }

    public void setRouteAreaName(String routeAreaName) {
        this.routeAreaName = routeAreaName;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getPalletNo() {
        return palletNo;
    }

    public void setPalletNo(String palletNo) {
        this.palletNo = palletNo;
    }

    public List<String> getOtherPalletNos() {
        return otherPalletNos;
    }

    public void setOtherPalletNos(List<String> otherPalletNos) {
        this.otherPalletNos = otherPalletNos;
    }
}
