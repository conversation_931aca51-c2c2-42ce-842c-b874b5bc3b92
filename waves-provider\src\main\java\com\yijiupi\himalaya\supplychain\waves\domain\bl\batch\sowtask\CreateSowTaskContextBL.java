package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.sowtask;

import java.util.List;
import java.util.Optional;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.GoodsCollectionLocationBO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.BatchBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowOrderPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.SowTaskPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/18
 */
@Service
public class CreateSowTaskContextBL {

    @Autowired
    private List<CreateSowTaskBaseBL> createSowTaskBlList;
    @Autowired
    private CreateSowTaskNormalSowBL createSowTaskNormalSowBL;
    protected static final Logger LOG = LoggerFactory.getLogger(CreateSowTaskContextBL.class);

    public CreateSowTaskResultBO createSowTask(CreateSowTaskBO createSowTaskBO) {
        Optional<CreateSowTaskBaseBL> optional =
            createSowTaskBlList.stream().filter(m -> m.support(createSowTaskBO)).findAny();
        if (!optional.isPresent()) {
            LOG.info("创建播种任务时，未匹配到播种任务信息！走普通创建播种方法！");
            return createSowTaskNormalSowBL.doCreateSowTask(createSowTaskBO);
        }
        CreateSowTaskBaseBL createSowTaskBaseBL = optional.get();

        return createSowTaskBaseBL.createSowTask(createSowTaskBO);

    }

    public List<WaveCreateDTO> createSowTask(WavesStrategyBO wavesStrategyDTO, PassageDTO passageDTO, String title,
        String operateUser, Integer cityId, WarehouseConfigDTO warehouseConfigDTO, BatchBO batchBO,
        List<GoodsCollectionLocationBO> lstLocations, List<SowTaskPO> lstSowTaskPO, String locationName,
        List<SowOrderPO> lstSowOrders, String driverName, Boolean allocationFlag, boolean needRecheck,
        List<OutStockOrderPO> splitOrderList, WaveCreateDTO oriCreateDTO) {
        CreateSowTaskBO createSowTaskBO =
            CreateSowTaskBO.CreateSowTaskBOBuilder.aCreateSowTaskBO().withLstSowTaskPO(lstSowTaskPO)
                .withLstSowOrders(lstSowOrders).withBatchBO(batchBO).withOriCreateDTO(oriCreateDTO).withCityId(cityId)
                .withLstLocations(lstLocations).withLocationName(locationName).withDriverName(driverName)
                .withPassageDTO(passageDTO).withAllocationFlag(allocationFlag).withNeedRecheck(needRecheck)
                .withOperateUser(operateUser).withTitle(title).withSplitOrderList(splitOrderList)
                .withWarehouseConfigDTO(warehouseConfigDTO).withWavesStrategyDTO(wavesStrategyDTO).build();

        // createSowTaskBlList.forEach(item -> {
        // LOG.info("创建播种任务的类有：{}", item.getClass().getName());
        // });

        Optional<CreateSowTaskBaseBL> optional =
            createSowTaskBlList.stream().filter(m -> m.support(createSowTaskBO)).findAny();
        if (!optional.isPresent()) {
            LOG.info("创建播种任务时，未匹配到播种任务信息！走普通创建播种方法！");
            return createSowTaskNormalSowBL.doCreateSowTask(createSowTaskBO).getWaveCreateDTOList();
        }
        CreateSowTaskBaseBL createSowTaskBaseBL = optional.get();

        return createSowTaskBaseBL.createSowTask(createSowTaskBO).getWaveCreateDTOList();
    }

}
