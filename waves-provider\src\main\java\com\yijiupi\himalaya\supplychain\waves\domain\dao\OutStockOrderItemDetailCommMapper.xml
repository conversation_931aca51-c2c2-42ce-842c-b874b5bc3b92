<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderItemDetailCommMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="INTEGER" property="orgId"/>
        <result column="OutStockOrderItem_Id" jdbcType="BIGINT" property="outStockOrderItemId"/>
        <result column="Location_Id" jdbcType="BIGINT" property="locationId"/>
        <result column="LocationName" jdbcType="VARCHAR" property="locationName"/>
        <result column="ProductionDate" jdbcType="TIMESTAMP" property="productionDate"/>
        <result column="BatchTime" jdbcType="TIMESTAMP" property="batchTime"/>
        <result column="UnitTotalCount" jdbcType="DECIMAL" property="unitTotalCount"/>
        <result column="OutStockUnitTotalCount" jdbcType="DECIMAL" property="outStockUnitTotalCount"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="ProductSpecification_Id" jdbcType="BIGINT" property="productSpecificationId"/>
        <result column="Owner_Id" jdbcType="BIGINT" property="ownerId"/>
        <result column="SecOwner_Id" jdbcType="BIGINT" property="secOwnerId"/>
        <result column="BatchAttributeInfoNo" jdbcType="VARCHAR" property="batchAttributeInfoNo"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id, Org_Id, OutStockOrderItem_Id, Location_Id, LocationName,
        ProductionDate, BatchTime, UnitTotalCount, OutStockUnitTotalCount,
        Remark, CreateTime, LastUpdateTime,
        ProductSpecification_Id, Owner_Id, SecOwner_Id, BatchAttributeInfoNo
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from outstockorderitemdetail
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from outstockorderitemdetail
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO">
        insert into outstockorderitemdetail (Id, Org_Id, OutStockOrderItem_Id,
        Location_Id, LocationName, ProductionDate, BatchTime,
        UnitTotalCount, OutStockUnitTotalCount, Remark, CreateTime, LastUpdateTime,
        ProductSpecification_Id, Owner_Id, SecOwner_Id, BatchAttributeInfoNo
        )
        values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=INTEGER}, #{outStockOrderItemId,jdbcType=BIGINT},
        #{locationId,jdbcType=BIGINT}, #{locationName,jdbcType=VARCHAR}, #{productionDate,jdbcType=TIMESTAMP},
        #{batchTime,jdbcType=TIMESTAMP},
        #{unitTotalCount,jdbcType=DECIMAL}, #{outStockUnitTotalCount,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR},
        now(), now(),
        #{productSpecificationId,jdbcType=BIGINT}, #{ownerId,jdbcType=BIGINT}, #{secOwnerId,jdbcType=BIGINT},
        #{batchAttributeInfoNo,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO">
        insert into outstockorderitemdetail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="orgId != null">
                Org_Id,
            </if>
            <if test="outStockOrderItemId != null">
                OutStockOrderItem_Id,
            </if>
            <if test="locationId != null">
                Location_Id,
            </if>
            <if test="locationName != null and locationName != '' ">
                LocationName,
            </if>
            <if test="productionDate != null">
                ProductionDate,
            </if>
            <if test="batchTime != null">
                BatchTime,
            </if>
            <if test="unitTotalCount != null">
                UnitTotalCount,
            </if>
            <if test="outStockUnitTotalCount != null">
                OutStockUnitTotalCount,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="productSpecificationId != null">
                ProductSpecification_Id,
            </if>
            <if test="ownerId != null">
                Owner_Id,
            </if>
            <if test="secOwnerId != null">
                SecOwner_Id,
            </if>
            <if test="batchAttributeInfoNo != null and batchAttributeInfoNo != '' ">
                BatchAttributeInfoNo,
            </if>
            CreateTime,
            LastUpdateTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="outStockOrderItemId != null">
                #{outStockOrderItemId,jdbcType=BIGINT},
            </if>
            <if test="locationId != null">
                #{locationId,jdbcType=BIGINT},
            </if>
            <if test="locationName != null and locationName != '' ">
                #{locationName,jdbcType=VARCHAR},
            </if>
            <if test="productionDate != null">
                #{productionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="batchTime != null">
                #{batchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="unitTotalCount != null">
                #{unitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="outStockUnitTotalCount != null">
                #{outStockUnitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="productSpecificationId != null">
                #{productSpecificationId,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="secOwnerId != null">
                #{secOwnerId,jdbcType=BIGINT},
            </if>
            <if test="batchAttributeInfoNo != null and batchAttributeInfoNo != '' ">
                #{batchAttributeInfoNo,jdbcType=VARCHAR},
            </if>
            now(),
            now()
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO">
        update outstockorderitemdetail
        <set>
            <if test="orgId != null">
                Org_Id = #{orgId,jdbcType=INTEGER},
            </if>
            <if test="outStockOrderItemId != null">
                OutStockOrderItem_Id = #{outStockOrderItemId,jdbcType=BIGINT},
            </if>
            <if test="locationId != null">
                Location_Id = #{locationId,jdbcType=BIGINT},
            </if>
            <if test="locationName != null and locationName != '' ">
                LocationName = #{locationName,jdbcType=VARCHAR},
            </if>
            <if test="productionDate != null">
                ProductionDate = #{productionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="batchTime != null">
                BatchTime = #{batchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="unitTotalCount != null">
                UnitTotalCount = #{unitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="outStockUnitTotalCount != null">
                OutStockUnitTotalCount = #{outStockUnitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="productSpecificationId != null">
                ProductSpecification_Id = #{productSpecificationId,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                Owner_Id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="secOwnerId != null">
                SecOwner_Id = #{secOwnerId,jdbcType=BIGINT},
            </if>
            <if test="batchAttributeInfoNo != null and batchAttributeInfoNo != '' ">
                BatchAttributeInfoNo = #{batchAttributeInfoNo,jdbcType=VARCHAR},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="insertBatch">
        insert into outstockorderitemdetail (Id, Org_Id, OutStockOrderItem_Id,
        Location_Id, LocationName, ProductionDate, BatchTime,
        UnitTotalCount, OutStockUnitTotalCount, Remark, CreateTime, LastUpdateTime,
        ProductSpecification_Id, Owner_Id, SecOwner_Id, BatchAttributeInfoNo
        )
        values
        <foreach collection="itemDetails" item="itemDetail" separator=",">
            (
            #{itemDetail.id,jdbcType=BIGINT}, #{itemDetail.orgId,jdbcType=INTEGER},
            #{itemDetail.outStockOrderItemId,jdbcType=BIGINT},
            #{itemDetail.locationId,jdbcType=BIGINT}, #{itemDetail.locationName,jdbcType=VARCHAR},
            #{itemDetail.productionDate,jdbcType=TIMESTAMP}, #{itemDetail.batchTime,jdbcType=TIMESTAMP},
            #{itemDetail.unitTotalCount,jdbcType=DECIMAL}, #{itemDetail.outStockUnitTotalCount,jdbcType=DECIMAL},
            #{itemDetail.remark,jdbcType=VARCHAR}, now(), now(),
            #{itemDetail.productSpecificationId,jdbcType=BIGINT}, #{itemDetail.ownerId,jdbcType=BIGINT},
            #{itemDetail.secOwnerId,jdbcType=BIGINT}, #{itemDetail.batchAttributeInfoNo,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch">
        insert into outstockorderitemdetail (Id, Org_Id, OutStockOrderItem_Id,
        Location_Id, LocationName, ProductionDate, BatchTime,
        UnitTotalCount, OutStockUnitTotalCount, Remark, CreateTime, LastUpdateTime,
        ProductSpecification_Id, Owner_Id, SecOwner_Id, BatchAttributeInfoNo
        )
        values
        <foreach collection="itemDetails" item="itemDetail" separator=",">
            (
            #{itemDetail.id,jdbcType=BIGINT}, #{itemDetail.orgId,jdbcType=INTEGER},
            #{itemDetail.outStockOrderItemId,jdbcType=BIGINT},
            #{itemDetail.locationId,jdbcType=BIGINT}, #{itemDetail.locationName,jdbcType=VARCHAR},
            #{itemDetail.productionDate,jdbcType=TIMESTAMP}, #{itemDetail.batchTime,jdbcType=TIMESTAMP},
            #{itemDetail.unitTotalCount,jdbcType=DECIMAL}, #{itemDetail.outStockUnitTotalCount,jdbcType=DECIMAL},
            #{itemDetail.remark,jdbcType=VARCHAR}, now(), now(),
            #{itemDetail.productSpecificationId,jdbcType=BIGINT}, #{itemDetail.ownerId,jdbcType=BIGINT},
            #{itemDetail.secOwnerId,jdbcType=BIGINT}, #{itemDetail.batchAttributeInfoNo,jdbcType=VARCHAR}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        Location_Id=VALUES(Location_Id),
        LocationName=VALUES(LocationName),
        ProductionDate=VALUES(ProductionDate),
        BatchTime=VALUES(BatchTime),
        UnitTotalCount=VALUES(UnitTotalCount),
        OutStockUnitTotalCount=VALUES(OutStockUnitTotalCount),
        ProductSpecification_Id=VALUES(ProductSpecification_Id),
        Owner_Id=VALUES(Owner_Id),
        SecOwner_Id=VALUES(SecOwner_Id),
        BatchAttributeInfoNo=VALUES(BatchAttributeInfoNo)
    </insert>

    <delete id="deleteByItemDetailIds">
        delete from outstockorderitemdetail
        where 1=1
        <if test="orgId != null">
            AND Org_Id = #{orgId,jdbcType=INTEGER}
        </if>
        and id in
        <foreach collection="delItemDetailIds" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </delete>

    <select id="findByItemIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from outstockorderitemdetail
        where
         OutStockOrderItem_Id in
        <foreach collection="itemIds" item="itemId" separator="," open="(" close=")">
            #{itemId,jdbcType=BIGINT}
        </foreach>
        <if test="orgId != null">
            and Org_Id = #{orgId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="listByOrderItemIds" resultType="java.lang.Long">
        select id
        from outstockorderitemdetail
        where OutStockOrderItem_Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <update id="updateBatchByPOList" parameterType="list">
        update outstockorderitemdetail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="UnitTotalCount =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.unitTotalCount != null ">
                        when id = #{po.id} then #{po.unitTotalCount}
                    </if>
                    <if test="po.unitTotalCount == null ">
                        when id = #{po.id} then outstockorderitemdetail.UnitTotalCount
                    </if>
                </foreach>
            </trim>
            <trim prefix="OutStockUnitTotalCount =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.outStockUnitTotalCount != null ">
                        when id = #{po.id} then #{po.outStockUnitTotalCount}
                    </if>
                    <if test="po.outStockUnitTotalCount == null ">
                        when id = #{po.id} then outstockorderitemdetail.OutStockUnitTotalCount
                    </if>
                </foreach>
            </trim>
            Lastupdatetime = now()
        </trim>
        where id in
        <foreach collection="list" item="po" index="index" open="(" close=")" separator=",">
            #{po.id}
        </foreach>
    </update>

    <delete id="updateByItemDetailIdsToZero">
        update outstockorderitemdetail
        set UnitTotalCount = 0
        where 1=1
        <if test="orgId != null">
            AND Org_Id = #{orgId,jdbcType=INTEGER}
        </if>
        and id in
        <foreach collection="delItemDetailIds" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </delete>

</mapper>