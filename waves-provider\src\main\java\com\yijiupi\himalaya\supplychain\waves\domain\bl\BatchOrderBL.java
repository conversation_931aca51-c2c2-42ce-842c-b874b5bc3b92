package com.yijiupi.himalaya.supplychain.waves.domain.bl;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.ObjectUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.common.constant.RedisConstant;
import com.yijiupi.himalaya.distributedlock.annotation.BatchDistributeLock;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryTransferCheckDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryManageService;
import com.yijiupi.himalaya.supplychain.constants.WarehouseConfigConstants;
import com.yijiupi.himalaya.supplychain.constants.WavesStrategyConstants;
import com.yijiupi.himalaya.supplychain.dto.OrderProcessRuleConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.enums.InventoryChangeTypeEnum;
import com.yijiupi.himalaya.supplychain.enums.ProcessOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.enums.ProcessRuleOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderItemDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.StoreTransferEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.IStoreTransferOrderService;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.service.IInventoryOrderBizService;
import com.yijiupi.himalaya.supplychain.service.IOrderProcessRuleConfigService;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.util.SecOwnerIdComparator;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IOrderLocationPalletService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchDeleteHelper;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchDeleteVerifyBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.BatchFinishedBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.BatchTaskChangeNotifyBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.dto.BatchCompleteEvent;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderpallet.OrderLocationPalletBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation.OutStockLocationHelper;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outlocation.RecommendOutLocationBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder.OutStockOrderItemQueryBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.outstockorder.OutStockOrderStateBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.packageorder.PackageOrderItemModBL;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchTaskConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.BatchTaskItemConverter;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.CancelDPSTaskConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.WaveOrderConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.*;
import com.yijiupi.himalaya.supplychain.waves.domain.model.SaaSOrderSelectionEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.model.SaaSPickingGroupStrategyEnum;
import com.yijiupi.himalaya.supplychain.waves.domain.po.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.*;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.*;
import com.yijiupi.himalaya.supplychain.waves.dto.orderpallet.OrderLocationPalletQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.*;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskCancelDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.sowtask.SowTaskCancelItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import com.yijiupi.himalaya.supplychain.waves.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.wcs.dto.task.DPSCancelPickTaskBatchCreateDTO;
import com.yijiupi.himalaya.supplychain.wcs.service.task.IDPSTaskService;

/**
 * 波次
 *
 * <AUTHOR> 2018/3/15
 */
@Service
public class BatchOrderBL {
    private static final Logger LOG = LoggerFactory.getLogger(BatchOrderBL.class);
    @Autowired
    private BatchMapper batchMapper;
    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchTaskItemMapper batchTaskItemMapper;
    @Autowired
    private OutStockOrderItemMapper outStockOrderItemMapper;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Autowired
    private OrderTraceBL orderTraceBL;
    @Autowired
    private SowTaskMapper sowTaskMapper;
    @Autowired
    private SowTaskItemMapper sowTaskItemMapper;
    @Autowired
    private SowOrderMapper sowOrderMapper;
    @Autowired
    private PackageOrderItemBL packageOrderItemBL;
    @Autowired
    private OutStockOrderBL outStockOrderBL;
    @Autowired
    private OutStockOrderItemChangeRecordMapper outStockOrderItemChangeRecordMapper;
    @Reference
    private IStoreTransferOrderService iStoreTransferOrderService;
    @Autowired
    private SowManagerBL sowManagerBL;
    @Autowired
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;
    @Autowired
    private OrderItemTaskInfoDetailMapper orderItemTaskInfoDetailMapper;
    @Autowired
    private OrderCenterBL orderCenterBL;
    @Autowired
    private BatchOrderTaskBL batchOrderTaskBL;
    @Reference
    private IDPSTaskService idpsTaskService;
    @Autowired
    private RecommendOutLocationBL recommendOutLocationBL;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private BatchFinishedBL batchFinishedBL;
    @Resource
    private PackageOrderItemModBL packageOrderItemModBL;
    @Resource
    private BatchDeleteHelper batchDeleteHelper;
    @Resource
    private BatchTaskChangeNotifyBL batchTaskChangeNotifyBL;

    @Reference
    private IBatchInventoryManageService iBatchInventoryManageService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Reference
    private ILocationService iLocationService;
    @Reference
    private IOrderProcessRuleConfigService iOrderProcessRuleConfigService;
    @Reference
    private IVariableValueService iVariableValueService;
    @Reference
    private IInventoryOrderBizService iInventoryOrderBizService;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Resource
    private OutStockOrderStateBL outStockOrderStateBL;
    @Resource
    private OutStockOrderItemQueryBL outStockOrderItemQueryBL;
    @Resource
    private BatchDeleteVerifyBL batchDeleteVerifyBL;
    @Reference
    private IOrderLocationPalletService iOrderLocationPalletService;
    @Autowired
    private OrderLocationPalletBL orderLocationPalletBL;

    /**
     * 查询波次列表
     *
     * @param batchQueryDTO
     * @return
     */
    public PageList<BatchDTO> findBatchOrderList(BatchQueryDTO batchQueryDTO) {
        // LOG.info("batchQueryDTO:{}", JSON.toJSONString(batchQueryDTO));
        String refOrderNo = batchQueryDTO.getRefOrderNo();
        PageList<BatchDTO> batchOrderDTOPageList = new PageList<>();

        if (StringUtils.isNotEmpty(refOrderNo)) {
            String batchNo = batchMapper.findBatchNoByOrderId(batchQueryDTO.getWarehouseId(), refOrderNo);
            if (StringUtils.isEmpty(batchNo)) {
                return new PageResult<BatchDTO>().toPageList();
            }
            batchQueryDTO.setBatchNo(batchNo);
        }
        PageResult<BatchPO> batchPOS =
            batchMapper.findList(batchQueryDTO, batchQueryDTO.getCurrentPage(), batchQueryDTO.getPageSize());
        PageList<BatchPO> batchOrderPOPageList = batchPOS.toPageList();
        List<BatchDTO> batchDTOS = WaveOrderConvertor.batchOrderPOS2BatchOrderDTOS(batchOrderPOPageList.getDataList());
        batchDTOS.forEach(dto -> {
            // 查询该波次关联的订单列表
            PageResult<OutStockOrderDTO> pageResult = outStockOrderMapper.listOrderByBatchNo(dto.getBatchNo(), 1, 1000);
            List<OutStockOrderDTO> list =
                pageResult.getResult().stream().filter(x -> x.getOrderType() == 0).collect(Collectors.toList());
            dto.setIncludeJpOrder(list != null && list.size() > 0 ? (byte)1 : (byte)0);
        });
        batchOrderDTOPageList.setDataList(batchDTOS);
        batchOrderDTOPageList.setPager(batchOrderPOPageList.getPager());

        return batchOrderDTOPageList;
    }

    /**
     * 根据订单号，查询订单存放的周转区货位
     *
     * @param orderIds
     * @return
     */
    public List<OutStockOrderLocationDTO> findBatchOrderLoctionList(List<Long> orderIds) {
        List<OutStockOrderLocationDTO> batchOrderLoctionList = batchMapper.findBatchOrderLoctionList(orderIds);
        if (CollectionUtils.isNotEmpty(batchOrderLoctionList)) {
            List<OutStockOrderLocationDTO> batchOrderLoctionListTmp = new ArrayList<>();
            for (OutStockOrderLocationDTO dto : batchOrderLoctionList) {
                if (dto.getOutStockOrderId() == null || dto.getLocationId() == null
                    || StringUtils.isEmpty(dto.getLocationName())) {
                    continue;
                }
                if (!batchOrderLoctionListTmp.stream()
                    .anyMatch(p -> p.getLocationId().equals(dto.getLocationId())
                        && p.getLocationName().equals(dto.getLocationName())
                        && p.getOutStockOrderId().equals(dto.getOutStockOrderId()))) {
                    batchOrderLoctionListTmp.add(dto);
                }
            }
            batchOrderLoctionList = batchOrderLoctionListTmp;
        }
        return batchOrderLoctionList;
    }

    /**
     * 根据波次编号删除波次(及波次任务,波次任务详情,释放订单)
     */
    // , propagation = Propagation.NESTED
    @BatchDistributeLock(conditions = "#deleteBatchDTO.batchNoDTOList", property = "batchNo", expireMills = 6000,
        sleepMills = 6000, key = RedisConstant.SUP_F + "batchTaskComplete:",
        lockType = BatchDistributeLock.LockType.WAITLOCK)
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteBatchOrder(DeleteBatchDTO deleteBatchDTO) {
        LOG.info("波次删除: {}", JSON.toJSONString(deleteBatchDTO, SerializerFeature.WriteMapNullValue));
        List<String> batchNoList = deleteBatchDTO.getBatchNoList();
        String operateUser = deleteBatchDTO.getOperateUser();
        // 查询波次
        List<BatchPO> batchPOList = batchMapper.listByBatchNos(batchNoList);
        // 校验波次能否删除
        batchDeleteVerifyBL.verifyCanDelete(batchPOList, deleteBatchDTO);
        // 需要删除的波次号集合
        List<String> delBatchNoList = batchPOList.stream().map(BatchPO::getBatchNo).collect(Collectors.toList());
        // 1、已拣货的订单项需要移库
        Map<String, String> exceptionBatchNoMap =
            pickBatchTaskItemTransfer(delBatchNoList, operateUser, deleteBatchDTO.getIgnoreBatchInventory());
        List<String> exceptionBatchNoList = org.springframework.util.CollectionUtils.isEmpty(exceptionBatchNoMap)
            ? Collections.emptyList() : new ArrayList<>(exceptionBatchNoMap.keySet());
        // 排除调移库失败的波次号
        if (CollectionUtils.isNotEmpty(exceptionBatchNoList)) {
            delBatchNoList.removeAll(exceptionBatchNoList);
        }
        if (CollectionUtils.isEmpty(delBatchNoList)) {
            if (!org.springframework.util.CollectionUtils.isEmpty(exceptionBatchNoMap)) {
                throw new BusinessValidateException("波次删除时移库失败：" + JSON.toJSONString(exceptionBatchNoMap.values()));
            } else {
                throw new BusinessValidateException("波次删除时移库失败：" + JSON.toJSONString(exceptionBatchNoList));
            }
        }
        // 过滤出最终删除的波次信息
        batchPOList =
            batchPOList.stream().filter(p -> delBatchNoList.contains(p.getBatchNo())).collect(Collectors.toList());

        // 查询拣货任务
        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findTaskByBatchNo(delBatchNoList);
        // 3、删除播种任务
        deleteSowTaskByBatch(delBatchNoList, operateUser);
        // 还原订单状态
        batchDeleteHelper.restoreOrderState(batchPOList, deleteBatchDTO);
        // 4、删除波次、拣货任务、拣货任务详情
        deleteBatchTask(batchPOList, batchTaskPOList, delBatchNoList, deleteBatchDTO);
        // 5、删除订单货位托盘关系数据
        deleteOrderLocationPallet(batchTaskPOList);
        batchTaskChangeNotifyBL.notifyBatchDelete(batchTaskPOList);
    }

    /**
     * 已完成的拣货任务项需要移库
     */
    private Map<String, String> pickBatchTaskItemTransfer(List<String> delBatchNoList, String operateUser,
        Boolean ignoreBatchInventory) {
        if (CollectionUtils.isEmpty(delBatchNoList)) {
            return null;
        }
        // 查询波次下已完成的拣货任务项
        List<BatchTaskItemPO> pickBatchTaskItemPOS =
            batchTaskItemMapper.findPickBatchTaskItemsByBatchNos(delBatchNoList);
        if (CollectionUtils.isEmpty(pickBatchTaskItemPOS)) {
            return null;
        }

        // 记录移库失败的波次号
        List<String> exceptionBatchNoList = new ArrayList<>();
        Map<String, String> exceptionBatchNoMap = new HashMap<>();
        // 1、查找拣货任务项关联的订单项
        List<String> batchTaskItemIds = pickBatchTaskItemPOS.stream().map(p -> p.getId()).collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByBatchTaskItemIds(batchTaskItemIds);

        // 2、获取订单项详情的出库位
        Map<Long, OutStockOrderItemPO> orderItemMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderItemTaskInfoPOList)) {
            List<Long> refOrderItemIds = orderItemTaskInfoPOList.stream().map(p -> p.getRefOrderItemId()).distinct()
                .collect(Collectors.toList());
            List<OutStockOrderItemPO> outStockOrderItemPOList =
                outStockOrderItemQueryBL.findOutStockOrderItemList(refOrderItemIds);
            if (CollectionUtils.isNotEmpty(outStockOrderItemPOList)) {
                outStockOrderItemPOList.forEach(p -> {
                    orderItemMap.put(p.getId(), p);
                });
            }
        }

        // 3、获取播种任务对应的集货位
        Map<Long, SowTaskPO> sowTaskMap = new HashMap<>();
        Map<Long, SowTaskItemPO> sowTaskItemMap = new HashMap<>();
        List<Long> sowTaskIds = pickBatchTaskItemPOS.stream().filter(p -> p.getSowTaskId() != null)
            .map(p -> p.getSowTaskId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sowTaskIds)) {
            // 播种任务
            List<SowTaskPO> sowTaskPOList = sowTaskMapper.findAllSowTaskByIds(null, sowTaskIds);
            if (CollectionUtils.isNotEmpty(sowTaskPOList)) {
                sowTaskPOList.forEach(p -> {
                    sowTaskMap.put(p.getId(), p);
                });
            }
            // 播种任务项
            List<SowTaskItemPO> sowTaskItemPOList = sowTaskItemMapper.findBySowTaskIds(sowTaskIds);
            if (CollectionUtils.isNotEmpty(sowTaskItemPOList)) {
                sowTaskItemPOList.forEach(p -> {
                    sowTaskItemMap.put(p.getId(), p);
                });
            }
        }

        // 4、按波次分组进行移库操作
        Map<String, List<BatchTaskItemPO>> pickBatchTaskItemMap =
            pickBatchTaskItemPOS.stream().collect(Collectors.groupingBy(p -> p.getBatchNo()));
        pickBatchTaskItemMap.forEach((batchNo, batchTaskItemList) -> {
            // 记录移库
            List<PickUpDTO> pickUpDTOList = new ArrayList<>();
            pickBatchTaskItemPOS.forEach(batchTaskItemPO -> {

                // 新版本
                if (CollectionUtils.isNotEmpty(orderItemTaskInfoPOList)) {
                    // 当前拣货任务项对应的订单项
                    List<OrderItemTaskInfoPO> filterList = orderItemTaskInfoPOList.stream()
                        .filter(p -> Objects.equals(p.getBatchTaskItemId(), batchTaskItemPO.getId()))
                        .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(filterList)) {
                        LOG.info("[波次删除]{}, 拣货任务项对应的订单项为空", batchNo);
                        return;
                    }
                    filterList.forEach(p -> {
                        // 来源货位（集货位/出库位）
                        Long fromLocationId = getFromLocationId(batchTaskItemPO, orderItemMap, sowTaskMap,
                            sowTaskItemMap, batchNo, p.getRefOrderItemId());
                        // 关联的明细
                        List<OrderItemTaskInfoDetailPO> detailPOList = p.getDetailList();
                        if (CollectionUtils.isNotEmpty(detailPOList)) {
                            detailPOList.forEach(detail -> {
                                // 记录移库
                                if (fromLocationId == null
                                    || detail.getUnitTotalCount().compareTo(BigDecimal.ZERO) == 0) {
                                    LOG.info("[波次删除]{}, 不满足移库！fromLocationId: {}, moveCount: {}", batchNo,
                                        fromLocationId, detail.getUnitTotalCount());
                                    return;
                                }
                                recordPickUpDTOList(pickUpDTOList, batchTaskItemPO, fromLocationId,
                                    detail.getUnitTotalCount(), detail.getSecOwnerId());
                            });
                            // 老数据
                        } else {
                            // 移库数量
                            BigDecimal moveCount = p.getOverSortCount().add(p.getMoveCount());
                            // 记录移库
                            if (fromLocationId == null || moveCount.compareTo(BigDecimal.ZERO) == 0) {
                                LOG.info("[波次删除]{}, 不满足移库！fromLocationId: {}, moveCount: {}", batchNo, fromLocationId,
                                    moveCount);
                                return;
                            }
                            recordPickUpDTOList(pickUpDTOList, batchTaskItemPO, fromLocationId, moveCount,
                                batchTaskItemPO.getSecOwnerId());
                        }
                    });
                    // 兼容老数据
                } else {
                    // 来源货位（集货位/出库位）
                    Long fromLocationId = batchTaskItemPO.getToLocationId();
                    // 生成播种任务，如果待播种则取集货位
                    if (batchTaskItemPO.getSowTaskId() != null) {
                        SowTaskPO sowTaskPO = sowTaskMap.get(batchTaskItemPO.getSowTaskId());
                        if (sowTaskPO == null) {
                            LOG.info("[波次删除old]{}, 找不到播种任务: {}", batchNo, batchTaskItemPO.getSowTaskId());
                            return;
                        }
                        if (Objects.equals(sowTaskPO.getState(), SowTaskStateEnum.待播种.getType())) {
                            // 集货位
                            fromLocationId = sowTaskPO.getLocationId();
                        }
                    }
                    // 记录移库
                    if (fromLocationId == null || batchTaskItemPO.getOverSortCount().compareTo(BigDecimal.ZERO) == 0) {
                        LOG.info("[波次删除old]{}, 不满足移库！fromLocationId: {}", batchNo, fromLocationId);
                        return;
                    }
                    recordPickUpDTOList(pickUpDTOList, batchTaskItemPO, fromLocationId,
                        batchTaskItemPO.getOverSortCount(), batchTaskItemPO.getSecOwnerId());
                }
            });

            // 移库
            if (CollectionUtils.isNotEmpty(pickUpDTOList)) {
                try {
                    LOG.info("波次删除移库：{}, batchNo: {}", JSON.toJSONString(pickUpDTOList), batchNo);
                    PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
                    pickUpChangeRecordDTO.setCityId(batchTaskItemList.get(0).getOrgId());
                    pickUpChangeRecordDTO.setOrderId(batchTaskItemList.get(0).getBatchId());
                    pickUpChangeRecordDTO.setOrderNo(batchTaskItemList.get(0).getBatchNo());
                    pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.波次删除.name());
                    pickUpChangeRecordDTO.setCreateUser(operateUser);
                    BatchInventoryTransferCheckDTO batchInventoryTransferCheckDTO =
                        new BatchInventoryTransferCheckDTO();
                    batchInventoryTransferCheckDTO.setIgnoreProductionDate(true);
                    if (Objects.equals(ignoreBatchInventory, true)) {
                        // 忽略库存
                        batchInventoryTransferCheckDTO.setIgnoreHasNotEnoughStore(true);
                    }
                    iBatchInventoryManageService.batchInventoryTransfer(pickUpDTOList, pickUpChangeRecordDTO,
                        batchInventoryTransferCheckDTO);
                } catch (Exception e) {
                    LOG.info("波次删除移库异常：" + batchNo, e);
                    exceptionBatchNoList.add(batchNo);
                    exceptionBatchNoMap.put(batchNo, e.getMessage());
                }
            }
        });
        return exceptionBatchNoMap;
    }

    private void recordPickUpDTOList(List<PickUpDTO> pickUpDTOList, BatchTaskItemPO batchTaskItemPO,
        Long fromLocationId, BigDecimal pickCount, Long SecOwnerId) {
        PickUpDTO pickUpDTO = new PickUpDTO();
        pickUpDTO.setProductSkuId(batchTaskItemPO.getSkuId());
        pickUpDTO.setWarehouseId(batchTaskItemPO.getWarehouseId());
        pickUpDTO.setFromChannel(batchTaskItemPO.getChannel().intValue());
        pickUpDTO.setFromSource(batchTaskItemPO.getSource().intValue());
        pickUpDTO.setToChannel(batchTaskItemPO.getChannel().intValue());
        pickUpDTO.setToSource(batchTaskItemPO.getSource().intValue());
        pickUpDTO.setFromLocationId(fromLocationId);
        pickUpDTO.setLocationId(batchTaskItemPO.getLocationId());
        pickUpDTO.setCount(pickCount);
        pickUpDTO.setSecOwnerId(SecOwnerId);
        pickUpDTOList.add(pickUpDTO);
    }

    private Long getFromLocationId(BatchTaskItemPO batchTaskItemPO, Map<Long, OutStockOrderItemPO> orderItemMap,
        Map<Long, SowTaskPO> sowTaskMap, Map<Long, SowTaskItemPO> sowTaskItemMap, String batchNo, Long refOrderItemId) {
        Long fromLocationId = null;
        // 未生成播种任务，直接取拣货任务的出库位
        if (batchTaskItemPO.getSowTaskId() == null) {
            fromLocationId = batchTaskItemPO.getToLocationId();
            // 生成播种任务，如果待播种则取集货位，已播种取订单项的出库位
        } else {
            OutStockOrderItemPO orderItemPO = orderItemMap.get(refOrderItemId);
            if (orderItemPO == null) {
                LOG.info("[波次删除]{}, 找不到订单项: {}", batchNo, refOrderItemId);
                return null;
            }
            // 根据订单项找到播种任务项
            SowTaskItemPO sowTaskItemPO = sowTaskItemMap.get(orderItemPO.getSowTaskItemId());
            if (sowTaskItemPO == null) {
                LOG.info("[波次删除]{}, 找不到播种任务项: {}", batchNo, orderItemPO.getSowTaskItemId());
                return null;
            }
            if (Objects.equals(sowTaskItemPO.getState(), SowTaskStateEnum.待播种.getType())) {
                SowTaskPO sowTaskPO = sowTaskMap.get(sowTaskItemPO.getSowTaskId());
                if (sowTaskPO == null) {
                    LOG.info("[波次删除]{}, 找不到播种任务: {}", batchNo, sowTaskItemPO.getSowTaskId());
                    return null;
                }
                // 集货位
                fromLocationId = sowTaskPO.getLocationId();
            } else {
                // 订单项的出库位
                fromLocationId = orderItemPO.getLocationId();
            }
        }
        return fromLocationId;
    }

    /**
     * 删除波次任务
     */
    private void deleteBatchTask(List<BatchPO> batchPOList, List<BatchTaskPO> batchTaskPOList,
        List<String> lstNoProcessBatchNos, DeleteBatchDTO deleteBatchDTO) {
        String operateUser = deleteBatchDTO.getOperateUser();
        // 1、删除波次任务详情
        List<String> batchTaskId = batchTaskPOList.stream().map(BatchTaskPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(batchTaskId)) {
            batchTaskItemMapper.deleteByBatchTaskId(batchTaskId);
            // 2、删除波次任务
            // -添加操作记录（删除拣货任务）
            orderTraceBL.deleteBatchTaskTrace(batchTaskPOList, operateUser);
            batchTaskMapper.deleteByBatchNo(lstNoProcessBatchNos);
        }
        // 3、删除波次
        // -添加操作记录（波次删除）
        orderTraceBL.deleteBatchTrace(batchPOList, operateUser);
        batchMapper.deleteByBatchNo(lstNoProcessBatchNos);
        // 4、删除订单项和拣货任务项关联
        if (CollectionUtils.isNotEmpty(batchTaskId)) {
            List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
                orderItemTaskInfoMapper.listTaskInfoAndDetailByBatchTaskIds(batchTaskId);
            if (CollectionUtils.isNotEmpty(orderItemTaskInfoPOList)) {
                Lists.partition(orderItemTaskInfoPOList, 500).forEach(lstPO -> {
                    List<Long> taskInfoIds =
                        lstPO.stream().map(OrderItemTaskInfoPO::getId).collect(Collectors.toList());
                    orderItemTaskInfoMapper.deleteByIds(taskInfoIds);
                    // 删除明细
                    List<Long> taskInfoDetailIds = lstPO.stream().map(OrderItemTaskInfoPO::getDetailList)
                        .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
                        .map(OrderItemTaskInfoDetailPO::getId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(taskInfoDetailIds)) {
                        orderItemTaskInfoDetailMapper.deleteByIds(taskInfoDetailIds);
                    }
                });
            }
        }

        LOG.info("删除波次待处理拣货任务, {}", JSON.toJSONString(batchTaskPOList));
        DPSCancelPickTaskBatchCreateDTO createDTO =
            CancelDPSTaskConvertor.convert(batchTaskPOList, deleteBatchDTO.getOperateUserId());
        LOG.info("删除机器人拣货任务, {}", JSON.toJSONString(createDTO));
        if (CollectionUtils.isNotEmpty(createDTO.getCancelList())) {
            // 这里要过滤机器人拣货任务才处理
            idpsTaskService.batchCreateCancelPickTask(createDTO);
        }
    }

    /**
     * 删除播种任务
     */
    private void deleteSowTaskByBatch(List<String> lstNoProcessBatchNos, String operateUser) {
        // 查找播种任务及关联信息
        List<SowTaskPO> sowTaskPOS = sowTaskMapper.findSowTaskByBatchNos(lstNoProcessBatchNos);
        if (CollectionUtils.isNotEmpty(sowTaskPOS)) {
            // // 1、验证播种任务是否开始播种
            // Map<String, List<SowTaskPO>> sowTaskMap =
            // sowTaskPOS.stream().collect(Collectors.groupingBy(p->p.getBatchNo()));
            // if (null != sowTaskMap) {
            // List<String> msg = new ArrayList<>();
            // sowTaskMap.forEach((batchNo, sowTaskList)->{
            // if (sowTaskList.stream().anyMatch(p-> Objects.equals(p.getState(), SowTaskStateEnum.播种中.getType()) ||
            // Objects.equals(p.getState(), SowTaskStateEnum.已播种.getType()))) {
            // msg.add(batchNo);
            // }
            // });
            // if (CollectionUtils.isNotEmpty(msg)) {
            // throw new BusinessValidateException(String.format("删除失败，以下波次已经开始播种，不能删除%s",
            // Arrays.toString(msg.toArray())));
            // }
            // }

            // 2、删除播种任务
            sowTaskPOS.stream().collect(
                Collectors.groupingBy(SowTaskPO::getOrgId, Collectors.mapping(SowTaskPO::getId, Collectors.toList())))
                .forEach((orgId, sowTaskIds) -> {
                    sowTaskMapper.deleteBySowTaskIds(sowTaskIds, orgId);
                    sowTaskItemMapper.deleteBySowTaskIds(sowTaskIds, orgId);
                });

            // 添加操作日志(删除播种任务)
            orderTraceBL.deleteSowTaskTrace(sowTaskPOS, operateUser);
        }

        // 播种任务订单关联表删除
        List<SowOrderPO> sowOrderPOS = sowOrderMapper.findByBatchNos(lstNoProcessBatchNos);
        if (CollectionUtils.isNotEmpty(sowOrderPOS)) {
            List<Long> sowOrderIds =
                sowOrderPOS.stream().map(SowOrderPO::getId).distinct().collect(Collectors.toList());
            sowOrderMapper.deleteByIds(sowOrderIds, sowOrderPOS.get(0).getOrgId());
        }
    }

    /**
     * 根据波次编号，查找所有标记缺货的项
     *
     * @param batchNos
     * @return
     */
    public List<BatchTaskItemLackDTO> listLackItemByBatchNos(List<String> batchNos) {
        List<BatchTaskItemLackDTO> batchTaskItemLackDTOS = batchMapper.listLackItemByBatchNos(batchNos);
        return batchTaskItemLackDTOS;
    }

    /**
     * 根据波次编号查询波次详情
     *
     * @param batchNo
     * @return
     */
    public BatchDTO selectBatchByBatchNo(Integer orgId, String batchNo) {
        BatchDTO batchDTO = new BatchDTO();
        BatchPO batchPO = batchMapper.selectBatchByBatchNo(orgId, batchNo);
        if (batchPO != null) {
            BeanUtils.copyProperties(batchPO, batchDTO);
        }
        return batchDTO;
    }

    /**
     * 根据拣货任务编号查询波次详情
     *
     * @param taskNo
     * @return
     */
    public BatchDTO selectBatchByTaskNo(Integer orgId, String taskNo) {
        BatchDTO batchDTO = new BatchDTO();
        BatchPO batchPO = batchMapper.selectBatchByTaskNo(orgId, taskNo);
        if (batchPO != null) {
            BeanUtils.copyProperties(batchPO, batchDTO);
        }
        return batchDTO;
    }

    /**
     * 分单拣货(整单完成需保存打印数据，整单未完成通知PDA将拆分出去的单合过来)
     */
    public Map<String, List<BatchTaskDTO>> separateOrderPicking(String batchTaskId, Set<String> updateBatchTaskItemIds,
        List<String> lstOrderNos, String userName, Integer warehouseId, Integer orgId) {
        List<String> orderNos = new ArrayList<>();
        List<String> undoneBatchTaskIds = new ArrayList<>();
        Map<String, List<String>> undoneBatchTaskMap = new HashMap<>();
        Map<String, List<BatchTaskDTO>> msgMap = new HashMap<>();

        // 查找订单号涉及的拣货任务(未完成拣货)
        List<BatchTaskItemDTO> undoneBatchTaskItems =
            batchTaskItemMapper.findUndoneItemsByOrderNos(lstOrderNos, orgId, warehouseId);
        // 过滤掉准备提交的项
        undoneBatchTaskItems = undoneBatchTaskItems.stream()
            .filter(batchTaskItem -> !updateBatchTaskItemIds.contains(batchTaskItem.getId()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(undoneBatchTaskItems)) {
            for (String orderNo : lstOrderNos) {
                // 匹配订单号相同的所有拣货任务项
                List<BatchTaskItemDTO> batchTaskItemDTOS =
                    undoneBatchTaskItems.stream().filter(batchTaskItem -> batchTaskItem.getRefOrderNo().equals(orderNo))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(batchTaskItemDTOS)) {
                    // 检查此次拣货任务中该订单是否已全部完成，若完成，通知去找另一个拣货员合单
                    if (batchTaskItemDTOS.stream()
                        .noneMatch(batchTaskItem -> batchTaskItem.getBatchTaskId().equals(batchTaskId))) {
                        List<String> batchTaskIds = batchTaskItemDTOS.stream().map(BatchTaskItemDTO::getBatchTaskId)
                            .collect(Collectors.toList());
                        undoneBatchTaskIds.addAll(batchTaskIds);
                        undoneBatchTaskMap.put(orderNo, batchTaskIds);
                    }
                } else {
                    orderNos.add(orderNo);
                }
            }
        } else {
            orderNos = lstOrderNos;
        }

        // 查找需要通知的拣货信息
        if (CollectionUtils.isNotEmpty(undoneBatchTaskIds)) {
            List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findTasksByBatchTaskId(undoneBatchTaskIds);
            List<BatchTaskDTO> batchTaskDTOS = BatchTaskConvertor.convertToBatchTaskDTO(batchTaskPOS);
            List<BatchTaskItemPO> batchTaskItemPOS =
                batchTaskItemMapper.listBatchTaskItemByTaskId(undoneBatchTaskIds, orgId);
            for (Map.Entry<String, List<String>> entry : undoneBatchTaskMap.entrySet()) {
                String orderNo = entry.getKey();
                List<String> batchTaskIds = entry.getValue();
                List<BatchTaskDTO> taskDTOS = batchTaskDTOS.stream()
                    .filter(batchTask -> batchTaskIds.contains(batchTask.getId())).collect(Collectors.toList());
                taskDTOS.forEach(task -> {
                    List<BatchTaskItemPO> itemPOS = batchTaskItemPOS.stream()
                        .filter(batchTaskItem -> task.getId().equals(batchTaskItem.getBatchTaskId())
                            && orderNo.equals(batchTaskItem.getRefOrderNo()))
                        .collect(Collectors.toList());
                    task.setPackageAmount(itemPOS.stream().map(BatchTaskItemPO::getPackageCount).reduce(BigDecimal.ZERO,
                        BigDecimal::add));
                    task.setUnitAmount(
                        itemPOS.stream().map(BatchTaskItemPO::getUnitCount).reduce(BigDecimal.ZERO, BigDecimal::add));
                });
                msgMap.put(orderNo, taskDTOS);
            }
        }

        // 分单拣货自动打印数据保存
        // if (CollectionUtils.isNotEmpty(orderNos) && StringUtils.isNotEmpty(printer)) {
        // OrderPrintInfoDTO orderPrintInfoDTO = new OrderPrintInfoDTO();
        // orderPrintInfoDTO.setWarehouseId(warehouseId);
        // orderPrintInfoDTO.setOrgId(orgId);
        // orderPrintInfoDTO.setOperateUser(userName);
        // orderPrintInfoDTO.setOrderNos(orderNos);
        // orderPrintInfoDTO.setPrinter(printer);
        // batchOrderInfoBL.pushPrintingOrderMsg(orderPrintInfoDTO);
        // }
        return msgMap;
    }

    /**
     * 波次出库
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchOutStock(BatchOutStockDTO batchOutStockDTO) {
        List<String> batchNos = batchOutStockDTO.getBatchNos();
        Integer orgId = batchOutStockDTO.getOrgId();
        // 校验波次状态
        List<BatchPO> batchPOS = batchMapper.findBatchByNos(batchNos, orgId);

        String errBatchNOS =
            batchPOS.stream().filter(batchPO -> batchPO.getState() != BatchStateEnum.PICKINGEND.getType().byteValue())
                .map(BatchPO::getBatchNo).distinct().collect(Collectors.joining(","));
        if (StringUtils.isNotEmpty(errBatchNOS)) {
            throw new BusinessValidateException("以下波次未拣货完成不允许出库：" + errBatchNOS);
        }

        // 组装批量发货数据
        List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.findByBatchNos(batchNos, orgId);
        if (CollectionUtils.isEmpty(outStockOrderPOS)) {
            throw new BusinessException("找不到相应订单信息,请检查波次下的订单");
        }
        String outStockBatchNos =
            outStockOrderPOS.stream().filter(order -> order.getState() == OutStockOrderStateEnum.已出库.getType())
                .flatMap(order -> order.getItems().stream()).map(OutStockOrderItemPO::getBatchno).distinct()
                .collect(Collectors.joining(","));
        if (StringUtils.isNotEmpty(outStockBatchNos)) {
            // batchMapper.updateBatchStateByNos(outStockBatchNos, BatchStateEnum.ALREADYOUTOFSTORE.getType());
            // List<BatchPO> batchPOList = batchPOS.stream().filter(batchPO ->
            // outStockBatchNos.contains(batchPO.getBatchNo())).collect(Collectors.toList());
            // orderTraceBL.updateBatchTrace(batchPOList, OrderTraceDescriptionEnum.波次完成出库.name(),
            // batchOutStockDTO.getOperateUser());
            throw new BusinessValidateException("以下波次的订单已出库，请刷新查看:" + outStockBatchNos);
        }

        // 出库订单、库存处理
        directOutStock(outStockOrderPOS, batchOutStockDTO);
    }

    /**
     * 根据单号更新波次状态
     *
     * @param batchUpdateDTO
     */
    public void updateBatchStateByOrderNos(BatchUpdateDTO batchUpdateDTO) {
        List<String> orderNos = batchUpdateDTO.getOrderNos();
        List<BatchPO> batchPOS =
            batchMapper.findBatchByOrderNos(orderNos, batchUpdateDTO.getOrgId(), batchUpdateDTO.getWarehouseId());

        Integer state = batchUpdateDTO.getBatchState();
        List<BatchPO> updateBatchPOS =
            batchPOS.stream().filter(batchPO -> batchPO.getState() != state.byteValue()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateBatchPOS)) {
            return;
        }

        List<String> batchNos = updateBatchPOS.stream().map(BatchPO::getBatchNo).collect(Collectors.toList());

        List<OutStockOrderPO> outStockOrderPOS =
            outStockOrderMapper.findByBatchNos(batchNos, batchUpdateDTO.getOrgId());

        List<String> noUpdateBatchNos = outStockOrderPOS.stream()
            .filter(order -> !orderNos.contains(order.getReforderno()) && !state.equals(order.getState()))
            .map(OutStockOrderPO::getBatchno).distinct().collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(noUpdateBatchNos)) {
            batchNos.removeAll(noUpdateBatchNos);
        }

        if (CollectionUtils.isEmpty(batchNos)) {
            return;
        } else {
            updateBatchPOS = updateBatchPOS.stream().filter(batchPO -> batchNos.contains(batchPO.getBatchNo()))
                .collect(Collectors.toList());
        }

        batchMapper.updateBatchStateByNos(batchNos, state, batchUpdateDTO.getOrgId());

        String description = null;
        if (state.equals(BatchStateEnum.ALREADYOUTOFSTORE.getType())) {
            description = OrderTraceDescriptionEnum.波次完成出库.name();
        } else if (state.equals(BatchStateEnum.PICKINGEND.getType())) {
            description = OrderTraceDescriptionEnum.波次完成拣货.name();
        }
        orderTraceBL.updateBatchTrace(updateBatchPOS, description, batchUpdateDTO.getOperateUser());
    }

    /**
     * 根据仓库id获取波次打包订单信息
     *
     * @param orgId
     * @param warehouseId
     * @return
     */
    public List<BatchOrderDTO> listBatchOrderByWarehouse(Integer orgId, Integer warehouseId) {
        List<BatchOrderDTO> batchOrderDTOS = sowTaskMapper.listBatchOrderByWarehouse(orgId, warehouseId);

        if (CollectionUtils.isEmpty(batchOrderDTOS)) {
            return batchOrderDTOS;
        }

        // 过滤已播种完但波次未完成的数据
        List<BatchOrderDTO> batchOrderResult = new ArrayList<>();
        Map<String, List<BatchOrderDTO>> batchOrderMap =
            batchOrderDTOS.stream().collect(Collectors.groupingBy(BatchOrderDTO::getBatchNo));
        batchOrderMap.forEach((batchNo, batchOrders) -> {
            List<BatchOrderDTO> filterBatchOrders =
                batchOrders.stream().filter(order -> order.getSowTaskState() != SowTaskStateEnum.已播种.getType())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterBatchOrders)) {
                batchOrderResult.addAll(batchOrders);
            }
        });

        // 根据订单编号查询拣货任务
        if (CollectionUtils.isEmpty(batchOrderResult)) {
            return batchOrderResult;
        }
        List<String> sowTaskNos =
            batchOrderResult.stream().map(BatchOrderDTO::getSowTaskNo).collect(Collectors.toList());
        List<String> refOrderNos =
            batchOrderResult.stream().map(BatchOrderDTO::getRefOrderNo).collect(Collectors.toList());
        List<BatchTaskItemPO> batchTaskItemPOS = batchTaskItemMapper.findBatchTaskItemsBySowTaskNos(sowTaskNos, orgId);

        Map<String, List<BatchTaskItemPO>> batchTaskItemMap =
            batchTaskItemPOS.stream().collect(Collectors.groupingBy(BatchTaskItemPO::getRefOrderNo));

        // 增加打包箱号数据
        List<PackageOrderItemDTO> packageOrderItemDTOS =
            packageOrderItemBL.listByOrderNos(orgId, warehouseId, refOrderNos);
        Map<String, List<PackageOrderItemDTO>> packageOrderItemMap =
            packageOrderItemDTOS.stream().collect(Collectors.groupingBy(PackageOrderItemDTO::getRefOrderNo));

        batchOrderResult.forEach(batchOrder -> {
            List<PackageOrderItemDTO> packageOrderItems = packageOrderItemMap.get(batchOrder.getRefOrderNo());
            List<BatchTaskItemPO> batchTaskItems = batchTaskItemMap.get(batchOrder.getRefOrderNo());

            if (CollectionUtils.isNotEmpty(packageOrderItems)) {
                List<String> boxCodeNos = packageOrderItems.stream().map(PackageOrderItemDTO::getBoxCodeNo).distinct()
                    .collect(Collectors.toList());
                batchOrder.setBoxCodeNos(boxCodeNos);
            }

            // 后续排序用
            if (CollectionUtils.isNotEmpty(batchTaskItems)) {
                batchOrder.setBatchTaskNo(batchTaskItems.get(0).getBatchTaskNo());
            } else {
                batchOrder.setBatchTaskNo("");
            }

            if (CollectionUtils.isNotEmpty(batchTaskItems)
                && CollectionUtils.isNotEmpty(batchTaskItems.stream()
                    .filter(item -> item.getTaskState() != TaskStateEnum.已完成.getType()).collect(Collectors.toList()))
                && batchOrder.getSowTaskState() != SowTaskStateEnum.已播种.getType()) {
                batchOrder.setOrderState(OutStockOrderStateEnum.拣货中.getType());
            } else {
                batchOrder.setOrderState(OutStockOrderStateEnum.已拣货.getType());
            }

        });

        batchOrderResult.sort(
            Comparator.comparing(BatchOrderDTO::getBatchState).reversed().thenComparing(BatchOrderDTO::getBatchTaskNo));
        return batchOrderResult;
    }

    /**
     * 移除订单时，删除波次相关信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteBatchByRemoveOrder(List<String> batchNoList) {
        LOG.info("移除订单时，删除的波次编号：{}", JSON.toJSONString(batchNoList));
        if (CollectionUtils.isEmpty(batchNoList)) {
            return;
        }

        // 1、删除播种任务
        List<SowTaskPO> sowTaskPOS = sowTaskMapper.findSowTaskByBatchNos(batchNoList);
        if (CollectionUtils.isNotEmpty(sowTaskPOS)) {
            sowTaskPOS.stream().collect(
                Collectors.groupingBy(SowTaskPO::getOrgId, Collectors.mapping(SowTaskPO::getId, Collectors.toList())))
                .forEach((orgId, sowTaskIds) -> {
                    sowTaskMapper.deleteBySowTaskIds(sowTaskIds, orgId);
                    sowTaskItemMapper.deleteBySowTaskIds(sowTaskIds, orgId);
                });
        }

        // 播种任务订单关联表删除
        List<SowOrderPO> sowOrderPOS = sowOrderMapper.findByBatchNos(batchNoList);
        if (CollectionUtils.isNotEmpty(sowOrderPOS)) {
            List<Long> sowOrderIds =
                sowOrderPOS.stream().map(SowOrderPO::getId).distinct().collect(Collectors.toList());
            sowOrderMapper.deleteByIds(sowOrderIds, sowOrderPOS.get(0).getOrgId());
        }

        // 2、删除波次、拣货任务、拣货任务详情
        batchTaskItemMapper.deleteByBatchTaskNo(batchNoList);
        batchTaskMapper.deleteByBatchNo(batchNoList);
        batchMapper.deleteByBatchNo(batchNoList);
    }

    /**
     * 更新波次状态 <br/>
     * 这里加锁已经没用了，已经在事务里了
     */
    @Deprecated
    @DistributeLock(conditions = "#batchNo", sleepMills = 3000, expireMills = 60000, key = "updateBatchStateByBatchNo",
        lockType = DistributeLock.LockType.WAITLOCK)
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchStateByBatchNo(String batchNo, String operateUser, Integer operatorUserId) {
        AssertUtils.notNull(batchNo, "波次编号不能为空");
        // 1、判断波次是否已完成
        boolean isFinish = isBatchStateFinish(batchNo, operateUser);
        if (isFinish) {
            // 2、处理订单缺货
            outStockOrderBL.processOrderLack(batchNo, operatorUserId);
            // 3、处理订单出库位
            outStockOrderBL.processOrderLocation(batchNo);
            // 4. 重算大小件信息
            applicationEventPublisher.publishEvent(BatchCompleteEvent.of(batchNo));
        }
    }

    @DistributeLock(conditions = "#batchNo", sleepMills = 3000, expireMills = 60000, key = "updateBatchStateByBatchNo",
        lockType = DistributeLock.LockType.WAITLOCK)
    @Transactional(rollbackFor = Exception.class)
    public void finishScopBatchStateByBatchNo(String batchNo, String operateUser, Integer operatorUserId) {
        AssertUtils.notNull(batchNo, "波次编号不能为空");
        // 1、判断波次是否已完成
        boolean isFinish = isScopBatchStateFinish(batchNo, operateUser);
        if (isFinish) {
            // 2、处理订单缺货
            outStockOrderBL.processOrderLack(batchNo, operatorUserId);
            // 3、处理订单出库位
            outStockOrderBL.processOrderLocation(batchNo);
            // 4. 重算大小件信息
            applicationEventPublisher.publishEvent(BatchCompleteEvent.of(batchNo));
        }
    }

    /**
     * 获取波次状态
     *
     * @return
     */
    private Integer getScopFinishBatchState(List<BatchTaskPO> batchTaskPOS, String batchId, Integer orgId) {
        Integer batchState = BatchStateEnum.PICKING.getType();
        long count = 0;
        if (CollectionUtils.isNotEmpty(batchTaskPOS)) {
            // 如果波次任务中不包含，拣货中，待拣货，待调度，说明本波次中已经全部拣货完毕
            count = batchTaskPOS.stream().filter(n -> TaskStateEnum.未分拣.getType() == n.getTaskState().byteValue()
                || TaskStateEnum.分拣中.getType() == n.getTaskState().byteValue()).count();
        }
        if (count == 0) {
            // 如果波次存在播种任务，且播种任务中包含未播种、播种中状态的，则波次状态为“播种中”
            batchState = getBatchStateEnumToSown(batchId, orgId);
        }

        if (batchState.equals(BatchStateEnum.REVIEWING.getType())) {
            return BatchStateEnum.PICKINGEND.getType();
        }
        return batchState;
    }

    /**
     * 获取波次状态
     *
     * @return
     */
    private Integer getBatchState(List<BatchTaskPO> batchTaskPOS, String batchId, Integer orgId) {
        Integer batchState = BatchStateEnum.PICKING.getType();
        long count = 0;
        if (CollectionUtils.isNotEmpty(batchTaskPOS)) {
            // 如果波次任务中不包含，拣货中，待拣货，待调度，说明本波次中已经全部拣货完毕
            count = batchTaskPOS.stream().filter(n -> TaskStateEnum.未分拣.getType() == n.getTaskState().byteValue()
                || TaskStateEnum.分拣中.getType() == n.getTaskState().byteValue()).count();
        }
        if (count == 0) {
            // 如果波次存在播种任务，且播种任务中包含未播种、播种中状态的，则波次状态为“播种中”
            batchState = getBatchStateEnumToSown(batchId, orgId);
        }

        if (batchState.equals(BatchStateEnum.PICKINGEND.getType())) {
            // 判断托盘复核是否全部完成
            List<Byte> reviewStates = new ArrayList<>();
            reviewStates.add(PackageReviewStateEnum.待复核.getType());
            reviewStates.add(PackageReviewStateEnum.复核中.getType());
            List<PackageOrderItemDTO> packageOrderItemDTOS = packageOrderItemBL.findPackageOrderItemsByBatchId(batchId,
                orgId, PackageTypeEnum.托盘.getType(), reviewStates);
            if (CollectionUtils.isNotEmpty(packageOrderItemDTOS)) {
                String boxCodeNos = packageOrderItemDTOS.stream().map(PackageOrderItemDTO::getBoxCodeNo)
                    .collect(Collectors.joining(","));
                LOG.info("{}波次完成拣货,托盘复核任务未完成:{}", batchId, boxCodeNos);
                batchState = BatchStateEnum.REVIEWING.getType();
            }
        }
        return batchState;
    }

    /**
     * 判断波次是否已完成
     *
     * @return
     */
    public boolean isBatchStateFinish(String batchNo, String operateUser) {
        AssertUtils.notNull(batchNo, "波次编号不能为空");
        List<BatchPO> batchPOList = batchMapper.listByBatchNos(Arrays.asList(batchNo));
        if (CollectionUtils.isEmpty(batchPOList)) {
            throw new BusinessException("找不到波次：" + batchNo);
        }
        BatchPO batchPO = batchPOList.get(0);
        // 已拣货和已出库的不用重复更新
        if (Objects.equals(BatchStateEnum.PICKINGEND.getType().byteValue(), batchPO.getState())
            || Objects.equals(BatchStateEnum.ALREADYOUTOFSTORE.getType().byteValue(), batchPO.getState())) {
            throw new BusinessValidateException("该波次已拣货或已出库，无法重复更新：" + batchNo);
        }
        String batchId = batchPO.getId();
        Integer orgId = batchPO.getOrgId();

        // 查询波次下所有拣货任务
        List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findTaskByBatchNo(Arrays.asList(batchNo));
        // 获取波次状态
        Integer batchState = getBatchState(batchTaskPOS, batchId, orgId);
        // 更新波次状态
        batchMapper.updateBatchState(Collections.singletonList(batchId), batchState, orgId);
        if (!batchState.equals(BatchStateEnum.PICKINGEND.getType())) {
            return false;
        }
        LOG.info("{}波次完成拣货时，波次状态为{}", batchNo, batchState);
        // -添加操作记录（波次完成拣货）
        orderTraceBL.updateBatchTrace(batchPOList, OrderTraceDescriptionEnum.波次完成拣货.name(), operateUser);
        return true;
    }

    /**
     * 判断波次是否已完成
     *
     * @return
     */
    public boolean isScopBatchStateFinish(String batchNo, String operateUser) {
        AssertUtils.notNull(batchNo, "波次编号不能为空");
        List<BatchPO> batchPOList = batchMapper.listByBatchNos(Arrays.asList(batchNo));
        if (CollectionUtils.isEmpty(batchPOList)) {
            throw new BusinessException("找不到波次：" + batchNo);
        }
        BatchPO batchPO = batchPOList.get(0);
        // 已拣货和已出库的不用重复更新
        if (Objects.equals(BatchStateEnum.PICKINGEND.getType().byteValue(), batchPO.getState())
            || Objects.equals(BatchStateEnum.ALREADYOUTOFSTORE.getType().byteValue(), batchPO.getState())) {
            throw new BusinessValidateException("该波次已拣货或已出库，无法重复更新：" + batchNo);
        }
        String batchId = batchPO.getId();
        Integer orgId = batchPO.getOrgId();

        // 查询波次下所有拣货任务
        List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findTaskByBatchNo(Arrays.asList(batchNo));
        // 获取波次状态
        Integer batchState = getScopFinishBatchState(batchTaskPOS, batchId, orgId);
        // 更新波次状态
        batchMapper.updateBatchState(Collections.singletonList(batchId), batchState, orgId);
        if (!batchState.equals(BatchStateEnum.PICKINGEND.getType())) {
            return false;
        }
        LOG.info("{}波次完成拣货时，波次状态为{}", batchNo, batchState);
        // -添加操作记录（波次完成拣货）
        orderTraceBL.updateBatchTrace(batchPOList, OrderTraceDescriptionEnum.波次完成拣货.name(), operateUser);
        return true;
    }

    /**
     * 判断波次是否已完成
     *
     * @return
     */
    public boolean isBatchStateFinish(String batchNo, String operateUser, Byte sowTaskType) {
        AssertUtils.notNull(batchNo, "波次编号不能为空");
        List<BatchPO> batchPOList = batchMapper.listByBatchNos(Arrays.asList(batchNo));
        if (CollectionUtils.isEmpty(batchPOList)) {
            throw new BusinessException("找不到波次：" + batchNo);
        }
        BatchPO batchPO = batchPOList.get(0);
        // 已拣货和已出库的不用重复更新
        if ((sowTaskType == null || SowTaskTypeEnum.二次分拣播种.getType() != sowTaskType)
            && (Objects.equals(BatchStateEnum.PICKINGEND.getType().byteValue(), batchPO.getState())
                || Objects.equals(BatchStateEnum.ALREADYOUTOFSTORE.getType().byteValue(), batchPO.getState()))) {
            LOG.info("该波次已拣货或已出库，无法重复更新：" + batchNo);
            return false;
        }
        String batchId = batchPO.getId();
        Integer orgId = batchPO.getOrgId();

        // 查询波次下所有拣货任务
        List<BatchTaskPO> batchTaskPOS = batchTaskMapper.findTaskByBatchNo(Arrays.asList(batchNo));
        // 获取波次状态
        Integer batchState = getBatchState(batchTaskPOS, batchId, orgId);
        // 更新波次状态
        batchMapper.updateBatchState(Collections.singletonList(batchId), batchState, orgId);
        if (!batchState.equals(BatchStateEnum.PICKINGEND.getType())) {
            return false;
        }
        LOG.info("{}波次完成拣货时，波次状态为{}", batchNo, batchState);
        // -添加操作记录（波次完成拣货）
        orderTraceBL.updateBatchTrace(batchPOList, OrderTraceDescriptionEnum.波次完成拣货.name(), operateUser);
        return true;
    }

    /**
     * 设置波次状态为“播种中”
     */
    private Integer getBatchStateEnumToSown(String batchId, Integer orgId) {
        Integer batchState = BatchStateEnum.PICKINGEND.getType();
        // 根据波次id查询所有播种任务
        List<SowTaskPO> sowTaskPOList = sowTaskMapper.listSowTaskByBatchId(batchId, orgId);
        LOG.info("播种任务列表：{}, batchId: {}", JSON.toJSONString(sowTaskPOList), batchId);
        // 拣货任务全部完成时，如果波次存在播种任务，且播种任务中包含未播种、播种中状态的，则波次状态为“播种中”
        if (CollectionUtils.isNotEmpty(sowTaskPOList)) {
            long sowTaskCount = sowTaskPOList.stream()
                .filter(n -> SowTaskStateEnum.待播种.getType() == n.getState().byteValue()
                    || SowTaskStateEnum.播种中.getType() == n.getState().byteValue()
                    || SowTaskStateEnum.待集货.getType() == n.getState().byteValue())
                .count();
            if (sowTaskCount > 0) {
                batchState = BatchStateEnum.SOWN.getType();
            }
        }
        return batchState;
    }

    /**
     * 直接出库
     *
     * @param outStockOrderPOS
     * @param batchOutStockDTO
     */
    private void directOutStock(List<OutStockOrderPO> outStockOrderPOS, BatchOutStockDTO batchOutStockDTO) {
        List<InventoryDeliveryJiupiOrder> deliveryOrders =
            WaveOrderConvertor.outStockOrderPOS2InventoryDeliveryJiupiOrders(outStockOrderPOS);

        // 批量发货
        if (CollectionUtils.isNotEmpty(deliveryOrders)) {
            // List<InventoryDeliveryJiupiOrder> processOrders = iInventoryOrderBizService.batchDeliver(deliveryOrders);
            // List<SecOwnerChangeDTO> secOwnerChangeDTOList = WaveOrderConvertor.convertSyncAddSecInfo(processOrders);

            // 修改订单状态
            List<String> orderNos = deliveryOrders.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).distinct()
                .collect(Collectors.toList());
            LOG.info("波次出库订单:{}", JSON.toJSONString(orderNos));
            // SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包
            Map<Long, List<LogisticsDTO>> longListMap = outStockOrderBL.checkLogisticsOrder(outStockOrderPOS);
            List<DirectOutStockDTO> directOutStockDTOS = new ArrayList<>();
            DirectOutStockDTO directOutStockDTO = new DirectOutStockDTO();
            directOutStockDTO.setOperateUserId(batchOutStockDTO.getOperateUserId());
            directOutStockDTO.setOperateUser(batchOutStockDTO.getOperateUser());
            directOutStockDTOS.add(directOutStockDTO);
            outStockOrderBL.directOutStockByOrderWithOrderCenter(directOutStockDTOS, outStockOrderPOS, longListMap);
        }
    }

    /**
     * 处理订单变更时相关拣货逻辑
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void processOrderChangeWithBatch(List<OutStockOrderProcessChangeDTO> processChangeDTOS, String operatorUser,
        Byte changeType) {
        long start = System.currentTimeMillis();

        validateOrderProcess(processChangeDTOS, changeType);
        LOG.info("处理订单变更时相关拣货逻辑参数:{}, changeType:{}", JSON.toJSONString(processChangeDTOS), changeType);

        // 添加相同播种任务下的其他订单
        // addOtherOrderBySowTask(processChangeDTOS);

        // 1、查询订单项关联的拣货任务项，获取订单项的实际拣货数量
        List<Long> orderItemIds = processChangeDTOS.stream().flatMap(p -> p.getItemList().stream()).map(p -> p.getId())
            .distinct().collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderItemIds(orderItemIds);
        if (CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            LOG.info("查询订单项关联的拣货任务项为空，跳过：{}", JSON.toJSONString(orderItemIds));
            return;
        }
        LOG.info("处理订单变更时查询订单项关联的拣货任务项：{}", JSON.toJSONString(orderItemTaskInfoPOList));

        // 2、查询订单项详情
        List<OutStockOrderItemPO> orderItemPOList = outStockOrderItemQueryBL.findOutStockOrderItemList(orderItemIds);
        if (CollectionUtils.isEmpty(orderItemPOList)) {
            LOG.info("订单项查询为空，跳过：{}", JSON.toJSONString(orderItemIds));
            return;
        }
        Map<Long, OutStockOrderItemPO> orderItemPOMap = new HashMap<>();
        orderItemPOList.forEach(item -> {
            orderItemPOMap.put(item.getId(), item);
        });

        // 没生成播种任务的订单项
        List<OutStockOrderChangeDTO> normalOrderChangeList = new ArrayList<>();
        // 生成播种任务的订单项
        List<SowTaskCancelDTO> sowTaskCancelDTOS = new ArrayList<>();

        // 3、遍历订单，找到有差异的订单项
        processChangeDTOS.forEach(processChangeDTO -> {
            List<OutStockOrderItemChangeDTO> normalOrderItemChangeList = new ArrayList<>();
            List<SowTaskCancelItemDTO> sowTaskCancelItemDTOS = new ArrayList<>();

            processChangeDTO.getItemList().forEach(processChangeItemDTO -> {
                // 获取订单项详情
                OutStockOrderItemPO orderItemPO = orderItemPOMap.get(processChangeItemDTO.getId());
                if (orderItemPO == null) {
                    return;
                }
                // 获取当前订单项关联的拣货任务项
                List<OrderItemTaskInfoPO> relateList = orderItemTaskInfoPOList.stream()
                    .filter(p -> Objects.equals(p.getRefOrderItemId(), processChangeItemDTO.getId()))
                    .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(relateList)) {
                    return;
                }
                // 获取订单项实际应拣数量
                BigDecimal actualCount = relateList.stream()
                    .map(p -> p.getUnitTotalCount().subtract(p.getLackUnitCount()).add(p.getMoveCount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                // if (actualCount.compareTo(processChangeItemDTO.getUnitTotalCount()) == 0) {
                // // 订单项数量与实际应拣数量相同时，不需要做任何操作
                // return;
                // }
                // 差异数量
                BigDecimal changeCount = processChangeItemDTO.getUnitTotalCount().subtract(actualCount);

                // 没生成播种任务的订单项
                if (orderItemPO.getSowTaskId() == null) {
                    OutStockOrderItemChangeDTO changeItemDTO = new OutStockOrderItemChangeDTO();
                    changeItemDTO.setId(processChangeItemDTO.getId());
                    changeItemDTO.setSkuId(processChangeItemDTO.getSkuId());
                    changeItemDTO.setChangeCount(changeCount);
                    normalOrderItemChangeList.add(changeItemDTO);
                } else {
                    if (processChangeItemDTO.getDiffCount() != null) {
                        SowTaskCancelItemDTO sowTaskCancelItemDTO = new SowTaskCancelItemDTO();
                        BigDecimal[] divideAndRemainder =
                            processChangeItemDTO.getDiffCount().divideAndRemainder(orderItemPO.getSpecquantity());
                        sowTaskCancelItemDTO.setChangePackageCount(divideAndRemainder[0]);
                        sowTaskCancelItemDTO.setChangeUnitCount(divideAndRemainder[1]);
                        sowTaskCancelItemDTO.setChangeCount(processChangeItemDTO.getDiffCount());
                        sowTaskCancelItemDTO.setOrderId(processChangeDTO.getId());
                        sowTaskCancelItemDTO.setOrderItemId(processChangeItemDTO.getId());
                        sowTaskCancelItemDTO.setSkuId(processChangeItemDTO.getSkuId());
                        sowTaskCancelItemDTO.setSowTaskId(orderItemPO.getSowTaskId());
                        sowTaskCancelItemDTO.setSowTaskNo(orderItemPO.getSowTaskNo());
                        BigDecimal overUnitTotalCount = orderItemPO.getUnittotalcount().add(changeCount);
                        sowTaskCancelItemDTO.setOverUnitTotalCount(overUnitTotalCount);
                        sowTaskCancelItemDTO.setSowTaskItemId(orderItemPO.getSowTaskItemId());
                        sowTaskCancelItemDTO.setSowTaskId(orderItemPO.getSowTaskId());
                        sowTaskCancelItemDTO.setSowTaskNo(orderItemPO.getSowTaskNo());
                        sowTaskCancelItemDTO.setBatchTaskChangeCount(changeCount);

                        sowTaskCancelItemDTOS.add(sowTaskCancelItemDTO);
                    } else {
                        LOG.error("处理订单变更，订单项数量异常:{}", JSON.toJSONString(processChangeItemDTO));
                    }
                }
            });

            // 记录没生成播种任务的订单项
            if (CollectionUtils.isNotEmpty(normalOrderItemChangeList)) {
                OutStockOrderChangeDTO normalOrderChangeDTO = new OutStockOrderChangeDTO();
                normalOrderChangeDTO.setId(processChangeDTO.getId());
                normalOrderChangeDTO.setRefOrderNo(processChangeDTO.getRefOrderNo());
                normalOrderChangeDTO.setCityId(processChangeDTO.getCityId());
                normalOrderChangeDTO.setWarehouseId(processChangeDTO.getWarehouseId());
                normalOrderChangeDTO.setChangeType(changeType);
                normalOrderChangeDTO.setOperatorUser(operatorUser);
                normalOrderChangeDTO.setItemList(normalOrderItemChangeList);
                normalOrderChangeList.add(normalOrderChangeDTO);
            }

            // 记录生成播种任务的订单项
            if (CollectionUtils.isNotEmpty(sowTaskCancelItemDTOS)) {
                SowTaskCancelDTO sowTaskCancelDTO = new SowTaskCancelDTO();
                sowTaskCancelDTO.setOperatorName(operatorUser);
                sowTaskCancelDTO.setOrderId(processChangeDTO.getId());
                sowTaskCancelDTO.setOrderNo(processChangeDTO.getRefOrderNo());
                sowTaskCancelDTO.setChangeType(changeType);
                sowTaskCancelDTO.setSowTaskCancelItemDTOS(sowTaskCancelItemDTOS);
                sowTaskCancelDTO.setCityId(processChangeDTO.getCityId());
                sowTaskCancelDTO.setWarehouseId(processChangeDTO.getWarehouseId());
                sowTaskCancelDTOS.add(sowTaskCancelDTO);
            }

        });

        // 没生成播种任务的订单变更时处理
        if (CollectionUtils.isNotEmpty(normalOrderChangeList)) {
            updateBatchByOrderChange(normalOrderChangeList);
        }
        // 有播种任务的订单变更时更新播种相关信息
        if (CollectionUtils.isNotEmpty(sowTaskCancelDTOS)) {
            sowManagerBL.cancelSowTaskByOrder(sowTaskCancelDTOS);
        }
        long end = System.currentTimeMillis();
        List<String> refOrderNoList =
            processChangeDTOS.stream().map(p -> p.getRefOrderNo()).distinct().collect(Collectors.toList());

        List<String> batchTaskIds = orderItemTaskInfoPOList.stream().map(OrderItemTaskInfoPO::getBatchTaskId).distinct()
            .collect(Collectors.toList());

        batchTaskChangeNotifyBL.notifyBatchTaskChanged(batchTaskIds);
        LOG.info("处理订单变更时相关拣货逻辑完成：{}, 总耗时：{}ms", JSON.toJSONString(refOrderNoList), end - start);
    }

    /**
     * 添加相同播种任务下的其他订单
     */
    public void addOtherOrderBySowTask(List<OutStockOrderProcessChangeDTO> processChangeDTOS) {
        if (CollectionUtils.isNotEmpty(processChangeDTOS)) {
            // 1、根据出库单id获取播种任务编号
            List<Long> orderIds =
                processChangeDTOS.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());
            List<String> sowTaskNoList = outStockOrderItemMapper.listSowTaskNoByOrderIds(orderIds);
            if (CollectionUtils.isEmpty(sowTaskNoList)) {
                return;
            }
            // 2、根据出库单id和播种任务编号查找其它出库单
            List<Long> otherOrderIds = outStockOrderItemMapper.listOtherOrderIdBySowTaskNos(sowTaskNoList, orderIds);
            if (CollectionUtils.isEmpty(otherOrderIds)) {
                return;
            }
            // 3、组装其它出库单
            List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.listOutStockOrderByOrderId(otherOrderIds);
            if (CollectionUtils.isEmpty(outStockOrderPOS)) {
                return;
            }
            LOG.info("查找出相同播种任务下的其他订单: {}",
                JSON.toJSONString(outStockOrderPOS.stream().map(p -> p.getReforderno()).collect(Collectors.toList())));
            // 已出库或已作废，不能重复触发
            List<Byte> lstNoNeedProcessOrderStates = Arrays.asList(OutStockOrderStateEnum.已作废.getType(),
                OutStockOrderStateEnum.已出库.getType(), OutStockOrderStateEnum.已取消.getType());
            List<String> lstNoNeedProcessOrderNos = outStockOrderPOS.stream()
                .filter(p -> (p.getState() != null && lstNoNeedProcessOrderStates.contains(p.getState())))
                .map(p -> p.getReforderno()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(lstNoNeedProcessOrderNos)) {
                LOG.info(
                    String.format("[添加相同播种任务下的其他订单]以下订单已出库或已作废，不用添加:%s", JSON.toJSONString(lstNoNeedProcessOrderNos)));
                outStockOrderPOS.removeIf(p -> lstNoNeedProcessOrderNos.contains(p.getReforderno()));
            }
            if (CollectionUtils.isNotEmpty(outStockOrderPOS)) {
                outStockOrderPOS.forEach(order -> {
                    if (CollectionUtils.isEmpty(order.getItems())) {
                        return;
                    }
                    List<OutStockOrderItemProcessChangeDTO> changeItemDTOList = order.getItems().stream().map(p -> {
                        OutStockOrderItemProcessChangeDTO itemChangeDTO = new OutStockOrderItemProcessChangeDTO();
                        itemChangeDTO.setId(p.getId());
                        itemChangeDTO.setSkuId(p.getSkuid());
                        itemChangeDTO.setUnitTotalCount(p.getUnittotalcount());
                        return itemChangeDTO;
                    }).collect(Collectors.toList());

                    OutStockOrderProcessChangeDTO changeDTO = new OutStockOrderProcessChangeDTO();
                    changeDTO.setId(order.getId());
                    changeDTO.setRefOrderNo(order.getReforderno());
                    changeDTO.setCityId(order.getOrgId());
                    changeDTO.setWarehouseId(order.getWarehouseId());
                    changeDTO.setItemList(changeItemDTOList);
                    processChangeDTOS.add(changeDTO);
                    LOG.info("添加相同播种任务下的其他订单: {}", JSON.toJSONString(changeDTO));
                });
            }
        }
    }

    /**
     * 校验参数
     */
    private void validateOrderProcess(List<OutStockOrderProcessChangeDTO> processChangeDTOS, Byte changeType) {
        AssertUtils.notEmpty(processChangeDTOS, "处理订单变更时订单不能为空");
        AssertUtils.notNull(changeType, "订单变更类型参数不能为空");
        processChangeDTOS.forEach(changeDTO -> {
            AssertUtils.notNull(changeDTO.getId(), "订单ID不能为空");
            AssertUtils.notNull(changeDTO.getRefOrderNo(), "订单号不能为空");
            AssertUtils.notNull(changeDTO.getCityId(), "城市id不能为空");
            AssertUtils.notNull(changeDTO.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notEmpty(changeDTO.getItemList(), "订单项明细不能为空");
            changeDTO.getItemList().forEach(changeItem -> {
                AssertUtils.notNull(changeItem.getId(), "订单项id不能为空");
                AssertUtils.notNull(changeItem.getSkuId(), "skuId不能为空");
                AssertUtils.notNull(changeItem.getUnitTotalCount(), "订单项数量不能为空");
            });
        });
    }

    /**
     * 校验参数
     */
    private void validateOrderChange(List<OutStockOrderChangeDTO> changeDTOList) {
        AssertUtils.notEmpty(changeDTOList, "处理订单变更时参数不能为空");
        changeDTOList.forEach(changeDTO -> {
            AssertUtils.notNull(changeDTO.getId(), "订单ID不能为空");
            AssertUtils.notNull(changeDTO.getRefOrderNo(), "订单号不能为空");
            AssertUtils.notEmpty(changeDTO.getItemList(), "订单项明细不能为空");
            AssertUtils.notNull(changeDTO.getChangeType(), "订单变更类型不能为空");
            AssertUtils.notNull(changeDTO.getCityId(), "城市id不能为空");
            AssertUtils.notNull(changeDTO.getWarehouseId(), "仓库id不能为空");
            changeDTO.getItemList().forEach(changeItem -> {
                AssertUtils.notNull(changeItem.getId(), "订单项id不能为空");
                AssertUtils.notNull(changeItem.getChangeCount(), "订单项变更数量不能为空");
            });
        });
    }

    /**
     * 订单变更时更新波次相关信息
     */
    public void updateBatchByOrderChange(List<OutStockOrderChangeDTO> changeDTOList) {
        long start = System.currentTimeMillis();

        validateOrderChange(changeDTOList);
        LOG.info("订单变更时更新波次相关信息参数:{}", JSON.toJSONString(changeDTOList));

        List<OutStockOrderChangeDTO> copyChangeDTOList =
            JSON.parseArray(JSON.toJSONString(changeDTOList), OutStockOrderChangeDTO.class);

        // 排除掉变更数量为0 TODO 这里不应该改变原始入参，以后有时间改
        changeDTOList.forEach(changeDTO -> {
            changeDTO.getItemList().removeIf(p -> p.getChangeCount().compareTo(BigDecimal.ZERO) == 0);
        });
        changeDTOList.removeIf(p -> CollectionUtils.isEmpty(p.getItemList()));
        if (CollectionUtils.isEmpty(changeDTOList)) {
            LOG.info("排除掉变更数量为0的数据,{}", JSON.toJSONString(copyChangeDTOList));
            handleAllLackOrderItem(copyChangeDTOList);
            return;
        }

        // 1、查询订单项对应的波次下所有订单详情
        List<Long> orderItemIds = changeDTOList.stream().flatMap(p -> p.getItemList().stream()).map(p -> p.getId())
            .distinct().collect(Collectors.toList());
        List<String> batchIds = outStockOrderItemMapper.listBatchIdByIds(orderItemIds);
        if (CollectionUtils.isEmpty(batchIds)) {
            LOG.info("订单项未生成拣货任务，跳过：{}", JSON.toJSONString(orderItemIds));
            return;
        }
        List<OutStockOrderItemPO> orderItemPOList = outStockOrderItemMapper.listByBatchIds(batchIds);
        if (CollectionUtils.isEmpty(orderItemPOList)) {
            LOG.info("查询波次下订单明细为空：{}", JSON.toJSONString(orderItemIds));
            return;
        }

        // 2、查询波次下所有订单项和拣货任务项关联关系
        List<Long> batchOrderItemIds = orderItemPOList.stream().map(p -> p.getId()).collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(batchOrderItemIds);
        if (orderItemTaskInfoPOList == null || CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            LOG.info("查询波次下订单项和拣货任务项关联为空：{}", JSON.toJSONString(batchOrderItemIds));
            return;
        }

        // 3、查询拣货任务详情
        List<BatchTaskItemPO> batchTaskItemPOList = batchTaskItemMapper.findBatchTaskItemsByBatchIds(batchIds);
        if (batchTaskItemPOList == null || CollectionUtils.isEmpty(batchTaskItemPOList)) {
            LOG.info("查询波次下拣货任务详情为空：{}", JSON.toJSONString(batchIds));
            return;
        }

        Integer warehouseId = changeDTOList.get(0).getWarehouseId();
        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(warehouseId);

        // 获取货位类型集合
        Map<Long, Byte> locationSubCategoryMap = getLocationSubCategoryMap(batchTaskItemPOList);

        // 4、找出需要更新的拣货任务详情
        List<BatchTaskItemPO> updateBatchTaskItemList = new ArrayList<>();
        List<BatchTaskItemPO> addBatchTaskItemRollbackCountList = new ArrayList<>();
        List<OrderItemTaskInfoPO> updateOrderItemTaskInfoList = new ArrayList<>();
        List<OrderItemTaskInfoDetailPO> updateOrderItemTaskInfoDetailList = new ArrayList<>();
        List<OutStockOrderItemChangeRecordPO> orderItemChangeRecordList = new ArrayList<>();
        Map<Long, StoreTransferOrderDTO> storeTransferMap = new HashMap<>();
        processUpdateBatchTaskItemList(changeDTOList, orderItemPOList, orderItemTaskInfoPOList, batchTaskItemPOList,
            updateBatchTaskItemList, addBatchTaskItemRollbackCountList, updateOrderItemTaskInfoList,
            updateOrderItemTaskInfoDetailList, orderItemChangeRecordList, storeTransferMap, locationSubCategoryMap,
            warehouseConfigDTO);

        LOG.info(
            "找出需要更新的拣货任务详情 changeDTOList：{}，orderItemPOList：{}，orderItemTaskInfoPOList：{}，batchTaskItemPOList：{}，updateBatchTaskItemList：{}，addBatchTaskItemRollbackCountList：{}，, updateOrderItemTaskInfoList：{}, updateOrderItemTaskInfoDetailList：{}, orderItemChangeRecordList：{}",
            JSON.toJSONString(changeDTOList), JSON.toJSONString(orderItemPOList),
            JSON.toJSONString(orderItemTaskInfoPOList), JSON.toJSONString(batchTaskItemPOList),
            JSON.toJSONString(updateBatchTaskItemList), JSON.toJSONString(addBatchTaskItemRollbackCountList),
            JSON.toJSONString(updateOrderItemTaskInfoList), JSON.toJSONString(updateOrderItemTaskInfoDetailList),
            JSON.toJSONString(orderItemChangeRecordList));
        // 5、更新相关波次、拣货任务的数量
        executeBatchUpdate(orderItemPOList, batchTaskItemPOList, updateBatchTaskItemList,
            addBatchTaskItemRollbackCountList, updateOrderItemTaskInfoList, updateOrderItemTaskInfoDetailList);

        // 6、移库操作（一个订单生成一个移库单）
        Map<Long, StoreTransferOrderDTO> storeTransferNoMap = new HashMap<>();
        try {
            Warehouse warehouse = iWarehouseQueryService.findWarehouseById(warehouseId);
            storeTransferMap.forEach((orderId, storeTransferOrderDTO) -> {
                storeTransferOrderDTO.setWarehouse_Id(warehouseId);
                storeTransferOrderDTO.setWarehouseName(warehouse != null ? warehouse.getName() : null);
                LOG.info("[订单变更]移库单：{}", JSON.toJSONString(storeTransferOrderDTO));
                StoreTransferOrderDTO returnTransferOrderDTO =
                    iStoreTransferOrderService.updateStoreTranferNoOrder(storeTransferOrderDTO);
                // 记录订单对应的移库单号
                storeTransferNoMap.put(orderId, returnTransferOrderDTO);
            });
        } catch (Exception e) {
            LOG.error("[订单变更]移库异常", e);
        }

        // 7、保存订单项变更记录
        if (CollectionUtils.isNotEmpty(orderItemChangeRecordList)) {
            // 需要移库的记录移库单ID和编号
            orderItemChangeRecordList.forEach(changeDTO -> {
                if (changeDTO.getMoveFlag()) {
                    StoreTransferOrderDTO transferOrderDTO = storeTransferNoMap.get(changeDTO.getRefOrderId());
                    if (transferOrderDTO != null) {
                        changeDTO.setTransferOrderId(transferOrderDTO.getId());
                        changeDTO.setTransferOrderNo(transferOrderDTO.getStoreTransferNo());
                    }
                }
            });
            LOG.info("[订单变更]保存订单项变更记录：{}", JSON.toJSONString(orderItemChangeRecordList));
            outStockOrderItemChangeRecordMapper.insertBatch(orderItemChangeRecordList);
        }

        // 8、更新波次和拣货任务状态
        updateBatchTaskState(batchIds, OrderChangeTypeEnum.getEnumByValue(changeDTOList.get(0).getChangeType()),
            changeDTOList);

        // 9、推送消息给分拣员
        pushMessage(changeDTOList, orderItemTaskInfoPOList);

        long end = System.currentTimeMillis();
        List<String> refOrderNoList =
            changeDTOList.stream().map(p -> p.getRefOrderNo()).distinct().collect(Collectors.toList());
        LOG.info("订单变更时更新波次相关信息完成：{}, 总耗时：{}ms", JSON.toJSONString(refOrderNoList), end - start);
    }

    private void handleAllLackOrderItem(List<OutStockOrderChangeDTO> changeDTOList) {
        // 1、查询订单项对应的波次下所有订单详情
        List<Long> orderItemIds = changeDTOList.stream().flatMap(p -> p.getItemList().stream()).map(p -> p.getId())
            .distinct().collect(Collectors.toList());
        List<String> batchIds = outStockOrderItemMapper.listBatchIdByIds(orderItemIds);
        if (CollectionUtils.isEmpty(batchIds)) {
            LOG.info("订单项未生成拣货任务，跳过：{}", JSON.toJSONString(orderItemIds));
            return;
        }
        List<OutStockOrderItemPO> orderItemPOList = outStockOrderItemMapper.listByBatchIds(batchIds);
        if (CollectionUtils.isEmpty(orderItemPOList)) {
            LOG.info("查询波次下订单明细为空：{}", JSON.toJSONString(orderItemIds));
            return;
        }

        // 2、查询波次下所有订单项和拣货任务项关联关系
        List<Long> batchOrderItemIds = orderItemPOList.stream().map(p -> p.getId()).collect(Collectors.toList());
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(batchOrderItemIds);
        if (orderItemTaskInfoPOList == null || CollectionUtils.isEmpty(orderItemTaskInfoPOList)) {
            LOG.info("查询波次下订单项和拣货任务项关联为空：{}", JSON.toJSONString(batchOrderItemIds));
            return;
        }
        // 8、更新波次和拣货任务状态
        updateBatchTaskState(batchIds, OrderChangeTypeEnum.getEnumByValue(changeDTOList.get(0).getChangeType()),
            changeDTOList);

        // 9、推送消息给分拣员
        // pushMessage(changeDTOList, orderItemTaskInfoPOList);
    }

    /**
     * 推送消息给分拣员
     */
    private void pushMessage(List<OutStockOrderChangeDTO> changeDTOList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        try {
            // 1、找出订单对应的拣货任务
            List<Long> refOrderIdList =
                changeDTOList.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());
            List<String> batchTaskIdList =
                orderItemTaskInfoPOList.stream().filter(p -> refOrderIdList.contains(p.getRefOrderId()))
                    .map(p -> p.getBatchTaskId()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(batchTaskIdList)) {
                return;
            }
            List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findTasksByBatchTaskId(batchTaskIdList);
            if (CollectionUtils.isEmpty(batchTaskPOList)) {
                return;
            }

            // 2、根据订单分别找到对应的拣货任务的分拣员，推送消息
            changeDTOList.forEach(changeDTO -> {
                // 当前订单对应的拣货任务
                List<String> filterBatchTaskIdList =
                    orderItemTaskInfoPOList.stream().filter(p -> Objects.equals(p.getRefOrderId(), changeDTO.getId()))
                        .map(p -> p.getBatchTaskId()).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filterBatchTaskIdList)) {
                    return;
                }
                // 拣货任务对应的分拣员
                List<Integer> sorterIds = batchTaskPOList.stream()
                    .filter(p -> filterBatchTaskIdList.contains(p.getId()) && p.getSorterId() != null)
                    .map(p -> p.getSorterId()).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(sorterIds)) {
                    return;
                }
                String content = String.format("有一笔订单被取消，订单号是%s", changeDTO.getRefOrderNo());
                if (Objects.equals(changeDTO.getChangeType(), OrderChangeTypeEnum.订单修改.getType())) {
                    content = String.format("有一笔订单被修改，订单号是%s", changeDTO.getRefOrderNo());
                }
                for (Integer sorterId : sorterIds) {
                    orderTraceBL.pushMsg(sorterId, content);
                }
            });
        } catch (Exception e) {
            LOG.error("订单变更时推送消息给分拣员异常", e);
        }
    }

    /**
     * 找出需要更新的拣货任务详情
     */
    private void processUpdateBatchTaskItemList(List<OutStockOrderChangeDTO> changeDTOList,
        List<OutStockOrderItemPO> orderItemPOList, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList,
        List<BatchTaskItemPO> batchTaskItemPOList, List<BatchTaskItemPO> updateBatchTaskItemList,
        List<BatchTaskItemPO> addBatchTaskItemRollbackCountList, List<OrderItemTaskInfoPO> updateOrderItemTaskInfoList,
        List<OrderItemTaskInfoDetailPO> updateOrderItemTaskInfoDetailList,
        List<OutStockOrderItemChangeRecordPO> orderItemChangeRecordList,
        Map<Long, StoreTransferOrderDTO> storeTransferMap, Map<Long, Byte> locationSubCategoryMap,
        WarehouseConfigDTO warehouseConfigDTO) {
        changeDTOList.forEach(changeDTO -> {
            // 移库单项
            List<StoreTransferOrderItemDTO> storeTransferOrderItemDTOS = new ArrayList<>();
            changeDTO.getItemList().forEach(changeItemDTO -> {
                // 1、查找订单项
                Optional<OutStockOrderItemPO> optionalOrderItemPO =
                    orderItemPOList.stream().filter(p -> Objects.equals(p.getId(), changeItemDTO.getId())).findFirst();
                if (!optionalOrderItemPO.isPresent()) {
                    LOG.info("订单项不存在：{}", changeItemDTO.getId());
                    return;
                }
                OutStockOrderItemPO orderItemPO = optionalOrderItemPO.get();

                // 2、根据订单项找出关联的拣货任务项集合 (按拣货数量由大到小排序)
                List<OrderItemTaskInfoPO> batchTaskInfoList = orderItemTaskInfoPOList.stream()
                    .filter(p -> Objects.equals(p.getRefOrderItemId(), changeItemDTO.getId()))
                    .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(batchTaskInfoList)) {
                    LOG.info("根据订单项找不到关联的拣货任务详情：{}", changeItemDTO.getId());
                    return;
                }
                // 找出关联的明细项
                List<OrderItemTaskInfoDetailPO> batchTaskInfoDetailList = new ArrayList<>();
                for (OrderItemTaskInfoPO taskInfoPO : batchTaskInfoList) {
                    if (CollectionUtils.isNotEmpty(taskInfoPO.getDetailList())) {
                        batchTaskInfoDetailList.addAll(taskInfoPO.getDetailList());
                    } else {
                        // 兼容老数据
                        OrderItemTaskInfoDetailPO newDetail = new OrderItemTaskInfoDetailPO();
                        newDetail.setOrgId(taskInfoPO.getOrgId());
                        newDetail.setTaskInfoId(taskInfoPO.getId());
                        newDetail.setProductSpecificationId(orderItemPO.getProductSpecificationId());
                        newDetail.setOwnerId(orderItemPO.getOwnerId());
                        newDetail.setUnitTotalCount(taskInfoPO.getUnitTotalCount()
                            .subtract(taskInfoPO.getLackUnitCount()).add(taskInfoPO.getMoveCount()));
                        batchTaskInfoDetailList.add(newDetail);
                    }
                }

                if (CollectionUtils.isEmpty(batchTaskInfoDetailList)) {
                    LOG.info("根据订单项找不到关联的拣货任务明细详情：{}", JSON.toJSONString(batchTaskInfoList));
                    return;
                }
                // 按优先级排序
                batchTaskInfoDetailList = sortOrderItemTaskInfoDetail(batchTaskInfoDetailList, changeDTO.getCityId(),
                    changeItemDTO.getChangeCount().compareTo(BigDecimal.ZERO) > 0);

                BigDecimal oldChangeCount = changeItemDTO.getChangeCount();
                // 3、遍历所有关联的明细项
                batchTaskInfoDetailList.forEach(taskInfoDetailPO -> {
                    // 订单项变更的数量扣成0时，不需要再扣
                    if (changeItemDTO.getChangeCount().compareTo(BigDecimal.ZERO) == 0) {
                        return;
                    }
                    // 获取关联项
                    Optional<OrderItemTaskInfoPO> optianOrderItemTaskInfoPO = batchTaskInfoList.stream()
                        .filter(p -> Objects.equals(p.getId(), taskInfoDetailPO.getTaskInfoId())).findFirst();
                    if (!optianOrderItemTaskInfoPO.isPresent()) {
                        LOG.info("找不到关联项：{}", taskInfoDetailPO.getTaskInfoId());
                        return;
                    }
                    OrderItemTaskInfoPO orderItemTaskInfoPO = optianOrderItemTaskInfoPO.get();
                    // 获取拣货任务详情
                    Optional<BatchTaskItemPO> optionBatchTaskItemPO = batchTaskItemPOList.stream()
                        .filter(p -> Objects.equals(p.getId(), orderItemTaskInfoPO.getBatchTaskItemId())).findFirst();
                    if (!optionBatchTaskItemPO.isPresent()) {
                        LOG.info("找不到拣货任务详情：{}", orderItemTaskInfoPO.getBatchTaskItemId());
                        return;
                    }
                    BatchTaskItemPO batchTaskItemPO = optionBatchTaskItemPO.get();

                    // (1) 当前拣货任务项修改的数量
                    BigDecimal changeCount = changeItemDTO.getChangeCount();
                    // 变更数量为正，表示订单数量增多，对应的拣货任务项数量也增加，如果对应多个拣货任务项，全部都加在第一个上
                    if (changeCount.compareTo(BigDecimal.ZERO) > 0) {
                        changeItemDTO.setChangeCount(BigDecimal.ZERO);

                        // 拣货任务项数量为0时，对应的拣货任务还未完成，则直接修改任务项数量，同时将拣货任务项状态改为"未分拣"
                        if (batchTaskItemPO.getUnitTotalCount().compareTo(BigDecimal.ZERO) == 0
                            && !Objects.equals(batchTaskItemPO.getBatchTaskState(), TaskStateEnum.已完成.getType())) {
                            batchTaskItemPO.setTaskState(TaskItemStateEnum.未分拣.getType());
                        }
                        // 变更数量为负，表示订单数量减少，依次去扣减关联的拣货任务项
                    } else {
                        // 关联的拣货任务项数量为0，则跳过
                        if (taskInfoDetailPO.getUnitTotalCount().compareTo(BigDecimal.ZERO) <= 0) {
                            // || batchTaskItemPO.getUnitTotalCount().compareTo(BigDecimal.ZERO) <= 0) {
                            LOG.info(String.format("关联的拣货任务项数量为0，跳过，taskInfoDetail:%s",
                                JSON.toJSONString(taskInfoDetailPO)));
                            return;
                        }
                        if (changeCount.abs().compareTo(taskInfoDetailPO.getUnitTotalCount()) > 0) {
                            // 订单项变更的数量大于当前拣货任务项的数量时，此次只扣减当前关联的数量
                            changeCount = taskInfoDetailPO.getUnitTotalCount().negate();
                        }
                        changeItemDTO.setChangeCount(changeItemDTO.getChangeCount().subtract(changeCount));
                    }

                    // (2) 处理拣货任务项
                    boolean moveFlag = false;
                    ChangeLocationPO changeLocationPO = new ChangeLocationPO();
                    // 未开始拣货时
                    if (Objects.equals(batchTaskItemPO.getTaskState(), TaskItemStateEnum.未分拣.getType())) {
                        // 记录需要修改数量的拣货任务项
                        recordUpdateBatchTaskItemList(updateBatchTaskItemList, changeCount, batchTaskItemPO,
                            locationSubCategoryMap, changeLocationPO);

                        // 记录需要修改的订单项关联拣货任务项数量
                        orderItemTaskInfoPO.setUnitTotalCount(orderItemTaskInfoPO.getUnitTotalCount().add(changeCount));
                        recordUpdateOrderItemTaskInfoList(updateOrderItemTaskInfoList, orderItemTaskInfoPO);

                        // 记录需要修改的订单项关联拣货任务项明细数量
                        recordUpdateOrderItemTaskInfoDetailList(updateOrderItemTaskInfoDetailList, taskInfoDetailPO,
                            changeCount);

                        // 已拣货，不修改拣货任务项数量，只记录回退数量，并且移库
                    } else {
                        // 记录不需要修改数量，只记录回退数量的拣货任务项
                        recordAddRollBackBatchTaskItemList(addBatchTaskItemRollbackCountList, changeDTO, changeCount,
                            batchTaskItemPO);

                        // 记录订单项关联拣货任务项的移库数量
                        orderItemTaskInfoPO.setMoveCount(getMoveCount(orderItemTaskInfoPO, changeCount));
                        recordUpdateOrderItemTaskInfoList(updateOrderItemTaskInfoList, orderItemTaskInfoPO);

                        // 记录需要修改的订单项关联拣货任务项明细数量
                        recordUpdateOrderItemTaskInfoDetailList(updateOrderItemTaskInfoDetailList, taskInfoDetailPO,
                            changeCount);

                        // 记录移库
                        recordStoreTransferOrderItemList(storeTransferOrderItemDTOS, batchTaskItemPO, changeCount,
                            changeItemDTO.getSowLocationId(), changeItemDTO.getSowLocationName(), orderItemPO,
                            changeLocationPO, taskInfoDetailPO.getSecOwnerId(), warehouseConfigDTO);
                        moveFlag = true;
                    }

                    // （4）订单项变更记录
                    recordOutStockOrderItemChangeRecordList(orderItemChangeRecordList, changeDTO, changeItemDTO,
                        orderItemPO, batchTaskItemPO, changeCount, moveFlag, changeLocationPO);
                });

                // 实际修改数量
                BigDecimal realChangeCount = oldChangeCount.subtract(changeItemDTO.getChangeCount());
                // 4、修改订单项数量，方便统计订单数
                orderItemPO.setUnittotalcount(orderItemPO.getUnittotalcount().add(realChangeCount));
                orderItemPO.setPackagecount(
                    orderItemPO.getUnittotalcount().divideAndRemainder(orderItemPO.getSpecquantity())[0]);
                orderItemPO
                    .setUnitcount(orderItemPO.getUnittotalcount().divideAndRemainder(orderItemPO.getSpecquantity())[1]);
            });

            // 按订单分组记录移库数据
            if (CollectionUtils.isNotEmpty(storeTransferOrderItemDTOS)) {
                StoreTransferOrderDTO storeTransferOrderDTO = new StoreTransferOrderDTO();
                storeTransferOrderDTO.setStoreTransferOrderItemDTOS(storeTransferOrderItemDTOS);
                storeTransferOrderDTO.setOrg_id(storeTransferOrderItemDTOS.get(0).getOrg_id());
                storeTransferOrderDTO.setTransferType(StoreTransferEnum.单据修改.getType());
                storeTransferOrderDTO.setStartTime(new Date());
                storeTransferOrderDTO.setFinishTime(storeTransferOrderDTO.getStartTime());
                storeTransferOrderDTO.setSorterName(changeDTO.getOperatorUser());
                storeTransferOrderDTO.setCreateUser(changeDTO.getOperatorUser());
                storeTransferOrderDTO.setRemark(
                    OrderChangeTypeEnum.getEnumByValue(changeDTO.getChangeType()) + ": " + changeDTO.getRefOrderNo());
                storeTransferOrderDTO.setIgnoreHasNotEnoughStore(true);
                storeTransferOrderDTO.setIgnoreProductionDate(true);
                storeTransferMap.put(changeDTO.getId(), storeTransferOrderDTO);
            }
        });
    }

    private BigDecimal getMoveCount(OrderItemTaskInfoPO orderItemTaskInfoPO, BigDecimal changeCount) {
        BigDecimal moveCount = orderItemTaskInfoPO.getMoveCount().add(changeCount);
        if (moveCount.compareTo(BigDecimal.ZERO) >= 0) {
            if (moveCount.compareTo(orderItemTaskInfoPO.getOriginalUnitTotalCount()) > 0) {
                return orderItemTaskInfoPO.getOriginalUnitTotalCount();
            }
        } else {
            if (moveCount.abs().compareTo(orderItemTaskInfoPO.getOriginalUnitTotalCount()) > 0) {
                return orderItemTaskInfoPO.getOriginalUnitTotalCount().multiply(BigDecimal.valueOf(-1));
            }
        }

        return moveCount;
    }

    /**
     * 订单修改优先级排序（数量按大到小）
     *
     * @return
     */
    public List<OrderItemTaskInfoDetailPO> sortOrderItemTaskInfoDetail(
        List<OrderItemTaskInfoDetailPO> taskInfoDetailPOList, Integer cityId, boolean isAdd) {
        OrderProcessRuleConfigDTO orderProcessRuleConfigDTO = iOrderProcessRuleConfigService
            .findByOrgIdAndOrderType(cityId, ProcessRuleOrderTypeEnum.订单修改还原货位库存.getType(), true);
        if (orderProcessRuleConfigDTO == null) {
            LOG.info("[订单修改还原货位库存]优先级为空！");
            return taskInfoDetailPOList;
        }
        ProcessOrderTypeEnum orderTypeEnum = ProcessOrderTypeEnum.getEnum(orderProcessRuleConfigDTO.getProcessType());
        LOG.info("[订单修改还原货位库存]优先级：{}", orderTypeEnum);
        taskInfoDetailPOList =
            taskInfoDetailPOList.stream()
                .sorted(Comparator.nullsFirst(Comparator
                    .comparing(OrderItemTaskInfoDetailPO::getSecOwnerId, new SecOwnerIdComparator(orderTypeEnum))
                    .thenComparing((o1, o2) -> o2.getUnitTotalCount().compareTo(o1.getUnitTotalCount()))))
                .collect(Collectors.toList());
        return taskInfoDetailPOList;
    }

    /**
     * 记录需要修改的订单项关联拣货任务项数量
     */
    private void recordUpdateOrderItemTaskInfoList(List<OrderItemTaskInfoPO> updateOrderItemTaskInfoList,
        OrderItemTaskInfoPO orderItemTaskInfoPO) {
        // 不需要重复添加
        if (updateOrderItemTaskInfoList.stream()
            .anyMatch(p -> Objects.equals(p.getId(), orderItemTaskInfoPO.getId()))) {
            return;
        }
        updateOrderItemTaskInfoList.add(orderItemTaskInfoPO);
    }

    /**
     * 记录需要修改的订单项关联拣货任务明细项数量
     */
    private void recordUpdateOrderItemTaskInfoDetailList(
        List<OrderItemTaskInfoDetailPO> updateOrderItemTaskInfoDetailList,
        OrderItemTaskInfoDetailPO orderItemTaskInfoDetailPO, BigDecimal changeCount) {
        // 兼容老数据
        if (orderItemTaskInfoDetailPO.getId() == null) {
            return;
        }
        // 不需要重复添加
        if (updateOrderItemTaskInfoDetailList.stream()
            .anyMatch(p -> Objects.equals(p.getId(), orderItemTaskInfoDetailPO.getId()))) {
            OrderItemTaskInfoDetailPO oldDetailPO = updateOrderItemTaskInfoDetailList.stream()
                .filter(p -> Objects.equals(p.getId(), orderItemTaskInfoDetailPO.getId())).findFirst().get();
            oldDetailPO.setUnitTotalCount(oldDetailPO.getUnitTotalCount().add(changeCount));
        } else {
            OrderItemTaskInfoDetailPO detailPO = new OrderItemTaskInfoDetailPO();
            detailPO.setId(orderItemTaskInfoDetailPO.getId());
            detailPO.setTaskInfoId(orderItemTaskInfoDetailPO.getTaskInfoId());
            detailPO.setUnitTotalCount(changeCount);
            updateOrderItemTaskInfoDetailList.add(detailPO);
        }
    }

    /**
     * 记录需要修改数量的拣货任务项
     */
    private void recordUpdateBatchTaskItemList(List<BatchTaskItemPO> updateBatchTaskItemList, BigDecimal changeCount,
        BatchTaskItemPO batchTaskItemPO, Map<Long, Byte> locationSubCategoryMap, ChangeLocationPO changeLocationPO) {
        // 变更的大单位数量和小单位数量
        BigDecimal changePackageCount = changeCount.divideAndRemainder(batchTaskItemPO.getSpecQuantity())[0];
        BigDecimal changeUnitCount = changeCount.divideAndRemainder(batchTaskItemPO.getSpecQuantity())[1];

        // SCM-18124 拣货任务明细的拣货数量，零拣位返回大小件，不返回小件数量
        // if (batchTaskItemPO.getLocationId() != null && locationSubCategoryMap != null
        // && locationSubCategoryMap.containsKey(batchTaskItemPO.getLocationId()) && Objects.equals(
        // locationSubCategoryMap.get(batchTaskItemPO.getLocationId()), LocationEnum.零拣位.getType().byteValue())) {
        // changePackageCount = BigDecimal.ZERO;
        // changeUnitCount = changeCount;
        // }

        // 去重
        if (updateBatchTaskItemList.stream().anyMatch(p -> Objects.equals(p.getId(), batchTaskItemPO.getId()))) {
            BatchTaskItemPO updateBatchTaskItem = updateBatchTaskItemList.stream()
                .filter(p -> Objects.equals(p.getId(), batchTaskItemPO.getId())).findFirst().get();
            updateBatchTaskItem.setUnitTotalCount(updateBatchTaskItem.getUnitTotalCount().add(changeCount));
            updateBatchTaskItem.setPackageCount(updateBatchTaskItem.getPackageCount().add(changePackageCount));
            updateBatchTaskItem.setUnitCount(updateBatchTaskItem.getUnitCount().add(changeUnitCount));
        } else {
            BatchTaskItemPO updateBatchTaskItem = new BatchTaskItemPO();
            updateBatchTaskItem.setId(batchTaskItemPO.getId());
            updateBatchTaskItem.setUnitTotalCount(changeCount);
            updateBatchTaskItem.setPackageCount(changePackageCount);
            updateBatchTaskItem.setUnitCount(changeUnitCount);
            updateBatchTaskItemList.add(updateBatchTaskItem);
        }

        // 记录回退货位
        changeLocationPO.setLocationId(batchTaskItemPO.getLocationId());
        changeLocationPO.setLocationName(batchTaskItemPO.getLocationName());
    }

    /**
     * 记录不需要修改数量，只记录回退数量的拣货任务项
     */
    private void recordAddRollBackBatchTaskItemList(List<BatchTaskItemPO> addBatchTaskItemRollbackCountList,
        OutStockOrderChangeDTO changeDTO, BigDecimal changeCount, BatchTaskItemPO batchTaskItemPO) {
        BigDecimal changePackageCount = changeCount.divideAndRemainder(batchTaskItemPO.getSpecQuantity())[0];
        BigDecimal changeUnitCount = changeCount.divideAndRemainder(batchTaskItemPO.getSpecQuantity())[1];
        // 订单号xxx：退5箱2瓶
        String remark = String.format("%s：(%s)%s%s%s%s；", changeCount.compareTo(BigDecimal.ZERO) < 0 ? "退" : "返",
            changeDTO.getRefOrderNo(), changePackageCount.abs(), batchTaskItemPO.getPackageName(),
            changeUnitCount.abs(), batchTaskItemPO.getUnitName());
        // 去重
        if (addBatchTaskItemRollbackCountList.stream()
            .anyMatch(p -> Objects.equals(p.getId(), batchTaskItemPO.getId()))) {
            BatchTaskItemPO addRollBackBatchTaskItem = addBatchTaskItemRollbackCountList.stream()
                .filter(p -> Objects.equals(p.getId(), batchTaskItemPO.getId())).findFirst().get();
            addRollBackBatchTaskItem
                .setRollbackUnitCount(addRollBackBatchTaskItem.getRollbackUnitCount().add(changeCount.negate()));
            addRollBackBatchTaskItem.setRemark(subRemark(addRollBackBatchTaskItem.getRemark() + remark));
        } else {
            BatchTaskItemPO addRollBackBatchTaskItem = new BatchTaskItemPO();
            addRollBackBatchTaskItem.setId(batchTaskItemPO.getId());
            addRollBackBatchTaskItem.setRollbackUnitCount(changeCount.negate());
            // 获取原备注
            if (StringUtils.isNotEmpty(batchTaskItemPO.getRemark())) {
                remark = batchTaskItemPO.getRemark() + remark;
            }
            addRollBackBatchTaskItem.setRemark(subRemark(remark));
            addBatchTaskItemRollbackCountList.add(addRollBackBatchTaskItem);
        }
    }

    /**
     * 截取备注
     */
    private String subRemark(String remark) {
        if (remark == null) {
            return null;
        }
        if (remark.length() > 255) {
            // 超长截取
            remark = remark.substring(0, 255);
        }
        return remark;
    }

    /**
     * 组装移库数据
     *
     * @return
     */
    private void recordStoreTransferOrderItemList(List<StoreTransferOrderItemDTO> storeTransferOrderItemDTOS,
        BatchTaskItemPO batchTaskItemPO, BigDecimal changeCount, Long sowLocationId, String sowLocationName,
        OutStockOrderItemPO orderItemPO, ChangeLocationPO changeLocationPO, Long secOwnerId,
        WarehouseConfigDTO warehouseConfigDTO) {
        BigDecimal changePackageCount = changeCount.divideAndRemainder(batchTaskItemPO.getSpecQuantity())[0];
        BigDecimal changeUnitCount = changeCount.divideAndRemainder(batchTaskItemPO.getSpecQuantity())[1];

        LocationInfo locationInfo =
            getFromLocationInfo(batchTaskItemPO, sowLocationId, sowLocationName, orderItemPO, warehouseConfigDTO);
        // 出库位
        // 1、优先取集货位
        Long fromLocationId = locationInfo.fromLocationId;
        String fromLocationName = locationInfo.fromLocationName;
        // 2、其次取拣货任务的出库位
        if (fromLocationId == null) {
            fromLocationId = batchTaskItemPO.getToLocationId();
            fromLocationName = batchTaskItemPO.getToLocationName();
        }
        // 3、最后取订单项的出库位
        if (fromLocationId == null) {
            fromLocationId = orderItemPO.getLocationId();
            fromLocationName = orderItemPO.getLocationName();
        }

        // 拣货位
        Long toLocationId = batchTaskItemPO.getLocationId();
        String toLocationName = batchTaskItemPO.getLocationName();

        // 变更数量大于0，表示订单项数量增加，需要从拣货位移到集货位/出库位
        if (changeCount.compareTo(BigDecimal.ZERO) > 0) {
            toLocationId = fromLocationId;
            toLocationName = fromLocationName;
            fromLocationId = batchTaskItemPO.getLocationId();
            fromLocationName = batchTaskItemPO.getLocationName();
        }

        if (fromLocationId == null || toLocationId == null) {
            LOG.info("订单修改移库时找不到货位：{}", JSON.toJSONString(batchTaskItemPO));
            return;
        }

        StoreTransferOrderItemDTO itemDTO = new StoreTransferOrderItemDTO();
        itemDTO.setOrg_id(batchTaskItemPO.getOrgId());
        itemDTO.setSkuId(batchTaskItemPO.getSkuId());
        itemDTO.setFromChannel(String.valueOf(batchTaskItemPO.getChannel()));
        itemDTO.setToChannel(String.valueOf(batchTaskItemPO.getChannel()));
        itemDTO.setFromLocation_id(fromLocationId);
        itemDTO.setFromLocationName(fromLocationName);
        itemDTO.setToLocation_id(toLocationId);
        itemDTO.setToLocationName(toLocationName);
        itemDTO.setOverMovePackageCount(changePackageCount.abs());
        itemDTO.setOverMoveUnitCount(changeUnitCount.abs());
        itemDTO.setSecOwnerId(secOwnerId);
        storeTransferOrderItemDTOS.add(itemDTO);

        // 记录回退货位
        changeLocationPO.setLocationId(toLocationId);
        changeLocationPO.setLocationName(toLocationName);
    }

    private LocationInfo getFromLocationInfo(BatchTaskItemPO batchTaskItemPO, Long sowLocationId,
        String sowLocationName, OutStockOrderItemPO orderItemPO, WarehouseConfigDTO warehouseConfigDTO) {
        LocationInfo defaultLocation = new LocationInfo(sowLocationId, sowLocationName);
        if (!WavesStrategyConstants.PACKAGEREVIEWTYPE_OPEN.equals(warehouseConfigDTO.getRobotLargePick())) {
            return defaultLocation;
        }
        if (!WarehouseConfigConstants.SOW_CONTROL_TYPE_LIQUID_NOT_PACK.equals(warehouseConfigDTO.getSowControlType())) {
            return defaultLocation;
        }

        if (PickingTypeEnum.订单拣货.getType() != batchTaskItemPO.getPickingType()) {
            return defaultLocation;
        }

        if (Objects.nonNull(batchTaskItemPO.getSowTaskId())) {
            return defaultLocation;
        }

        if (batchTaskItemPO.getUnitTotalCount().remainder(batchTaskItemPO.getSpecQuantity())
            .compareTo(BigDecimal.ZERO) != 0) {
            return defaultLocation;
        }

        if (Objects.isNull(batchTaskItemPO.getToLocationId())) {
            return defaultLocation;
        }

        return new LocationInfo(batchTaskItemPO.getToLocationId(), batchTaskItemPO.getToLocationName());
    }

    private static class LocationInfo {
        Long fromLocationId;
        String fromLocationName;

        public LocationInfo(Long fromLocationId, String fromLocationName) {
            this.fromLocationId = fromLocationId;
            this.fromLocationName = fromLocationName;
        }
    }

    /**
     * 组装订单项变更记录对象
     *
     * @return
     */
    private void recordOutStockOrderItemChangeRecordList(
        List<OutStockOrderItemChangeRecordPO> orderItemChangeRecordList, OutStockOrderChangeDTO changeDTO,
        OutStockOrderItemChangeDTO changeItemDTO, OutStockOrderItemPO orderItemPO, BatchTaskItemPO batchTaskItemPO,
        BigDecimal realChangeCount, boolean moveFlag, ChangeLocationPO changeLocationPO) {
        // 去重
        if (orderItemChangeRecordList.stream()
            .anyMatch(p -> Objects.equals(p.getRefOrderItemId(), changeItemDTO.getId())
                && Objects.equals(p.getBatchTaskId(), batchTaskItemPO.getBatchTaskId())
                && Objects.equals(p.getLocationId(), changeLocationPO.getLocationId()))) {
            OutStockOrderItemChangeRecordPO changeRecordPO = orderItemChangeRecordList.stream()
                .filter(p -> Objects.equals(p.getRefOrderItemId(), changeItemDTO.getId())
                    && Objects.equals(p.getBatchTaskId(), batchTaskItemPO.getBatchTaskId())
                    && Objects.equals(p.getLocationId(), changeLocationPO.getLocationId()))
                .findFirst().get();
            changeRecordPO.setUnitTotalCount(changeRecordPO.getUnitTotalCount().add(realChangeCount.abs()));
            changeRecordPO.setPackageCount(
                changeRecordPO.getUnitTotalCount().divideAndRemainder(changeRecordPO.getSpecQuantity())[0]);
            changeRecordPO.setUnitCount(
                changeRecordPO.getUnitTotalCount().divideAndRemainder(changeRecordPO.getSpecQuantity())[1]);
        } else {
            OutStockOrderItemChangeRecordPO changeRecordPO = new OutStockOrderItemChangeRecordPO();
            changeRecordPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.ORDER_ITEM_CHANGE_RECORD));
            changeRecordPO.setOrgId(batchTaskItemPO.getOrgId());
            changeRecordPO.setWarehouseId(batchTaskItemPO.getWarehouseId());
            changeRecordPO.setRefOrderId(changeDTO.getId());
            changeRecordPO.setRefOrderNo(changeDTO.getRefOrderNo());
            changeRecordPO.setRefOrderItemId(changeItemDTO.getId());
            changeRecordPO.setProductSkuId(orderItemPO.getSkuid());
            changeRecordPO.setProductName(orderItemPO.getProductname());
            changeRecordPO.setSpecName(orderItemPO.getSpecname());
            changeRecordPO.setSpecQuantity(orderItemPO.getSpecquantity());
            changeRecordPO.setPackageName(orderItemPO.getPackagename());
            changeRecordPO.setUnitName(orderItemPO.getUnitname());
            changeRecordPO.setUnitTotalCount(realChangeCount.abs());
            changeRecordPO.setPackageCount(
                changeRecordPO.getUnitTotalCount().divideAndRemainder(changeRecordPO.getSpecQuantity())[0]);
            changeRecordPO.setUnitCount(
                changeRecordPO.getUnitTotalCount().divideAndRemainder(changeRecordPO.getSpecQuantity())[1]);
            changeRecordPO.setBatchId(batchTaskItemPO.getBatchId());
            changeRecordPO.setBatchNo(batchTaskItemPO.getBatchNo());
            changeRecordPO.setBatchTaskId(batchTaskItemPO.getBatchTaskId());
            changeRecordPO.setBatchTaskNo(batchTaskItemPO.getBatchTaskNo());
            changeRecordPO.setSowTaskId(changeItemDTO.getSowTaskId());
            changeRecordPO.setSowTaskNo(changeItemDTO.getSowTaskNo());
            changeRecordPO.setChangeType(changeDTO.getChangeType());
            changeRecordPO.setCreateUser(changeDTO.getOperatorUser());
            changeRecordPO.setMoveFlag(moveFlag);
            changeRecordPO.setLocationId(changeLocationPO.getLocationId());
            changeRecordPO.setLocationName(changeLocationPO.getLocationName());
            orderItemChangeRecordList.add(changeRecordPO);
        }
    }

    /**
     * 更新相关波次、拣货任务的信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeBatchUpdate(List<OutStockOrderItemPO> orderItemPOList, List<BatchTaskItemPO> batchTaskItemPOList,
        List<BatchTaskItemPO> updateBatchTaskItemList, List<BatchTaskItemPO> addBatchTaskItemRollbackCountList,
        List<OrderItemTaskInfoPO> updateOrderItemTaskInfoList,
        List<OrderItemTaskInfoDetailPO> updateOrderItemTaskInfoDetailList) {
        if (CollectionUtils.isNotEmpty(updateBatchTaskItemList)) {
            List<String> updateWaitStateBatchTaskItemIdList = new ArrayList<>();
            // 修改原拣货任务项
            updateBatchTaskItemList.forEach(updateItem -> {
                Optional<BatchTaskItemPO> oldBatchTaskItemOption =
                    batchTaskItemPOList.stream().filter(p -> Objects.equals(p.getId(), updateItem.getId())).findFirst();
                if (oldBatchTaskItemOption.isPresent()) {
                    BatchTaskItemPO oldBatchTaskItemPO = oldBatchTaskItemOption.get();
                    // 原拣货任务项为0，修改数量后，需要把状态改为"待拣货"
                    if (oldBatchTaskItemPO.getUnitTotalCount().compareTo(BigDecimal.ZERO) == 0
                        && updateItem.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0) {
                        updateWaitStateBatchTaskItemIdList.add(oldBatchTaskItemPO.getId());
                    }

                    oldBatchTaskItemPO
                        .setUnitTotalCount(oldBatchTaskItemPO.getUnitTotalCount().add(updateItem.getUnitTotalCount()));

                    // 重新计算大小件，不直接减，否则容易出现负数
                    BigDecimal[] count =
                        oldBatchTaskItemPO.getUnitTotalCount().divideAndRemainder(oldBatchTaskItemPO.getSpecQuantity());
                    oldBatchTaskItemPO.setPackageCount(count[0]);
                    oldBatchTaskItemPO.setUnitCount(count[1]);

                    updateItem.setUnitTotalCount(oldBatchTaskItemPO.getUnitTotalCount());
                    updateItem.setPackageCount(oldBatchTaskItemPO.getPackageCount());
                    updateItem.setUnitCount(oldBatchTaskItemPO.getUnitCount());

                    // 记录拣货任务id和波次id，后面统计用
                    updateItem.setBatchTaskId(oldBatchTaskItemPO.getBatchTaskId());
                    updateItem.setBatchId(oldBatchTaskItemPO.getBatchId());
                }
            });

            // （1）找出需要更新的拣货任务
            List<BatchTaskPO> updateBatchTaskList = new ArrayList<>();
            List<String> updateBatchTaskIds =
                updateBatchTaskItemList.stream().map(p -> p.getBatchTaskId()).distinct().collect(Collectors.toList());
            Map<String, List<BatchTaskItemPO>> oldBatchTaskMap =
                batchTaskItemPOList.stream().filter(p -> updateBatchTaskIds.contains(p.getBatchTaskId()))
                    .collect(Collectors.groupingBy(p -> p.getBatchTaskId()));
            oldBatchTaskMap.forEach((batchTaskId, itemList) -> {
                List<BatchTaskItemPO> filterList = itemList.stream()
                    .filter(p -> p.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filterList)) {
                    // 更新拣货任务的数量
                    BatchTaskPO batchTaskPO = new BatchTaskPO();
                    batchTaskPO.setId(batchTaskId);
                    batchTaskPO.setSkuCount(
                        getStatisticsByBatchTaskItems(filterList, WavesStrategyLimitType.skuCountLimitType).intValue());
                    batchTaskPO.setPackageAmount(
                        getStatisticsByBatchTaskItems(filterList, WavesStrategyLimitType.piecePackageNumberLimitType));
                    batchTaskPO.setUnitAmount(
                        getStatisticsByBatchTaskItems(filterList, WavesStrategyLimitType.pieceUnitNumberLimitType));
                    batchTaskPO.setOrderCount(getOrderCount(orderItemPOList, true, batchTaskId));
                    updateBatchTaskList.add(batchTaskPO);
                }
            });

            // （2）找出需要更新的波次
            List<BatchPO> updateBatchList = new ArrayList<>();
            List<String> updateBatchIds =
                updateBatchTaskItemList.stream().map(p -> p.getBatchId()).distinct().collect(Collectors.toList());
            Map<String, List<BatchTaskItemPO>> oldBatchMap =
                batchTaskItemPOList.stream().filter(p -> updateBatchIds.contains(p.getBatchId()))
                    .collect(Collectors.groupingBy(p -> p.getBatchId()));
            oldBatchMap.forEach((batchId, itemList) -> {
                List<BatchTaskItemPO> filterList = itemList.stream()
                    .filter(p -> p.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filterList)) {
                    // 更新波次的数量
                    BatchPO batchPO = new BatchPO();
                    batchPO.setId(batchId);
                    batchPO.setSkuCount(
                        getStatisticsByBatchTaskItems(filterList, WavesStrategyLimitType.skuCountLimitType).intValue());
                    batchPO.setPackageAmount(
                        getStatisticsByBatchTaskItems(filterList, WavesStrategyLimitType.piecePackageNumberLimitType));
                    batchPO.setUnitAmount(
                        getStatisticsByBatchTaskItems(filterList, WavesStrategyLimitType.pieceUnitNumberLimitType));
                    batchPO.setOrderCount(getOrderCount(orderItemPOList, false, batchId));
                    updateBatchList.add(batchPO);
                }
            });

            // （3）找出数量为0的拣货任务项（更新状态为"已拣货"）
            List<String> updateFinishStateBatchTaskItemIdList =
                batchTaskItemPOList.stream().filter(p -> p.getUnitTotalCount().compareTo(BigDecimal.ZERO) == 0)
                    .map(p -> p.getId()).collect(Collectors.toList());

            // （4）执行更新操作
            if (CollectionUtils.isNotEmpty(updateFinishStateBatchTaskItemIdList)) {
                LOG.info("[订单变更]更新拣货任务项状态[已完成]：{}", JSON.toJSONString(updateFinishStateBatchTaskItemIdList));
                batchTaskItemMapper.updateStateByBatchTaskItemId(updateFinishStateBatchTaskItemIdList,
                    TaskItemStateEnum.已完成.getType());
            }
            if (CollectionUtils.isNotEmpty(updateWaitStateBatchTaskItemIdList)) {
                LOG.info("[订单变更]更新拣货任务项状态[待拣货]：{}", JSON.toJSONString(updateWaitStateBatchTaskItemIdList));
                batchTaskItemMapper.updateStateByBatchTaskItemId(updateWaitStateBatchTaskItemIdList,
                    TaskItemStateEnum.未分拣.getType());
            }
            if (CollectionUtils.isNotEmpty(updateBatchTaskItemList)) {
                LOG.info("[订单变更]更新拣货任务详情：{}", JSON.toJSONString(updateBatchTaskItemList));
                batchTaskItemMapper.updateBatchItemProductCountDirect(updateBatchTaskItemList);
            }
            if (CollectionUtils.isNotEmpty(updateBatchTaskList)) {
                LOG.info("[订单变更]更新拣货任务：{}", JSON.toJSONString(updateBatchTaskList));
                batchTaskMapper.updateBatchTaskStatistics(updateBatchTaskList);
            }
            if (CollectionUtils.isNotEmpty(updateBatchList)) {
                LOG.info("[订单变更]更新波次：{}", JSON.toJSONString(updateBatchList));
                batchMapper.updateBatchStatistics(updateBatchList);
            }
        }

        // （5）找出需要更新的订单项和拣货任务项关联
        if (CollectionUtils.isNotEmpty(updateOrderItemTaskInfoList)) {
            LOG.info("[订单变更]更新订单项和拣货任务项关联(OrderItemTaskInfo)：{}", JSON.toJSONString(updateOrderItemTaskInfoList));
            Lists.partition(updateOrderItemTaskInfoList, 50).forEach(p -> {
                orderItemTaskInfoMapper.updateBatch(p);
            });
        }
        if (CollectionUtils.isNotEmpty(updateOrderItemTaskInfoDetailList)) {
            LOG.info("[订单变更]更新订单项和拣货任务项关联明细(OrderItemTaskInfoDetail)：{}",
                JSON.toJSONString(updateOrderItemTaskInfoDetailList));
            orderItemTaskInfoDetailMapper.updateBatchCount(updateOrderItemTaskInfoDetailList);
        }

        // (6) 更新拣货任务详情的回退数量
        if (CollectionUtils.isNotEmpty(addBatchTaskItemRollbackCountList)) {
            LOG.info("[订单变更]更新拣货任务详情回退数量：{}", JSON.toJSONString(addBatchTaskItemRollbackCountList));
            batchTaskItemMapper.updateBatchItemRollBackCount(addBatchTaskItemRollbackCountList);
        }
        LOG.info("缺货处理拣货任务信息等 executeBatchUpdate 当前事务为：{}",
            TransactionSynchronizationManager.getCurrentTransactionName());
        if (CollectionUtils.isEmpty(updateOrderItemTaskInfoList)) {
            return;
        }
        // 这里原来是在下面 的方法 updateOrderItemDetail 中进行查询，但是十分罕见的几率会出现不在同一个事务中情况，导致查出的数据不是最新更新的数据，所以查询暂时放在这里。然后打上日志
        List<Long> orderItemIds = updateOrderItemTaskInfoList.stream().map(OrderItemTaskInfoPO::getRefOrderItemId)
            .distinct().collect(Collectors.toList());
        // (7) 更新波次已完成的订单项detail
        updateOrderItemDetail(orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(orderItemIds));
    }

    /**
     * 更新波次已完成的订单项detail
     */
    public void updateOrderItemDetail(List<OrderItemTaskInfoPO> updateOrderItemTaskInfoList) {
        if (CollectionUtils.isEmpty(updateOrderItemTaskInfoList)) {
            return;
        }
        // 查询波次
        List<String> batchIds =
            updateOrderItemTaskInfoList.stream().map(p -> p.getBatchId()).collect(Collectors.toList());
        List<BatchPO> batchPOS = batchMapper.findBatchByIds(batchIds);
        if (CollectionUtils.isEmpty(batchPOS)) {
            return;
        }
        // 1、找出已完成/已出库的波次id集合
        List<String> pickBatchIds = batchPOS.stream()
            .filter(p -> Objects.equals(p.getState(), BatchStateEnum.PICKINGEND.getType().byteValue())
                || Objects.equals(p.getState(), BatchStateEnum.ALREADYOUTOFSTORE.getType().byteValue()))
            .map(p -> p.getId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickBatchIds)) {
            return;
        }
        // 2、找出已完成拣货的波次关联的订单项
        List<Long> orderItemIds =
            updateOrderItemTaskInfoList.stream().filter(p -> pickBatchIds.contains(p.getBatchId()))
                .map(p -> p.getRefOrderItemId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemIds)) {
            return;
        }
        // 3、根据订单项关联的明细项重新更新订单项detail
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
            getNeedUpdateOrderItemTaskInfo(updateOrderItemTaskInfoList, orderItemIds);
        LOG.info("缺货处理拣货任务信息等 updateOrderItemDetail 当前事务为：{}",
            TransactionSynchronizationManager.getCurrentTransactionName());
        if (CollectionUtils.isNotEmpty(orderItemTaskInfoPOList)) {
            LOG.info("[订单变更][订单变更]更新订单项detail:{}", JSON.toJSONString(orderItemTaskInfoPOList));
            outStockOrderBL.updateOutStockOrderItemDetail(orderItemTaskInfoPOList);
        }
    }

    private List<OrderItemTaskInfoPO> getNeedUpdateOrderItemTaskInfo(List<OrderItemTaskInfoPO> oriOrderItemTaskInfoList,
        List<Long> orderItemIds) {
        try {
            Map<Long, Long> orderItemIdMap = orderItemIds.stream().distinct().collect(Collectors.toMap(k -> k, v -> v));
            List<OrderItemTaskInfoPO> needUpdateOrderItemTaskInfoList = oriOrderItemTaskInfoList.stream()
                .filter(itemTask -> Objects.nonNull(orderItemIdMap.get(itemTask.getRefOrderItemId())))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(needUpdateOrderItemTaskInfoList)) {
                return orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(orderItemIds);
            }

            Map<Long, Long> hasFilteredOrderItemIdMap = needUpdateOrderItemTaskInfoList.stream()
                .map(OrderItemTaskInfoPO::getRefOrderItemId).distinct().collect(Collectors.toMap(k -> k, v -> v));
            List<Long> notInItemIdList = orderItemIds.stream()
                .filter(id -> Objects.isNull(hasFilteredOrderItemIdMap.get(id))).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(notInItemIdList)) {
                return needUpdateOrderItemTaskInfoList;
            }
            List<OrderItemTaskInfoPO> orderItemTaskInfoPOList =
                orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(notInItemIdList);
            if (CollectionUtils.isNotEmpty(orderItemTaskInfoPOList)) {
                needUpdateOrderItemTaskInfoList.addAll(orderItemTaskInfoPOList);
            }
        } catch (Exception e) {
            LOG.error("缺货处理拣货任务信息等 失败，入参：" + JSON.toJSONString(oriOrderItemTaskInfoList) + " ; "
                + JSON.toJSONString(orderItemIds) + "走降级逻辑", e);
        }

        return orderItemTaskInfoMapper.listTaskInfoAndDetailByOrderItemIds(orderItemIds);
    }

    /**
     * 更新波次和拣货任务状态
     */
    private void updateBatchTaskState(List<String> batchIds, String changeTypeText,
        List<OutStockOrderChangeDTO> changeDTOList) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return;
        }
        // 查询波次
        List<BatchPO> batchPOS = batchMapper.findBatchByIds(batchIds);
        if (CollectionUtils.isEmpty(batchPOS)) {
            return;
        }
        handleBatchFinishedPackageOrderItem(changeDTOList, batchPOS);
        // 找出未完成拣货的波次
        batchPOS = batchPOS.stream()
            .filter(p -> !Objects.equals(p.getState(), BatchStateEnum.PICKINGEND.getType().byteValue())
                && !Objects.equals(p.getState(), BatchStateEnum.ALREADYOUTOFSTORE.getType().byteValue()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchPOS)) {
            return;
        }

        // 查询拣货任务详情
        List<BatchTaskItemPO> batchTaskItemPOList = batchTaskItemMapper
            .findBatchTaskItemsByBatchIds(batchPOS.stream().map(p -> p.getId()).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(batchTaskItemPOList)) {
            return;
        }

        // 1、修改拣货任务状态
        List<String> lstUpdateTaskNo = new ArrayList<>();
        // 按拣货任务分组
        Map<String, List<BatchTaskItemPO>> batchTaskMap =
            batchTaskItemPOList.stream().collect(Collectors.groupingBy(p -> p.getBatchTaskId()));
        batchTaskMap.forEach((batchTaskId, batchTaskItemList) -> {
            // 拣货任务对应的所有拣货任务项都已完成，则拣货任务为已完成
            if (batchTaskItemList.stream()
                .allMatch(p -> Objects.equals(p.getTaskState(), TaskStateEnum.已完成.getType()))) {
                lstUpdateTaskNo.add(batchTaskItemList.get(0).getBatchTaskNo());
            }
        });
        if (CollectionUtils.isNotEmpty(lstUpdateTaskNo)) {
            List<BatchTaskPO> updateBatchTaskList = batchTaskMapper.findTasksByBatchTaskNo(lstUpdateTaskNo);
            LOG.info("[订单变更]更新拣货任务状态为已完成：{}", JSON.toJSONString(updateBatchTaskList));
            batchTaskMapper.updateBatchTaskStateCompleteByIds(
                updateBatchTaskList.stream().map(p -> p.getId()).collect(Collectors.toList()));

            // -添加操作记录（任务拣货完成）
            orderTraceBL.updateBatchTaskTraceBatch(updateBatchTaskList, OrderTraceDescriptionEnum.拣货完成.name(),
                changeTypeText);
        }

        // 2、修改波次状态
        batchPOS.forEach(batchPO -> {
            batchFinishedBL.completeWave(batchPO.getBatchNo(), 1, batchPO.getOrgId());
            // _this.updateBatchStateByBatchNo(batchPO.getBatchNo(), changeTypeText, 1);
        });
    }

    private void handleBatchFinishedPackageOrderItem(List<OutStockOrderChangeDTO> changeDTOList,
        List<BatchPO> batchPOS) {
        List<Long> outStockOrderIds =
            changeDTOList.stream().map(OutStockOrderChangeDTO::getId).distinct().collect(Collectors.toList());
        List<OutStockOrderPO> outStockOrderPOS = outStockOrderMapper.findIdByIds(outStockOrderIds, null);
        if (CollectionUtils.isEmpty(outStockOrderPOS)) {
            return;
        }
        Map<Long, String> orderBatchMap =
            outStockOrderPOS.stream().collect(Collectors.toMap(OutStockOrderPO::getId, OutStockOrderPO::getBatchno));
        Map<String, BatchPO> batchPOMap = batchPOS.stream().collect(Collectors.toMap(BatchPO::getBatchNo, v -> v));

        List<Long> orderItemIds = changeDTOList.stream().filter(m -> !CollectionUtils.isEmpty(m.getItemList()))
            .filter(m -> StringUtils.isNotEmpty(orderBatchMap.get(m.getId()))).filter(m -> {
                String batchNo = orderBatchMap.get(m.getId());
                BatchPO batchPO = batchPOMap.get(batchNo);
                return Objects.equals(batchPO.getState(), BatchStateEnum.PICKINGEND.getType().byteValue());
            }).flatMap(m -> m.getItemList().stream()).map(OutStockOrderItemChangeDTO::getId).distinct()
            .collect(Collectors.toList());

        packageOrderItemModBL.batchCompleteModLackPackageOrderItem(orderItemIds);
    }

    /**
     * 获取统计数据
     *
     * @return
     */
    private BigDecimal getStatisticsByBatchTaskItems(List<BatchTaskItemPO> batchTaskItemPOList,
        String wavesStrategyLimitType) {
        BigDecimal totalCount = BigDecimal.valueOf(0);
        if (CollectionUtils.isNotEmpty(batchTaskItemPOList)) {
            switch (wavesStrategyLimitType) {
                // 商品种类
                case WavesStrategyLimitType.skuCountLimitType:
                    totalCount = BigDecimal.valueOf(batchTaskItemPOList.stream().map(p -> p.getSkuId()).distinct()
                        .collect(Collectors.toList()).size());
                    break;
                // 大件总数
                case WavesStrategyLimitType.piecePackageNumberLimitType:
                    totalCount = batchTaskItemPOList.stream().map(p -> p.getPackageCount()).reduce(BigDecimal.ZERO,
                        BigDecimal::add);
                    break;
                // 小件总数
                case WavesStrategyLimitType.pieceUnitNumberLimitType:
                    totalCount = batchTaskItemPOList.stream().map(p -> p.getUnitCount()).reduce(BigDecimal.ZERO,
                        BigDecimal::add);
                    break;
                default:
                    break;
            }
        }
        return totalCount;
    }

    /**
     * 获取订单数量
     *
     * @return
     */
    private Integer getOrderCount(List<OutStockOrderItemPO> orderItemPOList, boolean isBatchTask,
        String batchTaskIdOrBatchId) {
        Integer orderCount = 0;
        if (CollectionUtils.isNotEmpty(orderItemPOList)) {
            // 找出订单数量不为0的订单项
            List<OutStockOrderItemPO> filterList = orderItemPOList.stream()
                .filter(p -> p.getUnittotalcount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterList)) {
                // 拣货任务的订单数量
                if (isBatchTask) {
                    orderCount =
                        filterList.stream().filter(p -> Objects.equals(p.getBatchtaskId(), batchTaskIdOrBatchId))
                            .map(p -> p.getOutstockorderId()).distinct().collect(Collectors.toList()).size();
                    // 波次任务的订单数量
                } else {
                    orderCount = filterList.stream().filter(p -> Objects.equals(p.getBatchId(), batchTaskIdOrBatchId))
                        .map(p -> p.getOutstockorderId()).distinct().collect(Collectors.toList()).size();
                }
            }
        }
        return orderCount;
    }

    /**
     * 获取货位类型集合
     *
     * @return
     */
    private Map<Long, Byte> getLocationSubCategoryMap(List<BatchTaskItemPO> batchTaskItemPOList) {
        if (batchTaskItemPOList == null || CollectionUtils.isEmpty(batchTaskItemPOList)) {
            return Collections.EMPTY_MAP;
        }
        List<Long> locationIds =
            batchTaskItemPOList.stream().map(p -> p.getLocationId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationIds)) {
            return Collections.EMPTY_MAP;
        }
        List<LoactionDTO> loactionDTOList = iLocationService.findLocationByIds(locationIds);
        if (CollectionUtils.isEmpty(loactionDTOList)) {
            return Collections.EMPTY_MAP;
        }
        Map<Long, Byte> returnMap = new HashMap<>();
        loactionDTOList.forEach(p -> {
            returnMap.put(p.getId(), p.getSubcategory());
        });
        return returnMap;
    }

    /**
     * 获取仓库的波次作业设置
     *
     * @return
     */
    public BatchWorkSettingDTO getBatchWorkSetting(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        // 获取贸易伙伴配置
        VariableValueQueryDTO variableValueQuery = new VariableValueQueryDTO();
        variableValueQuery.setWarehouseId(warehouseId);
        variableValueQuery.setVariableKey("PartnerWorkSetting");
        VariableDefAndValueDTO valueDTO = iVariableValueService.detailVariable(variableValueQuery);
        if (valueDTO == null || StringUtils.isEmpty(valueDTO.getVariableData())) {
            return null;
        }
        BatchWorkSettingDTO workSettingDTO =
            JSONObject.parseObject(valueDTO.getVariableData(), BatchWorkSettingDTO.class);
        // 补充货位id
        if (workSettingDTO != null) {
            List<String> locationNameList = new ArrayList<>();
            // 整件收货位
            if (StringUtils.isNotEmpty(workSettingDTO.getLargeLocationName())) {
                locationNameList.add(workSettingDTO.getLargeLocationName());
            }
            // 拆零收货位
            if (StringUtils.isNotEmpty(workSettingDTO.getSmallLocationName())) {
                locationNameList.add(workSettingDTO.getSmallLocationName());
            }
            // 播种库区
            if (StringUtils.isNotEmpty(workSettingDTO.getSowLocationName())) {
                locationNameList.add(workSettingDTO.getSowLocationName());
            }
            // 根据货位名称查货位id
            if (CollectionUtils.isNotEmpty(locationNameList)) {
                List<LoactionDTO> loactionDTOList = iLocationService.getLocationByNames(warehouseId, locationNameList);
                if (CollectionUtils.isNotEmpty(loactionDTOList)) {
                    // 整件收货位
                    if (StringUtils.isNotEmpty(workSettingDTO.getLargeLocationName())) {
                        Optional<LoactionDTO> optional = loactionDTOList.stream()
                            .filter(p -> Objects.equals(p.getName(), workSettingDTO.getLargeLocationName()))
                            .findFirst();
                        if (optional.isPresent()) {
                            workSettingDTO.setLargeLocationId(optional.get().getId());
                        }
                    }
                    // 拆零收货位
                    if (StringUtils.isNotEmpty(workSettingDTO.getSmallLocationName())) {
                        Optional<LoactionDTO> optional = loactionDTOList.stream()
                            .filter(p -> Objects.equals(p.getName(), workSettingDTO.getSmallLocationName()))
                            .findFirst();
                        if (optional.isPresent()) {
                            workSettingDTO.setSmallLocationId(optional.get().getId());
                        }
                    }
                    // 播种库区
                    if (StringUtils.isNotEmpty(workSettingDTO.getSowLocationName())) {
                        Optional<LoactionDTO> optional = loactionDTOList.stream()
                            .filter(p -> Objects.equals(p.getName(), workSettingDTO.getSowLocationName())).findFirst();
                        if (optional.isPresent()) {
                            workSettingDTO.setSowLocationId(optional.get().getId());
                        }
                    }
                }
            }
        }
        return workSettingDTO;
    }

    public Map<String, List<BatchTaskSorterDTO>> listSorterByOrderNos(List<String> orderNos, Integer warehouseId) {
        Map<String, List<BatchTaskSorterDTO>> orderNoSorter = new HashMap<>();
        List<BatchTaskSorterDTO> sorterList = outStockOrderMapper.listSorterByOrderNos(orderNos, warehouseId);
        if (CollectionUtils.isEmpty(sorterList)) {
            return orderNoSorter;
        }
        // 根据orderNo进行分组
        orderNoSorter = sorterList.stream()
            .filter(sorter -> sorter.getSorterId() != null && StringUtils.isNotEmpty(sorter.getSorterName()))
            .collect(Collectors.groupingBy(BatchTaskSorterDTO::getRefOrderNo));
        return orderNoSorter;
    }

    public List<BatchOrderDTO> listBatchStatusByBatchNos(List<String> batchNos, Integer orgId, Integer warehouseId) {
        List<BatchOrderDTO> batchOrderDTOList = new ArrayList<>();
        List<BatchPO> batchPOS = Optional
            .ofNullable(batchMapper.listBatchStatusByBatchNos(batchNos, orgId, warehouseId)).orElse(new ArrayList<>());
        for (BatchPO batchPO : batchPOS) {
            BatchOrderDTO batchOrderDTO = new BatchOrderDTO();
            batchOrderDTO.setBatchNo(batchPO.getBatchNo());
            batchOrderDTO.setBatchState(batchPO.getState());
            batchOrderDTOList.add(batchOrderDTO);
        }
        return batchOrderDTOList;
    }

    /**
     * 根据车次变更修改波次 <br />
     * 没查到调用日志
     */
    @Deprecated
    public void updateBatchByDeliveryTask(BatchUpdateDTO updateDTO) {
        AssertUtils.notNull(updateDTO, "仓库id不能为空");
        AssertUtils.notNull(updateDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(updateDTO.getOrderNos(), "出库单不能为空");
        LOG.info("[根据车次变更更新波次]请求：{}", JSON.toJSONString(updateDTO));
        if (updateDTO.getOrgId() == null) {
            Warehouse warehouseInfo = iWarehouseQueryService.findWarehouseById(updateDTO.getWarehouseId());
            updateDTO.setOrgId(warehouseInfo == null ? null : warehouseInfo.getCityId());
        }
        List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper
            .listOutStockOrderAllByOrderNo(updateDTO.getOrderNos(), updateDTO.getOrgId(), updateDTO.getWarehouseId())
            .stream().filter(ord -> StringUtils.isNotEmpty(ord.getBatchId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outStockOrderPOList)) {
            throw new BusinessException("查不到出库单或出库单没有波次");
        }

        // 2.查询推荐出库位
        List<LocationRuleDTO> locRuleResponse =
            recommendOutLocationBL.getRecommendOutLocation(updateDTO.getWarehouseId(), outStockOrderPOList,
                updateDTO.getDeliveryCarId(), updateDTO.getLogisticsCompanyId());
        if (CollectionUtils.isEmpty(locRuleResponse)) {
            LOG.info("获取不到推荐出库位");
            return;
        }
        LOG.info("[根据车次变更更新波次]查询推荐出库位，响应：{}", JSON.toJSONString(locRuleResponse));
        List<LocationRuleDTO> locRuleList =
            locRuleResponse.stream().filter(rule -> rule.getLocationId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locRuleList)) {
            LOG.info("推荐出库位不含出库位信息");
            return;
        }
        // 查询条件->推荐出库位
        Map<String,
            LocationRuleDTO> locRuleMap = locRuleList.stream()
                .collect(Collectors.toMap(rule -> OutStockLocationHelper.getOutLocationQueryKey(rule,
                    updateDTO.getDeliveryCarId(), updateDTO.getLogisticsCompanyId()), Function.identity(),
                    (k1, k2) -> k1));
        // 出库位ID->出库单分组
        Map<String, List<OutStockOrderPO>> outOrderRuleMap =
            outStockOrderPOList.stream().collect(Collectors.groupingBy(ord -> {
                LocationRuleDTO locRule = locRuleMap.get(ord.getOutLocationQueryKey());
                return String.format("%s", locRule == null ? null : locRule.getLocationId());
            }));
        LOG.info("[根据车次变更更新波次]按推荐出库位分组结果：{}", JSON.toJSONString(outOrderRuleMap));
        outOrderRuleMap.forEach((locationId, outOrderRuleList) -> {
            LocationRuleDTO locRule = locRuleMap.get(outOrderRuleList.get(0).getOutLocationQueryKey());
            if (locRule == null || locRule.getLocationId() == null) {
                LOG.info("[根据车次变更更新波次]当前订单没有查到推荐出库位，订单号：{}", outOrderRuleList.stream()
                    .map(OutStockOrderPO::getReforderno).distinct().collect(Collectors.toList()));
                return;
            }
            BatchTaskUpdateLocationDTO btUpdateLocationDTO = new BatchTaskUpdateLocationDTO();
            btUpdateLocationDTO.setBatchTaskIdList(outOrderRuleList.stream().flatMap(ord -> ord.getItems().stream())
                .map(OutStockOrderItemPO::getBatchtaskId).filter(Objects::nonNull).distinct()
                .collect(Collectors.toList()));
            btUpdateLocationDTO.setOperateUser(updateDTO.getOperateUser());
            btUpdateLocationDTO.setLocationId(locRule.getLocationId());
            btUpdateLocationDTO.setLocationName(locRule.getLocationName());
            batchOrderTaskBL.updateBatchTaskLocationByCk(btUpdateLocationDTO);
        });
    }

    /**
     * 查询波次信息 处理加锁逻辑
     *
     * @param processChangeDTOS
     * @param operatorUser
     * @param changeType
     */
    @Transactional(rollbackFor = Exception.class)
    @DistributeLock(conditions = "#batchNo", sleepMills = 30000, key = RedisConstant.SUP_F + "batchTaskComplete:",
        lockType = DistributeLock.LockType.WAITLOCK, expireMills = 30000)
    public void waitProcessOrderChangeWithBatch(List<OutStockOrderProcessChangeDTO> processChangeDTOS,
        String operatorUser, Byte changeType, String batchNo) {
        if (CollectionUtils.isEmpty(processChangeDTOS)) {
            LOG.warn("processChangeDTOS is null");
            return;
        }
        // 根据城市分组 查询使用orgId分片
        Map<Integer, List<OutStockOrderProcessChangeDTO>> orgIdGroupList =
            processChangeDTOS.stream().collect(Collectors.groupingBy(OutStockOrderProcessChangeDTO::getCityId));
        Set<Integer> orgIdSet = orgIdGroupList.keySet();
        for (Integer orgId : orgIdSet) {
            // 获取当前城市下订单
            List<OutStockOrderProcessChangeDTO> outStockOrderProcessChangeDTOS = orgIdGroupList.get(orgId);
            if (CollectionUtils.isEmpty(outStockOrderProcessChangeDTOS)) {
                LOG.warn("订单信息为空");
                continue;
            }
            List<String> refOrderIdList = outStockOrderProcessChangeDTOS.stream()
                .map(OutStockOrderProcessChangeDTO::getRefOrderNo).collect(Collectors.toList());
            // 根据城市 + refOrderNo查询波次信息
            List<OutStockOrderPO> outStockOrderPOList = outStockOrderMapper.queryBatchNoByOrder(refOrderIdList, orgId);
            if (CollectionUtils.isEmpty(outStockOrderPOList)) {
                LOG.warn("查询波次信息为空");
                continue;
            }
            // 过滤波次为空数据
            List<OutStockOrderPO> batchIsNullOrder = outStockOrderPOList.stream()
                .filter(o -> ObjectUtils.isEmpty(o.getBatchno())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(batchIsNullOrder)) {
                batchIsNullProcessOrderChangeWithBatch(batchIsNullOrder, outStockOrderProcessChangeDTOS, operatorUser,
                    changeType);
            }
            // 波次不为空
            List<OutStockOrderPO> batchNotNullOrder = outStockOrderPOList.stream()
                .filter(o -> !ObjectUtils.isEmpty(o.getBatchno())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(batchNotNullOrder)) {
                batchNotNullProcessOrderChangeWithBatch(batchNotNullOrder, outStockOrderProcessChangeDTOS, operatorUser,
                    changeType);
            }
        }
    }

    /**
     * 波次不为空时
     *
     * @param batchNotNullOrder
     * @param outStockOrderProcessChangeDTOS
     * @param operatorUser
     * @param changeType
     */
    private void batchNotNullProcessOrderChangeWithBatch(List<OutStockOrderPO> batchNotNullOrder,
        List<OutStockOrderProcessChangeDTO> outStockOrderProcessChangeDTOS, String operatorUser, Byte changeType) {
        // 根据波次分组
        Map<String, List<OutStockOrderPO>> collect =
            batchNotNullOrder.stream().collect(Collectors.groupingBy(OutStockOrderPO::getBatchno));
        Set<String> keySet = collect.keySet();
        for (String batchNo : keySet) {
            List<OutStockOrderPO> outStockOrderPOS = collect.get(batchNo);
            // 同一个波次任务下的订单信息
            List<String> batchRefOrderIdList =
                outStockOrderPOS.stream().map(OutStockOrderPO::getReforderno).collect(Collectors.toList());
            // 过滤订单信息
            List<OutStockOrderProcessChangeDTO> filterOrder = outStockOrderProcessChangeDTOS.stream()
                .filter(o -> batchRefOrderIdList.contains(o.getRefOrderNo())).collect(Collectors.toList());
            waitLockProcessOrderChangeWithBatch(filterOrder, operatorUser, changeType, batchNo);
        }
    }

    /**
     * 波次为空的情况处理订单修改
     *
     * @param batchIsNullOrder
     * @param outStockOrderProcessChangeDTOS
     * @param operatorUser
     * @param changeType
     */
    private void batchIsNullProcessOrderChangeWithBatch(List<OutStockOrderPO> batchIsNullOrder,
        List<OutStockOrderProcessChangeDTO> outStockOrderProcessChangeDTOS, String operatorUser, Byte changeType) {
        // 波次为空的订单
        List<String> batchIsNullRefOrderIdList =
            batchIsNullOrder.stream().map(OutStockOrderPO::getReforderno).collect(Collectors.toList());
        // 过滤订单信息
        List<OutStockOrderProcessChangeDTO> batchIsNullOrderList = outStockOrderProcessChangeDTOS.stream()
            .filter(o -> batchIsNullRefOrderIdList.contains(o.getRefOrderNo())).collect(Collectors.toList());
        processOrderChangeWithBatch(batchIsNullOrderList, operatorUser, changeType);
    }

    @DistributeLock(conditions = "#batchNo", sleepMills = 6000, key = "waitLockProcessOrderChangeWithBatch",
        lockType = DistributeLock.LockType.WAITLOCK)
    private void waitLockProcessOrderChangeWithBatch(List<OutStockOrderProcessChangeDTO> filterOrder,
        String operatorUser, Byte changeType, String batchNo) {
        processOrderChangeWithBatch(filterOrder, operatorUser, changeType);
    }

    public PageList<BatchDTO> listSaaSBatchOrderList(BatchQueryDTO batchQueryDTO) {
        AssertUtils.notNull(batchQueryDTO.getWarehouseId(), "仓库id不能为空");
        PageList<BatchDTO> batchOrderList = findBatchOrderList(batchQueryDTO);
        if (null != batchOrderList && !CollectionUtils.isEmpty(batchOrderList.getDataList())) {
            batchOrderList.getDataList().forEach(it -> {
                if (it.getState() != null) {
                    OutStockOrderStateEnum eachState = OutStockOrderStateEnum.getEnum(it.getState());
                    if (null != eachState) {
                        it.setStateText(eachState.name());
                    }
                }
                it.setOrderSelectionText(SaaSOrderSelectionEnum.getType(it.getOrderSelection()));
                it.setPickingTypeText(PickingTypeEnum.getEnumByValue(it.getPickingType()));
                it.setPickingGroupStrategyText(SaaSPickingGroupStrategyEnum.getType(it.getPickingGroupStrategy()));
            });
        }
        return batchOrderList;
    }

    public List<BatchDTO> findByBatchNos(List<String> batchNos, Integer orgId) {
        AssertUtils.notNull(orgId, "组织机构信息不能为空！");
        AssertUtils.notEmpty(batchNos, "波次信息不能为空！");
        List<BatchPO> batchPOS = batchMapper.findBatchByNos(batchNos, orgId);
        return WaveOrderConvertor.batchOrderPOS2BatchOrderDTOS(batchPOS);
    }

    /**
     * 删除订单的货位托盘
     */
    private void deleteOrderLocationPallet(List<BatchTaskPO> batchTaskPOList) {
        if (CollectionUtils.isEmpty(batchTaskPOList)) {
            return;
        }

        List<String> batchTaskIdList = batchTaskPOList.stream().filter(p -> p.getId() != null).map(p -> p.getId())
            .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchTaskIdList)) {
            return;
        }

        OrderLocationPalletQueryDTO deleteDTO = new OrderLocationPalletQueryDTO();
        deleteDTO.setWarehouseId(batchTaskPOList.get(0).getWarehouseId());
        deleteDTO.setBatchTaskIdList(batchTaskIdList);
        orderLocationPalletBL.deletePalletByCondition(deleteDTO);
    }

    /**
     * 波次删除返架产品信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public List<BatchTaskItemDTO> listBatchReturnProductInfo(DeleteBatchDTO deleteBatchDTO) {
        LOG.info("波次删除返架产品信息 入参: {}", JSON.toJSONString(deleteBatchDTO));

        // 查询波次
        List<BatchPO> batchPOList = batchMapper.listByBatchNos(deleteBatchDTO.getBatchNoList());
        // 校验波次能否删除
        batchDeleteVerifyBL.verifyCanDelete(batchPOList, deleteBatchDTO);
        // 需要删除的波次号集合
        List<String> delBatchNoList = batchPOList.stream().map(BatchPO::getBatchNo).collect(Collectors.toList());
        // 查询拣货任务
        List<BatchTaskPO> batchTaskPOList = batchTaskMapper.findTaskByBatchNo(delBatchNoList);
        if (CollectionUtils.isEmpty(batchTaskPOList)) {
            return Collections.emptyList();
        }
        List<String> batchTaskIds = batchTaskPOList.stream()
            .filter(p -> Objects.equals(p.getTaskState(), TaskStateEnum.分拣中.getType())
                || Objects.equals(p.getTaskState(), TaskStateEnum.已完成.getType()))
            .map(p -> p.getId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchTaskIds)) {
            return Collections.emptyList();
        }

        // 查询已完成的拣货任务项
        List<BatchTaskItemPO> taskItemPOS = batchTaskItemMapper.findBatchTaskItemDTOListByTaskIds(batchTaskIds).stream()
            .filter(p -> p != null && Objects.equals(p.getTaskState(), TaskItemStateEnum.已完成.getType()))
            .collect(Collectors.toList());
        LOG.info("波次删除返架产品信息 结果: {}", JSON.toJSONString(taskItemPOS));
        return BatchTaskItemConverter.convertToBatchTaskItemDTO(taskItemPOS);
    }
}
