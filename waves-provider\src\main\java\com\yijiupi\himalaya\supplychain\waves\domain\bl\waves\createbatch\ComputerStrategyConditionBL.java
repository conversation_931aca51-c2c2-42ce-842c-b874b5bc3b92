package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.waves.util.OrderPieceUtils;
import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.WavesStrategyLimitType;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2024/4/29
 */
@Service
public class ComputerStrategyConditionBL {

    public BigDecimal computerStrategyConditionByOrders(List<OutStockOrderPO> lstItemOrders,
        String wavesStrategyLimitType) {
        List<OutStockOrderItemPO> items = new ArrayList<>();
        for (OutStockOrderPO order : lstItemOrders) {
            items.addAll(order.getItems());
        }
        switch (wavesStrategyLimitType) {
            case WavesStrategyLimitType.orderCountLimitType:
                return BigDecimal.valueOf(lstItemOrders.size());
            case WavesStrategyLimitType.skuCountLimitType:
                return BigDecimal.valueOf(items.stream().map(OutStockOrderItemPO::getSkuid).distinct().count());
            case WavesStrategyLimitType.piecePackageNumberLimitType:
                // SCM-18124 拣货任务明细的拣货数量，零拣位返回大小件，不返回小件数量
                return OrderPieceUtils.calcPackageCount(items);
            case WavesStrategyLimitType.pieceUnitNumberLimitType:
                return OrderPieceUtils.calcUnitCount(items);
            case WavesStrategyLimitType.orderAmountLimitType:
                return lstItemOrders.stream().map(OutStockOrderPO::getOrderamount).reduce(BigDecimal.ZERO, BigDecimal::add);
            default:
                return BigDecimal.ZERO;
        }
    }

    public BigDecimal computerStrategyConditionByOrderItems(List<OutStockOrderItemPO> items, String type) {
        switch (type) {
            case WavesStrategyLimitType.orderCountLimitType:
                return BigDecimal.valueOf(items.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getOutstockorderId)).size());
            case WavesStrategyLimitType.skuCountLimitType:
                return BigDecimal.valueOf(items.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid)).size());
            case WavesStrategyLimitType.piecePackageNumberLimitType:
                // SCM-18124 拣货任务明细的拣货数量，零拣位返回大小件，不返回小件数量
                return OrderPieceUtils.calcPackageCount(items);
            case WavesStrategyLimitType.pieceUnitNumberLimitType:
                return OrderPieceUtils.calcUnitCount(items);
            case WavesStrategyLimitType.orderAmountLimitType:
                // TODO: 这里是 break, 是否要实现这个分支
            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 获取单个订单的阈值
     */
    public BigDecimal computerStrategyConditionByOrder(OutStockOrderPO outStockOrderPO, String wavesStrategyLimitType) {
        BigDecimal totalCount = BigDecimal.valueOf(0);
        List<OutStockOrderItemPO> items = outStockOrderPO.getItems();
        switch (wavesStrategyLimitType) {
            case WavesStrategyLimitType.orderCountLimitType:
                totalCount = BigDecimal.valueOf(1);
                break;
            case WavesStrategyLimitType.skuCountLimitType:
                long sum = items.stream().collect(Collectors.groupingBy(OutStockOrderItemPO::getSkuid)).size();
                totalCount = BigDecimal.valueOf(sum);
                break;
            case WavesStrategyLimitType.piecePackageNumberLimitType:
                totalCount = outStockOrderPO.getPackageamount();
                break;
            case WavesStrategyLimitType.pieceUnitNumberLimitType:
                totalCount = outStockOrderPO.getUnitamount();
                break;
            case WavesStrategyLimitType.orderAmountLimitType:
                totalCount = outStockOrderPO.getOrderamount();
                break;
            default:
                break;
        }
        return totalCount;
    }

}
