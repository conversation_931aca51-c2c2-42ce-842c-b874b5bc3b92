package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.secowner.OrderWithItemOwnersItemDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.secowner.OrderWithItemOwnersItemDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDetailDTO;
import com.yijiupi.himalaya.supplychain.waves.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.waves.util.UuidUtil;

public class OutStockOrderItemDetailConverter {

    public static List<OutStockOrderItemDetailPO>
        outStockOrderItemDetailDTOS2OutStockOrderItemDetailPOS(List<OutStockOrderItemDetailDTO> detailDTOS) {
        if (CollectionUtils.isEmpty(detailDTOS)) {
            return null;
        }
        List<OutStockOrderItemDetailPO> detailPOS = new ArrayList<>();
        detailDTOS.forEach(dto -> {
            if (dto.getId() == null) {
                dto.setId(UuidUtil.getUUidInt());
            }
            OutStockOrderItemDetailPO po = new OutStockOrderItemDetailPO();
            BeanUtils.copyProperties(dto, po);

            detailPOS.add(po);
        });
        return detailPOS;
    }

    public static List<OutStockOrderItemDetailDTO> convertToDTO(List<OutStockOrderItemDetailPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return null;
        }
        List<OutStockOrderItemDetailDTO> detailPOS = new ArrayList<>();
        poList.forEach(po -> {
            OutStockOrderItemDetailDTO dto = new OutStockOrderItemDetailDTO();
            BeanUtils.copyProperties(po, dto);

            detailPOS.add(dto);
        });
        return detailPOS;
    }

    public static List<OutStockOrderItemDetailPO> convertNewOutStockOrderItemDetail(OutStockOrderItemPO item) {

        OutStockOrderItemDetailPO detailPO = new OutStockOrderItemDetailPO();

        detailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.OUT_STOCK_ORDER_ITEM_DETAIL));
        detailPO.setOrgId(item.getOrgId());
        detailPO.setOutStockOrderItemId(item.getId());
        detailPO.setLocationId(item.getLocationId());
        detailPO.setLocationName(item.getLocationName());
        detailPO.setBatchTime(item.getBatchTime());
        detailPO.setProductionDate(item.getProductionDate());
        detailPO.setUnitTotalCount(item.getUnittotalcount());
        // detailPO.setOutStockUnitTotalCount(item.getOutStockUnitTotalCount());
        detailPO.setProductSpecificationId(item.getProductSpecificationId());
        detailPO.setOwnerId(item.getOwnerId());
        detailPO.setSecOwnerId(item.getSecOwnerId());

        return Collections.singletonList(detailPO);
    }

    public static List<OutStockOrderItemDetailPO> convertNewOutStockOrderItemDetail(OutStockOrderItemPO item,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {

        Map<String, List<OrderItemTaskInfoDetailPO>> taskInfoDetailMap =
            orderItemTaskInfoPOList.stream().flatMap(m -> m.getDetailList().stream())
                .collect(Collectors.groupingBy(m -> m.getOwnerId() + "-" + m.getSecOwnerId()));

        BigDecimal itemUnitTotalCount = item.getUnittotalcount();

        return convertNewOutStockOrderItemDetail(item, taskInfoDetailMap, itemUnitTotalCount);
    }

    // 创建新的orderItemDetail,用orderItemTaskInfoDetail的原始数量去处理
    public static List<OutStockOrderItemDetailPO> convertNewOutStockOrderItemDetail(OutStockOrderItemPO item,
        Map<String, List<OrderItemTaskInfoDetailPO>> taskInfoDetailMap, BigDecimal itemUnitTotalCount) {

        List<OutStockOrderItemDetailPO> outStockOrderItemDetailPOList = new ArrayList<>();
        for (Map.Entry<String, List<OrderItemTaskInfoDetailPO>> entry : taskInfoDetailMap.entrySet()) {
            List<OrderItemTaskInfoDetailPO> taskInfoDetailPOS = entry.getValue();
            OrderItemTaskInfoDetailPO orderItemTaskInfoDetailPO = taskInfoDetailPOS.get(0);

            // 添加几个0的
            if (itemUnitTotalCount.compareTo(BigDecimal.ZERO) == 0) {
                OutStockOrderItemDetailPO detailPO = new OutStockOrderItemDetailPO();

                detailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.OUT_STOCK_ORDER_ITEM_DETAIL));
                detailPO.setOrgId(item.getOrgId());
                detailPO.setOutStockOrderItemId(item.getId());
                detailPO.setLocationId(item.getLocationId());
                detailPO.setLocationName(item.getLocationName());
                detailPO.setBatchTime(item.getBatchTime());
                detailPO.setProductionDate(item.getProductionDate());

                detailPO.setUnitTotalCount(BigDecimal.ZERO);
                detailPO.setProductSpecificationId(item.getProductSpecificationId());
                detailPO.setOwnerId(orderItemTaskInfoDetailPO.getOwnerId());
                detailPO.setSecOwnerId(orderItemTaskInfoDetailPO.getSecOwnerId());
                outStockOrderItemDetailPOList.add(detailPO);
                continue;
            }

            BigDecimal originalUnitTotalCount =
                taskInfoDetailPOS.stream().map(OrderItemTaskInfoDetailPO::getOriginalUnitTotalCount)
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

            OutStockOrderItemDetailPO detailPO = new OutStockOrderItemDetailPO();

            detailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.OUT_STOCK_ORDER_ITEM_DETAIL));
            detailPO.setOrgId(item.getOrgId());
            detailPO.setOutStockOrderItemId(item.getId());
            detailPO.setLocationId(item.getLocationId());
            detailPO.setLocationName(item.getLocationName());
            detailPO.setBatchTime(item.getBatchTime());
            detailPO.setProductionDate(item.getProductionDate());
            BigDecimal currentUnitTotalCount =
                originalUnitTotalCount.compareTo(itemUnitTotalCount) >= 0 ? itemUnitTotalCount : originalUnitTotalCount;
            itemUnitTotalCount = itemUnitTotalCount.subtract(currentUnitTotalCount);

            detailPO.setUnitTotalCount(currentUnitTotalCount);
            detailPO.setProductSpecificationId(item.getProductSpecificationId());
            detailPO.setOwnerId(orderItemTaskInfoDetailPO.getOwnerId());
            detailPO.setSecOwnerId(orderItemTaskInfoDetailPO.getSecOwnerId());
            outStockOrderItemDetailPOList.add(detailPO);
        }

        return outStockOrderItemDetailPOList;
    }

    /**
     * 处理orderItemDetail存在的场景
     * 
     * @param item
     * @param orderItemTaskInfoPOList
     * @return
     */
    public static List<OutStockOrderItemDetailPO> convertExistOutStockOrderItemDetail(OutStockOrderItemPO item,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {

        Map<String, List<OrderItemTaskInfoDetailPO>> taskInfoDetailMap =
            orderItemTaskInfoPOList.stream().flatMap(m -> m.getDetailList().stream())
                .collect(Collectors.groupingBy(m -> m.getOwnerId() + "-" + m.getSecOwnerId()));

        // 几种场景：1、orderitem的数量跟orderitemtaskinfodetail数量相等；2、orderitem的数量和orderitemtaskinfodetail数量不相等

        BigDecimal orderItemTaskInfoDetailCount =
            orderItemTaskInfoPOList.stream().flatMap(m -> m.getDetailList().stream())
                .map(OrderItemTaskInfoDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 1、orderitem的数量跟orderitemtaskinfodetail数量相等，直接取orderItemTaskInfoDetail的数据，但要注意负数
        if (orderItemTaskInfoDetailCount.compareTo(item.getUnittotalcount()) == 0) {
            return convertOutStockOrderItemDetailWithOrderItemTaskInfoDetail(item, orderItemTaskInfoPOList);
        }

        // 2、orderitem的数量跟orderitemtaskinfodetail数量不相等， 这里要区分 大于 还是 小于了

        return handleTaskInfoCountBiggerAndLessThanOrderItem(item, taskInfoDetailMap);
    }

    /**
     * 这里都和orderItemTaskInfoDetail的原始数量比的，所以大小无所谓了
     * 
     * @param item
     * @param taskInfoDetailMap
     * @return
     */
    public static List<OutStockOrderItemDetailPO> handleTaskInfoCountBiggerAndLessThanOrderItem(
        OutStockOrderItemPO item, Map<String, List<OrderItemTaskInfoDetailPO>> taskInfoDetailMap) {
        List<OutStockOrderItemDetailPO> itemDetailPOList = item.getItemDetails();

        BigDecimal itemUnitTotalCount = item.getUnittotalcount();

        List<OutStockOrderItemDetailPO> updateOutStockOrderItemDetailPOList = new ArrayList<>();
        for (OutStockOrderItemDetailPO detailPO : itemDetailPOList) {
            List<OrderItemTaskInfoDetailPO> taskInfoDetailPOS =
                taskInfoDetailMap.get(detailPO.getOwnerId() + "-" + detailPO.getSecOwnerId());
            // FIXME OrderItemTaskInfoDetailPO 的 OriginalUnitTotalCount 有可能是后来新增的，数量为0。需处理这种场景
            if (Objects.nonNull(taskInfoDetailPOS)) {
                BigDecimal originalUnitTotalCount = taskInfoDetailPOS.stream()
                        .map(OrderItemTaskInfoDetailPO::getOriginalUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal currentUnitTotalCount =
                        originalUnitTotalCount.compareTo(itemUnitTotalCount) >= 0 ? itemUnitTotalCount : originalUnitTotalCount;
                itemUnitTotalCount = itemUnitTotalCount.subtract(currentUnitTotalCount);

                detailPO.setUnitTotalCount(currentUnitTotalCount);
                updateOutStockOrderItemDetailPOList.add(detailPO);
                taskInfoDetailMap.remove(detailPO.getOwnerId() + "-" + detailPO.getSecOwnerId());
            } else {
                detailPO.setUnitTotalCount(BigDecimal.ZERO);
                updateOutStockOrderItemDetailPOList.add(detailPO);
            }
        }

        if (org.springframework.util.CollectionUtils.isEmpty(taskInfoDetailMap)) {
            return updateOutStockOrderItemDetailPOList;
        }

        if (itemUnitTotalCount.compareTo(BigDecimal.ZERO) == 0) {
            return updateOutStockOrderItemDetailPOList;
        }

        List<OutStockOrderItemDetailPO> newList =
            convertNewOutStockOrderItemDetail(item, taskInfoDetailMap, itemUnitTotalCount);
        if (CollectionUtils.isNotEmpty(newList)) {
            updateOutStockOrderItemDetailPOList.addAll(newList);
        }

        return updateOutStockOrderItemDetailPOList;
    }

    /**
     * 处理 订单项数量和 orderItemTaskInfoDetail和的数量相等的情况
     * 
     * @param item
     * @param orderItemTaskInfoPOList
     * @return
     */
    public static List<OutStockOrderItemDetailPO> convertOutStockOrderItemDetailWithOrderItemTaskInfoDetail(
        OutStockOrderItemPO item, List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {

        List<OrderItemTaskInfoPO> positiveOrderItemTaskInfoPOList =
            handleNegativeTaskInfoDetail(orderItemTaskInfoPOList);

        Map<String, List<OrderItemTaskInfoDetailPO>> taskInfoDetailMap =
            positiveOrderItemTaskInfoPOList.stream().flatMap(m -> m.getDetailList().stream())
                .collect(Collectors.groupingBy(m -> m.getOwnerId() + "-" + m.getSecOwnerId()));

        Map<String, OutStockOrderItemDetailPO> itemDetailPOMap = item.getItemDetails().stream()
            .collect(Collectors.toMap(m -> m.getOwnerId() + "-" + m.getSecOwnerId(), v -> v));

        List<OutStockOrderItemDetailPO> outStockOrderItemDetailPOS = new ArrayList<>();
        for (Map.Entry<String, List<OrderItemTaskInfoDetailPO>> entry : taskInfoDetailMap.entrySet()) {
            BigDecimal currentUnitTotalCount = entry.getValue().stream()
                .map(OrderItemTaskInfoDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            OutStockOrderItemDetailPO detailPO = itemDetailPOMap.get(entry.getKey());
            if (Objects.isNull(detailPO)) {
                detailPO = new OutStockOrderItemDetailPO();
                OrderItemTaskInfoDetailPO orderItemTaskInfoDetailPO = entry.getValue().get(0);

                detailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.OUT_STOCK_ORDER_ITEM_DETAIL));
                detailPO.setOrgId(item.getOrgId());
                detailPO.setOutStockOrderItemId(item.getId());
                detailPO.setLocationId(item.getLocationId());
                detailPO.setLocationName(item.getLocationName());
                detailPO.setBatchTime(item.getBatchTime());
                detailPO.setProductionDate(item.getProductionDate());

                detailPO.setUnitTotalCount(currentUnitTotalCount);
                detailPO.setProductSpecificationId(item.getProductSpecificationId());
                detailPO.setOwnerId(orderItemTaskInfoDetailPO.getOwnerId());
                detailPO.setSecOwnerId(orderItemTaskInfoDetailPO.getSecOwnerId());
                outStockOrderItemDetailPOS.add(detailPO);
            } else {
                detailPO.setUnitTotalCount(currentUnitTotalCount);
                outStockOrderItemDetailPOS.add(detailPO);
            }

        }

        return outStockOrderItemDetailPOS;
    }

    // 处理数量为负的场景
    public static List<OrderItemTaskInfoPO>
        handleNegativeTaskInfoDetail(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList) {
        List<OrderItemTaskInfoDetailPO> positiveDetailPOList = orderItemTaskInfoPOList.stream()
            .flatMap(m -> m.getDetailList().stream())
            .filter(detail -> detail.getUnitTotalCount().compareTo(BigDecimal.ZERO) >= 0).collect(Collectors.toList());
        // 都小于零，返回空
        if (CollectionUtils.isEmpty(positiveDetailPOList)) {
            return Collections.emptyList();
        }
        // 一部分小于零，则把小于零的置为0，大于零的 和 小于零的相加
        List<OrderItemTaskInfoDetailPO> negativeDetailPOList = orderItemTaskInfoPOList.stream()
            .flatMap(m -> m.getDetailList().stream())
            .filter(detail -> detail.getUnitTotalCount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
        // 取绝对值
        BigDecimal negativeUnitTotalCount = negativeDetailPOList.stream()
            .map(OrderItemTaskInfoDetailPO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add).abs();
        // 小于零的数量 大于 大于零的数量
        // TODO 暂不处理，还没遇到过这种场景
        // 小于零的数量 小于 大于零的数量
        for (OrderItemTaskInfoDetailPO detailPO : positiveDetailPOList) {
            if (detailPO.getUnitTotalCount().compareTo(negativeUnitTotalCount) >= 0) {
                detailPO.setUnitTotalCount(detailPO.getUnitTotalCount().subtract(negativeUnitTotalCount));
                break;
            } else {
                negativeUnitTotalCount = negativeUnitTotalCount.subtract(detailPO.getUnitTotalCount());
                detailPO.setUnitTotalCount(BigDecimal.ZERO);
            }
        }

        negativeDetailPOList.forEach(detail -> {
            detail.setUnitTotalCount(BigDecimal.ZERO);
        });

        return orderItemTaskInfoPOList;
    }

    public static List<OutStockOrderItemDetailPO> convertOutStockOrderItemDetailWithOrderCenterDetail(
        OrderWithItemOwnersItemDTO orderWithItemOwnersItemDTO, OutStockOrderItemPO item) {
        List<OrderWithItemOwnersItemDetailDTO> orderCenterItemDetailList =
            orderWithItemOwnersItemDTO.getOrderItemOwners();

        List<OutStockOrderItemDetailPO> itemDetailPOList = new ArrayList<>();
        for (OrderWithItemOwnersItemDetailDTO detailDTO : orderCenterItemDetailList) {
            OutStockOrderItemDetailPO detailPO = new OutStockOrderItemDetailPO();
            detailPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.OUT_STOCK_ORDER_ITEM_DETAIL));
            detailPO.setOrgId(item.getOrgId());
            detailPO.setOutStockOrderItemId(item.getId());
            detailPO.setLocationId(item.getLocationId());
            detailPO.setLocationName(item.getLocationName());
            detailPO.setBatchTime(item.getBatchTime());
            detailPO.setProductionDate(item.getProductionDate());

            detailPO.setUnitTotalCount(detailDTO.getCount());
            detailPO.setProductSpecificationId(item.getProductSpecificationId());
            detailPO.setOwnerId(detailDTO.getOwnerId());
            detailPO.setSecOwnerId(detailDTO.getSecOwnerId());
            itemDetailPOList.add(detailPO);
        }

        return itemDetailPOList;
    }
}
