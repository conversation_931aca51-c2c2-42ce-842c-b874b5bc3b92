package com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskFinishHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batchtask.bo.BatchTaskItemFinishBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderconstraint.OrderConstraintCheckBL;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.BatchTaskPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskCheckSaleSpecDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.BatchTaskItemCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2024/1/25
 */
@Service
public class BatchTaskFinishValidateBL {

    @Autowired
    private OutStockOrderMapper outStockOrderMapper;

    @Resource
    private OrderConstraintCheckBL orderConstraintCheckBL;

    protected static final Logger LOGGER = LoggerFactory.getLogger(BatchTaskFinishValidateBL.class);

    protected void validate(BatchTaskFinishHelperBO batchTaskFinishHelperBO) {
        BatchTaskItemFinishBO batchTaskItemFinishBO = batchTaskFinishHelperBO.getBatchTaskItemFinishBO();
        List<BatchTaskItemCompleteDTO> batchTaskItemUpdateDTOList =
            batchTaskItemFinishBO.getBatchTaskItemCompleteDTOS();

        if (batchTaskFinishHelperBO.getPickedBatchTaskItemList().size() != batchTaskItemUpdateDTOList.size()) {
            throw new BusinessValidateException("部分拣货详情不存在，请刷新重试！");
        }
        BatchTaskPO batchTaskPO = batchTaskFinishHelperBO.getBatchTaskPO();
        if (batchTaskPO.getTaskState() == TaskStateEnum.已完成.getType()) {
            throw new BusinessValidateException("拣货已完成，请勿重复操作！Id：" + batchTaskItemFinishBO.getBatchTaskId());
        }
        // 1111111为机器人拣货
        if (!RobotSorterConstants.ROBOT_ID.equals(batchTaskPO.getSorterId())) {
            if (!Objects.equals(batchTaskItemFinishBO.getUserId(), batchTaskPO.getSorterId())) {
                throw new BusinessValidateException("提交失败，此拣货任务已经指派给拣货员：" + batchTaskPO.getSorter());
            }
        }
        AssertUtils.notNull(batchTaskFinishHelperBO.getBatchPO(), "拣货任务对应的波次任务不存在！Id：" + batchTaskPO.getId());

        Long locationId = batchTaskItemFinishBO.getLocationId();
        // 前端传过来的出库位跟拣货任务出库位不一样时，提示报错
        if (batchTaskPO.getToLocationId() != null && locationId != null) {
            if (!Objects.equals(batchTaskPO.getToLocationId(), locationId)) {
                LOGGER.info("拣货任务的出库位已变更, ToLocationId: {}, ToLocationName: {}, locationId: {}, locationName: {}",
                    batchTaskPO.getToLocationId(), batchTaskPO.getToLocationName(), locationId,
                    batchTaskItemFinishBO.getLocationName());
                throw new BusinessValidateException("拣货任务的出库位已变更，请回退到待拣货列表重试！");
            }
        }

        List<BatchTaskItemDTO> batchTaskItemPOList = batchTaskFinishHelperBO.getTotalBatchTaskItemList();
        Map<String, BatchTaskItemDTO> batchTaskItemPOMap =
            batchTaskItemPOList.stream().collect(Collectors.toMap(BatchTaskItemDTO::getId, v -> v));

        Map<String, BatchTaskItemCompleteDTO> map =
            batchTaskItemUpdateDTOList.stream().collect(Collectors.toMap(BatchTaskItemCompleteDTO::getId, v -> v));

        for (BatchTaskItemDTO batchTaskItemDTO : batchTaskItemPOList) {
            BatchTaskItemCompleteDTO dto = map.get(batchTaskItemDTO.getId());
            if (Objects.isNull(dto)) {
                continue;
            }
            // 初次提交时，如果拣货任务项状态不是“未分拣”，则报异常
            // TODO 老代码 这里直接用枚举比较的
            if (Objects.equals(dto.getSubmitFlag(), SubmitFlagEnum.初次提交.getType())
                && !Objects.equals(batchTaskItemDTO.getTaskState(), TaskStateEnum.未分拣.getType())) {
                throw new BusinessValidateException("不能重复提交，请刷新重试或者联系技术支持！");
            }

            BigDecimal specQuantity = batchTaskItemDTO.getSpecQuantity();
            // 数据库中拣货数量+用户拣货数量
            BigDecimal overSortCount = BatchTaskFinishHelperBO.getOverSortUnitTotalCount(dto, batchTaskItemDTO);

            // 数据库中缺省数量+用户缺省数量
            BigDecimal lackCount = dto.getLackPackageCount().multiply(specQuantity).add(dto.getLackUnitCount());
            if (overSortCount.add(lackCount).compareTo(batchTaskItemDTO.getUnitTotalCount()) != 0) {
                LOGGER.info("已拣货数量 加 缺货数量 不等于 待拣货数量: {}", JSON.toJSONString(batchTaskItemDTO));
                throw new BusinessValidateException("拣货数量有误，请返回拣货任务列表刷新重试！");
            }
        }

        if (Objects.equals(batchTaskItemFinishBO.getContainerFlag(), ConditionStateEnum.是.getType())) {
            batchTaskItemFinishBO.getBatchTaskItemCompleteDTOS().forEach(dto -> {
                if (CollectionUtils.isEmpty(dto.getContainerList())) {
                    throw new BusinessException("容器位不能为空");
                }
            });
        }

        List<BatchTaskCheckSaleSpecDTO> checkSaleSpecDTOList = batchTaskItemUpdateDTOList.stream().map(item -> {
            BatchTaskItemDTO batchTaskItemDTO = batchTaskItemPOMap.get(item.getId());
            BigDecimal overSortCount = BatchTaskFinishHelperBO.getOverSortUnitTotalCount(item, batchTaskItemDTO);

            return getCheckSaleSpec(batchTaskItemDTO, overSortCount);
        }).collect(Collectors.toList());
        // 校验销售规格
        checkSaleSpec(checkSaleSpecDTOList);

        validateCouldLack(batchTaskItemUpdateDTOList, batchTaskFinishHelperBO.getBatchPO());
    }

    public void validateIsMeiTuan(List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, List<BatchTaskPO> batchTaskPOList) {
        LOGGER.info("OrderItemTaskInfoPO缺货信息为:{}", JSON.toJSONString(orderItemTaskInfoPOList));
        List<OrderItemTaskInfoPO> lackOrderItemTaskInfoList = orderItemTaskInfoPOList.stream()
            .filter(m -> m.getLackUnitCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lackOrderItemTaskInfoList)) {
            return;
        }
        Map<String, BatchTaskPO> orderBatchTaskMap = batchTaskPOList.stream()
                .filter(batchTask -> PickingTypeEnum.订单拣货.getType() == batchTask.getPickingType())
                .collect(Collectors.toMap(BatchTaskPO::getId, v -> v));
        if (org.springframework.util.CollectionUtils.isEmpty(orderBatchTaskMap)) {
            return;
        }
        lackOrderItemTaskInfoList = lackOrderItemTaskInfoList.stream()
            .filter(m -> Objects.nonNull(orderBatchTaskMap.get(m.getBatchTaskId()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lackOrderItemTaskInfoList)) {
            return;
        }

        List<Long> refOrderIds = lackOrderItemTaskInfoList.stream().map(OrderItemTaskInfoPO::getRefOrderId).distinct()
            .collect(Collectors.toList());
        List<OutStockOrderPO> orderList = outStockOrderMapper.findByOrderIds(refOrderIds);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        orderConstraintCheckBL.checkSaleInventory(orderList, orderItemTaskInfoPOList);
    }

    private BatchTaskCheckSaleSpecDTO getCheckSaleSpec(BatchTaskItemDTO batchTaskItemDTO, BigDecimal overSortCount) {
        BatchTaskCheckSaleSpecDTO batchTaskCheckSaleSpecDTO = new BatchTaskCheckSaleSpecDTO();
        batchTaskCheckSaleSpecDTO.setSkuId(batchTaskItemDTO.getSkuId());
        batchTaskCheckSaleSpecDTO.setRefOrderId(batchTaskItemDTO.getRefOrderId());
        batchTaskCheckSaleSpecDTO.setSaleSpecQuantity(batchTaskItemDTO.getSaleSpecQuantity());
        batchTaskCheckSaleSpecDTO.setOverSortUnitTotolCount(overSortCount);
        batchTaskCheckSaleSpecDTO.setUnitTotolCount(batchTaskItemDTO.getUnitTotalCount());
        return batchTaskCheckSaleSpecDTO;
    }

    private void checkSaleSpec(List<BatchTaskCheckSaleSpecDTO> checkSaleSpecDTOList) {
        Map<String, List<BatchTaskCheckSaleSpecDTO>> checkSaleSpecMap = checkSaleSpecDTOList.stream().collect(
            Collectors.groupingBy(p -> p.getSkuId() + "_" + p.getRefOrderId() + "_" + p.getSaleSpecQuantity()));
        if (null == checkSaleSpecMap) {
            return;
        }
        checkSaleSpecMap.forEach((k, list) -> {
            BigDecimal overSortUnitTotolCount =
                list.stream().map(p -> p.getOverSortUnitTotolCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal unitTotolCount =
                list.stream().map(p -> p.getUnitTotolCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal saleSpecQuantity = list.get(0).getSaleSpecQuantity();
            /*
             * 同时满足以下条件才需要检验：
             *  (1)拣货任务数量 > 销售规格
             *  (2)拣货任务数量 != 已拣数量
             *  (3)已拣数量不是销售规格倍数
             */
            if (unitTotolCount.compareTo(saleSpecQuantity) >= 0 && unitTotolCount.compareTo(overSortUnitTotolCount) != 0
                && overSortUnitTotolCount.divideAndRemainder(saleSpecQuantity)[1].compareTo(BigDecimal.ZERO) != 0) {
                throw new BusinessValidateException("已拣货数量不是销售规格的倍数！");
            }
        });
    }

    private void validateCouldLack(List<BatchTaskItemCompleteDTO> batchTaskItemCompleteDTOList, BatchPO batchPO) {
        boolean isPackageLack =
            batchTaskItemCompleteDTOList.stream().filter(m -> Objects.nonNull(m.getLackPackageCount()))
                .anyMatch(m -> BigDecimal.ZERO.compareTo(m.getLackPackageCount()) != 0);
        boolean isUnitLack = batchTaskItemCompleteDTOList.stream().filter(m -> Objects.nonNull(m.getLackUnitCount()))
            .anyMatch(m -> BigDecimal.ZERO.compareTo(m.getLackUnitCount()) != 0);

        if (Objects.equals(batchPO.getBatchType(), BatchTypeEnum.酒批.getType())) {
            return;
        }

        if (isPackageLack || isUnitLack) {
            throw new BusinessValidateException("当前订单不能做缺货拣货，无货时请通知客户取消订单！");
        }

    }

}
