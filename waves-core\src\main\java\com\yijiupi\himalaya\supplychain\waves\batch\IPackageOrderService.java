package com.yijiupi.himalaya.supplychain.waves.batch;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.*;
import com.yijiupi.himalaya.supplychain.waves.search.PackageOrderItemSO;

import java.util.List;
import java.util.Map;

/**
 * 二次包装
 *
 * <AUTHOR>
 * @since 2018/7/13 10:20
 */
public interface IPackageOrderService {
    /**
     * 根据出库单号查询包装信息详情
     */
    PageList<PackageOrderItemDTO> listPackageOrderItem(PackageOrderItemSO packageOrderItemSO);

    /**
     * 新增包装详情
     */
    void savePackage(PackageOrderItemDTO packageOrderItemDTO);

    /**
     * 批量新增包装详情
     */
    void savePackageBatch(List<PackageOrderItemDTO> packageOrderItemDTOList);

    /**
     * 批量新增包装详情(PDA)
     */
    void savePackageBatchByPDA(List<PackageOrderItemDTO> packageOrderItemDTOList);

    /**
     * 根据订单号获取打印子箱条码信息
     */
    List<PackageCodePrintDTO> listPackageCodePrint(String refOrderNo, Integer cityId, Integer warehouseId);

    /**
     * 根据订单号获取打印子箱条码信息
     */
    List<PackageCodePrintDTO> findPackageCodePrint(PackageCodePrintQueryParam queryParam);

    /**
     * 根据播种任务编号获取打印子箱条码信息
     */
    List<PackageCodePrintDTO> listPackageCodePrintBySowTaskNo(Integer orgId, String sowTaskNo);

    /**
     * 根据订单编号获取打印子箱条码信息
     */
    List<PackageCodePrintDTO> listPackageCodePrintByOrderNos(Integer orgId, Integer warehouseId, List<String> orderNos);

    /**
     * 批量新增包装详情
     */
    String savePackageBatchByOrderId(PackageOrderSaveDTO packageOrderSaveDTO);

    /**
     * 根据箱号完整编码查询内配单号
     */
    List<String> listOrderNoByBoxCodeNo(String boxCodeNo);

    /**
     * 扫码找单(根据号码判断是箱码或订单号)
     */
    PackageOrderDTO getPackageOrderByBarCode(Integer orgId, Integer warehouseId, String barCode);

    /**
     * 根据出库单号查询包装信息详情(不做状态校验)
     */
    PageList<PackageOrderItemDTO> listPackageOrderItemAll(PackageOrderItemSO packageOrderItemSO);

    /**
     * 根据单号查询已播种的包装信息
     */
    Map<String, List<PackageOrderItemDTO>> listSowPackageOrderItems(List<String> orderNos, Integer orgId,
        Integer warehouseId);

    /**
     * 整拖复核列表查询
     */
    PageList<PalletOrderDTO> pageListPalletOrders(PalletOrderQueryDTO palletOrderQueryDTO);

    /**
     * 整拖复核明细查询
     */
    List<PalletOrderItemDTO> listPalletOrderItems(PalletOrderQueryDTO palletOrderQueryDTO);

    /**
     * 整拖复核
     */
    void packageOrderItemReview(List<PackageOrderItemDTO> packageOrderItemDTOS);

    /**
     * 根据订单编号删除包装信息
     */
    void removePackageByRefOrderNos(List<String> refOrderNos, Integer orgId, Integer warehouseId);

    /**
     * 删除包装信息
     */
    void removePackageInfo(RemovePackageInfoParam param);

    /**
     * 批量获取订单打包信息
     */
    List<PackageOrderItemDTO> listPackageOrderItemByOrderIdList(PackageOrderItemSO packageOrderItemSO);

    /**
     * 根据订单号 城市id 获取最大箱数
     */
    List<PackageOrderItemDTO> getBoxMaxCountByOrderIdList(PackageOrderItemSO packageOrderItemSO);

    /**
     * 通过订单号查询装箱信息
     *
     * @param param 查询参数
     * @return 装箱信息
     */
    PackageOrderInfoDTO findPackageOrderByOrderNo(PackageOrderItemSO param);

    /**
     * 获取订单打包信息, 给客户端 订单详情页面使用<br/>
     * 其中的 refOrderItemId 将会被替换成 businessItemId
     */
    List<PackageOrderItemDTO> queryPackageOrderItem(PackageOrderItemSO packageOrderItemSO);

    /**
     * 修改包装箱信息工具
     *
     * @param dto
     */
    void modLackPackageOrderItemByOutBoundInfo(ModPackageInfoByOutBoundInfoDTO dto);

    /**
     * 重新同步包装箱信息工具
     *
     * @param dto
     */
    void syncPackageOrderItemByOutBoundInfo(ModPackageInfoByOutBoundInfoDTO dto);

    /**
     * 出库时如果打包信息有问题，重新计算
     *
     * @param dto
     */
    void resetPackageOrderItemBeforeOutBound(ResetPackageOrderItemBeforeOutBoundDTO dto);

    /**
     * 扫描订单箱号查询包装箱数
     *
     * @param dto
     * @return
     */
    List<ScanOrderPackageInfoResultDTO> scanOrderPackageInfo(ScanOrderPackageInfoQueryDTO dto);

    /**
     * 删除包装箱信息
     * 
     * @param dto
     */
    void removePackage(ModPackageInfoByOutBoundInfoDTO dto);

    /**
     * 修改
     * 
     * @param dto
     */
    void sendModPackage(ModPackageInfoByOutBoundInfoDTO dto);
}
