package com.yijiupi.himalaya.supplychain.waves.dto.soworder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

public class SowOrderItemDTO implements Serializable {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 播种订单序号
     */
    private Integer sowOrderSequence;

    /**
     * 箱号
     */
    private String boxCodeNo;

    /**
     * 播种完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeTime;

    /**
     * 播种大件数量
     */
    private BigDecimal sownPackageCount;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 播种小件数量
     */
    private BigDecimal sownUnitCount;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 播种人
     */
    private String operatorName;

    /**
     * 出库位
     */
    private String locationName;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getSowOrderSequence() {
        return sowOrderSequence;
    }

    public void setSowOrderSequence(Integer sowOrderSequence) {
        this.sowOrderSequence = sowOrderSequence;
    }

    public String getBoxCodeNo() {
        return boxCodeNo;
    }

    public void setBoxCodeNo(String boxCodeNo) {
        this.boxCodeNo = boxCodeNo;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public BigDecimal getSownPackageCount() {
        return sownPackageCount;
    }

    public void setSownPackageCount(BigDecimal sownPackageCount) {
        this.sownPackageCount = sownPackageCount;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getSownUnitCount() {
        return sownUnitCount;
    }

    public void setSownUnitCount(BigDecimal sownUnitCount) {
        this.sownUnitCount = sownUnitCount;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
