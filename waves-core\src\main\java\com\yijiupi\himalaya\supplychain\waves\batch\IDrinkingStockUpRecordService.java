package com.yijiupi.himalaya.supplychain.waves.batch;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.waves.dto.stockup.StockUpCompleteDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.stockup.StockUpRecordDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.stockup.StockUpRecordQueryParam;

/**
 * <AUTHOR>
 * @since 2024-10-11 16:20
 **/
public interface IDrinkingStockUpRecordService {

    /**
     * 完成备货
     *
     * @param param 完成参数
     */
    void completeStockUp(StockUpCompleteDTO param);

    /**
     * 新增或更新备货记录
     *
     * @param stockUpRecord 备货记录
     */
    void addOrUpdateStockUpRecord(StockUpRecordDTO stockUpRecord);

    /**
     * 查询备货记录
     *
     * @param param 备货记录查询参数
     * @return 查询结果
     */
    PageList<StockUpRecordDTO> pageListStockUpRecord(StockUpRecordQueryParam param);
}
