package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.createbatch;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.outstock.dto.orderexception.WMSOrderExceptionDTO;
import com.yijiupi.himalaya.supplychain.outstock.dto.orderexception.WMSOrderExceptionQueryDTO;
import com.yijiupi.himalaya.supplychain.outstock.enums.WMSOrderExceptionStateEnums;
import com.yijiupi.himalaya.supplychain.outstock.service.IWMSOrderExceptionService;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.OutStockOrderStateEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/21
 */
@Service
public class CreateBatchValidateBL {

    @Reference
    private IWMSOrderExceptionService iwmsOrderExceptionService;

    protected static final Logger LOG = LoggerFactory.getLogger(CreateBatchValidateBL.class);

    protected List<OutStockOrderPO> filterOutStockOrder(List<OutStockOrderPO> outStockOrderPOList) {
        // 过滤出在异常订单中的订单
        WMSOrderExceptionQueryDTO queryDTO = new WMSOrderExceptionQueryDTO();
        queryDTO.setOrderIds(
            outStockOrderPOList.stream().map(OutStockOrderPO::getId).distinct().collect(Collectors.toList()));
        queryDTO.setWarehouseId(outStockOrderPOList.get(0).getWarehouseId());
        queryDTO.setStateList(Collections.singletonList(WMSOrderExceptionStateEnums.STATE_WAIT));
        List<WMSOrderExceptionDTO> exceptionDTOList = iwmsOrderExceptionService.findList(queryDTO);
        if (CollectionUtils.isEmpty(exceptionDTOList)) {
            return outStockOrderPOList;
        }
        Map<Long, Long> exceptionOrderMap = exceptionDTOList.stream().collect(
            Collectors.toMap(WMSOrderExceptionDTO::getOrderId, WMSOrderExceptionDTO::getOrderId, (v1, v2) -> v1));
        return outStockOrderPOList.stream().filter(order -> Objects.isNull(exceptionOrderMap.get(order.getId())))
            .collect(Collectors.toList());
    }

    protected void checkStateErrorOrders(List<OutStockOrderPO> outStockOrderPOList) {
        // 排除已作废、已取消状态订单
        if (!CollectionUtils.isEmpty(outStockOrderPOList)) {
            outStockOrderPOList.removeIf(p -> Objects.equals(p.getState(), (int)OutStockOrderStateEnum.已作废.getType())
                || Objects.equals(p.getState(), (int)OutStockOrderStateEnum.已取消.getType()));
        }
        List<Integer> normalStateList =
            Arrays.asList((int)OutStockOrderStateEnum.待调度.getType(), (int)OutStockOrderStateEnum.调拨中.getType());
        // 记录状态异常订单
        if (!CollectionUtils.isEmpty(outStockOrderPOList)
            && outStockOrderPOList.stream().anyMatch(p -> !normalStateList.contains(p.getState()))) {
            List<String> lstTmp = outStockOrderPOList.stream().filter(p -> !normalStateList.contains(p.getState()))
                .map(p -> String.format("%s-%s", p.getReforderno(), p.getState())).collect(Collectors.toList());
            LOG.info(String.format("生波次发现状态异常订单，单号：%s", JSON.toJSONString(lstTmp)));
        }
    }

}
