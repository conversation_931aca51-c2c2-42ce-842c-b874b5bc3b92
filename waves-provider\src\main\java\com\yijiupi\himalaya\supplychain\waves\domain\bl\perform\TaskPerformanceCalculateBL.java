package com.yijiupi.himalaya.supplychain.waves.domain.bl.perform;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.user.dto.sign.UserSignBelongDateQueryDTO;
import com.yijiupi.himalaya.supplychain.user.dto.sign.UserSignBelongDateResultDTO;
import com.yijiupi.himalaya.supplychain.user.service.sign.IAdminUserSignService;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.TaskPerformanceCalculateBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.TaskPerformanceCalculateConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.UserSignBelongDateQueryDTOConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.BatchTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.SowTaskMapper;
import com.yijiupi.himalaya.supplychain.waves.dto.performance.TaskPerformanceCalculateDTO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/6/25
 */
@Service
public class TaskPerformanceCalculateBL {

    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private SowTaskMapper sowTaskMapper;
    @Autowired
    private TaskPerformanceCalculateConvertor taskPerformanceCalculateConvertor;

    @Reference
    private IAdminUserSignService iAdminUserSignService;
    private static final Logger LOG = LoggerFactory.getLogger(TaskPerformanceCalculateBL.class);

    public void calculateAndModTaskPerformance(TaskPerformanceCalculateBO calculateBO) {
        try {
            LOG.info("计算绩效入参 : {}", JSON.toJSONString(calculateBO));
            UserSignBelongDateQueryDTO queryDTO = UserSignBelongDateQueryDTOConvertor.convert(calculateBO);
            // 获取归属班次时间
            UserSignBelongDateResultDTO belongDateResultDTO = iAdminUserSignService.queryUserSignBelongDate(queryDTO);
            // 填充时间
            UserSignBelongDateQueryDTOConvertor.fillPerformanceDate(calculateBO,
                Collections.singletonList(belongDateResultDTO));
            // 更新操作
            calculateBO.getConsumer().accept(calculateBO);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    /**
     * 
     * @param dto
     */
    public void calculateTaskPerformance(TaskPerformanceCalculateDTO dto) {
        AssertUtils.notNull(dto.getType(), "任务类型不能为空！");
        List<TaskPerformanceCalculateBO> taskPerformanceCalculateBOList =
            taskPerformanceCalculateConvertor.convert(dto);
        if (CollectionUtils.isEmpty(taskPerformanceCalculateBOList)) {
            return;
        }

        taskPerformanceCalculateBOList.forEach(this::calculateAndModTaskPerformance);

    }

}
