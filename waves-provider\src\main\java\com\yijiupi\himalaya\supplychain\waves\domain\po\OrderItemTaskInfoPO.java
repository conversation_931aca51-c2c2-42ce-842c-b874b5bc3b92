package com.yijiupi.himalaya.supplychain.waves.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 出库单项拣货关联表
 *
 * <AUTHOR>
 * @date 2020-06-09 15:21
 */
public class OrderItemTaskInfoPO implements Serializable {

    private static final long serialVersionUID = 7475815857378921895L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 城市ID
     */
    private Integer orgId;

    /**
     * 订单项id
     */
    private Long refOrderItemId;

    /**
     * 订单id
     */
    private Long refOrderId;

    /**
     * 拣货任务项id
     */
    private String batchTaskItemId;

    /**
     * 拣货任务id
     */
    private String batchTaskId;

    /**
     * 拣货任务编号
     */
    private String batchTaskNo;

    /**
     * 波次id
     */
    private String batchId;

    /**
     * 波次编号
     */
    private String batchNo;

    /**
     * 分配变更小单位总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 已拣货数量
     */
    private BigDecimal overSortCount;

    /**
     * 缺货数量
     */
    private BigDecimal lackUnitCount;

    /**
     * 移库数量
     */
    private BigDecimal moveCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 最后修改人
     */
    private String lastUpdateUser;

    /**
     * 关联明细项
     */
    private List<OrderItemTaskInfoDetailPO> detailList;

    /**
     * 拣货任务项状态 未分拣(0), 分拣中(1), 已完成(2)
     */
    private Byte batchTaskItemState;

    /**
     * 订单号
     */
    private String refOrderNo;
    /**
     * 原始数量
     */
    private BigDecimal originalUnitTotalCount;

    /**
     * 商品名称
     */
    private String productName;
    /**
     * skuId（赠品SKUId可能为null）
     */
    private Long skuId;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Byte getBatchTaskItemState() {
        return batchTaskItemState;
    }

    public void setBatchTaskItemState(Byte batchTaskItemState) {
        this.batchTaskItemState = batchTaskItemState;
    }

    public List<OrderItemTaskInfoDetailPO> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<OrderItemTaskInfoDetailPO> detailList) {
        this.detailList = detailList;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getRefOrderItemId() {
        return refOrderItemId;
    }

    public void setRefOrderItemId(Long refOrderItemId) {
        this.refOrderItemId = refOrderItemId;
    }

    public Long getRefOrderId() {
        return refOrderId;
    }

    public void setRefOrderId(Long refOrderId) {
        this.refOrderId = refOrderId;
    }

    public String getBatchTaskItemId() {
        return batchTaskItemId;
    }

    public void setBatchTaskItemId(String batchTaskItemId) {
        this.batchTaskItemId = batchTaskItemId;
    }

    public String getBatchTaskId() {
        return batchTaskId;
    }

    public void setBatchTaskId(String batchTaskId) {
        this.batchTaskId = batchTaskId;
    }

    public String getBatchTaskNo() {
        return batchTaskNo;
    }

    public void setBatchTaskNo(String batchTaskNo) {
        this.batchTaskNo = batchTaskNo;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public BigDecimal getOverSortCount() {
        return overSortCount;
    }

    public void setOverSortCount(BigDecimal overSortCount) {
        this.overSortCount = overSortCount;
    }

    public BigDecimal getLackUnitCount() {
        return lackUnitCount;
    }

    public void setLackUnitCount(BigDecimal lackUnitCount) {
        this.lackUnitCount = lackUnitCount;
    }

    public BigDecimal getMoveCount() {
        return moveCount;
    }

    public void setMoveCount(BigDecimal moveCount) {
        this.moveCount = moveCount;
    }

    /**
     * 获取 原始数量
     *
     * @return originalUnitTotalCount 原始数量
     */
    public BigDecimal getOriginalUnitTotalCount() {
        return this.originalUnitTotalCount;
    }

    /**
     * 设置 原始数量
     *
     * @param originalUnitTotalCount 原始数量
     */
    public void setOriginalUnitTotalCount(BigDecimal originalUnitTotalCount) {
        this.originalUnitTotalCount = originalUnitTotalCount;
    }
}
