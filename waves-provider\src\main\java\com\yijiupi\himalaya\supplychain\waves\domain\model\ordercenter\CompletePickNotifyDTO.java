package com.yijiupi.himalaya.supplychain.waves.domain.model.ordercenter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * WMS完成拣货通知订单中台DTO
 * 
 * <AUTHOR>
 * @Date 2022/1/20
 */
public class CompletePickNotifyDTO implements Serializable {
    private static final long serialVersionUID = 4734272156225025295L;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     *
     * 分拣员Id
     */
    private Integer stevedoreUserId;
    /**
     * 分拣员名字
     */
    private String stevedoreUserName;
    /**
     * 货位Id
     */
    private Long defaultLocationId;
    /**
     * 货位名字
     */
    private String defaultLocationName;
    /**
     * 完成拣货时间
     */
    private Date completePickTime;
    /**
     * 操作人Id
     */
    private String optUserId;
    /**
     * 拣货人id列表
     */
    private List<String> pickUserNameList;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getStevedoreUserId() {
        return stevedoreUserId;
    }

    public void setStevedoreUserId(Integer stevedoreUserId) {
        this.stevedoreUserId = stevedoreUserId;
    }

    public String getStevedoreUserName() {
        return stevedoreUserName;
    }

    public void setStevedoreUserName(String stevedoreUserName) {
        this.stevedoreUserName = stevedoreUserName;
    }

    public Long getDefaultLocationId() {
        return defaultLocationId;
    }

    public void setDefaultLocationId(Long defaultLocationId) {
        this.defaultLocationId = defaultLocationId;
    }

    public String getDefaultLocationName() {
        return defaultLocationName;
    }

    public void setDefaultLocationName(String defaultLocationName) {
        this.defaultLocationName = defaultLocationName;
    }

    public Date getCompletePickTime() {
        return completePickTime;
    }

    public void setCompletePickTime(Date completePickTime) {
        this.completePickTime = completePickTime;
    }

    public String getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(String optUserId) {
        this.optUserId = optUserId;
    }

    /**
     * 获取 拣货人id列表
     *
     * @return pickUserNameList 拣货人id列表
     */
    public List<String> getPickUserNameList() {
        return this.pickUserNameList;
    }

    /**
     * 设置 拣货人id列表
     *
     * @param pickUserNameList 拣货人id列表
     */
    public void setPickUserNameList(List<String> pickUserNameList) {
        this.pickUserNameList = pickUserNameList;
    }
}
