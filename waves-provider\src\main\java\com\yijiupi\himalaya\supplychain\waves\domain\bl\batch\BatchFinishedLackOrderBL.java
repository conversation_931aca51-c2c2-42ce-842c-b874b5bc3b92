package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.postcommit.CommitAfterTransactionAnn;
import com.yijiupi.himalaya.supplychain.ordercenter.consant.SyncTraceMapKeyConstants;
import com.yijiupi.himalaya.supplychain.ordercenter.consant.SyncTraceTypeConstants;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.SyncTraceDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.service.OrderCenterService;
import com.yijiupi.himalaya.supplychain.outstock.service.IWMSOrderExceptionService;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderCenterBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.packageorder.PackageOrderItemModBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.storecheck.PickLackStoreCheckBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.*;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OrderItemTaskInfoMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.BatchTaskItemLackDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.PartSendWsmDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.ModPackageInfoByOutBoundInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.packageorder.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.util.GlobalCache;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/12/11
 */
@Service
public class BatchFinishedLackOrderBL {

    @Autowired
    private OrderCenterBL orderCenterBL;
    @Autowired
    private GlobalCache globalCache;
    @Autowired
    private PackageOrderItemModBL packageOrderItemModBL;
    @Resource
    private OrderItemTaskInfoMapper orderItemTaskInfoMapper;

    @Resource
    private PickLackStoreCheckBL pickLackStoreCheckBL;

    @Reference
    private IWMSOrderExceptionService wmsOrderExceptionService;

    @Reference
    private OrderCenterService orderCenterService;

    private static final Logger LOG = LoggerFactory.getLogger(BatchFinishedLackOrderBL.class);

    // 调用老缺货代码马上废除了，代码结构不费力气大调整了
    // @Transactional(rollbackFor = Exception.class)
    @CommitAfterTransactionAnn
    public void processOrderLack(NotifyOrderLackBO notifyOrderLackBO, Integer operatorUserId) {
        if (Objects.isNull(notifyOrderLackBO)) {
            return;
        }
        if (!CollectionUtils.isEmpty(notifyOrderLackBO.getLackOrderItemIds())) {
            packageOrderItemModBL.batchCompleteModLackPackageOrderItem(notifyOrderLackBO.getLackOrderItemIds());
        }

        if (!CollectionUtils.isEmpty(notifyOrderLackBO.getTotalLackOrderList())) {
            afterBatchTaskComplete(notifyOrderLackBO.getTotalLackOrderList(), operatorUserId);
        }

        if (Objects.nonNull(notifyOrderLackBO.getNotifyOmsOldOrderLackBO())) {
            omsMarkPartOld(notifyOrderLackBO.getNotifyOmsOldOrderLackBO());
            return;
        }

        if (Objects.nonNull(notifyOrderLackBO.getNotifyOmsOrderLackBO())) {
            invokeOMS(notifyOrderLackBO.getNotifyOmsOrderLackBO());
            return;
        }

        if (Objects.nonNull(notifyOrderLackBO.getNotifyOrderCenterLackBO())) {
            invokeOrderCenterByMark(notifyOrderLackBO.getNotifyOrderCenterLackBO());
            return;
        }
        if (Objects.nonNull(notifyOrderLackBO.getMeiTuanLackBO())) {
            handleMeiTuanLack(notifyOrderLackBO.getMeiTuanLackBO());
            return;
        }

    }

    /**
     * 调用订单中台订单标记通知接口
     */
    private void invokeOrderCenterByMark(NotifyOrderCenterLackBO notifyOrderCenterLackBO) {
        Map<String, StringBuffer> bufferMap = notifyOrderCenterLackBO.getBufferMap();
        Integer operatorUserId = notifyOrderCenterLackBO.getOperatorUserId();

        if (!CollectionUtils.isEmpty(notifyOrderCenterLackBO.getAllotPartSendWsmDTOList())) {
            LOG.info("[invokeOrderCenterByMark]订单中台调拨单标记缺货参数：{}；描述信息：{}",
                JSON.toJSONString(notifyOrderCenterLackBO.getAllotPartSendWsmDTOList()), JSON.toJSONString(bufferMap));
            orderCenterBL.orderLackNotify(
                new MarkLackBO(notifyOrderCenterLackBO.getAllotPartSendWsmDTOList(), bufferMap, operatorUserId));
        }

        if (!CollectionUtils.isEmpty(notifyOrderCenterLackBO.getNormalPartSendWsmDTOList())) {
            LOG.info("[invokeOrderCenterByMark]订单中台非调拨单标记缺货参数：{}；描述信息：{}",
                JSON.toJSONString(notifyOrderCenterLackBO.getNormalPartSendWsmDTOList()), JSON.toJSONString(bufferMap));
            orderCenterBL.orderMarkNotify(
                new MarkLackBO(notifyOrderCenterLackBO.getNormalPartSendWsmDTOList(), bufferMap, operatorUserId));
        }

    }

    /**
     * 调用oms缺货接口
     */
    private void invokeOMS(NotifyOmsOrderLackBO notifyOmsOrderLackBO) {
        List<PartSendWsmDTO> partSendWsmDTOList = notifyOmsOrderLackBO.getPartSendWsmDTOList();
        if (CollectionUtils.isEmpty(partSendWsmDTOList)) {
            LOG.info("标记缺货参数为空直接跳过");
            return;
        }
        partSendWsmDTOList.forEach(m -> {
            if (Objects.isNull(m.getUserId())) {
                m.setUserId(1);
            }
        });
        LOG.info("[markByWms]标记缺货参数：{}", JSON.toJSONString(partSendWsmDTOList));
        // orderPartSendService.markByWms(partSendWsmDTOList, notifyOmsOrderLackBO.getWarehouseId());
    }

    @Deprecated
    public void omsMarkPartOld(NotifyOmsOldOrderLackBO notifyOmsOldOrderLackBO) {
        // 打印缺货数量不是销售规格的倍数日志信息
        printLackCountWarningLog(notifyOmsOldOrderLackBO.getBatchNo(),
            notifyOmsOldOrderLackBO.getFilterBatchTaskItemLackDTOS());
        // 调用新的标记缺货接口
    }

    /**
     * 打印缺货数量不是销售规格的倍数日志信息
     */
    public void printLackCountWarningLog(String batchNo, List<BatchTaskItemLackDTO> filterBatchTaskItemLackDTOS) {
        try {
            if (CollectionUtils.isEmpty(filterBatchTaskItemLackDTOS)) {
                return;
            }
            Map<String,
                List<BatchTaskItemLackDTO>> lackMap = filterBatchTaskItemLackDTOS.stream()
                    .collect(Collectors.groupingBy(p -> String.format("orderNo=%s|skuId=%s|saleSpec=%s",
                        p.getRefOrderNo(), p.getSkuId(), p.getSaleSpecQuantity())));
            if (null == lackMap) {
                return;
            }
            lackMap.forEach((k, list) -> {
                BigDecimal lacktUnitTotolCount =
                    list.stream().map(p -> p.getLackUnitCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal saleSpecQuantity = list.get(0).getSaleSpecQuantity();
                if (lacktUnitTotolCount.divideAndRemainder(saleSpecQuantity)[1].compareTo(BigDecimal.ZERO) != 0) {
                    LOG.info("[{}]缺货数量不是销售规格的倍数：{}", batchNo, k);
                }
            });
        } catch (Exception e) {
            LOG.info("打印缺货数量不是销售规格的倍数日志报错", e);
        }
    }

    private void handleMeiTuanLack(NotifyMeiTuanLackBO meiTuanLackBO) {
        List<PartSendWsmDTO> partSendWsmDTOList = meiTuanLackBO.getPartSendWsmDTOList();
        Map<String, StringBuffer> bufferMap = meiTuanLackBO.getBufferMap();
        Integer operatorUserId = meiTuanLackBO.getOperatorUserId();
        for (PartSendWsmDTO partSendWsmDTO : partSendWsmDTOList) {
            StringBuffer stringBuffer = bufferMap.get(partSendWsmDTO.getBusinessId().toString());
            if (Objects.isNull(stringBuffer) || org.apache.commons.lang3.StringUtils.isBlank(stringBuffer.toString())) {
                continue;
            }

            SyncTraceDTO syncTraceDTO = new SyncTraceDTO();
            syncTraceDTO.setTraceType(SyncTraceTypeConstants.MT_ORDER_PICK_STOCK_OUT_FAIL);
            Map<String, String> traceParamMap = new HashMap<>();
            traceParamMap.put(SyncTraceMapKeyConstants.OP_USER_ID, globalCache.getAdminTrueName(operatorUserId));
            traceParamMap.put(SyncTraceMapKeyConstants.DESCRIBE, stringBuffer.toString());
            syncTraceDTO.setTraceParamMap(traceParamMap);
            orderCenterService.syncTrace(syncTraceDTO, Collections.singletonList(partSendWsmDTO.getBusinessId()));
        }
    }

    // 处理包装箱不一致的场景，延迟1.5秒发送
    @CommitAfterTransactionAnn(delayTime = 2000)
    public void handleNotEqualPackageItem(List<PackageOrderItemDTO> packageOrderItemDTOList) {
        PackageOrderItemDTO packageOrderItemDTO = packageOrderItemDTOList.get(0);
        ModPackageInfoByOutBoundInfoDTO dto = new ModPackageInfoByOutBoundInfoDTO();
        dto.setWarehouseId(packageOrderItemDTO.getWarehouseId());
        dto.setOrgId(packageOrderItemDTO.getOrgId());
        dto.setOutStockOrderIds(Collections.singletonList(packageOrderItemDTO.getRefOrderId()));
        packageOrderItemModBL.modLackPackageOrderItemByOutBoundInfo(dto);
    }

    /**
     * 波次完成后锁定全缺订单
     *
     * @param orders 有缺货的订单
     * @param opUserId 操作人 id
     */
    public void afterBatchTaskComplete(List<OutStockOrderPO> orders, Integer opUserId) {
        LOG.info("波次完成后锁定全缺订单, 订单信息: {}, userId: {}", orders, opUserId);
        Set<Long> lackOrderIds = orders.stream().map(OutStockOrderPO::getId).collect(Collectors.toSet());
        Set<Long> orderIds = orderItemTaskInfoMapper.listOrderItemTaskInfoByOrderIds(lackOrderIds).stream()
            .collect(Collectors.groupingBy(OrderItemTaskInfoPO::getRefOrderId)).values().stream()
            .filter(this::filterAllLack).flatMap(Collection::stream).map(OrderItemTaskInfoPO::getRefOrderId)
            .collect(Collectors.toSet());
        LOG.info("锁定全缺订单参数: {}, {}", orderIds, opUserId);
        wmsOrderExceptionService.forceLockAllLackOrder(orderIds, opUserId);
        // SCM-16412 分拣缺货必须扫仓库缺货码，生成盘亏单
        pickLackStoreCheckBL.generateLossOrder(orders, opUserId);
    }

    private boolean filterAllLack(List<OrderItemTaskInfoPO> items) {
        boolean itemNotEmpty = !items.isEmpty();
        boolean allLack = items.stream().allMatch(it -> it.getOverSortCount().compareTo(BigDecimal.ZERO) == 0);
        return itemNotEmpty && allLack;
    }

}
