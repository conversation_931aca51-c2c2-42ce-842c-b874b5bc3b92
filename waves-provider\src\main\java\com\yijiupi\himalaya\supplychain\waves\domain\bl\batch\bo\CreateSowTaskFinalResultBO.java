package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.RandomCreateProcessBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.utils.SplitWaveOrderUtil;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.createbatch.CreateSowTaskByPassageBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskKindOfPickingConstants;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/26
 */
public class CreateSowTaskFinalResultBO {
    /**
     * 处理新的播种任务
     */
    private List<CreateSowTaskResultBO> normalSowTaskList;
    /**
     * 处理已存在的播种任务
     */
    private List<CreateSowTaskResultBO> existSowTaskList;
    /**
     * 不需要播种的数据
     */
    private List<WaveCreateDTO> notSowWaveCreateList;

    public CreateSowTaskFinalResultBO() {}

    public CreateSowTaskFinalResultBO(List<CreateSowTaskResultBO> normalSowTaskList,
        List<CreateSowTaskResultBO> existSowTaskList) {
        this.normalSowTaskList = normalSowTaskList;
        this.existSowTaskList = existSowTaskList;
    }

    /**
     * 获取 处理新的播种任务
     *
     * @return normalSowTaskList 处理新的播种任务
     */
    public List<CreateSowTaskResultBO> getNormalSowTaskList() {
        return this.normalSowTaskList;
    }

    /**
     * 设置 处理新的播种任务
     *
     * @param normalSowTaskList 处理新的播种任务
     */
    public void setNormalSowTaskList(List<CreateSowTaskResultBO> normalSowTaskList) {
        this.normalSowTaskList = normalSowTaskList;
    }

    /**
     * 获取 处理已存在的播种任务
     *
     * @return existSowTaskList 处理已存在的播种任务
     */
    public List<CreateSowTaskResultBO> getExistSowTaskList() {
        return this.existSowTaskList;
    }

    /**
     * 设置 处理已存在的播种任务
     *
     * @param existSowTaskList 处理已存在的播种任务
     */
    public void setExistSowTaskList(List<CreateSowTaskResultBO> existSowTaskList) {
        this.existSowTaskList = existSowTaskList;
    }

    public static CreateSowTaskFinalResultBO getDefault(List<CreateSowTaskResultBO> normalSowTaskList,
        List<CreateSowTaskResultBO> existSowTaskList) {
        return new CreateSowTaskFinalResultBO(normalSowTaskList, existSowTaskList);
    }

    public static CreateSowTaskFinalResultBO getDefaultNormal(List<CreateSowTaskResultBO> normalSowTaskList) {
        return new CreateSowTaskFinalResultBO(normalSowTaskList, Collections.emptyList());
    }

    public static CreateSowTaskFinalResultBO getFromRandomCreateProcessBO(RandomCreateProcessBO randomCreateProcessBO) {
        CreateSowTaskFinalResultBO newCreateFinalResultBO = new CreateSowTaskFinalResultBO();
        CreateSowTaskFinalResultBO createSowTaskFinalResultBO = randomCreateProcessBO.getCreateSowTaskFinalResultBO();
        if (Objects.isNull(createSowTaskFinalResultBO)) {
            return newCreateFinalResultBO;
        }

        newCreateFinalResultBO.setNormalSowTaskList(createSowTaskFinalResultBO.getNormalSowTaskList());

        return newCreateFinalResultBO;
    }

    public static CreateSowTaskFinalResultBO getFromRandomCreateProcessBO(RandomCreateProcessBO randomCreateProcessBO,
        List<WaveCreateDTO> totalWaveCreateList) {
        CreateSowTaskFinalResultBO newCreateFinalResultBO = new CreateSowTaskFinalResultBO();
        CreateSowTaskFinalResultBO createSowTaskFinalResultBO = randomCreateProcessBO.getCreateSowTaskFinalResultBO();
        if (Objects.nonNull(createSowTaskFinalResultBO)) {
            newCreateFinalResultBO.setNormalSowTaskList(createSowTaskFinalResultBO.getNormalSowTaskList());
        }
        if (!CollectionUtils.isEmpty(totalWaveCreateList)) {
            newCreateFinalResultBO.setNotSowWaveCreateList(totalWaveCreateList);
        }

        return newCreateFinalResultBO;
    }

    /**
     * 获取 不需要播种的数据
     *
     * @return notSowWaveCreateList 不需要播种的数据
     */
    public List<WaveCreateDTO> getNotSowWaveCreateList() {
        return this.notSowWaveCreateList;
    }

    /**
     * 设置 不需要播种的数据
     *
     * @param notSowWaveCreateList 不需要播种的数据
     */
    public void setNotSowWaveCreateList(List<WaveCreateDTO> notSowWaveCreateList) {
        this.notSowWaveCreateList = notSowWaveCreateList;
    }

    public static CreateSowTaskFinalResultBO createNotSowResultBO(CreateSowTaskByPassageBO createSowTaskByPassageBO) {
        CreateSowTaskFinalResultBO createSowTaskFinalResultBO = new CreateSowTaskFinalResultBO();
        createSowTaskFinalResultBO.setNormalSowTaskList(Collections.emptyList());
        createSowTaskFinalResultBO.setExistSowTaskList(Collections.emptyList());

        List<OutStockOrderPO> splitOrderList = createSowTaskByPassageBO.getOrderList();
        WavesStrategyBO wavesStrategyBO = createSowTaskByPassageBO.getWavesStrategyDTO();
        PassageDTO passageDTO = createSowTaskByPassageBO.getPassageDTO();
        WaveCreateDTO waveCreateDTO = createSowTaskByPassageBO.getOriCreateDTO();

        WaveCreateDTO createDTO = SplitWaveOrderUtil.getWaveCreateDTO(splitOrderList, wavesStrategyBO,
            passageDTO.getPickingType(), passageDTO.getPickingGroupStrategy(), createSowTaskByPassageBO.getTitle(),
            createSowTaskByPassageBO.getOperateUser(), wavesStrategyBO.getOrgId(), "",
            createSowTaskByPassageBO.getDriverName(), passageDTO.getSowType(), passageDTO.getPassageType(),
            createSowTaskByPassageBO.getAllocationFlag(), waveCreateDTO.getOperateUserId());
        createDTO.setPassageDTO(passageDTO);
        createDTO.setKindOfPicking(BatchTaskKindOfPickingConstants.PICKING_SORTING);

        List<WaveCreateDTO> notSowWaveCreateList = new ArrayList<>();
        notSowWaveCreateList.add(createDTO);
        createSowTaskFinalResultBO.setNotSowWaveCreateList(notSowWaveCreateList);

        return createSowTaskFinalResultBO;
    }

}
