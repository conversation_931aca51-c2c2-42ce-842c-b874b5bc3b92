package com.yijiupi.himalaya.supplychain.waves.domain.model.tms;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/2/245:21 下午
 */
public class DeliveryOrderQueryConParam implements Serializable {

    /**
     * 配送地点id列表
     */
    private List<Long> deliveryLocationIds;

    /**
     * 配送中心id列表
     */
    private List<Integer> deliveryCenterIds;

    /**
     * 开始时间
     */
    private String dateStart;
    /**
     * 结束时间
     */
    private String dateEnd;
    /**
     * 配送批次id
     */
    private List<Long> deliveryTaskIds;
    /**
     * 配送司机id
     */
    private Integer deliveryUserId;
    /**
     * 配送单状态
     */
    private List<Byte> stateList;
    /**
     * 标记状态
     */
    private List<Byte> deliverySignStateList;
    /**
     * 调度批次id
     */
    private List<Long> scheduleTaskIds;
    /**
     * 开始时间
     */
    private String dispatchDateStart;
    /**
     * 结束时间
     */
    private String dispatchDateEnd;
    /**
     *
     */
    private List<Byte> deliveryTypeList;
    /**
     * 订单号
     */
    private List<String> deliveryNoList;
    /**
     * 订单id列表
     */
    private List<Long> orderIdList;
    /**
     * 配送单id列表
     */
    private List<Long> deliveryOrderIds;

    /**
     * 获取 配送地点id列表
     *
     * @return deliveryLocationIds 配送地点id列表
     */
    public List<Long> getDeliveryLocationIds() {
        return this.deliveryLocationIds;
    }

    /**
     * 设置 配送地点id列表
     *
     * @param deliveryLocationIds 配送地点id列表
     */
    public void setDeliveryLocationIds(List<Long> deliveryLocationIds) {
        this.deliveryLocationIds = deliveryLocationIds;
    }

    /**
     * 获取 开始时间
     *
     * @return dateStart 开始时间
     */
    public String getDateStart() {
        return this.dateStart;
    }

    /**
     * 设置 开始时间
     *
     * @param dateStart 开始时间
     */
    public void setDateStart(String dateStart) {
        this.dateStart = dateStart;
    }

    /**
     * 获取 结束时间
     *
     * @return dateEnd 结束时间
     */
    public String getDateEnd() {
        return this.dateEnd;
    }

    /**
     * 设置 结束时间
     *
     * @param dateEnd 结束时间
     */
    public void setDateEnd(String dateEnd) {
        this.dateEnd = dateEnd;
    }

    /**
     * 获取 配送司机id
     *
     * @return deliveryUserId 配送司机id
     */
    public Integer getDeliveryUserId() {
        return this.deliveryUserId;
    }

    /**
     * 设置 配送司机id
     *
     * @param deliveryUserId 配送司机id
     */
    public void setDeliveryUserId(Integer deliveryUserId) {
        this.deliveryUserId = deliveryUserId;
    }

    /**
     * 获取 配送批次id
     *
     * @return deliveryTaskIds 配送批次id
     */
    public List<Long> getDeliveryTaskIds() {
        return this.deliveryTaskIds;
    }

    /**
     * 设置 配送批次id
     *
     * @param deliveryTaskIds 配送批次id
     */
    public void setDeliveryTaskIds(List<Long> deliveryTaskIds) {
        this.deliveryTaskIds = deliveryTaskIds;
    }

    /**
     * 获取 配送单状态
     *
     * @return stateList 配送单状态
     */
    public List<Byte> getStateList() {
        return this.stateList;
    }

    /**
     * 设置 配送单状态
     *
     * @param stateList 配送单状态
     */
    public void setStateList(List<Byte> stateList) {
        this.stateList = stateList;
    }

    /**
     * 获取 标记状态
     *
     * @return deliverySignStateList 标记状态
     */
    public List<Byte> getDeliverySignStateList() {
        return this.deliverySignStateList;
    }

    /**
     * 设置 标记状态
     *
     * @param deliverySignStateList 标记状态
     */
    public void setDeliverySignStateList(List<Byte> deliverySignStateList) {
        this.deliverySignStateList = deliverySignStateList;
    }

    /**
     * 获取 配送中心id列表
     *
     * @return deliveryCenterIds 配送中心id列表
     */
    public List<Integer> getDeliveryCenterIds() {
        return this.deliveryCenterIds;
    }

    /**
     * 设置 配送中心id列表
     *
     * @param deliveryCenterIds 配送中心id列表
     */
    public void setDeliveryCenterIds(List<Integer> deliveryCenterIds) {
        this.deliveryCenterIds = deliveryCenterIds;
    }

    /**
     * 获取 调度批次id
     *
     * @return scheduleTaskIds 调度批次id
     */
    public List<Long> getScheduleTaskIds() {
        return this.scheduleTaskIds;
    }

    /**
     * 设置 调度批次id
     *
     * @param scheduleTaskIds 调度批次id
     */
    public void setScheduleTaskIds(List<Long> scheduleTaskIds) {
        this.scheduleTaskIds = scheduleTaskIds;
    }

    /**
     * 获取 开始时间
     *
     * @return dispatchDateStart 开始时间
     */
    public String getDispatchDateStart() {
        return this.dispatchDateStart;
    }

    /**
     * 设置 开始时间
     *
     * @param dispatchDateStart 开始时间
     */
    public void setDispatchDateStart(String dispatchDateStart) {
        this.dispatchDateStart = dispatchDateStart;
    }

    /**
     * 获取 结束时间
     *
     * @return dispatchDateEnd 结束时间
     */
    public String getDispatchDateEnd() {
        return this.dispatchDateEnd;
    }

    /**
     * 设置 结束时间
     *
     * @param dispatchDateEnd 结束时间
     */
    public void setDispatchDateEnd(String dispatchDateEnd) {
        this.dispatchDateEnd = dispatchDateEnd;
    }

    /**
     * 获取
     *
     * @return deliveryTypeList
     */
    public List<Byte> getDeliveryTypeList() {
        return this.deliveryTypeList;
    }

    /**
     * 设置
     *
     * @param deliveryTypeList
     */
    public void setDeliveryTypeList(List<Byte> deliveryTypeList) {
        this.deliveryTypeList = deliveryTypeList;
    }

    /**
     * 获取 订单号
     *
     * @return deliveryNoList 订单号
     */
    public List<String> getDeliveryNoList() {
        return this.deliveryNoList;
    }

    /**
     * 设置 订单号
     *
     * @param deliveryNoList 订单号
     */
    public void setDeliveryNoList(List<String> deliveryNoList) {
        this.deliveryNoList = deliveryNoList;
    }

    /**
     * 获取 订单id列表
     *
     * @return orderIdList 订单id列表
     */
    public List<Long> getOrderIdList() {
        return this.orderIdList;
    }

    /**
     * 设置 订单id列表
     *
     * @param orderIdList 订单id列表
     */
    public void setOrderIdList(List<Long> orderIdList) {
        this.orderIdList = orderIdList;
    }

    /**
     * 获取 配送单id列表
     *
     * @return deliveryOrderIds 配送单id列表
     */
    public List<Long> getDeliveryOrderIds() {
        return this.deliveryOrderIds;
    }

    /**
     * 设置 配送单id列表
     *
     * @param deliveryOrderIds 配送单id列表
     */
    public void setDeliveryOrderIds(List<Long> deliveryOrderIds) {
        this.deliveryOrderIds = deliveryOrderIds;
    }
}
