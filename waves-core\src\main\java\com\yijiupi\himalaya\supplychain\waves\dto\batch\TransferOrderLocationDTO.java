package com.yijiupi.himalaya.supplychain.waves.dto.batch;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/5/30
 */
public class TransferOrderLocationDTO implements Serializable {

    private List<String> orderNoList;

    private Integer orgId;

    private Integer warehouseId;

    private List<Long> orderIds;

    private Integer userId;

    /**
     * 获取
     *
     * @return orgId
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置
     *
     * @param orgId
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取
     *
     * @return warehouseId
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置
     *
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取
     *
     * @return orderNoList
     */
    public List<String> getOrderNoList() {
        return this.orderNoList;
    }

    /**
     * 设置
     *
     * @param orderNoList
     */
    public void setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
    }

    /**
     * 获取
     *
     * @return orderIds
     */
    public List<Long> getOrderIds() {
        return this.orderIds;
    }

    /**
     * 设置
     *
     * @param orderIds
     */
    public void setOrderIds(List<Long> orderIds) {
        this.orderIds = orderIds;
    }

    /**
     * 获取
     *
     * @return userId
     */
    public Integer getUserId() {
        return this.userId;
    }

    /**
     * 设置
     *
     * @param userId
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }
}
