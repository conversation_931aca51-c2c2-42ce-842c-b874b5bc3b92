package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.convertor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.WavesStrategyBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.CreateBatchLocationBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.location.LocationSplitHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;

/**
 * <AUTHOR>
 * @title: LocationSplitHelperBOConvertor
 * @description:
 * @date 2022-12-01 09:20
 */
public class LocationSplitHelperBOConvertor {
    private static final Logger LOG = LoggerFactory.getLogger(LocationSplitHelperBOConvertor.class);

    public static List<LocationSplitHelperBO> convert(Map<Long, List<OutStockOrderItemPO>> skuGroupMap,
        CreateBatchLocationBO createBatchLocationBO) {
        return convert(skuGroupMap, createBatchLocationBO.getWavesStrategyDTO(),
            createBatchLocationBO.getWarehouseConfigDTO());
    }

    public static List<LocationSplitHelperBO> convert(Map<Long, List<OutStockOrderItemPO>> skuGroupMap,
        WavesStrategyBO wavesStrategyDTO, WarehouseConfigDTO warehouseConfigDTO) {
        List<LocationSplitHelperBO> boList = new ArrayList<>();
        for (Map.Entry<Long, List<OutStockOrderItemPO>> entry : skuGroupMap.entrySet()) {
            BigDecimal totalUnitCount = entry.getValue().stream().map(OutStockOrderItemPO::getUnittotalcount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            OutStockOrderItemPO itemPO = entry.getValue().stream().findFirst().get();

            LocationSplitHelperBO bo = new LocationSplitHelperBO();
            bo.setSkuId(entry.getKey());
            bo.setUnitTotalCount(totalUnitCount);
            bo.setSpecQuantity(itemPO.getSpecquantity());
            bo.setSaleSpecQuantity(itemPO.getSalespecquantity());
            BigDecimal[] count = totalUnitCount.divideAndRemainder(bo.getSpecQuantity());
            bo.setPackageCount(count[0]);
            bo.setUnitCount(count[1]);
            bo.setItemList(entry.getValue());
            bo.setWavesStrategyBO(wavesStrategyDTO);
            bo.setWarehouseConfigDTO(warehouseConfigDTO);

            boList.add(bo);
        }

        return boList;
    }

}
