<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.waves.domain.dao.StockUpRecordItemPOMapper">
  <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordItemPO">
    <!--@mbg.generated-->
    <!--@Table stockuprecorditem-->
    <id column="Id" jdbcType="BIGINT" property="id" />
    <result column="OrgId" jdbcType="INTEGER" property="orgId" />
    <result column="StockUpRecordId" jdbcType="BIGINT" property="stockUpRecordId" />
    <result column="SkuId" jdbcType="BIGINT" property="skuId" />
    <result column="TaskState" jdbcType="TINYINT" property="taskState" />
    <result column="ProductName" jdbcType="VARCHAR" property="productName" />
    <result column="PickupUser" jdbcType="VARCHAR" property="pickupUser" />
    <result column="CompleteTime" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="PackageCount" jdbcType="DECIMAL" property="packageCount" />
    <result column="UnitCount" jdbcType="DECIMAL" property="unitCount" />
    <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    Id, OrgId, StockUpRecordId, SkuId, TaskState, ProductName, PickupUser, CompleteTime, 
    PackageCount, UnitCount, CreateTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from stockuprecorditem
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from stockuprecorditem
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordItemPO">
    <!--@mbg.generated-->
    insert into stockuprecorditem (Id, OrgId, StockUpRecordId, 
      SkuId, TaskState, ProductName, 
      PickupUser, CompleteTime, PackageCount, 
      UnitCount, CreateTime)
    values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=INTEGER}, #{stockUpRecordId,jdbcType=BIGINT}, 
      #{skuId,jdbcType=BIGINT}, #{taskState,jdbcType=TINYINT}, #{productName,jdbcType=VARCHAR}, 
      #{pickupUser,jdbcType=VARCHAR}, #{completeTime,jdbcType=TIMESTAMP}, #{packageCount,jdbcType=DECIMAL}, 
      #{unitCount,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordItemPO">
    <!--@mbg.generated-->
    insert into stockuprecorditem
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="orgId != null">
        OrgId,
      </if>
      <if test="stockUpRecordId != null">
        StockUpRecordId,
      </if>
      <if test="skuId != null">
        SkuId,
      </if>
      <if test="taskState != null">
        TaskState,
      </if>
      <if test="productName != null and productName != ''">
        ProductName,
      </if>
      <if test="pickupUser != null and pickupUser != ''">
        PickupUser,
      </if>
      <if test="completeTime != null">
        CompleteTime,
      </if>
      <if test="packageCount != null">
        PackageCount,
      </if>
      <if test="unitCount != null">
        UnitCount,
      </if>
      <if test="createTime != null">
        CreateTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="stockUpRecordId != null">
        #{stockUpRecordId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="taskState != null">
        #{taskState,jdbcType=TINYINT},
      </if>
      <if test="productName != null and productName != ''">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="pickupUser != null and pickupUser != ''">
        #{pickupUser,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="packageCount != null">
        #{packageCount,jdbcType=DECIMAL},
      </if>
      <if test="unitCount != null">
        #{unitCount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordItemPO">
    <!--@mbg.generated-->
    update stockuprecorditem
    <set>
      <if test="orgId != null">
        OrgId = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="stockUpRecordId != null">
        StockUpRecordId = #{stockUpRecordId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        SkuId = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="taskState != null">
        TaskState = #{taskState,jdbcType=TINYINT},
      </if>
      <if test="productName != null and productName != ''">
        ProductName = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="pickupUser != null and pickupUser != ''">
        PickupUser = #{pickupUser,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        CompleteTime = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="packageCount != null">
        PackageCount = #{packageCount,jdbcType=DECIMAL},
      </if>
      <if test="unitCount != null">
        UnitCount = #{unitCount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordItemPO">
    <!--@mbg.generated-->
    update stockuprecorditem
    set OrgId = #{orgId,jdbcType=INTEGER},
      StockUpRecordId = #{stockUpRecordId,jdbcType=BIGINT},
      SkuId = #{skuId,jdbcType=BIGINT},
      TaskState = #{taskState,jdbcType=TINYINT},
      ProductName = #{productName,jdbcType=VARCHAR},
      PickupUser = #{pickupUser,jdbcType=VARCHAR},
      CompleteTime = #{completeTime,jdbcType=TIMESTAMP},
      PackageCount = #{packageCount,jdbcType=DECIMAL},
      UnitCount = #{unitCount,jdbcType=DECIMAL},
      CreateTime = #{createTime,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update stockuprecorditem
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="OrgId = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.orgId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="StockUpRecordId = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.stockUpRecordId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="SkuId = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.skuId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="TaskState = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.taskState,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="ProductName = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.productName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PickupUser = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.pickupUser,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="CompleteTime = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.completeTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="PackageCount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.packageCount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="UnitCount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.unitCount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="CreateTime = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when Id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where Id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update stockuprecorditem
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="OrgId = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orgId != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.orgId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="StockUpRecordId = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.stockUpRecordId != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.stockUpRecordId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="SkuId = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuId != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.skuId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="TaskState = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.taskState != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.taskState,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="ProductName = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productName != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.productName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PickupUser = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pickupUser != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.pickupUser,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CompleteTime = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.completeTime != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.completeTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="PackageCount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.packageCount != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.packageCount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="UnitCount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.unitCount != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.unitCount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="CreateTime = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when Id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where Id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into stockuprecorditem
    (Id, OrgId, StockUpRecordId, SkuId, TaskState, ProductName, PickupUser, CompleteTime, 
      PackageCount, UnitCount, CreateTime)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.orgId,jdbcType=INTEGER}, #{item.stockUpRecordId,jdbcType=BIGINT}, 
        #{item.skuId,jdbcType=BIGINT}, #{item.taskState,jdbcType=TINYINT}, #{item.productName,jdbcType=VARCHAR}, 
        #{item.pickupUser,jdbcType=VARCHAR}, #{item.completeTime,jdbcType=TIMESTAMP}, #{item.packageCount,jdbcType=DECIMAL}, 
        #{item.unitCount,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from stockuprecorditem where Id in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=BIGINT}
    </foreach>
  </delete>
  <insert id="insertOrUpdate" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordItemPO">
    <!--@mbg.generated-->
    insert into stockuprecorditem
    (Id, OrgId, StockUpRecordId, SkuId, TaskState, ProductName, PickupUser, CompleteTime, 
      PackageCount, UnitCount, CreateTime)
    values
    (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=INTEGER}, #{stockUpRecordId,jdbcType=BIGINT}, 
      #{skuId,jdbcType=BIGINT}, #{taskState,jdbcType=TINYINT}, #{productName,jdbcType=VARCHAR}, 
      #{pickupUser,jdbcType=VARCHAR}, #{completeTime,jdbcType=TIMESTAMP}, #{packageCount,jdbcType=DECIMAL}, 
      #{unitCount,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP})
    on duplicate key update 
    Id = #{id,jdbcType=BIGINT}, 
    OrgId = #{orgId,jdbcType=INTEGER}, 
    StockUpRecordId = #{stockUpRecordId,jdbcType=BIGINT}, 
    SkuId = #{skuId,jdbcType=BIGINT}, 
    TaskState = #{taskState,jdbcType=TINYINT}, 
    ProductName = #{productName,jdbcType=VARCHAR}, 
    PickupUser = #{pickupUser,jdbcType=VARCHAR}, 
    CompleteTime = #{completeTime,jdbcType=TIMESTAMP}, 
    PackageCount = #{packageCount,jdbcType=DECIMAL}, 
    UnitCount = #{unitCount,jdbcType=DECIMAL}, 
    CreateTime = #{createTime,jdbcType=TIMESTAMP}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yijiupi.himalaya.supplychain.waves.domain.po.StockUpRecordItemPO">
    <!--@mbg.generated-->
    insert into stockuprecorditem
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="orgId != null">
        OrgId,
      </if>
      <if test="stockUpRecordId != null">
        StockUpRecordId,
      </if>
      <if test="skuId != null">
        SkuId,
      </if>
      <if test="taskState != null">
        TaskState,
      </if>
      <if test="productName != null and productName != ''">
        ProductName,
      </if>
      <if test="pickupUser != null and pickupUser != ''">
        PickupUser,
      </if>
      <if test="completeTime != null">
        CompleteTime,
      </if>
      <if test="packageCount != null">
        PackageCount,
      </if>
      <if test="unitCount != null">
        UnitCount,
      </if>
      <if test="createTime != null">
        CreateTime,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="stockUpRecordId != null">
        #{stockUpRecordId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="taskState != null">
        #{taskState,jdbcType=TINYINT},
      </if>
      <if test="productName != null and productName != ''">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="pickupUser != null and pickupUser != ''">
        #{pickupUser,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="packageCount != null">
        #{packageCount,jdbcType=DECIMAL},
      </if>
      <if test="unitCount != null">
        #{unitCount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        Id = #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        OrgId = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="stockUpRecordId != null">
        StockUpRecordId = #{stockUpRecordId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        SkuId = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="taskState != null">
        TaskState = #{taskState,jdbcType=TINYINT},
      </if>
      <if test="productName != null and productName != ''">
        ProductName = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="pickupUser != null and pickupUser != ''">
        PickupUser = #{pickupUser,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        CompleteTime = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="packageCount != null">
        PackageCount = #{packageCount,jdbcType=DECIMAL},
      </if>
      <if test="unitCount != null">
        UnitCount = #{unitCount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
</mapper>