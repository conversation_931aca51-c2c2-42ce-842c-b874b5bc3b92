package com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.decoratesplitbatchtask;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.waves.bo.createbatch.SplitBatchTaskDecorateHelperBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.SplitBatchTaskByOrderTypeBO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 正常装饰器 装饰 行为，这里 装饰 对象
 * 
 * <AUTHOR>
 * @date 2025/6/4
 */
public interface SplitBatchTaskByOrderType {

    void splitBatchTaskByOrderType(SplitBatchTaskByOrderTypeBO bo, SplitBatchTaskDecorateHelperBO helperBO);
}
