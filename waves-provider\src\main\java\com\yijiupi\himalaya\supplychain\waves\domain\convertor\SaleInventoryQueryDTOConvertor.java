package com.yijiupi.himalaya.supplychain.waves.domain.convertor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.outstockordersync.dto.outstockorder.SaleInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OrderItemTaskInfoPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemDetailPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderItemDetailDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2024/1/25
 */
public class SaleInventoryQueryDTOConvertor {

    public static List<SaleInventoryQueryDTO> convertAndFilterList(List<OutStockOrderPO> orderList,
        List<OrderItemTaskInfoPO> orderItemTaskInfoPOList, Integer warehouseId, Integer cityId) {
        Map<Long, Long> refOrderItemIdMap = orderItemTaskInfoPOList.stream()
            .collect(Collectors.toMap(OrderItemTaskInfoPO::getRefOrderItemId, OrderItemTaskInfoPO::getRefOrderItemId));

        List<OutStockOrderItemPO> outStockOrderItemList = orderList.stream().flatMap(m -> m.getItems().stream())
            .filter(m -> Objects.nonNull(refOrderItemIdMap.get(m.getId()))).collect(Collectors.toList());

        List<SaleInventoryQueryDTO> saleInventoryQueryDTOS = new ArrayList<>();
        for (OutStockOrderItemPO outStockOrderItemPO : outStockOrderItemList) {
            for (OutStockOrderItemDetailPO detailPO : outStockOrderItemPO.getItemDetails()) {
                SaleInventoryQueryDTO saleInventoryQueryDTO = new SaleInventoryQueryDTO();
                saleInventoryQueryDTO.setProductSpecificationId(outStockOrderItemPO.getProductSpecificationId());
                saleInventoryQueryDTO.setWarehouseId(warehouseId);
                saleInventoryQueryDTO.setOwnerId(detailPO.getOwnerId());
                saleInventoryQueryDTO.setSecOwnerId(detailPO.getSecOwnerId());
                saleInventoryQueryDTO.setCityId(cityId);

                saleInventoryQueryDTOS.add(saleInventoryQueryDTO);
            }
        }

        return saleInventoryQueryDTOS;
    }

    /**
     *
     * @param specificationIdMap key是 998-9981-560993-null-1
     * @param meiTuanOrderList
     * @return
     */
    public static List<String> findMeiTuanOrder(Map<String, String> specificationIdMap,
        List<OutStockOrderPO> meiTuanOrderList) {
        List<String> meiTuanOrderNos = new ArrayList<>();
        for (OutStockOrderPO outStockOrderPO : meiTuanOrderList) {
            for (OutStockOrderItemPO outStockOrderItemPO : outStockOrderPO.getItems()) {
                for (OutStockOrderItemDetailPO detailPO : outStockOrderItemPO.getItemDetails()) {
                    String key = outStockOrderPO.getOrgId() + "-" + outStockOrderPO.getWarehouseId() + "-"
                        + detailPO.getProductSpecificationId() + "-" + detailPO.getOwnerId() + "-"
                        + detailPO.getSecOwnerId();

                    if (Objects.nonNull(specificationIdMap.get(key))) {
                        meiTuanOrderNos.add(outStockOrderPO.getReforderno());
                    }
                }
            }
        }

        return meiTuanOrderNos;
    }

    public static List<SaleInventoryQueryDTO> convertAndFilterList(List<OutStockOrderItemDTO> items,
        Integer warehouseId, Integer cityId) {
        List<SaleInventoryQueryDTO> saleInventoryQueryDTOS = new ArrayList<>();
        for (OutStockOrderItemDTO item : items) {
            for (OutStockOrderItemDetailDTO detailPO : item.getOutStockOrderItemDetailDTOS()) {
                SaleInventoryQueryDTO saleInventoryQueryDTO = new SaleInventoryQueryDTO();
                saleInventoryQueryDTO.setProductSpecificationId(item.getProductSpecificationId());
                saleInventoryQueryDTO.setWarehouseId(warehouseId);
                saleInventoryQueryDTO.setOwnerId(detailPO.getOwnerId());
                saleInventoryQueryDTO.setSecOwnerId(detailPO.getSecOwnerId());
                saleInventoryQueryDTO.setCityId(cityId);
                saleInventoryQueryDTOS.add(saleInventoryQueryDTO);
            }
        }
        return saleInventoryQueryDTOS;
    }

}
