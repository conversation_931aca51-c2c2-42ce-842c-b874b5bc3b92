package com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.sowtask;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskResultBO;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskBO;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.batch.bo.CreateSowTaskByOutStockOrderResultBO;
import com.yijiupi.himalaya.supplychain.waves.domain.model.WaveCreateDTO;
import com.yijiupi.himalaya.supplychain.waves.util.RobotPickConstants;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved. <br />
 * 普通播种，什么配置都没开
 * 
 * <AUTHOR>
 * @date 2024/8/18
 */
@Service
public class CreateSowTaskNormalSowBL extends CreateSowTaskBaseBL {

    @Override
    public boolean doSupport(CreateSowTaskBO bo) {
        return BooleanUtils
            .isFalse(RobotPickConstants.isPassageRobotPickOpen(bo.getWavesStrategyDTO(), bo.getPassageDTO()))
            && BooleanUtils.isFalse(
                RobotPickConstants.isWarehouseOpenLargePick(bo.getWavesStrategyDTO(), bo.getWarehouseConfigDTO()));
    }

    @Override
    public CreateSowTaskResultBO doCreateSowTask(CreateSowTaskBO bo) {
        CreateSowTaskByOutStockOrderResultBO orderResultBO =
            createSowTaskByOutStockOrderBL.createSowTaskByOutStockOrder(bo);
        if (Objects.isNull(orderResultBO.getWaveCreateDTO())) {
            return CreateSowTaskResultBO.getDefault();
        }

        CreateSowTaskResultBO createSowTaskResultBO = new CreateSowTaskResultBO();
        createSowTaskResultBO.setSowTaskPOList(orderResultBO.getSowTaskPOList());
        createSowTaskResultBO.setSowOrderPOList(orderResultBO.getSowOrdersList());
        createSowTaskResultBO.setWaveCreateDTOList(Collections.singletonList(orderResultBO.getWaveCreateDTO()));

        return createSowTaskResultBO;
    }
}
